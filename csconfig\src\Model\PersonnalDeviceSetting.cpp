#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "SysEnv.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <list>
#include "DeviceSetting.h"
#include "PersonnalDeviceSetting.h"
#include "PersonalAccount.h"
#include <boost/algorithm/string.hpp>
#include "AkcsCommonDef.h"
#include "dbinterface/Sip.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


CPersonnalDeviceSetting* GetPersonnalDevSettingInstance()
{
    return CPersonnalDeviceSetting::GetInstance();
}

CPersonnalDeviceSetting::CPersonnalDeviceSetting()
{
}

CPersonnalDeviceSetting::~CPersonnalDeviceSetting()
{
}

CPersonnalDeviceSetting* CPersonnalDeviceSetting::instance = NULL;

CPersonnalDeviceSetting* CPersonnalDeviceSetting::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonnalDeviceSetting();
    }

    return instance;
}

// 和TransferDevToPointer的区别is_personal = 1.
static void TransferPerDevToPointer(const ResidentDev& dev, DEVICE_SETTING* dev_pointer)
{
    memset(dev_pointer, 0, sizeof(DEVICE_SETTING));
    dev_pointer->id = dev.id;
    dev_pointer->is_expire = 0;
    dev_pointer->is_personal = 1;    
    dev_pointer->port = dev.port;
    dev_pointer->grade = dev.grade;
    dev_pointer->brand = dev.brand;
    dev_pointer->flags = dev.flags;
    dev_pointer->type = dev.dev_type;
    dev_pointer->status = dev.status;
    dev_pointer->repost = dev.repost;
    dev_pointer->unit_id = dev.unit_id;
    dev_pointer->sip_type = dev.sip_type;
    dev_pointer->device_type = dev.conn_type;
    dev_pointer->stair_show = dev.stair_show; 
    dev_pointer->project_type = dev.project_type;
    dev_pointer->netgroup_num = dev.netgroup_num;
    dev_pointer->manager_account_id = dev.project_mng_id;
    dev_pointer->dclient_version = dev.dclient_ver;
    dev_pointer->fun_bit = dev.fun_bit;
    dev_pointer->firmware = dev.firmware;
    dev_pointer->oem_id = dev.oem_id;
    Snprintf(dev_pointer->mac, sizeof(dev_pointer->mac), dev.mac);
    Snprintf(dev_pointer->relay, sizeof(dev_pointer->relay), dev.relay);
    Snprintf(dev_pointer->SWVer, sizeof(dev_pointer->SWVer), dev.sw_ver);
    Snprintf(dev_pointer->HWVer, sizeof(dev_pointer->HWVer), dev.hw_ver);
    Snprintf(dev_pointer->config, sizeof(dev_pointer->config), dev.autop_config);
    Snprintf(dev_pointer->ip_addr, sizeof(dev_pointer->ip_addr), dev.ipaddr);
    Snprintf(dev_pointer->wired_ip_addr, sizeof(dev_pointer->wired_ip_addr), dev.wired_ipaddr);
    Snprintf(dev_pointer->gateway, sizeof(dev_pointer->gateway), dev.gateway);
    Snprintf(dev_pointer->outer_ip, sizeof(dev_pointer->outer_ip), dev.outer_ip);
    Snprintf(dev_pointer->location, sizeof(dev_pointer->location), dev.location);
    Snprintf(dev_pointer->sip_account, sizeof(dev_pointer->sip_account), dev.sip);
    Snprintf(dev_pointer->device_node, sizeof(dev_pointer->device_node), dev.node);
    Snprintf(dev_pointer->community, sizeof(dev_pointer->community), dev.community);
    Snprintf(dev_pointer->rf_id_md5, sizeof(dev_pointer->rf_id_md5), dev.rf_id_md5);
    Snprintf(dev_pointer->node_uuid, sizeof(dev_pointer->node_uuid), dev.node_uuid);
    Snprintf(dev_pointer->sip_password, sizeof(dev_pointer->sip_password), dev.sippwd);
    Snprintf(dev_pointer->config_md5, sizeof(dev_pointer->config_md5), dev.config_md5);
    Snprintf(dev_pointer->rtsp_password, sizeof(dev_pointer->rtsp_password), dev.rtsppwd);
    Snprintf(dev_pointer->contact_md5, sizeof(dev_pointer->contact_md5), dev.contact_md5);
    Snprintf(dev_pointer->subnet_mask, sizeof(dev_pointer->subnet_mask), dev.subnet_mask);
    Snprintf(dev_pointer->wired_subnet_mask, sizeof(dev_pointer->wired_subnet_mask), dev.wired_subnet_mask);
    Snprintf(dev_pointer->primary_dns, sizeof(dev_pointer->primary_dns), dev.primary_dns);
    Snprintf(dev_pointer->secondary_dns, sizeof(dev_pointer->secondary_dns), dev.secondary_dns);
    Snprintf(dev_pointer->security_relay, sizeof(dev_pointer->security_relay), dev.security_relay);
    Snprintf(dev_pointer->private_key_md5, sizeof(dev_pointer->private_key_md5), dev.private_key_md5);
    Snprintf(dev_pointer->last_connection, sizeof(dev_pointer->last_connection), dev.last_connection);
    Snprintf(dev_pointer->uuid, sizeof(dev_pointer->uuid), dev.uuid);

    dev_pointer->allow_end_user_monitor = dev.allow_end_user_monitor;
    dev_pointer->camera_num = 1; //默认一个设备对应一个Camera
    if (SwitchHandle(dev_pointer->fun_bit, FUNC_DEV_SUPPORT_MULTI_MONITOR))
    {
        dev_pointer->camera_num = 2; //多摄像头目前只有两个摄像头的场景
    }
}

void CPersonnalDeviceSetting::DestoryDeviceSettingList(DEVICE_SETTING* device_header)
{
    DEVICE_SETTING* cur_device = NULL;
    while (NULL != device_header)
    {
        cur_device = device_header;
        device_header = device_header->next;
        delete cur_device;
    }
}

//个人终端用户,获取根节点的设备列表
DEVICE_SETTING* CPersonnalDeviceSetting::GetDeviceSettingByID(int id)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentPerDevices::GetDevByID(id, dev))
    {
        DEVICE_SETTING* dev_setting = new DEVICE_SETTING;
        TransferPerDevToPointer(dev, dev_setting);
        return dev_setting;
    }
    return NULL;
}

//个人终端用户,获取根节点的设备列表
DEVICE_SETTING* CPersonnalDeviceSetting::GetDeviceSettingByMac(const std::string& mac)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
    {
        DEVICE_SETTING* dev_setting = new DEVICE_SETTING;
        TransferPerDevToPointer(dev, dev_setting);
        return dev_setting;
    }
    return NULL;
}

//公共设备目前就不用读取群组号了
DEVICE_SETTING* CPersonnalDeviceSetting::GetPerPublicDeviceSettingListByIDs(const std::string& pub_ids)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentPerDevices::GetDevByIds(pub_ids, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferPerDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

//个人终端用户,获取根节点的设备列表
DEVICE_SETTING* CPersonnalDeviceSetting::GetNodeDeviceSettingList(const std::string& node)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    PersonalAccountCnfInfo node_config;
    if (0 == dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(node, node_config))
    {
        std::vector<DEVICE_CONTACTLIST> app_list;
        if (node_config.enable_robin_call)
        {
            GetPersonalAccountInstance()->DaoGetApplistByNode(node, app_list);
        }

        //查询主账号，看是否开启落地
        ResidentPerAccount node_info;
        memset(&node_info, 0, sizeof(node_info));
        dbinterface::ResidentPersonalAccount::GetUserInfoByAccount(node, node_info);

        std::string sip_group = dbinterface::Sip::GetSipGroupByNode(node);

        std::list<DevNetInfo> push_ip_list;
        ResidentDeviceList dev_list;
        if (0 == dbinterface::ResidentPerDevices::GetNodeDevList(node, dev_list))
        {
            for (auto& dev : dev_list)
            {
                //启用ip直播才能添加室内机IP到pushbutton 并且要把室内机移出群组
                if (dev.dev_type == DEVICE_TYPE_INDOOR && node_info.ip_direct)
                {
                    DevNetInfo infos = {0};
                    infos.netgroup_num = dev.netgroup_num;
                    snprintf(infos.ip, sizeof(infos.ip), "%s", dev.ipaddr);
                    snprintf(infos.sip, sizeof(infos.sip), "%s", dev.sip);
                    push_ip_list.push_front(infos);
                }

                //写主账号配置信息
                dev.motion_time = node_config.motion_time;
                dev.enable_motion = node_config.enable_motion;
                dev.enable_package_detection = node_config.enable_package_detection;
                // dev.enable_sound_detection = node_config.enable_sound_detection,
                // dev.sound_type = node_config.sound_type;
                dev.robin_call_time = node_config.robin_call_time;
                dev.enable_robin_call = node_config.enable_robin_call;
                Snprintf(dev.node_uuid, sizeof(dev.node_uuid), node_info.uuid);
            }

            //写robin call
            std::vector <RobinCallValue> robin_val;
            if (node_config.enable_robin_call)
            {
                node_config.getRobinCallVal(robin_val);
                //初始化RobinVal信息
                for (auto& robin : robin_val)
                {
                    if (robin.type == CALLEE_TYPE_APP || robin.type == CALLEE_TYPE_PHONE || 
                        robin.type == CALLEE_TYPE_PHONE_2 || robin.type == CALLEE_TYPE_PHONE_3)
                    {
                        for (auto& app : app_list)
                        {
                            if (app.id == robin.id)
                            {
                                snprintf(robin.sip, sizeof(robin.sip), "%s", app.sip_account);
                                snprintf(robin.phone, sizeof(robin.phone), "%s", app.phone);
                                snprintf(robin.phone_code, sizeof(robin.phone_code), "%s", app.phone_code);
                                snprintf(robin.phone2, sizeof(robin.phone2), "%s", app.phone2);
                                snprintf(robin.phone3, sizeof(robin.phone3), "%s", app.phone3);
                                robin.role = app.role;
                                break;
                            }
                        }
                    }
                    else if (robin.type == CALLEE_TYPE_INDOOR)
                    {
                        for (auto dev : dev_list)
                        {
                            if (dev.id == robin.id)
                            {
                                snprintf(robin.ip, sizeof(robin.ip), "%s", dev.ipaddr);
                                snprintf(robin.sip, sizeof(robin.sip), "%s", dev.sip);
                                robin.netgroup = dev.netgroup_num;
                                break;
                            }
                        }
                    }
                }
            }
            
            //写pushbutton的值
            for (auto &dev : dev_list)
            {
                if (dev.dev_type == DEVICE_TYPE_DOOR || dev.dev_type == DEVICE_TYPE_STAIR ||
                    dev.dev_type == DEVICE_TYPE_WALL || dev.dev_type == DEVICE_TYPE_ACCESS)
                {
                    std::string robincall;
                    std::list<DevNetInfo>::iterator it = push_ip_list.begin();
                    while (it != push_ip_list.end())
                    {
                       if (it->netgroup_num == dev.netgroup_num && node_info.ip_direct)
                       {
                           snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", it->ip);
                       }
                       else if (it->netgroup_num != dev.netgroup_num || !node_info.ip_direct)
                       {
                           snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", it->sip);
                       }
                       it++;
                    }
                    snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", sip_group.c_str());
                    //落地则拨打手机号码
                    if (node_info.phone_status)
                    {
                       if (strlen(node_info.phone) > 0)
                       {
                           snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone);
                       }
                       if (strlen(node_info.phone2) > 0)
                       {
                           snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone2);
                       }
                       if (strlen(node_info.phone3) > 0)
                       {
                           snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone3);
                       }
                    }
                     //写RobinCall
                    if (node_config.enable_robin_call)
                    {
                        for (auto& robin : robin_val)
                        {
                            if (robin.type == CALLEE_TYPE_APP)
                            {
                                robincall += robin.sip;
                                robincall += ";";
                            }
                            //V5.0版本
                            else if (robin.type == CALLEE_TYPE_PHONE)
                            {
                                if (node_info.phone_status && strlen(robin.phone) > 0)
                                {
                                   robincall += PHONE_CALL_OUT_SUBFIX;
                                   robincall += robin.phone_code;
                                   robincall += robin.phone;
                                   robincall += ";";
                                }
                            }
                            else if (robin.type == CALLEE_TYPE_PHONE_2)
                            {
                                if (node_info.phone_status && strlen(robin.phone2) > 0)
                                {
                                   robincall += PHONE_CALL_OUT_SUBFIX;
                                   robincall += robin.phone_code;
                                   robincall += robin.phone2;
                                   robincall += ";";
                                }
                            }
                            else if (robin.type == CALLEE_TYPE_PHONE_3)
                            {
                                if (node_info.phone_status && strlen(robin.phone3) > 0)
                                {
                                   robincall += PHONE_CALL_OUT_SUBFIX;
                                   robincall += robin.phone_code;
                                   robincall += robin.phone3;
                                   robincall += ";";
                                }
                            }
                            else
                            {
                                if (node_info.ip_direct && dev.netgroup_num == robin.netgroup)
                                {
                                   robincall += robin.ip;
                                   robincall += ";";
                                }
                                else
                                {
                                   robincall += robin.sip;
                                   robincall += ";";
                                }
                            }
                        }
                    }
                    while (strstr(robincall.c_str(), ";;"))
                    {
                        boost::replace_all(robincall, ";;", ";");
                    }
                    snprintf(dev.robin_call_val, sizeof(dev.robin_call_val), "%s", robincall.c_str());

                    std::string push = dev.pushbutton;
                    while (strstr(push.c_str(), ";;"))
                    {
                        boost::replace_all(push, ";;", ";");
                    }
                    snprintf(dev.pushbutton, sizeof(dev.pushbutton), "%s", push.c_str());
                }
            }

            for (auto & dev : dev_list)
            {
                DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
                TransferPerDevToPointer(dev, dev_pointer);
                dev_pointer->motion_time = dev.motion_time;
                dev_pointer->enable_motion = dev.enable_motion;
                dev_pointer->enable_package_detection = dev.enable_package_detection;
                dev_pointer->robin_call_time = dev.robin_call_time;
                dev_pointer->enable_robin_call = dev.enable_robin_call;
                Snprintf(dev_pointer->push_button, sizeof(dev_pointer->push_button), dev.pushbutton);
                Snprintf(dev_pointer->robin_call_val, sizeof(dev_pointer->robin_call_val), dev.robin_call_val);
                InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
            }
       }
    }
    return dev_header;
}
