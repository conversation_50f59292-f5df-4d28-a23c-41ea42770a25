#ifndef __PERSONNAL_DEVICE_SETTING_H__
#define __PERSONNAL_DEVICE_SETTING_H__


typedef struct DevNetInfo_t
{
    int netgroup_num;
    char ip[40];
    char sip[32];
} DevNetInfo;


class CPersonnalDeviceSetting
{
public:
    CPersonnalDeviceSetting();
    ~CPersonnalDeviceSetting();
    static CPersonnalDeviceSetting* GetInstance();

    //个人终端用户,获取根节点的设备列表
    DEVICE_SETTING* GetNodeDeviceSettingList(const std::string& node);
    //更新md5到数据库

    DEVICE_SETTING* GetDeviceSettingByMac(const std::string& mac);
    DEVICE_SETTING* GetDeviceSettingByID(int id);
    DEVICE_SETTING* GetPerPublicDeviceSettingListByIDs(const std::string& pub_ids);
    
    void DestoryDeviceSettingList(DEVICE_SETTING* device_header);
private:
    static CPersonnalDeviceSetting* instance;

};

CPersonnalDeviceSetting* GetPersonnalDevSettingInstance();

#endif //__PERSONNAL_DEVICE_SETTING_H__
