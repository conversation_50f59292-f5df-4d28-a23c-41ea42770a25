#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "SysEnv.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "tinyxml.h"
#include "DeviceSetting.h"
#include "encrypt/Md5.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "PrivateKeyXml.h"
#include "util.h"


extern CRldb rldb;

#define XML_PRIVATE_KEY_NODE_NAME                       "KeyData"
#define XML_PRIVATE_KEY_NODE_KEY_NAME                   "Key"
#define XML_PRIVATE_KEY_ID_ATTR_NAME                    "ID"
#define XML_PRIVATE_KEY_USER_ATTR_NAME                  "Name"
#define XML_PRIVATE_KEY_CODE_ATTR_NAME                  "Code"
#define XML_PRIVATE_KEY_DOORNUM_ATTR_NAME               "DoorNum"
#define XML_PRIVATE_KEY_SECURITY_RELAY_ATTR_NAME        "SecurityRelay"
#define XML_PRIVATE_KEY_MON_ATTR_NAME                   "Mon"
#define XML_PRIVATE_KEY_TUE_ATTR_NAME                   "Tue"
#define XML_PRIVATE_KEY_WED_ATTR_NAME                   "Wed"
#define XML_PRIVATE_KEY_THUR_ATTR_NAME                  "Thur"
#define XML_PRIVATE_KEY_FRI_ATTR_NAME                   "Fri"
#define XML_PRIVATE_KEY_SAT_ATTR_NAME                   "Sat"
#define XML_PRIVATE_KEY_SUN_ATTR_NAME                   "Sun"
#define XML_PRIVATE_KEY_TIMESTART_ATTR_NAME             "TimeStart"
#define XML_PRIVATE_KEY_TIMEEND_ATTR_NAME               "TimeEnd"
#define XML_PRIVATE_KEY_DAYSTART_ATTR_NAME              "DayStart"
#define XML_PRIVATE_KEY_DAYEND_ATTR_NAME                "DayEnd"
#define XML_PRIVATE_KEY_APT_ATTR_NAME                   "UnitApt"
#define XML_PRIVATE_KEY_YES                             "1"
#define XML_PRIVATE_KEY_NO                              "0"
#define XML_PRIVATE_KEY_PHONE_ATTR_NAME                 "Phone"
#define XML_PRIVATE_KEY_MOBILE_ATTR_NAME                "Mobile"
#define XML_PRIVATE_KEY_EMAIL_ATTR_NAME                 "Email"
#define XML_PRIVATE_KEY_COMPANY_ATTR_NAME               "Company"
#define XML_PRIVATE_KEY_ADDRESS_ATTR_NAME               "Address"
#define XML_PRIVATE_KEY_CREATE_TIME_ATTR_NAME           "CreateTime"
#define XML_PRIVATE_KEY_EXPIRE_TIME_ATTR_NAME           "ExpireTime"
#define XML_PRIVATE_KEY_DESC_ATTR_NAME                  "Desc"
#define XML_MAX_ATTR_SIZE                               2048
#define XML_PRIVATE_KEY_TYPE_ATTR_NAME                  "Type"
#define XML_PRIVATE_KEY_TAGS_ATTR_NAME                  "Tags"
#define XML_PRIVATE_KEY_CARD_TYPE_ATTR_NAME             "CardType"
#define XML_PRIVATE_KEY_UFH_CARD_ATTR_NAME              "UHFCard"

//added by chenyc,2017-07-20,因设备继电器数量修改DoorNum
#define RFID_DOORNUM                                        "123"

extern CSCONFIG_CONF gstCSCONFIGConf;


/*
<?xml version="1.0" encoding="UTF-8" ?>
<KeyData>
    <Key ID="1" Name="101" Code="01011122" Mon="Yes" Tue="Yes" Wed="Yes" Thur="Yes" Fri="Yes" Sat="Yes" Sun="Yes" TimeStart="00:00" TimeEnd="23:59"/>
</KeyData >
*/
int community_create_private_key_xml(
    std::string& out_file_content, const char* file_path, PRIVATE_KEY* private_key_header, int default_relay, int default_security_relay)
{
    if (file_path == NULL)
    {
        return -1;
    }

    if (private_key_header == NULL)  //无卡时返回值清空的xml
    {
        std::stringstream key_body;
        key_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
        key_body << "<KeyData>\n";
        key_body << "</KeyData>\n";
        out_file_content = key_body.str();

        return 0;
    }

    TiXmlDeclaration* decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    TiXmlDocument doc;
    doc.LinkEndChild(decl);
    TiXmlElement* root_node = new TiXmlElement(XML_PRIVATE_KEY_NODE_NAME);
    doc.LinkEndChild(root_node);
    int nIndex = 1;

    PRIVATE_KEY* cur_key = private_key_header;
    while (cur_key != NULL)
    {
        TiXmlElement* new_node = new TiXmlElement(XML_PRIVATE_KEY_NODE_KEY_NAME);
        new_node->SetAttribute(XML_PRIVATE_KEY_ID_ATTR_NAME, nIndex++);
        new_node->SetAttribute(XML_PRIVATE_KEY_USER_ATTR_NAME, cur_key->user);
        new_node->SetAttribute(XML_PRIVATE_KEY_CODE_ATTR_NAME, cur_key->code);
        new_node->SetAttribute(XML_PRIVATE_KEY_TYPE_ATTR_NAME, cur_key->type);
        new_node->SetAttribute(XML_PRIVATE_KEY_TAGS_ATTR_NAME, "0");//用于设备区分是云下发还是本地schedule

        
        if (0 == strlen(cur_key->day_start) && 0 == strlen(cur_key->time_start)) // NFC,BLE,个人云终端用户key，按照全天开放
        {
            // 生成Relay的配置
            std::string door_num = RelayToString(default_relay);
            new_node->SetAttribute(XML_PRIVATE_KEY_DOORNUM_ATTR_NAME, door_num.c_str());

            // 生成SecurityRelay的配置
            std::string security_relay = RelayToString(default_security_relay);
            if (!security_relay.empty())
            {
                new_node->SetAttribute(XML_PRIVATE_KEY_SECURITY_RELAY_ATTR_NAME, security_relay.c_str());
            }

            // 生成星期的配置
            new_node->SetAttribute(XML_PRIVATE_KEY_MON_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_TUE_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_WED_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_THUR_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_FRI_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_SAT_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_SUN_ATTR_NAME, XML_PRIVATE_KEY_YES);

            // 生成时间段的配置
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMESTART_ATTR_NAME, "00:00");
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMEEND_ATTR_NAME, "23:59");
        }

        else
        {
            std::string door_num = RelayToString(cur_key->relay & default_relay);
            new_node->SetAttribute(XML_PRIVATE_KEY_DOORNUM_ATTR_NAME, door_num.c_str());

            std::string security_relay = RelayToString(cur_key->security_relay & default_security_relay);
            if (!security_relay.empty())
            {
                new_node->SetAttribute(XML_PRIVATE_KEY_SECURITY_RELAY_ATTR_NAME, security_relay.c_str());
            }

            new_node->SetAttribute(XML_PRIVATE_KEY_MON_ATTR_NAME, ((cur_key->week_day & 2) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_TUE_ATTR_NAME, ((cur_key->week_day & 4) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_WED_ATTR_NAME, ((cur_key->week_day & 8) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_THUR_ATTR_NAME, ((cur_key->week_day & 16) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_FRI_ATTR_NAME, ((cur_key->week_day & 32) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_SAT_ATTR_NAME, ((cur_key->week_day & 64) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_SUN_ATTR_NAME, ((cur_key->week_day & 1) > 0 ? 1 : 0));

            new_node->SetAttribute(XML_PRIVATE_KEY_TIMESTART_ATTR_NAME, cur_key->time_start);
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMEEND_ATTR_NAME, cur_key->time_end);

            new_node->SetAttribute(XML_PRIVATE_KEY_DAYSTART_ATTR_NAME, cur_key->day_start);
            new_node->SetAttribute(XML_PRIVATE_KEY_DAYEND_ATTR_NAME, cur_key->day_end);

        }

        std::stringstream building_apt;
        if (cur_key->building != 0)
        {
            building_apt << cur_key->building << "-" << ExtractFirstNumber(cur_key->apt);
        }
        new_node->SetAttribute(XML_PRIVATE_KEY_APT_ATTR_NAME, building_apt.str().c_str());

        TiXmlNode* sub_node = root_node->LinkEndChild(new_node);
        if (sub_node == NULL)
        {
            return -1;
        }

        cur_key = cur_key->next;
    }
    TiXmlPrinter printer;
    doc.Accept(&printer);
    out_file_content = printer.CStr();
#if 0
    if (!doc.SaveFile(file_path))
    {
        AK_LOG_WARN << "Failed to create rfid key files: " << file_path;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", file_path, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(file_path, AES_ENCRYPT_KEY_V1, file_path);
    }
#endif    
    return 0;
}


/*
<?xml version="1.0" encoding="UTF-8" ?>
<KeyData>
    <Key ID="1" Name="101" Code="01011122" Mon="Yes" Tue="Yes" Wed="Yes" Thur="Yes" Fri="Yes" Sat="Yes" Sun="Yes" TimeStart="00:00" TimeEnd="23:59"/>
</KeyData >
*/
int personal_create_private_key_xml(
    std::string& out_file_content, const char* file_path, PRIVATE_KEY* private_key_header, int default_relay, int default_security_relay)
{
    if (file_path == NULL)
    {
        return -1;
    }

    if (private_key_header == NULL)  //无卡时返回值清空的xml
    {
        std::stringstream key_body;
        key_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
        key_body << "<KeyData>\n";
        key_body << "</KeyData>\n";
        out_file_content = key_body.str();

        return 0;
    }

    TiXmlDeclaration* decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    TiXmlDocument doc;
    doc.LinkEndChild(decl);
    TiXmlElement* root_node = new TiXmlElement(XML_PRIVATE_KEY_NODE_NAME);
    doc.LinkEndChild(root_node);
    int nIndex = 1;

    PRIVATE_KEY* cur_key = private_key_header;
    while (cur_key != NULL)
    {
        TiXmlElement* new_node = new TiXmlElement(XML_PRIVATE_KEY_NODE_KEY_NAME);
        new_node->SetAttribute(XML_PRIVATE_KEY_ID_ATTR_NAME, nIndex++);
        new_node->SetAttribute(XML_PRIVATE_KEY_USER_ATTR_NAME, cur_key->user);
        new_node->SetAttribute(XML_PRIVATE_KEY_CODE_ATTR_NAME, cur_key->code);
        new_node->SetAttribute(XML_PRIVATE_KEY_TYPE_ATTR_NAME, cur_key->type);
        new_node->SetAttribute(XML_PRIVATE_KEY_TAGS_ATTR_NAME, "0");//用于设备区分是云下发还是本地schedule

        // 单住户没有设置时间段，按照全天开放
        {
            // 生成Relay的配置
            std::string door_num = RelayToString(cur_key->relay & default_relay);
            AK_LOG_INFO << "door_num:" << door_num << " relay:" << cur_key->relay << " default_relay:" << default_relay;
            // 生成SecurityRelay的配置
            std::string security_relay = RelayToString(cur_key->security_relay & default_security_relay);
            AK_LOG_INFO << "security_relay:" << security_relay << " cur_key->security_relay:" << cur_key->security_relay << " default_security_relay:" << default_security_relay;


            // 没有可用的relay,整条pin不下发
            if (door_num.empty() && security_relay.empty())
            {
                cur_key = cur_key->next;
                continue;
            }

            // if (!door_num.empty())
            // {
            new_node->SetAttribute(XML_PRIVATE_KEY_DOORNUM_ATTR_NAME, door_num.c_str());
            // }

            if (!security_relay.empty())
            {
                new_node->SetAttribute(XML_PRIVATE_KEY_SECURITY_RELAY_ATTR_NAME, security_relay.c_str());
            }

            // 生成星期的配置
            new_node->SetAttribute(XML_PRIVATE_KEY_MON_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_TUE_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_WED_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_THUR_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_FRI_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_SAT_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_SUN_ATTR_NAME, XML_PRIVATE_KEY_YES);

            // 生成时间段的配置
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMESTART_ATTR_NAME, "00:00");
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMEEND_ATTR_NAME, "23:59");
        }

        std::stringstream building_apt;
        if (cur_key->building != 0)
        {
            building_apt << cur_key->building << "-" << cur_key->apt;
        }
        new_node->SetAttribute(XML_PRIVATE_KEY_APT_ATTR_NAME, building_apt.str().c_str());

        TiXmlNode* sub_node = root_node->LinkEndChild(new_node);
        if (sub_node == NULL)
        {
            return -1;
        }

        cur_key = cur_key->next;
    }

    TiXmlPrinter printer;
    doc.Accept(&printer);
    out_file_content = printer.CStr();

#if 0
    if (!doc.SaveFile(file_path))
    {
        AK_LOG_WARN << "Failed to create rfid key files: " << file_path;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", file_path, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(file_path, AES_ENCRYPT_KEY_V1, file_path);
    }
#endif    
    return 0;
}



/*
<?xml version="1.0" encoding="UTF-8" ?>
<KeyData>
    <Key ID="1" Name="101" Code="01011122" Mon="Yes" Tue="Yes" Wed="Yes" Thur="Yes" Fri="Yes" Sat="Yes" Sun="Yes" TimeStart="00:00" TimeEnd="23:59"/>
</KeyData >
*/
int personal_create_rf_key_xml(
    std::string& out_file_content, const char* file_path, PRIVATE_KEY* private_key_header, int default_relay, int default_security_relay)
{
    if (file_path == NULL)
    {
        return -1;
    }

    if (private_key_header == NULL)  //无卡时返回值清空的xml
    {
        std::stringstream key_body;
        key_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
        key_body << "<KeyData>\n";
        key_body << "</KeyData>\n";
        out_file_content = key_body.str();

        return 0;
    }

    TiXmlDeclaration* decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    TiXmlDocument doc;
    doc.LinkEndChild(decl);
    TiXmlElement* root_node = new TiXmlElement(XML_PRIVATE_KEY_NODE_NAME);
    doc.LinkEndChild(root_node);
    int nIndex = 1;

    PRIVATE_KEY* cur_key = private_key_header;
    while (cur_key != NULL)
    {
        TiXmlElement* new_node = new TiXmlElement(XML_PRIVATE_KEY_NODE_KEY_NAME);
        new_node->SetAttribute(XML_PRIVATE_KEY_ID_ATTR_NAME, nIndex++);
        new_node->SetAttribute(XML_PRIVATE_KEY_USER_ATTR_NAME, cur_key->user);
        new_node->SetAttribute(XML_PRIVATE_KEY_CODE_ATTR_NAME, cur_key->code);
        new_node->SetAttribute(XML_PRIVATE_KEY_TYPE_ATTR_NAME, cur_key->type);
        new_node->SetAttribute(XML_PRIVATE_KEY_TAGS_ATTR_NAME, "0");//用于设备区分是云下发还是本地schedule

        new_node->SetAttribute(XML_PRIVATE_KEY_CARD_TYPE_ATTR_NAME, (int)cur_key->card_type);
        
        if (cur_key->card_type == CardType::LICENSE_PLATE) {
            new_node->SetAttribute(XML_PRIVATE_KEY_UFH_CARD_ATTR_NAME, cur_key->ufh);
        }
        
        if (0 == strlen(cur_key->day_start) && 0 == strlen(cur_key->time_start)) // NFC,BLE,个人云终端用户key，按照全天开放
        {
            // 生成Relay的配置
            std::string door_num = RelayToString(default_relay);
            new_node->SetAttribute(XML_PRIVATE_KEY_DOORNUM_ATTR_NAME, door_num.c_str());

            // 生成SecurityRelay的配置
            std::string security_relay = RelayToString(default_security_relay);
            if (!security_relay.empty())
            {
                new_node->SetAttribute(XML_PRIVATE_KEY_SECURITY_RELAY_ATTR_NAME, security_relay.c_str());
            }

            // 生成星期的配置
            new_node->SetAttribute(XML_PRIVATE_KEY_MON_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_TUE_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_WED_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_THUR_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_FRI_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_SAT_ATTR_NAME, XML_PRIVATE_KEY_YES);
            new_node->SetAttribute(XML_PRIVATE_KEY_SUN_ATTR_NAME, XML_PRIVATE_KEY_YES);

            // 生成时间段的配置
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMESTART_ATTR_NAME, "00:00");
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMEEND_ATTR_NAME, "23:59");
        }

        else
        {
            std::string door_num = RelayToString(cur_key->relay & default_relay);
            new_node->SetAttribute(XML_PRIVATE_KEY_DOORNUM_ATTR_NAME, door_num.c_str());

            std::string security_relay = RelayToString(cur_key->security_relay & default_security_relay);
            if (!security_relay.empty())
            {
                new_node->SetAttribute(XML_PRIVATE_KEY_SECURITY_RELAY_ATTR_NAME, security_relay.c_str());
            }

            new_node->SetAttribute(XML_PRIVATE_KEY_MON_ATTR_NAME, ((cur_key->week_day & 2) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_TUE_ATTR_NAME, ((cur_key->week_day & 4) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_WED_ATTR_NAME, ((cur_key->week_day & 8) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_THUR_ATTR_NAME, ((cur_key->week_day & 16) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_FRI_ATTR_NAME, ((cur_key->week_day & 32) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_SAT_ATTR_NAME, ((cur_key->week_day & 64) > 0 ? 1 : 0));
            new_node->SetAttribute(XML_PRIVATE_KEY_SUN_ATTR_NAME, ((cur_key->week_day & 1) > 0 ? 1 : 0));

            new_node->SetAttribute(XML_PRIVATE_KEY_TIMESTART_ATTR_NAME, cur_key->time_start);
            new_node->SetAttribute(XML_PRIVATE_KEY_TIMEEND_ATTR_NAME, cur_key->time_end);

            new_node->SetAttribute(XML_PRIVATE_KEY_DAYSTART_ATTR_NAME, cur_key->day_start);
            new_node->SetAttribute(XML_PRIVATE_KEY_DAYEND_ATTR_NAME, cur_key->day_end);

        }

        std::stringstream building_apt;
        if (cur_key->building != 0)
        {
            building_apt << cur_key->building << "-" << ExtractFirstNumber(cur_key->apt);
        }
        new_node->SetAttribute(XML_PRIVATE_KEY_APT_ATTR_NAME, building_apt.str().c_str());

        TiXmlNode* sub_node = root_node->LinkEndChild(new_node);
        if (sub_node == NULL)
        {
            return -1;
        }

        cur_key = cur_key->next;
    }
    TiXmlPrinter printer;
    doc.Accept(&printer);
    out_file_content = printer.CStr();
#if 0
    if (!doc.SaveFile(file_path))
    {
        AK_LOG_WARN << "Failed to create rfid key files: " << file_path;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", file_path, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(file_path, AES_ENCRYPT_KEY_V1, file_path);
    }
#endif    
    return 0;
}