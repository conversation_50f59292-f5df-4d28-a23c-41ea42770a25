#ifndef __PRIVATE_KEY_XML_H__
#define __PRIVATE_KEY_XML_H__
#include <string>

int community_create_private_key_xml(
    std::string &out_file_content, const char* file_path, PRIVATE_KEY* private_key_header, int default_relay, int default_security_relay);
int personal_create_private_key_xml(
    std::string &out_file_content, const char* file_path, PRIVATE_KEY* private_key_header, int default_relay, int default_security_relay);
int personal_create_rf_key_xml(
    std::string &out_file_content, const char* file_path, PRIVATE_KEY* private_key_header, int default_relay, int default_security_relay);

#endif
