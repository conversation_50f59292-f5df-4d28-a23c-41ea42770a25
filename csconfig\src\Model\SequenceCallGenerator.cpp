#include "SequenceCallGenerator.h"
#include <string>
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "AkLogging.h"
#include "AKCSMsg.h"
#include "dbinterface/CommunityCallRule.h"
#include "DevContact.h"
#include "dbinterface/AnalogDevice.h"
#include "ContactCommon.h"

//社区sequence call最大轮数
#define MAX_CALL_SEQ_ROUND 3 

SeqCallGenerator::SeqCallGenerator(const CommunityCallRuleInfo& comm_call_rule, int enable_ip_direct, int dev_network_group) : enable_ip_direct_(enable_ip_direct), dev_network_group_(dev_network_group)
{
    //群呼相关规则初始化
    comm_call_rule_ = comm_call_rule;
    
    if (comm_call_rule_.apt_call_type == AptCallType::APT_CALLTYPE_SEQUENCE_CALL)
    {
        dbinterface::CommunitySequenceCallList::GetCommunitySequenceCallMapByCallRuleUUID(comm_call_rule_.uuid, seq_call_map_);
    }
}

void SeqCallGenerator::GetSeqCallKv(ContactKvList& kv)
{
    //开启sequence call则下发
    if(comm_call_rule_.apt_call_type == AptCallType::APT_CALLTYPE_SEQUENCE_CALL)
    {
        kv.push_back(std::make_pair(CONTACT_ATTR::SEQCALLNUM, GetSeqCallNumStr()));
        kv.push_back(std::make_pair(CONTACT_ATTR::SEQCALLENABLE, "1"));
    }
}

//同一轮不同号码用","分隔，不同轮号码用";"分隔
std::string SeqCallGenerator::GetSeqCallNumStr()
{    
    std::vector<std::string> all_round_call_str_vec;
    //不同轮号码用";"分隔
    for (int seq_call_round = 1; seq_call_round <= MAX_CALL_SEQ_ROUND; ++seq_call_round)
    {
        std::vector<std::string> one_round_call_str_vec;
        //同一轮号码用","分隔
        for (const auto& seq_call_list : seq_call_map_[seq_call_round])
        {
            one_round_call_str_vec.push_back(GetCallNumStr(seq_call_list.callee_uuid, seq_call_list.callee_type));
        }
        all_round_call_str_vec.push_back(ListToSeparatedFormatString(one_round_call_str_vec, ','));
    }

    return ListToSeparatedFormatString(all_round_call_str_vec, ';');
}

std::string SeqCallGenerator::GetCallNumStr(const std::string& callee_uuid, int callee_type)
{
    if (callee_type == SeqCalleeType::CALLEE_TYPE_INDOOR)
    {
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(callee_uuid, dev))
        {
            AK_LOG_WARN << "get device info failed. device uuid=" << callee_uuid;
            return "";
        }
        //开启ip直播且网络组相同，则走ip
        if (enable_ip_direct_ && dev.netgroup_num == dev_network_group_)
        {
            return dev.ipaddr;
        }
        //其余情况，走sip
        return dev.sip;
    }
    else if (callee_type == SeqCalleeType::CALLEE_TYPE_ANALOG_DEVICE)
    {
        std::string analog_device_uid;
        AnalogDeviceInfo analog_device_info;
        if (0 != dbinterface::AnalogDevice::GetAnalogDeviceByUUID(callee_uuid, analog_device_info))
        {
            AK_LOG_WARN << "get analog device info failed. uuid=" << callee_uuid;
            return analog_device_uid;
        }
        return GetAnalogDeviceUID(analog_device_info.id);
    }
    //其余被叫类型都先要先查出用户信息
    ResidentPerAccount per_account;
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(callee_uuid, per_account))
    {
        AK_LOG_WARN << "get per account info failed. per account uuid=" << callee_uuid;
        return "";
    }
    if (callee_type == SeqCalleeType::CALLEE_TYPE_APP)
    {
        return per_account.sip_account;
    }

    std::string phone_head = PHONE_CALL_OUT_SUBFIX;
    phone_head += per_account.phone_code;

    if (callee_type == SeqCalleeType::CALLEE_TYPE_PHONE)
    {
        return phone_head + per_account.phone;
    }
    else if (callee_type == SeqCalleeType::CALLEE_TYPE_PHONE_2)
    {
        return phone_head + per_account.phone2;
    }
    else if (callee_type == SeqCalleeType::CALLEE_TYPE_PHONE_3)
    {
        return phone_head + per_account.phone3;
    }
    else
    {
        AK_LOG_WARN << "callee type not support, type=" << callee_type;
    }
    return "";
}
