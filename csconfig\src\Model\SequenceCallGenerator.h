#ifndef __SEQUENCE_CALLSTR_EGNERATOR_H__
#define __SEQUENCE_CALLSTR_EGNERATOR_H__
#include <string>
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityCallRule.h"
#include "ContactCommon.h"
class SeqCallGenerator
{
public:
    SeqCallGenerator(const CommunityCallRuleInfo& comm_call_rule, int enable_ip_direct, int dev_network_group);
    void GetSeqCallKv(ContactKvList& kv);
private:
    CommunityCallRuleInfo comm_call_rule_;
    CommunitySeqCallMap seq_call_map_;
    int enable_ip_direct_; //房间是否开启ip直播
    int dev_network_group_; //目标设备网络组

    std::string GetSeqCallNumStr();
    std::string GetCallNumStr(const std::string& callee_uuid, int callee_type);

};

#endif //__SEQUENCE_CALLSTR_EGNERATOR_H__

