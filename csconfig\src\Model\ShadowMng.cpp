#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include "base64.h"
#include "sockopt.h"
#include <assert.h>
#include "AkLogging.h"
#include "ShadowMng.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ConfigDef.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "util.h"

static const char kConfigFdfsGroup[] = "group2";
static const char kConfigFdfsFilePath[] = "/usr/local/akcs/csconfig/conf/csconfig_fdfs.conf";

extern CSCONFIG_CONF gstCSCONFIGConf;

CShadowMng::CShadowMng()
{
    uploader_ = std::unique_ptr<ConfigFdfsUploader>(new ConfigFdfsUploader());
    uploader_->Init(kConfigFdfsFilePath);
    uploader_->SetUploadGroupName(kConfigFdfsGroup);

}

CShadowMng::~CShadowMng()
{
}

bool CShadowMng::CheckFdfsNormal()
{
    return uploader_->GetFdfsStat();
}

int CShadowMng::DeleteDevShadow(const std::string &mac)
{
    std::string shadow_path[7];
    std::string remote_filename;
    std::string meta_value;
    DevShadow shadow = {0};

    if(dbinterface::Shadow::GetAllShadowByMac(mac, shadow) != 0)
    {
        return 0;
    }

    if(dbinterface::Shadow::DelShadowByMac(mac) == 0)
    {
        shadow_path[0] = shadow.config_storage_path;
        shadow_path[1] = shadow.contac_storage_path;    
        shadow_path[2] = shadow.face_storage_path;
        shadow_path[3] = shadow.usermeta_storage_path;
        shadow_path[4] = shadow.prikey_storage_path;
        shadow_path[5] = shadow.rfkey_storage_path;    
        shadow_path[6] = shadow.schedule_storage_path;

        for(int i = 0; i < sizeof(shadow_path) / sizeof(*shadow_path); i++)
        {
            remote_filename = shadow_path[i];
            if(remote_filename.length() > 0)
            {
                uploader_->DeleteFile(remote_filename, mac); 
            }
        }
    }
    return 0;
}


int CShadowMng::StoreDevShadow(const char* local_filepath, const std::string& mac, SHADOW_TYPE shadow_type)
{
    if(gstCSCONFIGConf.is_store_fdfs == 0)
    {
        return 0;
    }
    int storage_ret = 0;
    if(local_filepath == nullptr || mac.size() == 0)
    {
        AK_LOG_WARN << "StoreDevShadow param error, return";
        return -1;
    }

    std::string path_before;
    std::string path_after;
    
    //上传失败，重试次数为1次
    if(uploader_->UploadFile(local_filepath, path_after, 1) != 0) {
        // 针对tracker_server一段时间不用后挂了的情况，进行上传重试
        if(uploader_->UploadFile(local_filepath, path_after, 1) != 0) {
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", local_filepath, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
            AK_LOG_ERROR<< "UploadFile failed, direct update source file to database. local_filepath:" << local_filepath << " mac:" << mac << " shadow_type:" << shadow_type;

            path_after = local_filepath;
            //容错,上传fdfs失败,将local_filepath作为下载地址
            size_t pos = path_after.find("/download");  //去掉前缀 /var/www 
            if (pos != std::string::npos)
            {
                path_after = path_after.substr(pos);  
            }
            
            storage_ret = -1;
            dbinterface::Shadow::RecordShaowError(mac, shadow_type, gstCSCONFIGConf.server_inner_ip);
        }
    }

    path_before = dbinterface::Shadow::GetShadowByMac(mac, shadow_type);

    int ret = dbinterface::Shadow::UpdateShadowByMac(mac, shadow_type, path_after);
    if(ret == 0)
    {    
        //查询之前存的路径,用于删除
        uploader_->DeleteFile(path_before, mac);    
    } 
    else
    {
        AK_LOG_WARN << "UPDATE database failed, do not delete file";
        storage_ret = -1;
    }
    
    return storage_ret;    
}


