#ifndef __SHADOW_MNG_H__
#define __SHADOW_MNG_H__

#include <string>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "dbinterface/Shadow.h"
#include "config_fdfs_uploader.h"

class CShadowMng
{
public:
    CShadowMng();
    ~CShadowMng();    
    int StoreDevShadow(const char* local_filepath, const std::string& mac, SHADOW_TYPE shadow_type);
    int DeleteDevShadow(const std::string &mac);
    bool CheckFdfsNormal();
private:
    std::unique_ptr<ConfigFdfsUploader> uploader_;
};


#endif //__SHADOW_MNG_H__

