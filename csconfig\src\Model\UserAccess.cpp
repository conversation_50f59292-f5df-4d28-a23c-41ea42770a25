#include <sstream>
#include "UserAccess.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "FaceMng.h"
#include "AkcsCommonDef.h"
#include "dbinterface/CommPerRfKey.h"
#include "dbinterface/CommPerPrivateKey.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/AccountAccess.h"
#include "dbinterface/DeliveryAccess.h"
#include "dbinterface/StaffAccess.h"
#include "dbinterface/UserAccessGroup.h"
#include "dbinterface/VisitorIDAccess.h"
#include "dbinterface/PersonalIDAccess.h"
#include "dbinterface/LicensePlate.h"

UserAccess::UserAccess(  )
{

}


UserAccess::~UserAccess()
{
    
}

void UserAccess::GetPerUserDetailInfo(UserAccessInfoPtrMap &list)
{
    std::vector<unsigned int> ids_vev;
    std::vector<std::string> accounts_vec;
    std::vector<std::string> uuids_vec;
    //获取哪些ua是没有初始化过的，在后面进行初始化，并且执行SetOpendoorInit
    for (const auto& ua : list)
    {
        //多个设备对同个用户只需要初始化一次,不需要重复初始化
        if (ua.second->GetUserType() != USER_TYPE::ACCOUNT || ua.second->IsOpendoorInit())
        {
            continue;
        }
        ids_vev.push_back(ua.second->GetDBID());
        accounts_vec.push_back(ua.second->GetUUID());
        uuids_vec.push_back(ua.second->GetDBUUID());
    }
    
    //都已经是初始化过的了，直接退出
    if (accounts_vec.size() == 0)
    {
        return;
    }    

    std::string accounts_str = ListToSeparatedFormatString(accounts_vec);
    std::string ids_str = ListToSeparatedFormatString(ids_vev);
    std::string uuids_str = ListToSeparatedFormatString(uuids_vec);

    UsersRFInfoMap pm_rf;
    UsersRFInfoMap enduser_rf;
    UsersPinInfoMap pin;
    UsersPinInfoMap spicalkey_pin;
    UsersIDAccessMap id_access_map;
    UsersIDAccessMap spe_id_access_map;
    UsersLicensePlateInfoMap license_plate_map;

    dbinterface::PersonalIDAccess::GetIDAccessListByPersonalAccountUUID(uuids_str, id_access_map, spe_id_access_map);
    
    dbinterface::CommPersonalAccount::GetAccountRfkeyList(accounts_str, enduser_rf);
    dbinterface::CommPerRfKey::GetAccountRfkeyList(accounts_str, pm_rf);
    dbinterface::CommPerPrivateKey::GetAccountPrivatekeyList(accounts_str, pin, spicalkey_pin);
    dbinterface::LicensePlate::GetUserLicensePlateList(uuids_str, license_plate_map);

    std::string nodes_str;
    UserNodeInfoMap user_nodes;
    CommunityRoomInfoMap rooms;
    dbinterface::CommPersonalAccount::UsersToNodesMap(accounts_str, user_nodes);
    dbinterface::CommPersonalAccount::NodesToStrings(user_nodes, nodes_str);
    dbinterface::CommunityRoom::GetCommunityRoomByNodes(nodes_str, rooms);

    PersonalAccountCnfInfoMap nodes_config;
    dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNodes(nodes_str, nodes_config);

    std::map<std::string, FaceMngInfo> face_mng_infos;
    int ret = dbinterface::FaceMng::GetFaceMngByPersonalAccountIds(face_mng_infos, ids_str);    
    for(auto &it : list)
    {
        UserAccessInfoPtr ua = static_cast<UserAccessInfoPtr>(it.second);
        
        if (ua->GetUserType() != USER_TYPE::ACCOUNT)
        {
            continue;
        }
        if (ua->IsOpendoorInit())
        {
            continue;
        }
        else
        {
            ua->SetOpendoorInit();
        }        

        ua->SetFloor(kDefaultFloor);
        
        // 设置房间
        if (ua->GetRole() == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || ua->GetRole() == ACCOUNT_ROLE_COMMUNITY_MAIN)
        {
            UserNodeInfoMapIter user_node_iter;
            user_node_iter = user_nodes.find(ua->GetUUID());
            if (user_node_iter != user_nodes.end())
            {
                std::string node = user_node_iter->second;
                CommunityRoomInfoMapIter room_iter;
                room_iter = rooms.find(node); 
                if (room_iter != rooms.end())
                {
                    CommunityRoomInfoPtr room = std::make_shared<CommunityRoomInfo>(room_iter->second);
                    ua->SetRoomNumber(room->room_number);
                    std::string apt_floor = room->floor;
                    std::string accessible_floor = dbinterface::PersonalAccountCommunityInfo::GetFloorByUUID(ua->GetDBUUID());
                    ua->SetFloor(GetAccessibleFloor(apt_floor, accessible_floor));
                }
                ua->SetNode(node);
            }
        }

        //office梯控
        if (IsOfficeRole(ua->GetRole()))
        {
            std::string floor = dbinterface::OfficePersonalAccount::GetFloorByAccountUUID(ua->GetDBUUID());
            // 云上未填floor的下发"0"
            ua->SetFloor(floor.size() > 0 ? floor : kDefaultFloor); 
        }
            
        //设置pm添加的RF
        UsersRFInfoMapIter pm_rf_iter;
        pm_rf_iter = pm_rf.find(ua->GetUUID());
        if (pm_rf_iter != pm_rf.end())
        {
            for(auto const &key : pm_rf_iter->second)
            {
                ua->AddPmRf(key);
            }
        }

        //设置用户创建NFC,Bluetooth
        UsersRFInfoMapIter enduser_rf_iter;
        enduser_rf_iter = enduser_rf.find(ua->GetUUID());
        if (enduser_rf_iter != enduser_rf.end())
        {
            for(auto const &key : enduser_rf_iter->second)
            {
                ua->AddEnduserRf(key);
            }
        }
        
        //设置pin
        UsersPinInfoMapIter iter_pin;
        iter_pin = pin.find(ua->GetUUID());
        if (iter_pin != pin.end())
        {
            for(auto const &key : iter_pin->second)
            {
                ua->AddPin(key);
            }
        }
        
        UsersPinInfoMapIter iter_spicalkey_pin;
        iter_spicalkey_pin = spicalkey_pin.find(ua->GetUUID());
        if (iter_spicalkey_pin != spicalkey_pin.end())
        {
            for(auto const &key : iter_spicalkey_pin->second)
            {
                ua->AddSpecialPin(key);
            }
        }         

        //设置人脸
        std::map<std::string, FaceMngInfo>::iterator iter_face;
        iter_face = face_mng_infos.find(ua->GetUUID());
        if (iter_face != face_mng_infos.end())
        {
            FaceMngInfo *face = &iter_face->second;
            std::string md5 = face->face_md5;
            std::string face_url = face->face_url;
            if(md5.size() == 0) //face_md5为空时，以文件名做唯一值即可
            {
                std::string::size_type pos = face_url.find(".");
                if (pos != std::string::npos)
                {
                    std::string file_head = face_url.substr(0, pos);
                    pos = file_head.find_last_of ("/");
                    if (pos != std::string::npos)
                    {
                        md5 = file_head.substr(pos + 1);                    
                    }
                }
            }
            ua->SetFaceUrl(face_url);
            ua->SetFaceMd5(md5);
            //creator_type: 0-pm 1-enduser, enduser创建的人脸为special_face_
            ua->SetSpecialFace(face->creator_type);   
        }

        //设置webrelay
        UserNodeInfoMapIter user_node_iter;
        user_node_iter = user_nodes.find(ua->GetUUID());
        if (user_node_iter != user_nodes.end())
        {
            std::string node = user_node_iter->second;
            PersonalAccountCnfInfoMapIter config_iter;
            config_iter = nodes_config.find(node);
            if (config_iter != nodes_config.end())
            {
                PersonalAccountCnfInfo *config = &config_iter->second;
                ua->SetWebRelay(config->web_relay);                     
            }
        }                

        GetUserIDAccessInfo(ua, id_access_map, spe_id_access_map);

        UsersLicensePlateInfoMapIter iter_license_plate;
        iter_license_plate = license_plate_map.find(ua->GetDBUUID());
        if (iter_license_plate != license_plate_map.end())
        {
            for(auto const &car_data : iter_license_plate->second)
            {
                Json::Value license_plate;
                license_plate["LicensePlate"] = car_data.LicensePlate;
                license_plate["UFHCard"] = car_data.UFHCard;
                license_plate["BeginTime"] = car_data.BeginTime;
                license_plate["EndTime"] = car_data.EndTime;

                ua->AddLicensePlate(license_plate);
            }
        }    
    }
}


/*住户, 公共设备权限组包含的用户列表*/
void UserAccess::GetPubDevAccountListByAccessGroupID(uint32_t id, UserAccessInfoPtrMap &list)
{
    UserAccessNodeList ua_list;
    dbinterface::AccountAccess::GetPubDevAccountListByAccessGroupID(id, ua_list);
    for (const auto& ua_node : ua_list)
    {
        UserAccessInfoPtr ua = std::make_shared<UserAccessInfo>(USER_TYPE::ACCOUNT);
        ua->AddAccessGroupID(id);
        ua->SetName(ua_node.name);
        ua->SetUUID(ua_node.uuid);
        ua->SetMeta(ua_node.meta);
        ua->SetDBID(ua_node.dbid);
        ua->SetRole(ua_node.role);
        ua->SetUnitID(ua_node.unit_id);
        ua->SetDBUUID(ua_node.db_uuid);
        list.insert(std::make_pair(ua_node.uuid, ua));
    }
}

/*公共人员：公共设备权限组包含的staff/delivery列表*/
void UserAccess::GetPubDevPubAccountListByAccessGroupID(uint32_t id, UserAccessInfoPtrMap &list)
{
    UserAccessNodeList da_list;
    UserAccessNodeList sa_list;
    dbinterface::DeliveryAccess::GetPubDevDeliveryListByAccessGroupID(id, da_list);
    for (const auto& ua_node : da_list)
    {
        UserAccessInfoPtr ua = std::make_shared<UserAccessInfo>(USER_TYPE::DELIVERY);
        ua->AddAccessGroupID(id);
        ua->SetName(ua_node.name);
        ua->AddPmRf(ua_node.pm_rf);
        ua->AddPin(ua_node.pin);
        ua->SetMeta(ua_node.meta);
        ua->SetDBUUID(ua_node.db_uuid);
        //南美身份证构造
        IDAccessInfo id_access_info(ua_node.id_access_mode, ua_node.id_access_run, ua_node.id_access_serial);
        ua->SetIDAccessInfo(id_access_info);
        //UserMeta文件中的PerID
        ua->SetUUID(ua->CreateSpecialUUID(USER_TYPE::DELIVERY, ua_node.dbid)); 
        list.insert(std::make_pair(ua->GetUUID(), ua));
    }

    dbinterface::StaffAccess::GetPubDevStaffListByAccessGroupID(id, sa_list);
    for (const auto& ua_node : sa_list)
    {
        UserAccessInfoPtr ua = std::make_shared<UserAccessInfo>(USER_TYPE::STAFF);
        ua->AddAccessGroupID(id);
        ua->SetName(ua_node.name);
        ua->AddPmRf(ua_node.pm_rf);
        ua->SetMeta(ua_node.meta);
        ua->SetDBUUID(ua_node.db_uuid);
        std::string tmp_url = ua_node.face_url;
        ua->SetFaceUrl(tmp_url);
        ua->SetFaceMd5(ua_node.face_md5);
        ua->AddPin(ua_node.pin);
        //南美身份证构造
        IDAccessInfo id_access_info(ua_node.id_access_mode, ua_node.id_access_run, ua_node.id_access_serial);
        ua->SetIDAccessInfo(id_access_info);
        ua->SetUUID(ua->CreateSpecialUUID(USER_TYPE::STAFF, ua_node.dbid)); 
        list.insert(std::make_pair(ua->GetUUID(), ua));
    }
}


void UserAccess::GetPerDevAccountListByAccessGroupID(uint32_t id, UserAccessInfoPtrMap &list)
{
    UserAccessNodeList ua_list;
    dbinterface::UserAccessGroup::GetPerDevAccountListByAccessGroupID(id, ua_list);
    for (const auto& ua_node : ua_list)
    {
        UserAccessInfoPtr ua = std::make_shared<UserAccessInfo>(USER_TYPE::ACCOUNT);
        ua->AddAccessGroupID(id);
        ua->SetName(ua_node.name);
        ua->SetUUID(ua_node.uuid);
        ua->SetMeta(ua_node.meta);
        ua->SetDBID(ua_node.dbid);
        ua->SetRole(ua_node.role);
        ua->SetUnitID(ua_node.unit_id);
        ua->SetDBUUID(ua_node.db_uuid);
        list.insert(std::make_pair(ua_node.uuid, ua));
    }
}

void UserAccess::GetUserIDAccessInfo(UserAccessInfoPtr& ua, const UsersIDAccessMap& id_access_map,  const UsersIDAccessMap& spe_id_access_map)
{
    //社区设置id access
    if (ua->GetRole() == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || ua->GetRole() == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        auto iter = id_access_map.find(ua->GetDBUUID());
        if (iter != id_access_map.end())
        {
            PersonalIDAccessInfo id_access_info = iter->second;
            IDAccessInfo user_id_access_info(id_access_info.mode, id_access_info.run, id_access_info.serial);
            ua->SetSpecialIDAccess(0);
            ua->SetIDAccessInfo(user_id_access_info);
        }
        iter = spe_id_access_map.find(ua->GetDBUUID());
        if (iter != spe_id_access_map.end())
        {
            PersonalIDAccessInfo id_access_info = iter->second;
            IDAccessInfo user_id_access_info(id_access_info.mode, id_access_info.run, id_access_info.serial);
            ua->SetSpecialIDAccess(1);
            ua->SetSpecialIDAccessInfo(user_id_access_info);
        }
    }
}
