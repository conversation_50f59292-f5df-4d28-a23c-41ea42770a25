#ifndef __USER_ACCESS_H__
#define __USER_ACCESS_H__
#include <string>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"
#include "UserAccessInfo.h"
#include "dbinterface/PersonalIDAccess.h"
/*
包括：获取用户/staff/delivery所有的开门方式
*/
class UserAccess
{
public:
	UserAccess(  );
	~UserAccess();

    /*获取公共设备对应权限住的租户列表*/
    void GetPubDevAccountListByAccessGroupID(uint32_t id, UserAccessInfoPtrMap &list);
     /*获取公共设备对应权限租的staff/delivery列表*/
    void GetPubDevPubAccountListByAccessGroupID(uint32_t id, UserAccessInfoPtrMap &list);
     /*获取个人设备对应权限组的用户列表。个人设备一个权限组一个用户*/
    void GetPerDevAccountListByAccessGroupID(uint32_t id, UserAccessInfoPtrMap &list);
     /*获取用户详细信息，只有住户，不包括快递和物业*/
    void GetPerUserDetailInfo(UserAccessInfoPtrMap &list);
private: 
    //只获取元数据，公共人员因为需要在查询别的表，直接获取全部。用户数据需要分开
    int get_only_meta_data_;
    //获取用户id access相关信息，区分是否是用户创建的
    void GetUserIDAccessInfo(UserAccessInfoPtr& ua, const UsersIDAccessMap& id_access_map,  const UsersIDAccessMap& spe_id_access_map);
};




#endif 
