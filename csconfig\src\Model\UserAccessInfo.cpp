#include <sstream>
#include "UserAccessInfo.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/CommunityInfo.h"
#include "FaceMng.h"


UserAccessInfo::UserAccessInfo(USER_TYPE type  )
{
    user_type_ = type;
    opendoor_data_init_ = 0;    
    db_id_ = 0;
    unit_id_ = 0;
    role_ = 0;
    web_relay_ = 0;
    special_face_ = 0; 
    special_id_access_ = 0;
}

UserAccessInfo::~UserAccessInfo()
{
    
}


void UserAccessInfo::AddPmRf(const std::string &key)
{
    if (key.size() > 0)
    {
        pm_rf_list_.push_back(key);
    }
}   

//用户的NFC和Bluetooth
void UserAccessInfo::AddEnduserRf(const std::string &key)
{
    if (key.size() > 0)
    {
        enduser_rf_list_.push_back(key);
    }
} 

void UserAccessInfo::AddPin(const std::string &key)
{
    if (key.size() > 0)
    {
        pin_list_.push_back(key);
    }
}

void UserAccessInfo::AddSpecialPin(const std::string &key)
{
    if (key.size() > 0)
    {
        special_key_list_.push_back(key);
    }    
}

void UserAccessInfo::AddAccessGroupID(uint32_t id)
{
    access_group_id_list_.push_back(id);
}

void UserAccessInfo::GetAccessGroupIDList(UserAccessGroupIDList &list)
{
    list = access_group_id_list_;
}

void UserAccessInfo::SetDBID(uint32_t db_id)
{
    db_id_ = db_id;
}  

uint32_t UserAccessInfo::GetDBID()
{
    return db_id_;
}

USER_TYPE UserAccessInfo::GetUserType()
{
    return user_type_;
}

void UserAccessInfo::SetRole(uint32_t role)
{
    role_ = role;
}  

uint32_t UserAccessInfo::GetRole()
{
    return role_;
}

void UserAccessInfo::SetName(const std::string &name)
{
    name_ = name;
} 

std::string UserAccessInfo::GetName()
{
    return name_;
}

void UserAccessInfo::SetFaceMd5(const std::string &face_md5)
{
    face_md5_ = face_md5;
}  

std::string UserAccessInfo::GetFaceMd5()
{
    return face_md5_;
}

void UserAccessInfo::SetFaceUrl(const std::string &face_url)
{
    face_url_ = face_url;
}

std::string UserAccessInfo::GetFaceUrl(const std::string &mac)
{
    std::string download_face_url;
    if (face_url_.size() > 0)
    {
        std::string download_url_prefix = CFaceXmlWriter::GetInstance().GetPicFilePath(mac);
        download_face_url = download_url_prefix + face_url_;
    }
    return download_face_url;
}  

//获取user所有的rf card
std::string UserAccessInfo::GetRfString()
{
    std::string keys;
    //pm创的rf card
    for (const auto& key : pm_rf_list_)
    {
        keys += key + ";";
    }
    //用户创的NFC和BLE
    for (const auto& key : enduser_rf_list_)
    {
        keys += key + ";";
    }
    return keys;
}

std::string UserAccessInfo::GetEnduserRfString()
{
    std::string keys;
    for (const auto& key : enduser_rf_list_)
    {
        keys += key + ";";
    }
    return keys;
}

std::string UserAccessInfo::GetPinString(int pin_type, int can_create_pin, int enable_private_access)
{
    std::string keys;
    //判断pm能否创建apt_door pin
    if (enable_private_access)
    {
        //社区为APT_PIN模式,但Delivery和Pm不存在Apt,需要判断room_number_是否存在
        if (pin_type == CommunityInfo::AptPinTypeEnum::APT_PIN && room_number_.size() != 0)
        {
            for (const auto& key : pin_list_)
            {
                keys += room_number_ + "+" + key + ";";
            }            
        }
        else
        {
            for (const auto& key : pin_list_)
            {
                keys += key + ";";
            }
        }
    }

    //enduser创建的pin
    if (can_create_pin)
    {
        for (const auto& key : special_key_list_)
        {
            if (pin_type == CommunityInfo::AptPinTypeEnum::APT_PIN && room_number_.size() != 0)
            {
                keys += room_number_ + "+" + key + ";";
            }
            else
            {
                keys += key + ";";
            }
        }
    }

    return keys;
}

void UserAccessInfo::GetPinList(int pin_type, std::list<std::string> &list , int can_create_pin)
{
    if (pin_type == CommunityInfo::AptPinTypeEnum::APT_PIN)
    {
        for (auto &key : pin_list_)
        {
            std::string tmp = room_number_ + "+" + key;
            list.push_back(tmp);
        }            
    }
    else
    {
        for (auto &key : pin_list_)
        {
            list.push_back(key);
        }
    }
    
    if (can_create_pin)
    {
        for (auto &key : special_key_list_)
        {
            if (pin_type == CommunityInfo::AptPinTypeEnum::APT_PIN)
            {
                std::string tmp = room_number_ + "+" + key;
                list.push_back(tmp);
            }
            else
            {
                list.push_back(key);
            }
        }
    }    
} 

//办公获取pin接口
std::string UserAccessInfo::GetOfficePinString(int can_create_pin)
{
    std::string keys;

    for (auto &key : pin_list_)
    {
        keys += key + ";";
    }

    if (can_create_pin)
    {
        for (auto &key : special_key_list_)
        {
            keys += key + ";";
        }
    }
    return keys;
}

void UserAccessInfo::GetOfficePinList(std::list<std::string> &list , int can_create_pin)
{

    for (auto &key : pin_list_)
    {
        list.push_back(key);
    }
    
    if (can_create_pin)
    {
        for (auto &key : special_key_list_)
        {
            list.push_back(key);
        }
    }
} 

void UserAccessInfo::GetPinListForMonitor(std::list<std::string> &list)
{
    for (auto key : pin_list_)
    {
        list.push_back(key);
    }

    //app pin 不用保证唯一
    //if (special_key_.size() > 0)
    //{
        //list.push_back(special_key_);
    //}    
} 


void UserAccessInfo::GetRFList(std::list<std::string> &list)
{
    //pm创建的card
    for (const auto& key : pm_rf_list_)
    {
        list.push_back(key);
    }  

    //enduser创建的NFC和Bluetooth
    for (const auto& key : enduser_rf_list_)
    {
        list.push_back(key);
    }
}

void UserAccessInfo::SetRoomNumber(const std::string &str)
{
    room_number_ = str;
} 

std::string UserAccessInfo::GetRoomNumber()
{
    return room_number_;
}  

void UserAccessInfo::SetUnitID(uint32_t unit_id)
{
    unit_id_ = unit_id;
}   

uint32_t UserAccessInfo::GetUnitID()
{
    return unit_id_;
}

int UserAccessInfo::SetOpendoorInit()
{
    opendoor_data_init_ = 1;
    return 0;
}
int UserAccessInfo::IsOpendoorInit()
{
    return opendoor_data_init_;
}

std::string UserAccessInfo::CreateSpecialUUID(USER_TYPE type,uint32_t id)
{
    char uuid[64] = "";
    if (USER_TYPE::DELIVERY == type)
    {
        snprintf(uuid, sizeof(uuid), "D%09d", id);
    }
    else if (USER_TYPE::STAFF == type)
    {
        snprintf(uuid, sizeof(uuid), "S%09d", id);
    }
    return uuid;
}

USER_TYPE UserAccessInfo::DetectUserType(const std::string &user, std::string &real_user)
{
    if (user.size() > 1 && user[0] == 'D')
    {
        real_user = user[1];
        return USER_TYPE::DELIVERY;
    }
    else if (user.size() > 1 && user[0] == 'S')
    {
        real_user = user[1];
        return USER_TYPE::STAFF;
    }
    else
    {
        real_user = user;
        return USER_TYPE::ACCOUNT;
    }
    return USER_TYPE::USER_TYPE_NULL;
}

void UserAccessInfo::SetWebRelay(int web_relay)
{
    web_relay_ = web_relay;
}

int UserAccessInfo::GetWebRelay()
{
    return web_relay_;
}

void UserAccessInfo::SetFloor(const std::string &str)
{
    floor_ = str;
}

std::string UserAccessInfo::GetFloor()
{
    return floor_;
}

void UserAccessInfo::SetUnitUUID(const std::string &str)
{
    unit_uuid_ = str;
}

std::string UserAccessInfo::GetUnitUUID()
{
    return unit_uuid_;
}

void UserAccessInfo::SetSpecialFace(int special_face)
{
    special_face_ = special_face;
}

int UserAccessInfo::GetSpecialFace()
{
    return special_face_;
}

void UserAccessInfo::SetDBUUID(const std::string &str)
{
    db_uuid_ = str;
}

std::string UserAccessInfo::GetDBUUID()
{
    return db_uuid_;
}

void UserAccessInfo::SetNode(const std::string &str)
{
    node_ = str;
}

std::string UserAccessInfo::GetNode()
{
    return node_;
}

void UserAccessInfo::SetIDAccessInfo(const IDAccessInfo& id_access_info)
{
    id_access_info_.id_access_mode = id_access_info.id_access_mode;
    Snprintf(id_access_info_.id_access_run, sizeof(id_access_info_.id_access_run), id_access_info.id_access_run);
    Snprintf(id_access_info_.id_access_serial, sizeof(id_access_info_.id_access_serial), id_access_info.id_access_serial);
}

IDAccessInfo UserAccessInfo::GetIDAccessInfo()
{
    return id_access_info_;
}

void UserAccessInfo::SetSpecialIDAccessInfo(const IDAccessInfo& id_access_info)
{
    special_id_access_info_.id_access_mode = id_access_info.id_access_mode;
    Snprintf(special_id_access_info_.id_access_run, sizeof(special_id_access_info_.id_access_run), id_access_info.id_access_run);
    Snprintf(special_id_access_info_.id_access_serial, sizeof(special_id_access_info_.id_access_serial), id_access_info.id_access_serial);
}

IDAccessInfo UserAccessInfo::GetSpecialIDAccessInfo()
{
    return special_id_access_info_;
}

void UserAccessInfo::SetSpecialIDAccess(int is_special)
{
    special_id_access_ = is_special;
}

bool UserAccessInfo::isSpecialIDAccess()
{
    return special_id_access_ == 1;
}

void UserAccessInfo::AddLicensePlate(const Json::Value& license_plate)
{
    license_plate_list_.push_back(license_plate);
}

Json::Value UserAccessInfo::GetLicensePlateJson() const
{
    Json::Value license_plate_json(Json::arrayValue);
    for (const auto& key : license_plate_list_)
    {
        license_plate_json.append(key);
    }
    return license_plate_json;
}