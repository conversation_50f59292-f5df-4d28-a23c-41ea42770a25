#ifndef __USER_ACCESS_INFO_H__
#define __USER_ACCESS_INFO_H__
#include <string>
#include "AKCSMsg.h"
#include <memory>
#include <AkcsCommonDef.h>
#include "AkcsCommonSt.h"
#include "json/json.h"

typedef std::list<std::string> UserKeyList;
typedef std::list<uint32_t> UserAccessGroupIDList;
typedef std::list<Json::Value> UserLicensePlateList;


class UserAccessMetaInfo
{
public:
    UserAccessMetaInfo(  ){}
    ~UserAccessMetaInfo(){}
    void SetMeta(const std::string &meta)
    {
        meta_ = meta;
    }
    std::string GetMeta()
    {
        return meta_;
    }
    void SetUUID(const std::string &uuid)
    {
        uuid_ = uuid;
    }    
    std::string GetUUID()
    {
        return uuid_;
    }
    
private:
    std::string uuid_;
    std::string meta_;
};

typedef std::shared_ptr<UserAccessMetaInfo> UserAccessMetaInfoPtr;
typedef std::list<UserAccessMetaInfoPtr> UserAccessMetaInfoPtrList;


class UserAccessInfo:public UserAccessMetaInfo
{
public:
    UserAccessInfo(  USER_TYPE user_type);
    ~UserAccessInfo();

    void AddPmRf(const std::string &key);
    void AddEnduserRf(const std::string &key);
    void AddPin(const std::string &key);
    void AddSpecialPin(const std::string &key);
    void AddAccessGroupID(uint32_t id);
    void GetAccessGroupIDList(UserAccessGroupIDList &list);
    void SetDBID(uint32_t db_id);  
    uint32_t GetDBID();
    USER_TYPE GetUserType();
    void SetRole(uint32_t role); 
    uint32_t GetRole();
    void SetName(const std::string &name);
    std::string GetName();
    void SetFaceMd5(const std::string &face_md5);
    std::string GetFaceMd5();
    void SetFaceUrl(const std::string &face_url);
    std::string GetFaceUrl(const std::string &mac);
    std::string GetRfString();
    std::string GetEnduserRfString();
    std::string GetPinString(int    pin_type, int can_create_pin = 0, int enable_private_access = 1);
    void GetPinList(int pin_type, std::list<std::string> &list , int can_create_pin = 0);
    void GetPinListForMonitor(std::list<std::string> &list);
    void GetRFList(std::list<std::string> &list);
    void SetRoomNumber(const std::string &str);
    std::string GetRoomNumber();
    void SetFloor(const std::string &str);
    std::string GetFloor();    
    void SetUnitUUID(const std::string &str);
    std::string GetUnitUUID();
    void SetUnitID(uint32_t unit_id);
    uint32_t GetUnitID();
    int SetOpendoorInit();
    int IsOpendoorInit();
    std::string GetOfficePinString(int can_create_pin);
    void GetOfficePinList(std::list<std::string> &list , int can_create_pin);
	std::string CreateSpecialUUID(USER_TYPE        type, uint32_t id);
    static USER_TYPE DetectUserType(const       std::string &user, std::string &real_user);
    void SetWebRelay(int web_relay);
    int GetWebRelay();
    void SetSpecialFace(int special_face   );
    int GetSpecialFace();
    void SetDBUUID(const std::string &str);
    std::string GetDBUUID();
    void SetNode(const std::string &str);
    std::string GetNode();
    void SetIDAccessInfo(const IDAccessInfo& id_access_info);
    IDAccessInfo GetIDAccessInfo();
    void SetSpecialIDAccessInfo(const IDAccessInfo& id_access_info);
    IDAccessInfo GetSpecialIDAccessInfo();
    void SetSpecialIDAccess(int is_special);
    bool isSpecialIDAccess();
    void AddLicensePlate(const Json::Value& license_plate);
    Json::Value GetLicensePlateJson() const;
private:
    UserKeyList pm_rf_list_;          //pm创建的rf
    UserKeyList enduser_rf_list_;  //enduser的NFC和Bluetooth
    UserKeyList pin_list_;         //pm创建的pin
    UserKeyList special_key_list_; //enduser创建的pin
    UserAccessGroupIDList access_group_id_list_;
    std::string name_;
    std::string face_md5_;
    std::string face_url_;
    uint32_t db_id_;
    uint32_t unit_id_;
    std::string room_number_;
    USER_TYPE user_type_;
    int role_;
    int opendoor_data_init_;
    int web_relay_;
    std::string unit_uuid_;
    std::string floor_;
    int special_face_; //是否为用户创建的人脸
    std::string db_uuid_;
    std::string node_;
    IDAccessInfo id_access_info_;
    IDAccessInfo special_id_access_info_;
    int special_id_access_; //是否为用户创建的id access
    UserLicensePlateList license_plate_list_;
};

typedef std::shared_ptr<UserAccessInfo> UserAccessInfoPtr;
typedef std::map<std::string/*uuid*/, UserAccessInfoPtr> UserAccessInfoPtrMap;
typedef UserAccessInfoPtrMap::iterator UserAccessInfoPtrMapIter;
typedef std::list<UserAccessInfoPtr> UserAccessInfoPtrList;


#endif 
