#include "config_fdfs_uploader.h"
#include "tracker_types.h"
#include "storage_client.h"
#include "AkLogging.h"

int ConfigFdfsUploader::GetFileMeta(const std::string& remote_filename, std::string& meta_value)
{
    if(remote_filename.size() == 0 || tracker_server_ == nullptr)
    {
        return -1;
    }

    int meta_count = 0;    
    FDFSMetaData *meta_list = nullptr;
    int result = 0;

    size_t n = remote_filename.find("M00");
    if (std::string::npos == n)
    {
        AK_LOG_WARN << "file_remote is [" << remote_filename << "], format is error";
        return -1;
    }
    //获取到 M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580.cfg
    std::string file_remote = remote_filename.substr(n);
              
    std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
    if((result = storage_get_metadata(tracker_server_, \
    			NULL, group_name_.c_str(), file_remote.c_str(), \
    			&meta_list, &meta_count)) == 0)
    {
        if(meta_list != nullptr)
        {
            meta_value = meta_list[0].value;    
            free(meta_list);
        }
    }            

    return result;
}

int ConfigFdfsUploader::DeleteFile(const std::string& remote_filename, const std::string &local_tag)
{
    if(remote_filename.size() == 0 || tracker_server_ == nullptr)
    {
        return -1;
    }
    int result = 0;

    //分离出group跟file_name
    size_t n = remote_filename.find("group");
    if (std::string::npos == n)
    {
        AK_LOG_WARN << "file_remote is [" << remote_filename << "], format is error";
        return -1;
    }
    //获取到 group1/M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    std::string file = remote_filename.substr(n);
    n = file.find_first_of("/M");
    //获取到 M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    std::string file_remote = file.substr(n + 1);
    std::string group_remote = file.substr(0, n);
    
    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        result = storage_delete_file(tracker_server_, NULL, group_remote.c_str(), file_remote.c_str());
    }
    if (result == 0)
    {
        AK_LOG_INFO << "delete file " << remote_filename << " succeed. local tag:" << local_tag ;
    }
    else
    {
        //与tracker-ser断开链接了,重新连接
        if ((result = RefreshTrackerConn()) != 0)
        {
            AK_LOG_WARN << "Refresh, error no:" << result << " error info:" << STRERROR(result);
        }
        else
        {
            std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
            result = storage_delete_file(tracker_server_, NULL, group_remote.c_str(), file_remote.c_str());
        }
    }

    if(result != 0)
    {
        AK_LOG_WARN << "delete file " << remote_filename << " fail, error no:" << result << "error info:" << STRERROR(result);
    }
    return result;
}
