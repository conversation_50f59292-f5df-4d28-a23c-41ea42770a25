#ifndef __CONFIG_FDFS_UPLOADER_H__
#define __CONFIG_FDFS_UPLOADER_H__

#include "fdfs_uploader.h"

class ConfigFdfsUploader : public FdfsUploader
{
public:
    int GetFileMeta(const std::string& remote_filename, std::string& meta_value);
    //local_tag本地的标识 只用于日志打印，才能知道删除这个文件 本地对应的什么
    int DeleteFile(const std::string& remote_filename, const std::string &local_tag);
};

#endif // __CONFIG_FDFS_UPLOADER_H__