#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "OfficeConfigHandle.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "RfKeyControl.h"
#include "PrivateKeyControl.h"
#include "dbinterface/office/OfficeInfo.h"
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "DeviceSetting.h"
#include "CommunityMng.h"
#include "OfficeDevUser.h"
#include "OfficeDevSchedule.h"
#include "OfficeDevConfig.h"
#include "AkcsCommonDef.h"
#include "OfficeDevContact.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityUnit.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


#define CHECK_NODE_IS_NULL(node) \
    do { \
        if (node.length() <=0 ) {AK_LOG_WARN << "CHECK_NODE_IS_NULL is true";return;} \
       } while(0)

#define CHECK_UNIT_IS_NULL(department_id) \
    do { \
        if (department_id <=0 ){AK_LOG_WARN << "CHECK_UNIT_IS_NULL is true";return;}\
       }while(0)



OfficeConfigHandle::OfficeConfigHandle(uint32_t office_id, uint32_t department_id, const std::string &node, const std::vector<std::string> mac_list)
                    :office_id_(office_id),department_id_(department_id),node_(node)
{
    app_list_init_ = 0;
    mac_list_.assign(mac_list.begin(), mac_list.end());
    
    dbinterface::CommunityUnit::GetCommunityUnitsByMngID(office_id_, g_unit_list_);
    g_office_info_ = std::make_shared<OfficeInfo>(office_id_);

    g_mng_sip_type_ = 0;
    g_rtp_confuse_ = 0;
    g_mng_rtsp_type_ = 0;
    dbinterface::Account::GetMngTransType(office_id_, g_mng_sip_type_, g_rtp_confuse_, g_mng_rtsp_type_);
    
    dbinterface::OfficePersonalAccount::GetAllAccountList(office_id_, office_all_node_list_, unit_node_map_, all_account_map_);

    devices_contorl_.Init(office_id);
    config_context_ = std::make_shared<OfficeConfigContext>();
    config_context_->SetDevContorl(&devices_contorl_);
    config_context_->Init(office_id, g_office_info_->UUID()); 
}

OfficeConfigHandle::~OfficeConfigHandle()
{
    
}

void OfficeConfigHandle::InitNodeDev()
{
    CHECK_NODE_IS_NULL(node_);
    if (node_dev_list_.size() == 0)
    {
        node_dev_list_ = devices_contorl_.GetNodeDeviceInGlobal(node_);
    }
}

void OfficeConfigHandle::InitUnitDev()
{
    CHECK_UNIT_IS_NULL(department_id_);
    if (unit_dev_list_.size() == 0)
    {
        unit_dev_list_ = devices_contorl_.GetUnitDeviceInGlobal(department_id_);
    }

}

void OfficeConfigHandle::UpdateNodeDevConfig()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    if (node_dev_list_.size() == 0)
    {
        AK_LOG_INFO << "Node Devices List is null, no need update config file!";
        return;
    }
    std::string config_root_path = GetCommunityPersonalDownloadConfigPath(office_id_, department_id_, node_.c_str());
    
    OfficeDevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_mng_rtsp_type_);
    config.SetOfficeInfo(g_office_info_);
    config.SetContext(config_context_);   
    if ( config.WriteDevListFiles(node_dev_list_) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
}

void OfficeConfigHandle::UpdateNodeDevConfig(uint32_t office_id, uint32_t department_id, const std::string &node, const OfficeDevList &node_dev_list)
{
    if (node_dev_list.size() == 0)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }
    std::string config_root_path = GetCommunityPersonalDownloadConfigPath(office_id, department_id, node.c_str());
    OfficeDevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_mng_rtsp_type_);
    config.SetOfficeInfo(g_office_info_);
    config.SetContext(config_context_);   
    if (config.WriteDevListFiles(node_dev_list) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
}


void OfficeConfigHandle::UpdateUnitDevConfig()
{
    CHECK_UNIT_IS_NULL(department_id_);
    InitUnitDev();
    if (unit_dev_list_.size() == 0)
    {
        return;
    }    
    std::string config_root_path = GetCommunityUnitPublicDownloadConfigPath(office_id_, department_id_);
    OfficeDevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_mng_rtsp_type_);
    config.SetOfficeInfo(g_office_info_);
    config.SetContext(config_context_);   
    if (config.WriteDevListFiles(unit_dev_list_) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }  
}

void OfficeConfigHandle::UpdateUnitDevConfig(uint32_t office_id, uint32_t department_id, const OfficeDevList &unit_dev_list)
{
    if (unit_dev_list.size() == 0)
    {
        return;
    } 

    std::string config_root_path = GetCommunityUnitPublicDownloadConfigPath(office_id, department_id);
    OfficeDevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_mng_rtsp_type_);
    config.SetOfficeInfo(g_office_info_);    
    config.SetContext(config_context_);   
    if (config.WriteDevListFiles(unit_dev_list) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }   
}


void OfficeConfigHandle::UpdatePubDevConfig()
{
    const OfficeDevList &pub_dev_list = devices_contorl_.GetPubDeviceInGlobal();
    
    std::string config_root_path = GetCommunityPublicDownloadConfigPath(office_id_);      
    OfficeDevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_mng_rtsp_type_);
    config.SetOfficeInfo(g_office_info_);
    config.SetContext(config_context_);   
    if (config.WriteDevListFiles(pub_dev_list) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }  
}

void OfficeConfigHandle::UpdateNodeDevContactList()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    InitUnitDev();

    const OfficeDevList &pub_dev_list = devices_contorl_.GetPubDeviceInGlobal();
    UpdateNodeDevContactList(office_id_, department_id_, node_, node_dev_list_, unit_dev_list_, pub_dev_list);
}



void OfficeConfigHandle::UpdateNodeDevContactList(uint32_t office_id, uint32_t department_id, const std::string &node,
   const OfficeDevList& node_dev_list, const OfficeDevList& unit_dev_list, const OfficeDevList& pub_dev_list)
{
    if (node_dev_list.size() == 0)
    {
        return;
    }
    
    std::string contact_root_path_ = GetCommunitySaveContactListDir(office_id, department_id, node.c_str(), csmain::COMMUNITY_DEVICE_TYPE_PERSONAL);

    auto it = all_account_map_.find(node);
    if (it == all_account_map_.end())
    {
        return;
    }
    OfficeAccount account = it->second;
    
    for(auto cur_dev : node_dev_list)
    {
        OfficeDevContact contact(contact_root_path_, g_office_info_);
        contact.SetContext(config_context_);   
        contact.UpdateOfficeContactFile(cur_dev, node_dev_list, account, pub_dev_list, unit_dev_list);
    } 
    
}

void OfficeConfigHandle::UpdateUnitDevContactList()
{
    CHECK_UNIT_IS_NULL(department_id_);
    InitUnitDev();
    if (unit_dev_list_.size() == 0)
    {
        return;
    }   

    auto iter = unit_node_map_.find(department_id_);
    if(iter == unit_node_map_.end())
    {
        AK_LOG_WARN << "Wirte unit contact, but unit id=" << department_id_ << " can not found nodes" ;
        return;
    }
    OfficeAccountList account_list = iter->second;

    //获取文件路径
    std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(office_id_, department_id_);
    for(auto cur_dev : unit_dev_list_)
    {
        OfficeDevContact contact(contact_root_path, g_office_info_);
        contact.SetContext(config_context_);   
        contact.UpdateOfficePublicContactFile(cur_dev, account_list, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
    }
}

void OfficeConfigHandle::UpdatePubDevContactList()
{
    const OfficeDevList &pub_dev_list = devices_contorl_.GetPubDeviceInGlobal();
    //获取文件路径
    std::string contact_root_path = GetCommunityPublicDownloadContactPath(office_id_);
    for(const auto &cur_dev : pub_dev_list)
    {
        OfficeDevContact contact(contact_root_path, g_office_info_);
        contact.SetContext(config_context_);   
        contact.UpdateOfficePublicContactFile(cur_dev, office_all_node_list_, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC);
    }
}

void OfficeConfigHandle::UpdateUnitAllNodeDevContactList()
{
    InitUnitDev();

    auto iter = unit_node_map_.find(department_id_);
    if(iter == unit_node_map_.end())
    {
        AK_LOG_WARN << "Wirte unit contact, but unit id=" << department_id_ << " can not found nodes" ;
        return;
    }
    const OfficeAccountList &account_list = iter->second;

    const OfficeDevList &pub_dev_list = devices_contorl_.GetPubDeviceInGlobal();
    for (auto& node : account_list)
    {
        OfficeDevList dev_list = config_context_->GetNodeDeviceInGlobal(node.account);
        UpdateNodeDevContactList(office_id_, department_id_, node.account, dev_list, unit_dev_list_, pub_dev_list);
    }
    return;    
}

void OfficeConfigHandle::UpdateCommunityAllNodeDevContactList()
{
    const OfficeDevList &pub_dev_list = devices_contorl_.GetPubDeviceInGlobal();
    for (auto& unit : g_unit_list_)
    {   
        auto iter = unit_node_map_.find(unit.unit_id);
        if(iter == unit_node_map_.end())
        {
            AK_LOG_WARN << "Wirte unit contact, but unit id=" << unit.unit_id << " can not found nodes" ;
            return;
        }
        const OfficeAccountList &account_list = iter->second;
        
        for (auto& node : account_list)
        {
            OfficeDevList unit_dev_list = config_context_->GetUnitDeviceInGlobal(unit.unit_id);
            OfficeDevList dev_list = config_context_->GetNodeDeviceInGlobal(node.account);
            UpdateNodeDevContactList(office_id_, unit.unit_id, node.account, dev_list, unit_dev_list, pub_dev_list);
        }        
    }      
}

void OfficeConfigHandle::UpdateCommunityAllUnitDevContactList()
{
    for (auto& unit : g_unit_list_)
    {   
        OfficeDevList unit_dev_list = config_context_->GetUnitDeviceInGlobal(unit.unit_id);
        std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(office_id_, unit.unit_id);

        auto iter = unit_node_map_.find(unit.unit_id);
        if(iter == unit_node_map_.end())
        {
            AK_LOG_WARN << "Wirte unit contact, but unit id=" << unit.unit_id << " can not found nodes" ;
            return;
        }
        OfficeAccountList account_list = iter->second;

        for(auto cur_dev : unit_dev_list)
        {
            OfficeDevContact contact(contact_root_path, g_office_info_);
            contact.SetContext(config_context_);            
            contact.UpdateOfficePublicContactFile(cur_dev, account_list, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
        }
    }      
}

void OfficeConfigHandle::UpdateCommunityAllUnitDevConfig()
{
    for (auto& unit : g_unit_list_)
    {   
        OfficeDevList unit_dev_list = config_context_->GetUnitDeviceInGlobal(unit.unit_id);
        std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(office_id_, unit.unit_id);
        UpdateUnitDevConfig(office_id_, unit.unit_id, unit_dev_list);
    }      
}

void OfficeConfigHandle::UpdateUnitAllNodeDevConfig()
{
    InitUnitDev();

    auto iter = unit_node_map_.find(department_id_);
    if(iter == unit_node_map_.end())
    {
        AK_LOG_WARN << " unit id=" << department_id_ << " can not found nodes" ;
        return;
    }
    const OfficeAccountList & account_list = iter->second;

    for (auto& node : account_list)
    {
        OfficeDevList dev_list = config_context_->GetNodeDeviceInGlobal(node.account);
        UpdateNodeDevConfig(office_id_, department_id_, node.account, dev_list);
    }
    return;    
}

void OfficeConfigHandle::UpdateCommunityAllNodeDevConfig()
{
    for (auto& unit : g_unit_list_)
    {   
        auto iter = unit_node_map_.find(unit.unit_id);
        if(iter == unit_node_map_.end())
        {
            AK_LOG_WARN << " unit id=" << unit.unit_id << " can not found nodes" ;
            return;
        }
        const OfficeAccountList &account_list = iter->second;

        for (auto& node : account_list)
        {
            OfficeDevList dev_list = config_context_->GetNodeDeviceInGlobal(node.account);
            UpdateNodeDevConfig(office_id_, unit.unit_id, node.account, dev_list);
        }

    }
    return;    
}

void OfficeConfigHandle::UpdateMacDevConfig(const std::string& mac)
{
    OfficeDevList dev_list = devices_contorl_.GetMacDeviceInGlobal(mac);
    std::string config_root_path;
    if(node_.size() > 0)
    {
        config_root_path = GetCommunityPersonalDownloadConfigPath(office_id_, department_id_, node_.c_str());
    }
    else if(department_id_ > 0)
    {
        config_root_path = GetCommunityUnitPublicDownloadConfigPath(office_id_, department_id_);
    }
    else
    {
        config_root_path = GetCommunityPublicDownloadConfigPath(office_id_);
    }
  
    OfficeDevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_mng_rtsp_type_);
    config.SetContext(config_context_);    
    config.SetOfficeInfo(g_office_info_);     
    if (config.WriteDevListFiles(dev_list) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
}


void OfficeConfigHandle::UpdateMacUser(const std::string &mac)
{
    OfficeDevList dev_list = devices_contorl_.GetMacDeviceInGlobal(mac);
    OfficeDevUser user(g_office_info_);
    user.UpdateMetaData(dev_list);
}

void OfficeConfigHandle::UpdateCommunityAllDevUser()
{
    const OfficeDevList &dev_list = devices_contorl_.GetAllDeviceInGlobal();
    OfficeDevUser user(g_office_info_);
    user.UpdateMetaData(dev_list);
}

//更新员工/管理员公共权限组内设备的User
void OfficeConfigHandle::UpdateNodePubDevUser()
{
    if (node_.size() == 0)
    {
        AK_LOG_INFO << "UpdateNodePubDevUser node is null";
        return;
    }
    
    std::vector<std::string> accounts;
    std::set<std::string> pub_mac_set;
    accounts.push_back(node_);

    OfficeDevUser user(g_office_info_);
    user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
}

void OfficeConfigHandle::UpdateNodeUser()
{
    if (node_.size() == 0)
    {
        AK_LOG_INFO << "UpdateNodeUser node is null";
        return;
    }
    std::set<std::string> pub_mac_set;
    std::set<std::string> user_mac_set;
    std::vector<std::string> accounts;
    accounts.push_back(node_);
    
    OfficeDevUser user(g_office_info_);
    //更新用户关联的权限组的设备user
    user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
    //更新用户关联的家庭设备user
    user.UpdateUserDevMetaByAccount(accounts, user_mac_set);
}

//以设备为粒度
void OfficeConfigHandle::UpdateDevSchedule(const std::string &mac)
{
    OfficeDevList dev_list = devices_contorl_.GetMacDeviceInGlobal(mac);
    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(dev_list);
}


//添加用户就会给住户设备添加一个权限组
void OfficeConfigHandle::UpdateNodeDevSchedule()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    if (node_dev_list_.size() == 0 )
    {
        AK_LOG_INFO << "Node Devices List is null, no need update user file!";
        return;
    }

    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(node_dev_list_);
}

//更新楼栋/部门下的设备权限组
void OfficeConfigHandle::UpdateUnitDevSchedule()
{
    CHECK_UNIT_IS_NULL(department_id_);
    InitUnitDev();
    if (unit_dev_list_.size() == 0 )
    {
        AK_LOG_INFO << "Node Devices List is null, no need update user file!";
        return;
    }

    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(unit_dev_list_);
}

void OfficeConfigHandle::UpdatePubDevSchedule()
{
    const OfficeDevList &pub_dev_list = devices_contorl_.GetPubDeviceInGlobal();
    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(pub_dev_list);
}

void OfficeConfigHandle::UpdateCommunityAllDevSchedule()
{
    const OfficeDevList &dev_list = devices_contorl_.GetAllDeviceInGlobal();
    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(dev_list);  
}


