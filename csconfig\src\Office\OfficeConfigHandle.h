#ifndef __OFFICE_CONFIG_HANDLE__
#define __OFFICE_CONFIG_HANDLE__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "CommunityMng.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "DevConfig.h"
#include "DevContact.h"
#include "OfficeConfigHandleDevices.h"
#include "OfficeUpdateConfigContext.h"


//单元公共设备下的Account
typedef std::map<uint32_t/*unitid*/, OfficeAccountList> DepartmentAccountMap;
typedef DepartmentAccountMap::const_iterator DepartmentAccountMapIter;

class OfficeConfigHandle
{
public:
    OfficeConfigHandle(uint32_t office_id, uint32_t department_id, const std::string &node, const std::vector<std::string> macs);
    ~OfficeConfigHandle();

    void UpdateNodeDevConfig();
    void UpdateUnitDevConfig();
    void UpdatePubDevConfig();
    void UpdateNodeDevContactList();
    void UpdateUnitDevContactList();
    void UpdatePubDevContactList();


    //更新单元下所有用户设备得联系人
    void UpdateUnitAllNodeDevContactList();
    //更新社区下所有用户设备得联系人
    void UpdateCommunityAllNodeDevContactList();
    //更新单元下所有用户设备得配置文件
    void UpdateUnitAllNodeDevConfig();
    //更新社区下所有单元公共设备得联系人
    void UpdateCommunityAllUnitDevContactList();
    //更新社区下所有用户设备的配置文件
    void UpdateCommunityAllNodeDevConfig();

    //更新社区下所有单元公共设备的配置
    void UpdateCommunityAllUnitDevConfig();

    //更新单个设备配置
    void UpdateMacDevConfig(const std::string& mac);

    //更新user数据
    void UpdateMacUser(const std::string &mac);
    void UpdateCommunityAllDevUser();
    void UpdateNodePubDevUser();
    void UpdateNodeUser();
    
    //schedule
    //以设备为粒度
    void UpdateDevSchedule(const std::string &mac);
    void UpdateNodeDevSchedule();
    void UpdateUnitDevSchedule();
    void UpdatePubDevSchedule();
    void UpdateCommunityAllDevSchedule();

    /*联动更新，更新相关联的设备*/
    void UpdateNodeConfigEvent()
    {
        UpdateNodeDevConfig();
        UpdateUnitDevConfig();
        UpdatePubDevConfig();    
    }
    void UpdateNodeContactEvent()
    {
        UpdateNodeDevContactList();
        UpdateUnitDevContactList();
        UpdatePubDevContactList();    
    }
    //更新项目下所有设备的联系人
    void UpdateOfficeContactEvent()
    {
        UpdatePubDevContactList();
        UpdateCommunityAllUnitDevContactList();
        UpdateCommunityAllNodeDevContactList();
    }
    
    bool IsNewOffice()
    {
        return g_office_info_->IsNew();
    }
    int InitSuccess()
    {
        return g_office_info_->InitSuccess();
    }
private:
    void InitNodeDev();
    void InitUnitDev();
    void InitAllAppList();/*初始化所有app信息*/
    void UpdateNodeDevContactList(uint32_t office_id, uint32_t department_id, const std::string &node,
       const OfficeDevList& node_dev_list, const OfficeDevList& unit_dev_list,const OfficeDevList& pub_dev_list);
    void UpdateNodeDevConfig(uint32_t office_id, uint32_t department_id, const std::string &node, const OfficeDevList &node_dev_list);
    void UpdateUnitDevConfig(uint32_t office_id, uint32_t department_id, const OfficeDevList& unit_dev_list);

    std::string node_;
    uint32_t office_id_;
    uint32_t department_id_;
    int app_list_init_;
    int g_rtp_confuse_;/*g_开头为全局初始化的变量*/
    int g_mng_sip_type_;/*g_开头为全局初始化的变量*/
    int g_mng_rtsp_type_;/*g_开头为全局初始化的变量*/
    OfficeInfoPtr g_office_info_;/*g_开头为全局初始化的变量*/
    //每个函数需要用到下面的dev,就要自己调用,构造函数没有初始化对应值
    OfficeDevList unit_dev_list_;/*当前操作单元的所有公共设备*/
    OfficeDevList node_dev_list_;/*当前操作的node的所有设备*/
    OfficeDevList pub_dev_list_;/*当前操作所有最外层公共设备*/
    
    OfficeAccountUnitIDMap unit_node_map_;
    OfficeAccountList office_all_node_list_;
    std::vector<std::string> mac_list_;

    std::vector<COMMUNIT_UNIT_INFO> g_unit_list_;/*g_开头为全局初始化的变量*/

    OfficeConfigHandleDevices devices_contorl_;
    OfficeConfigContextPtr config_context_;
    OfficeAccountMateMap all_account_map_;
};


#endif


