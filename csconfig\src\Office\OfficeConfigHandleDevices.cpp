#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "OfficeConfigHandleDevices.h"
#include "PersonnalDeviceSetting.h"
#include <boost/algorithm/string.hpp>



OfficeConfigHandleDevices::OfficeConfigHandleDevices()
{

}

void OfficeConfigHandleDevices::Init(uint32_t office_id)
{
    dbinterface::OfficeDevices::GetAllOfficeDevListByProjectID(office_id, pub_dev_list, node_dev_map_, 
        unit_dev_map_, mng_dev_list, pub_unit_all_dev_list, mac_dev_map_, all_dev_list);
}


/*最外围公共设备*/
const OfficeDevList& OfficeConfigHandleDevices::GetPubDeviceInGlobal()
{    
    return pub_dev_list;    
}

/*单元公共设备*/ 
OfficeDevList OfficeConfigHandleDevices::GetUnitDeviceInGlobal(uint32_t unit_id)
{
    OfficeDevList dev_list;
    const auto &unit_dev_it = unit_dev_map_.equal_range(unit_id);
    for (auto it = unit_dev_it.first; it != unit_dev_it.second; ++it) {
        dev_list.push_back(it->second);
    }         
    return std::move(dev_list);    
}


OfficeDevList OfficeConfigHandleDevices::GetNodeDeviceInGlobal(const std::string &node)
{
    OfficeDevList dev_list;
    const auto &dev_it = node_dev_map_.equal_range(node);
    for (auto it = dev_it.first; it != dev_it.second; ++it) {
        dev_list.push_back(it->second);
    }         
    return std::move(dev_list);    
}


OfficeDevList OfficeConfigHandleDevices::GetMacDeviceInGlobal(const std::string &mac)
{
    OfficeDevList dev_list;
    const auto &dev_it = mac_dev_map_.find(mac);
    if(dev_it != mac_dev_map_.end())
    {
        dev_list.push_back(dev_it->second);
    }
    return std::move(dev_list);     
}


OfficeDevList OfficeConfigHandleDevices::GetUnitDeviceInGlobal(std::set<int> unit_set)
{
    OfficeDevList dev_list;
    for (auto unit_id : unit_set)
    {
        const auto &unit_dev_it = unit_dev_map_.equal_range(unit_id);
        for (auto it = unit_dev_it.first; it != unit_dev_it.second; ++it) {
            dev_list.push_back(it->second);
        }  
    }
       
    return std::move(dev_list); 
}

const OfficeDevList& OfficeConfigHandleDevices::AllMngDeviceSetting()
{
    return mng_dev_list;  
}

const OfficeDevList& OfficeConfigHandleDevices::GetAllPubUnitDeviceInGlobal()
{
    return pub_unit_all_dev_list;      
}

const OfficeDevList& OfficeConfigHandleDevices::GetAllDeviceInGlobal()
{
    return all_dev_list;      
}

