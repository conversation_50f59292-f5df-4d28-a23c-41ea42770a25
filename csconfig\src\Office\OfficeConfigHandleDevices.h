#ifndef __OFFICE_CONFIG_DEVICES_HANDLE__
#define __OFFICE_CONFIG_DEVICES_HANDLE__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "CommunityMng.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "DeviceControl.h"
#include "DeviceSetting.h"



class OfficeConfigHandleDevices
{
public:
    OfficeConfigHandleDevices();
    ~OfficeConfigHandleDevices()
    {
    }

    void Init(uint32_t office_id);

    
    /*最外围公共设备*/
    const OfficeDevList& GetPubDeviceInGlobal();
    /*单元公共设备*/ 
    OfficeDevList GetUnitDeviceInGlobal(uint32_t unit_id);
    OfficeDevList GetNodeDeviceInGlobal(const std::string &node);
    OfficeDevList GetUnitDeviceInGlobal(std::set<int> unit_set);
    const OfficeDevList& AllMngDeviceSetting();
    const OfficeDevList& GetAllDeviceInGlobal();
    const OfficeDevList& GetAllPubUnitDeviceInGlobal();
    OfficeDevList GetMacDeviceInGlobal(const std::string &mac);
private:
    //全部的社区设备数据
    OfficeDevList pub_dev_list;
    OfficeDevNodeMap node_dev_map_;
    OfficeDevUnitMap unit_dev_map_;
    OfficeDevList mng_dev_list;
    OfficeDevList pub_unit_all_dev_list;
    OfficeDevMacMap mac_dev_map_;
    OfficeDevList all_dev_list;
};

 
#endif


