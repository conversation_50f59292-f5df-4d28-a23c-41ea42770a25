#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include "OfficeFileUpdateControl.h"
#include <evpp/evnsq/message.h>
#include "OfficeCsmainMsgHandle.h"
#include "evpp/event_loop.h"
#include "AkcsMonitor.h"
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "GroupMsgMng.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "AkcsCommonDef.h"
#include "AK.Server.pb.h"
#include "AkcsWebMsgSt.h"
#include "OfficeDevUser.h"
#include "CachePool.h"
#include "AKCSView.h"
#include "AkcsCommonDef.h"
#include "CommonHandle.h"
#include "AK.Adapt.pb.h"
#include "AkcsWebPduBase.h"
#include "OfficeUnixSocketControl.h"

extern const char* g_redis_db_userdetail;
OfficeCsmainMsgHandle* OfficeCsmainMsgHandle::office_csmain_handle_ = nullptr;

OfficeCsmainMsgHandle* OfficeCsmainMsgHandle::Instance()
{
    if (!office_csmain_handle_)
    {
        office_csmain_handle_ = new OfficeCsmainMsgHandle();
    }
    return office_csmain_handle_;
}


OfficeCsmainMsgHandle::OfficeCsmainMsgHandle()
{
    
}

OfficeCsmainMsgHandle::~OfficeCsmainMsgHandle()
{

}

void OfficeCsmainMsgHandle::UpdateMacConfigByCsmain(void* msg_buf, unsigned int len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "UpdateMacConfigByCsmain The param is NULL";
        return;
    }

    AK::Server::P2PMainDevConfigRewriteMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    if(CommonHandle::CheckIpchangeRequest(msg, project::OFFICE) != 0)
    {
        return;
    }

    int changetype = msg.type();
    OfficeDevPtr dev = nullptr;
    dbinterface::OfficeDevices::GetMacDev(msg.mac(), dev);
    if (!dev)
    {
        AK_LOG_WARN << "Devices is not exist, mac=" <<  msg.mac();
        return;
    }
    AK_LOG_INFO << "Office UpdateMacConfigByCsmain mac=" <<  dev->mac << " changetype=" << changetype;
    std::string strmac = dev->mac;
    vector<std::string> mac = {strmac};
    switch (changetype)
    {
        case CSMAIN_UPDATE_CONFIG_IP_CHANGE://ip变化
        {
            if(CGroupMsgMng::Instance()->IpChangeMacFilter(msg.mac()))
            {
                AK_LOG_INFO << "Office IpChangeMacFilter mac:"<<msg.mac();
                break;
            }
            if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_PUB_DEV_IP_CHANGE, dev->office_id, dev->unit_id, dev->node, mac);
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_UNIT_DEV_IP_CHANGE, dev->office_id, dev->unit_id, dev->node, mac);
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_DEV_IP_CHANGE, dev->office_id, dev->unit_id, dev->node, mac);
            }

            break;
        }
        case CSMAIN_UPDATE_CONFIG_MAINTANCE://运维的接口
        {
            if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_PUB_DEV_MAINTANCE, dev->office_id, dev->unit_id, dev->node, mac);
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_UNIT_DEV_MAINTANCE, dev->office_id, dev->unit_id, dev->node, mac);
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_DEV_MAINTANCE, dev->office_id, dev->unit_id, dev->node, mac);
            }

            break;
        }
        case CSMAIN_UPDATE_CONFIG_UPGRADE://版本升级
        {
            if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_PUB_DEV_UPGRADE, dev->office_id, dev->unit_id, dev->node, mac);
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_UNIT_DEV_UPGRADE, dev->office_id, dev->unit_id, dev->node, mac);
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
            {
                OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(CSMAIN_OFFICE_DEV_UPGRADE, dev->office_id, dev->unit_id, dev->node, mac);
            }

            break;
        }            
    }    
}

void OfficeCsmainMsgHandle::UpdateDevAccountConfigByCsmain(void* msg_buf, unsigned int len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "UpdateMacConfigByCsmain The param is NULL";
        return;
    }

    AK::Server::P2PMainAccountConfigRewriteMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    int changetype = msg.type();
    int office_id = msg.manager_id();
    int unit_id = msg.unit_id();
    std::string account = msg.account();
    std::string node = msg.node();
    std::string main_site = msg.main_site();

    int new_type = 0;

    switch (changetype)
    {
        case CSMAIN_UPDATE_CONFIG_RF_CHANGE:
        {
            new_type = CSMAIN_OFFICE_ACCOUNT_NFC_UPDATE;
            break;
        }
    }

    std::vector<std::string> mac = {};
    //更新用户数据版本
    dbinterface::ProjectUserManage::UpdataDataVersionByAccount(account);
    AK_LOG_INFO << "UpdateAccountConfig Office Account: Account=" <<  account;
    OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(new_type, office_id, unit_id, node, mac);
    GetAKCSViewInstance()->NotifyAppRefreshConfig(account, project::OFFICE);
}

void OfficeCsmainMsgHandle::DevWriteUserinfoReq(void* msg_buf, unsigned int len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "UpdateMacConfigByCsmain The param is NULL";
        return;
    }

    AK::Server::P2PMainRequestWriteUserinfo msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    if(CommonHandle::CheckUserInfoRequest(msg, project::OFFICE) != 0)
    {
        return;
    }
	
	if(CGroupMsgMng::Instance()->UserInfoMacFilter(msg.mac()))
    {
        AK_LOG_INFO << "Office UserInfoMacFilter mac:"<<msg.mac();
        return;
    }
    
    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail);
    if (cache_conn)
    {
        cache_conn->del(msg.accounts_key());
        cache_mng->RelCacheConn(cache_conn);
    }
    uint64_t msg_time = msg.timestamp();
    std::time_t t = std::time(0);
    if (t - msg_time > 60)
    {
        AK_LOG_INFO << "Office devices request user handle time more than 60s.";
    }
    std::string uuids = msg.uuids();
    uint64_t traceid = msg.msg_traceid();
    std::string mac = msg.mac();
    
    OfficeDevPtr dev = nullptr;
    dbinterface::OfficeDevices::GetMacDev(mac, dev);
    if (!dev)
    {
        AK_LOG_WARN << "Office DevWriteUserinfoReq is not exist, mac=" <<  mac;
        return;
    }

    AK_LOG_INFO << "Office mac:" << mac << " request user info, user:" << uuids << " traceid:" << traceid;
    
    UserUUIDList list;
    SplitString(uuids, ";", list);
    OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(dev->office_id);
    std::string file_path;
    std::string file_md5;
    OfficeDevUser user(office_info);
    user.GetDetailDataForRequest(dev, list, traceid, file_path, file_md5);
    //TODO:应该把办公和社区的抽离到同个地方进行调用
    
    if(file_md5.size() > 0)
    {
        GetAKCSViewInstance()->NotifyDevFileChange(mac, DEV_FILE_CHANGE_NOTIFY_USER_INFO, traceid, file_path, file_md5);
    }
}


