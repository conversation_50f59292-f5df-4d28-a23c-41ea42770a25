#ifndef __OFFICE_CSMAIN_MSG_HANDLE_H__
#define __OFFICE_CSMAIN_MSG_HANDLE_H__

#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include <string>
#include <condition_variable>


typedef std::shared_ptr<CAkcsPdu> AkcsPduPrt;
typedef std::list<AkcsPduPrt> AkcsPduList;

class OfficeCsmainMsgHandle
{
public:
    OfficeCsmainMsgHandle();
    ~OfficeCsmainMsgHandle();
    static OfficeCsmainMsgHandle* Instance();
    void UpdateMacConfigByCsmain(void* msg_buf, unsigned int len);
    void UpdateDevAccountConfigByCsmain(void* msg_buf, unsigned int len);
    void DevWriteUserinfoReq(void* msg_buf, unsigned int len);
    void OnMessage(const AkcsPduPrt &pdu);
private:
    static OfficeCsmainMsgHandle* office_csmain_handle_;
};



#endif //__OFFICE_CSMAIN_MSG_HANDLE_H__

