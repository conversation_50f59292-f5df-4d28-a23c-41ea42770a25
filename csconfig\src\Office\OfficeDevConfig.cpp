#include <sstream>
#include "OfficeDevConfig.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "WriteFileControl.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/AccessGroupDB.h"
#include "ConfigCommon.h"
#include "util_judge.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


int OfficeDevConfig::WriteDevListFiles(const OfficeDevList &dev_list)
{
    int ret = 0;
    for(auto dev : dev_list)
    {
        ret = WriteFiles(dev);
    }
    return ret;
}


//检查设备给定ID的权限组中是否包含某个relay,传入原有权限组ID列表，通过"/"分隔;传入relay字段代表设备的第几个relay
std::string OfficeDevConfig::GetValidRelaySchedule(const std::string &mac, const std::string& relay_schedule, unsigned int relay_index)
{
    std::string valid_schedule;
    if(relay_index == 0)
    {
        AK_LOG_INFO << "pass relay_index wrong, val is 0";
        return valid_schedule;
    }

    std::set<std::string> schedules;
    SplitString(relay_schedule, "/", schedules);

    for(const auto &schedule : schedules) 
    {
        int schedule_id = ATOI(schedule.c_str());
        int relay_val = -1;
        if (context_->IsDefaultAccessGroup(schedule_id))
        {
            valid_schedule += schedule;
            valid_schedule += "/";
            continue;
        }
        relay_val = context_->DevSchduleIDRelay(mac, schedule_id);
        if (relay_val <= 0) {
            continue;
        }
        //判断当前relay是否在权限组中
        if ((relay_val >> (relay_index - 1)) & 1) {
            valid_schedule += schedule;
            valid_schedule += "/";
        }
    }
    return valid_schedule;

}


void OfficeDevConfig::WriteRelayConfig(std::stringstream &config, const OfficeDevPtr &dev)
{
    //relay 配置
    config << CONFIG_RELAY_DMTF_OPTION << 0 << "\n";
    std::vector<RELAY_INFO> relays;
    ParseRelay(dev->relay, relays);
    int i = 1;
    for (auto& Relay : relays)
    {
        if (!Relay.enable)
        {
            i++;
            continue;
        }
        std::string relay_schedule;
        switch (i)
        {
            case 1:
                config << CONFIG_RELAY_DMTF_CODE1 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME1 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK1 << Relay.access_control << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE1 << Relay.enable_schedule << "\n";
                //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
                relay_schedule = GetValidRelaySchedule(dev->mac, Relay.schedule, i);
                config << CONFIG_RELAY_RELAYSCHEDULE1 << relay_schedule << "\n";
                break;
    
            case 2:
                config << CONFIG_RELAY_DMTF_CODE2 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME2 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK2 << Relay.access_control << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE2 << Relay.enable_schedule << "\n";
                //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
                relay_schedule = GetValidRelaySchedule(dev->mac, Relay.schedule, i);
                config << CONFIG_RELAY_RELAYSCHEDULE2 << relay_schedule << "\n";
                break;
    
            case 3:
                config << CONFIG_RELAY_DMTF_CODE3 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME3 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK3 << Relay.access_control << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE3 << Relay.enable_schedule << "\n";
                //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
                relay_schedule = GetValidRelaySchedule(dev->mac, Relay.schedule, i);
                config << CONFIG_RELAY_RELAYSCHEDULE3 << relay_schedule << "\n";
                break;
                
            case 4:
                config << CONFIG_RELAY_DMTF_CODE4 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME4 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK4 << Relay.access_control << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE4 << Relay.enable_schedule << "\n";
                //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
                relay_schedule = GetValidRelaySchedule(dev->mac, Relay.schedule, i);
                config << CONFIG_RELAY_RELAYSCHEDULE4 << relay_schedule << "\n";
                break;
        }
        i++;
    }
    
    if (strlen(dev->security_relay) > 0)
    {
        int i = 1;
        std::vector<RELAY_INFO> security_relay_infos;
        ParseRelay(dev->security_relay, security_relay_infos);
        for (auto &relay : security_relay_infos)
        {
            if (!relay.enable)
            {
                i++;
                continue;
            }
            switch (i)
            {
                case 1:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE1 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME1 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED1 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK1 << relay.access_control << "\n";
                    break;

                case 2:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE2 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME2 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED2 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK2 << relay.access_control << "\n";
                    break;

                case 3:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE3 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME3 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED3 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK3 << relay.access_control << "\n";
                    break;

                case 4:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE4 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME4 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED4 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK4 << relay.access_control << "\n";
                    break;

                default:
                    //只支持4个
                    break;
            }
            i++;
        }
    }
}


void OfficeDevConfig::WriteTimeZoneConfig(std::stringstream &config, const OfficeDevPtr &dev)
{
    std::string ntp_time = "GMT";
    std::string city_name;
    std::string timezone = office_info_->TimeZone();
    int time_format = office_info_->TimeFormat();

    std::size_t found = timezone.find(' ');
    if (found != std::string::npos)
    {
        ntp_time += timezone.substr(0, found);

        city_name = timezone.substr(found + 1);
    }
    //旧版本设备用了新时区的不配
    if (dev->dclient_ver >= D_CLIENT_VERSION_5400 || \
        (city_name != CONFIG_NEW_TIMEZONE_NUUK && city_name != CONFIG_NEW_TIMEZONE_KOLKATA))  
    {
        config << CONFIG_CLOUDSERVER_TOMEZONE << ntp_time << "\n";
        config << CONFIG_CLOUDSERVER_CITYNAME << city_name << "\n";
    }
    
    int hour_format = CustomizeDateFormatToDeviceConfigValue(time_format, 1);
    config << CONFIG_CLOUDSERVER_TIMEFORMAT << hour_format << "\n";
    int date_format = CustomizeDateFormatToDeviceConfigValue(time_format, 2);
    config << CONFIG_CLOUDSERVER_DATEFORMAT << date_format << "\n";
}

void OfficeDevConfig::WriteManageKeyConfig(std::stringstream &config, const OfficeDevPtr &dev)
{
    if (akjudge::IsCommunityPublicDev(dev->grade))
    {
        std::string mng_list;//管理机全部走sip
        const OfficeDevList& mng_dev_list = context_->AllMngDeviceSetting();
        for(auto mng_dev : mng_dev_list)
        {
            if (dev->id == mng_dev->id
                    || mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
            {
                continue;
            }
            if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && mng_dev->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT
                && dbinterface::PubDevMngList::IsManageBuildingByMac(dev->mac, dev->unit_id) == 0)
            {
                continue;
            }
                    
            std::string contact;
            //公共设备直接的是否走ip，只能根据netgroup
            if (mng_dev->net_group_number == dev->net_group_number)
            {
                contact = mng_dev->ipaddr;
            }
            else
            {
                contact = mng_dev->sip;
            }

            if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
            {
                if ((dev->unit_id == mng_dev->unit_id
                        &&  mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)//梯口
                        ||  mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC//公共
                        || (mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL //个人
                            && strcmp(dev->node, mng_dev->node) == 0)
                   )
                {

                    mng_list += contact;
                    mng_list += ";";

                }
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                if ((dev->unit_id == mng_dev->unit_id
                        &&  mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)//梯口
                        ||  mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC//公共
                   )
                {
                    mng_list += contact;
                    mng_list += ";";

                }
            }
            else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                if (mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)//公共
                {
                    mng_list += contact;
                    mng_list += ";";

                }
            }       
        }        

        if (mng_list.length() > 2)
        {
            //R29
            config << "Config.DoorSetting.DISPLAY.Key6Typ =5\n";
            config << "Config.DoorSetting.DISPLAY.Key6Number=" << mng_list.c_str()  << "\n";
            //linux 设备
            if (akjudge::IsCommunityPublicDev(dev->grade)) //公共设备才配置这个选项
            {
                config << CONFIG_SIP_GROUP_ACCOUNT << mng_list.c_str() << "\n";
            }
        }
        else
        {
            config << "Config.DoorSetting.DISPLAY.Key6Typ =\n";
            config << "Config.DoorSetting.DISPLAY.Key6Number=\n";
            if (akjudge::IsCommunityPublicDev(dev->grade)) //公共设备才配置这个选项
            {
                config << CONFIG_SIP_GROUP_ACCOUNT << "\n";
            }
        }
    }  
}


int OfficeDevConfig::WriteFiles(const OfficeDevPtr &dev)
{
    //chenzhx 这个值不能一直变，相关设备/账户更新配置，会影响到这个配置重新下载
    int sip_port = HashtabHashString(dev->sippwd) % 55534 + 10000;
    std::stringstream config;

    config << CONFIG_ENABLE << "1" << "\n"
           << CONFIG_LABLE  << dev->sip << "\n"
           << CONFIG_DISPLAYNAME << dev->location << "\n"
           << CONFIG_USERNAME << dev->sip << "\n"
           << "Config.Account1.OUTPROXY.Enable=0" << "\n"
           << CONFIG_AUTHNAME << dev->sip << "\n"
           << CONFIG_PWD << dev->sippwd << "\n"
           << CONFIG_TIMEOUT << PERSONNAL_SIP_UA_TIMEOUT << "\n"
           << CONFIG_SIP_NAT_RPORT << "1" << "\n"
           << CONFIG_NAT_UDP_ENABLE <<  "1" << "\n"
           << CONFIG_NAT_UDP_INTERVAL <<  "30" << "\n"
           << CONFIG_CLOUDSERVER_FTP_USER << "akuvox" << "\n"
           << CONFIG_CLOUDSERVER_FTP_PWD << "pu6HYKvTkyGstq" << "\n"
           << CONFIG_CLOUDSERVER_DEV_EXPIRE << "0" << "\n"
           << "Config.Account1.Video00.ProfileLevel=2" << "\n"
           << "Config.Account1.Video00.MaxBR=512" << "\n"
           << "Config.Features.VIDEO_CODEC_PARAM.ProfileLevel=720P" << "\n"
           << "Config.Account1.SIP.ListenPortMin=" << sip_port << "\n"
           << "Config.Account1.SIP.ListenPortMax=" << sip_port + 10 << "\n";

    config << "Config.Account1.Audio0.Enable=0" << "\n" //PCMU
           << "Config.Account1.Audio1.Enable=1" << "\n" //PCMA
           << "Config.Account1.Audio4.Enable=0" << "\n" //G729
           << "Config.Account1.Audio5.Enable=0" << "\n"; //G722
           
    if (dev->dclient_ver >= D_CLIENT_VERSION_6100)
    {
        //******** 涂鸦强制用pcmu
        config << "Config.Account1.Audio0.Enable=1" << "\n"; //PCMU
    }

    //门口机、梯口机增加sip群组账号
    if (akjudge::DevDoorType(dev->dev_type))
    {
        config << CONFIG_DTMF_ENABLE << "1" << "\n"
               << CONFIG_DTMF_CODE1 << "11" << "\n"
               << CONFIG_RTSP_ENABLE << "1" << "\n"
               << CONFIG_RTSP_VIDEO << "1" << "\n"
               << CONFIG_RTSP_CODEC << "0" << "\n"
               << CONFIG_RTSP_H264_FRAMERATE << "30" << "\n";
               

        config << CONFIG_SIP_GROUP_ACCOUNT << "" << "\n";
        
        config << "Config.DoorSetting.RTSP.H264Resolution = 4\n";
        config << "Config.DoorSetting.RTSP.H264BitRate = 2048\n";
        config << CONFIG_FEATURES_CALLROBIN_ENABLE << "0" << "\n"
               << CONFIG_FEATURES_CALLROBIN_NUM << "" << "\n"
               << CONFIG_FEATURES_CALLROBIN_TIME << "20" << "\n";

        if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            OfficeAccountCnf cnf;
            if (context_->GetAccountCnf(dev->node, cnf) == 0)
            {
                int enable_motion = CDeviceSetting::GetInstance()->GetMotionDetection(dev->dclient_ver, cnf.enable_motion);
                config << CONFIG_DOORSETTING_MOTION_DETECT_ENABLE << enable_motion << "\n"
                         << CONFIG_DOORSETTING_MOTION_DETECT_TIME << cnf.motion_time << "\n";            
            }
        }
        else if (akjudge::IsCommunityPublicDev(dev->grade))
        {
            int enable_motion = CDeviceSetting::GetInstance()->GetMotionDetection(dev->dclient_ver, office_info_->isEnableMotion());
            config << CONFIG_DOORSETTING_MOTION_DETECT_ENABLE << enable_motion << "\n"
                     << CONFIG_DOORSETTING_MOTION_DETECT_TIME << office_info_->MotionTime() << "\n";
        }
         
        //relay 配置
        WriteRelayConfig(config, dev);
    }

    //apt+pin 0apt+key, 1key
    config << "Config.Doorsetting.PASSWORD.PrivateKeyType=1\n";


    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)//公共
    {
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_COMMUNITY_PUBLIC << "\n";
        //20230731 modify by chenzhx 0->1 all app/indoor already in contact
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";

        config << CONFIG_COMMUNITY_NAME << office_info_->Name() << "\n";  //社区公共设备做这个设置
        config <<  CONFIG_COMMUNITY_STREET << office_info_->Street() << "\n";
        config <<  CONFIG_IDCARD_ENABLE << office_info_->IDCard() << "\n";
        if (akjudge::DeviceSupportLimitFlow(dev->firmwares))
        {
            config << CONFIG_NETWORK_DATAUSAGE_DATATYPE << office_info_->LimitFlowDataType() << "\n";
        }
    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)//单元公共
    {
		std::string unit_name = context_->GetUnitName(dev->unit_id);
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_COMMUNITY_UNIT << "\n";
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
        config << CONFIG_COMMUNITY_NAME << office_info_->Name() << " " << unit_name << "\n";
        config <<  CONFIG_COMMUNITY_STREET << office_info_->Street() << "\n";
        config <<  CONFIG_IDCARD_ENABLE << office_info_->IDCard() << "\n";
        if (akjudge::DeviceSupportLimitFlow(dev->firmwares))
        {
            config << CONFIG_NETWORK_DATAUSAGE_DATATYPE << office_info_->LimitFlowDataType() << "\n";
        }
    }
    else
    {
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_PERSONAL << "\n";
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
    }

    WriteTimeZoneConfig(config, dev);

    //20191218 加入DEVICE_TYPE_DOOR，设备已经默认是2了，就是防止设备移来移去配置没有更新
    if (akjudge::DevDoorType(dev->dev_type))
    {
        if (dev->stair_show == 0)// 0是个人stair类型 用2显示app/devices
        {
            dev->stair_show = 2;
        }
        config <<  CONFIG_CONTACT_SHOW_TEYP << dev->stair_show << "\n";
    }
    if (dev->dev_type == DEVICE_TYPE_INDOOR)
    {
        //all call  50000-65535
        int port = ATOI(dev->node) % 15534 + 50000;
        config << "Config.Multicast.SELECTEDGROUP.SelectedGroup=1\n"
                 << "Config.Multicast.GROUP1.IP=**********:" << port << "\n"
                 << "Config.Multicast.LISTEN1.IP=**********:" << port << "\n"
                 << "Config.Netcast.SELECTEDGROUP.SelectedGroup=1\n"
                 << "Config.Netcast.GROUP1.IP=**********:" << port << "\n"
                 << "Config.Netcast.LISTEN1.IP=**********:" << port << "\n";
    }
    //写siptype siphacking
    config << "Config.Account1.CALL.PreventSIPHacking=1\n";
    if (DevMngSipType_NONE == mng_sip_type_)
    {
        config << "Config.Account1.SIP.TransType=" << dev->sip_type << "\n";
    }
    else
    {
        config << "Config.Account1.SIP.TransType=" << mng_sip_type_ << "\n";
    }
    //rtp confuse 6.0
    if (dev->dclient_ver >= D_CLIENT_VERSION_6000)
    {
        config << "Config.Account1.CALL.AudioVideoConfuse=" << rtp_confuse_ << "\n";
    }

    //v5.0加入设备名称配置项
    config << "Config.DoorSetting.DEVICENODE.Location=" << dev->location << "\n";

    //配置管理中心键
    WriteManageKeyConfig(config, dev);

    if (dev->dclient_ver >= D_CLIENT_VERSION_5200 && !SwitchHandle(dev->fun_bit, FUNC_DEV_GET_REMOTECONFIG_ADDR_BY_DCLIENT))
    {
        config << "Config.DoorSetting.DEVICENODE.SSHPassSrv=" << gstCSCONFIGConf.ssh_proxy_domain << "\n";
    }
     //******** add by chenzhx        
    if (gstCSCONFIGConf.server_type == ServerArea::ccloud &&  akjudge::DevDoorType(dev->dev_type) && dev->dclient_ver < D_CLIENT_VERSION_6400)
    {
        config <<  "Config.Account1.DTMF.Type=4" << "\n";
    }

    if (dev->dclient_ver >= D_CLIENT_VERSION_6200 && akjudge::IsCommunityPublicDev(dev->grade) &&  akjudge::DevDoorType(dev->dev_type))
    {
        if (!office_info_->IsExpire() && office_info_->CheckFeature(OfficeInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_DELIVERY))
        {
            config <<  "Config.DoorSetting.PACKAGEROOM.RemindAvailable=1\n";
        }
        else
        {
            config <<  "Config.DoorSetting.PACKAGEROOM.RemindAvailable=0\n";;
        }
    }
        
    if (dev->dclient_ver >= D_CLIENT_VERSION_6400 && akjudge::DevDoorType(dev->dev_type))
    {
        //1:Inband;2:RFC2833;3:Info;4:Info+Inband;5:Info+RFC2833;6:Info+Inband+RFC2833
        //add by czw, dtmf国内外方式不同处理
        if(gstCSCONFIGConf.server_type == ServerArea::ccloud)
        {
            config <<  "Config.Account1.DTMF.Type=6" << "\n";
        }
        else
        {
            config <<  "Config.Account1.DTMF.Type=2" << "\n";
        }      
    }

    config <<  "Config.DoorSetting.DISPLAY.Key4Type=7\n";//（隐藏默认配置顺序的拨号入口）

    //办公NewComm默认值为1
    config << "Config.DoorSetting.CLOUDSERVER.NewComm=1" << "\n";


    // rtsps 开关
    config << CONFIG_RTSPS_ENABLE << mng_rtsp_type_ << "\n";
    
    UpdateUcloudVideoBitRate(dev->sw_ver, config);
    UpdateSipSrtpConfig(mng_sip_type_, dev->fun_bit, config);
    
    config << dev->autop_config << "\n";
    
    //写入文件
    std::string config_path = config_root_path_ + dev->mac + ".cfg";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config.str(), SHADOW_TYPE::SHADOW_CONFIG, project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);
    return 0;
}



