#ifndef __OFFICE_DEV_CONFIG_H__
#define __OFFICE_DEV_CONFIG_H__
#include <string>
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "AKCSMsg.h"
#include "DevConfig.h"
#include "OfficeUpdateConfigContext.h"

//std::vector<std::string> gNewTimeZoneVec{"Pacific-New","Center","New_Salem","Beulah","Nuuk","Kolkata"};

class OfficeConfigHandle; 


class OfficeDevConfig
{
public:
    int WriteDevListFiles(const OfficeDevList &dev_list);

    int SetOfficeInfo(   OfficeInfoPtr &office_info)
    {
        office_info_ = office_info;
        return 0;
    }
    
    int SetContext(OfficeConfigContextPtr context)
    {
        context_ = context;
        return 0;
    }    
private:
    OfficeDevConfig(  const std::string& config_root_path, int mng_sip_type, int rtp_confuse, int mng_rtsp_type)
    {
        config_root_path_ = config_root_path;
        mng_sip_type_ = mng_sip_type;
        rtp_confuse_ = rtp_confuse;
        mng_rtsp_type_ = mng_rtsp_type;
    }
    // 声明友元类
    friend class OfficeConfigHandle;    

    int WriteFiles(const OfficeDevPtr &dev);
    void WriteRelayConfig(std::stringstream &config, const OfficeDevPtr &dev);
    void WriteTimeZoneConfig(std::stringstream &config, const OfficeDevPtr &dev);
    void WriteManageKeyConfig(std::stringstream &config, const OfficeDevPtr &dev);
    
    std::string GetValidRelaySchedule(const std::string &mac, const std::string& relay_schedule, unsigned int relay_index);

    int mng_rtsp_type_;
    int mng_sip_type_;
    int rtp_confuse_;
    std::string config_root_path_;
    OfficeInfoPtr office_info_;

    OfficeConfigContextPtr context_;
};




    

#endif 
