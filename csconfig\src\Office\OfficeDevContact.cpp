#include <sstream>
#include "OfficeDevContact.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "PersonalAccount.h"
#include "DeviceSetting.h"
#include "PersonnalDeviceSetting.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ContactCommon.h"
#include "WriteFileControl.h"
#include "dbinterface/VersionModel.h"
#include "util_judge.h"


extern CSCONFIG_CONF gstCSCONFIGConf;

/*确定呼叫顺序
比如：2-2;3-1 代表第二组呼叫，取SIP或IP呼叫。加代表第三组呼叫，取SIP0
    因为app有落地要呼叫，所以一条联系人可能会存在于多个呼叫组，用分号分隔。
SIP0(uid)  SIP(phone)
SIP0为下标1，SIP/IP为下标2，land下标为3
seq2是因为从账号加了落地号码，也需要按照calltype的顺序，因为群呼时候的落地又不包括从账号的落地，所以需要新增字段
开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，会呼叫app。平台会把land的选项配置为sip账号

*/

static int CreateGroupCallSeq(int call_type, OfficeAccount &app, OfficeDevList list)
{
    if (call_type == NODE_CALL_TYPE_INDOOR_PHONE)
    {
        for(auto dev : list)
        {
            if (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(dev->call_seq, sizeof(dev->call_seq), " seq=\"1-2\" ");
            }

        }
        ::snprintf(app.call_seq, sizeof(app.call_seq), " seq=\"1-2;1-3\" ");
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_APP)
    {
        for(auto dev : list)
        {
            if (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(dev->call_seq, sizeof(dev->call_seq), " seq=\"1-2\" ");
            }
        }
        ::snprintf(app.call_seq, sizeof(app.call_seq), " seq=\"2-2\" ");
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE)
    {
        for(auto dev : list)
        {
            if (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(dev->call_seq, sizeof(dev->call_seq), " seq=\"1-2\" ");
            }
        }
        ::snprintf(app.call_seq, sizeof(app.call_seq), " seq=\"2-2;2-3\" ");
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        for(auto dev : list)
        {
            if (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(dev->call_seq, sizeof(dev->call_seq), " seq=\"1-2\" ");
            }
        }
        ::snprintf(app.call_seq, sizeof(app.call_seq), " seq=\"2-1;3-2;3-3\" ");
    }
    return 0;

}

int OfficeDevContact::UpdateOfficeContactFile(const OfficeDevPtr dev, const OfficeDevList node_dev_list, OfficeAccount &account_list,
                                      const OfficeDevList pub_dev_list/*最外层*/, 
                                      const OfficeDevList unit_dev_list/*单元*/)
{
   UpdateContactFile(dev, node_dev_list, account_list, pub_dev_list, unit_dev_list); 
   return 0;
}

void OfficeDevContact::WriteContactNameByOrder(const int contact_display_order, const size_t length, const char* firstname, const char* lastname, char *name)
{
    if (contact_display_order == 0)
    {
        ::snprintf(name, length, "%s %s", firstname, lastname);
    } else if (contact_display_order == 1)
    {
        ::snprintf(name, length, "%s %s", lastname, firstname);
    }
}

int OfficeDevContact::UpdateContactFile(const OfficeDevPtr dev, OfficeDevList node_dev_list, OfficeAccount &account,
                                      const OfficeDevList pub_dev_list/*最外层*/, 
                                      const OfficeDevList unit_dev_list/*单元*/)
{
    if (!office_info_)
    {
        return -1;
    }
    int contact_name_order = office_info_->ContactDisplayOrder();
    char contact_name[256] = {};
    WriteContactNameByOrder(contact_name_order, sizeof(contact_name), account.firstname, account.lastname, contact_name);
    std::vector<OfficeDevPtr> pub_list1(pub_dev_list.begin(),pub_dev_list.end());
    std::vector<OfficeDevPtr> pub_list2(unit_dev_list.begin(),unit_dev_list.end());
    std::vector<OfficeDevPtr> pub_list;
    pub_list.insert(pub_list.end(), pub_list1.begin(), pub_list1.end());
    pub_list.insert(pub_list.end(), pub_list2.begin(), pub_list2.end());

    OfficeAccountCnf cnf2;
    if (context_->GetAccountCnf(account.account, cnf2) != 0)
    {
        return -1;
    }
    OfficeAccountCnf* cnf = &cnf2;
    int enable_call = SwitchHandle(cnf->flags, int(dbinterface::OfficePersonalAccount::EnableCall));

    if (cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
            || cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE
            || cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_APP
            || cnf->call_type == NODE_CALL_TYPE_INDOOR_PHONE)
    {
        cnf->call_loop = CALL_LOOP_TYPE_GROUP_CALL;
    }
    else if (cnf->call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
    {
         cnf->call_loop = CALL_LOOP_TYPE_APP_INDOOR_BACK_PHONE;
    }
    else
    {
         cnf->call_loop = CALL_LOOP_TYPE_NORMAL;
    }
    if (cnf->call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
            || cnf->call_type == NODE_CALL_TYPE_INDOOR_PHONE
            || cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
            || cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
         cnf->phone_status = 1;//落地V4.3改为CallType控制
    }
    else
    {
         cnf->phone_status = 0;
    }
    
    CreateGroupCallSeq(cnf->call_type, account, node_dev_list);

    std::stringstream config_body;
    std::stringstream group_info;
    std::stringstream contact_info;
    config_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    config_body << "<ContactData>\n";

    char room_num[256] = "";
    char apt[256] = "";

    std::string phone, phone_all, phone_last;
    if (strlen(account.phone) > 0)
    {
        phone = PHONE_CALL_OUT_SUBFIX;
        phone += account.phone_code;
        phone += account.phone;
        phone_all = phone;
    }

    std::string call_seq = account.call_seq;
    if (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
    {
        call_seq = "";
    }

    char unit_info[256] = "";
    std::string name = context_->GetUnitName(account.unit_id);
    ::snprintf(unit_info, sizeof(unit_info), "%s", name.c_str());
    
    ContactKvList kv;
    kv.push_back(std::make_pair(CONTACT_ATTR::NAME, contact_name));                   
    kv.push_back(std::make_pair(CONTACT_ATTR::NODE, account.sip_account));                    
    kv.push_back(std::make_pair(CONTACT_ATTR::ROOM, contact_name));     
    kv.push_back(std::make_pair(CONTACT_ATTR::ROOM_N, std::string("")));
    kv.push_back(std::make_pair(CONTACT_ATTR::UNIT_APT, std::string("")));
    kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, unit_info)); //管理机如果没有unit连续人显示异常
    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cnf->sipgroup));    
    kv.push_back(std::make_pair(CONTACT_ATTR::IP_DIRECT, std::to_string(account.ip_direct)));
    kv.push_back(std::make_pair(CONTACT_ATTR::CALL_LOOP, std::to_string(cnf->call_loop)));
    
    GetGroupStr(group_info, kv);

    //公共设备放在最前面
    if (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
    {
        for (auto cur_dev : pub_list)
        {
            if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == cur_dev->grade && GetDeviceControlInstance()->DeviceIsManageBuilding(cur_dev->dev_type))
            {
                if(!context_->DevMngUnitID(cur_dev, dev->unit_id))
                {
                    AK_LOG_INFO << "This Device mac=" << dev->mac << ",unit_id=" << dev->unit_id << " is not managed by Pub Device mac=" << cur_dev->mac;
                    continue;
                
                }
            }
            
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_dev->location));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_dev->sip));                  
            kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_dev->mac));
            kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_dev->rtsppwd));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_dev->dev_type)));
            kv.push_back(std::make_pair(CONTACT_ATTR::PUB, std::string("1")));
            if (DEVICE_TYPE_STAIR == cur_dev->dev_type || DEVICE_TYPE_DOOR == cur_dev->dev_type \
                || DEVICE_TYPE_ACCESS == cur_dev->dev_type)
            {
                int not_monitor = context_->IsNoMonitorDev(cur_dev->firmwares);
                kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
            }
            //室内机开门需求
            WriteRelayContact(cur_dev->dev_type, cur_dev->relay, cur_dev->security_relay, kv);
            
            if (account.ip_direct && dev->net_group_number == cur_dev->net_group_number)
            {
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, cur_dev->ipaddr));                                 
            }
            else
            {                   
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_dev->sip));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));                                          
            }
            GetContactStr(contact_info, kv);
        }

    }
    
    //主账号            
    //是否启用落地
    std::string sip;
    if (cnf->phone_status 
        && dev->dev_type != DEVICE_TYPE_INDOOR 
        && dev->dev_type != DEVICE_TYPE_MANAGEMENT
        && enable_call) //v5.0室内机不配置落地信息
    {

        ContactKvList kv;    
        kv.push_back(std::make_pair(CONTACT_ATTR::NAME, contact_name));
        kv.push_back(std::make_pair(CONTACT_ATTR::UID, account.sip_account));
        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));
        kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, account.sip_account));          
        kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
        kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
        kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
        kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
        kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF2, account.sip_account));
        kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq)); 
            
        //区分于个人终端       目前个人 没有这个顺序。先呼叫app 在呼叫落地
        //兼容：MatchDtmf1 MatchDtmf2一定是app和第一个落地号码，旧版本不识别MatchDtmf3 MatchDtmf4
        if (cnf->call_type == NODE_CALL_TYPE_INDOOR_PHONE
                || cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                || cnf->call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
        {                  
            kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_last));              
        }
        else if (cnf->call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
        {                  
            kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));                                    
        }
        GetContactStr(contact_info, kv); 
    }
    else
    {
        if (enable_call)
        {
            //没有落地，就不要赋值land* 和app
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, contact_name));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, account.sip_account));                    
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, account.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
            kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));              
            GetContactStr(contact_info, kv);                          
        }
    }

    /*
    不启用IP直播，则联系都是SIP
    启用ip直播，则根据网络号，如果网络号一致放IP，反之放SIP
    */
    for (auto cur_dev : node_dev_list)
    {
        if (cur_dev->id != dev->id &&
                ((dev->dev_type != DEVICE_TYPE_INDOOR && (cur_dev->dev_type == DEVICE_TYPE_INDOOR || cur_dev->dev_type == DEVICE_TYPE_MANAGEMENT))  //门口机梯口机不写  只写室内机/管理中心
                 || (dev->dev_type == DEVICE_TYPE_INDOOR || dev->dev_type == DEVICE_TYPE_MANAGEMENT)  //室内机/管理中心机写全部联系人
                )
           )
        {
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_dev->location));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_dev->sip)); 
            kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_dev->mac));
            kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_dev->rtsppwd));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_dev->dev_type)));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, cur_dev->call_seq));
            if (DEVICE_TYPE_STAIR == cur_dev->dev_type || DEVICE_TYPE_DOOR == cur_dev->dev_type \
                || DEVICE_TYPE_ACCESS == cur_dev->dev_type)
            {
                int not_monitor = context_->IsNoMonitorDev(cur_dev->firmwares);
                kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
            }

            WriteRelayContact(cur_dev->dev_type, cur_dev->relay, cur_dev->security_relay, kv);

            if (account.ip_direct && dev->net_group_number == cur_dev->net_group_number)
            {
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, cur_dev->ipaddr));       
            }
            else
            {                   
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_dev->sip));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));                
            }
            GetContactStr(contact_info, kv); 

        }      
    }

    //contact不为空才写,只有group没有contact有些设备会奔溃
    if(contact_info.str().length() > 0)
    {
        config_body << group_info.str() << contact_info.str() << "</Group>\n";
    }

    config_body << "</ContactData>\n";

    //写入文件
    std::string config_path = config_root_path_ + dev->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);
    return 0;
}

/*
<PubInfo > //公共设备信息，用于管理中心机，Unit代表对应building名称，Unit为空代表最外围公共设备
  <Contact Name="ddorrrd" Unit="building" SIP="" IP="************" MAC="************" RTSPPwd="1547152G2yX549l0" Type="0" seq="1-2"/>
  <Contact Name="ddorrrd" Unit="building2" SIP="" IP="************" MAC="************" RTSPPwd="1547152G2yX549l0" Type="0" seq="1-2"/>
  <Contact Name="ddorrrd" Unit="" SIP="" IP="************" MAC="************" RTSPPwd="1547152G2yX549l0" Type="0" seq="1-2"/>
</PubInfo>

*/
//写公共设备联系人 csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
int OfficeDevContact::UpdateOfficePublicContactFile(const OfficeDevPtr &dev, OfficeAccountList &account_list, int grade_type)
{
    std::stringstream config_body;
    
    if(!office_info_)
    {
        AK_LOG_WARN << "office info is null";
        return -1;
    }
    int contact_display_order = office_info_->ContactDisplayOrder(); //联系人姓名展示顺序，0-FirstName+LastName,1-LastName+FirstName
    config_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    config_body << "<ContactData>\n";
    int is_pub = 0;
    char unit_info[256] = "";
    std::vector<uint32_t> unit_list;
    int manage_all_flag = 0;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == grade_type)
    {
        is_pub = 1;
		if(GetDeviceControlInstance()->DeviceIsManageBuilding(dev->dev_type))
        {
            manage_all_flag = context_->DevMngUnitListOrMngAll(dev, unit_list);            
        }
    }
    
    for (auto& account : account_list)
    {
        char name[256] = {};
        WriteContactNameByOrder(contact_display_order, sizeof(name), account.firstname, account.lastname, name);
        int have_contact = 0;
        std::stringstream str_conf_account;
		if (is_pub && GetDeviceControlInstance()->DeviceIsManageBuilding(dev->dev_type))
        {
            if(!manage_all_flag)
            {
                auto iter = find(unit_list.begin(), unit_list.end(), account.unit_id);                
                if (iter == unit_list.end())
                {
                    AK_LOG_WARN << "Mac no belong acount unit id. mac=" << dev->mac;
                    continue;
                }
            }
        }
        OfficeAccountCnf cnf;
        if (context_->GetAccountCnf(account.account, cnf) != 0)
        {
            AK_LOG_WARN << "No get account cnf. account " << account.account;
            continue;
        }

        int enable_call = SwitchHandle(cnf.flags, int(dbinterface::OfficePersonalAccount::EnableCall));
        if (cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                || cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE
                || cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP
                || cnf.call_type == NODE_CALL_TYPE_INDOOR_PHONE)
        {
            cnf.call_loop = CALL_LOOP_TYPE_GROUP_CALL;
        }
        else if (cnf.call_type== NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
        {
            cnf.call_loop = CALL_LOOP_TYPE_APP_INDOOR_BACK_PHONE;
        }
        else
        {
            cnf.call_loop = CALL_LOOP_TYPE_NORMAL;
        }

        if (cnf.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
                || cnf.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                || cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                || cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
        {
            cnf.phone_status = 1;//落地V4.3改为CallType控制
        }
        else
        {
            cnf.phone_status = 0;
        }

        std::string unit_name = context_->GetUnitName(account.unit_id);                
        ::snprintf(unit_info, sizeof(unit_info), "%s", unit_name.c_str());

        OfficeDevList node_dev_list = context_->GetNodeDeviceInGlobal(account.account);
        CreateGroupCallSeq(cnf.call_type, account, node_dev_list);

        std::string phone_head;
        char apt[256] = "";
        char unit_apt[256] = "";
        char room_num[256] = "";
        char room_name[256] = "";

        phone_head = PHONE_CALL_OUT_SUBFIX;
        phone_head += account.phone_code;
        std::string phone, phone_all, phone_last;
        if (strlen(account.phone) > 0)
        {
            phone = phone_head;
            phone += account.phone;
            phone_all = phone;
        }

        ContactKvList kv;
        kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));                   
        kv.push_back(std::make_pair(CONTACT_ATTR::NODE, account.sip_account));  
        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cnf.sipgroup)); 
        kv.push_back(std::make_pair(CONTACT_ATTR::ROOM, name));     
        kv.push_back(std::make_pair(CONTACT_ATTR::ROOM_N, std::string("")));
        kv.push_back(std::make_pair(CONTACT_ATTR::UNIT_APT, std::string("")));
        kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, unit_info)); 
        kv.push_back(std::make_pair(CONTACT_ATTR::IP_DIRECT, std::to_string(account.ip_direct)));
        kv.push_back(std::make_pair(CONTACT_ATTR::CALL_LOOP, std::to_string(cnf.call_loop)));
        GetGroupStr(str_conf_account, kv);

        std::string sip;
        if (cnf.phone_status && enable_call)
        {
            have_contact = 1;
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, account.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
            kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
            kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
            kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
            kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF2, account.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, account.call_seq));              
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, account.sip_account));              
            //落地之后直接把号码赋值到IP,设备呼叫就会带上这个号码呼叫
            //区分于个人终端       目前个人 没有这个顺序。先呼叫app 在呼叫落地
            if (cnf.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                    || cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                    || cnf.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
            {                  
                kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_last));            
            }
            else if (cnf.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
            {                   
                kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));                                     
            }
            GetContactStr(str_conf_account, kv);
        }
        else
        {
            if (enable_call)
            {
                have_contact = 1;
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, account.sip_account));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, account.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, account.call_seq));              
                GetContactStr(str_conf_account, kv);
            }
        }

        for (auto cur_dev : node_dev_list)
        {
            //1、公共设备联系人会有室内机，室内机会呼叫公共设备
            //2、公共设备是管理机时候，联系人会有所有室内机和门口机。
            if ((cur_dev->dev_type == DEVICE_TYPE_INDOOR || cur_dev->dev_type == DEVICE_TYPE_MANAGEMENT) || dev->dev_type == DEVICE_TYPE_MANAGEMENT)
            {
                if(is_pub && GetDeviceControlInstance()->DeviceIsManageBuilding(dev->dev_type))
                {
                    if(!manage_all_flag)
                    {
                        auto iter = find(unit_list.begin(), unit_list.end(), cur_dev->unit_id);                
                        if (iter == unit_list.end())
                        {
                            continue;
                        }
                    }
                }  
                have_contact = 1;
                
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_dev->location));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_dev->sip));  
                kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_dev->mac));
                kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_dev->rtsppwd));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_dev->dev_type)));
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, cur_dev->call_seq));                
                if (account.ip_direct && cur_dev->net_group_number == dev->net_group_number)
                {                    
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, cur_dev->ipaddr));      
                }
                else if (account.ip_direct)
                {                   
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_dev->sip));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));                               
                }
                else
                {                   
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_dev->sip));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));                            
                }
                GetContactStr(str_conf_account, kv);
            }
     
        }
        str_conf_account << "</Group>\n";
        if (have_contact)
        {
            config_body << str_conf_account.str();
        }
    }

    //写管理中心机的配置（包括所有的公共设备）
    if (GetDeviceControlInstance()->DeviceIsManageBuilding(dev->dev_type))
    {
        const OfficeDevList &pub_list = context_->GetAllPubUnitDeviceInGlobal();
        
        config_body << "<PubInfo>\n";
        std::string unit_name = "";
        for (auto cur_dev : pub_list)
        {
            bool can_write = false; //是否写入当前设备信息
            if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                can_write = CanWriteCurDevToPublicDevPubInfo(dev, cur_dev, manage_all_flag, unit_list);
            }
            else if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                can_write = CanWriteCurDevToUnitDevPubInfo(dev, cur_dev);
            }
            if(!can_write)
            {
                //不能写入，则跳过
                continue;
            }

            if (cur_dev->unit_id)
            {
                unit_name = context_->GetUnitName(cur_dev->unit_id);
            }
            else
            {
                unit_name = "";
            }
            
            if (strcmp(dev->mac, cur_dev->mac)) //写别人的配置
            {
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_dev->location));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_dev->sip));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, unit_name));  
                kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_dev->mac));
                kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_dev->rtsppwd));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_dev->dev_type)));
                if (dev->dev_type == DEVICE_TYPE_MANAGEMENT)//管理机下发relay
                {
                    WriteRelayContact(cur_dev->dev_type, cur_dev->relay, cur_dev->security_relay, kv);
                }
                if (akjudge::DevDoorType(cur_dev->dev_type))
                {
                    int not_monitor = context_->IsNoMonitorDev(cur_dev->firmwares);
                    kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
                }
                
                if (cur_dev->net_group_number == dev->net_group_number)
                {                   
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, cur_dev->ipaddr));                                
                }
                else
                {                      
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_dev->sip));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));                              
                }
                GetContactStr(config_body, kv);  
            }         
        }
        
        config_body << "</PubInfo>\n";
    }

    config_body << "</ContactData>\n";
    //写入文件
    std::string config_path = config_root_path_ + dev->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);

    return 0;
}

bool OfficeDevContact::CanWriteCurDevToPublicDevPubInfo(const OfficeDevPtr& your_dev, const OfficeDevPtr& cur_dev, int manage_all_flag, std::vector<uint32_t>& unit_list)
{
    if(your_dev->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        return false;
    }
    //判断公共设备是否管理了楼栋设备所在楼栋
    if(cur_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        if(!manage_all_flag)
        {
            auto iter = find(unit_list.begin(), unit_list.end(), cur_dev->unit_id);
            if(iter == unit_list.end())
            {
                //未管理的楼栋设备不写入最外围公共设备
                return false;
            }
        }
    }
    return true;
}

bool OfficeDevContact::CanWriteCurDevToUnitDevPubInfo(const OfficeDevPtr& your_dev, const OfficeDevPtr& cur_dev)
{
    if(your_dev->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        return false;
    }
    //楼栋管理机需要写最外围公共设备信息
    if(your_dev->dev_type == DEVICE_TYPE_MANAGEMENT && cur_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        return true;
    }
    //其余情况，只写同一楼栋下公共设备
    if(your_dev->unit_id == cur_dev->unit_id)
    {
        return true;
    }
    return false;
}

