#ifndef __OFFICE_DEV_CONTACT_H__
#define __OFFICE_DEV_CONTACT_H__
#include <string>
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "AKCSMsg.h"
#include "OfficeUpdateConfigContext.h"

class OfficeConfigHandle; 

class OfficeDevContact
{
public:
    int SetOfficeInfo(const OfficeInfoPtr office_info)
    {
        office_info_ = office_info;
        return 0;
    }    

    int SetContext(OfficeConfigContextPtr context)
    {
        context_ = context;
        return 0;
    } 
    
   int UpdateOfficeContactFile(const OfficeDevPtr dev, const OfficeDevList node_dev_list, OfficeAccount &,
                                      const OfficeDevList pub_dev_list/*最外层*/, 
                                      const OfficeDevList unit_dev_list/*单元*/);

    int UpdateOfficePublicContactFile(const OfficeDevPtr &dev, OfficeAccountList &account_list, int type);
                                        
private:
    // 声明友元类
    friend class OfficeConfigHandle;
	OfficeDevContact(const std::string& config_root_path, const OfficeInfoPtr &office_info)
    {
        config_root_path_ = config_root_path;
        office_info_ = office_info;
    }    
    int UpdateContactFile(const OfficeDevPtr dev, const OfficeDevList node_dev_list, OfficeAccount &,
                                      const OfficeDevList pub_dev_list/*最外层*/, 
                                      const OfficeDevList unit_dev_list/*单元*/);

    bool CanWriteCurDevToPublicDevPubInfo(const OfficeDevPtr& your_dev, const OfficeDevPtr& cur_dev, int manage_all_flag, std::vector<uint32_t>& unit_list);
    bool CanWriteCurDevToUnitDevPubInfo(const OfficeDevPtr& your_dev, const OfficeDevPtr& cur_dev);
    void WriteContactNameByOrder(const int contact_display_order, const size_t length, const char* firstname, const char* lastname, char *name);
    std::string config_root_path_;
    OfficeInfoPtr office_info_;

    OfficeConfigContextPtr context_;
};

#endif 
