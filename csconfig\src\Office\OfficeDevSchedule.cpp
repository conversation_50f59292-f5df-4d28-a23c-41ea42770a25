#include <sstream>
#include "OfficeDevSchedule.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "OfficeDevUser.h"
#include "DevUser.h"
#include "ShadowMng.h"
#include "WriteFileControl.h"


extern CSCONFIG_CONF gstCSCONFIGConf;
OfficeDevSchedule::OfficeDevSchedule()
{

}


OfficeDevSchedule::~OfficeDevSchedule()
{
    
}


int OfficeDevSchedule::UpdateScheduleData(const OfficeDevList &dev_list)
{
    AK_LOG_INFO << "UpdateScheduleData Start.";
    for (auto cur_dev : dev_list)
    {
        if (!DevUser::DevSupportUser(cur_dev->dclient_ver) || !DevUser::DevTypeSupportUser(cur_dev->dev_type))
        {
            AK_LOG_INFO << "devices mac=" << cur_dev->mac << " not support user. type:" << cur_dev->dev_type 
                << " dclient ver:" << cur_dev->dclient_ver;      
            continue; 
        }
        
        AccessGroupInfoPtrList ag_list;
        
        OfficeDevUser::GetDevAccessGroupList(cur_dev, ag_list);
        if (ag_list.size() <= 0)
        {
            //需要写空的schedule
            WirteScheduleToJson(cur_dev, ag_list);
            AK_LOG_INFO << "UpdateScheduleData MAC=" << cur_dev->mac << " Access Group List is null";
            continue;           
        }
        WirteScheduleToJson(cur_dev, ag_list);
    }
    AK_LOG_INFO << "UpdateScheduleData End.";
    return 0;    
}

int OfficeDevSchedule::WirteFile(const std::string &filename, const std::string &content)
{
    FILE* file = fopen(filename.c_str(), "w+");
    if (file == NULL)
    {
        AK_LOG_WARN << "fopen failed " << filename;       
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", filename, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //将配置信息内容写入文件中
    fwrite(content.c_str(), sizeof(char), strlen(content.c_str()) + 1, file);
    fclose(file);
    AK_LOG_INFO << "The schedule file path is " << filename;

    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(filename.c_str(), AES_ENCRYPT_KEY_V1, filename.c_str());
    }
    return 0;

}


int OfficeDevSchedule::WirteScheduleToJson(const OfficeDevPtr &dev, AccessGroupInfoPtrList &ag_list)
{
    Json::Value item;
    Json::FastWriter w;
    item["UserType"] = 0;//0表示云，1表示ACMS;
    Json::Value sche;

    for (auto ag : ag_list)
    {
        Json::Value info;
        info["Name"] = ag->name_;

        char daily[128] = "";
        snprintf(daily, sizeof(daily), "%s-%s", ag->time_start_, ag->time_end_);        
        if (ag->scheduler_type_ == SchedType::ONCE_SCHED)
        {
            info["ID"] = ag->id_;
            info["Type"] = DevSchedType::DEV_SCHE_ONCE_SCHED;
            char dayinfo[128] = "";
            snprintf(dayinfo, sizeof(dayinfo), "%s-%s", ag->day_start_for_ymd_, ag->day_end_for_ymd_);             
            info["Date"] = dayinfo;
            info["Weekly"] = "0123456";
            info["Daily"] = "00:00:00-23:59:59";
        }
        else if (ag->scheduler_type_ == SchedType::WEEKLY_SCHED)
        {
            info["ID"] = ag->id_;
            info["Type"] = DevSchedType::DEV_SCHE_WEEKLY_SCHED;
            info["Date"] = "";
            info["Weekly"] = WeekBinaryToString(ag->date_flag_);
            info["Daily"] = daily;
        }
        else if (ag->scheduler_type_ == SchedType::DAILY_SCHED)
        {
            info["ID"] = ag->id_;
            info["Type"] = DevSchedType::DEV_SCHE_DAILY_SCHED;
            info["Date"] = "";
            info["Weekly"] = "";
            info["Daily"] = daily;
        }

        item["Schedule"].append(info);
    }     
    std::string msg_json = w.write(item);

    //写入文件
    std::string meta_path = GetCommunityScheduleRootDir(dev->office_id, dev->unit_id, dev->node, dev->grade);
    meta_path += dev->mac;
    meta_path += ".json";

    std::string config_path = meta_path;
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, msg_json, SHADOW_TYPE::SHADOW_SCHE,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);

    return 0;    
}


