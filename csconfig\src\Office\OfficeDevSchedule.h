#ifndef __OFFICE_DEV_SCHEDULE_H__
#define __OFFICE_DEV_SCHEDULE_H__
#include <string>
#include "AKCSMsg.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/AccessGroupDB.h"



class OfficeDevSchedule
{
public:
    enum SchedType
    {
        ONCE_SCHED = 0,
        DAILY_SCHED,
        WEEKLY_SCHED,
    };

public:
	OfficeDevSchedule(   );
	~OfficeDevSchedule();

    int UpdateScheduleData(const OfficeDevList &dev_list);
    
    
private:
    int WirteFile(const std::string &filename, const std::string &content);
    int WirteScheduleToJson(const OfficeDevPtr       &dev, AccessGroupInfoPtrList &ag_list);

    //传给设备的类型(和平台数据库不一致)
    enum DevSchedType
    {
        DEV_SCHE_ONCE_SCHED = 0,
        DEV_SCHE_WEEKLY_SCHED,
        DEV_SCHE_DAILY_SCHED,
    };
};

#endif 
