#include <sstream>
#include "OfficeDevUser.h"
#include "DevUser.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "AdaptUtility.h"
#include "DevKey.h"
#include "DevSchedule.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "dbinterface/LadderControl.h" 
#include "WriteFileControl.h"
#include "dbinterface/AccessGroupDB.h" 
#include "dbinterface/UserAccessGroup.h" 
#include "dbinterface/resident/ResidentDevices.h" 
#include "ShadowUserDetailMng.h"


static const DULONG all_user_info_traceid=123456789;
extern CSCONFIG_CONF gstCSCONFIGConf;

OfficeDevUser::OfficeDevUser(OfficeInfoPtr office_info)
{
    office_info_ = office_info;
}

OfficeDevUser::~OfficeDevUser()
{
    
}

void OfficeDevUser::GetDevAccessGroupList(OfficeDevPtr &dev, AccessGroupInfoPtrList &ag_list)
{
    AccessGroupInfoList ag_info_list;
    DEVICE_SETTING dev_setting;
    memset(&dev_setting, 0, sizeof(dev_setting));
    dbinterface::OfficeDevices::TransferOfficePtrToDevSetting(dev, &dev_setting);
    
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
        || dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
       dbinterface::AccessGroup::GetPubMacAccessGroupList(&dev_setting, ag_list);
       dbinterface::AccessGroup::GetDefaultAccessGroupList(&dev_setting, ag_list);
       if (ag_list.size() == 0)
       {
            AK_LOG_INFO << "Public/Unit MAC=" << dev->mac << " Access Group List is null";
       }
    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
       dbinterface::UserAccessGroup::GetUserMacAccessGroupList(&dev_setting, ag_list);
       if (ag_list.size() == 0)
       {
            AK_LOG_INFO << "Personal MAC=" << dev->mac << " Access Group List is null";
       }       
    }
    else
    {
        AK_LOG_WARN << "WriteMetaData device grade error. MAC=" << dev->mac << " Grade=" << dev->grade;
    }    
}

void OfficeDevUser::GetDevUserAccessMateInfoList(OfficeDevPtr &dev, uint32_t ag_id, UserAccessInfoPtrMap &userlist)
{
    UserAccess ua;    
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
        || dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
       ua.GetPubDevAccountListByAccessGroupID(ag_id, userlist);
       ua.GetPubDevPubAccountListByAccessGroupID(ag_id, userlist);
       if (userlist.size() == 0)
       {
            AK_LOG_INFO << "Public/Unit MAC=" << dev->mac << " access group id=" << ag_id << " user access List is null";
       }         

    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    { 
       ua.GetPerDevAccountListByAccessGroupID(ag_id, userlist);
       if (userlist.size() == 0)
       {
            AK_LOG_INFO << "Personal MAC=" << dev->mac << " access group id=" << ag_id << " user access List is null";
       }        
    }   
}

int OfficeDevUser::WirteFile(const std::string &filename, const std::string &content)
{
    //user detail
    if(!ThreadLocalSingleton::GetInstance().GetDbStatus())
    {
        AK_LOG_ERROR << "Alarm Monitoring: Db error. Pause write user detail file:" << filename;
        return -1;    
    }

    FILE* file = fopen(filename.c_str(), "w+");
    if (file == NULL)
    {
        AK_LOG_WARN << "fopen failed " << filename;       
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", filename, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //将配置信息内容写入文件中
    fwrite(content.c_str(), sizeof(char), strlen(content.c_str()) + 1, file);
    fclose(file);
    AK_LOG_INFO << "The user file path is " << filename;

    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(filename.c_str(), AES_ENCRYPT_KEY_V1, filename.c_str());
    }
    return 0;

}


std::string  OfficeDevUser::GetUserAccessInfoKey(uint32_t group_id, OfficeDevPtr &dev)
{
    //group_id 用户自己的/公共的  
    std::stringstream key;
    key << "agid=" << group_id << " devtype=" << dev->grade;
    return key.str();
}


int OfficeDevUser::UpdateMetaData(const OfficeDevList &dev_list)
{
    AK_LOG_INFO << "UpdateMetaData Start.";
    AgKeyUaListCacheMap ag_key_ualist_cache_map;

    for (auto cur_dev : dev_list)
    {
        if(!DevUser::DevTypeSupportUser(cur_dev->dev_type))
        {
            AK_LOG_INFO << "devices mac=" << cur_dev->mac << " type=" << cur_dev->dev_type << " not support user.";
            continue; 
        }
        AccessGroupInfoPtrList ag_list;
        GetDevAccessGroupList(cur_dev, ag_list);
        if (ag_list.size() <= 0)
        {
            AK_LOG_INFO << "devices mac=" << cur_dev->mac << " not bind access group. need clear data.";
            //cur_dev = cur_dev->next;
            //continue; //需要往下执行清空user
        }

        UserAccessInfoPtrMap all_ua_list;
        AgUaListPairList ag_ua_list;
        for (auto ag : ag_list)
        {
            //TODO:不需要一直查询,应该是有很多重复的权限组信息,可以弄个暂存的
            UserAccessInfoPtrMap ua_list;
            std::string key = GetUserAccessInfoKey(ag->id_, cur_dev);

            std::map<std::string, UserAccessInfoPtrMap>::iterator it;            
            it = ag_key_ualist_cache_map.find(key);
            if(it != ag_key_ualist_cache_map.end())
            {
                ua_list = it->second;
                //AK_LOG_INFO << "already exist ua key:" << key;
            }
            else
            {
                GetDevUserAccessMateInfoList(cur_dev, ag->id_, ua_list);
                ag_key_ualist_cache_map.insert(std::make_pair(key, ua_list));
            }
            
            AgUaListPair pair(ag, ua_list);
            ag_ua_list.push_back(pair);
            all_ua_list.insert(ua_list.begin(), ua_list.end());
        }
        
        WriteMetaDataToJson(cur_dev, all_ua_list);        
    }

    AK_LOG_INFO << "UpdateMetaData End.";
    return 0;
}


int OfficeDevUser::UpdatePubDevMetaByPubUser(const std::vector<uint32_t> &staff_ids, const std::vector<uint32_t> &delivery_ids, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdatePubDevMetaByPubUser Start.";
    dbinterface::AccessGroup::GetPubUserAccessGroupDevList(USER_TYPE::STAFF, staff_ids, ret_mac_set);
    dbinterface::AccessGroup::GetPubUserAccessGroupDevList(USER_TYPE::DELIVERY, delivery_ids, ret_mac_set);

    if (ret_mac_set.size() > 0)
    {
        OfficeDevList dev_list;
        dbinterface::OfficeDevices::GetMacListDevList(ret_mac_set, dev_list);
        UpdateMetaData(dev_list);
    }
    AK_LOG_INFO << "UpdatePubDevMetaByPubUser End.";
    return 0;
}

/*更新公共设备的user*/
int OfficeDevUser::UpdatePubDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdatePubDevMetaByAccount Start.";
    if (account_list.size() > 0 )
    {
        dbinterface::AccessGroup::GetAccessGroupDevListByUser(account_list, ret_mac_set);
        OfficeDevList dev_list;
        dbinterface::OfficeDevices::GetMacListDevList(ret_mac_set, dev_list);
        UpdateMetaData(dev_list);
    }
    AK_LOG_INFO << "UpdatePubDevMetaByAccount End.";
    return 0;
}

/*更新用户设备的user*/
int OfficeDevUser::UpdateUserDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdateUserDevMetaByAccount Start.";
    if (account_list.size() > 0 )
    {
        dbinterface::UserAccessGroup::GetUserAccessGroupDevList(account_list, ret_mac_set);
        OfficeDevList dev_list;
        dbinterface::OfficeDevices::GetMacListDevList(ret_mac_set, dev_list);
        UpdateMetaData(dev_list);
    }
    AK_LOG_INFO << "UpdateUserDevMetaByAccount End.";
    return 0;
}

/*更新家庭下设备的user*/
int OfficeDevUser::UpdateUserDevMetaByNodes(const std::vector<std::string> &node_list, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdateUserDevMetaByNodes Start.";
    dbinterface::ResidentDevices::GetNodesDevList(node_list, ret_mac_set);
    if (ret_mac_set.size() > 0 )
    {
        OfficeDevList dev_list;
        dbinterface::OfficeDevices::GetMacListDevList(ret_mac_set, dev_list);
        UpdateMetaData(dev_list);
    }
    AK_LOG_INFO << "UpdateUserDevMetaByNodes End.";
    return 0;
}

int OfficeDevUser::UpdatePubDevMetaByAccessGroupID(const std::vector<uint32_t> &ids, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdatePubDevMetaByAccessGroupID Start.";
    for (auto id :  ids)
    {
        if (id > 0)
        {
            dbinterface::AccessGroup::GetMacListByAccessGroupID(id, ret_mac_set);
        }
    } 
    if (ret_mac_set.size() > 0 )
    {
        OfficeDevList dev_list;
        dbinterface::OfficeDevices::GetMacListDevList(ret_mac_set, dev_list);
        UpdateMetaData(dev_list);

    }
    AK_LOG_INFO << "UpdatePubDevMetaByAccessGroupID End.";
}


int OfficeDevUser::CreateRequestUserListDetailData(OfficeDevPtr &dev, const UserUUIDList &user_list, DULONG traceid, 
                                                      std::string &file_path,std::string &file_md5)
{
    int ret = -1;
    AccessGroupInfoPtrList ag_list;
    GetDevAccessGroupList(dev, ag_list);
    if (ag_list.size() <= 0)
    {
        AK_LOG_WARN << "UpdateMetaData device " << dev->mac << " not found Access Group List!";
        return ret;
    }

    //权限组对应的人员列表
    AgUaListPairList ag_ua_list;
    UserAccessInfoPtrMap all_ua_list;
    for (auto ag : ag_list)
    {
        UserAccessInfoPtrMap ua_list;
        GetDevUserAccessMateInfoList(dev, ag->id_, ua_list);
        AgUaListPair pair(ag, ua_list);
        ag_ua_list.push_back(pair);

        all_ua_list.insert(ua_list.begin(), ua_list.end());
    }

    UserAccessInfoPtrMap tmp_dev_ua_list;
    for (const auto uuid : user_list)
    {
        UserAccessInfoPtrMapIter it;
        it = all_ua_list.find(uuid);
        if (it != all_ua_list.end())
        {
            tmp_dev_ua_list.insert(*it);
        }
        else
        {
            AK_LOG_INFO << "Devices request uid=" << uuid << " not found";
        }
    }
    
    
    if (traceid == USER_MAINTANCE_ALL_TRACEID)//维护过来的接口直接写全部
    {
        UserAccess ua; 
        ua.GetPerUserDetailInfo(all_ua_list);
        ret = WriteDetailDataForGiven(dev, ag_ua_list, all_ua_list, traceid, file_path);
    }
    else
    {
        UserAccess ua; 
        ua.GetPerUserDetailInfo(tmp_dev_ua_list);
        ret = WriteDetailDataForGiven(dev, ag_ua_list, tmp_dev_ua_list, traceid, file_path);
    }
    file_md5 = dev->user_info_md5; 
    return ret;
}


int OfficeDevUser::GetDetailDataForRequest(OfficeDevPtr &dev, const UserUUIDList &user_list, DULONG traceid, std::string &file_path, std::string &file_md5)
{
    return CreateRequestUserListDetailData(dev, user_list, traceid, file_path, file_md5);
}


int OfficeDevUser::WriteMetaDataToJson(OfficeDevPtr &dev, const UserAccessInfoPtrMap &user_list)
{
    Json::Value item;
    Json::FastWriter w;
    item["UserType"] = 0;


    std::vector<int> unit_list;
    int manage_all_flag = 1;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev->grade)
    {
        manage_all_flag = GetDeviceSettingInstance()->GetManagementBuilding(dev->id, unit_list);
    }

    for (auto ua_pair : user_list)
    {
        UserAccessInfoPtr ua = static_cast<UserAccessInfoPtr>(ua_pair.second);
		if (ua->GetUserType() == USER_TYPE::ACCOUNT 
             && (1 != manage_all_flag) 
             && !GetDeviceControlInstance()->DeviceIsBelongBuilding(dev->dev_type, ua->GetUnitID(), unit_list))
		{
		    //AK_LOG_INFO << "devices mac=" << dev->mac << " not belong the unit id:" << ua->GetUnitID();
			continue;
	    }
        if (ua->GetUUID().size() == 0)
        {
            continue;
        }
        Json::Value user;
        user["PerID"] = ua->GetUUID();
        user["Meta"] = ua->GetMeta();
        item["User"].append(user);
    }   

    std::string msg_json = w.write(item);

    //写入文件
    std::string meta_path = GetCommunityUserRootDir(dev->office_id, dev->unit_id, dev->node, dev->grade);
    meta_path += dev->mac;
    meta_path += ".json";

    std::string config_path = meta_path;
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, msg_json, SHADOW_TYPE::SHADOW_USERMETA,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);

    return 0;
}

void OfficeDevUser::CheckCodeUnique(const std::string &mac, std::set<std::string> &unique_list, const std::list<std::string> &codelist)
{
    std::string keystr;
    int old_count = unique_list.size();
    int count = 0;
    for(auto &code : codelist)
    {
        if (code.size() > 0)
        {
            
            unique_list.insert(code);
            keystr += code;
            keystr += ",";
            count ++;
        }
    }
    int new_count = unique_list.size();
    if (new_count != old_count + count)
    {
        
        std::string error = mac;
        error += " Pin/Rf some of them are not unique:";
        error += keystr;
        //modify chenzhx 20230413 end user add NFC code from pm web
        //AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", error, AKCS_MONITOR_ALARM_DATA_ERROR);

    }
}

int OfficeDevUser::WriteDetailDataToJson(OfficeDevPtr &dev, const AgUaListPairList &uag_map, 
     const UserAccessInfoPtrMap &user_list, const std::string &file_path)
{
    Json::Value item;
    Json::FastWriter w;
    
    
    item["UserType"] = 0;
    
    std::vector<int> unit_list;
    int manage_all_flag = 1;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev->grade)
    {
        manage_all_flag = GetDeviceSettingInstance()->GetManagementBuilding(dev->id, unit_list);
    }
    
    std::set<std::string> unique_pin_list;
    std::set<std::string> unique_rf_list;
    for (auto ua_pair : user_list)
    {
        UserAccessInfoPtr ua = static_cast<UserAccessInfoPtr>(ua_pair.second);
		if (ua->GetUserType() == USER_TYPE::ACCOUNT 
             && (1 != manage_all_flag) 
             && !GetDeviceControlInstance()->DeviceIsBelongBuilding(dev->dev_type, ua->GetUnitID(), unit_list))
		{
		    //AK_LOG_INFO << "devices mac=" << dev->mac << " not belong the unit id:" << ua->GetUnitID();
			continue;
	    }
    
        Json::Value schedule;
        Json::Value user;
        if (ua->GetUUID().size() == 0)
        {
            continue;
        }             
        user["PerID"] = ua->GetUUID();
        user["Name"] = ua->GetName();
        user["Card"] = ua->GetRfString();
        user["PerType"] = ua->GetUserType();
        if (ua->GetUserType() == USER_TYPE::ACCOUNT)
        {
            //add bu xuzr,检查pin高级功能.
            if (office_info_->CheckFeature(OfficeInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_PIN) && !office_info_->IsExpire())
            {
                user["PriKey"] = ua->GetOfficePinString(office_info_->IsAllowCreatePin());
            }
            else 
            {
                AK_LOG_INFO << "Office ID:" << dev->office_id << " Switch(pin) feature check result is 0, allow app create pin";
                //如果社区过期则默认允许app创建pin
                user["PriKey"] = ua->GetOfficePinString(OfficeInfo::AllowCreatePin::ALLOW);
            }
            user["LiftFloorNum"] = ua->GetFloor();
        }
        else
        {
            user["PriKey"] = ua->GetOfficePinString(OfficeInfo::AllowCreatePin::DENY);
            //delivery梯控
            user["LiftFloorNum"] = dbinterface::LadderControl::GetFloorByUUID(ua->GetDBUUID(), dev->unit_id,  ua->GetUserType());
        }
        //check unique
        std::list<std::string> pin_list;
        std::list<std::string> rf_list;
        ua->GetRFList(rf_list);
        ua->GetPinListForMonitor(pin_list);
        CheckCodeUnique(dev->mac, unique_pin_list, pin_list);
        CheckCodeUnique(dev->mac, unique_rf_list, rf_list);
        
        user["FaceUrl"] = ua->GetFaceUrl(dev->mac);
        user["FaceMD5"] = ua->GetFaceMd5();

        //通过马上遍历的方式，比起全部组装好【用户-权限组列表】对应关系复杂度更低。
        //因为组装对应关系 需要遍历所有权限组下所有用户，但是其实我们设备发过来的更新列表只有上百个。
        AccessGroupInfoPtrList ag_list;
        for (auto pair : uag_map)
        {
            AccessGroupInfoPtr ag = pair.first;
            UserAccessInfoPtrMap ua_list = pair.second;
            
             UserAccessInfoPtrMapIter it;
             it = ua_list.find(ua->GetUUID());
             if (it != ua_list.end())
             {
                 ag_list.push_back(ag);
             }
        }
        for (auto ag : ag_list)
        {
            Json::Value tmp;
            int default_relay = 0;  
            GetValueByRelay(dev->relay, default_relay);
            std::string door_num = RelayToString(ag->relay_&default_relay);

            int default_security_relay = 0;
            GetValueByRelay(dev->security_relay, default_security_relay);
            std::string security_door = RelayToString(ag->security_relay_ & default_security_relay);

            tmp["ScheduleID"] = std::to_string(ag->id_);
            tmp["Relay"] = door_num;
            if (!security_door.empty())
            {
                tmp["SecurityRelay"] = security_door;
            }
            schedule.append(tmp);
        }

        user["ScheduleRelay"] = schedule;
        item["User"].append(user);
    }        

    std::string msg_json = w.write(item);

    if (WirteFile(file_path, msg_json))
    {
        return -1;
    }    
    //计算MD5并保存到列表中以便后面一起更新到数据库中
    std::string md5 = akuvox_encrypt::MD5::GetFileMD5(file_path);
    Snprintf(dev->user_info_md5, sizeof(dev->user_info_md5), md5.c_str());
    return 0;
}



int OfficeDevUser::WriteDetailDataForGiven(OfficeDevPtr &dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list,
                                          DULONG traceid, std::string &path)
{
    //写入文件
    std::string download_path;
    std::string detail_path = GetUserDetailDownloadPath(dev->mac, download_path);
    detail_path += dev->mac;
    detail_path += "_";
    detail_path += GetNowTime();
    detail_path += "_";
    detail_path += std::to_string(traceid);
    detail_path += ".json";

    path = download_path;
    path += dev->mac;
    path += "_";
    path += GetNowTime();    
    path += "_";
    path += std::to_string(traceid);
    path += ".json";

    WriteDetailDataToJson(dev, uag_map, user_list, detail_path);    

    auto& pool = ConfigUserDetailFdfsUploaderPool::GetInstance();
    {
        ConfigUserDetailFdfsUploaderPool::UploaderHandle handle(pool);
        std::string path_after;
        if (handle.UploadFile(detail_path, path_after) == 0)
        {
            path = path_after;
        }
    }
    
    return 0;
}

int OfficeDevUser::WriteDetailDataForAll(OfficeDevPtr &dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list)
{
    //写入文件
    std::string all_path = GetCommunityUserAllDetailDir(dev->office_id, dev->unit_id, dev->node, dev->grade);
    all_path += dev->mac;
    all_path += ".json";    
    WriteDetailDataToJson(dev, uag_map, user_list, all_path);    
    return 0;
}
