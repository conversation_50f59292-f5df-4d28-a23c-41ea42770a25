#ifndef __OFFICE_DEV_USER_H__
#define __OFFICE_DEV_USER_H__
#include <string>
#include "dbinterface/office/OfficeInfo.h"
#include "AKCSMsg.h"
#include "UserAccess.h"
#include "UserAccessInfo.h"
#include "DevUser.h"
#include "dbinterface/CommunityInfo.h"
#include <set>
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"


#define USER_MAINTANCE_ALL_TRACEID 10000 /*我们的traceid算法不会出现这个值*/
#define USER_MAINTANCE_APPOINT_TRACEID 10001 /*我们的traceid算法不会出现这个值*/


class OfficeDevUser
{
    
public:
	OfficeDevUser(OfficeInfoPtr office_info);
	~OfficeDevUser();

    int UpdateMetaData(const OfficeDevList &dev_list);
    
    int UpdatePubDevMetaByPubUser(const std::vector<uint32_t> &staff_ids, const std::vector<uint32_t> &delivery_ids,
        std::set<std::string> &ret_mac_set);
    int UpdatePubDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_list); 
    int UpdatePubDevMetaByAccessGroupID(const std::vector<uint32_t> &ids, std::set<std::string> &ret_mac_set);
    int UpdateUserDevMetaByNodes(const std::vector<std::string> &node_list, std::set<std::string> &ret_mac_set);
    int UpdateUserDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_set);
    int GetDetailDataForRequest(OfficeDevPtr & dev, const UserUUIDList &list, DULONG traceid, std::string &file_path, std::string &file_md5);
    
    static void GetDevAccessGroupList(OfficeDevPtr &dev, AccessGroupInfoPtrList &ag_list);
private:
    int CreateRequestUserListDetailData(OfficeDevPtr &dev, const UserUUIDList &list, DULONG traceid,  
                               std::string &file_path, std::string &file_md5);
    void GetDevUserAccessMateInfoList(OfficeDevPtr &dev, uint32_t ag_id, UserAccessInfoPtrMap &usre_list);
    int WriteDetailDataForGiven(OfficeDevPtr &dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list,
                                              DULONG traceid, std::string &path);
    int WriteDetailDataForAll(OfficeDevPtr &dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list);
    int WriteMetaDataToJson(OfficeDevPtr &dev, const UserAccessInfoPtrMap &user_list);
    int WriteDetailDataToJson(OfficeDevPtr &dev, const  AgUaListPairList &uag_map, 
        const UserAccessInfoPtrMap &user_list,  const std::string &file_path);
    int WirteFile(const std::string &filename, const std::string &content);
    std::string  GetUserAccessInfoKey(uint32_t group_id, OfficeDevPtr & dev);
    
    void CheckCodeUnique(const std::string &mac, std::set<std::string> &unique_list, const std::list<std::string> &codelist);

private:
    OfficeInfoPtr office_info_;
    
};




#endif 
