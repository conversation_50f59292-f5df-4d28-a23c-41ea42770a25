#include "stdafx.h"
#include <functional>
#include "AkcsCommonDef.h"
#include "OfficeFileUpdateControl.h"
#include "OfficeUnixSocketControl.h"
#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <vector>
#include <string>
#include <sstream>
#include <evpp/evnsq/producer.h>
#include "AK.Adapt.pb.h"
#include "AK.AdaptOffice.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsWebMsgSt.h"
#include "FileUpdateControl.h"
#include "CommConfigHandle.h"
#include "PerConfigHandle.h"
#include "AKCSView.h"
#include "IPCControl.h"
#include "PersonnalDeviceSetting.h"
#include "DeviceSetting.h"
#include "FaceMng.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "OfficeConfigHandle.h"
#include "AkcsMonitor.h"
#include "OfficeDevUser.h"
#include "OfficeDevSchedule.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "ShadowMng.h"
#include "util.h"
#include "SnowFlakeGid.h"
#include "MsgIdToMsgName.h"

extern CSCONFIG_CONF gstCSCONFIGConf;



OfficeFileUpdateControl* OfficeFileUpdateControl::office_file_update_ = nullptr;

OfficeFileUpdateControl* OfficeFileUpdateControl::Instance()
{
    if (!office_file_update_)
    {
        office_file_update_ = new OfficeFileUpdateControl();
    }
    return office_file_update_;
}

OfficeFileUpdateControl::OfficeFileUpdateControl()
{

}

void OfficeFileUpdateControl::OnOfficeFileUpdate(int changetype, uint32_t office_id, uint32_t department_id, const std::string &node, std::vector<std::string> &macs)
{
    std::string mac_0;
    if (macs.size() > 0)
    {
        mac_0 = macs[0];
    }
    std::string macs_string;
    for (auto &mac : macs)
    {
        macs_string += mac;
        macs_string += ";";
    }

    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();

    
    AK_LOG_INFO << "OnOfficeFileUpdate change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype)<< " node= " << node
                << " office_id= " << office_id << " department_id= " << department_id << " mac= " << macs_string << " traceid=" << traceid;

    if (CheckStrInFilter(gstCSCONFIGConf.mng_id_filter, to_string(office_id)))
    {
        AK_LOG_INFO << "OnOfficeFileUpdate return " << office_id;    
        return;
    }

    if (changetype == WEB_COMM_DELETE_COMMUNITY)
    {
        AK_LOG_INFO << "Request delete office mngid:" << office_id;        
        CAKCSView::GetInstance()->NotifyDelCommunityPics(office_id);
        CFaceMng::GetInstance().DelFaceMngByMngId(office_id);

        std::string community_dir = "/var/www/download/community/";
        if (office_id > 0)
        {
            std::string community_dir = "/var/www/download/community/";
            community_dir += std::to_string(office_id);
            DeleteDir(community_dir);
        }
        OfficeUnixMsgControl::Instance()->NotifyCommunityChange(office_id);
        return;
    }
    
    OfficeConfigHandle handle(office_id, department_id, node, macs);
    if(handle.IsNewOffice())
    {
       AK_LOG_INFO << "is new office return " << office_id;   
       return;
    }
    if(!handle.InitSuccess())
    {
       AK_LOG_INFO << "init office info error ,project is correct. return " << office_id;   
       return;
    }
    std::time_t tstart = std::time(0);
    switch(changetype)
    {
        //csmain
        case CSMAIN_OFFICE_DEV_IP_CHANGE:
        {
            handle.UpdateNodeDevConfig();
            handle.UpdateNodeContactEvent();
            //刚添加设备时候需要更新
            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
            
            break;
        }
        case CSMAIN_OFFICE_DEV_UPGRADE://升级时候全部更新
        case CSMAIN_OFFICE_DEV_MAINTANCE:
        {
            handle.UpdateNodeDevConfig();
            handle.UpdateNodeDevContactList();
            
            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
            
            break;
        }
        case CSMAIN_OFFICE_UNIT_DEV_IP_CHANGE://刚添加设备时候走ip变化，不走升级流程
        {            
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitAllNodeDevContactList();
            handle.UpdatePubDevContactList();
            handle.UpdateUnitDevContactList();//刷新管理机的联系人

            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
            
            break;
        }          
        case CSMAIN_OFFICE_UNIT_DEV_UPGRADE:
        case CSMAIN_OFFICE_UNIT_DEV_MAINTANCE:           
        {
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitDevContactList();
            
            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);              
                   
            break;
        }
        
        case CSMAIN_OFFICE_PUB_DEV_IP_CHANGE:
        {
            handle.UpdatePubDevConfig();
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllNodeDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();

            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
            
            break;
        }
        case CSMAIN_OFFICE_PUB_DEV_UPGRADE:
        case CSMAIN_OFFICE_PUB_DEV_MAINTANCE:
        {
            handle.UpdatePubDevConfig();
            handle.UpdatePubDevContactList();

            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);  
            
            break;
        }
        case CSMAIN_OFFICE_ACCOUNT_NFC_UPDATE:
        {
            handle.UpdateNodeUser();
            break;
        }        
        //个人
        case WEB_OFFICE_NODE_UPDATE:/*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
        {
            handle.UpdateNodeConfigEvent();
            handle.UpdateNodeContactEvent();
            break;
        }
        //卡要更新，因为nfc/ble是和用户绑定的
        case WEB_OFFICE_ADD_USER:
        {
            handle.UpdateNodeContactEvent();
            handle.UpdateNodeDevSchedule();
            break;
        }
        case WEB_OFFICE_DEL_USER:
        case WEB_OFFICE_MODIFY_USER:
        {
            handle.UpdateNodeDevConfig();//pubutton/robin call
            handle.UpdateNodeContactEvent(); 
            
            handle.UpdateNodeDevSchedule();
            handle.UpdateNodeUser();
            if (changetype == WEB_OFFICE_DEL_USER) 
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::DELETE_USER;
                snprintf(refresh_cache.node, sizeof(refresh_cache.node), "%s", node.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }

            break;
        }
        case WEB_OFFICE_ADD_DEV:
        case WEB_OFFICE_DEL_DEV:
        case WEB_OFFICE_MODIFY_DEV:
        {
            handle.UpdateNodeConfigEvent();
            handle.UpdateNodeContactEvent();
          
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_OFFICE_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", mac_0.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }
            
            for (auto &mac : macs)
            {
               handle.UpdateMacUser(mac);
               handle.UpdateDevSchedule(mac);
            }
            break;
        }    
        //单元
        case WEB_OFFICE_UNIT_ADD_DEV:
        case WEB_OFFICE_UNIT_DEL_DEV:
        case WEB_OFFICE_UNIT_MODIFY_DEV:
        {
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitDevContactList();
            handle.UpdatePubDevContactList();

            handle.UpdateUnitAllNodeDevContactList();
   
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_OFFICE_UNIT_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", mac_0.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }

            if (changetype != WEB_OFFICE_UNIT_DEL_DEV)
            {
                for (auto &mac : macs)
                {
                   handle.UpdateMacUser(mac);
                   handle.UpdateDevSchedule(mac);
                }
            }
            break;
        }       
        //public
        case WEB_OFFICE_PUB_ADD_DEV:
        case WEB_OFFICE_PUB_DEL_DEV:
        case WEB_OFFICE_PUB_MODIFY_DEV:
        {
            handle.UpdatePubDevConfig();
            handle.UpdatePubDevContactList();

            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();
            handle.UpdateCommunityAllUnitDevConfig();   //更新管理机按键
  
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_OFFICE_PUB_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", mac_0.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }
            for (auto &mac : macs)
            {
               handle.UpdateMacUser(mac);
               handle.UpdateDevSchedule(mac);
            }
            break;
        }       
        //community info
        case WEB_OFFICE_INFO://社区信息
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            break;
        }
        case WEB_OFFICE_MOTION:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            break;
        }   
        case WEB_OFFICE_IMPORT_OFFICE:
        {
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();

            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();

            handle.UpdateCommunityAllDevUser();
            handle.UpdateCommunityAllDevSchedule();
            
            break;
        }
        case WEB_OFFICE_DELETE_OFFICE:
        {
            break;
        }
        case WEB_OFFICE_ADD_BUILDING:
        {
            break;
        }
        case WEB_OFFICE_DEL_BUILDING:
        {
            break;
        }
        case WEB_OFFICE_MODIFY_BUILDING:
        {
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitDevContactList();
            handle.UpdatePubDevContactList();
            handle.UpdateUnitAllNodeDevContactList();
            handle.UpdateUnitDevSchedule();
            break;
        }
        case WEB_OFFICE_MODIFY_TIMEINFO:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            break;
        }  
        case WEB_OFFICE_UPDATE_MAC_CONFIG:
        {
            handle.UpdateMacDevConfig(mac_0);
            break;
        }
        case WEB_OFFICE_NOTIFY_FLOW_OUT_OF_LIMIT:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            break;
        }
        case WEB_OFFICE_ALLOW_CREATE_PIN:
        {
            handle.UpdateCommunityAllDevUser();         
            break;
        }
        case WEB_OFFICE_FEATURE_PLAN_RENEW:
        {
            //目前高级功能只有涉及到快递间的
            //还有app pin
            dbinterface::OfficePersonalAccount::UpdateAllDataVersion(office_id);
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();

            handle.UpdateCommunityAllDevUser();
            break;
        }   
        case WEB_OFFICE_UPDATE_NODE_PUB_USER:
        {
            //更新员工公共权限组内设备的User
            handle.UpdateNodePubDevUser();
            break;
        }
        case WEB_OFFICE_MODIFY_DEPARTMENT_NAME:
        {
            //刷unit+pub设备的contact和schedule
            handle.UpdateUnitDevSchedule();
            handle.UpdatePubDevSchedule();
            handle.UpdateUnitDevContactList();
            handle.UpdatePubDevContactList();
            //刷unit设备的config,首页展示building字段
            handle.UpdateUnitDevConfig();
            break;
        }
        case WEB_OFFICE_UPDATE_NODE_CONTACT:
        {
            handle.UpdateNodeContactEvent();
            break;
        }
        case WEB_OFFICE_MODIFY_CONTACT_DISPLAY_ORDER:
        {
            handle.UpdateOfficeContactEvent();
            break;
        }
        default:
        {
            AK_LOG_WARN << "not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);
            std::string error = "office change message, not define this change type=";
            error += changetype;
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", error, AKCS_MONITOR_ALARM_DATA_ERROR);            
        }
    }
    
    std::time_t tend = std::time(0);
    AK_LOG_INFO << "officeid [" << office_id << "] update file time: " << tend - tstart << "s traceid=" << traceid << " change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);    

}


//远程配置设备的配置
void OfficeFileUpdateControl::OnOfficeFileUpdate(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnOfficeFileUpdate The param is NULL";
        return;
    }

    AK::AdaptOffice::WebOfficeModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    std::vector<std::string> macs;
    uint32_t mac_cnt = msg.mac_list_size();
    for (uint32_t i = 0; i < mac_cnt; ++i)
    {
        std::string mac = msg.mac_list(i);
        macs.push_back(mac);
    }
    uint32_t office_id = msg.office_id();
    uint32_t department_id = msg.department_id();
    std::string node = msg.node();
    int change_type = msg.change_type();
    OnOfficeFileUpdate(change_type, office_id, department_id,node, macs);
    OnDevUpdateCommonHandle(change_type, macs);
}


//权限组更新配置
void OfficeFileUpdateControl::OnOfficeAccessGroupHandle(int changetype, uint32_t mng_id, const std::string &node, std::set<std::string> &macs, uint32_t ag_id)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK_LOG_INFO << "OnOfficeAccessGroupHandle change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype) << " node= " << node << " office_id= " << mng_id << " traceid=" << traceid;
    if (CheckStrInFilter(gstCSCONFIGConf.mng_id_filter, to_string(mng_id)))
    {
        AK_LOG_INFO << "OnOfficeAccessGroupHandle return " << mng_id;    
        return;
    }  

    OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(mng_id);

    if(office_info->IsNew())
    {
       AK_LOG_INFO << "is new office return " << mng_id;   
       return;
    }
    if(!office_info->InitSuccess())
    {
       AK_LOG_INFO << "init office info error ,project is correct. return " << mng_id;   
       return;
    }     
    std::time_t tstart = std::time(0);
    switch (changetype)
    {
        case WEB_OFFICE_ADD_ACCOUNT_ACCESS:
        case WEB_OFFICE_MODIFY_ACCOUNT_ACCESS:
        {
            OfficeDevUser user(office_info);
            std::vector<std::string> accounts;
            std::set<std::string> pub_mac_set;
            accounts.push_back(node);
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
            //更新scedule
            OfficeDevList devlist;
            dbinterface::OfficeDevices::GetMacListDevList(pub_mac_set, devlist);
            OfficeDevSchedule schedule;
            schedule.UpdateScheduleData(devlist);
            break;
        }
        case WEB_OFFICE_ADD_USER_ACCESSGROUP:
        case WEB_OFFICE_MODIFY_USER_ACCESSGROUP:
        case WEB_OFFICE_MODIFY_USER_ACCESSGROUP_DEVICE:
        {
            OfficeDevUser user(office_info);
            std::vector<std::string> accounts;
            std::set<std::string> user_mac_set;
            accounts.push_back(node);
            //更新用户关联的设备user
            user.UpdateUserDevMetaByAccount(accounts, user_mac_set);
            //更新scedule
            OfficeDevList devlist;
            dbinterface::OfficeDevices::GetMacListDevList(user_mac_set, devlist);
            OfficeDevSchedule schedule;
            schedule.UpdateScheduleData(devlist);
            break;
        }
        //根据用户刷新权限组及个人设备，当前只有新社区和办公修改pin调用
        case WEB_OFFICE_MODIFY_USER_ALL_ACCESS:
        {
            OfficeDevUser user(office_info);
            std::vector<std::string> accounts;
            std::set<std::string> pub_mac_set;
            std::set<std::string> user_mac_set;
            accounts.push_back(node);
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
            //更新用户关联的设备user
            user.UpdateUserDevMetaByAccount(accounts, user_mac_set);
            //更新scedule
            OfficeDevList pub_devlist;
            OfficeDevList user_devlist;
            OfficeDevSchedule schedule;
            dbinterface::OfficeDevices::GetMacListDevList(pub_mac_set, pub_devlist);
            schedule.UpdateScheduleData(pub_devlist);
            dbinterface::OfficeDevices::GetMacListDevList(user_mac_set, user_devlist);
            schedule.UpdateScheduleData(user_devlist);
            break;
        }
        case WEB_OFFICE_DEL_ACCOUNT_ACCESS:
        {
            //删除用户时才回去删AccountAccess表，需要根据权限组id刷新权限组设备配置
            OfficeDevUser user(office_info);

            std::vector<uint32_t> ag_ids;
            ag_ids.push_back(ag_id);
            std::vector<std::string> accounts;
            std::set<std::string> pub_mac_set;
            accounts.push_back(node);
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
            //更新scedule
            OfficeDevList devlist;
            dbinterface::OfficeDevices::GetMacListDevList(pub_mac_set, devlist);
            OfficeDevSchedule schedule;
            schedule.UpdateScheduleData(devlist);
            break;
        }
        case WEB_OFFICE_MODIFY_ACCESS_GROUP:
        {
            std::set<std::string > mac_set;
            if (0 == macs.size())
            {
                //AccessGroup表数据分析，根据权限组ID查mac更新配置
                dbinterface::AccessGroup::GetMacListByAccessGroupID(ag_id, mac_set);
            }
            else
            {
                //AccessGroupDevice表数据分析，根据web数据收集给出的mac更新配置
                mac_set.insert(macs.begin(),macs.end());
            }

            OfficeDevList dev_list;
            dbinterface::OfficeDevices::GetMacListDevList(mac_set, dev_list);
            //更新Schedule
            OfficeDevSchedule schedule;
            schedule.UpdateScheduleData(dev_list);

            //更新user
            OfficeDevUser user(office_info);
            user.UpdateMetaData(dev_list);
            break;
        }
        case WEB_OFFICE_MODIFY_STAFF:
        case WEB_OFFICE_MODIFY_DELIVERY:
        {
            std::set<std::string> pub_mac_set;
            std::vector<uint32_t> ag_ids;

            if (ag_id > 0)
            {
                ag_ids.push_back(ag_id);
                OfficeDevUser user(office_info);
                user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
            }
            break;
        }
        default:
        {
            AK_LOG_WARN << "[OnOfficeAccessGroupHandle] not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);            
        }
    }

    std::time_t tend = std::time(0);
    AK_LOG_INFO << "officeid [" << mng_id << "] update file time: " << tend - tstart << "s traceid=" << traceid << " change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype); 
    
}


/*处理和写配置无关的信息*/
void OfficeFileUpdateControl::OnDevUpdateCommonHandle(int changetype, std::vector<std::string> &macs)
{
    if (macs.size() <= 0)
    {
        //AK_LOG_WARN << "OnDevUpdateCommonHandle: mac is null! changetype=" << changetype;
        return;
    }
    std::string mac = macs[0];

    switch (changetype)
    {
        case WEB_PER_ADD_DEV:
        case WEB_COMM_ADD_DEV:
        case WEB_COMM_UNIT_ADD_DEV:
        case WEB_COMM_PUB_ADD_DEV:   
        {
           AK_LOG_INFO << "Request add dev mac:" << mac;
           GetIPCControlInstance()->SendPersonalReportStatus(mac);
           CSP2A_DEV_CLEAN_DEVICE_CODE clean_dev_code;
           ::snprintf(clean_dev_code.szMacs, sizeof(clean_dev_code.szMacs), "%s", mac.c_str());
           if (GetIPCControlInstance()->SendDevCleanDeviceCode(&clean_dev_code) != 0)
           {
               AK_LOG_WARN << "SendDevCleanDeviceCode failed";
               return;
           }
           break;
        }
        case WEB_PER_DEL_DEV:
        case WEB_COMM_DEL_DEV:
        case WEB_COMM_UNIT_DEL_DEV:
        case WEB_COMM_PUB_DEL_DEV: 
        {

            AK_LOG_INFO << "Request delete dev mac:" << mac;
            //通知csmain去下发注销sip的指令
            if (GetIPCControlInstance()->SendPerDevLogOutSip(mac) != 0)
            {
                AK_LOG_WARN << "Send personal dev logout sip msg failed";
            }

            //删除掉设备的配置文件
            //删除这个设备的截图和motion
            // CAKCSView::GetInstance()->NotifyPerDelDevPics(mac);

            //删除mac对应的配置文件
            if (mac.length() > 8)
            {
                AKCS::Singleton<CShadowMng>::instance().DeleteDevShadow(mac);
            }          
            break;
        }
        case WEB_PER_MODIFY_DEV:
        case WEB_COMM_MODIFY_DEV:
        case WEB_COMM_UNIT_MODIFY_DEV:
        case WEB_COMM_PUB_MODIFY_DEV: 
        {
            break;
        }
        case WEB_OFFICE_DELETE_OFFICE:
        {
            for(const auto& strmac : macs)
            {
                //通知csmain去下发注销sip的指令
                AK_LOG_INFO << "Request delete office, mac:" << mac;
                if (GetIPCControlInstance()->SendPerDevLogOutSip(strmac) != 0)
                {
                    AK_LOG_WARN << "Send personal dev logout sip msg failed";
                }

                //删除掉设备的配置文件
                //删除这个设备的截图和motion
                // CAKCSView::GetInstance()->NotifyPerDelDevPics(strmac);

                //删除mac对应的配置文件
                if (mac.length() > 8)
                {
                    AKCS::Singleton<CShadowMng>::instance().DeleteDevShadow(mac);
                }
            }
            break;
        }
    }  
}



