#ifndef __OFFICE_FILE_UPDATE_CONTROL_H__
#define __OFFICE_FILE_UPDATE_CONTROL_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include "AkcsCommonDef.h"
class OfficeFileUpdateControl
{
public:
    OfficeFileUpdateControl();
    static OfficeFileUpdateControl* Instance();
    void OnOfficeFileUpdate(int changetype, uint32_t office_id, uint32_t department_id, 
        const std::string &node, std::vector<std::string> &macs);
    //远程配置设备的配置
    void OnOfficeFileUpdate(void* msg_buf, unsigned int msg_len);
    /*处理和写配置无关的信息*/
    void OnDevUpdateCommonHandle(int changetype, std::vector<std::string> &macs);
    void OnOfficeAccessGroupHandle(int changetype, uint32_t mng_id, const std::string &node, std::set<std::string> &macs, uint32_t ag_id);
private:
    static OfficeFileUpdateControl* office_file_update_;
};




#endif //__OFFICE_FILE_UPDATE_CONTROL_H__