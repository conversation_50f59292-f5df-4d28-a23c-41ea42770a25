#ifndef __OFFICE_IPC_CONTROL_H__
#define __OFFICE_IPC_CONTROL_H__

#include "stdint.h"

typedef struct CSP2A_REBOOT_DEVICE_T  CSP2A_REBOOT_DEVICE;

//modified by chenyc,2019-03-05, 由原先的跟csmain的直接通信改成经过csroute,ipc的名称暂时不变.
class OfficeIPCControl
{
public:
    OfficeIPCControl();
    virtual ~OfficeIPCControl();
    static OfficeIPCControl* GetInstance();

    int SendConfigFileChange(CSP2A_CONFIG_FILE_CHANGE* filechange);

private:
    static OfficeIPCControl* instance;
};
#endif //__IPC_CONTROL_H__

