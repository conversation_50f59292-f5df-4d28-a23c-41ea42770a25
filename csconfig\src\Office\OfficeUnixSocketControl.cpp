#include "stdafx.h"
#include <functional>
#include "OfficeUnixSocketControl.h"
#include "AKCSView.h"
#include "FileUpdateControl.h"
#include "OfficeFileUpdateControl.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "AkcsCommonDef.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "OfficeDevUser.h"
#include "OfficeDevSchedule.h"
#include "DeviceControl.h"
#include "DataAnalysis.h"
#include "DeviceSetting.h"
#include "OfficeIPCControl.h"
#include "AK.AdaptOffice.pb.h"
#include "AK.CrontabOffice.pb.h"
#include "OfficeCsmainMsgHandle.h"
#include "SnowFlakeGid.h"


extern CSCONFIG_CONF gstCSCONFIGConf;
OfficeUnixMsgControl* OfficeUnixMsgControl::office_unix_msg_instance_ = nullptr;

OfficeUnixMsgControl* OfficeUnixMsgControl::Instance()
{
    if (!office_unix_msg_instance_)
    {
        office_unix_msg_instance_ = new OfficeUnixMsgControl();
    }
    return office_unix_msg_instance_;
}

OfficeUnixMsgControl::OfficeUnixMsgControl()
{

}

int OfficeUnixMsgControl::OnSocketMsg(void* msg_buf, unsigned int len)
{
    uint32_t id = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 4)));
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 8)));
    //办公
    if (project_type != project::OFFICE)
    {
        AK_LOG_WARN << "Message Type is not OFFICE";
        return 0;
    }
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK_LOG_INFO << "OfficeUnixMsgControl::DispatchMsg start traceid:"<< traceid;    
    //除特殊情况，不新增新的msg_id，基本刷配置的都可以复用MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE
    //否则在csadapt投递mq那边需要做新的hash分配
    switch (id)
    {
        /**csmain 消息 start**/
        case MSG_S2C_DEV_CONFIG_REWRITE:
        {
            OfficeCsmainMsgHandle::Instance()->UpdateMacConfigByCsmain(msg_buf, len);
            break;
        }
        case MSG_S2C_ACCOUNT_CONFIG_REWRITE:
        {
            OfficeCsmainMsgHandle::Instance()->UpdateDevAccountConfigByCsmain(msg_buf, len);
            break;
        }
        case MSG_S2C_DEV_REQ_USER_INFO:
        {
            OfficeCsmainMsgHandle::Instance()->DevWriteUserinfoReq(msg_buf, len);
            break;
        }
        /**csmain 消息 end**/

        
        case MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE:
        {
            OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(msg_buf, len);
            break;
        }    
        case MSG_P2A_NOTIFY_OFFICE_ACCESS_GROUP_MODIFY:
        {
            OnAccessGroupModify(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY:
        {
            OnOfficeAccountModify(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_COMMUNITY_IMPORT_ACCOUNT_DATAS:
        {    
            OnOfficeImportAccountData(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_PERSONAL_MODIFY:
        {
            OnOfficePersonalModify(msg_buf, len);
            break;
        }
        default:
        {
            //TODO,chenyc:响应消息类型不匹配(由于具体消息ID未知,故只能由通用消息头带回)
            AK_LOG_WARN << "Failed to match msg id:" << id;
            return -1;
        }
    }
    AK_LOG_INFO << "OfficeUnixMsgControl::DispatchMsg end traceid:"<< traceid;
    return 0;
}


//因为权限组会删除，此时后台没有办法通过权限组查找设备，所以直接让前端传对应的mac
void OfficeUnixMsgControl::OnAccessGroupModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnAccessGroupModify The param is NULL";
        return;
    }

    AK::Adapt::AccessGroupModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnAccessGroupModify msg=" << msg.DebugString();

    OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(msg.community_id());
    
    //更新schedule
    std::set<std::string > macs;
    int size = msg.mac_list_size();
    if (size == 0)
    {
        //可能是默认权限组
        AK_LOG_INFO << "OnAccessGroupModify mac is null, maybe default accessgroup.";
        dbinterface::AccessGroup::GetMacListByAccessGroupID(msg.access_group_id(), macs);
    }
    
    for (int i = 0; i < size; i++)
    {
        const std::string mac = msg.mac_list(i);
        macs.insert(mac);
    }

    OfficeDevList dev_list;
    dbinterface::OfficeDevices::GetMacListDevList(macs, dev_list);
    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(dev_list);    

    //更新user
    OfficeDevUser user(office_info);
    user.UpdateMetaData(dev_list);

    return;
}

void OfficeUnixMsgControl::OnOfficeAccountModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityAccountModify The param is NULL";
        return;
    }

    AK::Adapt::CommunityAccountModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnCommunityAccountModify msg=" << msg.DebugString();

    OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(msg.community_id());

    OfficeDevUser user(office_info);

    std::set<std::string> pub_mac_set;
    std::set<std::string> user_mac_set;

    std::vector<std::string> accounts;
    std::vector<std::string> nodes;
    int size = msg.account_list_size();
    for (int i = 0; i < size; i++)
    {
        accounts.push_back(msg.account_list(i));
    }

    size = msg.node_list_size();
    for (int i = 0; i < size; i++)
    {
        nodes.push_back(msg.node_list(i));
    }

    //如果权限组有变化就不需要根据Accounts找对应权限组更新设备
    std::vector<uint32_t> ag_ids;
    size = msg.access_group_id_list_size();
    if (size > 0)
    {
        for (int i = 0; i < size; i++)
        {
            uint32_t id = msg.access_group_id_list(i);
            if ( id > 0 )
            {
                ag_ids.push_back(id);
            }        
        }
    }
    
    if (ag_ids.size() > 0)
    {
        //更新权限组关联的设备user
        user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
    }
    else
    {
        //更新用户关联的权限组的设备user
        user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
    }

    //更新用户关联的家庭设备user
    user.UpdateUserDevMetaByAccount(accounts, user_mac_set);

    if(nodes.size() > 0)
    {
        //删除用户：更新家庭设备user
        user.UpdateUserDevMetaByNodes(nodes, user_mac_set);
    }

    //TODO:目前网页没有更新用户权限组（用户设备的权限组）的接口。所以需要更新一遍schedule
    OfficeDevList dev_list;
    dbinterface::OfficeDevices::GetMacListDevList(user_mac_set, dev_list);

    OfficeDevSchedule schedule;
    schedule.UpdateScheduleData(dev_list);  
    return;
}

void OfficeUnixMsgControl::OnOfficeImportAccountData(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityImportAccountData The param is NULL";
        return;
    }

    AK::Adapt::CommunityImportAccountDataNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnCommunityImportAccountData msg=" << msg.DebugString();

    OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(msg.community_id());

    OfficeDevUser user(office_info);

    std::set<std::string> mac_set;
    int size = msg.mac_list_size();
    for (int i = 0; i < size; i++)
    {
        mac_set.insert(msg.mac_list(i));
    }
    
    OfficeDevList dev_list;
    dbinterface::OfficeDevices::GetMacListDevList(mac_set, dev_list);
    user.UpdateMetaData(dev_list);  
  
    return;
}

/*社区人员更新*/
void OfficeUnixMsgControl::OnOfficePersonalModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnOfficePersonalModify The param is NULL";
        return;
    }

    AK::Adapt::CommunityPersonalModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnCommunityPersonalModify msg=" << msg.DebugString();

    OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(msg.community_id());
    OfficeDevUser user(office_info);

    std::set<std::string> pub_mac_set;
    std::vector<uint32_t> ag_ids;
    int size = msg.access_group_id_list_size();
    if (size > 0)
    {
        
        for (int i = 0; i < size; i++)
        {
            uint32_t id = msg.access_group_id_list(i);
            if ( id > 0 )
            {
                ag_ids.push_back(id);
            }
        }
    }
    
    if (ag_ids.size() > 0)
    {
       user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
    }    
    else
    {
        std::vector<uint32_t> staff_ids;
        std::vector<uint32_t> delivery_ids;
        int size = msg.staff_id_list_size();  
        for (int i = 0; i < size; i++)
        {
            staff_ids.push_back(static_cast<uint32_t>(msg.staff_id_list(i)));
        }
        
        size = msg.delivery_id_list_size();
        for (int i = 0; i < size; i++)
        {
            delivery_ids.push_back(static_cast<uint32_t>(msg.delivery_id_list(i)));
        }
        user.UpdatePubDevMetaByPubUser(staff_ids, delivery_ids, pub_mac_set);        
    }
    
    return;
}

/*单个mac, 通知mac*/
void OfficeUnixMsgControl::NotifyMacChange(const std::string& mac)
{
    CSP2A_CONFIG_FILE_CHANGE file_change;
    memset(&file_change, 0, sizeof(file_change));
    file_change.nNotifyType = CONFIG_FILE_CHANGE_NOTIFY_TYPE_MAC;
    file_change.type |= CONFIG_FILE_CHANGE_TYPE_CONTACT;
    file_change.type |= CONFIG_FILE_CHANGE_TYPE_CONFIG;
    snprintf(file_change.mac, sizeof(file_change.mac), "%s", mac.c_str());
    if (OfficeIPCControl::GetInstance()->SendConfigFileChange(&file_change) != 0)
    {
        AK_LOG_WARN << "Send contact change failed";
    }
}

/*整个社区更新, 通知社区所有设备*/
/*只有删除社区时候会用到*/
void OfficeUnixMsgControl::NotifyCommunityChange(uint32_t mng_id)
{
    CSP2A_CONFIG_FILE_CHANGE file_change;
    memset(&file_change, 0, sizeof(file_change));
    file_change.nNotifyType = CONFIG_FILE_CHANGE_NOTIFY_TYPE_COMMUNITY;
    file_change.mng_id = mng_id;
    if (OfficeIPCControl::GetInstance()->SendConfigFileChange(&file_change) != 0)
    {
        AK_LOG_WARN << "Send contact change failed";
    }
}

