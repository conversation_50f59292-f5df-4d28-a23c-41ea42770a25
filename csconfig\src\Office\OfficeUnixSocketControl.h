#ifndef __OFFICE_WEB_MEESSAGE_HANDLE_H__
#define __OFFICE_WEB_MEESSAGE_HANDLE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>

class OfficeUnixMsgControl
{
public:
    OfficeUnixMsgControl();
    int OnSocketMsg(void* msg_buf, unsigned int len);    

    static OfficeUnixMsgControl* Instance();

    //因为权限组会删除，此时后台没有办法通过权限组查找设备，所以直接让前端传对应的mac
    void OnAccessGroupModify(void* msg_buf, unsigned int msg_len);
    /*人员更新 用于user*/
    void OnOfficeAccountModify(void* msg_buf, unsigned int msg_len);
    void OnOfficeImportAccountData(void* msg_buf, unsigned int msg_len);
    void OnOfficePersonalModify(void* msg_buf, unsigned int msg_len);

    /*单个mac, 通知mac*/
    void NotifyMacChange(const std::string& mac);
    /*整个社区更新, 通知社区所有设备*/
    void NotifyCommunityChange(uint32_t mng_id);
private:
    static OfficeUnixMsgControl* office_unix_msg_instance_;
};




#endif //__OFFICE_MEESSAGE_HANDLE_H__
