#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "OfficeUpdateConfigContext.h"
#include "dbinterface/Account.h"
#include "AkLogging.h"

OfficeConfigContext::OfficeConfigContext()
{

}

void OfficeConfigContext::Init(uint32_t office_id, const std::string &project_uuid)
{
    office_id_ = office_id;
    dbinterface::CommunityUnit::GetCommunityUnitMap(office_id_, units_map_);
    dbinterface::VersionModel::GetNoMonitorList(no_monitor_list_); 

    dbinterface::PubDevMngList::GetManagementBuildingListByProjectId(office_id_, dev_mng_unit_id_);
    dbinterface::OfficePersonalAccount::GetAllAccountCnfMap(project_uuid, account_cnf_map_);

    dbinterface::AccessGroup::GetAgInfoByCommunityID(office_id, ag_id_map_, ag_info_mac_map_, ag_id_mac_map_);
    ChangeDefaultAgMac();

    room_contorl_.Init(office_id_);
}

std::string OfficeConfigContext::GetUnitName(uint32_t unit_id)
{
    auto it = units_map_.find(unit_id);
    if (it != units_map_.end())
    {
        return it->second.unit_name;
    }
    return "";
}


int OfficeConfigContext::IsNoMonitorDev(uint32_t hw)
{
    int not_monitor = 0;
    if (no_monitor_list_.count(hw))
    {
        not_monitor = 1;
    }
    return not_monitor;
}

int OfficeConfigContext::DevMngUnitID(const OfficeDevPtr &dev, uint32_t unit_id)
{
    if (dev && dbinterface::SwitchHandle(dev->flags, DeviceSwitch::DEV_MNG_ALL))
    {
        return 1;
    }

    const auto &dev_unit_list = dev_mng_unit_id_.equal_range(dev->id);
    for (auto it = dev_unit_list.first; it != dev_unit_list.second; ++it)
    {
        if (unit_id == it->second)
        {
            AK_LOG_INFO << "mac: "<< dev->mac << " mng this unitid. unit_id=" << unit_id;
            return 1;
        }
    }
    return 0;
}

//返回1 代表管理全部
int OfficeConfigContext::DevMngUnitListOrMngAll(const OfficeDevPtr &dev, std::vector<uint32_t> &unit_list)
{
    if (dev && dbinterface::SwitchHandle(dev->flags, DeviceSwitch::DEV_MNG_ALL))
    {
        return 1;
    }

    const auto &dev_unit_list = dev_mng_unit_id_.equal_range(dev->id);
    for (auto it = dev_unit_list.first; it != dev_unit_list.second; ++it)
    {
        unit_list.push_back(it->second);
    }
    return 0;
}

const OfficeDevList& OfficeConfigContext::AllMngDeviceSetting()
{
    return dev_contorl_->AllMngDeviceSetting();
}

const OfficeDevList& OfficeConfigContext::GetAllPubUnitDeviceInGlobal()
{
    return dev_contorl_->GetAllPubUnitDeviceInGlobal();
}

OfficeDevList OfficeConfigContext::GetNodeDeviceInGlobal(const std::string &node)
{
    return std::move(dev_contorl_->GetNodeDeviceInGlobal(node));
}

OfficeDevList OfficeConfigContext::GetUnitDeviceInGlobal(uint32_t unit_id)
{
    return std::move(dev_contorl_->GetUnitDeviceInGlobal(unit_id));
}

const OfficeDevList& OfficeConfigContext::GetPubDeviceInGlobal()
{
    return dev_contorl_->GetPubDeviceInGlobal();
}


int OfficeConfigContext::GetAccountCnf(const std::string &node, OfficeAccountCnf &cnf)
{
    auto it = account_cnf_map_.find(node);
    if (it != account_cnf_map_.end())
    {
        cnf = it->second;
        return 0;
    }    
    return -1;
}


bool OfficeConfigContext::IsDefaultAccessGroup(uint32_t ag_id)
{
    const auto &it = ag_id_map_.find(ag_id);
    if (it == ag_id_map_.end())
    {
        return false;
    }
    if (it->second.is_default_)
    {
        return true;
    }
    return false;    
}

int OfficeConfigContext::DevSchduleIDRelay(const std::string &mac, uint32_t ag_id)
{
    const auto &dev_ag_list = ag_info_mac_map_.equal_range(mac);
    for (auto it = dev_ag_list.first; it != dev_ag_list.second; ++it)
    {
        if (ag_id == it->second.id_)
        {
            return it->second.relay_;
        }
    }

    return 0;    
}

void OfficeConfigContext::ChangeDefaultAgMac()
{
    const OfficeDevList&pub_dev  = GetPubDeviceInGlobal();
    
    auto add_mac_fn = [this](uint32_t ag_id, const OfficeDevList &dev_list){
        for (auto &cur_dev : dev_list)
        {
            ag_id_mac_map_.insert(std::make_pair(ag_id, cur_dev->mac));
        }        
    };

    for (const auto &it : ag_id_map_)
    {
        if(it.second.is_default_)
        {
            OfficeDevList pub_unit_dev = GetUnitDeviceInGlobal(it.second.unit_id_);
            
            add_mac_fn(it.second.id_, pub_unit_dev);
            add_mac_fn(it.second.id_, pub_dev);
        }
    }
}



