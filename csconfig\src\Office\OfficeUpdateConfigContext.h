#ifndef __OFFICE_CONFIG_UPDATE_CONTEXT__
#define __OFFICE_CONFIG_UPDATE_CONTEXT__
#include "OfficeConfigHandleDevices.h"

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "CommunityMng.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "DeviceControl.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "PersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/resident/AmenityDevice.h"
#include "dbinterface/UserAccessGroup.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/office/OfficePersonalAccount.h"


class OfficeConfigHandleDevices;

class OfficeConfigContext
{
public:
    OfficeConfigContext();
    ~OfficeConfigContext(){}
    void Init(uint32_t office_id, const std::string &project_uuid); 
    void SetDevContorl(OfficeConfigHandleDevices *dev_contorl)
    {
        dev_contorl_ = dev_contorl;
    }


    /*设备相关*/
    int IsNoMonitorDev(uint32_t hw);

    int DevMngUnitID(const OfficeDevPtr &dev, uint32_t unit_id);
    int DevMngUnitListOrMngAll(const OfficeDevPtr &dev, std::vector<uint32_t> &unit_list);  


     /*项目相关*/
    std::string GetUnitName(uint32_t unit_id);

    const OfficeDevList& AllMngDeviceSetting();
    const OfficeDevList& GetAllPubUnitDeviceInGlobal();
    const OfficeDevList& GetPubDeviceInGlobal();
    OfficeDevList GetNodeDeviceInGlobal(const std::string &node); 
    OfficeDevList GetUnitDeviceInGlobal(uint32_t unit_id);
    int GetAccountCnf(const std::string &node, OfficeAccountCnf &cnf);   

    bool IsDefaultAccessGroup(uint32_t ag_id);
    int DevSchduleIDRelay(const std::string &mac, uint32_t ag_id);
    void ChangeDefaultAgMac();
    
private:

    OfficeConfigHandleDevices* dev_contorl_;
    ManagementBuildingMap dev_mng_unit_id_;
    CommunityRoomContorl room_contorl_;

    OfficeAccountCnfMap account_cnf_map_;
    
    uint32_t office_id_;
    CommunityUnitMap units_map_;
    FirmwareList no_monitor_list_;
    
    AccessGroupIDMap ag_id_map_;
    AccessGroupMacMap ag_info_mac_map_; //这个包含默认权限组
    AccessGroupIDAccountMap ag_id_account_map_;
    AccessGroupIDMacMap ag_id_mac_map_;

};

typedef std::shared_ptr<OfficeConfigContext> OfficeConfigContextPtr;


#endif


