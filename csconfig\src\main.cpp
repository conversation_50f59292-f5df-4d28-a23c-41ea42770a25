#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <iostream>
#include <sstream>
#include <thread>
#include <fcntl.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "Rldb.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "ConfigFileReader.h"
#include "redis/PubSubManager.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPool.h"
#include "AwsConnectionPool.h"
#include "AkLogging.h"
#include <evpp/evnsq/producer.h>
#include <evpp/event_loop.h>
#include "MQProduce.h"
#include "Etcd.h"
#include "MQProduce.h"
#include "AES256.h"
#include "DataAnalysisControl.h"
#include "HttpServer.h"
#include "CachePool.h"
#include "CsmainMsgHandle.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "tinyxml.h"
#include "WriteFileControl.h"
#include "dbinterface/DataConfusion.h"
#include "util_time.h"
#include "beanstalk.hpp"
#include "WriteDevWorkPool.h"
#include "AkcsAppInit.h"
#include "util.h"
#include "ShadowUserDetailMng.h"
#include "KafkaConsumerNotifyConfigTopicHandle.h"
#include "KafkaConsumerNotifyUserDetailTopicHandle.h"
#include "kafka/AkcsKafkaProducerNotifyConfig.h"
#include "kafka/AkcsKafkaProducerNotifyUserDetail.h"
#include <KdcDecrypt.h>
#include "SpecialTubeHandle.h"
#include "RouteClientMng.h"
#include "Metric.h"


CSCONFIG_CONF gstCSCONFIGConf;
PubSubManager* g_pub_sub_mng_ptr = nullptr;
evnsq::Producer* g_nsq_pub_mng_ptr = nullptr;
#define MAX_RLDB_CONN 10
extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char* g_conf_db_master_addr;
int g_etcd_dns_res = 0;
extern const char* g_redis_db_userdetail;
std::map<string, AKCS_DST> g_time_zone_DST;
#define BEANSTALK_SERVER_PORT  (8519)

#define PIDFILE "/var/run/csconfig.pid"
#define CSCONFIG_CONF_FILE "/usr/local/akcs/csconfig/conf/csconfig.conf"

/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    conn_pool->Init(gstCSCONFIGConf.db_ip, gstCSCONFIGConf.db_username, gstCSCONFIGConf.db_password, gstCSCONFIGConf.db_database, gstCSCONFIGConf.db_port, MAX_RLDB_CONN, "csconfig");

    if (gstCSCONFIGConf.is_aws)
    {
        AwsConnPool* aws_conn_pool = GetAwsDBConnPollInstance();
        if (NULL == aws_conn_pool)
        {
            AK_LOG_WARN << "Aws DaoInit failed.";
            return -1;
        }
        aws_conn_pool->Init(gstCSCONFIGConf.aws_db_ip, gstCSCONFIGConf.db_username, gstCSCONFIGConf.db_password, gstCSCONFIGConf.db_database, gstCSCONFIGConf.db_port, 5, "csconfig");
    }
    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstCSCONFIGConf.db_ip, gstCSCONFIGConf.db_port);
    return 0;
}
void UpdateOuterConfFromConfSrv()
{
    if (gstCSCONFIGConf.is_aws)
    {
        return;
    }

    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if ((::strcmp(conf_tmp.db_master_ip, gstCSCONFIGConf.db_ip) != 0) || (conf_tmp.db_master_port != gstCSCONFIGConf.db_port))
    {
        Snprintf(gstCSCONFIGConf.db_ip, sizeof(gstCSCONFIGConf.db_ip), conf_tmp.db_master_ip);
        gstCSCONFIGConf.db_port = conf_tmp.db_master_port;
        DaoReInit();
    }
}
int LoadConfFromConfSrv()
{
    if (g_etcd_cli_mng == nullptr || gstCSCONFIGConf.is_aws)
    {
        return -1;
    }
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if ((strlen(conf_tmp.db_master_ip) == 0) || (conf_tmp.db_master_port == 0))
    {
        return -1;
    }
    Snprintf(gstCSCONFIGConf.db_ip, sizeof(gstCSCONFIGConf.db_ip), conf_tmp.db_master_ip);
    gstCSCONFIGConf.db_port = conf_tmp.db_master_port;
    return 0;
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSCONFIGConf.etcd_server_addr);

    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_master_addr, UpdateOuterConfFromConfSrv);
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void ConfInit(const std::string& file)
{
    CConfigFileReader config_file(file.c_str());

    const char* is_aws = config_file.GetConfigName("is_aws");
    gstCSCONFIGConf.is_aws = ATOI(is_aws);

    const char* log_level = config_file.GetConfigName("csconfig_loglevel");
    gstCSCONFIGConf.log_level = ATOI(log_level);

    Snprintf(gstCSCONFIGConf.cspbx_outer_ip, sizeof(gstCSCONFIGConf.cspbx_outer_ip), config_file.GetConfigName("cspbx_ip"));
    Snprintf(gstCSCONFIGConf.cspbx_outer_port, sizeof(gstCSCONFIGConf.cspbx_outer_port), config_file.GetConfigName("cspbx_port"));

    Snprintf(gstCSCONFIGConf.db_username, sizeof(gstCSCONFIGConf.db_username), config_file.GetConfigName("db_username"));
    Snprintf(gstCSCONFIGConf.db_database, sizeof(gstCSCONFIGConf.db_database), config_file.GetConfigName("db_database"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstCSCONFIGConf.db_password, sizeof(gstCSCONFIGConf.db_password), decrypt_db_passwd.c_str());

    if (LoadConfFromConfSrv() != 0)
    {
        Snprintf(gstCSCONFIGConf.db_ip, sizeof(gstCSCONFIGConf.db_ip), config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        gstCSCONFIGConf.db_port = ATOI(db_port);
    }

    const char* encrypt = config_file.GetConfigName("noencrypt");
    gstCSCONFIGConf.no_encrypt = ATOI(encrypt);

    /*读取OEM特殊配置文件，直接加入配置文件选项*/
    FILE* pFstream = nullptr;
    if ((pFstream = fopen("/usr/local/akcs/csconfig/conf/oem_config.conf", "r")) != nullptr)
    {
        fread(gstCSCONFIGConf.oem_config, sizeof(char), sizeof(gstCSCONFIGConf.oem_config), pFstream);
        fclose(pFstream);
    }
    Snprintf(gstCSCONFIGConf.nsq_topic_for_del_pic, sizeof(gstCSCONFIGConf.nsq_topic_for_del_pic), config_file.GetConfigName("nsq_delpic_topic"));
    Snprintf(gstCSCONFIGConf.nsq_route_topic, sizeof(gstCSCONFIGConf.nsq_route_topic), config_file.GetConfigName("nsq_route_topic"));

    Snprintf(gstCSCONFIGConf.beanstalk_addr, sizeof(gstCSCONFIGConf.beanstalk_addr), config_file.GetConfigName("beanstalkd_ip"));
    Snprintf(gstCSCONFIGConf.beanstalk_tube, sizeof(gstCSCONFIGConf.beanstalk_tube), config_file.GetConfigName("beanstalkd_tube"));
    Snprintf(gstCSCONFIGConf.beanstalk_backup_ip, sizeof(gstCSCONFIGConf.beanstalk_backup_ip), config_file.GetConfigName("beanstalkd_backup_ip"));

    Snprintf(gstCSCONFIGConf.ssh_proxy_domain, sizeof(gstCSCONFIGConf.ssh_proxy_domain), config_file.GetConfigName("remote_config_domain"));
    Snprintf(gstCSCONFIGConf.web_ip, sizeof(gstCSCONFIGConf.web_ip), config_file.GetConfigName("web_ip"));
    Snprintf(gstCSCONFIGConf.ftp_ip, sizeof(gstCSCONFIGConf.ftp_ip), config_file.GetConfigName("ftp_ip"));
    Snprintf(gstCSCONFIGConf.fdfs_config_addr, sizeof(gstCSCONFIGConf.fdfs_config_addr), config_file.GetConfigName("fdfs_config_addr"));

    gstCSCONFIGConf.config_server_domain_gray_percentage = ATOI(config_file.GetConfigName("config_server_domain_gray_percentage"));
    Snprintf(gstCSCONFIGConf.config_server_domain, sizeof(gstCSCONFIGConf.config_server_domain), config_file.GetConfigName("config_server_domain"));

    //获取服务器ip信息
    CConfigFileReader server_config_file("/etc/ip");
    Snprintf(gstCSCONFIGConf.server_hostname, sizeof(gstCSCONFIGConf.server_hostname), server_config_file.GetConfigName("AKCS_HOSTNAME"));
    Snprintf(gstCSCONFIGConf.server_inner_ip, sizeof(gstCSCONFIGConf.server_inner_ip), server_config_file.GetConfigName("SERVER_INNER_IP"));
    Snprintf(gstCSCONFIGConf.server_outer_ip, sizeof(gstCSCONFIGConf.server_outer_ip), server_config_file.GetConfigName("SERVERIP"));

    const char* system_area_type = config_file.GetConfigName("system_area_type");
    gstCSCONFIGConf.server_type = ATOI(system_area_type);
    Snprintf(gstCSCONFIGConf.community_ids, sizeof(gstCSCONFIGConf.community_ids), config_file.GetConfigName("community_ids"));

    Snprintf(gstCSCONFIGConf.aws_db_ip, sizeof(gstCSCONFIGConf.aws_db_ip), config_file.GetConfigName("aws_mysql_ip"));
    gstCSCONFIGConf.repeated_userdetail_timeout = ATOI(config_file.GetConfigName("repeated_userdetail_timeout"));
    gstCSCONFIGConf.repeated_ipchange_timeout = ATOI(config_file.GetConfigName("repeated_ipchange_timeout"));
    gstCSCONFIGConf.is_store_fdfs = ATOI(config_file.GetConfigName("is_store_fdfs"));


    Snprintf(gstCSCONFIGConf.ip_change_filter, sizeof(gstCSCONFIGConf.ip_change_filter), config_file.GetConfigName("ip_change_filter"));
    Snprintf(gstCSCONFIGConf.user_info_filter, sizeof(gstCSCONFIGConf.user_info_filter), config_file.GetConfigName("user_info_filter"));


    Snprintf(gstCSCONFIGConf.mng_id_filter, sizeof(gstCSCONFIGConf.mng_id_filter), config_file.GetConfigName("mng_id_filter"));
    Snprintf(gstCSCONFIGConf.ip_change_mng_id_filter, sizeof(gstCSCONFIGConf.ip_change_mng_id_filter), config_file.GetConfigName("ip_change_mng_id_filter"));

    gstCSCONFIGConf.check_big_project_handle_time = ATOI(config_file.GetConfigName("check_big_project_handle_time"));
    if (gstCSCONFIGConf.check_big_project_handle_time == 0)
    {
        gstCSCONFIGConf.check_big_project_handle_time = 60;
    }
    gstCSCONFIGConf.repeated_web_timeout = ATOI(config_file.GetConfigName("repeated_web_timeout"));

    gstCSCONFIGConf.write_thread_number = ATOI(config_file.GetConfigName("write_thread_number"));
    if (gstCSCONFIGConf.write_thread_number < 2)
    {
        gstCSCONFIGConf.write_thread_number = 2;
    }

    gstCSCONFIGConf.write_file_number = ATOI(config_file.GetConfigName("write_file_number"));
    if (gstCSCONFIGConf.write_file_number < 1)
    {
        gstCSCONFIGConf.write_file_number = 1;
    }
    Snprintf(gstCSCONFIGConf.vrtsp_server_domain, sizeof(gstCSCONFIGConf.vrtsp_server_domain), config_file.GetConfigName("vrtsp_server_domain"));

    const char* enable_db_confusion_cache = config_file.GetConfigName("enable_db_confusion_cache");
    gstCSCONFIGConf.enable_db_confusion_cache = ATOI(enable_db_confusion_cache);


    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));


    gstCSCONFIGConf.write_dev_work_thread_num = ATOI(config_file.GetConfigName("write_dev_work_thread_num"));
    if (gstCSCONFIGConf.write_dev_work_thread_num < 1)
    {
        gstCSCONFIGConf.write_dev_work_thread_num = 1;
    }

    Snprintf(gstCSCONFIGConf.ip_change_mng_id_filter, sizeof(gstCSCONFIGConf.ip_change_mng_id_filter), config_file.GetConfigName("ip_change_mng_id_filter"));

    Snprintf(gstCSCONFIGConf.kafka_broker_ip, sizeof(gstCSCONFIGConf.kafka_broker_ip), config_file.GetConfigName("kafka_broker_ip"));
    Snprintf(gstCSCONFIGConf.notify_csconfig_topic, sizeof(gstCSCONFIGConf.notify_csconfig_topic), config_file.GetConfigName("notify_csconfig_topic"));
    Snprintf(gstCSCONFIGConf.notify_csconfig_topic_group, sizeof(gstCSCONFIGConf.notify_csconfig_topic_group), config_file.GetConfigName("notify_csconfig_topic_group"));
    gstCSCONFIGConf.notify_csconfig_topic_thread = ATOI(config_file.GetConfigName("notify_csconfig_topic_thread"));

    Snprintf(gstCSCONFIGConf.notify_csconfig_user_detail_topic, sizeof(gstCSCONFIGConf.notify_csconfig_user_detail_topic), config_file.GetConfigName("notify_csconfig_user_detail_topic"));
    Snprintf(gstCSCONFIGConf.notify_csconfig_user_detail_topic_group, sizeof(gstCSCONFIGConf.notify_csconfig_user_detail_topic_group), config_file.GetConfigName("notify_csconfig_user_detail_topic_group"));
    gstCSCONFIGConf.notify_csconfig_user_detail_topic_thread = ATOI(config_file.GetConfigName("notify_csconfig_user_detail_topic_thread"));

    gstCSCONFIGConf.write_config_heap_up_num = ATOI(config_file.GetConfigName("write_config_heap_up_num"));
    gstCSCONFIGConf.write_config_batch_read_num = ATOI(config_file.GetConfigName("write_config_batch_read_num"));
    gstCSCONFIGConf.write_config_community_overflow_threshold = ATOI(config_file.GetConfigName("write_config_community_overflow_threshold"));
}


int ControlInit()
{
    //初始化所有单例,避免后面多线程竞争
    GetUnixSocketControlInstance()->Init(gstCSCONFIGConf.beanstalk_addr);
    GetUnixSocketControlInstance()->Init(gstCSCONFIGConf.beanstalk_backup_ip);
    GetUnixSocketControlInstance()->InitPduBeanstalk();
    GetAKCSViewInstance();
    dbinterface::DataConfusion::SetCacheOption(gstCSCONFIGConf.enable_db_confusion_cache);

    AKCS::Singleton<AkcsKafkaProducerNotifyConfig>::instance().Init(gstCSCONFIGConf.notify_csconfig_topic, gstCSCONFIGConf.kafka_broker_ip);
    AKCS::Singleton<AkcsKafkaProducerNotifyUserDetail>::instance().Init(gstCSCONFIGConf.notify_csconfig_user_detail_topic, gstCSCONFIGConf.kafka_broker_ip);

    AKCS::Singleton<HandleKafkaNotifyConfigTopicMsg>::instance().StartKafkaConsumer(gstCSCONFIGConf.kafka_broker_ip, gstCSCONFIGConf.notify_csconfig_topic,
        gstCSCONFIGConf.notify_csconfig_topic_group, gstCSCONFIGConf.notify_csconfig_topic_thread);
    AKCS::Singleton<HandleKafkaNotifyUserDetailTopicMsg>::instance().StartKafkaConsumer(gstCSCONFIGConf.kafka_broker_ip, gstCSCONFIGConf.notify_csconfig_user_detail_topic,
        gstCSCONFIGConf.notify_csconfig_user_detail_topic_group, gstCSCONFIGConf.notify_csconfig_user_detail_topic_thread);
}

void sig_handler(int sig_no, siginfo_t* info, void* ctext)
{
    AK_LOG_WARN << "receive sig_no=" << sig_no;
    if (sig_no == SIGUSR1)
    {
        CConfigFileReader config_file(CSCONFIG_CONF_FILE);
        const char* encrypt = config_file.GetConfigName("noencrypt");
        gstCSCONFIGConf.no_encrypt = ATOI(encrypt);

        const char* log_level = config_file.GetConfigName("csconfig_loglevel");
        gstCSCONFIGConf.log_level = ATOI(log_level);
    }
    else
    {

    }
    return;
}


int RedisInit()
{
    int ret = CacheManager::getInstance()->Init("/usr/local/akcs/csconfig/conf/csconfig_redis.conf", "csconfigCacheInstances");
    //清空userdetail，因为csmaint投递的userinfo消息
    //route和adapt不保证一定能处理到的，比如csmain写成功了，但是路由或adapt崩溃了    
    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail);
    if (cache_conn)
    {
        cache_conn->flushdb();
        cache_mng->RelCacheConn(cache_conn);
    }
    return ret;
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;
    std::stringstream etcd_ips_str;
    for (auto& ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }
    //更新为ip串 
    snprintf(gstCSCONFIGConf.etcd_server_addr, sizeof(gstCSCONFIGConf.etcd_server_addr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstCSCONFIGConf.etcd_server_addr;

    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }

}

void DnsResolver()
{
    CConfigFileReader config_file(CSCONFIG_CONF_FILE);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstCSCONFIGConf.etcd_server_addr, sizeof(gstCSCONFIGConf.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstCSCONFIGConf.etcd_server_addr;
    for (unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if (etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }

    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }

    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstCSCONFIGConf.etcd_server_addr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}


int main(int argc, char* argv[])
{

    std::string config_file = CSCONFIG_CONF_FILE;
    /*带参数控制命令处理*/
    if (argc >= 2)
    {
        if (!::strcasecmp(argv[1], "-d"))
        {
            if (argc < 4)
            {
                return 0;
            }
            char key[] = AES_ENCRYPT_KEY_V1;
            FileAESDecrypt(argv[2], key, argv[3]);
            return 0;
        }

    }
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2(PIDFILE))
    {
        printf("another csconfig has been running in this sytem.");
        return -1;
    }
    GlogInit2(argv[0], "csconfiglog");

    int ret = -1;

    //配置中心初始化
    //一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
    memset(&gstCSCONFIGConf, 0, sizeof(CSCONFIG_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while (!g_etcd_dns_res)
    {
        usleep(10);
    }
    ConfSrvInit();
    /* 读取配置文件 */
    ConfInit(config_file);

    ret = DaoInit();
    if (0 != ret)
    {
        AK_LOG_WARN << "DaoInit failed";
        GlogClean2();
        return -1;
    }
    if (RedisInit() != 0)
    {
        AK_LOG_FATAL << "Redis init instance failed";
        return -1;
    }

    AKCS::Singleton<WriteDevWorkPoolControl>::instance().Init(gstCSCONFIGConf.write_dev_work_thread_num);
    AKCS::Singleton<CShadowMng>::instance();
    GetWriteFileControlInstance();

    //注册数据变化分析处理函数
    RegDataAnalysisDBAllHandler();
    UpdateConfigInit();

    //csmain过来的消息处理线程
    GetCsmainMsgHandleInstance()->InitConsumerThread();

    //nsq消息发布 这个要先于ControlInit，因为里面有延时队列，如果程序重启时候队列里面有数据，
    //在消息处理时候，因为nsq没有初始化导致发布数据时候段错误
    std::thread mqProduceThread = std::thread(MQProduceInit);
    sleep(1);//等线程起来

    //启动控制器线程
    ControlInit();

    std::thread etcdCliThread = std::thread(EtcdSrvInit);

    //起http维护通道线程
    std::thread httpThread(startHttpServer); //port = 9996

    std::thread conf_watch_thread = std::thread(ConfWatch);

    ParseTimeZone("/usr/local/akcs/csconfig/conf/TimeZone.xml", g_time_zone_DST);

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csconfig is starting";

    conf_watch_thread.join();
    dnsThread.join();
    etcdCliThread.join();
    mqProduceThread.join();
    GlogClean2();
    return 0;
}

