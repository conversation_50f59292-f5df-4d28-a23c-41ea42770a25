﻿//#define CATCH_CONFIG_MAIN
#define CATCH_CONFIG_RUNNER
#include <catch2/catch.hpp>
#include <catch2/catch_reporter_teamcity.hpp>
#include <catch2/catch_reporter_tap.hpp>
#include <catch2/catch_reporter_sonarqube.hpp>

#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <iostream>
#include <sstream>
#include <thread>
#include <fcntl.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "Rldb.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "ConfigFileReader.h"
#include "redis/PubSubManager.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPool.h"
#include "AkLogging.h"
#include <evpp/evnsq/producer.h>
#include <evpp/event_loop.h>
#include "MQProduce.h"
#include "Etcd.h"
#include "AES256.h"


CSCONFIG_CONF gstCSCONFIGConf;
PubSubManager* g_pub_sub_mng_ptr = nullptr;
evnsq::Producer* g_nsq_pub_mng_ptr = nullptr;

#define CSADAPD_CONF_FILE "../conf/csadapt.conf"
//#define CSADAPD_CONF_FILE "/usr/local/akcs/csadapt/conf/csadapt.conf"


/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    conn_pool->Init(gstCSCONFIGConf.db_ip, gstCSCONFIGConf.db_username, gstCSCONFIGConf.db_password, gstCSCONFIGConf.db_database, gstCSCONFIGConf.db_port, MAX_RLDB_CONN, "csadapt");
    return 0;
}
void glogInit()
{
    google::InitGoogleLogging("csadaptTest");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csadaptlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csadaptlog/log/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csadaptlog/log/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csadaptlog/log/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void ConfInit()
{
    std::string file = CSADAPD_CONF_FILE;
    memset(&gstCSCONFIGConf, 0, sizeof(CSCONFIG_CONF));
    CConfigFileReader config_file(file.c_str());

    const char* log_level = config_file.GetConfigName("csconfig_loglevel");
    gstCSCONFIGConf.log_level = ATOI(log_level);

    Snprintf(gstCSCONFIGConf.cspbx_outer_ip, sizeof(gstCSCONFIGConf.cspbx_outer_ip), config_file.GetConfigName("cspbx_ip"));
    Snprintf(gstCSCONFIGConf.cspbx_outer_port, sizeof(gstCSCONFIGConf.cspbx_outer_port), config_file.GetConfigName("cspbx_port"));

    Snprintf(gstCSCONFIGConf.db_ip, sizeof(gstCSCONFIGConf.db_ip), config_file.GetConfigName("db_ip"));
    Snprintf(gstCSCONFIGConf.db_username, sizeof(gstCSCONFIGConf.db_username), config_file.GetConfigName("db_username"));
    Snprintf(gstCSCONFIGConf.db_password, sizeof(gstCSCONFIGConf.db_password), config_file.GetConfigName("db_passwd"));
    Snprintf(gstCSCONFIGConf.db_database, sizeof(gstCSCONFIGConf.db_database), config_file.GetConfigName("db_database"));

    const char* db_port = config_file.GetConfigName("db_port");
    gstCSCONFIGConf.db_port = ATOI(db_port);

    const char* encrypt = config_file.GetConfigName("noencrypt");
    gstCSCONFIGConf.no_encrypt = ATOI(encrypt);

    /*读取OEM特殊配置文件，直接加入配置文件选项*/
    FILE* pFstream = nullptr;
    if ((pFstream = fopen("/usr/local/akcs/csconfig/conf/oem_config.conf", "r")) != nullptr)
    {
        fread(gstCSCONFIGConf.oem_config, sizeof(char), sizeof(gstCSCONFIGConf.oem_config), pFstream);
        fclose(pFstream);
    }
    Snprintf(gstCSCONFIGConf.nsq_topic_for_del_pic, sizeof(gstCSCONFIGConf.nsq_topic_for_del_pic), config_file.GetConfigName("nsq_delpic_topic"));
    Snprintf(gstCSCONFIGConf.etcd_server_addr, sizeof(gstCSCONFIGConf.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));
    Snprintf(gstCSCONFIGConf.nsq_route_topic, sizeof(gstCSCONFIGConf.nsq_route_topic), config_file.GetConfigName("nsq_route_topic"));

    Snprintf(gstCSCONFIGConf.beanstalk_addr, sizeof(gstCSCONFIGConf.beanstalk_addr), config_file.GetConfigName("beanstalkd_ip"));
    Snprintf(gstCSCONFIGConf.beanstalk_tube, sizeof(gstCSCONFIGConf.beanstalk_tube), config_file.GetConfigName("beanstalkd_tube"));

    Snprintf(gstCSCONFIGConf.ssh_proxy_domain, sizeof(gstCSCONFIGConf.ssh_proxy_domain), config_file.GetConfigName("remote_config_domain"));
    Snprintf(gstCSCONFIGConf.web_ip, sizeof(gstCSCONFIGConf.web_ip), config_file.GetConfigName("web_ip"));
	Snprintf(gstCSCONFIGConf.ftp_ip, sizeof(gstCSCONFIGConf.ftp_ip), config_file.GetConfigName("ftp_ip"));


    //获取服务器ip信息
    CConfigFileReader server_config_file("/etc/ip");
    Snprintf(gstCSCONFIGConf.server_hostname, sizeof(gstCSCONFIGConf.server_hostname), server_config_file.GetConfigName("AKCS_HOSTNAME"));   
    Snprintf(gstCSCONFIGConf.server_inner_ip, sizeof(gstCSCONFIGConf.server_inner_ip), server_config_file.GetConfigName("SERVER_INNER_IP"));
    Snprintf(gstCSCONFIGConf.server_outer_ip, sizeof(gstCSCONFIGConf.server_outer_ip), server_config_file.GetConfigName("SERVERIP"));

    const char* system_area_type = config_file.GetConfigName("system_area_type");
    gstCSCONFIGConf.server_type = ATOI(system_area_type);
    Snprintf(gstCSCONFIGConf.community_ids, sizeof(gstCSCONFIGConf.community_ids), config_file.GetConfigName("community_ids"));
   
    const char* is_aws = config_file.GetConfigName("is_aws");
    gstCSCONFIGConf.is_aws = ATOI(is_aws);
    Snprintf(gstCSCONFIGConf.aws_db_ip, sizeof(gstCSCONFIGConf.aws_db_ip), config_file.GetConfigName("aws_mysql_ip"));
    gstCSCONFIGConf.repeated_userdetail_timeout = ATOI(config_file.GetConfigName("repeated_userdetail_timeout"));
}


int main(int argc, char* argv[])
{
    Catch::Session session;

    // 通过修改configData()对象来设置参数
    session.configData().reporterName = "compact";
    session.configData().showDurations = Catch::ShowDurations::OrNot::Always;

    // catch解析输入的命令行参数
    int ret_code = session.applyCommandLine(argc, argv);
    if (ret_code != 0) // Indicates a command line error
        return ret_code;

    // 在解析完输入参数后，这里可修改configData()以重写覆盖参数
    session.configData().reporterName = "xml";

    ConfInit();
    DaoInit();
    glogInit();

    int num_failed = session.run();

    return num_failed;
}


