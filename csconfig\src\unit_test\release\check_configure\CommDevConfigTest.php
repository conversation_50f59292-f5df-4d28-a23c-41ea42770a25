<?php
require("Common.php");
assert_options(ASSERT_BAIL, 1);

if ($argc != 3)
{
    echo("param error, usage ".$argv[0]." [case_name] [check_file]");
    exit(1);
}
$casename = $argv[1];
$file = $argv[2];

const CHECK_INI_FILE = "../conf/check_config_by_ini/CheckConfig.ini";

$data = parse_ini_file($file);
switch ($casename) {
	case "UpdateUnitAllNodeDevConfig":
    case "UpdateCommunityAllNodeDevConfig":
    case "UpdateCommunityAllUnitDevConfig":
        exit(0);
		break;
	default:
        $check_ini = parse_ini_file(CHECK_INI_FILE, true);
        check_common_dev_config($data, $casename, $check_ini);
}


function config_check($data, $case, $sec, $expect)
{
	if ($data[$sec] != $expect)
	{
		echo("check $sec failed. value=".$data[$sec]." expect:$expect");
		exit(1);
	}
}

function check_common_dev_config($data, $casename, $check_ini)
{
    if (array_key_exists($casename, $check_ini))
    {
        $checks = $check_ini[$casename];
        foreach($checks as $key =>  $value)
        {
            config_check($data, $casename, $key, $value);
        }
    }
}



