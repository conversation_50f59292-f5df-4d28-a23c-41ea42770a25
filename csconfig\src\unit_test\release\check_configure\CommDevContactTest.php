<?php
require("Common.php");
assert_options(ASSERT_BAIL, 1);

if ($argc != 3)
{
    echo("param error, usage ".$argv[0]." [case_name] [json_argv]");
    exit(1);
}

const JSON_CHECK_FILE = "../conf/check_contact_by_json/";
const CHECK_FILE = "check_file_path";
const CHECK_GROUP = "check_group";
const CHECK_UID = "check_uid";
const CHECK_CASE_NAME = "check_case";
const CHECK_CASE_BY_JSON = "check_by_json";

$casename = $argv[1];
$data = json_decode($argv[2],true);

$xml = get_xml_file($data[CHECK_FILE]);
switch ($casename) {		
	//下面三个是特殊的操作
	case "UpdateUnitAllNodeDevContactList":
	case "UpdateCommunityAllNodeDevContactList":
	case "UpdateCommunityAllUnitDevContactList":
		exit(0);
		break;
	default:
		check_common_dev_contact($xml, $data);
		exit(0);
}

function get_xml_file($file_path)
{
	if(file_exists($file_path)) {
		$str = file_get_contents($file_path);//将整个文件内容读入到一个字符串中
		$str = str_replace('<?xml version="1.0" encoding="UTF-8" ?>', "", $str);
		$XML = simplexml_load_string($str);

		$xml_data = array();
        foreach ($XML->Group as $group) {
			$sipgroup = (string)$group["SIP"];
			$xml_data[$sipgroup] = array();
			foreach($group->attributes() as $k => $v){
				$xml_data[$sipgroup][(string)$k] = (string)$v;
			}

			foreach ($group->Contact as $contact) {
				$account = (string)$contact["UID"];

				$xml_data[$sipgroup][$account] = array();
				foreach($contact->attributes() as $k => $v){
					$xml_data[$sipgroup][$account][(string)$k] = (string)$v;
				}
			}			
		 }
		 foreach ($XML->PubInfo as $group) {
			$sipgroup = "PubInfo";
			$xml_data[$sipgroup] = array();
			foreach ($group->Contact as $contact) {
				$account = (string)$contact["UID"];
				$xml_data[$sipgroup][$account] = array();
				foreach($contact->attributes() as $k => $v){
					$xml_data[$sipgroup][$account][(string)$k] = (string)$v;
				}
			}			
		 }
		return $xml_data;
	}
}

function contact_check_by_json_file($xml, $data)
{
	$case_name = $data[CHECK_CASE_NAME];
	$check_by_json = $data[CHECK_CASE_BY_JSON];
	if($check_by_json) {
		$json_file = JSON_CHECK_FILE."$case_name.json";
		if (!file_exists($json_file))
		{
			echo("contact check case $case_name failed. can not found:$json_file");
		}
		$str = file_get_contents($json_file);//将整个文件内容读入到一个字符串中
		$data_json = json_decode($str, true);
		foreach($data_json["Group"] as $group)
		{
			$sip_gruop = $group["SIP"];
			foreach($group as $key => $value)
			{
				if ($key == "Contact")
				{
					$contacts = $value;
					foreach($contacts as $contact)
					{
						$uid = $contact["UID"];
						foreach($contact as $key => $value)
						{
							if ($xml[$sip_gruop][$uid][$key] != $value)
							{
								echo("contact check <$key> failed. value=<$value>  expect:<".$xml[$sip_gruop][$uid][$key].">");
								exit(1);
							}								
						}
					}						
				}
				else 
				{
					if ($xml[$sip_gruop][$key] != $value)
					{
						echo("group check <$key> failed. value=<$value>  expect:".$xml[$sip_gruop][$key].">");
						exit(1);
					}
				}
			}
		}

        if (array_key_exists("PubInfo", $data_json))
        {
            $pubinfo = $data_json["PubInfo"];
            $sip_gruop="PubInfo";
            foreach($pubinfo as $key => $value)
            {
                if ($key == "Contact")
                {
                    $contacts = $value;
                    foreach($contacts as $contact)
                    {
                        $uid = $contact["UID"];
                        foreach($contact as $key => $value)
                        {
                            if ($xml[$sip_gruop][$uid][$key] != $value)
                            {
                                echo("PubInfo contact check <$key> failed. value=<$value>  expect:<".$xml[$sip_gruop][$uid][$key].">");
                                exit(1);
                            }								
                        }
                    }						
                }
            }
        }
	}
}


function contact_check_group($xml, $data)
{
	if (strlen($data[CHECK_GROUP]) <= 0)
	{
		return;
	}
	$case_name = $data[CHECK_CASE_NAME];
    $groups = explode("\n", $data[CHECK_GROUP]);
    foreach($groups as $groupstr)
    {
        $gruopinfo = explode('|', $groupstr);
        $group = $gruopinfo[0];
        if (!array_key_exists($group, $xml))
        {
            echo("group check: $group not found");
            exit(1);
        }
        foreach($gruopinfo as $row => $val)
        {
            $key = "";
            $expect = "";        
            if ($row == 0 || strlen($val) == 0)
            {
                continue;
            }
            $case=explode(':',$val);
            $key = $case[0];
            $expect = $case[1];
            if ($xml[$group][$key] != $expect)
            {
                echo("group <$group> check: <$key> failed. value=<". $xml[$group][$key] .">  expect:<".$expect.">");
                exit(1);
            }		
        }        
    }
}


function contact_check_uid($xml, $data)
{
	if (strlen($data[CHECK_UID]) <= 0)
	{
		return;
	}
	$case_name = $data[CHECK_CASE_NAME];
    
    $uids = explode("\n", $data[CHECK_UID]);
    foreach($uids as $check_uid)
    {
        $gruopinfo = explode('|', $check_uid);
        $group = $gruopinfo[0];
        $uid = $gruopinfo[1];

        if (!array_key_exists($group, $xml))
        {
            echo("uid check: group:$group not found");
            exit(1);
        }	
        if (!array_key_exists($uid, $xml[$group]))
        {
            echo("uid check: group:$group uid:$uid not found");
            exit(1);
        }
        foreach($gruopinfo as $row => $val)
        {
            $key = "";
            $expect = "";        
            if ($row == 0 || $row == 1 || strlen($val) == 0)
            {
                continue;
            }
            $case=explode(':', $val);
            $key = $case[0];
            $expect = $case[1];
            if ($xml[$group][$uid][$key] != $expect)
            {
                echo("uid <$group> <$uid> check: $key failed. value<". $xml[$group][$uid][$key] .">  expect:<".$expect.">");
                exit(1);
            }
            else        
            {
                #echo "key=".$xml[$group][$uid][$key]."=v=".$expect."=\n";
            }
        }        
    }
}

function check_common_dev_contact($xml, $data)
{
	contact_check_by_json_file($xml, $data);
	contact_check_uid($xml, $data);
	contact_check_group($xml, $data);
}



