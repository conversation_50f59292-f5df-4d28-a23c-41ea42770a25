#case的通用配置
[common]
#初始化当前用例的数据信息
init_sql_cmd=mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> AKCS_UnitTest -e 'source ../db/init.sql'
#如果其他的case有配置，用case的detect
detect_bin=php ../check_configure/CommDevConfigTest.php

[case_common]
#把file放在这里，那么写sql就可以很清晰知道更新哪个mac
check_file_path=/var/www//download/community/5/1/6300100003/Config/CE0000000009.cfg
sql_cmd=

[UpdateUnitAllNodeDevConfig]
check_file_path=/var/www//download/community/5/1/6300100003/Config/CE0000000009.cfg
#当前case需要特殊执行的sql,支持多个
sql_cmd=

[UpdateCommunityAllNodeDevConfig]
check_file_path=/var/www//download/community/5/1/6300100003/Config/CE0000000009.cfg
sql_cmd=

[UpdateCommunityAllUnitDevConfig]
check_file_path=/var/www//download/community/5/1/6300100003/Config/CE0000000009.cfg
sql_cmd=

[case_dev_v4400]
check_file_path=/var/www//download/community/5/1/6300100003/Config/CE0000000009.cfg
sql_cmd=update Devices set DclientVer=100 where Mac="CE0000000009"
sql_cmd=update Devices set IPAddress="*********" where Mac="CE0000000009"