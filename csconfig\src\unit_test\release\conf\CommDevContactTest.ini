#case的通用配置
[common]
init_sql_cmd=mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> AKCS_UnitTest -e 'source ../db/init.sql'
#如果其他的case有配置，用case的detect
detect_bin=php ../check_configure/CommDevContactTest.php

#[case_common]
#把file放在这里，那么写sql就可以很清晰知道更新哪个mac
#check_file_path=/var/www//download/community/5/1/**********/ContactList/CE0000000009.xml
#sql_cmd=
#group|属性:值|...
#check_group=**********|Name:System Test1|
#gruop|uid|属性:值|...
#check_uid=**********|**********|Name:System Test1|
#当检测项多了可以通过json进行配置
#check_by_json=1

[case_dev_node1]
check_file_path=/var/www//download/community/5/1/**********/ContactList/CE0000000009.xml
check_group=**********|Name:System Test1|Room:Room101|Node:**********|Unit:B1|SIP:**********|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:1
check_uid=**********|**********|Name:System Test1|SIP:************|UID:**********|SIP0:**********|Land:************;************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2

[case_dev_node2]
check_file_path=/var/www//download/community/5/1/**********/ContactList/CE0000000008.xml
check_group=**********|Name:System Test1|Room:Room101|Node:**********|Unit:B1|SIP:**********|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:1
check_uid=**********|**********|Name:Pub1|UID:**********|SIP:**********|IP:|MAC:CE0000000000|RTSPPwd:162449Q0p31EZ441|Type:0|Pub:1|Relay:0,门1,#;1,门2,0
check_uid=**********|**********|Name:Manage1|UID:**********|SIP:**********|IP:|MAC:CE0000000001|RTSPPwd:ok1643490ww353w4|Type:3|Pub:1
check_uid=**********|**********|Name:PubUnit0|UID:**********|SIP:**********|IP:|MAC:CE0000000002|RTSPPwd:p16e44Hjn9y80095|Type:0|Pub:1|Relay:0,门1,#;1,门2,0
check_uid=**********|**********|Name:PubUnit1|UID:**********|SIP:|IP:|MAC:CE0000000003|RTSPPwd:D716O449T5n80113|Type:0|Pub:1|Relay:0,门1,#;1,门2,0
check_uid=**********|**********|Name:UnitManage0|UID:**********|SIP:**********|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3|Pub:1
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:**********|Type:100|MatchDtmf1:3961155
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Type:100|MatchDtmf1:2345678
check_uid=**********|**********|Name:FamilyDoor1|UID:**********|SIP:|IP:|MAC:CE0000000009|RTSPPwd:c1cf644N99e38W94|Type:0|Relay:0,门1,#;1,门2,0

[case_dev_unit1]
check_file_path=/var/www//download/community/5/1/Public_1/ContactList/CE0000000002.xml

check_group=**********|Name:System Test0|Room:Room100|Node:**********|SIP:**********|Unit:B1|RoomN:100|UnitApt:1-100|IpDirect:1|CallLoop:0
check_uid=**********|**********|Name:System Test0|UID:**********|SIP:**********|Master:1|Type:100|MatchDtmf1:

check_group=**********|Name:System Test1|Room:Room101|Node:**********|SIP:**********|Unit:B1|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:1
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:************|SIP0:**********|Land:************;************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:**********|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2|GroupCall:1

check_uid=PubInfo|**********|Name:PubUnit1|UID:**********|SIP:**********|Unit:B1|IP:|MAC:CE0000000003|RTSPPwd:D716O449T5n80113|Type:0
check_uid=PubInfo|**********|Name:UnitManage0|UID:**********|SIP:|Unit:B1|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3


[case_dev_unit2]
check_file_path=/var/www//download/community/5/1/Public_1/ContactList/CE0000000003.xml

check_group=**********|Name:System Test0|Room:Room100|Node:**********|SIP:**********|Unit:B1|RoomN:100|UnitApt:1-100|IpDirect:1|CallLoop:0
check_uid=**********|**********|Name:System Test0|UID:**********|SIP:**********|Master:1|Type:100|MatchDtmf1:

check_group=**********|Name:System Test1|Room:Room101|Node:**********|SIP:**********|Unit:B1|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:1
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:************|SIP0:**********|Land:************;************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2

check_uid=PubInfo|**********|Name:PubUnit0|UID:**********|SIP:**********|Unit:B1|IP:|MAC:CE0000000002|RTSPPwd:p16e44Hjn9y80095|Type:0
check_uid=PubInfo|**********|Name:UnitManage0|UID:**********|SIP:**********|Unit:B1|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3

[case_dev_pub1]
check_file_path=/var/www//download/community/5/Public_5/ContactList/CE0000000000.xml

check_group=**********|Name:System Test0|Room:Room100|Node:**********|SIP:**********|Unit:B1|RoomN:100|UnitApt:1-100|IpDirect:1|CallLoop:0
check_uid=**********|**********|Name:System Test0|UID:**********|SIP:**********|Master:1|Type:100|MatchDtmf1:

check_group=**********|Name:System Test1|Room:Room101|Node:**********|SIP:**********|Unit:B1|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:1
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:************|SIP0:**********|Land:************;************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:**********|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2|GroupCall:1

check_group=6300000002|Name:System Test2|Room:Room102|Node:6300100004|SIP:6300000002|Unit:B2|RoomN:102|UnitApt:2-102|IpDirect:1|CallLoop:0
check_uid=6300000002|6300100004|Name:System Test2|UID:6300100004|SIP:6300100004|Master:1|Type:100|MatchDtmf1:
check_uid=6300000002|6300100015|Name:Monitor2|UID:6300100015|SIP:|IP:|MAC:CE000000000A|RTSPPwd:gk1644s9YR943100|Type:2

check_group=6300000003|Name:System Test3|Room:Room103|Node:6300100005|SIP:6300000003|Unit:B2|RoomN:103|UnitApt:2-103|IpDirect:1|CallLoop:2
check_uid=6300000003|6300100005|Name:System Test3|UID:6300100005|SIP:08622334455|SIP0:6300100005|Land:08622334466|Type:100|GroupCall:1|Master:1|MatchDtmf1:2334455|MatchDtmf2:6300100005|MatchDtmf3:2334466|MatchDtmf4:|seq:2-2;2-3

check_uid=PubInfo|**********|Name:Manage1|UID:**********|SIP:|Unit:|IP:|MAC:CE0000000001|RTSPPwd:ok1643490ww353w4|Type:3
check_uid=PubInfo|**********|Name:PubUnit0|UID:**********|SIP:|Unit:B1|IP:|MAC:CE0000000002|RTSPPwd:p16e44Hjn9y80095|Type:0
check_uid=PubInfo|**********|Name:PubUnit1|UID:**********|SIP:**********|Unit:B1|IP:|MAC:CE0000000003|RTSPPwd:D716O449T5n80113|Type:0
check_uid=PubInfo|**********|Name:UnitManage0|UID:**********|SIP:|Unit:B1|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3
check_uid=PubInfo|6300100009|Name:PubUnit2|UID:6300100009|SIP:|Unit:B2|IP:|MAC:CE0000000005|RTSPPwd:W16644980J23LEy9|Type:0
check_uid=PubInfo|6300100010|Name:PubUnit3|UID:6300100010|SIP:|Unit:B2|IP:|MAC:CE0000000006|RTSPPwd:jS1O64498072J5f9|Type:0
check_uid=PubInfo|6300100011|Name:UnitManage1|UID:6300100011|SIP:|Unit:B2|IP:|MAC:CE0000000007|RTSPPwd:2i1644GA98VY0323|Type:3


[case_dev_pub2]
check_file_path=/var/www//download/community/5/Public_5/ContactList/CE0000000001.xml

check_group=**********|Name:System Test0|Room:Room100|Node:**********|SIP:**********|Unit:B1|RoomN:100|UnitApt:1-100|IpDirect:1|CallLoop:0
check_uid=**********|**********|Name:System Test0|UID:**********|SIP:**********|Master:1|Type:100|MatchDtmf1:

check_group=**********|Name:System Test1|Room:Room101|Node:**********|SIP:**********|Unit:B1|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:1
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:************|SIP0:**********|Land:************;************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:**********|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2|GroupCall:1
check_uid=**********|**********|Name:FamilyDoor1|UID:**********|SIP:**********|IP:|MAC:CE0000000009|RTSPPwd:c1cf644N99e38W94|Type:0|GroupCall:1

check_group=6300000002|Name:System Test2|Room:Room102|Node:6300100004|SIP:6300000002|Unit:B2|RoomN:102|UnitApt:2-102|IpDirect:1|CallLoop:0
check_uid=6300000002|6300100004|Name:System Test2|UID:6300100004|SIP:6300100004|Master:1|Type:100|MatchDtmf1:
check_uid=6300000002|6300100015|Name:Monitor2|UID:6300100015|SIP:|IP:|MAC:CE000000000A|RTSPPwd:gk1644s9YR943100|Type:2

check_group=6300000003|Name:System Test3|Room:Room103|Node:6300100005|SIP:6300000003|Unit:B2|RoomN:103|UnitApt:2-103|IpDirect:1|CallLoop:2
check_uid=6300000003|6300100005|Name:System Test3|UID:6300100005|SIP:08622334455|SIP0:6300100005|Land:08622334466|Type:100|GroupCall:1|Master:1|MatchDtmf1:2334455|MatchDtmf2:6300100005|MatchDtmf3:2334466|MatchDtmf4:|seq:2-2;2-3

check_uid=PubInfo|**********|Name:Pub1|UID:**********|SIP:|Unit:|IP:|MAC:CE0000000000|RTSPPwd:162449Q0p31EZ441|Type:0
check_uid=PubInfo|**********|Name:PubUnit0|UID:**********|SIP:|Unit:B1|IP:|MAC:CE0000000002|RTSPPwd:p16e44Hjn9y80095|Type:0
check_uid=PubInfo|**********|Name:PubUnit1|UID:**********|SIP:**********|Unit:B1|IP:|MAC:CE0000000003|RTSPPwd:D716O449T5n80113|Type:0
check_uid=PubInfo|**********|Name:UnitManage0|UID:**********|SIP:|Unit:B1|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3
check_uid=PubInfo|6300100009|Name:PubUnit2|UID:6300100009|SIP:|Unit:B2|IP:|MAC:CE0000000005|RTSPPwd:W16644980J23LEy9|Type:0
check_uid=PubInfo|6300100010|Name:PubUnit3|UID:6300100010|SIP:|Unit:B2|IP:|MAC:CE0000000006|RTSPPwd:jS1O64498072J5f9|Type:0
check_uid=PubInfo|6300100011|Name:UnitManage1|UID:6300100011|SIP:|Unit:B2|IP:|MAC:CE0000000007|RTSPPwd:2i1644GA98VY0323|Type:3


#[case_common]
#把file放在这里，那么写sql就可以很清晰知道更新哪个mac
#check_file_path=/var/www//download/community/5/1/**********/ContactList/CE0000000009.xml
#sql_cmd=
#group|属性:值|...
#check_group=**********|Name:System Test1|
#gruop|uid|属性:值|...
#check_uid=**********|**********|Name:System Test1|
#当检测项多了可以通过json进行配置
#check_by_json=1

[case_dev_node_3]
check_file_path=/var/www//download/community/5/1/**********/ContactList/CE0000000009.xml
sql_cmd=update PersonalAccountCnf set CallType=4 where Account=**********

check_group=**********|Name:System Test1|Room:Room101|Node:**********|Unit:B1|SIP:**********|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:2
check_uid=**********|**********|Name:System Test1|SIP:************|UID:**********|SIP0:**********|Land:************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:|seq:2-2;2-3
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100|seq2:1-3;
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2|seq:1-2


[case_dev_node_20]
sql_cmd=update PersonalAccount set EnableIpDirect=0 where Account=**********
check_file_path=/var/www//download/community/5/1/Public_1/ContactList/CE0000000002.xml

check_group=**********|Name:System Test0|Room:Room100|Node:**********|SIP:**********|Unit:B1|RoomN:100|UnitApt:1-100|IpDirect:1|CallLoop:0
check_uid=**********|**********|Name:System Test0|UID:**********|SIP:**********|Master:1|Type:100|MatchDtmf1:

check_group=**********|Name:System Test1|Room:Room101|Node:**********|SIP:**********|Unit:B1|RoomN:101|UnitApt:1-101|IpDirect:0|CallLoop:2
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:************|SIP0:**********|Land:************|Type:100|GroupCall:1|Master:1|MatchDtmf1:3961155|MatchDtmf2:**********|MatchDtmf3:3961156|MatchDtmf4:|seq:2-2;2-3
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Land:************|MatchDtmf1:2345678|Type:100|seq2:1-3;
check_uid=**********|**********|Name:Monitor|UID:**********|SIP:**********|IP:|MAC:CE0000000008|RTSPPwd:AU16Ug44Uy993850|Type:2|seq:1-2


check_uid=PubInfo|**********|Name:PubUnit1|UID:**********|SIP:**********|Unit:B1|IP:|MAC:CE0000000003|RTSPPwd:D716O449T5n80113|Type:0
check_uid=PubInfo|**********|Name:UnitManage0|UID:**********|SIP:|Unit:B1|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3

[case_dev_node_10]
check_file_path=/var/www//download/community/5/1/**********/ContactList/CE0000000008.xml
sql_cmd=update PersonalAccount set EnableIpDirect=1 where Account=**********
sql_cmd=update Devices set NetGroupNumber=3 where Mac='CE0000000009'

check_group=**********|Name:System Test1|Room:Room101|Node:**********|Unit:B1|SIP:**********|RoomN:101|UnitApt:1-101|IpDirect:1|CallLoop:2
check_uid=**********|**********|Name:Pub1|UID:**********|SIP:**********|IP:|MAC:CE0000000000|RTSPPwd:162449Q0p31EZ441|Type:0|Pub:1|Relay:0,门1,#;1,门2,0
check_uid=**********|**********|Name:Manage1|UID:**********|SIP:**********|IP:|MAC:CE0000000001|RTSPPwd:ok1643490ww353w4|Type:3|Pub:1
check_uid=**********|**********|Name:PubUnit0|UID:**********|SIP:**********|IP:|MAC:CE0000000002|RTSPPwd:p16e44Hjn9y80095|Type:0|Pub:1|Relay:0,门1,#;1,门2,0
check_uid=**********|**********|Name:PubUnit1|UID:**********|SIP:|IP:|MAC:CE0000000003|RTSPPwd:D716O449T5n80113|Type:0|Pub:1|Relay:0,门1,#;1,门2,0
check_uid=**********|**********|Name:UnitManage0|UID:**********|SIP:**********|IP:|MAC:CE0000000004|RTSPPwd:16a4k498u0v1c8R4|Type:3|Pub:1
check_uid=**********|**********|Name:System Test1|UID:**********|SIP:**********|Type:100|MatchDtmf1:3961155
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Type:100|MatchDtmf1:2345678
check_uid=**********|**********|Name:FamilyDoor1|UID:**********|SIP:**********|IP:|MAC:CE0000000009|RTSPPwd:c1cf644N99e38W94|Type:0|Relay:0,门1,#;1,门2,0


[case_dev_node_17]
check_file_path=/var/www//download/community/5/Public_5/ContactList/CE0000000001.xml
sql_cmd=update PersonalAccountCnf set CallType=3 where Account=**********
check_uid=**********|**********|Name:101 Slave1|UID:**********|SIP:**********|Type:100|MatchDtmf1:2345678|seq:2-2
















