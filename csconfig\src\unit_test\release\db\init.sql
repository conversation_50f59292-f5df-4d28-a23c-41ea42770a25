-- MySQL dump 10.13  Distrib 5.6.33, for linux-glibc2.5 (x86_64)
--
-- Host: 127.0.0.1    Database: AKCS
-- ------------------------------------------------------
-- Server version	5.6.33-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `APPSpecial`
--

DROP TABLE IF EXISTS `APPSpecial`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `APPSpecial` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Node` char(32) NOT NULL COMMENT 'å®¶åº­æ ‡è¯†',
  `Account` char(32) NOT NULL COMMENT 'ä»Žè´¦å·æ ‡è¯†',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'åˆ›å»ºæ—¶é—´',
  PRIMARY KEY (`ID`),
  KEY `Account` (`Account`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `APPSpecial`
--

LOCK TABLES `APPSpecial` WRITE;
/*!40000 ALTER TABLE `APPSpecial` DISABLE KEYS */;
/*!40000 ALTER TABLE `APPSpecial` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AccessGroup`
--

DROP TABLE IF EXISTS `AccessGroup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AccessGroup` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(64) NOT NULL,
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'åˆ›å»ºæ—¶é—´',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT '0',
  `UnitID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'è¡¨ç¤ºæŸä¸ªæ¥¼æ ‹çš„ç³»ç»Ÿæƒé™ç»„ï¼Œå¯¹åº”CommunityUnit ID,0ä¸ºæ™®é€šæƒé™ç»„',
  `SchedulerType` tinyint(4) unsigned NOT NULL COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `BeginTime` datetime NOT NULL DEFAULT '2021-01-01 00:00:00' COMMENT 'å•æ¬¡è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `EndTime` datetime NOT NULL DEFAULT '2299-01-01 00:00:00' COMMENT 'å•æ¬¡è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT '00:00:00' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Name` (`CommunityID`,`Name`),
  KEY `UnitID` (`UnitID`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AccessGroup`
--

LOCK TABLES `AccessGroup` WRITE;
/*!40000 ALTER TABLE `AccessGroup` DISABLE KEYS */;
INSERT INTO `AccessGroup` VALUES (1,'住户-楼栋 B1','2022-02-15 11:30:29',5,1,1,0,'2021-01-01 00:00:00','2299-01-01 00:00:00','00:00:00','23:59:59');
INSERT INTO `AccessGroup` VALUES (2,'住户-楼栋 B2','2022-02-15 11:30:34',5,2,1,0,'2021-01-01 00:00:00','2299-01-01 00:00:00','00:00:00','23:59:59');
/*!40000 ALTER TABLE `AccessGroup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AccessGroupDevice`
--

DROP TABLE IF EXISTS `AccessGroupDevice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AccessGroupDevice` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AccessGroupID` int(10) unsigned NOT NULL,
  `MAC` char(12) NOT NULL COMMENT 'è®¾å¤‡çš„mac',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `AccessGroupID` (`AccessGroupID`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AccessGroupDevice`
--

LOCK TABLES `AccessGroupDevice` WRITE;
/*!40000 ALTER TABLE `AccessGroupDevice` DISABLE KEYS */;
/*!40000 ALTER TABLE `AccessGroupDevice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Account`
--

DROP TABLE IF EXISTS `Account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Account` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL,
  `Passwd` char(64) NOT NULL,
  `Grade` tinyint(1) DEFAULT '0' COMMENT 'ç­‰çº§:1è¶…;11åŒºåŸŸ;21å°åŒº;22ä¸ªäººç»ˆç«¯;31ç‰©ä¸š',
  `Role` tinyint(1) DEFAULT '0' COMMENT 'è§’è‰²:0è¶…ç®¡;1ç®¡ç†å‘˜|åŒºç®¡;2å°åŒºç‰©ä¸š;3å°åŒºå®‰è£…è€…;4ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜',
  `ParentID` int(10) DEFAULT NULL,
  `Location` char(32) NOT NULL COMMENT 'å¯¹äºŽå°åŒºç®¡ç†å‘˜:å°åŒºåï¼Œå¯¹äºŽä¸ªäººç»ˆç«¯ç®¡ç†å‘˜:å…¬å¸',
  `Email` char(32) NOT NULL,
  `Info` char(64) DEFAULT '',
  `SipPrefix` int(10) NOT NULL DEFAULT '0' COMMENT 'å¯¹äºŽæ¯ä¸€çº§ç®¡ç†å‘˜ï¼Œè®°å½•åˆ°æœ¬çº§sipè§„åˆ™çš„å‰ç¼€',
  `Special` tinyint(1) DEFAULT '0',
  `Phone` char(20) DEFAULT '',
  `TimeZone` char(64) DEFAULT '+0:00 Abidjan' COMMENT 'æ—¶åŒº',
  `HouseCount` int(11) DEFAULT '100' COMMENT 'ä¸ªäººç»ˆç«¯ä¸»è´¦å·åˆ›å»ºä¸ªæ•°',
  `EnableValidTimeSetting` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'ä¸ªäººç»ˆç«¯è®¾ç½®ä¸»è´¦å·æ—¶é—´',
  `EnableCountSetting` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'ä¸ªäººç»ˆç«¯è®¾ç½®ä¸»è´¦å·app/devä¸ªæ•°',
  `ManageGroup` int(11) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜/ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜ç»„æ ‡è¯†',
  `CustomizeForm` tinyint(1) DEFAULT '3' COMMENT 'time1:ï¼ˆ1:12å°æ—¶åˆ¶ï¼Œ2:24å°æ—¶åˆ¶ï¼‰ï¼›time2:(1:y-m-d,3:m-d-y,5:d-m-y),æ­¤å¤„å€¼ä¸ºtime1+time2',
  `ChargeMode` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'é’ˆå¯¹distributorå’Œinstaller 0ï¼šä¸‹çº§ç¼´è´¹ï¼›1ï¼šæœ¬çº§ç¼´è´¹',
  `SipType` tinyint(4) DEFAULT '3' COMMENT '0=udp  1=tcp 2=tls 3=none, æ€»å¼€å…³ å¦‚æžœæ˜¯noneæŒ‰è®¾å¤‡è¡¨SipTypeå†™é…ç½®',
  `Initialization` tinyint(1) DEFAULT '0' COMMENT 'æ˜¯å¦åˆå§‹åŒ–è¿‡ï¼Œç”¨äºŽinstalleré¦–æ¬¡ç™»å½•ä¿®æ”¹å¯†ç ',
  `Language` char(8) NOT NULL DEFAULT 'en',
  `SendExpireEmailType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:å‘é€ç»™è‡ªå·±ï¼Œ2:å‘é€ç»™PM/User',
  `Flags` int(11) DEFAULT '0' COMMENT 'æ ‡è¯†ä½: ç¬¬1ä½rtpæ··æ·†å¼€å…³,é»˜è®¤å…³',
  `CreateTime` datetime DEFAULT NULL,
  `SendRenew` tinyint(1) DEFAULT '1' COMMENT 'æ˜¯å¦å‘é€é‚®ç®±é€šçŸ¥æ›´æ–° 0:ä¸é€šçŸ¥ 1:é€šçŸ¥',
  `UUID` char(36) NOT NULL COMMENT 'å”¯ä¸€æ ‡è¯†',
  `ParentUUID` char(36) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE KEY `UUID` (`UUID`),
  KEY `ManageGroup` (`ManageGroup`),
  KEY `Grade_SipPreFix` (`Grade`,`SipPrefix`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Account`
--

LOCK TABLES `Account` WRITE;
/*!40000 ALTER TABLE `Account` DISABLE KEYS */;
INSERT INTO `Account` VALUES (1,'supermanage','68cf63c62bc68d71fc41c028375e2f6e',1,0,0,'','','',0,0,'','+0:00 Abidjan',100,0,0,0,3,0,3,1,'zh-cn',1,0,NULL,1,'','');
INSERT INTO `Account` VALUES (2,'unittest_dis','68cf63c62bc68d71fc41c028375e2f6e',11,1,1,'','','',0,0,'','+0:00 Abidjan',100,0,0,0,3,0,3,1,'zh-cn',1,0,NULL,1,'d830d1738e0c11ec96650242ac110001','');
INSERT INTO `Account` VALUES (3,'unittest_dis-PersonalManage','7c1efd7ef796e82c8ddcdcd691df7035',22,5,2,'','','',0,1,'','+0:00 Abidjan',100,0,0,3,3,0,3,0,'zh-cn',1,0,NULL,1,'d84add6e8e0c11ec96650242ac110001','');
INSERT INTO `Account` VALUES (4,'unittest_ins','68cf63c62bc68d71fc41c028375e2f6e',22,4,2,'','','',0,0,NULL,'+0:00 Abidjan',10000,0,0,4,3,0,3,1,'zh-cn',1,0,NULL,1,'4bce8d418e0d11ec96650242ac110001','');
INSERT INTO `Account` VALUES (5,'16448cAN95N6Ei708','2f6b1bdecc8044a42d0a8057a1d4eb92',21,2,2,'UnitTestComm','','',0,1,'','+0:00 Abidjan',100,0,0,4,3,1,3,0,'en',1,0,'2022-02-16 14:40:03',0,'459410248e0f11ec96650242ac110001','');
/*!40000 ALTER TABLE `Account` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AccountAccess`
--

DROP TABLE IF EXISTS `AccountAccess`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AccountAccess` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `AccessGroupID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `AccessGroupID` (`AccessGroupID`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AccountAccess`
--

LOCK TABLES `AccountAccess` WRITE;
/*!40000 ALTER TABLE `AccountAccess` DISABLE KEYS */;
INSERT INTO `AccountAccess` VALUES (1,'**********',1);
INSERT INTO `AccountAccess` VALUES (2,'**********',1);
INSERT INTO `AccountAccess` VALUES (3,'**********',2);
INSERT INTO `AccountAccess` VALUES (4,'**********',2);
INSERT INTO `AccountAccess` VALUES (5,'**********',1);
/*!40000 ALTER TABLE `AccountAccess` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Alarms`
--

DROP TABLE IF EXISTS `Alarms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Alarms` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AlarmType` char(64) DEFAULT '',
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `UnitID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¨å±€çš„å•å…ƒunit ID',
  `DevicesMAC` char(16) DEFAULT NULL COMMENT 'å‘Šè­¦è®¾å¤‡çš„macåœ°å€',
  `Node` char(16) DEFAULT '',
  `AlarmTime` datetime DEFAULT NULL,
  `Status` int(4) DEFAULT NULL,
  `DealTime` datetime DEFAULT NULL,
  `DealUser` char(64) DEFAULT '',
  `DealType` int(11) DEFAULT NULL,
  `DealResult` varchar(1024) DEFAULT '',
  `AlarmCode` tinyint(4) DEFAULT '0' COMMENT '0 old data/1-Door Unlock/2-Infrared/3-Drmagent/4-Smoke/5-Gas/6-Urgency/7-SOS/8-Tamper',
  `AlarmCustomize` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è‡ªå®šä¹‰',
  `AlarmLocation` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'ä½ç½®',
  `AlarmZone` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'é˜²åŒº',
  PRIMARY KEY (`ID`),
  KEY `MAC` (`DevicesMAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Alarms`
--

LOCK TABLES `Alarms` WRITE;
/*!40000 ALTER TABLE `Alarms` DISABLE KEYS */;
/*!40000 ALTER TABLE `Alarms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ApiClient`
--

DROP TABLE IF EXISTS `ApiClient`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ApiClient` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ClientId` char(32) NOT NULL,
  `ClientSecret` char(64) NOT NULL,
  `ClientName` varchar(32) NOT NULL DEFAULT 'Akuvox' COMMENT 'ç¬¬ä¸‰æ–¹APPä»£å·',
  `Trust` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'ä¿¡ä»»ç­‰çº§ 1-ç›´æŽ¥è¿”å›žå¯†ç ',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `ClientId` (`ClientId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ApiClient`
--

LOCK TABLES `ApiClient` WRITE;
/*!40000 ALTER TABLE `ApiClient` DISABLE KEYS */;
/*!40000 ALTER TABLE `ApiClient` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AppCallDND`
--

DROP TABLE IF EXISTS `AppCallDND`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AppCallDND` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) DEFAULT '' COMMENT 'PersonalAccount',
  `Status` tinyint(1) DEFAULT '0' COMMENT 'æ˜¯å¦å¼€å¯',
  `StartTime` int(10) unsigned DEFAULT '0' COMMENT 'å¼€å§‹æ—¶é—´,ä»Ž0ç‚¹åˆ°å¼€å§‹æ—¶é—´çš„åˆ†é’Ÿæ•°,æœ€å¤§å€¼:24*60',
  `EndTime` int(10) unsigned DEFAULT '0' COMMENT 'ç»“æŸæ—¶é—´,ä»Ž0ç‚¹åˆ°ç»“æŸæ—¶é—´çš„åˆ†é’Ÿæ•°,æœ€å¤§å€¼:24*60',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AppCallDND`
--

LOCK TABLES `AppCallDND` WRITE;
/*!40000 ALTER TABLE `AppCallDND` DISABLE KEYS */;
/*!40000 ALTER TABLE `AppCallDND` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AppEnduserFeedback`
--

DROP TABLE IF EXISTS `AppEnduserFeedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AppEnduserFeedback` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` int(10) unsigned DEFAULT '0' COMMENT 'ç”¨æˆ·è´¦å·',
  `ContactEmail` varchar(64) DEFAULT '' COMMENT 'è”ç³»çš„email',
  `Content` varchar(2048) DEFAULT '' COMMENT 'å†…å®¹',
  `FileList` varchar(512) DEFAULT '' COMMENT 'æ–‡ä»¶åˆ—è¡¨;åˆ†å·åˆ†å‰²(æ–‡ä»¶å­˜å‚¨é»˜è®¤è·¯å¾„/var/www/upload/freeback)',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AppEnduserFeedback`
--

LOCK TABLES `AppEnduserFeedback` WRITE;
/*!40000 ALTER TABLE `AppEnduserFeedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `AppEnduserFeedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AppLoginLog`
--

DROP TABLE IF EXISTS `AppLoginLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AppLoginLog` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Uid` char(32) NOT NULL,
  `Node` char(32) NOT NULL,
  `LoginTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AppLoginLog`
--

LOCK TABLES `AppLoginLog` WRITE;
/*!40000 ALTER TABLE `AppLoginLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `AppLoginLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AppManualRtsp`
--

DROP TABLE IF EXISTS `AppManualRtsp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AppManualRtsp` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `CreateTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `Duration` int(11) DEFAULT '0' COMMENT 'ç›‘æŽ§æ—¶é•¿',
  `UserAccount` char(20) DEFAULT '' COMMENT 'ç›‘æŽ§æ“ä½œäºº',
  `Account` char(20) DEFAULT '' COMMENT 'ç›‘æŽ§äººæ‰€åœ¨çš„è”åŠ¨',
  `MAC` char(20) DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AppManualRtsp`
--

LOCK TABLES `AppManualRtsp` WRITE;
/*!40000 ALTER TABLE `AppManualRtsp` DISABLE KEYS */;
/*!40000 ALTER TABLE `AppManualRtsp` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AppPushToken`
--

DROP TABLE IF EXISTS `AppPushToken`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AppPushToken` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(20) NOT NULL DEFAULT '',
  `AppType` tinyint(1) DEFAULT '0' COMMENT '0ios 4fcm',
  `FcmPushToken` varchar(256) DEFAULT '' COMMENT 'æŽ¨é€çš„token',
  `IOSPushToken` varchar(256) DEFAULT '' COMMENT 'æŽ¨é€çš„token',
  `VoipToken` varchar(256) DEFAULT '' COMMENT 'æŽ¨é€çš„voip token',
  `Node` char(20) NOT NULL DEFAULT '' COMMENT 'ç”¨æˆ·çš„è”åŠ¨æ ‡è¯†',
  `Login` tinyint(1) DEFAULT '1' COMMENT 'ç™»é™†çŠ¶æ€ 1ç™»é™† 0é€€å‡º',
  `Version` int(10) DEFAULT '0' COMMENT 'é€šç”¨ç‰ˆæœ¬å·ios/androidä¸€æ ·,å³é€šè¿‡dclientè®¾ç½®',
  `AppVersion` varchar(32) DEFAULT '' COMMENT 'appä¸Šæž¶ç‰ˆæœ¬å·',
  `Language` char(16) DEFAULT 'en' COMMENT 'app å®¢æˆ·ç«¯è¯­è¨€',
  `Oem` varchar(32) NOT NULL COMMENT 'ç¬¬ä¸‰æ–¹APP',
  `LandlineNotifyStatus` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0æœªå‘é€è½åœ°å·ç æ›´æ–°é€šçŸ¥ 1å·²å‘é€',
  `OnlineTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'APPæœ€è¿‘ä¸Šçº¿æ—¶é—´',
  `AppOem` tinyint(1) unsigned DEFAULT '0' COMMENT 'APPç±»åž‹:0:Common App;1:MySmart App;2:Fasttel App;',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AppPushToken`
--

LOCK TABLES `AppPushToken` WRITE;
/*!40000 ALTER TABLE `AppPushToken` DISABLE KEYS */;
/*!40000 ALTER TABLE `AppPushToken` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AuditLog`
--

DROP TABLE IF EXISTS `AuditLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AuditLog` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `IP` varchar(64) NOT NULL,
  `Operator` varchar(64) NOT NULL COMMENT 'æ“ä½œè€…',
  `Distributor` varchar(64) DEFAULT '',
  `Installer` varchar(64) DEFAULT '',
  `Community` varchar(64) DEFAULT '',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'åˆ›å»ºæ—¶é—´',
  `Type` tinyint(4) NOT NULL COMMENT 'æ—¥å¿—ç±»åž‹',
  `KeyInfo` varchar(1024) NOT NULL COMMENT 'æ—¥å¿—æ›¿æ¢Keyï¼Œä½¿ç”¨jsonæ•°ç»„',
  `OperaType` enum('SuperManager','Distributor','Installer','PM','SingleMaster','SingleMember','CommunityMaster','CommunityMember') NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `Operator` (`Operator`),
  KEY `Type_CreateTime` (`Type`,`CreateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AuditLog`
--

LOCK TABLES `AuditLog` WRITE;
/*!40000 ALTER TABLE `AuditLog` DISABLE KEYS */;
INSERT INTO `AuditLog` VALUES (1,'192.168.14.98','supermanage',NULL,NULL,NULL,'2022-02-15 09:29:35',65,'[]','SuperManager');
INSERT INTO `AuditLog` VALUES (2,'192.168.14.98','supermanage','supermanage',NULL,NULL,'2022-02-15 11:10:36',21,'[\"unittest_dis\"]','SuperManager');
INSERT INTO `AuditLog` VALUES (3,'192.168.14.98','supermanage','supermanage',NULL,NULL,'2022-02-15 11:10:36',32,'[\"GTM+0:00 Abidjan\"]','SuperManager');
INSERT INTO `AuditLog` VALUES (4,'192.168.14.98','unittest_dis','supermanage',NULL,NULL,'2022-02-15 11:10:46',65,'[]','Distributor');
INSERT INTO `AuditLog` VALUES (5,'192.168.14.98','unittest_dis','supermanage',NULL,NULL,'2022-02-15 11:13:08',25,'[]','Distributor');
INSERT INTO `AuditLog` VALUES (6,'192.168.14.98','unittest_dis','supermanage',NULL,NULL,'2022-02-15 11:13:18',65,'[]','Distributor');
INSERT INTO `AuditLog` VALUES (7,'192.168.14.98','unittest_dis','unittest_dis','unittest_ins',NULL,'2022-02-15 11:13:49',34,'[]','Distributor');
INSERT INTO `AuditLog` VALUES (8,'192.168.14.98','unittest_dis','unittest_dis','unittest_ins',NULL,'2022-02-15 11:13:49',35,'[]','Distributor');
INSERT INTO `AuditLog` VALUES (9,'192.168.14.98','unittest_dis','unittest_dis','unittest_ins',NULL,'2022-02-15 11:13:49',18,'[\"unittest_ins\"]','Distributor');
INSERT INTO `AuditLog` VALUES (10,'192.168.14.98','unittest_dis','unittest_dis','unittest_ins',NULL,'2022-02-15 11:13:49',32,'[\"GTM+0:00 Abidjan\"]','Distributor');
INSERT INTO `AuditLog` VALUES (11,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 11:14:08',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (12,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 11:14:17',25,'[]','Installer');
INSERT INTO `AuditLog` VALUES (13,'192.168.14.98','unittest_dis','supermanage',NULL,NULL,'2022-02-15 11:18:21',65,'[]','Distributor');
INSERT INTO `AuditLog` VALUES (14,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 11:23:55',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (15,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 11:27:58',39,'[\"unittest_ins-UnitTestComm\"]','Installer');
INSERT INTO `AuditLog` VALUES (16,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 11:27:58',51,'[\"GTM+0:00 Abidjan\"]','Installer');
INSERT INTO `AuditLog` VALUES (17,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 11:27:58',52,'[]','Installer');
INSERT INTO `AuditLog` VALUES (18,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 13:36:28',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (19,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 13:37:21',60,'[\"CE**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (20,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 13:37:21',46,'[\"0\",\"CE**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (21,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 13:38:54',63,'[\"CE0000000001\"]','Installer');
INSERT INTO `AuditLog` VALUES (22,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 13:38:54',46,'[\"0\",\"CE0000000001\"]','Installer');
INSERT INTO `AuditLog` VALUES (23,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:32:51',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (24,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:34:53',42,'[\"B1\",\"100\"]','Installer');
INSERT INTO `AuditLog` VALUES (25,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:34:53',12,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (26,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:34:53',54,'[\"B1\",\"100\"]','Installer');
INSERT INTO `AuditLog` VALUES (27,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:35:17',42,'[\"B1\",\"101\"]','Installer');
INSERT INTO `AuditLog` VALUES (28,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:35:17',12,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (29,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:35:17',54,'[\"B1\",\"101\"]','Installer');
INSERT INTO `AuditLog` VALUES (30,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:36:47',42,'[\"B2\",\"102\"]','Installer');
INSERT INTO `AuditLog` VALUES (31,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:36:47',12,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (32,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:36:47',54,'[\"B2\",\"102\"]','Installer');
INSERT INTO `AuditLog` VALUES (33,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:37:05',42,'[\"B2\",\"103\"]','Installer');
INSERT INTO `AuditLog` VALUES (34,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:37:05',12,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (35,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-15 15:37:05',54,'[\"B2\",\"103\"]','Installer');
INSERT INTO `AuditLog` VALUES (36,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:38:12',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (37,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:38:23',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (38,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:38:33',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (39,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-15 15:38:42',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (40,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-16 10:52:23',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (41,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:54:56',60,'[\"CE0000000002\"]','Installer');
INSERT INTO `AuditLog` VALUES (42,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:54:56',46,'[\"0\",\"CE0000000002\"]','Installer');
INSERT INTO `AuditLog` VALUES (43,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:55:13',60,'[\"CE0000000003\"]','Installer');
INSERT INTO `AuditLog` VALUES (44,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:55:13',46,'[\"0\",\"CE0000000003\"]','Installer');
INSERT INTO `AuditLog` VALUES (45,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:56:24',63,'[\"CE0000000004\"]','Installer');
INSERT INTO `AuditLog` VALUES (46,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:56:24',46,'[\"0\",\"CE0000000004\"]','Installer');
INSERT INTO `AuditLog` VALUES (47,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:57:19',60,'[\"CE0000000005\"]','Installer');
INSERT INTO `AuditLog` VALUES (48,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:57:19',46,'[\"0\",\"CE0000000005\"]','Installer');
INSERT INTO `AuditLog` VALUES (49,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:57:39',60,'[\"CE0000000006\"]','Installer');
INSERT INTO `AuditLog` VALUES (50,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:57:39',46,'[\"0\",\"CE0000000006\"]','Installer');
INSERT INTO `AuditLog` VALUES (51,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:58:43',63,'[\"CE0000000007\"]','Installer');
INSERT INTO `AuditLog` VALUES (52,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 10:58:43',46,'[\"0\",\"CE0000000007\"]','Installer');
INSERT INTO `AuditLog` VALUES (53,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-16 14:37:23',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (54,'192.168.14.98','supermanage',NULL,NULL,NULL,'2022-02-16 14:39:52',65,'[]','SuperManager');
INSERT INTO `AuditLog` VALUES (55,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-16 14:42:36',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (56,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:43:22',47,'[**********]','Installer');
INSERT INTO `AuditLog` VALUES (57,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:43:22',44,'[\"********\",\"101 Slave1\"]','Installer');
INSERT INTO `AuditLog` VALUES (58,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:44:10',62,'[\"CE0000000008\"]','Installer');
INSERT INTO `AuditLog` VALUES (59,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:44:10',46,'[\"0\",\"CE0000000008\"]','Installer');
INSERT INTO `AuditLog` VALUES (60,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:44:54',60,'[\"CE0000000009\"]','Installer');
INSERT INTO `AuditLog` VALUES (61,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:44:54',46,'[\"0\",\"CE0000000009\"]','Installer');
INSERT INTO `AuditLog` VALUES (62,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:48:20',62,'[\"CE000000000A\"]','Installer');
INSERT INTO `AuditLog` VALUES (63,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 14:48:20',46,'[\"0\",\"CE000000000A\"]','Installer');
INSERT INTO `AuditLog` VALUES (64,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 15:14:57',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (65,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-02-16 15:15:15',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (66,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-17 15:12:32',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (67,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-02-18 13:59:48',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (68,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-05-17 10:56:39',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (69,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-05-17 14:49:20',65,'[]','Installer');
INSERT INTO `AuditLog` VALUES (70,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-05-17 14:50:06',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (71,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-05-17 14:50:06',56,'[\"B1\",\"101\"]','Installer');
INSERT INTO `AuditLog` VALUES (72,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-05-17 14:50:47',13,'[\"**********\"]','Installer');
INSERT INTO `AuditLog` VALUES (73,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins',NULL,'2022-05-17 14:50:47',58,'[\"B2\",\"103\"]','Installer');
INSERT INTO `AuditLog` VALUES (74,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-05-17 14:54:19',46,'[\"1\",\"CE0000000009\"]','Installer');
INSERT INTO `AuditLog` VALUES (75,'192.168.14.98','unittest_ins','unittest_dis','unittest_ins','16448cAN95N6Ei708','2022-05-17 14:54:24',46,'[\"1\",\"CE0000000008\"]','Installer');
/*!40000 ALTER TABLE `AuditLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AuditLogInfo`
--

DROP TABLE IF EXISTS `AuditLogInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AuditLogInfo` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AuditLogID` int(11) unsigned NOT NULL,
  `Type` smallint(5) NOT NULL COMMENT 'æ—¥å¿—ç±»åž‹; æœ‰æ•°åç§ç±»åž‹,è¯¦è§6.1ç ”å‘è®¾è®¡æ–‡æ¡£',
  `KeyInfo` varchar(1024) NOT NULL COMMENT 'æ—¥å¿—æ›¿æ¢Keyï¼Œä½¿ç”¨jsonæ•°ç»„',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AuditLogInfo`
--

LOCK TABLES `AuditLogInfo` WRITE;
/*!40000 ALTER TABLE `AuditLogInfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `AuditLogInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AwsRedirect`
--

DROP TABLE IF EXISTS `AwsRedirect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AwsRedirect` (
  `ID` int(16) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL COMMENT 'è°ƒåº¦ç²’åº¦ä¸ºå•ä½æˆ·æ—¶ä¸ºInstallerAccount, è°ƒåº¦ç²’åº¦ä¸ºç¤¾åŒºæ—¶ä¸ºCommunityAccount',
  `AccountID` int(10) unsigned NOT NULL COMMENT 'è°ƒåº¦ç²’åº¦ä¸ºå•ä½æˆ·æ—¶ä¸ºInstallerID, è°ƒåº¦ç²’åº¦ä¸ºç¤¾åŒºæ—¶ä¸ºCommunityID',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE KEY `AccountID` (`AccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AwsRedirect`
--

LOCK TABLES `AwsRedirect` WRITE;
/*!40000 ALTER TABLE `AwsRedirect` DISABLE KEYS */;
/*!40000 ALTER TABLE `AwsRedirect` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CallHistory`
--

DROP TABLE IF EXISTS `CallHistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CallHistory` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT NULL COMMENT 'è¢«å«æ‰€åœ¨çš„è”åŠ¨ç³»ç»Ÿï¼Œå› ä¸ºä¸ªäººæ²¡åŠžæ³•å‘¼å«å…¬å…±è®¾å¤‡',
  `CallerID` char(16) NOT NULL DEFAULT '' COMMENT 'ä¸»å«æ–¹çš„sipè´¦å·',
  `CallerName` char(64) NOT NULL DEFAULT '' COMMENT 'ä¸»å«æ–¹çš„æ˜µç§°,è‹¥ä¸»å«æ–¹ä¸ºè®¾å¤‡ï¼Œåˆ™ä¸ºè®¾å¤‡Location;è‹¥ä¸»å«æ–¹ä¸ºApp,åˆ™ä¸ºAppç”¨æˆ·çš„è´¦å·æ˜µç§°',
  `CalleeID` char(16) NOT NULL DEFAULT '' COMMENT 'è¢«å«æ–¹çš„sipè´¦å·',
  `CalleeName` char(64) NOT NULL DEFAULT '' COMMENT 'è¢«å«æ–¹çš„æ˜µç§°,è‹¥è¢«å«æ–¹ä¸ºè®¾å¤‡ï¼Œåˆ™ä¸ºè®¾å¤‡Location;è‹¥è¢«å«æ–¹ä¸ºApp,åˆ™ä¸ºAppç”¨æˆ·çš„è´¦å·æ˜µç§°',
  `IsAnswer` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'å‘¼å«æ˜¯å¦æˆåŠŸæ ‡ç¤º,0:å‘¼å«æˆåŠŸ; 1:å‘¼å«ä¸æˆåŠŸ',
  `StartTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Duration` char(16) NOT NULL DEFAULT '' COMMENT 'å‘¼å«æ—¶é•¿ format 00:00:00',
  `Duration2` int(11) NOT NULL DEFAULT '0',
  `CallType` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'å‘¼å«ç±»åž‹ 1=app->app 2=dev->app 3=app->dev 4=dev->dev',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦å·²è¯»çš„æ ‡å¿—,0:æœªè¯»(appéœ€è¦); 1:å·²è¯»',
  `SipGroup` varchar(20) NOT NULL DEFAULT '' COMMENT 'æ˜¯å¦æ˜¯ç¾¤ç»„å‘¼å«, æœ‰å€¼ä»£è¡¨æ˜¯ç¾¤ç»„å‘¼å«',
  `MngAccountID` int(10) DEFAULT '0' COMMENT 'ç®¡ç†å‘˜id',
  PRIMARY KEY (`ID`,`StartTime`),
  KEY `CallerID` (`CallerID`),
  KEY `CalleeID` (`CalleeID`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
/*!50100 PARTITION BY RANGE (MONTH(StartTime))
(PARTITION P1 VALUES LESS THAN (2) ENGINE = InnoDB,
 PARTITION P2 VALUES LESS THAN (3) ENGINE = InnoDB,
 PARTITION P3 VALUES LESS THAN (4) ENGINE = InnoDB,
 PARTITION P4 VALUES LESS THAN (5) ENGINE = InnoDB,
 PARTITION P5 VALUES LESS THAN (6) ENGINE = InnoDB,
 PARTITION P6 VALUES LESS THAN (7) ENGINE = InnoDB,
 PARTITION P7 VALUES LESS THAN (8) ENGINE = InnoDB,
 PARTITION P8 VALUES LESS THAN (9) ENGINE = InnoDB,
 PARTITION P9 VALUES LESS THAN (10) ENGINE = InnoDB,
 PARTITION P10 VALUES LESS THAN (11) ENGINE = InnoDB,
 PARTITION P11 VALUES LESS THAN (12) ENGINE = InnoDB,
 PARTITION P12 VALUES LESS THAN (13) ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CallHistory`
--

LOCK TABLES `CallHistory` WRITE;
/*!40000 ALTER TABLE `CallHistory` DISABLE KEYS */;
/*!40000 ALTER TABLE `CallHistory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ChargePerPlan`
--

DROP TABLE IF EXISTS `ChargePerPlan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ChargePerPlan` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `EntrFee` int(11) NOT NULL DEFAULT '0',
  `MonthlyFee` int(11) NOT NULL DEFAULT '0',
  `AppsNumber` int(11) NOT NULL DEFAULT '0',
  `AddAppsFee` int(11) NOT NULL DEFAULT '0',
  `LandlineFee` int(11) NOT NULL DEFAULT '0' COMMENT 'è½åœ°æ”¶è´¹',
  `AccountID` int(11) unsigned NOT NULL DEFAULT '1' COMMENT 'AccountID=1ä¸ºç»Ÿä¸€çš„æ”¶è´¹æ–¹æ¡ˆ,å…¶ä½™çš„æ˜¯åŒºåŸŸç®¡ç†å‘˜ç‰¹å®šçš„æ”¶è´¹æ–¹æ¡ˆ',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ChargePerPlan`
--

LOCK TABLES `ChargePerPlan` WRITE;
/*!40000 ALTER TABLE `ChargePerPlan` DISABLE KEYS */;
INSERT INTO `ChargePerPlan` VALUES (1,800,0,4,400,200,1);
/*!40000 ALTER TABLE `ChargePerPlan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ChargePlan`
--

DROP TABLE IF EXISTS `ChargePlan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ChargePlan` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ManageID` int(11) NOT NULL DEFAULT '0' COMMENT 'å„çº§ç®¡ç†å‘˜çš„ID',
  `EntrFee` int(11) NOT NULL DEFAULT '0' COMMENT 'æ¿€æ´»è´¹ç”¨',
  `MonthlyFee` int(11) NOT NULL DEFAULT '0' COMMENT 'æ”¶è´¹è®¡åˆ’',
  `AppsNumber` int(11) NOT NULL DEFAULT '0',
  `AddAppsFee` int(11) NOT NULL DEFAULT '0' COMMENT 'é¢å¤–appæ¯ä¸ªçš„è´¹ç”¨',
  PRIMARY KEY (`ID`),
  KEY `MngAccount` (`ManageID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='æ”¶è´¹è®¡åˆ’';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ChargePlan`
--

LOCK TABLES `ChargePlan` WRITE;
/*!40000 ALTER TABLE `ChargePlan` DISABLE KEYS */;
INSERT INTO `ChargePlan` VALUES (1,1,800,400,4,400);
/*!40000 ALTER TABLE `ChargePlan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CommPerPrivateKey`
--

DROP TABLE IF EXISTS `CommPerPrivateKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CommPerPrivateKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT 'ç”¨æˆ·è´¦å·,åŒ…æ‹¬ä¸»ä»Ž',
  `Code` char(20) DEFAULT '',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT '0',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Special` tinyint(1) unsigned DEFAULT '0' COMMENT 'æ˜¯å¦æ˜¯åˆ›å»ºè´¦å·æ—¶å€™åˆ›å»ºçš„key',
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Code` (`CommunityID`,`Code`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CommPerPrivateKey`
--

LOCK TABLES `CommPerPrivateKey` WRITE;
/*!40000 ALTER TABLE `CommPerPrivateKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `CommPerPrivateKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CommPerRfKey`
--

DROP TABLE IF EXISTS `CommPerRfKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CommPerRfKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT 'ç”¨æˆ·è´¦å·,åŒ…æ‹¬ä¸»ä»Ž',
  `Code` char(20) DEFAULT '',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT '0',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Code` (`CommunityID`,`Code`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CommPerRfKey`
--

LOCK TABLES `CommPerRfKey` WRITE;
/*!40000 ALTER TABLE `CommPerRfKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `CommPerRfKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CommunityInfo`
--

DROP TABLE IF EXISTS `CommunityInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CommunityInfo` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(10) unsigned NOT NULL,
  `Street` varchar(128) NOT NULL DEFAULT '',
  `City` varchar(128) NOT NULL DEFAULT '',
  `PostalCode` varchar(32) NOT NULL DEFAULT '',
  `Country` varchar(128) NOT NULL DEFAULT '',
  `States` varchar(128) NOT NULL DEFAULT '',
  `EnableMotion` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'é—¨å£æœºæ˜¯å¦å¼€å¯motion',
  `MotionTime` tinyint(1) NOT NULL DEFAULT '10' COMMENT 'é—¨å£æœºç›‘æµ‹å¤šä¹…æ²¡æœ‰åŠ¨ä½œä¸ŠæŠ¥æˆªå›¾æ—¶é—´',
  `AptPinType` tinyint(1) DEFAULT '0' COMMENT '0ä¸ºPIN 1ä¸ºAPT+PIN',
  `FaceEnrollment` tinyint(1) NOT NULL DEFAULT '1',
  `IDCardVerification` tinyint(1) NOT NULL DEFAULT '1',
  `NumberOfApt` int(11) unsigned NOT NULL DEFAULT '20',
  `LastDevOfflineNotifyTime` datetime DEFAULT '1970-01-01 08:00:00' COMMENT 'ä¸Šæ¬¡é€šçŸ¥æ—¶é—´',
  `Switch` tinyint(1) DEFAULT '5' COMMENT 'æŒ‰ä½å¼€å…³æ ‡ç¤ºç¬¦:1=è½åœ°å¼€å…³,2=æŽ‰çº¿é€šçŸ¥å¼€å‘,3=æ˜¯å¦å…è®¸ç”¨æˆ·ä½¿ç”¨PIN,4=SIMå¡è¶…æµé‡å¼€å…³,5=æ˜¯å¦å¯ç”¨æ™ºèƒ½å®¶å±…',
  `IsNew` tinyint(4) DEFAULT '0' COMMENT 'V6.1æ–°æ—§ç¤¾åŒºæ ‡è¯† 0æ—§ç¤¾åŒº',
  `MobileNumber` char(24) NOT NULL DEFAULT '' COMMENT 'æ‰‹æœºå·',
  `PhoneCode` char(8) NOT NULL DEFAULT '' COMMENT 'åŒºå·',
  `FeatureExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT 'é«˜çº§åŠŸèƒ½è¿‡æœŸæ—¶é—´',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `AccountID` (`AccountID`),
  CONSTRAINT `CommunityInfo_ibfk_1` FOREIGN KEY (`AccountID`) REFERENCES `Account` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CommunityInfo`
--

LOCK TABLES `CommunityInfo` WRITE;
/*!40000 ALTER TABLE `CommunityInfo` DISABLE KEYS */;
INSERT INTO `CommunityInfo` VALUES (1,5,'si ming','xiamen','000000','1','fujian',0,10,0,1,1,20,'1970-01-01 08:00:00',5,1,'','','2299-12-31 23:59:59');
/*!40000 ALTER TABLE `CommunityInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CommunityRoom`
--

DROP TABLE IF EXISTS `CommunityRoom`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CommunityRoom` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `UnitID` int(11) DEFAULT NULL,
  `RoomName` varchar(128) DEFAULT '' COMMENT 'æˆ¿é—´åç§°,æœ€é•¿128ä¸ªå­—èŠ‚',
  `OldDataUnique` tinyint(1) unsigned DEFAULT '0' COMMENT 'ç”¨äºŽæ—§æ•°æ®å·²ç»å†²çªçš„å”¯ä¸€æ ‡è¯†',
  PRIMARY KEY (`ID`),
  KEY `UnitID_key` (`UnitID`),
  CONSTRAINT `CommunityRoom_ibfk_1` FOREIGN KEY (`UnitID`) REFERENCES `CommunityUnit` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CommunityRoom`
--

LOCK TABLES `CommunityRoom` WRITE;
/*!40000 ALTER TABLE `CommunityRoom` DISABLE KEYS */;
INSERT INTO `CommunityRoom` VALUES (1,1,'100',0);
INSERT INTO `CommunityRoom` VALUES (2,1,'101',0);
INSERT INTO `CommunityRoom` VALUES (3,2,'102',0);
INSERT INTO `CommunityRoom` VALUES (4,2,'103',0);
/*!40000 ALTER TABLE `CommunityRoom` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CommunityUnit`
--

DROP TABLE IF EXISTS `CommunityUnit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CommunityUnit` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'æ‰€å±žé‚£ä¸ªç¤¾åŒºID,è§Accountè¡¨',
  `UnitName` varchar(128) DEFAULT '' COMMENT 'unitåç§°,æœ€é•¿128ä¸ªå­—èŠ‚',
  PRIMARY KEY (`ID`),
  KEY `MngAccountID_key` (`MngAccountID`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CommunityUnit`
--

LOCK TABLES `CommunityUnit` WRITE;
/*!40000 ALTER TABLE `CommunityUnit` DISABLE KEYS */;
INSERT INTO `CommunityUnit` VALUES (1,5,'B1');
INSERT INTO `CommunityUnit` VALUES (2,5,'B2');
/*!40000 ALTER TABLE `CommunityUnit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CustomerService`
--

DROP TABLE IF EXISTS `CustomerService`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CustomerService` (
  `MngAccount` char(64) NOT NULL COMMENT 'åŒºåŸŸç®¡ç†å‘˜è´¦å·',
  `Phone` char(16) DEFAULT '',
  `Email` char(32) DEFAULT '',
  PRIMARY KEY (`MngAccount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CustomerService`
--

LOCK TABLES `CustomerService` WRITE;
/*!40000 ALTER TABLE `CustomerService` DISABLE KEYS */;
INSERT INTO `CustomerService` VALUES ('superManage','','');
INSERT INTO `CustomerService` VALUES ('unittest_dis','1111','<EMAIL>');
/*!40000 ALTER TABLE `CustomerService` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Delivery`
--

DROP TABLE IF EXISTS `Delivery`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Delivery` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` char(64) NOT NULL,
  `CommunityID` int(10) unsigned NOT NULL,
  `PinCode` char(20) DEFAULT NULL,
  `CardCode` char(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Version` int(10) unsigned DEFAULT '123456' COMMENT 'ç”¨æˆ·æ•°æ®ç‰ˆæœ¬å·',
  PRIMARY KEY (`ID`),
  KEY `Comm_Pin` (`CommunityID`,`PinCode`),
  KEY `Comm_Rf` (`CommunityID`,`CardCode`),
  KEY `Name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Delivery`
--

LOCK TABLES `Delivery` WRITE;
/*!40000 ALTER TABLE `Delivery` DISABLE KEYS */;
/*!40000 ALTER TABLE `Delivery` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DeliveryAccess`
--

DROP TABLE IF EXISTS `DeliveryAccess`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DeliveryAccess` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `DeliveryID` int(10) unsigned NOT NULL,
  `AccessGroupID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `DeliveryID` (`DeliveryID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DeliveryAccess`
--

LOCK TABLES `DeliveryAccess` WRITE;
/*!40000 ALTER TABLE `DeliveryAccess` DISABLE KEYS */;
/*!40000 ALTER TABLE `DeliveryAccess` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevDeliveryMsg`
--

DROP TABLE IF EXISTS `DevDeliveryMsg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevDeliveryMsg` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT 'ç”¨æˆ·è´¦å·',
  `DeliveryNumber` int(10) unsigned NOT NULL COMMENT 'å¿«é€’çš„ä¸ªæ•°',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '0æœªè¯» 1å·²è¯»',
  PRIMARY KEY (`ID`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevDeliveryMsg`
--

LOCK TABLES `DevDeliveryMsg` WRITE;
/*!40000 ALTER TABLE `DevDeliveryMsg` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevDeliveryMsg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevLoginLog`
--

DROP TABLE IF EXISTS `DevLoginLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevLoginLog` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Mac` char(32) NOT NULL,
  `Node` char(32) NOT NULL,
  `LoginTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevLoginLog`
--

LOCK TABLES `DevLoginLog` WRITE;
/*!40000 ALTER TABLE `DevLoginLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevLoginLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevOfflineLog`
--

DROP TABLE IF EXISTS `DevOfflineLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevOfflineLog` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºID',
  `Quantity` int(10) NOT NULL DEFAULT '0' COMMENT 'æŽ‰çº¿è®¾å¤‡æ•°é‡',
  `MacList` varchar(256) NOT NULL DEFAULT '' COMMENT 'æŽ‰çº¿è®¾å¤‡åˆ—è¡¨',
  `OfflineTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevOfflineLog`
--

LOCK TABLES `DevOfflineLog` WRITE;
/*!40000 ALTER TABLE `DevOfflineLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevOfflineLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevRtspLog`
--

DROP TABLE IF EXISTS `DevRtspLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevRtspLog` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Mac` char(32) NOT NULL,
  `Node` char(32) NOT NULL,
  `RtspTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevRtspLog`
--

LOCK TABLES `DevRtspLog` WRITE;
/*!40000 ALTER TABLE `DevRtspLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevRtspLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevUpdateUserLog`
--

DROP TABLE IF EXISTS `DevUpdateUserLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevUpdateUserLog` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(12) NOT NULL COMMENT 'è®¾å¤‡çš„mac',
  `Accounts` varchar(4096) NOT NULL COMMENT 'ä¸‹è½½ç”¨æˆ·åˆ—è¡¨ Då¼€å¤´æ˜¯å¿«é€’äººå‘˜çš„æ•°æ®åº“id, Så¼€å¤´æ˜¯ç‰©ä¸š',
  `TraceID` bigint(10) unsigned NOT NULL DEFAULT '0',
  `FilePath` varchar(256) DEFAULT '' COMMENT 'æ–‡ä»¶å­˜å‚¨è·¯å¾„',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `MAC_TRACE` (`MAC`,`TraceID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevUpdateUserLog`
--

LOCK TABLES `DevUpdateUserLog` WRITE;
/*!40000 ALTER TABLE `DevUpdateUserLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevUpdateUserLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DeviceCode`
--

DROP TABLE IF EXISTS `DeviceCode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DeviceCode` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(16) NOT NULL DEFAULT '' COMMENT 'è®¾å¤‡Mac',
  `HwCode` char(8) NOT NULL DEFAULT '' COMMENT 'å›ºä»¶ç ',
  `Code` char(16) NOT NULL DEFAULT '' COMMENT 'éšæœº10ä½çš„æ•°å­—ï¼Œå…¨è¡¨å”¯ä¸€',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Code` (`Code`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DeviceCode`
--

LOCK TABLES `DeviceCode` WRITE;
/*!40000 ALTER TABLE `DeviceCode` DISABLE KEYS */;
/*!40000 ALTER TABLE `DeviceCode` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DeviceForRegister`
--

DROP TABLE IF EXISTS `DeviceForRegister`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DeviceForRegister` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngID` int(11) NOT NULL COMMENT 'åŒºåŸŸç®¡ç†å‘˜ID',
  `MAC` char(12) NOT NULL,
  `Owner` varchar(128) DEFAULT '',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `PerMngID` int(11) NOT NULL DEFAULT '0' COMMENT 'ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜id',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `MAC` (`MAC`),
  KEY `MngID` (`MngID`),
  KEY `PerMngID` (`PerMngID`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DeviceForRegister`
--

LOCK TABLES `DeviceForRegister` WRITE;
/*!40000 ALTER TABLE `DeviceForRegister` DISABLE KEYS */;
INSERT INTO `DeviceForRegister` VALUES (1,2,'CE**********','Public Device','2022-02-15 03:18:49',5);
INSERT INTO `DeviceForRegister` VALUES (2,2,'CE0000000001','Public Device','2022-02-15 03:18:54',5);
INSERT INTO `DeviceForRegister` VALUES (3,2,'CE0000000002','Public Device','2022-02-15 03:18:58',5);
INSERT INTO `DeviceForRegister` VALUES (4,2,'CE0000000003','Public Device','2022-02-15 03:19:02',5);
INSERT INTO `DeviceForRegister` VALUES (5,2,'CE0000000004','Public Device','2022-02-15 03:19:06',5);
INSERT INTO `DeviceForRegister` VALUES (6,2,'CE0000000005','Public Device','2022-02-15 03:19:10',5);
INSERT INTO `DeviceForRegister` VALUES (7,2,'CE0000000006','Public Device','2022-02-15 03:19:15',5);
INSERT INTO `DeviceForRegister` VALUES (8,2,'CE0000000007','Public Device','2022-02-15 03:19:19',5);
INSERT INTO `DeviceForRegister` VALUES (9,2,'CE0000000008','B1-101','2022-02-15 03:19:23',5);
INSERT INTO `DeviceForRegister` VALUES (10,2,'CE0000000009','B1-101','2022-02-15 03:19:27',5);
INSERT INTO `DeviceForRegister` VALUES (11,2,'CE000000000A','B2-102','2022-02-15 03:19:32',5);
INSERT INTO `DeviceForRegister` VALUES (12,2,'CE000000000B','','2022-02-15 03:19:36',0);
INSERT INTO `DeviceForRegister` VALUES (13,2,'CE000000000C','','2022-02-15 03:19:40',0);
INSERT INTO `DeviceForRegister` VALUES (14,2,'CE000000000D','','2022-02-15 03:19:44',0);
INSERT INTO `DeviceForRegister` VALUES (15,2,'CE000000000E','','2022-02-15 03:19:48',0);
INSERT INTO `DeviceForRegister` VALUES (16,2,'CE000000000F','','2022-02-15 03:19:53',0);
/*!40000 ALTER TABLE `DeviceForRegister` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DeviceUpgrade`
--

DROP TABLE IF EXISTS `DeviceUpgrade`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DeviceUpgrade` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Model` char(64) NOT NULL,
  `ModelID` char(32) NOT NULL,
  `FirmwareVersion` char(64) DEFAULT '',
  `FirmwareFile` char(64) DEFAULT '',
  `UpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DeviceUpgrade`
--

LOCK TABLES `DeviceUpgrade` WRITE;
/*!40000 ALTER TABLE `DeviceUpgrade` DISABLE KEYS */;
/*!40000 ALTER TABLE `DeviceUpgrade` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Devices`
--

DROP TABLE IF EXISTS `Devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Devices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `UnitID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¨å±€çš„å•å…ƒunit ID',
  `Node` char(16) DEFAULT '' COMMENT 'å®¶åº­ä¸»è´¦å·uid',
  `MAC` char(32) DEFAULT '',
  `Type` int(11) NOT NULL COMMENT 'è®¾å¤‡çš„æœºåž‹,å®¤å†…æœºã€å®¤å¤–æœº...',
  `Grade` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è®¾å¤‡çš„å½’å±žç­‰çº§,1=ç¤¾åŒºå…±äº«,2=å•å…ƒå…±äº«,3=å®¶åº­ç‹¬å ...',
  `IPAddress` char(40) DEFAULT '',
  `Gateway` char(40) DEFAULT '',
  `SubnetMask` char(40) DEFAULT '',
  `PrimaryDNS` char(40) DEFAULT '',
  `SecondaryDNS` char(40) DEFAULT '',
  `Firmware` char(32) DEFAULT '',
  `Hardware` char(32) DEFAULT '',
  `Status` tinyint(1) DEFAULT '0',
  `outerIP` varchar(40) DEFAULT '',
  `Port` int(10) unsigned zerofill DEFAULT '**********',
  `LastConnection` datetime DEFAULT NULL,
  `PrivatekeyMD5` char(32) DEFAULT '',
  `RfidMD5` char(32) DEFAULT '',
  `ConfigSettings` varchar(2048) DEFAULT '',
  `ConfigMD5` char(32) DEFAULT '',
  `ContactMD5` char(33) NOT NULL DEFAULT '',
  `SipAccount` char(64) DEFAULT '',
  `SipPwd` char(16) NOT NULL DEFAULT '',
  `RtspPwd` char(24) DEFAULT '' COMMENT 'å®¤å¤–æœºrtspç›‘æŽ§å¯†ç ',
  `Location` char(64) DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT 'å…¬å…±è®¾å¤‡é»˜è®¤æ—¶é—´',
  `DclientVer` int(10) NOT NULL DEFAULT '0' COMMENT 'Dclientç‰ˆæœ¬å·ï¼Œç”¨æ¥åŒºåˆ†å®¢æˆ·ç«¯ç‰ˆæœ¬',
  `NetGroupNumber` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è®¾å¤‡çš„ç½‘ç»œç»„å·',
  `StairShow` tinyint(1) DEFAULT '0' COMMENT 'å…¬å…±è®¾å¤‡æ˜¾ç¤ºæ ¼å¼: 0:default 1:roomnum  2:app/devæ˜¾ç¤ºæŽ§åˆ¶',
  `AuthCode` char(20) DEFAULT '' COMMENT 'è®¾å¤‡æ ¡éªŒç ',
  `Relay` varchar(256) NOT NULL DEFAULT '#,Relay1,1,1,1' COMMENT 'æ ¼å¼: å¼€é—¨æŒ‰é”®,doorname,æ˜¯å¦æ˜¾ç¤ºåœ¨home,æ˜¯å¦æ˜¾ç¤ºåœ¨talking,æ˜¯å¦å¯ç”¨;å¤šä¸ªrelayä»¥é€—å·åˆ†éš”',
  `Config` varchar(2048) NOT NULL DEFAULT '' COMMENT 'æ‰‹åŠ¨æ–°å¢žçš„é…ç½®',
  `Arming` tinyint(1) DEFAULT '0' COMMENT 'å®¤å†…æœºarmingçŠ¶æ€',
  `SipType` tinyint(1) DEFAULT '1' COMMENT '0=udp  1=tcp 2=tls',
  `AccSrvID` char(24) NOT NULL DEFAULT '' COMMENT 'æŽ¥å…¥æœåŠ¡å™¨idçš„æ ‡ç¤º',
  `Flags` int(11) DEFAULT '8' COMMENT 'æŒ‰ä½æ ‡è¯† 1=home;2=away;3=sleep;4=ç®¡ç†æœºæ˜¯å¦å¼€å¯å…¨é€‰,é»˜è®¤å¼€å¯;5-8ä½ä»£è¡¨è®¾å¤‡relayçš„å¼€å…³æƒ…å†µ 0å…³1å¼€;9=å®¤å†…æœºä¸Šçº¿æ ‡è¯†;10=å®¤å†…æœºæ‰€å±žçš„å®¶åº­æ˜¯å¦ä¸ºKitæ–¹æ¡ˆ',
  `ArmingFunction` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'smartplusæ˜¾ç¤ºarmingçš„å…¥å£',
  `LastDisConn` datetime DEFAULT NULL COMMENT 'ä¸Šæ¬¡è®¾å¤‡æ–­çº¿æ—¶é—´',
  `FaceMD5` char(32) DEFAULT '',
  `UserMetaMD5` char(32) DEFAULT '',
  `ScheduleMD5` char(32) DEFAULT '',
  `ProjectType` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=ç¤¾åŒºï¼Œ1=åŠžå…¬',
  `SecurityRelay` varchar(256) NOT NULL DEFAULT '' COMMENT ': ,doorname,home,talking,;relay',
  PRIMARY KEY (`ID`),
  KEY `MAC` (`MAC`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `UnitID` (`UnitID`),
  KEY `SipAccount` (`SipAccount`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Devices`
--

LOCK TABLES `Devices` WRITE;
/*!40000 ALTER TABLE `Devices` DISABLE KEYS */;
INSERT INTO `Devices` VALUES (1,5,0,'','CE**********',0,1,'','','','','','','',0,'',**********,NULL,'','','','','','**********','jtLG5kriwudj','162449Q0p31EZ441','Pub1','2022-02-15 05:37:21','2299-12-31 23:59:59',0,0,1,'','#,门1,1,1,1;0,门2,1,1,1','',0,1,'',8,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (2,5,0,'','CE0000000001',3,1,'','','','','','','',0,'',**********,NULL,'','','','','','**********','QeeiP72cmhcf','ok1643490ww353w4','Manage1','2022-02-15 05:38:54','2299-12-31 23:59:59',0,0,1,'','','',0,1,'',8,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (3,5,1,'','CE0000000002',0,2,'','','','','','','',0,'',**********,NULL,'','','','','','**********','Jf9qurblggpq','p16e44Hjn9y80095','PubUnit0','2022-02-16 02:54:55','2299-12-31 23:59:59',0,0,1,'','#,门1,1,1,1;0,门2,1,1,1','',0,1,'',0,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (4,5,1,'','CE0000000003',0,2,'','','','','','','',0,'',**********,NULL,'','','','','','6300100007','drHucz9jZisH','D716O449T5n80113','PubUnit1','2022-02-16 02:55:13','2299-12-31 23:59:59',0,1,1,'','#,门1,1,1,1;0,门2,1,1,1','',0,1,'',0,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (5,5,1,'','CE0000000004',3,2,'','','','','','','',0,'',**********,NULL,'','','','','','6300100008','kKd527598959','16a4k498u0v1c8R4','UnitManage0','2022-02-16 02:56:24','2299-12-31 23:59:59',0,0,1,'','','',0,1,'',0,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (6,5,2,'','CE0000000005',0,2,'','','','','','','',0,'',**********,NULL,'','','','','','6300100009','eZk49kuivjtz','W16644980J23LEy9','PubUnit2','2022-02-16 02:57:19','2299-12-31 23:59:59',0,0,1,'','#,门1,1,1,1;0,门2,1,1,1','',0,1,'',0,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (7,5,2,'','CE0000000006',0,2,'','','','','','','',0,'',**********,NULL,'','','','','','6300100010','zefqavgTaW76','jS1O64498072J5f9','PubUnit3','2022-02-16 02:57:39','2299-12-31 23:59:59',0,0,1,'','#,门1,1,1,1;0,门2,1,1,1','',0,1,'',0,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (8,5,2,'','CE0000000007',3,2,'','','','','','','',0,'',**********,NULL,'','','','','','6300100011','wmJq4J040566','2i1644GA98VY0323','UnitManage1','2022-02-16 02:58:43','2299-12-31 23:59:59',0,0,1,'','','',0,1,'',0,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (9,5,1,'**********','CE0000000008',2,3,'','','','','','','',0,'',**********,NULL,'','','','','','6300100013','piuH9FnJNUGW','AU16Ug44Uy993850','Monitor','2022-02-16 06:44:10','2299-12-31 23:59:59',0,1,0,'','#,门1,1,1,1;0,门2,1,1,0','',0,1,'',8,1,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (10,5,1,'**********','CE0000000009',0,3,'','','','','','','',0,'',**********,NULL,'','','','','','6300100014','wxW2r4628317','c1cf644N99e38W94','FamilyDoor1','2022-02-16 06:44:54','2299-12-31 23:59:59',0,1,0,'','#,门1,1,1,1;0,门2,1,1,1','',0,1,'',8,0,NULL,'','','',0,'');
INSERT INTO `Devices` VALUES (11,5,2,'**********','CE000000000A',2,3,'','','','','','','',0,'',**********,NULL,'','','','','','**********','fN7tmzlfggwf','gk1644s9YR943100','Monitor2','2022-02-16 06:48:20','2299-12-31 23:59:59',0,0,0,'','#,门1,1,1,1;0,门2,1,1,0','',0,1,'',8,1,NULL,'','','',0,'');
/*!40000 ALTER TABLE `Devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevicesShadow`
--

DROP TABLE IF EXISTS `DevicesShadow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevicesShadow` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MAC` char(20) NOT NULL DEFAULT '' COMMENT 'mac',
  `PrivatekeyPath` varchar(128) NOT NULL DEFAULT '',
  `RfidPath` varchar(128) NOT NULL DEFAULT '',
  `ConfigPath` varchar(128) NOT NULL DEFAULT '',
  `ContactPath` varchar(128) NOT NULL DEFAULT '',
  `FacePath` varchar(128) NOT NULL DEFAULT '',
  `SchedulePath` varchar(128) NOT NULL DEFAULT '',
  `UserMetaPath` varchar(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevicesShadow`
--

LOCK TABLES `DevicesShadow` WRITE;
/*!40000 ALTER TABLE `DevicesShadow` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevicesShadow` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DevicesSpecial`
--

DROP TABLE IF EXISTS `DevicesSpecial`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DevicesSpecial` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` varchar(32) NOT NULL COMMENT 'ç¤¾åŒºå’Œä¸ªäººä¸»è´¦å·',
  `MAC` char(12) NOT NULL COMMENT 'Macåœ°å€',
  PRIMARY KEY (`ID`),
  KEY `Account` (`Account`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DevicesSpecial`
--

LOCK TABLES `DevicesSpecial` WRITE;
/*!40000 ALTER TABLE `DevicesSpecial` DISABLE KEYS */;
/*!40000 ALTER TABLE `DevicesSpecial` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DistributorInfo`
--

DROP TABLE IF EXISTS `DistributorInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DistributorInfo` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL,
  `IsEncryptPin` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'pin æ˜¯å¦åŠ å¯†,0:ä¸åŠ å¯†, 1:åŠ å¯†',
  `IsVillaMonitor` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'Inså•ä½æˆ·æ˜¯å¦æ˜¾ç¤ºç»‘å®šMonitoræ¨¡å— 0ï¼šoff, 1:on',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DistributorInfo`
--

LOCK TABLES `DistributorInfo` WRITE;
/*!40000 ALTER TABLE `DistributorInfo` DISABLE KEYS */;
INSERT INTO `DistributorInfo` VALUES (1,'unittest_dis',0,0);
/*!40000 ALTER TABLE `DistributorInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DtPbxServer`
--

DROP TABLE IF EXISTS `DtPbxServer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DtPbxServer` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Distributor` char(64) NOT NULL DEFAULT '' COMMENT 'åŒºåŸŸç®¡ç†å‘˜è´¦å·',
  `PbxIp` char(20) NOT NULL DEFAULT '',
  `PbxIpv6` char(40) NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `Distributor_key` (`Distributor`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DtPbxServer`
--

LOCK TABLES `DtPbxServer` WRITE;
/*!40000 ALTER TABLE `DtPbxServer` DISABLE KEYS */;
/*!40000 ALTER TABLE `DtPbxServer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `EmailCheckCode`
--

DROP TABLE IF EXISTS `EmailCheckCode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `EmailCheckCode` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Email` char(64) NOT NULL DEFAULT '',
  `IP` char(64) NOT NULL DEFAULT '' COMMENT 'å“ªä¸ªIPåœ°å€è¦æ±‚å‘é€éªŒè¯ç ',
  `CheckCode` char(16) NOT NULL DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Email` (`Email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `EmailCheckCode`
--

LOCK TABLES `EmailCheckCode` WRITE;
/*!40000 ALTER TABLE `EmailCheckCode` DISABLE KEYS */;
/*!40000 ALTER TABLE `EmailCheckCode` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ErrorConnect`
--

DROP TABLE IF EXISTS `ErrorConnect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ErrorConnect` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `IP` varchar(64) NOT NULL,
  `ConnectTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `ErrorType` tinyint(1) DEFAULT '0' COMMENT 'é”™è¯¯ç±»åž‹ 1=æœªæŒ‰æ—¶ä¸ŠæŠ¥çŠ¶æ€, 2=é”™è¯¯MACæ ¡éªŒç , 3=æœªç™»è®°çš„è®¾å¤‡',
  `MAC` char(20) DEFAULT '',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ErrorConnect`
--

LOCK TABLES `ErrorConnect` WRITE;
/*!40000 ALTER TABLE `ErrorConnect` DISABLE KEYS */;
/*!40000 ALTER TABLE `ErrorConnect` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FaceMng`
--

DROP TABLE IF EXISTS `FaceMng`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `FaceMng` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'å¯¹äºŽç¤¾åŒºç”¨æˆ·æ˜¯ç¤¾åŒºç®¡ç†å‘˜IDæˆ–è€…å¯¹äºŽä¸ªäººç”¨æˆ·æ˜¯installerID',
  `UnitID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'å…¨å±€çš„å•å…ƒunit ID, å•ä½æˆ·ä¸ºç©º',
  `Node` char(16) DEFAULT '' COMMENT 'å®¶åº­ä¸»è´¦å·uid',
  `PersonalAccountID` int(10) unsigned NOT NULL COMMENT 'PersonalAccountè¡¨ID',
  `FileName` varchar(128) DEFAULT '' COMMENT 'åŽŸå§‹æ–‡ä»¶å',
  `FaceUrl` char(64) DEFAULT '' COMMENT 'æ–‡ä»¶è·¯å¾„åŽç¼€,æ–‡ä»¶åä¸ºåŠ å¯†åŽçš„md5å€¼',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'åˆ›å»ºæ—¶é—´',
  `FaceMD5` char(32) NOT NULL DEFAULT '' COMMENT 'md5',
  PRIMARY KEY (`ID`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `UnitID` (`UnitID`),
  KEY `Node` (`Node`),
  KEY `PersonalAccountID` (`PersonalAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FaceMng`
--

LOCK TABLES `FaceMng` WRITE;
/*!40000 ALTER TABLE `FaceMng` DISABLE KEYS */;
/*!40000 ALTER TABLE `FaceMng` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FaceModel`
--

DROP TABLE IF EXISTS `FaceModel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `FaceModel` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` varchar(16) NOT NULL DEFAULT '',
  `ModelName` varchar(256) NOT NULL COMMENT 'æ¨¡åž‹çš„åå­—,ç”±è®¾å¤‡ç«¯å†³å®š',
  `ModelUrl` varchar(128) DEFAULT '' COMMENT 'æ¨¡åž‹çš„å®Œæ•´url',
  `UploadTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `MAC_ModelName` (`MAC`,`ModelName`(255)),
  KEY `UploadTime` (`UploadTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FaceModel`
--

LOCK TABLES `FaceModel` WRITE;
/*!40000 ALTER TABLE `FaceModel` DISABLE KEYS */;
/*!40000 ALTER TABLE `FaceModel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FeaturePlan`
--

DROP TABLE IF EXISTS `FeaturePlan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `FeaturePlan` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(256) NOT NULL,
  `Item` smallint(8) DEFAULT NULL COMMENT 'ç»‘å®šçš„é«˜çº§åŠŸèƒ½,æŒ‰ä½æ ‡è¯†:1=å¿«é€’ä»¶,2=pinæ˜¯å¦å¯ç”¨,3=QrCodeæ˜¯å¦å¯ç”¨,4=é™åˆ¶å®¶åº­æˆå‘˜,5=å®¤å†…æœºæ–¹æ¡ˆ',
  `Type` tinyint(1) NOT NULL COMMENT 'Featureç±»åž‹ 0ï¼šç¤¾åŒº 1ï¼šåŠžå…¬',
  PRIMARY KEY (`ID`),
  KEY `Name` (`Name`(255))
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FeaturePlan`
--

LOCK TABLES `FeaturePlan` WRITE;
/*!40000 ALTER TABLE `FeaturePlan` DISABLE KEYS */;
INSERT INTO `FeaturePlan` VALUES (1,'Basic',16,0);
INSERT INTO `FeaturePlan` VALUES (2,'Premium',15,0);
INSERT INTO `FeaturePlan` VALUES (3,'Basic',0,1);
INSERT INTO `FeaturePlan` VALUES (4,'Premium',7,1);
/*!40000 ALTER TABLE `FeaturePlan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ImportProjectTask`
--

DROP TABLE IF EXISTS `ImportProjectTask`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ImportProjectTask` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UUID` char(36) NOT NULL COMMENT 'uuid()åŽ»é™¤æ¨ªæ ,ä¾‹å¦‚45efccef469111ec8fe900163e047e78',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `BeginTime` datetime DEFAULT NULL,
  `UpdateUser` char(32) NOT NULL DEFAULT '',
  `AccountUUID` char(36) NOT NULL COMMENT 'Accountè¡¨UUID',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=æœªå¤„ç†;1=å·²å®Œæˆ;2=è¶…æ—¶ï¼Œ3=å¤„ç†ä¸­,4=å¤±è´¥',
  `Type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=ä½å®…;1=åŠžå…¬',
  `IsDeal` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'ç”¨æˆ·æ˜¯å¦å¤„ç†ï¼Œå¤„ç†åŽä¸å†æ˜¾ç¤º',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UUID` (`UUID`),
  KEY `CreateTime` (`CreateTime`),
  KEY `BeginTime` (`BeginTime`),
  KEY `AccountUUID` (`AccountUUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ImportProjectTask`
--

LOCK TABLES `ImportProjectTask` WRITE;
/*!40000 ALTER TABLE `ImportProjectTask` DISABLE KEYS */;
/*!40000 ALTER TABLE `ImportProjectTask` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ImportProjectTaskDetail`
--

DROP TABLE IF EXISTS `ImportProjectTaskDetail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ImportProjectTaskDetail` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UUID` char(36) NOT NULL COMMENT 'uuid()åŽ»é™¤æ¨ªæ ,ä¾‹å¦‚45efccef469111ec8fe900163e047e78',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `UpdateUser` char(32) NOT NULL DEFAULT '',
  `ImportTaskUUID` char(36) NOT NULL,
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=æœªå¤„ç†;1=æˆåŠŸ;2=è¶…æ—¶;3=å¤±è´¥',
  `Item` text NOT NULL COMMENT 'å¯¼å…¥ä¿¡æ¯çš„jsonæ•°æ®',
  `FailureInfo` varchar(2048) NOT NULL DEFAULT '' COMMENT 'å¤±è´¥åŽŸå› ï¼Œåšå†…éƒ¨æŽ’æŸ¥ç”¨',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UUID` (`UUID`),
  KEY `ImportTaskUUID` (`ImportTaskUUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ImportProjectTaskDetail`
--

LOCK TABLES `ImportProjectTaskDetail` WRITE;
/*!40000 ALTER TABLE `ImportProjectTaskDetail` DISABLE KEYS */;
/*!40000 ALTER TABLE `ImportProjectTaskDetail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ImportTask`
--

DROP TABLE IF EXISTS `ImportTask`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ImportTask` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UUID` char(36) NOT NULL COMMENT 'ä¸¤ä½æœåŠ¡å™¨å· + -uuid()åŽ»é™¤æ¨ªæ ,ä¾‹å¦‚na-45efccef469111ec8fe900163e047e78',
  `CreateTime` datetime NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `UpdateUser` char(8) NOT NULL DEFAULT '',
  `Account` char(64) NOT NULL COMMENT 'Accountè¡¨Account',
  `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0=è¿›è¡Œä¸­;1=å·²å®Œæˆ;2=å·¡æ£€ä»»åŠ¡ä¿®æ”¹çš„è¶…æ—¶ä»»åŠ¡',
  `Type` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'ç±»åž‹:0=å¯¼å…¥å°åŒº',
  PRIMARY KEY (`ID`),
  KEY `Account_Type` (`Account`,`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ImportTask`
--

LOCK TABLES `ImportTask` WRITE;
/*!40000 ALTER TABLE `ImportTask` DISABLE KEYS */;
/*!40000 ALTER TABLE `ImportTask` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `InstallerBillingInfo`
--

DROP TABLE IF EXISTS `InstallerBillingInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `InstallerBillingInfo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL DEFAULT '' COMMENT 'ç®¡ç†å‘˜è´¦å·åç§°',
  `BillingTitle` char(128) NOT NULL COMMENT 'å…¬å¸åå­—æˆ–å®¶åº­åå­—',
  `Contactor` char(128) NOT NULL DEFAULT '',
  `Street` char(128) NOT NULL DEFAULT '' COMMENT 'è¡—é“',
  `City` char(64) NOT NULL DEFAULT '' COMMENT 'åŸŽå¸‚',
  `Postcode` char(32) DEFAULT '' COMMENT 'é‚®ç¼–',
  `Country` varchar(8) DEFAULT 'USA',
  `TelePhone` char(32) DEFAULT '',
  `Fax` char(32) DEFAULT '' COMMENT 'ä¼ çœŸ',
  `Email` varchar(64) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `InstallerBillingInfo`
--

LOCK TABLES `InstallerBillingInfo` WRITE;
/*!40000 ALTER TABLE `InstallerBillingInfo` DISABLE KEYS */;
INSERT INTO `InstallerBillingInfo` VALUES (1,'unittest_dis-PersonalManage','','','','','','USA','','','');
INSERT INTO `InstallerBillingInfo` VALUES (2,'unittest_dis','','','','','','USA','','','');
INSERT INTO `InstallerBillingInfo` VALUES (3,'unittest_ins','','','','','','USA','','','');
/*!40000 ALTER TABLE `InstallerBillingInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `LocalSipTransaction`
--

DROP TABLE IF EXISTS `LocalSipTransaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `LocalSipTransaction` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Sip` char(16) DEFAULT '' COMMENT 'æ“ä½œå“ªä¸ªsipï¼Œç”¨äºŽåŽé¢æ¶ˆè´¹å¹¶å‘å¤„ç†',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `MessageStatus` tinyint(4) DEFAULT '0' COMMENT '0æœªå¤„ç† 1æˆåŠŸå¤„ç† 2é”™è¯¯æ¶ˆæ¯éœ€äººå·¥ä»‹å…¥',
  `Message` varchar(1024) DEFAULT '',
  `HandleTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `LocalSipTransaction`
--

LOCK TABLES `LocalSipTransaction` WRITE;
/*!40000 ALTER TABLE `LocalSipTransaction` DISABLE KEYS */;
INSERT INTO `LocalSipTransaction` VALUES (1,'**********','2022-02-15 05:37:21',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"jtLG5kriwudj\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"*******.0\",\"type\":\"0\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (2,'**********','2022-02-15 05:38:54',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"QeeiP72cmhcf\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"*******.0\",\"type\":\"3\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (3,'**********','2022-02-15 07:34:51',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"wehvhXnKKRT4\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.1\",\"type\":\"6\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (4,'**********','2022-02-15 07:34:51',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (5,'**********','2022-02-15 07:34:52',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (6,'**********','2022-02-15 07:35:17',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"aceg1uNfFbow\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.2\",\"type\":\"6\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (7,'**********','2022-02-15 07:35:17',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (8,'**********','2022-02-15 07:35:17',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (9,'**********','2022-02-15 07:36:47',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"EybsBpy6sv32\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.3\",\"type\":\"6\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (10,'**********','2022-02-15 07:36:47',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (11,'**********','2022-02-15 07:36:47',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (12,'**********','2022-02-15 07:37:05',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"Wvw5qcnVpJ5Q\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.4\",\"type\":\"6\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (13,'**********','2022-02-15 07:37:05',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (14,'**********','2022-02-15 07:37:05',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (15,'**********','2022-02-15 07:38:12',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (16,'**********','2022-02-15 07:38:23',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (17,'**********','2022-02-15 07:38:33',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (18,'**********','2022-02-15 07:38:42',0,'{\"messageType\":\"1\",\"sip\":\"**********\",\"groupring\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (19,'**********','2022-02-16 02:54:55',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"Jf9qurblggpq\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"1.*******\",\"type\":\"0\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (20,'6300100007','2022-02-16 02:55:13',0,'{\"messageType\":\"0\",\"sip\":\"6300100007\",\"pwd\":\"drHucz9jZisH\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"1.*******\",\"type\":\"0\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (21,'6300100008','2022-02-16 02:56:24',0,'{\"messageType\":\"0\",\"sip\":\"6300100008\",\"pwd\":\"kKd527598959\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"1.*******\",\"type\":\"3\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (22,'6300100009','2022-02-16 02:57:19',0,'{\"messageType\":\"0\",\"sip\":\"6300100009\",\"pwd\":\"eZk49kuivjtz\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"*******.0\",\"type\":\"0\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (23,'6300100010','2022-02-16 02:57:39',0,'{\"messageType\":\"0\",\"sip\":\"6300100010\",\"pwd\":\"zefqavgTaW76\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"*******.0\",\"type\":\"0\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (24,'6300100009','2022-02-16 02:58:04',0,'{\"messageType\":\"1\",\"sip\":\"6300100009\",\"groupring\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (25,'6300100011','2022-02-16 02:58:43',0,'{\"messageType\":\"0\",\"sip\":\"6300100011\",\"pwd\":\"wmJq4J040566\",\"group\":\"0\",\"groupring\":\"0\",\"devicenode\":\"*******.0\",\"type\":\"3\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (26,'**********','2022-02-16 06:40:03',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (27,'**********','2022-02-16 06:40:11',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (28,'**********','2022-02-16 06:40:18',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (29,'**********','2022-02-16 06:40:24',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (30,'**********','2022-02-16 06:40:54',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (31,'**********','2022-02-16 06:41:21',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (32,'**********','2022-02-16 06:41:39',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (33,'**********','2022-02-16 06:41:52',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (34,'**********','2022-02-16 06:42:06',0,'{\"messageType\":\"3\",\"sip\":\"**********\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (35,'**********','2022-02-16 06:43:20',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"xceM4FQQ7D3C\",\"group\":\"**********\",\"groupring\":\"1\",\"devicenode\":\"*******.2\",\"type\":\"6\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (36,'6300100013','2022-02-16 06:44:10',0,'{\"messageType\":\"0\",\"sip\":\"6300100013\",\"pwd\":\"piuH9FnJNUGW\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.2\",\"type\":\"2\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (37,'6300100014','2022-02-16 06:44:54',0,'{\"messageType\":\"0\",\"sip\":\"6300100014\",\"pwd\":\"wxW2r4628317\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.2\",\"type\":\"0\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (38,'**********','2022-02-16 06:48:20',0,'{\"messageType\":\"0\",\"sip\":\"**********\",\"pwd\":\"fN7tmzlfggwf\",\"group\":\"**********\",\"groupring\":\"0\",\"devicenode\":\"*******.3\",\"type\":\"2\",\"communityid\":\"5\",\"communityType\":\"0\",\"deviceAttribute\":\"0\",\"sipEnable\":\"1\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (39,'6300100014','2022-05-17 06:54:19',0,'{\"messageType\":\"1\",\"sip\":\"6300100014\",\"groupring\":\"0\"}',NULL);
INSERT INTO `LocalSipTransaction` VALUES (40,'6300100013','2022-05-17 06:54:24',0,'{\"messageType\":\"1\",\"sip\":\"6300100013\",\"groupring\":\"0\"}',NULL);
/*!40000 ALTER TABLE `LocalSipTransaction` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `LockOrder`
--

DROP TABLE IF EXISTS `LockOrder`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `LockOrder` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `OrderNumber` char(32) NOT NULL,
  `DataKey` char(32) NOT NULL COMMENT 'ç”¨æˆ·æˆ–å°åŒºè´¦å·',
  `Type` tinyint(1) unsigned NOT NULL COMMENT '1=æ¿€æ´»ç”¨æˆ·ï¼Œ2=ç»­è´¹ï¼Œ3=è´­ä¹°Appï¼Œ4=è½åœ°ï¼Œ5=é«˜çº§åŠŸèƒ½ä¸€æ¬¡æ€§ï¼Œ6=é«˜çº§åŠŸèƒ½æœˆè´¹ï¼Œ7=é«˜çº§åŠŸèƒ½å·®ä»·',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `DataKey_Type` (`DataKey`,`Type`),
  KEY `OrderNumber` (`OrderNumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `LockOrder`
--

LOCK TABLES `LockOrder` WRITE;
/*!40000 ALTER TABLE `LockOrder` DISABLE KEYS */;
/*!40000 ALTER TABLE `LockOrder` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `MacPool`
--

DROP TABLE IF EXISTS `MacPool`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MacPool` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Mac` char(12) NOT NULL,
  `AuthCode` char(32) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Mac` (`Mac`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `MacPool`
--

LOCK TABLES `MacPool` WRITE;
/*!40000 ALTER TABLE `MacPool` DISABLE KEYS */;
/*!40000 ALTER TABLE `MacPool` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ManageFeature`
--

DROP TABLE IF EXISTS `ManageFeature`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ManageFeature` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(11) unsigned NOT NULL COMMENT 'disã€å°åŒºè´¦å·ID',
  `FeatureID` int(11) NOT NULL COMMENT '0è¡¨ç¤º6.2å‰ç‰¹æ®Šæ–¹æ¡ˆï¼Œéž0è¡¨ç¤ºæ­£å¸¸çš„feature',
  `FeeUUID` char(32) DEFAULT NULL COMMENT 'è®¡è´¹ç³»ç»Ÿä¸­é«˜çº§åŠŸèƒ½è®¡åˆ’çš„UUIDï¼Œå°åŒºç»‘å®šä¸ºnull',
  PRIMARY KEY (`ID`),
  KEY `AccountID` (`AccountID`),
  KEY `FeatureID` (`FeatureID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ManageFeature`
--

LOCK TABLES `ManageFeature` WRITE;
/*!40000 ALTER TABLE `ManageFeature` DISABLE KEYS */;
INSERT INTO `ManageFeature` VALUES (1,2,1,'test-r15786aQ2I4gq4ZW8Kf9H2e0B39');
INSERT INTO `ManageFeature` VALUES (2,2,2,'test-1rv64Os4ppK89cuJ250jHu5ZDW6');
INSERT INTO `ManageFeature` VALUES (3,5,2,NULL);
/*!40000 ALTER TABLE `ManageFeature` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Message`
--

DROP TABLE IF EXISTS `Message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Message` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Title` varchar(256) NOT NULL COMMENT 'message æ ‡é¢˜',
  `Content` varchar(512) NOT NULL COMMENT 'å½“Type=0æ—¶:messageçš„å…·ä½“å†…å®¹ å½“Type=1æ—¶:å¿«é€’æ¶ˆæ¯ä¸­çš„number å½“Type=2æ—¶:TmpKeyæ¶ˆæ¯ä¸­çš„ä½¿ç”¨è€…',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `AccountID` int(11) NOT NULL DEFAULT '0' COMMENT 'messageåˆ›å»ºè€…(ç®¡ç†å‘˜)çš„ID',
  `Type` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT 'æ¶ˆæ¯ç±»åž‹ 0=message 1=å¿«é€’msg 2=TmpKeyä½¿ç”¨msg',
  `Status` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT 'Type=0æ—¶æœ‰æ•ˆmessageåˆ›å»ºè€…å‘é€çŠ¶æ€ 0 :è¿˜æœªå‘é€ 1:å‘é€å®Œæˆ',
  `NickNames` text NOT NULL COMMENT 'ç”¨äºŽç®¡ç†å‘˜æ˜¾ç¤º;ä¸åŒè”åŠ¨ç³»ç»Ÿä¸»è´¦å·æ˜µç§°ä¹‹é—´ä»¥åˆ†å·åˆ†å‰²:akuvox;xm',
  `ReceiverType` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT 'ç”¨äºŽç®¡ç†å‘˜æ˜¾ç¤º;0:å®¤å†…æœº+APP; 1:å®¤å†…æœº; 2:app',
  PRIMARY KEY (`ID`),
  KEY `AccountID_key` (`AccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Message`
--

LOCK TABLES `Message` WRITE;
/*!40000 ALTER TABLE `Message` DISABLE KEYS */;
/*!40000 ALTER TABLE `Message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `MessageAccountList`
--

DROP TABLE IF EXISTS `MessageAccountList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MessageAccountList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `ClientType` tinyint(2) unsigned NOT NULL DEFAULT '2' COMMENT ' 1:å‘é€ç»™å½“å‰accountçš„è®¾å¤‡; 2:å‘é€ç»™å½“å‰accountçš„app;',
  `Account` char(32) NOT NULL COMMENT 'å‘é€ç»™ç”¨æˆ·å¸å·',
  `Status` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '0 :æœªè¯» 1:å·²è¯»',
  `MessageID` int(11) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `MessageID_key` (`MessageID`),
  KEY `Account_key` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `MessageAccountList`
--

LOCK TABLES `MessageAccountList` WRITE;
/*!40000 ALTER TABLE `MessageAccountList` DISABLE KEYS */;
/*!40000 ALTER TABLE `MessageAccountList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `MessageTemplate`
--

DROP TABLE IF EXISTS `MessageTemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MessageTemplate` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(64) DEFAULT '' COMMENT 'message åç§°',
  `Title` varchar(256) DEFAULT '' COMMENT 'message æ ‡é¢˜',
  `Message` varchar(512) NOT NULL COMMENT 'message å†…å®¹',
  `CreateTime` timestamp NULL DEFAULT NULL,
  `AccountID` int(11) unsigned NOT NULL COMMENT 'messageåˆ›å»ºè€…çš„ID',
  PRIMARY KEY (`ID`),
  KEY `AccountID_key` (`AccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `MessageTemplate`
--

LOCK TABLES `MessageTemplate` WRITE;
/*!40000 ALTER TABLE `MessageTemplate` DISABLE KEYS */;
/*!40000 ALTER TABLE `MessageTemplate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `MonthCaptureCounted`
--

DROP TABLE IF EXISTS `MonthCaptureCounted`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MonthCaptureCounted` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MonthSliceTableNameID` int(10) unsigned DEFAULT '0' COMMENT 'å¯¹åº”æœˆä»½çš„è¡¨åID',
  `MngAccountID` int(10) unsigned DEFAULT '0',
  `DoorCount` int(10) unsigned DEFAULT '0' COMMENT 'æ‰€æœ‰å¼€é—¨çš„è®°å½•å€¼',
  `CallDoorCapture` int(10) unsigned DEFAULT '0' COMMENT 'é€šè¯å¼€é—¨æˆªå›¾0',
  `Tmpkey` int(10) unsigned DEFAULT '0',
  `Privkey` int(10) unsigned DEFAULT '0',
  `Rfkey` int(10) unsigned DEFAULT '0',
  `Face` int(10) unsigned DEFAULT '0',
  `RemoteApp` int(10) unsigned DEFAULT '0',
  `CallApp` int(10) unsigned DEFAULT '0',
  `CallIndoor` int(10) unsigned DEFAULT '0',
  `NFC` int(10) unsigned DEFAULT '0',
  `BLE` int(10) unsigned DEFAULT '0',
  `CallCapture` int(10) unsigned DEFAULT '0' COMMENT 'é€šè¯ä¸å¼€é—¨çš„æˆªå›¾103',
  `AppCapture` int(10) unsigned DEFAULT '0' COMMENT 'appæ‰‹åŠ¨æˆªå›¾102',
  PRIMARY KEY (`ID`),
  KEY `MonthSliceTableNameID_MngID` (`MngAccountID`,`MonthSliceTableNameID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `MonthCaptureCounted`
--

LOCK TABLES `MonthCaptureCounted` WRITE;
/*!40000 ALTER TABLE `MonthCaptureCounted` DISABLE KEYS */;
/*!40000 ALTER TABLE `MonthCaptureCounted` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `MonthSliceTableName`
--

DROP TABLE IF EXISTS `MonthSliceTableName`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MonthSliceTableName` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(64) NOT NULL DEFAULT '' COMMENT 'è¡¨å',
  `YearMonth` int(11) DEFAULT '0' COMMENT 'å¹´æœˆæ¯”å¦‚201908,å¯ç”¨äºŽæŽ’åº',
  `StartID` int(10) unsigned DEFAULT '0' COMMENT 'è¡¨æ•°æ®å¼€å§‹ID',
  `EndID` int(10) unsigned DEFAULT '0' COMMENT 'è¡¨æ•°æ®ç»“æŸID',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `MonthSliceTableName`
--

LOCK TABLES `MonthSliceTableName` WRITE;
/*!40000 ALTER TABLE `MonthSliceTableName` DISABLE KEYS */;
/*!40000 ALTER TABLE `MonthSliceTableName` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OfficeInfo`
--

DROP TABLE IF EXISTS `OfficeInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `OfficeInfo` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `UUID` char(36) NOT NULL,
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `UpdateUser` char(32) NOT NULL DEFAULT '',
  `AccountUUID` char(36) NOT NULL,
  `Street` varchar(128) NOT NULL DEFAULT '',
  `City` varchar(128) NOT NULL DEFAULT '',
  `PostalCode` varchar(32) NOT NULL DEFAULT '',
  `Country` varchar(128) NOT NULL DEFAULT '',
  `States` varchar(128) NOT NULL DEFAULT '',
  `EnableMotion` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'é—¨å£æœºæ˜¯å¦å¼€å¯motion',
  `MotionTime` tinyint(1) NOT NULL DEFAULT '10' COMMENT 'é—¨å£æœºç›‘æµ‹å¤šä¹…æ²¡æœ‰åŠ¨ä½œä¸ŠæŠ¥æˆªå›¾æ—¶é—´',
  `FaceEnrollment` tinyint(1) NOT NULL DEFAULT '1',
  `IDCardVerification` tinyint(1) NOT NULL DEFAULT '1',
  `LastDevOfflineNotifyTime` datetime DEFAULT '1970-01-01 08:00:00' COMMENT 'ä¸Šæ¬¡é€šçŸ¥æ—¶é—´',
  `Switch` tinyint(1) DEFAULT '5' COMMENT 'æŒ‰ä½å¼€å…³æ ‡ç¤ºç¬¦, ä»Žä½Žä½åˆ°é«˜ä½åˆ†åˆ«æ˜¯:è½åœ°å¼€å…³,æŽ‰çº¿é€šçŸ¥å¼€å‘,æ˜¯å¦å…è®¸ç”¨æˆ·ä½¿ç”¨PINï¼ŒSIMå¡è¶…æµé‡å¼€å…³.',
  `MobileNumber` char(24) NOT NULL DEFAULT '' COMMENT 'æ‰‹æœºå·',
  `PhoneCode` char(8) NOT NULL DEFAULT '' COMMENT 'åŒºå·',
  `FeatureExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT 'é«˜çº§åŠŸèƒ½è¿‡æœŸæ—¶é—´',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UUID` (`UUID`),
  UNIQUE KEY `AccountUUID` (`AccountUUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OfficeInfo`
--

LOCK TABLES `OfficeInfo` WRITE;
/*!40000 ALTER TABLE `OfficeInfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `OfficeInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OfflineResendLog`
--

DROP TABLE IF EXISTS `OfflineResendLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `OfflineResendLog` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Mac` char(16) NOT NULL DEFAULT '' COMMENT 'mac',
  `FileName` varchar(256) NOT NULL DEFAULT '',
  `MessageSeq` char(16) NOT NULL DEFAULT '' COMMENT 'seq',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Mac_FILE` (`Mac`,`FileName`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OfflineResendLog`
--

LOCK TABLES `OfflineResendLog` WRITE;
/*!40000 ALTER TABLE `OfflineResendLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `OfflineResendLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrderEndUserList`
--

DROP TABLE IF EXISTS `OrderEndUserList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `OrderEndUserList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `OrderID` int(10) unsigned NOT NULL,
  `Type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1=>; 2=>(); 3=>app; 4=>app; 5=>;8=;9=pm app;10=pm app',
  `Amount` int(11) NOT NULL COMMENT 'å•†å“é‡‘é¢ã€‚æ‰©å¤§100å€.ç»­è´¹æ—¶å®¶åº­å¥—é¤å†…ä»·æ ¼ï¼Œæˆ–è€…é¢å¤–appä»·æ ¼ï¼Œå‡ä¸ºæ¯ä¸ªæœˆå•ä»·',
  `AppID` int(11) unsigned NOT NULL COMMENT 'é¢å¤–appæ—¶ï¼Œä½¿ç”¨appIDï¼Œå®¶åº­å¥—é¤ä½¿ç”¨ä¸»è´¦å·IDï¼Œ6.2åŽå¯ä¸ºé«˜çº§æ”¶è´¹çš„æ–¹æ¡ˆFeatureID',
  `Object` varchar(64) NOT NULL DEFAULT '' COMMENT 'æˆ¿é—´å·ï¼Œç”¨äºŽç‰©ä¸šè´­ä¹°æ˜¾ç¤º;æˆ–è€…é¢å¤–Appè®°å½•Appé‚®ç®±',
  `ParentID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'è´­ä¹°é¢å¤–appæˆ–è€…ç»­è´¹é¢å¤–appæ—¶ä¸»è´¦å·id',
  `Discount` tinyint(4) DEFAULT '100',
  `DiscountInfo` varchar(512) DEFAULT '',
  `ActivityUUID` varchar(32) DEFAULT NULL,
  `ChargeData` varchar(1024) DEFAULT NULL,
  `ProjectUUID` char(36) NOT NULL COMMENT 'UUID',
  `ProjectName` char(36) NOT NULL COMMENT 'location',
  `Days` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `OrderID` (`OrderID`) USING BTREE,
  KEY `AppID` (`AppID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrderEndUserList`
--

LOCK TABLES `OrderEndUserList` WRITE;
/*!40000 ALTER TABLE `OrderEndUserList` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrderEndUserList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrderList`
--

DROP TABLE IF EXISTS `OrderList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `OrderList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `OrderNumber` char(32) NOT NULL DEFAULT '' COMMENT 'è®¢å•å·',
  `AccountID` int(11) NOT NULL COMMENT 'ä¸»è´¦å·æˆ–è€…ç‰©ä¸šID',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=æ”¯ä»˜ä¸­ 1=æ”¯ä»˜æˆåŠŸ 2=æ”¯ä»˜å¤±è´¥ 3=æ”¯ä»˜è¶…æ—¶ 4=å¼‚å¸¸è®¢å•(æ”¯ä»˜æˆåŠŸï¼Œä½†æ˜¯éƒ¨åˆ†appæ— æ³•æˆåŠŸç»­è´¹ï¼Œå­˜åœ¨å‰åŽappè¢«åˆ é™¤çš„æƒ…å†µ) 5=å–æ¶ˆ 6=ç³»ç»Ÿå¤„ç†ä¸­',
  `TotalPrice` int(11) NOT NULL DEFAULT '0' COMMENT 'å•ä½ ç¾Žå…ƒ/100 ,æ­¤å¤„é‡‘é¢æ‰©å¤§100å€;å®žé™…æ”¯ä»˜é‡‘é¢',
  `Type` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'æ”¯ä»˜ç±»åž‹ï¼š1=æ¿€æ´»;2=å®¶åº­ç»­è´¹;3=é¢å¤–appè´­ä¹°è´¹ç”¨,4=ä¸ªäººè½åœ°è´¹ç”¨;5=é«˜çº§åŠŸèƒ½ä¸€æ¬¡æ€§ä»˜è´¹;6=é«˜çº§åŠŸèƒ½æœˆè´¹;7=>é«˜çº§åŠŸèƒ½å·®ä»·',
  `Payer` varchar(64) NOT NULL COMMENT 'æ”¯ä»˜äººã€‚ç»ˆç«¯ç”¨æˆ·å’Œç‰©ä¸šçš„é‚®ç®±',
  `PayerType` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'æ”¯ä»˜äººç±»åž‹ï¼›0=>ç»ˆç«¯ç”¨æˆ·,1=>ç‰©ä¸š',
  `InstallID` int(10) unsigned NOT NULL COMMENT 'ç¤¾åŒºç®¡ç†å‘˜æˆ–è€…installçš„ID',
  `AreaManageID` int(10) unsigned NOT NULL COMMENT 'åŒºåŸŸç®¡ç†å‘˜ID',
  `Months` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'è´­ä¹°æœˆæ•°',
  `Days` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'è´­ä¹°å¤©æ•°',
  `AppNumbers` tinyint(3) unsigned DEFAULT NULL COMMENT 'ç”¨äºŽç»­è´¹æ—¶ï¼Œå¥—é¤å†…appä¸ªæ•°',
  `PaypalOrder` varchar(64) NOT NULL DEFAULT '',
  `PaypalEmail` varchar(64) NOT NULL DEFAULT '',
  `Discount` int(11) NOT NULL DEFAULT '100' COMMENT 'ä¼˜æƒ é‡‘é¢ç™¾åˆ†æ¯”',
  `IsDelete` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'ç”¨æˆ·åˆ é™¤',
  `PayPlatform` tinyint(4) DEFAULT '0' COMMENT 'paypalä»˜æ¬¾0ï¼Œstripeä»˜æ¬¾1',
  `BmOrderNumber` char(20) NOT NULL,
  `BeforeOncePrice` int(11) NOT NULL DEFAULT '0' COMMENT 'å•ä½ ç¾Žå…ƒ/100 ,åŒæ­¥è®¡è´¹ç³»ç»Ÿä¿®æ”¹è®¢å•ä»·æ ¼ä¹‹å‰çš„ä»·æ ¼',
  `CouponNumber` char(16) DEFAULT NULL COMMENT 'åŒæ­¥è®¡è´¹ç³»ç»Ÿä»£é‡‘åˆ¸',
  `CouponCount` int(11) unsigned DEFAULT '0' COMMENT 'å•ä½ ç¾Žå…ƒ/100 ,æ­¤å¤„é‡‘é¢æ‰©å¤§100å€,åŒæ­¥è®¡è´¹ç³»ç»Ÿä»£é‡‘åˆ¸é‡‘é¢',
  `FinalPrice` int(11) NOT NULL DEFAULT '0' COMMENT 'æ–°ç‰ˆè®¢å•æœ€ç»ˆä»·æ ¼,å°†æ—§è®¢å•çš„FinalPriceæ›´æ–°æˆTotalPriceï¼ŒåŽç»­ç»Ÿä¸€ä½¿ç”¨FinalPrice',
  `WebHookToken` varchar(64) NOT NULL COMMENT 'è®¡è´¹ç³»ç»Ÿåˆ·æ–°å½“å‰è®¢å•çŠ¶æ€çš„token',
  `PayCode` varchar(10) NOT NULL COMMENT 'ç”¨æˆ·è·³è½¬è®¡è´¹ç³»ç»Ÿæ”¯ä»˜æ—¶éªŒè¯ç ',
  `IsBatch` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0=1=',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `OrderNumber` (`OrderNumber`) USING HASH,
  KEY `Account` (`AccountID`),
  KEY `Status` (`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrderList`
--

LOCK TABLES `OrderList` WRITE;
/*!40000 ALTER TABLE `OrderList` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrderList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OwnerManagement`
--

DROP TABLE IF EXISTS `OwnerManagement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `OwnerManagement` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` char(48) DEFAULT '',
  `Sex` int(11) DEFAULT NULL,
  `Community` varchar(32) DEFAULT '',
  `Address` char(128) DEFAULT '',
  `IDCard` char(64) DEFAULT '',
  `Phone` char(64) DEFAULT '',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateUser` char(24) DEFAULT 'Administrator',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OwnerManagement`
--

LOCK TABLES `OwnerManagement` WRITE;
/*!40000 ALTER TABLE `OwnerManagement` DISABLE KEYS */;
/*!40000 ALTER TABLE `OwnerManagement` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PbxRedirectInfo`
--

DROP TABLE IF EXISTS `PbxRedirectInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PbxRedirectInfo` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `CommunitID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒº:å°åŒºID. ç¤¾åŒºç”¨è¿™ä¸ªè‡ªåŠ¨åŒ¹é…',
  `Node` char(32) NOT NULL DEFAULT '' COMMENT 'ä¸ªäººï¼šNode å•ä½æˆ·ç”¨è¿™ä¸ªè‡ªåŠ¨åŒ¹é…',
  `PbxID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `CommunitID_key` (`CommunitID`,`PbxID`),
  KEY `Node_key` (`Node`,`PbxID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PbxRedirectInfo`
--

LOCK TABLES `PbxRedirectInfo` WRITE;
/*!40000 ALTER TABLE `PbxRedirectInfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `PbxRedirectInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PbxServerList`
--

DROP TABLE IF EXISTS `PbxServerList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PbxServerList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `PbxIp` char(20) NOT NULL DEFAULT '',
  `PbxIpv6` char(40) NOT NULL DEFAULT '',
  `PbxDomain` char(64) NOT NULL DEFAULT '' COMMENT 'pbxåŸŸå',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PbxServerList`
--

LOCK TABLES `PbxServerList` WRITE;
/*!40000 ALTER TABLE `PbxServerList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PbxServerList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PendingRegUser`
--

DROP TABLE IF EXISTS `PendingRegUser`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PendingRegUser` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL,
  `MAC` char(32) NOT NULL COMMENT 'è®¾å¤‡MACåœ°å€',
  `Token` varchar(64) DEFAULT '',
  `Status` tinyint(1) DEFAULT '0' COMMENT 'æ³¨å†ŒçŠ¶æ€,0:æœªæ³¨å†Œ;1:å·²æ³¨å†Œ',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PendingRegUser`
--

LOCK TABLES `PendingRegUser` WRITE;
/*!40000 ALTER TABLE `PendingRegUser` DISABLE KEYS */;
/*!40000 ALTER TABLE `PendingRegUser` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PerNodeDevices`
--

DROP TABLE IF EXISTS `PerNodeDevices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PerNodeDevices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `NodeID` int(10) unsigned NOT NULL COMMENT 'ä¸»è´¦å·ID',
  `PerDevID` int(10) unsigned NOT NULL COMMENT 'ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜å…¬å…±è®¾å¤‡ID',
  PRIMARY KEY (`ID`),
  KEY `NodeID_PerDevID` (`NodeID`,`PerDevID`),
  KEY `PerDevID` (`PerDevID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PerNodeDevices`
--

LOCK TABLES `PerNodeDevices` WRITE;
/*!40000 ALTER TABLE `PerNodeDevices` DISABLE KEYS */;
/*!40000 ALTER TABLE `PerNodeDevices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalAccount`
--

DROP TABLE IF EXISTS `PersonalAccount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalAccount` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `Passwd` char(64) NOT NULL,
  `Role` tinyint(1) DEFAULT '0' COMMENT '10=;11=;20=;21=;;30=office;31=office admin;40=PM',
  `ParentID` int(10) unsigned DEFAULT '0' COMMENT 'å¯¹äºŽç¤¾åŒºç”¨æˆ·ä¸»è´¦å·ï¼Œå°±æ˜¯ç¤¾åŒºç®¡ç†å‘˜çš„ID',
  `UnitID` int(10) unsigned DEFAULT '0' COMMENT 'å¯¹äºŽç¤¾åŒºç”¨æˆ·å°±æ˜¯unitå•å…ƒçš„ID',
  `Email` char(64) DEFAULT NULL,
  `Info` char(64) DEFAULT '',
  `Name` varchar(128) DEFAULT '',
  `SipAccount` char(64) DEFAULT '',
  `SipPwd` char(16) NOT NULL DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `Address` char(64) DEFAULT '',
  `Phone` char(24) DEFAULT '' COMMENT 'è½åœ°æ—¶ä½¿ç”¨',
  `PhoneStatus` tinyint(1) DEFAULT '0' COMMENT 'è¯¥å·ç æ˜¯å¦å¼€å¯ç¾¤å“é“ƒçš„å¼€å…³ï¼š0:ä¸å¼€å¯ï¼Œ1:å¼€å¯',
  `RoomNumber` char(64) DEFAULT '',
  `TimeZone` char(64) DEFAULT '+0:00 Abidjan' COMMENT 'æ—¶åŒº',
  `ReadMsgID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'è´¦å·è¯»å–åˆ°é€šçŸ¥æ¶ˆæ¯çš„id',
  `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT 'è¿‡æœŸæ—¶é—´',
  `EnableIpDirect` tinyint(1) DEFAULT '1' COMMENT 'æ˜¯å¦å¯åŠ¨ipç›´æ’­ 1å¯åŠ¨ 0å…³é—­ ',
  `Special` tinyint(1) DEFAULT '0' COMMENT 'ç‰¹æ®Šè´¦å·æ ‡è¯†,æ ‡è¯†ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜é‡Œé¢çš„å…¬å…±è®¾å¤‡çš„è™šæ‹Ÿä¸»è´¦å·',
  `CustomizeForm` tinyint(1) DEFAULT '3' COMMENT 'time1:ï¼ˆ1:12å°æ—¶åˆ¶ï¼Œ2:24å°æ—¶åˆ¶ï¼‰ï¼›time2:(1:y-m-d,3:m-d-y,5:d-m-y),æ­¤å¤„å€¼ä¸ºtime1+time2',
  `FreeDays` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'ä¸»è´¦å·è¯•ç”¨å¤©æ•°',
  `NFCCode` varchar(32) NOT NULL DEFAULT '' COMMENT 'ä¸ªäººè´¦å·çš„NFC æœ‰å€¼ä»£è¡¨å¼€ï¼Œæ²¡æœ‰ä»£è¡¨å…³',
  `BLECode` varchar(32) NOT NULL DEFAULT '' COMMENT 'ä¸ªäººè´¦å·çš„BLE æœ‰å€¼ä»£è¡¨å¼€ï¼Œæ²¡æœ‰ä»£è¡¨å…³',
  `Active` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'æ˜¯å¦æ¿€æ´» 1æ¿€æ´» 0æœªæ¿€æ´»',
  `Initialization` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦åˆå§‹åŒ–è¿‡',
  `appLoginStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦åœ¨appç™»é™†è¿‡ 1ç™»é™†è¿‡ 0æœªç™»é™†è¿‡',
  `ActiveTime` timestamp NULL DEFAULT NULL,
  `FirstName` varchar(64) DEFAULT '',
  `LastName` varchar(64) DEFAULT '',
  `RoomID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'ä¸ªäººæˆ¿é—´æ ‡è¯†',
  `SipType` tinyint(1) DEFAULT '0' COMMENT '0udp/1tcp/2tls',
  `Codec` varchar(32) NOT NULL DEFAULT '0,8,18' COMMENT '0=PCMU, 8=PCMA, 18=G.729 ç”¨é€—å·éš”å¼€ä»£è¡¨ä¼˜å…ˆçº§ 18,0,8ã€‚å¦‚æžœå€¼ç©ºä»£è¡¨é»˜è®¤æˆ–å®¢æˆ·ç«¯è‡ªè¡Œå®šä¹‰',
  `TempKeyPermission` tinyint(1) DEFAULT '1' COMMENT 'TempKeyç”Ÿæˆæƒé™ 0:æ—  1:æœ‰',
  `Phone2` char(24) DEFAULT '',
  `Phone3` char(24) DEFAULT '',
  `PhoneCode` char(8) DEFAULT '' COMMENT 'åŒºå·',
  `PhoneExpireTime` datetime DEFAULT '2020-01-01 00:00:00' COMMENT 'ä¸èƒ½æ˜¯å½“å‰æ—¶é—´,ä¸ç„¶ä¸ç®¡æœ‰æ²¡æœ‰å¼€è½åœ°,éš”å¤©å°±ä¼šé‚®ä»¶æé†’',
  `Language` char(8) NOT NULL DEFAULT 'en',
  `MobileNumber` char(24) DEFAULT NULL COMMENT 'æ‰‹æœºå·å¸å·',
  `Version` int(10) unsigned DEFAULT '123456' COMMENT 'ç”¨æˆ·æ•°æ®ç‰ˆæœ¬å·',
  `BLEOpenDoorType` tinyint(1) DEFAULT '0' COMMENT 'è“ç‰™å¼€é—¨çš„æ–¹å¼ 0=æ‘‡ä¸€æ‘‡, 1=æ— æ„Ÿ',
  `UUID` char(36) NOT NULL COMMENT 'uuid()åŽ»é™¤æ¨ªæ ,ä¾‹å¦‚45efccef469111ec8fe900163e047e78',
  `ParentUUID` char(36) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE KEY `Email` (`Email`),
  UNIQUE KEY `MobileNumber` (`MobileNumber`),
  KEY `ParentID` (`ParentID`),
  KEY `UnitID` (`UnitID`),
  KEY `NFC_BLE` (`NFCCode`,`BLECode`),
  KEY `Name` (`Name`),
  KEY `RoomID` (`RoomID`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalAccount`
--

LOCK TABLES `PersonalAccount` WRITE;
/*!40000 ALTER TABLE `PersonalAccount` DISABLE KEYS */;
INSERT INTO `PersonalAccount` VALUES (1,'**********','c2567563a373a775a2217b49d4d8d37e',20,5,1,NULL,'','System Test0','**********','wehvhXnKKRT4','2022-02-15 07:34:51','','',0,'Room100','+0:00 Abidjan',0,'2030-03-30 14:40:24',1,0,3,0,'','',1,0,0,'2022-02-16 06:40:24','System','Test0',1,0,'0,8,18',1,'','','86','2020-01-01 00:00:00','zh-cn',NULL,**********,0,'','');
INSERT INTO `PersonalAccount` VALUES (2,'**********','5b68ddd15bd3fe8f315b1f64a6356a9a',20,5,1,NULL,'','System Test1','**********','aceg1uNfFbow','2022-02-15 07:35:17','','*********',0,'Room101','+0:00 Abidjan',0,'2030-03-30 14:40:18',1,0,3,0,'','',1,0,0,'2022-02-16 06:40:17','System','Test1',2,0,'0,8,18',1,'*********','','86','2020-01-01 00:00:00','zh-cn',NULL,**********,0,'','');
INSERT INTO `PersonalAccount` VALUES (3,'**********','720f71648f5ff2202fb253a3bce7bb46',20,5,2,NULL,'','System Test2','**********','EybsBpy6sv32','2022-02-15 07:36:47','','',0,'Room102','+0:00 Abidjan',0,'2030-03-30 14:40:11',1,0,3,0,'','',1,0,0,'2022-02-16 06:40:11','System','Test2',3,0,'0,8,18',1,'','','86','2020-01-01 00:00:00','zh-cn',NULL,**********,0,'','');
INSERT INTO `PersonalAccount` VALUES (4,'**********','0a9794736b09ea780b56743e7df1f445',20,5,2,NULL,'','System Test3','**********','Wvw5qcnVpJ5Q','2022-02-15 07:37:05','','********',0,'Room103','+0:00 Abidjan',0,'2030-03-30 14:40:03',1,0,3,0,'','',1,0,0,'2022-02-16 06:40:03','System','Test3',4,0,'0,8,18',1,'********','','86','2020-01-01 00:00:00','zh-cn',NULL,**********,0,'','');
INSERT INTO `PersonalAccount` VALUES (5,'**********','0104ce27a48d852feeea27d0c715e8c5',21,2,1,NULL,'','101 Slave1','**********','xceM4FQQ7D3C','2022-02-16 06:43:20','','0********',0,'','+0:00 Abidjan',0,'2299-12-31 23:59:59',1,0,3,0,'','',1,0,0,NULL,'101','Slave1',0,0,'0,8,18',1,'','','86','2020-01-01 00:00:00','zh-cn','********',**********,0,'','');
/*!40000 ALTER TABLE `PersonalAccount` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalAccountCnf`
--

DROP TABLE IF EXISTS `PersonalAccountCnf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalAccountCnf` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(20) NOT NULL DEFAULT '' COMMENT 'ä¸»è´¦å·è”åŠ¨',
  `EnableMotion` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'é—¨å£æœºæ˜¯å¦å¼€å¯motion',
  `MotionTime` tinyint(1) NOT NULL DEFAULT '10' COMMENT 'é—¨å£æœºç›‘æµ‹å¤šä¹…æ²¡æœ‰åŠ¨ä½œä¸ŠæŠ¥æˆªå›¾æ—¶é—´',
  `EnableRobinCall` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'å¼€å¯å¾ªçŽ¯å‘¼å«',
  `RobinCallTime` tinyint(1) NOT NULL DEFAULT '20' COMMENT 'å‘¼å«è¶…æ—¶æ—¶é—´',
  `RobinCallVal` varchar(512) DEFAULT '' COMMENT 'å¾ªçŽ¯å‘¼å«çš„å€¼jsonæ ¼å¼',
  `AppCount` tinyint(1) NOT NULL DEFAULT '7' COMMENT 'appä¸ªæ•°',
  `DevCount` smallint(11) NOT NULL DEFAULT '100' COMMENT 'devä¸ªæ•°',
  `ValidTime` datetime NOT NULL DEFAULT '2299-12-31 23:59:59' COMMENT 'è¿‡æœŸæ—¶é—´',
  `CallType` tinyint(4) DEFAULT '0' COMMENT 'å‘¼å«é¡ºåºå’Œç±»åž‹ 0=app 1=phone 2=appå…ˆ æœªæŽ¥å¬åŽphone',
  `AlreadySendEmail` tinyint(1) DEFAULT '0' COMMENT 'åˆ›å»ºåŽæ˜¯å¦å‘è¿‡é‚®ä»¶',
  `FreeAppCount` int(4) NOT NULL DEFAULT '0' COMMENT 'ä¸»è´¦å·å…è´¹ä»Žè´¦å·æ•°é‡ï¼Œä¼šéšç€ä¸»è´¦å·è´­ä¹°appæ•°é‡è€Œå¢žåŠ ',
  `Name` varchar(128) DEFAULT '',
  `AllowCreateSlaveCnt` int(11) DEFAULT NULL COMMENT 'appå…è®¸åˆ›å»ºçš„å®¶åº­æˆå‘˜çš„ä¸ªæ•°',
  `Flags` tinyint(1) DEFAULT '0' COMMENT 'æŒ‰ä½å¼€å…³,ä»Žä½Žä½åˆ°é«˜ä½åˆ†åˆ«æ˜¯:1=æŽ§åˆ¶å®¶åº­æˆå‘˜',
  `WebRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-50',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalAccountCnf`
--

LOCK TABLES `PersonalAccountCnf` WRITE;
/*!40000 ALTER TABLE `PersonalAccountCnf` DISABLE KEYS */;
INSERT INTO `PersonalAccountCnf` VALUES (1,'**********',0,10,0,20,'',7,100,'2299-12-31 23:59:59',0,0,0,'',NULL,0,0);
INSERT INTO `PersonalAccountCnf` VALUES (2,'**********',0,10,0,20,'',7,100,'2299-12-31 23:59:59',2,0,0,'',NULL,0,0);
INSERT INTO `PersonalAccountCnf` VALUES (3,'**********',0,10,0,20,'',7,100,'2299-12-31 23:59:59',0,0,0,'',NULL,0,0);
INSERT INTO `PersonalAccountCnf` VALUES (4,'**********',0,10,0,20,'',7,100,'2299-12-31 23:59:59',4,0,0,'',NULL,0,0);
/*!40000 ALTER TABLE `PersonalAccountCnf` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalAccountOfficeInfo`
--

DROP TABLE IF EXISTS `PersonalAccountOfficeInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalAccountOfficeInfo` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UUID` char(36) NOT NULL COMMENT 'uuid()åŽ»é™¤æ¨ªæ ,ä¾‹å¦‚45efccef469111ec8fe900163e047e78',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `UpdateUser` char(32) NOT NULL DEFAULT '',
  `PersonalAccountUUID` char(36) NOT NULL,
  `EmployeeID` varchar(36) NOT NULL,
  `Flags` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'ç¬¬ä¸€ä½=æ˜¯å¦å¼€å¯smart intercom',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UUID` (`UUID`),
  KEY `PersonalAccountUUID` (`PersonalAccountUUID`),
  KEY `EmployeeID` (`EmployeeID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalAccountOfficeInfo`
--

LOCK TABLES `PersonalAccountOfficeInfo` WRITE;
/*!40000 ALTER TABLE `PersonalAccountOfficeInfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalAccountOfficeInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalAlarms`
--

DROP TABLE IF EXISTS `PersonalAlarms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalAlarms` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AlarmType` char(64) DEFAULT '',
  `Address` char(64) DEFAULT '',
  `Community` varchar(32) DEFAULT NULL COMMENT 'ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜',
  `Node` char(32) DEFAULT '',
  `Extension` int(11) DEFAULT NULL,
  `AlarmTime` datetime DEFAULT NULL,
  `Status` int(4) DEFAULT NULL,
  `DealTime` datetime DEFAULT NULL,
  `DealUser` char(64) DEFAULT '',
  `DealType` int(11) DEFAULT NULL,
  `DealResult` varchar(1024) DEFAULT '',
  `DevicesMAC` char(16) DEFAULT '' COMMENT 'å‘Šè­¦è®¾å¤‡çš„macåœ°å€',
  `AlarmCode` tinyint(4) DEFAULT '0' COMMENT '0 old data/1-Door Unlock/2-Infrared/3-Drmagent/4-Smoke/5-Gas/6-Urgency/7-SOS/8-Tamper',
  `AlarmCustomize` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è‡ªå®šä¹‰',
  `AlarmLocation` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'ä½ç½®',
  `AlarmZone` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'é˜²åŒº',
  PRIMARY KEY (`ID`),
  KEY `Community_Node` (`Community`,`Node`),
  KEY `MAC` (`DevicesMAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalAlarms`
--

LOCK TABLES `PersonalAlarms` WRITE;
/*!40000 ALTER TABLE `PersonalAlarms` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalAlarms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalAppTmpKey`
--

DROP TABLE IF EXISTS `PersonalAppTmpKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalAppTmpKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `UnitID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¨å±€çš„å•å…ƒunit ID',
  `Type` tinyint(1) DEFAULT '0' COMMENT '0:ç¤¾åŒºç»ˆç«¯ç”¨æˆ·çš„tmp key;1:ä¸ªäººç»ˆç«¯ç”¨æˆ·çš„tmp key',
  `TmpKey` int(11) NOT NULL,
  `Node` char(32) DEFAULT '',
  `BeginTime` timestamp NULL DEFAULT NULL,
  `EndTime` timestamp NULL DEFAULT NULL,
  `AccessTimes` int(11) DEFAULT '0',
  `AllowedTimes` int(11) DEFAULT NULL,
  `QrCodeUrl` varchar(128) DEFAULT '' COMMENT 'ä¸´æ—¶ç§˜é’¥äºŒç»´ç å›¾ç‰‡çš„å®Œæ•´url',
  `DeliveryTXT` varchar(32) DEFAULT '' COMMENT 'åˆ†äº«æ–¹å¼ email',
  `Description` varchar(64) DEFAULT '' COMMENT 'æè¿°',
  `IDNumber` char(20) NOT NULL DEFAULT '',
  `Creator` char(32) DEFAULT '',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:æ¯å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰æ¯å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `StartTime` time DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`),
  KEY `Key_Mng_Unit` (`MngAccountID`,`TmpKey`,`UnitID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalAppTmpKey`
--

LOCK TABLES `PersonalAppTmpKey` WRITE;
/*!40000 ALTER TABLE `PersonalAppTmpKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalAppTmpKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalBillingInfo`
--

DROP TABLE IF EXISTS `PersonalBillingInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalBillingInfo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(20) NOT NULL DEFAULT '' COMMENT 'ä¸»è´¦å·',
  `BillingTitle` char(128) NOT NULL COMMENT 'å…¬å¸åå­—æˆ–å®¶åº­åå­—',
  `Contactor` char(128) NOT NULL DEFAULT '',
  `Street` char(128) NOT NULL DEFAULT '' COMMENT 'è¡—é“',
  `City` char(64) NOT NULL DEFAULT '' COMMENT 'åŸŽå¸‚',
  `Postcode` char(32) DEFAULT '' COMMENT 'é‚®ç¼–',
  `Country` varchar(8) DEFAULT 'USA',
  `TelePhone` char(32) DEFAULT '',
  `Fax` char(32) DEFAULT '' COMMENT 'ä¼ çœŸ',
  PRIMARY KEY (`ID`),
  KEY `AccountID` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalBillingInfo`
--

LOCK TABLES `PersonalBillingInfo` WRITE;
/*!40000 ALTER TABLE `PersonalBillingInfo` DISABLE KEYS */;
INSERT INTO `PersonalBillingInfo` VALUES (1,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (2,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (3,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (4,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (5,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (6,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (7,'**********','','','','','','USA','','');
INSERT INTO `PersonalBillingInfo` VALUES (8,'**********','','','','','','USA','','');
/*!40000 ALTER TABLE `PersonalBillingInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalCapture`
--

DROP TABLE IF EXISTS `PersonalCapture`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalCapture` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` varchar(16) NOT NULL DEFAULT '',
  `MngAccountID` int(11) DEFAULT '0',
  `DevType` int(11) DEFAULT '0' COMMENT '0 ä¸æ˜¯å…¬å…±è®¾å¤‡ 1æ˜¯å…¬å…±è®¾å¤‡',
  `MngType` int(11) DEFAULT '0' COMMENT '1æ˜¯ä¸ªäºº 0ç¤¾åŒº, ä¸ºäº†ä¸‹æ¬¡æ‹†åˆ†ä¸ªäººç¤¾åŒº',
  `SipAccount` char(16) DEFAULT '',
  `Location` varchar(64) DEFAULT '',
  `PicName` char(80) NOT NULL COMMENT 'å›¾ç‰‡çš„åå­—,ç”±è®¾å¤‡ç«¯å†³å®š',
  `PicUrl` char(64) DEFAULT '' COMMENT 'å›¾ç‰‡çš„å®Œæ•´url',
  `SPicUrl` char(64) DEFAULT '' COMMENT 'å°å›¾çš„å®Œæ•´url',
  `CaptureAction` varchar(64) DEFAULT '',
  `Initiator` varchar(128) DEFAULT '',
  `CaptureTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Response` tinyint(1) DEFAULT NULL COMMENT '0:success; 1:Fail',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦å·²è¯»çš„æ ‡å¿—,0:æœªè¯»; 1:å·²è¯»',
  `Node` char(32) DEFAULT '' COMMENT 'æ ‡è¯†ç”±é‚£ä¸ªè”åŠ¨æˆªå›¾,ç”¨äºŽå…¬å…±è®¾å¤‡',
  `CaptureType` tinyint(1) NOT NULL DEFAULT '99' COMMENT 'æˆªå›¾ç±»åž‹ 0=CALL Unlock,1=TMPKEY,2=LOCALKEY,3=RFCARD,4=FACE,100=NFC,101=BLE,102=appæ‰‹åŠ¨æˆªå›¾,103=callä¸å¼€é—¨æˆªå›¾,99=ä¹‹å‰ç‰ˆæœ¬æ•°æ®å±žäºŽå¼€é—¨è®°å½•',
  `KeyNum` varchar(32) NOT NULL DEFAULT '' COMMENT 'å¼€é—¨æ‰€ç”¨çš„key',
  `RoomNum` varchar(20) NOT NULL DEFAULT '' COMMENT 'keyæ‰€å±žçš„æˆ¿é—´å·',
  PRIMARY KEY (`ID`,`CaptureTime`),
  KEY `Node` (`Node`),
  KEY `MAC_PicName` (`MAC`,`PicName`),
  KEY `CaptureTime` (`CaptureTime`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `MngAccountID_PicName` (`MngAccountID`,`CaptureType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
/*!50100 PARTITION BY RANGE (MONTH(CaptureTime))
(PARTITION P1 VALUES LESS THAN (2) ENGINE = InnoDB,
 PARTITION P2 VALUES LESS THAN (3) ENGINE = InnoDB,
 PARTITION P3 VALUES LESS THAN (4) ENGINE = InnoDB,
 PARTITION P4 VALUES LESS THAN (5) ENGINE = InnoDB,
 PARTITION P5 VALUES LESS THAN (6) ENGINE = InnoDB,
 PARTITION P6 VALUES LESS THAN (7) ENGINE = InnoDB,
 PARTITION P7 VALUES LESS THAN (8) ENGINE = InnoDB,
 PARTITION P8 VALUES LESS THAN (9) ENGINE = InnoDB,
 PARTITION P9 VALUES LESS THAN (10) ENGINE = InnoDB,
 PARTITION P10 VALUES LESS THAN (11) ENGINE = InnoDB,
 PARTITION P11 VALUES LESS THAN (12) ENGINE = InnoDB,
 PARTITION P12 VALUES LESS THAN (13) ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalCapture`
--

LOCK TABLES `PersonalCapture` WRITE;
/*!40000 ALTER TABLE `PersonalCapture` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalCapture` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalDevices`
--

DROP TABLE IF EXISTS `PersonalDevices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalDevices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Type` int(11) NOT NULL,
  `Community` char(32) DEFAULT NULL COMMENT 'ä¸ªäººç»ˆç«¯ç®¡ç†å‘˜',
  `Node` char(64) DEFAULT '',
  `Extension` int(11) DEFAULT NULL,
  `IPAddress` char(40) DEFAULT '',
  `Gateway` char(40) DEFAULT '',
  `SubnetMask` char(40) DEFAULT '',
  `PrimaryDNS` char(40) DEFAULT '',
  `SecondaryDNS` char(40) DEFAULT '',
  `MAC` char(32) DEFAULT NULL,
  `Firmware` char(32) DEFAULT '',
  `Hardware` char(32) DEFAULT '',
  `Status` tinyint(1) DEFAULT '0',
  `outerIP` varchar(40) DEFAULT '',
  `Port` int(10) unsigned zerofill DEFAULT NULL,
  `LastConnection` datetime DEFAULT NULL,
  `PrivatekeyMD5` char(32) DEFAULT '',
  `RfidMD5` char(32) DEFAULT '',
  `ConfigMD5` char(32) DEFAULT '',
  `ContactMD5` char(33) NOT NULL DEFAULT '',
  `SipAccount` char(64) DEFAULT '',
  `SipPwd` char(16) NOT NULL DEFAULT '',
  `RtspPwd` char(24) DEFAULT '' COMMENT 'å®¤å¤–æœºrtspç›‘æŽ§å¯†ç ',
  `Location` char(64) DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT NULL,
  `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT 'å…¬å…±è®¾å¤‡é»˜è®¤æ—¶é—´',
  `DclientVer` int(10) NOT NULL DEFAULT '0' COMMENT 'Dclientç‰ˆæœ¬å·ï¼Œç”¨æ¥åŒºåˆ†å®¢æˆ·ç«¯ç‰ˆæœ¬',
  `NetGroupNumber` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è®¾å¤‡çš„ç½‘ç»œç»„å·',
  `Flag` int(11) NOT NULL DEFAULT '0' COMMENT 'ä¸ªäººç±»åž‹è®¾å¤‡çš„æ ‡ç¤ºç¬¦ï¼Œä½è®¡ç®—ã€‚ç¬¬1ä½æ ‡ç¤ºè®¾å¤‡æ˜¯å¦ä¸ºä¸ªäººç»ˆç«¯ç®¡ç†å‘˜çš„ï¼Œç¬¬äºŒä½ä¹‹åŽæš‚æ—¶ä¿ç•™',
  `StairShow` tinyint(1) DEFAULT '0' COMMENT 'å…¬å…±è®¾å¤‡æ˜¾ç¤ºæ ¼å¼: 0:default 1:roomnum  2:roomnum/app/dev 3:app/dev',
  `AuthCode` char(20) DEFAULT '' COMMENT 'è®¾å¤‡æ ¡éªŒç ',
  `Relay` varchar(256) NOT NULL DEFAULT '#,Relay1,1,1,1' COMMENT 'æ ¼å¼: å¼€é—¨æŒ‰é”®,doorname,æ˜¯å¦æ˜¾ç¤ºåœ¨home,æ˜¯å¦æ˜¾ç¤ºåœ¨talking,æ˜¯å¦å¯ç”¨;å¤šä¸ªrelayä»¥é€—å·åˆ†éš”',
  `Config` varchar(2048) NOT NULL DEFAULT '' COMMENT 'æ‰‹åŠ¨æ–°å¢žçš„é…ç½®',
  `AccSrvID` char(24) NOT NULL DEFAULT '' COMMENT 'æŽ¥å…¥æœåŠ¡å™¨idçš„æ ‡ç¤º',
  `Arming` tinyint(1) DEFAULT '0' COMMENT 'å®¤å†…æœºarmingçŠ¶æ€',
  `SipType` tinyint(1) DEFAULT '1' COMMENT '0=udp  1=tcp 2=tls',
  `Flags` int(11) DEFAULT '8' COMMENT 'æŒ‰ä½æ ‡è¯† 1=home;2=away;3=sleep;4=ç®¡ç†æœºæ˜¯å¦å¼€å¯å…¨é€‰,é»˜è®¤å¼€å¯;5-8ä½ä»£è¡¨è®¾å¤‡relayçš„å¼€å…³æƒ…å†µ 0å…³1å¼€;9=å®¤å†…æœºä¸Šçº¿æ ‡è¯†;10=å®¤å†…æœºæ‰€å±žçš„å®¶åº­æ˜¯å¦ä¸ºKitæ–¹æ¡ˆ',
  `LastDisConn` datetime DEFAULT NULL COMMENT 'ä¸Šæ¬¡è®¾å¤‡æ–­çº¿æ—¶é—´',
  `FaceMD5` char(32) DEFAULT '',
  `UserMetaMD5` char(32) DEFAULT '',
  `ScheduleMD5` char(32) DEFAULT '',
  `SecurityRelay` varchar(256) NOT NULL DEFAULT '' COMMENT ': ,doorname,home,talking,;relay',
  PRIMARY KEY (`ID`),
  KEY `MAC` (`MAC`),
  KEY `SipAccount` (`SipAccount`),
  KEY `Community_Node` (`Community`,`Node`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalDevices`
--

LOCK TABLES `PersonalDevices` WRITE;
/*!40000 ALTER TABLE `PersonalDevices` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalDevices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalLogs`
--

DROP TABLE IF EXISTS `PersonalLogs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalLogs` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `DevicesID` int(10) DEFAULT NULL,
  `AccountID` int(10) DEFAULT NULL,
  `Type` tinyint(1) DEFAULT NULL,
  `Action` char(32) DEFAULT '',
  `Operator` char(32) DEFAULT '',
  `Time` timestamp NULL DEFAULT NULL,
  `Result` char(32) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalLogs`
--

LOCK TABLES `PersonalLogs` WRITE;
/*!40000 ALTER TABLE `PersonalLogs` DISABLE KEYS */;
INSERT INTO `PersonalLogs` VALUES (1,NULL,2,2,'add user:101 Slave1','16448cAN95N6Ei708','2022-02-16 06:43:20','Success');
/*!40000 ALTER TABLE `PersonalLogs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalMotion`
--

DROP TABLE IF EXISTS `PersonalMotion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalMotion` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` varchar(16) NOT NULL DEFAULT '',
  `SipAccount` char(16) DEFAULT '',
  `Location` varchar(64) DEFAULT '',
  `MngAccountID` int(11) DEFAULT '0',
  `MngType` int(11) DEFAULT '0' COMMENT '1æ˜¯ä¸ªäºº 0ç¤¾åŒº, ä¸ºäº†ä¸‹æ¬¡æ‹†åˆ†ä¸ªäººç¤¾åŒº',
  `DevType` int(11) DEFAULT '0' COMMENT '0 ä¸æ˜¯å…¬å…±è®¾å¤‡ 1æ˜¯å…¬å…±è®¾å¤‡',
  `PicName` char(80) NOT NULL COMMENT 'å›¾ç‰‡çš„åå­—,ç”±è®¾å¤‡ç«¯å†³å®š',
  `PicUrl` char(64) DEFAULT '' COMMENT 'å›¾ç‰‡çš„å®Œæ•´url',
  `SPicUrl` char(64) DEFAULT '' COMMENT 'å°å›¾çš„å®Œæ•´url',
  `CaptureTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦å·²è¯»çš„æ ‡å¿—,0:æœªè¯»; 1:å·²è¯»',
  `Node` char(32) DEFAULT '' COMMENT 'æ ‡è¯†ç”±é‚£ä¸ªè”åŠ¨æˆªå›¾,ç”¨äºŽå…¬å…±è®¾å¤‡',
  PRIMARY KEY (`ID`,`CaptureTime`),
  KEY `Node` (`Node`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `CaptureTime` (`CaptureTime`),
  KEY `MAC_PicName` (`MAC`,`PicName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
/*!50100 PARTITION BY RANGE (MONTH(CaptureTime))
(PARTITION P1 VALUES LESS THAN (2) ENGINE = InnoDB,
 PARTITION P2 VALUES LESS THAN (3) ENGINE = InnoDB,
 PARTITION P3 VALUES LESS THAN (4) ENGINE = InnoDB,
 PARTITION P4 VALUES LESS THAN (5) ENGINE = InnoDB,
 PARTITION P5 VALUES LESS THAN (6) ENGINE = InnoDB,
 PARTITION P6 VALUES LESS THAN (7) ENGINE = InnoDB,
 PARTITION P7 VALUES LESS THAN (8) ENGINE = InnoDB,
 PARTITION P8 VALUES LESS THAN (9) ENGINE = InnoDB,
 PARTITION P9 VALUES LESS THAN (10) ENGINE = InnoDB,
 PARTITION P10 VALUES LESS THAN (11) ENGINE = InnoDB,
 PARTITION P11 VALUES LESS THAN (12) ENGINE = InnoDB,
 PARTITION P12 VALUES LESS THAN (13) ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalMotion`
--

LOCK TABLES `PersonalMotion` WRITE;
/*!40000 ALTER TABLE `PersonalMotion` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalMotion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalPrivateKey`
--

DROP TABLE IF EXISTS `PersonalPrivateKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalPrivateKey` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `UnitID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¨å±€çš„å•å…ƒunit ID',
  `Grade` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è®¾å¤‡çš„å½’å±žç­‰çº§,1=ç¤¾åŒºå…±äº«,2=å•å…ƒå…±äº«,3=å®¶åº­ç‹¬å ...',
  `Type` tinyint(1) DEFAULT '0' COMMENT '0:ç¤¾åŒºç»ˆç«¯ç”¨æˆ·çš„card;1:ä¸ªäººç»ˆç«¯ç”¨æˆ·çš„card',
  `Code` char(20) DEFAULT NULL,
  `Status` tinyint(1) NOT NULL DEFAULT '0',
  `Node` char(32) DEFAULT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT NULL,
  `Access` varchar(4096) DEFAULT '',
  `AccountID` int(11) unsigned DEFAULT NULL COMMENT 'personal account id',
  `Special` tinyint(1) DEFAULT '0' COMMENT 'æ˜¯å¦æ˜¯åˆ›å»ºè´¦å·æ—¶å€™åˆ›å»ºçš„key',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `BeginTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT '00:00:00' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`),
  KEY `MngAccountID_Code` (`MngAccountID`,`Code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalPrivateKey`
--

LOCK TABLES `PersonalPrivateKey` WRITE;
/*!40000 ALTER TABLE `PersonalPrivateKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalPrivateKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalPrivateKeyList`
--

DROP TABLE IF EXISTS `PersonalPrivateKeyList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalPrivateKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT 'è®¾å¤‡çš„mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'PersonalPrivateKeyè¡¨id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalPrivateKeyList`
--

LOCK TABLES `PersonalPrivateKeyList` WRITE;
/*!40000 ALTER TABLE `PersonalPrivateKeyList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalPrivateKeyList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalRfcardKey`
--

DROP TABLE IF EXISTS `PersonalRfcardKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalRfcardKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `UnitID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¨å±€çš„å•å…ƒunit ID',
  `Grade` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è®¾å¤‡çš„å½’å±žç­‰çº§,1=ç¤¾åŒºå…±äº«,2=å•å…ƒå…±äº«,3=å®¶åº­ç‹¬å ...',
  `Type` tinyint(1) DEFAULT '0' COMMENT '0:ç¤¾åŒºç»ˆç«¯ç”¨æˆ·çš„card;1:ä¸ªäººç»ˆç«¯ç”¨æˆ·çš„card',
  `Code` char(20) DEFAULT NULL,
  `Status` tinyint(1) NOT NULL DEFAULT '0',
  `Node` char(32) DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT NULL,
  `Access` varchar(4096) DEFAULT '',
  `AccountID` int(11) unsigned DEFAULT NULL COMMENT 'account id',
  `Special` tinyint(1) DEFAULT '0' COMMENT 'æ˜¯å¦æ˜¯åˆ›å»ºè´¦å·æ—¶å€™åˆ›å»ºçš„key',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `BeginTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT '00:00:00' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`),
  KEY `MngAccountID_Code` (`MngAccountID`,`Code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalRfcardKey`
--

LOCK TABLES `PersonalRfcardKey` WRITE;
/*!40000 ALTER TABLE `PersonalRfcardKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalRfcardKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PersonalRfcardKeyList`
--

DROP TABLE IF EXISTS `PersonalRfcardKeyList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PersonalRfcardKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT 'è®¾å¤‡çš„mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'PersonalRfcardKeyè¡¨id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PersonalRfcardKeyList`
--

LOCK TABLES `PersonalRfcardKeyList` WRITE;
/*!40000 ALTER TABLE `PersonalRfcardKeyList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PersonalRfcardKeyList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PmAccountMap`
--

DROP TABLE IF EXISTS `PmAccountMap`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PmAccountMap` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AccountUUID` char(36) NOT NULL DEFAULT '' COMMENT 'AccountPMUUID',
  `PersonalAccountUUID` char(36) NOT NULL DEFAULT '' COMMENT 'PersonalAccountUUID',
  `PersonalAccount` varchar(64) NOT NULL DEFAULT '',
  `ProjectUUID` char(36) NOT NULL DEFAULT '' COMMENT 'UUID',
  `AppStatus` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'PM APP0- 1-',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `AccountUUID` (`AccountUUID`),
  KEY `ProjectUUID` (`ProjectUUID`),
  KEY `PersonalAccountUUID` (`PersonalAccountUUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='pm app';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PmAccountMap`
--

LOCK TABLES `PmAccountMap` WRITE;
/*!40000 ALTER TABLE `PmAccountMap` DISABLE KEYS */;
/*!40000 ALTER TABLE `PmAccountMap` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PmExportLog`
--

DROP TABLE IF EXISTS `PmExportLog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PmExportLog` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `PmID` int(10) unsigned DEFAULT '0' COMMENT 'PMçš„Account ID',
  `CommunityID` int(10) unsigned DEFAULT '0' COMMENT 'å°åŒºID',
  `TraceID` varchar(128) NOT NULL COMMENT 'å¯¼å‡ºæ—¥å¿—çš„å”¯ä¸€æ ‡è¯†',
  `LogType` tinyint(1) DEFAULT '0' COMMENT '1=DoorLog 2=Capture 3=TemperatureCapture',
  `ExportType` tinyint(1) DEFAULT '0' COMMENT '0=all 1=only_excel',
  `LastTime` timestamp NULL DEFAULT NULL COMMENT 'å¯¼å‡ºçš„æˆªè‡³æ—¶é—´',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'å¯¼å‡ºçš„æ—¶é—´,æ›´æ–°ä¹Ÿè¦å˜åŒ–',
  `DownloadUrl` varchar(512) DEFAULT '' COMMENT 'ä¸‹è½½çš„url',
  PRIMARY KEY (`ID`),
  KEY `TraceID` (`TraceID`),
  KEY `PmID_CommunityID_Log_Export_time` (`PmID`,`CommunityID`,`LogType`,`ExportType`,`LastTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PmExportLog`
--

LOCK TABLES `PmExportLog` WRITE;
/*!40000 ALTER TABLE `PmExportLog` DISABLE KEYS */;
/*!40000 ALTER TABLE `PmExportLog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PropertyBillingInfo`
--

DROP TABLE IF EXISTS `PropertyBillingInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PropertyBillingInfo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL DEFAULT '' COMMENT 'ç‰©ä¸šç®¡ç†å‘˜è´¦å·åç§°',
  `BillingTitle` char(128) NOT NULL COMMENT 'å…¬å¸åå­—æˆ–å®¶åº­åå­—',
  `Contactor` char(128) NOT NULL DEFAULT '',
  `Street` char(128) NOT NULL DEFAULT '' COMMENT 'è¡—é“',
  `City` char(64) NOT NULL DEFAULT '' COMMENT 'åŸŽå¸‚',
  `Postcode` char(32) DEFAULT '' COMMENT 'é‚®ç¼–',
  `Country` varchar(8) DEFAULT 'USA',
  `TelePhone` char(32) DEFAULT '',
  `Fax` char(32) DEFAULT '' COMMENT 'ä¼ çœŸ',
  PRIMARY KEY (`ID`),
  KEY `AccountID` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PropertyBillingInfo`
--

LOCK TABLES `PropertyBillingInfo` WRITE;
/*!40000 ALTER TABLE `PropertyBillingInfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `PropertyBillingInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PropertyInfo`
--

DROP TABLE IF EXISTS `PropertyInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PropertyInfo` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(10) unsigned NOT NULL,
  `FirstName` varchar(64) NOT NULL DEFAULT '',
  `LastName` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `AccountID` (`AccountID`),
  CONSTRAINT `PropertyInfo_ibfk_1` FOREIGN KEY (`AccountID`) REFERENCES `Account` (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PropertyInfo`
--

LOCK TABLES `PropertyInfo` WRITE;
/*!40000 ALTER TABLE `PropertyInfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `PropertyInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PropertyMngList`
--

DROP TABLE IF EXISTS `PropertyMngList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PropertyMngList` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `PropertyID` int(10) unsigned DEFAULT NULL,
  `CommunityID` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `PropertyID` (`PropertyID`),
  KEY `CommunityID` (`CommunityID`),
  CONSTRAINT `PropertyMngList_ibfk_1` FOREIGN KEY (`PropertyID`) REFERENCES `Account` (`ID`),
  CONSTRAINT `PropertyMngList_ibfk_2` FOREIGN KEY (`CommunityID`) REFERENCES `Account` (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PropertyMngList`
--

LOCK TABLES `PropertyMngList` WRITE;
/*!40000 ALTER TABLE `PropertyMngList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PropertyMngList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubAppTmpKey`
--

DROP TABLE IF EXISTS `PubAppTmpKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubAppTmpKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `WorkID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç‰©ä¸šid',
  `Code` int(11) NOT NULL,
  `BeginTime` timestamp NULL DEFAULT NULL,
  `EndTime` timestamp NULL DEFAULT NULL,
  `AccessTimes` int(11) DEFAULT '0',
  `AllowedTimes` int(11) DEFAULT NULL,
  `QrCodeUrl` varchar(128) DEFAULT '' COMMENT 'ä¸´æ—¶ç§˜é’¥äºŒç»´ç å›¾ç‰‡çš„å®Œæ•´url',
  `Description` varchar(64) DEFAULT '' COMMENT 'æè¿°',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:æ¯å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰æ¯å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `StartTime` time DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `Account` char(64) NOT NULL DEFAULT '' COMMENT 'åˆ›å»ºè€…è´¦å·',
  `IDNumber` varchar(128) NOT NULL DEFAULT '',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `PersonalAccountID` int(11) unsigned DEFAULT '0' COMMENT 'å±žäºŽå“ªä¸ªç”¨æˆ·',
  PRIMARY KEY (`ID`),
  KEY `WorkID` (`WorkID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubAppTmpKey`
--

LOCK TABLES `PubAppTmpKey` WRITE;
/*!40000 ALTER TABLE `PubAppTmpKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubAppTmpKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubAppTmpKeyList`
--

DROP TABLE IF EXISTS `PubAppTmpKeyList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubAppTmpKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT 'å…¬å…±è®¾å¤‡çš„mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¬å…±æ·»åŠ åˆ—è¡¨id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubAppTmpKeyList`
--

LOCK TABLES `PubAppTmpKeyList` WRITE;
/*!40000 ALTER TABLE `PubAppTmpKeyList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubAppTmpKeyList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubDevMngList`
--

DROP TABLE IF EXISTS `PubDevMngList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubDevMngList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `DevicesID` int(10) unsigned NOT NULL COMMENT 'Devicesè¡¨ID',
  `UnitID` int(10) unsigned NOT NULL COMMENT 'å•å…ƒè¡¨ID',
  PRIMARY KEY (`ID`),
  KEY `DevicesID` (`DevicesID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubDevMngList`
--

LOCK TABLES `PubDevMngList` WRITE;
/*!40000 ALTER TABLE `PubDevMngList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubDevMngList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubPrivateKey`
--

DROP TABLE IF EXISTS `PubPrivateKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubPrivateKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `WorkID` int(10) NOT NULL DEFAULT '0' COMMENT 'ç‰©ä¸šid',
  `Code` char(20) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `OwnerType` tinyint(4) DEFAULT '0' COMMENT 'æ‰€å±žç±»åž‹ 0ç‰©ä¸š 1å¿«é€’',
  `Name` varchar(64) DEFAULT '' COMMENT 'å¡åç§°',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’;2:å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `BeginTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT '00:00:00' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  PRIMARY KEY (`ID`),
  KEY `WorkID` (`WorkID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubPrivateKey`
--

LOCK TABLES `PubPrivateKey` WRITE;
/*!40000 ALTER TABLE `PubPrivateKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubPrivateKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubPrivateKeyList`
--

DROP TABLE IF EXISTS `PubPrivateKeyList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubPrivateKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT 'å…¬å…±è®¾å¤‡çš„mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¬å…±æ·»åŠ åˆ—è¡¨id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubPrivateKeyList`
--

LOCK TABLES `PubPrivateKeyList` WRITE;
/*!40000 ALTER TABLE `PubPrivateKeyList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubPrivateKeyList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubRfcardKey`
--

DROP TABLE IF EXISTS `PubRfcardKey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubRfcardKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(11) NOT NULL DEFAULT '0' COMMENT 'ç¤¾åŒºç®¡ç†å‘˜IDï¼Œå‚è§Accountè¡¨',
  `WorkID` int(11) NOT NULL DEFAULT '0' COMMENT 'ç‰©ä¸šid',
  `Code` char(20) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `OwnerType` tinyint(4) DEFAULT '0' COMMENT 'æ‰€å±žç±»åž‹ 0ç‰©ä¸š 1å¿«é€’',
  `Name` varchar(64) DEFAULT '' COMMENT 'å¡åç§°',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `BeginTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp NULL DEFAULT NULL COMMENT 'å•æ¬¡è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT '00:00:00' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  PRIMARY KEY (`ID`),
  KEY `WorkID` (`WorkID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubRfcardKey`
--

LOCK TABLES `PubRfcardKey` WRITE;
/*!40000 ALTER TABLE `PubRfcardKey` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubRfcardKey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PubRfcardKeyList`
--

DROP TABLE IF EXISTS `PubRfcardKeyList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PubRfcardKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT 'å…¬å…±è®¾å¤‡çš„mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'å…¬å…±æ·»åŠ åˆ—è¡¨id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PubRfcardKeyList`
--

LOCK TABLES `PubRfcardKeyList` WRITE;
/*!40000 ALTER TABLE `PubRfcardKeyList` DISABLE KEYS */;
/*!40000 ALTER TABLE `PubRfcardKeyList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ReleaseRomVersion`
--

DROP TABLE IF EXISTS `ReleaseRomVersion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ReleaseRomVersion` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `VersionID` int(11) NOT NULL,
  `MngID` int(11) NOT NULL COMMENT 'åŒºåŸŸç®¡ç†å‘˜ID',
  PRIMARY KEY (`ID`),
  KEY `MngID` (`MngID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ReleaseRomVersion`
--

LOCK TABLES `ReleaseRomVersion` WRITE;
/*!40000 ALTER TABLE `ReleaseRomVersion` DISABLE KEYS */;
/*!40000 ALTER TABLE `ReleaseRomVersion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RomVersion`
--

DROP TABLE IF EXISTS `RomVersion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `RomVersion` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Version` varchar(32) NOT NULL,
  `Model` varchar(32) NOT NULL,
  `Log` varchar(1024) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL,
  `FileSrc` varchar(64) DEFAULT '',
  `Url` varchar(1024) DEFAULT '' COMMENT 'å›ºä»¶ä¸‹è½½è·¯å¾„',
  `AllManage` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦é»˜è®¤æ‰€æœ‰ç®¡ç†å‘˜;0å…³é—­ï¼Œ1å¼€å¯',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Version` (`Version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RomVersion`
--

LOCK TABLES `RomVersion` WRITE;
/*!40000 ALTER TABLE `RomVersion` DISABLE KEYS */;
/*!40000 ALTER TABLE `RomVersion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SipGroup2`
--

DROP TABLE IF EXISTS `SipGroup2`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SipGroup2` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `SipGroup` char(16) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `SipGroup` (`SipGroup`),
  UNIQUE KEY `Account` (`Account`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SipGroup2`
--

LOCK TABLES `SipGroup2` WRITE;
/*!40000 ALTER TABLE `SipGroup2` DISABLE KEYS */;
INSERT INTO `SipGroup2` VALUES (1,'**********','**********');
INSERT INTO `SipGroup2` VALUES (2,'**********','**********');
INSERT INTO `SipGroup2` VALUES (3,'**********','**********');
INSERT INTO `SipGroup2` VALUES (4,'**********','**********');
/*!40000 ALTER TABLE `SipGroup2` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SipLastAccount`
--

DROP TABLE IF EXISTS `SipLastAccount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SipLastAccount` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `SipAccount` char(16) NOT NULL COMMENT 'ä¸Šæ¬¡æ·»åŠ çš„æœ€æ–°sip',
  `MngAccount` char(64) NOT NULL COMMENT 'å¯¹åº”åŒºåŸŸç®¡ç†å‘˜è´¦å·',
  `SipGroup` char(16) NOT NULL DEFAULT '' COMMENT 'ä¸Šæ¬¡æ·»åŠ çš„æœ€æ–°sipç¾¤ç»„',
  PRIMARY KEY (`ID`),
  KEY `MngAccount` (`MngAccount`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SipLastAccount`
--

LOCK TABLES `SipLastAccount` WRITE;
/*!40000 ALTER TABLE `SipLastAccount` DISABLE KEYS */;
INSERT INTO `SipLastAccount` VALUES (1,'**********','unittest_dis','**********');
/*!40000 ALTER TABLE `SipLastAccount` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SipPrefix`
--

DROP TABLE IF EXISTS `SipPrefix`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SipPrefix` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UUID` char(36) NOT NULL COMMENT 'å”¯ä¸€æ ‡è¯†',
  `CreateTime` datetime NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `UpdateUser` varchar(32) DEFAULT '',
  `AccountUUID` char(36) NOT NULL COMMENT 'Accountè¡¨uuid',
  `SipPrefix` int(11) NOT NULL DEFAULT '0' COMMENT 'å¯¹äºŽæ¯ä¸€çº§ç®¡ç†å‘˜ï¼Œè®°å½•åˆ°æœ¬çº§sipè§„åˆ™çš„å‰ç¼€',
  `Flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'æŒ‰ä½æ ‡è¯†:1=sipæ˜¯å¦ä½¿ç”¨ï¼Œ2=sipç¾¤ç»„æ˜¯å¦ä½¿ç”¨',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `SipPrefix` (`SipPrefix`),
  KEY `AccountUUID` (`AccountUUID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SipPrefix`
--

LOCK TABLES `SipPrefix` WRITE;
/*!40000 ALTER TABLE `SipPrefix` DISABLE KEYS */;
INSERT INTO `SipPrefix` VALUES (1,'d842a1a38e0c11ec96650242ac110001','2022-02-15 11:10:36','2022-02-15 11:10:36','','d830d1738e0c11ec96650242ac110001',6300,1);
/*!40000 ALTER TABLE `SipPrefix` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SipPrefixUnique`
--

DROP TABLE IF EXISTS `SipPrefixUnique`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SipPrefixUnique` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `SipPrefix` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `SipPrefix` (`SipPrefix`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SipPrefixUnique`
--

LOCK TABLES `SipPrefixUnique` WRITE;
/*!40000 ALTER TABLE `SipPrefixUnique` DISABLE KEYS */;
INSERT INTO `SipPrefixUnique` VALUES (1,6300);
/*!40000 ALTER TABLE `SipPrefixUnique` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SipReuse`
--

DROP TABLE IF EXISTS `SipReuse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SipReuse` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `SipAccount` char(64) DEFAULT '',
  `MngAccount` char(64) DEFAULT '0' COMMENT 'å¯¹åº”åŒºåŸŸç®¡ç†å‘˜çš„è´¦å·',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SipReuse`
--

LOCK TABLES `SipReuse` WRITE;
/*!40000 ALTER TABLE `SipReuse` DISABLE KEYS */;
/*!40000 ALTER TABLE `SipReuse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SmartHomeDeviceMap`
--

DROP TABLE IF EXISTS `SmartHomeDeviceMap`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SmartHomeDeviceMap` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MAC` char(12) NOT NULL,
  `SmartHomeUUID` char(33) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SmartHomeDeviceMap`
--

LOCK TABLES `SmartHomeDeviceMap` WRITE;
/*!40000 ALTER TABLE `SmartHomeDeviceMap` DISABLE KEYS */;
/*!40000 ALTER TABLE `SmartHomeDeviceMap` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SmartHomeManageMap`
--

DROP TABLE IF EXISTS `SmartHomeManageMap`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SmartHomeManageMap` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` varchar(32) NOT NULL COMMENT 'ç®¡ç†å‘˜è´¦å·',
  `SmartHomeUUID` char(33) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SmartHomeManageMap`
--

LOCK TABLES `SmartHomeManageMap` WRITE;
/*!40000 ALTER TABLE `SmartHomeManageMap` DISABLE KEYS */;
/*!40000 ALTER TABLE `SmartHomeManageMap` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SmartHomeUserCache`
--

DROP TABLE IF EXISTS `SmartHomeUserCache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SmartHomeUserCache` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` varchar(32) NOT NULL COMMENT 'ç»ˆç«¯ç”¨æˆ·è´¦å·',
  `LayoutName` varchar(128) NOT NULL,
  `CreateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SmartHomeUserCache`
--

LOCK TABLES `SmartHomeUserCache` WRITE;
/*!40000 ALTER TABLE `SmartHomeUserCache` DISABLE KEYS */;
/*!40000 ALTER TABLE `SmartHomeUserCache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SmartHomeUserMap`
--

DROP TABLE IF EXISTS `SmartHomeUserMap`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SmartHomeUserMap` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` varchar(32) NOT NULL COMMENT 'ç»ˆç«¯ç”¨æˆ·è´¦å·',
  `SmartHomeUUID` char(33) NOT NULL,
  `HomeID` char(33) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SmartHomeUserMap`
--

LOCK TABLES `SmartHomeUserMap` WRITE;
/*!40000 ALTER TABLE `SmartHomeUserMap` DISABLE KEYS */;
/*!40000 ALTER TABLE `SmartHomeUserMap` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Staff`
--

DROP TABLE IF EXISTS `Staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Staff` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` char(64) NOT NULL,
  `CommunityID` int(10) unsigned NOT NULL,
  `CardCode` char(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Version` int(10) unsigned DEFAULT '123456' COMMENT 'ç”¨æˆ·æ•°æ®ç‰ˆæœ¬å·',
  PRIMARY KEY (`ID`),
  KEY `Comm_Rf` (`CommunityID`,`CardCode`),
  KEY `Name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Staff`
--

LOCK TABLES `Staff` WRITE;
/*!40000 ALTER TABLE `Staff` DISABLE KEYS */;
/*!40000 ALTER TABLE `Staff` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `StaffAccess`
--

DROP TABLE IF EXISTS `StaffAccess`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `StaffAccess` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `StaffID` int(10) unsigned NOT NULL,
  `AccessGroupID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `StaffID` (`StaffID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `StaffAccess`
--

LOCK TABLES `StaffAccess` WRITE;
/*!40000 ALTER TABLE `StaffAccess` DISABLE KEYS */;
/*!40000 ALTER TABLE `StaffAccess` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SystemExtremum`
--

DROP TABLE IF EXISTS `SystemExtremum`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SystemExtremum` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MaxEntryFee` int(11) NOT NULL DEFAULT '2000',
  `MaxMonthlyFee` int(11) NOT NULL DEFAULT '2000',
  `MaxFeeApps` int(11) NOT NULL DEFAULT '2000',
  `MaxApps` int(11) NOT NULL DEFAULT '20',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='æ”¶è´¹ç³»ç»Ÿçš„æœ€å¤§ã€æœ€å°å€¼';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SystemExtremum`
--

LOCK TABLES `SystemExtremum` WRITE;
/*!40000 ALTER TABLE `SystemExtremum` DISABLE KEYS */;
INSERT INTO `SystemExtremum` VALUES (1,2000,2000,2000,63);
/*!40000 ALTER TABLE `SystemExtremum` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SystemSetting`
--

DROP TABLE IF EXISTS `SystemSetting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SystemSetting` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `SchemeVer` int(11) NOT NULL DEFAULT '0' COMMENT 'æ•°æ®åº“schemeç‰ˆæœ¬',
  `MacPoolLastID` int(11) unsigned DEFAULT '0' COMMENT 'æœ€æ–°çš„Mac Pool ID',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SystemSetting`
--

LOCK TABLES `SystemSetting` WRITE;
/*!40000 ALTER TABLE `SystemSetting` DISABLE KEYS */;
INSERT INTO `SystemSetting` VALUES (1,6401,0);
/*!40000 ALTER TABLE `SystemSetting` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Temperature`
--

DROP TABLE IF EXISTS `Temperature`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Temperature` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MAC` char(12) NOT NULL DEFAULT '',
  `MngAccountID` int(11) unsigned DEFAULT '0',
  `PicName` char(80) NOT NULL COMMENT 'å›¾ç‰‡çš„åå­—,ç”±è®¾å¤‡ç«¯å†³å®š',
  `PicUrl` char(64) DEFAULT '' COMMENT 'å›¾ç‰‡çš„å®Œæ•´url',
  `SPicUrl` char(64) DEFAULT '' COMMENT 'å°å›¾çš„å®Œæ•´url',
  `Fahrenheit` char(8) DEFAULT '' COMMENT 'ä½“æ¸©',
  `CaptureTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(1) NOT NULL COMMENT '0:Normal; 1:Abnormalï¼Œ2ï¼šLow',
  PRIMARY KEY (`ID`),
  KEY `CaptureTime` (`CaptureTime`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Temperature`
--

LOCK TABLES `Temperature` WRITE;
/*!40000 ALTER TABLE `Temperature` DISABLE KEYS */;
/*!40000 ALTER TABLE `Temperature` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ThirdParty`
--

DROP TABLE IF EXISTS `ThirdParty`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ThirdParty` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(10) unsigned NOT NULL COMMENT 'PMè´¦æˆ·ID',
  `CommunityID` int(10) unsigned NOT NULL COMMENT 'PMä¸‹çš„ç¤¾åŒºID',
  `AccessName` char(32) NOT NULL COMMENT 'æŽ¥å…¥åç§°:GsFace',
  `GsfaceLoginApi` char(64) NOT NULL COMMENT 'GsFaceç»™è®¾å¤‡è°ƒç”¨çš„åœ°å€',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ThirdParty`
--

LOCK TABLES `ThirdParty` WRITE;
/*!40000 ALTER TABLE `ThirdParty` DISABLE KEYS */;
/*!40000 ALTER TABLE `ThirdParty` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `TmpToken`
--

DROP TABLE IF EXISTS `TmpToken`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `TmpToken` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `WebToken` varchar(64) NOT NULL,
  `AppToken` varchar(128) NOT NULL,
  `EndTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `TmpToken`
--

LOCK TABLES `TmpToken` WRITE;
/*!40000 ALTER TABLE `TmpToken` DISABLE KEYS */;
/*!40000 ALTER TABLE `TmpToken` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ToBeDealOrder`
--

DROP TABLE IF EXISTS `ToBeDealOrder`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ToBeDealOrder` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `OrderNumber` char(32) NOT NULL,
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '[0=è¿›è¡Œä¸­ï¼Œ2=å®Œæˆï¼Œ3=å¼‚å¸¸]',
  `CreateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `OrderNumber` (`OrderNumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ToBeDealOrder`
--

LOCK TABLES `ToBeDealOrder` WRITE;
/*!40000 ALTER TABLE `ToBeDealOrder` DISABLE KEYS */;
/*!40000 ALTER TABLE `ToBeDealOrder` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ToBeDealSmartHome`
--

DROP TABLE IF EXISTS `ToBeDealSmartHome`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ToBeDealSmartHome` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `DataKey` varchar(33) NOT NULL COMMENT 'å…³é”®å­—',
  `Type` tinyint(1) NOT NULL COMMENT 'ç±»åž‹[æ·»åŠ dis,æ·»åŠ installerï¼Œæ·»åŠ å°åŒºï¼Œåˆ é™¤dis,åˆ é™¤installerï¼Œåˆ é™¤å°åŒºï¼Œæ·»åŠ å®¶åº­ä¸»ï¼Œæ·»åŠ å®¶åº­ä»Žï¼Œæ·»åŠ è®¾å¤‡ï¼Œåˆ é™¤å®¶åº­ä¸»ï¼Œåˆ é™¤å®¶åº­ä»Žï¼Œåˆ é™¤è®¾å¤‡ï¼Œé‡ç½®æ–½å·¥ä¿¡æ¯]',
  `Info` varchar(2048) DEFAULT '' COMMENT 'é¢å¤–ä¿¡æ¯',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '[è¿›è¡Œä¸­ï¼Œå®Œæˆï¼Œå®¶å±…äº‘å¤±è´¥, å¯¹è®²äº‘æ•°æ®ä¸å­˜åœ¨, å®¶å±…äº‘æˆåŠŸï¼Œä½†æ“ä½œæ•°æ®åº“å¼‚å¸¸]',
  `CreateTime` datetime DEFAULT NULL,
  `HandleTime` datetime DEFAULT NULL,
  `NextHandleTime` datetime DEFAULT NULL,
  `HandleTimes` tinyint(1) DEFAULT '0' COMMENT 'å¤„ç†æ¬¡æ•°',
  PRIMARY KEY (`ID`),
  KEY `Status_NextHandleTime_HandleTimes` (`Status`,`NextHandleTime`,`HandleTimes`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ToBeDealSmartHome`
--

LOCK TABLES `ToBeDealSmartHome` WRITE;
/*!40000 ALTER TABLE `ToBeDealSmartHome` DISABLE KEYS */;
/*!40000 ALTER TABLE `ToBeDealSmartHome` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Token`
--

DROP TABLE IF EXISTS `Token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Token` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `WebToken` varchar(64) NOT NULL,
  `AppToken` varchar(64) DEFAULT NULL,
  `AlexaToken` varchar(64) NOT NULL DEFAULT '',
  `AlexaAccessToken` varchar(512) DEFAULT NULL,
  `AlexaReflashToken` varchar(512) DEFAULT NULL,
  `AppTokenEt` int(10) unsigned NOT NULL DEFAULT '**********' COMMENT 'tokenæ—¶æ•ˆæœŸ',
  `AuthCode` char(32) DEFAULT NULL,
  `AppRefreshToken` char(32) DEFAULT NULL,
  `RefreshTokenEt` int(10) unsigned NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'token åˆ›å»ºçš„æ—¶é—´',
  `AuthToken` varchar(64) DEFAULT NULL COMMENT 'çŸ­ä¿¡ç™»é™†æ–¹å¼ä¸­ç”¨äºŽtokenç»­æ—¶çš„èº«ä»½æ ‡è¯†',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE KEY `AuthCode` (`AuthCode`),
  UNIQUE KEY `AppRefreshToken` (`AppRefreshToken`),
  UNIQUE KEY `AppToken` (`AppToken`),
  UNIQUE KEY `AuthToken` (`AuthToken`),
  KEY `AlexaToken` (`AlexaToken`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Token`
--

LOCK TABLES `Token` WRITE;
/*!40000 ALTER TABLE `Token` DISABLE KEYS */;
/*!40000 ALTER TABLE `Token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UpgradeRomDevices`
--

DROP TABLE IF EXISTS `UpgradeRomDevices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UpgradeRomDevices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `UpgradeRomVerID` int(10) NOT NULL,
  `MAC` char(20) DEFAULT '',
  `Status` tinyint(1) NOT NULL COMMENT '0:æœªå‡çº§, 1:å‡çº§å®Œæˆ',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UpgradeRomDevices`
--

LOCK TABLES `UpgradeRomDevices` WRITE;
/*!40000 ALTER TABLE `UpgradeRomDevices` DISABLE KEYS */;
/*!40000 ALTER TABLE `UpgradeRomDevices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UpgradeRomVersion`
--

DROP TABLE IF EXISTS `UpgradeRomVersion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UpgradeRomVersion` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Version` char(32) NOT NULL,
  `Status` tinyint(1) NOT NULL COMMENT '0:æœªå¼€å§‹, 1:å‡çº§ä¸­, 2:å‡çº§å®Œæˆ',
  `CreateTime` timestamp NULL DEFAULT NULL,
  `UpdateTime` timestamp NULL DEFAULT NULL,
  `OwnerAccount` char(32) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `Version` (`Version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UpgradeRomVersion`
--

LOCK TABLES `UpgradeRomVersion` WRITE;
/*!40000 ALTER TABLE `UpgradeRomVersion` DISABLE KEYS */;
/*!40000 ALTER TABLE `UpgradeRomVersion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserAccessGroup`
--

DROP TABLE IF EXISTS `UserAccessGroup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UserAccessGroup` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT 'ç”¨æˆ·è´¦å·',
  `SchedulerType` tinyint(4) unsigned NOT NULL COMMENT 'è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:å‘¨è®¡åˆ’',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `BeginTime` datetime NOT NULL DEFAULT '2021-01-01 00:00:00' COMMENT 'å•æ¬¡è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `EndTime` datetime NOT NULL DEFAULT '2299-01-01 00:00:00' COMMENT 'å•æ¬¡è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT '00:00:00' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT 'æ—¥ã€å‘¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserAccessGroup`
--

LOCK TABLES `UserAccessGroup` WRITE;
/*!40000 ALTER TABLE `UserAccessGroup` DISABLE KEYS */;
INSERT INTO `UserAccessGroup` VALUES (1,'**********',1,0,'0000-00-00 00:00:00','0000-00-00 00:00:00','00:00:00','23:59:59');
INSERT INTO `UserAccessGroup` VALUES (2,'**********',1,0,'0000-00-00 00:00:00','0000-00-00 00:00:00','00:00:00','23:59:59');
INSERT INTO `UserAccessGroup` VALUES (3,'**********',1,0,'0000-00-00 00:00:00','0000-00-00 00:00:00','00:00:00','23:59:59');
INSERT INTO `UserAccessGroup` VALUES (4,'**********',1,0,'0000-00-00 00:00:00','0000-00-00 00:00:00','00:00:00','23:59:59');
INSERT INTO `UserAccessGroup` VALUES (5,'**********',1,0,'0000-00-00 00:00:00','0000-00-00 00:00:00','00:00:00','23:59:59');
/*!40000 ALTER TABLE `UserAccessGroup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserAccessGroupDevice`
--

DROP TABLE IF EXISTS `UserAccessGroupDevice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UserAccessGroupDevice` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UserAccessGroupID` int(10) unsigned NOT NULL,
  `MAC` char(12) NOT NULL COMMENT 'è®¾å¤‡çš„mac',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT 'é»˜è®¤å…¨å¼€ï¼Œå³äºŒè¿›åˆ¶1111',
  `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `UserAccessGroup` (`UserAccessGroupID`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserAccessGroupDevice`
--

LOCK TABLES `UserAccessGroupDevice` WRITE;
/*!40000 ALTER TABLE `UserAccessGroupDevice` DISABLE KEYS */;
INSERT INTO `UserAccessGroupDevice` VALUES (1,2,'CE0000000009',3,0);
INSERT INTO `UserAccessGroupDevice` VALUES (2,5,'CE0000000009',3,0);
/*!40000 ALTER TABLE `UserAccessGroupDevice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserActivityUsed`
--

DROP TABLE IF EXISTS `UserActivityUsed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UserActivityUsed` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `PersonalAccountID` int(11) unsigned NOT NULL,
  `Activity` varchar(32) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `PersonalAccountID` (`PersonalAccountID`),
  KEY `Activity` (`Activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserActivityUsed`
--

LOCK TABLES `UserActivityUsed` WRITE;
/*!40000 ALTER TABLE `UserActivityUsed` DISABLE KEYS */;
/*!40000 ALTER TABLE `UserActivityUsed` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VerificationCode`
--

DROP TABLE IF EXISTS `VerificationCode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VerificationCode` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT 'ç”¨æˆ·è´¦å·',
  `Code` char(6) NOT NULL DEFAULT '' COMMENT 'éªŒè¯ç ',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'ç”Ÿæˆæ—¶é—´',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VerificationCode`
--

LOCK TABLES `VerificationCode` WRITE;
/*!40000 ALTER TABLE `VerificationCode` DISABLE KEYS */;
/*!40000 ALTER TABLE `VerificationCode` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VersionModel`
--

DROP TABLE IF EXISTS `VersionModel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VersionModel` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `VersionNumber` char(32) DEFAULT '',
  `VersionName` char(32) NOT NULL,
  `Type` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT 'æ ‡è¯†0æ¢¯å£æœº 1é—¨å£æœº 2å®¤å†…æœº 3ç®¡ç†æœºç­‰ç­‰',
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VersionModel`
--

LOCK TABLES `VersionModel` WRITE;
/*!40000 ALTER TABLE `VersionModel` DISABLE KEYS */;
INSERT INTO `VersionModel` VALUES (1,'110','E10',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (2,'20','R20',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (3,'26','R26',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (4,'27','R27',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (5,'29','R29',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (6,'80','IT80',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (7,'81','IT81',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (8,'82','IT82',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (9,'83','IT83',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (10,'113','C313v1',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (11,'115','C315',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (12,'47','R47',3,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (13,'48','R48',3,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (14,'117','C317',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (15,'21','E21',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (16,'28','R28',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (17,'227','R27-V2',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (18,'226','R26-V2',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (19,'221','E21-V2',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (20,'111','E11',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (21,'916','X916',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (22,'101','A01',50,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (23,'105','A05',50,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (24,'106','A06',50,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (25,'92','A092',50,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (26,'116','E16',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (27,'12','E12',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (28,'933','X933',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (29,'915','X915',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (30,'18','E18',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (31,'220','R20v2',1,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (32,'912','X912',0,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (33,'112','C312',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (34,'213','C313v2',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (35,'223','C313v2-2',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (36,'119','C319',2,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (37,'49','R49G',3,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (38,'102','A02',50,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (39,'103','A03',50,'2022-02-13 01:37:08');
INSERT INTO `VersionModel` VALUES (40,'107','A07',50,'2022-02-13 01:37:08');
/*!40000 ALTER TABLE `VersionModel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VideoLength`
--

DROP TABLE IF EXISTS `VideoLength`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VideoLength` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT '',
  `VideoLength` int(10) DEFAULT '0' COMMENT 'å½“å‰å½•åˆ¶æ—¶é•¿,å•ä½:s',
  `VideoCap` int(10) DEFAULT '18000' COMMENT 'è”åŠ¨ç³»ç»Ÿå…è®¸çš„å½•åˆ¶å®¹é‡,å•ä½:s',
  `VideoStorageTime` int(10) DEFAULT '604800' COMMENT 'è”åŠ¨ç³»ç»Ÿæœ€å¤§çš„è§†é¢‘å­˜å‚¨æ—¶é—´ï¼Œè¶…è¿‡è¯¥æ—¶é—´ï¼Œåˆ™å¹³å°éœ€è¦è‡ªåŠ¨åˆ é™¤æŽ‰è§†é¢‘,å•ä½:sï¼Œé»˜è®¤ä¸€å‘¨',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Node_key` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='è§†é¢‘å­˜å‚¨ç©ºé—´å ç”¨çŽ‡';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VideoLength`
--

LOCK TABLES `VideoLength` WRITE;
/*!40000 ALTER TABLE `VideoLength` DISABLE KEYS */;
/*!40000 ALTER TABLE `VideoLength` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VideoList`
--

DROP TABLE IF EXISTS `VideoList`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VideoList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT '',
  `MAC` char(16) DEFAULT '',
  `VideoLength` int(10) DEFAULT '0' COMMENT 'å½•åˆ¶æ—¶é•¿,å•ä½:s',
  `VideoTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'å½•åˆ¶æ—¶é—´ç‚¹ï¼Œæ ¼å¼:YYYY-MM-DD HH:MM:SS',
  `VideoUri` char(128) DEFAULT '',
  `VideoUid` int(10) DEFAULT '0' COMMENT 'è§†é¢‘å­˜å‚¨æœåŠ¡å™¨ä¸­çš„å…¨å±€ID',
  PRIMARY KEY (`ID`),
  KEY `Node_key` (`Node`),
  KEY `MAC_key` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='è§†é¢‘å­˜å‚¨åˆ—è¡¨';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VideoList`
--

LOCK TABLES `VideoList` WRITE;
/*!40000 ALTER TABLE `VideoList` DISABLE KEYS */;
/*!40000 ALTER TABLE `VideoList` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VideoSchedule`
--

DROP TABLE IF EXISTS `VideoSchedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VideoSchedule` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT '',
  `MAC` char(16) DEFAULT '',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT 'åªæœ‰æ¯å‘¨è®¡åˆ’ä½¿ç”¨,å¯èƒ½çš„é€‰æ‹©å¦‚ä¸‹: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};ä»Žå‘¨æ—¥å¼€å§‹ï¼Œåˆ°å‘¨å…­ç»“æŸ',
  `StartDay` date DEFAULT NULL COMMENT 'åªæœ‰å•æ¬¡è®¡åˆ’ä½¿ç”¨,å­˜å‚¨è®¡åˆ’çš„å¼€å§‹æ—¥æœŸç‚¹,æ ¼å¼:YYYY-MM-DD',
  `StopDay` date DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„ç»“æŸæ—¥æœŸç‚¹,æ ¼å¼:åŒä¸Š',
  `StartTime` time DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„å¼€å§‹æ—¶é—´ç‚¹,æ ¼å¼:HH:MM:SS',
  `StopTime` time DEFAULT NULL COMMENT 'å­˜å‚¨è®¡åˆ’çš„ç»“æŸæ—¶é—´ç‚¹,æ ¼å¼:åŒä¸Š',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'è§†é¢‘å®šæ—¶å½•åˆ¶è®¡åˆ’çš„ç±»åž‹,0:å•æ¬¡è®¡åˆ’; 1:æ¯æ—¥è®¡åˆ’ï¼›2:æ¯å‘¨è®¡åˆ’',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'æ˜¯å¦å·²å¤±æ•ˆçš„æ ‡å¿—,0:æœªå¤±æ•ˆ; 1:å·²å¤±æ•ˆ',
  `UID` int(11) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `Node_key` (`Node`),
  KEY `MAC_key` (`MAC`),
  KEY `UID_key` (`UID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='è§†é¢‘å­˜å‚¨è®¡åˆ’';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VideoSchedule`
--

LOCK TABLES `VideoSchedule` WRITE;
/*!40000 ALTER TABLE `VideoSchedule` DISABLE KEYS */;
/*!40000 ALTER TABLE `VideoSchedule` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-05-17 14:54:33
