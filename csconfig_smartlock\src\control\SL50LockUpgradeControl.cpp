#include "AkLogging.h"
#include "ConfigDef.h"
#include "AK.Route.pb.h"
#include "SnowFlakeGid.h"
#include "RouteMqProduce.h"
#include "dbinterface/SmartLock.h"
#include "SL50LockUpgradeControl.h"
#include "dbinterface/SmartLockUpgrade.h"
#include "SL50/DownMessage/StartUpgrade.h"
#include "SL50/DownMessage/RequestUpgrade.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
extern RouteMQProduce* g_nsq_producer;

int SL50LockUpgradeControl::NotifyRequestUpgrade(const std::string& lock_uuid)
{
    SmartLockUpgradeInfo smartlock_upgrade_info;
    dbinterface::SmartLock::GetSmartLockInfoByUUID(lock_uuid, smart_lock_info);

    std::string trace_id = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    RequestUpgrade request_upgrade(smartlock_upgrade_info.uuid, smartlock_upgrade_info.upgrade_module_version);

    AK::Route::P2PRoutLockMessage p2p_route_lock_message;
    p2p_route_lock_message.set_trace_id(trace_id);
    p2p_route_lock_message.set_client_id(lock_uuid);
    p2p_route_lock_message.set_json_message(request_upgrade.to_json());
    p2p_route_lock_message.set_akcs_command(RequestUpgrade::AKCS_COMMAND);
    
    AK_LOG_INFO << "Send NotifyRequestUpgrade msg:" << p2p_route_lock_message.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&p2p_route_lock_message);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_R2S_P2P_SMARTLOCK_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}

int SL50LockUpgradeControl::NotifyStartUpgrade(const std::string& lock_uuid)
{
    SmartLockUpgradeInfo smartlock_upgrade_info;
    dbinterface::SmartLock::GetSmartLockInfoByUUID(lock_uuid, smart_lock_info);

    std::string trace_id = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());

    StartUpgrade start_upgrade(lock_uuid);

    AK::Route::P2PRoutLockMessage p2p_route_lock_message;
    p2p_route_lock_message.set_trace_id(trace_id);
    p2p_route_lock_message.set_client_id(lock_uuid);
    p2p_route_lock_message.set_json_message(request_upgrade.to_json());
    p2p_route_lock_message.set_akcs_command(RequestUpgrade::AKCS_COMMAND);
    
    AK_LOG_INFO << "Send NotifyStartUpgrade msg:" << p2p_route_lock_message.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&p2p_route_lock_message);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_R2S_P2P_SMARTLOCK_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}