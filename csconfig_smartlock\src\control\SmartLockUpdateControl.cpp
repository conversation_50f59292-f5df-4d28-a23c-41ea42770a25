#include "SmartLockUpdateControl.h"
#include "SL20/SL20LockConfigHandle.h"
#include "SL50/SL50LockConfigHandle.h"
#include "SmartLockConfigHandle.h"
#include <unistd.h>
#include <memory>
#include "MsgIdToMsgName.h"
#include "SL50LockUpgradeControl.h"

CSmartLockUpdateControl::CSmartLockUpdateControl()
{

}

CSmartLockUpdateControl::~CSmartLockUpdateControl()
{

}

CSmartLockUpdateControl* CSmartLockUpdateControl::instance = NULL;

CSmartLockUpdateControl* CSmartLockUpdateControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CSmartLockUpdateControl();
    }

    return instance;
}
CSmartLockUpdateControl* GetSmartLockUpdateContorlInstance()
{
    return CSmartLockUpdateControl::GetInstance();
}

//配置文件更新
void CSmartLockUpdateControl::SmartLockConfigUpdateHandle(int changetype, const std::string& lock_uuid, 
                                                        const std::string& node, int project_type, int mng_id)
{
    AK_LOG_INFO << "SmartLockConfigUpdateHandle change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype) << " lock uuid=" << lock_uuid;

    std::unique_ptr<SmartLockConfigHandle> smartlock_handle;

    switch(changetype)
    {
        // 更新数据库影子文件+保活情况下通知锁
        case SMARTLOCK_CONFIG_UPDATE:
        {
            SmartLockUpdateManager::UpdateSmartLock(lock_uuid, node, project_type, mng_id);
            break;
        }
        case SMARTLOCK_NODE_CONFIG_UPDATE:
        {
            SmartLockUpdateManager::UpdateNodeSmartLocks(node, project_type, mng_id);
            break;
        }
        case SMARTLOCK_PROJECT_CONFIG_UPDATE:
        {
            SmartLockUpdateManager::UpdateCommunitySmartLocks(mng_id);
            break;
        }
        case SMARTLOCK_SL20_LOCK_KEEP_ALIVE_SWITCH_CHANGE:
        {
            // 保活开关从开到关的情况，需要推一条消息给锁
            bool is_force_notify = true;
            SmartLockUpdateManager::UpdateSmartLock(lock_uuid, node, project_type, mng_id, is_force_notify);
            break;
        }
        case SMARTLOCK_SL50_LOCK_BASE_CONFIG_UPDATE:
        {
            SmartLockUpdateManager::UpdateSL50LockBaseConfig(lock_uuid, node, project_type, mng_id);
            break;
        }
        case SMARTLOCK_SL50_LOCK_REQUEST_UPGRADE:
        {
            SL50LockUpgradeControl::NotifyRequestUpgrade(lock_uuid);
            break;
        }
        case SMARTLOCK_SL50_LOCK_START_UPGRADE:
        {
            SL50LockUpgradeControl::NotifyStartUpgrade(lock_uuid);
            break;
        }
        default:
        {
            AK_LOG_WARN << "not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);            
        }
    }

    return;
}