CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

project (csconfwatch  CXX)
SET(CSBASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libglog.so libevpp.so libetcd-cpp-api.so libgpr.so libgrpc.so libgrpc++.so libboost_system.so libssl.so libcrypto.so libcpprest.so)

AUX_SOURCE_DIRECTORY(./src SRC_LIST_PRODUCER)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/encrypt SRC_LIST_ENCRYPT)

LINK_DIRECTORIES(${CSBASE_DIR} ${CSBASE_DIR}/thirdlib ${CSBASE_DIR}/evpp/lib /usr/local/lib)


SET(BASE_LIST_INC ${CSBASE_DIR} ${CSBASE_DIR}/evpp ${CSBASE_DIR}/grpc/gens ${CSBASE_DIR}/grpc ${CSBASE_DIR}/etcd ${CSBASE_DIR}/encrypt)

ADD_DEFINITIONS( -std=c++11 -g -W -Wall -Wno-unused-parameter -Wno-deprecated -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories( ${BASE_LIST_INC} ./src /usr/local/boost/include /usr/local/grpc/include /usr/local/protobuf/include)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

add_executable(csconfwatch ${SRC_LIST_PRODUCER} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_ENCRYPT})

set_target_properties(csconfwatch PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csconfwatch/lib")
target_link_libraries(csconfwatch  ${DEPENDENT_LIBRARIES})
