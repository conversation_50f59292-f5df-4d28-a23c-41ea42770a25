#!/bin/bash
ACMD="$1"
csconfwatch_BIN='/usr/local/akcs/csconfwatch/bin/csconfwatch'
PROCESS_PID_FILE=/var/run/csconfwatch.pid
if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

if [ -f $PROCESS_PID_FILE ];then
    pid=`cat $PROCESS_PID_FILE`
else
    #重启之后没有这个pid文件
    pid="xxxxxxxxxx"
fi

start_csconfwatch()
{
    nohup $csconfwatch_BIN >/dev/null 2>&1 &
    echo "Start csconfwatch successful"
    if [ -z "`ps -fe|grep "csconfwatchrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csconfwatch/scripts/csconfwatchrun.sh >/dev/null 2>&1 &
    fi
}
stop_csconfwatch()
{
    echo "Begin to stop csconfwatchrun.sh"
    kill -9 `ps aux | grep -w csconfwatchrun.sh | grep -v grep | awk '{ print $(2) }'`
    echo "Begin to stop csconfwatch"
    kill -9 `pidof csconfwatch`
    sleep 2
    echo "Stop csconfwatch successful"
}

case $ACMD in
  start)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csconfwatch
    else
        echo "csconfwatch is already running"
    fi
    ;;
  stop)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csconfwatch is already stopping"
    else
        stop_csconfwatch
    fi
    ;;
  restart)
    stop_csconfwatch
    sleep 1
    start_csconfwatch
    ;;
  status)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csconfwatch is stop!!!\033[0m"
    else
        echo "\033[0;32m csconfwatch is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

