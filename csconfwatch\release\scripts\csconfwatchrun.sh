#!/bin/bash
PROCESS_NAME=csconfwatch
PROCESS_START_CMD="/usr/local/akcs/csconfwatch/scripts/csconfwatchctl.sh start"
PROCESS_PID_FILE=/var/run/csconfwatch.pid
LOG_FILE=/var/log/csconfwatch_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csconfwatch/scripts/common.sh"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done
