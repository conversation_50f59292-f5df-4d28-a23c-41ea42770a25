#ifndef __CONF_WATCH_DEFINE_H__
#define __CONF_WATCH_DEFINE_H__


#define ETCD_WATCH_CONF_FILEPATH        "/usr/local/akcs/csconfwatch/conf/csconfwatch.conf"
#define CONF_WATCH_PID                  "/var/run/csconfwatch.pid" 


typedef struct ETCD_WATCH_CONF_T
{
    char etcd_server[128];
    char db_master_ip[64];
    char db_ip[64];
    int db_master_port;
    int db_port;
    char smg_addr[128];
    char web_adapt_entry[128];
}ETCD_WATCH_CONF;

#endif// __CONF_WATCH_DEFINE_H__

