#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include <string>
#include <iostream>
#include <sstream>
#include "util.h"
#include "conf_watch_define.h"
#include "ConfigFileReader.h"
#include "AkLogging.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"

CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_conf_db_master_addr;
extern const char *g_conf_db_addr;
extern const char *g_ak_srv_freeswitch;
extern const char *g_ak_srv_smg;
extern const char *g_ak_srv_web_adapt_entry;

int g_etcd_dns_res = 0;
const char* watch_exec = "/bin/bash /usr/local/akcs/csconfwatch/scripts/watch_exec.sh";

ETCD_WATCH_CONF g_etcd_watch_conf;
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(CONF_WATCH_PID, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

void WatchExec(const char* key)
{
    std::string result;
    char cmd[128];
    snprintf(cmd, sizeof(cmd), "%s %s %s", watch_exec, g_etcd_watch_conf.etcd_server, key);
    AkSystem(cmd, result);
    AK_LOG_INFO << "exec result: " << result;
}

void UpdateOuterConfFromConfSrv()
{
    AK_LOG_INFO << "etcd watch callback";
    
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(SrvDbConf));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_master_ip, g_etcd_watch_conf.db_master_ip) != 0) || (conf_tmp.db_master_port != g_etcd_watch_conf.db_master_port))
    {
        AK_LOG_INFO << g_conf_db_master_addr << " conf really change";
        Snprintf(g_etcd_watch_conf.db_master_ip, sizeof(g_etcd_watch_conf.db_master_ip),  conf_tmp.db_master_ip);
        g_etcd_watch_conf.db_master_port = conf_tmp.db_master_port;        
        WatchExec(g_conf_db_master_addr);        
    }
    
    if((::strcmp(conf_tmp.db_ip, g_etcd_watch_conf.db_ip) != 0) || (conf_tmp.db_port != g_etcd_watch_conf.db_port))
    {      
        AK_LOG_INFO << g_conf_db_addr << " conf really change";
        Snprintf(g_etcd_watch_conf.db_ip, sizeof(g_etcd_watch_conf.db_ip),  conf_tmp.db_ip);
        g_etcd_watch_conf.db_port = conf_tmp.db_port;        
        WatchExec(g_conf_db_addr);        
    }
    
}

void UpdateSmgConfFromConfSrv()
{
    AK_LOG_INFO << "etcd watch callback";

    std::string tmpconf_smg_addr;
    g_etcd_cli_mng->LoadSrvSmgConf(tmpconf_smg_addr);

    if (::strcmp(g_etcd_watch_conf.smg_addr, tmpconf_smg_addr.c_str()) != 0)
    {
        AK_LOG_INFO << g_ak_srv_smg << "conf really change";
        Snprintf(g_etcd_watch_conf.smg_addr, sizeof(g_etcd_watch_conf.smg_addr), tmpconf_smg_addr.c_str());
        WatchExec(g_ak_srv_smg);
    }
}

void UpdateWebAdaptEntrySrv()
{
    std::string web_adapt_entry_key;
    std::string web_adapt_entry_addr;
    g_etcd_cli_mng->LoadSrvWebAdaptEntryConf(web_adapt_entry_key, web_adapt_entry_addr);

    if (::strcmp(g_etcd_watch_conf.web_adapt_entry, web_adapt_entry_addr.c_str()) != 0)
    {
        AK_LOG_INFO << web_adapt_entry_key << "conf really change to " << web_adapt_entry_addr;
        Snprintf(g_etcd_watch_conf.web_adapt_entry, sizeof(g_etcd_watch_conf.web_adapt_entry), web_adapt_entry_addr.c_str());
        WatchExec(web_adapt_entry_key.c_str());
    }
}

void UpdateOpensipsLoadbalanceFromConfSrv()
{
    WatchExec(g_ak_srv_freeswitch);
}

int ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_watch_conf.etcd_server);
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_master_addr, UpdateOuterConfFromConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_freeswitch, UpdateOpensipsLoadbalanceFromConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_smg, UpdateSmgConfFromConfSrv);
    
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(SrvDbConf));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);

    Snprintf(g_etcd_watch_conf.db_master_ip, sizeof(g_etcd_watch_conf.db_master_ip),  conf_tmp.db_master_ip);
    g_etcd_watch_conf.db_master_port = conf_tmp.db_master_port;
    Snprintf(g_etcd_watch_conf.db_ip, sizeof(g_etcd_watch_conf.db_ip),  conf_tmp.db_ip);
    g_etcd_watch_conf.db_port = conf_tmp.db_port;

    if(g_etcd_watch_conf.db_port == 0)
    {
        return -1;
    }
    
    std::string tmpconf_smg_addr;
    g_etcd_cli_mng->LoadSrvSmgConf(tmpconf_smg_addr);
    Snprintf(g_etcd_watch_conf.smg_addr, sizeof(g_etcd_watch_conf.smg_addr), tmpconf_smg_addr.c_str());

    std::string web_adapt_entry_key;
    std::string web_adapt_entry_addr;
    g_etcd_cli_mng->LoadSrvWebAdaptEntryConf(web_adapt_entry_key, web_adapt_entry_addr);
    Snprintf(g_etcd_watch_conf.web_adapt_entry, sizeof(g_etcd_watch_conf.web_adapt_entry), web_adapt_entry_addr.c_str());
    return 0;
}

void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void ConfWatchTimer()
{
    do
    {
    	UpdateWebAdaptEntrySrv(); 
    	sleep(30);
    } while(true);
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;
    for (auto &ip : addrs)
    {
        //更新为ip 然后进入etcd连接。要用ip保证可以马上连上etcd进行注册信息
        snprintf(g_etcd_watch_conf.etcd_server, sizeof(g_etcd_watch_conf.etcd_server), "%s:8507", ip.c_str());
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        AK_LOG_INFO << "new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}
void DnsResolver()
{
    CConfigFileReader config_file(ETCD_WATCH_CONF_FILEPATH);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(g_etcd_watch_conf.etcd_server, sizeof(g_etcd_watch_conf.etcd_server),  config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = g_etcd_watch_conf.etcd_server;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(g_etcd_watch_conf.etcd_server, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}

void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csconfwatchlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csconfwatchlog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csconfwatchlog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csconfwatchlog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 10;    //单日志文件最大10M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

int main(int argc, char* argv[])
{

    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        printf("another etcdwatch has been running in this sytem.");
        return -1;
    }

    glogInit(argv[0]);

    //配置中心连接初始化
    memset(&g_etcd_watch_conf, 0, sizeof(ETCD_WATCH_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }   
    if(ConfSrvInit() != 0)
    {
        AK_LOG_WARN << "ConfSrvInit error, exit!!!";
        return -1;
    }

    std::thread conf_watch_thread = std::thread(ConfWatch);

    std::thread conf_watch_timer_thread = std::thread(ConfWatchTimer);
    
    AK_LOG_INFO << "csconfwatch is starting";

    conf_watch_thread.join();    
    glogClean();
    return 0;
}




