<?php
require_once(dirname(__FILE__).'/medoo.php');
require_once(dirname(__FILE__).'/data_confusion.php');

function getMedooDb() {
    $database = new Medoo([
        // 必须的
        'database_type' => 'mysql',
        'database_name' => 'AKCS',
        'server' => "172.18.41.221",
        'username' => 'dbuer01',
        'password' => "Ak@56@<EMAIL>",
        'charset' => 'utf8',
        'port' => 3308,
    ]);
    return $database;
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

function getExpirePersonalAccountInfo()
{
    global $db;
    global $medooDb;

    $before = 1;

    $expireList =  $medooDb->select("PersonalAccount", ["Account","Name","UserInfoUUID","ID","ParentID","Language","ParentUUID","UUID"], 
                    Medoo::raw("where TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before and Role = 20 and Active = 1 and Special = 0", [':before' => $before]));

    echo "getExpirePersonalAccountInfo expire_list = " . json_encode($expireList) . "\n";

    echo "==============================================================================================\n";

    $sth = $db->prepare("select Account,Name,UserInfoUUID,ID,ParentID,Language,ParentUUID,UUID from PersonalAccount where (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and Role = 20 and Active = 1 and Special = 0");
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);

    echo "getExpirePersonalAccountInfo expire_list = " . json_encode($expireList) . "\n";
}

function getPMAccountEmailByAccountUUID()
{
    global $db;
    global $medooDb;

    $accountUUID = "dv-b8201e60005711ef90f800163e047e78";

    $email = $medooDb->get("AccountUserInfo",
        [
            "[>]AccountMap" => ["AccountUserInfo.UUID" => "UserInfoUUID"]
        ],[
            "AccountUserInfo.Email"
        ],[
           "AccountMap.AccountUUID" => $accountUUID
        ])['Email'];

    echo "getPMAccountEmailByAccountUUID email = $email \n";

    echo "==============================================================================================\n";

    $sth = $db->prepare("select A.Email from AccountUserInfo A join AccountMap B on A.UUID = B.UserInfoUUID where B.AccountUUID=:uuid");
    $sth->bindParam(':uuid', $accountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    echo "getPMAccountEmailByAccountUUID email = {$result['Email']} \n";
}

function getDisEmailByDisAccount($account)
{
    global $db;
    global $medooDb;

    $email = $medooDb->get("InstallerBillingInfo", "Email", ["Account" => $account]);


    $sth = $db->prepare("select Email from InstallerBillingInfo where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['Email'];

}


function test()
{
    getExpirePersonalAccountInfo();

    echo "==============================================================================================\n";

    getPMAccountEmailByAccountUUID();

    //testGetPersonalEmailByUUID($db, $medooDb);
    //testGetPersonalUserInfoByUUID($db, $medooDb);
    //testGetExpirePersonalAccountInfo($db, $medooDb);
    //testGetLandlineWillExpireEmailStr($db, $medooDb);
    //testGetInstallerEmailInfoByInsID($db, $medooDb);
    //testGetDisEmailByDisAccount($db, $medooDb);
    //testGetCommExpirePmAppList($db, $medooDb);
    //testGetOfficeAppExpireList();
    //testGetOfficeAppWillExpireList($db, $medooDb);
    //testGetWillChargeOrderList($db, $medooDb);
    //testGetSingleAutoPayUsers($db, $medooDb);
    //testGetPayerEmailInfoByUUID($db, $medooDb);
    //testGetPMAccountEmailByAccountUUID($db, $medooDb);
}

test();