CMAKE_MINIMUM_REQUIRED(VERSION 3.6)
set (CMAKE_CXX_STANDARD 11)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

PROJECT (facecut C CXX)
SET(THIRDLIB_DIR ${PROJECT_SOURCE_DIR}/thirdlib)
SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libevent.so libhiredis.a libglog.so libmysqlclient.so libgpr.so libgrpc.so 
	libgrpc++.so libprotobuf.so libevpp.so -lcurl -lssl -lcrypto -lcpprest -letcd-cpp-api  -levpp -levent -lboost_system 
	facesdk opencv_highgui opencv_imgproc opencv_core zlib libjasper libjpeg libpng ncnn 
	libaws-cpp-sdk-core.so libaws-cpp-sdk-s3.so fdfsclient fastcommon)
SET(BASE_LIST_INC   ${BASE_SOURCE_DIR}/ 
					${BASE_SOURCE_DIR}/mysql/include 
					${BASE_SOURCE_DIR}/Rldb 
					${BASE_SOURCE_DIR}/redis 
					${BASE_SOURCE_DIR}/grpc 
					${BASE_SOURCE_DIR}/grpc/gens 
					${BASE_SOURCE_DIR}/grpc/include  
					${BASE_SOURCE_DIR}/etcd 
					${BASE_SOURCE_DIR}/evpp 
					${BASE_SOURCE_DIR}/jsoncpp0.5/include 
					${BASE_SOURCE_DIR}/encrypt 
					${BASE_SOURCE_DIR}/oss 
					${BASE_SOURCE_DIR}/oss/include
					${BASE_SOURCE_DIR}/metrics 
					)

AUX_SOURCE_DIRECTORY(./src SRC_LIST_FACECUT)
AUX_SOURCE_DIRECTORY(./src/face_detect SRC_LIST_FACE_DETECT)
AUX_SOURCE_DIRECTORY(../csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(../csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(../csbase/etcd SRC_LIST_BASE_ETCD)

AUX_SOURCE_DIRECTORY(../csbase/jsoncpp0.5/src SRC_LIST_JSON)
AUX_SOURCE_DIRECTORY(../csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(../csbase/metrics SRC_LIST_BASE_METRICS)

INCLUDE_DIRECTORIES(${BASE_LIST_INC} /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include 
					${THIRDLIB_DIR}/facesdk_open/include/ ${THIRDLIB_DIR}/opencv/include/ ${THIRDLIB_DIR}/ncnn/include/ncnn/
					${BASE_SOURCE_DIR}/uploader ${BASE_SOURCE_DIR}/fdfs_client/fdfsclient ${BASE_SOURCE_DIR}/fdfs_client/libfdfscomm)

LINK_DIRECTORIES(${BASE_SOURCE_DIR} ${BASE_SOURCE_DIR}/thirdlib/ ${BASE_SOURCE_DIR}/redis/hiredis/ ${BASE_SOURCE_DIR}/evpp/lib/ 
				${THIRDLIB_DIR}/facesdk_open/lib ${THIRDLIB_DIR}/opencv/lib/ ${THIRDLIB_DIR}/ncnn/lib/ 
				/usr/local/lib/ ${BASE_SOURCE_DIR}/thirdlib/oss)

			ADD_DEFINITIONS( -std=gnu++11 -g2 -Werror -Wno-unused-parameter -Wno-deprecated -Wno-error=format-truncation)

ADD_EXECUTABLE(csfacecut ${SRC_LIST_FACECUT} ${SRC_LIST_FACE_DETECT} 
	${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_JSON} ${SRC_LIST_BASE_METRICS}
	${BASE_SOURCE_DIR}/uploader/fdfs_uploader.cpp 
	${SRC_LIST_BASE_ENCRYPT})


SET_TARGET_PROPERTIES(csfacecut PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csfacecut/lib")

TARGET_LINK_LIBRARIES(csfacecut  ${DEPENDENT_LIBRARIES})
