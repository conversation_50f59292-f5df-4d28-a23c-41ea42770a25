#!/bin/bash

# ****************************************************************************
# Author        :   jianhong.weng
# Last modified :   2024-07-03
# Filename      :   install.sh
# Version       :   V1.0.0.0
# Description   :   csfacecut 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
#项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME=csfacecut

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
APP_NAME=csfacecut                                  # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csfacecut
RUN_SCRIPT=csfacecutrun.sh
CTRL_SCRIPT=csfacecutctl.sh
LOG_PATH=/var/log/csfacecutlog

SIGNAL=${SIGNAL:-TERM}
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

DIRS=(
    "/usr/local/akcs/csfacecut/data/images"
)

for dir in "${DIRS[@]}"
do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        chown nobody:nogroup -R "$dir"
    fi
done

ENV_CONF_PARAM="
-e SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
-e FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
-e FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
"

if [ `docker ps -a | grep -w $CONTAINER_NAME | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 $APP_NAME"
    app_pids=$(pidof csfacecut || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi
    sed -i '/csfacecutrun.sh/d' /etc/init.d/rc.local
fi
docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} --restart=always --net=host -v /usr/share/zoneinfo:/usr/share/zoneinfo -v /var/log/csfacecutlog:/var/log/csfacecutlog -v /var/core:/var/core -v /etc/ip:/etc/ip -v /etc/kdc.conf:/etc/kdc.conf -v /bin/crypto:/bin/crypto -v /usr/local/akcs/csfacecut/data/images:/usr/local/akcs/csfacecut/data/images \
--name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csfacecut/scripts/csfacecutrun.sh
sleep 3


#守护进程中会进行环境变量替换配置文件中的内容










