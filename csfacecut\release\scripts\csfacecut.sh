#!/bin/bash
ACMD="$1"
csfacecut_BIN='/usr/local/akcs/csfacecut/bin/csfacecut'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csfacecut()
{
	nohup $csfacecut_BIN >/dev/null 2>&1 &
    echo "Start csfacecut successful"
    if [ -z "`ps -fe|grep "csfacecutrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csfacecut/scripts/csfacecutrun.sh >/dev/null 2>&1 &
    fi
}
stop_csfacecut()
{
    echo "Begin to stop csfacecutrun.sh"
    csfacecutrunid=`ps aux | grep -w csfacecutrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csfacecutrunid}" ];then
	    echo "csfacecutrun.sh is running at ${csfacecutrunid}, will kill it first."
	    kill -9 ${csfacecutrunid}
    fi
    echo "Begin to stop csfacecut"
    kill -9 `pidof csfacecut`
    sleep 2
    echo "Stop csfacecut successful"
}

case $ACMD in
  start)
     start_csfacecut
    ;;
  stop)
     stop_csfacecut
    ;;
  restart)
    stop_csfacecut
    sleep 1
    start_csfacecut
    ;;
  status)
    if [ -f /var/run/csfacecut.pid ];then
        pid=`cat /var/run/csfacecut.pid`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csfacecut is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csfacecut is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

