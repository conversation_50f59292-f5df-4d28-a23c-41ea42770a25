#!/bin/bash
#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/csfacecut/scripts/sedconf.sh
PROCESS_NAME=csfacecut
PROCESS_START_CMD="/usr/local/akcs/csfacecut/scripts/csfacecutctl.sh start"
PROCESS_PID_FILE=/var/run/csfacecut.pid
LOG_FILE=/var/log/csfacecut_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csfacecut/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csfacecut/scripts/log_back.sh"
csfacecutlog_path="/var/log/csfacecutlog"
csfacecut_BIN='/usr/local/akcs/csfacecut/bin/csfacecut'

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

$csfacecut_BIN >/dev/null 2>&1

