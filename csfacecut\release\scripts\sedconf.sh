#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*beanstalkd_ip=.*/beanstalkd_ip=${BEANSTALKD_IP}/g
    " /usr/local/akcs/csfacecut/conf/csfacecut.conf

# FastDFS 配置
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" /usr/local/akcs/csfacecut/conf/csfacecut_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" /usr/local/akcs/csfacecut/conf/csfacecut_fdfs.conf
fi