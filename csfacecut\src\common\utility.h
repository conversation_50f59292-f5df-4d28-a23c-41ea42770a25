#ifndef __CSFACECUT_UTILITY_H__
#define __CSFACECUT_UTILITY_H__

#include <stdio.h>
#include <string.h>

#include "aes.h"
#include "AES128.h"

namespace csfacecut
{
    /**
     * @brief  match   查找 source 字符串中第一次出现 target 字符串的位置（支持空字符）
     *
     * @param  source  源字符串
     * @param  src_len 源字符串长度
     * @param  target  目标字符串
     * @param  tar_len 目标字符串长度
     * @return const char* nullptr=没有找到，其他=target出现的位置
     */
    __attribute__((unused)) static const char* memory_match(const char* source, size_t src_len, const char* target, size_t tar_len)
    {
        if (src_len <= 0 || 0 == tar_len || source == nullptr || target == nullptr)
        {
            return source;  // even if src_len is 0
        }

        const char* tar_start = target;
        const char* src_end = source + src_len;
        const char* tar_end = tar_start + tar_len;

        for (; source < src_end; ++source)
        {
            if (*source == *target)
            {
                if (++target == tar_end)
                {
                    return source + 1 - tar_len;
                }
            }
            else if (target != tar_start)
            {
                // must back up source in case a prefix matched (find "aab" in "aaab")
                source -= target - tar_start;  // for loop will advance one more
                target = tar_start;
            }
        }

        return nullptr;
    }
}
#endif
