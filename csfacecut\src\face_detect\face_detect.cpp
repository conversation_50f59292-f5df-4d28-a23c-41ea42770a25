#include <iostream>
#include <unistd.h>
#include <sys/stat.h>
#include "revision.h"
#include "facesdk_config.h"
#include "facesdk_callback.h"
#include "facesdkAPI.h"
#include "face_detect.h"
#include "opencv2/opencv.hpp"
#include "face_format_check.h"

CFaceDetector* CFaceDetector::instance_ = nullptr;

CFaceDetector::CFaceDetector() : detect_callback_(nullptr)
{
}

CFaceDetector::~CFaceDetector()
{
    if (detect_callback_ != nullptr)
    {
        delete detect_callback_;
        detect_callback_ = nullptr;
    }
}

CFaceDetector* CFaceDetector::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new CFaceDetector();
    }

    return instance_;
}

int CFaceDetector::InitEngine(const std::string& path)
{
    // init engine
    detect_callback_ = new ICallBack();
    if (detect_callback_ == nullptr)
    {
        return -1;
    }

    if (FacesdkAPI_InitEngine("/NoNeed/", detect_callback_) != 0)
    {
        return -2;
    }

    // setting config
    FacesdkConfig config;
    strncpy(config.cfgFaceInfoSavePath, path.c_str(), sizeof(config.cfgFaceInfoSavePath) - 1);
    if (FacesdkAPI_UpdateConfig(config) != 0)
    {
        return -3;
    }

    return 0;
}

int CFaceDetector::DeinitEngine()
{
    (void)FacesdkAPI_DeinitEngine();
    return 0;
}

const char* CFaceDetector::CheckFaceFlie(const std::string& face_path)
{
    // 判断文件大小
    struct stat file_stat;
    if (stat(face_path.c_str(), &file_stat) == 0)
    {
        if (file_stat.st_size > MAX_FACE_IMAGE_SIZE)
        {
            return ERR_CODE_FILE_TOO_LARGER;
        }
    }
    else
    {
        return ERR_CODE_FILE_FORMAT_ERROR;
    }

    // 判断文件格式
    if (IsJPG(face_path) == false && IsPNG(face_path) == false && IsBMP(face_path) == false)
    {
        return ERR_CODE_FILE_FORMAT_ERROR;
    }

    // 判断图片大小
    cv::Mat image = cv::imread(face_path);
    if (image.cols < MIN_FACE_IMAGE_COL || image.rows < MIN_FACE_IMAGE_ROW)
    {
        return ERR_CODE_LOW_RESOLUTION;
    }

    return ERR_CODE_SUCCESS;
}

const char* CFaceDetector::InternalCodeToUnifiedCode(int code)
{
    switch (code)
    {
        case (int)UPLOAD_FACEPIC_SUCCESS:                       //成功
            return ERR_CODE_SUCCESS;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_SYSTEM:                  //System Error：系统错误，包括解码失败，重命名图片失败等
            return ERR_CODE_DETECT_FAILED;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW:          //Not front view：人脸的旋转角度 或俯视、仰视、侧脸的角度过大
            return ERR_CODE_NOT_FRONT;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_WEAR_MASK:               //Mask detected：检测到口罩     
            return ERR_CODE_DETECT_MASK;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION:          //Resolution is too low.：人脸分辨率太小
            return ERR_CODE_LOW_RESOLUTION;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_WRONG_FORMAT:            //File format error：人脸格式错误
            return ERR_CODE_FILE_FORMAT_ERROR;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_NO_FACE:                 //No face dectected.：图片中未检测到人脸
            return ERR_CODE_NO_FACE;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_FILE_LARGE:              //The file is too larger：图片大于10MB
            return ERR_CODE_FILE_TOO_LARGER;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_FACE_LARGE:              //The face is too larger.：图片中人脸过大
            return ERR_CODE_FACE_TOO_LARGER;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_FACE_SMALL:              //The face is too small：图片中人脸过小
            return ERR_CODE_FACE_TOO_SMALL;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_MULTI_FACES:             //More than one face：图片中人脸不止1个
            return ERR_CODE_MORE_MULTI_FACE;
            break;
        case (int)UPLOAD_FACEPIC_ERROR_NOT_CLEAR:               //Face not clear enough.: 图片中人脸不清晰
            return ERR_CODE_FACE_NOT_CLEAR;
            break;

        default:
            break;
    }

    return ERR_CODE_DETECT_FAILED;
}

const char* CFaceDetector::FaceDetect(const std::string& path, const std::string& input_file, const std::string& output_file)
{
    size_t items = input_file.find_last_of('.');
    if (items == std::string::npos || detect_callback_ == nullptr)
    {
        return ERR_CODE_DETECT_FAILED;
    }

    std::string input_file_path = path + "/" + input_file;
    std::string output_file_path = path + "/" + output_file;
    const char* code = CheckFaceFlie(input_file_path);
    if (strcmp(code, ERR_CODE_SUCCESS) != 0)
    {
        return code;
    }

    cv::Mat image = cv::imread(input_file_path);
    cv::cvtColor(image, image, cv::COLOR_BGR2RGB);
    int ret = FacesdkAPI_DoRegFaceSingle(image.data, image.cols, image.rows);

    int param_a = detect_callback_->GetParamA();
    std::string tmp_file = detect_callback_->GetParamC() + ".png";  //裁剪后默认是png格式
    switch (ret)
    {
        case 1:
            ret = UPLOAD_FACEPIC_SUCCESS;
            break;
        case 0:
            ret = UPLOAD_FACEPIC_ERROR_NO_FACE;
            break;
        case MSG_ID_FACESDK_CHECK_FACENUM:
            ret = UPLOAD_FACEPIC_ERROR_MULTI_FACES;
            break;
        case MSG_ID_FACESDK_CHECK_FACESIZE:
        {
            if (param_a == CHECK_FACESIZE_LARGE)
            {
                ret = UPLOAD_FACEPIC_ERROR_FACE_LARGE;
            }
            else if (param_a == CHECK_FACESIZE_SMALL)
            {
                ret = UPLOAD_FACEPIC_ERROR_FACE_SMALL;
            }
            break;
        }
        case MSG_ID_FACESDK_CHECK_FACEPOSE:
            ret = UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW;
            break;
        case MSG_ID_FACESDK_CHECK_FACEQUALITY:
            ret = UPLOAD_FACEPIC_ERROR_NOT_CLEAR;
            break;
        case MSG_ID_FACESDK_CHECK_FACEMASK:
            ret = UPLOAD_FACEPIC_ERROR_WEAR_MASK;
            break;
        default:
            break;
    }

    if (ret == UPLOAD_FACEPIC_SUCCESS)
    {
        ::rename(tmp_file.c_str(), output_file_path.c_str());
    }
    else
    {
        unlink(tmp_file.c_str());
    }

    // FacesdkAPI_DeinitEngine();
    return InternalCodeToUnifiedCode(ret);
}

CFaceDetector* GetCFaceDetectorInstance()
{
    return CFaceDetector::GetInstance();
}
