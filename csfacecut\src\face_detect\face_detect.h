#ifndef __CSFACECUT_DETECT_IMPL_H__
#define __CSFACECUT_DETECT_IMPL_H__

#include <string>
#include "face_detect_callback.h"
#include "../facecut_error_code.h"

enum {
    UPLOAD_FACEPIC_ERROR_SYSTEM = -1,               //System Error：系统错误，包括解码失败，重命名图片失败等
    UPLOAD_FACEPIC_SUCCESS = 0,      
    UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW = 100,      //Not front view：人脸的旋转角度 或俯视、仰视、侧脸的角度过大
    UPLOAD_FACEPIC_ERROR_WEAR_MASK = 101,           //Mask detected：检测到口罩     
    UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION = 102,      //Resolution is too low.：人脸分辨率太小
    UPLOAD_FACEPIC_ERROR_WRONG_FORMAT = 103,        //File format error.：人脸格式错误
    UPLOAD_FACEPIC_ERROR_NO_FACE= 104,              //No face dectected.：图片中未检测到人脸
    UPLOAD_FACEPIC_ERROR_FILE_LARGE = 105,          //The file is too larger：图片大于10MB
    UPLOAD_FACEPIC_ERROR_FACE_LARGE = 106,          //The face is too larger.：图片中人脸过大
    UPLOAD_FACEPIC_ERROR_FACE_SMALL= 107,           //The face is too small：图片中人脸过小
    UPLOAD_FACEPIC_ERROR_MULTI_FACES= 108,          //More than one face：图片中人脸不止1个
    UPLOAD_FACEPIC_ERROR_NOT_CLEAR= 113             //Face not clear enough.: 图片中人脸不清晰
};

class CFaceDetector
{
private:
    CFaceDetector();

private:
    ICallBack* detect_callback_;
    static CFaceDetector* instance_;

    /**
     * @brief   内部错误码转外部统一错误码
     *
     * @param   code 内部错误码
     * @return  const char* 外部统一错误码
     */
    const char* InternalCodeToUnifiedCode(int code);

public:
    ~CFaceDetector();
    static CFaceDetector* GetInstance();
    
    /**
     * @brief  Init engine.
     * 
     * @param path image storage path.
     * @return int 0=success, otherwise failed.
     */
    int InitEngine(const std::string& path);

    /**
     * @brief  Deinit engine.
     * 
     * @return int 0=success, otherwise failed.
     */
    int DeinitEngine();

    /**
     * @brief  检查人脸文件的合法性
     * 
     * @param  face_path    人脸图片路径
     * @return int          const char* 外部统一错误码
     */
    const char* CheckFaceFlie(const std::string& face_path);

    /**
     * @brief  人脸裁剪      (裁剪成功直接覆盖原文件)
     *
     * @param  path         图片目录
     * @param  input_file   输入图片名
     * @param  output_file  输出图片名
     * @return const char*  统一错误码，0=成功，其他=失败
     */
    const char* FaceDetect(const std::string& path, const std::string& input_file, const std::string& output_file);
};

CFaceDetector* GetCFaceDetectorInstance();

#endif
