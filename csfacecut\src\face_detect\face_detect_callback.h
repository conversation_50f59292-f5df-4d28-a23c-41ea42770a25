
#ifndef __CSFACECUT_DETECT_CALLBACK_H__
#define __CSFACECUT_DETECT_CALLBACK_H__
#include <iostream>

#include "facesdk_callback.h"

class ICallBack : public FacesdkCallBack
{
public:
    ICallBack();
    virtual ~ICallBack();

    virtual int Notify(const int msgID, const int paramA, const int paramB, const char* paramC);
    int GetMsgID();
    int GetParamA();
    int GetParamB();
    std::string GetParamC();

private:
    int msg_id_ = 0;
    int param_a_ = 0;
    int param_b_ = 0;
    std::string param_c_;

};
#endif