#include <iostream>
#include "opencv2/opencv.hpp"
#include <fstream>
#include <vector>

// 判断文件是否为 JPG 格式
__attribute__((unused)) static bool IsJPG(const std::string& filepath)
{
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) return false;

    unsigned char buffer[3];
    file.read(reinterpret_cast<char*>(buffer), 3);

    // JPEG files start with FF D8 FF
    return buffer[0] == 0xFF && buffer[1] == 0xD8 && buffer[2] == 0xFF;
}

// 判断文件是否为 PNG 格式
__attribute__((unused)) static bool IsPNG(const std::string& filepath)
{
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) return false;

    unsigned char buffer[8];
    file.read(reinterpret_cast<char*>(buffer), 8);

    // PNG files start with 89 50 4E 47 0D 0A 1A 0A
    return buffer[0] == 0x89 && buffer[1] == 0x50 && buffer[2] == 0x4E &&
        buffer[3] == 0x47 && buffer[4] == 0x0D && buffer[5] == 0x0A &&
        buffer[6] == 0x1A && buffer[7] == 0x0A;
}

// 判断文件是否为 BMP 格式
__attribute__((unused)) static bool IsBMP(const std::string& filepath)
{
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) return false;

    unsigned char buffer[2];
    file.read(reinterpret_cast<char*>(buffer), 2);

    // BMP files start with BM
    return buffer[0] == 'B' && buffer[1] == 'M';
}

// 转换图像通道
__attribute__((unused)) static void CvtChannels(cv::Mat& img)
{
    if (img.channels() == 3)
    {
        return;
    }
    else if (img.channels() == 1)
    {
        cv::cvtColor(img, img, cv::COLOR_GRAY2BGR);
    }
    else if (img.channels() == 4)
    {
        cv::cvtColor(img, img, cv::COLOR_RGBA2BGR);
    }
}
