#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <assert.h>

#include "AkLogging.h"
#include "fdfs_client.h"
#include "facecut_define.h"
#include "facecut_config.h"
#include "face_fdfs_manager.h"

extern FACECUT_CONFIG g_facecut_config;

CFaceFdfsManager* GetFaceFdfsManagerInstance()
{
    return CFaceFdfsManager::GetInstance();
}

CFaceFdfsManager* CFaceFdfsManager::instance_ = NULL;

CFaceFdfsManager* CFaceFdfsManager::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new CFaceFdfsManager();
    }

    return instance_;
}

CFaceFdfsManager::CFaceFdfsManager()
{
    uploader_ = std::unique_ptr<FdfsUploader>(new FdfsUploader());
    uploader_->Init(PROCESS_FDFS_CONF_FILE);
    uploader_->SetUploadGroupName(g_facecut_config.fdfs_group);
}

CFaceFdfsManager::~CFaceFdfsManager()
{
}

int CFaceFdfsManager::UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times)
{
    return uploader_->UploadFile(local_filepath, remote_filepath, retry_times);
}

int CFaceFdfsManager::DeleteFile(const std::string& remote_filename)
{
    return uploader_->DeleteFile(remote_filename);
}
