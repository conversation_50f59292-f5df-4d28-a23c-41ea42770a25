#ifndef __CSFACECUT_FDFS_MANAGER__
#define __CSFACECUT_FDFS_MANAGER__

#include <mutex>
#include <string>
#include <boost/noncopyable.hpp>

#include "fdfs_uploader.h"

class CFaceFdfsManager : public boost::noncopyable
{
public:
    CFaceFdfsManager();
    ~CFaceFdfsManager();    
    static CFaceFdfsManager* GetInstance();

    /**
     * @brief  upload face picture to fdfs server.
     * 
     * @param  local_filepath   path to local
     * @param  remote_filepath  [out]path to remote
     * @param  retry_times      retry times
     * @return int              0=success, other=failure
     */
    int UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times);

    /**
     * @brief  delete face picture from fdfs server.
     * 
     * @param  remote_filename remote picture path to delete.
     * @return int             0=success, other=failure
     */
    int DeleteFile(const std::string& remote_filename);

private:
    static CFaceFdfsManager* instance_;
    std::unique_ptr<FdfsUploader> uploader_;
};

CFaceFdfsManager* GetFaceFdfsManagerInstance();

#endif //__CSFACECUT_FDFS_MANAGER__
