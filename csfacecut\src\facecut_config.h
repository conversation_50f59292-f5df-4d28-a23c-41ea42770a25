#ifndef __CSFACECUT_CONFIG_H__
#define __CSFACECUT_CONFIG_H__

#include <string>

// csfacecut config struct
typedef struct FACECUT_CONFIG
{
    // http server port.
    int http_port = 8798;

    // HTTP handler thread number
    int http_thread_num = 10;

    // etcd server
    std::string etcd_srv_net = "";

    // face file fdfs group name.
    std::string fdfs_group = "";
} FACECUT_CONFIG;


#endif //__CSFACECUT_CONFIG_H__
