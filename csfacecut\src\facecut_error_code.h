#ifndef __CSFACECUT_ERROR_CODE_H__
#define __CSFACECUT_ERROR_CODE_H__

#include <map>
#include <string>

#define MIN_FACE_IMAGE_COL  250         // 人脸图片最小宽度
#define MIN_FACE_IMAGE_ROW  250         // 人脸图片最小高度
#define MAX_FACE_IMAGE_SIZE (10<<20)    // 人脸图片最大文件大小 10MB

// error codes.
#define ERR_CODE_SUCCESS                "0"
#define ERR_CODE_SERVER_ERR             "1000300001"
#define ERR_CODE_INVALID_REQUEST_ROUTE  "1000300002"
#define ERR_CODE_INVALID_REQUEST_DATA   "1000300003"
#define ERR_CODE_FDFS_UPLOAD_FAIL       "1000300004"
#define ERR_CODE_FDFS_DELETE_FAIL       "1000300005"
#define ERR_CODE_FDFS_GET_FAIL          "1000300006"
#define ERR_CODE_ENCRYPT_FAIL           "1000300007"
#define ERR_CODE_DECRYPT_FAIL           "1000300008"
#define ERR_CODE_DETECT_FAILED          "1000300009"
#define ERR_CODE_NOT_FRONT              "1000300010"
#define ERR_CODE_DETECT_MASK            "1000300011"
#define ERR_CODE_LOW_RESOLUTION         "1000300012"
#define ERR_CODE_NO_FACE                "1000300013"
#define ERR_CODE_FILE_TOO_LARGER        "1000300014"
#define ERR_CODE_FACE_TOO_LARGER        "1000300015"
#define ERR_CODE_FACE_TOO_SMALL         "1000300016"
#define ERR_CODE_MORE_MULTI_FACE        "1000300017"
#define ERR_CODE_FACE_NOT_CLEAR         "1000300018"
#define ERR_CODE_FILE_FORMAT_ERROR      "1000300019"

// error codes description mapping.
static const std::map<std::string, std::string> g_error_code_message =
{
    {ERR_CODE_SUCCESS,                  "Success"},
    {ERR_CODE_SERVER_ERR,               "Internal Server Error"},
    {ERR_CODE_INVALID_REQUEST_ROUTE,    "Invalid request interface"},
    {ERR_CODE_INVALID_REQUEST_DATA,     "Invalid request data/parameters"},
    {ERR_CODE_FDFS_UPLOAD_FAIL,         "Upload file to fdfs failed"},
    {ERR_CODE_FDFS_DELETE_FAIL,         "Delete file to fdfs failed"},
    {ERR_CODE_FDFS_GET_FAIL,            "Get file to fdfs failed"},
    {ERR_CODE_ENCRYPT_FAIL,             "Encrypt file failed"},
    {ERR_CODE_DECRYPT_FAIL,             "Decrypt file failed"},
    {ERR_CODE_DETECT_FAILED,            "Detect face failed"},
    {ERR_CODE_NOT_FRONT,                "Not front view"},
    {ERR_CODE_DETECT_MASK,              "Mask detected"},
    {ERR_CODE_LOW_RESOLUTION,           "Resolution is too low"},
    {ERR_CODE_NO_FACE,                  "No face dectected"},
    {ERR_CODE_FILE_TOO_LARGER,          "The file is too larger"},
    {ERR_CODE_FACE_TOO_LARGER,          "The face is too larger"},
    {ERR_CODE_FACE_TOO_SMALL,           "The face is too small"},
    {ERR_CODE_MORE_MULTI_FACE,          "More than one face"},
    {ERR_CODE_FACE_NOT_CLEAR,           "Face not clear enough"},
    {ERR_CODE_FILE_FORMAT_ERROR,        "File format error"}
};

#endif
