#include <vector>
#include <etcd/Client.hpp>

#include "util.h"
#include "catch.hpp"
#include "EtcdCliMng.h"
#include "facecut_etcd.h"
#include "facecut_config.h"

#include "evpp/event_watcher.h"

extern FACECUT_CONFIG       g_facecut_config;
extern CAkEtcdCliManager*   g_etcd_cli_mng;
extern std::shared_ptr<evpp::EventLoop>     g_etcd_loop;

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}

void EtcdSrvInit()
{
    // value=内网ip:port
    std::string inner_addr = GetInnerIPAddr() + ":" + std::to_string(g_facecut_config.http_port);

    // key=/akcs/csfacecut/innerip/内网ip:port, 
    std::string reg_key = std::string("/akcs/csfacecut/innerip/") + inner_addr;

    RegSrv2Etcd(reg_key, inner_addr, 10, csbase::REG_INNER, g_etcd_loop.get());
    g_etcd_loop->Run();
}

