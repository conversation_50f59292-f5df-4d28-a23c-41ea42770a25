#include "common/utility.h"
#include "http_boundary_parser.h"

ns_facecut::CBoundaryParser::CBoundaryParser()
{
    boundary_list_.clear();
}

int ns_facecut::CBoundaryParser::Parse(const evpp::http::ContextPtr& ctx)
{
    int left_size = 0;
    const evpp::Slice& body = ctx->body();
    const char* current_position = body.data();
    while (current_position < body.data() + body.size())
    {
        BoundaryMeta meta;

        // find start position of boundary's header 
        current_position = strstr(current_position, "\r\n");
        if (current_position == nullptr)
        {
            break;
        }
        current_position += strlen("\r\n");
        meta.boundary_header_position = current_position;

        // find start position of boundary's data 
        left_size = int(body.size()) - (current_position - body.data());
        current_position = csfacecut::memory_match(current_position, left_size, "\r\n\r\n", strlen("\r\n\r\n"));
        if (current_position == nullptr)
        {
            break;
        }
        current_position += strlen("\r\n\r\n");
        meta.boundary_data_position = current_position;

        // find end position of boundary's data 
        left_size = int(body.size()) - (current_position - body.data());
        current_position = csfacecut::memory_match(current_position, left_size, "\r\n--", strlen("\r\n--"));
        if (current_position == nullptr)
        {
            break;
        }
        meta.boundary_data_size = current_position - meta.boundary_data_position;

        boundary_list_.emplace_back(meta);
    }

    return 0;
}

int ns_facecut::CBoundaryParser::GetBoundaryCount()
{
    return boundary_list_.size();
}

evpp::Slice ns_facecut::CBoundaryParser::GetBoundaryDataByIndex(int boundary_index)
{
    if (boundary_index < 0 || boundary_index >(int)boundary_list_.size())
    {
        return evpp::Slice();
    }

    const BoundaryMeta& meta = boundary_list_.at(boundary_index);
    return evpp::Slice(meta.boundary_data_position, meta.boundary_data_size);
}

evpp::Slice ns_facecut::CBoundaryParser::GetBoundaryDataByName(const std::string& boundary_name)
{
    std::string header;
    std::string target = "name=\"" + boundary_name + "\"";
    for (int boundary_index = 0; boundary_index < (int)boundary_list_.size(); boundary_index++)
    {
        header = FindBoundaryHeader(boundary_index, "Content-Disposition");
        const char* position = strstr(header.c_str(), target.c_str());
        if (position != nullptr)
        {
            const BoundaryMeta& meta = boundary_list_.at(boundary_index);
            return evpp::Slice(meta.boundary_data_position, meta.boundary_data_size);
        }
    }

    return evpp::Slice();
}

std::string ns_facecut::CBoundaryParser::FindBoundaryHeader(int boundary_index, const std::string& header)
{
    const BoundaryMeta& meta = boundary_list_.at(boundary_index);
    std::string tmp_header = std::string("\r\n") + header + ": ";

    const char* position = strstr(meta.boundary_header_position - 2, tmp_header.c_str());
    if (position == nullptr || position >= meta.boundary_data_position)
    {
        return std::string();
    }

    std::string result = "";
    position += tmp_header.length();
    while ((*position) == ' ')
    {
        position++;
    }
    while ((*position) != '\r' && (*position) != '\n')
    {
        result += (*position);
        position++;
    }

    return result;
}
