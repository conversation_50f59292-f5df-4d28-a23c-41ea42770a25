#ifndef __CSFACECUT_HTTP_PARSER_H
#define __CSFACECUT_HTTP_PARSER_H

#include <string>
#include <vector>
#include "evpp/http/context.h"

namespace ns_facecut
{
    /** 在 HTTP POST 请求中使用 multipart/form-data 格式来传输数据时, 
     *  数据会按照特定的boundary格式进行编码。
     * 
     */


    typedef struct  BoundaryMeta
    {
        const char* boundary_header_position;
        const char* boundary_data_position;
        size_t      boundary_data_size;

        BoundaryMeta()
        {
            boundary_header_position = nullptr;
            boundary_data_position = nullptr;
            boundary_data_size = 0;
        }
    }BoundaryMeta;

    class CBoundaryParser
    {
        std::vector<BoundaryMeta> boundary_list_;

    public:
        CBoundaryParser();

        //          get the number of boundary.  (0=success, other=failed)
        int         GetBoundaryCount();
        //          parse boundary in post body. (0=success, other=failed)
        int         Parse(const evpp::http::ContextPtr& ctx);
        //          获取指定下标的boundary，下标从0开始
        evpp::Slice GetBoundaryDataByIndex(int boundary_index);
        //          获取参数的bound
        evpp::Slice GetBoundaryDataByName(const std::string& boundary_name);
        //          从指定的boundary中获取header的头部值（注意，header会区分大小写）
        std::string FindBoundaryHeader(int boundary_index, const std::string& header);
    };
}

#endif 
