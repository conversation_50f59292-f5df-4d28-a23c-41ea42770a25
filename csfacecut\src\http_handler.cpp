#include <thread>
#include <fstream> 
#include <iostream>
#include <functional>

#include <evpp/http/context.h>

#include "json/json.h"

#include "AES128.h"
#include "SHA1.h"
#include "Md5.h"

#include "http_message.h"
#include "http_handler.h"
#include "common/utility.h"
#include "facecut_define.h"
#include "facecut_config.h"
#include "face_fdfs_manager.h"
#include "http_boundary_parser.h"
#include "face_detect/face_detect.h"

extern FACECUT_CONFIG g_facecut_config;     // global config variable

namespace ns_facecut
{
    /**
     * @brief  解析 UploadFace 的请求数据
     *
     * @param  ctx              请求体
     * @param  src_image_path   图片保存位置
     * @param  account          [out]Account
     * @return int              0=成功，其他=失败
     */
    int ParseUploadFaceData(const evpp::http::ContextPtr& ctx, const std::string& iamge_save_path, std::string& account)
    {
        const char* trace_id = ctx->FindRequestHeader("Trace-ID");

        // 1.Get image data and account from http post data.
        CBoundaryParser boundary_parser;
        (void)boundary_parser.Parse(ctx);
        if (boundary_parser.GetBoundaryCount() != 2)
        {
            LOG_WARN << "Parse upload data failed, trace_id=" << trace_id << ", boundary count: " << boundary_parser.GetBoundaryCount();
            return -1;
        }

        evpp::Slice account_data = boundary_parser.GetBoundaryDataByName("account");
        evpp::Slice image_data = boundary_parser.GetBoundaryDataByName("face");
        if (image_data.empty() || account_data.empty())
        {
            LOG_WARN << "Get image data failed, trace_id=" << trace_id
                      << ", image size=" << image_data.size()
                      << ", account size=" << account_data.size();
            return -1;
        }

        // 2.Save image data to file.
        std::ofstream ofs(iamge_save_path, std::ios::binary);
        ofs.write(image_data.data(), image_data.size());
        ofs.close();

        // 3. set account.
        account = std::string(account_data.data()).substr(0, account_data.size());

        return 0;
    }

        /**
     * @brief  解析 DetectFace 的请求数据
     *
     * @param  ctx              请求体
     * @param  src_image_path   图片保存位置
     * @return int              0=成功，其他=失败
     */
    int ParseDetectFaceData(const evpp::http::ContextPtr& ctx, const std::string& iamge_save_path)
    {
        const char* trace_id = ctx->FindRequestHeader("Trace-ID");

        // 1.Get image data and account from http post data.
        CBoundaryParser boundary_parser;
        (void)boundary_parser.Parse(ctx);
        if (boundary_parser.GetBoundaryCount() != 1)
        {
            LOG_WARN << "Parse upload data failed, trace_id=" << trace_id << ", boundary count: " << boundary_parser.GetBoundaryCount();
            return -1;
        }

        evpp::Slice image_data = boundary_parser.GetBoundaryDataByName("face");
        if (image_data.empty())
        {
            LOG_WARN << "Get image data failed, trace_id=" << trace_id
                      << ", image size=" << image_data.size();
            return -1;
        }

        // 2.Save image data to file.
        std::ofstream ofs(iamge_save_path, std::ios::binary);
        ofs.write(image_data.data(), image_data.size());
        ofs.close();

        return 0;
    }

    ns_facecut::HTTPRespCallback ReqFaceUploadHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
        const char* trace_id = ctx->FindRequestHeader("Trace-ID");

        // 1.define file path
        std::ostringstream oss;
        oss << std::this_thread::get_id();
        std::string thread_id = oss.str();

        std::string account = "";
        std::string image_dir = PROCESS_IMAGE_DIR;
        std::string src_image = thread_id + "_image.png";
        std::string detect_image = thread_id + "_detected.png";
        std::string src_image_path = image_dir + "/" + src_image;
        std::string detect_image_path = image_dir + "/" + detect_image;
        std::string encrypt_file_path = image_dir + "/enc_" + detect_image;

        // 2.Parse request data and save image to file.
        if (ParseUploadFaceData(ctx, src_image_path, account) != 0)
        {
            cb(BuildHttpErrorMessage(ERR_CODE_INVALID_REQUEST_DATA));
            return;
        }
        std::string key = (std::string(account) + FACE_AES128_KEY).substr(0, 16);

        // 3.Detect face picture.
        const char* code = GetCFaceDetectorInstance()->FaceDetect(image_dir, src_image, detect_image);
        if (strcmp(code, ERR_CODE_SUCCESS) != 0)
        {
            LOG_WARN << "Detect Face picture failed, trace_id=" << trace_id << ", error code=" << code;
            unlink(src_image_path.c_str());
            cb(BuildHttpErrorMessage(code));
            return;
        }

        // 4.AES encrypt face file.
        key = SHA1::encode(key).substr(0, 16);
        if (AES128CBCEncryptFile(detect_image_path.c_str(), key.c_str(), encrypt_file_path.c_str()) != 0)
        {
            LOG_WARN << "Encrypt face file failed, trace_id=" << trace_id;
            cb(BuildHttpErrorMessage(ERR_CODE_ENCRYPT_FAIL));
            unlink(src_image_path.c_str());
            unlink(detect_image_path.c_str());
            return;
        }

        // 5.Upload face picture to fdfs.
        std::string remote_filepath = "";
        if (GetFaceFdfsManagerInstance()->UploadFile(encrypt_file_path, remote_filepath, 2) != 0 || remote_filepath.length() == 0)
        {
            LOG_WARN << "Upload face file to fdfs failed, trace_id=" << trace_id;
            cb(BuildHttpErrorMessage(ERR_CODE_FDFS_UPLOAD_FAIL));
            unlink(src_image_path.c_str());
            unlink(detect_image_path.c_str());
            unlink(encrypt_file_path.c_str());
            return;
        }

        // 6.Generate response data.
        HttpRespKV rsp_data;
        rsp_data["face_file_url"] = remote_filepath;
        rsp_data["face_file_md5"] = akuvox_encrypt::MD5::GetFileMD5(encrypt_file_path);
        LOG_INFO << "Upload face file success. trace_id=" << trace_id << ", account=" << account << ", key=" << key
            << ", face_file_url=" << remote_filepath << ", face_file_md5" << rsp_data["face_file_md5"];

        // 7.Cleanup face picture file.
        unlink(src_image_path.c_str());
        unlink(detect_image_path.c_str());
        unlink(encrypt_file_path.c_str());

        cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS, rsp_data));
        return;
    };

    ns_facecut::HTTPRespCallback ReqFaceDeleteHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        const char* trace_id = ctx->FindRequestHeader("Trace-ID");
        std::string path = std::string(ctx->body().data(), ctx->body().size());

        // 1.Get value of parameter path.
        if (path.length() == 0 || path.compare(0, 5, "path=") != 0)
        {
            LOG_WARN << "Not found parameter[path], trace_id=" << trace_id;
            cb(BuildHttpErrorMessage(ERR_CODE_INVALID_REQUEST_DATA));
            return;
        }

        path = path.substr(5);
        // 2.Delete face picture on fdfs failed.
        if (GetFaceFdfsManagerInstance()->DeleteFile(path) != 0)
        {
            LOG_WARN << "Delete face file on fdfs failed, trace_id=" << trace_id << ", path=" << path;
            cb(BuildHttpErrorMessage(ERR_CODE_FDFS_DELETE_FAIL));
            return;
        }

        // 3.Generate response data.
        cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS));
        return;
    };

    ns_facecut::HTTPRespCallback ReqFaceDetectHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
        const char* trace_id = ctx->FindRequestHeader("Trace-ID");

        // 1.define file path
        std::ostringstream oss;
        oss << std::this_thread::get_id();
        std::string thread_id = oss.str();

        std::string image_dir = PROCESS_IMAGE_DIR;
        std::string src_image = thread_id + "_image.png";
        std::string detect_image = thread_id + "_detected.png";
        std::string src_image_path = image_dir + "/" + src_image;
        std::string detect_image_path = image_dir + "/" + detect_image;

        // 2.Parse request data and save image to file.
        if (ParseDetectFaceData(ctx, src_image_path) != 0)
        {
            cb(BuildHttpErrorMessage(ERR_CODE_INVALID_REQUEST_DATA));
            return;
        }

        // 3.Detect face picture.
        const char* code = GetCFaceDetectorInstance()->FaceDetect(image_dir, src_image, detect_image);
        if (strcmp(code, ERR_CODE_SUCCESS) != 0)
        {
            LOG_WARN << "Detect Face picture failed, trace_id=" << trace_id << ", error code=" << code;
            unlink(src_image_path.c_str());
            cb(BuildHttpErrorMessage(code));
            return;
        }

        // 4.Cleanup face picture file.
        unlink(src_image_path.c_str());
        unlink(detect_image_path.c_str());

        LOG_INFO << "Detect face file success. trace_id=" << trace_id;
        cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS));
        return;
    };

    ns_facecut::HTTPAllRespCallbackMap HTTPAllRespMapInit()
    {
        ns_facecut::HTTPAllRespCallbackMap OMap;
        OMap[ns_facecut::HTTP_ROUTE::FACE_UPLOAD] = ReqFaceUploadHandler;
        OMap[ns_facecut::HTTP_ROUTE::FACE_DELETE] = ReqFaceDeleteHandler;
        OMap[ns_facecut::HTTP_ROUTE::FACE_DETECT] = ReqFaceDetectHandler;
        return OMap;
    }
}

