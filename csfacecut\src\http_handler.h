#ifndef __CSFACECUT_HTTP_RESPONSE_H__
#define __CSFACECUT_HTTP_RESPONSE_H__
#include <functional>
#include <evpp/http/context.h>

namespace ns_facecut
{
    // http request router.
    enum HTTP_ROUTE
    {
        FACE_UPLOAD = 0,        // upload face picture.
        FACE_DELETE = 1,        // delete face picture.
        FACE_DETECT = 2,        // detect face picture.
    };

    typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
    typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
    typedef std::map<int, HTTPRespCallback> HTTPAllRespCallbackMap;

    HTTPAllRespCallbackMap HTTPAllRespMapInit();
}

#endif //__GSFACE_HTTP_RESP_H__
