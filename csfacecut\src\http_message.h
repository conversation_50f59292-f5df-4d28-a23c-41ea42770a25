#ifndef __CSFACECUT_HTTP_MESSAGE_H__
#define __CSFACECUT_HTTP_MESSAGE_H__
#include <map>
#include <string>

#include "evpp/http/context.h"

#include "facecut_error_code.h"

typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespKV;
// typedef std::map<std::string/*key*/, int/*value*/> HttpRespIntKV;

// @brief   Get error description by error code.
std::string GetErrorMessage(const std::string& code);
// @brief   Get an error message in json format by code.
std::string BuildHttpErrorMessage(const std::string& code);
// @brief   Get an error message in json format by code, with other kv.
std::string BuildHttpResponseMessage(const std::string& code, const HttpRespKV& kv = {});

#endif //__HTTP_MSG_CONTORL_H__
