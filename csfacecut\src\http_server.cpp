#include <stdio.h>
#include <stdlib.h>

#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include <evpp/http/service.h>
#include <evpp/http/context.h>
#include <evpp/http/http_server.h>

#include "http_server.h"
#include "http_message.h"
#include "http_handler.h"
#include "facecut_config.h"
#include "MetricService.h"

// 全局变量
extern FACECUT_CONFIG g_facecut_config;
static ns_facecut::HTTPAllRespCallbackMap g_http_response_callbacks;

// 当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    cb(BuildHttpErrorMessage(ERR_CODE_INVALID_REQUEST_ROUTE));
}

void HttpReqFaceUploadCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    ns_facecut::HTTPRespCallback ReqFaceUploadHandler = g_http_response_callbacks[ns_facecut::HTTP_ROUTE::FACE_UPLOAD];
    if (ReqFaceUploadHandler)
    {
        ReqFaceUploadHandler(ctx, cb);
    }
    return;
}

void HttpReqFaceDeleteCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    ns_facecut::HTTPRespCallback ReqFaceDeleteHandler = g_http_response_callbacks[ns_facecut::HTTP_ROUTE::FACE_DELETE];
    if (ReqFaceDeleteHandler)
    {
        ReqFaceDeleteHandler(ctx, cb);
    }
    return;
}

void HttpReqFaceDetectCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    ns_facecut::HTTPRespCallback ReqFaceDetectHandler = g_http_response_callbacks[ns_facecut::HTTP_ROUTE::FACE_DETECT];
    if (ReqFaceDetectHandler)
    {
        ReqFaceDetectHandler(ctx, cb);
    }
    return;
}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void startHttpServer()
{
    bool ipv6 = false;
    const int port = g_facecut_config.http_port;
    const int thread_num = g_facecut_config.http_thread_num;
    g_http_response_callbacks = ns_facecut::HTTPAllRespMapInit();

    evpp::http::Server server2(thread_num, ipv6);
    server2.RegisterDefaultHandler(&DefaultHandler);
    server2.RegisterHandler("/face/upload", HttpReqFaceUploadCallback);
    server2.RegisterHandler("/face/delete", HttpReqFaceDeleteCallback);
    server2.RegisterHandler("/face/detect", HttpReqFaceDetectCallback);
    server2.RegisterHandler("/metrics", HttpReqMetricsCallback);
    server2.Init(port);
    server2.Start();
    return;
}
