#include <evpp/tcp_server.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "ConfigFileReader.h"
#include "ConnectionPool.h"

#include "http_server.h"
#include "facecut_etcd.h"
#include "facecut_define.h"
#include "facecut_config.h"
#include "face_detect/face_detect.h"
#include "AkcsDnsResolver.h"
#include "AkcsAppInit.h"
#include "EtcdCliMng.h"
#include "EtcdDns.h"
// #include "HttpApiControl.h"
#include "metric.h"

FACECUT_CONFIG      g_facecut_config;           // 全局配置信息
int                 g_etcd_dns_res = 0;         // DNS resolved flag
CAkEtcdCliManager*  g_etcd_cli_mng = nullptr;   // 
std::string         g_etcd_ip_addr = "";        // etcd server ip address
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

#define PIDFILE "/var/run/csfacecut.pid"
#define write_lock(fd, offset, whence, len) lock_reg(fd, F_SETLK, F_WRLCK, offset, whence, len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

void GlogInit()
{
    google::InitGoogleLogging("csfacecut");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csfacecutlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csfacecutlog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csfacecutlog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csfacecutlog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // above WARNING level is output to the error stream;
    FLAGS_max_log_size = 64;                        // The max size of a single log file is 64 MB
}

void GlogClean()
{
    google::ShutdownGoogleLogging();
}

void ConfigInit()
{
    const char* value = nullptr;
    CConfigFileReader config_file(PROCESS_CONF_FILE);

    // storage group name in fdfs.
    value = config_file.GetConfigName("etcd_srv_net");
    if (value != nullptr)
    {
        g_facecut_config.etcd_srv_net = std::string(value);
    }

    // http server port.
    value = config_file.GetConfigName("http_port");
    if (value != nullptr)
    {
        g_facecut_config.http_port = ATOI(value);
    }

    // http handle thread num.
    value = config_file.GetConfigName("http_thread_num");
    if (value != nullptr)
    {
        g_facecut_config.http_thread_num = ATOI(value);
    }

    // storage group name in fdfs.
    value = config_file.GetConfigName("fdfs_group");
    if (value != nullptr)
    {
        g_facecut_config.fdfs_group = std::string(value);
    }
    
}

int EtcdConnInit()
{
    g_etcd_dns_mng = new CEtcdDnsManager(g_facecut_config.etcd_srv_net);
    std::thread dnsThread = std::thread(&CEtcdDnsManager::StartDnsResolver, g_etcd_dns_mng);
    while(!g_etcd_dns_mng->DnsIsOk())
    {
        usleep(10);
    }
    dnsThread.detach();

    //域名解析完才能初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_dns_mng->GetAddrs());
    g_etcd_dns_mng->SetEtcdCli(g_etcd_cli_mng); 
    return 0;
}

int main(int argc, char* argv[])
{
    // Make sure only one instance run.
    if (!IsSingleton2(PIDFILE))
    {
        printf("another csfacecut has been running in this system.\n") ;
        return -1;
    }
    
    GlogInit2(argv[0], "csfacecutlog");
    ConfigInit();

    // init face picture detector engine.
    CFaceDetector* detector_instance_ = GetCFaceDetectorInstance();
    if (detector_instance_ == nullptr)
    {
        LOG_WARN << "Get detector instance failed.";
        return -1;
    }
    if (detector_instance_->InitEngine(PROCESS_IMAGE_DIR) != 0)
    {
        LOG_WARN << "Detector init engine failed.";
        return -1;
    }
    
    EtcdConnInit();

    // init etcd register loop.
    std::thread etcdCliThread = std::thread(EtcdSrvInit);

    // start http server.
    LOG_INFO << "start sever!";
    std::thread httpThread(startHttpServer);

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csfacecut is starting";
    // 全部的准备工作全部做好之后,再起tcpserver
    // evpp::EventLoop loop;
    // loop.RunEvery(evpp::Duration(60.0 * g_facecut_config.heartbeat), std::bind(&ApiHearbeat));
    // loop.Run();

    httpThread.join();
    etcdCliThread.join();
    GlogClean();
    return 0;
}
