/*
 * 
 * Akuvox自研的人脸识别SDK
 * Akuvox Lisence
 * 
 * By minzhe.huang
 * 2020-07-01
 */

/* Header for face sdk API */

#ifndef __FACESDK_API_H__
#define __FACESDK_API_H__

#include "facesdk_config.h"
#include "facesdk_callback.h"

#ifdef __cplusplus
extern "C" {
#endif

// ------------------------------------------------------------------- facesdk v4
/*
     * [主线程]初始化引擎, 有二次调用保护
     *
     * pAllModelsRootPath   - 所有模型文件的根目录, SDK会自行选择加载
     * cb                     - 回调函数
     * return                 - 0表示成功,
     *                        - -1表示初始化异常
     */
int FacesdkAPI_InitEngine(const char* pAllModelsRootPath, FacesdkCallBack* cb);

/*
* [主线程]更新引擎配置, 在初始化InitEngine后调用, 可在SDK运行过程中使用
*
* engineCfg                     - 引擎配置参数
* return                           - 0表示成功,
*                                  - -1表示初始化异常
*/
int FacesdkAPI_UpdateConfig(FacesdkConfig engineCfg);

/*
* [主线程]注销引擎, 有二次调用保护
*
* return                            - 0表示成功,
*                                   - -1表示注销异常
*/
int FacesdkAPI_DeinitEngine();

/*
     * [主线程]人脸检测，保存裁剪数据
     *
     * pImgData             - 待检测人脸图片数据,
     * inputW               - 输入人脸图片的宽,
     * inputW               - 输入人脸图片的高,
     * return               - 1: FACE_REG_RET_SUCCEED, 注册成功,以时间戳为id在cfg指定目录下存储人脸裁剪图片和对应特征文件
     *                      - 0: FACE_REG_RET_FAIL, 未检测到人脸
     *                      - -1: FACE_REG_ERR_INIT, 模型未初始化或者传入图像异常
     *                      - 0x5001: FACE_STATUS_ERR_BAD_FACENUM, 人脸个数不合规，检测出超过一个人脸
     *                      - 0x5002: FACE_STATUS_ERR_BAD_FACESIZE, 人脸尺寸不合规，过大或过小/过近或过远
     *                      - 0x5003: FACE_STATUS_ERR_BAD_FACEPOSE, 人脸姿态不合规，可能存在歪头/侧脸/俯仰
     *                      - 0x5004: FACE_STATUS_ERR_BAD_FACEQUALITY, 人脸图像质量不佳
     *                      - 0x5005: FACE_STATUS_ERR_BAD_FACEMASK, 人脸图像佩戴口罩
     */
int FacesdkAPI_DoRegFaceSingle(
     const unsigned char* pImgData,
     const int inputW,
     const int inputH);

/*
     * [主线程]人脸检测-视频流
     *
     * pImgData             - 待检测人脸图片,若裁剪成功则更改为裁剪后人脸图片
     * inputW               - 输入人脸图片的宽,
     * inputW               - 输入人脸图片的高,
     * return               - 2: FACE_STATUS_RET_FINISH，人脸注册结束，表明内部已经获取到足够人脸数目
     *                      - 1: FACE_STATUS_RET_SUCCEED，人脸检测成功
     *                      - 0: FACE_STATUS_RET_FAIL, 未检测到人脸
     *                      - -1: FACE_STATUS_ERR_INIT, 模型未初始化或者传入图像异常
     *                      - -2: FACE_STATUS_ERR_BAD_SIMSCORE，表示待注册人脸列表与当前传入人脸不是同一人
     *                      - 0x5001: FACE_STATUS_ERR_BAD_FACENUM, 人脸个数不合规，检测出超过一个人脸
     *                      - 0x5002: FACE_STATUS_ERR_BAD_FACESIZE, 人脸尺寸不合规，过大或过小/过近或过远
     *                      - 0x5003: FACE_STATUS_ERR_BAD_FACEPOSE, 人脸姿态不合规，可能存在歪头/侧脸/俯仰
     *                      - 0x5004: FACE_STATUS_ERR_BAD_FACEQUALITY, 人脸图像质量不佳
     *                      - 0x5005: FACE_STATUS_ERR_BAD_FACEMASK, 人脸图像佩戴口罩
     */
int FacesdkAPI_DoRegFaceLoop(const unsigned char* pImgData,
     const int inputW, 
     const int inputH, 
     const bool restart);

/*
     * [主线程]检查当前学习的人脸特征模型是否为同一人, 该函数要求当前学习状态为LEARN_FACE_FINISHED
     * return               - 1: FACE_REG_RET_SUCCEED, 表示注册成功,以时间戳为id在指定目录下存储最佳人脸裁剪图片和对应特征文件
     *                      - 0：FACE_REG_RET_FAIL, 表示非同一人
     *                      - -1：FACE_REG_ERR_INIT, 表示不满足此接口使用条件
     */
int FacesdkAPI_DoCheckRegFace();

#ifdef __cplusplus
}
#endif
#endif
