/**
 * 
 * Akuvox自研的人脸识别SDK
 * Akuvox Lisence
 * 
 * By LinKy
 * 2018-06-22
 */

/* Header for Facesdk CALLBACK */

#ifndef __FACESDK_CALLBACK_H__
#define __FACESDK_CALLBACK_H__

enum {
    FACESDK_LOG_LEVEL_ERROR = 3,
    FACESDK_LOG_LEVEL_WARN  = 4,
    FACESDK_LOG_LEVEL_INFO  = 6,
    FACESDK_LOG_LEVEL_DEBUG = 7,
};

enum {
    CHECK_FACESIZE_SMALL    =  -1,
    CHECK_FACESIZE_RIGHT    =   0,
    CHECK_FACESIZE_LARGE    =   1,
};

#define MSG_ID_FACESDK_LOG_RESULT            0x4001
#define MSG_ID_FACESDK_SAVE_RESULT           0x4002

#define MSG_ID_FACESDK_CHECK_FACENUM         0x5001
#define MSG_ID_FACESDK_CHECK_FACESIZE        0x5002
#define MSG_ID_FACESDK_CHECK_FACEPOSE        0x5003
#define MSG_ID_FACESDK_CHECK_FACEQUALITY     0x5004
#define MSG_ID_FACESDK_CHECK_FACEMASK        0x5005

/* API Adatper 接口命名遵循Java规范 */
class FacesdkCallBack {
public:
    virtual ~FacesdkCallBack() {}

    /*
     * msgID
     * paramA       - 人脸识别结果数量
     * paramB       - 无, 预留给校验码
     * paramC        - 消息ID及参数, 具体含义详见上方宏定义
     * return        - 0表示清空成功, 其他表示出错
     */
    virtual int Notify(const int msgID, const int paramA, const int paramB, const char*paramC) = 0;
};


#endif

