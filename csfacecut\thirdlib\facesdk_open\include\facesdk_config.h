#ifndef __FACESDK_CONFIG_H__
#define __FACESDK_CONFIG_H__

#include "facesdk_defs.h"

typedef struct {
    // -----------------------------------------------------------
    /*
     *  通用      - facesdk引擎使用过程中图像的维度定义
     */
    int cfgDims = IMG_INPUT_CHANNELS;

    /*
     *  通用      - facesdk引擎使用过程中输入图像的最小高度
     */
    int cfgInputMinWidth = IMG_INPUT_MIN_W;

    /*
     *  通用      - facesdk引擎使用过程中输入图像的最小宽度
     */
    int cfgInputMinHeight = IMG_INPUT_MIN_H;

    /*
     *  通用      - facesdk引擎使用过程中输出图像的宽度定义
     */
    int cfgOutputWidth = FACE_CROP_OUTPUT_W;

    /*
     *  通用      - facesdk引擎使用过程中输出图像的高度定义
     */
    int cfgOutputHeight = FACE_CROP_OUTPUT_H;

    /*
     *  通用      - facesdk引擎使用过程中输出图像的中心缩放比例
     */
    float cfgOutputScale = IMG_PROCESS_SAVE_PADDING_SCALE;

    /*
     *  通用      - facesdk引擎使用过程中人脸使用个数限制
     */
    int cfgFaceLimitNum = LIMIT_MAX_FACE_NUMS;

    /*
     *  通用      - facesdk引擎使用过程中是否进行人脸标准检查
     */
    int cfgFaceStdCheck = DO_FACE_STD_CHECK;

    /*
     *  通用      - facesdk引擎使用过程中人脸面积最小限制
     */
    float cfgFaceLimitArea = LIMIT_MIN_FACE_AREA;

    /*
     *  通用      - facesdk引擎使用过程中人脸图像宽高占比最大限制
     */
    float cfgFaceLimitScale = LIMIT_MAX_FACE_SCALE;

    /*
     *  通用      - facesdk引擎使用过程中人脸图像歪头角度最大限制
     */
    int cfgFaceLimitRoll = LIMIT_FACE_ROLL_ANGLE_MAX;

    /*
     *  通用      - facesdk引擎使用过程中人脸图像侧脸角度最大限制
     */
    int cfgFaceLimitYaw = LIMIT_FACE_YAW_ANGLE_MAX;

    /*
     *  通用      - facesdk引擎使用过程中人脸图像宽俯仰角度最大限制
     */
    int cfgFaceLimitPitch = LIMIT_FACE_PITCH_ANGLE_MAX;

    /*
     *  通用      - facesdk引擎使用过程中人脸图像眼间距最小限制
     */
    int cfgFaceLimitEyeSpan = LIMIT_FACE_EYE_SPAN_MIN;

    /*
     *  通用      - facesdk引擎使用过程中是否存储特征
     */
    int cfgFaceFeatureSave = 0;

    /*
     *  通用      - facesdk引擎使用过程中人脸信息存储位置
     */
    char cfgFaceInfoSavePath[LOG_BUFFER_SIZE];

    /*
     *  通用      - facesdk引擎使用过程中人脸Debug选项
     */
    int cfgFaceDebug = 0;

} FacesdkConfig;

#endif

