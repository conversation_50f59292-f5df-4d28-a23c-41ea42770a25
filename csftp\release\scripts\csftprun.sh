#!/bin/bash
PROCESS_NAME=vsftpd
PROCESS_START_CMD="/usr/local/akcs/csftp/scripts/csftpctl.sh start"
LOG_FILE=/var/log/csftp_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csftp/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csftp/scripts/log_back.sh"
SERVERIP=`cat /etc/ip | grep SERVERIP=  | awk -F '=' '{print $2}'`
csftp_path="/var/log/csftplog"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

mkdir -p /var/run/vsftpd/empty

while [ 1 ]
do
    common_run_netstat_detect $PROCESS_NAME "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done


