CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project(gate C CXX)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 设置路径变量
SET(CSBASE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../csbase")
SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src")
SET(DBINTERFACE_FILES_OUTPUT_DIR "${CMAKE_CURRENT_SOURCE_DIR}")

# 包含生成文件列表的脚本
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

# 设置包含目录和源目录
SET(INC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/include")
SET(SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src")

# 链接目录
LINK_DIRECTORIES(
    ${CSBASE_SOURCE_DIR} 
    ${CSBASE_SOURCE_DIR}/thirdlib 
    ${CSBASE_SOURCE_DIR}/redis/hiredis 
    ${CSBASE_SOURCE_DIR}/evpp/lib
)

# 添加源文件目录
AUX_SOURCE_DIRECTORY(${SRC_DIR} SRC_LIST_GATE)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/cstring SRC_LIST_GATE_CSTRING)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/jsoncpp0.5/src/json SRC_LIST_BASE_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/beanstalk-client SRC_LIST_BASE_BEANSTALK)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/model SRC_LIST_BASE_MODEL)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Character SRC_LIST_BASE_CHARACTER)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Tinyxml SRC_LIST_BASE_TINYXML)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/kafka SRC_LIST_BASE_KAFKA)


# 设置包含目录列表
SET(BASE_LIST_INC 
    ${CSBASE_SOURCE_DIR} 
    ${CSBASE_SOURCE_DIR}/etcd 
    ${CSBASE_SOURCE_DIR}/Rldb 
    ${CSBASE_SOURCE_DIR}/evpp
    ${CSBASE_SOURCE_DIR}/encrypt 
    ${CSBASE_SOURCE_DIR}/beanstalk-client 
    ${CSBASE_SOURCE_DIR}/model
    ${CSBASE_SOURCE_DIR}/dbinterface 
    ${CSBASE_SOURCE_DIR}/dbinterface/office 
    ${CSBASE_SOURCE_DIR}/dbinterface/resident 
    ${CSBASE_SOURCE_DIR}/dbinterface/smarthome 
    ${CSBASE_SOURCE_DIR}/jsoncpp0.5/include 
    ${CSBASE_SOURCE_DIR}/redis 
    ${CSBASE_SOURCE_DIR}/redis/hiredis 
    ${CSBASE_SOURCE_DIR}/grpc
    ${CSBASE_SOURCE_DIR}/grpc/gens
    ${CSBASE_SOURCE_DIR}/Tinyxml
    ${CSBASE_SOURCE_DIR}/kafka
    ${CSBASE_SOURCE_DIR}/metrics
)

# 添加编译选项
ADD_DEFINITIONS(
    -std=gnu++11 
    -g 
    -Wall 
    -ggdb 
    -Werror 
    -Wno-unused-parameter 
    -Wno-deprecated
)

# 包含目录
include_directories(
    ${BASE_LIST_INC} 
    ${INC_DIR}/mysql 
    ${SRC_DIR} 
    ${SRC_DIR}/include 
    ${SRC_DIR}/Common/cstring 
    ${SRC_DIR}/Common/Curl 
    /usr/local/boost/include 
    /usr/local/protobuf/include 
    /usr/local/grpc/include
)

# 添加可执行文件
add_executable(csgate 
    ${SRC_LIST_GATE} 
    ${SRC_LIST_GATE_CSTRING} 
    ${SRC_LIST_BASE_TINYXML}
    ${SRC_LIST_BASE_JSON} 
    ${SRC_LIST_BASE_ETCD} 
    ${SRC_LIST_BASE_ENCRYPT} 
    ${SRC_LIST_BASE_BEANSTALK} 
    ${SRC_LIST_BASE_RLDB} 
    ${SRC_LIST_BASE_MODEL} 
    ${SRC_LIST_BASE_REDIS} 
    ${SRC_LIST_BASE_CHARACTER} 
    ${prefixed_file_list}
    ${SRC_LIST_BASE_METRICS} 
    ${SRC_LIST_BASE_KAFKA}
)

# 设置可执行文件输出路径
SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

# 设置目标属性
set_target_properties(csgate PROPERTIES LINK_FLAGS "-Wl,--rpath=/usr/local/akcs/csgate/lib")

# 链接库
target_link_libraries(csgate 
    pthread 
    mysqlclient 
    iconv 
    curl 
    evpp 
    glog 
    event 
    csbase 
    protobuf  
    boost_system 
    ssl 
    crypto 
    cpprest 
    etcd-cpp-api 
    hiredis  
    gpr 
    grpc 
    grpc++
    cppkafka
    rdkafka
    rdkafka++
)

