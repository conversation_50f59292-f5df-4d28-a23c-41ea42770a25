<?php
     
/**
* AES256加密算法,cbc模式,pkcs5Padding字符填充方式
*/

class AES
{
	public static function encrypt($input, $key, $iv, $base64 = true) 
	{
		$size = 8;
		$input = self::pkcs5_pad($input, $size);
		$encryption_descriptor = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', 'cbc', ''); //数据块128位,ps:AES256是指密钥256位
		mcrypt_generic_init($encryption_descriptor, $key, $iv);
		$data = mcrypt_generic($encryption_descriptor, $input);
		mcrypt_generic_deinit($encryption_descriptor);
		mcrypt_module_close($encryption_descriptor);
		return base64_encode($data);
	}

	public static function decrypt($crypt, $key, $iv, $base64 = true) 
	{
		$crypt = base64_decode($crypt);
		$encryption_descriptor = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', 'cbc', '');
		mcrypt_generic_init($encryption_descriptor, $key, $iv);
		$decrypted_data = mdecrypt_generic($encryption_descriptor, $crypt);
		mcrypt_generic_deinit($encryption_descriptor);
		mcrypt_module_close($encryption_descriptor);
		$decrypted_data = self::pkcs5_unpad($decrypted_data);
		return rtrim($decrypted_data);
	}

	private static function pkcs5_pad($text, $blocksize) 
	{
		$pad = $blocksize - (strlen($text) % $blocksize);
		return $text . str_repeat(chr($pad), $pad);
	}
	private static function pkcs5_unpad($text)
	{
		$pad = ord($text{strlen($text) - 1});
		if ($pad > strlen($text)){
		return false;
		}
		return substr($text, 0, -1 * $pad);
	}
}

echo "请输入user:";
$user = trim(fgets(STDIN));	//要计算md5 故移除换行符

echo "请输入密文:";
$text = fgets(STDIN);

$key = substr(md5($user),0,16)."Akuvox55069013!@";	//加密所需的密钥
$iv = "1234567887654321";	//初始化向量
$resulttext = AES::decrypt($text, $key, $iv);//解密

echo "\n".$resulttext;
?> 
