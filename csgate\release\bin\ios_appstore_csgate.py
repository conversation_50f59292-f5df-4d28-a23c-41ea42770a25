#!/usr/bin/python
import ctypes
import hashlib
import http.server
import inspect

import urllib
import urllib.request
import urllib.parse
import json
import os
import threading
import re
import socket
import time
import sys
import logging
import select
import pymysql

# apt-get install python3-pip && pip3 install pyftpdlib &&  pip3 install pymysql
PORT_NUMBER = 9999

ServerListRet={
"result": 0,
"message": "Get Server List Success",
"datas": 
{
"access_server": "cloudtest7.akuvox.com:8501",
"access_server_ipv6": "[cloudtest7.akuvox.com]:8501",
"web_server": "cloudtest7.akuvox.com",
"web_server_ipv6": "cloudtest7.akuvox.com",
"rest_server": "cloudtest7.akuvox.com:8080",
"rest_server_ipv6": "cloudtest7.akuvox.com:8080",
"rest_server_https": "cloudtest7.akuvox.com:8443",
"rest_server_https_ipv6": "cloudtest7.akuvox.com:8443",
"pbx_server": "cloudtest7.akuvox.com:5070",
"pbx_server_ipv6": "[cloudtest7.akuvox.com]:5070",
"vrtsp_server": "cloudtest7.akuvox.com:554",
"vrtsp_server_ipv6": "[cloudtest7.akuvox.com]:554",
"platform_ver": "4500"
}
}


gate_server=""

def csmaincli_log(msg):
    with open("./csmaincli.log", 'w+') as f:
        f.write(msg)

def updateToken(account, token):
    # 打开数据库连接
    db = pymysql.connect("127.0.0.1", "root", "Ak@56@<EMAIL>", "AKCS")
    # 使用cursor()方法获取操作游标
    cursor = db.cursor()

    # SQL 查询语句
    sql = "update Token set AppToken='%s' where Account='%s';" % (
         token,account)
    try:
        # 执行SQL语句
        e=cursor.execute(sql)
        db.commit()
    except:
        print("Error: unable to fetch data")
    # 关闭数据库连接
    cursor.close()
    db.close()
    return

# This class will handles any incoming request from
# the browser
class myHandlerV6(http.server.BaseHTTPRequestHandler):

    # Handler for the GET requests
    def do_GET(self):
        head = self.headers['api-version']
        arg=dict(urllib.parse.parse_qsl(urllib.parse.urlsplit(self.path).query))
        mpath, margs = urllib.parse.splitquery(self.path)
        if (re.search("^/login", mpath)):
            account=arg['user'].encode('utf-8')
            token = hashlib.md5(account).hexdigest()
            if (head == "4.5"):
                loginAppRet = {
                    "result": 0,
                    "message": "login success",
                    "datas":
                        {
                            "access_server": "cloudtest7.akuvox.com:8501",
                            "access_server_ipv6": "[cloudtest7.akuvox.com]:8501",
                            "web_server": "cloudtest7.akuvox.com",
                            "web_server_ipv6": "[cloudtest7.akuvox.com]",
                            "rest_server": "cloudtest7.akuvox.com:8080",
                            "rest_server_ipv6": "[cloudtest7.akuvox.com]:8080",
                            "rest_server_https": "cloudtest7.akuvox.com:8443",
                            "rest_server_https_ipv6": "[cloudtest7.akuvox.com]:8443",
                            "vrtsp_server": "cloudtest7.akuvox.com:554",
                            "vrtsp_server_ipv6": "[cloudtest7.akuvox.com]:554",
                            "pbx_server": "cloudtest7.akuvox.com:5070",
                            "pbx_server_ipv6": "[cloudtest7.akuvox.com]:5070",
                            "role": "10",
                            "have_public_dev": "0",
                            "token": token,
                            "platform_ver": "4500",
                            "is_init": "1",
                            "show_payment": "0"
                        }
                }
                data = json.dumps(loginAppRet)
            else:
                loginDevRet = {
                    "result": 0,
                    "message": "login success",
                    "datas":
                        {
                            "access_server": "*************:8501",
                            "rest_server": "*************:8080",
                            "rest_server_https": "*************:8443",
                            "web_server": "*************:443",
                            "vrtsp_server": "*************:554",
                            "pbx_server": "*************:5070",
                            "ftp_server": "*************:21",
                            "token": token,
                            "platform_ver": "4500"
                        }
                }
                data = json.dumps(loginDevRet)
            updateToken(account.decode ('gbk'),token)
            self.send_response(200)
            self.send_header('Content-type', "application/json")
            self.end_headers()
            self.wfile.write(data.encode('utf-8'))
        if (re.search("^/servers_list", mpath)):
            data = json.dumps(ServerListRet)
            self.send_response(200)
            self.send_header('Content-type', "application/json")
            self.end_headers()
            self.wfile.write(data.encode('utf-8'))            
        return

    def do_POST(self):
        mpath, margs = urllib.splitquery(self.path)
        datas = self.rfile.read(int(self.headers['content-length']))
        self.do_action(mpath, datas)

    def do_action(self, path, args):
        self.outputtxt(path + args)



# This class will handles any incoming request from
# the browser
class myHandlerV4(http.server.BaseHTTPRequestHandler):

    # Handler for the GET requests
    def do_GET(self):
        head = self.headers['api-version']
        arg=dict(urllib.parse.parse_qsl(urllib.parse.urlsplit(self.path).query))
        mpath, margs = urllib.parse.splitquery(self.path)
        if (re.search("^/login", mpath)):
            account=arg['user'].encode('utf-8')
            token = hashlib.md5(account).hexdigest()
            if (head == "4.5"):
                loginAppRet = {
                        "result": 0,
                        "message": "login success",
                        "datas":
                        {
                        "access_server": "*************:8501",
                        "access_server_ipv6": "[]:8501",
                        "web_server": "*************",
                        "web_server_ipv6": "[]",
                        "rest_server": "*************:8080",
                        "rest_server_ipv6": "[]:8080",
                        "rest_server_https": "*************:8443",
                        "rest_server_https_ipv6": "[]:8443",
                        "vrtsp_server": "*************:554",
                        "vrtsp_server_ipv6": "[]:554",
                        "pbx_server": "*************:5070",
                        "pbx_server_ipv6": "[]:5070",
                        "role": "10",
                        "have_public_dev": "1",
                        "token": token,
                        "platform_ver": "4600",
                        "is_init": "1",
                        "show_payment": "0"
                        }
                        }

                data = json.dumps(loginAppRet)
            else:
                loginDevRet = {
                    "result": 0,
                    "message": "login success",
                    "datas":
                        {
                            "access_server": "*************:8501",
                            "rest_server": "*************:8080",
                            "rest_server_https": "*************:8443",
                            "web_server": "*************:443",
                            "vrtsp_server": "*************:554",
                            "pbx_server": "*************:5070",
                            "ftp_server": "*************:21",
                            "token": token,
                            "platform_ver": "4500"
                        }
                }
                data = json.dumps(loginDevRet)
            updateToken(account.decode ('gbk'),token)
            self.send_response(200)
            self.send_header('Content-type', "application/json")
            self.end_headers()
            self.wfile.write(data.encode('utf-8'))
        if (re.search("^/servers_list", mpath)):
            ServerListRet = {
                "result": 0,
                "message": "Get Server List Success",
                "datas":
                    {
                        "access_server": "*************:8501",
                        "access_server_ipv6": "[cloudtest7.akuvox.com]:8501",
                        "web_server": "*************",
                        "web_server_ipv6": "cloudtest7.akuvox.com",
                        "rest_server": "*************:8080",
                        "rest_server_ipv6": "cloudtest7.akuvox.com:8080",
                        "rest_server_https": "*************:8443",
                        "rest_server_https_ipv6": "cloudtest7.akuvox.com:8443",
                        "pbx_server": "*************:5070",
                        "pbx_server_ipv6": "[cloudtest7.akuvox.com]:5070",
                        "vrtsp_server": "*************:554",
                        "vrtsp_server_ipv6": "[cloudtest7.akuvox.com]:554",
                        "platform_ver": "4500"
                    }
            }
            data = json.dumps(ServerListRet)
            self.send_response(200)
            self.send_header('Content-type', "application/json")
            self.end_headers()
            self.wfile.write(data.encode('utf-8'))
        return

    def do_POST(self):
        mpath, margs = urllib.splitquery(self.path)
        datas = self.rfile.read(int(self.headers['content-length']))
        self.do_action(mpath, datas)

    def do_action(self, path, args):
        self.outputtxt(path + args)

def csgateServer():
    try:
        # Create a web server and define the handler to manage the
        # incoming request
        global  gate_server
        gate_server = http.server.HTTPServer(('0.0.0.0', PORT_NUMBER), myHandlerV4)
        print('Started httpserver on port ', PORT_NUMBER)
        buffer = 1
        sys.stderr = open('9999.log', 'w', buffer)
        # Wait forever for incoming htto requests
        gate_server.serve_forever()
    except KeyboardInterrupt:
        print('^C received, shutting down the web server')
        gate_server.shutdown()
        exit(0)

def csgateServer19999():
    try:
        # Create a web server and define the handler to manage the
        # incoming request
        global  gate_server1
        gate_server1 = http.server.HTTPServer(('0.0.0.0', 19999), myHandlerV6)
        print('Started httpserver on port ', 19999)
        buffer = 1
        sys.stderr = open('19999.log', 'w', buffer)
        # Wait forever for incoming htto requests
        gate_server1.serve_forever()
    except KeyboardInterrupt:
        print('^C received, shutting down the web server')
        gate_server1.shutdown()
        exit(0)


#杀死进程
#os.system("ps -ef | grep  csgaterun | grep -v grep | awk '{print $2}' | xargs -i kill -9 {}")
#os.system("killall csgate")
#logging.basicConfig(filename='./autotest.log', level=logging.DEBUG)


gate= threading.Thread(target=csgateServer,args=())#创建线程
#gate.setDaemon(True)#设置为后台线程，这里默认是False，设置为True之后则主线程不用等待子线程
gate.start()#开启线程

gate2= threading.Thread(target=csgateServer19999,args=())#创建线程
#gate2.setDaemon(True)#设置为后台线程，这里默认是False，设置为True之后则主线程不用等待子线程
gate2.start()#开启线程

gate.join()
gate2.join()
