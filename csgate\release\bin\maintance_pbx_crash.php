<?php

const db_ip = "127.0.0.1";//master db ip
const redis_ip = "127.0.0.1";//master redis ip
const crash_pbx_ip= "127.0.0.1:5070";//需要查询的pbxip

const redis_master_port = 8504;//redis 端口
const redis_slave_port = 8505;//redis 端口
const redis_passwd = "Akcs#xm2610*";//redis 密码

if (strstr(db_ip, "127.0.0.1") || strstr(redis_ip, "127.0.0.1") || strstr(crash_pbx_ip, "127.0.0.1"))
{
    echo "please change script db_ip and redis_ip and crash_pbx_ip\n";
    exit(1);
}

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbip = db_ip;
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$gmacs= array();

$redis = new Redis();
$ret = $redis->connect(redis_ip, redis_master_port);
if ($ret)
{
     $redis->auth(redis_passwd);
}

$redis->select(16);
$keys = $redis-> keys('pbxipv4_*');

$chunk_result = array_chunk($keys, 100);
foreach($chunk_result as $key_row => $values)
{
    $values = $redis->mget($values);
    foreach($values as $row => $pbx)
    {
        if (strstr(crash_pbx_ip, $pbx))
        {
            $mac = $chunk_result[$key_row][$row];
            $mac = explode('pbxipv4_', $mac)[1];
            array_push($gmacs, $mac);
        }
    }
}

$db = getDB();
$chunk_result = array_chunk($gmacs, 100);
foreach($chunk_result as $key_row => $macs)
{
	$ids = '';
	foreach ($macs as $row => $data)
	{
		if (strlen($ids) == 0 )
		{
			$ids = "'$data'";
		}
		else 
		{
			$ids = $ids . ",'" . $data. "'";
		}
	}
    $sth = $db->prepare("select Mac,AccSrvID From PersonalDevices where Mac in($ids) and DclientVer < 6100 union all select Mac,AccSrvID From Devices where Mac in($ids) and DclientVer < 6100;");
    $sth->execute();
    $accs = $sth->fetchALL(PDO::FETCH_ASSOC);
    updateDevServer($accs);
}

function updateDevServer($accs)
{
    $count = count($accs);
    if($count == 0){
        return;
    }
    $fh = fopen('php://stdin', 'r');  
    echo "total devices:$count do you want to reboot devices. yes?no: ";
    $str = fread($fh, 100);
    if (!strstr($str, "yes"))
    {
        return;
    }
    foreach($accs as $v)
    {
        $mac = $v["Mac"];
        $srv = $v["AccSrvID"];
        /*
            $url = "curl -s \"http://$srv:9998/updateDevServer?type=pbx&mac=$mac\"";
            $ret=shell_exec($url);
            echo "send:$url\n";
            $value = json_decode($ret, true);
            if ($value["result"] != 0 )
            {
                echo "ret:$ret\n";
            }
        */
        $url = "curl -s -d '{\"mac\":\"{$mac}\"}'  \"http://{$srv}:9998/rebootDev\"";
        $ret=shell_exec($url);
        echo "send:$url\n";
        $value = json_decode($ret, true);
        if ($value["result"] != 0 )
        {
            echo "ret:$ret\n";
        }        
        // /notifyDevUpdateServer?type=ftp&mac=xxxxxxxxxx; type目前支持:ftp/pbx/rtsp/access/web
        //updateDevServer 
        # /rebootDev {"mac":"all"}
    }
}






















