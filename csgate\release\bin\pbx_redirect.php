<?php
date_default_timezone_set("PRC");


const CONF_PATH="/usr/local/akcs/csgate/conf/csgate.conf";

function getDB()
{
    @$conf=parse_ini_file(CONF_PATH);
    $dbhost = $conf["db_ip"];
    $dbuser = $conf["db_username"];
    $dbpass = $conf["db_passwd"];
    $dbname = $conf["db_database"];
    $dbport = $conf["db_port"];

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

//insert into PbxServerList(PbxIp,PbxIpv6) value("*************:5070","");
//insert into PbxRedirectInfo(CommunitID,Node,PbxID) value("","",1);
function ListDirectInfo()
{
    $db = getDB();
    //不做表是否存在的处理，因为如果不存在也就直接报错退出了
    echo "PbxServerList\n";
    $sth = $db->prepare("select *From PbxServerList;");
    $sth->execute();
    $PbxRedirectInfo = $sth->fetchALL(PDO::FETCH_ASSOC);

    echo "ID,PbxIp,PbxIpv6\n";
    foreach ($PbxRedirectInfo as $row => $info)
    {
        echo $info["ID"].",". $info["PbxIp"].",".$info["PbxIpv6"]."\n";
    }

    echo "\n\n\nPbxRedirectInfo\n";
    echo "ID,CommunitID,Node,PbxID\n";
    $sth = $db->prepare("select *From PbxRedirectInfo;");
    $sth->execute();
    $PbxRedirectInfo = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($PbxRedirectInfo as $row => $info)
    {
        echo $info["ID"].",". $info["CommunitID"].",".$info["Node"].",".$info["PbxID"]."\n";
    }
    echo "\n\n\n";
}

function insertPbxServer($ip, $ipv6)
{
    $db = getDB();
    //不做表是否存在的处理，因为如果不存在也就直接报错退出了
    $sth = $db->prepare("insert into PbxServerList(PbxIp,PbxIpv6) value(:PbxIp,:PbxIpv6)");
    $sth->bindParam(':PbxIp', $ip, PDO::PARAM_STR);
    $sth->bindParam(':PbxIpv6', $ipv6, PDO::PARAM_STR);
    $sth->execute();

    echo "ok\n\n\n";
}
function insertRedirectInfo($CommunitID, $Node, $PBXID)
{
    $db = getDB();
    $sth = $db->prepare("select ID from PbxServerList where ID=:ID;");
    $sth->bindParam(':ID', $PBXID, PDO::PARAM_STR);
    $sth->execute();
    $pbx = $sth->fetch(PDO::FETCH_ASSOC); 
    if (!$pbx)
    {
        echo "can not found pbx id $PBXID\n";
        exit;
    }

    $sth = $db->prepare("insert into PbxRedirectInfo(CommunitID,Node,PbxID) value(:CommunitID,:Node,:PBXID);");
    $sth->bindParam(':CommunitID', $CommunitID, PDO::PARAM_STR);
    $sth->bindParam(':Node', $Node, PDO::PARAM_STR);
    $sth->bindParam(':PBXID', $PBXID, PDO::PARAM_STR);
    $sth->execute();
    echo "ok\n\n\n";
}

function searchCommunityInfo($community_location)
{
    $db = getDB();
    $sth = $db->prepare("select A.ID,count(distinct A.ID) as cid ,count(distinct P.ID) as aid   From Account A left Join PersonalAccount P on P.ParentID=A.ID where A.Location=:Location and P.Role=20;");
    $sth->bindParam(':Location', $community_location, PDO::PARAM_STR);

    $sth->execute();
    $clist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($clist as $row => $info)
    {
        echo "\nCommunit ID: ".$info["ID"]." community number: ".$info["cid"]."  user count:". $info["aid"]."\n";
        if ($info["cid"] > 1)
        {
            echo "too many community search for name: $community_location\n";
        }
    }
    echo "\n\n\n";
}

function rebootCommunityInfo($comm_id, $reboot)
{
    $db = getDB();
    $sth = $db->prepare("select Mac,Location,Status From Devices where MngAccountID=:MngAccountID");
    $sth->bindParam(':MngAccountID', $comm_id, PDO::PARAM_STR);

    $sth->execute();
    $clist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($clist as $row => $info)
    {
        echo "\nCommunit Mac: ".$info["Mac"]." location: ".$info["Location"]."  status:". $info["Status"]."\n";
        $mac = $info["Mac"];
        if ($reboot)
        {
            shell_exec("python3.4 /bin/configcat reboot $mac");
        }
    }
    echo "\n\n\n";
}

function echo_help()
{
    echo "help:\n --list\n --insert_pbx --ipv4 xx:5070 --ipv6 [xx]:5070\n --insert_direct --pbx_id <id> --community_id <id> \n --search_community <name> \n --reboot_community <community_id>\n--community_dev_list <community_id>\n";
}

$shortOpts = "";
$longOpts = ["insert_direct","insert_pbx", "list", "help", "ipv4:", "ipv6:", "community_id:", "node:", "pbx_id:", "search_community:", "reboot_community:", "community_dev_list:"]; 
$options = getopt($shortOpts, $longOpts);
#var_dump($options);

if (array_key_exists("help", $options) )
{
    echo_help();
    exit;
}

if ( array_key_exists("list", $options))
{
   ListDirectInfo();
    exit;
}

if (array_key_exists("insert_pbx", $options) )
{
    if ( !array_key_exists("ipv4", $options) )
    {
        echo "help\n must have --ipv4 \n";
        exit;        
    }
    if ( !array_key_exists("ipv6", $options) )
    {
        echo "help\n must have --ipv6\n";
        exit;
    }
    insertPbxServer($options["ipv4"], $options["ipv6"]);
    exit;
}

if (array_key_exists("insert_direct", $options) )
{
    if ( !array_key_exists("pbx_id", $options) )
    {
        echo "help\n miss --pbx_id, can found by --list \n";
        exit;        
    }

     if ( array_key_exists("community_id", $options)  && array_key_exists("node", $options) )
    {
        echo "help\n --community_id and  --node  Can't be used together\n";
        exit;
    }
     if ( !(array_key_exists("community_id", $options)  || array_key_exists("node", $options)) )
    {
        echo "help\n param --community_id or --node need for insert_direct\n";
        exit;
    }


    if ( array_key_exists("community_id", $options))
    {
        insertRedirectInfo($options["community_id"], "", $options["pbx_id"]);
        ListDirectInfo();
        exit;
    }
    if ( array_key_exists("node", $options) )
    {
        insertRedirectInfo("", $options["node"], $options["pbx_id"]);
        ListDirectInfo();
        exit;
    }
    exit;
}

if (array_key_exists("search_community", $options) )
{

    searchCommunityInfo($options["search_community"]);
    exit;
}

if (array_key_exists("community_dev_list", $options) )
{

    rebootCommunityInfo($options["community_dev_list"], 0);
    exit;
}

if (array_key_exists("reboot_community", $options) )
{

    rebootCommunityInfo($options["reboot_community"], 1);
    exit;
}

echo_help();
exit;

?>