#!/usr/bin/python3

import argparse
import json
import subprocess
import logging
import requests
import sys
from logging import handlers
import os



class Logger(object):
    level_relations = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'crit': logging.CRITICAL
    }

    def __init__(self, filename, level='info', when='D', backCount=6,
                 fmt='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'):
        self.logger = logging.getLogger(filename)
        format_str = logging.Formatter(fmt)  # 设置日志格式
        self.logger.setLevel(self.level_relations.get(level))  # 设置日志级别
        sh = logging.StreamHandler()  # 往屏幕上输出
        sh.setFormatter(format_str)  # 设置屏幕上显示的格式
        th = handlers.TimedRotatingFileHandler(filename=filename, when=when, backupCount=backCount,
                                               encoding='utf-8')  # 往文件里写入#指定间隔时间自动生成文件的处理器
        # 实例化TimedRotatingFileHandler
        # interval是时间间隔，backupCount是备份文件的个数，如果超过这个个数，就会自动删除，when是间隔的时间单位，单位有以下几种：
        # S 秒
        # M 分
        # H 小时、
        # D 天、
        # W 每星期（interval==0时代表星期一）
        # midnight 每天凌晨
        th.setFormatter(format_str)  # 设置文件里写入的格式
        self.logger.addHandler(sh)  # 把对象加到logger里
        self.logger.addHandler(th)


log = Logger('/var/log/setappversion.log', level='debug')


class Operation(object):
    def __init__(self):
        self.port = 9999
        self.inner_ip = "127.0.0.1"
        self.check_code = "G@J9N2JuTeWYwxY?i"
        self.basic_url = "http://%s:%d/update_app_latest_version?check_code=%s" % (
            self.inner_ip, self.port, self.check_code)
        self.app_version_dict = dict()
        self.phone_type_dict = dict()
        self.phone_type_dict[0] = "sys_ios"
        self.phone_type_dict[1] = "sys_emui"
        self.phone_type_dict[2] = "sys_flyme"
        self.phone_type_dict[3] = "sys_oppo"
        self.phone_type_dict[4] = "sys_vivo"
        self.phone_type_dict[5] = "sys_miui"
        self.phone_type_dict[6] = "sys_google"
        self.phone_type_dict[7] = "sys_tencent"
        self.phone_type_dict[8] = "sys_other"
        self.sys_platform_dict = {v: k for k, v in self.phone_type_dict.items()}
        self.app_latest_version = ""
        log.logger.debug("basic_url=%s" % self.basic_url)

    def getAppLatestVersion(self):
        log.logger.debug("getAppLatestVersion start")
        json_data = dict()
        json_data["app_latest_version"] = ""
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))
        result_json = json.loads(str(r.content, encoding="utf-8"))
        app_latest_version = result_json["datas"]["app_latest_version"]
        self.parseAppLatestVersion(app_latest_version)
        force_upgrade_version = result_json["datas"]["force_upgrade_version"]
        log.logger.debug("force_upgrade_version=%s" % force_upgrade_version)
        log.logger.debug("getAppLatestVersion end,app_latest_version=%s" % app_latest_version)

    def setAppLatestVersion(self, phone_type, app_version):
        log.logger.debug("setAppLatestVersion start")
        self.getAppLatestVersion()
        self.app_version_dict[phone_type] = app_version
        self.combineVersion()

        json_data = dict()
        json_data["app_latest_version"] = self.app_latest_version
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))
        result_json = json.loads(str(r.content, encoding="utf-8"))
        if result_json['result'] == 0:
            comment = '不同平台的最新版本号用-分隔,比如60010,个位数0表示为平台IOS;6001表示为IOS的最新版本号,0:ios;1:emui;2:flyme;3:oppo;4:vivo;5:miui;6:google;7:tencent;8:other'
            self.updateCsgateConf('app_latest_version', self.app_latest_version, comment)
        log.logger.debug("setAppLatestVersion end")

    def setForceUpgradeVersion(self, app_version):
        log.logger.debug("setForceUpgradeVersion start")
        json_data = dict()
        json_data["force_upgrade_version"] = app_version
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))
        result_json = json.loads(str(r.content, encoding="utf-8"))
        if result_json['result'] == 0:
            comment = 'app force upgrade dclient version'
            self.updateCsgateConf('force_upgrade_version', app_version, comment)
        log.logger.debug("setForceUpgradeVersion end")

    def parseAppLatestVersion(self, app_latest_version):
        version_array = app_latest_version.split('-')
        for version_str in version_array:
            version = int(version_str)
            app_version = int(version / 10)
            phone_type = version % 10
            phone_type_str = self.phone_type_dict[phone_type]
            self.app_version_dict[phone_type_str] = app_version
            log.logger.debug("phone_type=%s,app_latest_version=%s" % (phone_type_str, app_version))

    def combineVersion(self):
        version_str = ""
        for key in self.app_version_dict:
            app_version = self.app_version_dict[key]
            phone_type = self.sys_platform_dict[key]
            version_str = version_str + str(app_version) + str(phone_type) + '-'
        self.app_latest_version = version_str[:-1]
        log.logger.debug("set app_latest_version=%s" % self.app_latest_version)

    def updateCsgateConf(self, key, version, comment):        
        cmd = "sed -i \"s/^%s =.*/%s = %s/g\" /usr/local/akcs/csgate/conf/csgate.conf" %(key, key, version)
        os.system(cmd)      
        log.logger.debug("update /usr/local/akcs/csgate/conf/csgate.conf success.cmd=%s" % cmd)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="update app latest version in csgate")
    parser.add_argument(
        "-o",
        "--operation",
        default="",
        help="operation=get/setl/setf",
        choices=['get', 'setl', 'setf'],
    )

    parser.add_argument(
        "-p",
        "--phonetype",
        default="",
        help="phonetype=sys_ios/sys_emui/sys_flyme/sys_oppo/sys_vivo/sys_miui/sys_google/sys_tencent/sys_other",
        choices=['sys_ios', 'sys_emui', 'sys_flyme', 'sys_oppo', 'sys_vivo', 'sys_miui', 'sys_google','sys_tencent','sys_other'],
    )

    parser.add_argument(
        "-v",
        "--version",
        default="",
        help="app dclient latest version",
    )

    args = parser.parse_args()
    operation = Operation()
    oper_args = args.operation

    if oper_args == 'get':
        operation.getAppLatestVersion()
    elif oper_args == 'setl':
        phone_type = args.phonetype
        app_version = args.version
        operation.setAppLatestVersion(phone_type, app_version)
    elif oper_args == 'setf':
        app_version = args.version
        operation.setForceUpgradeVersion(app_version)
    else:
        log.logger.debug("invalid operation:" + oper_args)
        parser.print_help(sys.stderr)
