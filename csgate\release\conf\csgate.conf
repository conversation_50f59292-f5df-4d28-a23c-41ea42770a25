#csgate conf
#is enable ipv6？
ipv6_enable=1

#csgate addr
csgate_https=
csgate_https_ipv6=

#csrest conf
csrest_addr=dev.akuvox.com:8080
csrest_ipv6=[2001:470:35:1fd::2]:8080
csrest_ssl_addr=dev.akuvox.com:8443
csrest_ssl_ipv6=[2001:470:35:1fd::2]:8443

#db conf
db_ip=127.0.0.1
db_username=dbuser01
db_database=AKCS
db_port=3306

#kafka conf
notify_web_auditlog_topic=notify_web_auditlog
kafka_broker_ip=127.0.0.1:8520

#etcd相关配置信息
etcd_srv_net=http://***********:8507

#不能加443端口 设备和app不一致，需要代码加入
web_ip=127.0.0.1
web_ipv6=[127.0.0.1]

#web 前端域名
web_domain_name=
#web后端域名
web_backend_domain=
beanstalk_ip=
beanstalk_port=8519

#Open Api Server
api_server=**************:7140

#file server
file_server=
file_server_ipv6=

#svn版本号
svn_version=

#是否是中国版本 用于区分ios网页端口
is_china_version=0

#使用http地址,即原本9999端口的地址(兼容旧APP)
new_gate_addr=
new_gate_addr_ipv6=

# app force upgrade dclient version
force_upgrade_version = 6000

# 不同平台的最新版本号用-分隔,比如60010,个位数0表示为平台IOS;6001表示为IOS的最新版本号,0:ios;1:emui;2:flyme;3:oppo;4:vivo;5:miui;6:google;7:tencent;8:other
app_latest_version = 60010-60011-60012-60013-60014-60015-60016-60017-60018

#是否打开限流 0：关闭限流；1：开启限流
limit_switch=1

#限流速率
rate=100

#智能家居域名
smart_home_domain=

have_slb=0
slb_net_segment=

#csgate是否查询AwsRedirect表 做重定向判
aws_redirect=0

server_area=0

#新加坡迁移到日本，日本服务器的网关信息。这个优先级高于new_gate_addr的配置
#在redirect=1 area=2（新加坡服务器）下生效
jp_new_gate_addr=gate.jcloud.akuvox.com:9999
jp_new_gate_addr_ipv6=gate6.jcloud.akuvox.com:19999
#在redirect=2 area=2（新加坡服务器）下生效
au_new_gate_addr=gate.aucloud.akuvox.com:9999
au_new_gate_addr_ipv6=gate6.aucloud.akuvox.com:19999
#在redirect=3下生效
asbj_new_gate_addr=gate.azcloud.akuvox.com:9999
asbj_new_gate_addr_ipv6=gate6.azcloud.akuvox.com:19999

#在redirect=4下生效 欧洲迁移到美国的数据
e2ucloud_new_gate_addr=gate.ucloud.akuvox.com:9999
e2ucloud_new_gate_addr_ipv6=gate6.ucloud.akuvox.com:19999

#redirect=1 area=2下生效，更新token到日本服务器 update_jp_token 在slim发送短信也有用到
update_jp_auth_http_head=http://**************:9999/update_auth
#在jcloud下生效
update_jp_auth_allow_ip=************,*************,***********,**************

#redirect=2 area=2下生效
update_au_auth_http_head=http://***********:9999/update_auth
#在aucloud下生效
update_au_auth_allow_ip=************,*************,***********,**************

#在scloud下生效
update_sjp_auth_allow_ip=**************,**************,*************,***********,************

#redirect=3下生效
update_asbj_auth_http_head=http://***********:9999/update_auth
#asbj鉴权需要
update_asbj_auth_allow_ip=***********,*************,*********

#redirect=4下生效 ucloud网关地址列表
update_e2ucloud_auth_http_head=http://**********:9999/update_auth

#e2ucloud鉴权需要 包含ucloud gate + ecloud gate + ecloud slim + ucloud slim
update_e2ucloud_auth_allow_ip=*************,*********,**********,*************,************,***********,*************,**************,***********,*************,***********



#网关允许哪些dis登陆，在area=7（日本服务器）下生效。注意前后逗号
allow_dis_login=,wavedge,Link,Jnets,Glamo,IIJ,VALTEC,ODI,Moncable,DigitalPower,Daminn,JTS,DOORCOM,cool_jp,testfee_jp,cool,ak_ops,testfee,CIP,Akuvox-kit,SUN,

log_encrypt=0
log_trace=1

# 设备请求login接口 鉴权开关：1=开启鉴权，0=关闭鉴权，默认值=1
device_login_auth_switch=1

# 若小于3分钟 设置为3分钟
token_valid_time=604800

cloud_env=dev

voice_assistant_server_domain=
