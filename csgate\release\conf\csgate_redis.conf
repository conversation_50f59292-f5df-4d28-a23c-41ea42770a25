#configure for motion

csgateCacheInstances=dev_srv_info,pm_tow_factor_auth
# dev与各个服务的分配关系
dev_srv_info_host=***********
dev_srv_info_port=8504
dev_srv_info_db=16
dev_srv_info_maxconncnt=3
dev_srv_info_master=1

# pm登录双重验证
pm_tow_factor_auth_host=127.0.0.1
pm_tow_factor_auth_port=8504
pm_tow_factor_auth_db=31
pm_tow_factor_auth_maxconncnt=2

#如果sentinels有值,代表启动主从，那么_host的配置就不生效，如果没有就是单机
sentinels=



