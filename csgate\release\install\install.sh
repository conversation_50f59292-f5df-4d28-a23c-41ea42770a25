#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-12
# Filename      :   install.sh
# Version       :
# Description   :   csgate 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csgate    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csgate
LOG_PATH=/var/log/csgatelog
CTRL_SCRIPT=csgatectl.sh
RUN_SCRIPT=csgaterun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}


# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

SERVERIP=$(grep_conf 'SERVERIP' $IP_FILE)
SERVERIPV6=$(grep_conf 'SERVERIPV6' $IP_FILE)
CLOUD_ENV=$(grep_conf 'cloud_env' $IP_FILE)


ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
WEB_DOMAIN_NAME=$(grep_conf 'WEB_DOMAIN' $INSTALL_CONF)
WEB_BACKEND_DOMAIN=$(grep_conf 'WEB_BACKEND_DOMAIN' $INSTALL_CONF)
WEB_OUTER_IPV4=$(grep_conf 'WEB_OUTER_IPV4' $INSTALL_CONF)
WEB_OUTER_IPV6=$(grep_conf 'WEB_OUTER_IPV6' $INSTALL_CONF)
IS_CHINA_VERSION=$(grep_conf 'IS_CHINA_VERSION' $INSTALL_CONF)
LIMIT_SWITCH=$(grep_conf 'CSGATE_LIMIT_SWITCH' $INSTALL_CONF)
RATE=$(grep_conf 'CSGATE_RATE' $INSTALL_CONF)
ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
OPENAPI_SERVER_DOMAIN=$(grep_conf 'OPENAPI_SERVER_DOMAIN' $INSTALL_CONF)
SMARTHOME_DOMAIN=$(grep_conf 'SMARTHOME_DOMAIN' $INSTALL_CONF)
HAVE_SLB=$(grep_conf 'CSGATE_HAVE_SLB' $INSTALL_CONF)
AWS_REDIRECT=$(grep_conf 'CSGATE_AWS_REDIRECT' $INSTALL_CONF)
SLB_NET_SEGMENT=$(grep_conf 'SLB_NET_SEGMENT' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
SYSTEM_AREA=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
CSGATE_DOMAIN=$(grep_conf 'CSGATE_DOMAIN' $INSTALL_CONF)
CSGATE_DOMAIN_IPV6=$(grep_conf 'CSGATE_DOMAIN_IPV6' $INSTALL_CONF)
FILE_SERVER=$(grep_conf 'FILE_SERVER' $INSTALL_CONF)
FILE_SERVER_IPV6=$(grep_conf 'FILE_SERVER_IPV6' $INSTALL_CONF)
APP_REST_ADDR=$(grep_conf 'APP_REST_ADDR' $INSTALL_CONF)
APP_REST_SSL_ADDR=$(grep_conf 'APP_REST_SSL_ADDR' $INSTALL_CONF)
APP_REST_IPV6=$(grep_conf 'APP_REST_IPV6' $INSTALL_CONF)
APP_REST_SSL_IPV6=$(grep_conf 'APP_REST_SSL_IPV6' $INSTALL_CONF)
KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
VOICE_ASSISTANT_SERVER_DOMAIN=$(grep_conf 'VOICE_ASSISTANT_SERVER_DOMAIN' $INSTALL_CONF || echo '')

# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
app_pids=$(pidof csgate || true)
if [ -n "$app_pids" ]; then
    kill -s $SIGNAL $app_pids
    sleep 2
fi


# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*csrest_addr=.*/csrest_addr=${APP_REST_ADDR}/g
    s/^.*csrest_ssl_addr=.*/csrest_ssl_addr=${APP_REST_SSL_ADDR}/g
    s/^.*csrest_ipv6=.*/csrest_ipv6=${APP_REST_IPV6}/g
    s/^.*csrest_ssl_ipv6=.*/csrest_ssl_ipv6=${APP_REST_SSL_IPV6}/g
    s/^.*web_ip=.*/web_ip=${WEB_OUTER_IPV4}/g
    s/^.*web_ipv6=.*/web_ipv6=${WEB_DOMAIN_NAME}/g
    s/^.*web_domain_name=.*/web_domain_name=${WEB_DOMAIN_NAME}/g
    s/^.*web_backend_domain=.*/web_backend_domain=${WEB_BACKEND_DOMAIN}/g
    s/^.*beanstalk_ip=.*/beanstalk_ip=${BEANSTALKD_IP}/g
    s/^.*api_server=.*/api_server=${OPENAPI_SERVER_DOMAIN}/g
    s/^.*is_china_version=.*/is_china_version=${IS_CHINA_VERSION}/g
    s/^.*limit_switch=.*/limit_switch=${LIMIT_SWITCH}/g
    s/^.*rate=.*/rate=${RATE}/g
    s/^.*have_slb=.*/have_slb=${HAVE_SLB}/g
    s#^.*slb_net_segment=.*#slb_net_segment=${SLB_NET_SEGMENT}#g
    s/^.*smart_home_domain=.*/smart_home_domain=${SMARTHOME_DOMAIN}/g
	s/^.*server_area=.*/server_area=${SYSTEM_AREA}/g
    s/^.*aws_redirect=.*/aws_redirect=${AWS_REDIRECT}/g
    s/^.*file_server=.*/file_server=${FILE_SERVER}/g
    s/^.*file_server_ipv6=.*/file_server_ipv6=${FILE_SERVER_IPV6}/g
    s/^.*csgate_https=.*/csgate_https=https:\/\/${CSGATE_DOMAIN}:8600/g
    s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g
    s/^.*cloud_env=.*/cloud_env=${CLOUD_ENV}/g
    s/^.*voice_assistant_server_domain=.*/voice_assistant_server_domain=${VOICE_ASSISTANT_SERVER_DOMAIN}/g
    s/^.*csgate_https_ipv6=.*/csgate_https_ipv6=https:\/\/${CSGATE_DOMAIN_IPV6}:8600/g" "$PKG_ROOT"/conf/csgate.conf

# dbproxy 配置
if [ "$ENABLE_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csgate.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csgate.conf
fi

# redis 配置
sed -i "
    s/^.*dev_srv_info_host=.*/dev_srv_info_host=${REDIS_INNER_IP}/g 
	s/^.*pm_tow_factor_auth_host=.*/pm_tow_factor_auth_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csgate_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csgate_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csgate_redis.conf
fi


echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

if [ -d /usr/local/akcs/csgate_scripts ]; then
    rm -rf /usr/local/akcs/csgate_scripts
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME
cp -f "$PKG_ROOT"/version $APP_HOME

cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi

ln -sf libcppkafka.so libcppkafka.so.0.4.0
ln -sf librdkafka.so librdkafka.so.1

cd "$PKG_ROOT"
# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts

echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi

ulimit -c unlimited


echo '启动服务'
$APP_HOME/scripts/$CTRL_SCRIPT start
sleep 2

echo '检查服务的运行状态'
$APP_HOME/scripts/$CTRL_SCRIPT status

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi

echo "$APP_NAME install complete."

