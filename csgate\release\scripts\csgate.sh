#!/bin/bash
ACMD="$1"
CSGATE_BIN='/usr/local/akcs/csgate/bin/csgate'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csgate()
{
    nohup $CSGATE_BIN >/dev/null 2>&1 &
    echo "Start csgate successful"
    if [ -z "`ps -fe|grep "csgaterun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csgate/scripts/csgaterun.sh >/dev/null 2>&1 &
    fi
}
stop_csgate()
{
    echo "Begin to stop csgaterun.sh"
    csgaterunid=`ps aux | grep -w csgaterun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csgaterunid}" ];then
	    echo "csgaterun.sh is running at ${csgaterunid}, will kill it first."
	    kill -9 ${csgaterunid}
    fi
    echo "Begin to stop csgate"
    kill -9 `pidof csgate`
    sleep 2
    echo "Stop csgate successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 9999 | grep csgate | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csgate
    else
        echo "csgate is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 9999 | grep csgate | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csgate is already stopping"
    else
        stop_csgate
    fi
    ;;
  restart)
    stop_csgate
    sleep 1
    start_csgate
    ;;
  status)
    cnt=`ss -alnp | grep 9999 | grep csgate | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csgate is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csgate is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

