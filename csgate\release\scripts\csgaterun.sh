#!/bin/bash
PROCESS_NAME=csgate
PROCESS_START_CMD="/usr/local/akcs/csgate/scripts/csgatectl.sh start"
PROCESS_PID_FILE=/var/run/csgate.pid
LOG_FILE=/var/log/csgate_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csgate/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csgate/scripts/log_back.sh"
csgatelog_path="/var/log/csgatelog"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done
