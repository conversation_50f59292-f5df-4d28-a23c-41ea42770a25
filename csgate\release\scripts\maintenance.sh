#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`

# Define functions
cmd_usage() {
  echo "usage: $0 echo"
  echo "       $0 svn_version"
  echo "       $0 decrypt_log_output <switch>"
  echo "       $0 set_rate <limit_switch> <rate>"
  echo "       $0 csgate_status"
  echo "       $0 set_testserver <type: 0-csmain; 1-rtsp; 2-pbx> <uid_or_mac: app uid or dev mac> <ipv4> <ipv6> 设置调度信息"
  echo "       $0 clear_all_testserver 清除所有调度"
  echo "       $0 print_all_testserver 打印调度信息"
  echo "       $0 get_device_login_auth_switch"
  echo "       $0 set_device_login_auth_switch <switch> (0=disable, 1=enable)"
  echo "       $0 metrics"
  exit 0
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi

# Check command
if [[ "$1" == "echo" ]]; then
  echo "curl $SERVER_INNER_IP:9999/echo"
  curl $SERVER_INNER_IP:9999/echo
elif [[ "$1" == "svn_version" ]]; then
  echo "curl $SERVER_INNER_IP:9999/svn_version"
  curl $SERVER_INNER_IP:9999/svn_version
elif [[ "$1" == "decrypt_log_output" ]]; then
  switch=$2
  echo "curl -X POST $SERVER_INNER_IP:9999/decrypt_log_output?switch=$switch"
  curl -X POST $SERVER_INNER_IP:9999/decrypt_log_output?switch=$switch
elif [[ "$1" == "set_rate" ]]; then
  limit_switch=$2
  rate=$3
  echo "curl -X POST $SERVER_INNER_IP:9999/set_rate?limit_switch=$limit_switch&rate=$rate"
  curl -X POST $SERVER_INNER_IP:9999/set_rate?limit_switch=$limit_switch&rate=$rate
elif [[ "$1" == "csgate_status" ]]; then
  echo "curl $SERVER_INNER_IP:9999/csgate_status"
  curl $SERVER_INNER_IP:9999/csgate_status
elif [[ "$1" == "set_testserver" ]]; then
  type=$2
  uid_or_mac=$3
  ipv4=$4
  ipv6=$5
  echo "curl -X POST $SERVER_INNER_IP:9999/set_testserver -d '{\"type\":\"$type\", \"uid_or_mac\":\"$uid_or_mac\", \"ipv4\":\"$ipv4\", \"ipv6\":\"$ipv6\"}'"
  curl -X POST $SERVER_INNER_IP:9999/set_testserver -d "{\"type\":\"$type\", \"uid_or_mac\":\"$uid_or_mac\", \"ipv4\":\"$ipv4\", \"ipv6\":\"$ipv6\"}"
elif [[ "$1" == "get_device_login_auth_switch" ]]; then
  echo "curl http:127.0.0.1:9999/get_device_login_auth_switch"
  curl "http://127.0.0.1:9999/get_device_login_auth_switch"
elif [[ "$1" == "set_device_login_auth_switch" ]]; then
  echo "curl http:127.0.0.1:9999/set_device_login_auth_switch?switch=$2"
  curl "http://127.0.0.1:9999/set_device_login_auth_switch?switch=$2"
elif [[ "$1" == "print_all_testserver" ]]; then
  echo "curl http:127.0.0.1:9999/print_all_testserver"
  curl "http://127.0.0.1:9999/print_all_testserver"
elif [[ "$1" == "clear_all_testserver" ]]; then
  echo "curl http:127.0.0.1:9999/clear_all_testserver"
  curl "http://127.0.0.1:9999/clear_all_testserver"    
elif [[ "$1" == "metrics" ]]; then
  echo "curl $SERVER_INNER_IP:9999/metrics"
  curl $SERVER_INNER_IP:9999/metrics
else
  cmd_usage
fi
