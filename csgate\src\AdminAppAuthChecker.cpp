#include "AdminAppAuthChecker.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AccountUserInfo.h"
#include "Caesar.h"
#include "AkLogging.h"
#include "util_string.h"
#include "Md5.h"
#include "Dao.h"

int AdminAppAuthChecker::HandleCheckAuthToken()
{
    if (0 != strcmp(auth_info_.auth_token, token_info_.auth_token))
    {
        return csgate::ERR_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}

int AdminAppAuthChecker::HandleCheckRefreshToken()
{
    if (0 != strcmp(auth_info_.refresh_token, token_info_.app_refresh_token))
    {
        return csgate::ERR_REFRESH_TOKEN_INVALID;
    }
    
    return csgate::ERR_SUCCESS;
}

int AdminAppAuthChecker::HandleCheckUserPassword()
{
    std::string user;
    //凯撒解密
    user = GetCaeSarStr(auth_info_.user);
    //去除前后空格
    TrimString(user);
    
    if (!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << user;
        return csgate::ERR_PASSWD_INVALID; 
    }
    //账号密码对应的用户信息
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        //密码错误直接return
        if (!csgate::PasswordCorrect(auth_info_.passwd, account_user_info.passwd))
        {
            AK_LOG_WARN << "continuation error, ins app passwd error, login_account:" << user;
            return csgate::ERR_PASSWD_INVALID;
        }
    }
    else
    {
        AK_LOG_WARN << "login failed, user not exist, login_account:" << user;
        return csgate::ERR_USER_NOT_EXIT; 
    }
    //确保账号密码和token对应的是同一用户
    if (0 != strcmp(token_info_.account, account_user_info.main_user_account))
    {
        return csgate::ERR_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}