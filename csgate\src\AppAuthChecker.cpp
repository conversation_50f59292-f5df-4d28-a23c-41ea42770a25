#include "AppAuthChecker.h"
#include "AkcsCommonDef.h"
#include "AkLogging.h"

int AppAuthChecker::HandleAuthCheck()
{
    switch(auth_info_.auth_type)
    {
        case AuthType::CheckRefreshToken:
            return HandleCheckRefreshToken();
        case AuthType::CheckUserPassword:
            return HandleCheckUserPassword();
        case AuthType::CheckAuthToken:
            return HandleCheckAuthToken();
    }
    AK_LOG_WARN << "auth type wrong, pass:" << auth_info_.auth_type;
    return -1;
}