#ifndef _CSGATE_AUTH_CHECKER_H_
#define _CSGATE_AUTH_CHECKER_H_

#include<cstring>
#include <stdint.h>
typedef struct AUTH_INFO_T
{
    uint32_t auth_type;
    char user[128];
    char passwd[128];
    char refresh_token[128];
    char auth_token[128];

    AUTH_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
}AuthInfo;

enum AuthType
{
    None,
    CheckUserPassword,
    CheckAuthToken,
    CheckRefreshToken,
};

class AppAuthChecker
{
public:
    AppAuthChecker() {
        memset(&auth_info_, 0, sizeof(auth_info_));
    }
    AppAuthChecker(const AuthInfo& auth_info) : auth_info_(auth_info){}
    virtual ~AppAuthChecker() {}
    int HandleAuthCheck();

protected:
    AuthInfo auth_info_;
    virtual int HandleCheckRefreshToken() = 0;
    virtual int HandleCheckUserPassword() = 0;
    virtual int HandleCheckAuthToken() = 0;
};


#endif // _CSGATE_AUTH_CHECKER_H_
 