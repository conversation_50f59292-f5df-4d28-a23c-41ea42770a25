#include <string>
#include <random>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include "AppTwoFactorAuth.h"
#include "SafeCacheConn.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/Account.h"
#include "dbinterface/TwoFactorAuthIDCode.h"
#include "CachePool.h"
#include "dbinterface/AccountMap.h"
#include "util_string.h"


//verify_code:邮箱验证码，检查邮箱验证码是否正确
bool AppTwoFactorAuth::IsVerifyCodeCorrect(const std::string &verify_code, const std::string &login_account)
{
    std::string verify_code_key = "APP_TwoFactorAuth_Verify_"+login_account;
    SafeCacheConn cache_conn(g_redis_db_pm_tow_factor_auth);
    if(cache_conn.isConnect())
    {
        std::string redis_verify_code = cache_conn.get(verify_code_key);
        if(redis_verify_code == verify_code)
        {
            return true;
        }
    }
    return false;
}

int AppTwoFactorAuth::CheckTwoFactorAuth(const std::string& user_name, const std::string& id_code)
{
    UserInfoAccount account_info;
    if(0 !=dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user_name, account_info))
    { 
        return csgate::ERR_USER_NOT_EXIT;
    }
        //不需要双重认证直接返回
    if (account_info.two_factor_auth != 1) 
    {
        return csgate::ERR_SUCCESS;
    }
    
    //开启双重认证，且id_code不存在或者为校验不正确，需要进行双重认证
    //检查看数据库中是否存在IDCode,没有的话则进行双重认证。查询到的话则判断是否过期。
    TwoFactorAuthIDCodeInfo two_factor_auth_id_code_info;
    if (0 != dbinterface::TwoFactorAuthIDCode::GetTwoFactorAuthIDCodeByAccountUserInfoUUIDAndIDCode(account_info.uuid, id_code, two_factor_auth_id_code_info)) 
    {
        return csgate::ERR_NEED_TWO_FACTOR_AUTH;
    }
    if(two_factor_auth_id_code_info.not_expired)
    {
        return csgate::ERR_SUCCESS;
    }
    return csgate::ERR_NEED_TWO_FACTOR_AUTH;
}

//生成临时token，用于slim接口鉴权generate_check_digit
std::string AppTwoFactorAuth::GenerateTempToken(const std::string& user_name)
{
    //生成六位的鉴权码
    std::string temp_token = GetNbitRandomString(TEMP_TOKEN_LENGTH);
    
    std::string temp_token_key = "APP_TwoFactorAuth_Temp_Token_"+user_name;
    SafeCacheConn cache_conn(g_redis_db_pm_tow_factor_auth);
    if(cache_conn.isConnect())
    {
        // 设置指定过期时间为600秒（10分钟）
        cache_conn.setex(temp_token_key, CHECK_DIGIT_CODE_EXPIRED_TIME, temp_token);
    }
    
    return temp_token;
}


