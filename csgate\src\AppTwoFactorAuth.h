#include <string>
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/Account.h"

#define TEMP_TOKEN_LENGTH 6 
#define CHECK_DIGIT_CODE_EXPIRED_TIME 600 
#define ID_CODE_LENGTH 36


class AppTwoFactorAuth
{
public:
    AppTwoFactorAuth(){};
    ~AppTwoFactorAuth(){};
    static bool IsVerifyCodeCorrect(const std::string &verify_code, const std::string &login_account);
    static int CheckTwoFactorAuth(const std::string& user_name, const std::string& id_code);
    static std::string GenerateTempToken(const std::string& user_name);
};
