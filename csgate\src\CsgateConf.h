#ifndef __CSGATE_CONF_H__
#define __CSGATE_CONF_H__

#define CSMAIN_CONF_COMMON_LEN 128
const unsigned int g_ipaddr_check_len = 5;
const unsigned short kRtspsPort = 8602;
const unsigned short kSipsPort = 8603;

typedef struct CSGATE_CONF_T
{
    /* rest服务配置信息 */
    char rest_addr[CSMAIN_CONF_COMMON_LEN];
    char rest_ipv6[CSMAIN_CONF_COMMON_LEN];
    char rest_ssl_addr[CSMAIN_CONF_COMMON_LEN];
    char rest_ssl_ipv6[CSMAIN_CONF_COMMON_LEN];

    //etcd集群的地址
    char etcd_server_addrs[CSMAIN_CONF_COMMON_LEN];
    
    /* DB配置项 */
    char db_ip[CSMAIN_CONF_COMMON_LEN];
    char db_username[CSMAIN_CONF_COMMON_LEN];
    char db_password[CSMAIN_CONF_COMMON_LEN];
    char db_database[CSMAIN_CONF_COMMON_LEN];
    int  db_port;
    int  enable_ipv6;

    /* kafka配置项 */
    char kafka_broker_ip[CSMAIN_CONF_COMMON_LEN];
    char notify_web_auditlog_topic[CSMAIN_CONF_COMMON_LEN];

    /* Rtsp服务配置信息 */
    char rtsp_config[CSMAIN_CONF_COMMON_LEN];
    char rtsp_ipv6_config[CSMAIN_CONF_COMMON_LEN];
    
    /* ftp服务配置信息 */
    char ftp_ip[CSMAIN_CONF_COMMON_LEN];
    char ftp_domain[CSMAIN_CONF_COMMON_LEN];
    char ftp_ipv6[CSMAIN_CONF_COMMON_LEN];

    /* pbx服务配置信息 */
    char pbx_ip[CSMAIN_CONF_COMMON_LEN];
    char pbx_domain[CSMAIN_CONF_COMMON_LEN];
    char pbx_ipv6[CSMAIN_CONF_COMMON_LEN];

    /* web服务配置信息 */
    char web_ip[CSMAIN_CONF_COMMON_LEN];
    char web_ipv6[CSMAIN_CONF_COMMON_LEN];
    char web_domain_name[CSMAIN_CONF_COMMON_LEN];//面向app,web前端域名
    char web_backend_domain[CSMAIN_CONF_COMMON_LEN];//web后端域名

    /* api服务配置信息*/
    char api_server[CSMAIN_CONF_COMMON_LEN];

    /* https file服务配置信息*/
    char file_server[CSMAIN_CONF_COMMON_LEN];
    char file_server_ipv6[CSMAIN_CONF_COMMON_LEN];

    /*svn版本号*/
    char svn_version[CSMAIN_CONF_COMMON_LEN];
    int  is_china_version;//国内版本ios网页服务器走9443端口

    /*更新APP gate*/
    char new_gate_addr[CSMAIN_CONF_COMMON_LEN];
    char new_gate_ipv6_addr[CSMAIN_CONF_COMMON_LEN];

    /*解密日志输出*/
    int  decrypt_log_ouput;

    /*app最新版本号:版本号+设备型号,比如61000,个数位0表示平台ios,6100表示ios的最新版本号,多个平台型号用-分隔*/
    char app_latest_version[128];

    /*app需要强制升级的版本号*/
    int force_upgrade_version;

    /*是否开启限流;0:关闭限流; 1:开启限流*/
    int limit_switch;

    /*限流速率*/
    int rate;
    /*smart home domain*/
    char smart_home_domain[CSMAIN_CONF_COMMON_LEN];
    /*slb*/
    char slb_net_segment[CSMAIN_CONF_COMMON_LEN];
    int have_slb;
    int aws_redirect;

    char server_tag[CSMAIN_CONF_COMMON_LEN];
    int server_area;
    /*更新日本 APP gate*/
    char jp_new_gate_addr[CSMAIN_CONF_COMMON_LEN];
    char jp_new_gate_ipv6_addr[CSMAIN_CONF_COMMON_LEN];
    char allow_dis_list[1024];   
    char update_jp_auth_http_head[128];//更新auth
    char update_jp_auth_allow_ip[128];//更新auth ip白名单
    char update_sjp_auth_allow_ip[128];//更新auth ip白名单   

    //aucloud迁移
    char au_new_gate_addr[128];
    char au_new_gate_ipv6_addr[128];
    char update_au_auth_http_head[128];
    char update_au_auth_allow_ip[128];

    //asbj迁移
    char asbj_new_gate_addr[128];
    char asbj_new_gate_ipv6_addr[128];
    char update_asbj_auth_http_head[128];
    char update_asbj_auth_allow_ip[128];

    //欧洲迁移到美国
    char e2ucloud_new_gate_addr[128];
    char e2ucloud_new_gate_ipv6_addr[128];
    char update_e2ucloud_auth_http_head[128];
    char update_e2ucloud_auth_allow_ip[128];

    
    char csgate_https[128];
    char csgate_https_ipv6[128];
    
    int token_valid_time;

    // login接口 鉴权开关：1=开启鉴权，0=关闭鉴权（默认值=1）
    int device_login_auth_switch;

    char mqtt_addr[128];

    char server_inner_ip[32]; // 内网ip

    char cloud_env[32];
    char auto_test_dispatch_uid[64];//自动化设置uid 需要进行调度到新网关
    char auto_test_newgate_addr[128];//自动化设置uid 调度的网关地址
    int auto_test_newgate_addr_time;//自动化调度设置新网关的时间

    char voice_assistant_server_domain[128];
} CSGATE_CONF;

typedef struct AWS_CSGATE_CONF_T
{
    char csvrtsp_ip[256];
    char csvrtsp_ipv6[256];
    char csvrtsp_domain[256];
    char csmain_ip[256];
    char csmain_ipv6[256];
    char csmain_domain[256];
    char ftp_ip[256];
    char ftp_ipv6[256];
    char ftp_domain[256];
    char pbx_ip[256];
    char pbx_ipv6[256];
    char pbx_domain[256];

    char web_ipv6[256];
    char web_domain[256];  
    char web_backend_domain[256];
    char rest_ipv6[256];
    char rest_addr[256];
    char rest_ssl_ipv6[256];
    char rest_ssl_addr[256];
        
}AWS_CSGATE_CONF;

typedef struct OUTER_SERVER_ADDR_T
{
    char csvrtsp_ipv6[128];
    char csvrtsp_domain[128];
    char csmain_ipv6[128];
    char csmain_domain[128];
    char pbx_ipv6[128];
    char pbx_domain[128];
}OUTER_SERVER_ADDR;



#endif //__CSGATE_CONF_H__

