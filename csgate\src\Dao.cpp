#include "ConnectionPool.h"
#include <evpp/logging.h>
#include "RldbQuery.h"
#include "Md5.h"
#include "Dao.h"
#include "AES256.h"
#include "util_cstring.h"
#include "util.h"
#include "ServerMng.h"
#include "AkcsCommonDef.h"
#include "CsgateConf.h"
#include "dbinterface/Account.h"
#include "HttpResp.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/DevLoginLog.h"
#include "dbinterface/AppLoginLog.h"
#include "dbinterface/smarthome/SmartHomeDeviceMap.h"
#include "dbinterface/smarthome/SmartHomeUserMap.h"
#include "dbinterface/Verification.h"
#include "dbinterface/InsToken.h"
#include "dbinterface/Token.h"
#include "dbinterface/PerNodeDevices.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/DtPbxServer.h"
#include "dbinterface/PbxServer.h"
#include "dbinterface/AppPushTokenDB.h"
#include "dbinterface/PmAccountMap.h"
#include "dbinterface/AwsRedirect.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Account.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/AccountMap.h"
#include "dbinterface/SystemSettingTable.h"
#include "AkLogging.h"
#include "AppTwoFactorAuth.h"
#include "dbinterface/new-office/OfficeAdmin.h"


#define TOKEN_CHANGE_TIME 1296000 //token 变换时间15天
#define SMS_CODE_LIMITED_TIME 300 //短信验证码时效5分钟

//小于1天进行token同步
const int g_jp_update_token_judge_time = 86400; 
//本地进行延长1小时,在判断迁移时候小于g_jp_update_token_judge_time进行token同步
const int g_jp_update_token_local_extend_time= 3600; 
//更新token延长15天
const int g_jp_token_extend_time = 1296000;

extern CSGATE_CONF gstCSGATEConf;

namespace csgate
{

int DaoSmarthomeDev(const std::string &user, const std::string &passwd, std::string &device_id)
{
    if(!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,User=" << user;
        return ERR_USER_NOT_EXIT;
    }
    
    std::string dev_mac = user;
    dev_mac += DEFAULT_CSGATE_KEY_MASK;

    if (passwd == akuvox_encrypt::MD5(dev_mac).toStr())
    {
        /*
        if (ERR_USER_NOT_EXIT == DaoInsertDevLoingLog(user))
        {
            return ERR_USER_NOT_EXIT;
        }
        */

        if (0 == dbinterface::SmartHomeDeviceMap::GetSmartHomeUUIDByMac(user, device_id))
        {
            return ERR_SUCCESS;
        }
    }
    return ERR_USER_NOT_EXIT;
}

int DaoSmarthomeAccount(const std::string &user, std::string &sm_uuid)
{
    if(!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,User=" << user;
        return -1;
    }

    if (0 == dbinterface::SmartHomeDeviceMap::GetSmartHomeUUIDByMac(user, sm_uuid))
    {
        return 0;
    }

    return -1;
}

int DaoCheckPhone(const std::string& phone, const std::string& code, const std::string& area_code, PersonalAccountInfo& personal_account_info)
{
    ResidentPerAccount account;
    PerAccountUserInfo user_info;
    memset(&account, 0, sizeof(account));
    
    if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(phone, user_info))
    {
        std::string last_login_account = user_info.last_login_account;
        std::string main_user_account = user_info.main_user_account;
       
        // 若last_login_account被删除查找不到,再查询main_account
        if (last_login_account.size() == 0 || 0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(last_login_account, account))
        {
            if (0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(main_user_account, account))
            {
                AK_LOG_WARN << "get user info failed, user not exist, phone=" << phone;
                return ERR_USER_NOT_EXIT;
            }
        }
        AK_LOG_INFO << "phone:" << phone << ",last_login_account: " << user_info.last_login_account << ",main_account:" << user_info.main_user_account;

        char uid[64] = "";
        int is_expire = 0;
        int is_code_expire = 0;
        int active = 0;
        Snprintf(uid, sizeof(uid), account.account);
        active = account.active;
        is_expire = account.is_expire;
        personal_account_info.role = account.role;
        personal_account_info.uid = uid;

        // 获取验证码
        VerificationPtr code_info;
        dbinterface::VerificationCode::GetVerificationCode(account.account, code_info);
        std::string tmp_code = code_info->code;
        is_code_expire = code_info->is_expire;
        personal_account_info.parent_id = account.parent_id;
        Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), main_user_account.c_str());

        // 校验区号
        if (area_code.length() > 0 && strcmp(area_code.c_str(), account.phone_code) != 0)
        {  
            AK_LOG_WARN << "area_code error, area_code = " << area_code << ",real area_code = " << account.phone_code;
            return ERR_USER_NOT_EXIT;
        }
        //验证码错误 err_code==2
        if (strcasecmp(code.c_str(), tmp_code.c_str()) || is_code_expire)
        {
            return ERR_PASSWD_INVALID;
        }
        //账号过期 err_code==3
        if ((active) && (is_expire))
        {
            return ERR_APP_EXPIRE;
        }
        //未激活 err_code==4
        if ((!active) && (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN))
        {
            return ERR_APP_UNACTIVE;
        }
        //从账号未激活,查询下对应的主账号
        int main_active = 0;
        if ((!active) && ((personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT) || (personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)))
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(personal_account_info.parent_id, main_account))
            {
                main_active = main_account.active;
            }

            if (main_active)//主账号已经激活,从账号未激活,则证明是额外的app
            {
                return ERR_APP_UNPAID;
            }
            else
            {
                return ERR_APP_UNACTIVE;
            }
        }
        
        dbinterface::VerificationCode::DeleteVerificationCode(account.account);
        return ERR_SUCCESS;
    }
    
    return ERR_USER_NOT_EXIT;
}

int DaoUpdateTokenRenewInfo(const std::string& user, const std::string& main_user, TokenRenewInfo& token_renew_info)
{
    unsigned int exp_time = 0;
    unsigned int limited_time = gstCSGATEConf.token_valid_time;
    unsigned int cur_time = GetCurrentTimeStamp();

    exp_time = cur_time + limited_time;

    AK_LOG_INFO << "user:" << user << ", main_user:" << main_user 
                        << ", token:" << token_renew_info.token << ", refresh token:" << token_renew_info.refresh_token;

    int ret = dbinterface::Token::InsertOrUpdateTokenRenewInfo(user, main_user, token_renew_info, exp_time);
    return ret;
}

int DaoUpdateToken(const std::string& user, const std::string& main_user, const std::string& token, const float ver)
{
    unsigned int exp_time = 0;
    unsigned int limited_time = gstCSGATEConf.token_valid_time;
    unsigned int cur_time = GetCurrentTimeStamp();
    
    if (ver > 4.5)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUserAccount(user, account))
        {
            exp_time = cur_time + limited_time;
        }
    }
    else
    {
        exp_time = **********;  //兼容旧版本,用户token时效至2029-01-01 00:00:00
    }

    AK_LOG_INFO << "user:" << user << ",main_user:" << main_user << ",token:" << token;
    int ret = dbinterface::Token::InsertOrUpdateToken(user, token, main_user, exp_time);
    return ret;
}

int DaoInsertOrUpdateInsToken(const std::string& uuid, std::string& token)
{
    unsigned int exp_time = 0;
    unsigned int limited_time = gstCSGATEConf.token_valid_time;
    unsigned int cur_time = GetCurrentTimeStamp();

    exp_time = cur_time + limited_time;

    int ret = dbinterface::InsToken::InsertOrUpdateInsToken(uuid, token, exp_time, gstCSGATEConf.server_tag);
    return ret;
}

int DaoInsertOrUpdateInsRenewToken(const std::string& uuid, const TokenRenewInfo& token_renew_info)
{
    unsigned int exp_time = 0;
    unsigned int limited_time = gstCSGATEConf.token_valid_time;
    unsigned int cur_time = GetCurrentTimeStamp();

    exp_time = cur_time + limited_time;

    int ret = dbinterface::InsToken::InsertOrUpdateInsTokenRenewInfo(uuid, exp_time, token_renew_info, gstCSGATEConf.server_tag);
    return ret;
}

int DaoUpdateAuthToken(const std::string& user, const std::string& main_user, std::string& token)
{
    return dbinterface::Token::InsertOrUpdateAuthToken(user, main_user, token);
}



int DaoGetUserConf(const PersonalAccountInfo& personal_account_info, USER_CONF& user_conf)
{
    int id = personal_account_info.id;
    int is_init = personal_account_info.is_init;
    int parent_id = personal_account_info.parent_id;
    
    user_conf.is_show_tempkey = personal_account_info.is_show_tmpkey;        
    user_conf.mng_account_id = parent_id;
    user_conf.role = personal_account_info.role;

    if (user_conf.role == ACCOUNT_ROLE_COMMUNITY_MAIN || 
        user_conf.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT ||
        user_conf.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        user_conf.have_public_dev = 1;
    }
    else if (user_conf.role > 0)
    {
        if (user_conf.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
        {
            if (dbinterface::PerNodeDevices::CheckNodeIDExist(id))
            {
                user_conf.have_public_dev = 1;
            }
        }
        else
        {
            if (dbinterface::PerNodeDevices::CheckNodeIDExist(parent_id))
            {
                user_conf.have_public_dev = 1;
            }
        }
    }
    if (ACCOUNT_ROLE_PERSONNAL_MAIN == user_conf.role)
    {
        user_conf.initialization = 1;
    }
    else if (ACCOUNT_ROLE_COMMUNITY_MAIN == user_conf.role || ACCOUNT_ROLE_COMMUNITY_PM == user_conf.role)
    {
        CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(parent_id);
        //设置不允许使用PIN则返回已初始化过，APP便不会跳转到设置页面
        //******** 有登录就初始化
       user_conf.initialization = comm_info->IsAllowCreatePin() ? is_init:1;
    }
    else
    {
        user_conf.initialization = 1;//社区从账号+个人从账号=1默认初始化过
    }

    //V6.0 TempKeyPermission 已经移到userconf, 过几个版本移除掉
    if (user_conf.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || user_conf.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        ResidentPerAccount main_account;
        memset(&main_account, 0, sizeof(main_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(parent_id, main_account))
        {
            if (user_conf.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
            {
                user_conf.is_show_tempkey = main_account.is_show_tmpkey;  //社区从账号用主账号的TempKey权限,个人没有TempKey权限配置
            }
            user_conf.mng_account_id = main_account.parent_id;
        }
    }
    return 0;
}

std::string DaoGetMacNode(const std::string& mac, int& mac_id)
{
    std::string node;
    ResidentDev per_dev;
    memset(&per_dev, 0, sizeof(per_dev));
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        node = dev.node;
        mac_id = dev.id;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        node = per_dev.node;
        mac_id = per_dev.id;
    }
    return node;

}

std::string DaoGetUidNode(const std::string& uid)
{
    //先查询是否为主账号
    int role = 0;
    std::string node;
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 == dbinterface::ResidentPersonalAccount::InitAccountByUid(uid, account))
    {
        node = account.account;
        role = account.role;
    }

    if (role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        ResidentPerAccount main_account;
        memset(&main_account, 0, sizeof(main_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(account.parent_id, main_account))
        {
            node = main_account.account;
        }
    }

    return node;
}


int DaoInsertAppLoingLog(const std::string& uid)
{
    std::string node = DaoGetUidNode(uid);
    int ret = dbinterface::AppLoginLog::InsertAppLoginLog(uid, node);
    return ret;
}

int DaoInsertDevLoingLog(const std::string& mac)
{
    int mac_id = 0;
    std::string node = DaoGetMacNode(mac, mac_id);
    
    if (0 == mac_id)
    {
        AK_LOG_WARN << "Device not added to cloud,MAC:" << mac;
        return ERR_USER_NOT_EXIT;  //MAC不存在   return 1
    }

    int ret = dbinterface::DevLoginLog::InsertDevLoginLog(mac, node);
    return ret;
}

int DaoCheckToken(const std::string &token, PersonalAccountInfo &personal_account_info, const float ver)
{
    if (!HttpCheckSqlParam(token))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Token=" << token;
        return ERR_TOKEN_INVALID;
    }

    unsigned int cur_time;
    unsigned int exp_time;
    TokenInfo token_info;
    if (0 == dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        Snprintf(personal_account_info.account, sizeof(personal_account_info.account), token_info.account);
        Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), token_info.app_main_account);
        personal_account_info.uid = personal_account_info.account;
        if (ver > 4.5)
        {
            exp_time = token_info.app_tokenet;
            cur_time = GetCurrentTimeStamp();
            if (exp_time < cur_time) //token时效性判断
            {
                return ERR_TOKEN_INVALID;
            }
        }

        //记录审计日志
        if (0 != dbinterface::AppLoginLog::InsertAppLoginLog(personal_account_info.account, ""))
        {
            return -1;
        }

        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::InitAccountByUid(token_info.account, account))
        {
            personal_account_info.role = account.role;
            personal_account_info.parent_id = account.parent_id;
            int active = account.active;
            int is_expire = account.is_expire;

            if (!active) //todo by chenyc, 这个时候需要查询下，是否从账号重新添加、删除，导致可以自动激活.
            {
                return ERR_APP_UNACTIVE;
            }
            //nExpTime的时间戳 可能大于2038年，int包含不了。MySQL出来是0
            //modified by chenyc,2019-07-24,v4.4之前版本的app过期不再拦截
            if (ver > 4.4 && is_expire)
            {
                return ERR_APP_EXPIRE;
            }
        }
        else
        {
            personal_account_info.is_dev = 1;
        }
    }
    else
    {
        return ERR_TOKEN_INVALID;
    }
    
    return ERR_SUCCESS;
}
//ins app token校验
int DaoCheckInsToken(const std::string &token, const std::string &user)
{
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    std::string userinfo_uuid;
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        userinfo_uuid = account_user_info.uuid; 
    }
    int ret = -1;
    InsTokenInfo token_info;
    ret = dbinterface::InsToken::GetInsTokenInfo(userinfo_uuid, token_info);
    if(-1 == ret)
    {
        AK_LOG_WARN << "get token info failed, user invalid";
        return ERR_USER_NOT_EXIT;
    }
    if(token_info.app_token != token)
    {
        AK_LOG_WARN << "installer token wrong";
        return -1;//适配http错误码
    }
    return 0;
}


//app 是否登陆过 更新,modified by chenyc,strUser调用方已经保证是uid了
int DaoUpdateAppLoginStatus(const std::string& user)
{
    return dbinterface::ResidentPersonalAccount::UpdateAppLoginStatus(user);
}

int DaoGetAppPayMode(const std::string& uid)
{
    int charge_mode = dbinterface::ResidentPersonalAccount::GetAppPayMode(uid);
    return charge_mode;
}

//token 续时
int DaoTokenContinuation(const std::string& user, const std::string& post_passwd, const std::string& token)
{
    if(user.size() == 0 || post_passwd.size() == 0 || token.size() == 0)
    {
       return ERR_PASSWD_INVALID; 
    }

    if (!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << user;
        return ERR_PASSWD_INVALID; 
    }

    std::string user_passwd;
    std::string main_user_account;
    PerAccountUserInfo user_info;

    // 先在PersonalAccountUserInfo表中查找用户密码
    if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(user, user_info))
    {
        user_passwd = user_info.passwd;
        main_user_account = user_info.main_user_account;
    }
    else
    {
        // 再到AccountUserInfo表中查找用户密码
        UserInfoAccount account_info;
        memset(&account_info, 0, sizeof(account_info));
        if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_info))
        {
            user_passwd = account_info.passwd;
            main_user_account = account_info.main_user_account;
        }
        else
        {
            AK_LOG_WARN << "DaoGetUserInfo Failed!" << " user: " << user << " token: " << token;
            return ERR_PASSWD_INVALID;
        }
    }

    if (!PasswordCorrect(post_passwd, user_passwd))
    {
        AK_LOG_WARN << "login error, ins app passwd error, login_account:" << user;
        return ERR_PASSWD_INVALID;
    }
   
    int ret = HandleTokenContinuation(main_user_account, token);
    return ret;
}

int DaoTokenContinuation2(const std::string auth_token, const std::string token)
{
    if(auth_token.size() == 0 || token.size() == 0)
    {
       return ERR_PASSWD_INVALID; 
    }

    TokenInfo token_info;
    if (0 == dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        char auth[64] = "";
        Snprintf(auth, sizeof(auth), token_info.auth_token);
        if (strcasecmp(auth, auth_token.c_str()))
        {
            return ERR_PASSWD_INVALID;
        }
        
        int ret = HandleTokenContinuation(token_info.app_main_account, token);
        return ret;
    }

    return ERR_USER_NOT_EXIT;
}

//6.6之后的pm app token 续时
int DaoPmTokenContinuation(const std::string& user, const std::string& post_passwd, const std::string& token)
{
    if(user.size() == 0 || post_passwd.size() == 0 || token.size() == 0)
    {
       return ERR_PASSWD_INVALID; 
    }

    if (!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << user;
        return ERR_PASSWD_INVALID; 
    }

    std::string main_account;
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));

    // 先在AccountUserInfo表中查询
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        //pm app在AccountUserInfo中,但是未link不允许登录
        if (account_user_info.is_link == 0)
        {
            AK_LOG_WARN << "continuation error, pm app not link, login_account:" << user;
            return ERR_PASSWD_INVALID;
        }

        if (!PasswordCorrect(post_passwd, account_user_info.passwd)) 
        {
            AK_LOG_WARN << "continuation error, pm app passwd error, login_account:" << user;
            return ERR_PASSWD_INVALID;
        }

        //token续时使用主站点
        main_account = account_user_info.main_user_account;
    }
    else
    {
        //再到PersonalAccountUserInfo表查询pm app账号是否存在
        PerAccountUserInfo user_info;
        if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(user, user_info))
        {
            if (!PasswordCorrect(post_passwd, user_info.passwd)) 
            {
                AK_LOG_WARN << "continuation error, pm app passwd error, login_account:" << user;
                return ERR_PASSWD_INVALID;
            }
            
            //token续时使用主站点
            main_account = user_info.main_user_account;
        }
    }
    
    //使用主站点进行token续时    
    int ret = HandleTokenContinuation(main_account, token);
    return ret;
}

//ins app token续时
int DaoInsTokenContinuation(const std::string& user, const std::string& post_passwd, const std::string& token)
{
    int ret = 0;
    unsigned int limited_time = gstCSGATEConf.token_valid_time; //限制token时效7天
    unsigned int cur_time = GetCurrentTimeStamp();
    limited_time = limited_time + cur_time;
    
    if(user.size() == 0 || post_passwd.size() == 0)
    {
        return ERR_PASSWD_INVALID; 
    }
    if(token.size() == 0)
    {
        return ERR_TOKEN_INVALID;
    }
    if (!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << user;
        return ERR_PASSWD_INVALID; 
    }
    
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        if(!PasswordCorrect(post_passwd, account_user_info.passwd))
        {
            AK_LOG_WARN << "continuation error, ins app passwd error, login_account:" << user;
            return ERR_PASSWD_INVALID;
        }
    }
    else
    {
        AK_LOG_WARN << "login failed, user not exist, login_account:" << user;
        return ERR_USER_NOT_EXIT; 
    }
    
    //token续时
    if(-1 == dbinterface::InsToken::UpdateInsToken(limited_time, token, account_user_info.uuid))
    {
        return ERR_USER_NOT_EXIT;
    }
    return ret;
}

int HandleTokenContinuation(const std::string& main_account, const std::string& token)
{
    unsigned int limited_time = gstCSGATEConf.token_valid_time; //限制token时效7天
    unsigned int cur_time = GetCurrentTimeStamp();
    limited_time = limited_time + cur_time;

    if (0 != dbinterface::Token::UpdateToken(limited_time, token, main_account))
    {
        return ERR_TOKEN_INVALID;
    }

    return ERR_SUCCESS;
}

//检验是否需求更新token, 
//TODO:目前一个请求查询太多次这个token表了，后期把之前没有用的接口去掉后再统一优化下这个查找
int DaoCheckAndUpdateToken(const std::string& main_user, std::string& token)
{
    unsigned int cur_time;
    unsigned int create_time;
    unsigned int id;
    TokenInfo token_info;
    if (0 == dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        cur_time = GetCurrentTimeStamp();
        create_time = token_info.create_time;
        id = token_info.id;
        if (create_time + TOKEN_CHANGE_TIME < cur_time) //token change判断
        {
            CreateToken(main_user, token, TokenType::AppToken);
            dbinterface::Token::UpdateAppTokenAndTimeByID(token, id);
        }
    }
    else
    {
        return -1;
    }

    return 0;
}

int DaoGetPbxServer(const std::string& distributor, std::string& pbx_ip, std::string& pbx_ipv6)
{
    return dbinterface::DtPbxServer::GetGetPbxServer(distributor, pbx_ip, pbx_ipv6);
}


/**
 * 个人管理员或者社区管理员获取到区域管理员账户
 * 
 * <AUTHOR>
 * 
 * @param account_id 
 * @param grade 
 * @param dt_account 
 * 
 * @return int 
 */
int DaoGetDistributor(const int account_id, std::string& dt_account)
{
    return dbinterface::Account::GetDistributor(account_id, dt_account);
}

int DaoGetPersonalAccountInfo(const int account_id, CRldb* rldb_conn, PersonalAccountInfo &personal_account_info)
{
    if (account_id <= 0)
    {
        return -1;
    }

    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(account_id, account))
    {
        personal_account_info.id = account.id;
        Snprintf(personal_account_info.account, sizeof(personal_account_info.account), account.account);
        Snprintf(personal_account_info.passwd, sizeof(personal_account_info.passwd), account.passwd);
        personal_account_info.role = account.role;
        personal_account_info.parent_id = account.parent_id;
        personal_account_info.active = account.active;
    }
    else
    {
        return -1;
    }

    return 0;
}

int DaoGetPersonalAccountInfo(const int account_id, PersonalAccountInfo &personal_account_info)
{
    if (account_id <= 0)
    {
        return -1;
    }

    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(account_id, account))
    {
        personal_account_info.id = account.id;
        Snprintf(personal_account_info.account, sizeof(personal_account_info.account), account.account);
        Snprintf(personal_account_info.passwd, sizeof(personal_account_info.passwd), account.passwd);
        personal_account_info.role = account.role;
        personal_account_info.parent_id = account.parent_id;
        personal_account_info.active = account.active;
    }
    else
    {
        return -1;
    }

    return 0;
}

int DaoGetPersonalManagerId(const std::string& mac, int& manager_id)
{
    if (mac.empty())
    {
        return -1;
    }

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
    {
        dbinterface::AccountInfo account;
        if (dbinterface::Account::GetAccountInfoByAccount(dev.community, account) == 0)
        {
            manager_id = account.id;
            return 0;
        }
    }

    return -1;
}


int DaoGetCommunityAccountId(const std::string& mac, int& community_id)
{
    if (mac.empty())
    {
        return -1;
    }

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        community_id = dev.project_mng_id;
        return 0;
    }

    return -1;
}


int DaoGetPersonalDevNode(const std::string& mac, std::string& node)
{
    if (mac.empty())
    {
        return -1;
    }
    
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
    {
        node = dev.node;
        return 0;
    }

    return -1;
}


int DaoGetCommPbxServer(int community_id, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{
    return dbinterface::PbxServer::GetCommPbxServer(community_id, pbx_ip, pbx_ipv6, pbx_domain);
}

int DaoGetPerPbxServer(const std::string& node, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{
    return dbinterface::PbxServer::GetPerPbxServer(node, pbx_ip, pbx_ipv6, pbx_domain);
}

//对应UserInfo表的AppMainUserAccount,多套房只记录主站点pushtoken
int DaoUpdateAppSmartType(const std::string main_account, int is_set)
{
    return dbinterface::AppPushToken::UpdateAppSmartType(main_account, is_set);
}

int GetDistributorIDByInfo(PersonalAccountInfo &personal_account_info)
{
    int community_id = 0;

    if(personal_account_info.is_dev)
    {        
        ResidentDev per_dev;
        memset(&per_dev, 0, sizeof(per_dev));
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        if (0 == dbinterface::ResidentDevices::GetMacDev(personal_account_info.uid, dev))
        {
            community_id = dev.project_mng_id;
        }
        else if (0 == dbinterface::ResidentPerDevices::GetMacDev(personal_account_info.uid, per_dev))
        {
            dbinterface::AccountInfo account;
            if (dbinterface::Account::GetAccountInfoByAccount(per_dev.community, account) == 0)
            {
                community_id = account.id;
            }
        }
    }
    else
    {
        if(personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN \
            || personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            community_id = personal_account_info.parent_id; 
        }
        else
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(personal_account_info.parent_id, main_account))
            {
                community_id = main_account.parent_id;
            }
        }

    }
    
    personal_account_info.redirect_project_id = community_id;
    
    int dis_id = 0;
    dbinterface::AccountInfo dis_account;
    if (dbinterface::Account::GetDisAccountBySubId(community_id, dis_account) == 0)
    {
        dis_id = dis_account.id;
    }
    return dis_id;

}

int GetDistributorIDAndProjectID(const std::string& account, uint32_t &project_id)
{
    ResidentPerAccount account_info;
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(account, account_info))
    {
        return 0;
    }

    if(account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN \
        || account_info.role == ACCOUNT_ROLE_COMMUNITY_PM || IsOfficeRole(account_info.role))
    {
        project_id = account_info.parent_id;
    }
    else
    {
        ResidentPerAccount master_account;
        memset(&master_account, 0, sizeof(master_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(account_info.parent_id, master_account))
        {
            project_id = master_account.parent_id;
        }
    }

    int dis_id = 0;
    dbinterface::AccountInfo dis_account;
    if (dbinterface::Account::GetDisAccountBySubId(project_id, dis_account) == 0)
    {
        dis_id = dis_account.id;
    }
    return dis_id;
}


static int DaoCheckUserRedirectByDisOrProject(PersonalAccountInfo &personal_account_info)
{
    int dis_id = 0;

    if (gstCSGATEConf.server_area == ServerArea::jpcloud || gstCSGATEConf.aws_redirect == 1)
    {
        dis_id = GetDistributorIDByInfo(personal_account_info);
        if(dis_id == 0)
        {
            return 0;
        }
        personal_account_info.dis_id = dis_id;        
    }

    //Distributor级别调度
    if(gstCSGATEConf.aws_redirect == 0)
    {
        return 0;
    }

    int ret = dbinterface::AwsRedirect::CheckUserRedirectByDisID(dis_id);
    if (ret)
    {
        personal_account_info.need_redirect = ret;
        return ret;
    }

    if (gstCSGATEConf.server_area == ServerArea::ecloud) //当前只有ecloud有这种按项目迁移的
    {
        ret = dbinterface::AwsRedirect::CheckUserRedirectByProjectID(personal_account_info.redirect_project_id);
        if(ret)
        {
            personal_account_info.need_redirect = ret;
            return ret;
        }
    }

    return 0;
}

RedirectCloudType CheckUserRedirectByAccount(const std::string& account)
{
    if (gstCSGATEConf.aws_redirect == 1)
    {
        uint32_t project_id = 0;
        int dis_id = GetDistributorIDAndProjectID(account, project_id);
        if(dis_id == 0)
        {
            return REDIRECT_NO_NEED;
        }
        
        int ret = dbinterface::AwsRedirect::CheckUserRedirectByDisID(dis_id);
        if (ret == 0)
        {
            if (gstCSGATEConf.server_area == ServerArea::ecloud) //当前只有ecloud有这种按项目迁移的
            {
                int ret = dbinterface::AwsRedirect::CheckUserRedirectByProjectID(project_id);
                if(ret)
                {
                    AK_LOG_INFO << "Office DaoCheckUserRedirect return Yes, project_id: " << project_id;   
                    return (RedirectCloudType)ret;
                }
            }              
        }
        return (RedirectCloudType)ret;
    }

    return REDIRECT_NO_NEED;
}


RedirectCloudType DaoCheckUserRedirect(PersonalAccountInfo &personal_account_info)
{
    //现有迁移只有通过Dis来调度的
    return (RedirectCloudType)DaoCheckUserRedirectByDisOrProject(personal_account_info);
}

RedirectCloudType OfficeDaoCheckUserRedirect(int office_id)
{
    //现有迁移只有通过Dis来调度的
    int res = dbinterface::OfficePersonalAccount::CheckUserRedirectByDis(office_id);
    if (res == 0)
    {
        if (gstCSGATEConf.server_area == ServerArea::ecloud) //当前只有ecloud有这种按项目迁移的
        {
            int ret = dbinterface::AwsRedirect::CheckUserRedirectByProjectID(office_id);
            if(ret)
            {
                AK_LOG_INFO << "Office DaoCheckUserRedirect return Yes, office_id: " << office_id;   
                return (RedirectCloudType)ret;
            }
        }         
    }
    return (RedirectCloudType)res;
}

int DaoCheckPMAppStatus(const std::string &user)
{
    int ret;
    int app_status = dbinterface::PmAccountMap::checkPMAppAccountStatusByAccount(user);
    if (app_status == PM_APP_STATUS_CLOSED)
    {
        ret = ERR_PM_APP_STATUS_CLOSED;
    }
    else if (app_status == PM_APP_STATUS_OPEN)
    {
        ret = ERR_SUCCESS;
    }
    else
    {
        ret = ERR_USER_NOT_EXIT;
    }

    return ret;
}

int DaoInnerUpdateToken(const std::string& account, const std::string& main_account, const std::string& token, const std::string &auth_token, const std::string &refresh_token, int type)
{
    if (type == TOKEN_CONTINUE)
    {
        AK_LOG_INFO << "continue token from other server, account=" << main_account;
        return dbinterface::Token::UpdateTokenExpireTime(main_account);
    }
    else if(type == TOKEN_REFRESH)
    {
        if(refresh_token.size() == 0)
        {
            AK_LOG_WARN << "refresh token from other server, refresh_token is empty";
            return -1;
        }
        AK_LOG_INFO << "refresh token from other server, account=" << main_account << ", refresh_token=" << refresh_token;
        return dbinterface::Token::UpdateRefreshToken(main_account, refresh_token);
    }

    //TOKEN_UPDATE
    AK_LOG_INFO << "update token from other server" << " account=" << main_account << " token=" << token << " auth_token=" << auth_token;
    unsigned int cur_time = GetCurrentTimeStamp();
    if (token.length() > 0)
    {
        unsigned int expire_time = cur_time + 1296000;
        return dbinterface::Token::InsertOrUpdateToken(account, token, main_account, expire_time);
    }
    else if (auth_token.length() > 0)
    {
        return dbinterface::Token::InsertOrUpdateAuthToken(account, main_account, auth_token);
    }

    return 0;
}

int GetTokenRemainingTime(const std::string& account)
{
    unsigned int cur_time = GetCurrentTimeStamp();
    TokenInfo token_info;
    if (0 == dbinterface::Token::GetTokenInfoByAccount(account, token_info))
    {
        unsigned int exp_time = token_info.app_tokenet;
        return exp_time - cur_time;
    }

    return 0;
}

int GetTokenRemainingTimeByToken(const std::string& token)
{
    unsigned int cur_time = GetCurrentTimeStamp();
    TokenInfo token_info;
    if (0 == dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        unsigned int exp_time = token_info.app_tokenet;
        return exp_time - cur_time;
    }

    return 0;
}


//日本只有用户
bool DaoCheckJpRedirect(const std::string       &account)
{
    if(gstCSGATEConf.aws_redirect == 0)
    {
        return false;
    }

    return dbinterface::ResidentPersonalAccount::CheckJpRedirect(account);
}


int DaoUpdateSmsCode(const std::string& account, const std::string& code)
{
    return dbinterface::VerificationCode::UpdateSmsCode(account, code);
}


//token校验，token存在就可以
int DaoTokenCheck(const std::string& token)
{
    if(token.size() == 0)
    {
       return ERR_TOKEN_INVALID; 
    }

    if (!HttpCheckSqlParam(token))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid token=" << token;
        return  ERR_TOKEN_INVALID; 
    }

    unsigned int exp_time;
    unsigned int cur_time = GetCurrentTimeStamp();
    TokenInfo token_info;
    if (0 == dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        exp_time = token_info.app_tokenet;
        if (exp_time > cur_time) //token时效性判断
        {
            return ERR_SUCCESS;
        }
    }
    
    return ERR_TOKEN_INVALID;
}

int GetRequsetUserInfo(float api_version, const std::string& login_account, PerAccountUserInfo& user_info, ResidentPerAccount& account_info)
{
    if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(login_account, user_info))
    {
        std::string last_login_account = user_info.last_login_account;
        std::string main_user_account = user_info.main_user_account;

        AK_LOG_INFO << "login_account:" << login_account << ", last_login_account: " << user_info.last_login_account << ",main_account:" << user_info.main_user_account;

        if (dbinterface::ProjectUserManage::IsMultiSiteUser(user_info.uuid))
        {
            // api_version < 6.4, 提示登录错误
            // 6.4 <= api_version < 6.6, 在login_conf提示升级app
            // 6.6 <= api_version , 不能用sip登录, 只能用email登录, 提示登录错误
            if ((api_version < std::stof(csgate::V64)) || (api_version >= std::stof(csgate::V66) && user_info.sip_login))
            {
                AK_LOG_WARN << "login error, account passwd not support multi sites, login_account:" << login_account;
                return -1;
            }
        }

        // 若last_login_account被删除查找不到,再查询main_account
        if (last_login_account.size() == 0 || 0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(last_login_account, account_info))
        {
            if (0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(main_user_account, account_info))
            {
                return -1;
            }
        }
        
        Snprintf(account_info.passwd, sizeof(account_info.passwd), user_info.passwd);
        
        return 0;
    }

    AK_LOG_WARN << "api_version = " << api_version << ", login error, user not found, user :" << login_account;
    return -1;
}

int GetOfficeRequsetUserInfo(float api_version, const std::string& login_account, PerAccountUserInfo& user_info, OfficeAccount& account_info)
{
    if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(login_account, user_info))
    {
        std::string last_login_account = user_info.last_login_account;
        std::string main_user_account = user_info.main_user_account;
        AK_LOG_INFO << "login_account:" << login_account << ",last_login_account: " << user_info.last_login_account << ",main_account:" << user_info.main_user_account;

        if (dbinterface::ProjectUserManage::IsMultiSiteUser(user_info.uuid))
        {
            // api_version < 6.4, 提示登录错误
            // 6.4 <= api_version < 6.6, 在login_conf提示升级app
            // 6.6 <= api_version , 不能用sip登录, 提示登录错误
            if ((api_version < std::stof(csgate::V64)) || (api_version >= std::stof(csgate::V66) && user_info.sip_login))
            {
                AK_LOG_WARN << "login error, account passwd not support multi sites, login_account:" << login_account;
                return -1;
            }
        }

        // 若last_login_account被删除查找不到,再查询main_account
        if (last_login_account.size() == 0 || 0 != dbinterface::OfficePersonalAccount::GetUserAccount(last_login_account, account_info))
        {
            if (0 != dbinterface::OfficePersonalAccount::GetUserAccount(main_user_account, account_info))
            {
                return -1;
            }
        }
        
        Snprintf(account_info.passwd, sizeof(account_info.passwd), user_info.passwd);
        
        return 0;
    }
    
    AK_LOG_WARN << "office login error, user not found, user :" << login_account;
    return -1;
}


int DaoCheckUser(const std::string& user, const std::string& password, float ver, PersonalAccountInfo& personal_account_info)
{
    int ret = ERR_SUCCESS;
    ret = csgate::DaoCheckDevLogin(user, password, ver, personal_account_info);
    if (personal_account_info.is_dev)
    {
        return ret;
    }

    if(!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,User=" << user;
        return ERR_USER_NOT_EXIT;
    }
    
    ret = csgate::DaoCheckAppLogin(user, password, ver, personal_account_info);
    return ret;
}

int DaoCheckDevLogin(const std::string& mac, const std::string& password, float ver, PersonalAccountInfo& personal_account_info)
{
    std::string cal_password;
    std::string dev_mac = mac;
    dev_mac += DEFAULT_CSGATE_KEY_MASK;

    if (ver > 4.5)
    {
        cal_password = akuvox_encrypt::MD5(dev_mac).toStr();
    }
    else
    {
        cal_password = akuvox_encrypt::MD5("akuvox").toStr();
    }

    if (cal_password != password)
    {
        return ERR_PASSWD_INVALID;
    }

    //对于设备,则uid赋值为mac地址
    personal_account_info.is_dev = 1;
    personal_account_info.uid = mac; 
    Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), mac.data());
    
    /*
    if (ERR_USER_NOT_EXIT == DaoInsertDevLoingLog(user))
    {
        return ERR_USER_NOT_EXIT;
    }
    */
    
    return ERR_SUCCESS;
}

int DaoCheckAppLogin(const std::string& user, const std::string& post_passwd, float ver, PersonalAccountInfo& personal_account_info)
{
    PerAccountUserInfo user_info;
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));

    if (0 == csgate::GetRequsetUserInfo(ver, user, user_info, account))
    {
        int active = account.active;
        int is_expire = account.is_expire;
        
        personal_account_info.role = account.role;
        personal_account_info.id = account.id;
        personal_account_info.parent_id = account.parent_id;
        personal_account_info.uid = account.account;
        personal_account_info.is_init = account.is_init;
        personal_account_info.is_show_tmpkey = account.is_show_tmpkey;
        Snprintf(personal_account_info.account, sizeof(personal_account_info.account), account.account);
        Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), user_info.main_user_account);

        if (!PasswordCorrect(post_passwd, user_info.passwd, ver))
        {
            AK_LOG_WARN << "app login on passwd error, login_account = " << user;
            return ERR_PASSWD_INVALID;
        }
        
        if ((active) && (is_expire))
        {
            return ERR_APP_EXPIRE;
        }
        
        //主账号未激活
        if ((!active) && (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN || personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_PM))
        {
            return ERR_APP_UNACTIVE;
        }
        
        //从账号未激活,查询下对应的主账号
        int main_active = 0;
        if ((!active) && ((personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT) || (personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)))
        {
            ResidentPerAccount main_account;
            if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(personal_account_info.parent_id, main_account))
            {
                main_active = main_account.active;
            }

            //主账号已经激活,从账号未激活,则证明是额外的app
            if (main_active)
            {
                return ERR_APP_UNPAID;
            }
            else
            {
                return ERR_APP_UNACTIVE;
            }
        }

        if(personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_PM && ver < 6.4)
        {
            AK_LOG_WARN << "pm login by old app, user=" << personal_account_info.uid;
            return ERR_USER_NOT_EXIT;
        }

        return ERR_SUCCESS;
    }
    
    return ERR_USER_NOT_EXIT;
}

int DaoCheckPmAppLogin(const std::string& login_account, const std::string& post_passwd, PersonalAccountInfo& personal_account_info, float api_version, const std::string& id_code)
{
    std::string last_login_account;
    std::string main_user_account;
    std::string user_info_passwd;
    std::string email;
    int is_old_pm_app_account = 0;
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));

    //先在AccountUserInfo表   查询pm app账号是否存在
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(login_account, account_user_info))
    {
        //pm app在AccountUserInfo中,但是未link不允许登录
        if (!dbinterface::Account::IsPm(account_user_info.uuid))
        {
            AK_LOG_WARN << "login error, login account is not pm, login_account:" << login_account;
            return ERR_USER_NOT_EXIT;
        }
        if (account_user_info.is_link == 0 )
        {
            AK_LOG_WARN << "login error, pm app not link, login_account:" << login_account;
            return ERR_USER_NOT_EXIT;
        }
        user_info_passwd = account_user_info.passwd;
        main_user_account = account_user_info.main_user_account;
        last_login_account = account_user_info.last_login_account; 
        email = account_user_info.email;

        // pm web创建了,但是pm app未创建(在AccountUserInfo表中),此时main_user_account为空
        if (main_user_account.size() == 0)
        {            
            AK_LOG_WARN << "login error, pm app not create, login_account:" << login_account;
            return ERR_PM_APP_UNCREATED;
        }
    }
    else 
    {
        // 在PersonalAccountUserInfo表中查询pm app账号是否存在
        PerAccountUserInfo user_info;
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(login_account, user_info))
        {
            user_info_passwd = user_info.passwd;
            main_user_account = user_info.main_user_account;
            last_login_account = user_info.last_login_account;
            email = account_user_info.email;
        }
        is_old_pm_app_account = 1;
    }
    
    if (!PasswordCorrect(post_passwd, user_info_passwd))
    {
        AK_LOG_WARN << "login error, pm app passwd error, login_account:" << login_account;
        return ERR_PASSWD_INVALID;
    }
    
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    // 先用last_login_account查
    if (last_login_account.size() == 0 || 0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(last_login_account, personal_account))
    {
        // last_login_account未查到再使用main_user_account查
        if (0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(main_user_account, personal_account))
        {
            AK_LOG_WARN << "check pm app info error, user:" << login_account;
            return ERR_USER_NOT_EXIT;
        }
    }

    // enduser不能使用pm_login接口
    if (personal_account.role != ACCOUNT_ROLE_COMMUNITY_PM)
    {
        AK_LOG_WARN << "check pm app info error, role is enduser, login_account:" << login_account;
        return ERR_USER_NOT_EXIT;
    }
    
    personal_account_info.role = personal_account.role; 
    personal_account_info.is_init = personal_account.is_init;
    personal_account_info.parent_id = personal_account.parent_id; 
    personal_account_info.is_show_tmpkey = personal_account.is_show_tmpkey;            
    Snprintf(personal_account_info.account, sizeof(personal_account_info.account), personal_account.account);
    Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), main_user_account.c_str());
    personal_account_info.uid = personal_account_info.account;
    Snprintf(personal_account_info.email, sizeof(personal_account_info.email), email.c_str());

    //检查是否进行双重认证
    //旧的pm app账号不用双重认证（sip登录那种），因为要先关联统一密码
    if(api_version >= 7.1f && !is_old_pm_app_account)
    {   
        return AppTwoFactorAuth::CheckTwoFactorAuth(login_account, id_code);
    }

    return ERR_SUCCESS;
}

//ins APP
int DaoCheckInsAppLogin(const std::string& user, const std::string& post_passwd, UserInfoAccount& ins_account_info, float api_version, const std::string& id_code)
{
    std::string user_info_passwd;
    int ins_app_status;
    memset(&ins_account_info, 0, sizeof(ins_account_info));

    //ins app账号校验
    if (dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, ins_account_info) == 0)
    {
        if (!dbinterface::Account::IsInstaller(ins_account_info.uuid))
        {
            AK_LOG_WARN << "login error, ins app account does not exist or group mismatch, login_account:" << user;
            return ERR_USER_NOT_EXIT;
        }
        
        user_info_passwd = ins_account_info.passwd;
        ins_app_status = ins_account_info.installer_app_status;
    }
    else
    {
        AK_LOG_WARN << "login error, ins app account does not exist, login_account:" << user;
        return ERR_USER_NOT_EXIT;
    }

    //APP Status检查
    if(0 == ins_app_status)
    {
        AK_LOG_WARN << "login error, ins app status is closed, login_account:" << user;
        return ERR_INSTALLER_APP_STATUS_CLOSED;
    }
    
    if (!PasswordCorrect(post_passwd, user_info_passwd))
    {
        AK_LOG_WARN << "ins app login error on passwd error, login_account:" << user;
        return ERR_PASSWD_INVALID;
    }
    
    //检查是否进行双重认证 
    if(api_version >= 7.1f)
    {   
        return AppTwoFactorAuth::CheckTwoFactorAuth(user, id_code);
    }
    
    return ERR_SUCCESS;
}

//根据loginAccount获取Account表ID和UUID
int DaoGetAccountIDAndUUID(const std::string& user, int &id, std::string &uuid)
{
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    if(0 != dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        //校验账号密码后才执行，DB连接为空已经有打日志，这里直接返回，下同
        return -1;
    }
    std::string userinfo_uuid = account_user_info.uuid;
    std::string account_uuid;
    if(0 != dbinterface::AccountMap::GetAccountUUIDByUserInfoUUID(userinfo_uuid, account_uuid))
    {
        return -1;
    }
    int manage_group = 0;
    dbinterface::AccountInfo account_info;
    memset(&account_info, 0, sizeof(account_info));
    if(0 == dbinterface::Account::GetAccountByUUID(account_uuid, account_info))
    {
        manage_group = account_info.manage_group;
    }
    memset(&account_info, 0, sizeof(account_info));
    //防止社区当ins导致的查不到对应installer的问题
    if(dbinterface::Account::GetInsAccountInfoByManageGroup(manage_group, account_info) < 0)
    {
        return -1;
    }
    id = account_info.id;
    uuid = account_info.uuid;
    return 0;
}

int DaoGetInsAccountByToken(const std::string& token, std::string& username)
{
    InsTokenInfo ins_token_info;
    if(0 != dbinterface::InsToken::GetInsTokenInfoByAppToken(token, ins_token_info))
    {
        AK_LOG_WARN << "ins token invalid";
        return -1;
    }

    uint64_t time_now = GetCurrentTimeStamp();
    if(time_now >= ins_token_info.exp_time)
    {
        AK_LOG_WARN << "ins token expire";
        return -2;
    }

    UserInfoAccount userinfo_account;
    memset(&userinfo_account, 0, sizeof(userinfo_account));

    if(0 != dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(ins_token_info.userinfo_uuid, userinfo_account))
    {
        AK_LOG_WARN << "ins userinfo not found";
        return -1;
    }

    username = userinfo_account.account;
    return 0;
}

bool PasswordCorrect(const std::string& post_passwd, const std::string& db_passwd, const float api_version)
{
    std::string post_passwd_salty;

    if (api_version <= 4.5)
    {
        // app发上来的是md5(passwd), mysql存储的是 md5(salt + md5(md5(passwd)))
        std::string twice_md5_passwd = akuvox_encrypt::MD5(post_passwd).toStr();
        post_passwd_salty = akuvox_encrypt::MD5(std::string(KPasswordSalt) + twice_md5_passwd).toStr();
    }
    else
    {
        // app发上来的是  md5(md5(passwd)),    mysql存储的是md5(salt + md5(md5(passwd)))
        post_passwd_salty = akuvox_encrypt::MD5(std::string(KPasswordSalt) + post_passwd).toStr();
    }

    return post_passwd_salty == db_passwd;
}

int DaoGetPmPersonalAccountInfo(const std::string& login_account, PersonalAccountInfo& personal_account_info)
{
    std::string last_login_account;
    std::string main_user_account;
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));

    dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(login_account, account_user_info);
    main_user_account = account_user_info.main_user_account;
    last_login_account = account_user_info.last_login_account; 
    
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    // 先用last_login_account查
    if (last_login_account.size() == 0 || 0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(last_login_account, personal_account))
    {
        // last_login_account未查到再使用main_user_account查
        if (0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(main_user_account, personal_account))
        {
            AK_LOG_WARN << "check pm app info error, user:" << login_account;
            return ERR_USER_NOT_EXIT;
        }
    }
    
    personal_account_info.role = personal_account.role; 
    personal_account_info.is_init = personal_account.is_init;
    personal_account_info.parent_id = personal_account.parent_id; 
    personal_account_info.is_show_tmpkey = personal_account.is_show_tmpkey;            
    Snprintf(personal_account_info.account, sizeof(personal_account_info.account), personal_account.account);
    Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), main_user_account.c_str());
    personal_account_info.uid = personal_account_info.account;

    return ERR_SUCCESS;
}

int DaoCheckAdminAppLogin(const std::string& login_account, const std::string& login_pwd, PersonalAccountInfo& admin_per_account)
{
    UserInfoAccount admin_account_user_info;

    //admin app账号校验
    if (0 != dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(login_account, admin_account_user_info)
        && 0 != dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByPhone(login_account, admin_account_user_info))
    {
        AK_LOG_WARN << "login error, admin app account does not exist, login_account:" << login_account;
        return ERR_USER_NOT_EXIT;
    }

    if (!PasswordCorrect(login_pwd, admin_account_user_info.passwd))
    {
        AK_LOG_WARN << "admin app login error on passwd error, login_account:" << login_account;
        return ERR_PASSWD_INVALID;
    }

    if (strlen(admin_account_user_info.main_user_account) == 0)
    {
        AK_LOG_WARN << "admin app main user account is empty, login_account:" << login_account;
        return ERR_ADMIN_APP_UNCREATED;
    }

    ResidentPerAccount per_account;
    //获取admin对应的App账号信息
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(admin_account_user_info.main_user_account, per_account))
    {
        AK_LOG_WARN << "admin app personal account info not found, login_account:" << login_account;
        return ERR_ADMIN_APP_UNCREATED;
    }
    
    if (ACCOUNT_ROLE_OFFICE_NEW_ADMIN != per_account.role)
    {
        AK_LOG_WARN << "admin app login error, login user role is:" << per_account.role << ", login_account:" << login_account;
        return ERR_ADMIN_APP_UNCREATED;
    }

    admin_per_account.role = per_account.role;
    admin_per_account.parent_id = per_account.parent_id;
    admin_per_account.is_init = per_account.is_init;
    Snprintf(admin_per_account.account, sizeof(admin_per_account.account), per_account.account);
    Snprintf(admin_per_account.main_account, sizeof(admin_per_account.main_account), per_account.account); //新办公admin暂不支持多套房
    admin_per_account.uid = per_account.account;
    Snprintf(admin_per_account.email, sizeof(admin_per_account.email), admin_account_user_info.email);

    return ERR_SUCCESS;
}

int DaoCheckAdminSmsLogin(const std::string& phone, const std::string& code, const std::string& area_code, PersonalAccountInfo& personal_account_info)
{
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    
    if (0 != dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByPhone(phone, account_user_info))
    {
        AK_LOG_WARN << "login error, admin sms account does not exist, login_account:" << phone;
        return ERR_USER_NOT_EXIT;
    }
    
    ResidentPerAccount per_account;
    std::string user_account = account_user_info.main_user_account;
    if (user_account.empty() || 0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(user_account, per_account))
    {
        AK_LOG_WARN << "get user info failed, user not exist, phone=" << phone;
        return ERR_USER_NOT_EXIT;
    }

    // 校验角色
    if (ACCOUNT_ROLE_OFFICE_NEW_ADMIN != per_account.role)
    {
        AK_LOG_WARN << "login failed, account role is not admin, phone=" << phone;
        return ERR_USER_NOT_EXIT;
    }
    
    AK_LOG_INFO << "phone:" << phone << ", account:" << per_account.account;

    std::string account_uuid;
    if (0 != dbinterface::AccountMap::GetAccountUUIDByUserInfoUUID(account_user_info.uuid, account_uuid))
    {
        AK_LOG_WARN << "get account uuid failed, account user info uuid=" << account_user_info.uuid;
        return ERR_USER_NOT_EXIT;
    }

    // 校验区号
    if (!DaoCheckAdminSmsLoginAreaCode(area_code, account_uuid))
    {
        AK_LOG_WARN << "admin sms login failed, area code is not match, phone=" << phone;
        return ERR_USER_NOT_EXIT;
    }

    // 校验验证码
    if (!DaoCheckLoginCode(code, user_account))
    {
        AK_LOG_WARN << "admin sms login failed, verification code is not match or expired, phone=" << phone;
        return ERR_PASSWD_INVALID;
    }

    // 相关信息
    personal_account_info.role = per_account.role;
    personal_account_info.parent_id = per_account.parent_id;
    Snprintf(personal_account_info.account, sizeof(personal_account_info.account), per_account.account);
    Snprintf(personal_account_info.main_account, sizeof(personal_account_info.main_account), per_account.account);
    personal_account_info.uid = user_account;

    // 校验账号激活
    int active = per_account.active;
    if (!active)
    {
        AK_LOG_WARN << "admin sms login failed, account is not active, phone=" << phone;
        return ERR_APP_UNACTIVE;
    }

    // 校验账号过期
    int expire = per_account.is_expire;
    if (expire)
    {
        AK_LOG_WARN << "admin sms login failed, account is expired, phone=" << phone;
        return ERR_APP_EXPIRE;
    }

    // 校验通过，删除验证码
    dbinterface::VerificationCode::DeleteVerificationCode(user_account);

    return ERR_SUCCESS;
}

bool DaoCheckAdminSmsLoginAreaCode(const std::string& report_area_code, const std::string& account_uuid)
{
    OfficeAdminInfo office_admin_info;
    if (0 != dbinterface::OfficeAdmin::GetOfficeAdminByAccountUUID(account_uuid, office_admin_info))
    {
        AK_LOG_WARN << "get office admin failed, account uuid not exist, phone=" << report_area_code;
        return false;
    }
    
    std::string admin_area_code = office_admin_info.phone_code;
    if (admin_area_code.empty())
    {
        AK_LOG_WARN << "admin area code is empty, phone=" << report_area_code;
        return false;
    }
    
    if (admin_area_code != report_area_code)
    {
        AK_LOG_WARN << "admin area code is not match, phone=" << report_area_code;
        return false;
    }

    return true;
}

bool DaoCheckLoginCode(const std::string& code, const std::string& account)
{
    VerificationPtr code_info;
    if (0 != dbinterface::VerificationCode::GetVerificationCode(account, code_info))
    {
        AK_LOG_WARN << "login failed, account code not exist";
        return false;
    }

    std::string verification_code = code_info->code;
    int is_code_expire = code_info->is_expire;
    if (verification_code != code || is_code_expire)
    {
        AK_LOG_WARN << "login failed, verification code is not match or expired";
        return false;
    }

    return true;
}

}

