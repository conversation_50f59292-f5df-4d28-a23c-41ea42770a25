#ifndef __CSGATE_DAO_H__
#define __CSGATE_DAO_H__

#include <string>
#include "ConnectionPool.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/Account.h"
#include "ServerMng.h"
#include "AwsRedirect.h"

extern const int g_jp_update_token_judge_time;
extern const int g_jp_token_extend_time;
const static char KPasswordSalt[] = "YQNas*1698+zc-";

enum CommunityInfoSwitchType
{
    Landline,
    OfflineNotify,
    AllowPin,
    SIMNotify,
};

typedef struct USER_CONF_T
{
    int role;
    int have_public_dev;
    int initialization;// 是否 初始化过 社区从账号+个人账号=1默认初始化过 只有社区主账号有可能0
    int is_show_tempkey; //app是否有生成tempkey的权限
    int mng_account_id;
} USER_CONF;


namespace csgate
{

enum AccountGrade
{
    SUPER_MANEGER_GRADE = 1,//超级管理员
    AREA_MANEGER_GRADE = 11, //区域管理员
    COMMUNITY_MANEGER_GRADE = 21, //社区管理
    PERSONAL_MANEGER_GRADE = 22, //单住户管理
    PERSONAL_USER_GRADE = 31, //普通用户
};

enum PMAppStatus
{
    PM_APP_STATUS_CLOSED = 0, //PM APP关闭，不允许登录
    PM_APP_STATUS_OPEN = 1
};

struct PersonalAccountInfo
{
    int id;
    char account[64];
    char passwd[65];
    int role;
    int parent_id;
    int active;
    std::string uid;
    int is_dev;
    std::string smarthome_uuid;
    int need_redirect;
    int dis_id;
    char main_account[64];
    int is_init;
    int is_show_tmpkey;
    int mng_account_id;

    int redirect_project_id;//按项目迁移时候赋的id
    char email[64];
    PersonalAccountInfo()
    {
        id = 0;
        memset(account, 0, sizeof(account));
        memset(main_account, 0, sizeof(main_account));
        memset(passwd, 0, sizeof(passwd));
        role = 0;
        parent_id = 0;
        active = -1;
        uid = "";
        smarthome_uuid = "";
        is_dev = 0;
        need_redirect = 0;
        dis_id = 0;
    }
};

#define user_type_ins 1 //用于ins APP标识用户类型

int DaoCheckUser(const std::string& user, const std::string& password, float ver, PersonalAccountInfo& personal_account_info);
int DaoCheckDevLogin(const std::string& user, const std::string& password, float ver, PersonalAccountInfo& personal_account_info);
int DaoCheckAppLogin(const std::string& user, const std::string& password, float ver, PersonalAccountInfo& personal_account_info);
int DaoCheckPmAppLogin(const std::string& login_account, const std::string& password, PersonalAccountInfo& personal_account_info, float api_version = 0.0f, const std::string& id_code = "");
//ins APP
int DaoCheckInsAppLogin(const std::string& user, const std::string& password, UserInfoAccount& personal_account_info, float api_version = 0.0f, const std::string& id_code = "");
int DaoCheckPhone(const std::string& phone, const std::string& code, const std::string& area_code, PersonalAccountInfo& personal_account_info);
int DaoUpdateToken(const std::string& user, const std::string& main_user, const std::string& token, const float ver);
//ins APP
int DaoInsertOrUpdateInsToken(const std::string& uuid, std::string& token);
int DaoInsertOrUpdateInsRenewToken(const std::string& uuid, const TokenRenewInfo& token_renew_info);
int DaoUpdateAuthToken(const std::string& user, const std::string& main_user, std::string& token);
int Copychar(char* dst, int size, const char* src);
int DaoGetUserConf(const PersonalAccountInfo& personal_account_info, USER_CONF& user_conf);
int DaoInsertAppLoingLog(const std::string& uid);
int DaoInsertDevLoingLog(const std::string& mac);
std::string DaoGetMacNode(const std::string& mac, int& mac_id);
std::string DaoGetUidNode(const std::string& uid);
int DaoCheckToken(const std::string &token, PersonalAccountInfo &personal_account_info, const float ver);
//ins APP
int DaoCheckInsToken(const std::string &token, const std::string &userinfo_uuid);
int DaoUpdateAppLoginStatus(const std::string& user);
int DaoGetAppPayMode(const std::string& uid);
int DaoTokenContinuation(const std::string& user, const std::string& password, const std::string& token);
int DaoTokenContinuation2(const std::string auth_token, const std::string token);
int DaoPmTokenContinuation(const std::string& user, const std::string& password, const std::string& token);
//ins app
int DaoInsTokenContinuation(const std::string& user, const std::string& password, const std::string& token);
int DaoGetAccountIDAndUUID(const std::string& user, int &id, std::string &uuid);

int HandleTokenContinuation(const std::string& main_account, const std::string& token);
int DaoCheckAndUpdateToken(const std::string& main_user, std::string& token);
int DaoGetPbxServer(const std::string& distributor, std::string& pbx_ip, std::string& pbx_ipv6);
int DaoGetDistributor(const int account_id, std::string& dt_account);
int DaoGetPersonalAccountInfo(const int account_id, CRldb* rldb_conn, PersonalAccountInfo &personal_account_info);
int DaoGetPersonalAccountInfo(const int account_id, PersonalAccountInfo &personal_account_info);
int DaoGetPersonalManagerId(const std::string& mac, int& manager_id);

int DaoGetCommunityAccountId(const std::string& mac, int& community_id);
int DaoGetPersonalDevNode(const std::string& mac, std::string& node);
int DaoGetCommPbxServer(int community_id, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain);
int DaoGetPerPbxServer(const std::string& node, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain);
int DaoUpdateAppSmartType(const std::string uid, int is_set);
RedirectCloudType DaoCheckUserRedirect(PersonalAccountInfo &personal_account_info);
RedirectCloudType OfficeDaoCheckUserRedirect(int office_id);
int GetDistributorIDByInfo(PersonalAccountInfo &personal_account_info);

int DaoSmarthomeDev(const std::string &user, const std::string &passwd, std::string &device_id);
int DaoSmarthomeAccount(const std::string &user, std::string &sm_uuid);

int DaoCheckPMAppStatus(const std::string &user);
int DaoInnerUpdateToken(const std::string& account, const std::string& main_account, const std::string& token, const std::string &auth_token, const std::string &refresh_token, int type);
int GetTokenRemainingTime(const std::string& account);
bool DaoCheckJpRedirect(const std::string       &account);
int GetTokenRemainingTimeByToken(const std::string& token);
int DaoUpdateSmsCode(const std::string& account, const std::string& code);
int DaoTokenCheck(const std::string& token);

int GetRequsetUserInfo(float api_version, const std::string& login_account, PerAccountUserInfo& user_info, ResidentPerAccount& account_info);
int GetOfficeRequsetUserInfo(float api_version, const std::string& login_account, PerAccountUserInfo& user_info, OfficeAccount& account_info);

int DaoGetInsAccountByToken(const std::string& token, std::string& username);
bool PasswordCorrect(const std::string& post_passwd, const std::string& db_passwd, const float api_version = 4.6); 
int DaoUpdateTokenRenewInfo(const std::string& user, const std::string& main_user, TokenRenewInfo& token_renew_info);
RedirectCloudType CheckUserRedirectByAccount(const std::string& account);
int DaoGetPmPersonalAccountInfo(const std::string& login_account, PersonalAccountInfo& personal_account_info);
int DaoCheckAdminAppLogin(const std::string& login_account, const std::string& login_pwd, PersonalAccountInfo& admin_per_account);
int DaoCheckAdminSmsLogin(const std::string& phone, const std::string& code, const std::string& area_code, PersonalAccountInfo& personal_account_info);
bool DaoCheckAdminSmsLoginAreaCode(const std::string& report_area_code, const std::string& account_uuid);
bool DaoCheckLoginCode(const std::string& code, const std::string& account);
}
#endif //__CSGATE_DAO_H__
