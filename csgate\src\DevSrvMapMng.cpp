#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AkLogging.h"
#include "DevSrvMapMng.h"
#include "CachePool.h"

const char * g_dev_srv_map_key = "dev_srv_info";
const std::string  g_dev_srv_pbx_ipv4_key = "pbxipv4_";

CDevSrvMapMng* CDevSrvMapMng::instance_ = nullptr;
//const int32_t CLogicSrvMng::kVnodeNum = 50; 直接初始化
CDevSrvMapMng* CDevSrvMapMng::Instance()
{
    if (!instance_)
    {
        instance_ = new CDevSrvMapMng();
    }
    return instance_;
}

//6.2之前的设备，登记mac和pbx以及csmain的信息
void CDevSrvMapMng::RegMacPbxIPv4Srv(const std::string &mac, const std::string &pbx_ip)
{
    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_dev_srv_map_key);
    if (cache_conn)
    {
        /*1、登记mac和pbx的对应关系，不能是pbx和mac的对应关系
          如果直接用set数据结构，用pbx找下面的mac列表，扩展结点分配更新了，但是mac还在旧的pbx里面
          2、登录请求比较频繁，不做复杂的映射关系，要用的时候直接脚本处理
          3、具体运维的处理逻辑
          3.1 查找所有的key-value, 过滤指定pbx管理的mac列表
          3.2 过滤掉已经升级到新版本的mac，dclientVer > 6100
          3.3 通过数据库查找mac列表在哪个csmain服务器
        */
        std::string key=g_dev_srv_pbx_ipv4_key;
        key += mac;
        cache_conn->set(key, pbx_ip);
        cache_mng->RelCacheConn(cache_conn);
    }

}

