#include "EndUserAppAuthChecker.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "Caesar.h"
#include "AkLogging.h"
#include "util_string.h"
#include "Md5.h"
#include "Dao.h"

int EndUserAppAuthChecker::HandleCheckAuthToken()
{
    if (0 != strcmp(auth_info_.auth_token, token_info_.auth_token))
    {
        return csgate::ERR_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}

int EndUserAppAuthChecker::HandleCheckRefreshToken()
{
    if (0 != strcmp(auth_info_.refresh_token, token_info_.app_refresh_token))
    {
        return csgate::ERR_REFRESH_TOKEN_INVALID;
    }
    
    return csgate::ERR_SUCCESS;
}

int EndUserAppAuthChecker::HandleCheckUserPassword()
{
    std::string username;
    //凯撒解密
    char user_tmp[128];
    snprintf(user_tmp, sizeof(user_tmp), "%s", auth_info_.user);
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    //去除前后空格
    TrimString(username);
    PerAccountUserInfo user_info;

    //查找对应userinfo
    if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(username, user_info))
    {
        if (!csgate::PasswordCorrect(auth_info_.passwd, user_info.passwd))
        {
            return csgate::ERR_PASSWD_INVALID;
        }
    }
    else
    {
        return csgate::ERR_USER_NOT_EXIT;
    }

    if (0 != strcmp(token_info_.app_main_account, user_info.main_user_account))
    {
        return csgate::ERR_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}