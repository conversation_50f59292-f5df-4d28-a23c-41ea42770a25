#include "AppAuthChecker.h"
#include "dbinterface/Token.h"

class EndUserAppAuthChecker : public AppAuthChecker
{

private:
    TokenInfo token_info_;

public:
    EndUserAppAuthChecker() : AppAuthChecker() {
        memset(&token_info_, 0, sizeof(token_info_));
    }
    EndUserAppAuthChecker(const TokenInfo& token_info, const AuthInfo& auth_info)
                                    :  AppAuthChecker(auth_info), token_info_(token_info) {}

private:
    virtual int HandleCheckAuthToken() override;
    virtual int HandleCheckUserPassword() override;
    virtual int HandleCheckRefreshToken() override;
};