#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evpp/event_loop.h>
#include "util.h"
#include "CsgateConf.h"
#include "EtcdCliMng.h"
#include "LogicSrvMng.h"
#include "ConnectionPool.h"


extern CSGATE_CONF gstCSGATEConf;
extern CAkEtcdCliManager* g_etcd_cli_mng;

void UpdateAccSrvList()
{
    std::vector<std::string> csmain_addrs;
    if (g_etcd_cli_mng->GetAllAccSrvs(csmain_addrs) == 0)
    {
        CLogicSrvMng::Instance()->UpdateAccSrvList(csmain_addrs);
    }
}
void UpdateRtspSrvList()
{
    std::vector<std::string> csvrtspd_addrs;
    if (g_etcd_cli_mng->GetAllRtspSrvs(csvrtspd_addrs) == 0)
    {
        CLogicSrvMng::Instance()->UpdateRtspSrvList(csvrtspd_addrs);
    }
}

void UpdateOpsSrvList()
{
    std::vector<std::string> ops_addrs;
    if (g_etcd_cli_mng->GetAllOpsSrvs(ops_addrs) == 0)
    {
        CLogicSrvMng::Instance()->UpdateOpsSrvList(ops_addrs);
    }
}

void UpdateFtpSrvList()
{
    std::vector<std::string> ftp_addrs;
    if (g_etcd_cli_mng->GetAllFtpSrvs(ftp_addrs) == 0)
    {
        CLogicSrvMng::Instance()->UpdateFtpSrvList(ftp_addrs);
    }
}

void UpdateAkcsSrvList()
{
    UpdateAccSrvList();
    UpdateRtspSrvList();
    UpdateOpsSrvList();
    UpdateFtpSrvList();
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstCSGATEConf.db_ip, gstCSGATEConf.db_port);
    return 0;
}

void UpdateOuterConfFromConfigSrv()
{
    GateSrvConf csgate_conf_tmp = {0};
    g_etcd_cli_mng->LoadGateSrvOuterConf(csgate_conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(csgate_conf_tmp.db_ip, gstCSGATEConf.db_ip) != 0) || (csgate_conf_tmp.db_port != gstCSGATEConf.db_port))
    {
        Snprintf(gstCSGATEConf.db_ip, sizeof(gstCSGATEConf.db_ip), csgate_conf_tmp.db_ip);
        gstCSGATEConf.db_port = csgate_conf_tmp.db_port;
        DaoReInit();
    }
}

void UpdateMqttAddrFromConfigSrv()
{
    std::string mqtt_addr;
    g_etcd_cli_mng->LoadSrvMqttOuterTlsConf(mqtt_addr);
    
    if(::strcmp(mqtt_addr.c_str(), gstCSGATEConf.mqtt_addr) != 0)
    {
        memset(gstCSGATEConf.mqtt_addr, 0, sizeof(gstCSGATEConf.mqtt_addr));
        Snprintf(gstCSGATEConf.mqtt_addr, sizeof(gstCSGATEConf.mqtt_addr), mqtt_addr.c_str());
    }
}

//暂时没有实现
void UpdateInnerConfFromConfigSrv()
{

}

