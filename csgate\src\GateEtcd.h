#ifndef __CSGATE_ETCD_H__
#define __CSGATE_ETCD_H__

#include <string>
//#include <evpp/event_loop.h>
#include "EtcdCliMng.h"

int WatchSrvFromEtcd(const std::string& key, WatchSrvCallback cb);
void UpdateAccSrvList();
void UpdateRtspSrvList();
void UpdateAkcsSrvList();
void UpdateOpsSrvList();
//gate外部的配置项,例如:与db通信,redis，cssession通信等也同样适用于其他csmain\csadapt等组件使用的配置项，配置中心统一用/akconf/db_addr,/akconf/...
void UpdateOuterConfFromConfigSrv();
//内部配置项,例如限流开关、have_slb等,配置中心统一用/akconf/csgate/node/{conf}来标记
void UpdateInnerConfFromConfigSrv();
void UpdateMqttAddrFromConfigSrv();

#endif //__CSGATE_ETCD_H__

