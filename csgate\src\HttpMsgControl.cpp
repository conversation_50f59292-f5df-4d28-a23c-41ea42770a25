#include "HttpMsgControl.h"
#include "json/json.h"
#include "HttpResp.h"

using namespace Akcs;


static struct http_state_table HTTPSTATE_CHART[] = {
    {HTTP_CODE_SUC, "success"},
    {HTTP_CODE_ERR_USER_NOT_EXIT, "username or passwd error."},
    {HTTP_CODE_ERR_PASSWD_INVALID, "username or passwd error."},
    {HTTP_CODE_ERR_APP_EXPIRE, "Your account is expire."},
    {HTTP_CODE_ERR_APP_UNACTIVE, "Your account is unactivated."},
    {HTTP_CODE_ERR_APP_UNPAID, "Your account is unactivated."}, 
    {HTTP_CODE_ERR_VERSION_INVALID, "version invalid."},
    {HTTP_CODE_ERR_TOKEN_INVALID, "request is invalid"},
    {HTTP_CODE_ERR_TOKEN_EXPIRE, "token is expired"},
    {HTTP_CODE_ERR_PM_APP_CLOSED, "Your pm app account is closed."},
    {HTTP_CODE_ERR_PM_APP_UNCREATED, "Your pm app account is uncreated."},
    {HTTP_CODE_ERR_INS_APP_CLOSED, "Your ins app account is closed."}
    
};

const char *httpRespState2str(int state)
{
    int x;
    const char *str = "UNKNOWN";
    int len = sizeof(HTTPSTATE_CHART) / sizeof(struct http_state_table);
    for (x = 0; x <= len - 1; x++) {
        if (HTTPSTATE_CHART[x].state == state) {
            str = HTTPSTATE_CHART[x].message;
            break;
        }
    }

    return str;
}


const std::map<std::string, std::string> g_gate_error_code = {
    {ERR_CODE_SUCCESS, "success"},
    {ERR_CODE_TOKEN_ERR, "Token error."},
    {ERR_CODE_TOKEN_EXPIRE, "Token expire."},
    {ERR_CODE_REFRESH_TOKENE_RR, "refreshToken error."},
    {ERR_CODE_USER_INFO_ERR, "user not exist or passwd incorrect."},
    {ERR_CODE_PM_APP_UNCREATED, "pm app uncreated."},
    {ERR_CODE_INS_APP_STATUS_CLOSED, "ins app status is closed."},
    {ERR_CODE_APP_VERIFY_CODE_FAILED, "Verification code is invalid."},
    {ERR_CODE_HTTP_BODY_INVALID, "http body is invalid"}
};

std::string httpRespCommonState2str(const std::string& key)
{
    auto it = g_gate_error_code.find(key);
    if (it != g_gate_error_code.end())
    {
        return it->second; 
    }
    return ""; 
}

std::string buildCommHttpMsg(int code,  const HttpRespKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[RESULT] = code;
    item[MESSAGE] = httpRespState2str(code);
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    item[DATAS] = itemData;

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string buildErrorHttpMsg(int code)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[RESULT] = code;
    item[MESSAGE] = httpRespState2str(code);

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string buildNewErrorHttpMsg(const std::string &code)
{
    Json::Value item;
    Json::FastWriter w;

    item[RET_ERR_CODE] = code;
    item[MESSAGE] = httpRespCommonState2str(code);

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string buildNewRespHttpMsg(const std::string &code, const HttpRespKV& kv, const HttpRespIntKV& kv_int)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[RET_ERR_CODE] = code;
    item[MESSAGE] = httpRespCommonState2str(code);
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    for (const auto& tmpkv : kv_int)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    item[DATAS] = itemData;

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string buildCommHttpMsg2(int code, const HttpRespIntKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[RESULT] = code;
    item[MESSAGE] = httpRespState2str(code);
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    item[DATAS] = itemData;

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string GetReqResponData(const std::string& msg)
{
    std::stringstream oss;

    oss << "{" <<  "\n"
        << "\"datas\": " << "\"" << msg << "\"" << "\n"
        << "}" << "\n";
        
    return oss.str();
}

// 判断是否支持pm app和enduser app使用同一个入口登录
bool CheckIsSupportPMAppEntrance(const evpp::http::ContextPtr& ctx)
{
    const char* api_version = ctx->FindRequestHeader("api-version");
    
    if (STOF(api_version) < std::stof(csgate::V66))
    {
        return true;    
    }
    
    return false;
}


