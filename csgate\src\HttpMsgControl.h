#ifndef __HTTP_MSG_CONTORL_H__
#define __HTTP_MSG_CONTORL_H__
#include <map>
#include <string>
#include "evpp/http/context.h"


typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespKV;
typedef std::map<std::string/*key*/, int/*value*/> HttpRespIntKV;

#define HTTP_CODE_ERR_TOKEN_EXPIRE -2
#define HTTP_CODE_ERR_TOKEN_INVALID -1


#define ERR_CODE_SUCCESS "0"
#define ERR_CODE_TOKEN_ERR  "1000100001"
#define ERR_CODE_TOKEN_EXPIRE  "1000100002"
#define ERR_CODE_REFRESH_TOKENE_RR  "1000100003"
#define ERR_CODE_USER_INFO_ERR "1000100004" //用户不存在或密码错误
#define ERR_CODE_PM_APP_UNCREATED "1000100005"
#define ERR_CODE_INS_APP_STATUS_CLOSED "1000100006"
#define ERR_CODE_APP_VERIFY_CODE_FAILED "1000100007"
#define ERR_CODE_HTTP_BODY_INVALID "1000100008"
#define ERR_CODE_ADMIN_APP_UNCREATED "1000100010"


enum HTTP_CODE
{
    HTTP_CODE_SUC = 0,
    HTTP_CODE_ERR_USER_NOT_EXIT = 1,
    HTTP_CODE_ERR_PASSWD_INVALID = 2,
    HTTP_CODE_ERR_APP_EXPIRE = 3,
    HTTP_CODE_ERR_APP_UNACTIVE = 4,
    HTTP_CODE_ERR_APP_UNPAID = 5,
    HTTP_CODE_ERR_VERSION_INVALID = 8,
    HTTP_CODE_ERR_PM_APP_CLOSED = 9,
    HTTP_CODE_ERR_PM_APP_UNCREATED = 10,
    HTTP_CODE_ERR_INS_APP_CLOSED = 11,
};


struct http_state_table {
    int state;
    const char *message;
};


std::string buildCommHttpMsg(int code, const HttpRespKV& kv);
std::string buildCommHttpMsg2(int code, const HttpRespIntKV& kv);
std::string buildErrorHttpMsg(int code);
std::string GetReqResponData(const std::string& msg);
bool CheckIsSupportPMAppEntrance(const evpp::http::ContextPtr& ctx);



std::string httpRespCommonState2str(const std::string& key);
std::string buildNewErrorHttpMsg(const std::string &code);
std::string buildNewRespHttpMsg(const std::string &code, const HttpRespKV& kv={}, const HttpRespIntKV& kv_int={});


#endif //__HTTP_MSG_CONTORL_H__
