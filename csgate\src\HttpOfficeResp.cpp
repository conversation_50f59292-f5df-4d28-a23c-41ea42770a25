#include "ConnectionPool.h"
#include <evpp/logging.h>
#include "RldbQuery.h"
#include "Md5.h"
#include "AES256.h"
#include "util_cstring.h"
#include "util.h"
#include "ServerMng.h"
#include "AkcsCommonDef.h"
#include "HttpResp.h"
#include <iostream>
#include <functional>
#include "ClientMng.h"
#include "ServerMng.h"
#include "CsgateConf.h"
#include "LogicSrvMng.h"
#include "AkLogging.h"
#include "AES256.h"
#include "json/json.h"
#include "Url.h"
#include "AuditLog.h"
#include "Caesar.h"
#include "util.h"
#include "evpp/rate_limiter/rate_limiter_interface.h"
#include "HttpMsgControl.h"
#include "HttpOfficeResp.h"
#include "dbinterface/Verification.h"
#include "dbinterface/Account.h"
#include "OfficeUserControl.h"
#include "dbinterface/office/OfficeInfo.h"
#include "KafkaNotifyHandler.h"
#include "HttpResp.h"

extern CSGATE_CONF gstCSGATEConf; //全局配置信息
extern evpp::rate_limiter::RateLimiterInterface *g_rate_limiter;
extern AWS_CSGATE_CONF gstAWSConf; //全局配置信息
extern const unsigned int g_ipaddr_check_len;
extern AWS_CSGATE_CONF gstAWSAucloudConf;
extern AWS_CSGATE_CONF gstASBJConf;
extern AWS_CSGATE_CONF gstEcloud2UcloudConf;

#define CHNAGE_REDIRECT_WEB_DOMAIN() \
if(ServerArea::scloud == gstCSGATEConf.server_area && redirect == RedirectCloudType::REDIRECT_AUCLOUD) \
{\
    web_addr = gstAWSAucloudConf.web_domain; \
    web_ipv6 = gstAWSAucloudConf.web_ipv6;     \
    rest_addr = gstAWSAucloudConf.rest_addr; \
    rest_ipv6 = gstAWSAucloudConf.rest_ipv6; \
    rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr; \
    rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6; \
} \
else if(ServerArea::scloud == gstCSGATEConf.server_area && redirect == RedirectCloudType::REDIRECT_JCLOUD) \
{\
    web_addr = gstAWSConf.web_domain; \
    web_ipv6 = gstAWSConf.web_ipv6; \
    rest_addr = gstAWSConf.rest_addr; \
    rest_ipv6 = gstAWSConf.rest_ipv6; \
    rest_ssl_addr = gstAWSConf.rest_ssl_addr; \
    rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6; \
} \
else if(ServerArea::ecloud == gstCSGATEConf.server_area && redirect == RedirectCloudType::REDIRECT_ASBJ && user_agent == "ASBJ") \
{\
    web_addr = gstASBJConf.web_domain; \
    web_ipv6 = gstASBJConf.web_ipv6; \
    rest_addr = gstASBJConf.rest_addr; \
    rest_ipv6 = gstASBJConf.rest_ipv6; \
    rest_ssl_addr = gstASBJConf.rest_ssl_addr; \
    rest_ssl_ipv6 = gstASBJConf.rest_ssl_ipv6; \
}\
else if(ServerArea::ecloud == gstCSGATEConf.server_area && redirect == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD) \
{\
    web_addr = gstEcloud2UcloudConf.web_domain; \
    web_ipv6 = gstEcloud2UcloudConf.web_ipv6; \
    rest_addr = gstEcloud2UcloudConf.rest_addr; \
    rest_ipv6 = gstEcloud2UcloudConf.rest_ipv6; \
    rest_ssl_addr = gstEcloud2UcloudConf.rest_ssl_addr; \
    rest_ssl_ipv6 = gstEcloud2UcloudConf.rest_ssl_ipv6; \
}



namespace csgate
{




RedirectCloudType OfficeUserRedirect(int office_id, const std::string &account, OUTER_SERVER_ADDR &server_addr)
{
    RedirectCloudType redirect_ret = OfficeDaoCheckUserRedirect(office_id);
    if(gstCSGATEConf.aws_redirect && redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
    {
        std::pair<std::string, std::string> pair_aws_access_ser46 = CLogicSrvMng::Instance()->GetAwsAccDomainSrv(account);
        std::pair<std::string, std::string> pair_aws_rtsp_ser46 = CLogicSrvMng::Instance()->GetAwsRtspDomainSrv(account);
        std::pair<std::string, std::string> pair_ops_ser46 = CLogicSrvMng::Instance()->GetAwsOpsDomainSrv(account);
        snprintf(server_addr.csmain_domain, sizeof(server_addr.csmain_domain), "%s", pair_aws_access_ser46.first.c_str());
        snprintf(server_addr.csmain_ipv6, sizeof(server_addr.csmain_ipv6), "%s", pair_aws_access_ser46.second.c_str());
        snprintf(server_addr.csvrtsp_domain, sizeof(server_addr.csvrtsp_domain), "%s", pair_aws_rtsp_ser46.first.c_str());
        snprintf(server_addr.csvrtsp_ipv6, sizeof(server_addr.csvrtsp_ipv6), "%s", pair_aws_rtsp_ser46.second.c_str());
        snprintf(server_addr.pbx_domain, sizeof(server_addr.pbx_domain), "%s", pair_ops_ser46.first.c_str());
        snprintf(server_addr.pbx_ipv6, sizeof(server_addr.pbx_ipv6), "%s", pair_ops_ser46.second.c_str());
    }
    else if(gstCSGATEConf.aws_redirect && redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        snprintf(server_addr.csmain_domain, sizeof(server_addr.csmain_domain), "%s", gstAWSAucloudConf.csmain_domain);
        snprintf(server_addr.csmain_ipv6, sizeof(server_addr.csmain_ipv6), "%s", gstAWSAucloudConf.csmain_ipv6);
        snprintf(server_addr.csvrtsp_domain, sizeof(server_addr.csvrtsp_domain), "%s", gstAWSAucloudConf.csvrtsp_domain);
        snprintf(server_addr.csvrtsp_ipv6, sizeof(server_addr.csvrtsp_ipv6), "%s", gstAWSAucloudConf.csvrtsp_ipv6);
        snprintf(server_addr.pbx_domain, sizeof(server_addr.pbx_domain), "%s", gstAWSAucloudConf.pbx_domain);
        snprintf(server_addr.pbx_ipv6, sizeof(server_addr.pbx_ipv6), "%s", gstAWSAucloudConf.pbx_ipv6);
    }
    else if(gstCSGATEConf.aws_redirect && redirect_ret == RedirectCloudType::REDIRECT_ASBJ)
    {
        snprintf(server_addr.csmain_domain, sizeof(server_addr.csmain_domain), "%s", gstASBJConf.csmain_domain);
        snprintf(server_addr.csmain_ipv6, sizeof(server_addr.csmain_ipv6), "%s", gstASBJConf.csmain_ipv6);
        snprintf(server_addr.csvrtsp_domain, sizeof(server_addr.csvrtsp_domain), "%s", gstASBJConf.csvrtsp_domain);
        snprintf(server_addr.csvrtsp_ipv6, sizeof(server_addr.csvrtsp_ipv6), "%s", gstASBJConf.csvrtsp_ipv6);
        snprintf(server_addr.pbx_domain, sizeof(server_addr.pbx_domain), "%s", gstASBJConf.pbx_domain);
        snprintf(server_addr.pbx_ipv6, sizeof(server_addr.pbx_ipv6), "%s", gstASBJConf.pbx_ipv6);
    }
    else if(gstCSGATEConf.aws_redirect && redirect_ret == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)
    {
        snprintf(server_addr.csmain_domain, sizeof(server_addr.csmain_domain), "%s", gstEcloud2UcloudConf.csmain_domain);
        snprintf(server_addr.csmain_ipv6, sizeof(server_addr.csmain_ipv6), "%s", gstEcloud2UcloudConf.csmain_ipv6);
        snprintf(server_addr.csvrtsp_domain, sizeof(server_addr.csvrtsp_domain), "%s", gstEcloud2UcloudConf.csvrtsp_domain);
        snprintf(server_addr.csvrtsp_ipv6, sizeof(server_addr.csvrtsp_ipv6), "%s", gstEcloud2UcloudConf.csvrtsp_ipv6);
        snprintf(server_addr.pbx_domain, sizeof(server_addr.pbx_domain), "%s", gstEcloud2UcloudConf.pbx_domain);
        snprintf(server_addr.pbx_ipv6, sizeof(server_addr.pbx_ipv6), "%s", gstEcloud2UcloudConf.pbx_ipv6);
    }    
    return redirect_ret;
}


void GetOfficePbxServerForApp(int office_id, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{
    csgate::DaoGetCommPbxServer(office_id, pbx_ip, pbx_ipv6, pbx_domain);
    return;
}

void ReqOfficeLoginHandlerV63(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");

    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char users[64];
        snprintf(users, sizeof(users), "%s", user.c_str());
        akuvox_encrypt::CaesarDecry(users);
        user = users;
    }

    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    std::string user_key = user;
    std::string user_md5 = akuvox_encrypt::MD5(user_key).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string encrypt_resp;
    TrimString(user);
    float ver = 4.6;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    
    PerAccountUserInfo user_info;
    OfficeAccount account;
    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), user, user_info, account))
    {
        AK_LOG_INFO << "Login error, user: not found, user:" << user;
        csgate::AESEncryptRespone(buildErrorHttpMsg(ERR_USER_NOT_EXIT), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    
    ret = dbcontrol::OfficeUserControl::CheckOfficeUser(account, passwd_md5);
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_INFO << "Login error, user: not found, ERR_PASSWD_INVALID  user:" << user;
        csgate::AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    const char* head_agent = ctx->FindRequestHeader("User-Agent");
    const char* head = ctx->FindRequestHeader("api-version");
    int is_ios = 0;
    float ios_ver = STOF(head);
    if (head_agent && strcasestr(head_agent, "ios"))
    {
        is_ios = 1;
    }

    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");


    std::string web_addr = strlen(gstCSGATEConf.web_domain_name) > 2 ? gstCSGATEConf.web_domain_name :  gstCSGATEConf.web_ip;
    if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
    {
        web_addr += ":9443";
    }

    USER_CONF user_conf = {0};
    OfficeInfo office_info(account.office_id);
    user_conf.mng_account_id = account.office_id;
    user_conf.have_public_dev = 1;
    user_conf.initialization = office_info.IsAllowCreatePin() ? account.is_init:1;
    user_conf.is_show_tempkey = account.is_show_tmpkey;
    user_conf.role = account.role;
    
    //added by chenyc, 2019-06-17,查询下是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
    int is_show_payment = 0, is_show_subscription = 0;
    {
        //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
        #if 0
        if (((personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN)) && 0 == gstCSGATEConf.is_china_version)
        {
            is_show_subscription = csgate::DaoGetAppPayMode(personal_account_info.uid) == 0 ? 1 : 0;
            is_show_payment = is_show_subscription;
        }
        #endif
    }
    //modified by chenyc 2020.06.30,全量切换到主动注销的模式
    //app在后台时的sip动作模式,0:app退到后台后,sip不注销;1:sip主动注销,对应的是全部走端外推送的来电呼叫
    int android_app_push_mode = 1;
    std::string token;
    csgate::GetToken(account.account, user_info.main_user_account, token, ver);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, account.account)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        if (passwd_md5 != akuvox_encrypt::MD5("akuvox").toStr())
        {
            csgate::DaoUpdateAppLoginStatus(account.account);
        }
        /*
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), account->account);
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", account->account);
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(account->role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, account->role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        */

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        csgate::GetOpsServer(account.account, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(account.account);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(account.account);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(account.account);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string web_ipv6 = gstCSGATEConf.web_ipv6;

        OUTER_SERVER_ADDR server_addr;
        memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
        RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;

            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::UpdateTokenToRedirectServer(account.account, token, "", redirect);
        }
        
        AK_LOG_INFO << "login, uid is " << account.account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        //获取smarthomeid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(account.account, smarthome_uid);

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(account.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_TEMPKEY, std::to_string(user_conf.is_show_tempkey))); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          
            
        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
}

void ReqOfficeSmsLoginHandlerV63(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string area_code;
    std::string code = ctx->GetQuery("code");
    std::string phone = ctx->GetQuery("phone");

    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char user[64];
        snprintf(user, sizeof(user), "%s", phone.c_str());
        akuvox_encrypt::CaesarDecry(user);
        phone = user;
    }

    float ver = 5.5;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    std::string user_md5 = akuvox_encrypt::MD5(phone).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string encrypt_resp;

    PerAccountUserInfo user_info;
    OfficeAccount account;
    
    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), phone, user_info, account))
    {
        AK_LOG_INFO << "sms login error, phone:" << phone << " not found";
        csgate::AESEncryptRespone(buildErrorHttpMsg(csgate::ERR_USER_NOT_EXIT), key, encrypt_resp);
        cb(encrypt_resp);        
        return;
    }

    VerificationPtr code_info = nullptr;
    dbinterface::VerificationCode::GetVerificationCode(account.account, code_info);
    ret = dbcontrol::OfficeUserControl::CheckOfficePhone(account, code_info, code, area_code);
    if (ret == csgate::ERR_SUCCESS)
    {
        dbinterface::VerificationCode::DeleteVerificationCode(account.account);
    }


    int role = account.role;
    std::string uid = account.account;
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");

    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << " user_agent:" << user_agent;

    std::string web_addr;
    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        csgate::AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    OfficeInfo office_info(account.office_id);
    user_conf.mng_account_id = account.office_id;
    user_conf.have_public_dev = 1;
    user_conf.initialization = office_info.IsAllowCreatePin() ? account.is_init:1;
    user_conf.is_show_tempkey = account.is_show_tmpkey;
    user_conf.role = account.role;

    //是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
    int is_show_payment = 0, is_show_subscription = 0;
    {
        //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
        #if 0
        if (((role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (role == ACCOUNT_ROLE_COMMUNITY_MAIN)) && 0 == gstCSGATEConf.is_china_version)
        {
            is_show_subscription = csgate::DaoGetAppPayMode(uid) == 0 ? 1 : 0;
            is_show_payment = is_show_subscription;
        }
        #endif
    }
    //app在后台时的sip动作模式,0:app退到后台后,sip不注销;1:sip主动注销,对应的是全部走端外推送的来电呼叫
    int android_app_push_mode = 1;
    std::string token;
    csgate::GetToken(uid, main_account, token, ver);
    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(role))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        csgate::DaoUpdateAppLoginStatus(uid);

        /*
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(user_conf.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, user_conf.role, user_conf.mng_account_id);
        if (user_conf.role > 0)
        {
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }
        */

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_domain;
        std::string pbx_ipv6;        
        std::string pbx_ip;
        std::string web_ipv6 = gstCSGATEConf.web_ipv6;
        csgate::GetOpsServer(account.account, pbx_ip, pbx_ipv6, pbx_domain);
        
        OUTER_SERVER_ADDR server_addr;
        memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
        RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;
            
            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::UpdateTokenToRedirectServer(account.account, token, auth_token, redirect);
        }
        
        AK_LOG_INFO << "login,uid is " << uid << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", rtmp is:" << pair_rtmp_ser46.first;
        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(user_conf.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_TEMPKEY, std::to_string(user_conf.is_show_tempkey))); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
}

void ReqOfficeServerListHandlerV63(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    int ret = 0;
    std::string encrypt_resp;
    std::stringstream oss;
    std::stringstream encrypt_oss;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string http_body = ctx->body().ToString();
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb("parse http body json error");
        return;
    }

    std::string req_user = root["user"].asString();

    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char user[64];
        snprintf(user, sizeof(user), "%s", req_user.c_str());
        akuvox_encrypt::CaesarDecry(user);
        req_user = user;
    }

    std::string datas = root["datas"].asString();
    std::string md5_user = akuvox_encrypt::MD5(req_user).toStr();
    std::string key_head = md5_user.substr(0, 16);
    std::string data_blank;
    csgate::AESDecryptRequest(datas, key_head, data_blank);

    Json::Reader reader1;
    Json::Value root1;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader1.parse(data_blank, root1))
    {
        AK_LOG_WARN << "parse json error.data=" << data_blank;
        csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }
    
    if (!HttpCheckSqlParam(req_user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << req_user;
        csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_PASSWD_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }

    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << data_blank;
    }

    std::string token = root1["token"].asString();
    std::string passwd = root1["passwd"].asString();
    std::string auth_token = root1["auth_token"].asString();
    
    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user: " << req_user << " passwd: " << passwd;
            csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
            cb(GetReqResponData(encrypt_resp));
            return;
        }
    }
        
    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.0);

    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << req_user;
        csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);
 
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        csgate::GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        const char* head_ag = ctx->FindRequestHeader("User-Agent");
        std::string user_agent = GetCtxHeader(ctx, "User-Agent");
        const char* head = ctx->FindRequestHeader("api-version");
        int is_ios = 0;
        float ios_ver = STOF(head);
        if (head_ag && strcasestr(head_ag, "ios"))
        {
            is_ios = 1;
        }
        if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
        {
            web_addr += ":9443";
        }

        std::string csmain_domain =  pair_access_ser_46.first;
        std::string csmain_ipv6 = pair_access_ser_46.second;
        std::string csvrtsp_domain = pair_rtsp_ser_46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;
        
        OUTER_SERVER_ADDR server_addr;
        memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
        RedirectCloudType redirect = OfficeUserRedirect(personal_account_info.parent_id, personal_account_info.account, server_addr);
        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;

            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::RedirectServerTokenRefresh(user, main_account, redirect);
        }

        ChangeRtspAddr(is_ios, csvrtsp_domain, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list, user is " << user << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
                    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

        InsertNewGateServer(redirect, kv, user_agent);
            
        csgate::AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
    }
    return;
}

void ReqOfficeLoginHandlerV64(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int ret = 0;
    std::string username = ctx->GetQuery("user");
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    const char* app_ver = ctx->FindRequestHeader("api-version");
    
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    std::string user_key = username;
    std::string md5_user = akuvox_encrypt::MD5(user_key).toStr();
    std::string encrypt_resp;
    std::string encrypt_key = md5_user.substr(0, 16);    
    TrimString(username);

    PerAccountUserInfo user_info;
    OfficeAccount account;
    if(0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), username, user_info, account))
    {
        AK_LOG_INFO << "office login error, user: not found, user:" << username;
        csgate::AESEncryptRespone(buildErrorHttpMsg(csgate::ERR_USER_NOT_EXIT), encrypt_key, encrypt_resp);
        cb(encrypt_resp);  
        return;
    }
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    
    std::string uid = account.account;
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");

    std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
    csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    ret = dbcontrol::OfficeUserControl::CheckOfficeUser(account, passwd_md5);

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        csgate::AESEncryptRespone(buildErrorHttpMsg(ret), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }    
    std::string access_token;
    csgate::GetToken(uid, main_account, access_token, 6.4);
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        csgate::GetOpsServer(account.account, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(account.account);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(account.account);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(account.account);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string web_ipv6 = gstCSGATEConf.web_ipv6;
        
        OUTER_SERVER_ADDR server_addr;
        memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
        RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;

            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::UpdateTokenToRedirectServer(account.account, access_token, "", redirect);            
        }

        AK_LOG_INFO << "login, uid is " << account.account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        //获取smarthomeid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(account.account, smarthome_uid);

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 
        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
}

void ReqOfficeSmsLoginHandlerV64(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string area_code;
    std::string code = ctx->GetQuery("code");
    std::string phone = ctx->GetQuery("phone");

    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", phone.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    phone = user_tmp;

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    const char* app_ver = ctx->FindRequestHeader("api-version");

    std::string encrypt_resp;
    std::string md5_user = akuvox_encrypt::MD5(phone).toStr();
    std::string encrypt_key = md5_user.substr(0, 16);
    
    PerAccountUserInfo user_info;
    OfficeAccount account;
    
    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), phone, user_info, account))
    {
        AK_LOG_WARN << "sms login error, phone: not found " << phone;
        csgate::AESEncryptRespone(buildErrorHttpMsg(csgate::ERR_USER_NOT_EXIT), encrypt_key, encrypt_resp);
        cb(encrypt_resp);  
        return;
    }
    
    std::string uid = account.account;
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");
    
    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << " user_agent:" << user_agent;
   
    std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
    csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);
        
    VerificationPtr code_info;
    dbinterface::VerificationCode::GetVerificationCode(account.account, code_info);
    ret = dbcontrol::OfficeUserControl::CheckOfficePhone(account, code_info, code, area_code);
    if (ret == csgate::ERR_SUCCESS)
    {
        dbinterface::VerificationCode::DeleteVerificationCode(account.account);
    }
    else if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        csgate::AESEncryptRespone(buildErrorHttpMsg(ret), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    std::string access_token;
    csgate::GetToken(uid, main_account, access_token, 6.4);
    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_domain;
        std::string pbx_ipv6;        
        std::string pbx_ip;
        csgate::GetOpsServer(account.account, pbx_ip, pbx_ipv6, pbx_domain);
        std::string web_ipv6 = gstCSGATEConf.web_ipv6;
        
        OUTER_SERVER_ADDR server_addr;
        memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
        RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;

            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::UpdateTokenToRedirectServer(account.account, access_token, "", redirect);         
        }

        AK_LOG_INFO << "login,uid is " << uid << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", rtmp is:" << pair_rtmp_ser46.first;
        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6,web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          
        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
}

void ReqOfficeServerListHandlerV64(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    int ret = 0;
    std::string encrypt_resp;
    std::stringstream oss;
    std::stringstream encrypt_oss;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string http_body = ctx->body().ToString();
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb("parse http body json error");
        return;
    }

    std::string req_user = root["user"].asString();
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", req_user.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    req_user = user_tmp;

    std::string datas = root["datas"].asString();
    std::string md5_user = akuvox_encrypt::MD5(req_user).toStr();
    std::string key_head = md5_user.substr(0, 16);
    std::string data_blank;
    csgate::AESDecryptRequest(datas, key_head, data_blank);

    Json::Reader reader1;
    Json::Value root1;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader1.parse(data_blank, root1))
    {
        AK_LOG_WARN << "parse json error.data=" << data_blank;
        csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        encrypt_oss << "{" <<  "\n"
                    << "\"datas\": " << "\"" << encrypt_resp << "\"" << "\n"
                    << "}" << "\n";
        cb(encrypt_oss.str());
        return;
    }

    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << data_blank;
    }

    std::string token = root1["token"].asString();
    std::string passwd = root1["passwd"].asString();
    std::string auth_token = root1["auth_token"].asString();

    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user: " << req_user << " passwd: " << passwd;
            csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
            cb(GetReqResponData(encrypt_resp));
            return;
        }
    }

    const char* head_ag = ctx->FindRequestHeader("User-Agent");
    int is_ios = 0;
    if (head_ag && strcasestr(head_ag, "ios"))
    {
        is_ios = 1;
    }

    std::string user_agent = GetCtxHeader(ctx, "User-Agent");
    
    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.0);

    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user:" << req_user;
        csgate::AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(user);
        
        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        csgate::GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        std::string csmain_domain =  pair_access_ser_46.first;
        std::string csmain_ipv6 = pair_access_ser_46.second;
        std::string csvrtsp_domain = pair_rtsp_ser_46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;

        OUTER_SERVER_ADDR server_addr;
        memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
        RedirectCloudType redirect = OfficeUserRedirect(personal_account_info.parent_id, personal_account_info.account, server_addr);
        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;
            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::RedirectServerTokenRefresh(user, main_account, redirect);           
        }

        ChangeRtspAddr(is_ios, csvrtsp_domain, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list, user is " << user << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
                    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        
        InsertNewGateServer(redirect, kv, user_agent);

        csgate::AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key_head, encrypt_resp);
        encrypt_oss << "{" <<  "\n"
                    << "\"datas\": " << "\"" << encrypt_resp << "\"" << "\n"
                    << "}" << "\n";
        cb(encrypt_oss.str());
    }
    return;
}

void ReqOfficeLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string username = ctx->GetQuery("user");    
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);    
    const char* app_ver = ctx->FindRequestHeader("api-version");
    
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    TrimString(username);
    
    PerAccountUserInfo user_info;
    OfficeAccount account;

    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), username, user_info, account))
    {
        AK_LOG_INFO << "Login error, user: not found, user:" << username;
        cb(buildErrorHttpMsg(csgate::ERR_USER_NOT_EXIT));  
        return;
    }
    
    ret = dbcontrol::OfficeUserControl::CheckOfficeUser(account, passwd_md5);
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        cb(buildErrorHttpMsg(ret));
        return;
    }    

    //不同app类型处理
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(account.account, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(account.account, main_account, token_renew_info);

    //web addr
    std::string web_addr, rest_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    // 判断用户是否需要重定向到迁移服务器
    OUTER_SERVER_ADDR server_addr;
    memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
    RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程
    if (redirect && STOF(app_ver) >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }

    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (STOF(app_ver) >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS,  rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildCommHttpMsg(ret, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        csgate::GetOpsServer(main_account, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(main_account);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(main_account);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(main_account);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);
        std::string rtmp_addr = pair_rtmp_ser46.first;
        std::string rtmp_ipv6 = pair_rtmp_ser46.second;
        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        
        //调度 -- 服务器迁移相关
		if(redirect)
		{
			csmain_domain =  server_addr.csmain_domain;
			csmain_ipv6 = server_addr.csmain_ipv6;
			csvrtsp_domain = server_addr.csvrtsp_domain;
			csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
			pbx_domain = server_addr.pbx_domain;
			pbx_ipv6 = server_addr.pbx_ipv6;

            CHNAGE_REDIRECT_WEB_DOMAIN();
			csgate::UpdateTokenToRedirectServer(account.account, token_renew_info.token, "", redirect);
		}


        AK_LOG_INFO << "login, uid is " << account.account << ",main_account is" << main_account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        //获取smarthomeid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(account.account, smarthome_uid);

        // 获取tls地址
        std::string pbxs_ipv6_addr;
        std::string pbxs_domain_addr;
        std::string csvrtsps_ipv6_addr;
        std::string csvrtsps_domain_addr;
        csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
        csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsps_domain_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsps_ipv6_addr));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, rtmp_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, rtmp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 

        cb(buildCommHttpMsg(ret, kv));
    }
    return;
}

void ReqNewOfficeLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string username = ctx->GetQuery("user");    
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);    
    const char* app_ver = ctx->FindRequestHeader("api-version");
    
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    TrimString(username);
    
    PerAccountUserInfo user_info;
    OfficeAccount account;

    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), username, user_info, account))
    {
        AK_LOG_INFO << "Login error, user: not found, user:" << username;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));  
        return;
    }
    
    ret = dbcontrol::OfficeUserControl::CheckOfficeUser(account, passwd_md5);
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }    

    if(account.role == ACCOUNT_ROLE_OFFICE_NEW_PER){
        // 新办公用户app登录，将审计日志发送到
        AK_LOG_INFO << "New Office App login success. account=" << account.account;
        KafkaNotifyHandler::GetInstance()->PushNewOfficeUserLoginAuditLogMessage(account.account, ctx->remote_ip());
    }

    //不同app类型处理
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");


    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(account.account, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(account.account, main_account, token_renew_info);

    //web addr
    std::string web_ip, rest_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server;
    GetWebServer(web_ip, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server);

    // 判断用户是否需要重定向到迁移服务器
    OUTER_SERVER_ADDR server_addr;
    memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
    RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程
    if (redirect && STOF(app_ver) >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }

    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (STOF(app_ver) >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_server));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS,  rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //获取smarthomeid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(account.account, smarthome_uid);

        HttpRespKV kv;
        OfficeLoginGerServerInfo(account.account, main_account, account.office_id, token_renew_info.token, kv, user_agent);
        csgate::UpdateRefreshTokenToRedirectServer(account.account, token_renew_info.refresh_token, redirect);


        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 

        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;
}

void ReqOfficeSmsLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string phone = ctx->GetQuery("phone");
    std::string code = ctx->GetQuery("code");
    std::string area_code = ctx->GetQuery("area_code");
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    const char* app_ver = ctx->FindRequestHeader("api-version");

    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", phone.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    phone = user_tmp;

    PerAccountUserInfo user_info;
    OfficeAccount account;
    
    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), phone, user_info, account))
    {
        AK_LOG_INFO << "sms login error, phone:" << phone << " not found ";
        cb(buildErrorHttpMsg(csgate::ERR_USER_NOT_EXIT));        
        return;
    }
        
    VerificationPtr code_info;
    dbinterface::VerificationCode::GetVerificationCode(account.account, code_info);
    ret = dbcontrol::OfficeUserControl::CheckOfficePhone(account, code_info, code, area_code);
    if (ret == csgate::ERR_SUCCESS)
    {
        dbinterface::VerificationCode::DeleteVerificationCode(account.account);
    }
    else if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        cb(buildErrorHttpMsg(ret));
        return;
    }
    
    std::string uid = account.account;
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");


    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << " user_agent:" << user_agent;

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);

    std::string auth_token;
    csgate::GetAuthToken(uid, user_info.main_user_account, auth_token);

    std::string web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    // 判断用户是否需要重定向到迁移服务器
    OUTER_SERVER_ADDR server_addr;
    memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
    RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程
    if (redirect && STOF(app_ver) >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }

    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (STOF(app_ver) >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }
    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildCommHttpMsg(ret, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(main_account);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(main_account);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(main_account);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);
        std::string rtmp_addr = pair_rtmp_ser46.first;
        std::string rtmp_ipv6 = pair_rtmp_ser46.second;
        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_domain;
        std::string pbx_ipv6;        
        std::string pbx_ip;
        csgate::GetOpsServer(main_account, pbx_ip, pbx_ipv6, pbx_domain);

        if(redirect)
        {
            csmain_domain =  server_addr.csmain_domain;
            csmain_ipv6 = server_addr.csmain_ipv6;
            csvrtsp_domain = server_addr.csvrtsp_domain;
            csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
            pbx_domain = server_addr.pbx_domain;
            pbx_ipv6 = server_addr.pbx_ipv6;
            
            CHNAGE_REDIRECT_WEB_DOMAIN();
            csgate::UpdateTokenToRedirectServer(account.account, token_renew_info.token, "", redirect);
        }


        // 获取tls地址
        std::string pbxs_ipv6_addr;
        std::string pbxs_domain_addr;
        std::string csvrtsps_ipv6_addr;
        std::string csvrtsps_domain_addr;
        csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
        csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

        AK_LOG_INFO << "login,uid is " << uid << ",main_account is " << main_account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", rtmp is:" << pair_rtmp_ser46.first;

        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6,web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr)); 
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, rtmp_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, rtmp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          

        cb(buildCommHttpMsg(ret, kv));
    }
    return;
}

void ReqNewOfficeSmsLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string phone = ctx->GetQuery("phone");
    std::string code = ctx->GetQuery("code");
    std::string area_code = ctx->GetQuery("area_code");
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    const char* app_ver = ctx->FindRequestHeader("api-version");

    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", phone.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    phone = user_tmp;

    PerAccountUserInfo user_info;
    OfficeAccount account;
    
    if (0 != csgate::GetOfficeRequsetUserInfo(STOF(app_ver), phone, user_info, account))
    {
        AK_LOG_INFO << "sms login error, phone:" << phone << " not found ";
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));        
        return;
    }
        
    VerificationPtr code_info;
    dbinterface::VerificationCode::GetVerificationCode(account.account, code_info);
    ret = dbcontrol::OfficeUserControl::CheckOfficePhone(account, code_info, code, area_code);
    if (ret == csgate::ERR_SUCCESS)
    {
        dbinterface::VerificationCode::DeleteVerificationCode(account.account);
    }
    else if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }
    
    std::string uid = account.account;
    std::string main_account = user_info.main_user_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");


    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << " user_agent:" << user_agent;

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);

    std::string auth_token;
    csgate::GetAuthToken(uid, user_info.main_user_account, auth_token);

    // 判断用户是否需要重定向到迁移服务器
    OUTER_SERVER_ADDR server_addr;
    memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
    RedirectCloudType redirect = OfficeUserRedirect(account.office_id, account.account, server_addr);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程
    if (redirect && STOF(app_ver) >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }

    std::string web_ip, rest_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server;
    GetWebServer(web_ip, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server);


    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (STOF(app_ver) >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }
    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_server));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        HttpRespKV kv;
        OfficeLoginGerServerInfo(uid, main_account, account.office_id, token_renew_info.token, kv, user_agent);
        csgate::UpdateRefreshTokenToRedirectServer(uid, token_renew_info.refresh_token, redirect);

        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          

        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;
}

void ReqOfficeServerListHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;   
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string http_body = ctx->body().ToString();
    const char* head = ctx->FindRequestHeader("api-version");
    float api_version = STOF(head);
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    
    std::string req_user = root["user"].asString();
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", req_user.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    req_user = user_tmp;
    
    std::string token = root["token"].asString();
    std::string passwd = root["passwd"].asString();
    std::string auth_token = root["auth_token"].asString();
    
    AK_LOG_INFO << "user:" << req_user << " token:" << token;

    //token刷新续时
    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user:" << req_user;
            cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
            return;
        }
    }

    std::string head_ag = GetCtxHeader(ctx, "User-Agent");
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, api_version);

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user:" << req_user;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        HttpRespKV kv;
        bool redirect_token_continue = true;
        OfficeGenerateServerInfo(personal_account_info, head_ag, STOF(head), token, kv, redirect_token_continue);
        AK_LOG_INFO << "office user req server_list";
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));
            
        cb(buildCommHttpMsg(csgate::ERR_SUCCESS, kv));
    }
    return;
}

void ReqOfficeSafeServerListHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;   
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    const char* head = ctx->FindRequestHeader("api-version");
    float api_version = STOF(head);
    std::string token = GetCtxHeader(ctx, "x-auth-token");
    std::string head_ag = GetCtxHeader(ctx, "User-Agent");

    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, api_version);
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << personal_account_info.account;
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR));
        return;
    }
    else
    {
        HttpRespKV kv;
        //新版本serverlist不要续时token
        bool redirect_token_continue = false;
        OfficeGenerateServerInfo(personal_account_info, head_ag, STOF(head), token, kv, redirect_token_continue);
        AK_LOG_INFO << "office user req server_list " << personal_account_info.account;
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
            
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;
}

void OfficeGenerateServerInfo(PersonalAccountInfo& personal_account_info, const std::string& user_agent, float ver, const std::string& token, HttpRespKV& kv, bool redirect_token_continue)
{
    //web addr
    std::string web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server;
    
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server);

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;

    //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
    std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccDomainSrv(main_account);
    std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(main_account);
    std::string pbx_ip;
    std::string pbx_ipv6;
    std::string pbx_domain;        
    csgate::GetOpsServer(main_account, pbx_ip, pbx_ipv6, pbx_domain);
    std::string csmain_domain =  pair_access_ser_46.first;
    std::string csmain_ipv6 = pair_access_ser_46.second;
    std::string csvrtsp_domain = pair_rtsp_ser_46.first;
    std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;

    //调度 -- 服务器迁移相关
    OUTER_SERVER_ADDR server_addr;
    memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
    RedirectCloudType redirect = OfficeUserRedirect(personal_account_info.parent_id, personal_account_info.account, server_addr);
    if(redirect)
    {
        csmain_domain =  server_addr.csmain_domain;
        csmain_ipv6 = server_addr.csmain_ipv6;
        csvrtsp_domain = server_addr.csvrtsp_domain;
        csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
        pbx_domain = server_addr.pbx_domain;
        pbx_ipv6 = server_addr.pbx_ipv6;
        
        CHNAGE_REDIRECT_WEB_DOMAIN();
        if(redirect_token_continue)
        {
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect);
        }
    }
    int is_ios = 0;
    if (user_agent.size()!=0 && strcasestr(user_agent.c_str(), "ios"))
    {
        is_ios = 1;
    }
    //ios rtsp特殊处理
    ChangeRtspAddr(is_ios, csvrtsp_domain, csvrtsp_ipv6);

    // 获取tls地址
    std::string pbxs_ipv6_addr;
    std::string pbxs_domain_addr;
    std::string csvrtsps_ipv6_addr;
    std::string csvrtsps_domain_addr;
    csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
    csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_server));
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr)); 
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));  

    InsertNewGateServer(redirect, kv, user_agent);


    AK_LOG_INFO << "user is " << uid << ",main_account is " << main_account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;
}

void OfficeLoginGerServerInfo(const std::string& uid, const std::string& main_account, int office_id, const std::string& token, HttpRespKV& kv, const std::string& user_agent)
{
        //web addr
    std::string web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_server);

    //根据负载均衡算法获取到系统的接入服务器和转流服务器
    std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(main_account);
    std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(main_account);
    std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(main_account);
    std::pair<std::string, std::string> pair_rtmp_ser46;
    csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);
    std::string rtmp_addr = pair_rtmp_ser46.first;
    std::string rtmp_ipv6 = pair_rtmp_ser46.second;
    std::string csmain_domain =  pair_access_ser46.first;
    std::string csmain_ipv6 = pair_access_ser46.second;
    std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
    std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;

    std::string pbx_ip;
    std::string pbx_ipv6;
    std::string pbx_domain;        
    csgate::GetOpsServer(main_account, pbx_ip, pbx_ipv6, pbx_domain);

    //调度 -- 服务器迁移相关
    OUTER_SERVER_ADDR server_addr;
    memset(&server_addr, 0, sizeof(OUTER_SERVER_ADDR));
    RedirectCloudType redirect = OfficeUserRedirect(office_id, uid, server_addr);
    if(redirect)
    {
        csmain_domain =  server_addr.csmain_domain;
        csmain_ipv6 = server_addr.csmain_ipv6;
        csvrtsp_domain = server_addr.csvrtsp_domain;
        csvrtsp_ipv6 = server_addr.csvrtsp_ipv6;
        pbx_domain = server_addr.pbx_domain;
        pbx_ipv6 = server_addr.pbx_ipv6;
        
        CHNAGE_REDIRECT_WEB_DOMAIN();
        csgate::UpdateTokenToRedirectServer(uid, token, "", redirect);
    }
    // 获取tls地址
    std::string pbxs_ipv6_addr;
    std::string pbxs_domain_addr;
    std::string csvrtsps_ipv6_addr;
    std::string csvrtsps_domain_addr;
    csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
    csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));   
    kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_server));
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr)); 
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));  

    AK_LOG_INFO << "user is " << uid << ",main_account is " << main_account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;
}


}

