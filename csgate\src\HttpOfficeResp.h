#ifndef __CSGATE_HTTP_OFFICE_RESP_H__
#define __CSGATE_HTTP_OFFICE_RESP_H__

#include <string>
#include <functional>
#include <evpp/http/context.h>
#include "Dao.h"
#include "dbinterface/AwsRedirect.h"


namespace csgate
{

void ReqOfficeLoginHandlerV63(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeSmsLoginHandlerV63(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeServerListHandlerV63(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);

void ReqOfficeLoginHandlerV64(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeSmsLoginHandlerV64(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeServerListHandlerV64(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);


void ReqOfficeLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeSmsLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeServerListHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqOfficeSafeServerListHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqNewOfficeLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void ReqNewOfficeSmsLoginHandlerApp(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);

void OfficeGenerateServerInfo(PersonalAccountInfo& personal_account_info, const std::string& user_agent, float ver, const std::string& token, HttpRespKV& kv, bool redirect_token_continue);
void OfficeLoginGerServerInfo(const std::string& uid, const std::string& main_account, int office_id, const std::string& token, HttpRespKV& kv, const std::string& user_agent);
}
#endif //__CSGATE_HTTP_OFFICE_RESP_H__
