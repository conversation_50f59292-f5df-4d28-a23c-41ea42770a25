#include "AkLogging.h"
#include "HttpResp.h"
#include "HttpPmVerifyCodeAndLogin.h"
#include "util_string.h"
#include "util.h"
#include "AppTwoFactorAuth.h"
#include "dbinterface/PmAccountMap.h"
#include "AuditLog.h"
#include "dbinterface/TwoFactorAuthIDCode.h"
#include "util_string.h"

namespace csgate
{

csgate::HTTPRespCallback ReqPmVerifyCodeAndLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = ERR_SUCCESS;

    std::string http_body = ctx->body().ToString();    
    Json::Reader reader;
    Json::Value root;
    
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildNewErrorHttpMsg(ERR_CODE_HTTP_BODY_INVALID));
        return;
    }
    std::string username;
    std::string verify_code;
    if (root.isMember("user") && root.isMember("verify_code"))
    {
        username = root["user"].asString();
        verify_code = root["verify_code"].asString();
    }
    else
    {
        AK_LOG_WARN << "Missing 'verify_code' field in JSON or Missing 'user' field in JSON.";
        cb(buildNewErrorHttpMsg(ERR_CODE_HTTP_BODY_INVALID));
        return;
    }
    float api_version = STOF(ctx->FindRequestHeader("api-version"));
    //检查邮箱验证码是否正确
    if(!AppTwoFactorAuth::IsVerifyCodeCorrect(verify_code, username))
    {
        AK_LOG_INFO << "check verify code failed, user:" << username ;
        cb(buildNewErrorHttpMsg(ERR_CODE_APP_VERIFY_CODE_FAILED));
        return;
    }
    AK_LOG_INFO << "check verify code success, user:" << username ;
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoGetPmPersonalAccountInfo(username, personal_account_info);

    
    std::string uid = personal_account_info.account;
    std::string main_account = personal_account_info.main_account;

    AK_LOG_INFO << "uid: " << uid << "  main_account: " <<  main_account;

    //生成IDCode,并记录数据库
    std::string id_code = GetNbitRandomString(ID_CODE_LENGTH);
    
    UserInfoAccount account_user_info;
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(username, account_user_info))
    {
       dbinterface::TwoFactorAuthIDCode::InsertTwoFactorAuthIDCodeByAccountUserInfoUUID(account_user_info.uuid, id_code);
       AK_LOG_INFO << "record idcode success, account_user_info_uuid: " << account_user_info.uuid << " id_code: " << id_code ;
    }
    
    //不同类型app类型处理
    const char* user_agent = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(user_agent, main_account);
 
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);
   
    //web、rest addr; APP未激活也要返回
    std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret )
    {

        ret = ERR_SUCCESS;
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(TWO_FACTOR_AUTH_IDCODE, id_code));

        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //审计日志
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        HttpRespKV kv;
        //默认不需要重定向
        RedirectCloudType redirect_ret = RedirectCloudType::REDIRECT_NO_NEED;
        GenerateServerInfo(redirect_ret, personal_account_info, user_agent, api_version, token_renew_info.token, kv);
        UpdateRefreshTokenToRedirectServer(personal_account_info.uid, token_renew_info.refresh_token, redirect_ret);
        
        AK_LOG_INFO << "login, get server list";

        //组装返回消息体
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(TWO_FACTOR_AUTH_IDCODE, id_code));
            
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;    
};

csgate::HTTPRespVerCallbackMap HTTPPmTwoFactorAuthMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V71] = ReqPmVerifyCodeAndLoginHandler;
    return OMap;
}

void HTTPPmTwoFactorAuthMapInit(csgate::HTTPAllRespCallbackMap &OMap)
{
    //7.1
    OMap[csgate::PM_VERIFY_CODE] = HTTPPmTwoFactorAuthMap();
}


}


