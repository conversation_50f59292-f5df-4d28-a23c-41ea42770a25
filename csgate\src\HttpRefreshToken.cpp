
#include "AkLogging.h"
#include "HttpResp.h"
#include "HttpRefreshToken.h"
#include "EndUserAppAuthChecker.h"
#include "PmAppAuthChecker.h"
#include "InsAppAuthChecker.h"
#include "util_string.h"
#include "util.h"
#include "util_judge.h"


namespace csgate
{


int GetAuthInfoFromRequestBody(const std::string& req_body, AuthInfo& auth_info)
{
    Json::Reader reader;
    Json::Value root;

    if (!reader.parse(req_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << req_body;
        return -1;    
    }

    //兼容旧App升级后第一次请求没有refresh token的情况
    if (!root.isMember("refresh_token"))
    {
        //手机号验证登录     
        if (root.isMember("auth_token") && root["auth_token"].asString().size() > 0)
        {
            auth_info.auth_type = AuthType::CheckAuthToken;
            Snprintf(auth_info.auth_token, sizeof(auth_info.auth_token), root["auth_token"].asString().c_str());
            
        }
        //账号密码登录
        else if (root.isMember("user") && root.isMember("passwd"))
        {
            auth_info.auth_type = AuthType::CheckUserPassword;
            Snprintf(auth_info.user, sizeof(auth_info.user), root["user"].asString().c_str());
            Snprintf(auth_info.passwd, sizeof(auth_info.passwd), root["passwd"].asString().c_str());
        }        
        else
        {
            AK_LOG_WARN << "http report param wrong. body:" << req_body; 
        }
    }
    else
    {
        auth_info.auth_type = AuthType::CheckRefreshToken;
        Snprintf(auth_info.refresh_token, sizeof(auth_info.refresh_token), root["refresh_token"].asString().c_str());
    }

    return 0;
}

void RespRefreshToken(int ret, TokenRenewInfo &token_renew_info, const evpp::http::HTTPSendResponseCallback& cb)
{
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
    kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));

    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));    
}


HTTPRespCallback ReqRefreshTokenHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string token = GetCtxHeader(ctx, "x-auth-token");

    AuthInfo auth_info;
    int ret = GetAuthInfoFromRequestBody(ctx->body().ToString(), auth_info);
    if (ret != 0)
    {
        cb("parse http body json error");
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    TokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 != dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        AK_LOG_WARN << "Token not exist, return refreshtoken err. relogin. token:" <<token; //需要重新登录
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }

    ResidentPerAccount personal_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUserAccount(token_info.account, personal_account)
        || personal_account.is_expire
        || !akjudge::IsEndUserRole(personal_account.role))
    {
        AK_LOG_WARN << "Check PersonalAccount invalid failed. account=" << token_info.account;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR)); //需要重新登录
        return;
    }

    EndUserAppAuthChecker auth_checker(token_info, auth_info);
    ret = auth_checker.HandleAuthCheck();
    if (ret != ERR_SUCCESS)
    {
        AK_LOG_WARN << "Check token invalid. auth type:" << auth_info.auth_type;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }
    
    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(token_info.account, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(token_info.account, token_info.app_main_account, token_renew_info);

    RedirectCloudType redirect_type = CheckUserRedirectByAccount(token_info.account);
    if(redirect_type)
    {
        csgate::UpdateTokenToRedirectServer(token_info.account, token_renew_info.token, "", redirect_type);
        csgate::UpdateRefreshTokenToRedirectServer(token_info.account, token_renew_info.refresh_token, redirect_type);
    }

    RespRefreshToken(ret, token_renew_info, cb);

    return;
};

HTTPRespCallback ReqPmRefreshTokenHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string token = GetCtxHeader(ctx, "x-auth-token");

    AuthInfo auth_info;
    int ret = GetAuthInfoFromRequestBody(ctx->body().ToString(), auth_info);
   
    if (ret != 0)
    {
        cb("parse http body json error");
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    TokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 != dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        AK_LOG_WARN << "Token not exist, return refreshtoken err. relogin."; //需要重新登录
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }

    ResidentPerAccount personal_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUserAccount(token_info.account, personal_account)
        || personal_account.is_expire
        || personal_account.role != ACCOUNT_ROLE_COMMUNITY_PM)
    {
        AK_LOG_WARN << "Check PersonalAccount invalid failed. account=" << token_info.account;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR)); //需要重新登录
        return;
    }

    PmAppAuthChecker auth_checker(token_info, auth_info);
    ret = auth_checker.HandleAuthCheck();
    if (ret != ERR_SUCCESS)
    {
        AK_LOG_WARN << "Check token invalid. auth type:" << auth_info.auth_type;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }
    
    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(token_info.account, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(token_info.account, token_info.app_main_account, token_renew_info);

    RedirectCloudType redirect_type = CheckUserRedirectByAccount(token_info.account);
    if(redirect_type)
    {
        csgate::UpdateTokenToRedirectServer(token_info.account, token_renew_info.token, "", redirect_type);
        csgate::UpdateRefreshTokenToRedirectServer(token_info.account, token_renew_info.refresh_token, redirect_type);
    }

    RespRefreshToken(ret, token_renew_info, cb);
    return;
};


csgate::HTTPRespCallback ReqInstallerRefreshTokenHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string token = GetCtxHeader(ctx, "x-auth-token");
    AuthInfo auth_info;
    int ret = GetAuthInfoFromRequestBody(ctx->body().ToString(), auth_info);
    if (ret != 0)
    {
        cb("parse http body json error");
        return;
    }

    InsTokenInfo token_info;
    if (0 != dbinterface::InsToken::GetInsTokenInfoByAppToken(token, token_info))
    {
        AK_LOG_WARN << "Token not exist, return refreshtoken err. relogin."; //需要重新登录
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }

    UserInfoAccount ins_account_info;
    memset(&ins_account_info, 0, sizeof(ins_account_info));
    if (0 != dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(token_info.userinfo_uuid, ins_account_info))
    {
        AK_LOG_WARN << "installer account user info not found. token:" << token;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }

    InsAppAuthChecker auth_checker(token_info, auth_info);
    ret = auth_checker.HandleAuthCheck();
    if (ret != ERR_SUCCESS)
    {
        AK_LOG_WARN << "Check token invalid. auth type:" << auth_info.auth_type;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }

    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(ins_account_info.account, token_renew_info);
    csgate::UpdateInsTokenRenewInfo(ins_account_info.uuid, token_renew_info);
    
    RespRefreshToken(ret, token_renew_info, cb);
    return;
};


HTTPRespVerCallbackMap HttpReqRefreshTokenMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V68] = ReqRefreshTokenHandler;
    return OMap;
}

HTTPRespVerCallbackMap HttpReqPmRefreshTokenMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V68] = ReqPmRefreshTokenHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqInstallerRefreshTokenMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V68] = ReqInstallerRefreshTokenHandler;
    return OMap;
}

void HTTPRefreshTokenRespMapInit(csgate::HTTPAllRespCallbackMap &OMap)
{
    //v6.8
    OMap[csgate::REF_TOKEN] = HttpReqRefreshTokenMap();
    OMap[csgate::INS_REF_TOKEN] = HTTPReqInstallerRefreshTokenMap();
    OMap[csgate::PM_REF_TOKEN] = HttpReqPmRefreshTokenMap();
}




}

