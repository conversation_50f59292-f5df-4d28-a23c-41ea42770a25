#ifndef __CSGATE_HTTP_REFRESHTOKEN_H__
#define __CSGATE_HTTP_REFRESHTOKEN_H__

#include <string>
#include <functional>
#include <evpp/http/context.h>
#include "Dao.h"
#include "dbinterface/AwsRedirect.h"
#include "json/json.h"

namespace csgate
{

int GetAuthInfoFromRequestBody(const std::string& req_body, AuthInfo& auth_info);
void RespRefreshToken(int ret, TokenRenewInfo &token_renew_info, const evpp::http::HTTPSendResponseCallback& cb);
void HTTPRefreshTokenRespMapInit(csgate::HTTPAllRespCallbackMap &OMap);

}
#endif //__CSGATE_HTTP_REFRESHTOKEN_H__