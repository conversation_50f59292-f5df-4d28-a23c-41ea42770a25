#include <iostream>
#include <functional>
#include "HttpResp.h"
#include "Dao.h"
#include "ClientMng.h"
#include "ServerMng.h"
#include "CsgateConf.h"
#include "Md5.h"
#include "LogicSrvMng.h"
#include "AkLogging.h"
#include "AES256.h"
#include "json/json.h"
#include "Url.h"
#include "AuditLog.h"
#include "Caesar.h"
#include "util.h"
#include "HttpMsgControl.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "HttpOfficeResp.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket_interface.h"
#include "evpp/rate_limiter/rate_limiter.h"
#include "DevSrvMapMng.h"
#include "AkcsHttpRequest.h"
#include "HttpSmartResp.h"
#include "dbinterface/TmpLoginJpFromScloudLog.h"
#include "HttpRespIns.h"
#include "dbinterface/Token.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "EndUserAppAuthChecker.h"
#include "PmAppAuthChecker.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/ApiUsageInfo.h"
#include "AppTwoFactorAuth.h"
#include "dbinterface/TwoFactorAuthIDCode.h"
#include "dbinterface/PmAccountMap.h"
#include "HttpRespDevice.h"
#include "HttpRespIns.h"
#include "KafkaNotifyHandler.h"

extern CSGATE_CONF gstCSGATEConf; //全局配置信息
extern evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter;
extern AWS_CSGATE_CONF gstAWSConf; //全局配置信息
extern const unsigned int g_ipaddr_check_len; 
extern AWS_CSGATE_CONF gstAWSAucloudConf;
extern AWS_CSGATE_CONF gstASBJConf;
extern AWS_CSGATE_CONF gstEcloud2UcloudConf;



const std::string g_aes_key_head = "akuvox1jp2server";
const std::string http_head = "https://"; //6.7后续新增接口返回http头


namespace csgate
{

std::string GetRedirectCloudWordKey(RedirectCloudType redirect_type)
{
    switch (redirect_type)
    {
        case RedirectCloudType::REDIRECT_JCLOUD:
            return REDIRECT_JCLOUD_WORD_KEY;
        case RedirectCloudType::REDIRECT_AUCLOUD:
            return REDIRECT_AUCLOUD_WORD_KEY;
        case RedirectCloudType::REDIRECT_ASBJ:
            return REDIRECT_ASBJ_WORD_KEY;
        case RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD:
            return REDIRECT_ECLOUD_TO_UCLOUD_WORD_KEY;
        default:
            return ""; // 返回空字符串表示无效的类型
    }
}

std::string GetRedirectUpdateHeader(RedirectCloudType redirect_type)
{
    std::string update_auth_http_head;
    if(redirect_type == RedirectCloudType::REDIRECT_JCLOUD)
    {
        update_auth_http_head = gstCSGATEConf.update_jp_auth_http_head;
    }
    else if(redirect_type == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        update_auth_http_head = gstCSGATEConf.update_au_auth_http_head;
    }
    else if(redirect_type == RedirectCloudType::REDIRECT_ASBJ)
    {
        update_auth_http_head = gstCSGATEConf.update_asbj_auth_http_head;
    }
    else if(redirect_type == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)
    {
        update_auth_http_head = gstCSGATEConf.update_e2ucloud_auth_http_head;
    }    

    return std::move(update_auth_http_head);
}

void InsertNewGateServer(RedirectCloudType redirect, HttpRespKV &kv, const std::string & user_agent)
{
    if (strlen(gstCSGATEConf.new_gate_addr) > g_ipaddr_check_len || strlen(gstCSGATEConf.new_gate_ipv6_addr) > g_ipaddr_check_len)
    {
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER, gstCSGATEConf.new_gate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_IPV6, gstCSGATEConf.new_gate_ipv6_addr));
    }
    
    if (redirect == RedirectCloudType::REDIRECT_JCLOUD
        && ServerArea::scloud == gstCSGATEConf.server_area
        && (strlen(gstCSGATEConf.jp_new_gate_addr) > g_ipaddr_check_len || strlen(gstCSGATEConf.jp_new_gate_ipv6_addr) > g_ipaddr_check_len))
    {
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER, gstCSGATEConf.jp_new_gate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_IPV6, gstCSGATEConf.jp_new_gate_ipv6_addr));
    }
    else if (redirect == RedirectCloudType::REDIRECT_AUCLOUD
        && ServerArea::scloud == gstCSGATEConf.server_area
        && (strlen(gstCSGATEConf.au_new_gate_addr) > g_ipaddr_check_len || strlen(gstCSGATEConf.au_new_gate_ipv6_addr) > g_ipaddr_check_len))
    {
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER, gstCSGATEConf.au_new_gate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_IPV6, gstCSGATEConf.au_new_gate_ipv6_addr));
    }
    else if (redirect == RedirectCloudType::REDIRECT_ASBJ
        && user_agent == "ASBJ"
        && ServerArea::ecloud == gstCSGATEConf.server_area
        && (strlen(gstCSGATEConf.asbj_new_gate_addr) > g_ipaddr_check_len || strlen(gstCSGATEConf.asbj_new_gate_ipv6_addr) > g_ipaddr_check_len))
    {
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER, gstCSGATEConf.asbj_new_gate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_IPV6, gstCSGATEConf.asbj_new_gate_ipv6_addr));
    }        
    else if (redirect == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD
        && ServerArea::ecloud == gstCSGATEConf.server_area
        && (strlen(gstCSGATEConf.e2ucloud_new_gate_addr) > g_ipaddr_check_len || strlen(gstCSGATEConf.e2ucloud_new_gate_ipv6_addr) > g_ipaddr_check_len))
    {
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER, gstCSGATEConf.e2ucloud_new_gate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_IPV6, gstCSGATEConf.e2ucloud_new_gate_ipv6_addr));
    }         

}


std::string GetCtxHeader(const evpp::http::ContextPtr& ctx, const char *head)
{
    const char* xhead = ctx->FindRequestHeader(head);
    std::string str_head;
    if (xhead)
    {
        str_head = xhead;
    }
    return std::move(str_head);
}


//TODO:目前rtmp地址和rtsp同一个，后期需要分时候在注册到etcd
void RtspAddr2RtmpAddr(const std::pair<std::string, std::string>& rtsp, std::pair<std::string, std::string>& rtmp)
{
    int rtmp_port = 553;//写死
    std::string ipv4 = rtsp.first;
    std::size_t found = ipv4.find_first_of(':');
    if (found == std::string::npos)
    {
        AK_LOG_WARN << "invalid rtsp ip info " << ipv4;
        return;
    }
    std::string host = ipv4.substr(0, found);
    std::string port = ipv4.substr(found + 1);

    rtmp.first = host;
    rtmp.first += ":";
    rtmp.first += std::to_string(rtmp_port);


    std::string ipv6 = rtsp.second;
    found = ipv6.find_last_of(':');
    if (found == std::string::npos)
    {
        AK_LOG_WARN << "invalid rtsp ipv6 info " << ipv6;
        return;
    }
    std::string hostv6 = ipv6.substr(0, found);

    rtmp.second = hostv6;
    rtmp.second += ":";
    rtmp.second += std::to_string(rtmp_port);
}

void ChangeRtspAddr(int is_ios, std::string &ipv4, std::string &ipv6)
{
    if (ServerArea::ucloud != gstCSGATEConf.server_area && ServerArea::ecloud != gstCSGATEConf.server_area)
    {
        return;
    }

    if (ServerArea::ucloud == gstCSGATEConf.server_area && !is_ios)
    {
        return;
    }
    
    int ios_rtsp_port = RTSP_SERVER_PORT2;
    std::size_t found = ipv4.find_first_of(':');
    if (found == std::string::npos)
    {
        AK_LOG_WARN << "invalid rtsp ip info " << ipv4;
        return;
    }
    std::string host = ipv4.substr(0, found);
    std::string port = ipv4.substr(found + 1);

    std::string tmp_ipv4;
    tmp_ipv4 = host;
    tmp_ipv4 += ":";
    tmp_ipv4 += std::to_string(ios_rtsp_port);

    found = ipv6.find_last_of(':');
    if (found == std::string::npos)
    {
        AK_LOG_WARN << "invalid rtsp ipv6 info " << ipv6;
        return;
    }
    std::string hostv6 = ipv6.substr(0, found);

    std::string tmp_ipv6;
    tmp_ipv6 = hostv6;
    tmp_ipv6 += ":";
    tmp_ipv6 += std::to_string(ios_rtsp_port);

    ipv4 = tmp_ipv4;
    ipv6 = tmp_ipv6;
}

void GetTlsAddress(const std::pair<std::string, std::string>& pair_addr, unsigned short port, std::string& tls_domain_addr, std::string& tls_ipv6_addr)
{
    auto ProcessTlsAddr = [&](const std::string& address) -> std::string
    {
        std::size_t found = address.find_last_of(':');
        if (found == std::string::npos)
        {
            AK_LOG_WARN << "Invalid addr info " << address;
            return "";
        }

        std::string host = address.substr(0, found);
        return host + ":" + std::to_string(port);
    };

    tls_domain_addr = ProcessTlsAddr(pair_addr.first);
    tls_ipv6_addr = ProcessTlsAddr(pair_addr.second);
}

//login调用更新
void UpdateTokenToRedirectServer(const std::string &account, const std::string &token, const std::string &auth_token, RedirectCloudType redirect_type)
{
    if(token.size() == 0)
    {
        return;
    }
    
    std::string update_auth_http_head = GetRedirectUpdateHeader(redirect_type);
    if(update_auth_http_head.size() == 0)
    {
        return;
    }
        
    Json::Value item;
    Json::FastWriter w;
    item["account"] = account;
    item["token"] = token;
    item["auth_token"] = auth_token;
    item["type"] = TOKEN_UPDATE;
    std::string msg_json = w.write(item);

    std::string encrypt_resp;
    AESEncryptRespone(msg_json, g_aes_key_head, encrypt_resp);

    std::string respone;
    if (model::HttpRequest::GetInstance().Post(update_auth_http_head, encrypt_resp, respone, HTTP_DATA_TYPE_JSON) != 0)
    {
        AK_LOG_WARN << "update token to redirect server error." << " account=" << account << " respone=" << respone;
        return;
    }
    AK_LOG_INFO << "update token to redirect server" << " account=" << account << " respone=" << respone;    
}

//serverlist调用续时
void RedirectServerTokenRefresh(const std::string &account, const std::string& main_account, RedirectCloudType redirect_type)
{
    std::string update_auth_http_head = GetRedirectUpdateHeader(redirect_type);
    if(update_auth_http_head.size() == 0)
    {
        return;
    }

    Json::Value item;
    Json::FastWriter w;
    item["account"] = account;
    item["type"] = TOKEN_CONTINUE;
    std::string msg_json = w.write(item);

    std::string encrypt_resp;
    AESEncryptRespone(msg_json, g_aes_key_head, encrypt_resp);
   
    std::string respone;
    if (model::HttpRequest::GetInstance().Post(update_auth_http_head, encrypt_resp, respone, HTTP_DATA_TYPE_JSON) != 0)
    {
        AK_LOG_WARN << "refresh token to " << update_auth_http_head << " error." << " account=" << account << " respone=" << respone;
        return;
    }
    AK_LOG_INFO << "refresh token to " << update_auth_http_head <<  " account=" << account << " respone=" << respone;
    dbinterface::TmpLoginJpFromScloudLog::InsertLog(account);
}

void UpdateRefreshTokenToRedirectServer(const std::string &account, const std::string &refresh_token, RedirectCloudType redirect_type)
{    
    std::string update_auth_http_head = GetRedirectUpdateHeader(redirect_type);
    if(update_auth_http_head.size() == 0)
    {
        return;
    }
        
    Json::Value item;
    Json::FastWriter w;
    item["account"] = account;
    item["refresh_token"] = refresh_token;
    item["type"] = TOKEN_REFRESH;
    std::string msg_json = w.write(item);

    std::string encrypt_resp;
    AESEncryptRespone(msg_json, g_aes_key_head, encrypt_resp);

    std::string respone;
    if (model::HttpRequest::GetInstance().Post(update_auth_http_head, encrypt_resp, respone, HTTP_DATA_TYPE_JSON) != 0)
    {
        AK_LOG_WARN << "update refresh token to redirect server error." << " account=" << account << " respone=" << respone;
        return;
    }
    AK_LOG_INFO << "update refresh token to redirect server" << " account=" << account << " respone=" << respone;    
}


void HandleUpdateInnerAuth(const std::string &resp)
{
    std::string data_blank;
    AESDecryptRequest(resp, g_aes_key_head, data_blank);

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(data_blank, root))
    {
        AK_LOG_WARN << "HandleUpdateInnerAuth respone error," << " data:" << resp;
        return;
    }
    
    std::string account = root["account"].asString();
    std::string token = root["token"].asString();
    std::string auth_token = root["auth_token"].asString();    
    std::string refresh_token = root["refresh_token"].asString();
    int type = root["type"].asInt();

    DaoInnerUpdateToken(account, account, token, auth_token, refresh_token, type);
    
    if(ServerArea::asbj == gstCSGATEConf.server_area)
    {
        //同步到阿塞拜疆的都是user agent为ASBJ的,因此APP_OEM也要更新,不然推送会异常
        csgate::DaoUpdateAppSmartType(account, csgate::APP_TYPE_ASBJ);    
    }
}


enum PhoneType
{
    PHONE_TYPE_IOS = 0,
    PHONE_TYPE_EMUI = 1,
    PHONE_TYPE_FLYME = 2,
    PHONE_TYPE_OPPO = 3,
    PHONE_TYPE_VIVO = 4,
    PHONE_TYPE_MIUI = 5,
    PHONE_TYPE_GOOGLE = 6,
    PHONE_TYPE_TENCENT = 7,
    PHONE_TYPE_OTHER = 8,
};

/**
 * 不同平台的最新版本号用-分隔,比如60010-60011-60012-60013-60014-60015
 * 其中60010,个位数0表示为平台IOS;6001表示为IOS的最新版本号
 * 60011,个位数1表示为平台EMUI;6001表示为EMUI的最新版本号
 *
 * <AUTHOR> (2021/6/7)
 *
 * @param phone_type
 * @param app_latest_version
 *
 * @return int
 */
int GetAppLatestVersion(const char *phone_type, int &app_latest_version)
{
    std::map<std::string, int> phone_type_map;
    phone_type_map["sys_ios"] = PHONE_TYPE_IOS;
    phone_type_map["sys_emui"] = PHONE_TYPE_EMUI;
    phone_type_map["sys_flyme"] = PHONE_TYPE_FLYME;
    phone_type_map["sys_oppo"] = PHONE_TYPE_OPPO;
    phone_type_map["sys_vivo"] = PHONE_TYPE_VIVO;
    phone_type_map["sys_miui"] = PHONE_TYPE_MIUI;
    phone_type_map["sys_google"] = PHONE_TYPE_GOOGLE;
    phone_type_map["sys_tencent"] = PHONE_TYPE_TENCENT;
    phone_type_map["sys_other"] = PHONE_TYPE_OTHER;

    if (phone_type_map.count(phone_type) == 0)
    {
        AK_LOG_WARN << "phone_type=" << phone_type << " is invalid.";
        return -1;
    }
    int app_phone_type = phone_type_map[phone_type];


    CStrExplode str_explode(gstCSGATEConf.app_latest_version, '-');
    for (uint32_t i = 0; i < str_explode.GetItemCnt(); i++)
    {
        int value = ATOI(str_explode.GetItem(i));
        int app_version = value / 10;
        int app_type = value % 10;
        if (app_type == app_phone_type)
        {
            app_latest_version = app_version;
            return 0;
        }
    }

    AK_LOG_WARN << "phone_type=" << phone_type << " not define app latest version." << ";conf=" << gstCSGATEConf.app_latest_version;
    return -1;
}


csgate::HTTPRespCallback ReqEchoHandlerV30 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    cb(ctx->body().ToString());
};

int AESDecryptRequest(const std::string& requrest, const std::string& key_head, std::string& out)
{
    char data_blank[2048] = "";
    std::string key = key_head;
    key += DEFAULT_CSGATE_KEY_MASK;
    char iv[20] = "";
    GetAppGateIV(iv);
    AES256Base64Decrypt(key.c_str(), iv, requrest.c_str(), requrest.length(), data_blank, sizeof(data_blank));
    out = data_blank;
    return 0;

}


int AESEncryptRespone(const std::string& src, const std::string& key_head, std::string& out)
{
    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << src;
    }
    char data_blank[2048] = "";
    std::string key = key_head;
    key += DEFAULT_CSGATE_KEY_MASK;
    char iv[20] = "";
    GetAppGateIV(iv);
    AES256Base64Encrypt(key.c_str(), iv, src.c_str(), src.length(), data_blank, sizeof(data_blank));
    out = data_blank;
    return 0;
}

int GetInvalidTokenRetStr(std::string& str)
{
    std::stringstream oss;
    oss << "{" <<  "\n"
        << RESULT << ": -1," << "\n"
        << MESSAGE << ": " << TOKEN_INVALID << "\n"
        << "}" << "\n";
    str = oss.str();
    return 0;
}

void GetPbxServerForApp(const PersonalAccountInfo& personal_account_info, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{   
    switch (personal_account_info.role)
    {
        case ACCOUNT_ROLE_COMMUNITY_MAIN:
        case ACCOUNT_ROLE_OFFICE_MAIN:
        case ACCOUNT_ROLE_OFFICE_ADMIN:
        case ACCOUNT_ROLE_OFFICE_NEW_PER:
            csgate::DaoGetCommPbxServer(personal_account_info.parent_id, pbx_ip, pbx_ipv6, pbx_domain);
            break;
        case ACCOUNT_ROLE_COMMUNITY_ATTENDANT:
            {
                PersonalAccountInfo main_account_info;
                int acc_ret = csgate::DaoGetPersonalAccountInfo(personal_account_info.parent_id, main_account_info);
                if (0 == acc_ret)
                {
                    csgate::DaoGetCommPbxServer(main_account_info.parent_id, pbx_ip, pbx_ipv6, pbx_domain);
                }
            }
            break;
        case ACCOUNT_ROLE_PERSONNAL_MAIN:
            csgate::DaoGetPerPbxServer(personal_account_info.account, pbx_ip, pbx_ipv6, pbx_domain);
            break;
        case ACCOUNT_ROLE_PERSONNAL_ATTENDANT:
            {
                PersonalAccountInfo main_account_info;
                int acc_ret = csgate::DaoGetPersonalAccountInfo(personal_account_info.parent_id, main_account_info);
                if (0 == acc_ret)
                {
                    csgate::DaoGetPerPbxServer(main_account_info.account, pbx_ip, pbx_ipv6, pbx_domain);
                }
            }            
            break;
        default :
            
            break;
    }
    return;
    }

void GetPbxServerForDevice(const std::string& mac, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{    
    int community_id = -1;
    int ret = csgate::DaoGetCommunityAccountId(mac, community_id);
    if (ret == 0)
    {        
        csgate::DaoGetCommPbxServer(community_id, pbx_ip, pbx_ipv6, pbx_domain);
    }
    else
    {
        std::string node;
        ret = csgate::DaoGetPersonalDevNode(mac, node);
        csgate::DaoGetPerPbxServer(node, pbx_ip, pbx_ipv6, pbx_domain);
    }
    return;
}

void GetOpsServer(const std::string& uid, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{
    std::pair<std::string, std::string> pair_ops_ip46 = CLogicSrvMng::Instance()->GetOpsSrv(uid);
    std::pair<std::string, std::string> pair_ops_domain46 = CLogicSrvMng::Instance()->GetOpsDomainSrv(uid); 
    if(pair_ops_ip46.first.size() > 0)
    {
        pbx_ip = pair_ops_ip46.first;
    }
    if(pair_ops_ip46.second.size() > 8)//[]:5070 modify by chenzhx 这种情况应该返回空,让上层直接找指定配置
    {
        pbx_ipv6 = pair_ops_ip46.second;
    }
    if(pair_ops_domain46.first.size() > 0)
    {
        pbx_domain = pair_ops_domain46.first;
    }
}

void GetWebServer(std::string& web_addr, std::string& rest_addr, std::string& web_ipv6, std::string& rest_ipv6, std::string& rest_ssl_addr, std::string& rest_ssl_ipv6 )
{
    web_ipv6=gstCSGATEConf.web_ipv6;
    rest_ipv6 = gstCSGATEConf.rest_ipv6;
    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    
    rest_addr = gstCSGATEConf.rest_addr;       
    rest_ipv6 = gstCSGATEConf.rest_ipv6;
    rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
    rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;
}

void GetWebServer(std::string& web_addr, std::string& rest_addr, std::string& web_ipv6, std::string& rest_ipv6, std::string& rest_ssl_addr, std::string& rest_ssl_ipv6 , std::string& web_backend_server )
{
    web_ipv6=gstCSGATEConf.web_ipv6;
    rest_ipv6 = gstCSGATEConf.rest_ipv6;
    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    
    rest_addr = gstCSGATEConf.rest_addr;       
    rest_ipv6 = gstCSGATEConf.rest_ipv6;
    rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
    rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;
    web_backend_server = gstCSGATEConf.web_backend_domain;
}

void UpdateAppSmartType(const char* agent, const std::string& uid)
{
    if (uid.length() > 0)
    {
        if(nullptr != agent)
        {
            std::string user_agent = agent;
            if(user_agent == "mysmart")
            {
                csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_MYSMART);
            }
            else if (user_agent == "fasttel")
            {
                csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_FASTTEL);
            }
            else if (user_agent == "belahome")
            {
                csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_BELAHOME);
            }    
            else if (user_agent == "ASBJ")
            {
                csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_ASBJ);
            }        
            else if (user_agent == "baksmartplus")
            {
                csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_BAKSMARTPLUS);
            }
            else
            {
                csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_COMMON);
            }
        }
        else
        {
            csgate::DaoUpdateAppSmartType(uid, csgate::APP_TYPE_COMMON);
        }
    }
}
void GenerateServerInfo(RedirectCloudType redirect_type, PersonalAccountInfo& personal_account_info, const std::string& user_agent, float ver, const std::string& redirect_update_token, HttpRespKV& kv)
{
    // 判断用户是否需要重定向到迁移服务器
    if (redirect_type == RedirectCloudType::REDIRECT_UNINIT)
    {
        redirect_type = csgate::DaoCheckUserRedirect(personal_account_info);
    }

    std::string main_account = personal_account_info.main_account;
    std::string uid = personal_account_info.uid;

    //pbx addr
    std::string pbx_ip;
    std::string pbx_ipv6;
    std::string pbx_domain;
    csgate::GetOpsServer(main_account, pbx_ip, pbx_ipv6, pbx_domain);

    //根据负载均衡算法获取到系统的接入服务器和流媒体服务器
    std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(main_account);
    std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(main_account);
    std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(main_account);
    std::pair<std::string, std::string> pair_rtmp_ser46;

    std::string csmain_domain =  pair_access_ser46.first;
    std::string csmain_ipv6 = pair_access_ser46.second;
    std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
    std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;

    std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);

    // 获取rtmp地址
    csgate::RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);
    std::string rtmp_addr = pair_rtmp_ser46.first;
    std::string rtmp_ipv6 = pair_rtmp_ser46.second;

    int is_ios = 0;
    if (user_agent.size() != 0 && strcasestr(user_agent.c_str(), "ios"))
    {
        is_ios = 1;
    }

    //调度 -- 如服务器迁移等情况 
    if(redirect_type == RedirectCloudType::REDIRECT_JCLOUD)
    {
        csmain_domain =  gstAWSConf.csmain_domain;
        csmain_ipv6 = gstAWSConf.csmain_ipv6;
        csvrtsp_domain = gstAWSConf.csvrtsp_domain;
        csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
        pbx_domain = gstAWSConf.pbx_domain;
        pbx_ipv6 = gstAWSConf.pbx_ipv6;
        web_addr = gstAWSConf.web_domain;
        web_backend_sever = gstAWSConf.web_backend_domain;
        web_ipv6 = gstAWSConf.web_ipv6;
        rest_addr = gstAWSConf.rest_addr;
        rest_ipv6 = gstAWSConf.rest_ipv6;
        rest_ssl_addr = gstAWSConf.rest_ssl_addr;
        rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

        csgate::UpdateTokenToRedirectServer(uid, redirect_update_token, "", redirect_type);               
    }        
    else if(redirect_type == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        csmain_domain =  gstAWSAucloudConf.csmain_domain;
        csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
        csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
        csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
        pbx_domain = gstAWSAucloudConf.pbx_domain;
        pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
        web_addr = gstAWSAucloudConf.web_domain;
        web_backend_sever = gstAWSAucloudConf.web_backend_domain;
        web_ipv6 = gstAWSAucloudConf.web_ipv6;
        rest_addr = gstAWSAucloudConf.rest_addr;
        rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
        rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
        rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
        csgate::UpdateTokenToRedirectServer(uid, redirect_update_token, "", redirect_type);
    }
    else if(redirect_type == RedirectCloudType::REDIRECT_ASBJ && user_agent == "ASBJ")
    {
        csmain_domain =  gstASBJConf.csmain_domain;
        csmain_ipv6 = gstASBJConf.csmain_ipv6;
        csvrtsp_domain = gstASBJConf.csvrtsp_domain;
        csvrtsp_ipv6 = gstASBJConf.csvrtsp_ipv6;
        pbx_domain = gstASBJConf.pbx_domain;
        pbx_ipv6 = gstASBJConf.pbx_ipv6;
        web_addr = gstASBJConf.web_domain;
        web_backend_sever = gstASBJConf.web_backend_domain;
        web_ipv6 = gstASBJConf.web_ipv6;
        rest_addr = gstASBJConf.rest_addr;
        rest_ipv6 = gstASBJConf.rest_ipv6;
        rest_ssl_addr = gstASBJConf.rest_ssl_addr;
        rest_ssl_ipv6 = gstASBJConf.rest_ssl_ipv6;
        csgate::UpdateTokenToRedirectServer(uid, redirect_update_token, "", redirect_type);
    }
    else if(redirect_type == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)
    {
        csmain_domain =  gstEcloud2UcloudConf.csmain_domain;
        csmain_ipv6 = gstEcloud2UcloudConf.csmain_ipv6;
        csvrtsp_domain = gstEcloud2UcloudConf.csvrtsp_domain;
        csvrtsp_ipv6 = gstEcloud2UcloudConf.csvrtsp_ipv6;
        pbx_domain = gstEcloud2UcloudConf.pbx_domain;
        pbx_ipv6 = gstEcloud2UcloudConf.pbx_ipv6;
        web_addr = gstEcloud2UcloudConf.web_domain;
        web_backend_sever = gstEcloud2UcloudConf.web_backend_domain;
        web_ipv6 = gstEcloud2UcloudConf.web_ipv6;
        rest_addr = gstEcloud2UcloudConf.rest_addr;
        rest_ipv6 = gstEcloud2UcloudConf.rest_ipv6;
        rest_ssl_addr = gstEcloud2UcloudConf.rest_ssl_addr;
        rest_ssl_ipv6 = gstEcloud2UcloudConf.rest_ssl_ipv6;
        csgate::UpdateTokenToRedirectServer(uid, redirect_update_token, "", redirect_type);
    }

    // 获取tls地址
    std::string pbxs_ipv6_addr;
    std::string pbxs_domain_addr;
    std::string csvrtsps_ipv6_addr;
    std::string csvrtsps_domain_addr;
    csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
    csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

    //ios rtsp服务器特殊处理
    ChangeRtspAddr(is_ios, csvrtsp_domain, csvrtsp_ipv6);

    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));
    kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr));   
    kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, rtmp_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, rtmp_ipv6));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr)); 

    InsertNewGateServer(redirect_type, kv, user_agent);

    AK_LOG_INFO << "user is " << uid << ",main_account is " << main_account << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;
}

csgate::HTTPRespCallback ReqUpdateInnerAuthHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* head = ctx->FindRequestHeader("authtype");
    if (head) //短信验证码 没有加密
    {
        Json::Reader reader;
        Json::Value root;  
        std::string http_body = ctx->body().ToString();
        if (!reader.parse(http_body, root))
        {
            AK_LOG_WARN << "ReqUpdateInnerAuthHandler respone error," << " data:" << http_body;
            return;
        }
        
        std::string account = root["account"].asString();
        std::string code = root["code"].asString();
        csgate::DaoUpdateSmsCode(account, code);
        cb("success");
        return;
    }
    //token 加密
    std::string http_body = ctx->body().ToString();
    csgate::HandleUpdateInnerAuth(http_body);
    cb("success");
    return;
};


//2018-04-19,v3.3,added by chenyc,增加接口支持ipv6,面向app. 每次调用login,app的token都会改变.设备还是v3.0接口
//modified by chenyc,2019-07-24,v4.4之前的版本不再在这里拦截过期的账号
csgate::HTTPRespCallback ReqLoginHandlerV33 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckUser(user, passwd_md5, 0.0, personal_account_info); 
    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqLoginHandlerV33");
    }
    
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
        
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, user:" << user << " error:" << ret;
        cb(buildErrorHttpMsg(ret));
    }
    else if ((csgate::ERR_APP_UNACTIVE == ret) || (csgate::ERR_APP_UNPAID == ret))
    {
        std::string token;
        csgate::GetToken(uid, main_account, token);

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, gstCSGATEConf.web_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));             
        cb(buildCommHttpMsg(ret, kv));
    }
    else if ((csgate::ERR_SUCCESS == ret) || (csgate::ERR_APP_EXPIRE == ret))
    {
        USER_CONF user_conf = {0};
        csgate::DaoGetUserConf(personal_account_info, user_conf);
        if (passwd_md5 != akuvox_encrypt::MD5("akuvox").toStr())
        {
            csgate::DaoUpdateAppLoginStatus(uid);
        }
        //added by chenyc, 2019-06-17,查询下是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
        int is_show_payment = 0;
        {
            //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
            #if 0
            if ((personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN))
            {
                is_show_payment = 1;
            }
            #endif
        }

        std::string token;
        csgate::GetToken(user, main_account, token);
        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);
        
        std::string webip = gstCSGATEConf.web_ip; 
        std::string webipv6 = gstCSGATEConf.web_ipv6;         
        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            ftp_ip = gstAWSConf.ftp_ip;
            ftp_ipv6 = gstAWSConf.ftp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            webip = gstAWSConf.web_domain;
            webipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;             
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            ftp_ip = gstAWSAucloudConf.ftp_ip;
            ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            webip = gstAWSAucloudConf.web_domain;
            webipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
        }
            

        AK_LOG_INFO << "login,uid is " << personal_account_info.uid << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip
                    << ", ftp_ip is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
                    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, webip));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(personal_account_info.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};


//added by chenyc,2019-07-25,v4.5版本的接口,v4.5版本之后的app需要拦截过期的账号
csgate::HTTPRespCallback ReqLoginHandlerV45 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    csgate::PersonalAccountInfo personal_account_info;
    std::string web_addr;
    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    ret = csgate::DaoCheckUser(user, passwd_md5, 0.0, personal_account_info);
    
    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqLoginHandlerV45");
    }
    std::string uid = personal_account_info.uid;
    
    
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, user:" << user << " error:" << ret;
        cb(buildErrorHttpMsg(ret));
    }
    else if (csgate::ERR_APP_EXPIRE == ret)
    {
        std::string token;
        csgate::GetToken(personal_account_info.uid, personal_account_info.main_account, token);
          
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(personal_account_info.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));             
        cb(buildCommHttpMsg(ret, kv));
    }
    else if ((csgate::ERR_APP_UNACTIVE == ret) || (csgate::ERR_APP_UNPAID == ret))
    {
        std::string token;
        csgate::GetToken(personal_account_info.uid, personal_account_info.main_account, token);
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));             
        cb(buildCommHttpMsg(ret, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        USER_CONF user_conf = {0};
        csgate::DaoGetUserConf(personal_account_info, user_conf);
        if (passwd_md5 != akuvox_encrypt::MD5("akuvox").toStr())
        {
            csgate::DaoUpdateAppLoginStatus(personal_account_info.uid);
        }
        //added by chenyc, 2019-06-17,查询下是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
        int is_show_payment = 0;
        {
            //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
            #if 0
            if ((personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN))
            {
                is_show_payment = 1;
            }
            #endif
        }

        std::string token;
        csgate::GetToken(user, personal_account_info.main_account, token);
        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);
        
        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            ftp_ip = gstAWSConf.ftp_ip;
            ftp_ipv6 = gstAWSConf.ftp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, "", redirect_ret);

        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            ftp_ip = gstAWSAucloudConf.ftp_ip;
            ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, "", redirect_ret);
        }
        AK_LOG_INFO << "login,uid is " << personal_account_info.uid << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip
                    << ", ftp_ip is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(user_conf.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};


/*4.6新增报文加密,GetToken接口加上版本参数,4.6之后的版本token有时效*/
csgate::HTTPRespCallback ReqLoginHandlerV46_61 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    
    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");  
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char users[64];
        snprintf(users, sizeof(users), "%s", user.c_str());
        akuvox_encrypt::CaesarDecry(users);
        user = users;
    }
    
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    std::string user_key = user;
    TrimString(user);
    csgate::PersonalAccountInfo personal_account_info;
    float ver = 4.6;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    ret = csgate::DaoCheckAppLogin(user, passwd_md5, ver, personal_account_info);
    //接口调用统计
     if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqLoginHandlerV46_61");
    }
    dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqLoginHandlerV46_61");
    std::string user_md5 = akuvox_encrypt::MD5(user_key).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string web_addr;

    const char* head_agent = ctx->FindRequestHeader("User-Agent");
    const char* head = ctx->FindRequestHeader("api-version");
    int is_ios = 0;
    float ios_ver = STOF(head);
    if (head_agent && strcasestr(head_agent, "ios"))
    {
        is_ios = 1;
    }

    std::string main_account = personal_account_info.main_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);

    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
    {
        web_addr += ":9443";
    }

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        std::string encrypt_resp;
        AK_LOG_WARN << "Check user error, user:" << user << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);
    //added by chenyc, 2019-06-17,查询下是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
    int is_show_payment = 0, is_show_subscription = 0;    
    {
        //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
        #if 0
        if (((personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN)) && 0 == gstCSGATEConf.is_china_version)
        {
            is_show_subscription = csgate::DaoGetAppPayMode(personal_account_info.uid) == 0 ? 1 : 0;
            is_show_payment = is_show_subscription;
        }
        #endif
    }        
    //modified by chenyc 2020.06.30,全量切换到主动注销的模式
    //app在后台时的sip动作模式,0:app退到后台后,sip不注销;1:sip主动注销,对应的是全部走端外推送的来电呼叫
    int android_app_push_mode = 1;    
    std::string token;
    csgate::GetToken(personal_account_info.uid, personal_account_info.main_account, token, ver);
    
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(personal_account_info.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          
        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }

    else if (csgate::ERR_SUCCESS == ret)
    {
        if (passwd_md5 != akuvox_encrypt::MD5("akuvox").toStr())
        {
            csgate::DaoUpdateAppLoginStatus(personal_account_info.uid);
        }
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), personal_account_info.uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", personal_account_info.uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);
        
        std::pair<std::string, std::string> pair_rtmp_ser46;
        RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;
        
        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            ftp_ip = gstAWSConf.ftp_ip;
            ftp_ipv6 = gstAWSConf.ftp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, "", redirect_ret);          
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            ftp_ip = gstAWSAucloudConf.ftp_ip;
            ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, "", redirect_ret);
        }
                
        AK_LOG_INFO << "login, uid is " << personal_account_info.uid << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip << ", pbx_ip is " << pbx_ip << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(user_conf.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_TEMPKEY, std::to_string(user_conf.is_show_tempkey))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqLoginHandlerV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");

    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char users[64];
        snprintf(users, sizeof(users), "%s", user.c_str());
        akuvox_encrypt::CaesarDecry(users);
        user = users;
    }

    int dev_type = dbinterface::ProjectUserManage::CheckUserType(user);

    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeLoginHandlerV63(ctx, cb);
        return;
    }
    
    std::string encrypt_resp;
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    std::string user_key = user;
    TrimString(user);
    csgate::PersonalAccountInfo personal_account_info;
    float ver = 4.6;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    ret = csgate::DaoCheckAppLogin(user, passwd_md5, ver, personal_account_info);
    std::string user_md5 = akuvox_encrypt::MD5(user_key).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string web_addr;

    const char* head_agent = ctx->FindRequestHeader("User-Agent");
    const char* head = ctx->FindRequestHeader("api-version");
    int is_ios = 0;
    float ios_ver = STOF(head);
    if (head_agent && strcasestr(head_agent, "ios"))
    {
        is_ios = 1;
    }

    //不同类型app类型处理
    std::string main_account = personal_account_info.main_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);

    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
    {
        web_addr += ":9443";
    }

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, user:" << user << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);
    //added by chenyc, 2019-06-17,查询下是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
    int is_show_payment = 0, is_show_subscription = 0;
    {
        //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
        #if 0
        if (((personal_account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN)) && 0 == gstCSGATEConf.is_china_version)
        {
            is_show_subscription = csgate::DaoGetAppPayMode(personal_account_info.uid) == 0 ? 1 : 0;
            is_show_payment = is_show_subscription;
        }
        #endif
    }
    //modified by chenyc 2020.06.30,全量切换到主动注销的模式
    //app在后台时的sip动作模式,0:app退到后台后,sip不注销;1:sip主动注销,对应的是全部走端外推送的来电呼叫
    int android_app_push_mode = 1;
    std::string token;
    csgate::GetToken(personal_account_info.uid, personal_account_info.main_account, token, ver);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(personal_account_info.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        if (passwd_md5 != akuvox_encrypt::MD5("akuvox").toStr())
        {
            csgate::DaoUpdateAppLoginStatus(personal_account_info.uid);
        }
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), personal_account_info.uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", personal_account_info.uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_domain = gstAWSConf.pbx_domain;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, "", redirect_ret);             
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_domain = gstAWSAucloudConf.pbx_domain;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, "", redirect_ret);
        }
        AK_LOG_INFO << "login, uid is " << personal_account_info.uid << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        //获取smarthomeid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(personal_account_info.uid, smarthome_uid);

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(personal_account_info.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_TEMPKEY, std::to_string(user_conf.is_show_tempkey))); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          
            
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqSmsLoginHandlerV55_61 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string area_code;
    std::string code = ctx->GetQuery("code");
    std::string phone = ctx->GetQuery("phone");
    
    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");  
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char user[64];
        snprintf(user, sizeof(user), "%s", phone.c_str());
        akuvox_encrypt::CaesarDecry(user);
        phone = user;
    }
    
    float ver = 5.5;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckPhone(phone, code, area_code, personal_account_info);    
    AK_LOG_INFO << "phone:" << phone << ",main_user:" << personal_account_info.main_account << ",user:" << personal_account_info.uid;    

     //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqSmsLoginHandlerV55_61");
    }
    
    std::string web_addr;
    std::string user_md5 = akuvox_encrypt::MD5(phone).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    int role = personal_account_info.role;

    //不同类型app类型处理
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid;

    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    
    std::string encrypt_resp;
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, phone:" << phone << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);
    
    //是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
    int is_show_payment = 0, is_show_subscription = 0;
    {
        //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
        #if 0
        if (((role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (role == ACCOUNT_ROLE_COMMUNITY_MAIN)) && 0 == gstCSGATEConf.is_china_version)
        {
            is_show_subscription = csgate::DaoGetAppPayMode(uid) == 0 ? 1 : 0;
            is_show_payment = is_show_subscription;
        }
        #endif
    }
    //app在后台时的sip动作模式,0:app退到后台后,sip不注销;1:sip主动注销,对应的是全部走端外推送的来电呼叫
    int android_app_push_mode = 1;
    std::string token;
    csgate::GetToken(uid, main_account, token, ver);
    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(role))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        csgate::DaoUpdateAppLoginStatus(uid);

        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(user_conf.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, user_conf.role, user_conf.mng_account_id);
        if (user_conf.role > 0)
        {
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token,auth_token, redirect_ret);             
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, auth_token, redirect_ret);
        }
        
        AK_LOG_INFO << "login,uid is " << uid << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip << ", rtmp is:" << pair_rtmp_ser46.first;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(user_conf.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_TEMPKEY, std::to_string(user_conf.is_show_tempkey))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqSmsLoginHandlerV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string phone = ctx->GetQuery("phone");
    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char user[64];
        snprintf(user, sizeof(user), "%s", phone.c_str());
        akuvox_encrypt::CaesarDecry(user);
        phone = user;
    }
    
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(phone);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeSmsLoginHandlerV63(ctx, cb);
        return;
    }

    std::string area_code;
    std::string code = ctx->GetQuery("code");
    float ver = 5.5;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    csgate::PersonalAccountInfo personal_account_info;
    
    ret = csgate::DaoCheckPhone(phone, code, area_code, personal_account_info);    

    std::string web_addr;
    std::string user_md5 = akuvox_encrypt::MD5(phone).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    int role = personal_account_info.role;

    AK_LOG_INFO << "phone:" << phone << ",main_account:" << main_account << ",user:" << uid;    

    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
 
    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid;

    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        std::string encrypt_resp;
        AK_LOG_WARN << "Check user error, phone:" << phone << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    //是否需要显示app侧边栏的payment,主账号显示,从账号统一不显示
    int is_show_payment = 0, is_show_subscription = 0;
    {
        //added by chenyc,2021-06-15,iOS收费问题临时解决方案,下发关闭收费的控制信令
        #if 0
        if (((role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (role == ACCOUNT_ROLE_COMMUNITY_MAIN)) && 0 == gstCSGATEConf.is_china_version)
        {
            is_show_subscription = csgate::DaoGetAppPayMode(uid) == 0 ? 1 : 0;
            is_show_payment = is_show_subscription;
        }
        #endif
    }
    //app在后台时的sip动作模式,0:app退到后台后,sip不注销;1:sip主动注销,对应的是全部走端外推送的来电呼叫
    int android_app_push_mode = 1;
    std::string token;
    csgate::GetToken(uid, main_account, token, ver);
    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(role))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          

        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        csgate::DaoUpdateAppLoginStatus(uid);

        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(user_conf.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, user_conf.role, user_conf.mng_account_id);
        if (user_conf.role > 0)
        {
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string rest_addr = gstCSGATEConf.rest_addr;       
        std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
        std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
        std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;
 
        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_domain;
        std::string pbx_ipv6;        
        std::string pbx_ip;
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_domain = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, auth_token, redirect_ret);            
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_domain = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, token, auth_token, redirect_ret);
        }
        AK_LOG_INFO << "login,uid is " << uid << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", rtmp is:" << pair_rtmp_ser46.first;

        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(USER_ROLE, std::to_string(user_conf.role))); 
        kv.insert(std::map<std::string, std::string>::value_type(HAVE_PUBLIC_DEV, std::to_string(user_conf.have_public_dev))); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(APP_INIT, std::to_string(user_conf.initialization))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, std::to_string(is_show_payment))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, std::to_string(is_show_subscription))); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_TEMPKEY, std::to_string(user_conf.is_show_tempkey))); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          
        kv.insert(std::map<std::string, std::string>::value_type(PUSH_MODE, std::to_string(android_app_push_mode)));          
        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

//accessserver
csgate::HTTPRespCallback ReqAccessSerHandlerV30 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;//uid or mac
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 3.0);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(user);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(user);

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            ftp_ip = gstAWSConf.ftp_ip;
            ftp_ipv6 = gstAWSConf.ftp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            ftp_ip = gstAWSAucloudConf.ftp_ip;
            ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
        }
        AK_LOG_INFO << "ReqAccessSerHandlerV30, user is " << user << ",csmain is: " << csmain_ip << ",csvrtsp is:" << csvrtsp_ip
                    << ", ftp_domain is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
                    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  gstCSGATEConf.web_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6,  gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

//accessserver V3.3版本 added by chenyc,2018-04-20
csgate::HTTPRespCallback ReqAccessSerHandlerV33 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 3.3);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(user);

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
        }
        AK_LOG_INFO << "ReqAccessSerHandlerV33, user is " << user << ",csmain is: " << csmain_ip;
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

csgate::HTTPRespCallback ReqAccessSerHandlerV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 3.3);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;

        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
        }
        AK_LOG_INFO << "ReqAccessSerHandlerV62, user is " << user << ",csmain is: " << csmain_domain;
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

//restserver
csgate::HTTPRespCallback ReqRestSerHandlerV60 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string rest_addr = gstCSGATEConf.rest_addr;       
    std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
    std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
    std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;

    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  

    if (IsTestServer(gstCSGATEConf.cloud_env) 
        && strlen(gstCSGATEConf.auto_test_dispatch_uid) > 0
        && GetCurrentTimeStamp() - gstCSGATEConf.auto_test_newgate_addr_time < 10)//间隔10s考虑不能影响正常的使用
    {
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_HTTPS, gstCSGATEConf.auto_test_newgate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_HTTPS_IPV6, gstCSGATEConf.auto_test_newgate_addr));
    }
    else
    {
        //服务器调度时候，app会用这个接口探测这个新的网关地址是否可用，并且拿这个接口访问的网关地址 当最新的网关地址
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_HTTPS, gstCSGATEConf.csgate_https));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_HTTPS_IPV6, gstCSGATEConf.csgate_https_ipv6));
    }
    
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

//app手动拉起后,非登录时调用的接口,此时app已经有token了. added by chenyc,判断是否过期
csgate::HTTPRespCallback ReqServerListHandlerV40 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.0);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(user);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            ftp_ip = gstAWSConf.ftp_ip;
            ftp_ipv6 = gstAWSConf.ftp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            ftp_ip = gstAWSAucloudConf.ftp_ip;
            ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
        }
        AK_LOG_INFO << "server_list,user is " << user << "csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip
                    << ", ftp_domain is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, gstCSGATEConf.web_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));          
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};


csgate::HTTPRespCallback ReqServerListHandlerV45 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.5);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;        
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;          
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
        }
        AK_LOG_INFO << "server_list, user is " << user << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip;
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));          
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

/*4.6新增报文加密*/
csgate::HTTPRespCallback ReqServerListHandlerV46 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string encrypt_resp;
    std::string token = ctx->GetQuery("token");
    std::string token_user = ctx->GetQuery("user"); //4.6新增token续时，通过serverlist接口带账号密码来续
    std::string token_passwd = ctx->GetQuery("passwd"); 
    std::string user_md5 = akuvox_encrypt::MD5(token_user).toStr();
    std::string key = user_md5.substr(0, 16);

    if (token_user.length() > 0)
    {
        ret = csgate::DaoTokenContinuation(token_user, token_passwd, token);
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user: " << token_user << " passwd: " << token_passwd;
            AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key, encrypt_resp);
            cb(encrypt_resp);
            return;
        }
    }

    std::string user;
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.6);

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "Token invalid!";
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        const char* head_agent = ctx->FindRequestHeader("User-Agent");
        int is_ios = 0;
        if (head_agent && strcasestr(head_agent, "ios"))
        {
            is_ios = 1;
        }
        if (is_ios == 1 && gstCSGATEConf.is_china_version)
        {
            web_addr = gstCSGATEConf.web_ip;
            web_addr += ":9443";
        }
        
        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6; 
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);          
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);
        }
        ChangeRtspAddr(is_ios, csvrtsp_ip, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list,user is " << user << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csmain_ipv6;
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));          
        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

/*5.4新增报文token加密
参数：&user=xxxxx&datas=xxxxxxxxxxxxxx
Data内容为AES256加密后经过Base64的数据。
内容为json格式，AES256 key为MD5(user)取前16位+固定的16位值。
内容如下：
{
    "passwd": "xxxxxxxx",
    "token": "xxxxxxxxxx",
    "auth_token": "xxxxxxxx"
}
*/
csgate::HTTPRespCallback ReqServerListHandlerV54 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string encrypt_resp;
    std::string req_user = akuvox_encrypt::UrlDecode(ctx->GetQuery("user"));
    std::string datas = ctx->GetQuery("datas");
    std::string req_user_key = req_user;
    TrimString(req_user);

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string md5_user = akuvox_encrypt::MD5(req_user_key).toStr();
    std::string key_head = md5_user.substr(0, 16);

    std::string data_blank;
    AESDecryptRequest(datas, key_head, data_blank);

    Json::Reader reader;
    Json::Value root;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader.parse(data_blank, root))
    {
        AK_LOG_WARN << "parse json error.data=" << data_blank;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << data_blank;
    }

    std::string token = root["token"].asString();
    std::string passwd = root["passwd"].asString();
    std::string auth_token = root["auth_token"].asString();
    
    AK_LOG_INFO << "user:" << req_user << ",token:" << token;

    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user: " << req_user << " passwd: " << passwd;
            AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
            cb(encrypt_resp);
            return;
        }
    }

    std::string user;
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 5.4);

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << req_user;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        const char* head_ag = ctx->FindRequestHeader("User-Agent");
        const char* head = ctx->FindRequestHeader("api-version");
        int is_ios = 0;
        float ios_ver = STOF(head);
        if (head_ag && strcasestr(head_ag, "ios"))
        {
            is_ios = 1;
        }
        if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
        {
            web_addr += ":9443";
        }
        std::string user_agent = GetCtxHeader(ctx, "User-Agent");

        std::string csmain_ip =  pair_access_ser_46.first;
        std::string csmain_ipv6 = pair_access_ser_46.second;
        std::string csvrtsp_ip = pair_rtsp_ser_46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);           
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);
        }
        
        ChangeRtspAddr(is_ios, csvrtsp_ip, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list,user is " << user << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip
                    << ", pbx_ip is " << pbx_ip << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

        InsertNewGateServer(redirect_ret, kv, user_agent);
        AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key_head, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqServerListHandlerV60_61 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    int ret = 0;
    std::string encrypt_resp;
    std::stringstream encrypt_oss;
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string http_body = ctx->body().ToString();
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb("parse http body json error");
        return;
    }

    std::string req_user = root["user"].asString();

    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");  
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char user[64];
        snprintf(user, sizeof(user), "%s", req_user.c_str());
        akuvox_encrypt::CaesarDecry(user);
        req_user = user;
    }
    
    std::string datas = root["datas"].asString();
    std::string md5_user = akuvox_encrypt::MD5(req_user).toStr();
    std::string key_head = md5_user.substr(0, 16);
    std::string data_blank;
    AESDecryptRequest(datas, key_head, data_blank);

    Json::Reader reader1;
    Json::Value root1;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader1.parse(data_blank, root1))
    {
        AK_LOG_WARN << "Token invalid!";
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }

    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << data_blank;
    }

    std::string token = root1["token"].asString();
    std::string passwd = root1["passwd"].asString();
    std::string auth_token = root1["auth_token"].asString();
    
    AK_LOG_INFO << "user:" << req_user << ",token:" << token;

    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret <<
                         " user: " << req_user << " passwd: " << passwd;
            AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
            cb(GetReqResponData(encrypt_resp));
            return;
        }
    }

    std::string user;
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.0);

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << req_user;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        const char* head_ag = ctx->FindRequestHeader("User-Agent");
        const char* head = ctx->FindRequestHeader("api-version");
        int is_ios = 0;
        float ios_ver = STOF(head);
        if (head_ag && strcasestr(head_ag, "ios"))
        {
            is_ios = 1;
        }
        if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
        {
            web_addr += ":9443";
        } 
        std::string user_agent = GetCtxHeader(ctx, "User-Agent");

        std::string csmain_ip =  pair_access_ser_46.first;
        std::string csmain_ipv6 = pair_access_ser_46.second;
        std::string csvrtsp_ip = pair_rtsp_ser_46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_ip =  gstAWSConf.csmain_ip;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_ip = gstAWSConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_ip = gstAWSConf.pbx_ip;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::RedirectServerTokenRefresh(uid, main_account,  redirect_ret);            
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_ip =  gstAWSAucloudConf.csmain_ip;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_ip = gstAWSAucloudConf.pbx_ip;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);
        }
        ChangeRtspAddr(is_ios, csvrtsp_ip, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list, user is " << user << ",csmain is: " << csmain_ip << ", csvrtspd is: " << csvrtsp_ip
                    << ", pbx_ip is " << pbx_ip << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        InsertNewGateServer(redirect_ret, kv, user_agent);       
        
        AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key_head, encrypt_resp);
        //之所以还要进行Json封装，是因为安卓客户端要求请求和响应的Content-Type要一致
        cb(GetReqResponData(encrypt_resp));
    }
    return;
};

csgate::HTTPRespCallback ReqServerListHandlerV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    int ret = 0;
    std::string encrypt_resp;
    std::stringstream oss;
    std::stringstream encrypt_oss;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string http_body = ctx->body().ToString();
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb("parse http body json error");
        return;
    }

    std::string req_user = root["user"].asString();

    //新版本的用户名进行了凯撒加密
    const char* app_ver = ctx->FindRequestHeader("api-version");
    if(app_ver != nullptr && STOF(app_ver) > 6.0)
    {
        char user[64];
        snprintf(user, sizeof(user), "%s", req_user.c_str());
        akuvox_encrypt::CaesarDecry(user);
        req_user = user;
    }

    int dev_type = dbinterface::ProjectUserManage::CheckUserType(req_user);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeServerListHandlerV63(ctx, cb);
        return;
    }

    std::string datas = root["datas"].asString();
    std::string md5_user = akuvox_encrypt::MD5(req_user).toStr();
    std::string key_head = md5_user.substr(0, 16);
    std::string data_blank;
    AESDecryptRequest(datas, key_head, data_blank);

    Json::Reader reader1;
    Json::Value root1;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader1.parse(data_blank, root1))
    {
        AK_LOG_WARN << "parse json error.data=" << data_blank;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }

    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << data_blank;
    }

    std::string token = root1["token"].asString();
    std::string passwd = root1["passwd"].asString();
    std::string auth_token = root1["auth_token"].asString();

    AK_LOG_INFO << "user:" << req_user <<",token:" << token;

    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret <<
                         " user: " << req_user << " passwd: " << passwd;
            AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
            cb(GetReqResponData(encrypt_resp));
            return;
        }
    }

    std::string user;
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.0);
    
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
   
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << req_user;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        const char* head_ag = ctx->FindRequestHeader("User-Agent");
        const char* head = ctx->FindRequestHeader("api-version");
        int is_ios = 0;
        float ios_ver = STOF(head);
        if (head_ag && strcasestr(head_ag, "ios"))
        {
            is_ios = 1;
        }
        if (is_ios == 1 && gstCSGATEConf.is_china_version && ios_ver < 5.5)
        {
            web_addr += ":9443";
        }
        std::string user_agent = GetCtxHeader(ctx, "User-Agent");

        std::string csmain_domain =  pair_access_ser_46.first;
        std::string csmain_ipv6 = pair_access_ser_46.second;
        std::string csvrtsp_domain = pair_rtsp_ser_46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_domain = gstAWSConf.pbx_domain;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);             
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_domain = gstAWSAucloudConf.pbx_domain;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);
        }

        ChangeRtspAddr(is_ios, csvrtsp_domain, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list, user is " << user << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
                    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_PAYMENT, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(SHOW_SUBSCRIPTION, "0")); 
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        InsertNewGateServer(redirect_ret, kv, user_agent);

        AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key_head, encrypt_resp);
        encrypt_oss << "{" <<  "\n"
                    << "\"datas\": " << "\"" << encrypt_resp << "\"" << "\n"
                    << "}" << "\n";
        cb(encrypt_oss.str());
    }
    return;
};

//app上注册账号的时候使用,此时还没办法用login
csgate::HTTPRespCallback ReqRegisterHandlerV40 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string web_addr;
    if (::strlen(gstCSGATEConf.web_domain_name) != 0)
    {
        web_addr = gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = gstCSGATEConf.web_ip;
    }
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    //user为空，为了统计，user设置为固定值
    std::string user = "EmptyUser";
    dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(user, "ReqRegisterHandlerV40");
    return;
};


//rtspserver
csgate::HTTPRespCallback ReqRtspServerHandlerV44 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;

    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.4);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }

    user = personal_account_info.account;
    std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(user);
    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(user, "ReqRtspServerHandlerV44");
    }
    
    std::string csvrtsp_ip = pair_rtsp_ser46.first;
    std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
    {
        csvrtsp_ip = gstAWSConf.csvrtsp_ip;
        csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;

    }
    else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;
        csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
    }
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, "["+csvrtsp_ipv6+"]"));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};


//pbxserver
csgate::HTTPRespCallback ReqPbxServerHandlerV44 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.4);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }

    std::string pbx_ip;
    std::string pbx_ipv6;
    std::string pbx_domain;
    GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
    {
        pbx_ip = gstAWSConf.pbx_ip;
        pbx_ipv6 = gstAWSConf.pbx_ipv6;
    }
    else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        pbx_ip = gstAWSAucloudConf.pbx_ip;
        pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
    }
    AK_LOG_INFO << "user: " << personal_account_info.account << ", pbx_ip is " << pbx_ip << ", pbx_ipv6 is " << pbx_ipv6;
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, "["+pbx_ipv6+"]"));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqPbxServerHandlerV44");
    }
    
    return;
};

csgate::HTTPRespCallback ReqPbxServerHandlerV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int ret = 0;
    std::string token = ctx->GetQuery("token");
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.4);

    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqPbxServerHandlerV62");
    }
    
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }

    std::string pbx_ip;
    std::string pbx_ipv6;
    std::string pbx_domain;    
    GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
    {
        pbx_domain = gstAWSConf.pbx_domain;
        pbx_ipv6 = gstAWSConf.pbx_ipv6;
    }
    else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        pbx_domain = gstAWSAucloudConf.pbx_domain;
        pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
    }
    AK_LOG_INFO << "user: " << personal_account_info.account << ", pbx_ip is " << pbx_ip << ", pbx_ipv6 is " << pbx_ipv6;

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, "["+pbx_ipv6+"]"));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

//ftpserver
csgate::HTTPRespCallback ReqFtpServerHandlerV44 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int ret = 0;
    std::string token = ctx->GetQuery("token");
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.4);
    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqFtpServerHandlerV44");
    }
    
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.account);

    std::string ftp_ip = pair_ftp_ser46.first;
    std::string ftp_ipv6 = pair_ftp_ser46.second;
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
    {
        ftp_ip = gstAWSConf.ftp_ip;
        ftp_ipv6 = gstAWSConf.ftp_ipv6;
    }
    else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        ftp_ip = gstAWSAucloudConf.ftp_ip;
        ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
    }
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
    kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

csgate::HTTPRespCallback ReqFtpServerHandlerV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int ret = 0;
    std::string token = ctx->GetQuery("token");
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.4);
    //接口调用统计
    if(std::strlen(personal_account_info.account) > 0)
    {
        dbinterface::ApiUsageInfo::InsertOrUpdateApiUsageInfo(personal_account_info.account, "ReqFtpServerHandlerV62");
    }
    
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }

    std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.account);

    std::string ftp_domain = pair_ftp_ser46.first;
    std::string ftp_ipv6 = pair_ftp_ser46.second;
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
    {
        ftp_domain = gstAWSConf.ftp_domain;
        ftp_ipv6 = gstAWSConf.ftp_ipv6;
    }
    else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
    {
        ftp_domain = gstAWSAucloudConf.ftp_domain;
        ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6;
    }
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

csgate::HTTPRespCallback ReqAppVerCheckHandlerV45 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int need_upgrade = 0;
    std::string app_ver_str = ctx->GetQuery("ver");
    std::string app_phone_type_str = ctx->GetQuery("phone_type");
    AK_LOG_INFO << "app_ver_param:" << app_ver_str << ";phone_type=" << app_phone_type_str << ";need_upgrade=" << need_upgrade;
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(APP_UPGRADE, std::to_string(need_upgrade)));      
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

//app检查版本，是否需要强制升级
csgate::HTTPRespCallback ReqAppVerCheckHandlerV61 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int need_upgrade = 0;
    int force_upgrade = 0;
    std::string app_ver_str = ctx->GetQuery("ver");
    std::string app_phone_type_str = ctx->GetQuery("phone_type");

    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    if(nullptr != tmp_user)
    {
        std::string user_agent = tmp_user;
        if (user_agent == "fasttel")
        {
            HttpRespIntKV kv;
            kv.insert(std::map<std::string, int>::value_type(APP_UPGRADE, 0));      
            kv.insert(std::map<std::string, int>::value_type(APP_LATEST_VERSION_TAG, 0)); 
            kv.insert(std::map<std::string, int>::value_type(FORCE_UPGRADE, 0));      
            cb(buildCommHttpMsg2(HTTP_CODE_SUC, kv));        
            return;
        }
    }

    int app_ver = std::stoi(app_ver_str);
    if (app_ver < gstCSGATEConf.force_upgrade_version)
    {
        force_upgrade = 1;
    }
    else
    {
        force_upgrade = 0;
    }

    int app_latest_version = 0;
    std::stringstream oss;
    if (GetAppLatestVersion(app_phone_type_str.c_str(), app_latest_version) != 0)
    {
        AK_LOG_INFO << "app_ver_param:" << app_ver_str << ";phone_type=" << app_phone_type_str << ";need_upgrade=" << need_upgrade << ";force_upgrade=" << force_upgrade << ";force_upgrade_version=" << gstCSGATEConf.force_upgrade_version;

        HttpRespIntKV kv;
        kv.insert(std::map<std::string, int>::value_type(APP_UPGRADE, need_upgrade));      
        kv.insert(std::map<std::string, int>::value_type(FORCE_UPGRADE, force_upgrade));      
        cb(buildCommHttpMsg2(HTTP_CODE_SUC, kv));
        return;
    }

    const int IGNORE_UPDATE_VERSION = 6000;
    if (app_ver >= app_latest_version || app_ver < IGNORE_UPDATE_VERSION)
    {
        need_upgrade = 0;
    }
    else
    {
        need_upgrade = 1;
    }
    AK_LOG_INFO << "app_ver_param:" << app_ver_str << ";phone_type=" << app_phone_type_str << ";need_upgrade=" << need_upgrade << ";force_upgrade=" << force_upgrade << ";app_latest_version=" << app_latest_version << ";force_upgrade_version=" << gstCSGATEConf.force_upgrade_version;
    
    HttpRespIntKV kv;
    kv.insert(std::map<std::string, int>::value_type(APP_UPGRADE, need_upgrade));      
    kv.insert(std::map<std::string, int>::value_type(APP_LATEST_VERSION_TAG, app_latest_version)); 
    kv.insert(std::map<std::string, int>::value_type(FORCE_UPGRADE, force_upgrade));      
    cb(buildCommHttpMsg2(HTTP_CODE_SUC, kv));
    return;
};

//更新app最新的版本号
csgate::HTTPRespCallback ReqUpdateAppLatestVersionHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string app_latest_version = ctx->GetQuery("app_latest_version");
    std::string force_upgrade_version = ctx->GetQuery("force_upgrade_version");
    std::string check_code = ctx->GetQuery("check_code");

    AK_LOG_INFO << "app_latest_version_param=" << app_latest_version << ";check_code=" << check_code << ";app_latest_version=" << gstCSGATEConf.app_latest_version;
    if (check_code == CHECK_CODE)
    {
        if (app_latest_version.size() > 0)
        {
            Snprintf(gstCSGATEConf.app_latest_version, sizeof(gstCSGATEConf.app_latest_version), app_latest_version.c_str());
        }

        if (force_upgrade_version.size() > 0)
        {
            gstCSGATEConf.force_upgrade_version = std::stoi(force_upgrade_version);
        }
    }
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(APP_LATEST_VERSION_TAG,  gstCSGATEConf.app_latest_version));      
    kv.insert(std::map<std::string, std::string>::value_type(FORCE_UPGRADE_VERSION_TAG, std::to_string(gstCSGATEConf.force_upgrade_version)));      
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};


csgate::HTTPRespCallback ReqUpdateLimitRateHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string limit_switch = ctx->GetQuery("limit_switch");
    std::string rate = ctx->GetQuery("rate");
    AK_LOG_INFO << "limit_switch=" << limit_switch << ";rate=" << rate;

    int limit_switch_flag = std::stoi(limit_switch);
    if (limit_switch_flag == evpp::rate_limiter::NO_LIMIT || limit_switch_flag == evpp::rate_limiter::LIMIT_FLOW)
    {
        gstCSGATEConf.limit_switch = limit_switch_flag;
        int rate_value = std::stoi(rate);
        if (rate_value > 0)
        {
            gstCSGATEConf.rate = rate_value;
            g_rate_limiter->SetRate(gstCSGATEConf.rate);
        }
    }
   
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(LIMIT_SWITCH_TAG,  std::to_string(gstCSGATEConf.limit_switch)));      
    kv.insert(std::map<std::string, std::string>::value_type(RATE_TAG, std::to_string(gstCSGATEConf.rate)));      
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

csgate::HTTPRespCallback ReqSmarthomeLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd = ctx->GetQuery("passwd"); //已经做过md5加密了

    std::string md5_user = akuvox_encrypt::MD5(user).toStr();
    std::string key = md5_user.substr(0, 16);

    std::string dev_id;
    ret = csgate::DaoSmarthomeDev(user, passwd, dev_id);
    if (csgate::ERR_SUCCESS != ret)
    {
        if (csgate::ERR_USER_NOT_EXIT == ret)
        {
            AK_LOG_WARN << "User not exist. user:" << user;
            std::string encrypt_resp;
            AESEncryptRespone(buildErrorHttpMsg(ret), key , encrypt_resp);
            cb(encrypt_resp);
        }

        return;
    }
    else
    {
        AK_LOG_INFO << "login smarthome,mac is " << user;            
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE,  gstCSGATEConf.smart_home_domain));      
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_DEVICES_ID, dev_id));      
        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(HTTP_CODE_SUC, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

//add by czw,新增v6.4网关接口,此后逻辑判断不再放在网关处理，统一放置login_conf、userconf接口
csgate::HTTPRespCallback ReqLoginHandlerV64 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string username = ctx->GetQuery("user");

    //凯撒解密
    char user[64];
    snprintf(user, sizeof(user), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user);
    username = user;

    int dev_type = dbinterface::ProjectUserManage::CheckUserType(username);    
    if (dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeLoginHandlerV64(ctx, cb);
        return;
    }
    
    std::string encrypt_resp;
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    std::string user_key = username;
    TrimString(username);
    csgate::PersonalAccountInfo personal_account_info;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    ret = csgate::DaoCheckAppLogin(username, passwd_md5, 6.4, personal_account_info); 
    
    std::string md5_user = akuvox_encrypt::MD5(user_key).toStr();
    std::string encrypt_key = md5_user.substr(0, 16);

    if (0 == ret && personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        int pm_ret = csgate::DaoCheckPMAppStatus(user);
        if (csgate::ERR_USER_NOT_EXIT == pm_ret || csgate::ERR_PM_APP_STATUS_CLOSED == pm_ret)
        {
            AK_LOG_WARN << "PM app " << user << " is closed.ret=" << pm_ret;
            AESEncryptRespone(buildErrorHttpMsg(pm_ret), encrypt_key, encrypt_resp);
            cb(encrypt_resp);
            return;
        }
    }

    std::string main_account = personal_account_info.main_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);

    std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
    csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, user:" << username << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    std::string access_token;
    csgate::GetToken(personal_account_info.uid, main_account, access_token, 6.4);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token));         
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), personal_account_info.uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", personal_account_info.uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        GetPbxServerForApp(personal_account_info, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_domain = gstAWSConf.pbx_domain;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;
            
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, access_token, "", redirect_ret);            
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_domain = gstAWSAucloudConf.pbx_domain;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, access_token, "", redirect_ret);
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)
        {
            csmain_domain =  gstEcloud2UcloudConf.csmain_domain;
            csmain_ipv6 = gstEcloud2UcloudConf.csmain_ipv6;
            csvrtsp_domain = gstEcloud2UcloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstEcloud2UcloudConf.csvrtsp_ipv6;
            pbx_domain = gstEcloud2UcloudConf.pbx_domain;
            pbx_ipv6 = gstEcloud2UcloudConf.pbx_ipv6;
            web_addr = gstEcloud2UcloudConf.web_domain;
            web_ipv6 = gstEcloud2UcloudConf.web_ipv6;
            rest_addr = gstEcloud2UcloudConf.rest_addr;
            rest_ipv6 = gstEcloud2UcloudConf.rest_ipv6;
            rest_ssl_addr = gstEcloud2UcloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstEcloud2UcloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, access_token, "", redirect_ret);
        }        
        
        AK_LOG_INFO << "login, uid is " << personal_account_info.uid << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        //获取smarthomeid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(personal_account_info.uid, smarthome_uid);

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 
            
        csgate::AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqSmsLoginHandlerV64 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string phone = ctx->GetQuery("phone");

    char user[64];
    snprintf(user, sizeof(user), "%s", phone.c_str());
    akuvox_encrypt::CaesarDecry(user);
    phone = user;


    int dev_type = dbinterface::ProjectUserManage::CheckUserType(phone);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeSmsLoginHandlerV64(ctx, cb);
        return;
    }

    std::string area_code;
    std::string code = ctx->GetQuery("code");
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    csgate::PersonalAccountInfo personal_account_info;    
    ret = csgate::DaoCheckPhone(phone, code, area_code, personal_account_info);  

    std::string encrypt_resp;
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    std::string md5_user = akuvox_encrypt::MD5(phone).toStr();
    std::string encrypt_key = md5_user.substr(0, 16);
    
    //不同类型app类型处理
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);

    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << " user_agent:" << tmp_user;

    std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
    csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, phone:" << phone << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    std::string access_token;
    csgate::GetToken(uid, main_account, access_token, 6.4);
    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, gstCSGATEConf.web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 

        AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(user_conf.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, user_conf.role, user_conf.mng_account_id);
        if (user_conf.role > 0)
        {
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }

        //根据负载均衡算法获取到系统的接入服务器和转流服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(uid);
        std::pair<std::string, std::string> pair_rtsp_domain_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(uid);
        std::pair<std::string, std::string> pair_rtmp_ser46;
        RtspAddr2RtmpAddr(pair_rtsp_ser46, pair_rtmp_ser46);

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_domain_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string pbx_domain;
        std::string pbx_ipv6;        
        std::string pbx_ip;
        std::string web_ipv6=gstCSGATEConf.web_ipv6;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_domain = gstAWSConf.pbx_domain;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;

            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, access_token, auth_token, redirect_ret);             
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_domain = gstAWSAucloudConf.pbx_domain;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, access_token, auth_token, redirect_ret);
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)
        {
            csmain_domain =  gstEcloud2UcloudConf.csmain_domain;
            csmain_ipv6 = gstEcloud2UcloudConf.csmain_ipv6;
            csvrtsp_domain = gstEcloud2UcloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstEcloud2UcloudConf.csvrtsp_ipv6;
            pbx_domain = gstEcloud2UcloudConf.pbx_domain;
            pbx_ipv6 = gstEcloud2UcloudConf.pbx_ipv6;
            web_addr = gstEcloud2UcloudConf.web_domain;
            web_ipv6 = gstEcloud2UcloudConf.web_ipv6;
            rest_addr = gstEcloud2UcloudConf.rest_addr;
            rest_ipv6 = gstEcloud2UcloudConf.rest_ipv6;
            rest_ssl_addr = gstEcloud2UcloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstEcloud2UcloudConf.rest_ssl_ipv6;
            csgate::UpdateTokenToRedirectServer(personal_account_info.uid, access_token, auth_token, redirect_ret);
        }          
        AK_LOG_INFO << "login,uid is " << uid << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain << ", rtmp is:" << pair_rtmp_ser46.first;
        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER, pair_rtmp_ser46.first));    
        kv.insert(std::map<std::string, std::string>::value_type(RTMP_SERVER_IPV6, pair_rtmp_ser46.second));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, access_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          

        AESEncryptRespone(buildCommHttpMsg(ret, kv), encrypt_key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqServerListHandlerV64 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    int ret = 0;
    std::string encrypt_resp;
    std::stringstream oss;
    std::stringstream encrypt_oss;
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    std::string http_body = ctx->body().ToString();
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb("parse http body json error");
        return;
    }

    std::string req_user = root["user"].asString();
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", req_user.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    req_user = user_tmp;

    int dev_type = dbinterface::ProjectUserManage::CheckUserType(req_user);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeServerListHandlerV64(ctx, cb);
        return;
    }

    std::string datas = root["datas"].asString();
    std::string md5_user = akuvox_encrypt::MD5(req_user).toStr();
    std::string key_head = md5_user.substr(0, 16);
    std::string data_blank;
    AESDecryptRequest(datas, key_head, data_blank);

    Json::Reader reader1;
    Json::Value root1;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader1.parse(data_blank, root1))
    {
        AK_LOG_WARN << "parse json error.data=" << data_blank;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }

    if (gstCSGATEConf.decrypt_log_ouput)
    {
        AK_LOG_INFO << data_blank;
    }

    std::string token = root1["token"].asString();
    std::string passwd = root1["passwd"].asString();
    std::string auth_token = root1["auth_token"].asString();
    AK_LOG_INFO << "user:" << req_user << " token:" << token;

    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret <<
                         " user: " << req_user << " passwd: " << passwd;
            AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
            cb(GetReqResponData(encrypt_resp));
            return;
        }
    }

    const char* head_ag = ctx->FindRequestHeader("User-Agent");
    int is_ios = 0;
    if (head_ag && strcasestr(head_ag, "ios"))
    {
        is_ios = 1;
    }
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");
    
    std::string user;
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.4);
   
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << req_user;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
        return;
    }
    else
    {
        user = personal_account_info.account;
        //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
        std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(user);

        std::string rest_addr, web_addr, rest_ipv6, web_ipv6, rest_ssl_addr, rest_ssl_ipv6;       
        csgate::GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        GetPbxServerForApp(personal_account_info, pbx_ip, pbx_ipv6, pbx_domain);

        std::string csmain_domain =  pair_access_ser_46.first;
        std::string csmain_ipv6 = pair_access_ser_46.second;
        std::string csvrtsp_domain = pair_rtsp_ser_46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD)
        {
            csmain_domain =  gstAWSConf.csmain_domain;
            csmain_ipv6 = gstAWSConf.csmain_ipv6;
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
            pbx_domain = gstAWSConf.pbx_domain;
            pbx_ipv6 = gstAWSConf.pbx_ipv6;
            web_addr = gstAWSConf.web_domain;
            web_ipv6 = gstAWSConf.web_ipv6;
            rest_addr = gstAWSConf.rest_addr;
            rest_ipv6 = gstAWSConf.rest_ipv6;
            rest_ssl_addr = gstAWSConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSConf.rest_ssl_ipv6;
            
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);            
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)
        {
            csmain_domain =  gstAWSAucloudConf.csmain_domain;
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;
            pbx_domain = gstAWSAucloudConf.pbx_domain;
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6;
            web_addr = gstAWSAucloudConf.web_domain;
            web_ipv6 = gstAWSAucloudConf.web_ipv6;
            rest_addr = gstAWSAucloudConf.rest_addr;
            rest_ipv6 = gstAWSAucloudConf.rest_ipv6;
            rest_ssl_addr = gstAWSAucloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstAWSAucloudConf.rest_ssl_ipv6;
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);
        }
        else if(redirect_ret == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)
        {
            csmain_domain =  gstEcloud2UcloudConf.csmain_domain;
            csmain_ipv6 = gstEcloud2UcloudConf.csmain_ipv6;
            csvrtsp_domain = gstEcloud2UcloudConf.csvrtsp_domain;
            csvrtsp_ipv6 = gstEcloud2UcloudConf.csvrtsp_ipv6;
            pbx_domain = gstEcloud2UcloudConf.pbx_domain;
            pbx_ipv6 = gstEcloud2UcloudConf.pbx_ipv6;
            web_addr = gstEcloud2UcloudConf.web_domain;
            web_ipv6 = gstEcloud2UcloudConf.web_ipv6;
            rest_addr = gstEcloud2UcloudConf.rest_addr;
            rest_ipv6 = gstEcloud2UcloudConf.rest_ipv6;
            rest_ssl_addr = gstEcloud2UcloudConf.rest_ssl_addr;
            rest_ssl_ipv6 = gstEcloud2UcloudConf.rest_ssl_ipv6;
            csgate::RedirectServerTokenRefresh(uid, main_account, redirect_ret);
        }    

        
        ChangeRtspAddr(is_ios, csvrtsp_domain, csvrtsp_ipv6);
        AK_LOG_INFO << "server_list, user is " << user << ",csmain is: " << csmain_domain << ", csvrtspd is: " << csvrtsp_domain
                    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        InsertNewGateServer(redirect_ret, kv, user_agent);


        AESEncryptRespone(buildCommHttpMsg(csgate::ERR_SUCCESS, kv), key_head, encrypt_resp);
        cb(GetReqResponData(encrypt_resp));
    }
    return;
};

csgate::HTTPRespCallback ReqLoginHandlerApp = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string username = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    float api_version = STOF(ctx->FindRequestHeader("api-version"));
    
    //凯撒解密
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
   
    //判断是否为办公
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(username);
    if (dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeLoginHandlerApp(ctx, cb);
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    csgate::PersonalAccountInfo personal_account_info;
    TrimString(username);
    ret = csgate::DaoCheckAppLogin(username, passwd_md5, api_version, personal_account_info);

    //pm app开关判断
    if (csgate::ERR_SUCCESS == ret && personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        int pm_ret = csgate::DaoCheckPMAppStatus(username);

        if (!CheckIsSupportPMAppEntrance(ctx))
        {
            pm_ret = ERR_USER_NOT_EXIT;
        }
    
        if (csgate::ERR_USER_NOT_EXIT == pm_ret || csgate::ERR_PM_APP_STATUS_CLOSED == pm_ret)
        {
            AK_LOG_WARN << "PM app user:" << username << " is closed.ret=" << pm_ret;
            cb(buildErrorHttpMsg(pm_ret));
            return;
        }
    }
    //用户错误处理
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, user:" << username << " error:" << ret;
        cb(buildErrorHttpMsg(ret));
        return;
    }

    //用户基础信息查询
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;

    //不同类型app类型处理
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
 
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);
    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);

    // 判断用户是否需要重定向到迁移服务器
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程,到aucloud的login_conf接口判断
    if (redirect_ret && api_version >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }
     
    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (api_version >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }

        //web、rest addr; APP未激活也要返回
        std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6;
        GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildCommHttpMsg(ret, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //审计日志
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), personal_account_info.uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", personal_account_info.uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        HttpRespKV kv;

        GenerateServerInfo(redirect_ret, personal_account_info, tmp_user, api_version, token_renew_info.token, kv);

        AK_LOG_INFO << "login, get server addr info";

        //获取smarthome uid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(personal_account_info.uid, smarthome_uid);

        //组装消息体  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 
            
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

csgate::HTTPRespCallback ReqLoginHandlerApp68 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string username = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    float api_version = STOF(ctx->FindRequestHeader("api-version"));
    
    //凯撒解密
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
   
    //判断是否为办公
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(username);
    if (dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqNewOfficeLoginHandlerApp(ctx, cb);
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    csgate::PersonalAccountInfo personal_account_info;
    TrimString(username);
    ret = csgate::DaoCheckAppLogin(username, passwd_md5, api_version, personal_account_info);

    //pm app开关判断
    if (csgate::ERR_SUCCESS == ret && personal_account_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        int pm_ret = csgate::DaoCheckPMAppStatus(username);

        if (!CheckIsSupportPMAppEntrance(ctx))
        {
            pm_ret = ERR_USER_NOT_EXIT;
        }
    
        if (csgate::ERR_USER_NOT_EXIT == pm_ret || csgate::ERR_PM_APP_STATUS_CLOSED == pm_ret)
        {
            AK_LOG_WARN << "PM app user:" << username << " is closed.ret=" << pm_ret;
            cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
            return;
        }
    }
    //用户错误处理
    if ((csgate::ERR_USER_NOT_EXIT == ret) || (csgate::ERR_PASSWD_INVALID == ret))
    {
        AK_LOG_WARN << "Check user error, user:" << username << " error:" << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    //用户基础信息查询
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;

    //不同类型app类型处理
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(tmp_user, main_account);
 
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);
    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);

    // 判断用户是否需要重定向到迁移服务器
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程,到aucloud的login_conf接口判断
    if (redirect_ret && api_version >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }
    HttpRespKV kv;
    // api_version >= 7.11 版本app 重定向的的用户。返回word_key app会通过/apache-v3.0/getAppWord接口请求词条
    if (redirect_ret != REDIRECT_NO_NEED && redirect_ret != REDIRECT_UNINIT && api_version >= std::stof(csgate::V711))
    {
        std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
        GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);
        AK_LOG_INFO << "api_version >= 7.11, enduser is need redirect, user: " << username ;
        kv.insert(std::map<std::string, std::string>::value_type(IS_NEED_REDIRECT, "1"));
        std::string redirect_cloud_word_key = csgate::GetRedirectCloudWordKey(redirect_ret);
        kv.insert(std::map<std::string, std::string>::value_type(REDIRECT_CLOUD_WORD_KEY, redirect_cloud_word_key));
        
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
        return;
    }
    else if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (api_version >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }

        //web、rest addr; APP未激活也要返回
        std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
        GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);
        
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        if(personal_account_info.role == ACCOUNT_ROLE_OFFICE_NEW_PER){
            // 新办公用户app登录，将审计日志发送到
            AK_LOG_INFO << "New Office App login success. account=" << personal_account_info.account;
            KafkaNotifyHandler::GetInstance()->PushNewOfficeUserLoginAuditLogMessage(personal_account_info.account, ctx->remote_ip());
        }
        else{
            // 其他直接向数据库中插入审计日志
            model::AuditLogInfo audit_log_info;
            memset(&audit_log_info, 0, sizeof(audit_log_info));
            Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
            Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), personal_account_info.uid.c_str());
            audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
            snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", personal_account_info.uid.c_str());
            const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
            Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
            model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }

        HttpRespKV kv;
        GenerateServerInfo(redirect_ret, personal_account_info, tmp_user, api_version, token_renew_info.token, kv);
        UpdateRefreshTokenToRedirectServer(personal_account_info.uid, token_renew_info.refresh_token, redirect_ret);
        
        AK_LOG_INFO << "login, get server addr info";

        //获取smarthome uid
        std::string smarthome_uid;;
        csgate::DaoSmarthomeAccount(personal_account_info.uid, smarthome_uid);

        //组装消息体  
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid)); 
            
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;
};

csgate::HTTPRespCallback ReqSmsLoginHandlerApp = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string code = ctx->GetQuery("code");
    std::string phone = ctx->GetQuery("phone");
    std::string area_code = ctx->GetQuery("area_code");
    const char* api_version = ctx->FindRequestHeader("api-version");

    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", phone.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    phone = user_tmp;
    
    if(!HttpCheckSqlParam(phone))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Phone=" << phone;
        cb(buildErrorHttpMsg(csgate::ERR_USER_NOT_EXIT));
        return;
    }
    
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(phone);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeSmsLoginHandlerApp(ctx, cb);
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    //校验手机号
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckPhone(phone, code, area_code, personal_account_info);    

    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret)
    {
        AK_LOG_WARN << "Check user error, phone:" << phone << " error:" << ret;
        cb(buildErrorHttpMsg(ret));
        return;
    }

    //判断不同app类型
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    UpdateAppSmartType(tmp_user, uid);

    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << ",main_account: " << main_account << ", user_agent:" << tmp_user;

    //获取用户信息
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);

    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);

    // 判断用户是否需要重定向到迁移服务器
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程,到aucloud的login_conf接口判断
    if (redirect_ret && STOF(api_version) >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }
    // api_version >= 7.11 版本app 重定向的的用户。返回word_key app会通过/apache-v3.0/getAppWord接口请求词条
    if (redirect_ret != REDIRECT_NO_NEED && redirect_ret != REDIRECT_UNINIT && STOF(api_version) >= std::stof(csgate::V711))
    {
        HttpRespKV kv;
        std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
        GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);
        AK_LOG_INFO << "api_version >= 7.11, sms login need redirect, phone: " << phone ;
        kv.insert(std::map<std::string, std::string>::value_type(IS_NEED_REDIRECT, "1"));
        std::string redirect_cloud_word_key = csgate::GetRedirectCloudWordKey(redirect_ret);
        kv.insert(std::map<std::string, std::string>::value_type(REDIRECT_CLOUD_WORD_KEY, redirect_cloud_word_key));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
        return;
    }
    else if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (STOF(api_version) >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }
    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildCommHttpMsg(ret, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //审计日志
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(user_conf.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, user_conf.role, user_conf.mng_account_id);
        if (user_conf.role > 0)
        {
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }

        HttpRespKV kv;
        GenerateServerInfo(redirect_ret, personal_account_info, tmp_user, STOF(api_version), token_renew_info.token, kv);
       
        AK_LOG_INFO << "login, get server addr info";

        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

csgate::HTTPRespCallback ReqNewSmsLoginHandlerApp = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string code = ctx->GetQuery("code");
    std::string phone = ctx->GetQuery("phone");
    std::string area_code = ctx->GetQuery("area_code");
    const char* api_version = ctx->FindRequestHeader("api-version");

    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", phone.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    phone = user_tmp;
    
    if(!HttpCheckSqlParam(phone))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Phone=" << phone;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }
    
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(phone);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqNewOfficeSmsLoginHandlerApp(ctx, cb);
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    //校验手机号
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckPhone(phone, code, area_code, personal_account_info);    

    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret)
    {
        AK_LOG_WARN << "Check user error, phone:" << phone << " error:" << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    //判断不同app类型
    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    UpdateAppSmartType(tmp_user, uid);

    AK_LOG_INFO << "sms login, phone:" << phone << " uid:" << uid << ",main_account: " << main_account << ", user_agent:" << tmp_user;

    //获取用户信息
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);

    std::string auth_token;
    csgate::GetAuthToken(uid, main_account, auth_token);

    std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);


    // 判断用户是否需要重定向到迁移服务器
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程,到aucloud的login_conf接口判断
    if (redirect_ret && STOF(api_version) >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }

    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret)
    {
        // v6.6将以上三种状态移到login_conf判断
        if (STOF(api_version) >= std::stof(csgate::V66))
        {
            ret = ERR_SUCCESS;
        }
    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token)); 
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //审计日志
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(user_conf.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, user_conf.role, user_conf.mng_account_id);
        if (user_conf.role > 0)
        {
            model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);
        }

        HttpRespKV kv;
        GenerateServerInfo(redirect_ret, personal_account_info, tmp_user, STOF(api_version), token_renew_info.token, kv);
        UpdateRefreshTokenToRedirectServer(personal_account_info.uid, token_renew_info.refresh_token, redirect_ret);
        
        AK_LOG_INFO << "login, get server addr info";

        //获取smarthomeid
        std::string smarthome_uid;
        csgate::DaoSmarthomeAccount(uid, smarthome_uid);
            
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, csgate::PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(AUTH_TOKEN, auth_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, smarthome_uid));          
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;
};

csgate::HTTPRespCallback ReqServerListHandlerApp = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string http_body = ctx->body().ToString();    
    const char* head = ctx->FindRequestHeader("api-version");
    float api_version = STOF(head);
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }

    std::string token = root["token"].asString();
    std::string passwd = root["passwd"].asString();
    std::string auth_token = root["auth_token"].asString();
    std::string req_user = root["user"].asString();
    
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", req_user.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    req_user = user_tmp;
    
    //判断办公处理
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(req_user);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeServerListHandlerApp(ctx, cb);
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    //token刷新续时
    ret = csgate::DaoTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user:" << req_user << " passwd: " << passwd;
            cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
            return;
        }
    }

    //ios标识，特殊处理
    std::string head_ag = GetCtxHeader(ctx, "User-Agent");
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, api_version);
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user:" << req_user;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        HttpRespKV kv;
        std::string redirect_update_token = token;
        GenerateServerInfo(RedirectCloudType::REDIRECT_UNINIT, personal_account_info, head_ag, api_version, redirect_update_token, kv);
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

        cb(buildCommHttpMsg(csgate::ERR_SUCCESS, kv));
    }
    return;
};

csgate::HTTPRespCallback ReqSafeServerListHandlerApp = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    const char* head = ctx->FindRequestHeader("api-version");
    float api_version = STOF(head);

    const char* token = ctx->FindRequestHeader("x-auth-token");
    if (nullptr == token)
    {
        AK_LOG_WARN << "req serverlist report token is null";
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR));
        return;
    }
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, api_version);

    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << personal_account_info.account;
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR));
        return;
    }

    //判断办公处理
    int dev_type = dbinterface::ProjectUserManage::CheckUserType(personal_account_info.main_account);
    if(dev_type == csmain::OFFICE_APP)
    {
        csgate::ReqOfficeSafeServerListHandlerApp(ctx, cb);
        return;
    }

    //ios标识，特殊处理
    std::string head_ag = GetCtxHeader(ctx, "User-Agent");

   HttpRespKV kv;
   //新版本serverlist接口无需续时token,因此不赋值
   std::string redirect_update_token;
   GenerateServerInfo(RedirectCloudType::REDIRECT_UNINIT, personal_account_info, head_ag, api_version, redirect_update_token, kv);
    
    AK_LOG_INFO << "server_list, get server info";
  
    kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
};

csgate::HTTPRespCallback ReqPmLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = ERR_SUCCESS;
    std::string username = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    float api_version = STOF(ctx->FindRequestHeader("api-version"));

    //凯撒解密
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    TrimString(username);
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckPmAppLogin(username, passwd_md5, personal_account_info);

    std::string uid = personal_account_info.account;
    std::string main_account = personal_account_info.main_account;

    AK_LOG_INFO << "uid: " << uid << "  main_account: " <<  main_account;
    
    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret || csgate::ERR_PM_APP_UNCREATED == ret)
    {
        AK_LOG_WARN << "check pm error, user not exist or passwd invalid, user:" << username << " error:" << ret;
        cb(buildErrorHttpMsg(ret));
        return;
    }

    //不同类型app类型处理
    const char* user_agent = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(user_agent, main_account);
 
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);
   
    //web、rest addr; APP未激活也要返回
    std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);

    // 判断用户是否需要重定向到迁移服务器
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程
    if (redirect_ret && api_version >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }

    // 非重定向的异常用户
    if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret )
    {
        // pm登录已经将以上三种状态移到login_conf判断
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        cb(buildCommHttpMsg(ERR_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //审计日志
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        HttpRespKV kv;
        GenerateServerInfo(redirect_ret, personal_account_info, user_agent, api_version, token_renew_info.token, kv);
        
        AK_LOG_INFO << "login, get server list";

        //组装返回消息体
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_ACCOUNT_ID, "")); 
            
        cb(buildCommHttpMsg(ret, kv));
    }
    return;    
};


csgate::HTTPRespCallback ReqNewPmLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = ERR_SUCCESS;
    std::string username = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    std::string id_code = ctx->GetQuery("id_code");
    
    float api_version = STOF(ctx->FindRequestHeader("api-version"));
    //凯撒解密
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    TrimString(username);
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckPmAppLogin(username, passwd_md5, personal_account_info, api_version, id_code);

    std::string uid = personal_account_info.account;
    std::string main_account = personal_account_info.main_account;

    AK_LOG_INFO << "uid: " << uid << "  main_account: " <<  main_account;
    
    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret)
    {
        AK_LOG_WARN << "check pm error, user not exist or passwd invalid, user:" << username << " error:" << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    if (csgate::ERR_PM_APP_UNCREATED == ret)
    {
        AK_LOG_WARN << "check pm error, pm app uncreated. user:" << username << " error:" << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_PM_APP_UNCREATED));
        return;
    }
    
    //不同类型app类型处理
    const char* user_agent = ctx->FindRequestHeader("User-Agent");
    csgate::UpdateAppSmartType(user_agent, main_account);
 
    USER_CONF user_conf = {0};
    csgate::DaoGetUserConf(personal_account_info, user_conf);

    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(uid, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(uid, main_account, token_renew_info);
   
    //web、rest addr; APP未激活也要返回
    std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
    GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);
    
    RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
    
    // 防止出现在scloud上未激活,但是在aucloud上激活导致无法登录的问题
    // api_version < 6.6, app直接在login接口判断了账号的异常情况,这个问题无法通过重定向避免
    // api_version >= 6.6, app在login_conf接口判断账号的异常状态,此时将ret转为success走重定向流程
    if (redirect_ret && api_version >= std::stof(csgate::V66))
    {
        ret =  ERR_SUCCESS;
    }
    
    HttpRespKV kv;
    // api_version >= 7.11 版本app 重定向的的用户。返回word_key app会通过/apache-v3.0/getAppWord接口请求词条
    if (redirect_ret != REDIRECT_NO_NEED && redirect_ret != REDIRECT_UNINIT && api_version >= std::stof(csgate::V711))
    {
        std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever;
        GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6, web_backend_sever);
        AK_LOG_INFO << "api_version >= 7.11, pm is need redirect, user: " << username ;
        kv.insert(std::map<std::string, std::string>::value_type(IS_NEED_REDIRECT, "1"));
        std::string redirect_cloud_word_key = csgate::GetRedirectCloudWordKey(redirect_ret);
        kv.insert(std::map<std::string, std::string>::value_type(REDIRECT_CLOUD_WORD_KEY, redirect_cloud_word_key));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
        return;
    }
    else if (csgate::ERR_APP_EXPIRE == ret || csgate::ERR_APP_UNACTIVE == ret || csgate::ERR_APP_UNPAID == ret || csgate::ERR_NEED_TWO_FACTOR_AUTH == ret)
    {
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_sever));
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  

        if(csgate::ERR_NEED_TWO_FACTOR_AUTH == ret)
        {
            AK_LOG_INFO << "api_version >= 7.1, pm need check two factor auth, user: " << username ;
            std::string temp_token = AppTwoFactorAuth::GenerateTempToken(username);
            kv.insert(std::map<std::string, std::string>::value_type(IS_NEED_TWO_FACTOR_AUTH, "1"));
            kv.insert(std::map<std::string, std::string>::value_type(TWO_FACTOR_AUTH_TEMP_TOKEN, temp_token));
            kv.insert(std::map<std::string, std::string>::value_type(EMAIL, personal_account_info.email));
        }
        //pm 登录双重认证，不下发token,app未激活，app过期，app未支付三种状态下发token
        else
        {
            kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
            kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        }
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    else if (csgate::ERR_SUCCESS == ret)
    {
        //审计日志
        model::AuditLogInfo audit_log_info;
        memset(&audit_log_info, 0, sizeof(audit_log_info));
        Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), ctx->remote_ip().c_str());
        Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), uid.c_str());
        audit_log_info.type = model::AUDIT_TYPE_LOG_IN;
        snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", uid.c_str());
        const char* opera_type = model::AuditLog::GetInstance().GetOperaType(personal_account_info.role);
        Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
        model::AuditLog::GetInstance().GetDistributor(audit_log_info, personal_account_info.role, user_conf.mng_account_id);
        model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

        csgate::GenerateServerInfo(redirect_ret, personal_account_info, user_agent, api_version, token_renew_info.token, kv);
        UpdateRefreshTokenToRedirectServer(personal_account_info.uid, token_renew_info.refresh_token, redirect_ret);
        
        AK_LOG_INFO << "login, get server list";

        //组装返回消息体
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
            
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;    
};

csgate::HTTPRespCallback ReqPmServerListHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string http_body = ctx->body().ToString();    
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    const char* api_version = ctx->FindRequestHeader("api-version");
    std::string token = root["token"].asString();
    std::string passwd = root["passwd"].asString();
    std::string auth_token = root["auth_token"].asString();
    std::string req_user = root["user"].asString();
    
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", req_user.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    req_user = user_tmp;

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    //token刷新续时
    ret = csgate::DaoPmTokenContinuation(req_user, passwd, token);
    if (csgate::ERR_SUCCESS != ret)
    {
        ret = csgate::DaoTokenContinuation2(auth_token, token); //auth_token方式续时
        if (csgate::ERR_SUCCESS != ret)
        {
            AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user:" << req_user << " passwd: " << passwd;
            cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
            return;
        }
    }

    //ios标识，特殊处理
    std::string head_ag = GetCtxHeader(ctx, "User-Agent");
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.6);

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;
    
    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user:" << req_user;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        HttpRespKV kv;
        std::string redirect_update_token = token;
        GenerateServerInfo(RedirectCloudType::REDIRECT_UNINIT, personal_account_info, head_ag, STOF(api_version), redirect_update_token, kv);

        AK_LOG_INFO << "pm server_list, get server addr info ";

        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

        cb(buildCommHttpMsg(csgate::ERR_SUCCESS, kv));
    }
    return;  
};

csgate::HTTPRespCallback ReqPmSafeServerListHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;

    const char* api_version = ctx->FindRequestHeader("api-version");
    std::string token = GetCtxHeader(ctx, "x-auth-token");

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, STOF(api_version));

    std::string uid = personal_account_info.uid;
    std::string main_account = personal_account_info.main_account;

    //ios标识，特殊处理
    std::string head_ag = GetCtxHeader(ctx, "User-Agent");

    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << personal_account_info.account;
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR));
        return;
    }
    else
    {
        HttpRespKV kv;
        //新版本serverlist接口无需续时token,因此不赋值
        std::string redirect_update_token;
        GenerateServerInfo(RedirectCloudType::REDIRECT_UNINIT, personal_account_info, head_ag, STOF(api_version), redirect_update_token, kv);
        
        AK_LOG_INFO << "pm safe server_list, get server addr info ";
  
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    return;  
};



//全局变量http请求版本号与响应回调函数的容器
csgate::HTTPRespVerCallbackMap HTTPReqEchoMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V30] = ReqEchoHandlerV30;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V30] = ReqLoginHandlerV30;
    OMap[V33] = ReqLoginHandlerV33;  //v3.3增加ipv6,rtsp服务地址
    OMap[V45] = ReqLoginHandlerV45;
    OMap[V46] = ReqLoginHandlerV46_61;
    OMap[V55] = ReqLoginHandlerV46_61;
    OMap[V60] = ReqLoginHandlerV46_61; 
    OMap[V61] = ReqLoginHandlerV46_61;
    OMap[V62] = ReqLoginHandlerV62;
    OMap[V63] = ReqLoginHandlerV62;
    OMap[V64] = ReqLoginHandlerV64;    
    OMap[V65] = ReqLoginHandlerV64;
    OMap[V654] = ReqLoginHandlerApp;
    OMap[V66] = ReqLoginHandlerApp;
    OMap[V68] = ReqLoginHandlerApp68;
    OMap[V711] = ReqLoginHandlerApp68;
    OMap[V46Dev] = ReqLoginHandlerDevV46_61;
    OMap[V61Dev] = ReqLoginHandlerDevV46_61;
    OMap[V62Dev] = ReqLoginHandlerDevV62;
    OMap[V65Dev] = ReqLoginHandlerDev;
    OMap["DevNoAuth"] = ReqLoginHandlerDevNoAuth;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqPmLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V66] = ReqPmLoginHandler;
    OMap[V68] = ReqNewPmLoginHandler;
    OMap[V71] = ReqNewPmLoginHandler;
    OMap[V711] = ReqNewPmLoginHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqSmsLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V55] = ReqSmsLoginHandlerV55_61;
    OMap[V60] = ReqSmsLoginHandlerV55_61;
    OMap[V61] = ReqSmsLoginHandlerV55_61;
    OMap[V62] = ReqSmsLoginHandlerV62;
    OMap[V63] = ReqSmsLoginHandlerV62;
    OMap[V64] = ReqSmsLoginHandlerV64;
    OMap[V654] = ReqSmsLoginHandlerApp;
    OMap[V66] = ReqSmsLoginHandlerApp;
    OMap[V68] = ReqNewSmsLoginHandlerApp;
    OMap[V711] = ReqNewSmsLoginHandlerApp;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqAccessSerMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V30] = ReqAccessSerHandlerV30;
    OMap[V33] = ReqAccessSerHandlerV33;
    OMap[V62] = ReqAccessSerHandlerV62;
    OMap[V46Dev] = ReqAccessSerHandlerDevV46;
    OMap[V61Dev] = ReqAccessSerHandlerDevV46;
    OMap[V62Dev] = ReqAccessSerHandlerDevV62;
    OMap[V65Dev] = ReqAccessSerHandlerDev;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqGetApiSerMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V30] = ReqGetApiSerHandlerV30;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqRestSerMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V60] = ReqRestSerHandlerV60;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqServerListMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V40] = ReqServerListHandlerV40;
    OMap[V45] = ReqServerListHandlerV45;
    OMap[V46] = ReqServerListHandlerV46;
    OMap[V54] = ReqServerListHandlerV54;
    OMap[V55] = ReqServerListHandlerV54;
    OMap[V60] = ReqServerListHandlerV60_61;   
    OMap[V61] = ReqServerListHandlerV60_61;
    OMap[V62] = ReqServerListHandlerV62;
    OMap[V63] = ReqServerListHandlerV62;
    OMap[V64] = ReqServerListHandlerV64;    
    OMap[V654] = ReqServerListHandlerApp;
    OMap[V66] = ReqServerListHandlerApp;
    OMap[V68] = ReqSafeServerListHandlerApp;

    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqPmServerListMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V66] = ReqPmServerListHandler;
    OMap[V68] = ReqPmSafeServerListHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqRegisterMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V40] = ReqRegisterHandlerV40;
    OMap[VDevice] = ReqRegisterHandlerVDevice;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqVrtspSerMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V44] = ReqRtspServerHandlerV44;
    return OMap;
}


csgate::HTTPRespVerCallbackMap HTTPReqPbxSerMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V44] = ReqPbxServerHandlerV44;
    OMap[V62] = ReqPbxServerHandlerV62;
    return OMap;
}


csgate::HTTPRespVerCallbackMap HTTPReqFtpSerMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V44] = ReqFtpServerHandlerV44;
    OMap[V62] = ReqFtpServerHandlerV62;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqAppVerCheckMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V45] = ReqAppVerCheckHandlerV45;
    OMap[V61] = ReqAppVerCheckHandlerV61;
    return OMap;
}


csgate::HTTPRespVerCallbackMap HTTPReqUpdateAppLatestVersionMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V61] = ReqUpdateAppLatestVersionHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqUpdateLimitRateMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V62] = ReqUpdateLimitRateHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqSmarthomeLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V62] = ReqSmarthomeLoginHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqUpdatInnerAuthMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V64] = ReqUpdateInnerAuthHandler;
    return OMap;
}

csgate::HTTPAllRespCallbackMap HTTPAllRespMapInit()
{
    csgate::HTTPAllRespCallbackMap OMap;
    OMap[csgate::LOGIN] = HTTPReqLoginMap();
    OMap[csgate::SMS_LOGIN] = HTTPReqSmsLoginMap();
    OMap[csgate::ECHO] = HTTPReqEchoMap();
    OMap[csgate::ACCESSSERVER] = HTTPReqAccessSerMap();
    OMap[csgate::HTTP_API_SERVER] = HTTPReqGetApiSerMap();
    //v4.0
    OMap[csgate::SERVERS_LIST] = HTTPReqServerListMap();
    OMap[csgate::REGISTERSERVER] = HTTPReqRegisterMap();
    //v4.4
    OMap[csgate::HTTP_PBX_SERVER] = HTTPReqPbxSerMap();
    OMap[csgate::HTTP_FTP_SERVER] = HTTPReqFtpSerMap();
    OMap[csgate::HTTP_RTSP_SERVER] = HTTPReqVrtspSerMap();
    //v6.1
    OMap[csgate::HTTP_APP_VER_CHECK] = HTTPReqAppVerCheckMap();
    //v6.0
    OMap[csgate::RESTSERVER] = HTTPReqRestSerMap();

    //v6.1
    OMap[csgate::UPDATE_APP_LATEST_VERSION] = HTTPReqUpdateAppLatestVersionMap();

    //v6.2
    OMap[csgate::UPDATE_LIMIT_RATE] = HTTPReqUpdateLimitRateMap();
    OMap[csgate::SMARTHOME_LOGIN] = HTTPReqSmarthomeLoginMap();

    OMap[csgate::UPDATE_INNER_AUTH] = HTTPReqUpdatInnerAuthMap();
    //家居
    OMap[csgate::SH_SERVER_LIST] = HTTPReqSHServerListMap();

    //v6.6
    OMap[csgate::PM_LOGIN] = HTTPReqPmLoginMap();
    OMap[csgate::PM_SERVERS_LIST] = HTTPReqPmServerListMap();

    return OMap;
}

}

