#ifndef __CSGATE_HTTP_RESP_H__
#define __CSGATE_HTTP_RESP_H__
#include <functional>
#include <evpp/http/context.h>
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/AwsRedirect.h"
#include "dbinterface/Token.h"
#include "HttpMsgControl.h"
#include "Dao.h"
#include "AppAuthChecker.h"
#include "AwsRedirect.h"


#define ERR_APP_UNACTIVE_STR     "Your account is unactivated."
#define ERR_APP_EXPIRE1          "Your account is expire."
#define USER_NOT_EXIT            "Your account is not exist."
#define PASSWD_INVALID           "passwd is invalid"
#define TOKEN_INVALID            "token is invalid"
#define PHONE_TYPE_INVALID       "phone_type is invalid"
#define IP_INVALID               "ip is invalid"
#define RESULT                   "result"
#define MESSAGE                  "message"
#define DATAS                    "datas"
#define RET_CODE                   "code"
#define RET_ERR_CODE            "err_code"

#define API_SERVER               "api_server"
#define ACCESS_SERVER            "access_server"
#define ACCESS_SERVER_IPV6       "access_server_ipv6"

#define VRTSP_SERVER             "vrtsp_server"
#define VRTSP_SERVER_IPV6        "vrtsp_server_ipv6"
#define VRTSPS_SERVER            "vrtsps_server"        // rtsps_ipv4
#define VRTSPS_SERVER_IPV6       "vrtsps_server_ipv6"   // rtsps_ipv6

#define RTMP_SERVER              "rtmp_server"
#define RTMP_SERVER_IPV6         "rtmp_server_ipv6"

#define REST_SERVER              "rest_server"
#define REST_SERVER_IPV6         "rest_server_ipv6"
#define REST_SERVER_HTTPS        "rest_server_https"
#define REST_SERVER_HTTPS_IPV6   "rest_server_https_ipv6"

#define FILE_SERVER              "file_server"
#define FILE_SERVER_IPV6         "file_server_ipv6"
#define VOICE_ASSISTANT_SERVER   "voice_assistant_server"


#define WEB_SERVER               "web_server"
#define WEB_SERVER_IPV6          "web_server_ipv6"
#define WEB_BACKEND_SERVER       "web_backend_server"

#define FTP_SERVER               "ftp_server"
#define FTP_SERVER_IPV6          "ftp_server_ipv6"

#define PBX_SERVER               "pbx_server"
#define PBX_SERVER_IPV6          "pbx_server_ipv6"
#define PBXS_SERVER              "pbxs_server"
#define PBXS_SERVER_IPV6         "pbxs_server_ipv6"

#define GATE_SERVER_HTTPS        "gate_srv_net_https"
#define GATE_SERVER_HTTPS_IPV6   "gate_srv_net_ipv6_https"
#define LOGIN_SUCCESS            "login success"
#define API_VERSION              "api-version"
#define FORWARD_IP               "X-Forwarded-For"

#define TOKEN                    "token"
#define AUTH_TOKEN               "auth_token"
#define TOKEN_VALID         "token_valid"
#define REFRESH_TOKEN       "refresh_token"
#define ACCESS_SERVER_LIST       "get access server list success"
#define API_SERVER_LIST          "get api server success"
#define VRTSP_SERVER_LIST        "get vrtsp server list success"

#define USER_ROLE                   "role"
#define HAVE_PUBLIC_DEV             "have_public_dev"
#define APP_INIT                    "is_init"

#define PLATFORM_VER_STR           "platform_ver"
#define ERR_APP_SUCCEE             "login successful"
#define ERR_APP_LOGIN              "login failed"
#define SHOW_PAYMENT               "show_payment"
#define SHOW_SUBSCRIPTION          "show_subscription"
#define SHOW_TEMPKEY               "show_tempkey"
#define APP_UPGRADE                "upgrade"
#define FORCE_UPGRADE              "force_upgrade"
#define PUSH_MODE                  "push_mode"
#define GATE_SERVER                "gate_svr"
#define GATE_SERVER_IPV6           "gate_svr_ipv6"
#define APP_LATEST_VERSION_TAG     "app_latest_version"
#define CLOUD_LATEST_VERSION_TAG   "cloud_latest_version"
#define FORCE_UPGRADE_VERSION_TAG  "force_upgrade_version"
#define LIMIT_SWITCH_TAG           "limit_switch"
#define RATE_TAG                   "rate"
#define SMARTHOME_DEVICES_ID       "devices_id"
#define SMARTHOME_SITE             "smarthome_site"
#define SMARTHOME_ACCOUNT_ID       "smarthome_uid"
#define INSTALLER_ID               "installer_id"
#define INSTALLER_UUID             "installer_uuid"
#define INSTALLER_USERNAME         "installer_username"
#define TWO_FACTOR_AUTH_IDCODE     "two_factor_auth_idcode"
#define IS_NEED_TWO_FACTOR_AUTH    "is_need_two_factor_auth"
#define TWO_FACTOR_AUTH_TEMP_TOKEN "two_factor_auth_temp_token"
#define EMAIL                      "email"
#define IS_NEED_REDIRECT           "is_need_redirect"
#define REDIRECT_CLOUD_WORD_KEY    "redirect_cloud_word_key"


namespace csgate
{

const std::string PLATFORM_VER = "7140";    //当前平台版本号，接口如果有变更要对应修改

const std::string V30 = "3.0";
const std::string V31 = "3.1";
const std::string V33 = "3.3";
const std::string V40 = "4.0";
const std::string V44 = "4.4";
const std::string V45 = "4.5";
const std::string V46 = "4.6";
const std::string V54 = "5.4";
const std::string V55 = "5.5";
const std::string V60 = "6.0";
const std::string V61 = "6.1";
const std::string V62 = "6.2";
const std::string V63 = "6.3";
const std::string V64 = "6.4";
const std::string V65 = "6.5";
const std::string V654 = "6.54";
const std::string V66 = "6.6";
const std::string V67 = "6.7";
const std::string V68 = "6.8";
const std::string V71 = "7.1";
const std::string V711 = "7.11";

const std::string V46Dev = "4600";
const std::string V61Dev = "6100";
const std::string V62Dev = "6200";
const std::string V63Dev = "6300";
const std::string V65Dev = "6500";
const std::string VDevice = "VDevice";
const std::string CHECK_CODE = "G@J9N2JuTeWYwxY?i";

const std::string CSGATE_HTTP_BUSSINESS = "csgate_http";
const uint32_t CSGATE_HTTP_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t CSGATE_HTTP_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
const uint32_t CSGATE_HTTP_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s
const std::string AKCS_ATTACK_IP = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用

const char err_describe[8][36] =
{
    ERR_APP_SUCCEE,
    USER_NOT_EXIT,
    PASSWD_INVALID,
    ERR_APP_EXPIRE1,
    ERR_APP_UNACTIVE_STR,
    ERR_APP_LOGIN,
    ERR_APP_UNACTIVE_STR,
    TOKEN_INVALID
};

//http路由
enum HTTP_ROUTE
{
    LOGIN = 0,
    ECHO,
    ACCESSSERVER,
    HTTP_API_SERVER,
    RESTSERVER,
    ACCESSSERVER_LIST,
    VRTSPSERVER_LIST,
    //v4.0
    SERVERS_LIST,
    REGISTERSERVER,
    //v4.4
    HTTP_PBX_SERVER,
    HTTP_FTP_SERVER,
    HTTP_RTSP_SERVER,
    HTTP_WEB_SERVER,
    //v4.5
    HTTP_APP_VER_CHECK,
    //v5.5
    SMS_LOGIN,
    //v6.1
    UPDATE_APP_LATEST_VERSION,
    //v6.2
    UPDATE_LIMIT_RATE,
    SMARTHOME_LOGIN,
    //6.4 日本服务器部署
    UPDATE_INNER_AUTH,
    //家居
    SH_SERVER_LIST,
    PM_LOGIN,
    PM_SERVERS_LIST,
    //6.6
    //6.7
    INSTALLER_LOGIN,
    INSTALLER_SERVER_LIST,
    //6.8
    REF_TOKEN,
    INS_REF_TOKEN,
    PM_REF_TOKEN,

    //7.1
    PM_VERIFY_CODE,
    INS_VERIFY_CODE,

    //7.1.1
    ADMIN_LOGIN,
    ADMIN_SERVER_LIST,
    ADMIN_REF_TOKEN,
    ADMIN_SMS_LOGIN,
};

enum APP_TYPE
{
    APP_TYPE_COMMON = 0,
    APP_TYPE_MYSMART = 1,
    APP_TYPE_FASTTEL= 2,
    APP_TYPE_BELAHOME= 3,    
    APP_TYPE_ASBJ= 4,
    APP_TYPE_BAKSMARTPLUS = 5,
};

enum UpdateTokenType
{
    TOKEN_UPDATE = 0,
    TOKEN_CONTINUE = 1,    
    TOKEN_REFRESH = 2,
};

enum TestServerSetType
{
    TEST_SERVER_SET_CSMAIN = 0,
    TEST_SERVER_SET_CSVRTSP,
    TEST_SERVER_SET_PBX
};

typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
typedef std::function<void(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPServerCallBack;

typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
typedef std::map<int, HTTPRespVerCallbackMap> HTTPAllRespCallbackMap;
HTTPAllRespCallbackMap HTTPAllRespMapInit();
void RtspAddr2RtmpAddr(const std::pair<std::string, std::string>& rtsp, std::pair<std::string, std::string>& rtmp);
int GetAppLatestVersion(const char *phone_type, int &app_latest_version);
int AESDecryptRequest(const std::string& requrest, const std::string& key_head, std::string& out);
int GetInvalidTokenRetStr(std::string& str);
void GetPbxServerForDevice(const std::string& mac, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain);
void GetOpsServer(const std::string& uid, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain);
int AESEncryptRespone(const std::string& src, const std::string& key_head, std::string& out);

void UpdateTokenToRedirectServer(const std::string &account, const std::string &token, const std::string &auth_token, RedirectCloudType redirect_type);
void RedirectServerTokenRefresh(const std::string &account, const std::string& main_account,  RedirectCloudType redirect_type);
void UpdateRefreshTokenToRedirectServer(const std::string &account, const std::string &refresh_token, RedirectCloudType redirect_type);
void HandleUpdateInnerAuth(const std::string &resp);
void ChangeRtspAddr(int is_ios, std::string &ipv4, std::string &ipv6);
void GetTlsAddress(const std::pair<std::string, std::string>& pair_addr, unsigned short port, std::string& tls_domain_addr, std::string& tls_ipv6_addr);
void GetWebServer(std::string& web_addr, std::string& rest_addr, std::string& web_ipv6, std::string& rest_ipv6, std::string& rest_ssl_addr, std::string& rest_ssl_ipv6);
void GetWebServer(std::string& web_addr, std::string& rest_addr, std::string& web_ipv6, std::string& rest_ipv6, std::string& rest_ssl_addr, std::string& rest_ssl_ipv6, std::string& web_backend_server);
void UpdateAppSmartType(const char* agent, const std::string& uid);
void HandleSetAccessServer(const std::string &uid_or_mac, const std::string &csmain_ipv4, const std::string &csmain_ipv6);
void HandleSetRtspServer(const std::string &uid_or_mac, const std::string &csvrtsp_ipv4, const std::string &csvrtsp_ipv6);
void HandleSetPbxServer(const std::string &uid_or_mac, const std::string &pbx_ipv4, const std::string &pbx_ipv6);

//获取服务器地址信息
void GenerateServerInfo(RedirectCloudType redirect_type, PersonalAccountInfo& personal_account_info, const std::string& user_agent, float ver, const std::string& redirect_update_token, HttpRespKV& kv);

void InsertNewGateServer(RedirectCloudType redirect, HttpRespKV &kv, const std::string & user_agent);


std::string GetCtxHeader(const evpp::http::ContextPtr& ctx, const char *head);
std::string GetRedirectUpdateHeader(RedirectCloudType redirect_type);
std::string GetRedirectCloudWordKey(int cloud_id);




}
#endif //__CSGATE_HTTP_RESP_H__
