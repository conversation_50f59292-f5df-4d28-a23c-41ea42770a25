#include "HttpRespAdmin.h"
#include "Dao.h"
#include "util_string.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/Token.h"
#include "KafkaNotifyHandler.h"
#include "AdminAppAuthChecker.h"
#include "HttpRefreshToken.h"
#include "CsgateConf.h"

extern CSGATE_CONF gstCSGATEConf;

namespace csgate
{

HTTPRespCallback ReqAdminLoginHandlerV711 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string username = ctx-><PERSON><PERSON><PERSON><PERSON>("user");
    std::string passwd_md5 = ctx->GetQuery("passwd");
    float api_version = STOF(ctx->FindRequestHeader("api-version"));
    //不同类型app类型处理
    std::string user_agent = GetCtxHeader(ctx, "User-Agent");

    //凯撒解密
    username = GetCaeSarStr(username);
    TrimString(username);

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    PersonalAccountInfo admin_per_account;
    int ret = DaoCheckAdminAppLogin(username, passwd_md5, admin_per_account);

    //账号密码校验
    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret)
    {
        AK_LOG_WARN << "Admin App login failed. user info incorrect";
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }
    else if (csgate::ERR_ADMIN_APP_UNCREATED == ret)
    {
        AK_LOG_WARN << "Admin App login failed. app uncreated";
        cb(buildNewErrorHttpMsg(ERR_CODE_ADMIN_APP_UNCREATED));
        return;
    }

    csgate::UpdateAppSmartType(user_agent.c_str(), admin_per_account.account);

    //基本校验通过，更新并返回Token相关信息
    TokenRenewInfo token_renew_info;
    GetTokenRenewInfo(admin_per_account.account, token_renew_info);
    DaoUpdateTokenRenewInfo(admin_per_account.account, admin_per_account.account, token_renew_info);//admin app没有多套房，都传account即可

    //审计日志记录
    if(admin_per_account.role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN){
        AK_LOG_INFO << "New Office Admin App login success. account=" << admin_per_account.account;
        KafkaNotifyHandler::GetInstance()->PushNewOfficeAdminLoginAuditLogMessage(admin_per_account.account, ctx->remote_ip());
    }

    //获取服务器地址信息
    HttpRespKV kv;
    GenerateServerInfo(RedirectCloudType::REDIRECT_NO_NEED, admin_per_account, user_agent.c_str(), api_version, token_renew_info.token, kv);

    //token相关 组装返回消息体
    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
    kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));

    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    return;
};

HTTPRespCallback ReqAdminServerListHandlerV711 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    float api_version = STOF(ctx->FindRequestHeader("api-version"));
    std::string token = GetCtxHeader(ctx, "x-auth-token");

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    
    csgate::PersonalAccountInfo personal_account_info;
    int ret = csgate::DaoCheckToken(token, personal_account_info, api_version);

    //不能返回app过期，APP未处理。但这部分h5有处理。
    if (csgate::ERR_TOKEN_INVALID == ret || personal_account_info.role != ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        AK_LOG_WARN << "DaoCheckToken Failed!" << " user: " << personal_account_info.account;
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR));
        return;
    }

    HttpRespKV kv;
    //ios标识，特殊处理
    std::string head_ag = GetCtxHeader(ctx, "User-Agent");
    //新版本serverlist接口无需续时token,因此不赋值
    std::string redirect_update_token;
    GenerateServerInfo(RedirectCloudType::REDIRECT_NO_NEED, personal_account_info, head_ag, api_version, redirect_update_token, kv);
    
    AK_LOG_INFO << "admin safe server_list, get server addr info ";

    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   

    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    return; 
};

HTTPRespCallback ReqAdminRefreshTokenHandlerV711 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string token = GetCtxHeader(ctx, "x-auth-token");

    AuthInfo auth_info;
    int ret = GetAuthInfoFromRequestBody(ctx->body().ToString(), auth_info);
   
    if (ret != 0)
    {
        cb("parse http body json error");
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    TokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 != dbinterface::Token::GetTokenInfoByAppToken(token, token_info))
    {
        AK_LOG_WARN << "Token not exist, return refreshtoken err. relogin."; //需要重新登录
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }

    ResidentPerAccount per_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(token_info.app_main_account, per_account)
        || per_account.is_expire
        || per_account.role != ACCOUNT_ROLE_OFFICE_NEW_ADMIN) {
        AK_LOG_WARN << "Check PersonalAccount invalid failed. account=" << token_info.app_main_account;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR)); //需要重新登录
        return;
    }

    AdminAppAuthChecker auth_checker(token_info, auth_info);
    ret = auth_checker.HandleAuthCheck();
    if (ret != ERR_SUCCESS)
    {
        AK_LOG_WARN << "Check token invalid. auth type:" << auth_info.auth_type;
        cb(buildNewErrorHttpMsg(ERR_CODE_REFRESH_TOKENE_RR));
        return;
    }
    
    //获取token信息并更新数据库
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(token_info.app_main_account, token_renew_info);
    csgate::DaoUpdateTokenRenewInfo(token_info.app_main_account, token_info.app_main_account, token_renew_info);

    /* admin app暂不考虑重定向
    RedirectCloudType redirect_type = CheckUserRedirectByAccount(token_info.app_main_account);
    if(redirect_type)
    {
        csgate::UpdateTokenToRedirectServer(token_info.app_main_account, token_renew_info.token, "", redirect_type);
        csgate::UpdateRefreshTokenToRedirectServer(token_info.app_main_account, token_renew_info.refresh_token, redirect_type);
    }
    */

    RespRefreshToken(ret, token_renew_info, cb);
    return;
};

HTTPRespCallback ReqAdminSmsLoginHandlerV711 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string code = ctx->GetQuery("code");
    std::string phone = ctx->GetQuery("phone");
    std::string area_code = ctx->GetQuery("area_code");
    float api_version = STOF(ctx->FindRequestHeader("api-version"));

    phone = GetCaeSarStr(phone);
    TrimString(phone);

    if(!HttpCheckSqlParam(phone))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Phone=" << phone;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    // 校验手机号
    csgate::PersonalAccountInfo personal_account_info;
    int ret = DaoCheckAdminSmsLogin(phone, code, area_code, personal_account_info);

    // 过期和未激活在login_conf接口判断
    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret)
    {
        AK_LOG_WARN << "Check user error, phone:" << phone << " error:" << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    //app类型更新
    std::string uid = personal_account_info.uid;
    const char* tmp_user = ctx->FindRequestHeader("User-Agent");
    UpdateAppSmartType(tmp_user, uid);

    //基本校验通过，更新并返回Token相关信息
    TokenRenewInfo token_renew_info;
    GetTokenRenewInfo(uid, token_renew_info);
    DaoUpdateTokenRenewInfo(uid, uid, token_renew_info);

    // 审计日志
    AK_LOG_INFO << "New Office Admin App login success. account=" << uid;
    KafkaNotifyHandler::GetInstance()->PushNewOfficeAdminLoginAuditLogMessage(uid, ctx->remote_ip());

    // 服务器地址生成
    HttpRespKV kv;
    GenerateServerInfo(RedirectCloudType::REDIRECT_NO_NEED, personal_account_info, tmp_user, api_version, token_renew_info.token, kv);

    //token相关 组装返回消息体
    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
    kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));

    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    return;
};

HTTPRespVerCallbackMap HTTPReqAdminLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V711] = ReqAdminLoginHandlerV711;
    return OMap;
}

HTTPRespVerCallbackMap HTTPReqAdminServerListMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V711] = ReqAdminServerListHandlerV711;
    return OMap;
}

HTTPRespVerCallbackMap HTTPReqAdminRefreshTokenMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V711] = ReqAdminRefreshTokenHandlerV711;
    return OMap;
}

HTTPRespVerCallbackMap HTTPReqAdminSmsLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V711] = ReqAdminSmsLoginHandlerV711;
    return OMap;
}

void HTTPAdminRespMapInit(csgate::HTTPAllRespCallbackMap &OMap)
{
    //v7.1.1新增Admin App
    OMap[ADMIN_LOGIN] = HTTPReqAdminLoginMap();
    OMap[ADMIN_SERVER_LIST] = HTTPReqAdminServerListMap();
    OMap[ADMIN_REF_TOKEN] = HTTPReqAdminRefreshTokenMap();
    OMap[ADMIN_SMS_LOGIN] = HTTPReqAdminSmsLoginMap();
}

}