#ifndef __CSGATE_HTTP_RESP_ADMIN_H__
#define __CSGATE_HTTP_RESP_ADMIN_H__


#include "HttpResp.h"
#include "dbinterface/InsToken.h"

namespace csgate
{
//注册Admin接口处理方法
HTTPRespVerCallbackMap HTTPReqAdminLoginMap();
HTTPRespVerCallbackMap HTTPReqAdminServerListMap();
HTTPRespVerCallbackMap HTTPReqAdminRefreshTokenMap();
HTTPRespVerCallbackMap HTTPReqAdminSmsLoginMap();

//refresh token接口校验方式
int HandleCheckInsUserPassword(const std::string& username, const std::string& passwd, const std::string& token_username);

void HTTPAdminRespMapInit(csgate::HTTPAllRespCallbackMap &OMap);

}

#endif //__CSGATE_HTTP_RESP_ADMIN_H__
