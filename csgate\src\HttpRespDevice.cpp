#include <iostream>
#include <functional>
#include "HttpResp.h"
#include "HttpRespDevice.h"
#include "Dao.h"
#include "ClientMng.h"
#include "ServerMng.h"
#include "CsgateConf.h"
#include "Md5.h"
#include "LogicSrvMng.h"
#include "AkLogging.h"
#include "AES256.h"
#include "json/json.h"
#include "Url.h"
#include "AuditLog.h"
#include "Caesar.h"
#include "util.h"
#include "HttpMsgControl.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "HttpOfficeResp.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket_interface.h"
#include "evpp/rate_limiter/rate_limiter.h"
#include "DevSrvMapMng.h"
#include "AkcsHttpRequest.h"
#include "HttpSmartResp.h"
#include "dbinterface/TmpLoginJpFromScloudLog.h"
#include "HttpRespIns.h"
#include "dbinterface/Token.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "EndUserAppAuthChecker.h"
#include "PmAppAuthChecker.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/ApiUsageInfo.h"

extern CSGATE_CONF gstCSGATEConf; //全局配置信息
extern evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter;
extern AWS_CSGATE_CONF gstAWSConf; //全局配置信息
extern const unsigned int g_ipaddr_check_len; 
extern AWS_CSGATE_CONF gstAWSAucloudConf;
extern AWS_CSGATE_CONF gstASBJConf;
extern AWS_CSGATE_CONF gstEcloud2UcloudConf;



#define CHNAGE_REDIRECT_IP() \
    if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD) \
    {  \
        csmain_ip =  gstAWSConf.csmain_ip;  \
        csmain_ipv6 = gstAWSConf.csmain_ipv6;  \
        csvrtsp_ip = gstAWSConf.csvrtsp_ip;  \
        csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;  \
        ftp_ip = gstAWSConf.ftp_ip;  \
        ftp_ipv6 = gstAWSConf.ftp_ipv6;  \
        pbx_ip = gstAWSConf.pbx_ip;  \
        pbx_ipv6 = gstAWSConf.pbx_ipv6;  \
        webip = gstAWSConf.web_domain; \
        webipv6 = gstAWSConf.web_ipv6; \
    }  \
    else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)  \
    {  \
        csmain_ip =  gstAWSAucloudConf.csmain_ip;  \
        csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;  \
        csvrtsp_ip = gstAWSAucloudConf.csvrtsp_ip;  \
        csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;  \
        ftp_ip = gstAWSAucloudConf.ftp_ip; \
        ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6; \
        pbx_ip = gstAWSAucloudConf.pbx_ip; \
        pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6; \
        webip = gstAWSAucloudConf.web_domain; \
        webipv6 = gstAWSAucloudConf.web_ipv6; \
    }\
    else if(redirect_ret == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)  \
    {  \
        csmain_ip =  gstEcloud2UcloudConf.csmain_ip;  \
        csmain_ipv6 = gstEcloud2UcloudConf.csmain_ipv6;  \
        csvrtsp_ip = gstEcloud2UcloudConf.csvrtsp_ip;  \
        csvrtsp_ipv6 = gstEcloud2UcloudConf.csvrtsp_ipv6;  \
        ftp_ip = gstEcloud2UcloudConf.ftp_ip; \
        ftp_ipv6 = gstEcloud2UcloudConf.ftp_ipv6; \
        pbx_ip = gstEcloud2UcloudConf.pbx_ip; \
        pbx_ipv6 = gstEcloud2UcloudConf.pbx_ipv6; \
        webip = gstEcloud2UcloudConf.web_domain; \
        webipv6 = gstEcloud2UcloudConf.web_ipv6; \
    }    

#define CHNAGE_REDIRECT_DOMAIN() \
        if(redirect_ret == RedirectCloudType::REDIRECT_JCLOUD) \
        {  \
            csmain_domain =  gstAWSConf.csmain_domain;  \
            csmain_ipv6 = gstAWSConf.csmain_ipv6;  \
            csvrtsp_domain = gstAWSConf.csvrtsp_domain;  \
            csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;  \
            ftp_ip = gstAWSConf.ftp_ip;  \
            ftp_ipv6 = gstAWSConf.ftp_ipv6;  \
            pbx_domain = gstAWSConf.pbx_domain;  \
            pbx_ipv6 = gstAWSConf.pbx_ipv6;  \
            webip = gstAWSConf.web_domain; \
            webipv6 = gstAWSConf.web_ipv6; \
        }  \
        else if(redirect_ret == RedirectCloudType::REDIRECT_AUCLOUD)  \
        {  \
            csmain_domain =  gstAWSAucloudConf.csmain_domain;  \
            csmain_ipv6 = gstAWSAucloudConf.csmain_ipv6;  \
            csvrtsp_domain = gstAWSAucloudConf.csvrtsp_domain;  \
            csvrtsp_ipv6 = gstAWSAucloudConf.csvrtsp_ipv6;  \
            ftp_ip = gstAWSAucloudConf.ftp_ip; \
            ftp_ipv6 = gstAWSAucloudConf.ftp_ipv6; \
            pbx_domain = gstAWSAucloudConf.pbx_domain; \
            pbx_ipv6 = gstAWSAucloudConf.pbx_ipv6; \
            webip = gstAWSAucloudConf.web_domain; \
            webipv6 = gstAWSAucloudConf.web_ipv6; \
        }\
        else if(redirect_ret == RedirectCloudType::REDIRECT_ECLOUD_TO_UCLOUD)  \
        {  \
            csmain_domain =  gstEcloud2UcloudConf.csmain_domain;  \
            csmain_ipv6 = gstEcloud2UcloudConf.csmain_ipv6;  \
            csvrtsp_domain = gstEcloud2UcloudConf.csvrtsp_domain;  \
            csvrtsp_ipv6 = gstEcloud2UcloudConf.csvrtsp_ipv6;  \
            ftp_ip = gstEcloud2UcloudConf.ftp_ip; \
            ftp_ipv6 = gstEcloud2UcloudConf.ftp_ipv6; \
            pbx_domain = gstEcloud2UcloudConf.pbx_domain; \
            pbx_ipv6 = gstEcloud2UcloudConf.pbx_ipv6; \
            webip = gstEcloud2UcloudConf.web_domain; \
            webipv6 = gstEcloud2UcloudConf.web_ipv6; \
        }        


namespace csgate
{

//login v30,当前面向设备
//TODO,增加pbx+vsftp的负载均衡策略.
csgate::HTTPRespCallback ReqLoginHandlerV30 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了

    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckUser(user, passwd_md5, 0.0, personal_account_info);

    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Check user error, user:" << user << " error:" << ret;
        cb(buildErrorHttpMsg(ret));
        return;
    }
    else
    {
        //根据负载均衡算法获取到系统的接入服务器,对于设备来说以mac地址来分配
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);

        std::string token;
        std::string uid = personal_account_info.uid;
        std::string main_account = personal_account_info.main_account;
        csgate::GetToken(user, main_account, token);
        if (passwd_md5 != akuvox_encrypt::MD5("akuvox").toStr())
        {
            csgate::DaoUpdateAppLoginStatus(uid);
        }

        std::string csmain_ip = pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;
        csgate::GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_IP()

            AK_LOG_INFO << "login,uid is " << personal_account_info.uid << ",csmain is: " << csmain_ip << ",csvrtsp is:" << csvrtsp_ip;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, webip + ":443"));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6 + ":443"));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));
        cb(buildCommHttpMsg(ret, kv));

        CDevSrvMapMng::Instance()->RegMacPbxIPv4Srv(personal_account_info.uid, pbx_ip);
    }
    return;
};

csgate::HTTPRespCallback ReqLoginHandlerDevNoAuth = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb) {
    int ret = 0;
    int dev_ver_int = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); // 已经做过md5加密了
    const char* dev_ver = ctx->FindRequestHeader("dev-version");

    //新版本的用户名进行了凯撒加密
    if (dev_ver != nullptr)
    {
        dev_ver_int = ATOI(dev_ver);
        if (dev_ver_int >= 6100 && !akuvox_encrypt::AkDeviceCaesarDecry(user))
        {
            AK_LOG_WARN << "AkDeviceCaesarDecry error!";
            return;
        }
    }

    // float ver = 4.6;
    std::string response;
    std::string mac = user;
    std::string user_md5 = akuvox_encrypt::MD5(user).toStr();
    std::string key = user_md5.substr(0, 16);

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    if (dev_ver_int > ATOI(PLATFORM_VER.c_str()))
    {
        AK_LOG_INFO << "dev version invalid, dev: " << dev_ver << ", platform: " << PLATFORM_VER;
        cb(buildErrorHttpMsg(ERR_VERSION_INVALID));
        return;
    }
    else
    {
        // 关闭鉴权（异常）情况下，不返回token
        // std::string token;
        // csgate::GetToken(user, personal_account_info.main_account, token, ver);

        //根据负载均衡算法获取到系统的接入服务器,对于设备来说以mac地址来分配
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(mac);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(mac);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(mac);

        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string csmain_ip = pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string file_server_domain = gstCSGATEConf.file_server;
        std::string file_server_ipv6 = gstCSGATEConf.file_server_ipv6;
        
        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(mac, pbx_ip, pbx_ipv6, pbx_domain);

        // 关闭鉴权（异常）情况下，跳过迁移判断
        // RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        // CHNAGE_REDIRECT_DOMAIN();

        // 获取tls地址
        std::string pbxs_ipv6_addr;
        std::string pbxs_domain_addr;
        std::string csvrtsps_ipv6_addr;
        std::string csvrtsps_domain_addr;
        csgate::GetTlsAddress({ pbx_domain, pbx_ipv6 }, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
        csgate::GetTlsAddress({ csvrtsp_domain, csvrtsp_ipv6 }, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

        AK_LOG_INFO << "ReqLoginHandlerDevNoAuth: mac=" << mac << ", dev_ver_int=" << dev_ver_int
            << ", csmain=" << csmain_ip << ", csvrtsp=" << csvrtsp_domain
            << ", pbx_domain=" << pbx_domain << ", pbx_ipv6=" << pbx_ipv6
            << ", ftp_domain=" << ftp_ip << ", ftp_ipv6=" << ftp_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, webip + ":443"));
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6 + ":443"));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6));
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));
        // kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));

        if(dev_ver_int > ATOI(V62Dev.c_str())){
            kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));
            kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));
            kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));
            kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr));
            kv.insert(std::map<std::string, std::string>::value_type(FILE_SERVER, file_server_domain));
            kv.insert(std::map<std::string, std::string>::value_type(FILE_SERVER_IPV6, file_server_ipv6));
        }

        response = buildCommHttpMsg(ret, kv);
    }

    // 4.6~6.2版本 返回结果需要加密  (注意：返回长度超过1024会导致dclient解析失败无法连接!!!)
    if(4600 <= dev_ver_int && dev_ver_int <= 6200){
        std::string encrypt_response;
        AESEncryptRespone(response, key, encrypt_response);
        cb(encrypt_response);
    }
    else{
        cb(response);
    }
    
    return;
};

//login 面向设备,4.6报文加密接口
csgate::HTTPRespCallback ReqLoginHandlerDevV46_61 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    const char* dev_ver = ctx->FindRequestHeader("dev-version");   
    //新版本的用户名进行了凯撒加密
    if(dev_ver != nullptr && ATOI(dev_ver) >= 6100)
    {
        if(!akuvox_encrypt::AkDeviceCaesarDecry(user))
        {
            AK_LOG_WARN << "AkDeviceCaesarDecry error!";
            return;
        }
    }
    
    float ver = 4.6;
    std::string encrypt_resp;
    std::string user_md5 = akuvox_encrypt::MD5(user).toStr();
    std::string key = user_md5.substr(0, 16);
    
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    if(dev_ver != nullptr && ATOI(dev_ver) > ATOI(PLATFORM_VER.c_str()))
    {
        AK_LOG_INFO << "dev version invalid, dev: " << dev_ver << ", platform: " << PLATFORM_VER;
        AESEncryptRespone(buildErrorHttpMsg(ERR_VERSION_INVALID), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }

    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckDevLogin(user, passwd_md5, ver, personal_account_info);
    if (csgate::ERR_SUCCESS != ret)
    {   
        AK_LOG_WARN << "Check user error, user:" << user << " error:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    else
    {
        //USER_CONF user_conf = {0};
        //csgate::DaoGetUserConf(user, user_conf);
        //根据负载均衡算法获取到系统的接入服务器,对于设备来说以mac地址来分配
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);

        std::string token;
        csgate::GetToken(user, personal_account_info.main_account ,token, ver);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;              
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_IP();

        AK_LOG_INFO << "login,mac is " << personal_account_info.uid << ",csmain is: " << csmain_ip << ", csvrtsp is:" << csvrtsp_ip
                    << ", pbx_ip is " << pbx_ip << ", pbx_ipv6 is " << pbx_ipv6
                    << ", ftp_ip is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
      
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  webip+":443"));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6+":443"));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        // 注意：返回长度超过1024会导致dclient解析失败无法连接!!!
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);

        CDevSrvMapMng::Instance()->RegMacPbxIPv4Srv(personal_account_info.uid, pbx_ip);
    }
    return;
};

csgate::HTTPRespCallback ReqLoginHandlerDevV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); //已经做过md5加密了
    const char* dev_ver = ctx->FindRequestHeader("dev-version");
    //新版本的用户名进行了凯撒加密
    if(dev_ver != nullptr && ATOI(dev_ver) >= 6100)
    {
        if(!akuvox_encrypt::AkDeviceCaesarDecry(user))
        {
            AK_LOG_WARN << "AkDeviceCaesarDecry error!";
            return;
        }
    }

    float ver = 4.6;
    std::string user_md5 = akuvox_encrypt::MD5(user).toStr();
    std::string key = user_md5.substr(0, 16);
    std::string encrypt_resp;

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    if(dev_ver != nullptr && ATOI(dev_ver) > ATOI(PLATFORM_VER.c_str()))
    {
        AK_LOG_WARN << "dev version invalid, dev: " << dev_ver << ", platform: " << PLATFORM_VER;
        AESEncryptRespone(buildErrorHttpMsg(ERR_VERSION_INVALID), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
        
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckDevLogin(user, passwd_md5, ver, personal_account_info);
    
    if (csgate::ERR_SUCCESS != ret)
    {   
        AK_LOG_WARN << "Dev: " << user << ", check error, error number:" << ret;
        AESEncryptRespone(buildErrorHttpMsg(ret), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    else
    {
        //USER_CONF user_conf = {0};
        //csgate::DaoGetUserConf(user, user_conf);
        //根据负载均衡算法获取到系统的接入服务器,对于设备来说以mac地址来分配
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);

        std::string token;
        csgate::GetToken(user, personal_account_info.main_account, token, ver);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;        
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;          
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_DOMAIN();
        
        AK_LOG_INFO << "login,mac is " << personal_account_info.uid << ",csmain is: " << csmain_domain << ", csvrtsp is:" << csvrtsp_domain
                    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6
                    << ", ftp_ip is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
                    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  webip+":443"));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6+":443"));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        // 注意：返回长度超过1024会导致dclient解析失败无法连接!!!
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqLoginHandlerDev = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string user = ctx->GetQuery("user");
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    if(!akuvox_encrypt::AkDeviceCaesarDecry(user))
    {
        AK_LOG_WARN << "AkDeviceCaesarDecry error!";
        return;
    }

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    float ver = 6.5;
    csgate::PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckDevLogin(user, passwd_md5, ver, personal_account_info);
    
    if (csgate::ERR_SUCCESS != ret)
    {   
        AK_LOG_WARN << "Dev: " << user << ", check error, error number:" << ret;
        cb(buildErrorHttpMsg(ret));
        return;
    }
    else
    {
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(personal_account_info.uid);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(personal_account_info.uid);

        std::string token;
        csgate::GetToken(user, personal_account_info.main_account, token, ver);

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);
        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;     
        std::string file_server_domain = gstCSGATEConf.file_server;
        std::string file_server_ipv6 = gstCSGATEConf.file_server_ipv6;
        std::string voice_assistant_server = gstCSGATEConf.voice_assistant_server_domain;

        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_DOMAIN();
        
        // 获取tls地址
        std::string pbxs_ipv6_addr;
        std::string pbxs_domain_addr;
        std::string csvrtsps_ipv6_addr;
        std::string csvrtsps_domain_addr;
        csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
        csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);

        AK_LOG_INFO << "login,mac is " << personal_account_info.uid 
                    << ", csmain is: " << csmain_domain << ", csvrtsp is:" << csvrtsp_domain
                    << ", pbx_domain is " << pbx_domain << ", pbx_ipv6 is " << pbx_ipv6 
                    << ", ftp_domain is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;
                    
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  webip+":443"));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6+":443"));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr)); 
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(FILE_SERVER, file_server_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(FILE_SERVER_IPV6, file_server_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(VOICE_ASSISTANT_SERVER, voice_assistant_server)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));   
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 

        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

//getApiServer
csgate::HTTPRespCallback ReqGetApiSerHandlerV30 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "ReqGetApiSerHandlerV30 ,api server is: " << gstCSGATEConf.api_server;

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(API_SERVER, gstCSGATEConf.api_server));    
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};


//accessserver 面向设备，4.6加密
csgate::HTTPRespCallback ReqAccessSerHandlerDevV46 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;//uid or mac
    PersonalAccountInfo personal_account_info;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.6);
    user = personal_account_info.account;
    std::string user_md5 = akuvox_encrypt::MD5(user).toStr();
    std::string key = user_md5.substr(0, 16);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "token invalid!";
        std::string encrypt_resp;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    else
    {
        user = personal_account_info.account;

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspSrv(user);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(user);

        std::string csmain_ip =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_ip = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_access_ser46.first;
        std::string ftp_ipv6 = pair_access_ser46.second;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;        
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_IP();
        
        AK_LOG_INFO << "ReqAccessSerHandlerDevV46, user is " << user << ",csmain is: " << csmain_ip << ",csvrtsp is:" <<
                    csvrtsp_ip << ",pbx_ip is " << pbx_ip << ",pbx_ipv6 is " << pbx_ipv6;
            
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  webip+":443"));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6,  webipv6+":443"));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER)); 
        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqAccessSerHandlerDevV62 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = ctx->GetQuery("token");
    std::string user;//uid or mac
    PersonalAccountInfo personal_account_info;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    ret = csgate::DaoCheckToken(token, personal_account_info, 4.6);
    user = personal_account_info.account;
    std::string user_md5 = akuvox_encrypt::MD5(user).toStr();
    std::string key = user_md5.substr(0, 16);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token invalid!";
        std::string encrypt_resp;
        AESEncryptRespone(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID), key, encrypt_resp);
        cb(encrypt_resp);
        return;
    }
    else
    {
        user = personal_account_info.account;

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        //根据负载均衡算法获取到系统的接入服务器
        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(user);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(user);

        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;        
        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_DOMAIN();
        
        AK_LOG_INFO << "ReqAccessSerHandlerDevV46, user is " << user << ",csmain is: " << csmain_domain 
                    <<  ",csvrtsp is:"  << csvrtsp_domain << ",pbx_domain is " << pbx_domain << ",pbx_ipv6 is " << pbx_ipv6
                    << ", ftp_domain is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  webip+":443"));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6+":443"));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER)); 
           
        std::string encrypt_resp;
        AESEncryptRespone(buildCommHttpMsg(ret, kv), key, encrypt_resp);
        cb(encrypt_resp);
    }
    return;
};

csgate::HTTPRespCallback ReqAccessSerHandlerDev = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    
    std::string token = ctx->GetQuery("token");
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    
    PersonalAccountInfo personal_account_info;
    ret = csgate::DaoCheckToken(token, personal_account_info, 6.5);

    if (csgate::ERR_SUCCESS != ret)
    {
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }
    else
    {
        std::string user;
        user = personal_account_info.account;

        std::string pbx_ip;
        std::string pbx_ipv6;
        std::string pbx_domain;
        GetOpsServer(personal_account_info.uid, pbx_ip, pbx_ipv6, pbx_domain);

        std::pair<std::string, std::string> pair_access_ser46 = CLogicSrvMng::Instance()->GetAccDomainSrv(user);
        std::pair<std::string, std::string> pair_rtsp_ser46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(user);
        std::pair<std::string, std::string> pair_ftp_ser46 = CLogicSrvMng::Instance()->GetFtpDomainSrv(user);
                
        std::string csmain_domain =  pair_access_ser46.first;
        std::string csmain_ipv6 = pair_access_ser46.second;
        std::string csvrtsp_domain = pair_rtsp_ser46.first;
        std::string csvrtsp_ipv6 = pair_rtsp_ser46.second;
        std::string ftp_ip = pair_ftp_ser46.first;
        std::string ftp_ipv6 = pair_ftp_ser46.second;
        std::string file_server_domain = gstCSGATEConf.file_server;
        std::string file_server_ipv6 = gstCSGATEConf.file_server_ipv6;
        std::string webip = gstCSGATEConf.web_ip;
        std::string webipv6 = gstCSGATEConf.web_ipv6;
        std::string voice_assistant_server = gstCSGATEConf.voice_assistant_server_domain;

        RedirectCloudType redirect_ret = csgate::DaoCheckUserRedirect(personal_account_info);
        CHNAGE_REDIRECT_DOMAIN();


        // 获取tls地址
        std::string pbxs_ipv6_addr;
        std::string pbxs_domain_addr;
        std::string csvrtsps_ipv6_addr;
        std::string csvrtsps_domain_addr;
        csgate::GetTlsAddress({pbx_domain, pbx_ipv6}, kSipsPort, pbxs_domain_addr, pbxs_ipv6_addr);
        csgate::GetTlsAddress({csvrtsp_domain, csvrtsp_ipv6}, kRtspsPort, csvrtsps_domain_addr, csvrtsps_ipv6_addr);
        
        AK_LOG_INFO << "ReqAccessSerHandlerDevV46, user is " << user << ",csmain is: " << csmain_domain 
                    << ", csvrtsp is:" << csvrtsp_domain << ",pbx_domain is " << pbx_domain << ",pbx_ipv6 is " << pbx_ipv6
                    << ", ftp_domain is " << ftp_ip << ", ftp_ipv6 is " << ftp_ipv6;

        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER,  webip+":443"));    
        kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, webipv6+":443"));   
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER, csvrtsps_domain_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(VRTSPS_SERVER_IPV6, csvrtsps_ipv6_addr)); 
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER, pbxs_domain_addr));
        kv.insert(std::map<std::string, std::string>::value_type(PBXS_SERVER_IPV6, pbxs_ipv6_addr));
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER, ftp_ip));    
        kv.insert(std::map<std::string, std::string>::value_type(FTP_SERVER_IPV6, ftp_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(FILE_SERVER, file_server_domain));    
        kv.insert(std::map<std::string, std::string>::value_type(FILE_SERVER_IPV6, file_server_ipv6)); 
        kv.insert(std::map<std::string, std::string>::value_type(VOICE_ASSISTANT_SERVER, voice_assistant_server)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER)); 
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};



//设备请求网页服务器，这个和app不一样，app支持重定向不用赋值端口，设备要赋值端口
csgate::HTTPRespCallback ReqRegisterHandlerVDevice = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{    
    std::string web_addr = gstCSGATEConf.web_ip;
    std::string web_addr_ipv6 = gstCSGATEConf.web_ipv6;
    
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr+":443"));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_addr_ipv6+":443"));   
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};


}

