#ifndef __CSGATE_HTTP_RESP_DEV_H__
#define __CSGATE_HTTP_RESP_DEV_H__
#include <functional>
#include <evpp/http/context.h>
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/AwsRedirect.h"
#include "dbinterface/Token.h"
#include "HttpMsgControl.h"
#include "Dao.h"
#include "AppAuthChecker.h"
#include "AwsRedirect.h"
#include "HttpResp.h"


namespace csgate
{


extern HTTPRespCallback ReqLoginHandlerV30;
//login 面向设备,4.6报文加密接口
extern HTTPRespCallback ReqLoginHandlerDevV46_61;
extern HTTPRespCallback ReqLoginHandlerDevV62;
extern HTTPRespCallback ReqLoginHandlerDev;
extern HTTPRespCallback ReqGetApiSerHandlerV30;
extern HTTPRespCallback ReqLoginHandlerDevNoAuth;

extern HTTPRespCallback ReqAccessSerHandlerDevV46;
extern HTTPRespCallback ReqAccessSerHandlerDevV62;
extern HTTPRespCallback ReqAccessSerHandlerDev;
extern HTTPRespCallback ReqRegisterHandlerVDevice;








}
#endif //__CSGATE_HTTP_RESP_H__
