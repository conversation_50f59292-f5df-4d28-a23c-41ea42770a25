#include "HttpRespIns.h"
#include "HttpMsgControl.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/Account.h"
#include "json/json.h"
#include "Caesar.h"
#include "Dao.h"
#include "CsgateConf.h"
#include "ServerMng.h"
#include "Url.h"
#include "dbinterface/InsToken.h"
#include "util.h"
#include "Md5.h"
#include "AkLogging.h"
#include "HttpResp.h"
#include "InsAppAuthChecker.h"
#include "dbinterface/TwoFactorAuthIDCode.h"
#include "AppTwoFactorAuth.h"

const std::string http_head = "https://"; //6.7后续新增接口 APP服务器地址要求返回协议头
extern CSGATE_CONF gstCSGATEConf; //全局配置信息

namespace csgate
{

//6.7 ins APP login处理
csgate::HTTPRespCallback ReqInstallerLoginHandlerV67 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = -1;
    
    std::string username = akuvox_encrypt::UrlDecode(ctx->GetQuery("user"));
    std::string passwd_md5 = ctx->GetQuery("passwd"); 

    //对user字段进行凯撒解密获取account_user
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    UserInfoAccount ins_account_info;
    memset(&ins_account_info, 0, sizeof(UserInfoAccount));
    
    ret = csgate::DaoCheckInsAppLogin(username, passwd_md5, ins_account_info);
    //账号密码以及App Status的校验
    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret || csgate::ERR_INSTALLER_APP_STATUS_CLOSED == ret)
    {
        //校验失败,构建错误消息并返回
        AK_LOG_WARN << "Ins APP login failed, ret value:" << ret;
        cb(buildErrorHttpMsg(ret));
        return;
    }
    else
    {
        //校验成功

        //生成并更新token
        std::string token;
        csgate::GetInsToken(username, token, ins_account_info.uuid);

        //消息构建
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));	
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token));
        //结果返回
        cb(buildCommHttpMsg(ret, kv));
    }
    return;
};

//安全整改版本 ins App login处理
csgate::HTTPRespCallback ReqInstallerSafeLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = -1;
    std::string username = akuvox_encrypt::UrlDecode(ctx->GetQuery("user"));
    std::string passwd_md5 = ctx->GetQuery("passwd"); 
    std::string id_code = ctx->GetQuery("id_code");
    float api_version = STOF(ctx->FindRequestHeader("api-version"));

    //对user字段进行凯撒解密获取account_user
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;

    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);
    UserInfoAccount ins_account_info;
    memset(&ins_account_info, 0, sizeof(UserInfoAccount));
    ret = csgate::DaoCheckInsAppLogin(username, passwd_md5, ins_account_info, api_version, id_code);
    //账号密码以及App Status的校验
    if (csgate::ERR_USER_NOT_EXIT == ret || csgate::ERR_PASSWD_INVALID == ret)
    {
        //校验失败,构建错误消息并返回
        AK_LOG_WARN << "Ins APP login failed, user info incorrect";
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }
    else if (csgate::ERR_INSTALLER_APP_STATUS_CLOSED == ret)
    {
        //校验失败,构建错误消息并返回
        AK_LOG_WARN << "Ins APP login failed, app status is closed:" << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_INS_APP_STATUS_CLOSED));
        return;
    }
    else if (csgate::ERR_NEED_TWO_FACTOR_AUTH == ret)
    {
        std::string web_addr, web_ipv6, rest_addr, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6;
        GetWebServer(web_addr, rest_addr, web_ipv6, rest_ipv6, rest_ssl_addr, rest_ssl_ipv6);
        //需要双重认证
        AK_LOG_INFO << "api_version >= 7.1, ins need check two factor auth, user: " << username ;
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));	
        std::string temp_token = AppTwoFactorAuth::GenerateTempToken(username);
        kv.insert(std::map<std::string, std::string>::value_type(IS_NEED_TWO_FACTOR_AUTH, "1"));
        kv.insert(std::map<std::string, std::string>::value_type(TWO_FACTOR_AUTH_TEMP_TOKEN, temp_token));
        //双重认证发送短信验证码，需要用到rest地址
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
        kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
        kv.insert(std::map<std::string, std::string>::value_type(EMAIL, ins_account_info.email));
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
        return;
    }
    else
    {
        TokenRenewInfo token_renew_info;
        csgate::GetTokenRenewInfo(ins_account_info.account, token_renew_info);
        csgate::UpdateInsTokenRenewInfo(ins_account_info.uuid, token_renew_info);

        //消息构建
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));	
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
        kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
        //结果返回
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
};

csgate::HTTPRespCallback ReqInstallerVerifyCodeAndLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string http_body = ctx->body().ToString();    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildNewErrorHttpMsg(ERR_CODE_HTTP_BODY_INVALID));
        return;
    }
    std::string username;
    std::string verify_code;
    if (root.isMember("user") && root.isMember("verify_code"))
    {
        username = root["user"].asString();
        verify_code = root["verify_code"].asString();
    }
    else
    {
        AK_LOG_WARN << "Missing 'verify_code' field in JSON or Missing 'user' field in JSON.";
        cb(buildNewErrorHttpMsg(ERR_CODE_HTTP_BODY_INVALID));
        return;
    }
    //检查邮箱验证码是否正确
    if(!AppTwoFactorAuth::IsVerifyCodeCorrect(verify_code, username))
    {
        AK_LOG_INFO << "check verify code failed, user:" << username ;
        cb(buildNewErrorHttpMsg(ERR_CODE_APP_VERIFY_CODE_FAILED));
        return;
    }
    AK_LOG_INFO << "check verify code success, user:" << username ;
    
    //生成IDCode,并记录数据库
    std::string id_code = GetNbitRandomString(ID_CODE_LENGTH);
    UserInfoAccount ins_account_info;
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(username, ins_account_info))
    {
       dbinterface::TwoFactorAuthIDCode::InsertTwoFactorAuthIDCodeByAccountUserInfoUUID(ins_account_info.uuid, id_code);
       AK_LOG_INFO << "record idcode success, ins_account_info_uuid: " << ins_account_info.uuid << " id_code: " << id_code ;
    }
    else
    {
        AK_LOG_WARN << "Ins APP verify code and login failed, user info incorrect";
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }
    TokenRenewInfo token_renew_info;
    csgate::GetTokenRenewInfo(ins_account_info.account, token_renew_info);
    csgate::UpdateInsTokenRenewInfo(ins_account_info.uuid, token_renew_info);

    //消息构建
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));	
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token_renew_info.token));
    kv.insert(std::map<std::string, std::string>::value_type(TOKEN_VALID, std::to_string(token_renew_info.valid_time)));
    kv.insert(std::map<std::string, std::string>::value_type(REFRESH_TOKEN, token_renew_info.refresh_token));
    kv.insert(std::map<std::string, std::string>::value_type(TWO_FACTOR_AUTH_IDCODE, id_code));
    //结果返回
    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    
};

//6.7 ins APP serverList处理
csgate::HTTPRespCallback ReqInstallerServerListHandlerV67 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string http_body = ctx->body().ToString();    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildErrorHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID));
        return;
    }

    std::string token = root["token"].asString();
    std::string passwd = root["passwd"].asString();
    std::string user = root["user"].asString();

    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", user.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    user = user_tmp;
    ctx->AddResponseHeader("platform_ver", PLATFORM_VER);

    //token刷新续时
    ret = csgate::DaoInsTokenContinuation(user, passwd, token);

    //Todo:ret判断场景增加
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Token Continuation Failed!" << " error: " << ret << " user:" << user << " passwd: " << passwd;
        cb(buildErrorHttpMsg(ret));
        return;
    }

    //token校验
    ret = csgate::DaoCheckInsToken(token, user);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Check Token Failed!" << "error: " << ret << " user:" << user;
        cb(buildErrorHttpMsg(ret));
        return;
    }
    //查询对应Account表中Installer的id
    
    int id = 0;
    std::string ins_uuid;
    ret = DaoGetAccountIDAndUUID(user, id, ins_uuid);
    if(ret < 0) 
    {
        AK_LOG_WARN << "Query Installer failed, user:" << user;
        cb(buildErrorHttpMsg(ERR_USER_NOT_EXIT)); 
        return;
    }
    std::string ins_id = std::to_string(id);

    //服务器列表回复
    std::string web_addr, web_ipv6;
    web_ipv6 = http_head + gstCSGATEConf.web_ipv6;
    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = http_head + gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = http_head + gstCSGATEConf.web_ip;
    }
    //消息构建
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));	
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));
    kv.insert(std::map<std::string, std::string>::value_type(INSTALLER_ID, ins_id));
    kv.insert(std::map<std::string, std::string>::value_type(INSTALLER_UUID, ins_uuid));
    kv.insert(std::map<std::string, std::string>::value_type(INSTALLER_USERNAME, user)); 

    //结果返回
    cb(buildCommHttpMsg(ret, kv));
    return;
};

csgate::HTTPRespCallback ReqInstallerSafeServerListHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string token = GetCtxHeader(ctx, "x-auth-token");
    std::string username;
    ret = DaoGetInsAccountByToken(token, username);
    if (csgate::ERR_SUCCESS != ret)
    {
        AK_LOG_WARN << "Check Token Failed!" << "error: " << ret;
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR));
        return;
    }

    int ins_account_id = 0;
    std::string ins_account_uuid;
    ret = DaoGetAccountIDAndUUID(username, ins_account_id, ins_account_uuid);
    if(ret < 0) 
    {
        AK_LOG_WARN << "Query Installer failed, username:" << username;
        cb(buildNewErrorHttpMsg(ERR_CODE_TOKEN_ERR)); 
        return;
    }

    //服务器列表回复
    std::string web_addr, web_ipv6, web_backend_server;
    web_ipv6 = http_head + gstCSGATEConf.web_ipv6;
    if (strlen(gstCSGATEConf.web_domain_name) > 2)
    {
        web_addr = http_head + gstCSGATEConf.web_domain_name;
    }
    else
    {
        web_addr = http_head + gstCSGATEConf.web_ip;
    }
    web_backend_server = http_head + gstCSGATEConf.web_backend_domain;
    //消息构建
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));	
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));
    kv.insert(std::map<std::string, std::string>::value_type(WEB_BACKEND_SERVER, web_backend_server));
    kv.insert(std::map<std::string, std::string>::value_type(INSTALLER_ID, std::to_string(ins_account_id)));
    kv.insert(std::map<std::string, std::string>::value_type(INSTALLER_UUID, ins_account_uuid));
    kv.insert(std::map<std::string, std::string>::value_type(INSTALLER_USERNAME, username)); 

    //结果返回
    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    return;
};


int HandleCheckInsUserPassword(const std::string& username, const std::string& passwd, const std::string& token_username)
{
    std::string user;
    //凯撒解密
    char user_tmp[64];
    snprintf(user_tmp, sizeof(user_tmp), "%s", username.c_str());
    akuvox_encrypt::CaesarDecry(user_tmp);
    user = user_tmp;

    if (!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << user;
        return ERR_PASSWD_INVALID; 
    }
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        //密码错误直接return
        if (passwd != akuvox_encrypt::MD5(account_user_info.passwd).toStr())
        {
            AK_LOG_WARN << "continuation error, ins app passwd error, login_account:" << user;
            return ERR_PASSWD_INVALID;
        }
    }
    else
    {
        AK_LOG_WARN << "login failed, user not exist, login_account:" << user;
        return ERR_USER_NOT_EXIT; 
    }
    if (account_user_info.account != token_username)
    {
        return ERR_TOKEN_INVALID;
    }

    return ERR_SUCCESS;
}

csgate::HTTPRespVerCallbackMap HTTPReqInstallerLoginMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V67] = ReqInstallerLoginHandlerV67;
    OMap[V68] = ReqInstallerSafeLoginHandler;
    OMap[V71] = ReqInstallerSafeLoginHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPReqInstallerServerListMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V67] = ReqInstallerServerListHandlerV67;
    OMap[V68] = ReqInstallerSafeServerListHandler;
    return OMap;
}

csgate::HTTPRespVerCallbackMap HTTPInstallerTwoFactorAuthMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V71] = ReqInstallerVerifyCodeAndLoginHandler;
    return OMap;
}

void HTTPInsRespMapInit(csgate::HTTPAllRespCallbackMap &OMap)
{
    //6.7
    OMap[INSTALLER_LOGIN] = HTTPReqInstallerLoginMap();
    OMap[INSTALLER_SERVER_LIST] = HTTPReqInstallerServerListMap();
    //7.1
    OMap[INS_VERIFY_CODE] = HTTPInstallerTwoFactorAuthMap();
}


}
