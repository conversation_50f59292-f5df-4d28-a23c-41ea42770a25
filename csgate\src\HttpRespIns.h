#ifndef __CSGATE_HTTP_RESP_INS_H__
#define __CSGATE_HTTP_RESP_INS_H__


#include "HttpResp.h"
#include "dbinterface/InsToken.h"

namespace csgate
{
//注册Installer接口处理方法
csgate::HTTPRespVerCallbackMap HTTPReqInstallerLoginMap();
csgate::HTTPRespVerCallbackMap HTTPReqInstallerServerListMap();
csgate::HTTPRespVerCallbackMap HTTPInstallerTwoFactorAuthMap();

//refresh token接口校验方式
int HandleCheckInsUserPassword(const std::string& username, const std::string& passwd, const std::string& token_username);

void HTTPInsRespMapInit(csgate::HTTPAllRespCallbackMap &OMap);
}

#endif //__CSGATE_HTTP_RESP_INS_H__
