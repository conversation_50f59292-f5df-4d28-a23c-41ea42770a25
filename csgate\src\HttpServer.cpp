#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "HttpResp.h"
#include "CsgateConf.h"
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "AkcsMonitor.h"
#include "Md5.h"
#include "evpp/rate_limiter/rate_limiter_interface.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "HttpRefreshToken.h"
#include "HttpSmartHomeLock.h"
#include "HttpPmVerifyCodeAndLogin.h"
#include "LogicSrvMng.h"
#include "HttpServerMaintenance.h"
#include "HttpRespAdmin.h"
#include "HttpRespIns.h"
#include "MetricService.h"

//全局变量
static csgate::HTTPAllRespCallbackMap httpRespCbs;
extern CSGATE_CONF gstCSGATEConf;
extern evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter;

const int TIME_OUT = 1000;
const char *RESPONSE_OF_TIME_OUT = "Get token failed cause of server is busy.Please try again.";

//added by chenyc, 2019-11-25,防止攻击的回调函数
//两个步骤:1、加入系统的iptables黑名单;2、加入延迟队列,一段时间后将该ip放出来,避免由于ip动态修改后,正常的用户被误判为攻击源
void HttpAttackedCallback(const std::string& bussiness, const std::string& key)
{
    LOG_WARN << "there is one attack happens, iptables input drop, ip is " << key;
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("csgate", key);
    return;
}

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if (IsInLocalNetSegment(ctx->remote_ip()))
    {
        const char* header_forward = ctx->FindRequestHeader(FORWARD_IP);
        if (header_forward != nullptr)
        {
            LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", X-Forwarded-For:" << header_forward;
            AKCS::Singleton<BussinessLimit>::instance().AddBussiness(csgate::CSGATE_HTTP_BUSSINESS, header_forward);
        }
    }
    else
    {
        LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();
        //chenyc 2019-11-25,记录出错的次数,达到一定频率的时候,通过iptables加入黑名单. eg：remote_ip is ************ remote_ip是ipv4的形式
        AKCS::Singleton<BussinessLimit>::instance().AddBussiness(csgate::CSGATE_HTTP_BUSSINESS, ctx->remote_ip());
    }

    cb("http req route is not define");
}

/*
typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
void RateLimit(const evpp::http::HTTPSendResponseCallback &cb, const HTTPRespCallback &resp_cb, const evpp::http::ContextPtr& ctx)
{
    if (gstCSGATEConf.limit_switch == evpp::rate_limiter::NO_LIMIT)
    {
        resp_cb(ctx, cb);
        return;
    }

    bool result = g_rate_limiter->try_aquire(1, TIME_OUT);
    if (false == result)
    {
        AK_LOG_WARN << RESPONSE_OF_TIME_OUT << ";Rate is " << g_rate_limiter->get_rate() << ";time_out is " << TIME_OUT << " milisecond.";
        //AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csgate", RESPONSE_OF_TIME_OUT, AKCS_MONITOR_ALARM_API_RATE_LIMIT);
        cb(RESPONSE_OF_TIME_OUT);
        return;
    }

    //调用回调函数
    resp_cb(ctx, cb);
    return ;
}
*/
void HttpReqEchoCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    std::string name = ctx->GetQuery("user");
    if (name.length() == 0)
    {
        LOG_WARN << "http req name is null";
        cb("http req name is null");
        return;
    }
    const char * header_version = ctx->FindRequestHeader(API_VERSION);
    if (header_version == nullptr)
    {
        LOG_WARN << "http req head 'api-version' is null";
        cb("http req head 'api-version' is null");
        return;
    }
    auto http_resp_echo_map = httpRespCbs[csgate::ECHO];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(header_version);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << header_version << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    it->second(ctx, cb);
	return;
}
void HttpReqCsgateStatusCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    cb("csgate is ok");
    return;
}

void HttpReqLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* tmp_user = ctx->FindRequestHeader("user-agent");
    if(nullptr != tmp_user)
    {
        std::string user_agent = tmp_user;
        
        if(user_agent == "mysmart") //本地只能服务器配套APP 进行拦截
        {
        #if 0
            LOG_WARN << "mysmart login";
            
            std::string user = ctx->GetQuery("user");
            std::string md5_user = akuvox_encrypt::MD5(user).toStr();
            std::string key = md5_user.substr(0, 16);
            
            std::stringstream oss;
            oss << "{" <<  "\n"
                << RESULT << ": " << csgate::ERR_USER_NOT_EXIT << ",\n"
                << MESSAGE << ": " << "\"Your account is not exist.\"" << "\n"
                << "}" << "\n";
            
            std::string encrypt_resp;
            csgate::AESEncryptRespone(oss.str(), key, encrypt_resp);
            ctx->AddResponseHeader("platform_ver", csgate::PLATFORM_VER);
            cb(encrypt_resp);
            return;
        #endif
        }
        
    }
    
    bool is_dev = false;
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        is_dev = true;
        head = ctx->FindRequestHeader("dev-version");    //dclient版本，4600代表4.6，区分于app
        if (nullptr == head)
        {
            head = "3.0";       //4.6之前的dclient版本，不带api-version和dev-version请求头
            is_dev = false;     //不能通过请求头区分设备和用户
        }
        else if (ATOI(head) > ATOI(CURRENT_DEV_VERSION))
        {
            head = CURRENT_DEV_VERSION;
            is_dev = true;
        }
    }
    else if (STOF(head) > STOF(CURRENT_API_VERSION))   //新app兼容旧平台,防止新app的"api-version"，已经是新版本了,平台未升级时，新app无法使用的问题.
    {
        head = CURRENT_API_VERSION;
    }

    LOG_INFO << "version:"<< head << ". http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    auto http_resp_echo_map = httpRespCbs[csgate::LOGIN];

    // 请求的是设备并且关闭鉴权
    if (is_dev && gstCSGATEConf.device_login_auth_switch == 0)
    {
        http_resp_echo_map["DevNoAuth"](ctx, cb);
        return;
    }

    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it == http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqPmLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "6.6";    
    }
    if (STOF(head) > STOF(CURRENT_API_VERSION))   //新app兼容旧平台,防止新app的"api-version"，已经是新版本了,平台未升级时，新app无法使用的问题.
    {
        head = CURRENT_API_VERSION;
    }
    
    LOG_INFO << "version:"<< head << ". http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    auto http_resp_echo_map = httpRespCbs[csgate::PM_LOGIN];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqSmsLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{   
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "5.5";    
    }
    if (STOF(head) > STOF(CURRENT_API_VERSION))   //新app兼容旧平台,防止新app的"api-version"，已经是新版本了,平台未升级时，新app无法使用的问题.
    {
        head = CURRENT_API_VERSION;
    }
    
    LOG_INFO << "version:"<< head << ". http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    auto http_resp_echo_map = httpRespCbs[csgate::SMS_LOGIN];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}


void HttpReqGetApiServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "3.0";
    }

    auto http_resp_echo_map = httpRespCbs[csgate::HTTP_API_SERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return;
}

//根据负载均衡算法,分配接入服务器
void HttpReqAccServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = ctx->FindRequestHeader("dev-version");    //dclient版本，4600代表4.6，区分于app
        if (nullptr == head)
        {
            head = "3.0";    //4.6之前的dclient版本
        }
        else if (ATOI(head) > ATOI(CURRENT_DEV_VERSION))
        {
            head = CURRENT_DEV_VERSION;
        }
    }
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << " version:" << head << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    //const std::string strApiVersion = ctx->FindRequestHeader("api-version");
    auto http_resp_echo_map = httpRespCbs[csgate::ACCESSSERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

//获取接入服务器列表,运维接口
void HttpReqAccServerListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //LOG_WARN << "http req head 'api-version' is null";
        //cb("http req head 'api-version' is null");
        //return;
        head = "3.1";
    }
    //const std::string strApiVersion = ctx->FindRequestHeader("api-version");
    auto http_resp_map = httpRespCbs[csgate::ACCESSSERVER_LIST];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_map.find(head);
    if (it ==  http_resp_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

//获取接入服务器列表,运维接口
void HttpReqRtspServerListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //LOG_WARN << "http req head 'api-version' is null";
        //cb("http req head 'api-version' is null");
        //return;
        head = "3.1";
    }
    //const std::string strApiVersion = ctx->FindRequestHeader("api-version");
    auto http_resp_map = httpRespCbs[csgate::VRTSPSERVER_LIST];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_map.find(head);
    if (it ==  http_resp_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

//根据负载均衡算法,分配接入服务器
void HttpReqServersListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.0";
    }
    LOG_INFO << "version:"<< head << ". http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    
    if (STOF(head) > STOF(CURRENT_API_VERSION))
    {
        head = CURRENT_API_VERSION;
    }

    //处理app调度自动化测试的
    if (IsTestServer(gstCSGATEConf.cloud_env) && strlen(gstCSGATEConf.auto_test_dispatch_uid) > 0 && csgate::HandleAutoTestServerList(ctx, cb) == true)
    {
        return;
    }
    
    auto httpRespSrvListMap = httpRespCbs[csgate::SERVERS_LIST];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it ==  httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqPmServersListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "6.6";
    }
    LOG_INFO << "version:"<< head << ". http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    
    if (STOF(head) > STOF(CURRENT_API_VERSION))
    {
        head = CURRENT_API_VERSION;
    }
    auto httpRespSrvListMap = httpRespCbs[csgate::PM_SERVERS_LIST];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it ==  httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

//根据负载均衡算法,分配接入服务器
void HttpReqRegisterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //LOG_WARN << "http req head 'api-version' is null";
        //cb("http req head 'api-version' is null");
        //return;
        head = "VDevice";
    }
    //const std::string strApiVersion = ctx->FindRequestHeader("api-version");
    auto http_resp_echo_map = httpRespCbs[csgate::REGISTERSERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqRtspServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.4";
    }
    auto http_resp_echo_map = httpRespCbs[csgate::HTTP_RTSP_SERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqFtpServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.4";
    }
    auto http_resp_echo_map = httpRespCbs[csgate::HTTP_FTP_SERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqPbxServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.4";
    }
    auto http_resp_echo_map = httpRespCbs[csgate::HTTP_PBX_SERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqAppCheckVerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.5";
    }
    auto httpRespAppVerCheckMap = httpRespCbs[csgate::HTTP_APP_VER_CHECK];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespAppVerCheckMap.find(head);
    if (it ==  httpRespAppVerCheckMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqUpdateAppLatestVersionCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "6.1";
    }
    if(ctx->remote_ip() != "127.0.0.1")
    {
        cb("Remote ip is invalid.");
        return;
    }

    auto httpRespUpdateAppLatestVersionMap = httpRespCbs[csgate::UPDATE_APP_LATEST_VERSION];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespUpdateAppLatestVersionMap.find(head);
    if (it ==  httpRespUpdateAppLatestVersionMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqUpdateLimitRateCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    if(ctx->remote_ip() != "127.0.0.1")
    {
        cb("Remote ip is invalid.");
        return;
    }

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "6.2";
    }

    auto httpRespUpdateLimitRateMap = httpRespCbs[csgate::UPDATE_LIMIT_RATE];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespUpdateLimitRateMap.find(head);
    if (it ==  httpRespUpdateLimitRateMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqSvnVersionCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    std::string svn_version;
    svn_version = gstCSGATEConf.svn_version;
    svn_version += "\n";
    cb(svn_version);
    return;
}

void HttpReqRestServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = "6.0";//写死6.0 安卓app迁移时候好像header获取版本会有问题
    auto http_resp_echo_map = httpRespCbs[csgate::RESTSERVER];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqDecryptLogOutputCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    if(ctx->remote_ip() != "127.0.0.1")
    {
        return;
    }

    std::string switch_flag;
    switch_flag = ctx->GetQuery("switch");
    if(switch_flag.size() == 0)
    {
        cb("please request like http://127.0.0.1:9999/decrypt_log_output?switch=1");
        return ;
    }
    
    gstCSGATEConf.decrypt_log_ouput = ATOI(switch_flag.c_str()) > 0 ? 1:0;    
    cb("request success!");
    return ;
}

void HttpReqSmartHomeLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "6.2";
    }

    auto http_resp_echo_map = httpRespCbs[csgate::SMARTHOME_LOGIN];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqUpdateInnerAuthCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    std::string ip = ctx->remote_ip();
    
    if(ServerArea::jpcloud == gstCSGATEConf.server_area)
    {
        if (!strstr(gstCSGATEConf.update_jp_auth_allow_ip, ip.c_str()) )
        {
            LOG_WARN << "jp cloud allow update token allow ip list:" << gstCSGATEConf.update_jp_auth_allow_ip ;
            cb("http req ip not allow");
            return;
        }
    }
    else if(ServerArea::aucloud == gstCSGATEConf.server_area)
    {
        if (!strstr(gstCSGATEConf.update_au_auth_allow_ip, ip.c_str()) )
        {
            LOG_WARN << "au cloud allow update token allow ip list:" << gstCSGATEConf.update_au_auth_allow_ip ;
            cb("http req ip not allow");
            return;
        }
    }
    else if(ServerArea::scloud == gstCSGATEConf.server_area)
    {
        //澳洲的直接接
        if (!strstr(gstCSGATEConf.update_sjp_auth_allow_ip, ip.c_str()) )
        {
            LOG_WARN << "s cloud allow update token allow ip list:" << gstCSGATEConf.update_sjp_auth_allow_ip ;
            cb("http req ip not allow");
            return;
        }
    }    
    else if(ServerArea::asbj == gstCSGATEConf.server_area)
    {
        if (!strstr(gstCSGATEConf.update_asbj_auth_allow_ip, ip.c_str()) )
        {
            LOG_WARN << "asbj/europe cloud allow update token allow ip list:" << gstCSGATEConf.update_asbj_auth_allow_ip ;
            cb("http req ip not allow");
            return;
        }
    }
    else if (ServerArea::ecloud == gstCSGATEConf.server_area)
    {
        if (!strstr(gstCSGATEConf.update_asbj_auth_allow_ip, ip.c_str()) && !strstr(gstCSGATEConf.update_e2ucloud_auth_allow_ip, ip.c_str()) )
        {
            LOG_WARN << "asbj/europe cloud allow update token allow ip list:" << gstCSGATEConf.update_asbj_auth_allow_ip << " " <<   gstCSGATEConf.update_e2ucloud_auth_allow_ip;
            cb("http req ip not allow");
            return;
        }

    }
    else if (ServerArea::ucloud == gstCSGATEConf.server_area)
    {
        if (!strstr(gstCSGATEConf.update_e2ucloud_auth_allow_ip, ip.c_str()) )
        {
            LOG_WARN << "asbj/europe cloud allow update token allow ip list:" << gstCSGATEConf.update_e2ucloud_auth_allow_ip ;
            cb("http req ip not allow");
            return;
        }

    }    
    else
    {
        LOG_WARN << "cloud not allow update token";
        cb("cloud not allow update token");
        return;
    }
    
    auto httpMap = httpRespCbs[csgate::UPDATE_INNER_AUTH];
    const char* head = "6.4";
    csgate::HTTPRespVerCallbackMap::iterator it = httpMap.find(head);
    if (it ==  httpMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}
void HttpReqSHServerListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "6.4";
    }

    auto http_resp_echo_map = httpRespCbs[csgate::SH_SERVER_LIST];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

//ins APP新增

//ins APP登陆鉴权
void HttpReqInstallerLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if(nullptr == head) 
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    if (STOF(head) > STOF(CURRENT_API_VERSION))
    {
        head = CURRENT_API_VERSION;
    }
    LOG_INFO <<"receive Ins APP login req, API Version:" << head;
    //具体消息处理
    auto http_resp_echo_map = httpRespCbs[csgate::INSTALLER_LOGIN];
    csgate::HTTPRespVerCallbackMap::iterator it = http_resp_echo_map.find(head);
    if (it ==  http_resp_echo_map.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    it->second(ctx, cb);
    return;
}

//ins APP获取服务器列表
void HttpReqInstallerServerListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if(nullptr == head) 
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    if (STOF(head) > STOF(CURRENT_API_VERSION))
    {
        head = CURRENT_API_VERSION;
    }
    LOG_INFO << "receive Ins APP Server List req, API Version:" << head;
    //消息处理
    auto httpRespSrvListMap = httpRespCbs[csgate::INSTALLER_SERVER_LIST];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it ==  httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

void HttpReqInsRefreshTokenCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if(nullptr == head) 
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    else
    {
        LOG_INFO << "receive Ins APP refresh token req, API Version:" << head;
    }
    //消息处理
    auto httpRespSrvListMap = httpRespCbs[csgate::INS_REF_TOKEN];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it ==  httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;
}

//安全版本 refresh token
void HttpReqRefreshTokenCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    else
    {
        LOG_INFO << "receive refresh token req, API Version:" << head;
    }
    //消息处理
    auto httpRespSrvListMap = httpRespCbs[csgate::REF_TOKEN];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it == httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] , which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return;
}

//安全版本 refresh token
void HttpReqPmRefreshTokenCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    else
    {
        LOG_INFO << "receive refresh token req, API Version:" << head;
    }
    //消息处理
    auto httpRespSrvListMap = httpRespCbs[csgate::PM_REF_TOKEN];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it == httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] , which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return;
}

void HttpReqPmTwoFactorAuthCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    else
    {
        LOG_INFO << "receive refresh token req, API Version:" << head;
    }
    //消息处理
    auto httpRespSrvListMap = httpRespCbs[csgate::PM_VERIFY_CODE];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it == httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] , which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return;
}

void HttpReqInsTwoFactorAuthCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    //HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    else
    {
        LOG_INFO << "receive refresh token req, API Version:" << head;
    }
    //消息处理
    auto httpRespSrvListMap = httpRespCbs[csgate::INS_VERIFY_CODE];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespSrvListMap.find(head);
    if (it == httpRespSrvListMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] , which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return;
}

void HttpCommonReqHandleCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb, const std::string& request_name, csgate::HTTP_ROUTE req_type)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    // HTTP头校验
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        LOG_WARN << "empty request header : api-version";
        cb("http req version is out of request");
        return;
    }
    else
    {
        LOG_INFO << "receive " << request_name << " req, API Version:" << head;
    }

    // 消息处理
    auto httpRespMap = httpRespCbs[req_type];
    csgate::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] , which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
}

void HttpReqAdminLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    HttpCommonReqHandleCallback(loop, ctx, cb, "admin app login", csgate::HTTP_ROUTE::ADMIN_LOGIN);
}

void HttpReqAdminServersListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    HttpCommonReqHandleCallback(loop, ctx, cb, "admin servers list", csgate::HTTP_ROUTE::ADMIN_SERVER_LIST);
}

void HttpReqAdminRefreshTokenCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    HttpCommonReqHandleCallback(loop, ctx, cb, "admin refresh token", csgate::HTTP_ROUTE::ADMIN_REF_TOKEN);
}

void HttpReqAdminSmsLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    HttpCommonReqHandleCallback(loop, ctx, cb, "admin sms login", csgate::HTTP_ROUTE::ADMIN_SMS_LOGIN);
}

void HttpReqSmartHomeLockLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    csgate::HTTPSmartHomeLockLogin(ctx, cb);
    return;
}

void HttpReqSmartHomeLockLogOutCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    csgate::HTTPSmartHomeLockLogout(ctx, cb);
    return;
}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}


void RegInterface(evpp::http::Server &server)
{
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/echo", HttpReqEchoCallback);
    server.RegisterHandler("/login", HttpReqLoginCallback);
    server.RegisterHandler("/pm_login", HttpReqPmLoginCallback);
    server.RegisterHandler("/sms_login", HttpReqSmsLoginCallback);
    server.RegisterHandler("/apiserver", HttpReqGetApiServerCallback);
    server.RegisterHandler("/accessserver", HttpReqAccServerCallback);
    server.RegisterHandler("/accessserver_list", HttpReqAccServerListCallback);
    server.RegisterHandler("/vrtspserver_list", HttpReqRtspServerListCallback);
    server.RegisterHandler("/servers_list", HttpReqServersListCallback);
    server.RegisterHandler("/pm_servers_list", HttpReqPmServersListCallback);
    server.RegisterHandler("/web_server", HttpReqRegisterCallback);//注册账号时,获取到web服务器
    server.RegisterHandler("/rtspserver", HttpReqRtspServerCallback);
    server.RegisterHandler("/ftpserver", HttpReqFtpServerCallback);
    server.RegisterHandler("/pbxserver", HttpReqPbxServerCallback);
    server.RegisterHandler("/ver_check", HttpReqAppCheckVerCallback);
    server.RegisterHandler("/svn_version", HttpReqSvnVersionCallback);
    server.RegisterHandler("/rest_server", HttpReqRestServerCallback);//迁移时候app会拿这个接口判断新的网关是否正常, 并且新网关从这个接口返回
    server.RegisterHandler("/decrypt_log_output", HttpReqDecryptLogOutputCallback);//内部接口
    server.RegisterHandler("/update_app_latest_version", HttpReqUpdateAppLatestVersionCallback);//内部接口
    server.RegisterHandler("/set_rate", HttpReqUpdateLimitRateCallback);//内部接口
    //给uid/mac分配指定的accseeserver/rtspserver/pbxserver
    server.RegisterHandler("/set_testserver", csgate::HttpReqSetTestServerCallback);//内部接口
    server.RegisterHandler("/clear_all_testserver", csgate::HttpReqClearTestServerCallback);//内部接口
    server.RegisterHandler("/print_all_testserver", csgate::HttpReqPrintfTestServerCallback);//内部接口

    // 获取/设置 鉴权开关(内部接口)
    server.RegisterHandler("/get_device_login_auth_switch", csgate::HttpReqGetDeviceLoginAuthSwitchCallback);
    server.RegisterHandler("/set_device_login_auth_switch", csgate::HttpReqSetDeviceLoginAuthSwitchCallback);
    
    //智能家居
    server.RegisterHandler("/smarthome", HttpReqSmartHomeLoginCallback);
    server.RegisterHandler("/csgate_status", HttpReqCsgateStatusCallback);
    //新加坡服务器更新token到日本服务器的接口 只对特定ip生效
    server.RegisterHandler("/update_auth", HttpReqUpdateInnerAuthCallback);

    /*后续的接口不能往这里添加，这里是http 9999的接口,不在支持*/
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);

    //自动化调用接口
    server.RegisterHandler("/autotest_set_newgate", csgate::HttpReqAutoTestSetNewGateCallback);
    server.RegisterHandler("/autotest_set_clear_newgate", csgate::HttpReqAutoTestClearNewGateCallback);    
}

void RegNewSSLInterface(evpp::http::Server &server)
{
    server.RegisterHandler("/smarthome_servers_list", HttpReqSHServerListCallback);
    //ins APP新增
    server.RegisterHandler("/installer/login", HttpReqInstallerLoginCallback);
    server.RegisterHandler("/installer/servers_list", HttpReqInstallerServerListCallback);
    
    server.RegisterHandler("/installer/refresh_token", HttpReqInsRefreshTokenCallback);
    server.RegisterHandler("/refresh_token", HttpReqRefreshTokenCallback);
    server.RegisterHandler("/pm_refresh_token", HttpReqPmRefreshTokenCallback);  
    server.RegisterHandler("/pm_verify_code", HttpReqPmTwoFactorAuthCallback);
    server.RegisterHandler("/installer/ins_verify_code", HttpReqInsTwoFactorAuthCallback);

    server.RegisterHandler("/admin/login", HttpReqAdminLoginCallback);
    server.RegisterHandler("/admin/servers_list", HttpReqAdminServersListCallback);
    server.RegisterHandler("/admin/refresh_token", HttpReqAdminRefreshTokenCallback);
    server.RegisterHandler("/admin/sms_login", HttpReqAdminSmsLoginCallback);
    // SL20
    server.RegisterHandler("/smarthome_lock/login", HttpReqSmartHomeLockLoginCallback);
    server.RegisterHandler("/smarthome_lock/logout", HttpReqSmartHomeLockLogOutCallback);
    
}


//add by chenzhx 20214828后续的接口不用在这里添加了，只需要在9995/19999接口添加
void startHttpServer()
{
    //added by chenyc,2019-11-25,设置判断客户端攻击时的相关参数,当满足条件时,BussinessLimit会回调本段设置的函数.
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(csgate::CSGATE_HTTP_BUSSINESS, csgate::CSGATE_HTTP_PERIOD,
            csgate::CSGATE_HTTP_NUM, csgate::CSGATE_HTTP_KEY_EXPIRE, HttpAttackedCallback);

    //临时处理，ipv6下,9999端口不可用的问题,避免北美云环境中的设备要全部恢复出厂设置
    //保持ipv4:9999 ipv6:19999
    const int port2 = 9999;
    const int thread_num2 = 5;
    bool ipv6_flag = false;
    evpp::http::Server server(thread_num2, ipv6_flag);
    server.SetRateLimiter(g_rate_limiter, gstCSGATEConf.limit_switch);
    server.SetThreadDispatchPolicy(evpp::ThreadDispatchPolicy::kIPAddressHashing);
    RegInterface(server);
    server.Init(port2);
    server.Start();
    return ;
}


void startHttpServerIPV6()
{
    int enable_ipv6 = gstCSGATEConf.enable_ipv6;
    if (enable_ipv6 == 1)
    {
        const int port = 19999;
        const int thread_num = 2;
        bool ipv6_flag = true;
        evpp::http::Server server(thread_num, ipv6_flag);
        server.SetRateLimiter(g_rate_limiter, gstCSGATEConf.limit_switch);
        server.SetThreadDispatchPolicy(evpp::ThreadDispatchPolicy::kIPAddressHashing);

        RegInterface(server);
        RegNewSSLInterface(server);

        server.Init(port);
        server.Start();
    }
}

void startHttpSSLServer()
{
    httpRespCbs = csgate::HTTPAllRespMapInit();
    csgate::HTTPRefreshTokenRespMapInit(httpRespCbs);
    csgate::HTTPPmTwoFactorAuthMapInit(httpRespCbs);
    csgate::HTTPInsRespMapInit(httpRespCbs);
    csgate::HTTPAdminRespMapInit(httpRespCbs);
    //added by chenyc,2019-11-25,设置判断客户端攻击时的相关参数,当满足条件时,BussinessLimit会回调本段设置的函数.
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(csgate::CSGATE_HTTP_BUSSINESS, csgate::CSGATE_HTTP_PERIOD,
            csgate::CSGATE_HTTP_NUM, csgate::CSGATE_HTTP_KEY_EXPIRE, HttpAttackedCallback);

    const int port2 = 9995;
    const int thread_num2 = 5;
    bool ipv6_flag = false;
    evpp::http::Server server(thread_num2, ipv6_flag);
    server.SetRateLimiter(g_rate_limiter, gstCSGATEConf.limit_switch);
    server.SetThreadDispatchPolicy(evpp::ThreadDispatchPolicy::kIPAddressHashing);

    RegInterface(server);
    RegNewSSLInterface(server);

    server.Init(port2);
    server.Start();
    return ;
}





