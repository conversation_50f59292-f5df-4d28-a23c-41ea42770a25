#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>
#include <functional>
#include <memory>
#include <string>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "HttpResp.h"
#include "CsgateConf.h"
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "AkcsMonitor.h"
#include "Md5.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "BasicDefine.h"
#include "LogicSrvMng.h"
#include "HttpServerMaintenance.h"
#include "json/json.h"

extern CSGATE_CONF gstCSGATEConf;

namespace csgate
{


HTTPServerCallBack  HttpReqGetDeviceLoginAuthSwitchCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    
    if(ctx->remote_ip() != "127.0.0.1")
    {
        return;
    }

    HttpRespKV kv;
    HttpRespIntKV kv_int;
    kv_int.insert(std::map<std::string, int>::value_type("switch", gstCSGATEConf.device_login_auth_switch));    
    cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv, kv_int));
    return;
};

HTTPServerCallBack  HttpReqSetDeviceLoginAuthSwitchCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);
    
    if(ctx->remote_ip() != "127.0.0.1")
    {
        return;
    }

    std::string value = ctx->GetQuery("switch");
    LOG_INFO << "invoking function set device_login_auth_switch, value=" << value;
    if (value == "0")
    {
        gstCSGATEConf.device_login_auth_switch = 0;
        cb(buildNewErrorHttpMsg(ERR_CODE_SUCCESS));
    }
    else if (value == "1")
    {
        gstCSGATEConf.device_login_auth_switch = 1;
        cb(buildNewErrorHttpMsg(ERR_CODE_SUCCESS));
    }
    else
    {
        cb(buildNewErrorHttpMsg(ERR_CODE_HTTP_BODY_INVALID));
    }
    return;
};

HTTPServerCallBack  HttpReqSetTestServerCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    if (strncmp(gstCSGATEConf.server_inner_ip, ctx->remote_ip().c_str(), strlen(gstCSGATEConf.server_inner_ip)) != 0)
    {
        return;
    }
    
    Json::Reader reader;
    Json::Value root;  
    std::string http_body = ctx->body().ToString();
    AK_LOG_INFO << "ReqSetTestServerHandler, http_body = " << http_body; 
    
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "ReqSetAccessServerHandler respone error," << " data:" << http_body;
        cb("body format error");
        return;
    }

    // 0-set_accessserver; 1-set_rtspserver; 2-set_pbxserver
    int set_type = ATOI(root["type"].asString().c_str()); 
    std::string ipv4_addr = root["ipv4"].asString();
    std::string ipv6_addr = root["ipv6"].asString();
    std::string uid_or_mac = root["uid_or_mac"].asString();

    if (ipv4_addr.size() == 0 || uid_or_mac.size() == 0)
    {
        AK_LOG_WARN << "parma error ip or uid is null!";
        cb("parma error ip or uid is null!");
        return;        
    }

    switch (set_type)
    {
        case csgate::TestServerSetType::TEST_SERVER_SET_CSMAIN:
            //指定access_server
            CLogicSrvMng::Instance()->SetMaintainenceAccIp46Map(uid_or_mac, ipv4_addr, ipv6_addr);
            AK_LOG_INFO << "set access server, user is " << uid_or_mac << ",csmain_ipv4 is = " << ipv4_addr << ", csmain_ipv6 = " << ipv6_addr;
            break;
        case csgate::TestServerSetType::TEST_SERVER_SET_CSVRTSP:
            //指定rtsp_server
            CLogicSrvMng::Instance()->SetMaintainenceRtspIp46Map(uid_or_mac, ipv4_addr, ipv6_addr);
            AK_LOG_INFO << "set rtsp server, user is " << uid_or_mac << ",csvrtsp_ipv4 is = " << ipv4_addr << ", csvrtsp_ipv6 = " << ipv6_addr;

            break;
        case csgate::TestServerSetType::TEST_SERVER_SET_PBX:
            //指定pbx_server
            CLogicSrvMng::Instance()->SetMaintainenceOpsIp46Map(uid_or_mac, ipv4_addr, ipv6_addr);
            AK_LOG_INFO << "set pbx server, user is " << uid_or_mac << ", pbx_ipv4 = " << ipv4_addr << ", pbx_ipv6 = " << ipv6_addr;
            break;
        default:
            cb("SetTestServer Type Error, 0 is set_accessserver, 1 is set_rtspserver, 2 is set_pbxserver");
            return;
    }

    cb("success");

    return ;
};

HTTPServerCallBack  HttpReqClearTestServerCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    if (strncmp(gstCSGATEConf.server_inner_ip, ctx->remote_ip().c_str(), strlen(gstCSGATEConf.server_inner_ip)) != 0)
    {
        return;
    }
    
    CLogicSrvMng::Instance()->ClearMaintainenceAllIp();
    cb("success");
    return ;
};

HTTPServerCallBack  HttpReqPrintfTestServerCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip() << ", forward_ip is " << csgate::GetCtxHeader(ctx, FORWARD_IP);

    if (strncmp(gstCSGATEConf.server_inner_ip, ctx->remote_ip().c_str(), strlen(gstCSGATEConf.server_inner_ip)) != 0)
    {
        return;
    }
    
    cb(CLogicSrvMng::Instance()->GetMaintainenceAllIpStr());
    return ;
};

//给app自动化测试用的 验证网关调度是否正常
HTTPServerCallBack  HttpReqAutoTestSetNewGateCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if(!IsTestServer(gstCSGATEConf.cloud_env))
    {
        cb("env error");
        return;
    }

    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The request parameter is invalid";
        cb("error");
        return;
    }

    // 获取和检查参数
    std::string remote_ip = ctx->remote_ip();
    std::string token = ctx->GetQuery("token");
    std:: string uid = root["uid"].asString();
    std:: string serveraddr = root["new_gate_serveraddr"].asString();
    if (serveraddr.empty() || token.empty())
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: token or severaddr is null";
        cb("error");
        return;

    }

    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: remote_ip=" << remote_ip 
             << ", uid=" << uid << ", new_gate_serveraddr=" << serveraddr;

    // 验证token
    std::string mac_key_str = uid + TEST_API_PRIVATE_MD5_KEY;
    std::string md5_str = akuvox_encrypt::MD5(mac_key_str).toStr();

    std::transform(token.begin(), token.end(), token.begin(), ::tolower);
    std::transform(md5_str.begin(), md5_str.end(), md5_str.begin(), ::tolower);
    if (md5_str != token)
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The token parameter is invalid";
        cb("error");
        return;
    }
    Snprintf(gstCSGATEConf.auto_test_dispatch_uid, sizeof(gstCSGATEConf.auto_test_dispatch_uid), uid.c_str());
    Snprintf(gstCSGATEConf.auto_test_newgate_addr, sizeof(gstCSGATEConf.auto_test_newgate_addr), serveraddr.c_str());
    
    cb("ok");
    return;
};

HTTPServerCallBack  HttpReqAutoTestClearNewGateCallback = [](evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if(!IsTestServer(gstCSGATEConf.cloud_env))
    {
        cb("env error");
        return;
    }

    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The request parameter is invalid";
        cb("error");
        return;
    }

    // 获取和检查参数
    std::string remote_ip = ctx->remote_ip();
    std::string token = ctx->GetQuery("token");
    std:: string uid = root["uid"].asString();
    if (uid.empty() || token.empty())
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: token or severaddr is null";
        cb("error");
        return;

    }

    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: remote_ip=" << remote_ip 
             << ", uid=" << uid;

    // 验证token
    std::string mac_key_str = uid + TEST_API_PRIVATE_MD5_KEY;
    std::string md5_str = akuvox_encrypt::MD5(mac_key_str).toStr();

    std::transform(token.begin(), token.end(), token.begin(), ::tolower);
    std::transform(md5_str.begin(), md5_str.end(), md5_str.begin(), ::tolower);
    if (md5_str != token)
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The token parameter is invalid";
        cb("error");
        return;
    }
    gstCSGATEConf.auto_test_dispatch_uid[0] = 0;
    gstCSGATEConf.auto_test_newgate_addr[0] = 0;

    cb("ok");
    return;
};

bool HandleAutoTestServerList(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    const char* head = ctx->FindRequestHeader("api-version");
    float api_version = STOF(head);

    const char* token = ctx->FindRequestHeader("x-auth-token");
    if (nullptr == token)
    {
        return false;
    }
    csgate::PersonalAccountInfo personal_account_info;
    csgate::DaoCheckToken(token, personal_account_info, api_version);
    
    if (strlen(personal_account_info.account) >0  && strcmp(personal_account_info.account, gstCSGATEConf.auto_test_dispatch_uid) == 0)
    {
        LOG_INFO << "Is AutoTest redirect uid " << personal_account_info.account << " newgate:" <<gstCSGATEConf.auto_test_newgate_addr;

        HttpRespKV kv;
        std::string redirect_update_token;
        std::string head_ag;
        
        GenerateServerInfo(RedirectCloudType::REDIRECT_NO_NEED, personal_account_info, head_ag, api_version, redirect_update_token, kv);
        kv.insert(std::map<std::string, std::string>::value_type(TOKEN, token)); 
        kv.insert(std::map<std::string, std::string>::value_type(SMARTHOME_SITE, gstCSGATEConf.smart_home_domain)); 
        kv.insert(std::map<std::string, std::string>::value_type(PLATFORM_VER_STR, PLATFORM_VER));  
        
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER, gstCSGATEConf.auto_test_newgate_addr));
        kv.insert(std::map<std::string, std::string>::value_type(GATE_SERVER_IPV6, gstCSGATEConf.auto_test_newgate_addr));

        gstCSGATEConf.auto_test_newgate_addr_time = GetCurrentTimeStamp();
        
        cb(buildCommHttpMsg(csgate::ERR_SUCCESS, kv));
        return true;
    }
    return false;

}


}

