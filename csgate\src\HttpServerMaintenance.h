#ifndef __CSGATE_HTTP_SERVER_MAINTENANCE_H__
#define __CSGATE_HTTP_SERVER_MAINTENANCE_H__


#include <functional>
#include <evpp/http/context.h>
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/AwsRedirect.h"
#include "dbinterface/Token.h"
#include "HttpMsgControl.h"
#include "Dao.h"
#include "AppAuthChecker.h"
#include "AwsRedirect.h"
#include "HttpResp.h"


namespace csgate
{

extern HTTPServerCallBack  HttpReqGetDeviceLoginAuthSwitchCallback;
extern HTTPServerCallBack  HttpReqSetDeviceLoginAuthSwitchCallback;
extern HTTPServerCallBack  HttpReqSetTestServerCallback;
extern HTTPServerCallBack  HttpReqClearTestServerCallback;
extern HTTPServerCallBack  HttpReqPrintfTestServerCallback;
extern HTTPServerCallBack  HttpReqAutoTestSetNewGateCallback;
extern HTTPServerCallBack  HttpReqAutoTestClearNewGateCallback;


bool HandleAutoTestServerList(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);

}

#endif // __CSGATE_HTTP_SERVER_MAINTENANCE_H__
