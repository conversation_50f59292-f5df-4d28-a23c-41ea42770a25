#include "AkLogging.h"
#include "HttpResp.h"
#include "HttpSmartHomeLock.h"
#include "SmartHomeLockAuth.h"
#include "CsgateConf.h"

extern CSGATE_CONF gstCSGATEConf;

namespace csgate
{

void HTTPSmartHomeLockLogin(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string http_body = ctx->body().ToString();    
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    std::string device_passwd = root["device_passwd"].asString();
    std::string device_mac = root["device_mac"].asString();
    std::string device_style = root["device_style"].asString();

    LockAuthFactory lock_auth_factory;
    std::unique_ptr<LockAuth> lock_auth = lock_auth_factory.CreateLockAuth(device_style, device_mac);
    if(lock_auth == nullptr)
    {
        cb(buildNewRespHttpMsg(ERR_CODE_USER_INFO_ERR));    
        return;
    }

    if(!lock_auth->AuthCheck(device_passwd))
    {
       cb(buildNewRespHttpMsg(ERR_CODE_USER_INFO_ERR));    
       return;
    }
    else
    {
        HttpRespKV kv;
        kv.insert(std::map<std::string, std::string>::value_type("username", lock_auth->GetUserName()));    
        kv.insert(std::map<std::string, std::string>::value_type("password", lock_auth->GetPassword()));    
        kv.insert(std::map<std::string, std::string>::value_type("client_id", lock_auth->GetClientId()));    
        kv.insert(std::map<std::string, std::string>::value_type("device_id", lock_auth->GetDeviceId()));    
        kv.insert(std::map<std::string, std::string>::value_type("mqtt_addr", gstCSGATEConf.mqtt_addr));    
        
        //锁端要求host和port拆分下发
        std::string url = gstCSGATEConf.mqtt_addr;
        std::string host, port;        
        //移除协议头 "ssl://"
        size_t protocol_pos = url.find("://");
        if (protocol_pos != std::string::npos) 
        {
            url = url.substr(protocol_pos + 3); // 跳过 "://" 共3个字符
        }      
        //分割主机和端口
        size_t colon_pos = url.find_last_of(':'); // 查找最后一个冒号
        if (colon_pos != std::string::npos)
        {
            host = url.substr(0, colon_pos);
            port = url.substr(colon_pos + 1);
        } 
        else 
        { // 没有端口的情况
            host = url;
            port = ""; // 或设置默认端口
        }      
        kv.insert(std::map<std::string, std::string>::value_type("mqtt_ssl_domain", host));    
        kv.insert(std::map<std::string, std::string>::value_type("mqtt_ssl_port", port)); 

        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv));
    }
    
}

void HTTPSmartHomeLockLogout(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string http_body = ctx->body().ToString();    
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildNewErrorHttpMsg(ERR_CODE_USER_INFO_ERR));
        return;
    }

    std::string device_passwd = root["device_passwd"].asString();
    std::string device_mac = root["device_mac"].asString();
    std::string device_style = root["device_style"].asString();

    LockAuthFactory lock_auth_factory;
    std::unique_ptr<LockAuth> lock_auth = lock_auth_factory.CreateLockAuth(device_style, device_mac);
    if(lock_auth == nullptr)
    {
        cb(buildNewRespHttpMsg(ERR_CODE_USER_INFO_ERR));    
        return;
    }

    bool is_logout = true;

    if(!lock_auth->AuthCheck(device_passwd, is_logout))
    {
       cb(buildNewRespHttpMsg(ERR_CODE_USER_INFO_ERR));    
       return;
    }
    else
    {
        int reset = lock_auth->GetDeviceId().size() == 0 ? 1 : 0;

        HttpRespKV kv;
        HttpRespIntKV kv_int;
        kv_int.insert(std::map<std::string, int>::value_type("reset", reset));    
        cb(buildNewRespHttpMsg(ERR_CODE_SUCCESS, kv, kv_int));
    }
    
}


}

