#ifndef __CSGATE_HTTP_SMARTHOME_LOCK_H__
#define __CSGATE_HTTP_SMARTHOME_LOCK_H__

#include <string>
#include <evpp/http/context.h>
#include "Dao.h"
#include "json/json.h"

namespace csgate
{
    void HTTPSmartHomeLockLogin(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);    
    void HTTPSmartHomeLockLogout(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
}
#endif 
