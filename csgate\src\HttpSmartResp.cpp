#include <iostream>
#include <functional>
#include "HttpResp.h"
#include "Dao.h"
#include "ClientMng.h"
#include "ServerMng.h"
#include "CsgateConf.h"
#include "Md5.h"
#include "LogicSrvMng.h"
#include "AkLogging.h"
#include "AES256.h"
#include "json/json.h"
#include "Url.h"
#include "AuditLog.h"
#include "Caesar.h"
#include "util.h"
#include "HttpMsgControl.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "HttpOfficeResp.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket_interface.h"
#include "evpp/rate_limiter/rate_limiter.h"
#include "DevSrvMapMng.h"

extern CSGATE_CONF gstCSGATEConf; //全局配置信息
extern evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter;
extern AWS_CSGATE_CONF gstAWSConf; //全局配置信息
extern const unsigned int g_ipaddr_check_len;

namespace csgate
{

csgate::HTTPRespCallback ReqSHServerListHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    HttpRespKV kv;
    const char* token = ctx->FindRequestHeader("token");
    if (nullptr == token)
    {
        cb(buildCommHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID, kv));
        return;
    }

    ret = csgate::DaoTokenCheck(token);
    if (csgate::ERR_SUCCESS != ret)
    {
        cb(buildCommHttpMsg(HTTP_CODE_ERR_TOKEN_INVALID, kv));
        return;
    }

    //根据负载均衡算法获取到系统的接入服务器和转流服务器,以uid为负载均衡参数
    std::pair<std::string, std::string> pair_access_ser_46 = CLogicSrvMng::Instance()->GetAccDomainSrv(token);
    std::pair<std::string, std::string> pair_rtsp_ser_46 = CLogicSrvMng::Instance()->GetRtspDomainSrv(token);
    
    std::string csmain_ip =  pair_access_ser_46.first;
    std::string csmain_ipv6 = pair_access_ser_46.second;
    std::string csvrtsp_ip = pair_rtsp_ser_46.first;
    std::string csvrtsp_ipv6 = pair_rtsp_ser_46.second;


    std::string rest_addr = gstCSGATEConf.rest_addr;       
    std::string rest_ipv6 = gstCSGATEConf.rest_ipv6;
    std::string rest_ssl_addr = gstCSGATEConf.rest_ssl_addr;       
    std::string rest_ssl_ipv6 = gstCSGATEConf.rest_ssl_ipv6;
    std::string web_ip = gstCSGATEConf.web_domain_name;
    std::string web_ipv6 = gstCSGATEConf.web_ipv6;

    std::string pbx_ip;
    std::string pbx_ipv6;
    std::string pbx_domain;        
    GetOpsServer(token, pbx_ip, pbx_ipv6, pbx_domain);

    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER, csmain_ip));    
    kv.insert(std::map<std::string, std::string>::value_type(ACCESS_SERVER_IPV6, csmain_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER, web_ip));    
    kv.insert(std::map<std::string, std::string>::value_type(WEB_SERVER_IPV6, web_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER, rest_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_IPV6, rest_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS, rest_ssl_addr));    
    kv.insert(std::map<std::string, std::string>::value_type(REST_SERVER_HTTPS_IPV6, rest_ssl_ipv6));  
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER, csvrtsp_ip));    
    kv.insert(std::map<std::string, std::string>::value_type(VRTSP_SERVER_IPV6, csvrtsp_ipv6));   
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER, pbx_domain));    
    kv.insert(std::map<std::string, std::string>::value_type(PBX_SERVER_IPV6, pbx_ipv6));       

    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));  
};


csgate::HTTPRespVerCallbackMap HTTPReqSHServerListMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V64] = ReqSHServerListHandler;
    return OMap;
}


}

