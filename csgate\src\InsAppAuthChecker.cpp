#include "InsAppAuthChecker.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AccountUserInfo.h"
#include "Caesar.h"
#include "AkLogging.h"
#include "util_string.h"
#include "Md5.h"
#include "util.h"
#include "Dao.h"

int InsAppAuthChecker::HandleCheckAuthToken()
{
    //ins 暂不支持
    return csgate::ERR_SUCCESS;
}

int InsAppAuthChecker::HandleCheckRefreshToken()
{
    if (0 != strcmp(auth_info_.refresh_token, token_info_.refresh_token))
    {
        return csgate::ERR_REFRESH_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}

int InsAppAuthChecker::HandleCheckUserPassword()
{
    std::string user;
    //凯撒解密
    char user_tmp[128];
    snprintf(user_tmp, sizeof(user_tmp), "%s", auth_info_.user);
    akuvox_encrypt::CaesarDecry(user_tmp);
    user = user_tmp;

    if (!HttpCheckSqlParam(user))
    {
        AK_LOG_WARN << "HttpCheckSqlParam failed,Invalid User=" << user;
        return csgate::ERR_PASSWD_INVALID; 
    }
    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
    {
        //密码错误直接return
        if (!csgate::PasswordCorrect(auth_info_.passwd, account_user_info.passwd))
        {
            AK_LOG_WARN << "continuation error, ins app passwd error, login_account:" << user;
            return csgate::ERR_PASSWD_INVALID;
        }
    }
    else
    {
        AK_LOG_WARN << "login failed, user not exist, login_account:" << user;
        return csgate::ERR_USER_NOT_EXIT; 
    }
    UserInfoAccount ins_account_info;
    memset(&ins_account_info, 0, sizeof(ins_account_info));
    if (0 != dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(token_info_.userinfo_uuid, ins_account_info))
    {
        return -1;
    }
    if (0 != strcmp(account_user_info.account, ins_account_info.account))
    {
        return csgate::ERR_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}