#include "AppAuthChecker.h"
#include <cstring>
#include "dbinterface/InsToken.h"

class InsAppAuthChecker : public AppAuthChecker
{

private:
    InsTokenInfo token_info_;

public:
    InsAppAuthChecker() : AppAuth<PERSON><PERSON><PERSON>() {
        memset(&token_info_, 0, sizeof(token_info_));
    }
    InsAppAuthChecker(const InsTokenInfo& token_info, const AuthInfo& auth_info)
                                    :  AppAuth<PERSON><PERSON><PERSON>(auth_info), token_info_(token_info) {}

private:
    virtual int HandleCheckAuthToken() override;
    virtual int HandleCheckUserPassword() override;
    virtual int HandleCheckRefreshToken() override;
};

