#include "json/json.h"
#include "AkLogging.h"

#include "KafkaNotifyHandler.h"
#include "dbinterface/Account.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/office/OfficePersonalAccount.h"

extern CSGATE_CONF gstCSGATEConf;
std::mutex KafkaNotifyHandler::audit_kafka_mutex_;
KafkaNotifyHandler* KafkaNotifyHandler::instance_ = nullptr;
std::shared_ptr<AkcsKafkaProducer> KafkaNotifyHandler::audit_kafka_producer_ = nullptr;

KafkaNotifyHandler* KafkaNotifyHandler::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new KafkaNotifyHandler();
    }

    return instance_;
}

void KafkaNotifyHandler::InitAuditLogKafkaProducer()
{
    std::unique_lock<std::mutex> lock(KafkaNotifyHandler::audit_kafka_mutex_);

    if (KafkaNotifyHandler::audit_kafka_producer_ == nullptr)
    {
        KafkaNotifyHandler::audit_kafka_producer_ = std::make_shared<AkcsKafkaProducer>(
            gstCSGATEConf.notify_web_auditlog_topic, gstCSGATEConf.kafka_broker_ip
        );
    }
}

void KafkaNotifyHandler::PushNewOfficeUserLoginAuditLogMessage(const std::string& account, const std::string& ip)
{
    if (KafkaNotifyHandler::audit_kafka_producer_ == nullptr)
    {
        AK_LOG_WARN << "KafkaNotifyHandler PushAuditLog kafka failed: producer is null. account=" << account;
        return;
    }

    // 获取用户的UUID
    OfficeAccount office_account;
    if (dbinterface::OfficePersonalAccount::GetUidAccount(account, office_account) != 0)
    {
        AK_LOG_WARN << "KafkaNotifyHandler PushAuditLog GetUUIDByAccount failed: account=" << account;
        return;
    }

    std::string project_uuid = office_account.parent_uuid;
    std::string personal_account_uuid = office_account.uuid;
    std::string trace_id = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());

    // 获取OfficePersonal信息
    OfficePersonnelInfo office_personnel_info;
    if (dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(personal_account_uuid, office_personnel_info) != 0)
    {
        AK_LOG_WARN << "Get OfficePersonnel failed: PersonalAccountUUID=" << personal_account_uuid;
        return;
    }

    // 获取项目的Ins和Dis的uuid
    std::string ins_uuid, dis_uuid;
    if (dbinterface::Account::GetInsDisUUIDByProjectUUID(project_uuid, ins_uuid, dis_uuid) == false)
    {
        AK_LOG_WARN << "Get Ins and Dis UUID failed: project_uuid=" << project_uuid;
        return;
    }

    // 设置用户信息
    Json::Value role_set;
    role_set["dis"] = dis_uuid;
    role_set["ins"] = ins_uuid;
    role_set["project"] = project_uuid;
    role_set["company"] = std::string(office_personnel_info.office_company_uuid);
    role_set["mainUser"] = personal_account_uuid;

    // 设置审计日志数据
    Json::Value audio_data;
    audio_data["ip"] = ip;
    audio_data["action"] = "login";
    audio_data["userType"] = "personnel";
    audio_data["model"] = "officePersonnel";
    audio_data["roleSet"] = role_set;
    audio_data["user"] = personal_account_uuid;
    audio_data["list"] = Json::arrayValue; // 初始化为空数组

    // 发送审计日志
    Json::Value json_data;
    json_data["timestamp"] = (unsigned int)(time(nullptr) * 1000000);
    json_data["trace_id"] = trace_id;
    json_data["data"] = audio_data;

    Json::FastWriter writer;
    std::string json_msg = writer.write(json_data);

    AK_LOG_INFO << "KafkaNotifyHandler PushAuditLog trace_id=" << trace_id
        << ", key=" << personal_account_uuid << ", json_msg=" << json_msg;

    audit_kafka_producer_->ProduceMsgWithLock(trace_id, json_msg);
}

void KafkaNotifyHandler::PushNewOfficeAdminLoginAuditLogMessage(const std::string& account, const std::string& ip)
{
    if (KafkaNotifyHandler::audit_kafka_producer_ == nullptr)
    {
        AK_LOG_WARN << "KafkaNotifyHandler PushAuditLog kafka failed: producer is null. account=" << account;
        return;
    }
    // 获取用户的UUID
    OfficeAccount office_account;
    if (dbinterface::OfficePersonalAccount::GetUidAccount(account, office_account) != 0)
    {
        AK_LOG_WARN << "KafkaNotifyHandler PushAuditLog GetUUIDByAccount failed: account=" << account;
        return;
    }

    std::string project_uuid = office_account.parent_uuid;
    std::string personal_account_uuid = office_account.uuid;
    std::string trace_id = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());

    // 获取OfficeAdmin信息
    OfficeAdminInfo office_admin_info;
    if (dbinterface::OfficeAdmin::GetOfficeAdminByPersonalAccountUUID(personal_account_uuid, office_admin_info) != 0)
    {
        AK_LOG_WARN << "Get OfficePersonnel failed: PersonalAccountUUID=" << personal_account_uuid;
        return;
    }

    // 获取项目的Ins和Dis的uuid
    std::string ins_uuid, dis_uuid;
    if (dbinterface::Account::GetInsDisUUIDByProjectUUID(project_uuid, ins_uuid, dis_uuid) == false)
    {
        AK_LOG_WARN << "Get Ins and Dis UUID failed: project_uuid=" << project_uuid;
        return;
    }

    // 设置用户信息
    Json::Value role_set;
    role_set["dis"] = dis_uuid;
    role_set["ins"] = ins_uuid;
    role_set["project"] = project_uuid;
    role_set["company"] = std::string(office_admin_info.office_company_uuid);
    role_set["mainUser"] = personal_account_uuid;

    // 设置审计日志数据
    Json::Value audio_data;
    audio_data["ip"] = ip;
    audio_data["userType"] = "officeAdminApp";
    audio_data["model"] = "officeAdminApp";
    audio_data["action"] = "adminAppLogin";
    audio_data["roleSet"] = role_set;
    audio_data["user"] = personal_account_uuid;
    audio_data["list"] = Json::arrayValue; // 初始化为空数组

    // 发送审计日志
    Json::Value json_data;
    json_data["timestamp"] = (unsigned int)(time(nullptr) * 1000000);
    json_data["trace_id"] = trace_id;
    json_data["data"] = audio_data;

    Json::FastWriter writer;
    std::string json_msg = writer.write(json_data);

    AK_LOG_INFO << "KafkaNotifyHandler PushAuditLog trace_id=" << trace_id
        << ", key=" << personal_account_uuid << ", json_msg=" << json_msg;

    audit_kafka_producer_->ProduceMsgWithLock(trace_id, json_msg);
}

