#ifndef __KAFKA_NOTIFY_HANDLER_H__
#define __KAFKA_NOTIFY_HANDLER_H__

#include <mutex>
#include <atomic>
#include <string>

#include "AkcsMsgDef.h"
#include "CsgateConf.h"
#include "KafkaParseWebMsg.h"
#include "AkcsKafkaProducer.h"
#include "gid/SnowFlakeGid.h"

class KafkaNotifyHandler
{
    KafkaNotifyHandler(){}
    static KafkaNotifyHandler* instance_;
    static std::mutex audit_kafka_mutex_;
    static std::shared_ptr<AkcsKafkaProducer> audit_kafka_producer_;

public:
    static KafkaNotifyHandler* GetInstance();
    static void InitAuditLogKafkaProducer();
    static void PushNewOfficeUserLoginAuditLogMessage(const std::string& account, const std::string& ip);
    static void PushNewOfficeAdminLoginAuditLogMessage(const std::string& account, const std::string& ip);

};

#endif // __KAFKA_NOTIFY_HANDLER_H__
