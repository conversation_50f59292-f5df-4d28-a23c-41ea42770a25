#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AkLogging.h"
#include <sstream>
#include "util.h"
#include "ConsistentHashMap.hpp"
#include "LogicSrvMng.h"
#include "CsgateConf.h"
#include "AkLogging.h"


extern AWS_CSGATE_CONF gstAWSConf;


const static int g_domain_detect_len = 7;
CLogicSrvMng* CLogicSrvMng::instance_ = nullptr;
//const int32_t CLogicSrvMng::kVnodeNum = 50; 直接初始化
CLogicSrvMng* CLogicSrvMng::Instance()
{
    if (!instance_)
    {
        instance_ = new CLogicSrvMng();
    }
    return instance_;
}
uint32_t CLogicSrvMng::crc32_hash(const std::string key)
{
    boost::crc_32_type ret;
    ret.process_bytes(key.c_str(), key.size());
    return ret.checksum();
}

void CLogicSrvMng::InitAccSrvList(const std::vector<std::string>& csmain_addrs)
{
    for (const auto& addr : csmain_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd csmain ip info:" << addr;
            continue;
        }
        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = "";
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }
        //构建一致性哈希的环
        {
            std::lock_guard<std::mutex> lock(acc_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                acc_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }
}

void CLogicSrvMng::InitRtspSrvList(const std::vector<std::string>& csvrtspd_addrs)
{
    for (const auto& addr : csvrtspd_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd csvrtsp ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = "";
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }
        {
            std::lock_guard<std::mutex> lock(vrtspd_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                vrtspd_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }

}

void CLogicSrvMng::InitOpsSrvList(const std::vector<std::string>& ops_addrs)
{
    for (const auto& addr : ops_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd ops ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = addr_split.GetItem(2);
        
        AK_LOG_INFO << "InitOpsSrvList : ipv4_info = " << ipv4_info << ",ipv6_info = " << ipv6_info << ",domain_info= " << domain_info;
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }
        {
            std::lock_guard<std::mutex> lock(ops_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                ops_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }
}

void CLogicSrvMng::InitFtpSrvList(const std::vector<std::string>& ftp_addrs)
{
    for (const auto& addr : ftp_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd csftp ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = addr_split.GetItem(2);
        AK_LOG_INFO << "InitFtpSrvList : ipv4_info = " << ipv4_info << ",ipv6_info = " << ipv6_info << ",domain_info= " << domain_info;
        
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }
        {
            std::lock_guard<std::mutex> lock(ftp_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                ftp_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }
}

void CLogicSrvMng::UpdateAccSrvList(const std::vector<std::string>& csmain_addrs)
{
    std::vector<vnode_t> vnode_list;
    for (const auto& addr : csmain_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd csmain ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = "";
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }

        vnode_list.push_back(vnode_t(ipv4_info, ipv6_info, domain_info, 0));
    }
    std::lock_guard<std::mutex> lock(acc_consistent_mutex_);
    acc_consistent_hash_.clear();
    for (const auto &vnode: vnode_list)
    {
        for (int j = 0; j < kVnodeNum; j++)
        {
            acc_consistent_hash_.insert(vnode_t(vnode.ipv4_, vnode.ipv6_, vnode.domain_, j));
        }
    }

}

void CLogicSrvMng::UpdateRtspSrvList(const std::vector<std::string>& csvrtspd_addrs)
{
    std::vector<vnode_t> vnode_list;
    for (const auto& addr : csvrtspd_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd csvrtsp ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = "";
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }


        vnode_list.push_back(vnode_t(ipv4_info, ipv6_info, domain_info, 0));
    }

    std::lock_guard<std::mutex> lock(vrtspd_consistent_mutex_);
    vrtspd_consistent_hash_.clear();
    for (const auto &vnode: vnode_list)
    {
        for (int j = 0; j < kVnodeNum; j++)
        {
            vrtspd_consistent_hash_.insert(vnode_t(vnode.ipv4_, vnode.ipv6_, vnode.domain_, j));
        }
    }
}

void CLogicSrvMng::UpdateOpsSrvList(const std::vector<std::string>& ops_addrs)
{
    std::vector<vnode_t> vnode_list;
    for (const auto& addr : ops_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd ops ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = "";
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }

        vnode_list.push_back(vnode_t(ipv4_info, ipv6_info, domain_info, 0));
    }

    std::lock_guard<std::mutex> lock(ops_consistent_mutex_);
    ops_consistent_hash_.clear();
    for (const auto &vnode: vnode_list)
    {
        for (int j = 0; j < kVnodeNum; j++)
        {
            ops_consistent_hash_.insert(vnode_t(vnode.ipv4_, vnode.ipv6_, vnode.domain_, j));
        }
    }
}

void CLogicSrvMng::UpdateFtpSrvList(const std::vector<std::string>& ftp_addrs)
{
    std::vector<vnode_t> vnode_list;
    for (const auto& addr : ftp_addrs)
    {
        CStrExplode addr_split(addr.c_str(), '&');
        uint32_t cnt = addr_split.GetItemCnt();
        if (cnt != 3 && cnt != 2)
        {
            AK_LOG_WARN << "invalid etcd ops ip info:" << addr;
            continue;
        }

        std::string ipv4_info = addr_split.GetItem(0);
        std::string ipv6_info = addr_split.GetItem(1);
        std::string domain_info = addr_split.GetItem(2);
        
        if (3 == cnt)
        {
            domain_info = addr_split.GetItem(2);
        }
        else
        {
            domain_info = addr_split.GetItem(0);
        }

        vnode_list.push_back(vnode_t(ipv4_info, ipv6_info, domain_info, 0));
    }

    std::lock_guard<std::mutex> lock(ftp_consistent_mutex_);
    ftp_consistent_hash_.clear();
    for (const auto &vnode: vnode_list)
    {
        for (int j = 0; j < kVnodeNum; j++)
        {
            ftp_consistent_hash_.insert(vnode_t(vnode.ipv4_, vnode.ipv6_, vnode.domain_, j));
        }
    }
}

void CLogicSrvMng::SetMaintainenceAccIp46Map(const std::string& uid_or_mac, const std::string& csmain_ipv4, const std::string& csmain_ipv6)
{
	maintainence_acc_ip46_map_[uid_or_mac] = std::make_pair(csmain_ipv4, csmain_ipv6);
	return;
}

void CLogicSrvMng::SetMaintainenceRtspIp46Map(const std::string& uid_or_mac, const std::string& rtsp_ipv4, const std::string& rtsp_ipv6)
{
    maintainence_rtsp_ip46_map_[uid_or_mac] = std::make_pair(rtsp_ipv4, rtsp_ipv6);
	return;
}

void CLogicSrvMng::SetMaintainenceOpsIp46Map(const std::string& uid_or_mac, const std::string& pbx_ipv4, const std::string& pbx_ipv6)
{
    maintainence_ops_ip46_map_[uid_or_mac] = std::make_pair(pbx_ipv4, pbx_ipv6);
	return;
}

int CLogicSrvMng::GetMaintainenceAccessIp(const std::string& uid_or_mac, std::pair<std::string, std::string>& ip46_addr_info)
{
    //先查询是否通过接口指定server
    if (!maintainence_acc_ip46_map_.empty())
    {
        auto it = maintainence_acc_ip46_map_.find(uid_or_mac);
        if (it != maintainence_acc_ip46_map_.end())
        {
            ip46_addr_info = it->second;
            return 0;
        }
    }
    
    return -1;
}

int CLogicSrvMng::GetMaintainenceRtspIp(const std::string& uid_or_mac, std::pair<std::string, std::string>& ip46_addr_info)
{
    //先查询是否通过接口指定server
    if (!maintainence_rtsp_ip46_map_.empty())
    {
        auto it = maintainence_rtsp_ip46_map_.find(uid_or_mac);
        if (it != maintainence_rtsp_ip46_map_.end())
        {
            ip46_addr_info = it->second;
            return 0;
        }
    }
    
    return -1;
}

int CLogicSrvMng::GetMaintainencePbxIp(const std::string& uid_or_mac, std::pair<std::string, std::string>& ip46_addr_info)
{
    //先查询是否通过接口指定server
    if (!maintainence_ops_ip46_map_.empty())
    {
        auto it = maintainence_ops_ip46_map_.find(uid_or_mac);
        if (it != maintainence_ops_ip46_map_.end())
        {
            ip46_addr_info = it->second;
            return 0;
        }
    }
    
    return -1;
}

int CLogicSrvMng::ClearMaintainenceAllIp()
{
    maintainence_acc_ip46_map_.clear();
    maintainence_ops_ip46_map_.clear();
    maintainence_rtsp_ip46_map_.clear();
    return 0;
}

std::string CLogicSrvMng::GetMaintainenceAllIpStr()
{

    auto printMapWithLambda = [](const std::string&key, const std::map<std::string, std::pair<std::string, std::string>>& mymap, std::stringstream &ss) {
        for (const auto& entry : mymap) {
            ss << key << " uid: " << entry.first 
               << ", ip(v4,v6): (" << entry.second.first 
               << ", " << entry.second.second << ")\n";
        }
    };
    std::stringstream ss;
    printMapWithLambda("csmain", maintainence_acc_ip46_map_, ss);
    printMapWithLambda("rtsp", maintainence_rtsp_ip46_map_, ss);
    printMapWithLambda("opensips", maintainence_ops_ip46_map_, ss);
    
    return ss.str();
}


std::pair<std::string, std::string> CLogicSrvMng::GetAccSrv(const std::string& uid_or_mac)
{
    std::pair<std::string, std::string> ip46_info;

	// 是否指定csmain节点
	if (0 == CLogicSrvMng::Instance()->GetMaintainenceAccessIp(uid_or_mac, ip46_info))
	{
		return ip46_info;
	}
    
    uint32_t key_hash = crc32_hash(uid_or_mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(acc_consistent_mutex_);//TODO,chenyc,后续通过shared_ptr进行优化.实现copy-on-wirte
        it = acc_consistent_hash_.find(key_hash);
        if (it == acc_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csmain server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        
        ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
    }
    return ip46_info;
}

std::pair<std::string, std::string> CLogicSrvMng::GetAccDomainSrv(const std::string& uid_or_mac)
{
    //长连接接入服务器分配 暂不分配域名
    return GetAccSrv(uid_or_mac);
    
    /*
    uint32_t key_hash = crc32_hash(uid_or_mac);
    std::pair<std::string, std::string> ip46_info;
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(acc_consistent_mutex_);//TODO,chenyc,后续通过shared_ptr进行优化.实现copy-on-wirte
        it = acc_consistent_hash_.find(key_hash);
        if (it == acc_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csmain server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        if (it->second.domain_.size() > g_domain_detect_len)
        {
            ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
        }
        else
        {
            ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
        }
    }

    return ip46_info;
    */
}

std::pair<std::string, std::string> CLogicSrvMng::GetOpsSrv(const std::string& uid_or_mac)
{
    std::pair<std::string, std::string> ip46_info;
    if (0 == CLogicSrvMng::Instance()->GetMaintainencePbxIp(uid_or_mac, ip46_info))
    {
        return ip46_info;
    }

    uint32_t key_hash = crc32_hash(uid_or_mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(ops_consistent_mutex_);
        it = ops_consistent_hash_.find(key_hash);
        if (it == ops_consistent_hash_.end())
        {
            AK_LOG_WARN << "get ops server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        
        ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
    }
    return ip46_info;
}

std::pair<std::string, std::string> CLogicSrvMng::GetOpsDomainSrv(const std::string& uid_or_mac)
{
    std::pair<std::string, std::string> ip46_info;
    if (0 == CLogicSrvMng::Instance()->GetMaintainencePbxIp(uid_or_mac, ip46_info))
    {
        return ip46_info;
    }
		
    uint32_t key_hash = crc32_hash(uid_or_mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(ops_consistent_mutex_);
        it = ops_consistent_hash_.find(key_hash);
        if (it == ops_consistent_hash_.end())
        {
            AK_LOG_WARN << "get ops server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        if (it->second.domain_.size() > g_domain_detect_len)
        {
            ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
        }
        else
        {
            ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
        }
    }

    return ip46_info;
}


std::pair<std::string, std::string> CLogicSrvMng::GetRtspSrv(const std::string& uid_or_mac)
{
    std::pair<std::string, std::string> ip46_info;
    if (0 == CLogicSrvMng::Instance()->GetMaintainenceRtspIp(uid_or_mac, ip46_info))
    {
        return ip46_info;
    }

    uint32_t key_hash = crc32_hash(uid_or_mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(vrtspd_consistent_mutex_);
        it = vrtspd_consistent_hash_.find(key_hash);
        if (it == vrtspd_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csvrtspd server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        
        ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
    }
    return ip46_info;
}

std::pair<std::string, std::string> CLogicSrvMng::GetRtspDomainSrv(const std::string& uid_or_mac)
{
    std::pair<std::string, std::string> ip46_info;
    if (0 == CLogicSrvMng::Instance()->GetMaintainenceRtspIp(uid_or_mac, ip46_info))
    {
        return ip46_info;
    }
		
    uint32_t key_hash = crc32_hash(uid_or_mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(vrtspd_consistent_mutex_);
        it = vrtspd_consistent_hash_.find(key_hash);
        if (it == vrtspd_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csvrtspd server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }

        if (it->second.domain_.size() > g_domain_detect_len)
        {
            ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
        }
        else
        {
            ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
        }
    }

    return ip46_info;
}

std::pair<std::string, std::string> CLogicSrvMng::GetFtpSrv(const std::string& mac)
{
    std::pair<std::string, std::string> ip46_info;
    
    uint32_t key_hash = crc32_hash(mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(ftp_consistent_mutex_);
        it = ftp_consistent_hash_.find(key_hash);
        if (it == ftp_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csftp server fialed, mac is " << mac;
            return ip46_info;
        }
        
        ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
    }
    return ip46_info;
}

std::pair<std::string, std::string> CLogicSrvMng::GetFtpDomainSrv(const std::string& mac)
{
    std::pair<std::string, std::string> ip46_info;
    
    uint32_t key_hash = crc32_hash(mac);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(ftp_consistent_mutex_);
        it = ftp_consistent_hash_.find(key_hash);
        if (it == ftp_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csftp server fialed, uid_or_mac is " << mac;
            return ip46_info;
        }

        if (it->second.domain_.size() > g_domain_detect_len)
        {
            ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
        }
        else
        {
            ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
        }
    }

    return ip46_info;
}

void CLogicSrvMng::InitAwsSrvList()
{
    //AWS csmain
    std::vector<std::string> csmain_ips;
    std::string csmain_ip = gstAWSConf.csmain_ip;
    SplitString(csmain_ip, ";", csmain_ips);
    int csmain_node_num = csmain_ips.size();
    std::vector<std::string> csmain_domains;
    std::string csmain_domain = gstAWSConf.csmain_domain;
    SplitString(csmain_domain, ";", csmain_domains);
    std::vector<std::string> csmain_ipv6s;
    std::string csmain_ipv6 = gstAWSConf.csmain_ipv6;
    SplitString(csmain_ipv6, ";", csmain_ipv6s);
    for (int i = 0; i < csmain_node_num; i++)
    {
        std::string ipv4_info = csmain_ips[i];
        std::string ipv6_info = csmain_ipv6s[i];
        std::string domain_info = csmain_domains[i];

        //构建一致性哈希的环
        {
            std::lock_guard<std::mutex> lock(aws_acc_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                aws_acc_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }

    //AWS csvrtsp
    std::vector<std::string> csvrtsp_ips;
    std::string csvrtsp_ip = gstAWSConf.csvrtsp_ip;
    SplitString(csvrtsp_ip, ";", csvrtsp_ips);
    int csvrtsp_node_num = csvrtsp_ips.size();
    std::vector<std::string> csvrtsp_domains;
    std::string csvrtsp_domain = gstAWSConf.csvrtsp_domain;
    SplitString(csvrtsp_domain, ";", csvrtsp_domains);
    std::vector<std::string> csvrtsp_ipv6s;
    std::string csvrtsp_ipv6 = gstAWSConf.csvrtsp_ipv6;
    SplitString(csvrtsp_ipv6, ";", csvrtsp_ipv6s);
    for (int i = 0; i < csvrtsp_node_num; i++)
    {
        std::string ipv4_info = csvrtsp_ips[i];
        std::string ipv6_info = csvrtsp_ipv6s[i];
        std::string domain_info = csvrtsp_domains[i];

        //构建一致性哈希的环
        {
            std::lock_guard<std::mutex> lock(aws_vrtspd_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                aws_vrtspd_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }

    //AWS ops
    std::vector<std::string> ops_ips;
    std::string ops_ip = gstAWSConf.pbx_ip;
    SplitString(ops_ip, ";", ops_ips);
    int ops_node_num = ops_ips.size();
    std::vector<std::string> ops_domains;
    std::string ops_domain = gstAWSConf.pbx_domain;
    SplitString(ops_domain, ";", ops_domains);
    std::vector<std::string> ops_ipv6s;
    std::string ops_ipv6 = gstAWSConf.pbx_ipv6;
    SplitString(ops_ipv6, ";", ops_ipv6s);
    for (int i = 0; i < ops_node_num; i++)
    {
        std::string ipv4_info = ops_ips[i];
        std::string ipv6_info = ops_ipv6s[i];
        std::string domain_info = ops_domains[i];

        //构建一致性哈希的环
        {
            std::lock_guard<std::mutex> lock(aws_ops_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                aws_ops_consistent_hash_.insert(vnode_t(ipv4_info, ipv6_info, domain_info, j));
            }
        }
    }
}

std::pair<std::string, std::string> CLogicSrvMng::GetAwsAccSrv(const std::string& uid_or_mac)
{
    uint32_t key_hash = crc32_hash(uid_or_mac);
    std::pair<std::string, std::string> ip46_info;
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(aws_acc_consistent_mutex_);
        it = aws_acc_consistent_hash_.find(key_hash);
        if (it == aws_acc_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csmain server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
    }
    return ip46_info;
}

std::pair<std::string, std::string> CLogicSrvMng::GetAwsAccDomainSrv(const std::string& uid_or_mac)
{
    //长连接接入服务器分配 暂不分配域名
    return GetAwsAccSrv(uid_or_mac);

    /*
    uint32_t key_hash = crc32_hash(uid_or_mac);
    std::pair<std::string, std::string> ip46_info;
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(aws_acc_consistent_mutex_);
        it = aws_acc_consistent_hash_.find(key_hash);
        if (it == aws_acc_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csmain server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
    }

    return ip46_info;
    */
}


std::pair<std::string, std::string> CLogicSrvMng::GetAwsRtspDomainSrv(const std::string& uid_or_mac)
{
    uint32_t key_hash = crc32_hash(uid_or_mac);
    std::pair<std::string, std::string> ip46_info;
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(aws_vrtspd_consistent_mutex_);
        it = aws_vrtspd_consistent_hash_.find(key_hash);
        if (it == aws_vrtspd_consistent_hash_.end())
        {
            AK_LOG_WARN << "get csvrtspd server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }

        ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
    }

    return ip46_info;
}



std::pair<std::string, std::string> CLogicSrvMng::GetAwsOpsDomainSrv(const std::string& uid_or_mac)
{
    uint32_t key_hash = crc32_hash(uid_or_mac);
    std::pair<std::string, std::string> ip46_info;
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(aws_ops_consistent_mutex_);
        it = aws_ops_consistent_hash_.find(key_hash);
        if (it == aws_ops_consistent_hash_.end())
        {
            AK_LOG_WARN << "get ops server fialed, uid_or_mac is " << uid_or_mac;
            return ip46_info;
        }
        if (it->second.domain_.size() > 0)
        {
            ip46_info = std::make_pair(it->second.domain_, it->second.ipv6_);
        }
        else
        {
            ip46_info = std::make_pair(it->second.ipv4_, it->second.ipv6_);
        }
    }

    return ip46_info;
}


