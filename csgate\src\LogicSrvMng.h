#ifndef __CSGATE_LOGIC_SRV_MANAGE_H__
#define __CSGATE_LOGIC_SRV_MANAGE_H__

#include <boost/noncopyable.hpp>
#include <map>
#include <string>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "ConsistentHashMap.hpp"
#include <mutex>

struct vnode_t
{
    vnode_t() {}
    vnode_t(std::string ipv4, std::string ipv6, std::string domain, std::size_t v)
        : ipv4_(ipv4), ipv6_(ipv6), domain_(domain), vnode_id_(v) {}

    std::string to_str() const
    {
        return boost::str(boost::format("%1%-%2%") % ipv4_ % vnode_id_);
    }
    std::string ipv4_;
    std::string ipv6_;
    std::string domain_;
    std::size_t vnode_id_;
};
//只对ipv4做hash
struct crc32_hasher
{
    uint32_t operator()(const vnode_t& node)   //这样就可以这样了: size_type hash = hasher_(node);
    {
        boost::crc_32_type ret;
        std::string vnode = node.to_str();
        ret.process_bytes(vnode.c_str(), vnode.size()); //对vnode的str进行哈希,得到虚拟节点的key
        return ret.checksum();
    }
    typedef uint32_t result_type;
};

//通过一致性哈希算法进行负载均衡
class CLogicSrvMng : boost::noncopyable
{
public:
    typedef ConsistentHash<vnode_t, crc32_hasher> consistent_hash_t;
    static const int kVnodeNum = 5000;
public:
    CLogicSrvMng() {}
    ~CLogicSrvMng() {}
    static CLogicSrvMng* Instance();
    uint32_t crc32_hash(const std::string key);
    //下面四个函数保证只有一个线程会操作
    void InitAccSrvList(const std::vector<std::string>& csmain_addrs);
    void InitRtspSrvList(const std::vector<std::string>& csvrtspd_addrs);
    void InitOpsSrvList(const std::vector<std::string>& ops_addrs);
    void InitFtpSrvList(const std::vector<std::string>& ftp_addrs);
    void UpdateAccSrvList(const std::vector<std::string>& csmain_addrs);
    void UpdateRtspSrvList(const std::vector<std::string>& csvrtspd_addrs);
    void UpdateOpsSrvList(const std::vector<std::string>& ops_addrs);
    void UpdateFtpSrvList(const std::vector<std::string>& ftp_addrs);
    //根据负载均衡算法获取其中一台逻辑服务器
    std::pair<std::string, std::string> GetAccSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetAccDomainSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetRtspSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetRtspDomainSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetOpsSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetOpsDomainSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetFtpSrv(const std::string& mac);
    std::pair<std::string, std::string> GetFtpDomainSrv(const std::string& mac);
    void InitAwsSrvList();
    std::pair<std::string, std::string> GetAwsAccSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetAwsAccDomainSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetAwsRtspDomainSrv(const std::string& uid_or_mac);
    std::pair<std::string, std::string> GetAwsOpsDomainSrv(const std::string& uid_or_mac);

    void SetMaintainenceAccIp46Map(const std::string& uid_or_mac, const std::string& csmain_ipv4, const std::string& csmain_ipv6);
    void SetMaintainenceRtspIp46Map(const std::string& uid_or_mac, const std::string& csvrtsp_ipv4, const std::string& csvrtsp_ipv6);
    void SetMaintainenceOpsIp46Map(const std::string& uid_or_mac, const std::string& pbx_ipv4,  const std::string& pbx_ipv6);
    int GetMaintainenceAccessIp(const std::string& uid_or_mac, std::pair<std::string, std::string>& ip46_addr_info);
    int GetMaintainenceRtspIp(const std::string& uid_or_mac, std::pair<std::string, std::string>& ip46_addr_info);
    int GetMaintainencePbxIp(const std::string& uid_or_mac, std::pair<std::string, std::string>& ip46_addr_info);
    int ClearMaintainenceAllIp();
    std::string GetMaintainenceAllIpStr();
private:
    static CLogicSrvMng* instance_;
    std::mutex acc_consistent_mutex_;
    consistent_hash_t acc_consistent_hash_;
    std::mutex vrtspd_consistent_mutex_;
    consistent_hash_t vrtspd_consistent_hash_;
    std::mutex ops_consistent_mutex_;
    consistent_hash_t ops_consistent_hash_;
    std::mutex ftp_consistent_mutex_;
    consistent_hash_t ftp_consistent_hash_;

    std::mutex aws_acc_consistent_mutex_;
    consistent_hash_t aws_acc_consistent_hash_;
    std::mutex aws_vrtspd_consistent_mutex_;
    consistent_hash_t aws_vrtspd_consistent_hash_;
    std::mutex aws_ops_consistent_mutex_;
    consistent_hash_t aws_ops_consistent_hash_;

    //运维接口指定的
	std::map<std::string, std::pair<std::string, std::string>> maintainence_acc_ip46_map_;
    std::map<std::string, std::pair<std::string, std::string>> maintainence_ops_ip46_map_;
    std::map<std::string, std::pair<std::string, std::string>> maintainence_rtsp_ip46_map_;
};

#endif // __CSGATE_LOGIC_SRV_MANAGE_H__

