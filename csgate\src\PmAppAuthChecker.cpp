#include "PmAppAuthChecker.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/AccountUserInfo.h"
#include "Caesar.h"
#include "AkLogging.h"
#include "util_string.h"
#include "Md5.h"
#include "Dao.h"

int PmAppAuthChecker::HandleCheckAuthToken()
{
    if (0 != strcmp(auth_info_.auth_token, token_info_.auth_token))
    {
        return csgate::ERR_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}

int PmAppAuthChecker::HandleCheckRefreshToken()
{
    if (0 != strcmp(auth_info_.refresh_token, token_info_.app_refresh_token))
    {
        return csgate::ERR_REFRESH_TOKEN_INVALID;
    }
    return csgate::ERR_SUCCESS;
}

int PmAppAuthChecker::HandleCheckUserPassword()
{
    std::string username;
    //凯撒解密
    char user_tmp[128];
    snprintf(user_tmp, sizeof(user_tmp), "%s", auth_info_.user);
    akuvox_encrypt::CaesarDecry(user_tmp);
    username = user_tmp;
    TrimString(username);

    UserInfoAccount account_user_info;
    memset(&account_user_info, 0, sizeof(account_user_info));
    // 先在AccountUserInfo表中查询
    if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(username, account_user_info))
    {
        //密码错误直接return
        if (!csgate::PasswordCorrect(auth_info_.passwd, account_user_info.passwd))
        {
            AK_LOG_WARN << "continuation error, pm app passwd error, login_account:" << username;
            return csgate::ERR_PASSWD_INVALID;
        }
    }
    else
    {
        //再到PersonalAccountUserInfo表查询pm app账号是否存在
        PerAccountUserInfo user_info;
        if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(username, user_info))
        {
            //密码错误直接return
            if (!csgate::PasswordCorrect(auth_info_.passwd, user_info.passwd))
            {
                AK_LOG_WARN << "continuation error, pm app passwd error, login_account:" << username;
                return csgate::ERR_PASSWD_INVALID;
            }
        }
    }
    
    //使用主站点进行token续时    
    return csgate::ERR_SUCCESS;
}