#include "AppAuthChecker.h"
#include "dbinterface/Token.h"

class PmAppAuthChecker : public AppAuth<PERSON>hecker
{

private:
    TokenInfo token_info_;

public:
    PmAppAuthChecker() : AppAuthChecker() {
        memset(&token_info_, 0, sizeof(token_info_));
    }
    PmAppAuthChecker(const TokenInfo& token_info, const AuthInfo& auth_info)
                                    :  AppAuthChecker(auth_info), token_info_(token_info) {}

private:
    virtual int HandleCheckAuthToken() override;
    virtual int HandleCheckUserPassword() override;
    virtual int HandleCheckRefreshToken() override;
};