#include "ServerMng.h"
#include "Md5.h"
#include <string>
#include <evpp/logging.h>
#include "Dao.h"
#include "util.h"
#include "Caesar.h"
#include "AkLogging.h"
#include "CsgateConf.h"
#include <random>

extern CSGATE_CONF gstCSGATEConf;
namespace csgate
{

std::string GetRandomString(int length)
{
    const char CH[] = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    // srand((unsigned)time(NULL));
    // 使用随机数引擎和均匀分布
    std::random_device rd;  // 获取随机数种子
    std::mt19937 gen(rd()); // 初始化随机数生成器
    std::uniform_int_distribution<> dis(0, sizeof(CH) - 2); // 生成 [0, sizeof(CH)-2] 的均匀分布
    std::string str = "";
    for (int i = 0; i < length; ++i)
    {
        int x = dis(gen);
        str += CH[x];
    }
    return str;
}


//app每次调用login接口登陆,都会生成一个不同的随机token
int GetToken(const std::string& user, const std::string& main_user, std::string& token, float ver)
{
    int ret = 0;
    //V4.2改成随机的  不允许app/设备多地登陆。
    std::string user_token;
    CreateToken(main_user, user_token, TokenType::AppToken);
    //更新数据库中账号的AppToken
    ret = csgate::DaoUpdateToken(user, main_user, user_token, ver);
    if (0 != ret)
    {
        LOG_WARN << "get " << user << " token error";
        return -1;
    }
    token = user_token;
    return 0;
}

//ins app token获取并更新到数据库
int GetInsToken(const std::string& user, std::string& token, const std::string& userinfo_uuid)
{
    int ret = 0;
    std::string user_token;
    CreateToken(user, user_token, TokenType::AppToken);
    //插入或更新数据库中Ins账号的AppToken
    ret = csgate::DaoInsertOrUpdateInsToken(userinfo_uuid, user_token);
    if (0 != ret)
    {
        LOG_WARN << "update " << user << " token error";
        return -1;
    }
    token = user_token;
    return 0;
}

int UpdateInsTokenRenewInfo(const std::string& userinfo_uuid, TokenRenewInfo& token_renew_info)
{
    //插入或更新Token
    if (0 != csgate::DaoInsertOrUpdateInsRenewToken(userinfo_uuid, token_renew_info))
    {
        LOG_WARN << "update ins token error. userinfo_uuid:" << userinfo_uuid;
        return -1;
    }

    return 0;
}

int GetAuthToken(const std::string& user, const std::string& main_user, std::string& token)
{
    int ret = 0;
    std::string auth_token;
    CreateToken(main_user, auth_token, TokenType::AuthToken);
    //更新数据库中账号的AuthToken
    ret = csgate::DaoUpdateAuthToken(user, main_user, auth_token);
    if (0 != ret)
    {
        LOG_WARN << "get " << user << " token error";
        return -1;
    }
    token = auth_token;
    return 0;
}

void GetTokenRenewInfo(const std::string& user, TokenRenewInfo& token_info)
{
    //token获取
    std::string token;
    CreateToken(user, token, TokenType::AppToken);

    //RefreshToken获取
    std::string refresh_token;
    CreateToken(user, refresh_token, TokenType::RefreshToken);

    Snprintf(token_info.token, sizeof(token_info.token), token.c_str());
    token_info.valid_time = gstCSGATEConf.token_valid_time; //token不续时，过期时间7天
    Snprintf(token_info.refresh_token, sizeof(token_info.refresh_token), refresh_token.c_str());
}

//type: 0-AppToken 1-AuthToken 2-RefreshToken
void CreateToken(const std::string &user, std::string &token, int type)
{
    std::string user_token_head = akuvox_encrypt::MD5(user).toStr();
    std::string user_token;
    if (type == TokenType::AppToken) 
    {
        user_token = user_token_head.substr(0, 8);//取0-8位
    } 
    else if (type == TokenType::AuthToken)
    {
        user_token = user_token_head.substr(8, 8);//取8-16位
    }
    else if (type == TokenType::RefreshToken)
    {
        user_token = user_token_head.substr(16, 8);//取16-24位
    }
    user_token += GetRandomString(24);
    token = user_token;
}
}
