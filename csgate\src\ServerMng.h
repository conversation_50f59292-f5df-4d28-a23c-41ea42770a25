#ifndef __CSGATE_SERVER_MNG_H__
#define __CSGATE_SERVER_MNG_H__

#include <string>
#include "memory.h"
#include "dbinterface/Token.h"
#include "dbinterface/InsToken.h"

namespace csgate
{
enum TokenType
{
    AppToken,
    AuthToken,
    RefreshToken,
};

int GetToken(const std::string& user, const std::string& main_user, std::string& token, float ver = 0.0);
int GetAuthToken(const std::string& user, const std::string& main_user, std::string& token);
int GetInsToken(const std::string& user, std::string& token,  const std::string& userinfo_uuid);
int UpdateInsTokenRenewInfo(const std::string& userinfo_uuid, TokenRenewInfo& token_renew_info);
void GetTokenRenewInfo(const std::string& user, TokenRenewInfo& token_info);
//type: 0-AppToken 1-AuthToken 2-RefreshToken
void CreateToken(const std::string &user, std::string& token, int type);

}
#endif //__CSGATE_SERVER_MNG_H__
