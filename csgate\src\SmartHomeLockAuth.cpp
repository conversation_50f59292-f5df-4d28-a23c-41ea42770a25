#include "SmartHomeLockAuth.h"
#include "dbinterface/SmartLock.h"

SL20LockAuth::SL20LockAuth(const std::string& mac)
{
    SmartLockInfo sl20_lock_info;
    if (0 == dbinterface::SmartLock::GetSmartLockInfoByMac(mac, sl20_lock_info))
    {
        lock_exist_ = true;
    }
    snprintf(auth_info_.username, sizeof(auth_info_.username), "%s-%s", LOCK_DEVICE_STYLE_SL20, sl20_lock_info.mac);
    snprintf(auth_info_.password, sizeof(auth_info_.password), "%s", sl20_lock_info.mqtt_pwd);
    snprintf(auth_info_.client_id, sizeof(auth_info_.client_id), "%s", sl20_lock_info.uuid);
    snprintf(auth_info_.device_id, sizeof(auth_info_.device_id), "%s", sl20_lock_info.uuid);
    snprintf(auth_info_.mac, sizeof(auth_info_.mac), "%s", mac.c_str());
}

