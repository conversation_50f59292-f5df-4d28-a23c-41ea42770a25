#include "SmartHomeLockAuth.h"
#include "dbinterface/SL20Lock.h"

SL20LockAuth::SL20LockAuth(const std::string& mac)
{
    SL20LockInfo sl20_lock_info;
    if (0 == dbinterface::SL20Lock::GetSL20LockInfoByMac(mac, sl20_lock_info))
    {
        lock_exist_ = true;
    }
    snprintf(auth_info_.username, sizeof(auth_info_.username), "%s-%s", LOCK_DEVICE_STYLE_SL20, sl20_lock_info.mac);
    snprintf(auth_info_.password, sizeof(auth_info_.password), "%s", sl20_lock_info.mqtt_pwd);
    snprintf(auth_info_.client_id, sizeof(auth_info_.client_id), "%s", sl20_lock_info.uuid);
    snprintf(auth_info_.device_id, sizeof(auth_info_.device_id), "%s", sl20_lock_info.uuid);
    snprintf(auth_info_.mac, sizeof(auth_info_.mac), "%s", mac.c_str());
}

