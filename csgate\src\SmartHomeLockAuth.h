#ifndef __SMARTHOME_LOCK_AUTH_H__
#define __SMARTHOME_LOCK_AUTH_H__

#include <memory>
#include <cstring>
#include "Md5.h"
#include "AES256.h"

#define LOCK_DEVICE_STYLE_SL20 "SL20"

typedef struct LockAuthInfo_T
{
    char client_id[64];
    char username[64];
    char password[64];
    char device_id[64];
    char mac[64];
    LockAuthInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} LockAuthInfo;

//基类
class LockAuth {
public:
    LockAuth() : lock_exist_(false) {

    };
    virtual ~LockAuth() = default;
    virtual bool AuthCheck(const std::string& passwd, bool is_logout = false)
    {
        std::string dev_mac = auth_info_.mac;
        dev_mac += DEFAULT_CSGATE_KEY_MASK;
        bool pwd_check_result = (akuvox_encrypt::MD5(dev_mac).toStr() == passwd);
        return is_logout ? pwd_check_result : (pwd_check_result && lock_exist_);
    }
    virtual std::string GetClientId() {
        return auth_info_.client_id;
    }
    virtual std::string GetUserName() {
        return auth_info_.username;
    }
    virtual std::string GetPassword() {
        return auth_info_.password;
    }
    virtual std::string GetDeviceId() {
        return auth_info_.device_id;
    }
    
protected:
    LockAuthInfo auth_info_;
    bool lock_exist_;
};


//各种锁的子类
class SL20LockAuth : public LockAuth {
public:
    SL20LockAuth(const std::string& mac);
    ~SL20LockAuth() = default;
};


//工厂类
class LockAuthFactory {
public:
    std::unique_ptr<LockAuth> CreateLockAuth(const std::string& type, const std::string& mac) {
        if (type == LOCK_DEVICE_STYLE_SL20) {
            return std::unique_ptr<LockAuth>(new SL20LockAuth(mac));
        }
        return nullptr;
    }
};


#endif
