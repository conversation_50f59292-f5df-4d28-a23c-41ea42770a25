#include <evpp/tcp_server.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "HttpServer.h"
#include "CsgateConf.h"
#include "ConfigFileReader.h"
#include "GateEtcd.h"
#include "EtcdCliMng.h"
#include "LogicSrvMng.h"
#include "ConnectionPool.h"
#include <evnsq/message.h>
#include <evnsq/producer.h>
#include "AkcsMonitor.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket.h"
#include "CachePool.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "AkcsCommonDef.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/InterfaceComm.h"
#include "KafkaNotifyHandler.h"
#include <KdcDecrypt.h>
#include "Metric.h"

#define GATE_MAX_RLDB_CONN 5
#define MIN_SOCKET_FRAME_BUFFER 10
CSGATE_CONF gstCSGATEConf; //全局配置信息
AWS_CSGATE_CONF gstAWSConf; //全局配置信息
AWS_CSGATE_CONF gstAWSAucloudConf; 
AWS_CSGATE_CONF gstASBJConf; 
AWS_CSGATE_CONF gstEcloud2UcloudConf; //app信息都是6400+的,旧的不处理


CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
KafkaNotifyHandler* g_kafka_notify_handler = nullptr;
evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter = nullptr;
constexpr int kDefaultRate = 1000;
extern const char *g_gate_config_service_key;
extern const char *g_conf_db_addr;
extern const char *g_ak_srv;
int g_etcd_dns_res = 0;
const char *g_csgate_redirect_conf_jcloud = "/usr/local/akcs/csgate/conf/csgate_AWS_jcloud.conf";
const char *g_csgate_redirect_conf_aucloud = "/usr/local/akcs/csgate/conf/csgate_AWS_aucloud.conf";
const char *g_csgate_redirect_conf_asbj = "/usr/local/akcs/csgate/conf/csgate_asbj.conf";
extern const char *g_ak_srv_mqtt_outer_tls;
const char *g_csgate_redirect_conf_e2u = "/usr/local/akcs/csgate/conf/csgate_ecloud2ucloud.conf";

#define PIDFILE "/var/run/csgate.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}
void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csgatelog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csgatelog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csgatelog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csgatelog/FATAL");
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_logbufsecs = 0;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}


/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    conn_pool->Init(gstCSGATEConf.db_ip, gstCSGATEConf.db_username, gstCSGATEConf.db_password,
                    gstCSGATEConf.db_database, gstCSGATEConf.db_port, GATE_MAX_RLDB_CONN, "csgate");
    return 0;
}
//配置中心上相关动作的初始化
void ConfigSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSGATEConf.etcd_server_addrs);

    g_etcd_cli_mng->AddAsyncWatchKey(g_gate_config_service_key, UpdateInnerConfFromConfigSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfigSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_mqtt_outer_tls, UpdateMqttAddrFromConfigSrv);
}

void LoadConfFromConfigSrv()
{
    GateSrvConf csgate_conf_tmp = {0};
    g_etcd_cli_mng->LoadGateSrvOuterConf(csgate_conf_tmp);
    Snprintf(gstCSGATEConf.db_ip, sizeof(gstCSGATEConf.db_ip), csgate_conf_tmp.db_ip);
    gstCSGATEConf.db_port = csgate_conf_tmp.db_port;

    UpdateMqttAddrFromConfigSrv();
}
//serverTag初始化
void ServerTagInit()
{
    Snprintf(gstCSGATEConf.server_tag, sizeof(gstCSGATEConf.server_tag), dbinterface::SystemSetting::GetServerTag().c_str());
}

//modified by chenyc,2022.03.18,涉及到内部分布式组件之间的信息全部上配置中心处理,方便做故障转移
void ConfInit()
{
    //memset(&gstCSGATEConf, 0, sizeof(CSGATE_CONF));//注意： gstCSGATEConf 在前面已经初始化过
    
    //先初始化在配置中心上的配置项,配置项移到配置中心的原则有几个:
    //1、需要频繁调整的配置项
    //2、一旦出现单点故障,需要快速转移的配置项(eg: 内部组件的地址信息)
    LoadConfFromConfigSrv();

    CConfigFileReader config_file("/usr/local/akcs/csgate/conf/csgate.conf");
    
    //先对配置中心的配置项是否正常进行容错
    if((strlen(gstCSGATEConf.db_ip) == 0) || ( gstCSGATEConf.db_port == 0))
    {
        Snprintf(gstCSGATEConf.db_ip, sizeof(gstCSGATEConf.db_ip), config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        gstCSGATEConf.db_port = ATOI(db_port);
    }

    //以下开始是从配置文件里面获取到的k-v值
    Snprintf(gstCSGATEConf.rest_addr, sizeof(gstCSGATEConf.rest_addr), config_file.GetConfigName("csrest_addr"));
    Snprintf(gstCSGATEConf.rest_ipv6, sizeof(gstCSGATEConf.rest_ipv6), config_file.GetConfigName("csrest_ipv6"));
    Snprintf(gstCSGATEConf.rest_ssl_addr, sizeof(gstCSGATEConf.rest_ssl_addr), config_file.GetConfigName("csrest_ssl_addr"));
    Snprintf(gstCSGATEConf.rest_ssl_ipv6, sizeof(gstCSGATEConf.rest_ssl_ipv6), config_file.GetConfigName("csrest_ssl_ipv6"));

    Snprintf(gstCSGATEConf.db_username, sizeof(gstCSGATEConf.db_username), config_file.GetConfigName("db_username"));
    Snprintf(gstCSGATEConf.db_database, sizeof(gstCSGATEConf.db_database), config_file.GetConfigName("db_database"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstCSGATEConf.db_password, sizeof(gstCSGATEConf.db_password), decrypt_db_passwd.c_str());
    
    const char* ipv6_enable = config_file.GetConfigName("ipv6_enable");
    gstCSGATEConf.enable_ipv6 = ATOI(ipv6_enable);

    // kafka配置项
    Snprintf(gstCSGATEConf.kafka_broker_ip, sizeof(gstCSGATEConf.kafka_broker_ip), config_file.GetConfigName("kafka_broker_ip"));
    Snprintf(gstCSGATEConf.notify_web_auditlog_topic, sizeof(gstCSGATEConf.notify_web_auditlog_topic), config_file.GetConfigName("notify_web_auditlog_topic"));

    Snprintf(gstCSGATEConf.web_ip, sizeof(gstCSGATEConf.web_ip), config_file.GetConfigName("web_ip"));
    Snprintf(gstCSGATEConf.web_ipv6, sizeof(gstCSGATEConf.web_ipv6), config_file.GetConfigName("web_ipv6"));
    Snprintf(gstCSGATEConf.web_domain_name, sizeof(gstCSGATEConf.web_domain_name), config_file.GetConfigName("web_domain_name"));
    Snprintf(gstCSGATEConf.web_backend_domain, sizeof(gstCSGATEConf.web_backend_domain), config_file.GetConfigName("web_backend_domain"));

    Snprintf(gstCSGATEConf.api_server, sizeof(gstCSGATEConf.api_server), config_file.GetConfigName("api_server"));

    Snprintf(gstCSGATEConf.svn_version, sizeof(gstCSGATEConf.svn_version), config_file.GetConfigName("svn_version"));

    const char* china = config_file.GetConfigName("is_china_version");
    gstCSGATEConf.is_china_version = ATOI(china);

    Snprintf(gstCSGATEConf.new_gate_addr, sizeof(gstCSGATEConf.new_gate_addr), config_file.GetConfigName("new_gate_addr"));
    Snprintf(gstCSGATEConf.new_gate_ipv6_addr, sizeof(gstCSGATEConf.new_gate_ipv6_addr), config_file.GetConfigName("new_gate_addr_ipv6"));

    const char* area = config_file.GetConfigName("server_area");
    gstCSGATEConf.server_area = ATOI(area);
    Snprintf(gstCSGATEConf.jp_new_gate_addr, sizeof(gstCSGATEConf.jp_new_gate_addr), config_file.GetConfigName("jp_new_gate_addr"));
    Snprintf(gstCSGATEConf.jp_new_gate_ipv6_addr, sizeof(gstCSGATEConf.jp_new_gate_ipv6_addr), config_file.GetConfigName("jp_new_gate_addr_ipv6"));
    Snprintf(gstCSGATEConf.allow_dis_list, sizeof(gstCSGATEConf.allow_dis_list), config_file.GetConfigName("allow_dis_login"));
    
    Snprintf(gstCSGATEConf.update_jp_auth_http_head, sizeof(gstCSGATEConf.update_jp_auth_http_head), config_file.GetConfigName("update_jp_auth_http_head"));
    Snprintf(gstCSGATEConf.update_jp_auth_allow_ip, sizeof(gstCSGATEConf.update_jp_auth_allow_ip), config_file.GetConfigName("update_jp_auth_allow_ip"));
    Snprintf(gstCSGATEConf.update_sjp_auth_allow_ip, sizeof(gstCSGATEConf.update_sjp_auth_allow_ip), config_file.GetConfigName("update_sjp_auth_allow_ip"));

    Snprintf(gstCSGATEConf.au_new_gate_addr, sizeof(gstCSGATEConf.au_new_gate_addr), config_file.GetConfigName("au_new_gate_addr"));
    Snprintf(gstCSGATEConf.au_new_gate_ipv6_addr, sizeof(gstCSGATEConf.au_new_gate_ipv6_addr), config_file.GetConfigName("au_new_gate_ipv6_addr"));
    Snprintf(gstCSGATEConf.update_au_auth_http_head, sizeof(gstCSGATEConf.update_au_auth_http_head), config_file.GetConfigName("update_au_auth_http_head"));
    Snprintf(gstCSGATEConf.update_au_auth_allow_ip, sizeof(gstCSGATEConf.update_au_auth_allow_ip), config_file.GetConfigName("update_au_auth_allow_ip"));

    Snprintf(gstCSGATEConf.asbj_new_gate_addr, sizeof(gstCSGATEConf.asbj_new_gate_addr), config_file.GetConfigName("asbj_new_gate_addr"));
    Snprintf(gstCSGATEConf.asbj_new_gate_ipv6_addr, sizeof(gstCSGATEConf.asbj_new_gate_ipv6_addr), config_file.GetConfigName("asbj_new_gate_ipv6_addr"));
    Snprintf(gstCSGATEConf.update_asbj_auth_http_head, sizeof(gstCSGATEConf.update_asbj_auth_http_head), config_file.GetConfigName("update_asbj_auth_http_head"));
    Snprintf(gstCSGATEConf.update_asbj_auth_allow_ip, sizeof(gstCSGATEConf.update_asbj_auth_allow_ip), config_file.GetConfigName("update_asbj_auth_allow_ip"));

    Snprintf(gstCSGATEConf.e2ucloud_new_gate_addr, sizeof(gstCSGATEConf.e2ucloud_new_gate_addr), config_file.GetConfigName("e2ucloud_new_gate_addr"));
    Snprintf(gstCSGATEConf.e2ucloud_new_gate_ipv6_addr, sizeof(gstCSGATEConf.e2ucloud_new_gate_ipv6_addr), config_file.GetConfigName("e2ucloud_new_gate_ipv6_addr"));
    Snprintf(gstCSGATEConf.update_e2ucloud_auth_http_head, sizeof(gstCSGATEConf.update_e2ucloud_auth_http_head), config_file.GetConfigName("update_e2ucloud_auth_http_head"));
    Snprintf(gstCSGATEConf.update_e2ucloud_auth_allow_ip, sizeof(gstCSGATEConf.update_e2ucloud_auth_allow_ip), config_file.GetConfigName("update_e2ucloud_auth_allow_ip"));


    Snprintf(gstCSGATEConf.file_server, sizeof(gstCSGATEConf.file_server), config_file.GetConfigName("file_server"));
    Snprintf(gstCSGATEConf.file_server_ipv6, sizeof(gstCSGATEConf.file_server_ipv6), config_file.GetConfigName("file_server_ipv6"));

    const char *app_latest_version = config_file.GetConfigName("app_latest_version");
    if (strlen(app_latest_version) > 0)
    {
        Snprintf(gstCSGATEConf.app_latest_version, sizeof(gstCSGATEConf.app_latest_version), app_latest_version);
    }
    else
    {
        Snprintf(gstCSGATEConf.app_latest_version, sizeof(gstCSGATEConf.app_latest_version), APP_LATEST_VERSION);
    }

    const char *force_upgrade_version = config_file.GetConfigName("force_upgrade_version");
    if (strlen(force_upgrade_version) > 0)
    {
        gstCSGATEConf.force_upgrade_version = ATOI(force_upgrade_version);
    }
    else
    {
        gstCSGATEConf.force_upgrade_version = ATOI(APP_FORCE_UPGRADE_VERSION);
    }

    const char *limit_switch = config_file.GetConfigName("limit_switch");
    if (strlen(limit_switch) > 0)
    {
        gstCSGATEConf.limit_switch = ATOI(limit_switch);
    }
    else
    {
        gstCSGATEConf.limit_switch = evpp::rate_limiter::NO_LIMIT;
        gstCSGATEConf.rate = kDefaultRate;
    }

    const char *rate = config_file.GetConfigName("rate");
    if (strlen(rate) > 0)
    {
        gstCSGATEConf.rate = (ATOI(rate) <= 0) ? kDefaultRate : ATOI(rate);
    }
    else
    {
        gstCSGATEConf.rate = kDefaultRate;
    }

    Snprintf(gstCSGATEConf.smart_home_domain, sizeof(gstCSGATEConf.smart_home_domain), config_file.GetConfigName("smart_home_domain"));    

    Snprintf(gstCSGATEConf.slb_net_segment, sizeof(gstCSGATEConf.slb_net_segment), config_file.GetConfigName("slb_net_segment"));
    const char* have_slb = config_file.GetConfigName("have_slb");
    gstCSGATEConf.have_slb = ATOI(have_slb);
    
    const char* aws_redirect = config_file.GetConfigName("aws_redirect");
    gstCSGATEConf.aws_redirect = ATOI(aws_redirect);


    snprintf(gstCSGATEConf.csgate_https, sizeof(gstCSGATEConf.csgate_https), "%s", config_file.GetConfigName("csgate_https"));
    snprintf(gstCSGATEConf.csgate_https_ipv6, sizeof(gstCSGATEConf.csgate_https_ipv6), "%s", config_file.GetConfigName("csgate_https_ipv6"));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    gstCSGATEConf.token_valid_time = ATOI(config_file.GetConfigName("token_valid_time"));

    // 鉴权开关，默认值=1
    gstCSGATEConf.device_login_auth_switch = 1;
    if(strcmp(config_file.GetConfigName("device_login_auth_switch"), "0") == 0){
        gstCSGATEConf.device_login_auth_switch = 0;
    }

    //若小于3分钟 设置3分钟
    if (gstCSGATEConf.token_valid_time < 180)
    {
        gstCSGATEConf.token_valid_time = 180;
    }

    Snprintf(gstCSGATEConf.cloud_env, sizeof(gstCSGATEConf.cloud_env), config_file.GetConfigName("cloud_env"));

    Snprintf(gstCSGATEConf.voice_assistant_server_domain, sizeof(gstCSGATEConf.voice_assistant_server_domain), config_file.GetConfigName("voice_assistant_server_domain"));

    
    CConfigFileReader ip_config("/etc/ip");
    snprintf(gstCSGATEConf.server_inner_ip, sizeof(gstCSGATEConf.server_inner_ip), "%s", config_file.GetConfigName("SERVER_INNER_IP"));
}

void AwsConfInit(AWS_CSGATE_CONF& conf, const std::string& aws_conf_path)
{
    memset(&conf, 0, sizeof(AWS_CSGATE_CONF));

    CConfigFileReader config_file(aws_conf_path.c_str());

    Snprintf(conf.pbx_ip, sizeof(conf.pbx_ip), config_file.GetConfigName("pbx_ip"));
    Snprintf(conf.pbx_domain, sizeof(conf.pbx_domain), config_file.GetConfigName("pbx_domain"));
    Snprintf(conf.pbx_ipv6, sizeof(conf.pbx_ipv6), config_file.GetConfigName("pbx_ipv6"));

    Snprintf(conf.ftp_ip, sizeof(conf.ftp_ip), config_file.GetConfigName("ftp_ip"));
    Snprintf(conf.ftp_domain, sizeof(conf.ftp_domain), config_file.GetConfigName("ftp_domain"));
    Snprintf(conf.ftp_ipv6, sizeof(conf.ftp_ipv6), config_file.GetConfigName("ftp_ipv6"));

    Snprintf(conf.csmain_ip, sizeof(conf.csmain_ip), config_file.GetConfigName("csmain_ip"));
    Snprintf(conf.csmain_domain, sizeof(conf.csmain_domain), config_file.GetConfigName("csmain_domain"));
    Snprintf(conf.csmain_ipv6, sizeof(conf.csmain_ipv6), config_file.GetConfigName("csmain_ipv6"));

    Snprintf(conf.csvrtsp_ip, sizeof(conf.csvrtsp_ip), config_file.GetConfigName("csvrtsp_ip"));
    Snprintf(conf.csvrtsp_domain, sizeof(conf.csvrtsp_domain), config_file.GetConfigName("csvrtsp_domain"));
    Snprintf(conf.csvrtsp_ipv6, sizeof(conf.csvrtsp_ipv6), config_file.GetConfigName("csvrtsp_ipv6"));

    Snprintf(conf.web_ipv6, sizeof(conf.web_ipv6), config_file.GetConfigName("web_ipv6"));
    Snprintf(conf.web_domain, sizeof(conf.web_domain), config_file.GetConfigName("web_domain"));    
    Snprintf(conf.web_backend_domain, sizeof(conf.web_backend_domain), config_file.GetConfigName("web_backend_domain"));
    Snprintf(conf.rest_ipv6, sizeof(conf.rest_ipv6), config_file.GetConfigName("rest_ipv6"));
    Snprintf(conf.rest_addr, sizeof(conf.rest_addr), config_file.GetConfigName("rest_addr"));
    Snprintf(conf.rest_ssl_ipv6, sizeof(conf.rest_ssl_ipv6), config_file.GetConfigName("rest_ssl_ipv6"));
    Snprintf(conf.rest_ssl_addr, sizeof(conf.rest_ssl_addr), config_file.GetConfigName("rest_ssl_addr"));

}

int GlobalObjInit()
{
    g_rate_limiter = new evpp::rate_limiter::RateLimiterTokenBucket(gstCSGATEConf.rate, 1);
    return 0;
}

int OnRouteMQAlarmMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void ProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQAlarmMessage);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}

int InstanceInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/csgate/conf/csgate_redis.conf", "csgateCacheInstances");
}


int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstCSGATEConf.etcd_server_addrs, sizeof(gstCSGATEConf.etcd_server_addrs), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstCSGATEConf.etcd_server_addrs;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
	return 0;
    
}

void DnsResolver()
{
    CConfigFileReader config_file("/usr/local/akcs/csgate/conf/csgate.conf");
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstCSGATEConf.etcd_server_addrs, sizeof(gstCSGATEConf.etcd_server_addrs), config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstCSGATEConf.etcd_server_addrs;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstCSGATEConf.etcd_server_addrs, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}


int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        LOG_WARN << "another csgate has been running in this system.";
        return -1;
    }

    glogInit(argv[0]);

    //一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
	memset(&gstCSGATEConf, 0, sizeof(CSGATE_CONF));
    std::thread dns_thread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }
    
    //added by chenyc,2022.03.18, 配置中心服务初始化
    ConfigSrvInit();
    
    //本地配置项初始化
    ConfInit();
        
    //added by chenyc,2022.03.18, 全局变量的初始化工作全部在这里完成,避免出现未初始化完成及引用的问题
    if(GlobalObjInit() != 0)
    {
        AK_LOG_FATAL << "Init global object failed";
        return -1;
    }

    int ret = DaoInit();
    if (0 != ret)
    {
        AK_LOG_WARN << "DaoInit failed";
        glogClean();
        return -1;
    }
    
    ServerTagInit();

    if (InstanceInit() != 0)
    {
        AK_LOG_FATAL << "init instance failed";
        return -1;
    }    
    
    // 初始化kafka生产者
    KafkaNotifyHandler::InitAuditLogKafkaProducer();

    std::thread mq_produce_thread = std::thread(ProduceInit);
    //起http服务线程
    std::thread http_thread(startHttpServer);
    std::thread http_ipv6_thread(startHttpServerIPV6);
    std::thread http_smart_thread(startHttpSSLServer);

    //亚马逊服务器列表
    AwsConfInit(gstAWSConf, g_csgate_redirect_conf_jcloud);
    CLogicSrvMng::Instance()->InitAwsSrvList();

    AwsConfInit(gstAWSAucloudConf, g_csgate_redirect_conf_aucloud);
    AwsConfInit(gstASBJConf, g_csgate_redirect_conf_asbj);
    AwsConfInit(gstEcloud2UcloudConf, g_csgate_redirect_conf_e2u);
    
    //etcd发现服务
    std::vector<std::string> csmain_addrs;
    if (g_etcd_cli_mng->GetAllAccSrvs(csmain_addrs) != 0)
    {
        AK_LOG_FATAL << "connect to etcd srv failed";
        return -1;
    }
    CLogicSrvMng::Instance()->InitAccSrvList(csmain_addrs);
    
    std::vector<std::string> csvrtspd_addrs;
    if (g_etcd_cli_mng->GetAllRtspSrvs(csvrtspd_addrs) != 0)
    {
        AK_LOG_FATAL << "connect to etcd srv failed";
        return -1;
    }
    CLogicSrvMng::Instance()->InitRtspSrvList(csvrtspd_addrs);
    
    std::vector<std::string> ops_addrs;
    if (g_etcd_cli_mng->GetAllOpsSrvs(ops_addrs) != 0)
    {
        AK_LOG_FATAL << "connect to etcd srv failed";
        return -1;
    }
    CLogicSrvMng::Instance()->InitOpsSrvList(ops_addrs);

    std::vector<std::string> ftp_addrs;
    if (g_etcd_cli_mng->GetAllFtpSrvs(ftp_addrs) != 0)
    {
        AK_LOG_FATAL << "connect to etcd srv failed";
        return -1;
    }
    CLogicSrvMng::Instance()->InitFtpSrvList(ftp_addrs);

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv, UpdateAkcsSrvList);

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csgate is starting";
    
    //开始启动watch loop
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
    
    mq_produce_thread.join();
    http_thread.join();
    http_ipv6_thread.join();
    http_smart_thread.join();
    dns_thread.join();
    glogClean();
    return 0;
}
