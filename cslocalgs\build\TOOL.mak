##########################################################################################
## (C)Copyright 2012-2020 Ringslink .Ltd 
##
##########################################################################################
##

export CC := $(CROSS_TOOLS)gcc
export CXX := $(CROSS_TOOLS)g++
CXX += -std=gnu++11
export AR := $(CROSS_TOOLS)ar
export LD := $(CROSS_TOOLS)ld
export STRIP := $(CROSS_TOOLS)strip
export LDFLAGS := -shared -fpic

ifeq ($(PLATFORMID), $(PLATFORMID_ANDROID))
export LD := $(CROSS_TOOLS)g++
endif
