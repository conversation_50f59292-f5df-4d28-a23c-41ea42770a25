WARNING: Logging before InitGoogleLogging() is written to STDERR
I0829 20:55:07.957933 20008 inner_pre.cc:27] ignore SIGPIPE
E0829 22:46:48.440109 20010 Dao.cpp:30] Failed to insert new record into db,<PERSON><PERSON> is INSERT IGNORE INTO Token (Account, AppToken) VALUES ('zhiming','2bc30291e2fcbb81de7e131cdfbc5bd8');
E0829 22:46:48.440538 20010 ServerMng.cpp:19] get zhiming token error
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0829 22:48:19.407454 20134 inner_pre.cc:27] ignore SIGPIPE
E0829 22:48:19.410197 20135 http_server.cc:46] this=0x7fb1f4c8cda0 http server listen at port 9999 failed. errno=98 Address already in use
csgate: /home/<USER>/evpp-0.7.0/evpp/event_loop_thread.cc:15: evpp::EventLoopThread::~EventLoopThread(): Assertion `IsStopped()' failed.
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0829 22:49:05.568955 20142 inner_pre.cc:27] ignore SIGPIPE
E0829 22:49:42.148356 20144 Dao.cpp:32] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('zhiming','2bc30291e2fcbb81de7e131cdfbc5bd8') ON DUPLICATE KEY UPDATE AppToken = '2bc30291e2fcbb81de7e131cdfbc5bd8;
E0829 22:49:42.148974 20144 ServerMng.cpp:19] get zhiming token error
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0829 22:51:10.326769 20183 inner_pre.cc:27] ignore SIGPIPE
E0829 22:52:06.804864 20186 Dao.cpp:32] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('zhiming','2bc30291e2fcbb81de7e131cdfbc5bd8') ON DUPLICATE KEY UPDATE AppToken = '2bc30291e2fcbb81de7e131cdfbc5bd8';
E0829 22:52:06.805577 20186 ServerMng.cpp:19] get zhiming token error
E0829 22:55:40.553927 20185 Dao.cpp:32] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('zhiming','2bc30291e2fcbb81de7e131cdfbc5bd8') ON DUPLICATE KEY UPDATE AppToken = '2bc30291e2fcbb81de7e131cdfbc5bd8';
E0829 22:55:40.554227 20185 ServerMng.cpp:19] get zhiming token error
E0829 22:57:02.829655 20186 Dao.cpp:32] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('zhiming','2bc30291e2fcbb81de7e131cdfbc5bd8') ON DUPLICATE KEY UPDATE AppToken = '2bc30291e2fcbb81de7e131cdfbc5bd8';
E0829 22:57:02.830090 20186 ServerMng.cpp:19] get zhiming token error
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0829 23:40:43.227574 20481 inner_pre.cc:27] ignore SIGPIPE
E0829 23:43:20.347141 20483 Dao.cpp:32] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('chejjjn','a1b11b7e060f45e4a168931ed20b1156') ON DUPLICATE KEY UPDATE AppToken = 'a1b11b7e060f45e4a168931ed20b1156';
E0829 23:43:20.347785 20483 ServerMng.cpp:19] get chejjjn token error
E0830 23:01:24.869294 20484 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:01:58.929076 20483 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:07:18.384735 20484 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:07:40.904331 20483 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:10:31.804592 20484 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:13:55.498843 20483 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:14:15.954891 20484 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:16:29.295230 20483 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:26:08.829746 20484 HttpServer.cpp:61] http req head 'api-version' is null
E0830 23:39:24.676983 20483 HttpServer.cpp:61] http req head 'api-version' is null
E0831 02:55:20.775984 20484 HttpServer.cpp:61] http req head 'api-version' is null
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0831 18:10:47.358269 25125 inner_pre.cc:27] ignore SIGPIPE
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0831 19:05:43.764153 25157 inner_pre.cc:27] ignore SIGPIPE
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0831 19:05:57.186569 25192 inner_pre.cc:27] ignore SIGPIPE
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0831 19:12:54.130003 25229 inner_pre.cc:27] ignore SIGPIPE
WARNING: Logging before InitGoogleLogging() is written to STDERR
I0831 19:14:44.898067 25263 inner_pre.cc:27] ignore SIGPIPE
E0905 14:44:47.700490 25266 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:47.700978 25266 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:47.701020 25266 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:44:49.591835 25265 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:49.591934 25265 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:49.591967 25265 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:44:51.484812 25266 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:51.485134 25266 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:51.485179 25266 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:44:53.343750 25265 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:53.343839 25265 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:53.343873 25265 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:44:55.258779 25266 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:55.258844 25266 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:55.258875 25266 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:44:57.233448 25265 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:57.233503 25265 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:57.233530 25265 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:44:59.256367 25266 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:44:59.256420 25266 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:44:59.256511 25266 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:45:01.258787 25265 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:45:01.258854 25265 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:45:01.258884 25265 ServerMng.cpp:19] get 0C110532D248 token error
E0905 14:45:03.190522 25266 Rldb.cpp:54] Connect to Database failed: Can't connect to MySQL server on '**************' (111)
E0905 14:45:03.190577 25266 Dao.cpp:33] Failed to insert new record into db,SQL is INSERT INTO Token (Account, AppToken) VALUES ('0C110532D248','e0d6751d823e58b8eb668c1cd9218a59') ON DUPLICATE KEY UPDATE AppToken = 'e0d6751d823e58b8eb668c1cd9218a59';
E0905 14:45:03.190937 25266 ServerMng.cpp:19] get 0C110532D248 token error
