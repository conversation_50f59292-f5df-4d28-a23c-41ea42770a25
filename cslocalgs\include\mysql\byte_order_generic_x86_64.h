/* Copyright (c) 2001, 2012, Oracle and/or its affiliates. All rights reserved.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; version 2 of the License.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA */

/*
  Optimized function-like macros for the x86 architecture (_WIN32 included).
*/
#define sint2korr(A)    (int16) (*((int16 *) (A)))
#define sint3korr(A)    ((int32) ((((uchar) (A)[2]) & 128) ? \
                                  (((uint32) 255L << 24) | \
                                   (((uint32) (uchar) (A)[2]) << 16) |\
                                   (((uint32) (uchar) (A)[1]) << 8) | \
                                   ((uint32) (uchar) (A)[0])) : \
                                  (((uint32) (uchar) (A)[2]) << 16) |\
                                  (((uint32) (uchar) (A)[1]) << 8) | \
                                  ((uint32) (uchar) (A)[0])))
#define sint4korr(A)    (int32)  (*((int32 *) (A)))
#define uint2korr(A)    (uint16) (*((uint16 *) (A)))
/*
  Attention: Please, note, uint3korr reads 4 bytes (not 3)!
  It means, that you have to provide enough allocated space.
*/
#if defined(HAVE_purify) && !defined(_WIN32)
#define uint3korr(A)    (uint32) (((uint32) ((uchar) (A)[0])) +\
                                  (((uint32) ((uchar) (A)[1])) << 8) +\
                                  (((uint32) ((uchar) (A)[2])) << 16))
#else
#define uint3korr(A)    (uint32) (*((unsigned int *) (A)) & 0xFFFFFF)
#endif
#define uint4korr(A)    (uint32) (*((uint32 *) (A)))
#define uint5korr(A)    ((ulonglong)(((uint32) ((uchar) (A)[0])) +\
                                     (((uint32) ((uchar) (A)[1])) << 8) +\
                                     (((uint32) ((uchar) (A)[2])) << 16) +\
                                     (((uint32) ((uchar) (A)[3])) << 24)) +\
                         (((ulonglong) ((uchar) (A)[4])) << 32))
#define uint6korr(A)    ((ulonglong)(((uint32)    ((uchar) (A)[0]))          + \
                                     (((uint32)    ((uchar) (A)[1])) << 8)   + \
                                     (((uint32)    ((uchar) (A)[2])) << 16)  + \
                                     (((uint32)    ((uchar) (A)[3])) << 24)) + \
                         (((ulonglong) ((uchar) (A)[4])) << 32) +       \
                         (((ulonglong) ((uchar) (A)[5])) << 40))
#define uint8korr(A)    (ulonglong) (*((ulonglong *) (A)))
#define sint8korr(A)    (longlong) (*((longlong *) (A)))

#define int2store(T,A)  do { uchar *pT= (uchar*)(T);\
        *((uint16*)(pT))= (uint16) (A);\
    } while (0)

#define int3store(T,A)  do { *(T)=  (uchar) ((A));\
        *(T+1)=(uchar) (((uint) (A) >> 8));\
        *(T+2)=(uchar) (((A) >> 16));\
    } while (0)
#define int4store(T,A)  do { uchar *pT= (uchar*)(T);\
        *((uint32 *) (pT))= (uint32) (A); \
    } while (0)

#define int5store(T,A)  do { *(T)= (uchar)((A));\
        *((T)+1)=(uchar) (((A) >> 8));\
        *((T)+2)=(uchar) (((A) >> 16));\
        *((T)+3)=(uchar) (((A) >> 24));\
        *((T)+4)=(uchar) (((A) >> 32));\
    } while(0)
#define int6store(T,A)  do { *(T)=    (uchar)((A));          \
        *((T)+1)=(uchar) (((A) >> 8));  \
        *((T)+2)=(uchar) (((A) >> 16)); \
        *((T)+3)=(uchar) (((A) >> 24)); \
        *((T)+4)=(uchar) (((A) >> 32)); \
        *((T)+5)=(uchar) (((A) >> 40)); \
    } while(0)
#define int8store(T,A)  do { uchar *pT= (uchar*)(T);\
        *((ulonglong *) (pT))= (ulonglong) (A);\
    } while(0)

