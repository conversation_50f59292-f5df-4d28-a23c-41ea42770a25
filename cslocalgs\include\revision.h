#ifndef __REVISION_H__
#define __REVISION_H__

#define MOD_VERSION                 "1.0.0.11"
/*********************************************
*  New version : 1.0.0.11
*  Old version : 1.0.0.10
*  User       : <PERSON>/<PERSON>
*  Author     : Ben
*  Date       : 2020/10/10
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 对每个Json字段进行安全性判断
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.10
*  Old version : 1.0.0.9
*  User       : <PERSON><PERSON>
*  Author     : Ben
*  Date       : 2020/9/9
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 修复段错误BUG，完善返回json格式
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.9
*  Old version : 1.0.0.8
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/7/1
*  Reason     : 修改mobile-admin/subjects/list 分页获取异常的问题
*  Modified   : 
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.8
*  Old version : 1.0.0.7
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/30
*  Reason     : B-20843-M:输入的PM账号为空，密码正确填写，或者账号正确填写，密码为空，没有任何返回
                B-20850-M:上传图片，返回的JSON数据格式和文档有偏差
                B-20852-M:获取开门记录的命令没有带对应的token也可以正常返回消息
                B-20851-M:账号被异地登录了，设备下载图片的时候依旧返回正确的消息，实际上没有正常下发消息给设备
*  Modified   : 1、修改文件格式为UTF-8，解决一些注释出错的问题
                3、解决部分log没有打印的问题
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.7
*  Old version : 1.0.0.6
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 修改modify异常的问题
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.6
*  Old version : 1.0.0.5
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 提交三方库
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.5
*  Old version : 1.0.0.4
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 更新部署脚本格式不对，导致安装失败的问题
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.4
*  Old version : 1.0.0.3
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 新增数据库的安装
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.3
*  Old version : 1.0.0.2
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 修改自测时发现的一些问题和一些编码规范
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.2
*  Old version : 1.0.0.1
*  User       : Jeffrey/
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 提交安装部署脚本
*  Affected   :
**********************************************/
/*********************************************
*  New version : 1.0.0.1
*  Old version :
*  User       : Jeffrey/yicong.chen
*  Author     : Jeffrey
*  Date       : 2020/6/23
*  Reason     : S-4895-M【沈阳嘉里】新增FaceServer 作为人脸服务器，提供API给第三方调用等
*  Modified   : 提交第一版本
*  Affected   :
**********************************************/

#define MOD_NAME                     "cslocalgs"


#endif