#!/bin/bash

#HOSTNAME="localhost"                                          
#PORT="3305"
USERNAME="cslocalgs"
PASSWORD="Ak@56@<EMAIL>"
DBNAME="cslocalgs"
SOCKPATH="/tmp/mysql.sock"
SQL_FILE='cslocalgs.sql'

if [ $# != 2 ]; then
    echo "usage:$0 <Hostport and Port>"
    exit 1
fi
HOSTNAME=$1
PORT=$2

SCHEMEVER=5500
mysql -u${USERNAME} -p${PASSWORD} -h ${HOSTNAME} -P${PORT} -e "use cslocalgs"
if [ 0 -eq $? ];then
	result=`mysql -u${USERNAME} -p${PASSWORD} -h ${HOSTNAME} -P${PORT} -D$DBNAME -e "select SchemeVer from SystemSetting WHERE ID=1"`
	if [ 0 -eq $? ];then
		SCHEMEVER=`echo ${result} | awk '{print $2}'`
		echo "SCHEMEVER=$SCHEMEVER"
		let SCHEMEVER+=1
		if [ -f "./sql/$SCHEMEVER.sql" ];then
			mysql -u${USERNAME} -p${PASSWORD} -h ${HOSTNAME} -P${PORT} -f < ./sql/$SCHEMEVER.sql
		fi
	else
		let SCHEMEVER+=1
		echo $SCHEMEVER
		mysql -u${USERNAME} -p${PASSWORD} -h ${HOSTNAME} -P${PORT} -f < ./sql/$SCHEMEVER.sql
	fi
else
	mysql -u${USERNAME} -p${PASSWORD} -h ${HOSTNAME} -P${PORT} < ./sql/cslocalgs.sql		
fi


