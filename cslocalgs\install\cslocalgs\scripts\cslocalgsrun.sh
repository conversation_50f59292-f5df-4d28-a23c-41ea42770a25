#!/bin/bash
PROCESS_CSLOCALGS_NAME="cslocalgs"
PROCESS_CSLOCALGS_RUN_SCRIPTS="cslocalgsrun"
PROCESS_CSLOCALGS_PATH=/usr/local/cslocalgs/bin
LOG_FILE=/var/log/cslocalgsrun.log
CSLOCALGS_LISTEN_PORT=9988
CSLOCALGS_SOURCE_FILE=/usr/local/cslocalgs/bin/source
APP_STOP_EMAIL_LIST=/usr/local/freeswitch/scripts/notify_email.conf

FIRST_RUN=1 #第一次启动这个程序 不需要通知运维。
SERVERIP=`cat /etc/ip`
MAIL_LIST=`cat $APP_STOP_EMAIL_LIST | grep APP_STOP_EMAIL_LIST | awk -F'=' '{ print $2 }'`

EMAIL_TIMES=60
DEATH_DETECT_TIME_FILE=/tmp/.${PROCESS_CSLOCALGS_NAME}

app_stop_email() 
{
    email=0
    if [ -f ${DEATH_DETECT_TIME_FILE}* ];then
        time=`ls ${DEATH_DETECT_TIME_FILE}* | awk -F '_' '{print $NF}'`
        unix=`date +%s`
        let time=$time+$EMAIL_TIMES
        if [ $time -lt $unix ];then
            #报警  重新计算时间
            rm  /tmp/.$1*
            touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
            email=1
        fi
    else
        touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
        email=1
    fi
    if [ $email -eq 1 ];then
        processname=$1
        echo "sending email...." >> $LOG_FILE
        echo "${processname} is stopped，请及时排查原因。\nIP:${SERVERIP}" | mutt -s "应用程序停止警告"  ${MAIL_LIST}
    fi
}

run() {
	processname=$1
    processpath=$2
	ps -fe|grep "${processname}" |grep -v grep	|grep -v ${PROCESS_CSLOCALGS_RUN_SCRIPTS}
	if [ $? -ne 0 ]
	then
		date >> $LOG_FILE
		echo "$1 is stopped..." >> $LOG_FILE
		source ${CSLOCALGS_SOURCE_FILE}
		$processpath & > /dev/null
		if [ $FIRST_RUN -ne 1 ];then
			echo "${processname} stop alarm"  >> $LOG_FILE
			#app_stop_email ${processname}
		fi
		sleep 30
	fi

	TCPListeningnum=`netstat -an | egrep ":${CSLOCALGS_LISTEN_PORT}" | awk '$1 == "tcp" && $NF == "LISTEN" {print $0}' |wc -l`
	##UDPListeningnum=`netstat -an | egrep ":${FREESWITCH_PROFILE_PORT}" | awk '$1 == "udp" && $NF == "0.0.0.0:*" {print $0}' |wc -l`
	if [ ${TCPListeningnum} -lt 1 ]
	then
		rm /var/run/cslocalgs.pid
		cslocalgspid=`ps -fe|grep "${processpath}" |grep -v grep |awk '{print $2}'`
		echo "Port:${CSLOCALGS_LISTEN_PORT} not be listend normally" >> $LOG_FILE
		if [ -n "${cslocalgspid}" ];then
			kill -9 ${cslocalgspid}
		fi
	fi
}

while [ 1 ]
do
	run "${PROCESS_CSLOCALGS_NAME}" "${PROCESS_CSLOCALGS_PATH}/${PROCESS_CSLOCALGS_NAME}"
	FIRST_RUN=0
	sleep 3

done
