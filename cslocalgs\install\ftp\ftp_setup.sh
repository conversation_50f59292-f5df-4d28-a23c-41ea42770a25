#!/bin/sh
# vftpd安装脚本
# author yicong.chen

install_vftpd()
{
    sudo apt-get update
    sudo apt-get install vsftpd -y
    #sudo apt-get install -y libdb4.8 libdb4.8++
    sudo dpkg -i ./vftp/libdb4.8_4.8.30-trusty1_amd64.deb
    sudo dpkg -i ./vftp/libdb4.8++_4.8.30-trusty1_amd64.deb
    wget http://cz.archive.ubuntu.com/ubuntu/pool/universe/d/db4.8/db4.8-util_4.8.30-11ubuntu1_amd64.deb
    sudo dpkg -i db4.8-util_4.8.30-11ubuntu1_amd64.deb 
    sudo mkdir /etc/vsftpd.d 
    sudo rm -f /etc/vsftpd.d/vsftpd_login.db 

    sudo db4.8_load -T -t hash -f ./vftp/ftpvusers.txt /etc/vsftpd.d/vsftpd_login.db 
    chmod 600 /etc/vsftpd.d/vsftpd_login.db

    mkdir -p /lib/security/
    ln -s /lib/x86_64-linux-gnu/security/pam_userdb.so /lib/security/pam_userdb.so

    if [ -f /etc/pam.d/vsftpd_login ]; then
        rm -rf /etc/pam.d/vsftpd_login
    fi
    cp ./vftp/vsftpd_login /etc/pam.d/vsftpd_login

    sudo mkdir -p /home/<USER>/akuvox
    sudo useradd vsftpd -d /home/<USER>/bin/false  
    sudo chown -R vsftpd:vsftpd /home/<USER>
    sudo chgrp -R vsftpd /home/<USER>
    sudo chmod -R 700 /home/<USER>

    if [ -f /etc/vsftpd.conf ]; then
        rm -rf /etc/vsftpd.conf
    fi
    cp ./vftp/vsftpd.conf /etc/vsftpd.conf

    if [ -f /etc/vsftpd.d/akuvox ]; then
        rm -rf /etc/vsftpd.d/akuvox
    fi
    cp ./vftp/akuvox /etc/vsftpd.d/akuvox
}

cnt=`netstat -alnp | grep vsftpd | grep LISTEN | wc -l`
if [ "$cnt" -eq "0" ]
then
    install_vftpd
else
    echo "\033[0;32m vftpd is running \033[0m"
fi
sudo service vsftpd restart

 
