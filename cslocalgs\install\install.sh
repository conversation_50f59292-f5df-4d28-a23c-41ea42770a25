#!/bin/sh

PWD=`pwd`
CSLOCALGS_PACKAGES_PATH=${PWD}/cslocalgs
CSLOCALGS_FTP_INSTALL_PATH=${PWD}/ftp
CSLOCALGS_INSTALL_PATH=/usr/local
CSLOCALGS_RUN_SCRIPT=${CSLOCALGS_INSTALL_PATH}/cslocalgs/scripts/cslocalgsrun.sh
CSLOCALGS_RUN_SCRIPT_CONF_PATH=${CSLOCALGS_INSTALL_PATH}/cslocalgs/conf/cslocalgs.conf
CSLOCALGS_LOG_PATH=/var/log/cslocalgslog
CSLOCALGS_RUN_SCRIPT_NAME=cslocalgsrun.sh
CSLOCALGS_BIN_PATH=/usr/local/cslocalgs/bin/cslocalgs
CSLOCALGS_CMD_NAME="cslocalgs"

INSTALLMODE=0
INSTALL_MODE_UPGRADE=1
INSTALL_MODE_INSTALL=0


scriptpid=`ps -fe|grep "${CSLOCALGS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${CSLOCALGS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 1
fi

cslocalgspid=`ps -fe|grep "${CSLOCALGS_CMD_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${cslocalgspid}" ];then
        echo "${CSLOCALGS_CMD_NAME} is running at ${cslocalgspid}, will kill it first."
        kill -kill ${cslocalgspid}
        sleep 2
fi

if [ -f "${CSLOCALGS_BIN_PATH}" ];then
	INSTALLMODE=$INSTALL_MODE_UPGRADE
fi

if [ $INSTALLMODE -eq $INSTALL_MODE_INSTALL ];then
	echo ""
    echo "*********           Install          *********"
    echo ""
	read -p "Please input csgate_addr <**************:9999> :" CSGATE_ADDR
	read -p "Please input Local IPV4 <**************> :" LOCAL_INNER_IP 
	cd ${CSLOCALGS_FTP_INSTALL_PATH}
	bash ${CSLOCALGS_FTP_INSTALL_PATH}/ftp_setup.sh
	cd -
	
elif [ $INSTALLMODE -eq $INSTALL_MODE_UPGRADE ];then
	echo ""
    echo "*********           Upgrade          *********"
    echo ""
	if [ -f "${CSLOCALGS_RUN_SCRIPT_CONF_PATH}" ];then
		cat ${CSLOCALGS_RUN_SCRIPT_CONF_PATH}
		LOCAL_INNER_IP=$(cat ${CSLOCALGS_RUN_SCRIPT_CONF_PATH} |grep listen_ip | sed 's/listen_ip=//g')
		CSGATE_ADDR=$(cat ${CSLOCALGS_RUN_SCRIPT_CONF_PATH} |grep csgate_addr | sed 's/csgate_addr=//g')

		read -p "please comfirm the server is ok(1 means ok, 0 means no):" yes
		if [ $yes -eq 0 ];then
			read -p "Please input csgate_addr <**************:9999> :" CSGATE_ADDR
			read -p "Please input Local IPV4 <**************> :" LOCAL_INNER_IP 
		fi
		
	fi
fi	

cp -rf ${CSLOCALGS_PACKAGES_PATH} ${CSLOCALGS_INSTALL_PATH}

#鏇存柊cslocalgs.conf
sed -i "s/LOCAL_INNER_IP/${LOCAL_INNER_IP}/g" ${CSLOCALGS_RUN_SCRIPT_CONF_PATH}
sed -i "s/CSGATE_ADDR/${CSGATE_ADDR}/g" ${CSLOCALGS_RUN_SCRIPT_CONF_PATH}


#鍒涘缓mysql
bash ./cslocalgs-mysql.sh ${LOCAL_INNER_IP} 3306

if [ ! -d  $CSLOCALGS_LOG_PATH ];then
    mkdir $CSLOCALGS_LOG_PATH
fi

if [ -z "`ps -fe|grep "${CSLOCALGS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup ${CSLOCALGS_RUN_SCRIPT} >/dev/null 2>&1 &
	echo "try to run ${CSLOCALGS_RUN_SCRIPT}..."
fi

echo "cslocalgs install completed!"
echo ""


