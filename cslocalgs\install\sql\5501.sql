use cslocalgs;

CREATE TABLE `SystemSetting` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `SchemeVer` int(11) NOT NULL DEFAULT 0 comment '数据库scheme版本',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=UTF8;
INSERT INTO SystemSetting(ID, SchemeVer) VALUES ('1', 5500);

CREATE TABLE `subjectGroup` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8;

alter table subject add column groupflag tinyint(1) NOT NULL DEFAULT '0';

update SystemSetting set SchemeVer=5501;

