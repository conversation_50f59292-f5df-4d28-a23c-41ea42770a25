#include <iomanip>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <string.h>
#include <linux/if.h>
#include "ClientMng.h"

#include <unistd.h>
//获取本地IP地址
std::string GetLocalIPAddr()
{
    //added by chenyc,2017-04-21,对aliyun的网卡以及本地的测试虚拟机做兼容,如果存在网卡1,就用网卡1的数据,没有的话就用网卡0的IP
    char szLocalIPAddr[16] = {0};
    int inet_sock;
    struct ifreq ifr;
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);
    strncpy(ifr.ifr_name, "eth1", strlen("eth1"));
    ioctl(inet_sock, SIOCGIFADDR, &ifr);
    strncpy(szLocalIPAddr, inet_ntoa(((struct sockaddr_in*)&ifr.ifr_addr)->sin_addr), 16);
    if (szLocalIPAddr[0] == '0')
    {
        strcpy(ifr.ifr_name, "eth0");
        ioctl(inet_sock, SIOCGIFADDR, &ifr);
        strncpy(szLocalIPAddr, inet_ntoa(((struct sockaddr_in*)&ifr.ifr_addr)->sin_addr), 16);
    }
    close(inet_sock);
    return szLocalIPAddr;
}

std::string GetAccessServer()
{
    //return GetLocalIPAddr();
    //CLogicSrvMng::Instance()->GetAccSrv(const  string & uid_or_mac)
	return "";
}
std::string GetRestServer()
{
    return GetLocalIPAddr();
}
