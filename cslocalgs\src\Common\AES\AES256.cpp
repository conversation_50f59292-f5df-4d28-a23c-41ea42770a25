//#include "stdafx.h"
#include <string>
#include <string.h>
#include "AES256.h"
#include "aes.h"
#include <arpa/inet.h>
#include <sstream>
#include "AkLogging.h"
#include "base64/Base64.h"

#define HTONS   htons
#define NTOHS   ntohs
#define HTONL   htonl
#define NTOHL   ntohl



char* strupr(char* str)
{
    char* ptr = str;
    while (*ptr != '\0')
    {
        if (islower(*ptr))
        {
            *ptr = toupper(*ptr);
        }
        ptr++;
    }
    return str;
}
void genKey(char* key, char* keyout, int nsize)
{
    if (key == NULL || keyout == NULL || nsize < KEY_LENGTH)
    {
        return;
    }
    int nSize = strlen(key);
    if (nSize > KEY_LENGTH)
    {
        nSize = KEY_LENGTH;
    }
    ::strncpy(keyout, key, nSize + 1);
    for (int i = nSize; i < KEY_LENGTH; i++)
    {
        keyout[i] = '0';
    }
    return;
}

void AES_256_DECRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return;
    }
    AES_KEY aes_key;
    unsigned char iv[16];
    char szkey[KEY_LENGTH + 1] = {0};
    memset(iv, 0, sizeof(iv)); //初始化向量
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(szkey));   //保证秘钥不超过32字节(即aes 的最高级别 256 bit)，若超过则截断
    AES_set_decrypt_key((const unsigned char*)szkey, 256, &aes_key);
    AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_DECRYPT);
    //CBC模式对于每个待加密的密码块在加密前会先与前一个密码块的密文异或然后再用加密器加密。第一个明文块与一个叫初始化向量的数据块异或
    // AES_cbc_encrypt允许length不是16(128位)的整数倍，不足的部分会用0填充，输出总是16的整数倍。
}

void AES_256_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return;
    }
    AES_KEY aes_key;
    unsigned char iv[16];
    char szkey[KEY_LENGTH + 1] = {0};
    memset(iv, 0, sizeof(iv));
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(key));
    AES_set_encrypt_key((const unsigned char*)key, 256, &aes_key);
    AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_ENCRYPT);
}

void AES_256_ENCRYPT_IV(unsigned char* in, unsigned char* out, unsigned char* key, int nSize)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return;
    }
    AES_KEY aes_key;
    unsigned char iv[16 + 1] = "1234567887654321"; //配合app端
    char szkey[KEY_LENGTH + 1] = {0};
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(key));
    AES_set_encrypt_key((const unsigned char*)key, 256, &aes_key);
    AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_ENCRYPT);
}

int AesEncryptByMac(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize)
{
    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    char* pszOutBuf = new char[*pDataSize + 1];
    memset(pszOutBuf, 0, *pDataSize + 1);

    //生成key
    std::string strAesKey = strMac;
    strAesKey += AES_KEY_DEFAULT_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    char* pszAesKey = NULL;
    ::strncpy(szAesKeyTmp, strAesKey.c_str(), sizeof(szAesKeyTmp) - 1);
    pszAesKey = strupr(szAesKeyTmp);

    AES_256_ENCRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, *pDataSize);
    memcpy(pOut, pszOutBuf, *pDataSize + 1);
    delete []pszOutBuf;
    return 0;
}

int AesEncryptByDefault(char* pIn, char* pOut, int* pDataSize)
{
    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    char* pszOutBuf = new char[*pDataSize + 1];
    memset(pszOutBuf, 0, *pDataSize + 1);
    AES_256_ENCRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, *pDataSize);
    memcpy(pOut, pszOutBuf, *pDataSize + 1);
    delete []pszOutBuf;
    return 0;
}

int AesEncryptPadding5(char* pIn, char* pOut, int* pDataSize, const std::string& strKey)
{
    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    int nPadding;
    int nDatalen = strlen(pIn);
    //填充明文时，如果明文长度原本就是16字节的整数倍，那么除了NoPadding以外，其他的填充方式都会填充一组额外的16字节明文块。
    *pDataSize = (nDatalen / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    char* pszOutBuf = new char[(*pDataSize) + 1];
    char* pszInBuf = new char[(*pDataSize) + 1];
    memset(pszOutBuf, 0, (*pDataSize) + 1);
    memset(pszInBuf, 0, (*pDataSize) + 1);

    /*Padding5*/
    if (nDatalen % 16 > 0)
    {
        nPadding = *pDataSize - nDatalen;
    }
    else
    {
        nPadding = 16;
    }

    memset(pszInBuf, nPadding, (*pDataSize));
    *(pszInBuf +  *pDataSize) = 0;
    memcpy(pszInBuf, pIn, nDatalen);

    //生成key
    std::string strAesKey = strKey;
    strAesKey += DEFAULT_KEY_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    ::strncpy(szAesKeyTmp, strAesKey.c_str(), sizeof(szAesKeyTmp) - 1);

    AES_256_ENCRYPT_IV((unsigned char*)pszInBuf, (unsigned char*)pszOutBuf, (unsigned char*)szAesKeyTmp, *pDataSize);
    memcpy(pOut, pszOutBuf, *pDataSize + 1);
    delete []pszOutBuf;
    delete []pszInBuf;
    return 0;

}

std::string AesEncryptResp(const std::string& resp, const std::string& key)
{
    char* temp_buf = new char[TEMP_BUF_LEN];
    memset(temp_buf, 0, TEMP_BUF_LEN);
    std::string encrypt_resp;

    memcpy(temp_buf, resp.c_str(), TEMP_BUF_LEN);
    int buf_size = strlen(temp_buf);

    AesEncryptPadding5(temp_buf, temp_buf, &buf_size, key);

    char* encrypt_data = nullptr;
    Base64Encrypt(temp_buf, buf_size, &encrypt_data);   //函数内开辟内存了，记得释放
    if (nullptr != encrypt_data)
    {
        encrypt_resp = encrypt_data;
        free(encrypt_data);
    }

    delete []temp_buf;

    return encrypt_resp;
}

int AesDecryptByMac(char* pIn, char* pOut, const std::string& strMac, int nDataSize)
{
    if (pIn == NULL || pOut == NULL)
    {
        return -1;
    }
    //对MSG进行AES加密
    char* pszOutBuf = new char[nDataSize + 1];
    memset(pszOutBuf, 0, nDataSize + 1);

    //生成key
    std::string strAesKey = strMac;
    strAesKey += AES_KEY_DEFAULT_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    char* pszAesKey = NULL;
    ::strncpy(szAesKeyTmp, strAesKey.c_str(), sizeof(szAesKeyTmp) - 1);
    pszAesKey = strupr(szAesKeyTmp);

    AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, nDataSize);
    memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
    delete []pszOutBuf;
    return 0;
}

int AesDecryptByDefault(char* pIn, char* pOut, int nDataSize)
{
    if (pIn == NULL || pOut == NULL)
    {
        return -1;
    }
    //对MSG进行AES加密
    char* pszOutBuf = new char[nDataSize + 1];
    memset(pszOutBuf, 0, nDataSize + 1);
    AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, nDataSize);
    memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
    delete []pszOutBuf;
    return 0;
}


int FileAESEncrypt(const char* pszFilePath, const char* pKey, const char* pszDstFilePath)
{
    if (pszFilePath == NULL || pKey == NULL || pszDstFilePath == NULL)
    {
        return -1;
    }

    int nEncipherTextLen = 0;
    unsigned char* key = (unsigned char*)pKey;
    unsigned char* pEncipherText;
    unsigned char* pOriFileBuf;  //定义文件指针
    unsigned char* pEncryptFileBuf;
    int nHeaderLen = sizeof(AES_FILE_HEADER);
    AES_KEY aes_key;
    unsigned char iv[16];
    memset(iv, 0, sizeof(iv));
    memset(&aes_key, 0, sizeof(AES_KEY));

    FILE* pFile = fopen(pszFilePath, "rb");
    if (NULL == pFile)
    {
        return -1;
    }
    fseek(pFile, 0, SEEK_END); //把指针移动到文件的结尾 ，获取文件长度
    int nOriFileLen = ftell(pFile); //获取文件长度
    int nFillOriFileLen = ((nOriFileLen - 1) / 16 + 1) * 16;

    int nEncryptFileLen = nFillOriFileLen + nHeaderLen;

    nEncipherTextLen = nFillOriFileLen;

    pOriFileBuf = new unsigned char[nEncipherTextLen];
    pEncryptFileBuf = new unsigned char[nEncryptFileLen];
    pEncipherText = new unsigned char[nEncipherTextLen];
    memset(pOriFileBuf, 0, nEncipherTextLen);
    memset(pEncryptFileBuf, 0, nEncryptFileLen);
    memset(pEncipherText, 0, nEncipherTextLen);

    rewind(pFile); //把指针移动到文件开头 因为我们一开始把指针移动到结尾，如果不移动回来 会出错
    fread(pOriFileBuf, 1, nOriFileLen, pFile);
    fclose(pFile);

    //组织文件头数据
    AES_FILE_HEADER* pHeader = (AES_FILE_HEADER*)pEncryptFileBuf;
    pHeader->byMagicMSB = AES_FILE_HEADER_MAGIC_MSB;
    pHeader->byMagicLSB = AES_FILE_HEADER_MAGIC_LSB;
    pHeader->version = HTONS(1);
    pHeader->nFileSize = HTONL(nOriFileLen);

    //加密
    AES_set_encrypt_key(key, 256, &aes_key);
    memset(iv, 0, sizeof(iv));
    AES_cbc_encrypt(pOriFileBuf, pEncipherText, nFillOriFileLen, &aes_key, iv, AES_ENCRYPT);

    memcpy(pEncryptFileBuf + nHeaderLen, pEncipherText, nFillOriFileLen);
    //memcpy(pEncryptFileBuf, pEncipherText, nFillOriFileLen);

    //将文件头和加密后的文件数据写入文件
    FILE* fp = fopen(pszDstFilePath, "wb+");
    if (fp != NULL)
    {
        if ((std::size_t)nEncryptFileLen != fwrite(pEncryptFileBuf, 1, nEncryptFileLen, fp))
        {
        }
        fclose(fp);
        fp = NULL;
    }
    delete []pOriFileBuf;
    delete []pEncryptFileBuf;
    delete []pEncipherText;
    return 0;
}

int FileAESDecrypt(const char* pszFilePath, const char* pKey, const char* pDstFilePath)
{
    if (pszFilePath == NULL || pKey == NULL || pDstFilePath == NULL)
    {
        return -1;
    }
    unsigned char* pOriFileBuf = NULL;
    unsigned char* pEncryptFileBuf = NULL;
    const char* pszKey = pKey;
    int nOriFileLen = 0;
    int nEncryptFileLen = 0;
    int nVersion = 0;
    AES_KEY aes_key;
    unsigned char iv[16];
    int nRet = 0;

    //读取文件数据
    FILE* pFile = fopen(pszFilePath, "rb");
    if (pFile == NULL)
    {
        return -1;
    }

    fseek(pFile, 0, SEEK_END);
    nEncryptFileLen = ftell(pFile);
    pEncryptFileBuf = new unsigned char[nEncryptFileLen];
    rewind(pFile);
    fread(pEncryptFileBuf, 1, nEncryptFileLen, pFile);
    fclose(pFile);

    //判断MAGICSUM
    AES_FILE_HEADER* pHeader = (AES_FILE_HEADER*)pEncryptFileBuf;
    if ((pHeader->byMagicMSB != AES_FILE_HEADER_MAGIC_MSB) || (pHeader->byMagicLSB != AES_FILE_HEADER_MAGIC_LSB))
    {
        nRet = -1;
        goto OpenfileAndDecrypt_Exit;
    }
    //判断version
    nVersion = NTOHS(pHeader->version);
    if (nVersion != 1)
    {
        nRet = -1;
        goto OpenfileAndDecrypt_Exit;
    }
    //获取长度
    nOriFileLen = NTOHL(pHeader->nFileSize);

    //对收到的数据进行安全性判断
    if (nEncryptFileLen < nOriFileLen || (std::size_t)nEncryptFileLen < sizeof(AES_FILE_HEADER))
    {
        nRet = -1;
        //rl_log_err("receive data error");
        goto OpenfileAndDecrypt_Exit;
    }
    //根据version解密对应的数据
    pOriFileBuf = new unsigned char[nEncryptFileLen - sizeof(AES_FILE_HEADER)];

    memset(&aes_key, 0, sizeof(AES_KEY));
    AES_set_decrypt_key((unsigned char*)pszKey, 256, &aes_key);
    memset(iv, 0, sizeof(iv));
    AES_cbc_encrypt(pEncryptFileBuf + sizeof(AES_FILE_HEADER),  pOriFileBuf, nEncryptFileLen - sizeof(AES_FILE_HEADER), &aes_key, iv, AES_DECRYPT);

    //将解密后的文件数据写入文件
    pFile = fopen(pDstFilePath, "wb+");
    if (pFile != NULL)
    {
        if ((std::size_t)nOriFileLen != fwrite(pOriFileBuf, 1, nOriFileLen, pFile))
        {
            //rl_log_err("Write error!");
        }
        fclose(pFile);
        pFile = NULL;
    }
OpenfileAndDecrypt_Exit:
    if (pOriFileBuf != NULL)
    {
        delete []pOriFileBuf;
    }
    if (pEncryptFileBuf != NULL)
    {
        delete []pEncryptFileBuf;
    }
    return nRet;
}



