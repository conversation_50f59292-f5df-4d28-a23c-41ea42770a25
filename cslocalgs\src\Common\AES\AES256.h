#ifndef __AES_INCLUDED__
#define __AES_INCLUDED__

#define KEY_LENGTH 32
#define AES_KEY_DEFAULT_MAC         "0C11050000FF" // ALARM 这一类消息由于有设备/app跟平台通讯，所以统一用统一MAC进行加密
#define AES_ENCRYPT_KEY_V1          "Akuvox55069013!@Akuvox55069013!@"
#define AES_KEY_DEFAULT_MASK        "Akuvox55069013Akuvox"
#define DEFAULT_KEY_MASK                "Akuvox55069013!@"
#define TEMP_BUF_LEN 4096

void genKey(char* key, char* keyout, int nsize);

typedef struct AES_FILE_HEADER_T
{
#define AES_FILE_HEADER_MAGIC_MSB 0xAA
#define AES_FILE_HEADER_MAGIC_LSB 0xAE
    unsigned char byMagicMSB;
    unsigned char byMagicLSB;
    unsigned short version;
    unsigned int nFileSize;
    unsigned int nReserved1;
    unsigned int nReserved2;
} AES_FILE_HEADER;



void AES_256_DECRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize);
void AES_256_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize);
int AesEncryptByMac(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize);
int AesEncryptByDefault(char* pIn, char* pOut, int* pDataSize);
int AesDecryptByMac(char* pIn, char* pOut, const std::string& strMac, int nDataSize);
int AesDecryptByDefault(char* pIn, char* pOut, int nDataSize);
int AesEncryptPadding5(char* pIn, char* pOut, int* pDataSize, const std::string& strKey);

/*http回复报文加密，Add by czw*/
std::string AesEncryptResp(const std::string& resp, const std::string& key);

int OpenfileAndEncrypt(char* pszFilePath);
int OpenfileAndDecrypt(char* pszFilePath);
int FileAESEncrypt(const char* pszFilePath, const char* pKey, const char* pszDstFilePath);
int FileAESDecrypt(const char* pszFilePath, const char* pKey, const char* pDstFilePath);


#endif
