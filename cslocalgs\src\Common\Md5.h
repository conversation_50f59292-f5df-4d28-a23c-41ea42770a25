
#ifndef __MD5_INCLUDED__
#define __MD5_INCLUDED__

#include <stdio.h>
#include <string.h>
#include <string>


#ifndef MD5_SIZE
#define MD5_SIZE    64
#endif

//MD5摘要值结构体
typedef struct MD5VAL_STRUCT
{
    unsigned int a;
    unsigned int b;
    unsigned int c;
    unsigned int d;
} MD5VAL;


//计算字符串的MD5值(若不指定长度则由函数计算)
MD5VAL md5(const char* str, unsigned int size = 0);

//MD5文件摘要
MD5VAL md5File(FILE* fpin);

//MD5文件摘要
std::string GetFileMD5(std::string file_path);

std::string GetFileMD5(char* pFilePath);


//获取内存数据的MD5
std::string GetBufMd5(const char* buf, int nSize);

#endif

