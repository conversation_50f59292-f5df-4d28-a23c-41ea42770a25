#include "Utility.h"
#include "curl.h"
#include "json/json.h"

HTTP_CODE_MAP code_map[] = 
{
	{JSON_RETURN_CODE_1000, "未知错误"},
	{JSON_RETURN_CODE_1001, "请求方法不允许"},
	{JSON_RETURN_CODE_1002, "参数错误"},
	{JSON_RETURN_CODE_1003, "数据库错误"},
	{JSON_RETURN_CODE_1004, "操作不允许"},
	{JSON_RETURN_CODE_1005, "网络错误"},
	{JSON_RETURN_CODE_1006, "无效主题"},
	{JSON_RETURN_CODE_1007, "密码需要更改"},
	{JSON_RETURN_CODE_1008, "二次验证错误"},
	{JSON_RETURN_CODE_1009, "请重新登录"},
	{JSON_RETURN_CODE_1010, "请求超时"},
	
	{JSON_RETURN_CODE_2000, "用户不存在"},
	{JSON_RETURN_CODE_2001, "Group不存在"},
	{JSON_RETURN_CODE_2002, "Group已存在"},
	{JSON_RETURN_CODE_2003, "相机不存在"},
	{JSON_RETURN_CODE_2004, "Core不存在"},
	{JSON_RETURN_CODE_2005, "用户不存在"},
	{JSON_RETURN_CODE_2006, "照片不存在"},
	{JSON_RETURN_CODE_2007, "主机不存在"},
	{JSON_RETURN_CODE_2008, "屏幕不存在"},
	{JSON_RETURN_CODE_2009, "公司不存在"},
	{JSON_RETURN_CODE_2010, "历史记录不存在"},
	{JSON_RETURN_CODE_2011, "用户名已存在"},
	{JSON_RETURN_CODE_2012, "公司已存在"},
	{JSON_RETURN_CODE_2013, "设备不存在，请刷新"},
	
	{JSON_RETURN_CODE_20011, "主机已存在"},
	{JSON_RETURN_CODE_20012, "主机已绑定"},
};


static size_t WriteDateCallBack(void* data, size_t size, size_t nmemb, void* userdata)
{
    if (data == NULL || userdata == NULL)
    {
        return 0;
    }
	size_t realsize = size * nmemb;
    CURL_WRITE_CALLBACK_BUF* pcallBackBuf = (CURL_WRITE_CALLBACK_BUF*)userdata;
	char *ptr = (char*)realloc(pcallBackBuf->pRecvBuf, pcallBackBuf->nRecvSize + realsize + 1);
	if(ptr == NULL)
		return 0;  /* out of memory! */

	pcallBackBuf->pRecvBuf = ptr;
	memcpy(&(pcallBackBuf->pRecvBuf[pcallBackBuf->nRecvSize]), data, realsize);
	pcallBackBuf->nRecvSize += realsize;
	pcallBackBuf->pRecvBuf[pcallBackBuf->nRecvSize] = 0;
	return realsize;   
}

static size_t WriteHeaderDataCallBack(void* ptr, size_t size, size_t nmemb, void* userdata)
{
    if (ptr == NULL || userdata == NULL)
    {
        return 0;
    }
    CURL_WRITE_CALLBACK_BUF* pcallBackBuf = (CURL_WRITE_CALLBACK_BUF*)userdata;
    int nLastSize = strlen((char*)pcallBackBuf->pRecvBuf);
    memcpy((char*)pcallBackBuf->pRecvBuf + nLastSize, ptr, size * nmemb);
    return size * nmemb;
}


int SendRequestUrl(CURL_HTTP_REQUEST* pCurlHttpRequest)
{
    if (pCurlHttpRequest == NULL || pCurlHttpRequest->pUrl == NULL)
    {
        return -1;
    }
    CURL* curl = NULL;
    struct curl_slist* headers = NULL;
    CURLcode res = CURLE_OK;
    curl_global_init(CURL_GLOBAL_ALL);
    curl = curl_easy_init();
    char szUrl[URL_SIZE] = { 0 };
    if (curl == NULL)
    {
        LOG_WARN << "curl is NULL.";
        return -1;
    }
    CURL_WRITE_CALLBACK_BUF callBackHeaderBuf;
    memset(&callBackHeaderBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));
    snprintf(szUrl, sizeof(szUrl), "%s", pCurlHttpRequest->pUrl);
    if (pCurlHttpRequest->pHeadData != NULL) //是否有头数据
    {
        headers = curl_slist_append(headers, pCurlHttpRequest->pHeadData);
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);//设置HTTP头
    }
    if (pCurlHttpRequest->nRequestMethod == HTTP_REQUEST_METHOD_POST)
    {
        curl_easy_setopt(curl, CURLOPT_POST, 1);//设置请求为post类型
        if (pCurlHttpRequest->pPostData != NULL)
        {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, pCurlHttpRequest->pPostData);//post发送的数据
        }
    }
    if (pCurlHttpRequest->pHeadRecvBuf != NULL)
    {
        callBackHeaderBuf.pRecvBuf = pCurlHttpRequest->pHeadRecvBuf;
        curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, WriteHeaderDataCallBack);
        curl_easy_setopt(curl, CURLOPT_HEADERDATA, &callBackHeaderBuf);
    }
    switch (pCurlHttpRequest->nAuthMethod)
    {
        case HTTP_AUTH_METHOD_NONE:
        {
        }
        break;
        case HTTP_AUTH_METHOD_BASIC:
        {
            curl_easy_setopt(curl, CURLOPT_USERNAME, pCurlHttpRequest->pAuthUser);
            curl_easy_setopt(curl, CURLOPT_PASSWORD, pCurlHttpRequest->pAuthPassword);
            curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        }
        break;
        case HTTP_AUTH_METHOD_DIGEST:
        {
            curl_easy_setopt(curl, CURLOPT_USERNAME, pCurlHttpRequest->pAuthUser);
            curl_easy_setopt(curl, CURLOPT_PASSWORD, pCurlHttpRequest->pAuthPassword);
            curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_DIGEST);
        }
        break;
        default:
            break;
    }
    curl_easy_setopt(curl, CURLOPT_URL, szUrl);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, false); //跳过证书验证（https）的网站无法跳过，会报错
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false); //跳过证书验证
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 20);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteDateCallBack);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, pCurlHttpRequest->pRecvData);//这是WriteDateCallBack的第四个参数
    curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);
    res = curl_easy_perform(curl);

    int nRetryCount = 3;
    char szLogMsg[VALUE_SIZE] = { 0 };
    while (res != CURLE_OK && --nRetryCount >= 0)
    {
        LOG_WARN << "request [" << szUrl << "] failed [" << szLogMsg << "] try again";
        res = curl_easy_perform(curl);
        strncpy(szLogMsg, curl_easy_strerror(res), sizeof(szLogMsg));
        if (res != CURLE_OK)
        {
            LOG_WARN << "request [" << szUrl << "] failed [" << szLogMsg << "] try again";
        }
        else
        {
            LOG_DEBUG << "request [" << szUrl << "] succeed.";
            break;
        }
    }
    //LOG_DEBUG << "request [" << szUrl << res==CURLE_OK ? "succeed":"failed";

    /* always cleanup */
    curl_easy_cleanup(curl);
    curl_global_cleanup();
    return (res == CURLE_OK) ? 0 : -1;
}


int Copychar(char* pszDst, int nSize, const char* pszSrc)
{
    if (pszDst == NULL
            || pszSrc == NULL
            || (std::size_t)nSize <= strlen(pszSrc))
    {
        return -1;
    }
    ::strncpy(pszDst, pszSrc, nSize);
    pszDst[nSize - 1] = 0;
    return 0;
}

std::string GetJsonErrorInfo(int code)
{
    std::string error_info("");
	if (code <= 0)
	{
		return error_info;
	}
	
	Json::Value resp_root;
	resp_root["code"] = code;
	int nSize = sizeof(code_map) / sizeof(code_map[0]);
	int i = 0;
	for (i = 0; i < nSize; i++)
	{
		if (code == code_map[i].code)
		{
			resp_root["desc"] = code_map[i].name;
			return resp_root.toStyledString();
		}
	}
	LOG_WARN << resp_root.toStyledString();
	return resp_root.toStyledString();
}

char dec2hexChar(short int n) {
 if ( 0 <= n && n <= 9 ) {
  return char( short('0') + n );
 } else if ( 10 <= n && n <= 15 ) {
  return char( short('A') + n - 10 );
 } else {
  return char(0);
 }
}
 
short int hexChar2dec(char c) {
 if ( '0'<=c && c<='9' ) {
  return short(c-'0');
 } else if ( 'a'<=c && c<='f' ) {
  return ( short(c-'a') + 10 );
 } else if ( 'A'<=c && c<='F' ) {
  return ( short(c-'A') + 10 );
 } else {
  return -1;
 }
}
 
std::string escapeURL(const std::string &URL)
{
	std::string result = "";
	for ( unsigned int i=0; i<URL.size(); i++ ) 
	{
		char c = URL[i];
  		if (
   			( '0'<=c && c<='9' ) ||
   			( 'a'<=c && c<='z' ) ||
   			( 'A'<=c && c<='Z' ) ||
   			c=='/' || c=='.'
   		   ) 
   	    {
   			result += c;
  		} 
		else 
		{
		   int j = (short int)c;
		   if ( j < 0 ) 
		   {
				j += 256;
		   }
		   int i1, i0;
		   i1 = j / 16;
		   i0 = j - i1*16;
		   result += '%';
		   result += dec2hexChar(i1);
		   result += dec2hexChar(i0);
  	    }
 	}
 	return result;
}

std::string deescapeURL(const std::string &URL) 
{
 	std::string result = "";
 	for(unsigned int i=0; i<URL.size(); i++) 
	{
  		char c = URL[i];
  		if ( c != '%' ) 
		{
   			result += c;
  		} 
		else 
		{
		   char c1 = URL[++i];
		   char c0 = URL[++i];
		   int num = 0;
		   num += hexChar2dec(c1) * 16 + hexChar2dec(c0);
		   result += char(num);
  		}
 	}
 	return result;
}

