#ifndef __GSFACE_UTILITY_H__
#define __GSFACE_UTILITY_H__
#pragma once

#include "MsgCommonDefine.h"

typedef struct HTTP_CODE_MAP_T
{
	int code;
	const char* name;
}HTTP_CODE_MAP;

std::string GetJsonErrorInfo(int code);

int SendRequestUrl(CURL_HTTP_REQUEST* pCurlHttpRequest);
int Copychar(char* pszDst, int nSize, const char* pszSrc);
std::string deescapeURL(const std::string &URL);
std::string escapeURL(const std::string &URL);

#endif //__GSFACE_UTILITY_H__