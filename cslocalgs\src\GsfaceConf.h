#ifndef __GSFACE_CONF_H__
#define __GSFACE_CONF_H__

#define GSFACE_CONF_COMMON_LEN 64
#define GSFACE_HTTP_BIND_PORT   9988

typedef struct GSFACE_CONF_T
{
    /* DB配置项 */
    char db_ip[GSFACE_CONF_COMMON_LEN];
    char db_username[GSFACE_CONF_COMMON_LEN];
    char db_password[GSFACE_CONF_COMMON_LEN];
    char db_database[GSFACE_CONF_COMMON_LEN];
    int db_port;

    //listen ip
    char listen_ip[GSFACE_CONF_COMMON_LEN];

    /* ftp服务配置信息 */
    char ftp_server[GSFACE_CONF_COMMON_LEN];

    /*csgate的地址*/
    char csgate_addr[GSFACE_CONF_COMMON_LEN];

    //本地图片的存储地址
    char storage_path[GSFACE_CONF_COMMON_LEN];

    //图片下载地址
    char pic_download_path[GSFACE_CONF_COMMON_LEN];

    //faceID.xml的存放地址
    char face_xml_path[GSFACE_CONF_COMMON_LEN];

    //xml下载地址
    char face_dw_path[GSFACE_CONF_COMMON_LEN];

    int heartbeat;
} GSFACE_CONF;

typedef enum
{
    SUBJECT_TYPE_ALL = -1,
    SUBJECT_TYPE_EMPLOYEE = 0,
    SUBJECT_TYPE_VISITOR,
    SUBJECT_TYPE_VIP,
} SUBJECT_TYPE;


#endif //__GSFACE_CONF_H__

