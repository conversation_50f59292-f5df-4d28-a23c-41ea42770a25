#include "HttpBodyParse.h"
#include "Md5.h"
#include "GsfaceConf.h"
#include "MsgCommonDefine.h"
#include "httplib.h"

extern GSFACE_CONF gstGSFACEConf; //全局配置信息
static char Escape(char ch)
{
    char buffer;
    if (ch < 'A')
    {
        buffer = ch - 48;
    }
    else if (ch < 'a')
    {
        buffer = ch - 55;
    }
    else
    {
        buffer = ch - 87;
    }
    return buffer;
}

static int memstr(char* src, int srclen, char* key, int keylen)
{
    int i, j, k;
    for (k = srclen - keylen + 1, i = 0; i < k; i++)
    {
        for (j = 0; j < keylen; j++)
        {
            if (key[j] != src[i + j])
            {
                break;
            }
        }
        if (j == keylen)
        {
            return i;
        }
    }
    return -1;
}

static void SaveFileByTimestamp(std::string file_name, char* save_name, int name_size, char* src_data, int size)
{
    if (src_data == NULL || save_name == NULL || name_size == 0 || size == 0)
    {
        return;
    }

    //以时间戳+文件名保存文件
    time_t timestamp;
    timestamp = time(NULL);
    std::string strTimestamp = std::to_string(timestamp);

    int index = file_name.rfind(".");
    std::string file_type = file_name.substr(index + 1);

    //做MD5计算
    char tmp[128] = {0};
    snprintf(tmp, sizeof(tmp), "%s_%s", strTimestamp.data(), file_name.data());
    std::string filename_md5 = GetBufMd5(tmp, sizeof(tmp));

    snprintf(save_name, name_size, "%s.%s", filename_md5.data(), file_type.data());

    //保存文件
    char file_path[256] = {0};
    snprintf(file_path, sizeof(file_path), "%s/%s", gstGSFACEConf.storage_path, save_name);

    FILE* file = fopen(file_path, "wb+");
    if (file == NULL)
    {
        LOG_WARN << "open [ " << file_path << " ] error.";
    }
    fwrite(src_data, size, 1, file);
    fclose(file);
    return;
}

//编码格式为'application/x-www-form-urlencoded'的内容
static NODE* AnalyXWWWFromUrlencoded(char* data)
{
    //创建第一个节点
    NODE* head = (NODE*)malloc(sizeof(NODE));
    memset(head, 0, sizeof(NODE));
    NODE* temp = head;
    temp->pName = (char*)data;
    char* b_temp = (char*)data;
    //通过移动、改变部分字符来分离字符串
    while (*data != 0)
    {
        if (*data == '=')
        {
            //'='，则表示name已经结束，value将开始
            *b_temp = 0;
            temp->pValue = b_temp + 1;
        }
        else if (*data == '+')
        {
            //'+'代表空格
            *b_temp = ' ';
        }
        else if (*data == '%')
        {
            //'%'则紧跟两位十六进制表示的特殊字符
            *b_temp = Escape(*(data + 1)) * 16 + Escape(*(data + 2));
            data += 2;
        }
        else if (*data == '&')
        {
            //'&'表示value已经结束，name即将开始
            *b_temp = 0;
            //重新申请内存，存储新内容地址
            temp->next = (NODE*)malloc(sizeof(NODE));
            memset(temp->next, 0, sizeof(NODE));
            temp = temp->next;
            temp->pName = b_temp + 1;
        }
        else
        {
            *b_temp = *data;
        }
        data++;
        b_temp++;
    }
    //最后一个结束符
    *b_temp = 0;
    return head;
}

static NODE* AnalyFormData(char* buffer, int nBufSize, char* bound)
{
    if (bound == NULL)
    {
        return NULL;
    }
    char* start = NULL;
    char* end = NULL;
    char* endtmp = NULL;
    int nCount = 0;
    //第一个节点
    NODE* head = (NODE*)malloc(sizeof(NODE));
    memset(head, 0, sizeof(NODE));
    NODE* temp = head;
    while (((start = strstr(buffer, bound)) != NULL) && (*(start + strlen(bound)) != '-'))
    {
        if (++nCount >= 2)
        {
            temp->next = (NODE*)malloc(sizeof(NODE));
            memset(temp->next, 0, sizeof(NODE));
            temp = temp->next;
        }
        //开始解析内容
        temp->pName = index(buffer, '"') + 1;
        end = strstr(temp->pName, "\"\r\n\r\n");
        endtmp = strstr(temp->pName, bound);
        if (endtmp > end && end != NULL) //是正常的Name
        {
            temp->nType = NODE_TYPE_NORMAL;
            *end = 0;
            temp->pValue = end + 5;
            buffer = strstr(temp->pValue, bound);
            *(buffer - 4) = 0;
            printf("debug:key:Value is %s:%s\n", temp->pName, temp->pValue);
        }
        else if ((endtmp = strstr(temp->pName, "\"; filename=\"")) != NULL) //文件
        {
            temp->nType = NODE_TYPE_FILE;
            *endtmp = 0;
            temp->pValue = endtmp + strlen("\"; filename=\"");
            end = strstr(temp->pValue, "\"\r\n");
            start = strstr(end, "\r\n\r\n") + 4;
            *end = 0;
            int nIndex = memstr(start, nBufSize, bound, strlen(bound));
            buffer = start + nIndex - 2;
            int nSize = buffer - start - 2;
            SaveFileByTimestamp(temp->pValue, temp->szSaveName, sizeof(temp->szSaveName), start, nSize);
            printf("debug:file key:Value is %s:%s\n", temp->pName, temp->pValue);
        }
    }
    return head;
}


INDEX* PostBodyInit(int nSize, const char* pszContentType, const char* pData)
{
    if (nSize == 0 || pszContentType == NULL)
    {
        return NULL;
    }
    INDEX* pIndex = (INDEX*)malloc(sizeof(INDEX));
    NODE* pHead = NULL;
    char* pBuffer = NULL;
    pBuffer = (char*)malloc(nSize + 1);
    memset(pBuffer, 0, nSize + 1);
    memcpy(pBuffer, (char*)pData, nSize);
    char szBoundary[128] = {0};
    if (strcmp(pszContentType, "application/x-www-form-urlencoded") == 0)
    {
        pHead = AnalyXWWWFromUrlencoded(pBuffer);
    }
    else if (strstr(pszContentType, "multipart/form-data;") != NULL)
    {
        std::string boundary;
        auto ret = httplib::detail::parse_multipart_boundary(pszContentType, boundary);
        
        strncpy(szBoundary, boundary.c_str(), sizeof(szBoundary));
        pHead = AnalyFormData(pBuffer, nSize, szBoundary);
    }
    //链表头
    pIndex->pHead = pHead;
    //接受到的字符串
    pIndex->pBuffer = pBuffer;
    return pIndex;
}


char* GetValue(NODE* pHead, char* pName)
{
    NODE* p = NULL;
    while (pHead != NULL)
    {
        if (strcmp(pHead->pName, pName) == 0)
        {
            return pHead->pValue;
        }
        p = pHead->next;
        pHead = p;
    }
    return NULL;
}

char* GetFileName(NODE* pHead, char* pName)
{
    NODE* p = NULL;
    while (pHead != NULL)
    {
        if (strcmp(pHead->pName, pName) == 0 && pHead->nType == NODE_TYPE_FILE)
        {
            return pHead->szSaveName;
        }
        p = pHead->next;
        pHead = p;
    }
    return NULL;
}

void FreePostBody(INDEX* pIndex)
{
    if (pIndex == NULL)
    {
        return;
    }
    NODE* pTemp = pIndex->pHead;
    NODE* p = NULL;
    while (pTemp != NULL)
    {
        p = pTemp->next;
        free(pTemp);
        pTemp = p;
    }
    free(pIndex->pBuffer);
    free(pIndex);
    return;
}



