#ifndef __HTTP_PARSE_H__
#define __HTTP_PARSE_H__

#include <stdio.h>
#include <string.h>
#include <string>
#include <stdlib.h>
#include <iostream>

typedef enum
{
    NODE_TYPE_NORMAL = 0,
    NODE_TYPE_FILE,
} NODE_TYPE;

typedef struct NODE_T
{
    int nType;
    char* pName;
    char* pValue;
    char szSaveName[128];
    struct NODE_T* next;
} NODE;

typedef struct INDEX_T
{
    NODE* pHead;
    char* pBuffer;
} INDEX;

INDEX* PostBodyInit(int nSize, const char* pszContentType, const char* pData);
char* GetValue(NODE* pHead, char* pName);
void FreePostBody(INDEX* pIndex);
char* GetFileName(NODE* pHead, char* pName);


#endif

