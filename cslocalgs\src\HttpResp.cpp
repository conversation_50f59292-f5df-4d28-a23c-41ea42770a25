#include <iostream>
#include <functional>
#include "HttpResp.h"
#include "Errcode.h"
#include "ClientMng.h"
#include "GsfaceConf.h"
#include "Md5.h"
#include "AkLogging.h"
#include "AES256.h"
#include "HttpBodyParse.h"
#include "json/json.h"
#include "HttpApiControl.h"
#include "AccountHandle.h"
#include "SubjectHandle.h"
#include "PhotoHandle.h"
#include "FaceIDXml.h"
#include "Utility.h"
#include "httplib.h"

extern GSFACE_CONF gstGSFACEConf; //全局配置信息

namespace gsface
{

static bool CheckCookieToken(const char* cookie)
{
    if (cookie == NULL)
    {
        return false;
    }
    std::string token;
    //解析出token
    std::string str_cookie(cookie);
    std::size_t begin = str_cookie.find("token=");
    std::size_t end = str_cookie.find(";");
    if (end == std::string::npos)
    {
        token = str_cookie.substr(begin + 6);
    }
    else
    {
        token = str_cookie.substr(begin + 6, end - begin - 6);
    }
    LOG_INFO << "Token [ " << token.data() << " ]";
    return GetAccountHandleInstance()->CheckToken(token.data());
}

static int CreateSubjectObj(Json::Value& obj, FACE_SUBJECT& subject)
{
    obj["avatar"] = subject.avatar;
    obj["come_from"] = subject.come_from;
    obj["department"] = subject.department;
    obj["description"] = subject.description;
    obj["email"] = subject.email;
    obj["end_time"] = subject.end_time;
    obj["gender"] = subject.gender;
    obj["id"] = subject.id;
    obj["interviewee"] = subject.interviewee;
    obj["name"] = subject.name;
    obj["password_reseted"] = false;
    obj["phone"] = subject.phone;
    obj["birthday"] = subject.birthday;
    obj["entry_date"] = subject.entry_date;
    obj["job_number"] = subject.job_num;
    obj["remark"] = subject.remark;

    int photos_size = subject.photos.size();
    if (photos_size > 0)
    {
        for (int i = 0; i < photos_size; i++)
        {
            obj["photo_ids"].append(subject.photos[i].id);
            Json::Value photos;
            photos["company_id"] = subject.photos[i].company_id;
            photos["id"] = subject.photos[i].id;
            photos["subject_id"] = subject.photos[i].subject_id;
            char url[URL_SIZE] = {0};
            snprintf(url, sizeof(url), "%s/%s", gstGSFACEConf.pic_download_path, subject.photos[i].name);
            photos["url"] = url;
            obj["photos"].append(photos);
        }
    }
    else
    {
        Json::Value empty_array;
        empty_array.append(Json::Value::null);
        empty_array.clear();
        obj["photo_ids"] = empty_array;
        obj["photos"] = empty_array;
    }

	int group_size = subject.group_ids.size();
	if(group_size > 0)
	{
		for (int i = 0; i < group_size; i++)
        {
            Json::Value groups;
            groups["id"] = subject.group_ids[i];
            groups["name"] = "";
            obj["groups"].append(groups);
        }
	}
	else
	{
		Json::Value empty_array;
        empty_array.append(Json::Value::null);
        empty_array.clear();
        obj["groups"] = empty_array;
	}
    obj["purpose"] = subject.purpose;
    obj["start_time"] = subject.start_time;
    obj["subject_type"] = subject.subject_type;
    obj["title"] = subject.title;
    obj["visit_notify"] = false;
    return 0;
}

gsface::HTTPRespCallback ReqLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    const char* content_type = ctx->FindRequestHeader("content-type");
    const evpp::Slice& body = ctx->body();
    char szUserName[VALUE_SIZE] = {0};
    char szPassword[VALUE_SIZE] = {0};
    if (content_type && strcmp(content_type, "application/json") == 0)
    {
        Json::Reader reader;
        Json::Value root;
		if (!reader.parse(body.data(), root))
        {
            LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
			cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
            return ;
        }
		if(!root["password"].isString())
		{
            LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
			cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
            return ;
		}
		if(!root["username"].isString())
		{
            LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
			cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
            return ;
		}
        strncpy(szUserName, root["username"].asCString(), sizeof(szUserName));
        strncpy(szPassword, root["password"].asCString(), sizeof(szPassword));
        //发起http请求
        int result = GetHttpApiControlInstance()->Login(szUserName, szPassword);
        if (result == 0)
        {
            char szToken[VALUE_SIZE] = {0};
            char szExpire[VALUE_SIZE] = {0};
            GetAccountHandleInstance()->GetTokenAndExpire(szUserName, szToken, sizeof(szToken), szExpire, sizeof(szExpire));
            char szCookie[BUFF_SIZE] = {0};
            snprintf(szCookie, sizeof(szCookie), "token=%s; expires=%s; HttpOnly; Path=/", szToken, szExpire);
            ctx->AddResponseHeader("Set-Cookie", szCookie);
            Json::Value resp_root;
            Json::Value data;
            resp_root["code"] = 0;
            data["avatar"];
            data["company_id"] = 1;
            data["id"] = 1;
            data["password_reseted"] = true;
            data["role_id"] = 2;
            data["username"] = szUserName;
            resp_root["data"] = data;
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
            cb(resp_root.toStyledString());
        }
        else if (result == HTTP_RESULT_CODE_ACCOUT_NOT_EXIST)
        {
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
			cb(GetJsonErrorInfo(JSON_RETURN_CODE_1007));
        }
        else
        {
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
			cb(GetJsonErrorInfo(JSON_RETURN_CODE_1008));
        }
    }
    else
    {
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
    }
    return;
};

gsface::HTTPRespCallback ReqGetSubjectHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    if (!gsface::CheckCookieToken(ctx->FindRequestHeader("Cookie")))
    {
        ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1009));
        return;
    }
    std::vector<FACE_SUBJECT> subject_list;
    int total = GetSubjectHandleInstance()->GetSubjectList(SUBJECT_TYPE_ALL, "", "", 0, 0, subject_list);
	(void)total; //warning: unused variable
    Json::Value resp_root;
    resp_root["code"] = 0;
    for (std::size_t i = 0; i < subject_list.size(); i++)
    {
        Json::Value subject_obj;
        gsface::CreateSubjectObj(subject_obj, subject_list[i]);
        resp_root["data"].append(subject_obj);
    }
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    cb(resp_root.toStyledString());
    return;
};

gsface::HTTPRespCallback ReqGetSubjectListHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    if (!gsface::CheckCookieToken(ctx->FindRequestHeader("Cookie")))
    {
        ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1009));
        return;
    }
    std::string req_category = ctx->GetQuery("category");
    std::string req_name = ctx->GetQuery("name");
    std::string req_order = ctx->GetQuery("order");
    int req_page = atoi(ctx->GetQuery("page").data()) > 0 ? atoi(ctx->GetQuery("page").data()) : 1;
    int req_size = atoi(ctx->GetQuery("size").data()) > 0 ? atoi(ctx->GetQuery("size").data()) : 10;
    int subject_type = SUBJECT_TYPE_EMPLOYEE;
    if (!strcmp(req_category.data(), "employee"))
    {
        subject_type = SUBJECT_TYPE_EMPLOYEE;
    }
    else if (!strcmp(req_category.data(), "visitor"))
    {
        subject_type = SUBJECT_TYPE_VISITOR;
    }
    else if (!strcmp(req_category.data(), "vip"))
    {
        subject_type = SUBJECT_TYPE_VIP;
    }
    std::vector<FACE_SUBJECT> subject_list;
	std::string decode_req_name = deescapeURL(req_name);
    int total = GetSubjectHandleInstance()->GetSubjectList(subject_type, decode_req_name, req_order, req_page, req_size, subject_list);
    Json::Value resp_root;
    resp_root["code"] = 0;
    for (std::size_t i = 0; i < subject_list.size(); i++)
    {
        Json::Value subject_obj;
        gsface::CreateSubjectObj(subject_obj, subject_list[i]);
        resp_root["data"].append(subject_obj);
    }
    int total_page = (total + req_size - 1) / req_size;
    Json::Value page;
    page["count"] = total;
    page["current"] = req_page;
    page["size"] = req_size;
    page["total"] = total_page;
    resp_root["page"] = page;
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    cb(resp_root.toStyledString());
    return;
};

gsface::HTTPRespCallback ReqSubjectHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    LOG_INFO << "http req url is [ " << ctx->original_uri() << " ]";
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    const char* content_type = ctx->FindRequestHeader("content-type");
    const evpp::Slice& body = ctx->body();
    
    if (!gsface::CheckCookieToken(ctx->FindRequestHeader("Cookie")))
    {
        ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1009));
        return;
    }
    if (ctx->original_type() == 1) //get表示获取用户信息
    {
        //解析出ID
        std::string subject_uri(ctx->original_uri());
        std::size_t index = subject_uri.find("/subject/");
        if (index != std::string::npos)
        {
            int id = atoi(subject_uri.substr(9).data());
            if (!GetSubjectHandleInstance()->CheckSubjectExistByID(id))
            {
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_2000));
                return;
            }
            FACE_SUBJECT face_subject;
            GetSubjectHandleInstance()->GetSubjectByID(id, face_subject);

            Json::Value resp_root;
            resp_root["code"] = 0;
            Json::Value subject_obj;
            gsface::CreateSubjectObj(subject_obj, face_subject);
            resp_root["data"] = subject_obj;
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
            cb(resp_root.toStyledString());
            return;
        }
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_2000));
    }
    else if (ctx->original_type() == 2) //post 表示新增用户
    {
        if (content_type && strcmp(content_type, "application/json") == 0)
        {
            FACE_SUBJECT subject;
            memset(&subject, 0, sizeof(FACE_SUBJECT));
            Json::Reader reader;
            Json::Value root;
            if (!reader.parse(body.data(), root))
            {
                LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
                return ;
            }
			
			if(root["subject_type"].isNull())
			{
		        LOG_WARN << "json parse subject_type is null";
	            LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			
			if(!root["subject_type"].isInt())
			{
				LOG_WARN << "json parse subject_type failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
				return ;
			}
			
			if(root["name"].isNull())
			{
		        LOG_WARN << "json parse name is null";
	            LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
            	return ;
			}
			
			if(!root["name"].isString())
			{
	            LOG_WARN << "json parse name failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
            	return ;
			}

			if (!root["start_time"].isNull() && !root["start_time"].isInt())
			{
	            LOG_WARN << "json parse start_time failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if (!root["end_time"].isNull() && !root["end_time"].isInt())
			{
	            LOG_WARN << "json parse end_time failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if (!root["purpose"].isNull() && !root["purpose"].isInt())
			{
	            LOG_WARN << "json parse purpose failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if (!root["birthday"].isNull() && !root["birthday"].isInt())
			{
	            LOG_WARN << "json parse birthday failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if (!root["entry_date"].isNull() && !root["entry_date"].isInt())
			{
	            LOG_WARN << "json parse entry_date failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if (!root["gender"].isNull() && !root["gender"].isInt())
			{
	            LOG_WARN << "json parse gender failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}

			//String的安全性判断
			if(!root["email"].isNull() && !root["email"].isString())
			{
	            LOG_WARN << "json parse email failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["phone"].isNull() && !root["phone"].isString())
			{
	            LOG_WARN << "json parse phone failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["avatar"].isNull() && !root["avatar"].isString())
			{
	            LOG_WARN << "json parse avatar failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["department"].isNull() && !root["department"].isString())
			{
	            LOG_WARN << "json parse department failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["title"].isNull() && !root["title"].isString())
			{
	            LOG_WARN << "json parse title failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["description"].isNull() && !root["description"].isString())
			{
	            LOG_WARN << "json parse description failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["interviewee"].isNull() && !root["interviewee"].isString())
			{
	            LOG_WARN << "json parse interviewee failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["come_from"].isNull() && !root["come_from"].isString())
			{
	            LOG_WARN << "json parse come_from failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["job_number"].isNull() && !root["job_number"].isString())
			{
	            LOG_WARN << "json parse job_number failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["remark"].isNull() && !root["remark"].isString())
			{
	            LOG_WARN << "json parse remark failed, data is [ " << body.data() << " ]";
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
	            return ;
			}
			if(!root["group_ids"].isNull())
			{
				int group_size = root["group_ids"].size();
				for(int i=0; i<group_size; i++)
				{
					subject.group_ids.push_back(root["group_ids"][i].asInt());
				}
			}
            subject.start_time = root["start_time"].asInt();
            subject.end_time = root["end_time"].asInt();
            subject.purpose = root["purpose"].asInt();
            subject.birthday = root["birthday"].asInt();
            subject.entry_date = root["entry_date"].asInt();
            subject.gender = root["gender"].asInt();
            subject.subject_type = root["subject_type"].asInt();
            strncpy(subject.email, root["email"].isString() ? root["email"].asCString() : "", sizeof(subject.email));
            strncpy(subject.phone, root["phone"].isString() ? root["phone"].asCString() : "", sizeof(subject.phone));
            strncpy(subject.avatar, root["avatar"].isString() ? root["avatar"].asCString() : "", sizeof(subject.avatar));
            strncpy(subject.department, root["department"].isString() ? root["department"].asCString() : "", sizeof(subject.department));
            strncpy(subject.title, root["title"].isString() ? root["title"].asCString() : "", sizeof(subject.title));
            strncpy(subject.description, root["description"].isString() ? root["description"].asCString() : "", sizeof(subject.description));
            strncpy(subject.interviewee, root["interviewee"].isString() ? root["interviewee"].asCString() : "", sizeof(subject.interviewee));
            strncpy(subject.come_from, root["come_from"].isString() ? root["come_from"].asCString() : "", sizeof(subject.come_from));
            strncpy(subject.job_num, root["job_number"].isString() ? root["job_number"].asCString() : "", sizeof(subject.job_num));
            strncpy(subject.remark, root["remark"].isString() ? root["remark"].asCString() : "", sizeof(subject.remark));
            strncpy(subject.name, root["name"].isString() ? root["name"].asCString() : "", sizeof(subject.name));

            GetSubjectHandleInstance()->AddSubject(subject);

            int photo_size = root["photo_ids"].size();
            if (photo_size > 0)
            {
                for (int i = 0; i < photo_size; i++)
                {
                    GetPhotoHandleInstance()->UpdateSubjectIDByID(subject.id, root["photo_ids"][i].asInt());
                    GetHttpApiControlInstance()->NotifyDownloadPic(subject.id, root["photo_ids"][i].asInt(), subject.group_ids);
                }
            }
            FACE_SUBJECT face_subject;
            memset(&face_subject, 0, sizeof(FACE_SUBJECT));
            GetSubjectHandleInstance()->GetSubjectByID(subject.id, face_subject);
            Json::Value resp_root;
            resp_root["code"] = 0;
            Json::Value subject_obj;
            gsface::CreateSubjectObj(subject_obj, face_subject);
            resp_root["data"] = subject_obj;
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
            cb(resp_root.toStyledString());
            return;
        }
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
    }
    else if (ctx->original_type() == 8) //put 表示更新用户信息
    {
        //解析出ID
        std::string subject_url(ctx->original_uri());
        std::size_t index = subject_url.find("/subject/");
        if (index != std::string::npos)
        {
            int id = atoi(subject_url.substr(9).data());
            if (content_type && strcmp(content_type, "application/json") == 0)
            {
                FACE_SUBJECT subject;
                memset(&subject, 0, sizeof(FACE_SUBJECT));
                Json::Reader reader;
                Json::Value root;
                if (!reader.parse(body.data(), root))
                {
                    LOG_WARN << "json parse failed, data is [ " << body.data() << " ]";
					ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
					cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
                    return ;
                }
                subject.id = id;
                subject.start_time = root["start_time"].asInt();
                subject.end_time = root["end_time"].asInt();
                subject.purpose = root["purpose"].asInt();
                subject.birthday = root["birthday"].asInt();
                subject.entry_date = root["entry_date"].asInt();
                subject.gender = root["gender"].asInt();
                subject.subject_type = root["subject_type"].asInt();
                strncpy(subject.email, root["email"].isString() ? root["email"].asCString() : "", sizeof(subject.email));
                strncpy(subject.phone, root["phone"].isString() ? root["phone"].asCString() : "", sizeof(subject.phone));
                strncpy(subject.avatar, root["avatar"].isString() ? root["avatar"].asCString() : "", sizeof(subject.avatar));
                strncpy(subject.department, root["department"].isString() ? root["department"].asCString() : "", sizeof(subject.department));
                strncpy(subject.title, root["title"].isString() ? root["title"].asCString() : "", sizeof(subject.title));
                strncpy(subject.description, root["description"].isString() ? root["description"].asCString() : "", sizeof(subject.description));
                strncpy(subject.interviewee, root["interviewee"].isString() ? root["interviewee"].asCString() : "", sizeof(subject.interviewee));
                strncpy(subject.come_from, root["come_from"].isString() ? root["come_from"].asCString() : "", sizeof(subject.come_from));
                strncpy(subject.job_num, root["job_number"].isString() ? root["job_number"].asCString() : "", sizeof(subject.job_num));
                strncpy(subject.remark, root["remark"].isString() ? root["remark"].asCString() : "", sizeof(subject.remark));
                strncpy(subject.name, root["name"].isString() ? root["name"].asCString() : "", sizeof(subject.name));
				if(!root["group_ids"].isNull())
				{
					int group_size = root["group_ids"].size();
					for(int i=0; i<group_size; i++)
					{
						subject.group_ids.push_back(root["group_ids"][i].asInt());
					}
				}
                if (!GetSubjectHandleInstance()->CheckSubjectExistByID(subject.id))
                {
					ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
					cb(GetJsonErrorInfo(JSON_RETURN_CODE_2000));
                    return;
                }

                //比较名字是否一样
                bool need_update_name = false;
                std::string subject_old_name = GetSubjectHandleInstance()->GetSubjectNameByID(subject.id);
                if (strcmp(subject.name, subject_old_name.data()) != 0)
                {
                    need_update_name = true;
                }

                std::vector<int> photo_id;
                GetPhotoHandleInstance()->GetPhotoIDListBySubjectID(subject.id, photo_id);

				//获取旧的group_ids
				std::vector<int> old_groupIDs = GetSubjectHandleInstance()->GetSubGroupIDsBySubID(subject.id);

				std::vector<int> group_del;
				std::vector<int> group_add;
                std::vector<int> group_update;
                group_del.clear();
                group_add.clear();
				bool need_update_group = true;
				if(old_groupIDs.size() != 0 && subject.group_ids.size() != 0 && old_groupIDs.size() == subject.group_ids.size())//通知旧群组删除
				{
					int match_size = 0;
					for(int i=0; i<old_groupIDs.size(); i++)
					{
						for(int j=0; j<subject.group_ids.size(); j++)
						{
							if(old_groupIDs[i] == subject.group_ids[j])
							{
								match_size++;
							}
						}
					}
					if(match_size == old_groupIDs.size())
					{
						need_update_group = false;
					}
				}
				if(old_groupIDs.size() == 0 && subject.group_ids.size() == 0)
				{
					need_update_group = false;
				}
                std::vector<int> photo_del;
                std::vector<int> photo_add;
                std::vector<int> photo_update;
                photo_del.clear();
                photo_add.clear();

                for (std::size_t i = 0; i < photo_id.size(); i++)
                {
                    bool need_del = true;
                    for (std::size_t j = 0; j < root["photo_ids"].size(); j++)
                    {
                        if (photo_id[i] == root["photo_ids"][j].asInt())
                        {
                            need_del = false;
                            break;
                        }
                    }
                    if (need_del)
                    {
                        photo_del.push_back(photo_id[i]);
						GetPhotoHandleInstance()->UpdateSubjectIDByID(0, photo_id[i]);
                    }
                }
                for (std::size_t i = 0; i < root["photo_ids"].size(); i++)
                {
                    bool need_add = true;
                    for (std::size_t j = 0; j < photo_id.size(); j++)
                    {
                        if (root["photo_ids"][i].asInt() == photo_id[j])
                        {
                            need_add = false;
                            break;
                        }
                    }
                    if (need_add)
                    {
                        photo_add.push_back(root["photo_ids"][i].asInt());
						GetPhotoHandleInstance()->UpdateSubjectIDByID(subject.id, root["photo_ids"][i].asInt());
                    }
                }
                if (need_update_name)
                {
                    for (std::size_t i = 0; i < root["photo_ids"].size(); i++)
                    {
                        bool need_update = false;
                        for (std::size_t j = 0; j < photo_id.size(); j++)
                        {
                            if (root["photo_ids"][i].asInt() == photo_id[j])
                            {
                                need_update = true;
                                break;
                            }
                        }
                        if (need_update)
                        {
                            photo_update.push_back(root["photo_ids"][i].asInt());
                        }
                    }
                }
				
                GetSubjectHandleInstance()->UpdateSubject(subject);
				
				if(need_update_group)
				{
					for (std::size_t i = 0; i < photo_id.size(); i++)
					{
						GetHttpApiControlInstance()->NotifyDeleteFaceByPhotoID(photo_id[i], old_groupIDs, subject_old_name);
					}
					for (std::size_t i = 0; i < root["photo_ids"].size(); i++)
					{
						int photo_id = root["photo_ids"][i].asInt();
						GetHttpApiControlInstance()->NotifyAddFaceByPhotoID(photo_id, subject.group_ids);
					}
				}
				else
				{
	                for (std::size_t i = 0; i < photo_del.size(); i++)
	                {
	                    GetHttpApiControlInstance()->NotifyDeleteFaceByPhotoID(photo_del[i], old_groupIDs, subject_old_name);
	                    //GetPhotoHandleInstance()->UpdateSubjectIDByID(0, photo_del[i]);
	                }
	                for (std::size_t i = 0; i < photo_add.size(); i++)
	                {
	                    //GetPhotoHandleInstance()->UpdateSubjectIDByID(subject.id, photo_add[i]);
	                    GetHttpApiControlInstance()->NotifyAddFaceByPhotoID(photo_add[i], subject.group_ids);
	                }
					for (std::size_t i = 0; i < photo_update.size(); i++)
	                {
	                    GetHttpApiControlInstance()->NotifyModifyFaceByPhotoID(photo_update[i], subject.group_ids);
	                }
				}
                
                FACE_SUBJECT new_subject;
                memset(&new_subject, 0, sizeof(new_subject));
                GetSubjectHandleInstance()->GetSubjectByID(subject.id, new_subject);
                Json::Value resp_root;
                Json::Value subject_obj;
                resp_root["code"] = 0;
                gsface::CreateSubjectObj(subject_obj, new_subject);
                resp_root["data"] = subject_obj;
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
                cb(resp_root.toStyledString());
                return;
            }
        }
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
    }
    else if (ctx->original_type() == 16) //delete表示删除用户信息
    {
        //解析出ID
        std::string subject_url(ctx->original_uri());
        std::size_t index = subject_url.find("/subject/");
        if (index != std::string::npos)
        {
            int subject_id = atoi(subject_url.substr(9).data());
            if (!GetSubjectHandleInstance()->CheckSubjectExistByID(subject_id))
            {
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_2000));
                return;
            }
            if (GetHttpApiControlInstance()->NotifyDeleteFaceBySubjectID(subject_id) == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
            {
                ctx->set_response_http_code(HTTP_REDIRECT_CODE);
				ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
				cb(GetJsonErrorInfo(JSON_RETURN_CODE_1010));
                return;
            }
            GetSubjectHandleInstance()->DeleteSubjectByID(subject_id);
            //删除对应的文件
            GetPhotoHandleInstance()->DeletePhotoFileBySubjectID(subject_id);
            GetPhotoHandleInstance()->DeletePhotoBySubjectID(subject_id);
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
            cb("{\"code\":0 }");
            return;
        }
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
    }
    return;
};

gsface::HTTPRespCallback ReqUploadPhotoHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    const char* content_type = ctx->FindRequestHeader("content-type");
    const evpp::Slice& body = ctx->body();
    
    if (!gsface::CheckCookieToken(ctx->FindRequestHeader("Cookie")))
    {
        ctx->set_response_http_code(302);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1009));
        int code = ctx->response_http_code();
		(void)code; //warning: unused variable
        return;
    }
    if (content_type && strstr(content_type, "multipart/form-data;") != NULL)
    {
        INDEX* body_data = PostBodyInit(body.size(), content_type, body.data());
        if (body_data == nullptr)
        {
			ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
			cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
            return;
        }

        char* photo_name = GetFileName(body_data->pHead, "photo");
		char *subject_id = GetValue(body_data->pHead, "subject_id");
        char *old_photo_id = GetValue(body_data->pHead, "old_photo_id");

        LOG_INFO << "photo_name:" << photo_name;
        LOG_INFO << "subject_id:" << subject_id;
        LOG_INFO << "old_photo_id:" << old_photo_id;
        
        FACE_PHOTO photo;
        memset(&photo, 0, sizeof(photo));
        if (photo_name)
        {
            strncpy(photo.name, photo_name, sizeof(photo.name));
        }

        if (subject_id)
        {
            photo.subject_id = atoi(subject_id);
            if (!GetSubjectHandleInstance()->CheckSubjectExistByID(photo.subject_id))
            {
                photo.subject_id = 0;
            }
        }
        
        if (old_photo_id && subject_id)
        {
            photo.subject_id = atoi(subject_id);
            GetPhotoHandleInstance()->UpdateSubjectIDByID(photo.subject_id, atoi(old_photo_id));
        }
        else
        {
            GetPhotoHandleInstance()->AddPhoto(photo);
        }
        
        if (photo.subject_id > 0)
        {
        	std::vector<int> group_ids = GetSubjectHandleInstance()->GetSubGroupIDsBySubID(photo.subject_id);
            GetHttpApiControlInstance()->NotifyDownloadPic(photo.subject_id, photo.id, group_ids);
        }
        //返回数据
        Json::Value resp_root;
        Json::Value data;
        resp_root["code"] = 0;
        data["company_id"] = 0;
        data["id"] = photo.id;
        if (photo.subject_id > 0)
        {
            data["subject_id"] = photo.subject_id;
        }
        else
        {
            data["subject_id"];
        }
        char szUrl[URL_SIZE] = {0};
        snprintf(szUrl, sizeof(szUrl), "%s/%s", gstGSFACEConf.storage_path, photo.name);
        data["url"] = szUrl;
        resp_root["data"] = data;
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
        //ctx->AddResponseHeader("Content-Type", "text/html;charset=utf-8");
        cb(resp_root.toStyledString());
        FreePostBody(body_data);
        return;
    }
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
	cb(GetJsonErrorInfo(JSON_RETURN_CODE_1002));
    return;
};

gsface::HTTPRespCallback ReqIdentityRecordHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    if (!gsface::CheckCookieToken(ctx->FindRequestHeader("Cookie")))
    {
        ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1009));
        return;
    }
    REQUEST_FACE_RECORD rface_record;
    memset(&rface_record, 0, sizeof(rface_record));
    rface_record.start_time = atoi(ctx->GetQuery("start").data());
    rface_record.end_time = atoi(ctx->GetQuery("end").data());
    rface_record.user_role = atoi(ctx->GetQuery("user_role").data());
    rface_record.screen_id = atoi(ctx->GetQuery("screen_id").data());
    rface_record.subject_id = atoi(ctx->GetQuery("subject_id").data());
    rface_record.page = atoi(ctx->GetQuery("page").data()) > 0 ? atoi(ctx->GetQuery("page").data()) : 1;
    rface_record.size = atoi(ctx->GetQuery("size").data()) > 0 ? atoi(ctx->GetQuery("size").data()) : 10;
    strncpy(rface_record.user_name, ctx->GetQuery("user_name").data(), sizeof(rface_record.user_name));
    std::vector<FACE_RECORD> face_record;
    PAGE_INFO page_info;
    memset(&page_info, 0, sizeof(page_info));
    if (GetHttpApiControlInstance()->RequestFaceRecord(&rface_record, face_record, page_info) == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1010));
        return;
    }
    //组装数据回复
    std::vector<FACE_SUBJECT> subject_vector;
    Json::Value resp_root;
    resp_root["code"] = 0;
    for (std::size_t i = 0; i < face_record.size(); i++)
    {
        FACE_SUBJECT subject;
        memset(&subject, 0, sizeof(subject));
        GetSubjectHandleInstance()->GetSubjectByName(face_record[i].initiator, subject);
        if (subject.id > 0)
        {
            Json::Value data;
            data["photo"] = face_record[i].picture_url;
            data["timestamp"] = face_record[i].capture_time;
            data["subject_id"] = subject.id;
            Json::Value subject_obj;
            gsface::CreateSubjectObj(subject_obj, subject);
            //data["subject"].append(subject_obj);
			data["subject"] = subject_obj;
            resp_root["data"].append(data);
        }
    }
    Json::Value page;
    page["count"] = page_info.count;
    page["current"] = page_info.current;
    page["size"] = page_info.size;
    page["total"] = page_info.total;
    resp_root["page"] = page;
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    //ctx->AddResponseHeader("Content-Type", "text/html;charset=utf-8");
    cb(resp_root.toStyledString());
    return;
};

gsface::HTTPRespCallback ReqGSFaceLoginHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    //const evpp::Slice& body = ctx->body();
    //解析出ID
    int group_id = 0;
    std::string subject_url(ctx->original_uri());
    std::size_t index = subject_url.find("/login/");
    if (index != std::string::npos)
    {
    	index += 7;
    	int index_param = subject_url.find("?");
    	group_id = atoi(subject_url.substr(index, index_param-index).data());
    }	
    char file_xml[URL_SIZE] = {0};
    char file_xml_aes[URL_SIZE] = {0};
    snprintf(file_xml, sizeof(file_xml), "%s/%s", gstGSFACEConf.face_xml_path, FACE_ID_XML_FILE);
    snprintf(file_xml_aes, sizeof(file_xml_aes), "%s/%s", gstGSFACEConf.face_xml_path, FACE_ID_XML_FILE_AES);
    unlink(file_xml);
    unlink(file_xml_aes);

    std::vector<FACE_PHOTO> photo_vec;
    GetPhotoHandleInstance()->GetRegisteredPhotoList(photo_vec, group_id);
    GetFaceIDXmlInstance()->CreateXmlFile(file_xml, gstGSFACEConf.pic_download_path, photo_vec);
    //文件加密
    FileAESEncrypt(file_xml, AES_ENCRYPT_KEY_V1, file_xml_aes);
    Json::Value resp_root;
    char file_dw_path[URL_SIZE] = {0};
    snprintf(file_dw_path, sizeof(file_dw_path), "%s/%s", gstGSFACEConf.face_dw_path, FACE_ID_XML_FILE_AES);
    resp_root["facelist_path"] = file_dw_path;
    std::string file_md5 = GetFileMD5(file_xml_aes);
    resp_root["md5"] = file_md5.data();
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    cb(resp_root.toStyledString());
    return;
};

gsface::HTTPRespCallback ReqGetSubGroupListHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
	LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
	if (!gsface::CheckCookieToken(ctx->FindRequestHeader("Cookie")))
	{
		ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1009));
		return;
	}
	REQUEST_SUBJECT_GROUP rsubject_group;
	memset(&rsubject_group, 0, sizeof(rsubject_group));

	rsubject_group.page = atoi(ctx->GetQuery("page").data()) > 0 ? atoi(ctx->GetQuery("page").data()) : 1;
	rsubject_group.size = atoi(ctx->GetQuery("size").data()) > 0 ? atoi(ctx->GetQuery("size").data()) : 10;
	strncpy(rsubject_group.name, ctx->GetQuery("name").data(), sizeof(rsubject_group.name));
	std::vector<SUBJECT_GROUP> subject_group;
	PAGE_INFO page_info;
	memset(&page_info, 0, sizeof(page_info));
	if (GetHttpApiControlInstance()->RequestSubjectGroup(&rsubject_group, subject_group, page_info) == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
	{
		ctx->set_response_http_code(HTTP_REDIRECT_CODE);
		ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
		cb(GetJsonErrorInfo(JSON_RETURN_CODE_1010));
		return;
	}
	
	//组装数据回复
	Json::Value resp_root;
	resp_root["code"] = 0;	
	for (std::size_t i = 0; i < subject_group.size(); i++)
	{
		Json::Value data;
		int subject_count = GetSubjectHandleInstance()->GetSubjectCountByGroupID(subject_group[i].id);	
		data["comment"] = Json::Value::null;
		data["subject_count"] = subject_count;
		data["id"] = subject_group[i].id;
		data["name"] = subject_group[i].name;
		data["subject_type"] = 0;
		data["update_by"] = "";
		data["update_time"] = subject_group[i].update_time;
		resp_root["data"].append(data);
	}
	Json::Value page;
	page["count"] = page_info.count;
	page["current"] = page_info.current;
	page["size"] = page_info.size;
	page["total"] = page_info.total;
	resp_root["page"] = page;
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
	cb(resp_root.toStyledString());
	return;
};

gsface::HTTPAllRespCallbackMap HTTPAllRespMapInit()
{
    gsface::HTTPAllRespCallbackMap OMap;
    OMap[gsface::LOGIN] = ReqLoginHandler;
    OMap[gsface::GET_SUBJECT] = ReqGetSubjectHandler;
    OMap[gsface::GET_SUBJECT_LIST] = ReqGetSubjectListHandler;
    OMap[gsface::SUBJECT] = ReqSubjectHandler;
    OMap[gsface::UPLOAD_PHOTO] = ReqUploadPhotoHandler;
    OMap[gsface::IDENTITY_RECORD] = ReqIdentityRecordHandler;
    OMap[gsface::GSFACE_LOGIN] = ReqGSFaceLoginHandler;
	OMap[gsface::GET_SUBJECT_GROUP_LIST] = ReqGetSubGroupListHandler;
    return OMap;
}
}

