#ifndef __GSFACE_HTTP_RESP_H__
#define __GSFACE_HTTP_RESP_H__
#include <functional>
#include <evpp/http/context.h>

#define ERR_APP_UNACTIVE_STR     "\"Your account is unactivated.\""
#define ERR_APP_EXPIRE1          "\"Your account is expire.\""
#define USER_NOT_EXIT            "\"Your account is not exist.\""
#define PASSWD_INVALID           "\"passwd is invalid\""
#define TOKEN_INVALID            "\"token is invalid\""
#define RESULT                   "\"result\""
#define MESSAGE                  "\"message\""
#define DATAS                    "\"datas\""
#define ACCESS_SERVER            "\"access_server\""
#define ACCESS_SERVER_IPV6       "\"access_server_ipv6\""
#define VRTSP_SERVER             "\"vrtsp_server\""
#define VRTSP_SERVER_IPV6        "\"vrtsp_server_ipv6\""
#define REST_SERVER              "\"rest_server\""
#define REST_SERVER_IPV6         "\"rest_server_ipv6\""
#define REST_SERVER_HTTPS        "\"rest_server_https\""
#define REST_SERVER_HTTPS_IPV6   "\"rest_server_https_ipv6\""
#define LOGIN_SUCCESS            "\"login success\""
#define API_VERSION              "api-version"
#define TOKEN                    "\"token\""
#define ACCESS_SERVER_LIST       "\"get access server list success\""
#define VRTSP_SERVER_LIST        "\"get vrtsp server list success\""

#define WEB_SERVER            "\"web_server\""
#define WEB_SERVER_IPV6       "\"web_server_ipv6\""

#define FTP_SERVER            "\"ftp_server\""
#define FTP_SERVER_IPV6       "\"ftp_server_ipv6\""

#define PBX_SERVER            "\"pbx_server\""
#define PBX_SERVER_IPV6       "\"pbx_server_ipv6\""



#define USER_ROLE                   "\"role\""
#define HAVE_PUBLIC_DEV             "\"have_public_dev\""
#define APP_INIT                    "\"is_init\""

#define PLATFORM_VER_STR            "\"platform_ver\""
#define ERR_APP_SUCCEE             "\"login successful \""
#define ERR_APP_LOGIN              "\"login failed \""
#define SHOW_PAYMENT               "\"show_payment\""
#define SHOW_SUBSCRIPTION          "\"show_subscription\""
#define SHOW_TEMPKEY               "\"show_tempkey\""
#define APP_UPGRADE                "\"upgrade\""
#define PUSH_MODE                  "\"push_mode\""

namespace gsface
{

const std::string V30 = "3.0";
const std::string V31 = "3.1";
const std::string V33 = "3.3";
const std::string V40 = "4.0";
const std::string V44 = "4.4";
const std::string V45 = "4.5";
const std::string V46 = "4.6";
const std::string V46Dev = "4600";
const std::string VDevice = "VDevice";

const std::string CSGATE_HTTP_BUSSINESS = "csgate_http";
const uint32_t CSGATE_HTTP_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t CSGATE_HTTP_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
const uint32_t CSGATE_HTTP_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s
const std::string AKCS_ATTACK_IP = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用
const uint32_t AKCS_ATTACK_IP_RELEASE = 172800; //2天后将攻击者的ip从iptables列表中删除掉,60 * 60 * 24 *2s

const char err_describe[8][36] =
{
    ERR_APP_SUCCEE,
    USER_NOT_EXIT,
    PASSWD_INVALID,
    ERR_APP_EXPIRE1,
    ERR_APP_UNACTIVE_STR,
    ERR_APP_LOGIN,
    ERR_APP_UNACTIVE_STR,
    TOKEN_INVALID
};

//http路由
enum HTTP_ROUTE
{
    LOGIN = 0,
    GET_SUBJECT,
    GET_SUBJECT_LIST,
    SUBJECT,
    UPLOAD_PHOTO,
    IDENTITY_RECORD,
    GSFACE_LOGIN,
    GET_SUBJECT_GROUP_LIST,
};

typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
//typedef std::map<int, HTTPRespVerCallbackMap> HTTPAllRespCallbackMap;
typedef std::map<int, HTTPRespCallback> HTTPAllRespCallbackMap;

HTTPAllRespCallbackMap HTTPAllRespMapInit();

}
#endif //__GSFACE_HTTP_RESP_H__
