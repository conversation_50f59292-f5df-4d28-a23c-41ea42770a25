#include <stdio.h>
#include <stdlib.h>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"

#include "HttpServer.h"
#include "HttpResp.h"
#include "GsfaceConf.h"
#include "MsgCommonDefine.h"
#include "Utility.h"
#include "json/json.h"

//全局变量
static gsface::HTTPAllRespCallbackMap httpRespCbs;
extern GSFACE_CONF gstGSFACEConf;

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
	ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
	cb(GetJsonErrorInfo(JSON_RETURN_CODE_1001));
}

void HttpReqLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqLoginHandler = httpRespCbs[gsface::LOGIN];
    if (ReqLoginHandler)
    {
        ReqLoginHandler(ctx, cb);
    }
    return ;
}

void HttpReqGetSubjectCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqGetSubjectHandler = httpRespCbs[gsface::GET_SUBJECT];
    if (ReqGetSubjectHandler)
    {
        ReqGetSubjectHandler(ctx, cb);
    }
    return ;
}


void HttpReqGetSubjectListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqGetSubjectListHandler = httpRespCbs[gsface::GET_SUBJECT_LIST];
    if (ReqGetSubjectListHandler)
    {
        ReqGetSubjectListHandler(ctx, cb);
    }
    return ;
}


void HttpReqSubjectCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqSubjectHandler = httpRespCbs[gsface::SUBJECT];
    if (ReqSubjectHandler)
    {
        ReqSubjectHandler(ctx, cb);
    }
    return ;
}


//获取接入服务器列表,运维接口
void HttpReqUploadPhotoCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqUploadPhotoHandler = httpRespCbs[gsface::UPLOAD_PHOTO];
    if (ReqUploadPhotoHandler)
    {
        ReqUploadPhotoHandler(ctx, cb);
    }
    return ;
}


void HttpReqIdentityRecordCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqIdentityRecordHandler = httpRespCbs[gsface::IDENTITY_RECORD];
    if (ReqIdentityRecordHandler)
    {
        ReqIdentityRecordHandler(ctx, cb);
    }
    return ;
}

void HttpReqGsFaceLoginCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gsface::HTTPRespCallback ReqGsFaceLoginHandler = httpRespCbs[gsface::GSFACE_LOGIN];
    if (ReqGsFaceLoginHandler)
    {
        ReqGsFaceLoginHandler(ctx, cb);
    }
    return ;
}

void HttpReqGetSubjectGroupListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
	LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
	gsface::HTTPRespCallback ReqGetSubGroupListHandler = httpRespCbs[gsface::GET_SUBJECT_GROUP_LIST];
	if (ReqGetSubGroupListHandler)
	{
		ReqGetSubGroupListHandler(ctx, cb);
	}
	return ;
}

void startHttpServer()
{
    httpRespCbs = gsface::HTTPAllRespMapInit();
    const int port = GSFACE_HTTP_BIND_PORT;
    const int thread_num = 2;
    bool ipv6 = false;
    evpp::http::Server server2(thread_num, ipv6);
    server2.RegisterDefaultHandler(&DefaultHandler);
    server2.RegisterHandler("/auth/login", HttpReqLoginCallback);
    server2.RegisterHandler("/mobile-admin/subjects", HttpReqGetSubjectCallback);
    server2.RegisterHandler("/mobile-admin/subjects/list", HttpReqGetSubjectListCallback);
    server2.RegisterHandler("/subject", HttpReqSubjectCallback);
    server2.RegisterHandler("/subject/photo", HttpReqUploadPhotoCallback);
    server2.RegisterHandler("/event/events", HttpReqIdentityRecordCallback);
    server2.RegisterHandler("/gsface/v1/login", HttpReqGsFaceLoginCallback);
	server2.RegisterHandler("/subjects/group/list", HttpReqGetSubjectGroupListCallback);
    server2.Init(port);
    server2.Start();
    return ;
}


