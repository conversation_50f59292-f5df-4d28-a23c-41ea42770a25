#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "Control.h"
#include "json/json.h"
#include "GsfaceConf.h"
#include "Utility.h"
#include "HttpApiControl.h"

extern GSFACE_CONF gstGSFACEConf;

CControl* GetControlInstance()
{
    return CControl::GetInstance();
}

CControl::CControl()
{
    memset(api_server_, 0, sizeof(api_server_));
}

CControl::~CControl()
{

}

CControl* CControl::instance = NULL;

CControl* CControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CControl();
    }

    return instance;
}

int CControl::Init()
{
    GetApiSrvByHttp();
    GetHttpApiControlInstance()->Init();
    return 0;
}

int CControl::GetApiSrvByHttp()
{
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "http://%s/apiserver", gstGSFACEConf.csgate_addr);
    CURL_WRITE_CALLBACK_BUF recvData;
   	memset(&recvData, 0, sizeof(recvData));
    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));
    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_GET;
    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }

    Json::Reader reader;
    Json::Value root;
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == 0)
    {
        strncpy(api_server_, root["datas"]["api_server"].asCString(), sizeof(api_server_));
    }
	if(recvData.pRecvBuf != NULL)
	{
		free(recvData.pRecvBuf);
		recvData.pRecvBuf = NULL;
	}
    return 0;
}

int CControl::GetApiSrv(char* dst, int size)
{
    if (dst == NULL || size == 0)
    {
        return -1;
    }
    if (strlen(api_server_) == 0)
    {
        GetApiSrvByHttp();
    }
    strncpy(dst, api_server_, size);
    return 0;
}


