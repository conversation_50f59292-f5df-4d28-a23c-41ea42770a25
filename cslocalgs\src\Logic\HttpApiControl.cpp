#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <fstream>

#include "HttpApiControl.h"
#include "AccountHandle.h"
#include "Control.h"
#include "json/json.h"
#include "Utility.h"
#include "AccountHandle.h"
#include "GsfaceConf.h"
#include "PhotoHandle.h"
#include "SubjectHandle.h"
#include "Md5.h"

extern GSFACE_CONF gstGSFACEConf; //全局变量

CHttpApiControl* GetHttpApiControlInstance()
{
    return CHttpApiControl::GetInstance();
}

CHttpApiControl::CHttpApiControl()
{
    memset(token_, 0, sizeof(token_));
}

CHttpApiControl::~CHttpApiControl()
{

}

int CHttpApiControl::Init()
{
    GetAccountHandleInstance()->GetToken(token_, sizeof(token_));
    return 0;
}

int CHttpApiControl::Login(char* user_name, char* password)
{
    if (user_name == nullptr || password == nullptr)
    {
        return -1;
    }
    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_LOGIN);
    CURL_WRITE_CALLBACK_BUF recvData;
   	memset(&recvData, 0, sizeof(recvData));

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_POST;

    //head
    char head_buf[VALUE_SIZE] = {0};
    snprintf(head_buf, sizeof(head_buf), "Content-Type: application/json");
    http_request.pHeadData = head_buf;

    //body
    Json::Value item;
    Json::Value gsface_api;
    item["account"] = user_name;
    item["passwd"] = password;
    char login_api[URL_SIZE] = {0};
    snprintf(login_api, sizeof(login_api), "http://%s:%d/%s", gstGSFACEConf.listen_ip, GSFACE_HTTP_BIND_PORT, GSFACE_HTTP_API_V1_LOGIN);
    gsface_api["login"] = login_api;
    item["gsface_api"] = gsface_api;
    std::string jsonout = item.toStyledString();
    http_request.pPostData = (char*)jsonout.data();

    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;

    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "SendRequestUrl failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    int result = root["result"].asInt();

    if (result == 0)
    {
        char token[VALUE_SIZE] = {0};
        strncpy(token, root["token"].asCString(), sizeof(token));
        time_t timestamp;
        timestamp = time(NULL);
        timestamp += 9000;
        GetAccountHandleInstance()->AddAcount(user_name, password, token, timestamp);
        strncpy(token_, token, sizeof(token_));
        LOG_INFO << "Login succeed.";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return 0;
    }
    else
    {
        char msg[VALUE_SIZE] = {0};
        strncpy(msg, root["message"].asCString(), sizeof(msg));
        LOG_WARN << "[ " << msg << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return result;
    }
}

int CHttpApiControl::GetDeviceList(int device_type, int device_status, DEVICE_INFO_LIST*& device_list)
{
    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s?token=%s&status=%d&type=%d", api_server, HTTP_API_GET_DEVICELIST, token_, device_status, device_type);
    CURL_WRITE_CALLBACK_BUF recvData;
   	memset(&recvData, 0, sizeof(recvData));

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(CURL_HTTP_REQUEST));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_GET;

    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }

    AK_LOG_WARN << "data is [ " << recvData.pRecvBuf << " ]";
    if (root["result"].asInt() == 0)
    {
        DEVICE_INFO_LIST* head = nullptr;
        DEVICE_INFO_LIST* cur_node = nullptr;
        const Json::Value array_obj = root["data"];
        int size = array_obj.size();
        if (size > 0)
        {
            for (int i = 0; i < size; i++)
            {
                DEVICE_INFO_LIST* new_node = new DEVICE_INFO_LIST;
                memset(new_node, 0, sizeof(DEVICE_INFO_LIST));
                new_node->status = atoi(array_obj[i]["status"].asCString());
                strncpy(new_node->mac, array_obj[i]["mac"].asCString(), sizeof(new_node->mac));
                strncpy(new_node->firmware, array_obj[i]["firmware"].asCString(), sizeof(new_node->firmware));
                if (head == nullptr)
                {
                    head = new_node;
                }
                else
                {
                    cur_node->next = new_node;
                }
                cur_node = new_node;
            }
        }
        device_list = head;
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return 0;
    }
    else
    {
        LOG_WARN << "failed";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }

}

int CHttpApiControl::DestoryDeviceList(DEVICE_INFO_LIST* head)
{
    DEVICE_INFO_LIST* cur_node = NULL;
    while (NULL != head)
    {
        cur_node = head;
        head = head->next;
        delete cur_node;
    }
}

int CHttpApiControl::NotifyDownloadPic(int subject_id, int photo_id, std::vector<int> group_ids)
{
    std::string photo_name = GetPhotoHandleInstance()->GetPhotoNameByID(photo_id);
    std::string subject_name = GetSubjectHandleInstance()->GetSubjectNameByID(subject_id);
    char url[URL_SIZE] = {0};
    snprintf(url, sizeof(url), "%s/%s", gstGSFACEConf.pic_download_path, photo_name.data());

    char tphoto_id[INT_SIZE] = {0};
    snprintf(tphoto_id, sizeof(tphoto_id), "%d", photo_id);

    char storage_path[URL_SIZE] = {0};
    snprintf(storage_path, sizeof(storage_path), "%s/%s", gstGSFACEConf.storage_path, photo_name.data());
    std::string photo_md5 = GetFileMD5(storage_path);
    Json::Value item;
    item["Url"] = url;
    item["PicMd5"] = photo_md5.data();
    item["Name"] = subject_name;
    item["DoorNum"] = "1234";
    item["Week"] = "1111111";
    item["TimeStart"] = "00:01";
    item["TimeEnd"] = "23:59";
    item["Id"] = tphoto_id;
    item["Token"] = token_;
	if(group_ids.size() > 0)
	{
		Json::Value groupIDs;
		for(int i=0; i<group_ids.size(); i++)
		{
			groupIDs.append(group_ids[i]);
		}	 
		item["GroupIDs"] = groupIDs;
	}

    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_NOTIFY_DOWNLOAD);
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));	

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_POST;

    //head
    char head_buf[VALUE_SIZE] = {0};
    snprintf(head_buf, sizeof(head_buf), "Content-Type: application/json");
    http_request.pHeadData = head_buf;

    //body
    std::string jsonout = item.toStyledString();
    http_request.pPostData = (char*)jsonout.data();

    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == 0)
    {
        //更新expire超时时间
        time_t timestamp;
        timestamp = time(NULL);
        timestamp += 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Notify device download pic succeed[ " << http_url << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return 0;
    }
    else if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Notify device download pic failed[ " << root["message"].asCString() << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
	if(recvData.pRecvBuf != NULL)
	{
		free(recvData.pRecvBuf);
		recvData.pRecvBuf = NULL;
	}
    return -1;
}

int CHttpApiControl::NotifyDeleteFaceBySubjectID(int subject_id)
{
    std::string subject_name = GetSubjectHandleInstance()->GetSubjectNameByID(subject_id);
    std::vector<int> photo_id;
    GetPhotoHandleInstance()->GetPhotoIDListBySubjectID(subject_id, photo_id);

    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_NOTIFY_DELETE_FACE);
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_POST;
    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    //head
    char head_buf[VALUE_SIZE] = {0};
    snprintf(head_buf, sizeof(head_buf), "Content-Type: application/json");
    http_request.pHeadData = head_buf;

	bool bIsNeedDeal = false;
    for (int i = 0; i < photo_id.size(); i++)
    {
    	bIsNeedDeal = true;
        Json::Value item;
        item["Id"] = photo_id[i];
        item["Name"] = subject_name;
        item["Token"] = token_;
        //body
        std::string jsonout = item.toStyledString();
        http_request.pPostData = (char*)jsonout.data();
        if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        memset(&recvData, 0, sizeof(recvData));
        if ((SendRequestUrl(&http_request) < 0))
        {
            LOG_WARN << "send request url failed.";
            usleep(100 * 1000);
            continue;
        }
        usleep(100 * 1000);
    }
	if (!bIsNeedDeal)
	{
		return 0;
	}
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Notify device delete pic failed[ " << root["message"].asCString() << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
    LOG_INFO << "Notify device delete pic succeed[ " << http_url << " ]";
    //更新expire超时时间
    time_t timestamp;
    timestamp = time(NULL);
    timestamp += 9000;
    GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
	if(recvData.pRecvBuf != NULL)
	{
		free(recvData.pRecvBuf);
		recvData.pRecvBuf = NULL;
	}
    return 0;
}

int CHttpApiControl::NotifyDeleteFaceByPhotoID(int photo_id, std::vector<int> group_ids, std::string subject_name_old)
{
	std::string subject_name;
	if(subject_name_old.size() > 0)
	{
		subject_name = subject_name_old;
	}
	else
	{
    	subject_name = GetSubjectHandleInstance()->GetSubjectNameByPhotoID(photo_id);
	}

    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_NOTIFY_DELETE_FACE);
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));	

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_POST;
    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    //head
    char head_buf[VALUE_SIZE] = {0};
    snprintf(head_buf, sizeof(head_buf), "Content-Type: application/json");
    http_request.pHeadData = head_buf;

    Json::Value item;
    item["Id"] = photo_id;
    item["Name"] = subject_name;
    item["Token"] = token_;
	if(group_ids.size() > 0)
	{
		Json::Value GroupIDs;
		for(int i=0; i<group_ids.size(); i++)
		{
			GroupIDs.append(group_ids[i]);
		}	 
		item["GroupIDs"] = GroupIDs;
	}
    //body
    std::string jsonout = item.toStyledString();
    http_request.pPostData = (char*)jsonout.data();
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}	
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Notify device delete pic failed[ " << root["message"].asCString() << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
    LOG_INFO << "Notify device delete pic succeed[ " << http_url << " ]";
    //更新expire超时时间
    time_t timestamp;
    timestamp = time(NULL);
    timestamp += 9000;
    GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
	if(recvData.pRecvBuf != NULL)
	{
		free(recvData.pRecvBuf);
		recvData.pRecvBuf = NULL;
	}
    return 0;
}

int CHttpApiControl::NotifyAddFaceByPhotoID(int photo_id, std::vector<int>group_ids)
{
    std::string photo_name = GetPhotoHandleInstance()->GetPhotoNameByID(photo_id);
    int subject_id = GetPhotoHandleInstance()->GetSubjectIDByID(photo_id);
    std::string subject_name = GetSubjectHandleInstance()->GetSubjectNameByID(subject_id);
    char url[URL_SIZE] = {0};
    snprintf(url, sizeof(url), "%s/%s", gstGSFACEConf.pic_download_path, photo_name.data());

    char tphoto_id[INT_SIZE] = {0};
    snprintf(tphoto_id, sizeof(tphoto_id), "%d", photo_id);

    char storage_path[URL_SIZE] = {0};
    snprintf(storage_path, sizeof(storage_path), "%s/%s", gstGSFACEConf.storage_path, photo_name.data());
    std::string photo_md5 = GetFileMD5(storage_path);

    Json::Value item;
    item["Url"] = url;
    item["PicMd5"] = photo_md5.data();
    item["Name"] = subject_name;
    item["DoorNum"] = "1234";
    item["Week"] = "1111111";
    item["TimeStart"] = "00:01";
    item["TimeEnd"] = "23:59";
    item["Id"] = tphoto_id;
    item["Token"] = token_;
	if(group_ids.size() > 0)
	{
		Json::Value GroupIDs;
		for(int i=0; i<group_ids.size(); i++)
		{
			GroupIDs.append(group_ids[i]);
		}	 
		item["GroupIDs"] = GroupIDs;
	}

    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_NOTIFY_DOWNLOAD);
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));	

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_POST;

    //head
    char head_buf[VALUE_SIZE] = {0};
    snprintf(head_buf, sizeof(head_buf), "Content-Type: application/json");
    http_request.pHeadData = head_buf;

    //body
    std::string jsonout = item.toStyledString();
    http_request.pPostData = (char*)jsonout.data();

    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == 0)
    {
        //更新expire超时时间
        time_t timestamp;
        timestamp = time(NULL);
        timestamp += 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);

        LOG_INFO << "Notify device add pic succeed[ " << http_url << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return 0;
    }
    else if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Notify device add pic failed[ " << root["message"].asCString() << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
    else
    {
    	if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    return 0;
}

int CHttpApiControl::NotifyModifyFaceByPhotoID(int photo_id, std::vector<int> group_ids)
{
    std::string photo_name = GetPhotoHandleInstance()->GetPhotoNameByID(photo_id);
    int subject_id = GetPhotoHandleInstance()->GetSubjectIDByID(photo_id);
    std::string subject_name = GetSubjectHandleInstance()->GetSubjectNameByID(subject_id);

    char url[URL_SIZE] = {0};
    snprintf(url, sizeof(url), "%s/%s", gstGSFACEConf.pic_download_path, photo_name.data());

    char tphoto_id[INT_SIZE] = {0};
    snprintf(tphoto_id, sizeof(tphoto_id), "%d", photo_id);

    char storage_path[VALUE_SIZE] = {0};
    snprintf(storage_path, sizeof(storage_path), "%s/%s", gstGSFACEConf.storage_path, photo_name.data());
    std::string photo_md5 = GetFileMD5(storage_path);

    Json::Value item;
    item["Url"] = url;
    item["PicMd5"] = photo_md5.data();
    item["Name"] = subject_name;
    item["DoorNum"] = "1234";
    item["Week"] = "1111111";
    item["TimeStart"] = "00:01";
    item["TimeEnd"] = "23:59";
    item["Id"] = tphoto_id;
    item["Token"] = token_;
	if(group_ids.size() > 0)
	{
		Json::Value GroupIDs;
		for(int i=0; i<group_ids.size(); i++)
		{
			GroupIDs.append(group_ids[i]);
		}	 
		item["GroupIDs"] = GroupIDs;
	}

    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_NOTIFY_MODIFY_FACE);
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));	

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_POST;

    //head
    char head_buf[VALUE_SIZE] = {0};
    snprintf(head_buf, sizeof(head_buf), "Content-Type: application/json");
    http_request.pHeadData = head_buf;

    //body
    std::string jsonout = item.toStyledString();
    http_request.pPostData = (char*)jsonout.data();

    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == 0)
    {
        //更新expire超时时间
        time_t timestamp;
        timestamp = time(NULL);
        timestamp += 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);

        LOG_INFO << "Notify device modify pic succeed[ " << http_url << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return 0;
    }
    else if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Notify device modify pic failed[ " << root["message"].asCString() << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
    else
    {
    	if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    return 0;
}

//http://api.xxx.akuvox.com/faceserver/property/facerecord?token=1v58ZDx875L21807&pageNo=1&pageSize=10&startTime=**********&endTime=**********&userName=jeffrey
int CHttpApiControl::RequestFaceRecord(REQUEST_FACE_RECORD* record, std::vector<FACE_RECORD>& face_record, PAGE_INFO& page_info)
{
    if (record == nullptr)
    {
        return -1;
    }
    face_record.clear();
    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_REQUEST_FACE_RECORD);
    char url_param[URL_SIZE] = {0};
    snprintf(url_param, sizeof(url_param), "?token=%s&pageNo=%d&pageSize=%d&startTime=%d&endTime=%d%s%s", token_, record->page,
             record->size, record->start_time ? record->start_time : 961309591, record->end_time ? record->end_time : 2128919191,
             strlen(record->user_name) > 0 ? "&userName=" : "", strlen(record->user_name) > 0 ? record->user_name : "");
    strncat(http_url, url_param, sizeof(http_url));
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));
    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));
    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_GET;
    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == 0)
    {
        Json::Value data = root["data"];
        for (int i = 0; i < data.size(); i++)
        {
            FACE_RECORD trecord;
            memset(&trecord, 0, sizeof(trecord));
            trecord.capture_time = atoi(data[i]["CaptureTime"].asCString());
            strncpy(trecord.picture_name, data[i]["PicName"].asCString(), sizeof(trecord.picture_name));
            strncpy(trecord.picture_url, data[i]["PicUrl"].asCString(), sizeof(trecord.picture_url));
            strncpy(trecord.initiator, data[i]["Initiator"].asCString(), sizeof(trecord.initiator));
            face_record.push_back(trecord);
        }
        Json::Value page = root["page"];
        page_info.count = page["count"].asInt();
        page_info.current = page["current"].asInt();
        page_info.size = page["size"].asInt();
        page_info.total = page["total"].asInt();

        //更新expire超时时间
        time_t timestamp;
        timestamp = time(NULL);
        timestamp += 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);

        LOG_INFO << "Request face record succeed[ " << http_url << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return 0;
    }
    else if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Request face record failed[ " << root["message"].asCString() << " ].";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
    else
    {
    	if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    return 0;
}

int CHttpApiControl::RequestSubjectGroup(REQUEST_SUBJECT_GROUP* group, std::vector<SUBJECT_GROUP>& subject_group, PAGE_INFO& page_info)
{
    if (group == nullptr)
    {
        return -1;
    }
    subject_group.clear();
    char api_server[VALUE_SIZE] = {0};
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_GET_SUBJECT_GROUPLIST);
    char url_param[URL_SIZE] = {0};
    snprintf(url_param, sizeof(url_param), "?token=%s&pageNo=%d&pageSize=%d%s%s", token_, group->page, group->size,
             strlen(group->name) > 0 ? "&name=" : "", strlen(group->name) > 0 ? group->name : "");
    strncat(http_url, url_param, sizeof(http_url));
    CURL_WRITE_CALLBACK_BUF recvData;
	memset(&recvData, 0, sizeof(recvData));	
	
    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(http_request));
    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_GET;
    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        return -1;
    }
    if (root["result"].asInt() == 0)
    {
        Json::Value data = root["data"];
        for (int i = 0; i < data.size(); i++)
        {
            SUBJECT_GROUP tgroup;
            memset(&tgroup, 0, sizeof(tgroup));
			tgroup.id = atoi(data[i]["ID"].asCString());
            strncpy(tgroup.name, data[i]["LocationName"].asCString(), sizeof(tgroup.name));
			strncpy(tgroup.update_time, data[i]["UpdateTime"].isString() ? data[i]["UpdateTime"].asCString() : "", sizeof(tgroup.update_time));
            subject_group.push_back(tgroup);
        }
        Json::Value page = root["page"];
        page_info.count = page["count"].asInt();
        page_info.current = page["current"].asInt();
        page_info.size = page["size"].asInt();
        page_info.total = page["total"].asInt();

        //更新expire超时时间
        time_t timestamp;
        timestamp = time(NULL);
        timestamp += 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);

        LOG_INFO << "Request subject group list succeed[ " << http_url << " ]";
        return 0;
    }
    else if (root["result"].asInt() == HTTP_RESULT_CODE_TOKEN_TIMEOUT)
    {
        time_t timestamp;
        timestamp = time(NULL);
        timestamp -= 9000;
        GetAccountHandleInstance()->UpdateExpire(token_, timestamp);
        LOG_INFO << "Request subject group list failed[ " << root["message"].asCString() << " ].";
        return HTTP_RESULT_CODE_TOKEN_TIMEOUT;
    }
    else
    {
        return -1;
    }
    return 0;
}


int CHttpApiControl::Heartbeat(int retry)
{
    LOG_INFO << "send Heartbeat";
    char api_server[VALUE_SIZE] = "api.ccloud.akuvox.com";
    GetControlInstance()->GetApiSrv(api_server, sizeof(api_server));
    char http_url[URL_SIZE] = {0};
    snprintf(http_url, sizeof(http_url), "https://%s/%s", api_server, HTTP_API_HEARTBEAT);
    
    CURL_WRITE_CALLBACK_BUF recvData;
   	memset(&recvData, 0, sizeof(recvData));

    CURL_HTTP_REQUEST http_request;
    memset(&http_request, 0, sizeof(CURL_HTTP_REQUEST));

    http_request.nAuthMethod = HTTP_AUTH_METHOD_NONE;
    http_request.nRequestMethod = HTTP_REQUEST_METHOD_GET;

    http_request.pRecvData = &recvData;
    http_request.pUrl = http_url;
    if ((SendRequestUrl(&http_request) < 0))
    {
        LOG_WARN << "send request url failed.";
        return -1;
    }
	if(recvData.pRecvBuf == NULL)
	{
		return -1;
	}
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(recvData.pRecvBuf, root))
    {
        LOG_WARN << "json parse failed, data is [ " << recvData.pRecvBuf << " ]";
		if(recvData.pRecvBuf != NULL)
		{
			free(recvData.pRecvBuf);
			recvData.pRecvBuf = NULL;
		}
        if (retry)
        {
            Heartbeat(0);
        }
    }
    return 0;
}



CHttpApiControl* CHttpApiControl::instance = NULL;

CHttpApiControl* CHttpApiControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CHttpApiControl();
    }

    return instance;
}


