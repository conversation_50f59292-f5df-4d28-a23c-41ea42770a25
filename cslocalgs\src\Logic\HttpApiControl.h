#ifndef __GSFACE_HTTP_API_CONTROL_H__
#define __GSFACE_HTTP_API_CONTROL_H__

#include "MsgCommonDefine.h"

#define HTTP_API_HEARTBEAT  "faceserver/property/heartbeat"
#define HTTP_API_LOGIN      "faceserver/property/login"
#define HTTP_API_GET_DEVICELIST  "faceserver/property/devicelist"
#define HTTP_API_NOTIFY_DOWNLOAD    "faceserver/v1.1/pic/download"
#define HTTP_API_NOTIFY_DELETE_FACE "faceserver/v1.1/pic/delete"
#define HTTP_API_NOTIFY_MODIFY_FACE "faceserver/v1.1/pic/modify"
#define HTTP_API_REQUEST_FACE_RECORD    "faceserver/property/facerecord"
#define HTTP_API_GET_SUBJECT_GROUPLIST	"faceserver/property/subjectgrouplist"
#define GSFACE_HTTP_API_V1_LOGIN    "gsface/v1/login"
#define HTTP_API_RECV_TMP_FILE      "tmp1258956_recv.txt"


#define HTTP_RESULT_CODE_ACCOUT_NOT_EXIST   1009
#define HTTP_RESULT_CODE_TOKEN_TIMEOUT      1005
#define HTTP_REDIRECT_CODE      302

typedef struct DEVICE_INFO_LIST_T
{
    int status;
    char mac[MAC_SIZE];
    char firmware[VALUE_SIZE];
    struct DEVICE_INFO_LIST_T* next;
} DEVICE_INFO_LIST;

typedef struct REQUEST_FACE_RECORD_T
{
    int start_time;
    int end_time;
    int user_role;
    int screen_id;
    int subject_id;
    int page;
    int size;
    char user_name[VALUE_SIZE];
} REQUEST_FACE_RECORD;

typedef struct FACE_RECORD_T
{
    int capture_time;
    char picture_name[VALUE_SIZE];
    char picture_url[URL_SIZE];
    char initiator[VALUE_SIZE*3];
} FACE_RECORD;

typedef struct REQUEST_SUBJECT_GROUP_T
{
	int page;
    int size;
    char name[VALUE_SIZE];
}REQUEST_SUBJECT_GROUP;

typedef struct SUBJECT_GROUP_T
{
	int id;
	char name[VALUE_SIZE];
	char update_time[VALUE_SIZE];
}SUBJECT_GROUP;

typedef struct PAGE_INFO_T
{
    int count;// 总记录数
    int current;// 当前页数
    int size;// 每页size
    int total;// 总页数
} PAGE_INFO;

class CHttpApiControl
{
public:
    CHttpApiControl();
    ~CHttpApiControl();
    static CHttpApiControl* GetInstance();
    int Init();
    int Login(char* user_name, char* password);
    int GetDeviceList(int device_type, int device_status, DEVICE_INFO_LIST*& device_list);
    int DestoryDeviceList(DEVICE_INFO_LIST* head);
    int NotifyDownloadPic(int subject_id, int photo_id, std::vector<int> group_ids);
    int NotifyDeleteFaceBySubjectID(int subject_id);
    int NotifyDeleteFaceByPhotoID(int photo_id, std::vector<int> group_ids, std::string subject_name_old="");
    int NotifyAddFaceByPhotoID(int photo_id, std::vector<int> group_ids);
    int NotifyModifyFaceByPhotoID(int photo_id, std::vector<int> group_ids);
    int RequestFaceRecord(REQUEST_FACE_RECORD* record, std::vector<FACE_RECORD>& face_record, PAGE_INFO& page_info);
	int RequestSubjectGroup(REQUEST_SUBJECT_GROUP* group, std::vector<SUBJECT_GROUP>& subject_group, PAGE_INFO& page_info);
    int Heartbeat(int retry);    
private:
    static CHttpApiControl* instance;

private:
    char token_[VALUE_SIZE];
};

CHttpApiControl* GetHttpApiControlInstance();

#endif //__GSFACE_HTTP_API_CONTROL_H__
