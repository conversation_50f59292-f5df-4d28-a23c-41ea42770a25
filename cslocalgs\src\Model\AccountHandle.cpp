#include "ConnectionPool.h"
#include <evpp/logging.h>
#include "Errcode.h"
#include "RldbQuery.h"
#include "Md5.h"
#include "AccountHandle.h"
#include "AES256.h"
#include "util_cstring.h"
#include "Utility.h"

#define GSFACE_ACCOUNT_TABLE    "account"

CAccountHandle* GetAccountHandleInstance()
{
    return CAccountHandle::GetInstance();
}

CAccountHandle::CAccountHandle()
{
}

CAccountHandle::~CAccountHandle()
{

}

int CAccountHandle::AddAcount(const std::string user, const std::string password, const std::string token, int expire)
{
    if (user.empty() || password.empty())
    {
        LOG_WARN << "username or password  empty";
        return -1;
    }
    std::string strPasswdMD5 = GetBufMd5(password.data(), password.size());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    //先查询是否有这个用户，有的话，更新消息
    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "SELECT count(*) FROM %s WHERE username='%s';", GSFACE_ACCOUNT_TABLE, user.data());
    query.Query(sql);
    int nCount = 0;
    if (query.MoveToNextRow())
    {
        nCount = atoi(query.GetRowData(0));
    }
    if (nCount > 0)
    {
        memset(sql, 0, sizeof(sql));
        snprintf(sql, sizeof(sql), "UPDATE %s SET passwdMD5='%s',token='%s',expire=%d where username='%s';",
                 GSFACE_ACCOUNT_TABLE, password.data(), token.data(), expire, user.data());
    }
    else
    {
        memset(sql, 0, sizeof(sql));
        snprintf(sql, sizeof(sql), "INSERT INTO %s (username, passwdMD5, token, expire) VALUES ('%s', '%s', '%s', %d);",
                 GSFACE_ACCOUNT_TABLE, user.data(), password.data(), token.data(), expire);
    }
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to insert new record into db,SQL is " << sql;
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int CAccountHandle::UpdateToken(const std::string user, const std::string token)
{
    if (user.empty() || token.empty())
    {
        LOG_WARN << "username or strToken empty";
        return -1;
    }


    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "UPDATE %s SET token='%s' where username='%s';",
             GSFACE_ACCOUNT_TABLE, token.data(), user.data());
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to insert new record into db,SQL is " << sql;
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int CAccountHandle::UpdateExpire(const std::string token, int timestamp)
{
    if (token.empty() || timestamp <= 0)
    {
        LOG_WARN << "UpdateExpire failed";
        return -1;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "UPDATE %s SET expire=%d where token='%s';",
             GSFACE_ACCOUNT_TABLE, timestamp, token.data());
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to insert new record into db,SQL is " << sql;
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int CAccountHandle::GetTokenAndExpire(char* user, char* token, int token_size, char* expire, int expire_size)
{
    if (token == NULL || expire == NULL || token_size <= 0 || expire_size <= 0)
    {
        return -1;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "SELECT token, FROM_UNIXTIME(expire) FROM %s WHERE username='%s';",
             GSFACE_ACCOUNT_TABLE, user);

    query.Query(sql);
    if (query.MoveToNextRow())
    {
        Copychar(token, token_size, query.GetRowData(0));
        Copychar(expire, expire_size, query.GetRowData(1));
    }
    else
    {
        LOG_WARN << "Get token and expire from DB failed.";
    }
    ReleaseDBConn(conn);
    return 0;

}

int CAccountHandle::GetToken(char* token, int token_size)
{
    if (token == NULL || token_size <= 0)
    {
        return -1;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "SELECT token FROM %s limit 1;", GSFACE_ACCOUNT_TABLE);

    query.Query(sql);
    if (query.MoveToNextRow())
    {
        Copychar(token, token_size, query.GetRowData(0));
    }
    else
    {
        LOG_WARN << "Get token and expire from DB failed.";
    }
    ReleaseDBConn(conn);
    return 0;
}

bool CAccountHandle::CheckToken(const char* token)
{
    if (token == NULL)
    {
        return false;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return false;
    }
    int nCount = 0;
    time_t timestamp;
    timestamp = time(NULL);
    CRldbQuery query(tmp_conn);
    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "SELECT count(*) FROM %s WHERE token='%s' AND expire>%ld;",
             GSFACE_ACCOUNT_TABLE, token, timestamp);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        nCount = atoi(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return nCount > 0 ? true : false;
}


CAccountHandle* CAccountHandle::instance = NULL;

CAccountHandle* CAccountHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAccountHandle();
    }

    return instance;
}


