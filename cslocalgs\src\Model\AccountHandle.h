#ifndef __GSFACE_ACCOUNT_HANDLE_CONTROL_H__
#define __GSFACE_ACCOUNT_HANDLE_CONTROL_H__

#include <string>

class CAccountHandle
{
public:
    CAccountHandle();
    ~CAccountHandle();
    static CAccountHandle* GetInstance();
    int AddAcount(const std::string user, const std::string password, const std::string token, int expire);
    int UpdateToken(const std::string user, const std::string token);
    int UpdateExpire(const std::string token, int timestamp);
    int GetTokenAndExpire(char* user, char* token, int token_size, char* expire, int expire_size);
    int GetToken(char* token, int token_size);
    bool CheckToken(const char* token);

private:
    static CAccountHandle* instance;

};

CAccountHandle* GetAccountHandleInstance();

#endif //__GSFACE_ACCOUNT_HANDEL_CONTROL_H__

