
#include "FaceIDXml.h"
#include "Md5.h"
#include "tinyxml.h"

#define XML_FACE_ID_NODE_NAME                       "KeyData"
#define XML_FACE_ID_NODE_KEY_NAME                   "Key"
#define XML_FACE_ID_NODE_TARFILE_PATH               "TarFilePath"
#define XML_FACE_ID_NODE_PICFILE_PATH               "PicFilePath"
#define XML_FACE_ID_ID_ATTR_NAME                    "ID"
#define XML_FACE_ID_USER_ATTR_NAME                  "Name"
#define XML_FACE_ID_CODE_ATTR_NAME                  "Code"
#define XML_FACE_ID_FILE_ATTR_NAME                  "File"
#define XML_FACE_ID_FILETYPE_ATTR_NAME              "FileType"
#define XML_FACE_ID_DOORNUM_ATTR_NAME               "DoorNum"
#define XML_FACE_ID_MON_ATTR_NAME                   "Mon"
#define XML_FACE_ID_TUE_ATTR_NAME                   "Tue"
#define XML_FACE_ID_WED_ATTR_NAME                   "Wed"
#define XML_FACE_ID_THUR_ATTR_NAME                  "Thur"
#define XML_FACE_ID_FRI_ATTR_NAME                   "Fri"
#define XML_FACE_ID_SAT_ATTR_NAME                   "Sat"
#define XML_FACE_ID_SUN_ATTR_NAME                   "Sun"
#define XML_FACE_ID_TIMESTART_ATTR_NAME             "TimeStart"
#define XML_FACE_ID_TIMEEND_ATTR_NAME               "TimeEnd"
#define XML_FACE_ID_YES                             "1"
#define XML_FACE_ID_NO                              "0"
#define XML_FACE_ID_PHONE_ATTR_NAME                 "Phone"
#define XML_FACE_ID_MOBILE_ATTR_NAME                "Mobile"
#define XML_FACE_ID_EMAIL_ATTR_NAME                 "Email"
#define XML_FACE_ID_COMPANY_ATTR_NAME               "Company"
#define XML_FACE_ID_ADDRESS_ATTR_NAME               "Address"
#define XML_FACE_ID_CREATE_TIME_ATTR_NAME           "CreateTime"
#define XML_FACE_ID_EXPIRE_TIME_ATTR_NAME           "ExpireTime"
#define XML_FACE_ID_DESC_ATTR_NAME                  "Desc"
#define XML_FACE_ID_USER_ATTR_NAME_MD5              "NameMd5"

#define XML_MAX_ATTR_SIZE                           2048

CFaceIDXml* GetFaceIDXmlInstance()
{
    return CFaceIDXml::GetInstance();
}

CFaceIDXml::CFaceIDXml()
{

}

CFaceIDXml::~CFaceIDXml()
{

}

CFaceIDXml* CFaceIDXml::instance = NULL;

CFaceIDXml* CFaceIDXml::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CFaceIDXml();
    }

    return instance;
}


/*
<?xml version="1.0" encoding="UTF-8" ?>
<KeyData>
    <Key ID="1" Name="101" Code="01011122" DoorNum="3" Mon="1" Tue="1" Wed="1" Thur="1" Fri="1" Sat="1" Sun="1" TimeStart="00:00" TimeEnd="23:59"/>
</KeyData >
*/
static int create_faceid_xml(const char* file_name, const char* file_dw_path, std::vector<FACE_PHOTO>& photo_vec)
{
    if (file_name == NULL || file_dw_path == NULL)
    {
        return -1;
    }

    TiXmlDeclaration* decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    TiXmlDocument doc;
    doc.LinkEndChild(decl);

    TiXmlElement* root_node = new TiXmlElement(XML_FACE_ID_NODE_NAME);
    doc.LinkEndChild(root_node);

    root_node->SetAttribute(XML_FACE_ID_NODE_PICFILE_PATH, file_dw_path);

    for (std::size_t i = 0; i < photo_vec.size(); i++)
    {
        TiXmlElement* pNewNode = new TiXmlElement(XML_FACE_ID_NODE_KEY_NAME);

        pNewNode->SetAttribute(XML_FACE_ID_ID_ATTR_NAME, photo_vec[i].id);

        pNewNode->SetAttribute(XML_FACE_ID_USER_ATTR_NAME, photo_vec[i].subject_name);

        pNewNode->SetAttribute(XML_FACE_ID_USER_ATTR_NAME_MD5, "");

        pNewNode->SetAttribute(XML_FACE_ID_FILETYPE_ATTR_NAME, 0);//FileType, 0=jpg, 1=特征码

        pNewNode->SetAttribute(XML_FACE_ID_FILE_ATTR_NAME, photo_vec[i].name);

        pNewNode->SetAttribute(XML_FACE_ID_DOORNUM_ATTR_NAME, "1234");

        pNewNode->SetAttribute(XML_FACE_ID_MON_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_TUE_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_WED_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_THUR_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_FRI_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_SAT_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_SUN_ATTR_NAME, XML_FACE_ID_YES);

        pNewNode->SetAttribute(XML_FACE_ID_TIMESTART_ATTR_NAME, "00:00");

        pNewNode->SetAttribute(XML_FACE_ID_TIMEEND_ATTR_NAME, "23:59");

        TiXmlNode* pSubNode = root_node->LinkEndChild(pNewNode);
        if (pSubNode == nullptr)
        {
            return -1;
        }
    }

    if (!doc.SaveFile(file_name))
    {
        return -1;
    }
    return 0;
}

int CFaceIDXml::CreateXmlFile(char* file_name, char* file_dw_path, std::vector<FACE_PHOTO>& photo_vec)
{
    if (file_name == nullptr || file_dw_path == nullptr)
    {
        return -1;
    }
    create_faceid_xml(file_name, file_dw_path, photo_vec);
    return 0;
}





