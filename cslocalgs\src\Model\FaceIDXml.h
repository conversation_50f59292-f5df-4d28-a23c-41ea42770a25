#ifndef __GSFACE_FACE_ID_XML_H__
#define __GSFACE_FACE_ID_XML_H__

#include <string>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "PhotoHandle.h"

#define FACE_ID_XML_FILE    "FaceID.xml"
#define FACE_ID_XML_FILE_AES    "FaceID_AES.xml"


class CFaceIDXml
{
public:
    CFaceIDXml();
    ~CFaceIDXml();
    static CFaceIDXml* GetInstance();
    int CreateXmlFile(char* file_name, char* file_dw_path, std::vector<FACE_PHOTO>& photo_vec);
private:

    static CFaceIDXml* instance;
};

CFaceIDXml* GetFaceIDXmlInstance();

#endif //__GSFACE_FACE_ID_XML_H__

