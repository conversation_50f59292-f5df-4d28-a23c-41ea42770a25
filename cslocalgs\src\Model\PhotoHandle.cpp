#include "ConnectionPool.h"
#include <evpp/logging.h>
#include "Errcode.h"
#include "RldbQuery.h"
#include "Md5.h"
#include "PhotoHandle.h"
#include "AES256.h"
#include "util_cstring.h"
#include "Utility.h"
#include "GsfaceConf.h"

#define GSFACE_PHOTO_TABLE  "photo"

extern GSFACE_CONF gstGSFACEConf; //鍏ㄥ眬閰嶇疆淇℃伅

CPhotoHandle* GetPhotoHandleInstance()
{
    return CPhotoHandle::GetInstance();
}

CPhotoHandle::CPhotoHandle()
{
}

CPhotoHandle::~CPhotoHandle()
{

}


int CPhotoHandle::AddPhoto(FACE_PHOTO& photo)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "INSERT INTO %s (subject_id, company_id, name, quality, version, feature) VALUES (%d,%d,'%s',%f,%d,'%s');",
             GSFACE_PHOTO_TABLE, photo.subject_id, photo.company_id, photo.name, photo.quality, photo.version, photo.feature);

    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to insert new photo into db,SQL is " << sql;
        return -1;
    }

    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql), "SELECT LAST_INSERT_ID();");
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        photo.id = atoi(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return 0;
}

int CPhotoHandle::UpdateSubjectIDByID(int subject_id, int id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "UPDATE %s SET subject_id=%d WHERE ID=%d;", GSFACE_PHOTO_TABLE, subject_id, id);
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to insert new photo into db,SQL is " << sql;
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

std::string CPhotoHandle::GetPhotoNameByID(int photo_id)
{
    std::string photo_name("");
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return photo_name;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT name FROM %s where ID=%d;", GSFACE_PHOTO_TABLE, photo_id);

    query.Query(sql);
    if (query.MoveToNextRow())
    {
        photo_name = query.GetRowData(0);
    }
    else
    {
        LOG_WARN << "failed.";
    }
    ReleaseDBConn(conn);
    return photo_name;
}

int CPhotoHandle::GetPhotoIDListBySubjectID(int subject_id, std::vector<int>& photo_id)
{
    photo_id.clear();
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT ID FROM %s where subject_id=%d;", GSFACE_PHOTO_TABLE, subject_id);

    query.Query(sql);
    while (query.MoveToNextRow())
    {
        photo_id.push_back(atoi(query.GetRowData(0)));
    }
    ReleaseDBConn(conn);
    return 0;
}

int CPhotoHandle::DeletePhotoBySubjectID(int subject_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "DELETE FROM %s WHERE subject_id=%d;", GSFACE_PHOTO_TABLE, subject_id);
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed,SQL is " << sql;
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int CPhotoHandle::DeletePhotoFileBySubjectID(int subject_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "SELECT name FROM %s WHERE subject_id=%d;", GSFACE_PHOTO_TABLE, subject_id);
    query.Query(sql);
    while (query.MoveToNextRow())
    {
        char file_path[URL_SIZE] = {0};
        snprintf(file_path, sizeof(file_path), "%s/%s", gstGSFACEConf.storage_path, query.GetRowData(0));
        unlink(file_path);
    }
    ReleaseDBConn(conn);
	return 0;
}

int CPhotoHandle::GetSubjectIDByID(int photo_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT subject_id FROM %s where ID=%d;", GSFACE_PHOTO_TABLE, photo_id);

    query.Query(sql);
    int subject_id = -1;
    if (query.MoveToNextRow())
    {
        subject_id = atoi(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return subject_id;
}

int CPhotoHandle::GetPhotoListBySubjectID(int subject_id, std::vector<FACE_PHOTO>& photo_list)
{
    photo_list.clear();
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT * FROM %s where subject_id=%d;", GSFACE_PHOTO_TABLE, subject_id);
	LOG_INFO << "sql [" << sql << "]";
    query.Query(sql);
    while (query.MoveToNextRow())
    {
        FACE_PHOTO photo;
        memset(&photo, 0, sizeof(photo));
        photo.id = atoi(query.GetRowData(PHOTO_TABLE_COL_ID));
        photo.subject_id = subject_id;
        photo.company_id = atoi(query.GetRowData(PHOTO_TABLE_COL_COMPANY_ID));
        photo.version = atoi(query.GetRowData(PHOTO_TABLE_COL_VERSION));
        photo.quality = atoi(query.GetRowData(PHOTO_TABLE_COL_QUALITY));
        Copychar(photo.name, sizeof(photo.name), query.GetRowData(PHOTO_TABLE_COL_NAME));
        Copychar(photo.feature, sizeof(photo.feature), query.GetRowData(PHOTO_TABLE_COL_FEATURE));
        photo_list.push_back(photo);
    }
    ReleaseDBConn(conn);
    return 0;
}

int CPhotoHandle::GetRegisteredPhotoList(std::vector<FACE_PHOTO>& photo_vec, int group_id)
{
    photo_vec.clear();
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT photo.ID, photo.subject_id, photo.company_id, photo.name, subject.name FROM photo JOIN subject ON photo.subject_id=subject.ID WHERE subject.groupflag=0;");

    query.Query(sql);
    while (query.MoveToNextRow())
    {
        FACE_PHOTO photo;
        memset(&photo, 0, sizeof(photo));
        photo.id = atoi(query.GetRowData(0));
        photo.subject_id = atoi(query.GetRowData(1));;
        photo.company_id = atoi(query.GetRowData(2));
        Copychar(photo.name, sizeof(photo.name), query.GetRowData(3));
        Copychar(photo.subject_name, sizeof(photo.subject_name), query.GetRowData(4));
        photo_vec.push_back(photo);
    }
	
	memset(&sql, 0, sizeof(sql));	
    snprintf(sql, sizeof(sql), "SELECT photo.ID, photo.subject_id, photo.company_id, photo.name, B.name FROM photo JOIN \
		(SELECT sub.ID id, sub.name name FROM subject sub JOIN subjectGroup subG ON sub.ID = subG.subject_id WHERE subG.group_id=%d) AS B \
		ON photo.subject_id=B.id;", group_id);
	query.Query(sql);
    while (query.MoveToNextRow())
    {
        FACE_PHOTO photo;
        memset(&photo, 0, sizeof(photo));
        photo.id = atoi(query.GetRowData(0));
        photo.subject_id = atoi(query.GetRowData(1));;
        photo.company_id = atoi(query.GetRowData(2));
        Copychar(photo.name, sizeof(photo.name), query.GetRowData(3));
        Copychar(photo.subject_name, sizeof(photo.subject_name), query.GetRowData(4));
        photo_vec.push_back(photo);
    }
	ReleaseDBConn(conn);
    return 0;
}

CPhotoHandle* CPhotoHandle::instance = NULL;

CPhotoHandle* CPhotoHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPhotoHandle();
    }

    return instance;
}

