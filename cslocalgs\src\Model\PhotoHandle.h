#ifndef __GSFACE_PHOTO_HANDLE_CONTROL_H__
#define __GSFACE_PHOTO_HANDLE_CONTROL_H__

#include <string>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <map>
#include <vector>
#include "MsgCommonDefine.h"

typedef enum
{
    PHOTO_TABLE_COL_ID = 0,
    PHOTO_TABLE_COL_SUBJECT_ID,
    PHOTO_TABLE_COL_COMPANY_ID,
    PHOTO_TABLE_COL_NAME,
    PHOTO_TABLE_COL_QUALITY,
    PHOTO_TABLE_COL_VERSION,
    PHOTO_TABLE_COL_FEATURE,
} PHOTO_TABLE_COL;

typedef struct FACE_PHOTO_T
{
    int id;
    int subject_id;
    int company_id;
    int version;
    float quality;
    char name[VALUE_SIZE * 2]; //图片名称
    char subject_name[VALUE_SIZE];//用户名称
    char feature[BUFF_SIZE];
} FACE_PHOTO;

typedef std::map<int, std::vector<FACE_PHOTO>> SUBJECT_ID_PHOTO_MAP;

class CPhotoHandle
{
public:
    CPhotoHandle();
    ~CPhotoHandle();
    static CPhotoHandle* GetInstance();
    int AddPhoto(FACE_PHOTO& photo);
    int UpdateSubjectIDByID(int subject_id, int id);
    std::string GetPhotoNameByID(int photo_id);
    int GetPhotoIDListBySubjectID(int subject_id, std::vector<int>& photo_id);
    int DeletePhotoBySubjectID(int subject_id);
    int DeletePhotoFileBySubjectID(int subject_id);
    int GetSubjectIDByID(int photo_id);
    int GetPhotoListBySubjectID(int subject_id, std::vector<FACE_PHOTO>& photo_list);
    int GetRegisteredPhotoList(std::vector<FACE_PHOTO>& photo_vec, int group_id);
private:
    static CPhotoHandle* instance;

};

CPhotoHandle* GetPhotoHandleInstance();


#endif //__GSFACE_PHOTO_HANDLE_CONTROL_H__

