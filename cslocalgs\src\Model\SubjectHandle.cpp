#include "ConnectionPool.h"
#include <evpp/logging.h>
#include "Errcode.h"
#include "RldbQuery.h"
#include "Md5.h"
#include "SubjectHandle.h"
#include "AES256.h"
#include "util_cstring.h"
#include "Utility.h"
#include "PhotoHandle.h"

#define GSFACE_SUBJECT_TABLE    "subject"
#define GSFACE_SUBJECT_GROUP_TABLE	"subjectGroup"

CSubjectHandle* GetSubjectHandleInstance()
{
    return CSubjectHandle::GetInstance();
}

CSubjectHandle::CSubjectHandle()
{
}

CSubjectHandle::~CSubjectHandle()
{

}

int CSubjectHandle::AddSubject(FACE_SUBJECT& subject)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "INSERT INTO %s (subject_type, email, name, gender, phone, avatar, department, title,"
             "description, job_number, remark, birthday, entry_date, purpose, interviewee, come_from, start_time, end_time,groupflag) "
             "VALUES (%d,'%s','%s',%d,'%s','%s','%s','%s','%s','%s','%s',%d,%d,%d,'%s','%s',%d,%d,%d);",
             GSFACE_SUBJECT_TABLE, subject.subject_type, subject.email, subject.name, subject.gender, subject.phone,
             subject.avatar, subject.department, subject.title, subject.description, subject.job_num, subject.remark,
             subject.birthday, subject.entry_date, subject.purpose, subject.interviewee, subject.come_from, subject.start_time, subject.end_time, 
             subject.group_ids.size() > 0 ? 1 : 0);

    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to insert new subject into db,SQL is " << sql;
        return -1;
    }

    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql), "SELECT LAST_INSERT_ID();");
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        subject.id = atoi(query.GetRowData(0));
    }

	//更新subjectGroup列表
	for(int i=0; i<subject.group_ids.size(); i++)
	{
		memset(sql, 0, sizeof(sql));
		snprintf(sql, sizeof(sql), "INSERT INTO %s (subject_id, group_id) VALUES (%d, %d)", GSFACE_SUBJECT_GROUP_TABLE, subject.id, subject.group_ids[i]);
		conn->Execute(sql);
	}
    ReleaseDBConn(conn);
    return 0;
}

int CSubjectHandle::UpdateSubject(FACE_SUBJECT& subject)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "UPDATE %s SET subject_type=%d, email='%s', name='%s', gender=%d, phone='%s', avatar='%s', "
             "department='%s', title='%s', description='%s', job_number='%s', remark='%s', birthday=%d, entry_date=%d, "
             "purpose=%d, interviewee='%s', come_from='%s', start_time=%d, end_time=%d, groupflag=%d WHERE ID=%d;",
             GSFACE_SUBJECT_TABLE, subject.subject_type, subject.email, subject.name, subject.gender, subject.phone,
             subject.avatar, subject.department, subject.title, subject.description, subject.job_num, subject.remark,
             subject.birthday, subject.entry_date, subject.purpose, subject.interviewee, subject.come_from, subject.start_time, subject.end_time, subject.group_ids.size() > 0 ? 1 : 0, subject.id);

    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "Failed to update subject,SQL is " << sql;
        return -1;
    }
	//删除subjecGoup列表中旧的关系
	memset(sql, 0, sizeof(sql));
	snprintf(sql, sizeof(sql), "DELETE FROM %s WHERE subject_id=%d", GSFACE_SUBJECT_GROUP_TABLE, subject.id);
	conn->Execute(sql);

	//更新subjectGroup列表
	memset(sql, 0, sizeof(sql));
	for(int i=0; i<subject.group_ids.size(); i++)
	{
		snprintf(sql, sizeof(sql), "INSERT INTO %s (subject_id, group_id) VALUES (%d, %d)", GSFACE_SUBJECT_GROUP_TABLE, subject.id, subject.group_ids[i]);
		conn->Execute(sql);
	}
    ReleaseDBConn(conn);
    //GetPhotoHandleInstance()->GetPhotoListBySubjectID(subject.id, subject.photos);
    return 0;
}

std::string CSubjectHandle::GetSubjectNameByID(int subject_id)
{
    std::string subject_name("");
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return subject_name;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT name FROM %s where ID=%d;", GSFACE_SUBJECT_TABLE, subject_id);
	LOG_WARN << sql ;
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        subject_name = query.GetRowData(0);
    }
    else
    {
        LOG_WARN << "failed.";
    }
    ReleaseDBConn(conn);
    return subject_name;
}

std::string CSubjectHandle::GetSubjectNameByPhotoID(int photo_id)
{
    int subject_id = GetPhotoHandleInstance()->GetSubjectIDByID(photo_id);
    return GetSubjectNameByID(subject_id);
}

int CSubjectHandle::GetSubjectByID(int subject_id, FACE_SUBJECT& face_subject)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
	CRldbQuery query1(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT * FROM %s where ID=%d;", GSFACE_SUBJECT_TABLE, subject_id);
	LOG_INFO << "sql [" << sql << "]";
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        face_subject.id = atoi(query.GetRowData(SUBJECT_TABLE_COL_ID));
        face_subject.start_time = atoi(query.GetRowData(SUBJECT_TABLE_COL_START_TIME));
        face_subject.end_time = atoi(query.GetRowData(SUBJECT_TABLE_COL_END_TIME));
        face_subject.purpose = atoi(query.GetRowData(SUBJECT_TABLE_COL_PURPOSE));
        face_subject.birthday = atoi(query.GetRowData(SUBJECT_TABLE_COL_BIRTHDAY));
        face_subject.entry_date = atoi(query.GetRowData(SUBJECT_TABLE_COL_ENTRY_DATE));
        face_subject.gender = atoi(query.GetRowData(SUBJECT_TABLE_COL_GENDER));
        face_subject.subject_type = atoi(query.GetRowData(SUBJECT_TABLE_COL_SUBJECT_TYPE));
        Copychar(face_subject.email, sizeof(face_subject.email), query.GetRowData(SUBJECT_TABLE_COL_EMAIL));
        Copychar(face_subject.phone, sizeof(face_subject.phone), query.GetRowData(SUBJECT_TABLE_COL_PHONE));
        Copychar(face_subject.avatar, sizeof(face_subject.avatar), query.GetRowData(SUBJECT_TABLE_COL_AVATAR));
        Copychar(face_subject.department, sizeof(face_subject.department), query.GetRowData(SUBJECT_TABLE_COL_DEPARTMENT));
        Copychar(face_subject.title, sizeof(face_subject.title), query.GetRowData(SUBJECT_TABLE_COL_TITLE));
        Copychar(face_subject.description, sizeof(face_subject.description), query.GetRowData(SUBJECT_TABLE_COL_DESCRIPTION));
        Copychar(face_subject.interviewee, sizeof(face_subject.interviewee), query.GetRowData(SUBJECT_TABLE_COL_INTERVIEWEE));
        Copychar(face_subject.come_from, sizeof(face_subject.come_from), query.GetRowData(SUBJECT_TABLE_COL_COME_FROM));
        Copychar(face_subject.job_num, sizeof(face_subject.job_num), query.GetRowData(SUBJECT_TABLE_COL_JOB_NUMBER));
        Copychar(face_subject.remark, sizeof(face_subject.remark), query.GetRowData(SUBJECT_TABLE_COL_REMARK));
        Copychar(face_subject.name, sizeof(face_subject.name), query.GetRowData(SUBJECT_TABLE_COL_NAME));

		memset(&sql, 0, sizeof(sql));
		snprintf(sql, sizeof(sql), "SELECT group_id FROM %s where subject_id=%d;", GSFACE_SUBJECT_GROUP_TABLE, subject_id);
		query1.Query(sql);
    	while (query1.MoveToNextRow())
    	{
    		face_subject.group_ids.push_back(atoi(query1.GetRowData(0)));
    	}
    }
    else
    {
        LOG_WARN << "failed.";
    }

    ReleaseDBConn(conn);
    GetPhotoHandleInstance()->GetPhotoListBySubjectID(face_subject.id, face_subject.photos);
    return 0;
}

int CSubjectHandle::DeleteSubjectByID(int subject_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    char sql[512] = {0};
    snprintf(sql, sizeof(sql), "DELETE FROM %s where ID=%d;", GSFACE_SUBJECT_TABLE, subject_id);
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        LOG_WARN << "failed, SQL is " << sql;
        return -1;
    }
	//删除subjectGroup关系
	memset(sql, 0, sizeof(sql));
	snprintf(sql, sizeof(sql), "DELETE FROM %s where subject_id=%d;", GSFACE_SUBJECT_GROUP_TABLE, subject_id);
	conn->Execute(sql);
	
    ReleaseDBConn(conn);
    return 0;
}

//分页查询返回个数
int CSubjectHandle::GetSubjectList(int subject_type, std::string name, std::string order, int page, int size, std::vector<FACE_SUBJECT>& subject_list)
{
    subject_list.clear();
    int total = 0;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
	CRldbQuery query1(tmp_conn);
	CRldbQuery query2(tmp_conn);
    char sql[BUFF_SIZE] = {0};

    char name_filter[VALUE_SIZE*3] = {0};
    if (name.size() > 0)
    {
        snprintf(name_filter, sizeof(name_filter), " AND name='%s' OR 'pinyin=%s'", name.data(), name.data());
    }
    char order_filter[VALUE_SIZE] = {0};
    if (order.size() > 0 && !strcmp(order.data(), "name"))
    {
        snprintf(order_filter, sizeof(order_filter), " ORDER BY name ASC");
    }
    char type_filter[VALUE_SIZE] = {0};
    if (subject_type >= 0)
    {
        snprintf(type_filter, sizeof(type_filter), " where subject_type=%d", subject_type);
    }
    char limit_filter[VALUE_SIZE] = {0};
    if (size > 0)
    {
        snprintf(limit_filter, sizeof(limit_filter), " limit %d,%d", (page - 1)*size, size);
    }
    snprintf(sql, sizeof(sql), "SELECT SQL_CALC_FOUND_ROWS * FROM %s%s%s%s%s;", GSFACE_SUBJECT_TABLE, type_filter, name_filter, order_filter, limit_filter);
    query.Query(sql);
	memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql), "SELECT FOUND_ROWS();");
    query2.Query(sql);
    if (query2.MoveToNextRow())
    {
        total = atoi(query2.GetRowData(0));
    }
    while (query.MoveToNextRow())
    {
        FACE_SUBJECT subject_node;
        //memset(&subject_node, 0, sizeof(subject_node));
        subject_node.id = atoi(query.GetRowData(SUBJECT_TABLE_COL_ID));
        subject_node.start_time = atoi(query.GetRowData(SUBJECT_TABLE_COL_START_TIME));
        subject_node.end_time = atoi(query.GetRowData(SUBJECT_TABLE_COL_END_TIME));
        subject_node.purpose = atoi(query.GetRowData(SUBJECT_TABLE_COL_PURPOSE));
        subject_node.birthday = atoi(query.GetRowData(SUBJECT_TABLE_COL_BIRTHDAY));
        subject_node.entry_date = atoi(query.GetRowData(SUBJECT_TABLE_COL_ENTRY_DATE));
        subject_node.gender = atoi(query.GetRowData(SUBJECT_TABLE_COL_GENDER));
        subject_node.subject_type = atoi(query.GetRowData(SUBJECT_TABLE_COL_SUBJECT_TYPE));
        Copychar(subject_node.email, sizeof(subject_node.email), query.GetRowData(SUBJECT_TABLE_COL_EMAIL));
        Copychar(subject_node.phone, sizeof(subject_node.phone), query.GetRowData(SUBJECT_TABLE_COL_PHONE));
        Copychar(subject_node.avatar, sizeof(subject_node.avatar), query.GetRowData(SUBJECT_TABLE_COL_AVATAR));
        Copychar(subject_node.department, sizeof(subject_node.department), query.GetRowData(SUBJECT_TABLE_COL_DEPARTMENT));
        Copychar(subject_node.title, sizeof(subject_node.title), query.GetRowData(SUBJECT_TABLE_COL_TITLE));
        Copychar(subject_node.description, sizeof(subject_node.description), query.GetRowData(SUBJECT_TABLE_COL_DESCRIPTION));
        Copychar(subject_node.interviewee, sizeof(subject_node.interviewee), query.GetRowData(SUBJECT_TABLE_COL_INTERVIEWEE));
        Copychar(subject_node.come_from, sizeof(subject_node.come_from), query.GetRowData(SUBJECT_TABLE_COL_COME_FROM));
        Copychar(subject_node.job_num, sizeof(subject_node.job_num), query.GetRowData(SUBJECT_TABLE_COL_JOB_NUMBER));
        Copychar(subject_node.remark, sizeof(subject_node.remark), query.GetRowData(SUBJECT_TABLE_COL_REMARK));
        Copychar(subject_node.name, sizeof(subject_node.name), query.GetRowData(SUBJECT_TABLE_COL_NAME));

		memset(sql, 0, sizeof(sql));
		snprintf(sql, sizeof(sql), "SELECT group_id FROM %s WHERE subject_id=%d", GSFACE_SUBJECT_GROUP_TABLE, subject_node.id);
		query1.Query(sql);
		while(query1.MoveToNextRow())
		{		
			subject_node.group_ids.push_back(atoi(query.GetRowData(0)));
		}
        subject_list.push_back(subject_node);
    }
    ReleaseDBConn(conn);
    for (std::size_t i = 0; i < subject_list.size(); i++)
    {
        GetPhotoHandleInstance()->GetPhotoListBySubjectID(subject_list[i].id, subject_list[i].photos);
    }
    return total;
}

int CSubjectHandle::GetSubjectByName(char* name, FACE_SUBJECT& subject)
{
    if (name == nullptr)
    {
        return -1;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    snprintf(sql, sizeof(sql), "SELECT * FROM %s WHERE name='%s' limit 1;", GSFACE_SUBJECT_TABLE, name);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        subject.id = atoi(query.GetRowData(SUBJECT_TABLE_COL_ID));
        subject.start_time = atoi(query.GetRowData(SUBJECT_TABLE_COL_START_TIME));
        subject.end_time = atoi(query.GetRowData(SUBJECT_TABLE_COL_END_TIME));
        subject.purpose = atoi(query.GetRowData(SUBJECT_TABLE_COL_PURPOSE));
        subject.birthday = atoi(query.GetRowData(SUBJECT_TABLE_COL_BIRTHDAY));
        subject.entry_date = atoi(query.GetRowData(SUBJECT_TABLE_COL_ENTRY_DATE));
        subject.gender = atoi(query.GetRowData(SUBJECT_TABLE_COL_GENDER));
        subject.subject_type = atoi(query.GetRowData(SUBJECT_TABLE_COL_SUBJECT_TYPE));
        Copychar(subject.email, sizeof(subject.email), query.GetRowData(SUBJECT_TABLE_COL_EMAIL));
        Copychar(subject.phone, sizeof(subject.phone), query.GetRowData(SUBJECT_TABLE_COL_PHONE));
        Copychar(subject.avatar, sizeof(subject.avatar), query.GetRowData(SUBJECT_TABLE_COL_AVATAR));
        Copychar(subject.department, sizeof(subject.department), query.GetRowData(SUBJECT_TABLE_COL_DEPARTMENT));
        Copychar(subject.title, sizeof(subject.title), query.GetRowData(SUBJECT_TABLE_COL_TITLE));
        Copychar(subject.description, sizeof(subject.description), query.GetRowData(SUBJECT_TABLE_COL_DESCRIPTION));
        Copychar(subject.interviewee, sizeof(subject.interviewee), query.GetRowData(SUBJECT_TABLE_COL_INTERVIEWEE));
        Copychar(subject.come_from, sizeof(subject.come_from), query.GetRowData(SUBJECT_TABLE_COL_COME_FROM));
        Copychar(subject.job_num, sizeof(subject.job_num), query.GetRowData(SUBJECT_TABLE_COL_JOB_NUMBER));
        Copychar(subject.remark, sizeof(subject.remark), query.GetRowData(SUBJECT_TABLE_COL_REMARK));
        Copychar(subject.name, sizeof(subject.name), query.GetRowData(SUBJECT_TABLE_COL_NAME));
    }
    ReleaseDBConn(conn);
    GetPhotoHandleInstance()->GetPhotoListBySubjectID(subject.id, subject.photos);
    return 0;
}

bool CSubjectHandle::CheckSubjectExistByID(int subject_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    int count = 0;
    snprintf(sql, sizeof(sql), "SELECT count(*) FROM %s WHERE ID=%d;", GSFACE_SUBJECT_TABLE, subject_id);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        count = atoi(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return count > 0 ? true : false;

}

int CSubjectHandle::GetSubjectCountByGroupID(int group_id)
{
	//先找出groupflag为0的所有subject
	RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    int count = 0;
	snprintf(sql, sizeof(sql), "SELECT count(*) FROM %s WHERE groupflag=0;", GSFACE_SUBJECT_TABLE);
	query.Query(sql);
    if (query.MoveToNextRow())
    {
        count = atoi(query.GetRowData(0));
    }
	snprintf(sql, sizeof(sql), "SELECT count(*) FROM %s WHERE group_id=%d;", GSFACE_SUBJECT_GROUP_TABLE, group_id);
	query.Query(sql);
    if (query.MoveToNextRow())
    {
        count += atoi(query.GetRowData(0));
    }
	ReleaseDBConn(conn);
	return count;
}

std::vector<int> CSubjectHandle::GetSubGroupIDsBySubID(int subject_id)
{
	std::vector<int> sub_groupids;
	sub_groupids.clear();
	RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        LOG_WARN << "Get DB conn failed.";
        return sub_groupids;
    }
    CRldbQuery query(tmp_conn);
    char sql[BUFF_SIZE] = {0};
    int count = 0;
	snprintf(sql, sizeof(sql), "SELECT suG.group_id FROM %s suG join %s sub ON suG.subject_id = sub.ID WHERE sub.groupflag=1 AND suG.subject_id=%d;", GSFACE_SUBJECT_GROUP_TABLE, GSFACE_SUBJECT_TABLE, subject_id);
	query.Query(sql);
	while (query.MoveToNextRow())
    {
        int gourp_id = atoi(query.GetRowData(0));
		sub_groupids.push_back(gourp_id);
    }
	ReleaseDBConn(conn);
	return sub_groupids;
}

CSubjectHandle* CSubjectHandle::instance = NULL;

CSubjectHandle* CSubjectHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CSubjectHandle();
    }

    return instance;
}


