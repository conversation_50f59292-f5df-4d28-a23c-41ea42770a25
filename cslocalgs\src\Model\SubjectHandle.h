#ifndef __GSFACE_SUBJECT_HANDLE_CONTROL_H__
#define __GSFACE_SUBJECT_HANDLE_CONTROL_H__


#include <string>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "MsgCommonDefine.h"
#include "PhotoHandle.h"

typedef enum
{
    SUBJECT_TABLE_COL_ID = 0,
    SUBJECT_TABLE_COL_SUBJECT_TYPE,
    SUBJECT_TABLE_COL_EMAIL,
    SUBJECT_TABLE_COL_PWD_RESETED,
    SUBJECT_TABLE_COL_NAME,
    SUBJECT_TABLE_COL_PINYIN,
    SUBJECT_TABLE_COL_GENDER,
    SUBJECT_TABLE_COL_PHONE,
    SUBJECT_TABLE_COL_AVATAR,
    SUBJECT_TABLE_COL_DEPARTMENT,
    SUBJECT_TABLE_COL_TITLE,
    SUBJECT_TABLE_COL_DESCRIPTION,
    SUBJECT_TABLE_COL_JOB_NUMBER,
    SUBJECT_TABLE_COL_REMARK,
    SUBJECT_TABLE_COL_BIRTHDAY,
    SUBJECT_TABLE_COL_ENTRY_DATE,
    SUBJECT_TABLE_COL_PURPOSE,
    SUBJECT_TABLE_COL_INTERVIEWEE,
    SUBJECT_TABLE_COL_COME_FROM,
    SUBJECT_TABLE_COL_START_TIME,
    SUBJECT_TABLE_COL_END_TIME,
    SUBJECT_TABLE_COL_VISIT_NOTIFY,
} SUBJECT_TABLE_COL;


typedef struct FACE_SUBJECT_T
{
    int id;
    int start_time;    //来访时间 时间戳(单位秒)
    int end_time;    //离开时间 时间戳(单位秒))
    int purpose;    //来访目的
    int birthday;    //生日
    int entry_date;    //入职时间
    int gender;    //性别{0: 未知, 1: 男, 2: 女}
    int subject_type;    //用户类型 {0:员工, 1:访客, 2: VIP访客}
    int pwd_reseted; //是否重置过密码
    int visit_notify; //(访客属性) 来访是否发APP消息推送
    std::vector<FACE_PHOTO> photos; //识别头像列表
    char email[VALUE_SIZE];    //邮箱
    char phone[VALUE_SIZE];    //手机
    char avatar[URL_SIZE];    //头像
    char department[VALUE_SIZE];    //部门
    char title[VALUE_SIZE];    //职位
    char description[BUFF_SIZE];    //签名
    char interviewee[VALUE_SIZE];    //受访人
    char come_from[VALUE_SIZE];    //来访单位
    char job_num[VALUE_SIZE];    //工号
    char remark[BUFF_SIZE];    //备注
    char name[VALUE_SIZE];     //姓名
    std::vector<int> group_ids; //群组ids
} FACE_SUBJECT;

class CSubjectHandle
{
public:
    CSubjectHandle();
    ~CSubjectHandle();
    static CSubjectHandle* GetInstance();
    int AddSubject(FACE_SUBJECT& subject);
    int UpdateSubject(FACE_SUBJECT& subject);
    std::string GetSubjectNameByID(int subject_id);
    std::string GetSubjectNameByPhotoID(int photo_id);
    int GetSubjectByID(int subject_id, FACE_SUBJECT& face_subject);
    int DeleteSubjectByID(int subject_id);
    int GetSubjectList(int subject_type, std::string name, std::string order, int page, int size, std::vector<FACE_SUBJECT>& subject_list);
    int GetSubjectByName(char* name, FACE_SUBJECT& subject);
    bool CheckSubjectExistByID(int subject_id);
	int GetSubjectCountByGroupID(int group_id);
	std::vector<int> GetSubGroupIDsBySubID(int subject_id);
private:
    static CSubjectHandle* instance;

};

CSubjectHandle* GetSubjectHandleInstance();

#endif //__GSFACE_SUBJECT_HANDLE_CONTROL_H__

