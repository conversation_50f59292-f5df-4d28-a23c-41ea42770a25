#ifndef __GSFACE_MSG_COMMON_H__
#define __GSFACE_MSG_COMMON_H__
#pragma once

#include <evpp/logging.h>

#ifndef IP_SIZE
#define IP_SIZE                         16
#endif

#ifndef MAC_SIZE
#define MAC_SIZE                        20
#endif

#ifndef VALUE_SIZE
#define VALUE_SIZE                      64
#endif

#ifndef INT_SIZE
#define INT_SIZE                        12
#endif

#ifndef URL_SIZE
#define URL_SIZE                        256
#endif

#ifndef BUFF_SIZE
#define BUFF_SIZE                       512
#endif

#ifndef BUFF_SIZE_MAX
#define BUFF_SIZE_MAX                       4096
#endif

typedef struct CURL_HTTP_REQUEST_T
{
    int nRequestMethod;
    int nAuthMethod;
    int nRecvSize;
    char* pUrl;
    char* pPostData;
    char* pAuthUser;
    char* pAuthPassword;
    char* pHeadData;
    char* pHeadRecvBuf;
    void* pRecvData;
} CURL_HTTP_REQUEST;

typedef struct CURL_WRITE_CALLBACK_BUF_T
{
    char* pRecvBuf;
    int nRecvSize;	
} CURL_WRITE_CALLBACK_BUF;

typedef enum
{
    HTTP_AUTH_METHOD_NONE,
    HTTP_AUTH_METHOD_BASIC,
    HTTP_AUTH_METHOD_DIGEST,
} HTTP_AUTH_METHOD;

typedef enum
{
    HTTP_REQUEST_METHOD_NONE,
    HTTP_REQUEST_METHOD_GET,
    HTTP_REQUEST_METHOD_POST,
} HTTP_REQUEST_METHOD;

typedef enum
{
    JSON_RETURN_CODE_1000 = 1000,
    JSON_RETURN_CODE_1001 = 1001,
    JSON_RETURN_CODE_1002 = 1002,
    JSON_RETURN_CODE_1003 = 1003,
    JSON_RETURN_CODE_1004 = 1004,
    JSON_RETURN_CODE_1005 = 1005,
    JSON_RETURN_CODE_1006 = 1006,
    JSON_RETURN_CODE_1007 = 1007,
    JSON_RETURN_CODE_1008 = 1008,
    JSON_RETURN_CODE_1009 = 1009,
    JSON_RETURN_CODE_1010 = 1010,
    JSON_RETURN_CODE_2000 = 2000,
    JSON_RETURN_CODE_2001 = 2001,
    JSON_RETURN_CODE_2002 = 2002,
    JSON_RETURN_CODE_2003 = 2003,
    JSON_RETURN_CODE_2004 = 2004,
    JSON_RETURN_CODE_2005 = 2005,
    JSON_RETURN_CODE_2006 = 2006,
    JSON_RETURN_CODE_2007 = 2007,
    JSON_RETURN_CODE_2008 = 2008,
    JSON_RETURN_CODE_2009 = 2009,
    JSON_RETURN_CODE_2010 = 2010,
    JSON_RETURN_CODE_2011 = 2011,
    JSON_RETURN_CODE_2012 = 2012,
    JSON_RETURN_CODE_2013 = 2013,
    JSON_RETURN_CODE_20011 = 20011,
    JSON_RETURN_CODE_20012 = 20012,
} JSON_RETURN_CODE;

#endif //__GSFACE_MSG_COMMON_H__