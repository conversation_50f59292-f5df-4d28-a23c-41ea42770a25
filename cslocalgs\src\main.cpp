#include <evpp/tcp_server.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "HttpServer.h"
#include "GsfaceConf.h"
#include "ConfigFileReader.h"
#include "ConnectionPool.h"
#include "Control.h"
#include "HttpApiControl.h"
#include <KdcDecrypt.h>

#define GS_MAX_RLDB_CONN 3
#define MIN_SOCKET_FRAME_BUFFER 10
GSFACE_CONF gstGSFACEConf; //全局配置信息

#define PIDFILE "/var/run/cslocalgs.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}
void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/cslocalgslog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/cslocalgslog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/cslocalgslog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/cslocalgslog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 10;    //单日志文件最大10M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

void ConfInit()
{
    memset(&gstGSFACEConf, 0, sizeof(GSFACE_CONF));
    CConfigFileReader config_file("/usr/local/cslocalgs/conf/cslocalgs.conf");

    //DB
    ::strncpy(gstGSFACEConf.db_ip, config_file.GetConfigName("db_ip"), sizeof(gstGSFACEConf.db_ip));
    ::strncpy(gstGSFACEConf.db_username, config_file.GetConfigName("db_username"), sizeof(gstGSFACEConf.db_username));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    ::strncpy(gstGSFACEConf.db_password, decrypt_db_passwd.c_str(), sizeof(gstGSFACEConf.db_password));
    ::strncpy(gstGSFACEConf.db_database, config_file.GetConfigName("db_database"), sizeof(gstGSFACEConf.db_database));
    const char* db_port = config_file.GetConfigName("db_port");
    gstGSFACEConf.db_port = ::atoi(db_port);

    const char* heartbeat = config_file.GetConfigName("heartbeat");
    gstGSFACEConf.heartbeat = ::atoi(heartbeat);

    if (gstGSFACEConf.heartbeat == 0)
    {
        gstGSFACEConf.heartbeat = 10;
    }
    //listen ip
    ::strncpy(gstGSFACEConf.listen_ip, config_file.GetConfigName("listen_ip"), sizeof(gstGSFACEConf.listen_ip));

    //FTP
    ::strncpy(gstGSFACEConf.ftp_server, config_file.GetConfigName("ftp_server"), sizeof(gstGSFACEConf.ftp_server));

    //存储地址
    ::strncpy(gstGSFACEConf.storage_path, config_file.GetConfigName("storage_path"), sizeof(gstGSFACEConf.storage_path));

    //图片下载地址
    ::strncpy(gstGSFACEConf.pic_download_path, config_file.GetConfigName("pic_download_path"), sizeof(gstGSFACEConf.pic_download_path));

    //xml地址
    ::strncpy(gstGSFACEConf.face_xml_path, config_file.GetConfigName("face_xml_path"), sizeof(gstGSFACEConf.face_xml_path));
    ::strncpy(gstGSFACEConf.face_dw_path, config_file.GetConfigName("face_dw_path"), sizeof(gstGSFACEConf.face_dw_path));

    //csgate
    ::strncpy(gstGSFACEConf.csgate_addr, config_file.GetConfigName("csgate_addr"), sizeof(gstGSFACEConf.csgate_addr));
}

/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        LOG_WARN << "DaoInit failed.";
        return -1;
    }
    conn_pool->Init(gstGSFACEConf.db_ip, gstGSFACEConf.db_username, gstGSFACEConf.db_password,
                    gstGSFACEConf.db_database, gstGSFACEConf.db_port, GS_MAX_RLDB_CONN, "cslocalgs");
    return 0;
}

void ApiHearbeat()
{
    GetHttpApiControlInstance()->Heartbeat(1);
}


int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        LOG_WARN << "another cslocalgs has been running in this system.";
        return -1;
    }
    glogInit(argv[0]);
    ConfInit();

    int nRet = DaoInit();
    if (0 != nRet)
    {
        LOG_WARN << "DaoInit failed";
        glogClean();
        return -1;
    }
    LOG_INFO << "start sever!";
    //起http服务线程
    std::thread httpThread(startHttpServer);

    //全部的准备工作全部做好之后,再起tcpserver
     evpp::EventLoop loop;
    loop.RunEvery(evpp::Duration(60.0 * gstGSFACEConf.heartbeat ), std::bind(&ApiHearbeat));
    loop.Run();

    httpThread.join();
    glogClean();
    return 0;
}

