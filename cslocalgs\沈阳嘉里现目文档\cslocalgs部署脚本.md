# CSLOCALGS部署说明  
## 1.数据库的安装部署  
### 1.1 安装mysql  
tar -zxvf akcs_mysql_local_packeg-r83620.tar.gz  
chmod -R 777 akcs_mysql_packeg  
cd akcs_mysql_packeg/scripts  
bash ./akcs_install_mysql_master.sh  

### 1.2 创建cslocalgs用户，密码为Ak@56@<EMAIL>  
mysql -uroot -pAk@xxxxxxx -h**************
create user 'cslocalgs'@'**************' identified by 'Ak@56@<EMAIL>';  
grant all privileges on cslocalgs.* to 'cslocalgs'@'**************';  
flush privileges;  
注意:**************要替换为部署服务器的ip

## 2.cslocal的安装部署  
tar -zxvf akcs_cslocalgs_packeg-v1.0.0-r84380.tar.gz  
chmod -R 777 akcs_cslocalgs_packeg  
cd akcs_cslocalgs_packeg/install/  
bash install.sh  
注：根据提示填写对应的网关地址，本次的填写国内云的网关地址。