<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
<style>
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
    margin: 0;
    padding: 0;
}
body {
    font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
    font-size: 13px;
    line-height: 18px;
    color: #737373;
    background-color: white;
    margin: 10px 13px 10px 13px;
}
table {
	margin: 10px 0 15px 0;
	border-collapse: collapse;
}
td,th {	
	border: 1px solid #ddd;
	padding: 3px 10px;
}
th {
	padding: 5px 10px;	
}

a {
    color: #0069d6;
}
a:hover {
    color: #0050a3;
    text-decoration: none;
}
a img {
    border: none;
}
p {
    margin-bottom: 9px;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    color: #404040;
    line-height: 36px;
}
h1 {
    margin-bottom: 18px;
    font-size: 30px;
}
h2 {
    font-size: 24px;
}
h3 {
    font-size: 18px;
}
h4 {
    font-size: 16px;
}
h5 {
    font-size: 14px;
}
h6 {
    font-size: 13px;
}
hr {
    margin: 0 0 19px;
    border: 0;
    border-bottom: 1px solid #ccc;
}
blockquote {
    padding: 13px 13px 21px 15px;
    margin-bottom: 18px;
    font-family:georgia,serif;
    font-style: italic;
}
blockquote:before {
    content:"\201C";
    font-size:40px;
    margin-left:-10px;
    font-family:georgia,serif;
    color:#eee;
}
blockquote p {
    font-size: 14px;
    font-weight: 300;
    line-height: 18px;
    margin-bottom: 0;
    font-style: italic;
}
code, pre {
    font-family: Monaco, Andale Mono, Courier New, monospace;
}
code {
    background-color: #fee9cc;
    color: rgba(0, 0, 0, 0.75);
    padding: 1px 3px;
    font-size: 12px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}
pre {
    display: block;
    padding: 14px;
    margin: 0 0 18px;
    line-height: 16px;
    font-size: 11px;
    border: 1px solid #d9d9d9;
    white-space: pre-wrap;
    word-wrap: break-word;
}
pre code {
    background-color: #fff;
    color:#737373;
    font-size: 11px;
    padding: 0;
}
sup {
    font-size: 0.83em;
    vertical-align: super;
    line-height: 0;
}
* {
	-webkit-print-color-adjust: exact;
}
@media screen and (min-width: 914px) {
    body {
        width: 854px;
        margin:10px auto;
    }
}
@media print {
	body,code,pre code,h1,h2,h3,h4,h5,h6 {
		color: black;
	}
	table, pre {
		page-break-inside: avoid;
	}
}
</style>
<title><!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width</title>

</head>
<body>
<p>&lt;!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"></p>



<p><title>版本更新历史</title></p>

<p></head>
<body></p>

<h1>版本更新历史</h1>




<ul>

<li>API版本：1.2.5</li>
<li>服务器端版本：1.5</li>
<li><p>本地主机版本：2.6</p>
<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>9.1</td>
<td>删除</td>
<td>接口废弃，建议使用 <a href='#11'>11. 视频流识别</td>
</tr>
<tr>
<td>11.1</td>
<td>修改</td>
<td>删除示例程序，请访问<a href='http://techsupport.megvii.com/hc/kb/section/105584/'>API相关及示例</td>
</tr>
</tbody>
</table>
</li>

<li>API版本：1.2.4</li>
<li>服务器端版本：1.5</li>
<li><p>本地主机版本：2.6</p>
<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>2.3</td>
<td>修改描述</td>
<td>gender 性别{0: 未知, 1: 男, 2: 女}</td>
</tr>
</tbody>
</table>
</li>

<li>API版本：1.2.3</li>
<li>服务器端版本：1.5</li>
<li><p>本地主机版本：2.6</p>

<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>11.1</td>
<td>修改描述</td>
<td></td>
</tr>
</tbody>
</table>
</li>
<li>API版本：1.2.2</li>
<li>服务器端版本：1.5</li>
<li><p>本地主机版本：2.6</p>

<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>9.1</td>
<td>修改，并标记为过时的(Deprecated)</td>
<td>socketio的transport只能用websocket类型，不可以用polling</td>
</tr>
</tbody>
</table>
</li>
<li>API版本：1.2.1</li>
<li>服务器端版本：1.5</li>
<li><p>本地主机版本：2.6</p>

<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>7.2</td>
<td>修改</td>
<td>增加分页参数</td>
</tr>
<tr>
<td>2.7</td>
<td>修改</td>
<td>返回值增加quality等</td>
</tr>
</tbody>
</table>
</li>
<li>API版本：1.2</li>
<li>服务器端版本：1.5</li>
<li><p>本地主机版本：2.6</p>

<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>10.1 1比N识别</td>
<td>修改</td>
<td>返回值里增加了"screen"数据</td>
</tr>
</tbody>
</table>
</li>
<li>API版本：1.1</li>
<li>服务器端版本：1.4.2</li>
<li><p>本地主机版本：2.5</p>

<table>
<thead>
<tr>
<th>内容</th>
<th>操作</th>
<th>描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>文档格式</td>
<td>整理</td>
<td></td>
</tr>
</tbody>
</table>
</li>
</ul>




<h1>目录</h1>




<table>
    <thead>
        <tr>
            <th colspan="4" style="background-color : #eee;">一、服务器端API</th>
        </tr>
         <tr style="background-color : #eee">
            <td>功能</td>
            <td>方法</td>
            <td>接口url</td>
            <td>接口作用</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><a href='#0'>0. 错误处理</a></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>            
        <tr>
            <td><a href='#1'>1. 登录</href></td>
            <td>POST</td>
            <td><a href='#1.1'>/auth/login</td>
            <td>1.1 登录</td>
        </tr>
        <tr>
            <td rowspan="7"><a href='#2'>2. 底库管理</td>
            <td>GET</td>
            <td><a href='#2.1'>/mobile-admin/subjects</td>
            <td>2.1 获取所有用户列表</td>
        </tr>
        <tr>
            <td>GET</td>
            <td><a href='#2.2'>/mobile-admin/subjects/list/</td>
            <td>2.2 获取用户列表（分页分类可搜索）</td>
        </tr>
        <tr>
            <td>POST</td>
            <td><a href='#2.3'>/subject</td>
            <td>2.3 创建用户</td>
        </tr>
        <tr>
            <td>GET</td>
            <td><a href='#2.4'>/subject/[id]</td>
            <td>2.4 获取用户信息</td>
        </tr>
        <tr>
            <td>PUT</td>
            <td><a href='#2.5'>/subject/[id]</td>
            <td>2.5 更新用户信息</td>
        </tr>
        <tr>
            <td>DELETE</td>
            <td><a href='#2.6'>/subject/[id]</td>
            <td>2.6 删除用户</td>
        </tr>
        <tr>        
            <td>POST</td>
            <td><a href='#2.7'>/subject/photo</td>
            <td>2.7上传识别照片</td>
        </tr>
        <tr>
            <td><a href='#3'>3. 用户头像</href></td>
            <td>POST</td>
            <td><a href='#3.1'>/subject/avatar</td>
            <td>3.1 上传显示头像</td>
        </tr>
        <tr>
            <td rowspan="6"><a href='#4'>4. 门禁管理</td>
            <td>GET</td>
            <td><a href='#4.1'>/system/screen</td>
            <td>4.1 获取门禁列表</td>
        </tr>
        <tr>
            <td>GET</td>
            <td><a href='#4.2'>/system/screen/[id]</td>
            <td>4.2 获取门禁信息</td>
        </tr>
        <tr>
            <td>GET</td>
            <td><a href='#4.3'>/system/boxes</td>
            <td>4.3 获取所有可用本地主机</td>
        </tr>
        <tr>
            <td>POST</td>
            <td><a href='#4.4'>/system/screen</td>
            <td>4.4 创建一个门禁</td>
        </tr>
        <tr>
            <td>PUT</td>
            <td><a href='#4.5'>/system/screen/[id]</td>
            <td>4.5 更新门禁信息</td>
        </tr>
        <tr>
            <td>DELETE</td>
            <td><a href='#4.6'>/system/screen/[id]</td>
            <td>4.6 删除门禁</td>
        </tr>
        <tr>
            <td><a href='#5'>5. 历史记录</td>
            <td>GET</td>
            <td><a href='#5.1'>/event/events</td>
            <td>5.1 历史识别记录</td>
        </tr>
        <tr>
            <td><a href='#6'>6. 考勤记录</td>
            <td>GET</td>
            <td><a href='#6.1'>/attendance/records</td>
            <td>6.1 考勤记录</td>
        </tr>
        <tr>
            <td rowspan="2"><a href='#7'>7. 年龄性别分析</td>
            <td>GET</td>
            <td><a href='#7.1'>/statistics/overview</td>
            <td>7.1 年龄性别统计</td>
        </tr>
        <tr>
            <td>GET</td>
            <td><a href='#7.2'>/statistics/event</td>
            <td>7.2 年龄性别记录</td>
        </tr>
        <tr>
            <td rowspan="3"><a href='#8'>8. Pad 接口</td>
            <td>POST</td>
            <td><a href='#8.1'>/pad/login</td>
            <td>8.1 登录</td>
        </tr>
        <tr>
            <td>POST</td>
            <td><a href='#8.2'>/pad/add-visitor</td>
            <td>8.2 添加访客</td>
        </tr>
        <tr>
            <td>PUT</td>
            <td><a href='#8.3'>/pad/set-info</td>
            <td>8.3 设置Pad信息</td>
        </tr>
        <tr>
            <td><a href='#9'>9. 识别推送<font color='red'>（Deprecated）</font></td>
            <td>socket.io</td>
            <td><a href='#9.1'>/event/</td>
            <td>9.1 识别消息推送（<font color='red'>Deprecated</font>）</td>
        </tr>
    </tbody>
    
    <thead>
        <tr>
            <th colspan="4" style="background-color : #eee;">二、本地主机端API</th>
        </tr>
         <tr style="background-color : #eee">
            <td>功能</td>
            <td>方法</td>
            <td>接口url</td>
            <td>接口作用</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td rowspan="2"><a href='#10'>10. 静态识别</td>
            <td>POST</td>
            <td><a href='#10.1'>/recognize</td>
            <td>10.1 1比N识别</td>
        </tr>
        <tr>
            <td>POST</td>
            <td><a href='#10.2'>/checkin</td>
            <td>10.2 1比1认证</td>
        </tr>
        <tr>
            <td><a href='#11'>11. 视频流识别</td>
            <td>websocket</td>
            <td><a href='#11.1'>ws://[本地主机ip]:9000</td>
            <td>11.1 获取识别结果（websocket接口）</td>
        </tr>
    </tbody>

    <thead>
        <tr>
            <th colspan="4" style="background-color : #eee;">附录</th>
        </tr>
        <tr>
            <th colspan="4"><a href='#ZH3'>三、模型定义</th>
        </tr>
        <tr>
            <th colspan="4"><a href='#ZH4'>四、错误码定义</th>
        </tr>
        <tr>
            <th colspan="4"><a href='#ZH5'>五、分页信息定义</th>
        </tr>
    </thead>
</table>




<table>

</table>




<table>

</table>




<ul>
<li><h4>标识符说明</h4>

<table>
<thead>
<tr>
<th align="left"> 概念</th>
<th align="left">解释</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">code</td>
<td align="left">0表示正常，其他参考<a href='#27'>错误码定义</td>
</tr>
<tr>
<td align="left">id</td>
<td align="left"> 数据库索引</td>
</tr>
<tr>
<td align="left">subject</td>
<td align="left">识别对象：员工，访客</td>
</tr>
<tr>
<td align="left">avatar</td>
<td align="left">subject显示的头像</td>
</tr>
<tr>
<td align="left">photo</td>
<td align="left"> subject上传的识别照片</td>
</tr>
<tr>
<td align="left">box</td>
<td align="left">主机盒子</td>
</tr>
<tr>
<td align="left">network_switch</td>
<td align="left">网络开关</td>
</tr>
<tr>
<td align="left">screen</td>
<td align="left">一路识别相机</td>
</tr>
</tbody>
</table>
</li>
<li><h4>注意</h4>

<p>服务器API地址为： https://v2.koalacam.net<br>
离线版API地址为： http://主机ip <br>
<code>此文档内接口除特别说明外，传入参数均为JSON格式（另外需要注意Content-Type需要为 application/json）,请自行转换格式</code></p></li>
</ul>




<p><br>
<br></p>




<h1>一、服务器端API</h1>




<hr />




<h1><span id="0"/> 0. 错误处理</h1>




<ul>
<li><h4>未登录</h4>

<p>如果没有调用登录接口，在调用别的接口时会返回HTTP 302重定向，用这个来判断是否登录</p></li>
<li><h4>没权限</h4>

<p>如果登录的账户没有调用某个接口的权限，在调用这个接口时会返回HTTP 302重定向</p></li>
</ul>




<hr />




<h1><span id="1"/> 1. 登录</h1>




<h2><span id="1.1"/> 1.1 登录</h2>




<ul>
<li><h4>URL:</h4>

<p>/auth/login</p></li>
<li><h4>Method:</h4>

<p>POST</p></li>
<li><h4>描述：</h4>

<p>登录并获取cookie，<code>后面所有的接口都需要在请求时带入此cookie</code></p></li>
<li><h4>参数：</h4>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> username </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 邮箱 </td>
</tr>
<tr>
<td align="left"> password </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 密码 </td>
</tr>
</tbody>
</table>


<p><code>此接口需要 设置user-agent为 "Koala Admin" , 否则不能登录成功</code></p></li>
<li><h4>返回值说明：</h4>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> avatar </td>
<td align="left"> str </td>
<td align="left"> 头像地址 </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
<tr>
<td align="left"> password_reseted </td>
<td align="left"> bool </td>
<td align="left"> 是否保存密码 </td>
</tr>
<tr>
<td align="left"> role_id </td>
<td align="left"> int </td>
<td align="left"> 账号类型, 1:root, 2:管理员, 3:普通用户</td>
</tr>
<tr>
<td align="left"> username </td>
<td align="left"> str </td>
<td align="left"> 用户名 </td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回JSON：</h4>

<pre><code>{
"code": 0,
"data": {
  "avatar": null,
  "company_id": 1,
  "id": 2,
  "password_reseted": true,
  "role_id": 2,
  "username": "<EMAIL>"
},
"page": {}
}
</code></pre></li>
</ul>




<hr />




<h1><span id="2"/> 2. 底库管理</h1>




<h2><span id="2.1"/> 2.1 获取所有用户列表</h2>




<ul>
<li><h4>URL:</h4>

<p>/mobile-admin/subjects</p></li>
<li><h4>Method:</h4>

<p>GET</p></li>
<li><h4>描述：</h4>

<p>获取所有底库用户列表，包括访客</p></li>
<li><h4>参数：</h4>

<p>无</p></li>
<li><h4>返回值说明：</h4>

<p>返回的data字段是subject对象列表，subject字段含义可以参考<a href="#subject_model">subject模型定义</a></p></li>
<li><h4>返回JSON：</h4>

<pre><code>{
"code": 0,
"data": [
  {
    "avatar": "",
    "come_from": "",
    "company_id": 1,
    "department": "",
    "description": "",
    "email": "<EMAIL>",
    "end_time": 0,
    "gender": 0,
    "id": 4,
    "interviewee": "",
    "name": "dfds",
    "password_reseted": false,
    "phone": "",
    "photo_ids": [
      4
    ],
    "photos": [
      {
        "company_id": 1,
        "id": 4,
        "subject_id": 4,
        "url": "/static/upload/photo/2015-10-13/3ee5d084439065548440749c334957e3fdaa0132.jpg"
      }
    ],
    "purpose": 0,
    "start_time": 0,
    "subject_type": 0,
    "title": ""
  }
],
、}    
}
</code></pre></li>
</ul>




<h2><span id="2.2"/> 2.2 获取用户列表(分类分页可搜索)</h2>




<ul>
<li><h4>URL:</h4>

<p>/mobile-admin/subjects/list</p></li>
<li><h4>Method:</h4>

<p>GET</p></li>
<li><h4>描述：</h4>

<p>按参数分类获取指定的用户</p></li>
<li><h4>参数：</h4>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 默认值 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">  category </td>
<td align="left"> string </td>
<td align="left"> 空 </td>
<td align="left"> √ </td>
<td align="left"> 'employee' - 员工; 'visitor' - 所有访客; 'vip' - VIP访客 </td>
</tr>
<tr>
<td align="left">  name     </td>
<td align="left"> string </td>
<td align="left"> 空 </td>
<td align="left">   </td>
<td align="left"> 要筛选的人的中文或拼音 </td>
</tr>
<tr>
<td align="left">  order    </td>
<td align="left"> string </td>
<td align="left"> 'time' </td>
<td align="left"> </td>
<td align="left"> 排序的方式: 'time' - 按照创建时间倒序排序; 'name' - 按照英文和拼音的字典序排序 </td>
</tr>
<tr>
<td align="left">  page     </td>
<td align="left"> int </td>
<td align="left"> 1 </td>
<td align="left">   </td>
<td align="left"> 第几页 </td>
</tr>
<tr>
<td align="left">  size     </td>
<td align="left"> int </td>
<td align="left"> 10 </td>
<td align="left">   </td>
<td align="left"> 每页多少条数据 </td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回值说明：</h4>

<p>返回的是subject对象数组，subject字段含义可以参考<a href="#subject_model">subject模型定义</a></p></li>
<li><h4>返回JSON：</h4>

<pre><code>{
"code": 0,
"data": [
  {
    "avatar": "",
    "come_from": "",
    "company_id": 1,
    "department": "",
    "description": "",
    "email": "<EMAIL>",
    "end_time": 0,
    "gender": 0,
    "id": 4,
    "interviewee": "",
    "name": "dfds",
    "password_reseted": false,
    "phone": "",
    "photo_ids": [
      4
    ],
    "photos": [
      {
        "company_id": 1,
        "id": 4,
        "subject_id": 4,
        "url": "/static/upload/photo/2015-10-13/3ee5d084439065548440749c334957e3fdaa0132.jpg"
      }
    ],
    "purpose": 0,
    "start_time": 0,
    "subject_type": 0,
    "title": ""
  }
],
"page": {
      "count": 757,
      "current": 1,
      "size": 10,
      "total": 76
      }
  }    
}
</code></pre></li>
</ul>




<h2><span id="2.3"/> 2.3 创建用户</h2>




<ul>
<li><h5>URL:</h5>

<p>/subject</p></li>
<li><h5>Method:</h5>

<p>POST</p></li>
<li><h5>描述：</h5>

<p>创建一个新用户</p></li>
<li><h5><span id="2.3para">参数:</span>（<code>如果subject_type不等于0，必须要指定start_time，end_time</code>)</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 默认值 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">  start_time </td>
<td align="left"> int </td>
<td align="left"> 0 </td>
<td align="left">  </td>
<td align="left"> 来访时间 时间戳(单位秒)</td>
</tr>
<tr>
<td align="left"> end_time </td>
<td align="left"> int </td>
<td align="left"> 0 </td>
<td align="left">  </td>
<td align="left"> 离开时间 时间戳(单位秒)</td>
</tr>
<tr>
<td align="left"> purpose </td>
<td align="left"> int </td>
<td align="left"> 0 </td>
<td align="left">  </td>
<td align="left"> 来访目的</td>
</tr>
<tr>
<td align="left"> birthday </td>
<td align="left"> int </td>
<td align="left"> 0 </td>
<td align="left">  </td>
<td align="left"> 生日</td>
</tr>
<tr>
<td align="left"> entry_date </td>
<td align="left"> int </td>
<td align="left"> 0 </td>
<td align="left">  </td>
<td align="left"> 入职时间</td>
</tr>
<tr>
<td align="left"> photo_ids </td>
<td align="left"> array </td>
<td align="left"> [] </td>
<td align="left">  </td>
<td align="left"> 识别头像列表（<a href='#2.7'>通过接口2.7上传识别头像</a>）</td>
</tr>
<tr>
<td align="left"> gender </td>
<td align="left"> int </td>
<td align="left"> 男 </td>
<td align="left"> </td>
<td align="left">  性别{0: 未知, 1: 男, 2: 女} </td>
</tr>
<tr>
<td align="left"> email </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 邮箱 </td>
</tr>
<tr>
<td align="left"> phone </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 手机 </td>
</tr>
<tr>
<td align="left"> avatar </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 头像（ <a href='#3.1'>通过接口3.1上传头像）</a></td>
</tr>
<tr>
<td align="left"> department </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 部门 </td>
</tr>
<tr>
<td align="left"> title </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 职位 </td>
</tr>
<tr>
<td align="left"> description </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 签名 </td>
</tr>
<tr>
<td align="left"> interviewee </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 受访人 </td>
</tr>
<tr>
<td align="left"> come_from </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 来访单位 </td>
</tr>
<tr>
<td align="left"> job_number </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 工号 </td>
</tr>
<tr>
<td align="left"> remark </td>
<td align="left"> str </td>
<td align="left"> 空 </td>
<td align="left">  </td>
<td align="left"> 备注 </td>
</tr>
<tr>
<td align="left"> subject_type </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> √ </td>
<td align="left"> 用户类型 {0:员工, 1:访客, 2: VIP访客} </td>
</tr>
<tr>
<td align="left"> name </td>
<td align="left"> str </td>
<td align="left">  </td>
<td align="left"> √ </td>
<td align="left"> 姓名 </td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回值说明：</h4>

<p>创建的subject信息</p></li>
<li><h4>返回JSON：</h4>

<p><a href='#2.4json'>同2.4. 获取用户信息接口</a></p></li>
</ul>




<h2><span id="2.4"/> 2.4 获取用户信息</h2>




<ul>
<li><h4>URL:</h4>

<p>/subject/[id]</p></li>
<li><h4>Method:</h4>

<p>GET</p></li>
<li><h4>描述：</h4>

<p>获取单个subject信息</p></li>
<li><h4>参数：</h4>

<p>无</p></li>
<li><h4><span id="2.4return">返回值说明：</span></h4>

<p>返回的是data字段是一个subject对象，subject字段含义可以参考<a href="#subject_model">subject模型定义</a></p></li>
<li><h4><span id="2.4json">返回JSON：</span></h4>

<pre><code>{
"code": 0,
"data": {
  "avatar": "",
  "birthday": null,
  "come_from": "",
  "company_id": 2,
  "department": "\u6280\u672f\u90e8",
  "description": "\u68a6\u60f3\uff0c\u5c31\u662f\u8fd9\u4e48\u4efb\u6027\uff01",
  "email": "<EMAIL>",
  "end_time": null,
  "entry_date": null,
  "gender": 1,
  "id": 19633,
  "interviewee": "",
  "job_number": "104",
  "name": "\u8d75\u516d",
  "password_reseted": false,
  "phone": "13788890284",
  "photos": [],
  "purpose": 0,
  "remark": "\u6211\u662f\u540e\u7aef",
  "start_time": null,
  "subject_type": 0,
  "title": "\u5de5\u7a0b\u5e08",
  "visit_notify": false
},
}
</code></pre></li>
</ul>




<h2><span id="2.5"/> 2.5 更新用户信息</h2>




<ul>
<li><h4>URL:</h4>

<p>/subject/[id]</p></li>
<li><h4>Method:</h4>

<p>PUT</p></li>
<li><h4>描述：</h4>

<p>对用户的部分字段进行更新</p></li>
<li><h4>参数：</h4>

<p><a href='#2.3para'>同2.3. 创建用户</a></p></li>
<li><h4>返回值说明：</h4>

<p>修改后的的subject信息</p></li>
<li><h4>返回JSON：</h4>

<p><a href='#2.4json'>同2.4 获取用户信息接口</a></p></li>
</ul>




<h2><span id="2.6"/> 2.6 删除用户</h2>




<ul>
<li><h4>URL:</h4>

<p>/subject/[id]</p></li>
<li><h4>Method:</h4>

<p>DELETE</p></li>
<li><h4>描述：</h4>

<p>删除用户</p></li>
<li><h4>参数：</h4>

<p>无</p></li>
<li><h4>返回值说明：</h4>

<p>无</p></li>
<li><h4>返回JSON：</h4>

<p>无</p></li>
</ul>




<h2><span id="2.7"/> 2.7 上传识别底库</h2>




<ul>
<li><h4>URL:</h4>

<p>/subject/photo</p></li>
<li><h4>Method:</h4>

<p>POST</p></li>
<li><h4>描述：</h4>

<p>个人识别照片上传</p></li>
<li><h4>参数：</h4>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> photo </td>
<td align="left"> file </td>
<td align="left"> √ </td>
<td align="left"> 识别照片 </td>
</tr>
<tr>
<td align="left"> subject_id </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 若传入此参数，则在图片上传成功的同时更新用户的识别照片。否则需要手动将返回的id (返回值中的563) 放入<a href='#2.3'>接口2.3</a>，<a href='#2.5'>接口2.5</a>中的photo_ids参数里，实现更新此用户的识别头像 </td>
</tr>
</tbody>
</table>


<p><code>注意：此接口格式为 multipart/form-data 格式</code></p></li>
<li><h4>返回值：</h4>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> id </td>
<td align="left"> int </td>
<td align="left"> 图片的id </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
<tr>
<td align="left"> subject_id </td>
<td align="left"> int </td>
<td align="left"> 数据库索引，如果为null表示照片没有绑定subject </td>
</tr>
<tr>
<td align="left"> url </td>
<td align="left"> str </td>
<td align="left"> 识别照片地址 </td>
</tr>
<tr>
<td align="left"> quality </td>
<td align="left"> float </td>
<td align="left"> 人脸质量 </td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回JSON：</h4>

<pre><code>{
  "code": 0,
  "data": {
      "company_id": 2,
      "id": 563,
      "subject_id": null,
      "url": "/static/upload/photo/2015-10-22/78d86cadaad1e1a8f43c87ae3adeafb560e96ee3.jpg"
  },
  "page": {}
}
</code></pre></li>
</ul>




<hr />




<h1><span id="3"/> 3. 用户头像</h1>




<h2><span id="3.1"/> 3.1 上传头像</h2>




<ul>
<li><h4>URL:</h4>

<p>/subject/avatar</p></li>
<li><h4>Method:</h4>

<p>POST</p></li>
<li><h4>描述：</h4>

<p>上传个人头像</p></li>
<li><h4>参数：</h4>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> avatar </td>
<td align="left"> file </td>
<td align="left"> √ </td>
<td align="left"> 识别照片 </td>
</tr>
<tr>
<td align="left"> subject_id </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 若传入此参数，则在图片上传成功的同时更新subject_id用户头像，否则需要手动将返回的 url, 设置为<a href='#2.3'>接口2.3</a>，<a href='#2.5'>接口2.5</a>中的avatar参数里，实现更新此用户的识别头像 </td>
</tr>
</tbody>
</table>


<p><code>注意：此接口格式为 multipart/form-data 格式</code></p></li>
<li><h4>返回值：</h4>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> url </td>
<td align="left"> str </td>
<td align="left"> 识别头像地址 </td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回JSON：</h4>

<pre><code>{
    "code": 0,
    "data": {
        "url": "/static/upload/photo/2015-10-22/78d86cadaad1e1a8f43c87ae3adeafb560e96ee3.jpg"
    },
    "page": {}
}
</code></pre></li>
</ul>




<hr />




<h1><span id="4"/> 4. 门禁管理</h1>




<h2><span id="4.1"/> 4.1 获取门禁列表</h2>




<ul>
<li><h4>URL:</h4>

<p>/system/screen</p></li>
<li><h4>Method:</h4>

<p>GET</p></li>
<li><h4>描述：</h4>

<p>获取门禁列表</p></li>
<li><h4>参数：</h4>

<p>无</p></li>
<li><h4>返回值：</h4>

<p>返回的data字段是门禁列表，门禁字段含义参考<a href="#screen_model">screen模型定义</a></p></li>
<li><h4>返回JSON：</h4>

<pre><code>"data": [
  {
    "allow_all_subjects": true,
    "allow_visitor": true,
    "allowed_subject_ids": [],
    "box_address": null,
    "box_heartbeat": null,
    "box_status": "1",
    "box_token": null,
    "camera_address": null,
    "camera_name": null,
    "camera_position": "neo test",
    "camera_status": null,
    "description": null,
    "id": 102,
    "network_switcher": "http:",
    "network_switcher_status": "2",
    "network_switcher_token": "2wee",
    "screen_token": "Guard_10:2a:b3:4f:d7:f3",
    "server_time": 1467883753.050689,
    "type": 2
  },
</code></pre></li>
</ul>




<h2><span id="4.2"/> 4.2 获取单个门禁设置</h2>




<ul>
<li><h4>URL:</h4>

<p>/system/screen/[id]</p></li>
<li><h4>Method:</h4>

<p>GET</p></li>
<li><h4>描述：</h4>

<p>获取单个门禁设置</p></li>
<li><h4>参数：</h4>

<p>无</p></li>
<li><h4><span id='4.2return'>返回值：</span></h4>

<p>门禁信息，字段含义可以参考<a href="#screen_model">screen模型定义</a></p></li>
<li><h4><span id='4.2json'>返回JSON：</span></h4>

<pre><code>{
"code": 0,
"data": {
  "allow_all_subjects": true,
  "allow_visitor": true,
  "box_address": null,
  "box_heartbeat": null,
  "box_status": "1",
  "box_token": null,
  "camera_address": null,
  "camera_name": null,
  "camera_position": "neo test",
  "camera_status": null,
  "description": null,
  "id": 102,
  "network_switcher": "http:",
  "network_switcher_status": "2",
  "network_switcher_token": "2wee",
  "screen_token": "Guard_10:2a:b3:4f:d7:f3",
  "type": 2
},
"page": {}
}
</code></pre></li>
</ul>




<h2><span id="4.3"/> 4.3 获取所有可用本地主机</h2>




<ul>
<li><h5>URL:</h5>

<p>/system/boxes</p></li>
<li><h5>Method:</h5>

<p>GET</p></li>
<li><h5>描述：</h5>

<p>获取所有可用本地主机</p></li>
<li><h5>参数：</h5>

<p>无</p></li>
<li><h5>返回值：</h5>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> all_screens </td>
<td align="left"> <a href="#screen_model">screen</a>列表 </td>
<td align="left">  </td>
</tr>
<tr>
<td align="left"> box_token </td>
<td align="left"> str </td>
<td align="left">  主机Token </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
<tr>
<td align="left"> current_version </td>
<td align="left"> string </td>
<td align="left"> 当前主机使用的配置版本 </td>
</tr>
<tr>
<td align="left"> heartbeat </td>
<td align="left"> int </td>
<td align="left"> 收到主机心跳消息的时间戳 </td>
</tr>
<tr>
<td align="left">status</td>
<td align="left">str</td>
<td align="left">值为‘1’表示长时间没有收到heartbeat, ‘0’正常</td>
</tr>
<tr>
<td align="left">leaf_config</td>
<td align="left">map</td>
<td align="left">识别算法相关配置</td>
</tr>
<tr>
<td align="left">dog_expiration</td>
<td align="left">int</td>
<td align="left">加密狗到期时间</td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回JSON：</h5>

<pre><code>{
    "all_screens": [
    {
        "allow_all_subjects": true,
        "allow_visitor": true,
        "box_address": "***********",
        "box_heartbeat": 1467884088,
        "box_status": "0",
        "box_token": "c4589b6d-29a6-4e26-a91e-8e317a193104",
        "camera_address": "rtsp://*************/live1.sdp1",
        "camera_name": "",
        "camera_position": "\u95e8\u53e3",
        "camera_status": "0",
        "description": null,
        "id": 161,
        "network_switcher": "",
        "network_switcher_status": null,
        "network_switcher_token": null,
        "screen_token": "9c8e8bc4-7371-4a4d-9fd7-9e89cade245d",
        "type": 1
      }
    ],
    "box_address": "127.0.0.1",
    "box_token": "duyufei-test",
    "company_id": 2,
    "current_version": null,
    "dog_expiration": "",
    "heartbeat": 0,
    "id": 8,
    "leaf_config": {
      "-quality": 0.75,
      "-threshold": 78,
      "-unthreshold": 62,
      "-video.facemin": 50
    },
    "model": 3,
    "status": "1"
  }
</code></pre></li>
</ul>




<h2><span id="4.4"/> 4.4 创建一个门禁（一路识别相机）</h2>




<ul>
<li><h5>URL:</h5>

<p>/system/screen</p></li>
<li><h5>Method:</h5>

<p>POST</p></li>
<li><h5>描述：</h5>

<p>创建一个门禁（一路识别相机）</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> box_id </td>
<td align="left"> int </td>
<td align="left"> √ </td>
<td align="left"> 主机ID，通过/system/boxes接口获得，表明需要用哪台主机识别 </td>
</tr>
<tr>
<td align="left"> network_switcher </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 网络开关IP </td>
</tr>
<tr>
<td align="left"> description </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 描述 </td>
</tr>
<tr>
<td align="left"> camera_address </td>
<td align="left"> string </td>
<td align="left"> √ </td>
<td align="left"> 网络开关IP </td>
</tr>
<tr>
<td align="left"> camera_position </td>
<td align="left"> string </td>
<td align="left"> √ </td>
<td align="left"> 相机视频流地址 </td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回值：</h5>

<p>无</p></li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": {},
"page": {}
}
</code></pre></li>
</ul>




<h2><span id="4.5"/> 4.5 修改单个门禁设置</h2>




<ul>
<li><h5>URL:</h5>

<p>/system/screen/[id]</p></li>
<li><h5>Method:</h5>

<p>PUT</p></li>
<li><h5>描述：</h5>

<p>修改单个门禁设置</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> camera_name </td>
<td align="left"> str </td>
<td align="left">  </td>
<td align="left"> 名称</td>
</tr>
<tr>
<td align="left"> camera_address </td>
<td align="left"> str  </td>
<td align="left">  </td>
<td align="left"> 相机视频流地址</td>
</tr>
<tr>
<td align="left"> camera_position </td>
<td align="left"> str  </td>
<td align="left">  </td>
<td align="left"> 相机位置</td>
</tr>
<tr>
<td align="left"> network_switcher </td>
<td align="left"> str </td>
<td align="left">  </td>
<td align="left"> 网络开关IP</td>
</tr>
<tr>
<td align="left"> allow_all_subjects </td>
<td align="left">  bool </td>
<td align="left">  </td>
<td align="left"> 是否允许所有人进入</td>
</tr>
<tr>
<td align="left"> allow_visitor </td>
<td align="left"> bool </td>
<td align="left">  </td>
<td align="left"> 是否允许访客进入</td>
</tr>
<tr>
<td align="left"> allowed_subject_ids </td>
<td align="left"> array of int </td>
<td align="left">  </td>
<td align="left"> 可进入人员ID列表</td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回值：</h5>

<p><a href='#4.2return'>同接口4.2</a></p></li>
<li><h5>返回JSON：</h5>

<p><a href='#4.2json'>同接口4.2</a></p></li>
</ul>




<h2><span id="4.6"/> 4.6 删除单个门禁设置</h2>




<ul>
<li><h5>URL:</h5>

<p>/system/screen/[id]</p></li>
<li><h5>Method:</h5>

<p>DELETE</p></li>
<li><h5>描述：</h5>

<p>删除单个门禁设置</p></li>
<li><h5>参数：</h5>

<p>无</p></li>
<li><h5>返回值：</h5>

<p>无</p></li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": {},
"page": {}
}
</code></pre></li>
</ul>




<hr />




<h1><span id="5"/> 5. 历史记录</h1>




<h2><span id="5.1"/> 5.1 历史识别记录</h2>




<ul>
<li><h5>URL:</h5>

<p>/event/events</p></li>
<li><h5>Method:</h5>

<p>GET</p></li>
<li><h5>描述：</h5>

<p>历史识别记录</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> start </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 开始时间时间戳(单位秒)</td>
</tr>
<tr>
<td align="left"> end </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 开始时间时间戳(单位秒)</td>
</tr>
<tr>
<td align="left"> user_role </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 角色 {不传: 所有, 0: 员工(默认)， 1: 访客, 2: VIP} </td>
</tr>
<tr>
<td align="left"> user_name </td>
<td align="left"> str </td>
<td align="left">  </td>
<td align="left"> 按用户名模糊查询 </td>
</tr>
<tr>
<td align="left"> screen_id </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 按门禁ID查询 </td>
</tr>
<tr>
<td align="left"> subject_id </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 按用户ID精确查询 </td>
</tr>
<tr>
<td align="left">page   </td>
<td align="left">int</td>
<td align="left"></td>
<td align="left"> 第几页 默认 1</td>
</tr>
<tr>
<td align="left">size</td>
<td align="left">  int</td>
<td align="left"> </td>
<td align="left">每页数量 默认 10</td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回值：</h5>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> age </td>
<td align="left"> float </td>
<td align="left"> 年龄 </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
<tr>
<td align="left"> confidence </td>
<td align="left"> float </td>
<td align="left"> 相似度 </td>
</tr>
<tr>
<td align="left"> gender </td>
<td align="left"> float </td>
<td align="left"> 性别为男的概率 </td>
</tr>
<tr>
<td align="left"> photo </td>
<td align="left"> string </td>
<td align="left"> 抓拍照片url </td>
</tr>
<tr>
<td align="left"> quality </td>
<td align="left"> float </td>
<td align="left"> 照片质量</td>
</tr>
<tr>
<td align="left"> screen</td>
<td align="left"> <a href="#screen_model">screen模型</a> </td>
<td align="left"> 抓拍相机</td>
</tr>
<tr>
<td align="left"> subject </td>
<td align="left"> <a href="#subject_model">subject模型</a> </td>
<td align="left"> 抓拍的subject对象 </td>
</tr>
<tr>
<td align="left"> timestamp </td>
<td align="left"> int </td>
<td align="left"> 抓拍时间戳 </td>
</tr>
<tr>
<td align="left">page</td>
<td align="left"></td>
<td align="left">参考<a href='#ZH5'>分页信息定义</td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回JSON：</h5>

<pre><code>{
  "code": 0,
  "data": [
      {
          "age": 35.7292,
          "company_id": 1,
          "confidence": 87.4096,
          "gender": 0.999992,
          "group": 80,
          "id": 2581617,
          "photo": "https://o7rv4xhdy.qnssl.com/@/static/upload/event/2016-05-31/452cb86b9e81add249451837637ab020482229ab.jpg",   //抓拍照
          "quality": 0.75279,
          "screen": {   // 参考附录门禁模型定义
              "allow_all_subjects": true,
              "allow_visitor": true,
              "box_address": "**************",
              "box_heartbeat": 1464683851,
              "box_status": "0",
              "box_token": "5c31ba4b-f9c5-4b3f-bb21-fd632b73b690",
              "camera_address": "rtsp://*************/live1.sdp",  //抓拍的相机地址
              "camera_name": "",
              "camera_position": "tss\u5de5\u4f4d\u6d4b\u8bd5",  //抓拍的位置
              "camera_status": "0",
              "description": null,
              "id": 706,
              "network_switcher": "",
              "network_switcher_status": null,
              "network_switcher_token": null,
              "screen_token": "55e6fb9d-5153-43e2-9829-1dba83e6810f",
              "type": 1                                        //抓拍设备类型 1表示相机  2表示门禁PAD
          },
          "subject": {   // 抓拍的人信息，详细请参考附录的 模型定义
              "avatar": "https://o7rv4xhdy.qnssl.com/@/static/upload/avatar/2015-12-02/ed49064ca0eb5853f98b1c73a7a69cc1306c051a.jpg",
              "birthday": null,
              "come_from": "",
              "company_id": 1,
              "department": "Develop-PD",       //部门
              "description": "easy come\uff0ceasy go\uff01",          //签名
              "email": "<EMAIL>",
              "end_time": 0,
              "entry_date": null,
              "gender": 0,
              "id": 7021,
              "interviewee": "",
              "job_number": "135",           // 工号
              "name": "\u89e3\u8a00\u658c",  //姓名
              "password_reseted": true,
              "phone": "15652936120",
              "photos": [     //底库头像列表
                  {
                      "company_id": 1,
                      "id": 16491,
                      "quality": 0.940321,
                      "subject_id": 7021,
                      "url": "https://o7rv4xhdy.qnssl.com/@/static/upload/photo/2015-11-12/bb1758c460abf63ec00bee908cf8e187f87b6547.jpg",
                      "version": 2
                  }
              ],
              "purpose": 0,
              "remark": "",
              "start_time": 0,
              "subject_type": 0,              // 人员类型
              "title": "",
              "visit_notify": null
          },
          "subject_id": 7021,               //人员ID
          "timestamp": 1464683891
      },
      ...
  ],
  "page": {
      "count": 251967,            // 总记录数
      "current": 1,               // 当前页数
      "size": 10,                 // 每页size
      "total": 25197              // 总页数
  }
}
</code></pre></li>
</ul>




<hr />




<h1><span id="6"/> 6. 考勤记录</h1>




<h2><span id="6.1"/> 6.1 考勤记录</h2>




<ul>
<li><h5>URL:</h5>

<p>/attendance/records</p></li>
<li><h5>Method:</h5>

<p>GET</p></li>
<li><h5>描述：</h5>

<p>考勤记录</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 默认值 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> start_time </td>
<td align="left"> int </td>
<td align="left"> 空（表示不限） </td>
<td align="left">  </td>
<td align="left"> 开始日期（含）时间戳 （精确到天，须取该天0点的时间戳，比如 2016-01-01，为 1451577600）</td>
</tr>
<tr>
<td align="left"> end_time </td>
<td align="left"> int </td>
<td align="left"> 空（表示不限） </td>
<td align="left">  </td>
<td align="left">结束日期（含）时间戳</td>
</tr>
<tr>
<td align="left"> user_name </td>
<td align="left"> str </td>
<td align="left"> 空（表示不限） </td>
<td align="left">  </td>
<td align="left"> 按用户名模糊查询</td>
</tr>
<tr>
<td align="left"> department </td>
<td align="left"> str </td>
<td align="left"> 空（表示不限） </td>
<td align="left">  </td>
<td align="left"> 按部门模糊查询</td>
</tr>
<tr>
<td align="left"> subject_id </td>
<td align="left"> int </td>
<td align="left"> 空（表示不限） </td>
<td align="left">  </td>
<td align="left"> 按用户ID精确查询</td>
</tr>
</tbody>
</table>


<p>需要特别注意的是，考勤的分割时间点是每天的6:00, 所以如果选择 2016-01-01 至 2016-01-03 的考勤记录，考勤记录的 check_out_time 可能会出现 2016-01-04夜里 的时间。</p></li>
<li><h5>返回值：</h5>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> check_in_time </td>
<td align="left"> int </td>
<td align="left"> 最早签到记录 </td>
</tr>
<tr>
<td align="left"> check_out_time </td>
<td align="left"> int </td>
<td align="left"> 最晚签到记录 </td>
</tr>
<tr>
<td align="left"> clock_in </td>
<td align="left"> int </td>
<td align="left"> 签到状态 </td>
</tr>
<tr>
<td align="left"> clock_out </td>
<td align="left"> int </td>
<td align="left"> 签退状态 </td>
</tr>
<tr>
<td align="left"> date </td>
<td align="left"> int </td>
<td align="left"> 日期的时间戳 </td>
</tr>
<tr>
<td align="left"> id </td>
<td align="left"> int </td>
<td align="left"> 索引ID </td>
</tr>
<tr>
<td align="left"> subject </td>
<td align="left"> <a href="#subject_model">subject模型</a> </td>
<td align="left"> subject对象</td>
</tr>
<tr>
<td align="left"> worktime</td>
<td align="left"> str </td>
<td align="left"> 工作时长 </td>
</tr>
<tr>
<td align="left">page</td>
<td align="left"> 参考<a href='#ZH5'>分页信息定义</a></td>
<td></td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回JSON：</h5>

<pre><code>{
  "code": 0,
  "data": [
      {
          "check_in_time": 1465178327, //最早签到记录
          "check_out_time": 1465215678, //最晚签到记录
          "clock_in": 1,
          "clock_out": 1,
          "date": 1465142400,       //日期0点的时间戳
          "id": 2260067,
          "subject": {
              "avatar": "https://o7rv4xhdy.qnssl.com/@/static/upload/photo/2016-06-03/21a32f098a064fb088f3ef16c6fb80830abc1d79.jpg",
              "birthday": null,
              "department": "",
              "description": "",
              "end_time": 0,
              "entry_date": null,
              "id": 49774,
              "job_number": "",    //工号
              "name": "\u90ed\u7389\u9999", // 姓名
              "remark": "",
              "start_time": 0,
              "subject_type": 0,
              "title": ""
          },
          "worktime": "10\u5c0f\u65f622\u5206"
      }
  ],
  "page": {
      "count": 15745,      //总记录数
      "current": 1,        // 当前第几页
      "size": 1,           // 每页数量
      "total": 15745       // 总页数
  }
}
</code></pre></li>
</ul>




<hr />




<h1><span id="7"/> 7. 年龄性别分析</h1>




<h2><span id="7.1"/> 7.1 年龄性别统计</h2>




<ul>
<li><h5>URL:</h5>

<p>/statistics/overview</p></li>
<li><h5>Method:</h5>

<p>GET</p></li>
<li><h5>描述：</h5>

<p>查询登录公司某段时间内的统计数据，包括不同性别及年龄段的人数</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> start_time </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 查询时间段的开始时间 </td>
</tr>
<tr>
<td align="left"> end_time </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 查询时间段的结束时间 </td>
</tr>
</tbody>
</table>


<p>参数说明：时间参数的格式为："2015-11-26 11:53:00"</p></li>
<li><h5>返回值：</h5>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> ages </td>
<td align="left"> array </td>
<td align="left"> 长度为100，每一项表示1~100岁各年龄的[男，女]人数 </td>
</tr>
<tr>
<td align="left"> gender </td>
<td align="left"> map </td>
<td align="left"> 男、女总人数 </td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": {
  "ages": [
    [0, 0],
    [1, 2],
    ...
  ],
  "gender": {
    "female": 0,
    "male": 0
  }
},
"page": {}
}
</code></pre></li>
</ul>




<h2><span id="7.2"/> 7.2 年龄性别记录</h2>




<ul>
<li><h5>URL:</h5>

<p>/statistics/event</p></li>
<li><h5>Method:</h5>

<p>GET</p></li>
<li><h5>描述：</h5>

<p>查询某段时间内的所有具体识别记录</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> start_time </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 查询时间段的开始时间 </td>
</tr>
<tr>
<td align="left"> end_time </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 查询时间段的结束时间 </td>
</tr>
<tr>
<td align="left"> page </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 第几页（默认第1页） </td>
</tr>
<tr>
<td align="left"> size </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 一页多少条数据（默认20条）</td>
</tr>
<tr>
<td align="left"> unique </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 查询是否排重，0表示不排除，1表示排重，默认值为0 </td>
</tr>
<tr>
<td align="left"> position </td>
<td align="left"> str </td>
<td align="left">  </td>
<td align="left"> screen的位置名称（<a href="#screen_model">screen模型</a>中的camera_position），如果传了这个参数，就只返回此位置的数据，如果不传这个参数，返回所有位置的数据 </td>
</tr>
</tbody>
</table>


<p>参数说明：时间参数的格式为："2015-11-26 11:53:00"</p></li>
<li><h5>返回值：</h5>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> age </td>
<td align="left"> float </td>
<td align="left"> 年龄 </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
<tr>
<td align="left"> gender </td>
<td align="left"> float </td>
<td align="left"> 男性的概率 </td>
</tr>
<tr>
<td align="left"> photo </td>
<td align="left"> str </td>
<td align="left"> 抓拍照片 </td>
</tr>
<tr>
<td align="left"> subject_id </td>
<td align="left"> int </td>
<td align="left"> subject ID </td>
</tr>
<tr>
<td align="left"> timestamp </td>
<td align="left"> int </td>
<td align="left"> 发生时间 </td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": [
  {
    "age": 24.1945,
    "company_id": 1,
    "gender": 1.0,
    "id": 2150,
    "photo": "/static/event/screen/2015-11-20/****************************************.jpg",
    "subject_id": null,
    "timestamp": 1448019496
  },
  ...
],
"page": {
  "count": 563,
  "current": 1,
  "size": 20,
  "total": 29
}
}
</code></pre></li>
</ul>




<hr />




<h1><span id="8"/> 8. Pad 接口</h1>




<h2><span id="8.1"/> 8.1 Pad 登录</h2>




<ul>
<li><h5>URL:</h5>

<p>/pad/login</p></li>
<li><h5>Method:</h5>

<p>POST</p></li>
<li><h5>描述：</h5>

<p>此接口作用是注册一个门禁设备，并得到screen_token， 用做 后面主机上 1:1 和 1:N接口的参数</p></li>
<li><h5>参数：</h5>

<table
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> username </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 登录邮箱 </td>
</tr>
<tr>
<td align="left"> password </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 登录密码 </td>
</tr>
<tr>
<td align="left"> pad_id </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> Pad 自己生成的唯一 ID </td>
</tr>
<tr>
<td align="left"> device_type </td>
<td align="left"> str </td>
<td align="left"> √ </td>
<td align="left"> 固定值：2 </td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回值：</h5>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> avatar </td>
<td align="left"> str </td>
<td align="left"> 头像地址 </td>
</tr>
<tr>
<td align="left"> boxes </td>
<td align="left"> list </td>
<td align="left"> 主机列表 </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
<tr>
<td align="left"> password_reseted </td>
<td align="left"> bool </td>
<td align="left"> 是否保存密码 </td>
</tr>
<tr>
<td align="left"> role_id </td>
<td align="left"> int </td>
<td align="left"> 账号类型 </td>
</tr>
<tr>
<td align="left"> screen_token </td>
<td align="left"> str </td>
<td align="left"> 一路识别相机token</td>
</tr>
<tr>
<td align="left"> username </td>
<td align="left"> str </td>
<td align="left"> 用户名 </td>
</tr>
</tbody>
</table>


<ul>
<li><h6>boxes</h6>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> box_address </td>
<td align="left"> str </td>
<td align="left"> 主机地址 </td>
</tr>
<tr>
<td align="left"> box_token </td>
<td align="left"> str </td>
<td align="left"> 主机Token </td>
</tr>
<tr>
<td align="left"> company_id </td>
<td align="left"> int </td>
<td align="left"> 公司ID </td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": {
  "avatar": null,
  "boxes": [
    {
      "box_address": "************",
      "box_token": "token",
      "company_id": 1,
      "id": 1
    }
  ],
  "company_id": 1,
  "id": 2,
  "password_reseted": false,
  "role_id": 2,
  "screen_token": "29HSPI3412PwrZUM",
  "username": "a"
},
"page": {}
}
</code></pre></li>
</ul>




<h2><span id="8.2"/> 8.2 添加访客</h2>




<ul>
<li><h5>URL:</h5>

<p>/pad/add-visitor</p></li>
<li><h5>Method:</h5>

<p>POST</p></li>
<li><h5>描述：</h5>

<p>为当前登录公司添加访客</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> come_from </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 来访单位 </td>
</tr>
<tr>
<td align="left"> description </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 识别时的显示签名 </td>
</tr>
<tr>
<td align="left"> end_time </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 访问结束时间的时间戳，默认为当前时间后2小时 </td>
</tr>
<tr>
<td align="left"> interviewee </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 受访者姓名 </td>
</tr>
<tr>
<td align="left"> name </td>
<td align="left"> string </td>
<td align="left"> √ </td>
<td align="left"> 访客姓名 </td>
</tr>
<tr>
<td align="left"> photo </td>
<td align="left"> file </td>
<td align="left">  </td>
<td align="left"> 用于识别的底库照片 </td>
</tr>
<tr>
<td align="left"> purpose </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 来访目的，含义见  附录：模型定义 </td>
</tr>
<tr>
<td align="left"> remark </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 备注 </td>
</tr>
<tr>
<td align="left"> start_time </td>
<td align="left"> int </td>
<td align="left">  </td>
<td align="left"> 访问开始时间的时间戳，默认为当前时间前 5 分钟 </td>
</tr>
<tr>
<td align="left"> vip </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 是否为 VIP 访客，默认为否 </td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回值：</h5>

<p>无</p></li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": {},
"page": {}
}
</code></pre></li>
</ul>




<h2><span id="8.3"/> 8.3 设置Pad信息</h2>




<ul>
<li><h5>URL:</h5>

<p>/pad/set-info</p></li>
<li><h5>Method:</h5>

<p>PUT</p></li>
<li><h5>描述：</h5>

<p>为当前登录公司添加访客</p></li>
<li><h5>参数：</h5>

<table>
<thead>
<tr>
<th align="left"> 参数 </th>
<th align="left"> 类型 </th>
<th align="left"> 必选 </th>
<th align="left"> 描述 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> pad_id </td>
<td align="left"> string </td>
<td align="left"> √ </td>
<td align="left"> pad_id </td>
</tr>
<tr>
<td align="left"> box_token </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 当前连接的主机box_token </td>
</tr>
<tr>
<td align="left"> position </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 门禁pad的位置 </td>
</tr>
<tr>
<td align="left"> network_switcher </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 开关IP地址 </td>
</tr>
<tr>
<td align="left"> network_switcher_token </td>
<td align="left"> string </td>
<td align="left">  </td>
<td align="left"> 开关唯一标识（二代开关） </td>
</tr>
</tbody>
</table>
</li>
<li><h5>返回值：</h5>

<p>无</p></li>
<li><h5>返回JSON：</h5>

<pre><code>{
"code": 0,
"data": {},
"page": {}
}
</code></pre></li>
</ul>




<hr />




<h1><span id="9"/> 9. 识别推送</h1>




<h2><span id="9.1"/> 9.1 识别消息推送<font color='red'>（Deprecated）</font></h2>




<ul>
<li><h5>URL:</h5>

<p>socket.io/event/</p></li>
<li><h5>描述：</h5>

<p>通过 socket.io 订阅消息，在有识别事件时为用户推送相关信息
<font color='red'>这个接口已废弃，请使用<a href="#11.1">11.1 获取识别结果（websocket接口）</a>获取实时识别事件</font></p></li>

</ul>


<p><br><br></p>




<h1>二、本地主机API</h1>




<hr />




<p><code>注：本地主机API的调用地址不再是 https://v2.koalacam.net，而是本地主机直接提供的接口</code></p>




<hr />




<h1><span id="10"/> 10. 静态识别</h1>




<h2><span id="10.1"/> 10.1 1比N识别</h2>




<ul>
<li><h4>URL:</h4>

<p>http://本地主机地址:8866/recognize</p></li>
<li><h4>Method:</h4>

<p>POST</p></li>
<li><h4>描述：</h4>

<p>在调用此接口之前需要调用 <a href="#8.1">/pad/login</a> 获得本设备的screen_token<br/>
本地主机地址 为 <a href="#8.1">/pad/login</a> 接口 登录时返回的主机信息中的相应字段值</p></li>
<li><h4>参数：</h4>

<table>
<thead>
<tr>
<th align="left">参数</th>
<th align="left">类型</th>
<th align="left">必选</th>
<th align="left">描述</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">image</td>
<td align="left">file</td>
<td align="left">√</td>
<td align="left">用于识别的照片</td>
</tr>
<tr>
<td align="left">screen_token</td>
<td align="left">str</td>
<td align="left">√</td>
<td align="left">从 <a href="#8.1">/pad/login</a> 接口获得</td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回值说明：</h4>

<table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">person</td>
<td align="left">dict</td>
<td align="left">识别出来的用户, confidence表示相似度，tag表示用户信息</td>
</tr>
<tr>
<td align="left">can_door_open</td>
<td align="left">bool</td>
<td align="left">综合考虑门禁设置、当前时间、识别用户后是否可以开门</td>
</tr>
<tr>
<td align="left">error</td>
<td align="left">int</td>
<td align="left">0:允许进入, 1:不允许访客进入, 2:来访时间未到或已过期, 3:此门只允许部分员工进入, 4:此刻门禁设置为关闭, 5:此刻门禁设置为关闭, 6:设置找不到, 7:陌生人</td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回结果</h4>

<pre><code>{
"person": {
  "confidence": 96.52632,
  "tag": "{\"subject_type\": 0, \"start_time\": 0, \"name\": \"A\u4e01\u4e9a\u5149\", \"title\": \"\", \"department\": \"\", \"end_time\": 0, \"description\": \"hahah\", \"id\": 175, \"avatar\": \"/static/upload/photo/2015-10-22/b7a6916b663129c4078a089307da3f8893c22652.jpg\"}",
  "id": "175"
},
"can_door_open": true,
"error": 0
}
</code></pre></li>
</ul>




<h2><span id="10.2"/> 10.2 1比1认证</h2>




<ul>
<li><h4>URL:</h4>

<p>http://本地主机地址:8866/checkin</p></li>
<li><h4>Method:</h4>

<p>POST</p></li>
<li><h4>描述：</h4>

<p>本地主机地址 为 <a href="#8.1">/pad/login</a> 接口 登录时返回的主机信息中的相应字段值</p></li>
<li><h4>参数：</h4>

<table>
<thead>
<tr>
<th align="left">参数</th>
<th align="left">类型</th>
<th align="left">必选</th>
<th align="left">描述</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">image</td>
<td align="left">file</td>
<td align="left">√</td>
<td align="left">用于识别的照片</td>
</tr>
<tr>
<td align="left">person_id</td>
<td align="left">int</td>
<td align="left">√</td>
<td align="left">认证者的 ID</td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回值说明：</h4>

<p>person_id是从照片识别出来的用户id，如果为0表示未识别出</p></li>
<li><h4>返回结果</h4></li>
</ul>




<pre><code>{
  "code": 0,
  "data": {
    "person_id": 1
  },
  "page": {}
}
</code></pre>


<hr />




<h1><span id="11"/> 11. 视频流识别</h1>




<h2><span id="11.1"/> 11.1 获取识别结果（websocket接口）</h2>




<p><ul>
<li><h4>URL:</h4></p>

<p><p>ws://本地主机地址:9000/video</p></li>
<li><h4>Method:</h4></p>

<p><p>POST</p></li>
<li><h4>描述：</h4></p>

<p><p>本地主机地址 为 <a href="#8.1">/pad/login</a> 接口 登录时返回的主机信息中的相应字段值</p></li>
<li><h4>参数：</h4></p>

<p><table>
<thead>
<tr>
<th align="left">参数</th>
<th align="left">类型</th>
<th align="left">描述</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">url</td>
<td align="left">str</td>
<td align="left">视频流地址, 例如 rtsp://10.101.1.60/live1.sdp。这个参数必须经过url encode再传到服务器</td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回值说明：</h4></li>
<li>算法会将一个人在视频中出现到离开的所有图片当做一个track，每个track有个id，同一个track的信息都代表一个人</li>
<li>算法处理的流程有可能有三种：</li>
<li>recognizing...recognizing...gone</li>
<li>recognizing...recognizing...recognized，gone</p>

<p><ul>
<li>recognizing...recognizing...unrecognized，gone</li>
</ul>
</li>
<li><table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> screen </td>
<td align="left"> <a href="#screen_model">Screen</a> </td>
<td align="left"> 识别位置的信息，只有type不为gone时才会有</td>
</tr>
<tr>
<td align="left"> person </td>
<td align="left"> Person </td>
<td align="left"> 和底库里相似的人，只有type为recognized时才会有</td>
</tr>
<tr>
<td align="left"> open_door </td>
<td align="left"> bool </td>
<td align="left"> 是否开门，只有type为recognized时才会有</td>
</tr>
<tr>
<td align="left"> error </td>
<td align="left"> string </td>
<td align="left"> 如果open_door为false，这个字段就是不能开门的原因 </td>
</tr>
<tr>
<td align="left"> type </td>
<td align="left"> string </td>
<td align="left"> 本条信息的类型，4种，lastface（识别中）、recognized、unrecognized、gone</td>
</tr>
<tr>
<td align="left"> data </td>
<td align="left"> RecognizeInfo </td>
<td align="left"> 算法识别的底层信息，无特殊需求的话不用处理，其中track为track id。只有当type为gone的时候，这里才包含年龄性别信息。</td>
</tr>
</tbody>
</table>
</li>
<li><h5>Person（未列出说明的字段与<a href="#subject_model">subject</a>相同）</h5></p>

<p><table>
<thead>
<tr>
<th align="left"> 字段 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> src </td>
<td align="left"> base64 image </td>
<td align="left"> 实时抓拍的照片 </td>
</tr>
</tbody>
</table>
</li>
<li><h4>返回结果</h4></p>

<p><pre><code>{
"data": {
  "status": "recognized",
  "track": 0,
  "timestamp": 1474454214,
  "face": {
    "image": "",
    "rect": {
      "top": 207,
      "right": 913,
      "bottom": 775,
      "left": 345
    }
  },
  "person": {
    "feature_id": 0,
    "confidence": 79.35476,
    "tag": "{\"subject_type\": 0, \"description\": \"\", \"start_time\": 0, \"birthday\": null, \"id\": 103821, \"remark\": \"\", \"name\": \"\u5218\u5b9a\u5cf0\", \"title\": \"\", \"job_number\": \"\", \"entry_date\": null, \"end_time\": 0, \"department\": \"\", \"avatar\": \"http://**************:8867/static/upload/photo/2016-09-21/4a6c5f1ea4fff5df02edadffa37c82308bde0e63.jpg\"}",
    "id": "103821"
  },
  "quality": 0.7678973
},
"screen": {
  "camera_address": "rtsp://***********/live1.sdp",
  "allowed_subject_ids": [],
  "network_switcher_status": "0",
  "box_token": "66666666-f988-4fc9-8a67-6a4c4220dd8a",
  "description": "",
  "box_heartbeat": 1474454143,
  "network_switcher": "*************",
  "camera_name": "",
  "camera_status": "0",
  "allow_visitor": true,
  "screen_token": "55VkFeQOHzgoIMQe",
  "network_switcher_token": null,
  "box_status": "0",
  "allow_all_subjects": true,
  "type": 1,
  "id": 243,
  "camera_position": "\u4e0a\u65b9\u540a\u88c5",
  "box_address": "**************"
},
"person": {
  "src": "data:image/jpeg;base64...",
  "remark": "",
  "subject_type": 0,
  "description": "",
  "title": "",
  "timestamp": 1474454214,
  "start_time": 0,
  "avatar": "http://**************:8867/static/upload/photo/2016-09-21/4a6c5f1ea4fff5df02edadffa37c82308bde0e63.jpg",
  "job_number": "",
  "birthday": null,
  "entry_date": null,
  "department": "",
  "end_time": 0,
  "id": 103821,
  "name": "\u5218\u5b9a\u5cf0"
},
"error": "\u5141\u8bb8\u8fdb\u5165",
"open_door": true,
"type": "recognized"
}
或
{
"data": {
  "track": 0,
  "timestamp": 1474454216,
  "track_groups": {
    "short": 3,
    "long": 2
  },
  "status": "gone",
  "attr": {
    "age": 26.324329376220703,
    "male": 0.0574454702436924,
    "female": 0.9425545334815979
  }
},
"type": "gone"
}
</code></pre></li></p>

<p><p><br><br></p></p>

<p><h1>三、<span id="ZH3"> 模型定义 </span></h1></p>

<p><hr /></p>

<p><p><span id="subject_model"></p></p>

<p><h5>底库对象subject，即用户和访客</h5></p>

<p><p></span></p></p>

<p><table>
<thead>
<tr>
<th align="left"> 属性 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
<th align="left"> 备注 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">subject_type</td>
<td align="left">int</td>
<td align="left">用户类型</td>
<td align="left">0:员工, 1:访客, 2: VIP访客</td>
</tr>
<tr>
<td align="left">email</td>
<td align="left">string</td>
<td align="left">邮箱</td>
<td></td>
</tr>
<tr>
<td align="left">password_reseted</td>
<td align="left">bool</td>
<td align="left">是否重置过密码</td>
<td></td>
</tr>
<tr>
<td align="left">name</td>
<td align="left">string</td>
<td align="left">姓名</td>
<td></td>
</tr>
<tr>
<td align="left">pinyin</td>
<td align="left">string</td>
<td align="left">姓名的拼音</td>
<td></td>
</tr>
<tr>
<td align="left">gender</td>
<td align="left">int</td>
<td align="left">性别</td>
<td></td>
</tr>
<tr>
<td align="left">phone</td>
<td align="left">str</td>
<td align="left">电话</td>
<td></td>
</tr>
<tr>
<td align="left">avatar</td>
<td align="left">str</td>
<td align="left">头像地址</td>
<td></td>
</tr>
<tr>
<td align="left">department</td>
<td align="left">str</td>
<td align="left">部门</td>
<td></td>
</tr>
<tr>
<td align="left">title</td>
<td align="left">str</td>
<td align="left">职位</td>
<td></td>
</tr>
<tr>
<td align="left">description</td>
<td align="left">str</td>
<td align="left">签名</td>
<td></td>
</tr>
<tr>
<td align="left">job_number</td>
<td align="left">str</td>
<td align="left">工号</td>
<td></td>
</tr>
<tr>
<td align="left">remark</td>
<td align="left">str</td>
<td align="left">备注</td>
<td></td>
</tr>
<tr>
<td align="left">birthday</td>
<td align="left">int</td>
<td align="left">生日</td>
<td align="left">时间戳(单位秒)</td>
</tr>
<tr>
<td align="left">entry_date </td>
<td align="left">int</td>
<td align="left">入职时间</td>
<td align="left">时间戳(单位秒)</td>
</tr>
<tr>
<td align="left">photos</td>
<td align="left">list </td>
<td align="left"> 识别照片列表</td>
<td></td>
</tr>
<tr>
<td align="left">purpose</td>
<td align="left">int</td>
<td align="left">(访客属性) 来访目的</td>
<td align="left">   0: 其他, 1: 面试, 2: 商务, 3: 亲友, 4: 快递送货</td>
</tr>
<tr>
<td align="left">interviewee</td>
<td align="left">   str </td>
<td align="left">(访客属性) 受访人</td>
<td></td>
</tr>
<tr>
<td align="left">come_from</td>
<td align="left"> str</td>
<td align="left">    (访客属性) 来访单位</td>
<td></td>
</tr>
<tr>
<td align="left">start_time </td>
<td align="left">int</td>
<td align="left">   (访客属性) 预定来访时间</td>
<td align="left">  时间戳(单位秒)</td>
</tr>
<tr>
<td align="left">end_time   </td>
<td align="left">int</td>
<td align="left">   (访客属性) 预定离开时间   </td>
<td align="left">时间戳(单位秒)</td>
</tr>
<tr>
<td align="left">visit_notify</td>
<td align="left">  bool</td>
<td align="left">   (访客属性) 来访是否发APP消息推送</td>
<td></td>
</tr>
</tbody>
</table></p>

<p><p><span id="photo_model"></p></p>

<p><h5>识别照片photo</h5></p>

<p><p></span></p></p>

<p><table>
<thead>
<tr>
<th align="left"> 属性 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">subject_id</td>
<td align="left">int</td>
<td align="left">subject ID</td>
</tr>
<tr>
<td align="left">company_id</td>
<td align="left">int</td>
<td align="left">公司ID</td>
</tr>
<tr>
<td align="left">url</td>
<td align="left">str</td>
<td align="left">照片存储地址</td>
</tr>
<tr>
<td align="left">quality</td>
<td align="left">float</td>
<td align="left">照片质量</td>
</tr>
<tr>
<td align="left">version</td>
<td align="left">int</td>
<td align="left">识别算法版本</td>
</tr>
<tr>
<td align="left">feature</td>
<td align="left">str</td>
<td align="left">识别算法抽取出来的特征值</td>
</tr>
</tbody>
</table></p>

<p><p><span id="screen_model"></p></p>

<p><h5>门禁信息screen，即一路识别相机</h5></p>

<p><p></span></p></p>

<p><table>
<thead>
<tr>
<th align="left"> 属性 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
<th align="left">备注</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">type</td>
<td align="left">int</td>
<td align="left"> 类型</td>
<td align="left">1:摄像头, 2:门禁Pad, 3:前台Pad</td>
</tr>
<tr>
<td align="left">box_address</td>
<td align="left">str</td>
<td align="left">相机关联的主机ip地址</td>
<td></td>
</tr>
<tr>
<td align="left">box_heartbeat</td>
<td align="left">int</td>
<td align="left">主机心跳时间戳</td>
<td></td>
</tr>
<tr>
<td align="left">box_status</td>
<td align="left">str</td>
<td align="left">服务器与主机连接状态</td>
<td align="left">‘1’表示较长没有收到主机心跳消息，'0'正常</td>
</tr>
<tr>
<td align="left">camera_address</td>
<td align="left">str</td>
<td align="left">相机ip地址</td>
<td></td>
</tr>
<tr>
<td align="left">camera_position</td>
<td align="left">str</td>
<td align="left">相机位置描述</td>
<td></td>
</tr>
<tr>
<td align="left">camera_status</td>
<td align="left">str</td>
<td align="left">服务器与相机连接状态</td>
<td align="left">‘0’表示连接正常</td>
</tr>
<tr>
<td align="left">network_switcher</td>
<td align="left">str</td>
<td align="left">网络开关ip地址</td>
<td></td>
</tr>
<tr>
<td align="left">network_switcher_status</td>
<td align="left">str</td>
<td align="left">网络开关连接状态</td>
<td align="left">‘0’表示连接正常</td>
</tr>
<tr>
<td align="left">allow_all_subjects</td>
<td align="left">bool</td>
<td align="left">是否允许所有人进入</td>
<td></td>
</tr>
<tr>
<td align="left">allow_visitor  </td>
<td align="left">bool</td>
<td align="left">是否允许访客进入</td>
<td></td>
</tr>
</tbody>
</table></p>

<p><p><br><br></p></p>

<p><h2>四、<span id="ZH4">错误码定义</span></h2></p>

<p><hr /></p>

<p><table>
<thead>
<tr>
<th align="left"> 错误码</th>
<th align="left">说明</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">1000</td>
<td align="left">未知错误</td>
</tr>
<tr>
<td align="left">1001</td>
<td align="left">请求方法不允许</td>
</tr>
<tr>
<td align="left">1002</td>
<td align="left">参数错误</td>
</tr>
<tr>
<td align="left">1003</td>
<td align="left">数据库错误</td>
</tr>
<tr>
<td align="left">1004</td>
<td align="left">操作不允许</td>
</tr>
<tr>
<td align="left">1005</td>
<td align="left">网络错误</td>
</tr>
<tr>
<td align="left">1006</td>
<td align="left">无效主题</td>
</tr>
<tr>
<td align="left">1007</td>
<td align="left">密码需要更改</td>
</tr>
<tr>
<td align="left">1008</td>
<td align="left">二次验证错误</td>
</tr>
<tr>
<td align="left">2000</td>
<td align="left">用户不存在</td>
</tr>
<tr>
<td align="left">2001</td>
<td align="left">Group 不存在</td>
</tr>
<tr>
<td align="left">2002</td>
<td align="left">Group 已存在</td>
</tr>
<tr>
<td align="left">2003</td>
<td align="left">相机不存在</td>
</tr>
<tr>
<td align="left">2004</td>
<td align="left">Core 不存在</td>
</tr>
<tr>
<td align="left">2005</td>
<td align="left">用户不存在</td>
</tr>
<tr>
<td align="left">2006</td>
<td align="left">照片不存在</td>
</tr>
<tr>
<td align="left">2007</td>
<td align="left">主机不存在</td>
</tr>
<tr>
<td align="left">2008</td>
<td align="left">屏幕不存在</td>
</tr>
<tr>
<td align="left">2009</td>
<td align="left">公司不存在</td>
</tr>
<tr>
<td align="left">2010</td>
<td align="left">历史记录不存在</td>
</tr>
<tr>
<td align="left">2011</td>
<td align="left">用户名已存在</td>
</tr>
<tr>
<td align="left">2012</td>
<td align="left">公司已存在</td>
</tr>
<tr>
<td align="left">2013</td>
<td align="left">设备不存在,请刷新</td>
</tr>
<tr>
<td align="left">20011</td>
<td align="left">主机已经存在</td>
</tr>
<tr>
<td align="left">20012</td>
<td align="left">主机已绑定</td>
</tr>
<tr>
<td align="left">20013</td>
<td align="left">主机未绑定</td>
</tr>
<tr>
<td align="left">20014</td>
<td align="left">主机与用户公司不符</td>
</tr>
<tr>
<td align="left">20110</td>
<td align="left">姓名已存在</td>
</tr>
<tr>
<td align="left">20120</td>
<td align="left">照片已满</td>
</tr>
<tr>
<td align="left">20150</td>
<td align="left">邮箱已存在</td>
</tr>
<tr>
<td align="left">20160</td>
<td align="left">权限不足</td>
</tr>
<tr>
<td align="left">20130</td>
<td align="left">Excel 包含错误</td>
</tr>
<tr>
<td align="left">20140</td>
<td align="left">文件不存在</td>
</tr>
<tr>
<td align="left">3000</td>
<td align="left">密码不正确</td>
</tr>
<tr>
<td align="left">3001</td>
<td align="left">验证码不正确</td>
</tr>
<tr>
<td align="left">3002</td>
<td align="left">两次密码不一致</td>
</tr>
<tr>
<td align="left">3003</td>
<td align="left">请登录</td>
</tr>
<tr>
<td align="left">4001</td>
<td align="left">获取天气信息失败</td>
</tr>
<tr>
<td align="left">5000</td>
<td align="left">未检测到人脸</td>
</tr>
<tr>
<td align="left">5004</td>
<td align="left">人脸质量太低</td>
</tr>
<tr>
<td align="left">5001</td>
<td align="left">摄像头配置不正确</td>
</tr>
<tr>
<td align="left">5002</td>
<td align="left">不是有效的 JSON 格式</td>
</tr>
<tr>
<td align="left">5003</td>
<td align="left">名称已存在</td>
</tr>
</tbody>
</table></p>

<p><p><br><br></p></p>

<p><h2>五、<span id="ZH5">分页信息定义</span></h2></p>

<p><h5>如果某条API有分页的功能，那么返回值中的page项会有内容：</h5></p>

<p><table>
<thead>
<tr>
<th align="left"> 属性 </th>
<th align="left"> 类型 </th>
<th align="left"> 说明 </th>
</tr>
</thead>
<tbody>
<tr>
<td align="left"> count </td>
<td align="left"> int </td>
<td align="left"> 数据总条数 </td>
</tr>
<tr>
<td align="left"> current </td>
<td align="left"> int </td>
<td align="left"> 返回的是第几页（从1开始计数） </td>
</tr>
<tr>
<td align="left"> size </td>
<td align="left"> int </td>
<td align="left"> 每页多少条数据 </td>
</tr>
<tr>
<td align="left"> count </td>
<td align="left"> int </td>
<td align="left"> 总页数 </td>
</tr>
</tbody>
</table></p>

<p><pre><code>{
    "page":{
        "count": 757,
        "current": 1,
        "size": 10,
        "total": 76
    }
}
</code></pre>
</body>
</html></p>
</body>
</html>