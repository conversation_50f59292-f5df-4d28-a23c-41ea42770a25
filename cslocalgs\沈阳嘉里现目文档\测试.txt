登录:
注册人员:
POST
http://**************:9988/subject

{
"start_time":0,
"end_time":0,
"purpose":0,
"birthday":1,
"photo_ids":
[
],
"group_ids":
[473],
"gender":1,
"email":"<EMAIL>",
"phone":"12446",
"avatar":"",
"department":"",
"title":"",
"description":"a1",
"interviewee":"",
"come_from":"",
"job_number":"",
"remark":"1",
"subject_type":2,
"name":"cy5"
}


测试注意事项：
1、平台新增获取人员分组列表接口（查看koala_api_v3.2.5.html）
  /subjects/group/list
  
  目前只支持page、size、name这三个参数
  
2、新增人员的时候多了group_ids字段，该字段为Arrary形式，比如"group_ids":[1,2,3,4]
   如果注册时带有photo_ids,则会把对应的人脸下发到1,2,3,4这4台设备，如果没有携带group_ids字段，则会下发给所有PM下面的所有设备
   
3、设备重连的时候，也需要只下载对应group的人脸数据

4、更改group_ids时，需要更改之前的对应的group_ids的设备删掉之前的人脸数据；
