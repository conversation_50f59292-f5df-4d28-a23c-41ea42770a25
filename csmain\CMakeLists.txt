CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (main C CXX)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src ${CMAKE_CURRENT_SOURCE_DIR}/../csbase/doorlog")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
SET(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
SET(BIN_DIR ${CMAKE_CURRENT_SOURCE_DIR}/release/bin/)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/redis/hiredis ${CSBASE_SOURCE_DIR}/evpp/lib ${SRC_DIR}/csmain_cli/editline)

AUX_SOURCE_DIRECTORY(${SRC_DIR} SRC_LIST_MAIN)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Basic SRC_LIST_MAIN_BASIC)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Character SRC_LIST_MAIN_CHARACTER)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Character/cstring SRC_LIST_MAIN_CSTRING)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Dao SRC_LIST_MAIN_DAO)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Utility SRC_LIST_MAIN_UTILITY)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model SRC_LIST_MAIN_MODEL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Logic SRC_LIST_MAIN_LOGIC)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Office/Model SRC_LIST_MAIN_OFFICE_MODEL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Office/Logic SRC_LIST_MAIN_OFFICE_LOGIC)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Tools SRC_LIST_MAIN_TOOLS)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/MsgRateLimiter SRC_LIST_MAIN_MSG_RATE_LIMIT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/jsoncpp0.5/src/json SRC_LIST_BASE_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc SRC_LIST_BASE_GRPC)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/protobuf SRC_LIST_BASE_PROTOBUF)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/session SRC_LIST_BASE_SESSION)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc/cssession SRC_LIST_BASE_GRPC_SESSION)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc/csmain SRC_LIST_BASE_GRPC_MAIN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/csmain SRC_LIST_BASE_CSMAIN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/beanstalk-client SRC_LIST_BASE_BEANSTALK)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/model SRC_LIST_BASE_MODEL)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/doorlog SRC_LIST_BASE_DOORLOG)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/kafka SRC_LIST_BASE_KAFKA)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/zipkin SRC_LIST_BASE_ZIPKIN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Tinyxml SRC_LIST_BASE_TINYXML)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/http SRC_LIST_BASE_HTTP)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/loop SRC_LIST_BASE_LOOP)

SET(BASE_LIST_INC  ${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/redis ${CSBASE_SOURCE_DIR}/redis/hiredis ${CSBASE_SOURCE_DIR}/grpc 
                   ${CSBASE_SOURCE_DIR}/grpc/include ${CSBASE_SOURCE_DIR}/etcd ${CSBASE_SOURCE_DIR}/evpp ${CSBASE_SOURCE_DIR}/session
                   ${CSBASE_SOURCE_DIR}/grpc/cssession ${CSBASE_SOURCE_DIR}/protobuf ${CSBASE_SOURCE_DIR}/Rldb ${CSBASE_SOURCE_DIR}/encrypt 
                   ${CSBASE_SOURCE_DIR}/grpc/csmain ${CSBASE_SOURCE_DIR}/grpc/gens ${CSBASE_SOURCE_DIR}/beanstalk-client ${CSBASE_SOURCE_DIR}/gid 
                   ${CSBASE_SOURCE_DIR}/curl ${CSBASE_SOURCE_DIR}/model ${CSBASE_SOURCE_DIR}/jsoncpp0.5/include ${CSBASE_SOURCE_DIR}/pbxmod ${CSBASE_SOURCE_DIR}/Tinyxml ${CSBASE_SOURCE_DIR}/timerticker)

ADD_DEFINITIONS(-g -Wall -DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -DNDEBUG -fPIE -Werror -Wno-unused-parameter -Wno-deprecated -Wno-cpp -Wno-format-truncation)

include_directories(${INC_DIR}/mysql ${SRC_DIR}/include ${SRC_DIR}/Common/Utility ${SRC_DIR}/Common/Tinyxml ${SRC_DIR}/MsgRateLimiter
                    ${SRC_DIR}/Common/Basic ${SRC_DIR}/Common/Character ${SRC_DIR}/Common/Character/cstring
                    ${SRC_DIR}/Common/Socket ${SRC_DIR}/Common/Dao ${SRC_DIR}/Common/Curl ${SRC_DIR}/Model
                    ${SRC_DIR}/Logic ${SRC_DIR}/Office/Model ${SRC_DIR}/Office/Logic ${SRC_DIR}/Tools ${SRC_DIR}/csmain_cli/editline ${BASE_LIST_INC} 
					/usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include ${CSBASE_SOURCE_DIR}/metrics )

add_executable(csmain ${SRC_LIST_MAIN} ${SRC_LIST_MAIN_BASIC} ${SRC_LIST_MAIN_CHARACTER} ${SRC_LIST_MAIN_CSTRING} 
                      ${SRC_LIST_MAIN_DAO} ${SRC_LIST_BASE_TINYXML} ${SRC_LIST_MAIN_UTILITY} ${SRC_LIST_MAIN_MODEL} 
                      ${SRC_LIST_MAIN_LOGIC} ${SRC_LIST_MAIN_OFFICE_MODEL} ${SRC_LIST_MAIN_OFFICE_LOGIC} ${SRC_LIST_MAIN_TOOLS}
                      ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_JSON} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_GRPC} ${SRC_LIST_MAIN_MSG_RATE_LIMIT}
                      ${SRC_LIST_BASE_ETCD} ${SRC_LIST_BASE_PROTOBUF} ${SRC_LIST_BASE_SESSION} ${SRC_LIST_BASE_GRPC_SESSION} 
                      ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_BASE_GRPC_MAIN} ${SRC_LIST_BASE_CSMAIN} ${SRC_LIST_BASE_BEANSTALK} 
                      ${SRC_LIST_BASE_MODEL} ${SRC_LIST_BASE_DOORLOG} ${SRC_LIST_BASE_KAFKA} ${SRC_LIST_BASE_HTTP} ${SRC_LIST_BASE_LOOP}
                      ${SRC_LIST_BASE_ZIPKIN} ${SRC_LIST_BASE_METRICS} ${prefixed_file_list})

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

set_target_properties(csmain PROPERTIES LINK_FLAGS  "-Wl,--rpath=/usr/local/akcs/csmain/lib")

target_link_libraries(csmain pthread mysqlclient iconv  evpp glog ssl crypto event -Bstatic hiredis csbase gpr grpc grpc++ 
                      protobuf boost_system cpprest etcd-cpp-api curl cppkafka rdkafka rdkafka++ z dl)

target_compile_options(csmain PRIVATE -gstabs+ -std=c++11)

add_executable(csmain_cli ${SRC_DIR}/csmain_cli/csmain_cli.c ${SRC_DIR}/csmain_cli/editline)

set_target_properties(csmain_cli PROPERTIES LINK_FLAGS  "-Wl,--rpath=/usr/local/akcs/csmain/lib")

target_link_libraries(csmain_cli edit ncurses ssl crypto stdc++)

file(COPY ${SRC_DIR}/csmain_cli/getDevServer.php DESTINATION ${BIN_DIR})
file(COPY ${SRC_DIR}/csmain_cli/loadAppCallDnd.php DESTINATION ${BIN_DIR})
file(COPY ${SRC_DIR}/csmain_cli/RedisManage.php DESTINATION ${BIN_DIR})
file(COPY ${SRC_DIR}/csmain_cli/transferToUserConcept.php DESTINATION ${BIN_DIR})
