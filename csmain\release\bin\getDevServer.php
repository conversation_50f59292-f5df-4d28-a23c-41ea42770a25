<?php
/*处理大版本升级时候，需要根据低版本在插入数据的操作*/
/*V4.0到4.1需要插入PersonalAccountCnf配置*/
date_default_timezone_set("PRC");
function getDB($dbhost)
{
    $dbhost = $dbhost;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;port=3308;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}



exec("/bin/cat /usr/local/akcs/csmain/conf/csmain.conf  | grep -w akcs_db_ip | awk -F'=' '{ print $2 }' | tr -d  \"\\n\"",$output);

$dbhost=$output[0];

$db = getDB($dbhost);
$mac = $argv[1];

$sth = $db->prepare("select AccSrvID From Devices where Mac=:MAC union all select AccSrvID From PersonalDevices where Mac=:MAC;");
$sth->bindParam(':MAC', $mac, PDO::PARAM_STR);
$sth->execute();
$dev = $sth->fetch(PDO::FETCH_ASSOC);

print($dev['AccSrvID']);
?>
