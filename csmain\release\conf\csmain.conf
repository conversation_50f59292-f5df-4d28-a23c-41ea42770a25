#csmain conf
cloud_env=
csmain_outerip=*************
#内部调用前端接口的地址,以及配置文件下载地址
csmain_outer_domain=dev.akuvox.com
csmain_outeripv6=
csmain_port=8501

#web sev conf, ip填写web服务器的外网ip,供客户端下载配置文件|rom包升级文件用
csweb_net=**************
csweb_net_ipv6=**************

#cspush conf
cspush_net=***********:8000

#csvs conf
csvs_net=***********:9001
video_length=30

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#nsq conf
nsq_route_topic=ak_route

#akcs db conf
akcs_db_ip=***********
akcs_db_port=3306
akcs_db_database=AKCS

#log db conf
log_db_ip=***********
log_db_port=3306
log_db_database=LOG

#mapping db conf
mapping_db_ip=**************
mapping_db_port=3306
mapping_db_database=AKCSMapping

#common db conf
db_username=dbuser01

#配置OEM，用于推送服务的区分,空值就是akuvox
oem_name=Akuvox
#配置和cspush推送的aes加密密钥
push_aeskey=
#网关编号
gateway_code=
#终端控制允许的ip
cliserver_allowip=127.0.0.1;*************;**************;*************
cliserver_port=1025

beanstalk_ip=
beanstalk_port=8519
#svn版本号
svn_version=

#是否注册到etcd
reg_etcd=1

#/var/www/download/DST/TimeZone-dev.xml
tz_md5=1fbfff473f66ad953a71afcf3c711416
tz_data_md5=138affbf3d80751072844ba595d6d36d
#除了R29/x916外，其他都下发这个，不进行tzdata更新
tz_data_md5_old=1b88cc17a195d24c07e796d487f45a4e


#是否打开限流 0：关闭限流；1：开启限流
limit_switch=1

#限流速率
rate=250

#csconfig ip
config_server_ipv4=
config_server_ipv6=

config_server_port=
config_server_tlshigh_port=8604
config_server_domain=
config_server_domain_gray_percentage=20
#临时aucloud兼容，可不配
#use_config_ip_mng规则是(支持多个社区id): ,xxx,xxx,
csconfig_ip=
use_config_ip_mng=

is_aws=0
apiurl=

#设备下载user超时时间(秒)
download_user_timeout=300

#added by chenyc,2022.01.25,大项目设备权限组文件刷新测试框架的配置项
stress_test=0

nsq_linker_topic=ak_linker
nsq_linker_ip=

server_area=1

log_encrypt=0
log_trace=1

#限流时间
limiting_timeout=40

#msg id限流开关
msg_id_limitswitch=1

#请求统计开关
request_statics_switch=0

#SL20锁开门过期时间
sl20_opendoor_expire=60
#是否开启设备消息处理延时统计，弄个配置如果有性能问题可以快速关闭
msg_lantency_metric=1