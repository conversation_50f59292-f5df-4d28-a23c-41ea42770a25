#configure for motion

csmainCacheInstances=appconf,dev_outerip,appdnd,appstat,userdetail,weather,backend_limiting,sl20_lock
# uid-app是否接受motion alert的redis
appconf_host=***********
appconf_port=8504
appconf_db=1
appconf_maxconncnt=2
dev_sid_conn_master=1

# dev_outerip: 设备外网IP
dev_outerip_host=127.0.0.1
dev_outerip_port=8504
dev_outerip_db=11
dev_outerip_maxconncnt=2

# phone_account: phone-account
#phone_account_host=127.0.0.1
#phone_account_port=8504
#phone_account_db=7
#phone_account_maxconncnt=5

# appdnd: app免打扰
appdnd_host=127.0.0.1
appdnd_port=8504
appdnd_db=3
appdnd_maxconncnt=2

# appstat: app信息统计
appstat_host=127.0.0.1
appstat_port=8504
appstat_db=12
appstat_maxconncnt=2

# userdetail: 设备请求用户详细数据去重
userdetail_host=127.0.0.1
userdetail_port=8504
userdetail_db=14
userdetail_maxconncnt=2

# weather: 天气状态
weather_host=127.0.0.1
weather_port=8504
weather_db=21
weather_maxconncnt=2

# backend_limiting: 限流
backend_limiting_host=127.0.0.1
backend_limiting_port=8504
backend_limiting_db=24
backend_limiting_maxconncnt=2

# sl20_lock: SL20锁
sl20_lock_host=127.0.0.1
sl20_lock_port=8504
sl20_lock_db=30
sl20_lock_maxconncnt=2

#如果sentinels有值,代表启动主从，那么_host的配置就不生效，如果没有就是单机
sentinels=



