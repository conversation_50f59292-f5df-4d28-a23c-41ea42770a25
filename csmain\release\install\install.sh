#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-12
# Filename      :   install.sh
# Version       :
# Description   :   csmain 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IS_REG_ETCD=$3             #是否注册到etcd

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }


# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csmain    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csmain
LOG_PATH=/var/log/csmainlog
CTRL_SCRIPT=csmainctl.sh
RUN_SCRIPT=csmainrun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}


# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

# SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
SERVERIP=$(grep_conf 'SERVERIP' $IP_FILE)
SERVERIPV6=$(grep_conf 'SERVERIPV6' $IP_FILE)
CLOUD_ENV=$(grep_conf 'cloud_env' $IP_FILE)

CSMAIN_SERVER_DOMAIN=$(grep_conf 'CSMAIN_SERVER_DOMAIN' $INSTALL_CONF)
GATEWAY_NUM=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
SYSTEM_OEM=$(grep_conf 'SYSTEM_OEM' $INSTALL_CONF)
WEB_OUTER_IP=$(grep_conf 'WEB_OUTER_IPV4' $INSTALL_CONF)
WEB_OUTER_IPV6=$(grep_conf 'WEB_OUTER_IPV6' $INSTALL_CONF)
PUSH_OUTER_IP=$(grep_conf 'PUSH_OUTER_IP' $INSTALL_CONF)
CONFIG_OUTER_IP=$(grep_conf 'CONFIG_OUTER_IPV4' $INSTALL_CONF)
CONFIG_OUTER_IPV6=$(grep_conf 'CONFIG_OUTER_IPV6' $INSTALL_CONF)
LIMIT_SWITCH=$(grep_conf 'CSMAIN_LIMIT_SWITCH' $INSTALL_CONF)
RATE=$(grep_conf 'CSMAIN_RATE' $INSTALL_CONF)
AKCS_MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
LOG_MYSQL_INNER_IP=$(grep_conf 'LOG_MYSQL_INNER_IP' $INSTALL_CONF)
# NSQLOOKUPD_INNER_IP=$(grep_conf 'NSQLOOKUPD_INNER_IP' $INSTALL_CONF)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
CSSESSION_INNER_IP=$(grep_conf 'CSSESSION_INNER_IP' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
ENABLE_AKCS_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
ENABLE_LOG_DBPROXY=$(grep_conf 'ENABLE_LOG_DBPROXY' $INSTALL_CONF)
AKCS_DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
LOG_DBPROXY_INNER_IP=$(grep_conf 'LOG_DBPROXY_INNER_IP' $INSTALL_CONF)
IS_AWS=$(grep_conf 'IS_AWS' $INSTALL_CONF || echo '0')
API_URL=$(grep_conf 'API_URL' $INSTALL_CONF)
CONFIG_PORT=$(grep_conf 'CONFIG_PORT' $INSTALL_CONF)
CSLINKER_NSQD_IP=$(grep_conf 'CSLINKER_NSQD_IP' $INSTALL_CONF)
SYSTEM_AREA=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
CONFIG_SERVER_DOMAIN=$(grep_conf 'CONFIG_SERVER_DOMAIN' $INSTALL_CONF)

#临时aucloud处理
csconfig_ip=$(grep_conf 'csconfig_ip' $INSTALL_CONF || echo '')
use_config_ip_mng=$(grep_conf 'use_config_ip_mng' $INSTALL_CONF || echo '')

if [ -z "$CSMAIN_SERVER_DOMAIN" ]; then
    echo "Please input your csmain Domain."
    exit 1;
fi


# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
if [ -f $APP_HOME/scripts/$CTRL_SCRIPT ]; then
    bash "$APP_HOME"/scripts/$CTRL_SCRIPT stop
    sleep 2
fi


# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*csmain_outeripv6=.*/csmain_outeripv6=${SERVERIPV6}/g
    s/^.*csmain_outerip=.*/csmain_outerip=${SERVERIP}/g
    s/^.*cloud_env=.*/cloud_env=${CLOUD_ENV}/g
    s/^.*csmain_outer_domain=.*/csmain_outer_domain=${CSMAIN_SERVER_DOMAIN}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*cssession_net=.*/cssession_net=${CSSESSION_INNER_IP}:9002/g
    s/^.*csweb_net=.*/csweb_net=${WEB_OUTER_IP}/g
    s/^.*csweb_net_ipv6=.*/csweb_net_ipv6=${WEB_OUTER_IPV6}/g
    s/^.*config_server_ipv4=.*/config_server_ipv4=${CONFIG_OUTER_IP}/g
    s/^.*config_server_ipv6=.*/config_server_ipv6=${CONFIG_OUTER_IPV6}/g
    s/^.*config_server_domain=.*/config_server_domain=${CONFIG_SERVER_DOMAIN}/g
    s/^.*config_server_port=.*/config_server_port=${CONFIG_PORT}/g
    s/^.*cspush_net=.*/cspush_net=${PUSH_OUTER_IP}:8000/g
    s/^.*akcs_db_ip=.*/akcs_db_ip=${AKCS_MYSQL_INNER_IP}/g
    s/^.*log_db_ip=.*/log_db_ip=${LOG_MYSQL_INNER_IP}/g
    s/^.*mapping_db_ip=.*/mapping_db_ip=${AKCS_MYSQL_INNER_IP}/g
    s/^.*gateway_code=.*/gateway_code=${GATEWAY_NUM}/g
    s/^.*reg_etcd=.*/reg_etcd=${IS_REG_ETCD}/g
    s/^.*limit_switch=.*/limit_switch=${LIMIT_SWITCH}/g
    s/^.*rate=.*/rate=${RATE}/g
    s/^.*beanstalk_ip=.*/beanstalk_ip=${BEANSTALKD_IP}/g
    s/^.*is_aws=.*/is_aws=${IS_AWS}/g
    s/^.*apiurl=.*/apiurl=${API_URL}/g
    s/^.*nsq_linker_ip=.*/nsq_linker_ip=${CSLINKER_NSQD_IP}/g
    s/^.*server_area=.*/server_area=${SYSTEM_AREA}/g
    s/^.*csconfig_ip=.*/csconfig_ip=${csconfig_ip}/g
    s/^.*use_config_ip_mng=.*/use_config_ip_mng=${use_config_ip_mng}/g" "$PKG_ROOT"/conf/csmain.conf

# dbproxy 配置
if [ "$ENABLE_AKCS_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*akcs_db_port=.*/akcs_db_port=3308/g
        s/^.*akcs_db_ip=.*/akcs_db_ip=${AKCS_DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csmain.conf
else
    sed -i "
        s/^.*akcs_db_port=.*/akcs_db_port=3306/g
        s/^.*akcs_db_ip=.*/akcs_db_ip=${AKCS_MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csmain.conf
fi

if [ "$ENABLE_LOG_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*log_db_port=.*/log_db_port=3308/g
        s/^.*log_db_ip=.*/log_db_ip=${LOG_DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csmain.conf
else
    sed -i "
        s/^.*log_db_port=.*/log_db_port=3306/g
        s/^.*log_db_ip=.*/log_db_ip=${LOG_MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csmain.conf
fi

# redis 配置
sed -i "
    s/^.*appconf_host=.*/appconf_host=${REDIS_INNER_IP}/g
    s/^.*dev_outerip_host=.*/dev_outerip_host=${REDIS_INNER_IP}/g
    s/^.*appdnd_host=.*/appdnd_host=${REDIS_INNER_IP}/g
    s/^.*userdetail_host=.*/userdetail_host=${REDIS_INNER_IP}/g
    s/^.*appstat_host=.*/appstat_host=${REDIS_INNER_IP}/g
    s/^.*weather_host=.*/weather_host=${REDIS_INNER_IP}/g
    s/^.*backend_limiting_host=.*/backend_limiting_host=${REDIS_INNER_IP}/g
    s/^.*sl20_lock_host=.*/sl20_lock_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csmain_redis.conf
# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csmain_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csmain_redis.conf
fi

# 选择不同 OEM 做不同处理
case "$SYSTEM_OEM" in
    1)
        echo "oem is Akuvox"
        OEM_NAME='Akuvox'
        PUSH_AESKEY='GW9t4bM-Sdjao8@1'
        ;;
	2)
        echo "oem is Discreet"
        OEM_NAME='Discreet'
        PUSH_AESKEY='29EzQeWr+27nB0Zk'
        ;;
    *)
        echo "oem is Default"
        OEM_NAME='Akuvox'
        PUSH_AESKEY=''
    ;;
esac

sed -i "
    s/^oem_name.*/oem_name=${OEM_NAME}/g
    s/^push_aeskey.*/push_aeskey=${PUSH_AESKEY}/g" "$PKG_ROOT"/conf/csmain.conf


echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

if [ -d /usr/local/akcs/csmain_scripts ]; then
    rm -rf /usr/local/akcs/csmain_scripts
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME
cp -f "$PKG_ROOT"/version $APP_HOME

cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi

ln -sf libcppkafka.so libcppkafka.so.0.4.0
ln -sf librdkafka.so librdkafka.so.1

cd "$PKG_ROOT"


# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts

# 添加到开机启动
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi

ulimit -c unlimited

echo '启动服务'
$APP_HOME/scripts/$CTRL_SCRIPT start
sleep 8

echo '检查服务的运行状态'
$APP_HOME/scripts/$CTRL_SCRIPT status

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi

echo "$APP_NAME install complete."

