#!/bin/bash
ACMD="$1"
CSMAIND_BIN='/usr/local/akcs/csmain/bin/csmain'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csmain()
{
    nohup $CSMAIND_BIN >/dev/null 2>&1 &
    echo "Start csmain successful"
    if [ -z "`ps -fe|grep "csmainrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csmain/scripts/csmainrun.sh >/dev/null 2>&1 &
    fi
}
stop_csmain()
{
    echo "Begin to stop csmainrun.sh"
    csmainrunid=`ps aux | grep -w csmainrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csmainrunid}" ];then
	    echo "csmainrun.sh is running at ${csmainrunid}, will kill it first."
	    kill -9 ${csmainrunid}
    fi
    echo "Begin to stop csmain"
    kill -9 `pidof csmain`
    sleep 2
    echo "Stop csmain successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 8501 | grep csmain | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csmain
    else
        echo "csmain is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 8501 | grep csmain | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csmain is already stopping"
    else
        stop_csmain
    fi
    ;;
  restart)
    stop_csmain
    sleep 1
    start_csmain
    ;;
  status)
    cnt=`ss -alnp | grep 8501 | grep csmain | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csmain is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csmain is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

