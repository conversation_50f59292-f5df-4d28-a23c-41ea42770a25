#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`

# Define functions
cmd_usage() {
  echo "usage: $0 dev_accsrv <mac>"
  echo "       $0 rebootDev <mac/all>"
  echo "       $0 setsendlog <1/0>"
  echo "       $0 requestDevReportStatus <mac>"
  echo "       $0 setTzMd <tz_md5> <tz_data_md5>"
  echo "       $0 pmOfflineNotify <enable>"
  echo "       $0 setMsgRateLimiterSwitch <switch> // 设置msg id限流开关"
  echo "       $0 getMsgRateLimiterConf    // 获取当前msg id限流配置"
  echo "       $0 reloadMsgRateLimiterConf // 重新加载msg id限流配置"
  echo "       $0 getReuestStatics <top_number>  // 获取msg id请求数量前几的数据"
  echo "       $0 setMsgRateLimiterConf <msg_id(0x0123)> <seconds(几秒)> <requests(几次)>"
  echo "       $0 setRequestStaticsSwitch <switch 0关闭 1开启>"
  echo "       $0 metrics"
  exit 0
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi

# Check command
if [[ "$1" == "dev_accsrv" ]]; then
  mac=$2
  echo "curl $SERVER_INNER_IP:9998/dev_accsrv?mac=$mac"
  curl $SERVER_INNER_IP:9998/dev_accsrv?mac=$mac
elif [[ "$1" == "rebootDev" ]]; then
  mac=$2
  echo "curl -X POST $SERVER_INNER_IP:9998/rebootDev -d '{\"mac\":\"$mac\"}'"
  curl -X POST $SERVER_INNER_IP:9998/rebootDev -d "{\"mac\":\"$mac\"}"
elif [[ "$1" == "setsendlog" ]]; then
  enable=$2
  echo "curl -X POST $SERVER_INNER_IP:9998/setsendlog?enable=$enable"
  curl -X POST $SERVER_INNER_IP:9998/setsendlog?enable=$enable
elif [[ "$1" == "requestDevReportStatus" ]]; then
  mac=$2
  echo "curl $SERVER_INNER_IP:9998/requestDevReportStatus?mac=$mac"
  curl $SERVER_INNER_IP:9998/requestDevReportStatus?mac=$mac
elif [[ "$1" == "setTzMd" ]]; then
  tz_md5=$2
  tz_data_md5=$3
  echo "curl -X POST $SERVER_INNER_IP:9998/setTzMd?tz_md5=$tz_md5&tz_data_md5=$tz_data_md5"
  curl -X POST $SERVER_INNER_IP:9998/setTzMd?tz_md5=$tz_md5&tz_data_md5=$tz_data_md5
elif [[ "$1" == "pmOfflineNotify" ]]; then
  enable=$2
  echo "curl -X POST $SERVER_INNER_IP:9998/pmOfflineNotify?enable=$enable"
  curl -X POST $SERVER_INNER_IP:9998/pmOfflineNotify?enable=$enable
elif [[ "$1" == "setMsgRateLimiterSwitch" ]]; then
  switch=$2
  echo "curl -X POST $SERVER_INNER_IP:9998/setMsgRateLimiterSwitch?switch=$switch"
  curl -X POST "$SERVER_INNER_IP:9998/setMsgRateLimiterSwitch?switch=$switch"
elif [[ "$1" == "getMsgRateLimiterConf" ]]; then
  echo "curl -X POST $SERVER_INNER_IP:9998/getMsgRateLimiterConf"
  curl -X POST "$SERVER_INNER_IP:9998/getMsgRateLimiterConf"
elif [[ "$1" == "reloadMsgRateLimiterConf" ]]; then
  echo "curl -X POST $SERVER_INNER_IP:9998/reloadMsgRateLimiterConf"
  curl -X POST "$SERVER_INNER_IP:9998/reloadMsgRateLimiterConf"
elif [[ "$1" == "getReuestStatics" ]]; then
  top_number=$2
  echo "curl -X POST $SERVER_INNER_IP:9998/getReuestStatics?top_number=$top_number?"
  curl -X POST "$SERVER_INNER_IP:9998/getReuestStatics?top_number=$top_number"
elif [[ "$1" == "setMsgRateLimiterConf" ]]; then
  msg_id=$2
  seconds=$3
  requests=$4
  echo "curl -X POST $SERVER_INNER_IP:9998/setMsgRateLimiterConf?seconds=$seconds&requests=$requests&msg_id=$msg_id"
  curl -X POST "$SERVER_INNER_IP:9998/setMsgRateLimiterConf?seconds=$seconds&requests=$requests&msg_id=$msg_id"
elif [[ "$1" == "setRequestStaticsSwitch" ]]; then
  switch=$2
  echo "curl -X POST $SERVER_INNER_IP:9998/setRequestStaticsSwitch?switch=$switch"
  curl -X POST "$SERVER_INNER_IP:9998/setRequestStaticsSwitch?switch=$switch"
elif [[ "$1" == "metrics" ]]; then
  echo "curl -X GET $SERVER_INNER_IP:9998/metrics"
  curl -X GET "$SERVER_INNER_IP:9998/metrics"  
else
  cmd_usage
fi