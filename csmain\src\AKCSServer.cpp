#include "stdafx.h"
#include <string.h>
#include <signal.h>
#include "AkcsServer.h"
#include "ConnectionPool.h"
#include "LogConnectionPool.h"
#include "MappingConnectionPool.h"
#include "NotifyMsgControl.h"
#include "ConfigFileReader.h"
#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evnsq/producer.h>
#include "PushClient.h"
#include "RouteMqProduce.h"
#include "rpc_client.h"
#include "session_rpc_client.h"
#include "RouteClient.h"
#include "RouteClientMng.h"
#include "AkcsMonitor.h"
#include "evpp/rate_limiter/rate_limiter_interface.h"
#include "dbinterface/SystemSettingTable.h"
#include "DevOnlineMng.h"
#include "PushClientMng.h"
#include "dbinterface/Log/LogSlice.h"
#include "zipkin/ZipkinConf.h"
#include "json/json.h"
#include "MsgRateLimiterConf.h"
#include "MsgIdToMsgName.h"
#include "util.h"
#include <KdcDecrypt.h>
#include "loop/RouteLoopManager.h"

#define MAX_RLDB_CONN 10
extern AKCS_CONF gstAKCSConf;
extern MessageRateLimitMap gstAKCSMsgRateLimitConf;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern const char *g_conf_db_addr;
extern const char *g_ak_srv_session;
extern const char *g_ak_srv_nsqlookupd;
extern const char *g_ak_srv_adapt;
extern const char *g_ak_srv_route;
extern const char *g_ak_srv_cspush;
extern const char *g_ak_srv_smg_alexa;

RouteMQProduce* g_nsq_producer = nullptr;
extern std::string g_logic_srv_id; //服务id,etcd,cssession,redis均会用到

extern VideoStorageClient* g_vs_client_ptr;
extern SmRpcClient* g_sm_client_ptr;
static evpp::PipeEventWatcher* ev_csroute = nullptr;
static evpp::PipeEventWatcher* ev_csadapt = nullptr;
static evpp::PipeEventWatcher*  ev_cssession = nullptr;
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);
static int64_t csmain_inner_srv_leaseid = 0;
static int64_t csmain_outer_srv_leaseid = 0;
std::string g_csadapt_client_ip = "";//ip类型，没有port
extern bool g_etcd_ready;
extern bool g_access_srv_ready;
extern std::map<string, AKCS_DST> g_time_zone_DST;  
extern LOG_DELIVERY gstAKCSLogDelivery;
extern CAkEtcdCliManager* g_etcd_zipkinconf_cli;
extern const char *g_ak_srv_zipkin_kafka;

enum ETCD_UPDATE_SRV_TYPE
{
    ETCD_UPDATE_SRV_TYPE_ROUTE,
    ETCD_UPDATE_SRV_TYPE_ADAPT,
    ETCD_UPDATE_SRV_TYPE_SESSION,

};

int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip), conf_tmp.db_ip);
    gstAKCSConf.akcs_db_port = conf_tmp.db_port;
    return 0;
}

void ConfSrvInit()
{
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
    
    
    g_etcd_zipkinconf_cli = g_etcd_cli_mng;
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_zipkin_kafka, UpdateZipkinConfFromConfSrv);
    //从配置中心获取 初始化zipkin配置
    UpdateZipkinConfFromConfSrv();
}

int LogDeliveryInit()
{
    gstAKCSLogDelivery.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    gstAKCSLogDelivery.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    gstAKCSLogDelivery.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    if(gstAKCSLogDelivery.personal_capture_delivery == 0 || gstAKCSLogDelivery.personal_motion_delivery == 0 || gstAKCSLogDelivery.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}

void MsgRateLimitConfInit()
{ 
    std::ifstream filepath("/usr/local/akcs/csmain/conf/msg_rate_limit.json");
    if (!filepath.is_open()) 
    {
        AK_LOG_INFO << "MsgRateLimitConfInit open msg_rate_limit conf failed";
        return;
    }

    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(filepath, root)) 
    {
        AK_LOG_INFO << "MsgRateLimitConfInit Failed to parse JSON";
        return;
    }
	
    // 读取 JSON 数据
    for (const auto& msg : root.getMemberNames()) 
    {
        std::string msg_id = root[msg]["msg_id"].asString();
		std::string mac_list = root[msg]["mac_list"].asString();
		
        MessageRateLimitConf rate_limit_conf(static_cast<uint64_t>(root[msg]["seconds"].asInt()), static_cast<uint64_t>(root[msg]["requests"].asInt()));
        rate_limit_conf.AddMacList(mac_list);
		
        gstAKCSMsgRateLimitConf.insert({msg_id, rate_limit_conf});
        AK_LOG_INFO << "MsgRateLimitConfInit msg = " << msg << ", msg_id = " << msg_id << ", msgname = " << MsgIdToMsgName::GetDeclientMessageName(ATOI(msg_id.c_str())) << ", seconds = " << rate_limit_conf.Seconds() 
					<< ", requests = " << rate_limit_conf.Requests() << ", mac_list = " << mac_list;
    }

    for (const auto& msg_rate_limit : gstAKCSMsgRateLimitConf)
    {
        AK_LOG_INFO << "msg_id = " << msg_rate_limit.first << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(ATOI(msg_rate_limit.first.c_str())) << ", seconds = " << msg_rate_limit.second.Seconds() << ", requests = " << msg_rate_limit.second.Requests();

		auto mac_list = msg_rate_limit.second.MacList();
		for (auto& mac :  mac_list) 
		{
			AK_LOG_INFO  << "mac = " << mac;
		}
    }
    
    return;
}

uint32_t ConfInit()
{

    CConfigFileReader oss_config_file("/etc/oss_install.conf");
    Snprintf(gstAKCSConf.oss_bucket, sizeof(gstAKCSConf.oss_bucket), oss_config_file.GetConfigName("BucketForFace"));
    Snprintf(gstAKCSConf.oss_role_arn, sizeof(gstAKCSConf.oss_role_arn), oss_config_file.GetConfigName("RoleArn"));
    Snprintf(gstAKCSConf.oss_outer_endpoint, sizeof(gstAKCSConf.oss_outer_endpoint), oss_config_file.GetConfigName("OuterEndpoint"));
    Snprintf(gstAKCSConf.oss_sts_endpoint, sizeof(gstAKCSConf.oss_sts_endpoint), oss_config_file.GetConfigName("StsEndpoint"));
    Snprintf(gstAKCSConf.oss_region_id, sizeof(gstAKCSConf.oss_region_id), oss_config_file.GetConfigName("RegionID"));

    CConfigFileReader config_file("/usr/local/akcs/csmain/conf/csmain.conf");
    Snprintf(gstAKCSConf.cloud_env, sizeof(gstAKCSConf.cloud_env), config_file.GetConfigName("cloud_env"));
    Snprintf(gstAKCSConf.csmain_outer_ip, sizeof(gstAKCSConf.csmain_outer_ip), config_file.GetConfigName("csmain_outerip"));
    Snprintf(gstAKCSConf.csmain_outer_domain, sizeof(gstAKCSConf.csmain_outer_domain), config_file.GetConfigName("csmain_outer_domain"));
    Snprintf(gstAKCSConf.csmain_outer_ipv6, sizeof(gstAKCSConf.csmain_outer_ipv6), config_file.GetConfigName("csmain_outeripv6"));

    //char* enable_ipv6 = config_file.GetConfigName("ipv6_enable");
    //gstAKCSConf.enable_ipv6 = ATOI(enable_ipv6);

    const char* log_level = config_file.GetConfigName("csmain_loglevel");
    gstAKCSConf.log_level = ATOI(log_level);
    Snprintf(gstAKCSConf.log_file, COMMON_STR_LEN, "/var/log/csmainlog/csmain00.log");
    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username), config_file.GetConfigName("db_username"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), decrypt_db_passwd.c_str());
    
    Snprintf(gstAKCSConf.push_server_addr, sizeof(gstAKCSConf.push_server_addr), config_file.GetConfigName("cspush_net"));
    Snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));

    //网关编号
    Snprintf(gstAKCSConf.gateway_code, sizeof(gstAKCSConf.gateway_code), config_file.GetConfigName("gateway_code"));

    // 数据库ip
    Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip), config_file.GetConfigName("akcs_db_ip"));
    Snprintf(gstAKCSConf.log_db_ip, sizeof(gstAKCSConf.log_db_ip), config_file.GetConfigName("log_db_ip"));
    Snprintf(gstAKCSConf.mapping_db_ip, sizeof(gstAKCSConf.mapping_db_ip), config_file.GetConfigName("mapping_db_ip"));

    // 数据库端口
    const char* akcs_db_port = config_file.GetConfigName("akcs_db_port");
    gstAKCSConf.akcs_db_port = ATOI(akcs_db_port);
    const char* log_db_port = config_file.GetConfigName("log_db_port");
    gstAKCSConf.log_db_port = ATOI(log_db_port);
    const char* mapping_db_port = config_file.GetConfigName("mapping_db_port");
    gstAKCSConf.mapping_db_port = ATOI(mapping_db_port);

    // 数据库名称
    Snprintf(gstAKCSConf.akcs_db_database, sizeof(gstAKCSConf.akcs_db_database), config_file.GetConfigName("akcs_db_database"));
    Snprintf(gstAKCSConf.log_db_database, sizeof(gstAKCSConf.log_db_database), config_file.GetConfigName("log_db_database"));
    Snprintf(gstAKCSConf.mapping_db_database, sizeof(gstAKCSConf.mapping_db_database), config_file.GetConfigName("mapping_db_database"));

    Snprintf(gstAKCSConf.web_server_addr, sizeof(gstAKCSConf.web_server_addr), config_file.GetConfigName("csweb_net"));
    Snprintf(gstAKCSConf.web_server_ipv6_addr, sizeof(gstAKCSConf.web_server_ipv6_addr), config_file.GetConfigName("csweb_net_ipv6"));
    Snprintf(gstAKCSConf.video_server_addr, sizeof(gstAKCSConf.video_server_addr), config_file.GetConfigName("csvs_net"));
    Snprintf(gstAKCSConf.oem_name, sizeof(gstAKCSConf.oem_name), config_file.GetConfigName("oem_name"));
    if (!strcasecmp(gstAKCSConf.oem_name, "AKUVOX"))
    {
        gstAKCSConf.oem_num = OEM_AKUVOX;
    }
    else if (!strcasecmp(gstAKCSConf.oem_name, "DISCREET"))
    {
        gstAKCSConf.oem_num = OEM_DISCREET;
    }
    Snprintf(gstAKCSConf.push_AESkey, sizeof(gstAKCSConf.push_AESkey), config_file.GetConfigName("push_aeskey"));

    const char* video_length = config_file.GetConfigName("video_length");
    gstAKCSConf.video_length = ATOI(video_length);

    const char* tmp = config_file.GetConfigName("cliserver_port");
    gstAKCSConf.client_server_port = ATOI(tmp);
    Snprintf(gstAKCSConf.client_server_allow_ip, sizeof(gstAKCSConf.client_server_allow_ip), config_file.GetConfigName("cliserver_allowip"));

    Snprintf(gstAKCSConf.nsq_topic, sizeof(gstAKCSConf.nsq_topic), config_file.GetConfigName("nsq_route_topic"));
    Snprintf(gstAKCSConf.beanstalk_ip, sizeof(gstAKCSConf.beanstalk_ip), config_file.GetConfigName("beanstalk_ip"));
    const char* beanstalk_port = config_file.GetConfigName("beanstalk_port");
    gstAKCSConf.beanstalk_port = ATOI(beanstalk_port);

    Snprintf(gstAKCSConf.svn_version, sizeof(gstAKCSConf.svn_version), config_file.GetConfigName("svn_version"));

    const char* reg_etcd = config_file.GetConfigName("reg_etcd");
    gstAKCSConf.reg_etcd = ATOI(reg_etcd);

    gstAKCSConf.upgrade_status = fileExist("/tmp/system.upgrade") ? 1 : 0;

    Snprintf(gstAKCSConf.tz_md5, sizeof(gstAKCSConf.tz_md5), config_file.GetConfigName("tz_md5"));
    Snprintf(gstAKCSConf.tz_data_md5, sizeof(gstAKCSConf.tz_data_md5), config_file.GetConfigName("tz_data_md5"));
    //除了R29/x916外，其他都下发这个，不进行tzdata更新。因为别的设备没有支持，会导致每次重连都下载这个文件
    Snprintf(gstAKCSConf.tz_data_md5_old, sizeof(gstAKCSConf.tz_data_md5_old), config_file.GetConfigName("tz_data_md5_old"));
    gstAKCSConf.offline_notify = 1;

    const uint32_t kDefaultRateLimitQps = 200;
    const char *limit_switch = config_file.GetConfigName("limit_switch");
    if (strlen(limit_switch) > 0)
    {
        gstAKCSConf.limit_switch = ATOI(limit_switch);
    }
    else
    {
        gstAKCSConf.limit_switch = evpp::rate_limiter::NO_LIMIT;
        gstAKCSConf.rate = kDefaultRateLimitQps;
    }
    
    const char *rate = config_file.GetConfigName("rate");
    if (strlen(rate) > 0)
    {
        gstAKCSConf.rate = (ATOI(rate) <= 0) ? kDefaultRateLimitQps : ATOI(rate);
    }
    else
    {
        gstAKCSConf.rate = kDefaultRateLimitQps;
    }
    Snprintf(gstAKCSConf.apiurl, sizeof(gstAKCSConf.apiurl), config_file.GetConfigName("apiurl"));

    Snprintf(gstAKCSConf.config_server_ipv4, sizeof(gstAKCSConf.config_server_ipv4), config_file.GetConfigName("config_server_ipv4"));
    Snprintf(gstAKCSConf.config_server_ipv6, sizeof(gstAKCSConf.config_server_ipv6), config_file.GetConfigName("config_server_ipv6"));
    Snprintf(gstAKCSConf.config_server_domain, sizeof(gstAKCSConf.config_server_domain), config_file.GetConfigName("config_server_domain"));

    gstAKCSConf.config_server_domain_gray_percentage = ATOI(config_file.GetConfigName("config_server_domain_gray_percentage"));

    const char *tmp_port = config_file.GetConfigName("config_server_port");
    gstAKCSConf.config_server_port = ATOI(tmp_port);
    const char *tlshigh_port = config_file.GetConfigName("config_server_tlshigh_port");
    gstAKCSConf.config_server_tlshigh_port = ATOI(tlshigh_port);
    
    const char* is_aws = config_file.GetConfigName("is_aws");
    gstAKCSConf.is_aws = ATOI(is_aws);

    tmp = config_file.GetConfigName("download_user_timeout");
    gstAKCSConf.download_user_timeout = ATOI(tmp);    

    const char* stress_test = config_file.GetConfigName("stress_test");
    gstAKCSConf.stress_test = ATOI(stress_test);

    Snprintf(gstAKCSConf.linker_nsq_topic, sizeof(gstAKCSConf.linker_nsq_topic), config_file.GetConfigName("nsq_linker_topic"));

    //现阶段一样，后面会改
    Snprintf(gstAKCSConf.voice_server_ipv4, sizeof(gstAKCSConf.voice_server_ipv4), config_file.GetConfigName("config_server_ipv4"));
    Snprintf(gstAKCSConf.voice_server_ipv6, sizeof(gstAKCSConf.voice_server_ipv6), config_file.GetConfigName("config_server_ipv6"));
    
    Snprintf(gstAKCSConf.linker_nsq_ip, sizeof(gstAKCSConf.linker_nsq_ip), config_file.GetConfigName("nsq_linker_ip"));

    const char* area = config_file.GetConfigName("server_area");
    gstAKCSConf.server_area = ATOI(area);

    const char* call_no_limit = config_file.GetConfigName("call_no_limit");
    gstAKCSConf.call_no_limit = ATOI(call_no_limit);

    ::strncpy(gstAKCSConf.use_config_ip_mng, config_file.GetConfigName("use_config_ip_mng"), sizeof(gstAKCSConf.use_config_ip_mng));
    ::strncpy(gstAKCSConf.csconfig_ip, config_file.GetConfigName("csconfig_ip"), sizeof(gstAKCSConf.csconfig_ip));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    const char* limiting_timeout = config_file.GetConfigName("limiting_timeout");
    gstAKCSConf.limiting_timeout = ATOI(limiting_timeout);  

    const char* sl20_opendoor_expire = config_file.GetConfigName("sl20_opendoor_expire");
    gstAKCSConf.sl20_opendoor_expire = ATOI(sl20_opendoor_expire);

    const char* msg_id_limit_switch = config_file.GetConfigName("msg_id_limitswitch");
    gstAKCSConf.msg_id_limit_switch = ATOI(msg_id_limit_switch);    

    gstAKCSConf.request_statics_switch = ATOI(config_file.GetConfigName("request_statics_switch"));
    gstAKCSConf.msg_lantency_metric = ATOI(config_file.GetConfigName("msg_lantency_metric"));

    
    return 0;
}

/*
static void QuitJob()
{
    //清理本逻辑服务器sid的租约, TODO,用etcd.rm来处理即可
    g_etcd_cli_mng->LeaseRevoke();
    AK_LOG_WARN << "I'm ready quit...";
}*/

/*
static void StopProcess(int signo)
{
    AK_LOG_WARN << "receive signal:" << signo;
    switch (signo)
    {
        //典型的终止进程的几个信号
        case SIGINT:
        case SIGTERM:
        case SIGQUIT:
        //case SIGHUP:
        case SIGSEGV:
        case SIGABRT:
        case SIGKILL:
        {
            QuitJob();
            ::_exit(0);
            break;
        }
    }
}
*/

void SignalInit()
{
    ::signal(SIGPIPE, SIG_IGN);
    //modified by chenyc, 2019-05-30,etcd永久生效了,不需要这个了.
#if 0
    ::signal(SIGINT, StopProcess);
    ::signal(SIGTERM, StopProcess);
    ::signal(SIGQUIT, StopProcess);
    //::signal(SIGHUP, StopProcess);
    ::signal(SIGKILL, StopProcess);
    ::signal(SIGSEGV, StopProcess);
    ::signal(SIGABRT, StopProcess);
#endif
}

void ServerTagInit()
{
    snprintf(gstAKCSConf.server_tag, sizeof(gstAKCSConf.server_tag), "%s", dbinterface::SystemSetting::GetServerTag().c_str());
}

/*
程序配置文件问题：
配置文件全局变量，被多个线程读取，而又有重新读取配置文件的操作。
那么重新读取时候，又被别的线程试用怎么办
*/
uint32_t ConfReInitPushServer()
{
    CConfigFileReader config_file("/usr/local/akcs/csmain/conf/csmain.conf");

    Snprintf(gstAKCSConf.push_server_addr, sizeof(gstAKCSConf.push_server_addr), config_file.GetConfigName("cspush_net"));
    Snprintf(gstAKCSConf.oem_name, sizeof(gstAKCSConf.oem_name), config_file.GetConfigName("oem_name"));
    if (!strcasecmp(gstAKCSConf.oem_name, "AKUVOX"))
    {
        gstAKCSConf.oem_num = OEM_AKUVOX;
    }
    else if (!strcasecmp(gstAKCSConf.oem_name, "DISCREET"))
    {
        gstAKCSConf.oem_num = OEM_DISCREET;
    }
    Snprintf(gstAKCSConf.push_AESkey, sizeof(gstAKCSConf.push_AESkey), config_file.GetConfigName("push_aeskey"));
    return 0;

}


/* 初始化控制器服务 */
uint32_t ControlInit()
{
    GetControlInstance()->Init();
    GetNotifyMsgControlInstance()->Init();
    GetHttpReqMsgControlInstance()->Init();
    GetMotionMsgControlInstance()->Init();
	GetRtspMsgControlInstance()->Init();
    DevOnlineMng::GetInstance()->Init();
    //GetIPCUnixControlInstance()->Init();
    return 0;
}

/* 初始化数据库连接 */
int DaoInit()
{
    LoadConfFromConfSrv();
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstAKCSConf.akcs_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.akcs_db_database, gstAKCSConf.akcs_db_port, MAX_RLDB_CONN, "csmain");

    LogConnPool* log_conn_pool = GetLogDBConnPollInstance();
    if (NULL == log_conn_pool)
    {
        AK_LOG_WARN << "LOG DaoInit failed.";
        return -1;
    }
    log_conn_pool->Init(gstAKCSConf.log_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.log_db_database, gstAKCSConf.log_db_port, MAX_RLDB_CONN, "csmain");

    MappingConnPool* mapping_conn_pool = GetMappingDBConnPollInstance();
    if (NULL == mapping_conn_pool)
    {
        AK_LOG_WARN << "Mapping DaoInit failed.";
        return -1;
    }
    mapping_conn_pool->Init(gstAKCSConf.mapping_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.mapping_db_database, gstAKCSConf.mapping_db_port, MAX_RLDB_CONN, "csmain");

    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstAKCSConf.akcs_db_ip, gstAKCSConf.akcs_db_port);
    return 0;
}

/* 主动断开数据库连接 */
uint32_t DaoRelease()
{
    return 0;
}

static void PushSrvConnInit(const std::set<std::string> &cspush_addr, evpp::EventLoop* loop)
{
    for (const auto& cspush : cspush_addr) //ip:port的形式
    {
        PushClientPtr push_cli_ptr(new CPushClient(loop, cspush, "csroute push client"));
        push_cli_ptr->Start();
        CPushClientMng::Instance()->AddPushSrv(cspush, push_cli_ptr);
    }

}

//与所有的csroute建立tcp连接
static void RouteSrvConnInit(const std::set<std::string>& csroute_addrs, evpp::EventLoop* loop,
                             const std::string& logic_srv_id)
{
    for (const auto& csroute : csroute_addrs) //ip:port的形式
    {
        RouteClientPtr route_cli_ptr(new CRouteClient(loop, csroute, "csmain client", logic_srv_id));
        route_cli_ptr->Start();
        CRouteClientMng::Instance()->AddRouteSrv(csroute, route_cli_ptr);
    }
}

static void SessionSrvConnInit(const std::set<std::string>& cssession_addrs)
{
    std::string cssesson_str = "session list:";
    std::vector<AddressData> addresses;
    AddressData addr_tmp;
    for (const auto& cssesson : cssession_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = cssesson.find(":");
        if (std::string::npos != pos)
        {
            ip = cssesson.substr(0, pos);
            port = cssesson.substr(pos + 1);
        }
        addresses.emplace_back(AddressData{ATOI(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
        cssesson_str += cssesson;
        cssesson_str += " ";
    }
    g_sm_client_ptr->SetNextResolution(addresses);
    AK_LOG_INFO << cssesson_str;
}

void UpdateSessionSrvList()
{
    std::set<std::string> cssession_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) == 0)
    {
        SessionSrvConnInit(cssession_addrs);
    }

}

void UpdateRouteSrvList()
{
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) == 0)
    {
        AK_LOG_INFO << "UpdateRouteSrv begin";
        //更新route的连接列表
        CRouteClientMng::Instance()->UpdateRouteSrv(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), g_logic_srv_id);
        AK_LOG_INFO << "UpdateRouteSrv end";
    }
}

void UpdateAdaptSrvList()
{
    std::string csadapt_addr = g_etcd_cli_mng->GetRandomAdaptSrv(); //csdapt连接任意一个
    if (!csadapt_addr.empty())
    {
        std::string::size_type pos = csadapt_addr.find(":");
        if (std::string::npos != pos)
        {
            g_csadapt_client_ip = csadapt_addr.substr(0, pos);
        }
    }
}

void UpdatePushSrvList()
{
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) == 0)
    {
        AK_LOG_INFO << "UpdatePushSrv begin";
        //更新route的连接列表
        CPushClientMng::Instance()->UpdatePushSrv(cspush_addrs, g_etcd_loop.get());
        AK_LOG_INFO << "UpdatePushSrv end";
    }
}

// alexa
void UpdateSmgAlexaConfSrv()
{
    std::string smg_alexa_addr;
    g_etcd_cli_mng->LoadSrvSmgAlexaConf(smg_alexa_addr);
    Snprintf(gstAKCSConf.smg_alexa_addr, sizeof(gstAKCSConf.smg_alexa_addr), smg_alexa_addr.c_str());
}

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}


void EtcdRegCsmainOuterInfo()
{
    const char *csmain_port = "8501";
    char outer_addr[256] = {0};
    ::snprintf(outer_addr, sizeof(outer_addr), "%s:%s&[%s]:%s&%s:%s", gstAKCSConf.csmain_outer_ip, csmain_port, gstAKCSConf.csmain_outer_ipv6, csmain_port, gstAKCSConf.csmain_outer_domain, csmain_port);
    char outer_reg_info[256] = {0};
    ::snprintf(outer_reg_info, sizeof(outer_reg_info), "%s%s", "/akcs/csmain/outerip/", gstAKCSConf.csmain_outer_ip);
    RegSrv2Etcd(outer_reg_info, outer_addr, 10, csbase::REG_OUTER, g_etcd_loop.get());
}

void EtcdRegCsmainInnerInfo()
{
    std::string inner_addr = GetEth0IPAddr();
    char inner_rpc_addr[256] = {0};
    ::snprintf(inner_rpc_addr, sizeof(inner_rpc_addr), "%s:%s", inner_addr.c_str(), "8506");
    char inner_reg_info[256] = {0};
    ::snprintf(inner_reg_info, sizeof(inner_reg_info), "%s%s", "/akcs/csmain/innerip/rpc/", gstAKCSConf.csmain_outer_ip);//注册内网rpc服务地址
    RegSrv2Etcd(inner_reg_info, inner_rpc_addr, 10, csbase::REG_INNER, g_etcd_loop.get());
}

//含csroute cspush smg的连接
void EtcdSrvInit()
{
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    PushSrvConnInit(cspush_addrs, g_etcd_loop.get());

    if (strlen(gstAKCSConf.push_server_addr) > 5)
    {
        PushClientPtr push_cli_ptr(new CPushClient(g_etcd_loop.get(), gstAKCSConf.push_server_addr, "csmain push client"));
        push_cli_ptr->Start();
        CPushClientMng::Instance()->AddOuterPushSrv(push_cli_ptr);
    }

    //csadapt
    std::string csadapt_addr = g_etcd_cli_mng->GetRandomAdaptSrv(); //csdapt连接任意一个
    if (!csadapt_addr.empty())
    {
        std::string::size_type pos = csadapt_addr.find(":");
        if (std::string::npos != pos)
        {
            g_csadapt_client_ip = csadapt_addr.substr(0, pos);
        }
    }

    //csroute
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    //创建独立的 route loop

    RouteSrvConnInit(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), g_logic_srv_id);

    GetRouteLoopManagerInstance()->StartLoop();

    //cssession
    std::set<std::string> cssession_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    SessionSrvConnInit(cssession_addrs);

    //smg alexa
    UpdateSmgAlexaConfSrv();
    
    //added by chenyc,v5.0,2019-12-26,标示所有的下游服务已经对接完成,通知主线程启动access接入服务
    g_etcd_ready = 1;

    //等待接入服务启动完成,再启动csroute的loop,让csmain去建立与csroute的长连接
    while (!g_access_srv_ready)
    {
        usleep(100 * 1000);
    }

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_session, UpdateSessionSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_route, UpdateRouteSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_adapt, UpdateAdaptSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_cspush, UpdatePushSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_smg_alexa, UpdateSmgAlexaConfSrv);
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get());

    g_etcd_loop->Run();//etcd_loop 目前只有route的连接在用  

}
void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.akcs_db_ip) != 0) || (conf_tmp.db_port != gstAKCSConf.akcs_db_port))
    {
        Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip), conf_tmp.db_ip);
        gstAKCSConf.akcs_db_port = conf_tmp.db_port;
        DaoReInit();
    }
}

void MQProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    //op.auth_secret = auth_secret;
    evnsq::Producer client(&nsq_loop, op);
    client.SetConnectErrorCallback(&OnConnectError);
    client.SetMessageCallback(&OnRouteMQMessage);//基本不需要关心
    client.SetReadyCallback(&OnNSQReady);//ready(与其中一个nsqd服务tcp连接上之后)之后才能开始发布消息.
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    g_nsq_producer = new RouteMQProduce(&client);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}
void SMRPCClientInit()
{
    g_sm_client_ptr->AsyncCompleteRpc();
}
