/*
 * akcs服务,主要代码框架从sdmcserver移植过来
 * Author:yicong.chen
 * DateTime:2016-12-19
 */

#include "stdafx.h"
#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <signal.h>
#include <unistd.h>
#include <sys/stat.h>
#include <errno.h>
#include <signal.h>
#include "Control.h"
#include "AkcsServer.h"
#include "HttpServer.h"
#include "HttpOuterServer.h"
#include <evpp/tcp_client.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "PushClient.h"
#include "rpc_client.h"
#include "VideoSchedMng.h"
#include "csmainserver.h"
#include "CliServer.h"
#include "DeviceControl.h"
#include "util.h"
#include "session_rpc_client.h"
#include "AKUserMng.h"
#include "CachePool.h"
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "grpc_balancer_service.h"
#include "csmain_rpc_server.h"
#include "AKUpgradeMonitor.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket.h"
#include "EventFilterWriteFileImpl.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "ConfigFileReader.h"
#include "dbinterface/ProjectUserManage.h"
#include "DevOnlineMng.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "Main2ResidHandle.h"
#include "AkcsAppInit.h"
#include "EtcdDns.h"
#include "dbinterface/Log/LogSlice.h"
#include "zipkin/ZipkinAsyn.h"
#include "util_time.h"
#include "MsgRateLimiterConf.h"
#include "Metric.h"
#include "BusinessThreadPool.h"

AccessServer* g_accSer_ptr = nullptr;
CliServer*  g_cliSer_prt = nullptr;

std::map<string, AKCS_DST> g_time_zone_DST;  
AKCS_CONF gstAKCSConf; //全局配置信息
MessageRateLimitMap gstAKCSMsgRateLimitConf; // msg id限流配置
VideoStorageClient* g_vs_client_ptr = nullptr;
SmRpcClient* g_sm_client_ptr = nullptr;
std::string g_logic_srv_id;
Beanstalk::Client* g_beanstalkd_client_ptr = nullptr;
bool g_etcd_ready = 0;
bool g_access_srv_ready = 0;
evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter = nullptr;
akcs::CEventFilterInterface *g_event_filter = nullptr;
char g_stress_test[64] = "/home/<USER>/stress_test_result";
extern CAkEtcdCliManager* g_etcd_cli_mng;
LOG_DELIVERY gstAKCSLogDelivery;

#define CSMAIN_IO_THREAD_NUM (4)
BusinessThreadPool g_business_pool(CSMAIN_IO_THREAD_NUM);


#define PIDFILE "/var/run/csmain.pid"

int RedisInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/csmain/conf/csmain_redis.conf", "csmainCacheInstances");
}

int RpcServerInit()
{
    //grpc
    MainRpcServer rpc_server("8506");
    rpc_server.Run();
	return 0;
}

int GlobalObjInit()
{
    g_rate_limiter = new evpp::rate_limiter::RateLimiterTokenBucket(gstAKCSConf.rate, 1);
    
#ifdef AKCS_EVENT_FILTER
    g_event_filter = new akcs::CEventFilterWriteFileImpl(g_stress_test);
#endif
    return 0;
}


void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void EtcdConnInit()
{
    g_etcd_dns_mng = new CEtcdDnsManager(gstAKCSConf.etcd_server_addr);
    std::thread dnsThread = std::thread(&CEtcdDnsManager::StartDnsResolver, g_etcd_dns_mng);
    while(!g_etcd_dns_mng->DnsIsOk())
    {
        usleep(10);
    }
    dnsThread.detach();
    //域名解析完才能初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_dns_mng->GetAddrs());
    g_etcd_dns_mng->SetEtcdCli(g_etcd_cli_mng);    
}

int main(int argc, char** argv)
{
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2("/var/run/csmain.pid"))
    {
        printf("another csmain has been running in this system.");
        return -1;
    }

    GlogInit2(argv[0], "csmainlog");
    int ret = ERR_COMM_FAIL;

    /* 读取配置文件 */
    ret = ConfInit();
    if (0 != ret)
    {
        AK_LOG_WARN << "ConfInit fialed";
        GlogClean2();
        return -1;
    }    

    // msg限流配置
    MsgRateLimitConfInit();
    
    //dns一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
    EtcdConnInit();
    
    ConfSrvInit();
    //added by chenyc,2021.12.22, 全局变量的初始化工作全部在这里完成，避免出现未初始化完成及引用的问题
    if(GlobalObjInit() != 0)
    {
        AK_LOG_FATAL << "Init global object failed";
        return -1;
    }

    ret = ParseTimeZone("/usr/local/akcs/csmain/conf/TimeZone.xml", g_time_zone_DST);
    if (0 != ret)
    {
        AK_LOG_WARN << "ParseTimeZone fialed";
        GlogClean2();
        return -1;
    }

    if (RedisInit() != 0)
    {
        AK_LOG_FATAL << "redis init failed";
        return -1;
    }    
    
    //nsq消息发布
    std::thread mqProduceThread = std::thread(MQProduceInit); 
    
    /* 初始化数据库连接 */
    ret = DaoInit();
    if (0 != ret)
    {
        AK_LOG_WARN << "DaoInit fialed.";
        GlogClean2();
        return -1;
    }
    //初始化信号处理
    SignalInit();
    //初始化server_tag
    ServerTagInit();
    /* 初始化服务控制器单例 */
    ret = ControlInit();
    if (0 != ret)
    {
        DaoRelease(); //释放数据库连接
        AK_LOG_WARN << "ControlInit fialed.";
        GlogClean2();
        return -1;
    }
    //获取srv id
    g_logic_srv_id = "csmain_";
    g_logic_srv_id += GetEth0IPAddr();

    //获取LOG库日志表分片数
    if(LogDeliveryInit() != 0)
    {
        AK_LOG_WARN << "LogDeliveryInit fialed.";
        GlogClean2();
        return -1;
    }

    //记载视频录制计划
    ret = CVideoSchedMng::Instance()->Init();
    if (0 != ret)
    {
        AK_LOG_WARN << "CVideoSchedMng Init fialed.";
        GlogClean2();
        DaoRelease(); //释放数据库连接
        return -1;
    }
    //更新数据库设备连接状态为未连接
    std::string logic_srv_ip = GetEth0IPAddr();
    dbinterface::ResidentDevices::SetAllDevDisconnect(logic_srv_ip);

    //起http维护通道线程，仅支持内网访问
    std::thread httpServerThread(startHttpServer); //port = 9998

    //起http维护通道线程，支持外网访问
    std::thread httpOuterServerThread;
    if(IsTestServer(gstAKCSConf.cloud_env)){
        httpOuterServerThread = std::thread(startHttpOuterServer); //port = 8814
    }

    //初始化rpc客户端通道
    std::string vs_srv_net = gstAKCSConf.video_server_addr;
    //另一种写法: CreateCustomChannel
    g_vs_client_ptr = new VideoStorageClient(grpc::CreateChannel(
                vs_srv_net, grpc::InsecureChannelCredentials()));

    g_sm_client_ptr = new SmRpcClient();
    std::string cssession_client_node = "csmain_" + std::string(gstAKCSConf.csmain_outer_ip);
    g_sm_client_ptr->RegisterNode(cssession_client_node);

    //在另一个线程里面获取到异步rpc结果,TODO,后续所有rpc的异步结果获取需要合并起来..
    std::thread rpcVsClientThread = std::thread(&VideoStorageClient::AsyncCompleteRpc, g_vs_client_ptr);
    std::thread rpcSmClientThread = std::thread(SMRPCClientInit);

    //etcd,获取cspush\csroute\session地址，并监控其变化
    std::thread etcdCliThread = std::thread(EtcdSrvInit);


    //初始化业务限制-延迟队列producer
    g_beanstalkd_client_ptr = new Beanstalk::Client(gstAKCSConf.beanstalk_ip, gstAKCSConf.beanstalk_port);
    g_beanstalkd_client_ptr->use(AKCS_ATTACK_IP_TUBE);

    //启动后等待etcd获取完服务器列表，再启动AccessServer, g_sm_client_ptr负载均衡要等待 etcd获取完列表
    while (!g_etcd_ready)
    {
        usleep(100 * 1000);
    }

    std::thread linux_mq_thread = std::thread(std::bind(&Main2ResidMsgHandle::Start, Main2ResidMsgHandle::Instance()));

    //全部的准备工作全部做好之后,再起tcpserver
    evpp::EventLoop loop;
    std::string addr;
    addr = "[::]:8501"; //modified by chenyc,直接监听在ipv6下面
    g_accSer_ptr = new AccessServer(&loop, addr, "AccessServer", CSMAIN_IO_THREAD_NUM);
    g_accSer_ptr->Start();
    
    //added by chenyc,v5.0 2019-12-26,通知csmain去建立与csroute的长连接
    g_access_srv_ready = 1;
    std::string addr_cli = GetEth0IPAddr();
    addr_cli += ":1025";
    g_cliSer_prt = new CliServer(&loop, addr_cli, "CliServer", 1);
    g_cliSer_prt->InitAllowIp(gstAKCSConf.client_server_allow_ip);
    g_cliSer_prt->Start();

    //rpc 服务
    std::thread rpcThread = std::thread(RpcServerInit);

    //服务器全部启动完全 把csmain注册到etcd
    if (gstAKCSConf.reg_etcd)
    {
        //把csmain外网地址注册到etcd
        EtcdRegCsmainOuterInfo();
    }
    //added by chenyc,2020-05-20,升级过程csmain监控设备端信令交互的文件
    AKCS::Singleton<UpgradeMonitor>::instance().Init(gstAKCSConf.upgrade_status);

    EtcdRegCsmainInnerInfo();
    std::thread conf_watch_thread = std::thread(ConfWatch);

    GetZipkinAsynInstance()->Init();

    //初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csmain is starting";

    loop.Run();
    httpServerThread.join();
    if(httpOuterServerThread.joinable()){
        httpOuterServerThread.join();
    }
    
    rpcVsClientThread.join();
    rpcSmClientThread.join();
    conf_watch_thread.join();
    etcdCliThread.join();
    mqProduceThread.join();
    rpcThread.join();
    linux_mq_thread.join();
    GlogClean2();
    return 0;
}

