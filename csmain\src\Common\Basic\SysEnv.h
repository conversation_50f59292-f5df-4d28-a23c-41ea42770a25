#ifndef _SYS_ENV_H_
#define _SYS_ENV_H_

#ifdef __cplusplus
extern "C" {
#endif

//VOID GetCurTime(INOUT char *pszDate, INOUT uint32_t nSize);
int code_convert(const char* from_charset, char* inbuf, size_t inlen, const char* to_charset, char* outbuf, size_t outlen) ;
int AKCS_U2G(IN char* inbuf, IN size_t inlen, OUT char* outbuf, OUT size_t outlen);
int AKCS_G2U(IN char* inbuf, IN size_t inlen, OUT char* outbuf, OUT size_t outlen);

int GetLastError();

#ifdef __cplusplus
}
#endif
#endif //_SYS_ENV_H_