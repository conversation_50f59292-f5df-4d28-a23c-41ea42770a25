#include "stdafx.h"
#include "BasicDefine.h"
#include "CharChans.h"
#include <string.h>
#include <boost/scoped_ptr.hpp>
#include "util.h"

int TransUtf8ToTchar(const char* pszSrc, TCHAR* pszDst, int nDstSize)
{
    if (pszSrc == NULL
            || pszDst == NULL
            || nDstSize <= 0)
    {
        return -1;
    }
    Snprintf(pszDst, nDstSize, pszSrc);
    return 0;
}

int TransTcharToUtf8(const TCHAR* pszSrc, char* pszDst, int nDstSize)
{
    //不需要做转换 add chenzhx 20181012
    if (pszSrc == NULL
            || pszDst == NULL
            || nDstSize <= 0)
    {
        return -1;
    }
    ::snprintf(pszDst, nDstSize, "%s", pszSrc);
    return 0;
}

/*不转换因为之前有很多，这样写替换最简单---chenzhx20181012*/
int NoTransTcharToUtf8(const TCHAR* pszSrc, char* pszDst, int nDstSize)
{
    if (pszSrc == NULL
            || pszDst == NULL
            || nDstSize <= 0)
    {
        return -1;
    }
    ::snprintf(pszDst, nDstSize, "%s", pszSrc);
    return 0;
}


char* _tcscpy_s(char* pszDst, uint32_t nsize, const char* pszSrc)
{
    Snprintf(pszDst, nsize, pszSrc);

    return pszDst;
}

