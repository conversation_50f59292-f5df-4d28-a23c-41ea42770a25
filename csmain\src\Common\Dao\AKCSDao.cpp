#include "stdafx.h"
#include <string.h>
//#include "BindCode.h"
#include "DeviceSetting.h"
#include "AlarmControl.h"
//#include "AppControl.h"
#include "PersonnalTmpKey.h"
#include "PersonalAccount.h"
#include "PersonnalDeviceSetting.h"
#include "PersonnalAlarm.h"
#include "AKCSDao.h"
#include "Alarm.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "util.h"


//解除告警,刷新数据库中的状态
int DaoDealAlarmStatus(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info)
{
    return (GetAlarmControlInstance()->DealAlarmStatus(alarm_deal_info));
}

//个人终端用户,解除告警,刷新数据库中的状态
int DaoPersonnalDealAlarmStatus(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& personnal_alarm_deal_info)
{
    return (GetAlarmControlInstance()->PersonnalDealAlarmStatus(personnal_alarm_deal_info));
}
//个人终端用户App查询所在的联动系统主账号
//chenzhx ********添加获取最后的消息id
int DaoGetNodeByAppUser(SOCKET_MSG_PERSONNAL_APP_CONF& app_config)
{
    int ret = -1;
    ret = GetPersonalAccountInstance()->DaoGetNodeByAppUser(app_config);
    return ret;
}

//个人终端用户设备查询所在联动单元的设备与app列表
int DaoGetPeronnalDevListByNode(const std::string& node, int type, std::vector<PERSONNAL_DEVICE_SIP>& device)
{
    if (GetPersonnalDevSettingInstance()->GetDeviceSettingByNode(node, type, device) != 0)
    {
        return -1;
    }
    if (GetPersonalAccountInstance()->DaoGetApplistByNode(node, device) != 0)
    {
        return -1;
    }
    return 0;
}

int DaoGetCommunityDevListByNode(const std::string& node, int type, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    if (GetDeviceSettingInstance()->GetDeviceSettingByNode(node, type, device) != 0)
    {
        return -1;
    }
    if (GetPersonalAccountInstance()->DaoGetCommunityApplistByNode(node, device) != 0)
    {
        return -1;
    }
    return 0;

}

int GetPersonalAppAndIndoorDevListByNode(const std::string& node, std::vector<PERSONNAL_DEVICE_SIP>& device)
{
    PERSONNAL_DEVICE_SIP tmp_device;
    ResidentDeviceList devlist;
    if (0 == dbinterface::ResidentPerDevices::GetNodeIndoorDevList(node, devlist))
    {
        for (const auto& dev : devlist)
        {
            memset(&tmp_device, 0, sizeof(tmp_device));
            snprintf(tmp_device.name, sizeof(tmp_device.name), "%s", dev.location);
            snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), "%s", dev.sip);
            snprintf(tmp_device.ip, sizeof(tmp_device.ip), "%s", dev.ipaddr);
            tmp_device.type = dev.dev_type;
            snprintf(tmp_device.mac, sizeof(tmp_device.mac), "%s", dev.mac);
            snprintf(tmp_device.rtsp_password, sizeof(tmp_device.rtsp_password), "%s", dev.rtsppwd);
            snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), "%s", dev.uuid);
            device.push_back(tmp_device);
        }
    }

    if (GetPersonalAccountInstance()->DaoGetApplistByNode(node, device) != 0)
    {
        return -1;
    }
    return 0;
}

int GetOfficeAppAndIndoorDevListByNode(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    COMMUNITY_DEVICE_SIP tmp_device;
    ResidentDeviceList devlist;
    if (0 == dbinterface::ResidentDevices::GetNodeIndoorDevList(node, devlist))
    {
        for (const auto dev : devlist)
        {
            memset(&tmp_device, 0, sizeof(tmp_device));
            Snprintf(tmp_device.name, sizeof(tmp_device.name), dev.location);
            Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), dev.sip);
            Snprintf(tmp_device.ip, sizeof(tmp_device.ip), dev.ipaddr);
            tmp_device.type = dev.dev_type;
            Snprintf(tmp_device.mac, sizeof(tmp_device.mac), dev.mac);
            Snprintf(tmp_device.rtsp_password, sizeof(tmp_device.rtsp_password), dev.rtsppwd);
            Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), dev.uuid);
            device.push_back(tmp_device);
        }
    }

    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, account))
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        Snprintf(tmp_device.name, sizeof(tmp_device.name), account.name);
        Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), account.sip_account);
        Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }

    return 0;
}


int DaoGetCommunityPublicAppMaster(const int grade, const int manager_id, const int unit_id, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    if (GetPersonalAccountInstance()->DaoGetCommunityAppMaster(grade, manager_id, unit_id, device) != 0)
    {
        return -1;
    }
    return 0;

}


/*modify by chenzhx ********
//个人终端用户,转换email->uid
int DaoPerChangeEmail2Uid(char *pszEmail, int size)
{
    if (strchr(pszEmail, '@') == NULL)
    {
        return 0;
    }
    return GetPersonalAccountInstance()->DaoChangeEmail2Uid(pszEmail, size);
}
*/
int DaoPerGetNickNameByUid(const std::string& uid, std::string& nick_name)
{
    return GetPersonalAccountInstance()->DaoGetNickNameByUid(uid, nick_name);
}

int DaoPerGetNickNameAndNodeByUid(const std::string& uid, std::string& nick_name, std::string& node)
{
    return GetPersonalAccountInstance()->DaoGetNickNameAndNodeByUid(uid, nick_name, node);
}

int DaoPerGetNickNameAndNodeAndMngByUid(const std::string& uid, std::string& nick_name, std::string& node, int& manager_id)
{
    return GetPersonalAccountInstance()->DaoGetNickNameAndNodeAndMngIDByUid(uid, nick_name, node, manager_id);
}

//通过alarm id获取具体的告警信息
int DaoPerGetAlarmInfoById(const std::string& id, SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE& alarm_info)
{
    return GetPersonnalAlarmInstance()->GetAlarmInfo(id, alarm_info);
}

//add by chenzhx ******** 个人和社区公用一个personalAccount表
int DaoChangeEmail2Uid(char* pszEmail, int size)
{
    if (strchr(pszEmail, '@') == NULL)//肯定是uid，旧版本可能是邮箱
    {
        return 0;
    }
    return GetPersonalAccountInstance()->DaoChangeEmail2Uid(pszEmail, size);
}

//通过dclient上报的user的获取主站点账号和当前站点账号
int GetUserAccount(const std::string& user, std::string& main_user_account, std::string& report_user_account)
{
    if (strchr(user.c_str(), '@') == NULL) 
    {
        report_user_account = user;
        dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(user, main_user_account);
    }
    else    //v5.5之前可能是email
    {
        PerAccountUserInfo user_info;
        if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(user, user_info))
        {
            main_user_account = user_info.main_user_account;
            //旧版本没有多套房，故两个account相同
            report_user_account = user_info.main_user_account;
        }
    }
    return 0;
}


//通过alarm id获取具体的告警信息
int DaoCommGetAlarmInfoById(const std::string& id, SOCKET_MSG_ALARM_DEAL_OFFLINE& alarm_info)
{
    return GetAlarmInstance()->GetAlarmInfo(id, alarm_info);
}

//社区设备通过sip->location
int DaoCommunityGetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node)
{
    return GetDeviceSettingInstance()->GetLocationAndNodeBySip(sip, location, node);
}
int DaoCommunityGetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& manager_id)
{
    return GetDeviceSettingInstance()->GetLocationAndNodeAndMngIDBySip(sip, location, node, manager_id);
}

int DaoAccountMsgBySipAccount(const char* sip, ACCOUNT_MSG& stAccountMsg)
{
    return GetPersonalAccountInstance()->DaoGetAccountBySipAccount(sip, stAccountMsg);
}

