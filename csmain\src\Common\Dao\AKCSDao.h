#ifndef __AKCS_DAO_H__
#define __AKCS_DAO_H__
#include "dbinterface/AlarmDB.h"


//校验数据库中临时秘钥
int DaoDealAlarmStatus(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info);
int DaoGetNodeByAppUser(SOCKET_MSG_PERSONNAL_APP_CONF& app_config);
//个人终端用户,转换email->uid
//int DaoPerChangeEmail2Uid(char *pszEmail, int size);

//个人终端用户,解除告警,刷新数据库中的状态
int DaoPersonnalDealAlarmStatus(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& personnal_alarm_deal_info);
//个人终端用户设备查询所在联动单元的设备与app列表
int DaoGetPeronnalDevListByNode(const std::string& node, int type, std::vector<PERSONNAL_DEVICE_SIP>& device);

//通过uid->nick name
int DaoPerGetNickNameByUid(const std::string& uid, std::string& nick_name);

//通过alarm id获取具体的告警信息
int DaoPerGetAlarmInfoById(const std::string& id, SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE& alarm_info);

//通过alarm id获取具体的告警信息---社区
int DaoCommGetAlarmInfoById(const std::string& id, SOCKET_MSG_ALARM_DEAL_OFFLINE& alarm_info);
//社区和个人终端用户,转换email->uid
int DaoChangeEmail2Uid(char* pszEmail, int size);
int DaoGetCommunityDevListByNode(const std::string& node, int type, std::vector<COMMUNITY_DEVICE_SIP>& device);

//社区通过sip获取设备的Location
int DaoCommunityGetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node);
int DaoPerGetNickNameAndNodeByUid(const std::string& uid, std::string& nick_name, std::string& node);

//获取公共设备对应的主账号
int DaoGetCommunityPublicAppMaster(const int grade, const int manager_id, const int unit_id, std::vector<COMMUNITY_DEVICE_SIP>& device);


//通过sip获取账号信息
int DaoAccountMsgBySipAccount(const char* sip, ACCOUNT_MSG& stAccountMsg);
int DaoPerGetNickNameAndNodeAndMngByUid(const std::string& uid, std::string& nick_name, std::string& node, int& manager_id);
int DaoCommunityGetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& manager_id);
//单住户语音留言获取发送列表
int GetPersonalAppAndIndoorDevListByNode(const std::string& node, std::vector<PERSONNAL_DEVICE_SIP>& device);

//办公语音留言获取发送列表
int GetOfficeAppAndIndoorDevListByNode(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device);

int GetUserAccount(const std::string& user, std::string& main_user_account, std::string& report_user_account);


#endif //__AKCS_DAO_H__

