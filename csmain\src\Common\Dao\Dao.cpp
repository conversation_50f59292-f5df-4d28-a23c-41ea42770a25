#include "Dao.h"
#include "stdafx.h"
#include <stdio.h>
#include "ConnectionPool.h"
#include "AppPushToken.h"
#include "NotifyMsgControl.h"
#include "MsgControl.h"
#include "util.h"
#include "dbinterface/Message.h"
#include "dbinterface/DevRtspLog.h"


extern AKCS_CONF gstAKCSConf; 

namespace csmain
{
int DaoInsertDeliveryMsg(const std::string& account, const std::string& content, int type)
{
    //6.6一人多套房修改，需要查主站点的dclient_ver
    std::string main_uid;
    if(0 != dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(account, main_uid))
    {
        main_uid = account;
    }

    int ver = GetAppPushTokenInstance()->getAppDcliVerByUid(main_uid);
    if(ver < D_CLIENT_VERSION_6000)
    {
        return -1;
    }

    if(gstAKCSConf.is_aws)
    {
        PerMsgSendList text_messages;
        PersoanlMessageSend send_text_msg;
        memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
        Snprintf(send_text_msg.text_message.title, sizeof(send_text_msg.text_message.title), "Delivery");
        Snprintf(send_text_msg.text_message.content, sizeof(send_text_msg.text_message.content), content.c_str());
        Snprintf(send_text_msg.text_message.type, sizeof(send_text_msg.text_message.type), to_string(CPerTextNotifyMsg::DELIVERY_MSG).c_str());
        send_text_msg.client_type = CPerTextNotifyMsg::APP_SEND;
        send_text_msg.account = account;   
        text_messages.push_back(send_text_msg);
        GetMsgControlInstance()->PostAwsInsertMessageHttpReq(text_messages);
        return 0;
    }

    return dbinterface::Message::InsertDeliveryMsg(account, content, type);
}

int DaoInsertMessage(const std::string& account, const std::string& content)
{
    //6.6一人多套房修改，需要查主站点的dclient_ver
    std::string main_uid;
    if(0 != dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(account, main_uid))
    {
        main_uid = account;
    }
    
    int ver = GetAppPushTokenInstance()->getAppDcliVerByUid(main_uid);
    if(ver < D_CLIENT_VERSION_6100)
    {
        return -1;
    }

    if(gstAKCSConf.is_aws)
    {
        PerMsgSendList text_messages;
        PersoanlMessageSend send_text_msg;
        memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
        Snprintf(send_text_msg.text_message.title, sizeof(send_text_msg.text_message.title), "TmpKey Used");
        Snprintf(send_text_msg.text_message.content, sizeof(send_text_msg.text_message.content), content.c_str());
        Snprintf(send_text_msg.text_message.type, sizeof(send_text_msg.text_message.type), to_string(CPerTextNotifyMsg::TMPKEY_MSG).c_str());
        send_text_msg.client_type = CPerTextNotifyMsg::APP_SEND;
        send_text_msg.account = account;   
        text_messages.push_back(send_text_msg);
        GetMsgControlInstance()->PostAwsInsertMessageHttpReq(text_messages);
        return 0;        
    }

    return dbinterface::Message::InsertMessage(account, content);
}

} // namespace csmain
