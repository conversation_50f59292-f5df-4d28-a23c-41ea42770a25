/*
Navicat MySQL Data Transfer

Source Server         : localhost_3306
Source Server Version : 50622
Source Host           : localhost:3306
Source Database       : sdmc

Target Server Type    : MYSQL
Target Server Version : 50622
File Encoding         : 65001

Date: 2016-12-23 14:17:15
*/
DROP database if exists AKCS;
CREATE database AKCS;
use AKCS;

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for `Ad`
-- ----------------------------
DROP TABLE IF EXISTS `Ad`;
CREATE TABLE `Ad` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `FileName` varchar(256) NOT NULL,
  `Type` int(11) NOT NULL,
  `Duration` int(10) unsigned zerofill NOT NULL,
  `Count` int(10) unsigned zerofill NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;



-- ----------------------------
-- Table structure for `Admin`
-- ----------------------------
DROP TABLE IF EXISTS `Admin`;
CREATE TABLE `Admin` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `User` char(64) DEFAULT NULL,
  `Type` int(11) DEFAULT NULL,
  `Password` char(64) DEFAULT NULL,
  `Phone` char(20) DEFAULT NULL,
  `Mobile` char(20) DEFAULT NULL,
  `Email` char(64) DEFAULT NULL,
  `Company` char(64) DEFAULT NULL,
  `Address` char(128) DEFAULT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `DescInfo` text,
  `AuthList` text,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of Admin
-- ----------------------------

-- ----------------------------
-- Table structure for `AdMsg`
-- ----------------------------
DROP TABLE IF EXISTS `AdMsg`;
CREATE TABLE `AdMsg` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Content` text NOT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  `CreateUser` char(32) NOT NULL DEFAULT 'Administrator',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;


-- ----------------------------
-- Table structure for `AdSendList`
-- ----------------------------
DROP TABLE IF EXISTS `AdSendList`;
CREATE TABLE `AdSendList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MessageID` int(11) NOT NULL,
  `DeviceNode` char(32) NOT NULL,
  `Extension` int(11) NOT NULL,
  `CreateTime` datetime NOT NULL,
  `SendTime` datetime DEFAULT NULL,
  `RetryCount` int(10) unsigned zerofill NOT NULL,
  `Status` int(10) unsigned zerofill NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `Alarms`
-- ----------------------------
DROP TABLE IF EXISTS `Alarms`;
CREATE TABLE `Alarms` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AlarmType` char(64) DEFAULT NULL,
  `Address` char(64) DEFAULT NULL,
  `DeviceNode` char(32) DEFAULT NULL,
  `Extension` int(11) DEFAULT NULL,
  `AlarmTime` datetime DEFAULT NULL,
  `Status` int(11) DEFAULT NULL,
  `DealTime` datetime DEFAULT NULL,
  `DealUser` char(64) DEFAULT NULL,
  `DealType` int(11) DEFAULT NULL,
  `DealResult` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of Alarms
-- ----------------------------

-- ----------------------------
-- Table structure for `AreaNode`
-- ----------------------------
DROP TABLE IF EXISTS `AreaNode`;
CREATE TABLE `AreaNode` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Type` int(11) DEFAULT NULL,
  `Area` char(8) DEFAULT NULL,
  `Build` char(8) DEFAULT NULL,
  `Unit` char(8) DEFAULT NULL,
  `Floor` char(8) DEFAULT NULL,
  `Room` char(8) DEFAULT NULL,
  `Name` char(64) DEFAULT NULL,
  `Network` char(32) DEFAULT NULL,
  `DescInfo` text,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;



-- ----------------------------
-- Table structure for `BackupSetting`
-- ----------------------------
DROP TABLE IF EXISTS `BackupSetting`;
CREATE TABLE `BackupSetting` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Active` int(11) DEFAULT NULL,
  `Type` int(11) DEFAULT NULL,
  `Hour` int(11) DEFAULT NULL,
  `Min` int(11) DEFAULT NULL,
  `Sec` int(11) DEFAULT NULL,
  `Dir` char(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;



-- ----------------------------
-- Table structure for `CallHistory`
-- ----------------------------
DROP TABLE IF EXISTS `CallHistory`;
CREATE TABLE `CallHistory` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Type` int(11) DEFAULT NULL,
  `Number` char(64) DEFAULT NULL,
  `Time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `Duration` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;


-- ----------------------------
-- Table structure for `CommunityMonitor`
-- ----------------------------
DROP TABLE IF EXISTS `CommunityMonitor`;
CREATE TABLE `CommunityMonitor` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` char(64) DEFAULT NULL,
  `Status` int(11) unsigned DEFAULT NULL,
  `Address` char(64) DEFAULT NULL,
  `IP` char(32) DEFAULT NULL,
  `Port` int(11) unsigned DEFAULT NULL,
  `User` char(64) DEFAULT NULL,
  `Password` char(64) DEFAULT NULL,
  `Manufacturer` char(64) DEFAULT NULL,
  `Sn` char(64) DEFAULT NULL,
  `Version` char(64) DEFAULT NULL,
  `AccessUrl` varchar(256) DEFAULT NULL,
  `Model` char(64) DEFAULT NULL,
  `DescInfo` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of CommunityMonitor
-- ----------------------------

-- ----------------------------
-- Table structure for `ComunityMonitor`
-- ----------------------------
DROP TABLE IF EXISTS `ComunityMonitor`;
CREATE TABLE `ComunityMonitor` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Online` int(11) DEFAULT NULL,
  `Locate` char(64) DEFAULT NULL,
  `Ip` char(32) DEFAULT NULL,
  `Port` int(11) DEFAULT NULL,
  `User` char(64) DEFAULT NULL,
  `Password` char(64) DEFAULT NULL,
  `Manufucturer` char(64) DEFAULT NULL,
  `Sn` char(64) DEFAULT NULL,
  `Version` char(64) DEFAULT NULL,
  `Model` char(64) DEFAULT NULL,
  `DescInfo` char(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of ComunityMonitor
-- ----------------------------

-- ----------------------------
-- Table structure for `ConfigItem`
-- ----------------------------
DROP TABLE IF EXISTS `ConfigItem`;
CREATE TABLE `ConfigItem` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ModuleID` int(10) NOT NULL,
  `ItemName` varchar(256) DEFAULT NULL,
  `Config` varchar(256) DEFAULT NULL,
  `DescInfo` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;



-- ----------------------------
-- Table structure for `ConfigModule`
-- ----------------------------
DROP TABLE IF EXISTS `ConfigModule`;
CREATE TABLE `ConfigModule` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ModuleName` varchar(256) DEFAULT NULL,
  `DescInfo` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;



-- ----------------------------
-- Table structure for `DefaultSettingList`
-- ----------------------------
DROP TABLE IF EXISTS `DefaultSettingList`;
CREATE TABLE `DefaultSettingList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Name` char(64) NOT NULL,
  `Config` char(64) NOT NULL,
  `DefaultValue` char(64) DEFAULT NULL,
  `Description` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;


-- ----------------------------
-- Table structure for `Devices`
-- ----------------------------
DROP TABLE IF EXISTS `Devices`;
CREATE TABLE `Devices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Type` int(11) NOT NULL,
  `DeviceNode` char(64) DEFAULT NULL,
  `Extension` int(11) DEFAULT NULL,
  `IPAddress` char(32) DEFAULT NULL,
  `Gateway` char(32) DEFAULT NULL,
  `SubnetMask` char(32) DEFAULT NULL,
  `PrimaryDNS` char(32) DEFAULT NULL,
  `SecondaryDNS` char(32) DEFAULT NULL,
  `MAC` char(32) DEFAULT NULL,
  `Firmware` char(32) DEFAULT NULL,
  `Hardware` char(32) DEFAULT NULL,
  `Status` char(32) DEFAULT NULL,
  `Port` int(10) unsigned zerofill DEFAULT NULL,
  `LastConnection` datetime DEFAULT NULL,
  `PrivatekeyMD5` char(32) DEFAULT NULL,
  `RfidMD5` char(32) DEFAULT NULL,
  `ConfigSettings` varchar(2048) DEFAULT NULL,
  `ConfigMD5` char(32) DEFAULT NULL,
  `SipAccount` char(64) DEFAULT NULL,
  `BindAppCount` TINYINT DEFAULT 5, 
  PRIMARY KEY (`ID`),
  KEY (`MAC`)
) ENGINE=InnoDB  DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `DeviceUpgrade`
-- ----------------------------
DROP TABLE IF EXISTS `DeviceUpgrade`;
CREATE TABLE `DeviceUpgrade` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Model` char(64) NOT NULL,
  `ModelID` char(32) NOT NULL,
  `FirmwareVersion` char(64) DEFAULT NULL,
  `FirmwareFile` char(64) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;



-- ----------------------------
-- Table structure for `keysendlist`
-- ----------------------------
DROP TABLE IF EXISTS `KeySendList`;
CREATE TABLE `KeySendList` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `DeviceNode` char(32) NOT NULL,
  `Extension` int(11) NOT NULL,
  `RemoteAddr` char(24) NOT NULL,
  `RemotePort` int(11) NOT NULL,
  `CreateTime` datetime NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of KeySendList
-- ----------------------------

-- ----------------------------
-- Table structure for `MessageSendList`
-- ----------------------------
DROP TABLE IF EXISTS `MessageSendList`;
CREATE TABLE `MessageSendList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MessageID` int(11) NOT NULL,
  `DeviceNode` char(32) NOT NULL,
  `Extension` int(11) NOT NULL,
  `CreateTime` datetime NOT NULL,
  `SendTime` datetime DEFAULT NULL,
  `RetryCount` int(10) unsigned zerofill NOT NULL,
  `Status` int(10) unsigned zerofill NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;


-- ----------------------------
-- Table structure for `OwnerManagement`
-- ----------------------------
DROP TABLE IF EXISTS `OwnerManagement`;
CREATE TABLE `OwnerManagement` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` char(48) DEFAULT NULL,
  `Sex` int(11) DEFAULT NULL,
  `Address` char(128) DEFAULT NULL,
  `IDCard` char(64) DEFAULT NULL,
  `Phone` char(64) DEFAULT NULL,
  `UpdateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `UpdateUser` char(24) DEFAULT 'Administrator',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `OwnerMessage`
-- ----------------------------
DROP TABLE IF EXISTS `OwnerMessage`;
CREATE TABLE `OwnerMessage` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MessageType` int(11) DEFAULT NULL,
  `MessageTitle` varchar(360) DEFAULT NULL,
  `MessageContent` text NOT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `CreateUser` char(24) DEFAULT 'Administrator',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of OwnerMessage
-- ----------------------------

-- ----------------------------
-- Table structure for `PrivateKey`
-- ----------------------------
DROP TABLE IF EXISTS `PrivateKey`;
CREATE TABLE `PrivateKey` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Type` int(11) DEFAULT NULL,
  `Code` char(20) DEFAULT NULL,
  `Status` int(11) unsigned zerofill DEFAULT NULL,
  `User` char(64) DEFAULT NULL,
  `Address` char(128) DEFAULT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT NULL,
  `DescInfo` char(255) DEFAULT NULL,
  `Access` varchar(4096) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for `PropertyWorker`
-- ----------------------------
DROP TABLE IF EXISTS `PropertyWorker`;
CREATE TABLE `PropertyWorker` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` char(48) DEFAULT NULL,
  `Sex` int(11) DEFAULT NULL,
  `Address` char(128) DEFAULT NULL,
  `IDCard` char(64) DEFAULT NULL,
  `Phone` char(64) DEFAULT NULL,
  `UpdateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `UpdateUser` char(24) DEFAULT 'Administrator',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

------------------------------
-- Table structure for `PushAd`
-- ----------------------------
DROP TABLE IF EXISTS `PushAd`;
CREATE TABLE `PushAd` (
  `ID` int(11) NOT NULL,
  `Enable` int(11) NOT NULL DEFAULT '0',
  `FilePath` char(255) DEFAULT NULL,
  `Duration` int(11) NOT NULL DEFAULT '10',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of pushAd
-- ----------------------------

-- ----------------------------
-- Table structure for `RfcardKey`
-- ----------------------------
DROP TABLE IF EXISTS `RfcardKey`;
CREATE TABLE `RfcardKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Type` int(11) DEFAULT NULL,
  `Code` char(20) DEFAULT NULL,
  `Status` int(11) unsigned zerofill DEFAULT NULL,
  `User` char(64) DEFAULT NULL,
  `Address` char(128) DEFAULT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT NULL,
  `DescInfo` char(255) DEFAULT NULL,
  `Access` varchar(4096) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of RfcardKey
-- ----------------------------

-- ----------------------------
-- Table structure for `SystemSetting`
-- ----------------------------
DROP TABLE IF EXISTS `SystemSetting`;
CREATE TABLE `SystemSetting` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AddrMD5` char(32) DEFAULT NULL,
  `SipAccount` varchar(4096) DEFAULT NULL,
  `UpdateFilesFlag` int(11) DEFAULT NULL,
  `MissedCallFlag` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=UTF8;


-- ----------------------------
-- Table structure for `TextMessage`
-- ----------------------------
DROP TABLE IF EXISTS `TextMessage`;
CREATE TABLE `TextMessage` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MessageType` int(11) DEFAULT NULL,
  `MessageTitle` varchar(360) DEFAULT NULL,
  `MessageContent` text NOT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `FromUser` varchar(24) DEFAULT NULL,
  `ToUser` varchar(24) DEFAULT NULL,
  `MessageFlag` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `TextMessageRecord`
-- ----------------------------
DROP TABLE IF EXISTS `TextMessageRecord`;
CREATE TABLE `TextMessageRecord` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MessageType` int(11) DEFAULT NULL,
  `MessageTitle` varchar(360) DEFAULT NULL,
  `MessageContent` text NOT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `FromUser` varchar(24) DEFAULT 'Administrator',
  `ToUser` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of TextMessageRecord
-- ----------------------------

-- ----------------------------
-- Table structure for `UndealedAlarm`
-- ----------------------------
DROP TABLE IF EXISTS `UndealedAlarm`;
CREATE TABLE `UndealedAlarm` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `AlarmType` char(64) DEFAULT NULL,
  `Area` char(64) DEFAULT NULL,
  `FullAddress` char(64) DEFAULT NULL,
  `Equipment` char(64) DEFAULT NULL,
  `AlarmTime` datetime DEFAULT NULL,
  `DealTime` datetime DEFAULT NULL,
  `DealUser` char(64) DEFAULT NULL,
  `DealResult` char(64) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of UndealedAlarm
-- ----------------------------

-- ----------------------------
-- Table structure for `UpgradeSendList`
-- ----------------------------
DROP TABLE IF EXISTS `UpgradeSendList`;
CREATE TABLE `UpgradeSendList` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ModelID` char(32) NOT NULL,
  `RemoteAddr` char(24) NOT NULL,
  `RemotePort` int(11) NOT NULL,
  `CreateTime` datetime NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Records of UpgradeSendList
-- ----------------------------

-- ----------------------------
-- Table structure for `users`
-- ----------------------------
DROP TABLE IF EXISTS `Users`;
CREATE TABLE `Users` (
  `Host` char(60) COLLATE utf8_bin NOT NULL DEFAULT '',
  `User` char(16) COLLATE utf8_bin NOT NULL DEFAULT '',
  `Password` char(41) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL DEFAULT '',
  `Select_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Insert_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Update_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Delete_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Create_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Drop_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `ReloAd_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Shutdown_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Process_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `File_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Grant_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `References_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Index_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Alter_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Show_db_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Super_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Create_tmp_table_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Lock_tables_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Execute_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Repl_slave_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Repl_client_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Create_view_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Show_view_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Create_routine_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Alter_routine_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Create_user_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Event_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Trigger_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `Create_tablespace_priv` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  `ssl_type` enum('','ANY','X509','SPECIFIED') CHARACTER SET utf8 NOT NULL DEFAULT '',
  `ssl_cipher` blob NOT NULL,
  `x509_issuer` blob NOT NULL,
  `x509_subject` blob NOT NULL,
  `max_questions` int(11) unsigned NOT NULL DEFAULT '0',
  `max_updates` int(11) unsigned NOT NULL DEFAULT '0',
  `max_connections` int(11) unsigned NOT NULL DEFAULT '0',
  `max_user_connections` int(11) unsigned NOT NULL DEFAULT '0',
  `plugin` char(64) COLLATE utf8_bin DEFAULT '',
  `authentication_string` text COLLATE utf8_bin,
  `password_expired` enum('N','Y') CHARACTER SET utf8 NOT NULL DEFAULT 'N',
  PRIMARY KEY (`Host`,`User`)
) ENGINE=MyISAM DEFAULT CHARSET=UTF8 COLLATE=utf8_bin COMMENT='Users and global privileges';

-- ----------------------------
-- Records of users
-- ----------------------------
-- ----------------------------
-- Table structure for `UpgradeSendList`
-- ----------------------------
DROP TABLE IF EXISTS `AppBindDev`;
CREATE TABLE `AppBindDev` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AppName` char(32) NOT NULL,
  `DevMac` char(32) DEFAULT NULL,
  `LastConnTime` datetime DEFAULT NULL,
  `Status` enum('N','Y') NOT NULL DEFAULT 'N',
  CONSTRAINT PRIMARY KEY (`ID`),
  KEY (`DevMac`),
  CONSTRAINT `FK_DEV` FOREIGN KEY (`DevMac`) REFERENCES `Devices` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;