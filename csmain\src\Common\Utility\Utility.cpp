#include "stdafx.h"
#include <iomanip>
#include <sstream>
#include <random>
#include <sys/time.h>
#include "Utility.h"
#include <boost/algorithm/string/replace.hpp>
#include "util.h"

extern AKCS_CONF gstAKCSConf;

void GetCurTime(TIME_DATA* pTimeData)
{
    if (pTimeData == NULL)
    {
        return;
    }

    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    pTimeData->nYear = 1900 + p->tm_year;
    pTimeData->nMonth = p->tm_mon + 1;
    pTimeData->nDay = p->tm_mday;
    pTimeData->nDayOfWeek = p->tm_wday;
    pTimeData->nHour = p->tm_hour;
    pTimeData->nMin = p->tm_min;
    pTimeData->nSec = p->tm_sec;

    return;
}

VOID GetCurTime(INOUT char* pszDate, IN int size)
{
    if (pszDate == NULL)
    {
        return;
    }
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);
    snprintf(pszDate, size, "%d-%02d-%02d %02d:%02d:%02d", (1900 + p->tm_year), (p->tm_mon + 1), p->tm_mday,
             p->tm_hour, p->tm_min, p->tm_sec);
    return;
}


//获取csconfig/fdfs服务器的代理地址
std::string GetConfigDownloadServer(const std::string& ip_address, int is_support_tls_high_version)
{
    char szPath[128] = {0};
    int port = gstAKCSConf.config_server_port;
    if(is_support_tls_high_version) 
    {
        port = gstAKCSConf.config_server_tlshigh_port;
    }
    snprintf(szPath, 128, "https://%s:%d", ip_address.c_str(), port);
    std::string strPath = szPath;
    return strPath;

}


//获取被管理员从界面上删除后的默认设备配置文件,eg:http:*************:8080/personnal_download/000000000001.cfg
std::string GetPerDelDevDownloadWebConfPath(const std::string& ip_address)
{
    std::stringstream strPath;
    strPath << GetConfigDownloadServer(ip_address)
            << "/download/000000000001.cfg";

    return strPath.str();
}

//获取被管理员从界面上删除后的默认设备配置文件,eg:http:*************:8080/personnal_download/000000000001.cfg
std::string GetPerDelDevDownloadWebContactPath(const std::string& ip_address)
{
    std::stringstream strPath;
    strPath << GetConfigDownloadServer(ip_address)
            << "/download/000000000100.xml";

    return strPath.str();
}

std::string GetPerDelDevDownloadWebFacePath(const std::string& ip_address)
{
    std::stringstream strPath;
    strPath << GetConfigDownloadServer(ip_address)
            << "/download/000000000010.xml";

    return strPath.str();
}

std::string GetPerDelDevDownloadWebRfcardPath(const std::string& ip_addr)
{
    std::stringstream str_path;
    str_path << GetConfigDownloadServer(ip_addr)
            << "/download/000000000010.xml";

    return str_path.str();
}

std::string GetPerDelDevDownloadWebPrivateKeyPath(const std::string& ip_addr)
{
    std::stringstream str_path;
    str_path << GetConfigDownloadServer(ip_addr)
            << "/download/000000000010.xml";

    return str_path.str();
}

std::string GetPerDelDevDownloadWebUserPath(const std::string& ip_address)
{
    std::stringstream strPath;
    strPath << GetConfigDownloadServer(ip_address)
            << "/download/000000000011.json";

    return strPath.str();
}

std::string GetPerDelDevDownloadWebScheduelPath(const std::string& ip_address)
{
    std::stringstream strPath;
    strPath << GetConfigDownloadServer(ip_address)
            << "/download/000000000111.json";

    return strPath.str();
}


std::string GetDownloadDSTTimeZoneXmlPath(const std::string& ip_addr)
{
    std::stringstream path;
    path << GetConfigDownloadServer(ip_addr) << "/download/TimeZone-dev.xml";   //嵌入式设备使用
    return path.str();
}

std::string GetDownloadDSTTimeZoneDataPath(const std::string& ip_addr)
{
    std::stringstream path;
    path << GetConfigDownloadServer(ip_addr) << "/download/tzdata"; //安卓设备使用
    return path.str();
}


//获取当前时间
CString GetCurTime()
{
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    CString strCurTime;
    //strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), curTime.wYear, curTime.wMonth, curTime.wDay, curTime.wHour, curTime.wMinute, curTime.wSecond);
    strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), (1900 + p->tm_year), (p->tm_mon + 1), p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    return strCurTime;
}
CString GetStringFromUtf8(const char* pszUtf8)
{
    CString str = _T("");
    if (pszUtf8 == NULL)
    {
        return str;
    }
    int len = strlen(pszUtf8) + 1;

    TCHAR* pwszString = new TCHAR[len];
    TransUtf8ToTchar(pszUtf8, pwszString, len);
    str = pwszString;

    delete []pwszString;

    return str;
}


void ParseConfigItem(const std::string &msg, SOCKET_MSG_CONFIG &socket_msg_config)
{
    socket_msg_config.config_count = 0;
    std::stringstream stream(msg);

    while (1) {
        std::string line;
        std::getline(stream, line);
        if (socket_msg_config.config_count >= CONFIG_MODULE_ITEM_NUM)
        {
            break;
        }

        if (line.find("=") != std::string::npos)
        {
            Snprintf(socket_msg_config.module.item[socket_msg_config.config_count], CONFIG_MODULE_ITEM_SIZE, line.c_str());
            ++socket_msg_config.config_count;
        }

        if (!stream.good())
        {
            break;
        }
    }
    return;
}

