#pragma once


#include "DclientMsgSt.h"
#include "util_cstring.h"

#define HTONS   htons
#define NTOHS   ntohs
#define HTONL   htonl
#define NTOHL   ntohl

#define CONFFILEPATH "/usr/local/akcs/csmain/conf/csmain.conf"
#define MD5_SIZE                        36
#define NAME_SIZE                       32
#define KEY_VAL_LEN                     32


//#define HTTP_LISTEN_PORT  8085
#define HTTP_LISTEN_PORT    80
#define HTTPROOT            "/var/www/" //chang by chenzhx V4.0

#define PERSONNAL_DOWNLOAD  "/download/personal/" //chang by chenzhx V4.0 去掉_download
#define COMMUNITY_DOWNLOAD  "/download/community/" //chang by chenzhx V4.0 去掉_downloa


#define WEB_URL            "http://120.79.38.106/"

typedef struct TIME_DATA_T
{
    int nYear;
    int nMonth;
    int nDay;
    int nDayOfWeek;
    int nHour;
    int nMin;
    int nSec;
} TIME_DATA;

//比较时间
int CompareTime(TIME_DATA* pTimeData1, TIME_DATA* pTimeData2);

//获取当前时间
CString GetCurTime();
VOID GetCurTime(INOUT char* pszDate, IN int size);

CString GetStringFromUtf8(const char* pszUtf8);

int TransUtf8ToTchar(const char* pszSrc, TCHAR* pszDst, int nDstSize);

std::string GetPerDelDevDownloadWebConfPath(const std::string& ip_address);
std::string GetPerDelDevDownloadWebContactPath(const std::string& ip_address);
std::string GetPerDelDevDownloadWebFacePath(const std::string& ip_address);
std::string GetPerDelDevDownloadWebRfcardPath(const std::string& ip_addr);
std::string GetPerDelDevDownloadWebPrivateKeyPath(const std::string& ip_addr);
std::string GetPerDelDevDownloadWebScheduelPath(const std::string& ip_address);
std::string GetPerDelDevDownloadWebUserPath(const std::string& ip_address);


std::string GetDownloadDSTTimeZoneXmlPath(const std::string& ip_addr);
std::string GetDownloadDSTTimeZoneDataPath(const std::string& ip_addr);

void ParseConfigItem(const std::string &msg, SOCKET_MSG_CONFIG &socket_msg_config);


std::string GetConfigDownloadServer(const std::string& ip_address, int is_support_tls_high_version = 0);
