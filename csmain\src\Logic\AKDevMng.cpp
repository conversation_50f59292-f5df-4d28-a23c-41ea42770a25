#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AKDevMng.h"
#include "stdafx.h"
#include "PersonnalDeviceSetting.h"
#include "DeviceSetting.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"


CAkDevManager::~CAkDevManager()
{
    RemoveAll();
}

CAkDevManager* CAkDevManager::GetInstance()
{
    static CAkDevManager s_manager;  
    return &s_manager;
}

void CAkDevManager::RemoveAll()
{
    std::lock_guard<std::mutex> lock(sip_mutex_);
    sip_macs_.clear();
}

void CAkDevManager::UpdateSipMacList(const std::string& sip, const std::string& mac)
{
    std::lock_guard<std::mutex> lock(sip_mutex_);
    sip_macs_[sip] = mac;
}

//通过sip来查找mac
int CAkDevManager::GetMacBySip(const std::string& sip, std::string& mac)
{
    std::lock_guard<std::mutex> lock(sip_mutex_);
    SipMacList::iterator it = sip_macs_.find(sip);  
    if (it == sip_macs_.end()) 
    {
        return -1;
    }
    mac = it->second;
    return 0;
}


void CAkDevManager::DelSip(const std::string& sip)
{
    std::lock_guard<std::mutex> lock(sip_mutex_);
    SipMacList::iterator it = sip_macs_.find(sip);  
    if (it != sip_macs_.end()) 
    {
        sip_macs_.erase(it);
    }
}

void CAkDevManager::GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node)
{
    ResidentDev per_dev;
    ResidentDev dev;
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
    }
}

int CAkDevManager::GetLocationAndNodeBySip2(const std::string& sip, std::string& location, std::string& node)
{
    ResidentDev per_dev;
    ResidentDev dev;
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
    }

    return ret == 0 ? 1 : 0;
}

void CAkDevManager::GetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& manager_id)
{
    ResidentDev per_dev;
    ResidentDev dev;
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);

    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
            manager_id = dev.project_mng_id;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
        manager_id = per_dev.project_mng_id;
    }
}


void CAkDevManager::UpdateMacTypeList(const std::string& mac, const DevType type)
{
    std::lock_guard<std::mutex> lock(mac_types_mutex_);
    mac_types_[mac] = type;
}

DevType CAkDevManager::GetTypeByMac(const std::string& mac)
{
    DevType type = PER_NULL;
    std::lock_guard<std::mutex> lock(mac_types_mutex_);
    type = mac_types_[mac];
    return type;
}

