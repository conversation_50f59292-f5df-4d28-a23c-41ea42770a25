#include <time.h>
#include <string>
#include <stdlib.h>
#include <sstream>
#include "AkLogging.h"
#include "util.h"
#include "AKUpgradeMonitor.h"

static const char upgrade_monitor_file[] = "/usr/local/akcs/csmain/csmain.upgrade";

//status:通过读取配置文件的信息获取,由调用者传入
void UpgradeMonitor::Init(const int status)
{
    upgrade_monitor_fd_ = nullptr;
    if (status)
    {
        upgrade_monitor_fd_ = fopen(upgrade_monitor_file, "a");
        if (!upgrade_monitor_fd_)
        {
            AK_LOG_WARN << "akcs now is on upgrade status, but fopen " << upgrade_monitor_file << " file fialed.";
            return ;
        }
        char now[24] = {0};
        GetNowTime(now, 24);
        char upgrade_init[64] = {0};
        snprintf(upgrade_init, 64, "UpgradeMonitor init at %s\n", now);

        ::fseek(upgrade_monitor_fd_, 0, SEEK_END);
        ::fwrite(upgrade_init, 1, strlen(upgrade_init), upgrade_monitor_fd_);
        ::fflush(upgrade_monitor_fd_);
    }
}

int UpgradeMonitor::ChangeUpgradeStatus(const int enable)
{
    if ((enable == 1) && (!upgrade_monitor_fd_))
    {
        upgrade_monitor_fd_ = fopen(upgrade_monitor_file, "a");
        if (!upgrade_monitor_fd_)
        {
            AK_LOG_WARN << "akcs now is changing to upgrade status, but fopen " << upgrade_monitor_file << " file failed.";
            return -1;
        }
        char now[24] = {0};
        GetNowTime(now, 24);
        char upgrade_change[64] = {0};
        snprintf(upgrade_change, 64, "ChangeUpgradeStatus at %s\n", now);

        ::fseek(upgrade_monitor_fd_, 0, SEEK_END);
        ::fwrite(upgrade_change, 1, strlen(upgrade_change), upgrade_monitor_fd_);
        ::fflush(upgrade_monitor_fd_);
        AK_LOG_INFO << "ChangeUpgradeStatus, status is " << enable;
    }
    else if ((enable == 0) && (upgrade_monitor_fd_))
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ::fclose(upgrade_monitor_fd_);
        upgrade_monitor_fd_ = nullptr;
    }
    return 0;
}

void UpgradeMonitor::WriteUpgradeKeyMsg(const std::string& mac, const SOCKET_MSG_KEY_SEND_T& key_send_msg)
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (upgrade_monitor_fd_)
    {
        char dev_desc[2046] = {0};
        snprintf(dev_desc, 2046, "DEV:%s %s:%s %s:%s %s:%s %s:%s\n", mac.c_str(),
                 key_send_msg.private_key_md5, key_send_msg.private_key_url,
                 key_send_msg.rf_id_md5, key_send_msg.rf_id_url,
                 key_send_msg.config_md5, key_send_msg.config_url,
                 key_send_msg.contact_md5, key_send_msg.contact_url);


        ::fseek(upgrade_monitor_fd_, 0, SEEK_END);
        int n = ::fwrite(dev_desc, 1, strlen(dev_desc), upgrade_monitor_fd_);
        ::fflush(upgrade_monitor_fd_);
        AK_LOG_INFO << "write dec desc info, " << dev_desc << ", size is " << n;
    }
}
//判断是否处于升级过程中
bool UpgradeMonitor::UpgradeStatus()
{
    return upgrade_monitor_fd_ ? true : false;
}
