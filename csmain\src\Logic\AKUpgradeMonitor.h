// Author: chency
// Date: 2020-05-21
// File: AkcsMonitor.h
// Des: 云升级过程中,csmain对终端设备的交互信令监控

#ifndef __AKCS_MAIN_UPGRADE_MONITOR_H__
#define __AKCS_MAIN_UPGRADE_MONITOR_H__

#include "Singleton.h"
#include "DclientMsgSt.h"
#include <mutex>
#include <stdio.h>

struct SOCKET_MSG_KEY_SEND_T;
class UpgradeMonitor
{
public:
    // 实现单例
    friend class AKCS::Singleton<UpgradeMonitor>;
    // 用法:AKCS::Singleton<UpgradeMonitor>::instance().xx()
    virtual ~UpgradeMonitor()
    {
        if (upgrade_monitor_fd_)
        {
            fclose(upgrade_monitor_fd_);
            upgrade_monitor_fd_ = nullptr;
        }
    }
public:
    void Init(const int status);
    int ChangeUpgradeStatus(const int enable);
    void WriteUpgradeKeyMsg(const std::string& mac, const SOCKET_MSG_KEY_SEND_T& key_send_msg);
    bool UpgradeStatus();
private:
    FILE* upgrade_monitor_fd_;
    std::mutex mutex_;
};

#endif /* __AKCS_MAIN_UPGRADE_MONITOR_H__ */