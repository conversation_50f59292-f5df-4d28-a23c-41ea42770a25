#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AKUserMng.h"
#include "AppPushToken.h"
#include "session_rpc_client.h"
#include "PersonalAccount.h"
#include "AKCSDao.h"
#include "AkcsMonitor.h"
#include "dbinterface/Token.h"

extern SmRpcClient* g_sm_client_ptr;
extern std::string g_logic_srv_id;

CAkUserManager::~CAkUserManager()
{
    RemoveAll();
}

CAkUserManager* CAkUserManager::GetInstance()
{
    static CAkUserManager s_manager;  //当类的构造时无参数的时候,可以这样来操作,否则不行
    return &s_manager;
}

void CAkUserManager::SetAkUserOfflineById(const std::string& uid)
{
    std::lock_guard<std::mutex> lock(uid_mutex_);
    UIdTokenListIter it = uidTokens_.find(uid);
    if (it != uidTokens_.end())
    {
        it->second.setMobileOnline(0);
        AK_LOG_INFO << "App offline, uid is " << uid.c_str() << ", token is " << it->second.Token().c_str();
    }
    else
    {
        AK_LOG_WARN << "there are not UID " << uid << " in uidTokens_";
    }
}
void CAkUserManager::GetAkOfflineUsersByNode(const std::string& node, std::vector<CMobileToken>& oVec)
{
    NodeUidList node_uids = GetNodeUidList();
    NodeUidListIter it = node_uids.find(node);
    if (it == node_uids.end())
    {
        return;
    }
    std::map<std::string, std::string> uid_map;
    std::map<std::string, std::string> uid_map_del;
    {
        std::lock_guard<std::mutex> lock(node_uids_mutex_);
        uid_map = it->second;
    }
    UIdTokenListIter Iter;
    for (const auto& uid_info : uid_map)
    {
        std::string uid = uid_info.first;
        std::string main_site = uid_info.second;
        //added by chenyc,2018-12-21,先判断是否有在其他的csmain上面接入过或者logout过了.确保该uid归属于该csmain sid
        if (g_logic_srv_id != g_sm_client_ptr->QueryUid(main_site))
        {
            //uidTokens_  nodeUIds_.second均需要删除uid
            {
                std::lock_guard<std::mutex> lock(uid_mutex_);
                uidTokens_.erase(main_site);
            }
            uid_map_del.insert(uid_info);
            continue;
        }
        //added by chenyc, 2019-03-14,分布式系统改造,接下来判断该uid是否是在线的.分两种情况:
        //1、uidTokens_里面有该uid,那么证明最后一次该uid上线时,本csmain进程已经在运行了.此时根据uidTokens_.second.MobileOnline()
        //   的状态即可判断uid的上下线状态;
        //2、uidTokens_里面没有该uid,那么证明最后一次该uid上线后,本cmsain进程还没运行.那么该uid需要端外推送,且需要到mysql里面同步push token
        {
            //为避免在锁里面执行sql,先加锁判断是否需要执行sql,然后执行sql,最后再加锁.所以这个过程会有两次加锁.
            CMobileToken token;
            bool b_need_sql = false;
            {
                std::lock_guard<std::mutex> lock(uid_mutex_);
                Iter = uidTokens_.find(main_site);
                if (Iter == uidTokens_.end())
                {
                    b_need_sql = true;
                }
            }
            if (b_need_sql) //找不到uidTokens_里面的缓存,证明是上述的情况2
            {
                if (GetAppPushTokenInstance()->getAppPushTokenByUid(main_site, token) != 0)
                {
                    AK_LOG_WARN << "the main_site [" << main_site << "] is invalid";
                    continue;
                }
            }
            std::lock_guard<std::mutex> lock(uid_mutex_);//再次加锁,防止执行sql的过程中,uid执行logout或过期,导致uid对应的iter失效
            Iter = uidTokens_.find(main_site);
            if (Iter != uidTokens_.end())
            {
                if (!(Iter->second.MobileOnline()))
                {
                    oVec.push_back(Iter->second);
                    AK_LOG_INFO << "offline app msg must be sent by push server, uid is " << Iter->first << ", token is " << Iter->second.Token() << ", fcm token is " << Iter->second.FcmToken();
                }
            }
            else //如果运行到这里uidTokens_还是没有该uid,证明在本线程执行sql的时候,uid没有重新上线,需要重建uidTokens_.该token是sql里面查找出来的.
            {
                uidTokens_[main_site] = token; //added by chenyc,2018-12-24,重建uidTokens_
                oVec.push_back(token);
            }
        }
    }
    //同步本地的副本,除非重新分配csmain，导致uid->csmain的映射漂移(即上次分配给uid的csmain跟本次不同),否则不会发生这种现象.
    {
        std::lock_guard<std::mutex> lock(node_uids_mutex_);
        for (auto& uid_info : uid_map_del)
        {
            it->second.erase(uid_info.first);
        }
    }
}

void CAkUserManager::GetAkOfflineUidTokensByNode(const std::string& node, UIdTokenList& uid_tokens)
{
    NodeUidListIter it = nodeUIds_.find(node);
    if (it == nodeUIds_.end())
    {
        return;
    }
    std::map<std::string, std::string> uid_map;
    std::map<std::string, std::string> uid_map_del;
    {
        std::lock_guard<std::mutex> lock(node_uids_mutex_);
        uid_map = it->second; //拷贝一份
    }
    UIdTokenListIter Iter;
    for (const auto& uid_info : uid_map) //遍历本地的副本
    {
        std::string uid = uid_info.first;
        std::string main_site = uid_info.second;
        if (g_logic_srv_id != g_sm_client_ptr->QueryUid(main_site))
        {
            {
                std::lock_guard<std::mutex> lock(uid_mutex_);
                uidTokens_.erase(main_site);
            }
            uid_map_del.insert(uid_info);
            continue;
        }
        {
            CMobileToken token;
            bool flag = false;
            {
                std::lock_guard<std::mutex> lock(uid_mutex_); //这里也要加锁,防止迭代器失效.
                Iter = uidTokens_.find(main_site);
                if (Iter == uidTokens_.end())
                {
                    flag = true;
                }
            }
            if (flag)
            {
                if (GetAppPushTokenInstance()->getAppPushTokenByUid(main_site, token) != 0)
                {
                    AK_LOG_WARN << "the uid [" << main_site.c_str() << "] is invalid";
                    continue;
                }
            }

            {
                std::lock_guard<std::mutex> lock(uid_mutex_);
                Iter = uidTokens_.find(main_site);
                if (Iter != uidTokens_.end())
                {
                    if (!(Iter->second.MobileOnline()))
                    {
                        uid_tokens.insert(std::make_pair(main_site, Iter->second));
                    }
                }
                else
                {
                    uid_tokens.insert(std::make_pair(main_site, token));
                    uidTokens_[main_site] = token; //added by chenyc,2018-12-24,重建uidTokens_
                }
            }
        }
    }
    //同步本地的副本,除非重新分配csmain，导致uid->csmain的映射漂移,否则不会发生这种现象.
    {
        std::lock_guard<std::mutex> lock(node_uids_mutex_);
        for (auto& uid_info : uid_map_del)
        {
            it->second.erase(uid_info.first);
        }
    }

}

int CAkUserManager::GetRealSiteByNodeAndMainSite(const std::string& node, const std::string& main_site, std::string& uid)
{
    NodeUidListIter it = nodeUIds_.find(node);
    if (it == nodeUIds_.end())
    {
        AK_LOG_INFO << "GetRealSiteByNodeAndMainSite not find node: " << node;
        return -1;
    }
    
    std::map<std::string, std::string> uid_map = it->second;
    for (const auto& uid_info : uid_map)
    {
        if (uid_info.second == main_site)
        {
            uid = uid_info.first;  // 返回匹配到的uid
            return 0;
        }
    }

    return -1; 
}

//pbx端外唤醒app,注:只在app下线的时候才返回token
int CAkUserManager::GetAkOfflineUserTokenByUid(const std::string& uid, CMobileToken& token)
{
    //防止uid token关系变大, 所以做了个清除的动作  ，但是排除因为下游rpc查询接口失败导致的返回值为空字符串的场景
    std::string srv_id = g_sm_client_ptr->QueryUid(uid);
    if (srv_id.empty())
    {
        return -1;
    }
    else if (!srv_id.empty() && g_logic_srv_id != srv_id)
    {
        {
            std::lock_guard<std::mutex> lock(uid_mutex_);
            uidTokens_.erase(uid);
        }
        return -1;
    }
    return GetUserTokenByUid(uid, token);
}

int CAkUserManager::GetAkOfflineUserTokenByUid2(const std::string& uid, CMobileToken& token)
{
    return GetUserTokenByUid(uid, token);
}

int CAkUserManager::GetUserTokenByUid(const std::string& uid, CMobileToken& token)
{

    //为避免在锁里面执行sql,先判断是否需要执行sql，然后再加锁
    CMobileToken token2;
    bool flag = false;
    {
        std::lock_guard<std::mutex> lock(uid_mutex_); //这里也要加锁,防止迭代器失效.
        auto Iter = uidTokens_.find(uid);
        if (Iter == uidTokens_.end())
        {
            flag = true;
        }
        else
        {
            token = Iter->second;
            return 0;
        }
    }
    //当首次找不到token时，查找数据库中的token并重建缓存
    if (flag)
    {
        if (GetAppPushTokenInstance()->getAppPushTokenByUid(uid, token2) != 0)
        {
            AK_LOG_WARN << "the uid [" << uid << "] is invalide";
            return -1;
        }
    }
    {
        std::lock_guard<std::mutex> lock(uid_mutex_);
        UIdTokenListIter Iter = uidTokens_.find(uid);
        if (Iter == uidTokens_.end())
        {
            uidTokens_[uid] = token2; //added by chenyc,2018-12-24,重建uidTokens_
            token = token2;
            return 0;
        }
        else
        {
            token = Iter->second;
        }
    }
    return 0;
}


//pbx端外唤醒第三方app(集成Akuvox SDK)
int CAkUserManager::GetSdkOfflineUserTokenByUid(const std::string& uid, CMobileToken& token)
{
    if (GetAppPushTokenInstance()->getAppPushTokenByUid(uid, token) != 0)
    {
        AK_LOG_WARN << "the uid [" << uid.c_str() << "] is invalide";
        return -1;
    }
    return 0;
}


void CAkUserManager::AddAkUserByNodeId(const PersonalAccountNodeInfoMap& nodes, const std::string& main_site, const CMobileToken& token)
{
    for(const auto& node_info : nodes)
    {
        //真实站点
        std::string uid = node_info.first;
        NodeUidListIter it = nodeUIds_.find(node_info.second.node);
        if (it != nodeUIds_.end())
        {
            std::lock_guard<std::mutex> lock(node_uids_mutex_);
            it->second.insert(std::make_pair(uid, main_site)); //set insert
        }
        else
        {
            std::map<std::string,std::string> uidmap;
            uidmap.insert(std::make_pair(uid, main_site));
            std::lock_guard<std::mutex> lock(node_uids_mutex_);
            nodeUIds_[node_info.second.node] = uidmap;
        }
    }

    std::lock_guard<std::mutex> lock(uid_mutex_);
    {
        UIdTokenListIter Iter = uidTokens_.find(main_site);
        if (Iter == uidTokens_.end())
        {
            uidTokens_.insert(std::make_pair(main_site, token));
            AK_LOG_INFO << "new device token, main_site is " << main_site << ",token is " << token.Token();
        }
        else
        {
            Iter->second = token;
            AK_LOG_INFO << "must to replace device token, main_site is " << main_site << ",token is " << Iter->second.Token() << ", fcm token is " << Iter->second.FcmToken() << ", app_oem is " << token.AppOem();
        }
        if (-1 == GetAppPushTokenInstance()->updateAppPushInfo(main_site, token))
		{
			char error_msg[1024];
			snprintf(error_msg, sizeof(error_msg), "Update AppPushToken failed,main_site is [%s],token is [%s].", main_site.c_str(), token.Token().c_str());
			AK_LOG_WARN <<"Update AppPushToken failed,main_site is:" << main_site << ",token is:"  <<token.Token();
			AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", error_msg, AKCS_MONITOR_ALARM_UPDATE_APP_PUSH_TOKEN);
		}
    }
}

void CAkUserManager::AddMainSiteToNodes(const std::string& main_site)
{
    PersonalAccountNodeInfoMap nodes;
    std::string user_info_uuid = dbinterface::ResidentPersonalAccount::GetUserInfoUUIDByAccount(main_site);
    dbinterface::ResidentPersonalAccount::GetNodesByUserInfoUUID(user_info_uuid, nodes);

    for(const auto& node_info : nodes)
    {
        //node及其房间下从账户的用户信息列表<userinfo_uuid, 当前房间account>
        std::map<std::string, std::string> account_list;
        std::string uid = node_info.first;
        
        NodeUidListIter it = nodeUIds_.find(node_info.second.node);
        if (it != nodeUIds_.end())
        {
            std::lock_guard<std::mutex> lock(node_uids_mutex_);
            it->second.insert(std::make_pair(uid, main_site)); //map insert
        }
        else
        {
            std::map<std::string,std::string> uidmap;
            uidmap.insert(std::make_pair(uid, main_site));
            std::lock_guard<std::mutex> lock(node_uids_mutex_);
            nodeUIds_[node_info.second.node] = uidmap;
        }
    }
}

//logout的时候执行
void CAkUserManager::RemoveAkUserByNodeId(const PersonalAccountNodeInfoMap& nodes, const std::string& main_site)
{
    for(const auto& node_info : nodes)
    {
        //获取所有node的真实站点，然后一一移除
        std::string uid = node_info.first;
        NodeUidListIter it = nodeUIds_.find(node_info.second.node);
        if (it != nodeUIds_.end())
        {
            std::lock_guard<std::mutex> lock(node_uids_mutex_);
            it->second.erase(uid); //uids map del one uid
        }
    }
    {
        std::lock_guard<std::mutex> lock(uid_mutex_);
        uidTokens_.erase(main_site);
    }
    GetAppPushTokenInstance()->deleteAppPushToken(main_site);
    dbinterface::Token::ClearToken(main_site);
}
//过期的时候执行
void CAkUserManager::RemoveAkUserTokenByUid(const std::string& uid)
{
    {
        std::lock_guard<std::mutex> lock(uid_mutex_);
        uidTokens_.erase(uid);
    }
    GetAppPushTokenInstance()->deleteAppPushToken(uid);
}

void CAkUserManager::RemoveUselessUserTokenByNode(const std::string& node)
{
    NodeUidListIter it = nodeUIds_.find(node);
    if (it == nodeUIds_.end())
    {
        return;
    }
    std::map<std::string,std::string> uid_map;
    {
        std::lock_guard<std::mutex> lock(node_uids_mutex_);
        uid_map = it->second;
    }

    std::set<std::string> uids; 
    dbinterface::ResidentPersonalAccount::GetAttendantListByUid(node, uids);
    for (auto& uid_info : uid_map)
    {
        std::string uid = uid_info.first;
        std::set<std::string>::iterator iter = uids.find(uid);
        if(iter == uids.end())  //缓存中的uid(真实站点)在数据库中不存在
        {        
            {
                std::lock_guard<std::mutex> lock(node_uids_mutex_);
                it->second.erase(uid); 
            }
            {
                std::lock_guard<std::mutex> lock(uid_mutex_);
                uidTokens_.erase(uid);
            }
            GetAppPushTokenInstance()->deleteAppPushToken(uid);
            AK_LOG_INFO << "delete app push token, site:" << uid;
        }
    }
}

void CAkUserManager::RemoveAll()
{
    nodeUIds_.clear();
    uidTokens_.clear();
    sipUids_.clear();
}

//更新sip-uid列表
void CAkUserManager::UpdateSipUidList(const std::string& sip, const std::string& uid)
{
    //std::lock_guard<std::mutex> lock(sip_mutex_);
    SipUidList::iterator it = sipUids_.find(sip);   //终端用户app的sip账号对应的uid
    if (it == sipUids_.end())  //sip-uid是固定的,所以只需要新建,不需要刷新
    {
        sipUids_[sip] = uid;
    }
}

//uid->nick name
std::string CAkUserManager::GetNickNameByUid(const std::string& uid) const
{
    std::string nick_name;
    DaoPerGetNickNameByUid(uid, nick_name);
    return nick_name;
}

//uid->nick name
void CAkUserManager::GetNickNameAndNodeByUid(const std::string& uid, std::string& nick_name, std::string& Node) const
{
    DaoPerGetNickNameAndNodeByUid(uid, nick_name, Node);
}

void CAkUserManager::GetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& nick_name, std::string& Node, int& manager_id) const
{
    DaoPerGetNickNameAndNodeAndMngByUid(uid, nick_name, Node, manager_id);
}

//modified by chenyc,v5.0,2012-25,LogOut状态只需要从session中uid是否挂在在csmain的sid下面即可判断.
//记录在app上执行过logout的uid
void CAkUserManager::SetLogOutStatus(const std::string& uid)
{
    //todo,处理cssessio的注销
    g_sm_client_ptr->RemoveUid(uid, g_logic_srv_id);
}

NodeUidList CAkUserManager::GetNodeUidList()
{
    std::lock_guard<std::mutex> lock(node_mutex_);
    return nodeUIds_;
}

bool CAkUserManager::IsUidOnLine(const std::string& uid)
{
    if (uid.length() == 0)
    {
        AK_LOG_INFO << "parameter error! uid is null!";
        return false;
    }
    std::lock_guard<std::mutex> lock(uid_mutex_);
    UIdTokenListIter Iter = uidTokens_.find(uid);
    if (Iter == uidTokens_.end())
    {
        return false;
    }
    if ((Iter->second.MobileOnline()))
    {
        return true;
    }
    return false;
}


