#include "stdafx.h"
#include <functional>
#include <evpp/tcp_conn.h>
#include <evpp/buffer.h>
#include "util_string.h"
#include "AkcsMsgCodec.h"
#include "csmainserver.h"
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "AkcsServer.h"
#include "AkcsMonitor.h"
#include "InnerSt.h"
#include "AkcsRequestRecorder.h"
#include "DclientMsgDef.h"
#include "MsgIdToMsgName.h"

extern AKCS_CONF gstAKCSConf; 
extern AccessServer* g_accSer_ptr;
extern Beanstalk::Client* g_beanstalkd_client_ptr;

void AkcsMsgCodec::Init()
{
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(ACC_TCP_BUSSINESS, ACC_TCP_PERIOD, ACC_TCP_NUM, ACC_TCP_KEY_EXPIRE,
            std::bind(&AkcsMsgCodec::TcpAttackCallback, this, std::placeholders::_1, std::placeholders::_2));//需要保证this的声明周期
}

//跟设备的消息交互解析
void AkcsMsgCodec::OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
{
    while (buf->size() >= kHeaderLen)
    {
        //检查是否能够构成一个完整的报文,保证头部是 0XBCDE 消息头
        unsigned char magic[2] = {0};
        ::memcpy(magic, buf->data(), 2);
        if (((unsigned char)magic[0] != 0XBC) || ((unsigned char)magic[1] != 0XDE))
        {
            AK_LOG_WARN << "The Msg packet head is not begin with 0XBCDE " << conn->remote_addr();
            //清空buf的内容
            buf->Reset();
            conn->Close();
            BussinessLimits(conn);
            return ;
        }

        uint16_t len1, len2;
        ::memcpy(&len1, buf->data() + 6, 2);//参见消息协议
        ::memcpy(&len2, buf->data() + 8, 2);
        const int32_t  headLen = ::ntohs(len1);
        const int32_t  bodyLen = ::ntohs(len2);
        const int32_t  msgLen = headLen + bodyLen;
		//added by chenyc,2020.07.07,根据结构体 SOCKET_MSG_NORMAL       定义消息体长度最长为4096.
        if (msgLen > 65536 || (std::size_t)msgLen < kHeaderLen || bodyLen > 4096)
        {
            AK_LOG_WARN << "Invalid length . len: " << msgLen << "bodylen: " << bodyLen;
            conn->Close(); //下回epoll返回时,就会返回该tcpconn断开的消息
            BussinessLimits(conn);
            break;
        }

        if (buf->size() >= (std::size_t)msgLen)
        {
            //buf->Skip(kHeaderLen); //对于akcs消息头也要传过去,否则下面的业务流程无法进行
            //取出消息体即可,对于akcs是不行的,因为消息id也在消息头\消息体长度标示符的里面,所以不行,这个设计不好
            std::string message(buf->NextString(msgLen));

            // 根据消息ID进行限流
            if (!ConnMsgRateLimit(conn, message))
            {
                //解码完之后，保证是一条完整的消息，然后就调用完整的消息处理函数
                accSerPtr->OnStringMessage(conn, message); 
            }
        }
        else
        {
            AK_LOG_WARN << "The Msg packet is still not complete, from " << conn->remote_addr();
            break;
        }
    }
}

void AkcsMsgCodec::BussinessLimits(const evpp::TCPConnPtr& conn)
{
    //chenyc 2019-11-25,记录出错的次数,达到一定频率的时候,通过iptables加入黑名单,目前暂时只处理ipv4.remote_addr格式: [::ffff:**************]:7033
    std::string remote_addr = conn->remote_addr();
    std::size_t found = remote_addr.find("[::ff");
    if (found != std::string::npos)
    {
        remote_addr = remote_addr.substr(found + sizeof("[::ffff:") - 1); // **************]:7033 sizeof会多计算结束符
        std::size_t found2 = remote_addr.find("]:");
        std::string remote_port = remote_addr.substr(found2);// ]:7033
        remote_addr = remote_addr.substr(0, remote_addr.size() - remote_port.size());//**************
        AKCS::Singleton<BussinessLimit>::instance().AddBussiness(ACC_TCP_BUSSINESS, remote_addr);
        AK_LOG_INFO << " " << conn->remote_addr();
    }
    return;
}
void AkcsMsgCodec::TcpAttackCallback(const std::string& bussiness, const std::string& key)
{
    AK_LOG_WARN << "there is one attack happens, iptables input drop, ip is " << key;
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("csmain", key);
}

// 根据消息ID限流,限制每个conn几秒内处理几次同个MsgID的消息
bool AkcsMsgCodec::ConnMsgRateLimit(const evpp::TCPConnPtr& conn, const std::string& message)
{
	if (gstAKCSConf.msg_id_limit_switch == 0)
	{
		return false;
	}

	DevicePtr conn_dev;
	if (g_accSer_ptr->GetClientFromConn(conn, conn_dev) != 0)
	{
		return false;
	}

	SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)message.data();
    int msgid = normal_msg->message_id & SOCKET_MSG_ID_MASK;
    if (msgid == MSG_FROM_DEVICE_HEART_BEAT || msgid == MSG_FROM_DEVICE_ACK_HEARTBEAT)
    {
        //心跳回复去掉
        return false;
    }
	std::string msg_id_str = GetMsgIDStr(msgid);


    if (conn_dev->IsDev())
    {
        // 进行请求统计
        if (gstAKCSConf.request_statics_switch)
        {
            CAkcsRequestRecorder::getInstance().RecordRequest(conn_dev->GetMAC(), msg_id_str);
        }

        // motion有单独限流,直接跳过；包裹检测不做限流
        if (msgid == MSG_FROM_DEVICE_MOTION_ALERT)
        {
            return false;
        }

        // 进行限流判断
        if (!conn_dev->TryAcquireMsg(msg_id_str))
        {
            AK_LOG_INFO << "MsgRateLimit TryAcquireMsg mac = " << conn_dev->GetMAC() << ", msg_id = " << msg_id_str << ", msgname = " << MsgIdToMsgName::GetDeclientMessageName(msgid) ;
            return true;
        }
    }

    return false;
}
