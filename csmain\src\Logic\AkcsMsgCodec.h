#pragma once

#include <functional>
#include <evpp/tcp_conn.h>
#include <evpp/buffer.h>

class AccessServer;
class AkcsMsgCodec
{
public:
    AkcsMsgCodec(AccessServer* paccSer)
        : accSerPtr(paccSer)
    {}
    void Init();
    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf);
private:
    //added by chenyc,2019-11-26,解码发现对端错误时,进行错误次数的统计,实现攻击拒绝
    void BussinessLimits(const evpp::TCPConnPtr& conn);
    bool ConnMsgRateLimit(const evpp::TCPConnPtr& conn, const std::string& message);
    void TcpAttackCallback(const std::string& bussiness, const std::string& key);
private:
    AccessServer* accSerPtr;
    const static size_t kHeaderLen = 10;
    const static char ipc_ip[];
    const static char ipc_ipv6[];
};
