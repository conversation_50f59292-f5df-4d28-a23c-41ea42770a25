#include "stdafx.h"
#include "AlarmControl.h"
#include "Alarm.h"
#include "PersonnalAlarm.h"

CAlarmControl* GetAlarmControlInstance()
{
    return CAlarmControl::GetInstance();
}

CAlarmControl::CAlarmControl()
{

}

CAlarmControl::~CAlarmControl()
{

}

CAlarmControl* CAlarmControl::instance = NULL;

CAlarmControl* CAlarmControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAlarmControl();
    }

    return instance;
}
/* add by chenzhx
//获取ALARM数量
uint32_t CAlarmControl::GetAlarmCount(uint32_t alarm_status)
{
    return GetAlarmInstance()->GetAlarmCount(alarm_status);
}

//根据状态获取ALARM列表
ALARM *CAlarmControl::GetAlarmList(uint32_t alarm_status, uint32_t item_from, uint32_t item_count)
{
    return GetAlarmInstance()->GetAlarmList(alarm_status, item_from, item_count);
}

//销毁ALARM列表
void CAlarmControl::DestoryAlarmList(ALARM *alarm_header)
{
    return GetAlarmInstance()->DestoryAlarmList(alarm_header);
}
*/
//获取ALARM详细信息
int CAlarmControl::GetAlarm(uint32_t id, ALARM* alarm)
{
    return GetAlarmInstance()->GetAlarm(id, alarm);
}
/*add by chenzhx
//删除ALARM
int CAlarmControl::DeleteAlarm(uint32_t id)
{
    return GetAlarmInstance()->DeleteAlarm(id);
}
*/
//处理ALARM
int CAlarmControl::DealAlarm(ALARM* alarm)
{
    return GetAlarmInstance()->DealAlarm(alarm);
}

//添加ALARM
int CAlarmControl::AddAlarm(ALARM* alarm)
{
    return GetAlarmInstance()->AddAlarm(alarm);
}

//更新ALARM状态
int CAlarmControl::DealAlarmStatus(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info)
{
    return GetAlarmInstance()->DealAlarmStatus(alarm_deal_info);
}

//个人终端用户,更新ALARM状态
int CAlarmControl::PersonnalDealAlarmStatus(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& personnal_alarm_deal_info)
{
    return GetPersonnalAlarmInstance()->DealAlarmStatus(personnal_alarm_deal_info);
}

