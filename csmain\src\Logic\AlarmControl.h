#ifndef __ALARM_CONTROL_H__
#define __ALARM_CONTROL_H__

#pragma once
#include "dbinterface/AlarmDB.h"

class CAlarmControl
{
public:
    CAlarmControl();
    ~CAlarmControl();

    //获取ALARM数量
    uint32_t GetAlarmCount(uint32_t alarm_status);

    //根据状态获取ALARM列表
    ALARM* GetAlarmList(uint32_t alarm_status, uint32_t item_from, uint32_t item_count);

    //销毁ALARM列表
    void DestoryAlarmList(ALARM* alarm_header);

    //获取ALARM详细信息
    int GetAlarm(uint32_t id, ALARM* alarm);

    //删除ALARM
    int DeleteAlarm(uint32_t id);

    //处理ALARM
    int DealAlarm(ALARM* alarm);

    //添加ALARM
    int AddAlarm(ALARM* alarm);
    int DealAlarmStatus(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info);
    int PersonnalDealAlarmStatus(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& personnal_alarm_deal_info);
    static CAlarmControl* GetInstance();
private:
    static CAlarmControl* instance;

};

CAlarmControl* GetAlarmControlInstance();

#endif