#include "BookingTmpkeyHandler.h"
#include "AkcsCommonDef.h"
#include "dbinterface/Account.h"
#include "csmainserver.h"
#include "util_time.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "DeviceSetting.h"

extern std::map<string, AKCS_DST> g_time_zone_DST;

BookingTmpkeyHandler::BookingTmpkeyHandler(const std::string& mac) : mac_(mac)
{
    is_bind_with_amenity_ = true;
    if (0 != dbinterface::AmenityDevice::GetAmenityDeviceByDeviceMac(mac, amenity_device_info_))
    {
        is_bind_with_amenity_ = false;
    }
}

int BookingTmpkeyHandler::CheckBookingTmpKeyValid(const std::string& tmpkey)
{
    if (!is_bind_with_amenity_)
    {
        return BookingTmpkeyCheckRes::CHECK_RES_NOT_BIND_AMENITY;
    }

    //根据项目uuid获取时区信息
    dbinterface::AccountInfo account_info;
    if (0 != dbinterface::Account::GetAccountByUUID(amenity_device_info_.project_uuid, account_info))
    {
        AK_LOG_WARN << "get project info failed. project_uuid=" << amenity_device_info_.project_uuid;
        return BookingTmpkeyCheckRes::CHECK_RES_FAILED;
    }
    //根据时区信息获取当前时间
    std::string now_time = GetNodeNowDateTimeByTimeZoneStr(account_info.timezone, g_time_zone_DST);

    if (0 != dbinterface::AmenityReservation::GetAmenityReservationByTmpKeyAndTime(tmpkey, now_time, amenity_reservation_info_))
    {
        AK_LOG_WARN << "booking temp key code not found. check failed. code=" <<tmpkey;
        return BookingTmpkeyCheckRes::CHECK_RES_TMPKEY_NOT_FOUND;
    }

    if (0 != strcmp(amenity_device_info_.amenity_uuid, amenity_reservation_info_.amenity_uuid))
    {
        AK_LOG_WARN << "temp key amenity no match device amenity.";
        return BookingTmpkeyCheckRes::CHECK_RES_AMENITY_NOT_MATCH;
    }

    //预约状态正常校验
    if (amenity_reservation_info_.status != AmenityReservationStatus::AMENITY_RESERVATION_SUCCESS)
    {
        AK_LOG_WARN << "amenity reservation check failed. status=" << amenity_reservation_info_.status;
        return BookingTmpkeyCheckRes::CHECK_RES_RESERVATION_STATUS_UNNORMAL;
    }

    //次数校验
    if (amenity_reservation_info_.key_used_counts >= amenity_reservation_info_.key_allowed_counts)
    {
        AK_LOG_WARN << "amenity tmpkey check failed. allowed count=" << amenity_reservation_info_.key_allowed_counts
                                   << ", already used count=" << amenity_reservation_info_.key_used_counts;
        return BookingTmpkeyCheckRes::CHECK_RES_TMPKEY_USED_TIME_LIMIT;
    }

    return BookingTmpkeyCheckRes::CHECK_RES_SUCCESS;
}

void BookingTmpkeyHandler::GetBookingTmpkeyInfo(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY &tmpkey_info)
{
    //获取设备relay信息
    std::string relay_jsonstr;
    std::string security_relay_jsonstr;
    if (GetDeviceSettingInstance()->GetAllRelayByMac(mac_, relay_jsonstr, security_relay_jsonstr) < 0)
    {
        AK_LOG_WARN << "GetRelayByMac failed.";
        return;
    }
    int relay = 0;
    int serelay = 0;
    //获取实际开启relay
    GetValueByRelay(relay_jsonstr, relay);
    GetValueByRelay(security_relay_jsonstr, serelay);
    std::string relay_val = RelayToString(relay);
    std::string se_relay_val = RelayToString(serelay);

    Snprintf(tmpkey_info.relay, sizeof(tmpkey_info.relay), relay_val.c_str());
    Snprintf(tmpkey_info.security_relay, sizeof(tmpkey_info.security_relay), se_relay_val.c_str());

    if(strlen(tmpkey_info.relay) == 0 && strlen(tmpkey_info.security_relay) == 0)
    {
        //兼容旧设备relay为空会全开的问题
        tmpkey_info.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
        return;
    }

    //unit_apt获取
    dbinterface::PersonalAppTmpKey::GetUnitAptByRoomUUID(amenity_reservation_info_.room_uuid, tmpkey_info);

    //perID获取
    std::string account;
    dbinterface::ResidentPersonalAccount::GetAccountByUUID(amenity_reservation_info_.personal_account_uuid, account);
    Snprintf(tmpkey_info.account, sizeof(tmpkey_info.account), account.c_str());

    //预约uuid
    Snprintf(tmpkey_info.amenity_reservation_uuid, sizeof(tmpkey_info.amenity_reservation_uuid), amenity_reservation_info_.uuid);
}