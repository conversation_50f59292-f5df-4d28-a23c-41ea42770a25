#ifndef __BOOKING_TMPKEY_HANDLER_H__
#define __BOOKING_TMPKEY_HANDLER_H__

#include "dbinterface/resident/AmenityDevice.h"
#include "dbinterface/AmenityReservation.h"
#include <string>
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"

class BookingTmpkeyHandler
{
private:
    AmenityReservationInfo  amenity_reservation_info_;
    AmenityDeviceInfo          amenity_device_info_;
    bool                                 is_bind_with_amenity_; //是否绑定公共设施
    std::string                        mac_;


public:
    BookingTmpkeyHandler(const std::string& mac);
    ~BookingTmpkeyHandler() = default;

    int CheckBookingTmpKeyValid(const std::string& tmpkey);
    void GetBookingTmpkeyInfo(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY &tmpkey_info);
};


#endif //__BOOKING_TMPKEY_HANDLER_H__