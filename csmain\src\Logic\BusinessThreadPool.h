#ifndef __CSMAIN_SERVER_BUSINESS_POOL_H__
#define __CSMAIN_SERVER_BUSINESS_POOL_H__

#include <vector>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <string>
#include "AkLogging.h"
class BusinessThreadPool {
public:
    explicit BusinessThreadPool(size_t num_queues) 
        : queues_(num_queues),
          mutexes_(num_queues),
          conditions_(num_queues),
          stops_(num_queues, false) {
        // 每个队列绑定一个线程
        for (size_t i = 0; i < num_queues; ++i) {
            workers_.emplace_back([this, i] {
                while (true) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(mutexes_[i]);
                        conditions_[i].wait(lock, [this, i] {
                            return stops_[i] || !queues_[i].empty();
                        });
                        if (stops_[i] && queues_[i].empty())
                        {
                            return;
                        }
                        task = std::move(queues_[i].front());
                        queues_[i].pop();
                    }
                    task(); // 执行任务
                }
            });
        }
    }


    void Enqueue(const std::string& ip, std::function<void()> task) {
        size_t index = HashIP(ip) % queues_.size(); // 计算队列索引
        {
            std::unique_lock<std::mutex> lock(mutexes_[index]);
            queues_[index].emplace(std::move(task));
            if (queues_[index].size() > 500)
            {
                AK_LOG_WARN << "csmain bussiness message queue number : " << queues_[index].size();
            }
        }
        conditions_[index].notify_one();
    }

    size_t MaxQueueSize() {
        size_t max_size = 0;
        for (size_t index = 0; index < queues_.size(); ++index) {
            std::unique_lock<std::mutex> lock(mutexes_[index]);
            max_size = std::max(max_size, queues_[index].size());
        }
        return max_size;
    }

    ~BusinessThreadPool() {
        // 通知所有队列停止
        for (size_t i = 0; i < queues_.size(); ++i) {
            {
                std::unique_lock<std::mutex> lock(mutexes_[i]);
                stops_[i] = true;
            }
            conditions_[i].notify_one();
        }
        for (auto& worker : workers_) {
            if (worker.joinable()) worker.join();
        }
    }

private:
    size_t HashIP(const std::string& ip) {
        return std::hash<std::string>{}(ip);
    }

    std::vector<std::queue<std::function<void()>>> queues_; // 任务队列
    std::vector<std::mutex> mutexes_;                      // 每个队列的锁
    std::vector<std::condition_variable> conditions_;       // 每个队列的条件变量
    std::vector<bool> stops_;                               // 停止标志
    std::vector<std::thread> workers_;                      // 线程数
};


#endif //__CSMAIN_SERVER_BUSINESS_POOL_H__