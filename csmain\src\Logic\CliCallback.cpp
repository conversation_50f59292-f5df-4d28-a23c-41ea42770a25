#include "stdafx.h"
#include "CliCallback.h"
#include "CliServer.h"
#include "csmainserver.h"
#include "AkcsServer.h"
#include "PushClient.h"
#include <boost/algorithm/string.hpp>


extern AKCS_CONF gstAKCSConf;
extern AccessServer* g_accSer_ptr;

//全局变量
CliServerCallbackMap CliServerCbs;
extern CliServer* g_cliSer_prt;
#define END_CMD "_EOF_"
#define CMD_FOR_FOUND  "_COMMAND"

int RegCliServerCb(const std::string command, CliServerCallbackFunc cbs)
{
    CliServerCbs[command] = cbs;
    return 0;
}

int ReturnEndofCmd(const evpp::TCPConnPtr& conn)
{
    conn->Send(END_CMD, strlen(END_CMD));
	return 0;
}

void CliServerSetMac(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    if (oParmas.size() == 0)
    {
        g_cliSer_prt->setMacToConn(conn, "");
        conn->Send("ok! now mac is null!", 3);
        return;
    }
    //校验mac是否在连接
    if (g_accSer_ptr->IsDevOnline(oParmas[0]) != 0) //不在线
    {
        char data[128] = "";
        snprintf(data, sizeof(data), "mac [%s] is out off line", oParmas[0].c_str());
        conn->Send(data, strlen(data));
    }
    else
    {
        g_cliSer_prt->setMacToConn(conn, oParmas[0]);
        conn->Send("ok!", 3);
    }
    return;
}

void CliServerGetMac(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    std::string mac;
    g_cliSer_prt->getMacFromConn(conn, mac);
    char data[128] = "";
    snprintf(data, sizeof(data), "mac [%s]", mac.c_str());
    conn->Send(data, strlen(data));
    return;
}


void CliServerAddCliip(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    if (g_cliSer_prt->isInnerIp(conn->remote_addr()))
    {
        g_cliSer_prt->addAllowIp(oParmas[0]);
        conn->Send("ok!", 3);
    }
    else
    {
        conn->Send("not inner ip, can not set allow ip", strlen("not inner ip, can not set allow ip"));        
    }
    return;
}

void CliServerDelCliip(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    if (g_cliSer_prt->isInnerIp(conn->remote_addr()))
    {
        g_cliSer_prt->delAllowIp(oParmas[0]);
        conn->Send("ok!", 3);
    }
    else
    {
        conn->Send("not inner ip, can not delete ip", strlen("not inner ip, can not delete ip"));        
    }
    return;
}

void CliServerGetCliip(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    std::string stAllowIp = "";
    g_cliSer_prt->AllowIp(stAllowIp);
    conn->Send(stAllowIp.c_str(), stAllowIp.length());
    return;
}

void CliServerReloadInnerip(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    g_cliSer_prt->reloadAllowInnerIp();
    conn->Send("ok!", 3);
    return;
}

void CliServerDoorRtspTest(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{
    if (oParmas.size() < 3)
    {
        AK_LOG_WARN  << "CliServerDoorRtspTest oParmas Error";
        return;
    }
    g_accSer_ptr->CliDoorRtspTest(oParmas[0], ATOI(oParmas[1].c_str()), oParmas[2]);
    conn->Send("ok!", 3);
    return;
}

/*
传入：_COMMAND "输入的命令"
返回：最长匹配 匹配命令 匹配命令...._EOF_
*/
void CliServerCommandFind(const evpp::TCPConnPtr& conn, const std::vector<std::string>& oParmas)
{

    std::vector<std::string> cmds;
    std::string stCur = "";
    std::string stAfter = "";
    std::string mach = "";
    std::string inputcmd = oParmas[0];
    boost::erase_first(inputcmd, "\"");
    boost::erase_last(inputcmd, "\"");
    std::map<std::string, CliServerCallbackFunc>::iterator iter = CliServerCbs.begin();
    while (iter != CliServerCbs.end())
    {
        if (boost::starts_with(iter->first, inputcmd)
                && inputcmd != iter->first //不能为匹配到的词
                && strcmp(CMD_FOR_FOUND, iter->first.c_str()))
        {
            stCur = iter->first;
            if (!cmds.empty())
            {
                std::size_t i = 0;
                std::size_t j = 0;
                while (i < stCur.length() && j < mach.length() &&  stCur[i] && mach[j] && stCur[i] == mach[j])
                {
                    i++;
                    j++;
                }
                mach = mach.substr(0, j);
            }
            else
            {
                stAfter = iter->first;
                mach = iter->first;
            }
            cmds.push_back(iter->first);
        }
        iter++;
    }

    vector<string>::iterator t ;
    std::string stCommands = mach;
    stCommands += " ";
    for (t = cmds.begin(); t != cmds.end(); t++)
    {
        stCommands += *t;
        stCommands += " ";
    }

    stCommands += END_CMD;
    conn->Send(stCommands.c_str(), stCommands.length());
    return;
}


int CliServerCbInit()
{
    RegCliServerCb("setmac", CliServerSetMac);
    RegCliServerCb("getmac", CliServerGetMac);
    RegCliServerCb("addcliip", CliServerAddCliip);
    RegCliServerCb("delcliip", CliServerDelCliip);
    RegCliServerCb("getcliip", CliServerGetCliip);
    RegCliServerCb("reload_csmain_ip", CliServerReloadInnerip);

    RegCliServerCb("door_rtsp_test", CliServerDoorRtspTest);

    RegCliServerCb(CMD_FOR_FOUND, CliServerCommandFind);//tab find
    return 0;
}

/*
oParmas[0] 是CMD, 后面为对应的参数
*/
int CliServerCallback(const evpp::TCPConnPtr& conn, const std::string& command, const std::vector<std::string>& oParmas)
{
    std::map<std::string, CliServerCallbackFunc>::iterator it = CliServerCbs.find(command);
    if (it != CliServerCbs.end())
    {
        CliServerCbs[command](conn, oParmas);
        return 0;
    }

    char data[128] = "";
    snprintf(data, sizeof(data), "the command is not exist!");
    conn->Send(data, strlen(data));
    return -1;
}




