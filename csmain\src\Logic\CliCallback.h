#ifndef __CLI_CALL_BACK_H_
#define __CLI_CALL_BACK_H_
#include "CliServer.h"


class CCliInfo;
typedef std::function<void(const evpp::TCPConnPtr& conn, const std::vector<std::string> &oParmas)> CliServerCallbackFunc;
typedef std::map<std::string, CliServerCallbackFunc> CliServerCallbackMap;

int RegCliServerCb(const std::string command, CliServerCallbackFunc cbs);
int CliServerCbInit();
int CliServerCallback(const evpp::TCPConnPtr& conn, const std::string& command, const std::vector<std::string>& oParmas);





#endif
