#include "stdafx.h"
#include <functional>
#include "util.h"
#include "CliServer.h"
#include "CliCallback.h"
#include "csmainserver.h"
#include "EtcdCliMng.h"

extern AccessServer* g_accSer_ptr;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern AKCS_CONF gstAKCSConf;

CliServer::CliServer(evpp::EventLoop* loop, const std::string& addr, const std::string& name, uint32_t thread_num)
    : server_(loop, addr, name, thread_num)
{
    is_load_inner_ip_already_ = false;
    //codec_.SetMsgCallback(  //codec_要先初始化,才能用SetMsgCallback
    server_.SetConnectionCallback(
        std::bind(&CliServer::OnConnectionCli, this, std::placeholders::_1));

    //tcp应用层有消息的时候,就调用该函数
    server_.SetMessageCallback(
        std::bind(&CliServer::OnMessageCli, this, std::placeholders::_1, std::placeholders::_2));
    loop->RunEvery(evpp::Duration(30.0), std::bind(&CliServer::onHeartBeatTimer, this));
    connectionBuckets_.resize(20); //保活600s = 30*20
    CliServerCbInit();
}

void CliServer::Start()
{
    server_.Init();
    server_.Start();
}
//保活失败,服务端执行conn->close(),也会回调该函数
//11-29:TCPConn::HandleClose()中的 conn_fn_(conn)会回调该函数
void CliServer::OnConnectionCli(const evpp::TCPConnPtr& conn) //
{
    AK_LOG_INFO << "CLI: " << conn->AddrToString() << " is " << (conn->IsConnected() ? "UP" : "DOWN");
    if (conn->IsConnected())
    {
        //判断是否加载过csmain内网ip,不能每次加载，防止攻击时候查询etcd压力太大
        if (!is_load_inner_ip_already_)
        {
            reloadAllowInnerIp();
            is_load_inner_ip_already_ = true;
        }
        if (!existAllowIp(conn->remote_addr()))
        {
            conn->Close();
            AK_LOG_WARN << "CLI: " << conn->remote_addr() << " is not allow connect!";
            return;
        }
        CliInfoPtr dev = std::make_shared<CCliInfo>(conn->remote_addr());
        {
            std::lock_guard<std::mutex> lock(mutex_);
            connection_clis_[conn] = dev;
        }

        EntryCliPtr entry(new EntryCli(conn));
        {
            std::lock_guard<std::mutex> lock(buckets_mutex_);
            connectionBuckets_.back().insert(entry);
        }
        WeakEntryPtr weakEntry(entry);
        evpp::Any any_tmp(weakEntry);
        conn->set_context(any_tmp);

        //当在TCPConn::HandleClose()中再次回调这个:conn_fn_(conn)时, conn->set_context(any_tmp)还未执行,导致
        //assert(!conn->context().IsEmpty()) 断言失败
        //GetControlInstance()->OnTcpConnMsg(conn);
    }
    else
    {
        if (conn->context().IsEmpty())
        {
            return;
        }
        //assert(!conn->context().IsEmpty());
        WeakEntryPtr weakEntry(evpp::any_cast<WeakEntryPtr>(conn->context()));
        CliInfoPtr dev;
        {
            std::lock_guard<std::mutex> lock(mutex_);
            dev = connection_clis_[conn];
            connection_clis_.erase(conn); //心跳的轮盘中保存的是conn的弱引用,不需要特殊理会,当保活时间到的时候自然会删除掉
        }
    }
}


void CliServer::reloadAllowInnerIp()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.etcd_server_addr);//"http://ip:port"
    std::set<std::string> csmain_inner_addrs;
    if (g_etcd_cli_mng->GetAllAccRpcInnerSrvs(csmain_inner_addrs) == 0)
    {
        for (auto& addr : csmain_inner_addrs)
        {
            std::string::size_type pos = addr.find(":");
            if (std::string::npos != pos)
            {
                std::string ip = addr.substr(0, pos);
                AK_LOG_INFO << "CLI: add allow inner ip connect. IP:" << ip;
                addAllowIp(ip);
                mapInnerIp[ip] = ip; 
            }
        }
    }
}


void CliServer::OnMessageCli(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
{
    if (::strncmp(conn->remote_addr().data(), "127.0.0.1", ::strlen("127.0.0.1")) == 0)
    {

    }
    std::string message(buf->NextString(buf->size()));
    OnStringMessageCli(conn, message);
}



//message已经是一条完整的消息了
void CliServer::OnStringMessageCli(const evpp::TCPConnPtr& conn, std::string& message)
{
    //开始业务处理
    OnSocketMsgCli(conn, message);

    //刷新保活时间,原则上是要在codec_里面的消息回调中保活,为避免造成类结构的侵入,挪到这里.
    //在客户端传输消息很慢时,即一个保活周期内无法完成一条完整的消息的传输时,会造成假性保活失败
    //assert(!conn->context().IsEmpty());
    WeakEntryPtr weakEntry(evpp::any_cast<WeakEntryPtr>(conn->context()));
    EntryCliPtr entry(weakEntry.lock());//原子操作,把它提升为强引用 EntryPtr，然后放到当前的 timing wheel 队尾。
    //保证~Entry()不被调用->不会析构,从而保证不执行conn->shutdown()->fd不会被关闭
    if (entry)
    {
        {
            std::lock_guard<std::mutex> lock(buckets_mutex_); //加锁,从原理上是不需要的。。。
            connectionBuckets_.back().insert(entry);
        }
    }
}


int CliServer::OnSocketMsgCli(const evpp::TCPConnPtr& conn, std::string& message)
{
    std::string mac, ret;
    if (getMacFromConn(conn, mac)
            && message.find("setmac") == std::string::npos
            && message.find("getmac") == std::string::npos
            && message.find("_COMMAND") == std::string::npos) //已经设置了设备 命令直接发送到设备
    {
        AK_LOG_INFO << "CLI: send cmd to mac:" << mac << " cmd:" << message;
        if (g_accSer_ptr->SendCommandToDevice(mac.c_str(), message, ret) != 0)
        {
            AK_LOG_WARN << "CLI: error " << ret;
            conn->Send(ret.c_str(), ret.length());
        }
        return 0;
    }
    message.erase(0, message.find_first_not_of(" "));
    message.erase(message.find_last_not_of(" ") + 1);

    char messages[256] = "";
    char command[256] = "";
    char param1[256] = "";
    snprintf(messages, sizeof(messages), "%s ", message.c_str());//多个空格作为下面的区分
    char* tmp_next = messages;
    char* tmp_head = messages;
    int index = 0;
    ::snprintf(command, sizeof(command), "%s", tmp_head);//头
    std::vector<std::string> oVec;
    //SplitString(message, " ", oVec); 直接用这个函数 最后面会有特殊字符十六进制00 应该是cli带过来的
    while (*tmp_next++)
    {
        if (*tmp_next == ' ')
        {
            *tmp_next = 0;
            if (index == 0)
            {
                ::snprintf(command, sizeof(command), "%s", tmp_head);
            }
            else
            {
                ::snprintf(param1, sizeof(param1), "%s", tmp_head);
                oVec.push_back(param1);
            }

            *tmp_next = ' ';
            while (*tmp_next == ' ')
            {
                tmp_next++;
            }
            tmp_head = tmp_next;
            index++;
        }
    }

    CliServerCallback(conn, command, oVec);
    return 0;
}

