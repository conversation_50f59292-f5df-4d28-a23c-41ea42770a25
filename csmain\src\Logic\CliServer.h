#ifndef __CLI_SERVER_H__
#define __CLI_SERVER_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include <evpp/tcp_server.h>
#include <evpp/any.h>
#include <boost/circular_buffer.hpp>
#include "SDMCMsg.h"
#include "util.h"


class CCliInfo;
struct EntryCli;
typedef std::shared_ptr<CCliInfo> CliInfoPtr;
typedef std::map<evpp::TCPConnPtr, CliInfoPtr> ConnectionCliList;
typedef ConnectionCliList::iterator ConnectionCliListIter;


class CCliInfo
{
public:
    CCliInfo(std::string addr)
    {
        addr_ = addr;
    }

public:
    void getMac(std::string& mac)
    {
        mac = mac_;
    }
    void setMac(const std::string& mac)
    {
        mac_ = mac;
    }

    std::string mac_;
    std::string addr_;

};


//typedef ConnectionList::iterator ConnectionListIter;

class CliServer
{
public:
    CliServer(evpp::EventLoop* loop, const std::string& addr, const std::string& name, uint32_t thread_num);

    void Start();

public:
    //friend struct Entry;
    //message已经是一条完整的消息了
    void OnStringMessageCli(const evpp::TCPConnPtr& conn, std::string& message);
    //对于akcs来说,就是在这里讲请求设备上报状态的消息下发了,同时保存设备的相关状态信息(含地址信息\联动系统等)
    void OnConnectionCli(const evpp::TCPConnPtr& conn);

    void onHeartBeatTimer()
    {
        std::lock_guard<std::mutex> lock(buckets_mutex_);
        connectionBuckets_.push_back(Bucket());
    }
    void OnMessageCli(const evpp::TCPConnPtr& conn, evpp::Buffer* buf);
    int OnSocketMsgCli(const evpp::TCPConnPtr& conn, std::string& message);

    int GetMacFromConnList(const evpp::TCPConnPtr& conn, std::string& mac)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionCliListIter it = connection_clis_.find(conn);
        if (it != connection_clis_.end())
        {
            CliInfoPtr Device = it->second;
            Device->getMac(mac);
            return 0;
        }
        return -1;
    }
    int setMacToConn(const evpp::TCPConnPtr& conn, const std::string& mac)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionCliListIter it = connection_clis_.find(conn);
        if (it != connection_clis_.end())
        {
            CliInfoPtr Device = it->second;
            Device->setMac(mac);
            return 0;
        }
        return -1;
    }

    int getMacFromConn(const evpp::TCPConnPtr& conn, std::string& mac)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionCliListIter it = connection_clis_.find(conn);
        if (it != connection_clis_.end())
        {
            CliInfoPtr Device = it->second;
            Device->getMac(mac);
            if (mac.length() == 0)
            {
                return 0;
            }
            return 1;
        }
        return 0;
    }

    int getConnByMac(const std::string& mac, evpp::TCPConnPtr& conn)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionCliListIter iter;
        iter = connection_clis_.begin();
        while (iter != connection_clis_.end())
        {
            CliInfoPtr Device = iter->second;
            std::string mactmp;
            Device->getMac(mactmp);
            if (mactmp == mac)
            {
                conn = iter->first;
                return 0;
            }
            iter++;
        }
        return -1;
    }

    void InitAllowIp(const char* AllowIPs)
    {
        std::string strIps(AllowIPs);
        std::vector<std::string> oVec;
        SplitString(strIps, ";", oVec);
        for (auto& strIps : oVec)
        {
            mapAllowIp[strIps] = strIps;
        }
    };
    void addAllowIp(const std::string& ip)
    {
        mapAllowIp[ip] = ip;
    }
    int isInnerIp(const std::string& ip)
    {
        map<std::string, std::string>::iterator iter;
        for (iter = mapInnerIp.begin(); iter != mapInnerIp.end(); iter++)
        {
            if (ip.find(iter->second) != std::string::npos)
            {
                return 1;
            }
        }    
        return 0;
    }



    void reloadAllowInnerIp();


    void delAllowIp(const std::string& ip)
    {
        mapAllowIp.erase(ip);
    }
    void AllowIp(std::string& ip)
    {
        map<std::string, std::string>::iterator iter;
        for (iter = mapAllowIp.begin(); iter != mapAllowIp.end(); iter++)
        {
            ip += iter->second + ";";
        }

    }
    int existAllowIp(const std::string& ip)
    {
        map<std::string, std::string>::iterator iter;
        for (iter = mapAllowIp.begin(); iter != mapAllowIp.end(); iter++)
        {
            if (ip.find(iter->second) != std::string::npos)
            {
                return 1;
            }
        }
        return 0;
    }
    void onDevRespContent(const std::string& mac, SOCKET_MSG_COMMAND_RESP& resp)
    {
        evpp::TCPConnPtr conn;
        if (getConnByMac(mac, conn) < 0)
        {
            AK_LOG_WARN << "CLI: cli to the Mac: " << mac << " connect is lost";
            return;
        }
        /*
        std::string stMsg = "Seq:";
        stMsg = stMsg + resp.sequence + "\r\n";
        stMsg = stMsg + "Resp:" + resp.message + "\r\n";
        conn->Send(stMsg.c_str(), stMsg.length());*/
        std::string message = resp.message;
        message = message + "\r\n\r\n";
        conn->Send(message.c_str(), message.length());
    }

public:
    typedef std::shared_ptr<EntryCli> EntryCliPtr;
    typedef std::weak_ptr<EntryCli> WeakEntryPtr;
    typedef std::unordered_set<EntryCliPtr> Bucket;
    typedef boost::circular_buffer<Bucket> WeakConnectionList;
private:
    std::mutex buckets_mutex_;
    WeakConnectionList connectionBuckets_;

    evpp::TCPServer server_;
    std::mutex mutex_;
    ConnectionCliList connection_clis_;

    std::map<std::string, std::string> mapAllowIp;
    std::map<std::string, std::string> mapInnerIp;
    bool is_load_inner_ip_already_;
};


struct EntryCli
{
    explicit EntryCli(const WeakTCPConnPtr& weakConn)
        : weakConn_(weakConn)
          // ,  paccServer(server)
    {
    }

    ~EntryCli()
    {
        evpp::TCPConnPtr conn = weakConn_.lock(); //提升为强引用
        if (conn)
        {
            conn->Close(); //会回调OnConnection,即使对端时非正常断开tcp连接
            //paccServer->connections_.erase(conn); //关闭掉应用层的conn,2017-10-19,这个不需要处理,因为网络库会回调OnConnection,在哪里已经关闭了
            //当客户端保活失败时,uid_conns跟mac_conns_不需要处理,只在要发送消息时,转换成强引用失败,再去删除这两个容器中的元素
            AK_LOG_WARN << "CLI: client " << conn->remote_addr().c_str() << " keepalive failed";
        }
    }

    WeakTCPConnPtr weakConn_;
};

#endif //__CLI_SERVER_H__

