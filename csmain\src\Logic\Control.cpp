#include "stdafx.h"
#include "Control.h"
#include "DeviceControl.h"
#include "MsgControl.h"
#include "KeyControl.h"
#include "AlarmControl.h"
#include "Device.h"
#include "NotifyMsgControl.h"

CControl* GetControlInstance()
{
    return CControl::GetInstance();
}

CControl::CControl()
{
}

CControl::~CControl()
{
}

CControl* CControl::instance = NULL;

CControl* CControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CControl();
    }

    return instance;
}


int CControl::Init()
{
    GetDeviceControlInstance()->Init();
    //UpdateAddressMD5(); add by chenzhx 20180328
    return 0;
}

//处理tcp client新建连接时下发请求状态
int CControl::OnTcpConnMsg(const evpp::TCPConnPtr& conn)
{
    //发送REQUEST_STATUS
    SOCKET_MSG_REQ_STATUS req_status_msg;
    memset(&req_status_msg, 0, sizeof(SOCKET_MSG_REQ_STATUS));
    _tcscpy_s(req_status_msg.protocal, sizeof(req_status_msg.protocal), PROTOCAL_NAME_DEFAULT);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;

    if (GetMsgControlInstance()->BuildReqStatusMsg(socket_message, &req_status_msg) < 0)
    {
        return -1;
    }

    std::string message(reinterpret_cast<char*>(socket_message->data), static_cast<size_t>(socket_message->size));   //string ctor string (const char* s, size_t n); ctor from c-string string (const char* s);
    conn->Send(message);
    return 0;
}
/* add by chenzhx 20180328
//获取ADDRESS列表文件的MD5
CString CControl::GetAddressMD5()
{
    return addr_md5_;
}

//更新地址文件的MD5, 注：这里保存到内存是为了不要每个设备过来都去查一次数据库，维护时要注意永远保持与数据库同步
void CControl::UpdateAddressMD5()
{
    //读取ADDRMD5
    SYSTEM_SETTING systemSetting;
    memset(&systemSetting, 0, sizeof(systemSetting));
    if(GetSystemSettingControlInstance()->GetSystemSetting(&systemSetting) == 0)
    {
        addr_md5_ = systemSetting.addr_md5;
    }
}*/

