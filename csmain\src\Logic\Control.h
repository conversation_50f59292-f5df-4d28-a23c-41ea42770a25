#ifndef __CONTROL_H__
#define __CONTROL_H__

#include "util_cstring.h"
#include "SDMCMsg.h"
#include <evpp/tcp_conn.h>

class CControl
{
public:
    CControl();
    ~CControl();

    static CControl* GetInstance();

    //初始化
    int Init();

    //处理tcp_conn新连接的消息
    int OnTcpConnMsg(const evpp::TCPConnPtr& conn);
    /* add by chenzhx 20180328
        //获取ADDRESS列表文件的MD5
        CString GetAddressMD5();

        //更新地址文件的MD5, 注：这里保存到内存是为了不要每个设备过来都去查一次数据库，维护时要注意永远保持与数据库同步
        void UpdateAddressMD5();
    */
    CString download_server_;

private:

    static CControl* instance;
    CString addr_md5_;
};

CControl* GetControlInstance();

#endif
