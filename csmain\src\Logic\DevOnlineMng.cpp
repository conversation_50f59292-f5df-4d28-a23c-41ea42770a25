#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "DevOnlineMng.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "DeviceControl.h"
#include "csmainserver.h"
#include "MsgControl.h"
#include "SnowFlakeGid.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/AlexaToken.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


extern AccessServer* g_accSer_ptr;
extern AKCS_CONF gstAKCSConf;


void CreateOnlineMacInfo(MacInfo &macinfo, const DEVICE_SETTING &dev_setting)
{
    macinfo.type  = dev_setting.type;
    macinfo.flags  = dev_setting.flags;
    macinfo.grade = dev_setting.grade;
    macinfo.firmware_number  = dev_setting.firmware;
    macinfo.is_personal  = dev_setting.is_personal;
    macinfo.init_status = dev_setting.init_status;
    macinfo.project_type = dev_setting.project_type;
    macinfo.mng_id = dev_setting.manager_account_id;
    snprintf(macinfo.node, sizeof(macinfo.node), "%s", dev_setting.device_node);
    snprintf(macinfo.mac, sizeof(macinfo.mac), "%s", dev_setting.mac);
    snprintf(macinfo.uuid, sizeof(macinfo.uuid), "%s", dev_setting.uuid);
}

DevOnlineMng::~DevOnlineMng()
{
    LOG_INFO << "Release DevOnlineMng.";
}

DevOnlineMng* DevOnlineMng::GetInstance()
{
    static DevOnlineMng dev_online_mng;
    return &dev_online_mng;
}

void DevOnlineMng::AddPerMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(per_online_mutex_);
    per_deque_.push_back(msg);
}

void DevOnlineMng::AddCommunityMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(comm_online_mutex_);
    comm_deque_.push_back(msg);    
}

void DevOnlineMng::AddOfficeMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(office_online_mutex_);
    office_deque_.push_back(msg);    
}

void DevOnlineMng::NotifyOnlineMsg(const SOCKET_MSG_DEV_ONLINE_NOTIFY&online_msg)
{
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(online_msg.mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << online_msg.mac << ") not connected.";
        return;
    }
    GetMsgControlInstance()->OnSendOnlineNotifyMsg(conn, online_msg);
}

void DevOnlineMng::UpdateDevicesConnInfo(MacInfo &macinfo, const ProjectUserInfo &project_info)
{
    if (macinfo.init_status == 0)
    {
        macinfo.init_status = 1;
        macinfo.enable_smarthome = project_info.enable_smarthome;
        Snprintf(macinfo.node_uuid, sizeof(macinfo.node_uuid), project_info.node_uuid);
        Snprintf(macinfo.project_uuid, sizeof(macinfo.project_uuid), project_info.project_uuid);
        Snprintf(macinfo.ins_uuid, sizeof(macinfo.ins_uuid), project_info.ins_uuid);

        //更新device_setting
        evpp::TCPConnPtr conn;
        if (g_accSer_ptr->GetDevConnByMac(macinfo.mac, conn) != 0)
        {
            AK_LOG_WARN << "device(" << macinfo.mac << ") not connected.";
            return;
        }

        g_accSer_ptr->UpdateDeviceProjectInfo(conn, macinfo);
    }
}

void DevOnlineMng::SendDevOnlineNotifyMsg(const MacInfo& macinfo)
{
    SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
    memset(&online_msg, 0, sizeof(online_msg));
    snprintf(online_msg.mac, sizeof(online_msg.mac),"%s", macinfo.mac);
    online_msg.unread_voice_count = 0;
    if (macinfo.type == DEVICE_TYPE_INDOOR)
    {
        int count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(macinfo.uuid);
        if (count > 0)
        {
            online_msg.unread_voice_count = count;
        }
    }
    snprintf(online_msg.uuid, sizeof(online_msg.uuid),"%s", macinfo.uuid);
    NotifyOnlineMsg(online_msg);
}

void DevOnlineMng::CheckPerOnlineMsg()
{
    MacInfo mac_info;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(per_online_mutex_);
        if (per_deque_.size() == 0)
        {
            return;
        }        
        per_deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        mac_info = tmp_deque.front();
        tmp_deque.pop_front();

        //更新设备连接的项目信息
        ProjectUserInfo project_info;
        ProjectInfo project;
        project.GetPersonalProjectInfo(mac_info.node, project_info);
        UpdateDevicesConnInfo(mac_info, project_info);
        
        // 判断设备是否关联alexa的用户,关联则需要发送心跳检测
        PostAlexaDevStatusChange(mac_info);        
    }
}

void DevOnlineMng::CheckCommunityOnlineMsg()
{
    MacInfo mac_info;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(comm_online_mutex_);
        if (comm_deque_.size() == 0)
        {
            return;
        }        
        comm_deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        mac_info = tmp_deque.front();
        tmp_deque.pop_front();
        
        //更新设备连接的项目信息
        ProjectInfo project;
        ProjectUserInfo project_info;
        project.GetCommProjectInfo(mac_info.mng_id, mac_info.node, project_info);
        UpdateDevicesConnInfo(mac_info, project_info);

        // 判断设备是否关联alexa的用户,关联则需要发送心跳检测
        PostAlexaDevStatusChange(mac_info);
    }
}

void DevOnlineMng::CheckOfficeOnlineMsg()
{
    MacInfo mac_info;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(office_online_mutex_);
        if (office_deque_.size() == 0)
        {
            return;
        }
        office_deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        mac_info = tmp_deque.front();
        tmp_deque.pop_front();

        //更新设备连接的项目信息
        ProjectInfo project;
        ProjectUserInfo project_info;
        project.GetCommProjectInfo(mac_info.mng_id, mac_info.node, project_info);
        UpdateDevicesConnInfo(mac_info, project_info);

        //设备在线后返回通知消息
        SendDevOnlineNotifyMsg(mac_info);

        // 判断设备是否关联alexa的用户,关联则需要发送心跳检测
        PostAlexaDevStatusChange(mac_info);
    }
}

void DevOnlineMng::AddPubDevRelateToAlexa(const std::string& mac)
{
    std::lock_guard<std::mutex> lock(public_alexa_mac_mutex_);
    public_alexa_mac_set_.insert(mac);
}

void DevOnlineMng::RemovePubDevRelateToAlexa(const std::string& mac)
{
    std::lock_guard<std::mutex> lock(public_alexa_mac_mutex_);
    public_alexa_mac_set_.erase(mac);
}

bool DevOnlineMng::DevRelateToAlexa(const std::string& mac)
{
    std::lock_guard<std::mutex> lock(public_alexa_mac_mutex_);
    return public_alexa_mac_set_.find(mac) != public_alexa_mac_set_.end();
}

// 判断设备是否关联alexa的用户,关联则需要发送心跳检测
void DevOnlineMng::PostAlexaDevStatusChange(const MacInfo& mac_info)
{
    AK_LOG_INFO << "mac:" << mac_info.mac << ",node_uuid:" << mac_info.node_uuid << ",project_uuid:" << mac_info.project_uuid;
    // 对于社区/办公/单住户,若存在alexa用户,则所有设备上线时状态都要推给web及发送心跳检测 (精准推送查询太多)
    bool add_alexa_heartbeat = false;
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    
    if (mac_info.project_type == project::PERSONAL)
    {
        // 单住户设备关联alexa用户就需要推送+心跳
        if (0 == dbinterface::AlexaToken::CheckDevRelateToAlexaUser(mac_info.node_uuid))
        {
            // 开启主动心跳检测
            add_alexa_heartbeat = true;
           
            // 推送设备上线状态
            GetMsgControlInstance()->PostAlexaChangeStatus(mac_info.mac, traceid);
            AK_LOG_INFO << "alexa device online notify web , mac :" << mac_info.mac << ", traceid : " << traceid;
        }
    }
    else
    {
        // 社区/办公project下的设备上下线都要推送, 只有apt下的设备要加入主动心跳
        if (0 == dbinterface::AlexaToken::CheckDevRelateToAlexaUser(mac_info.project_uuid))
        {
            // 推送设备上线状态
            GetMsgControlInstance()->PostAlexaChangeStatus(mac_info.mac, traceid);
            AK_LOG_INFO << "alexa device online notify web , mac :" << mac_info.mac << ", traceid : " << traceid;
            
            // apt下的设备关联alexa用户,才开启主动心跳检测
            dbinterface::AlexaTokenInfo alexa_token_info;
            if (0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(mac_info.node_uuid, alexa_token_info))
            {
                // apt下设备: 开启主动心跳检测
                add_alexa_heartbeat = true;
            }
            else
            {
                // pub设备: 不开启主动心跳检测, 插入set用于下线通知
                AddPubDevRelateToAlexa(mac_info.mac);
            }
        }
    }

    if (add_alexa_heartbeat)
    {
        evpp::TCPConnPtr conn;
        if (g_accSer_ptr->GetDevConnByMac(mac_info.mac, conn) != 0)
        {
            AK_LOG_WARN << "device( " << mac_info.mac << " ) not connected, add alexa heartbeat failed";
            return;
        }

        // 开启主动心跳检测
        g_accSer_ptr->AlexaDevConnDetect(conn);
        AK_LOG_INFO << "dev " << mac_info.mac << " relate to alexa, dev online add heartbeat detect, traceid : " << traceid;

        if (mac_info.type == DEVICE_TYPE_INDOOR)
        {
            // 室内机获取Sensor类型,设备要求mode默认下发0
            SOCKET_MSG_SENSOR_TRIGGER sensor_trigger;
            sensor_trigger.mode = 0;
            Snprintf(sensor_trigger.mac, sizeof(sensor_trigger.mac), mac_info.mac);
            GetMsgControlInstance()->SendRequestSensorTrigger(conn, sensor_trigger);
            
        }
    }
}

int DevOnlineMng::Init()
{   
    pthread_create(&thread_process, NULL, &DevOnlineThread, (void*)this);
    LOG_INFO << "Create DevOnlineMng thread.";
    return 0;
}


void* DevOnlineMng::DevOnlineThread(void* this_mng)
{
    DevOnlineMng *mng = (DevOnlineMng*)this_mng;
    while (true)
    {
        mng->CheckOfficeOnlineMsg();
        mng->CheckCommunityOnlineMsg();
        mng->CheckPerOnlineMsg();
        sleep(2);
    }

    return NULL;
}


