#ifndef __CSMAIN_ONLINE_MANAGE_H__
#define __CSMAIN_ONLINE_MANAGE_H__
#include <string>
#include <mutex>
#include <deque>
#include <pthread.h>
#include <unistd.h>
#include <unordered_set>
#include "InnerSt.h"
#include "SDMCMsg.h"
#include "dbinterface/ProjectInfo.h"

typedef struct MacInfo_t
{
    char mac[16];
    char uuid[64];
    char node[64];
    char ins_uuid[64];
    char node_uuid[64];
    char project_uuid[64];
    int type;
    int flags;
    int firmware_number; 
    int is_personal;
    int init_status;
    int mng_id;
    int project_type;
    int enable_smarthome;
    unsigned int grade;

    MacInfo_t() {
        memset(this, 0, sizeof(*this));
    }
}MacInfo;

void CreateOnlineMacInfo(MacInfo &macinfo, const DEVICE_SETTING &dev_setting);

class DevOnlineMng
{
public:
    DevOnlineMng() {}
    ~DevOnlineMng();

    static DevOnlineMng* GetInstance();
    void AddPerMac(const MacInfo &msg);
    void AddCommunityMac(const MacInfo &msg);
    void AddOfficeMac(const MacInfo &msg);
    bool DevRelateToAlexa(const std::string& mac);
    void RemovePubDevRelateToAlexa(const std::string& mac);
    static void* DevOnlineThread(void* mng);
    int Init();
private:
    void NotifyOnlineMsg(const SOCKET_MSG_DEV_ONLINE_NOTIFY&online_msg);
    void UpdateDevicesConnInfo(MacInfo &macinfo, const ProjectUserInfo &project_info);
    void CheckPerOnlineMsg();
    void CheckOfficeOnlineMsg();
    void CheckCommunityOnlineMsg();    
    void AddPubDevRelateToAlexa(const std::string& mac);
    void SendDevOnlineNotifyMsg(const MacInfo& macinfo);
    void PostAlexaDevStatusChange(const MacInfo& mac_info);
    pthread_t thread_process;
    std::mutex per_online_mutex_;
    std::deque<MacInfo> per_deque_;

    std::mutex comm_online_mutex_;
    std::deque<MacInfo> comm_deque_;

    std::mutex office_online_mutex_;
    std::deque<MacInfo> office_deque_;    

    std::mutex public_alexa_mac_mutex_;
    std::unordered_set<std::string> public_alexa_mac_set_; // 关联alexa的公共设备
};



#endif // __CSMAIN_ONLINE_MANAGE_H__
