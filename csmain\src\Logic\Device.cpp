#include "stdafx.h"
#include "Device.h"
#include "util.h"

extern MessageRateLimitMap gstAKCSMsgRateLimitConf; // msg id限流配置

CDevice::CDevice(const std::string& addr)
    : remote_addr_(addr)
{
    types_ = csmain::COMMUNITY_NONE;
    ::memset(&m_deviceSetting, 0, sizeof(m_deviceSetting));
}

CDevice::~CDevice()
{

}

int CDevice::GetDeviceSetting(DEVICE_SETTING* device_setting)
{
    if (device_setting == NULL)
    {
        return -1;
    }

    memcpy(device_setting, &m_deviceSetting, sizeof(DEVICE_SETTING));
    return 0;
}

void CDevice::SetDeviceSetting(IN const DEVICE_SETTING* device_setting)
{
    if (device_setting != NULL)
    {
        memcpy(&m_deviceSetting, device_setting, sizeof(DEVICE_SETTING));
    }
}

const std::string CDevice::GetMAC() const
{
    return m_deviceSetting.mac;
}

void CDevice::SetMAC(CString mac)
{
    Snprintf(m_deviceSetting.mac, sizeof(m_deviceSetting.mac), mac.GetBuffer());
}

std::string CDevice::GetAreaNode()
{
    return m_deviceSetting.device_node;
}

int CDevice::Type() const
{
    return types_;
}
void CDevice::SetType(int t)
{
    types_ = t;
}


//目前仅personnal app用到这个
void CDevice::SetContext(const boost::any& context)
{
    context_ = context;
}

const boost::any& CDevice::GetContext() const
{
    return context_;
}

int  CDevice::GetPerMainSiteUid(std::string& user) const
{
    //获取主站点uid
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        user = stApp.user;
        return 0;
    }
    return -1;
}

int  CDevice::GetPerUid(std::string& user) const
{
    //获取上报站点的uid
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        user = stApp.report_user;
        return 0;
    }
    return -1;
}

//由于多套房APP会关联多个Node(房间)，所以要确定某套房的站点uid时，需要使用此函数
//一人多套房 只能关联一套房(Node)里面的一个账号，因此可以用Node来找对应的Uid
int  CDevice::GetPerUidByNode(std::string& user, const std::string& node) const
{    
    //获取某套房站点的uid
    if (IsApp())
    {
        for(const auto& node_info : app_nodes_)
        {
            std::string node_tmp = node_info.second.node;
            if(node == node_tmp)
            {
                //返回对应node的uid
                user = node_info.first;
                return 0;
            }
        }
    }
    return -1;
}

int  CDevice::GetPerNodeApp(std::string& node) const
{
    //获取上报站点的node
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        
        auto iter = app_nodes_.find(stApp.report_user);
        if(iter != app_nodes_.end())
        {
            node = iter->second.node;
        }
        return 0;
    }
    return -1;
}

int  CDevice::GetPerNodeBySite(std::string& node, const std::string& uid) const
{
    //获取某站点的node
    if (IsApp())
    {
    
        auto iter = app_nodes_.find(uid);
        if(iter != app_nodes_.end())
        {
            //返回对应站点的node
            node = iter->second.node;
            return 0;
        }
    }
    return -1;
}

int  CDevice::SetAppNodes(const PersonalAccountNodeInfoMap& nodes)
{
    if (IsApp())
    {
        app_nodes_ = nodes;
        return 0;
    }    
    return -1;
}
int  CDevice::GetAppNodes(PersonalAccountNodeInfoMap& nodes) const
{
    if (IsApp())
    {
        nodes = app_nodes_;        
        return 0;
    }
    return -1;
}

int  CDevice::GetPerNodeDev(std::string& node) const
{
    if (IsDev())
    {
        if(strlen(m_deviceSetting.device_node) > 0)
        {
            node = m_deviceSetting.device_node;
            return 0;
        }
    }
    return -1;
}

int CDevice::GetAppUserName(std::string& username) const
{
    //获取上报站点的username
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        
        auto iter = app_nodes_.find(stApp.report_user);
        if(iter != app_nodes_.end())
        {
            username = iter->second.username;
        }
        return 0;
    }
    return -1;
}

int  CDevice::GetAppUserNameBySite(std::string& username, const std::string& uid) const
{
    //获取某站点的username
    if (IsApp())
    {
    
        auto iter = app_nodes_.find(uid);
        if(iter != app_nodes_.end())
        {
            //返回对应站点的username
            username = iter->second.username;
            return 0;
        }
    }
    return -1;
}


int  CDevice::GetAppUidAndAppToken(std::string& Uid, std::string& AppToken) const
{
    //获取主站点的uid和app_token
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        Uid = stApp.user;
        AppToken = stApp.app_token;
        return 0;
    }
    return -1;
}


//设备localtion修改，修改内存数据
int CDevice::SetDevLocation(char* location)
{
    if (IsDev())
    {
        if (location)
        {
            snprintf(m_deviceSetting.location, sizeof(m_deviceSetting.location), "%s", location);
        }
        return 0;
    }
    return -1;
}

//更新设备ins,dis信息
int CDevice::UpdateProjectInfo(const MacInfo &mac_info)
{
    if (IsDev())
    {
        m_deviceSetting.init_status = mac_info.init_status;
        m_deviceSetting.enable_smarthome = mac_info.enable_smarthome;
        snprintf(m_deviceSetting.node_uuid, sizeof(m_deviceSetting.node_uuid), "%s", mac_info.node_uuid);
        snprintf(m_deviceSetting.project_uuid, sizeof(m_deviceSetting.project_uuid), "%s", mac_info.project_uuid);
        snprintf(m_deviceSetting.ins_uuid, sizeof(m_deviceSetting.ins_uuid), "%s", mac_info.ins_uuid);
        return 0;
    }
    return -1;
}

unsigned int CDevice::GetUnitID()
{
    if (IsDev())
    {
        return m_deviceSetting.unit_id;
    }
    return 0;

}

unsigned int CDevice::GetMngAccountID()
{
    if (IsDev())
    {
        return m_deviceSetting.manager_account_id;
    }
    return 0;
}

unsigned int CDevice::GetDclientVer()
{
    if (IsDev())
    {
        return m_deviceSetting.dclient_version;
    }
    return 0;
}

unsigned int CDevice::GetDevGrade()
{
    return m_deviceSetting.grade;
}

unsigned int CDevice::GetDevType()
{
    return m_deviceSetting.type;
}  

bool CDevice::IsApp() const
{
    if (types_ == csmain::PERSONNAL_APP || types_ == csmain::COMMUNITY_APP || types_ == csmain::OFFICE_APP)
    {
        return true;
    }
    return false;
}

bool CDevice::IsDev() const
{
    if (types_ == csmain::PERSONNAL_DEV || types_ == csmain::COMMUNITY_DEV || types_ == csmain::OFFICE_DEV)
    {
        return true;
    }
    return false;    
}

int CDevice::GetUserRole(uint32_t& role) const
{
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& app = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        role = app.role;
        return 0;
    }

    return -1;
}

int CDevice::GetUserInfoUUID(std::string& user_info_uuid) const
{
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& app = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        user_info_uuid = app.user_info_uuid;
        return 0;
    }
    return -1;
}

int CDevice::GetPersonalAccountUUID(std::string& personal_account_uuid) const
{
    if (IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& app = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(GetContext());
        personal_account_uuid = app.uuid;
        return 0;
    }

    return -1;
}

bool CDevice::TryAcquireMsg(const std::string& msg_id)
{
    return msg_rate_limiter_.TryAcquire(msg_id, m_deviceSetting.mac);
}