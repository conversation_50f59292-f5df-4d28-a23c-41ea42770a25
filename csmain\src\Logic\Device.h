#ifndef __Device_H__
#define __Device_H__
#include <boost/any.hpp>
#include <set>
#include <unordered_map>
#include "util_cstring.h"
#include "AkcsCommonDef.h"
#include "DevOnlineMng.h"
#include "dbinterface/DbCommonSt.h"
#include "MsgRateLimiterConf.h"
#include "SlidingWindowRateLimiter.h"

class OfficeConnInfo;

namespace csmain
{

enum REMOTE_FILE_TYPE
{
    DEV_PCAP = 0,
    DEV_LOG,
    DEV_AUTOP,
    DEV_ANY,
};

}

class CDevice
{
public:
    CDevice(const std::string& addr);
    CDevice()
    {
        ::memset(&m_deviceSetting, 0, sizeof(m_deviceSetting));
    }
    ~CDevice();
    /******************************COMMON*******************************/
    const std::string GetMAC() const;
    void SetMAC(CString mac);    
    int Type() const;
    void SetType(int t);
    //目前仅personnal app用到这个
    void SetContext(const boost::any& context);
    //获取DEVICE_SETTING
    int GetDeviceSetting(DEVICE_SETTING* device_setting);
    //设置DEVICE_SETTING
    void SetDeviceSetting(const DEVICE_SETTING* device_setting);    
    unsigned int GetDclientVer();
    bool IsApp() const;
    bool IsDev() const;
    bool TryAcquireMsg (const std::string& msg_id); 
    /*****************************residence******************************/
    //added by chenyc,2017-05-31,app接入
    std::string GetAreaNode();
    const boost::any& GetContext() const;
    int  GetPerUid(std::string& user) const;
    int  GetPerMainSiteUid(std::string& user) const;
    //避免上报状态比业务慢处理,导致断言失败
    int  GetPerNodeApp(std::string& node) const;
    int GetPerUidByNode(std::string& user, const std::string& node) const;
    int  GetPerNodeDev(std::string& node) const;
    int GetAppUserName(std::string& username) const;
    int  GetAppUserNameBySite(std::string& username, const std::string& uid) const;
    int  GetPerNodeBySite(std::string& node, const std::string& uid) const;
    int  GetAppUidAndAppToken(std::string& Uid, std::string& AppToken) const;
    //设备localtion修改，修改内存数据
    int SetDevLocation(char* location);
    //更新设备ins,dis信息
    int UpdateProjectInfo(const MacInfo &mac_info);
    unsigned int GetUnitID();
    unsigned int GetMngAccountID();
    unsigned int GetDevGrade();
    unsigned int GetDevType();
    int GetUserRole(uint32_t& role) const;
    int GetUserInfoUUID(std::string& user_info_uuid) const;
    int GetPersonalAccountUUID(std::string& personal_account_uuid) const;
    int SetAppNodes(const PersonalAccountNodeInfoMap& nodes);
    int GetAppNodes(PersonalAccountNodeInfoMap& nodes) const;
    /*****************************office******************************/

    
private:
    std::string remote_addr_;// the remote address with form : "ip:port"
    int types_ = csmain::COMMUNITY_NONE;
    boost::any context_; //存放TCP客户端的必要信息
    //FIXME:用boost::any来保存,另外不要当public成员变量
    DEVICE_SETTING m_deviceSetting;
    PersonalAccountNodeInfoMap app_nodes_;    //多套房nodes
    SlidingWindowRateLimiter msg_rate_limiter_; // 消息ID限流器
};

#endif
