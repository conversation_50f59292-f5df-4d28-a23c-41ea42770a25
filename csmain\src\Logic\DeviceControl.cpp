#include "stdafx.h"
#include <sstream>
#include "Control.h"
#include "DeviceControl.h"
#include "MsgControl.h"
#include "Device.h"
#include "DeviceSetting.h"
#include "NotifyMsgControl.h"
#include <evpp/tcp_conn.h>
#include "util.h"
#include "csmainserver.h"
#include "SDMCMsg.h"
#include "AKDevMng.h"
#include "PersonalAccount.h"
#include "CsmainAES256.h"
#include "AKUserMng.h"
#include "dbinterface/VideoScheduleDB.h"
#include "VideoSchedMng.h"
#include "NodeTimeZone.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "RouteMqProduce.h"
#include "ConnectionPool.h"
#include "Singleton.h"
#include "SnowFlakeGid.h"
#include "XmlTagDefine.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/Message.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"
#include "BasicDefine.h"
#include "DclientMsgDef.h"
#include "MsgIdToMsgName.h"


extern AKCS_CONF gstAKCSConf;
extern AccessServer* g_accSer_ptr;
extern RouteMQProduce* g_nsq_producer;

CDeviceControl* GetDeviceControlInstance()
{
    return CDeviceControl::GetInstance();
}

CDeviceControl::CDeviceControl()
{
    device_updated_ = FALSE;
}

CDeviceControl::~CDeviceControl()
{
}

CDeviceControl* CDeviceControl::instance = NULL;

CDeviceControl* CDeviceControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceControl();
    }

    return instance;
}

int CDeviceControl::Init()
{
    Snprintf(local_ip_addr_, sizeof(local_ip_addr_), gstAKCSConf.csmain_outer_ip);
    return 0;
}

//设备断开
int CDeviceControl::OnDeviceDisconnectedByMac(const std::string& mac, uint64_t conn_version)
{
    AK_LOG_INFO << "set db dev status Disconnected mac is " << mac.c_str();
    //added by chenyc,2019-03-12,分布式,需要刷新设备对应的接入服务器的id
    std::string logic_srv_ip = GetEth0IPAddr();
    dbinterface::ResidentDevices::SetDeviceDisConnectStatus(mac, logic_srv_ip, conn_version);
    dbinterface::ResidentPerDevices::SetPerDeviceDisConnectStatus(mac, logic_srv_ip, conn_version);

    // 关联alexa的公共设备,下线推送给web
    if (DevOnlineMng::GetInstance()->DevRelateToAlexa(mac))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        
        // 离线: 从set中移除该mac
        DevOnlineMng::GetInstance()->RemovePubDevRelateToAlexa(mac);

        // 推送设备下线状态
        GetMsgControlInstance()->PostAlexaChangeStatus(mac, traceid);

        AK_LOG_INFO << "dev " << mac << " relate to alexa, notify dev offline status, traceid : " << traceid;
    }
    return 0;
}

//获取本地IP地址
int CDeviceControl::GetCurrentIPAddr(TCHAR* ip_addr, int size)
{
    if (ip_addr == NULL)
    {
        return -1;
    }
    _tcscpy_s(ip_addr, size, local_ip_addr_);

    return 0;
}

//发送TCP消息
int CDeviceControl::SendTcpMsg(const evpp::TCPConnPtr& conn, unsigned char* data, uint32_t size)
{
    if (g_accSer_ptr->enable_send_log)
    {
        SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)data;
        int msg_version = (msg_normal->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
        int msgid = msg_normal->message_id & SOCKET_MSG_ID_MASK;
        uint32_t data_size = NTOHS(msg_normal->data_size);
        char* payload = (char*)msg_normal->data;
        char tmp[4096];
        //新版本需要先进行AES解密处理
        if (msg_version == VERSION_2_0)
        {
            if (MSG_TO_DEVICE_NOTIFY_ALARM_ACK == msgid
                    || MSG_TO_DEVICE_SEND_TEXT_MESSAGE == msgid
                    || MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED == msgid
                    || MSG_TO_DEVICE_NOTIFY_ALARM_DEAL == msgid
                    || MSG_TO_DEVICE_DOOR_MOTION_ALERT == msgid
                    || MSG_TO_DEVICE_MANAGE_ALARM == msgid)
            {
                AesDecryptByMac(payload, tmp, AES_KEY_DEFAULT_MAC, data_size);
            }
            else if (MSG_TO_DEVICE_APP_LOGIN_RESP == msgid 
                      || MSG_TO_APP_NOTIFY_MOTION_OCCURED == msgid 
                      || MSG_TO_APP_RESP_DEV_ARMING_STATUS == msgid
                      || MSG_TO_APP_CHANGE_RELAY_STATUS == msgid)
            {
                AesDecryptByDefault(payload, tmp, data_size);
            }
            else
            {
                DEVICE_SETTING deviceSetting;
                memset(&deviceSetting, 0, sizeof(deviceSetting));
                if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) >=  0)
                {
                    AesDecryptByMac(payload, tmp, deviceSetting.mac, data_size);
                }
                else
                {
                    AesDecryptByMac(payload, tmp, AES_ENCRYPT_KEY_V1, data_size);
                }
            }
            AK_LOG_INFO << conn->remote_addr() << " sending2>>>>msgid=" << msgid << MsgIdToMsgName::GetDeclientMessageName(msgid) << " ,len=" << size << ",msg:" << tmp;
        }
        else
        {
            AK_LOG_INFO << conn->remote_addr() <<  " sending>>>>msgid=" << msgid << MsgIdToMsgName::GetDeclientMessageName(msgid) << ",len=" << size << ",msg:" << msg_normal->data;
        }

    }
    conn->Send(data, size);
    return 0;
}

//发送TCP消息
int CDeviceControl::SendTcpFormateDyIvMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG &msg, const SOCKET_MSG &dy_iv_msg)
{
    int dynamics = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DY_AES_IV_INDEX));
    if(!dynamics)
    {
        SendTcpMsg(conn, (unsigned char*)msg.data, msg.size);
    }
    else
    {
        SendTcpMsg(conn, (unsigned char*)dy_iv_msg.data, dy_iv_msg.size);
    }
    return 0;
}


//根据MAC获取设备设置信息
int CDeviceControl::GetDeviceSettingByMac(CString mac, DEVICE_SETTING* device_setting)
{
    return GetDeviceSettingInstance()->GetDeviceSettingByMac(mac, device_setting);
}
//只有获取所需的字段
int CDeviceControl::GetDevicesByNode(std::string node, int is_per, std::vector<DEVICE_SETTING>& devs)
{
    return GetDeviceSettingInstance()->GetDevicesByNode(node, is_per, devs);
}


//根据sip获取设备设置信息
int CDeviceControl::GetDeviceSettingBySip(const CString& sip, DEVICE_SETTING* device_setting)
{
    return GetDeviceSettingInstance()->GetDeviceSettingBySip(sip, device_setting);
}

//根据MAC获取设备设置信息
int CDeviceControl::GetDeviceSettingByID(int id, int is_personal, DEVICE_SETTING* device_setting)
{
    return GetDeviceSettingInstance()->GetDeviceSettingByID(id, is_personal, device_setting);
}

//发送REQUEST_STATUS
int CDeviceControl::SendRequestStatus(const evpp::TCPConnPtr& conn)
{
    SOCKET_MSG_REQ_STATUS reqStatusMsg;
    memset(&reqStatusMsg, 0, sizeof(SOCKET_MSG_REQ_STATUS));
    _tcscpy_s(reqStatusMsg.protocal, sizeof(reqStatusMsg.protocal) / sizeof(TCHAR), PROTOCAL_NAME_DEFAULT);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;

    if (GetMsgControlInstance()->BuildReqStatusMsg(socket_message, &reqStatusMsg) < 0)
    {
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        return -1;
    }

    return 0;
}

//发送请求设备rtsp服务的指令
int CDeviceControl::SendRequestRtsp(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REQ_RTSP& req_rtsp_msg, std::string mac)
{
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;
    if (GetMsgControlInstance()->BuildReqRtspMsg(socket_message, req_rtsp_msg, mac) < 0)
    {
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        return -1;
    }

    return 0;
}

//发送保持设备rtsp链路的信令
int CDeviceControl::SendKeepRtsp(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REQ_RTSP& keepalive_rtsp_msg, std::string mac)
{
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;
    if (GetMsgControlInstance()->BuildKeepRtspMsg(socket_message, keepalive_rtsp_msg, mac) < 0)
    {
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        return -1;
    }

    return 0;
}


//根据MAC发送REQUEST_STATUS
int CDeviceControl::SendRequestStatus(const std::string& mac)
{
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "The tcp conn of mac [" << mac << "] is offline.";
        return -1;
    }
    return SendRequestStatus(conn);
}

// Added by chenyc,2017-02-24,请求设备重启
int CDeviceControl::SendRequestReboot(const std::string& mac)
{
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    //AK_LOG_INFO << "Request reboot traceid:" << traceid;

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remoteControlMsg;
    memset(&remoteControlMsg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remoteControlMsg.protocal, sizeof(remoteControlMsg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remoteControlMsg.type, sizeof(remoteControlMsg.type), SOCKET_MSG_TYPE_NAME_REBOOT);
    ::snprintf(remoteControlMsg.item[0], sizeof(remoteControlMsg.item[0]), "%ld", traceid);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;

    if (GetMsgControlInstance()->BuildRemoteControlMsg(socket_message, &remoteControlMsg, mac) < 0)
    {
        AK_LOG_WARN << "BuildRemoteControlMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "SendTcpMsg failed, remote ip:port is:" << conn->remote_addr().c_str();
        return -1;
    }

    return 0;
}

int CDeviceControl::SendRequestReset(const std::string& mac)
{
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    //AK_LOG_INFO << "Request reboot traceid:" << traceid;

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remoteControlMsg;
    memset(&remoteControlMsg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remoteControlMsg.protocal, sizeof(remoteControlMsg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remoteControlMsg.type, sizeof(remoteControlMsg.type), SOCKET_MSG_TYPE_NAME_RESET);
    ::snprintf(remoteControlMsg.item[0], sizeof(remoteControlMsg.item[0]), "%ld", traceid);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;

    if (GetMsgControlInstance()->BuildRemoteControlMsg(socket_message, &remoteControlMsg, mac) < 0)
    {
        AK_LOG_WARN << "BuildRemoteControlMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "SendTcpMsg failed, remote ip:port is:" << conn->remote_addr().c_str();
        return -1;
    }

    return 0;
}

// Added by chenyc,2020-08-25,下发设备重启信令
int CDeviceControl::SendRequestReboot(const evpp::TCPConnPtr& conn, const std::string& mac)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remoteControlMsg;
    memset(&remoteControlMsg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remoteControlMsg.protocal, sizeof(remoteControlMsg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remoteControlMsg.type, sizeof(remoteControlMsg.type), SOCKET_MSG_TYPE_NAME_REBOOT);
    ::snprintf(remoteControlMsg.item[0], sizeof(remoteControlMsg.item[0]), "%" PRId64, traceid);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;

    if (GetMsgControlInstance()->BuildRemoteControlMsg(socket_message, &remoteControlMsg, mac) < 0)
    {
        AK_LOG_WARN << "BuildRemoteControlMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "SendTcpMsg failed, remote ip:port is:" << conn->remote_addr();
        return -1;
    }

    return 0;
}

void CDeviceControl::SendRequestToDevChangeIndoorRelay(const std::string& mac, int relay_id, int relay_status, int relay_type)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        return;
    }

    uint16_t msg_id = MSG_TO_DEVICE_REQUEST_CHANGE_RELAY;
    XmlKV tag_map;
    XmlKeyAttrKv attr_map;
    if (relay_type == IndoorRelayType::TYPE_EXTERN)
    {
        XmlKV kv;
        kv[csmain::xmltag::RELAY_TYPE] = XML_NODE_ATTRIBUTE_RELAY_TYPE_EXTERN;
        kv[csmain::xmltag::STATUS] = std::to_string(relay_status);
        attr_map[csmain::xmltag::RELAY] = kv;   
    }
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::TYPE] = "RequestDevChangeRelay";
    tag_map[csmain::xmltag::RELAY] = std::to_string(relay_id + 1); //App从0开始，设备从1开始
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildNewCommonMsg(&socket_msg, msg_id, tag_map, attr_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }
}
void CDeviceControl::SendRequestKeepChangeRelay(const std::string& mac, int relay, int type)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        return;
    }

    uint16_t msg_id = 0;
    std::map<std::string, std::string> tag_map;
    if (type == DoorControlType::CLOSE)
    {
        tag_map[csmain::xmltag::TYPE] = "RequestKeepCloseRelay";
        msg_id = MSG_TO_DEVICE_REQUEST_KEEP_CLOSE_RELAY;
    }
    else if (type == DoorControlType::OPEN)
    {
        tag_map[csmain::xmltag::TYPE] = "RequestKeepOpenRelay";
        msg_id = MSG_TO_DEVICE_REQUEST_KEEP_OPEN_RELAY;
    }
    else
    {
        AK_LOG_WARN << "SendRequestKeepChangeRelay Failed, MAC=" << mac << " has invalid type=" << type;
        return;
    }
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::RELAY] = std::to_string(relay+1);   //APP传的是relay_id,设备端支持的是1234的relay形式

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, msg_id, tag_map, MsgParamControl::NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }
}

int CDeviceControl::SendRequestCloseTcp(const std::string& mac)
{
    evpp::TCPConnPtr conn;

    if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }
    conn->Close();
    return 0;
}


//csmain去下发设备重新上报状态的通知
int CDeviceControl::NotifyPersonnalNodeChange(const std::string& node)
{
    //将通知消息压入队列中
    CPersonnalNodeChangeNotifyMsg cNotifyMsg(node);
    GetNotifyMsgControlInstance()->AddPersonnalNodeChangeNotifyMsg(cNotifyMsg);
    return 0;
}


//社区 csmain去下发设备重新上报状态的通知
int CDeviceControl::NotifyCommunityNodeChange(const CSP2A_COMMUNITY_UPDATE_NODE* updateNode)
{
    //将通知消息压入队列中
    CCommunityNodeChangeNotifyMsg cNotifyMsg(updateNode);
    GetNotifyMsgControlInstance()->AddCommunityNodeChangeNotifyMsg(cNotifyMsg);
    return 0;
}


//csmain去通知设备告警已经被处理
int CDeviceControl::NotifyPersonnalAlarmDeal(const CSP2A_PERSONNAL_DEAL_ALARM* per_alarm_deal)
{
    if (NULL == per_alarm_deal)
    {
        AK_LOG_WARN << "The param is NULL.";
        return -1;
    }
    char szNow[24] = {0};
    GetCurTime(szNow, 24);
    SOCKET_MSG_PERSONNAL_ALARM_DEAL stPerAlarmDeal;
    memset(&stPerAlarmDeal, 0, sizeof(stPerAlarmDeal));
    Snprintf(stPerAlarmDeal.alarm_id, sizeof(stPerAlarmDeal.alarm_id), per_alarm_deal->szAlarmID);
    Snprintf(stPerAlarmDeal.area_node, sizeof(stPerAlarmDeal.area_node), per_alarm_deal->szAreaNode);
    Snprintf(stPerAlarmDeal.user, sizeof(stPerAlarmDeal.user), per_alarm_deal->szUser);
    Snprintf(stPerAlarmDeal.result, sizeof(stPerAlarmDeal.result), per_alarm_deal->szResult);
    Snprintf(stPerAlarmDeal.protocal, sizeof(stPerAlarmDeal.protocal), "1.0");

    std::string NodeTime = getNodeCurrentTimeString(stPerAlarmDeal.area_node);
    ::snprintf(stPerAlarmDeal.time, sizeof(stPerAlarmDeal.time), "%s", NodeTime.c_str());
    //Snprintf(stPerAlarmDeal.time, sizeof(stPerAlarmDeal.time), szNow);
    Snprintf(stPerAlarmDeal.type, sizeof(stPerAlarmDeal.type), "deal personal alarm");

    SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE stPerAlarmDealInfo;
    if (DaoPerGetAlarmInfoById(stPerAlarmDeal.alarm_id, stPerAlarmDealInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id " << stPerAlarmDeal.alarm_id;
    }
    Snprintf(stPerAlarmDeal.type, sizeof(stPerAlarmDeal.type), stPerAlarmDealInfo.alarm_type);
    Snprintf(stPerAlarmDeal.device_name, sizeof(stPerAlarmDeal.device_name), stPerAlarmDealInfo.device_location);
    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupPerAlarmDealMsg msg;
    msg.set_node(stPerAlarmDeal.area_node);
    msg.set_alarm_id(stPerAlarmDeal.alarm_id);
    msg.set_deal_user(stPerAlarmDeal.user);
    msg.set_deal_result(stPerAlarmDeal.result);
    msg.set_deal_type(stPerAlarmDeal.type);
    msg.set_deal_time(stPerAlarmDeal.time);
    msg.set_mac("");
    msg.set_dev_location("");
    msg.set_alarm_type("");
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);
    return 0;
}

//csmain去通知设备告警已经被处理
int CDeviceControl::NotifyCommunityAlarmDeal(const CSP2A_COMMUNITY_DEAL_ALARM* per_alarm_deal)
{
    if (NULL == per_alarm_deal)
    {
        AK_LOG_WARN << "The param is NULL.";
        return -1;
    }
    char szNow[24] = {0};
    GetCurTime(szNow, 24);
    SOCKET_MSG_ALARM_DEAL stCommunityAlarmDeal;
    memset(&stCommunityAlarmDeal, 0, sizeof(stCommunityAlarmDeal));
    Snprintf(stCommunityAlarmDeal.alarm_id, sizeof(stCommunityAlarmDeal.alarm_id), per_alarm_deal->szAlarmID);
    Snprintf(stCommunityAlarmDeal.area_node, sizeof(stCommunityAlarmDeal.area_node), per_alarm_deal->szAreaNode);
    Snprintf(stCommunityAlarmDeal.user, sizeof(stCommunityAlarmDeal.user), per_alarm_deal->szUser);
    Snprintf(stCommunityAlarmDeal.result, sizeof(stCommunityAlarmDeal.result), per_alarm_deal->szResult);
    Snprintf(stCommunityAlarmDeal.protocal, sizeof(stCommunityAlarmDeal.protocal), "1.0");
    //Snprintf(stCommunityAlarmDeal.time, sizeof(stCommunityAlarmDeal.time), szNow);
    std::string NodeTime = getNodeCurrentTimeString(stCommunityAlarmDeal.area_node);
    ::snprintf(stCommunityAlarmDeal.time, sizeof(stCommunityAlarmDeal.time), "%s", NodeTime.c_str());
    Snprintf(stCommunityAlarmDeal.type, sizeof(stCommunityAlarmDeal.type), "deal personal alarm");

    SOCKET_MSG_ALARM_DEAL_OFFLINE stPerAlarmDealInfo;
    if (DaoCommGetAlarmInfoById(stCommunityAlarmDeal.alarm_id, stPerAlarmDealInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id " << stCommunityAlarmDeal.alarm_id;
    }
    stCommunityAlarmDeal.manager_account_id = stPerAlarmDealInfo.manager_account_id;
    Snprintf(stCommunityAlarmDeal.type, sizeof(stCommunityAlarmDeal.type), stPerAlarmDealInfo.alarm_type);
    Snprintf(stCommunityAlarmDeal.device_name, sizeof(stCommunityAlarmDeal.device_name), stPerAlarmDealInfo.device_location);
    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupCommAlarmDealMsg group_comm_alarm_deal_msg;
    group_comm_alarm_deal_msg.set_node(stCommunityAlarmDeal.area_node);
    group_comm_alarm_deal_msg.set_alarm_id(stCommunityAlarmDeal.alarm_id);
    group_comm_alarm_deal_msg.set_deal_user(stCommunityAlarmDeal.user);
    group_comm_alarm_deal_msg.set_deal_result(stCommunityAlarmDeal.result);
    group_comm_alarm_deal_msg.set_deal_type(stCommunityAlarmDeal.type);
    group_comm_alarm_deal_msg.set_deal_time(stCommunityAlarmDeal.time);
    group_comm_alarm_deal_msg.set_mac(stCommunityAlarmDeal.mac);
    group_comm_alarm_deal_msg.set_dev_location("");
    group_comm_alarm_deal_msg.set_alarm_type("");
    group_comm_alarm_deal_msg.set_mng_account_id(stCommunityAlarmDeal.manager_account_id);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&group_comm_alarm_deal_msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);
    return 0;
}


//个人终端用户,设备状态请求
int CDeviceControl::OnPerDevStatusReq(const std::string& mac)
{
    //清除连接状态
    GetDeviceControlInstance()->SendRequestStatus(mac);
    return 0;
}

//个人终端用户,设备清除与原联动系统相关的东西.
int CDeviceControl::OnPerDelDev(const std::string& macs)
{
    //modify by chenzhx 可以批量删除
    std::vector<std::string> oMac;
    //分割出各个设备,形式是*******.1-1
    SplitString(macs, ",", oMac);
    for (const auto& mac : oMac)
    {
        evpp::TCPConnPtr conn;
        if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
        {
            AK_LOG_WARN << "The tcp conn of mac [" <<  mac << "] is offline.";
            continue;
        }
        DEVICE_SETTING DeviceSetting = {0};
        if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &DeviceSetting) == 0)
        {
            CAkDevManager::GetInstance()->DelSip(DeviceSetting.sip_account); //清理sip-mac列表
        }
        //若设备当前处于连接状态
        SendReqQuitNode(conn, mac); //通知设备退出联动系统
    }

    return 0;
}

//个人终端用户,终端用户UID注销sip
int CDeviceControl::OnPerUidLogOutSip(const std::string& uids)
{
    std::vector<std::string> oUid;
    SplitString(uids, ",", oUid);
    for (const auto& strUid : oUid)
    {
        evpp::TCPConnPtr conn;
        if (g_accSer_ptr->GetDevConnByUid(strUid, conn) != 0)
        {
            AK_LOG_WARN << "find online uid to send logout msg failed, uid is " << strUid;
            continue;
        }
        SendReqLogOutSip(conn);
    }
    return 0;
}

//根据发送LOGOUT SIP.2018-01-29,目前适用于app.设备不再使用这个了。
int CDeviceControl::SendReqLogOutSip(const evpp::TCPConnPtr& conn)
{
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;
    if (GetMsgControlInstance()->BuildReqLogOutSipMsg(socket_message) < 0)
    {
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        return -1;
    }

    return 0;
}

//发送给设备退出联动系统的信令
int CDeviceControl::SendReqQuitNode(const evpp::TCPConnPtr& conn, const std::string& mac)
{
    GetKeyControlInstance()->PerDelDevKeySend(conn, mac);//发送空的配置文件给设备,让设备清除所有状态
    //GetMsgControlInstance()->OnSendDevListChangeMsg(conn); //通知设备过来请求同一联动单元的设备列表,刷新联系人列表

    //删除设备后，断开连接csgate不会返回csmain地址，导致设备不会重新连接上来，会影响我们开发测试移动设备。
    //csgate不会返回csmain地址主要是因为如果设备有ftp地址，会往ftp服务器发送截图，这是会被拉黑
    //conn->Close();
    g_accSer_ptr->ClearDevConnMap(conn);
    DEVICE_SETTING devicesetting;
    memset(&devicesetting, 0, sizeof(devicesetting));
    g_accSer_ptr->UpdateTcpConnSetting(conn, &devicesetting);
    return 0;
}

int CDeviceControl::SendUpgrade(const evpp::TCPConnPtr& conn, const std::string& file_path,
                                const std::string& file_ver, std::string mac, int is_need_reset)
{

    //创建MSG_TO_DEVICE_UPGRADE_START消息
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));

    SOCKET_MSG_UPGRADE_SEND upgradeStartMsg;
    memset(&upgradeStartMsg, 0, sizeof(SOCKET_MSG_UPGRADE_START));
    _tcscpy_s(upgradeStartMsg.protocal, sizeof(upgradeStartMsg.protocal), PROTOCAL_NAME_DEFAULT);
    _tcscpy_s(upgradeStartMsg.firmware_url, sizeof(upgradeStartMsg.firmware_url), file_path.c_str());
    _tcscpy_s(upgradeStartMsg.firmware_version, sizeof(upgradeStartMsg.firmware_version), file_ver.c_str());
    upgradeStartMsg.is_need_reset = is_need_reset;

    if (GetMsgControlInstance()->BuildUpgradeSendMsg(&socket_msg, &upgradeStartMsg, mac) < 0)
    {
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size) < 0)
    {
        return -1;
    }

    return 0;
}

int CDeviceControl::AddConnToKeySend(std::vector<evpp::TCPConnPtr> &dev_conns)
{
    for (const auto& conn : dev_conns)
    {
        if (g_accSer_ptr->IsTCPConnIsAPP(conn))
        {
            GetMsgControlInstance()->OnSendDevListChangeMsg(conn);//如果是app,只需要通知联系人变更
            continue;
        }
        DEVICE_SETTING DeviceSetting = {0};
        if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &DeviceSetting) != 0)
        {
            AK_LOG_WARN << "AddConnToKeySend The tcp conn of  dev not exist.";
            continue;
        }
        if (DeviceSetting.is_personal)//个人
        {
            PERSONAL_KEY_SEND PersonalkeySend;
            memset(PersonalkeySend.node, 0, sizeof(PersonalkeySend.node));
            memset(PersonalkeySend.mac, 0, sizeof(PersonalkeySend.mac));
            Snprintf(PersonalkeySend.node, sizeof(PersonalkeySend.node), DeviceSetting.device_node);
            Snprintf(PersonalkeySend.mac, sizeof(PersonalkeySend.mac), DeviceSetting.mac);
            PersonalkeySend.weak_conn = conn;
        
            //todo:需要设备过来下载的消息需要放入队列中等待发送，目前没有实现条件变量通知,仅仅使用定时器来刷数据
            //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
            if (GetKeyControlInstance()->AddPersonalKeySend(PersonalkeySend) < 0)
            {
                AK_LOG_WARN << "Add personal KeySend failed.";
                return -1;
            }
        
        }
        else
        {
            KEY_SEND keySend;
            memset(keySend.community, 0, sizeof(keySend.community));
            memset(keySend.device_node, 0, sizeof(keySend.device_node));
            Snprintf(keySend.mac, sizeof(keySend.mac) / sizeof(TCHAR), DeviceSetting.mac);
            Snprintf(keySend.device_node, sizeof(keySend.device_node) / sizeof(TCHAR), DeviceSetting.device_node);
            keySend.weak_conn = conn;
            //需要设备过来下载的消息需要放入数据库发送
            //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
            if (GetKeyControlInstance()->AddKeySend(keySend) < 0)
            {
                AK_LOG_WARN << "AddKeySend failed.";
                return -1;
            }
        }   

        if (DeviceSetting.dclient_version < D_CLIENT_VERSION_1_0)
        {
            GetMsgControlInstance()->OnSendDevListChangeMsg(conn);
        }
    }           
    return 0;
}


int CDeviceControl::OnConfigFileChangeReq(const CSP2A_CONFIG_FILE_CHANGE*  config_change)
{

    char file_change_type_str[][32] = {
        "",
        "Node",
        "Mac",
        "AllCommunity",
        "AllUnit",
        "PubDev",
        "UnitDev",
        "NodeOnly"
    };
    
    std::string change_type = std::to_string(config_change->nNotifyType);
    if (config_change->nNotifyType <=7 && config_change->nNotifyType > 0)
    {
        change_type = file_change_type_str[config_change->nNotifyType];
    }
    
    AK_LOG_INFO << "[csadapt] config file change. node: " << config_change->node 
        << " mac: " << config_change->mac 
        << " notifytype: " << change_type
        << " mngid: " << config_change->mng_id 
        << " unitid: " << config_change->unit_id;

    switch(config_change->nNotifyType)
    {
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_NODE:
        {
            std::vector<evpp::TCPConnPtr> dev_conns1;
            g_accSer_ptr->GetDevListByNode(config_change->node, dev_conns1);
            AddConnToKeySend(dev_conns1);

            SOCKET_MSG_PERSONNAL_APP_CONF_T node_info;
            ::snprintf(node_info.user, sizeof(node_info.user), "%s", config_change->node);
            int ret = DaoGetNodeByAppUser(node_info);
            //判断是否是社区用户
            if (!(node_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || node_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT))
            {
                if (ret == 0)
                {
                    return 1;
                }
                AK_LOG_WARN << "OnConfigFileChangeReq node has been delete. node=" << config_change->node 
                            << " continue send keysend to public devices.";
                //社区用户删除
                node_info.manager_account_id = config_change->mng_id;
                node_info.unit_id = config_change->unit_id;
            }
            //单元
            std::vector<evpp::TCPConnPtr> dev_conns2;
            if (node_info.unit_id <= 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The unit_id is = " << node_info.unit_id;
                return -1;
            }
            g_accSer_ptr->GetDevListCommunityUnitPublicDev(node_info.manager_account_id, node_info.unit_id, dev_conns2);
            AddConnToKeySend(dev_conns2); 

            //公共
            std::vector<evpp::TCPConnPtr> dev_conns3;
            if (node_info.manager_account_id <= 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The mng_Id is = " << node_info.manager_account_id;
                return -1;
            }
            g_accSer_ptr->GetDevListCommunityPublicDev(node_info.manager_account_id, dev_conns3);
            AddConnToKeySend(dev_conns3);   

            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_MAC:
        {
            evpp::TCPConnPtr conn;
            if (g_accSer_ptr->GetDevConnByMac(config_change->mac, conn) != 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The tcp conn of mac [" << config_change->mac << "] is offline.";
                return -1;
            }
            std::vector<evpp::TCPConnPtr> dev_conns;
            dev_conns.push_back(conn);
            AddConnToKeySend(dev_conns);       
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_COMMUNITY://整个社区更新
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (config_change->mng_id <= 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The mng_id is = " << config_change->mng_id;
                return -1;
            }
            g_accSer_ptr->GetDevListCommunityAllDevAndAPP(config_change->mng_id, dev_conns);
            AddConnToKeySend(dev_conns);            
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_UNIT://整个单元更新
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (config_change->unit_id <= 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The unit_id is = " << config_change->unit_id;
                return -1;
            }
            g_accSer_ptr->GetDevListCommunityAllUnitDevAndAPP(config_change->mng_id, config_change->unit_id, dev_conns);
            g_accSer_ptr->GetDevListCommunityPublicDev(config_change->mng_id, dev_conns);
            AddConnToKeySend(dev_conns);
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_PUB_DEV://公共设备
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (config_change->mng_id <= 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The mng_id is = " << config_change->mng_id;
                return -1;
            }
            g_accSer_ptr->GetDevListCommunityPublicDev(config_change->mng_id, dev_conns);
            AddConnToKeySend(dev_conns);           
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_UNIT_DEV://单元公共设备
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (config_change->unit_id <= 0)
            {
                AK_LOG_WARN << "OnConfigFileChangeReq The unit_id is = " << config_change->unit_id;
                return -1;
            }
            g_accSer_ptr->GetDevListCommunityUnitPublicDev(config_change->mng_id, config_change->unit_id, dev_conns);
            AddConnToKeySend(dev_conns);           
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_NODE_ONLY://只需要通知节点，不需要通知unit pub
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            g_accSer_ptr->GetDevListByNode(config_change->node, dev_conns);
            AddConnToKeySend(dev_conns);        
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_ALL_PUB://所有公共设备
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            g_accSer_ptr->GetDevListCommunityAllPubDev(config_change->mng_id, dev_conns);
            AddConnToKeySend(dev_conns);        
            break;
        }         
    }
    return 0;
}

int CDeviceControl::OnDevAppExpireReq(const std::string& uid)
{
    //这个uid csadapt并没有传过来，实际没用,先注释 后续考虑在这里做离线推送
    //AK_LOG_INFO << "[" <<  uid << "]app expire make it logout.";
    //CAkUserManager::GetInstance()->SetLogOutStatus(uid);
    //CAkUserManager::GetInstance()->RemoveAkUserTokenByUid(uid);
    return 0;
}

int CDeviceControl::OnDevNotExpireReq(const CSP2A_DEV_NOT_EXPIRE*  dev_notice_expire)
{
    //设备已经在csadapt通过的信令通知设备过期了
    /*
    TCHAR szIPAddress2[IP_SIZE];
    GetDeviceControlInstance()->GetCurrentIPAddr(szIPAddress2, sizeof(szIPAddress2)/sizeof(TCHAR));
    std::string ip_address = szIPAddress2;

    std::string strMacs(dev_notice_expire->szMacs);
    std::vector<std::string> oVec;
    SplitString(strMacs, ";", oVec);

    evpp::TCPConnPtr conn;
    for (auto &strmac : oVec)
    {
        if (g_accSer_ptr->GetDevConnByMac(strmac, conn) != 0)
        {
            AK_LOG_WARN << "The tcp conn of mac [%s] is offline."),__FUNCTIONW__, strmac.c_str());
            continue;
        }
        DEVICE_SETTING DeviceSetting = {0};
        if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &DeviceSetting) != 0)
        {
            AK_LOG_WARN << "The tcp conn of mac [%s]'s dev not exist."),__FUNCTIONW__, strmac.c_str());
            continue;
        }
        SOCKET_MSG socket_msg;
        if(GetMsgControlInstance()->BuildReqDevConfigFileChangeUrlMsg(&socket_msg, ip_address, DeviceSetting, CONFIG_FILE_CHANGE_TYPE_CONFIG) < 0)
        {
            continue;
        }
        if(GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size) < 0)
        {
           continue;
        }
    }
    */
    return 0;
}

int CDeviceControl::OnDevCleanDevCodeReq(const std::string& mac)
{
    evpp::TCPConnPtr conn;
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "The tcp conn of mac [" <<  mac << "] is offline.";
        return -1;
    }
    GetMsgControlInstance()->OnSendDevCodeMsg(conn, "", mac);
    AK_LOG_INFO << "[csadapt] dev clean device code mac:" << mac.c_str();
    return 0;
}

int CDeviceControl::OnAddVsSched(CSP2A_ADD_VIDEO_STORAGE_SCHED* add_video_storage_sched)
{
    return CVideoSchedMng::Instance()->AddVsSched(add_video_storage_sched);
}

int CDeviceControl::OnDelVsSched(CSP2A_DEL_VIDEO_STORAGE_SCHED* del_video_storage_sched)
{
    return CVideoSchedMng::Instance()->DelVsSched(del_video_storage_sched);
}

int CDeviceControl::OnDelVs(const uint32_t video_id)
{
    return CVideoSchedMng::Instance()->DelVs(video_id);
}

//web上修改设备的相关信息,csadapt通知csmain修改相应本地缓存中的数据.
int CDeviceControl::OnDevChange(const CSP2A_DEVICE_CHANGE_INFO* dev_change)
{
    DEVICE_SETTING dev;
    if (GetDeviceControlInstance()->GetDeviceSettingByID(dev_change->nMacid, dev_change->nIsPer, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSettingByID is fail, macid:" << dev_change->nMacid << " is per:" << dev_change->nIsPer;
        return -1;
    }
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(dev.mac, conn) != 0)
    {
        //AK_LOG_WARN << "The tcp conn of mac [" <<  strmac.c_str() <<"] is offline.";
        return -1;
    }
    g_accSer_ptr->UpdateDevSomeMsgFromConnList(conn, &dev);
    return 0;
}

// 重新发keysend给设备
int CDeviceControl::SendMacKeysend(const char* mac)
{
    if (NULL == mac)
    {
        AK_LOG_WARN << "The param is NULL.";
        return -1;
    }
    evpp::TCPConnPtr conn;

    if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    if (GetDeviceControlInstance()->GetDeviceSettingByMac(mac, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "device(" << mac << ") connot found in mysql.";
        return -1;
    }

    if (!deviceSetting.is_personal)
    {
        KEY_SEND keySend;
        memset(keySend.community, 0, sizeof(keySend.community));
        memset(keySend.device_node, 0, sizeof(keySend.device_node));
        Snprintf(keySend.community, sizeof(keySend.community) / sizeof(TCHAR), deviceSetting.community);
        Snprintf(keySend.mac, sizeof(keySend.mac) / sizeof(TCHAR), deviceSetting.mac);
        Snprintf(keySend.device_node, sizeof(keySend.device_node) / sizeof(TCHAR), deviceSetting.device_node);
        keySend.extension = deviceSetting.extension;
        keySend.weak_conn = conn;
        //需要设备过来下载的消息需要放入数据库发送
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        if (GetKeyControlInstance()->AddKeySend(keySend) < 0)
        {
            AK_LOG_WARN << "AddKeySend failed.";
            return -1;
        }
    }
    else
    {
        PERSONAL_KEY_SEND PersonalkeySend;
        memset(PersonalkeySend.node, 0, sizeof(PersonalkeySend.node));
        memset(PersonalkeySend.mac, 0, sizeof(PersonalkeySend.mac));
        Snprintf(PersonalkeySend.node, sizeof(PersonalkeySend.node), deviceSetting.device_node);
        Snprintf(PersonalkeySend.mac, sizeof(PersonalkeySend.mac), deviceSetting.mac);
        PersonalkeySend.weak_conn = conn;

        //todo:需要设备过来下载的消息需要放入队列中等待发送，目前没有实现条件变量通知,仅仅使用定时器来刷数据
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        if (GetKeyControlInstance()->AddPersonalKeySend(PersonalkeySend) < 0)
        {
            AK_LOG_WARN << "Add personal KeySend failed. Mac:" << PersonalkeySend.mac;
            return -1;
        }
    }
    return 0;
}

int CDeviceControl::SendConnKeysend(const evpp::TCPConnPtr& conn)
{
    DEVICE_SETTING deviceSetting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "get devicesetting by connect failed.";
        return -1;
    }
    if (!deviceSetting.is_personal)
    {
        KEY_SEND keySend;
        memset(keySend.community, 0, sizeof(keySend.community));
        memset(keySend.device_node, 0, sizeof(keySend.device_node));
        Snprintf(keySend.community, sizeof(keySend.community) / sizeof(TCHAR), deviceSetting.community);
        Snprintf(keySend.mac, sizeof(keySend.mac) / sizeof(TCHAR), deviceSetting.mac);
        Snprintf(keySend.device_node, sizeof(keySend.device_node) / sizeof(TCHAR), deviceSetting.device_node);
        keySend.extension = deviceSetting.extension;
        keySend.weak_conn = conn;
        //需要设备过来下载的消息需要放入数据库发送
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        if (GetKeyControlInstance()->AddKeySend(keySend) < 0)
        {
            AK_LOG_WARN << "AddKeySend failed.";
            return -1;
        }
    }
    else
    {
        PERSONAL_KEY_SEND PersonalkeySend;
        memset(PersonalkeySend.node, 0, sizeof(PersonalkeySend.node));
        memset(PersonalkeySend.mac, 0, sizeof(PersonalkeySend.mac));
        Snprintf(PersonalkeySend.node, sizeof(PersonalkeySend.node), deviceSetting.device_node);
        Snprintf(PersonalkeySend.mac, sizeof(PersonalkeySend.mac), deviceSetting.mac);
        PersonalkeySend.weak_conn = conn;

        //todo:需要设备过来下载的消息需要放入队列中等待发送，目前没有实现条件变量通知,仅仅使用定时器来刷数据
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        if (GetKeyControlInstance()->AddPersonalKeySend(PersonalkeySend) < 0)
        {
            AK_LOG_WARN << "Add personal KeySend failed. Mac:" << PersonalkeySend.mac;
            return -1;
        }
    }
    return 0;
}

int CDeviceControl::UpdateDeviceArming(const std::string& mac, int arming, int is_per)
{
    if (is_per)
    {
        dbinterface::ResidentPerDevices::SetPerDeviceArmingStatus(mac, arming);
    }
    else
    {
        dbinterface::ResidentDevices::SetDeviceArmingStatus(mac, arming);
    }
    return 0;
}

int CDeviceControl::OnOldDevMaintenanceReq(const HTTP_MSG_DEV_GET_FILE_COMMON& dev_file, int file_type)
{
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(dev_file.mac, conn) != 0)
    {
        AK_LOG_WARN << "OnOldDevMaintenanceReq The tcp conn of mac [" << dev_file.mac << "] is offline.";
        return -1;
    }
    DEVICE_SETTING device_setting = {0};
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "OnOldDevMaintenanceReq The tcp conn of mac [" << dev_file.mac << "]'s dev not exist.";
        return -1;
    }
    if(device_setting.dclient_version < D_CLIENT_VERSION_4600)
    {
        return -1;   //旧版本不支持
    }
    else if(device_setting.dclient_version >= D_CLIENT_VERSION_5300)
    {
        return 1;   //使用新运维指令
    }
    
    std::string firmware_code;
    std::string firmware = device_setting.SWVer;
    std::size_t pos = firmware.find_first_of(".");
    if(pos != string::npos)
    {
        firmware_code = firmware.substr(0, pos);
    }
    else
    {
        AK_LOG_WARN << "device firmware error";
        return -1;
    }

    std::string ret;
    std::vector<std::string> cmd_list;
    switch (file_type)  //根据类型、机型，组合相关旧运维命令
    {
        case csmain::DEV_PCAP:
            {   
                std::string tmp1,tmp2;
                char tmp[256];
                tmp1 = "pcapstop";
                cmd_list.push_back(tmp1);
                snprintf(tmp, sizeof(tmp), "putfile %s /tmp/download/phone.pcap %s %s %s", \
                    dev_file.server_url, dev_file.file_name, dev_file.username, dev_file.password);
                tmp2 = tmp;
                cmd_list.push_back(tmp2);
            }
            break;
        case csmain::DEV_LOG:
            {   
                std::string tmp1,tmp2;
                char tmp[256];
                if(firmware_code == "26" || firmware_code == "27" || firmware_code == "28" || firmware_code == "221")
                {
                    tmp1 = "cat /tmp/Messages > /tmp/AKCStmplog";
                    cmd_list.push_back(tmp1);
                }
                else
                {
                    tmp1 = "logcat -d -v time > /tmp/AKCStmplog";
                    cmd_list.push_back(tmp1);
                }
                snprintf(tmp, sizeof(tmp), "putfile %s /tmp/AKCStmplog %s %s %s", \
                    dev_file.server_url, dev_file.file_name, dev_file.username, dev_file.password);
                tmp2 = tmp;
                cmd_list.push_back(tmp2);
            }
            break;
        case csmain::DEV_AUTOP:
            {
                std::string tmp1,tmp2;
                char tmp[256];
                tmp1 = "exportcfg";
                cmd_list.push_back(tmp1);
                snprintf(tmp, sizeof(tmp), "putfile %s /tmp/autop_config_template.config %s %s %s", \
                    dev_file.server_url, dev_file.file_name, dev_file.username, dev_file.password);
                tmp2 = tmp;
                cmd_list.push_back(tmp2);            
            }
            break;
        case csmain::DEV_ANY:
            {
                std::string tmp1;
                char tmp[256];
                snprintf(tmp, sizeof(tmp), "putfile %s %s %s %s %s", \
                    dev_file.server_url, dev_file.location, dev_file.file_name, dev_file.username, dev_file.password);
                tmp1 = tmp;
                cmd_list.push_back(tmp1);
            }
            break;

        default:
            {
                AK_LOG_WARN << "putfile type error";
                return -1;
            }
    }
    
    for(std::vector<std::string>::iterator iter = cmd_list.begin(); iter != cmd_list.end(); iter++)
    {
        if (g_accSer_ptr->SendCommandToDevice(dev_file.mac, *iter, ret) != 0)
        {
            AK_LOG_WARN << "CLI: error " << ret;
            return -1;
        }
        usleep(2000 * 1000);    //旧设备不支持两条命令连续下发，会造成异常
    }
    return 0;    
}

void CDeviceControl::SendRegEndUserUrl(const std::string& mac, const RegEndUserInfo &user_info)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        return;
    }

    char status[10];
    snprintf(status, sizeof(status), "%d", user_info.status);

    uint16_t msg_id = MSG_TO_DEVICE_REG_END_USER;
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::ACCOUNT] = user_info.account;
    tag_map[csmain::xmltag::REG_URL] = user_info.reg_url;
    tag_map[csmain::xmltag::STATUS] = status;
    tag_map[csmain::xmltag::ACCOUNT_NAME] = user_info.account_name;
    tag_map[csmain::xmltag::EMAIL] = user_info.email;
    tag_map[csmain::xmltag::MOBILE_NUMBER] = user_info.mobile_number;
    tag_map[csmain::xmltag::TYPE] = "RegEndUser";


    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }
}

void CDeviceControl::SendIsKit(const std::string& mac)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        return;
    }

    uint16_t msg_id = MSG_TO_DEVICE_REQUEST_IS_KIT;
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::IS_KIT] = "1";
    tag_map[csmain::xmltag::TYPE] = "RequestIsKit";


    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }
}

int CDeviceControl::SendAck(const std::string &mac, const std::string &msg_seq, uint16_t msg_id)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        return -1;
    }

    char msg_id_str[9];
    snprintf(msg_id_str, sizeof(msg_id_str), "%x", msg_id);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::MSG_SEQ] = msg_seq;
    tag_map[csmain::xmltag::RESULT] = "OK";
    tag_map[csmain::xmltag::TYPE] = "Ack";
    tag_map[csmain::xmltag::MSG_ID] = msg_id_str;

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_ACK, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return -1;
    }

    return 0;
}

void CDeviceControl::SendAppLogoutAckMsg(const evpp::TCPConnPtr& conn)
{
    SOCKET_MSG socket_msg, dy_iv_socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));
    memset(&dy_iv_socket_msg, 0, sizeof(dy_iv_socket_msg));

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "Logout";   
    tag_map[csmain::xmltag::RESULT] = "0";    

    if (CMsgControl::GetInstance()->OnBuildCommonEncDefaultMsg(socket_msg, dy_iv_socket_msg, MSG_TO_APP_LOGOUT_ACK, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "SendAppLogoutAckMsg OnBuildCommonMsg failed.";
        return;
    }
    
    if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socket_msg, dy_iv_socket_msg) < 0)
    {
        AK_LOG_WARN << "SendAppLogoutAckMsg failed.";
        return;
    }
}
