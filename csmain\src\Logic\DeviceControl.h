#ifndef __DEVICE_CONTROL_H__
#define __DEVICE_CONTROL_H__

#include "SDMCMsg.h"
#include "AkcsWebMsgSt.h"
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "util_cstring.h"
#include <string>
#include "dbinterface/PendingRegUser.h"

enum DoorControlType
{
    CLOSE = 0,
    OPEN = 1
};

class CDeviceControl
{
public:
    CDeviceControl();
    ~CDeviceControl();

    //初始化
    int Init();
    void Lock();
    void Unlock();
    static CDeviceControl* GetInstance();

    //获取本地IP地址
    int GetCurrentIPAddr(TCHAR* ip_addr, int size);

    //发送TCP消息
    int SendTcpMsg(const evpp::TCPConnPtr& conn, unsigned char* data, uint32_t size);
    int SendTcpFormateDyIvMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG &msg, const SOCKET_MSG &dy_iv_msg);


    //根据MAC获取设备设置信息
    int GetDeviceSettingByMac(CString mac, DEVICE_SETTING* device_setting);

    //发送REQUEST_STATUS
    int SendRequestStatus(const evpp::TCPConnPtr& conn);

    //根据MAC发送REQUEST_STATUS
    int SendRequestStatus(const std::string& mac);

    // Added by chenyc,2017-02-24,请求设备重启
    int SendRequestReboot(const std::string& mac);
    // Added by chenyc,2020-08-25,下发设备重启信令
    int SendRequestReboot(const evpp::TCPConnPtr& conn, const std::string& mac);
    // 关闭套接字
    int SendRequestCloseTcp(const std::string& mac);

    // Added by chenyc,2017-08-24,发送请求设备rtsp服务的指令
    int SendRequestRtsp(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REQ_RTSP& req_rtsp_msg, std::string mac);
    int OnDeviceDisconnectedByMac(const std::string& mac, uint64_t conn_version);

    //csmain去下发设备重新上报状态的通知
    int NotifyPersonnalNodeChange(const std::string& node);
    //社区 csmain去下发设备重新上报状态的通知
    int NotifyCommunityNodeChange(const CSP2A_COMMUNITY_UPDATE_NODE* updateNode);
    //csmain去通知设备告警已经被处理
    int NotifyPersonnalAlarmDeal(const CSP2A_PERSONNAL_DEAL_ALARM* per_alarm_deal);
    // 社区 去通知设备告警被处理
    int NotifyCommunityAlarmDeal(const CSP2A_COMMUNITY_DEAL_ALARM* per_alarm_deal);
    //个人终端用户,设备状态请求
    int OnPerDevStatusReq(const std::string& mac);
    int OnPerDelDev(const std::string& macs);
    int OnPerUidLogOutSip(const std::string& uids);
    //发送设备升级开始指令
    int SendUpgrade(const evpp::TCPConnPtr& conn, const std::string& file_path, const std::string& file_ver, std::string mac, int is_need_reset);
    int SendKeepRtsp(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REQ_RTSP& keepalive_rtsp_msg, std::string mac);

    int GetDeviceSettingBySip(const CString& sip, DEVICE_SETTING* device_setting);

    int OnDevAppExpireReq(const std::string& uid);
    int OnDevNotExpireReq(const CSP2A_DEV_NOT_EXPIRE*  dev_notice_expire);
    int OnDevCleanDevCodeReq(const std::string& mac);
    int OnConfigFileChangeReq(const CSP2A_CONFIG_FILE_CHANGE*  contact_change);
    int BuildReqDevConfigFileChangeUrlMsg(SOCKET_MSG* socket_message, const std::string& ip_address, DEVICE_SETTING& device, int change_type);
    int OnAddVsSched(CSP2A_ADD_VIDEO_STORAGE_SCHED* add_video_storage_sched);
    int OnDelVsSched(CSP2A_DEL_VIDEO_STORAGE_SCHED* del_video_storage_sched);
    int OnDelVs(const uint32_t video_id);
    int OnDevChange(const CSP2A_DEVICE_CHANGE_INFO* dev_change);
    int GetDeviceSettingByID(int id, int is_personal, DEVICE_SETTING* device_setting);
    int SendMacKeysend(const char* mac);
    int SendConnKeysend(const evpp::TCPConnPtr& conn);
    void SendRequestToDevChangeIndoorRelay(const std::string& mac, int relay_id, int relay_status, int relay_type);
    void SendRequestKeepChangeRelay(const std::string& mac, int relay, int type);
    int UpdateDeviceArming(const std::string& mac, int arming, int is_per);
    int GetDevicesByNode(std::string node, int is_per, std::vector<DEVICE_SETTING>& devs);
    
    int OnOldDevMaintenanceReq(const HTTP_MSG_DEV_GET_FILE_COMMON& dev_file, int file_type);

    int AddConnToKeySend(std::vector<evpp::TCPConnPtr> &dev_conns);

    void SendRegEndUserUrl(const std::string& mac, const RegEndUserInfo &user_info);
    void SendIsKit(const std::string& mac);
    int SendAck(const std::string &mac, const std::string &msg_seq, uint16_t msg_id);
    int SendRequestReset(const std::string& mac);
    void SendAppLogoutAckMsg(const evpp::TCPConnPtr& conn);
private:
    int SendReqLogOutSip(const evpp::TCPConnPtr& conn);
    int SendReqQuitNode(const evpp::TCPConnPtr& conn, const std::string& mac);
private:
    static CDeviceControl* instance;

    TCHAR local_ip_addr_[IP_SIZE];
    int tcp_listen_port_;
    //设备列表更新标志
    BOOL device_updated_;

};

CDeviceControl* GetDeviceControlInstance();

#endif

