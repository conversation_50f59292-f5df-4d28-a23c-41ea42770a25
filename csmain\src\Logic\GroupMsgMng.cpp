#include "NotifyMsgControl.h"
#include "AkcsWebMsgSt.h"
#include "GroupMsgMng.h"
#include "CachePool.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AK.Adapt.pb.h"
#include "AK.Linker.pb.h"
#include "AK.Resid.pb.h"
#include "AkcsMsgDef.h"
#include "AKUserMng.h"
#include "DeviceControl.h"
#include "NodeTimeZone.h"
#include "AKCSDao.h"
#include "AkcsCommonDef.h"
#include "RouteClient.h"
#include "csmainserver.h"
#include "MsgControl.h"
#include "AkcsOemDefine.h"
#include "XmlTagDefine.h"
#include "Dao.h"
#include "KeyControl.h"
#include "DevUpdateUserLog.h"
#include "EventFilterWriteFileImpl.h"
#include "akuvox.h"
#include "dbinterface/OfflineResendLog.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/DevUpdateUserLogDB.h"
#include "NotifyMsgControl.h"
#include "DclientMsgDef.h"
#include "MsgControl.h"
#include "Utility.h"
#include "InnerSt.h"


extern AccessServer* g_accSer_ptr;
extern akcs::CEventFilterInterface *g_event_filter;
CGroupMsgMng* CGroupMsgMng::s_group_msg_mng_instance_ = nullptr;
extern AKCS_CONF gstAKCSConf;
extern const char *g_redis_db_weather;

CGroupMsgMng* CGroupMsgMng::Instance()
{
    if (!s_group_msg_mng_instance_)
    {
        s_group_msg_mng_instance_ = new CGroupMsgMng();
    }
    return s_group_msg_mng_instance_;
}

void CGroupMsgMng::HandleGroupCommAlarmReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupCommAlarmMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    //将消息组装完成,写入csmain的notify消息队列
    SOCKET_MSG_ALARM_SEND recvAlarmMsg;
    memset(&recvAlarmMsg, 0, sizeof(recvAlarmMsg));
    Snprintf(recvAlarmMsg.type, sizeof(recvAlarmMsg.type), msg.alarm_type().c_str());
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address), msg.address().c_str());
    Snprintf(recvAlarmMsg.time, sizeof(recvAlarmMsg.time), msg.time().c_str());
    Snprintf(recvAlarmMsg.from_local, sizeof(recvAlarmMsg.from_local), msg.from_local().c_str());
    Snprintf(recvAlarmMsg.mac, sizeof(recvAlarmMsg.mac), msg.mac().c_str());
    recvAlarmMsg.grade = msg.grade();
    recvAlarmMsg.manager_account_id = msg.mng_account_id();
    recvAlarmMsg.unit_id = msg.unit_id();
    recvAlarmMsg.device_type = msg.device_type();
    recvAlarmMsg.id = msg.id();
    recvAlarmMsg.alarm_code = msg.alarm_code();
    recvAlarmMsg.alarm_location = msg.alarm_location();
    recvAlarmMsg.alarm_zone = msg.alarm_zone();
    recvAlarmMsg.alarm_customize = msg.alarm_customize();
    recvAlarmMsg.trace_id = msg.trace_id();

    CAlarmNotifyMsg cNotifyMsg(recvAlarmMsg);
    GetNotifyMsgControlInstance()->AddAlarmNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleGroupCommAlarmDealReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupCommAlarmDealMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    SOCKET_MSG_ALARM_DEAL stAlarmDeal;
    memset(&stAlarmDeal, 0, sizeof(stAlarmDeal));
    Snprintf(stAlarmDeal.area_node, sizeof(stAlarmDeal.area_node), msg.node().c_str());
    Snprintf(stAlarmDeal.alarm_id, sizeof(stAlarmDeal.alarm_id), msg.alarm_id().c_str());
    Snprintf(stAlarmDeal.user, sizeof(stAlarmDeal.user), msg.deal_user().c_str());
    Snprintf(stAlarmDeal.result, sizeof(stAlarmDeal.result), msg.deal_result().c_str());
    Snprintf(stAlarmDeal.time, sizeof(stAlarmDeal.time), msg.deal_time().c_str());
    stAlarmDeal.manager_account_id = msg.mng_account_id();

    std::string NodeTime = getNodeCurrentTimeString(stAlarmDeal.area_node);
    ::snprintf(stAlarmDeal.time, sizeof(stAlarmDeal.time), "%s", NodeTime.c_str());

    SOCKET_MSG_ALARM_DEAL_OFFLINE stCommAlarmInfo;
    memset(&stCommAlarmInfo, 0, sizeof(stCommAlarmInfo));
    if (DaoCommGetAlarmInfoById(stAlarmDeal.alarm_id, stCommAlarmInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id " << stAlarmDeal.alarm_id;
    }
    Snprintf(stAlarmDeal.type, sizeof(stAlarmDeal.type), stCommAlarmInfo.alarm_type);
    Snprintf(stAlarmDeal.device_name, sizeof(stAlarmDeal.device_name), stCommAlarmInfo.device_location);
    stAlarmDeal.alarm_code = stCommAlarmInfo.alarm_code;
    stAlarmDeal.alarm_zone = stCommAlarmInfo.alarm_zone;
    stAlarmDeal.alarm_location = stCommAlarmInfo.alarm_location;
    stAlarmDeal.alarm_customize = stCommAlarmInfo.alarm_customize;
    stAlarmDeal.manager_account_id = stCommAlarmInfo.manager_account_id;
    stAlarmDeal.unit_id = stCommAlarmInfo.unit_id;
    stAlarmDeal.trace_id = stCommAlarmInfo.trace_id;
    CAlarmDealNotifyMsg cNotifyMsg(stAlarmDeal, stCommAlarmInfo.mac, stCommAlarmInfo.device_location, stCommAlarmInfo.alarm_type);
    GetNotifyMsgControlInstance()->AddAlarmDealNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleGroupPerAlarmReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupPerAlarmMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    SOCKET_MSG_ALARM_SEND recvAlarmMsg;
    memset(&recvAlarmMsg, 0, sizeof(recvAlarmMsg));
    Snprintf(recvAlarmMsg.type, sizeof(recvAlarmMsg.type), msg.alarm_type().c_str());
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address), msg.address().c_str());
    Snprintf(recvAlarmMsg.time, sizeof(recvAlarmMsg.time), msg.time().c_str());
    Snprintf(recvAlarmMsg.msg_seq, sizeof(recvAlarmMsg.msg_seq), msg.msg_seq().c_str());
    Snprintf(recvAlarmMsg.from_local, sizeof(recvAlarmMsg.from_local), msg.from_local().c_str());
    Snprintf(recvAlarmMsg.mac, sizeof(recvAlarmMsg.mac), msg.mac().c_str());
    recvAlarmMsg.id = msg.id();
    recvAlarmMsg.extension = msg.extension();
    recvAlarmMsg.device_type = msg.device_type();
    recvAlarmMsg.alarm_code = msg.alarm_code();
    recvAlarmMsg.alarm_zone = msg.alarm_zone();
    recvAlarmMsg.alarm_location = msg.alarm_location();
    recvAlarmMsg.alarm_customize = msg.alarm_customize();
    Snprintf(recvAlarmMsg.community, sizeof(recvAlarmMsg.community), msg.community().c_str());
    recvAlarmMsg.trace_id = msg.trace_id();

    CPersonnalAlarmNotifyMsg cNotifyMsg(recvAlarmMsg, msg.mac(), msg.from_local(), msg.alarm_type());
    GetNotifyMsgControlInstance()->AddPersonnalAlarmNotifyMsg(cNotifyMsg);

}

void CGroupMsgMng::HandleGroupPerAlarmDealReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupPerAlarmDealMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    SOCKET_MSG_PERSONNAL_ALARM_DEAL personnal_alarm_deal_info;
    memset(&personnal_alarm_deal_info, 0, sizeof(personnal_alarm_deal_info));
    Snprintf(personnal_alarm_deal_info.area_node, sizeof(personnal_alarm_deal_info.area_node), msg.node().c_str());
    Snprintf(personnal_alarm_deal_info.alarm_id, sizeof(personnal_alarm_deal_info.alarm_id), msg.alarm_id().c_str());
    Snprintf(personnal_alarm_deal_info.user, sizeof(personnal_alarm_deal_info.user), msg.deal_user().c_str());
    Snprintf(personnal_alarm_deal_info.result, sizeof(personnal_alarm_deal_info.result), msg.deal_result().c_str());
    Snprintf(personnal_alarm_deal_info.type, sizeof(personnal_alarm_deal_info.type), msg.deal_type().c_str());
    Snprintf(personnal_alarm_deal_info.time, sizeof(personnal_alarm_deal_info.result), msg.deal_time().c_str());
    Snprintf(personnal_alarm_deal_info.community, sizeof(personnal_alarm_deal_info.community), msg.community().c_str());
    //TODO,chenyc,后续改成csadapt查询这部分信息,避免多个csmain均需要查询
    std::string NodeTime = getNodeCurrentTimeString(personnal_alarm_deal_info.area_node);
    ::snprintf(personnal_alarm_deal_info.time, sizeof(personnal_alarm_deal_info.time), "%s", NodeTime.c_str());
    Snprintf(personnal_alarm_deal_info.type, sizeof(personnal_alarm_deal_info.type), "deal personal alarm");

    SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE stPerAlarmDeal;
    if (DaoPerGetAlarmInfoById(personnal_alarm_deal_info.alarm_id, stPerAlarmDeal) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id " << personnal_alarm_deal_info.alarm_id;
    }
    Snprintf(personnal_alarm_deal_info.type, sizeof(personnal_alarm_deal_info.type), stPerAlarmDeal.alarm_type);
    Snprintf(personnal_alarm_deal_info.device_name, sizeof(personnal_alarm_deal_info.device_name), stPerAlarmDeal.device_location);
    personnal_alarm_deal_info.alarm_code = stPerAlarmDeal.alarm_code;
    personnal_alarm_deal_info.alarm_zone = stPerAlarmDeal.alarm_zone;
    personnal_alarm_deal_info.alarm_location = stPerAlarmDeal.alarm_location;
    personnal_alarm_deal_info.alarm_customize = stPerAlarmDeal.alarm_customize;
    personnal_alarm_deal_info.trace_id = stPerAlarmDeal.trace_id;
    CPersonnalAlarmDealMsg cNotifyMsg(personnal_alarm_deal_info, stPerAlarmDeal.mac, stPerAlarmDeal.device_location, stPerAlarmDeal.alarm_type);
    GetNotifyMsgControlInstance()->AddPersonnalAlarmDealNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleGroupMotionMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupPerMotionMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive motion alert from csroute, mac is " << msg.mac();

    SOCKET_MSG_MOTION_ALERT_SEND recv_motion;
    memset(&recv_motion, 0, sizeof(recv_motion));
    Snprintf(recv_motion.mac, sizeof(recv_motion.mac), msg.mac().c_str());
    Snprintf(recv_motion.node, sizeof(recv_motion.node), msg.node().c_str());
    Snprintf(recv_motion.location, sizeof(recv_motion.location), msg.dev_location().c_str());
    Snprintf(recv_motion.sip_account, sizeof(recv_motion.sip_account), msg.sip_account().c_str());
    Snprintf(recv_motion.capture_time, sizeof(recv_motion.capture_time), msg.motion_time().c_str());
    recv_motion.id = msg.id();

    CPersonnalMotionNotifyMsg cNotifyMsg(recv_motion);
    GetMotionMsgControlInstance()->AddPerMotionNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleP2PStartRtspReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::StartRtspReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    uint64_t traceid = pdu->GetTraceId();
    AK_LOG_INFO << "receive start rtsp msg:" << msg.DebugString();
    
    CRtspActionNotifyMsg CRtspMsg(msg.ip(), msg.port(), msg.mac(), csmain::kRtspStart);
    CRtspMsg.setTraceId(traceid);
    CRtspMsg.setRemoteIPV6(msg.ipv6());
    CRtspMsg.setSSRC(msg.ssrc());
    //is_third为1时，mac为三方摄像头的uuid，dev_mac为绑定设备的mac地址
    //is_third为0时，mac/dev_mac为设备mac地址
    CRtspMsg.setDevMac(msg.dev_mac());
    CRtspMsg.setIsthird(msg.is_third());
    CRtspMsg.setVideoType(msg.video_type());
    CRtspMsg.setVideoPt(msg.video_pt());
    CRtspMsg.setTransferDoorUUID(msg.transfer_door_uuid());
    CRtspMsg.setTransferIndoorMac(msg.transfer_indoor_mac());
    CRtspMsg.setVideoFmtp(msg.video_fmtp());
    CRtspMsg.setSrtpKey(msg.srtp_key());
    CRtspMsg.SetRtpConfuse(msg.rtp_confuse());
    CRtspMsg.SetCameraName(msg.camera());
    CRtspMsg.SetStreamID(msg.stream_id());
    GetNotifyMsgControlInstance()->AddRtspActionNotifyMsg(CRtspMsg);
}

void CGroupMsgMng::HandleP2PStopRtspReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    uint64_t traceid = pdu->GetTraceId();
    AK::Route::StopRtspReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "HandleP2PStopRtspReq: " << msg.DebugString();
    CRtspActionNotifyMsg CRtspMsg(msg.ip(), msg.port(), msg.mac(), csmain::kRtspStop);
    CRtspMsg.setTraceId(traceid);
    CRtspMsg.setDevMac(msg.dev_mac());
    CRtspMsg.setIsthird(msg.is_third());
    CRtspMsg.setTransferDoorUUID(msg.transfer_door_uuid());
    CRtspMsg.setTransferIndoorMac(msg.transfer_indoor_mac());
    CRtspMsg.SetCameraName(msg.camera());
    CRtspMsg.SetStreamID(msg.stream_id());
    GetNotifyMsgControlInstance()->AddRtspActionNotifyMsg(CRtspMsg);
}

void CGroupMsgMng::HandleP2PRtspKeepAliveMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::RtspKeepAliveReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    uint64_t traceid = pdu->GetTraceId();
    AK_LOG_INFO << "HandleP2PRtspKeepAliveMsgReq: " << msg.DebugString();
    CRtspKeepNotifyMsg CRtspMsg(msg.mac(), msg.dev_mac(), msg.is_third(), traceid);
    CRtspMsg.setTransferDoorUUID(msg.transfer_door_uuid());
    CRtspMsg.setTransferIndoorMac(msg.transfer_indoor_mac());
    CRtspMsg.SetCameraName(msg.camera());
    CRtspMsg.SetStreamID(msg.stream_id());
    
    GetNotifyMsgControlInstance()->AddRtspKeepNotifyMsg(CRtspMsg);
}

void CGroupMsgMng::HandleP2PAdaptRebootDevMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptRebootDevMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] SendRequestReboot ,mac is " << msg.mac();
    GetDeviceControlInstance()->SendRequestReboot(msg.mac());
}

void CGroupMsgMng::HandleP2PAdaptResetDevMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptRebootDevMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] SendRequestReset ,mac is " << msg.mac();
    GetDeviceControlInstance()->SendRequestReset(msg.mac());
}

void CGroupMsgMng::HandleGroupAdaptPerNodeChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptPerUpdateNodeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] receive AdaptPerNodeChange";
    CPersonnalNodeChangeNotifyMsg cNotifyMsg(msg.node());
    GetNotifyMsgControlInstance()->AddPersonnalNodeChangeNotifyMsg(cNotifyMsg);

}
void CGroupMsgMng::HandleGroupAdaptCommNodeChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptCommUpdateNodeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] receive update comunity node msg from csroute, node is " << msg.node();
    CSP2A_COMMUNITY_UPDATE_NODE update_node;
    memset(&update_node, 0, sizeof(update_node));
    Snprintf(update_node.mac, sizeof(update_node.mac), msg.mac().c_str());
    Snprintf(update_node.node, sizeof(update_node.node), msg.node().c_str());
    update_node.manager_account_id = msg.mng_account_id();
    update_node.unit_id = msg.unit_id();
    update_node.nUpdateDevType = msg.update_dev_type();
    update_node.change_type = msg.change_type();

    CCommunityNodeChangeNotifyMsg cNotifyMsg(&update_node);
    GetNotifyMsgControlInstance()->AddCommunityNodeChangeNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleGroupAdaptPerAlarmDealMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptPerAlarmDealMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    SOCKET_MSG_PERSONNAL_ALARM_DEAL stPerAlarmDeal;
    memset(&stPerAlarmDeal, 0, sizeof(stPerAlarmDeal));
    Snprintf(stPerAlarmDeal.alarm_id, sizeof(stPerAlarmDeal.alarm_id), msg.alarm_id().c_str());
    Snprintf(stPerAlarmDeal.area_node, sizeof(stPerAlarmDeal.area_node), msg.node().c_str());
    Snprintf(stPerAlarmDeal.user, sizeof(stPerAlarmDeal.user), msg.deal_user().c_str());
    Snprintf(stPerAlarmDeal.result, sizeof(stPerAlarmDeal.result), msg.deal_result().c_str());
    Snprintf(stPerAlarmDeal.protocal, sizeof(stPerAlarmDeal.protocal), "1.0");

    std::string NodeTime = getNodeCurrentTimeString(stPerAlarmDeal.area_node);
    ::snprintf(stPerAlarmDeal.time, sizeof(stPerAlarmDeal.time), "%s", NodeTime.c_str());
    Snprintf(stPerAlarmDeal.type, sizeof(stPerAlarmDeal.type), "deal personal alarm");

    SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE stPerAlarmDealInfo;
    if (DaoPerGetAlarmInfoById(stPerAlarmDeal.alarm_id, stPerAlarmDealInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id " << stPerAlarmDeal.alarm_id;
    }

    stPerAlarmDeal.alarm_code = stPerAlarmDealInfo.alarm_code;
    Snprintf(stPerAlarmDeal.community, sizeof(stPerAlarmDeal.community), stPerAlarmDealInfo.community);
    //将通知消息压入队列中
    CPersonnalAlarmDealMsg cNotifyMsg(stPerAlarmDeal, stPerAlarmDealInfo.mac, \
                                      stPerAlarmDealInfo.device_location, stPerAlarmDealInfo.alarm_type);
    GetNotifyMsgControlInstance()->AddPersonnalAlarmDealNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleGroupAdaptCommAlarmDealMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptPerAlarmDealMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    SOCKET_MSG_ALARM_DEAL stCommunityAlarmDeal;
    memset(&stCommunityAlarmDeal, 0, sizeof(stCommunityAlarmDeal));
    Snprintf(stCommunityAlarmDeal.alarm_id, sizeof(stCommunityAlarmDeal.alarm_id), msg.alarm_id().c_str());
    Snprintf(stCommunityAlarmDeal.area_node, sizeof(stCommunityAlarmDeal.area_node), msg.node().c_str());
    Snprintf(stCommunityAlarmDeal.user, sizeof(stCommunityAlarmDeal.user), msg.deal_user().c_str());
    Snprintf(stCommunityAlarmDeal.result, sizeof(stCommunityAlarmDeal.result), msg.deal_result().c_str());
    Snprintf(stCommunityAlarmDeal.protocal, sizeof(stCommunityAlarmDeal.protocal), "1.0");
    std::string NodeTime = getNodeCurrentTimeString(stCommunityAlarmDeal.area_node);
    ::snprintf(stCommunityAlarmDeal.time, sizeof(stCommunityAlarmDeal.time), "%s", NodeTime.c_str());
    Snprintf(stCommunityAlarmDeal.type, sizeof(stCommunityAlarmDeal.type), "deal personal alarm");

    SOCKET_MSG_ALARM_DEAL_OFFLINE stPerAlarmDealInfo;
    if (DaoCommGetAlarmInfoById(stCommunityAlarmDeal.alarm_id, stPerAlarmDealInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id " << stCommunityAlarmDeal.alarm_id;
    }
    stCommunityAlarmDeal.manager_account_id = stPerAlarmDealInfo.manager_account_id;
    stCommunityAlarmDeal.unit_id = stPerAlarmDealInfo.unit_id;
    Snprintf(stCommunityAlarmDeal.device_name, sizeof(stCommunityAlarmDeal.device_name), stPerAlarmDealInfo.device_location);
    stCommunityAlarmDeal.alarm_code = stPerAlarmDealInfo.alarm_code;


    CAlarmDealNotifyMsg cNotifyMsg(stCommunityAlarmDeal, stPerAlarmDealInfo.mac, \
                                   stPerAlarmDealInfo.device_location, stPerAlarmDealInfo.alarm_type);
    GetNotifyMsgControlInstance()->AddAlarmDealNotifyMsg(cNotifyMsg);

}

void CGroupMsgMng::HandleP2PAdaptReportStatusMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptReportStatusMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] Personnal send status request, mac is " << msg.mac();
    GetDeviceControlInstance()->OnPerDevStatusReq(msg.mac());
}
void CGroupMsgMng::HandleP2PAdaptDevLogOutMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::P2PRouteOneDevLogOutMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] Personnal delete device, mac is " << msg.mac();
    GetDeviceControlInstance()->OnPerDelDev(msg.mac());
}
void CGroupMsgMng::HandleP2PAdaptUidLogOutMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::P2PRouteOneUidLogOutMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csadapt] Personnal delete uid, uid is " << msg.uid();
    GetDeviceControlInstance()->OnPerUidLogOutSip(msg.uid());
}

void CGroupMsgMng::HandleGroupAdaptTextMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptTextMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    uint32_t node_cnt = msg.node_list_size();
    if(node_cnt == 0)
    {      
        AK_LOG_WARN << "HandleText node_list empty";
        return;
    }
    std::string account = msg.node_list(0);

    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    text_send.client_type = msg.client_type();
    text_send.text_message.id = msg.id();
    text_send.text_message.type = ATOI(msg.type().c_str());
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), msg.title().c_str());
    Snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), msg.content().c_str());
    Snprintf(text_send.text_message.time, sizeof(text_send.text_message.time), msg.time().c_str());
    Snprintf(text_send.text_message.from, sizeof(text_send.text_message.from), msg.from().c_str());
    Snprintf(text_send.text_message.to, sizeof(text_send.text_message.to), msg.to().c_str());

    CPerTextNotifyMsg notify_msg(text_send, account);
    GetNotifyMsgControlInstance()->AddTextNotifyMsg(notify_msg);
}

void CGroupMsgMng::HandleGroupAdaptConfFileChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CSP2A_CONFIG_FILE_CHANGE conf_file;
    ::memset(&conf_file, 0, sizeof(conf_file));
    Snprintf(conf_file.mac, sizeof(conf_file.mac), msg.mac().c_str());
    Snprintf(conf_file.node, sizeof(conf_file.node), msg.node().c_str());
    conf_file.type = msg.type();
    conf_file.nNotifyType = msg.notify_type();
    conf_file.mng_id = msg.mng_id();
    conf_file.unit_id = msg.unit_id();
    GetDeviceControlInstance()->OnConfigFileChangeReq(&conf_file);
}

void CGroupMsgMng::HandleGroupAdaptDevAppExpireMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptDevAppExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK::Server::GroupAdaptDevAppExpireMsg::GroupAdaptAppExpireInnerMsg inner_msg;
    uint32_t uid_cnt = msg.expire_uid_list_size();
    for (uint32_t i = 0; i < uid_cnt; ++i)
    {
        inner_msg = msg.expire_uid_list(i);
        GetDeviceControlInstance()->OnDevAppExpireReq(inner_msg.uid());
    }
}

void CGroupMsgMng::HandleGroupAdaptDevNotExpireMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptDevNotExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CSP2A_DEV_NOT_EXPIRE dev_expire;;
    ::memset(&dev_expire, 0, sizeof(dev_expire));
    Snprintf(dev_expire.szUids, sizeof(dev_expire.szUids), msg.uids().c_str());
    Snprintf(dev_expire.szMacs, sizeof(dev_expire.szMacs), msg.macs().c_str());
    Snprintf(dev_expire.szNode, sizeof(dev_expire.szMacs), msg.node().c_str());
    dev_expire.nType = msg.type();
    GetDeviceControlInstance()->OnDevNotExpireReq(&dev_expire);
}

void CGroupMsgMng::HandleP2PAdaptOneCleanDeviceCodeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::P2PAdaptOneDevCleanDeviceCodeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    GetDeviceControlInstance()->OnDevCleanDevCodeReq(msg.mac());
}

void CGroupMsgMng::HandleP2PAdaptDevChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDevChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CSP2A_DEVICE_CHANGE_INFO dev_change;
    ::memset(&dev_change, 0, sizeof(dev_change));
    dev_change.nMacid = msg.mac_id();
    dev_change.nIsPer = msg.is_per();
    AK_LOG_INFO << "[csadapt] device modify mac " << msg.mac();
    GetDeviceControlInstance()->OnDevChange(&dev_change);
}

void CGroupMsgMng::HandleGroupAdaptAddVsSchedMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptAddVsSchedMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CSP2A_ADD_VIDEO_STORAGE_SCHED add_video_sched;
    ::memset(&add_video_sched, 0, sizeof(add_video_sched));
    add_video_sched.id = msg.id();
    add_video_sched.sched_type = msg.sched_type();
    add_video_sched.date_flag = msg.date_flag();
    Snprintf(add_video_sched.mac, sizeof(add_video_sched.mac), msg.mac().c_str());
    Snprintf(add_video_sched.begin_time, sizeof(add_video_sched.begin_time), msg.begin_time().c_str());
    Snprintf(add_video_sched.end_time, sizeof(add_video_sched.end_time), msg.end_time().c_str());

    AK_LOG_INFO << "[csadapt] receive add video storage sched msg";
    GetDeviceControlInstance()->OnAddVsSched(&add_video_sched);
}

void CGroupMsgMng::HandleGroupAdaptDelVsSchedMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptDelVsSchedMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CSP2A_DEL_VIDEO_STORAGE_SCHED del_video_sched;
    ::memset(&del_video_sched, 0, sizeof(del_video_sched));
    del_video_sched.id = msg.id();
    del_video_sched.sched_type = msg.sched_type();
    Snprintf(del_video_sched.mac, sizeof(del_video_sched.mac), msg.mac().c_str());

    AK_LOG_INFO << "[csadapt] receive del video storage sched msg";
    GetDeviceControlInstance()->OnDelVsSched(&del_video_sched);
}

void CGroupMsgMng::HandleP2PPmEmergencyDoorControlReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PPmEmergencyDoorControlMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "HandleP2PPmEmergencyDoorControlReq msg=" << msg.DebugString();
    SOCKET_MSG_EMERGENCY_CONTROL control_msg;
    ::memset(&control_msg, 0, sizeof(control_msg));
    Snprintf(control_msg.msg_uuid, sizeof(control_msg.msg_uuid), msg.msg_uuid().c_str());
    Snprintf(control_msg.device_uuid, sizeof(control_msg.device_uuid), msg.device_uuid().c_str());
    Snprintf(control_msg.initiator, sizeof(control_msg.initiator), msg.initiator().c_str());
    Snprintf(control_msg.relay, sizeof(control_msg.relay), msg.relay().c_str());
    Snprintf(control_msg.security_relay, sizeof(control_msg.security_relay), msg.security_relay().c_str());
    control_msg.auto_manual = msg.auto_manual();
    control_msg.operation_type = msg.operation_type();
}

void CGroupMsgMng::HandleGroupMngTextMsgReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupMngTextMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    uint32_t node_cnt = msg.node_list_size();
    if(node_cnt == 0)
    {
        AK_LOG_WARN << "HandleText node_list empty";
        return;
    }
    std::string account = msg.node_list(0);

    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    text_send.client_type = msg.client_type();
    text_send.text_message.id = msg.id();
    text_send.text_message.type = CPerTextNotifyMsg::TEXT_MSG;
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), msg.title().c_str());
    Snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), msg.content().c_str());
    Snprintf(text_send.text_message.time, sizeof(text_send.text_message.time), msg.time().c_str());
    Snprintf(text_send.text_message.from, sizeof(text_send.text_message.from), msg.from().c_str());
    Snprintf(text_send.text_message.to, sizeof(text_send.text_message.to), msg.to().c_str());

    CPerTextNotifyMsg cNotifyMsg(text_send, account);
    GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleP2PUpgradeDevReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::P2PUpgradeDevMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    std::vector<std::string> macs;
    uint32_t mac_cnt = msg.mac_list_size();
    for (uint32_t i = 0; i < mac_cnt; ++i)
    {
        std::string mac = msg.mac_list(i);
        macs.push_back(mac);
    }
    std::string firmware_url = msg.firmware_url();
    std::string firmware_ver = msg.firmware_ver();
    int is_need_reset = msg.is_need_reset();
    AK_LOG_INFO << "[csroute] request upgrade dev";
    for (const auto& mac : macs)
    {
        evpp::TCPConnPtr conn;
        if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
        {
            AK_LOG_WARN << "dev's tcp conn has been disconnected, so Upgrade failed, mac is " << mac;
            continue;
            //后续如果支持设备上线后,再次更新,则可以根据上报状态,查表比较formware的版本.
        }

        //升级需要恢复出厂，升级前下发Autop
        if (is_need_reset)
        {
            SOCKET_MSG_CONFIG socket_msg_config;
            memset(&socket_msg_config, 0, sizeof(socket_msg_config));
            Snprintf(socket_msg_config.mac, sizeof(socket_msg_config.mac), mac.c_str());
            Snprintf(socket_msg_config.protocal, sizeof(socket_msg_config.protocal), "1.0");
            socket_msg_config.config_count = 1;
            Snprintf(socket_msg_config.module.item[0], sizeof(socket_msg_config.module.item[0]), "Config.Setting.UPGRADE_RESET.Enable=1");

            SOCKET_MSG socket_msg;
            memset(&socket_msg, 0, sizeof(socket_msg));

            if (CMsgControl::GetInstance()->BuildUpdateConfigMsg(&socket_msg, &socket_msg_config) < 0)
            {
                AK_LOG_WARN << "BuildUpdateConfigMsg failed.";
                continue;
            }

            if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size) < 0)
            {
                AK_LOG_WARN << "Send TcpMsg failed.";
                continue;
            }
        }

        GetDeviceControlInstance()->SendUpgrade(conn, firmware_url, firmware_ver, mac, is_need_reset);
        AK_LOG_INFO << "send Upgrade msg to dev, mac is " << mac;
    }
}

void CGroupMsgMng::HandleP2PAppGetArmingReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainAppHandleArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] app get dev arming req, mac is " << msg.mac() << ", uid is " << msg.uid();
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), "%s", msg.mac().c_str());
    snprintf(stArmingMsg.uid, sizeof(stArmingMsg.uid), "%s", msg.uid().c_str());
    snprintf(stArmingMsg.szAction, sizeof(stArmingMsg.szAction), "%s", msg.action().c_str());
    stArmingMsg.mode = msg.mode();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(stArmingMsg.mac, dev_conn) != 0)
    {
        AK_LOG_WARN << msg.mac() << " dev offline, RequestArming failed.";
        return;
    }

    //组装消息
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildReqArming(&stSocketMsg, stArmingMsg) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal RequestArming to dev failed.";
        return;
    }
}

//app请求上报，或者室内机自己设置后上报
void CGroupMsgMng::HandleP2PAppGetArmingResp(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainAppHandleArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] app get dev arming resp, uid is " << msg.DebugString();
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), "%s", msg.mac().c_str());
    snprintf(stArmingMsg.uid, sizeof(stArmingMsg.uid), "%s", msg.uid().c_str());
    stArmingMsg.mode = msg.mode();
    stArmingMsg.resp_action = msg.resp_action();
    stArmingMsg.home_sync = msg.home_sync();
    int oem  = msg.oem();

    //组装消息
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildRespArming(stSocketMsg, dy_iv_msg, stArmingMsg) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return;
    }

    if (oem == OEMID_ROBERT || oem == OEMID_ROBERT2 || msg.home_sync() == ARMING_HOME_SYNC_TYPE_ON || 
        msg.home_sync() == ARMING_HOME_SYNC_TYPE_NOTIFY_OFF_CONFIG ||  msg.home_sync() == ARMING_HOME_SYNC_TYPE_NOTIFY_ON_CONFIG)
    {
        int resp_action  = msg.resp_action();
        if (resp_action != REPORT_ARMING_ACTION_TYPE_GET) //别的情况广播到全部的联动
        {
            std::vector<evpp::TCPConnPtr> oDevConn;
            g_accSer_ptr->GetDevListByNode(msg.node(), oDevConn);

            for (const auto& DevConn : oDevConn)
            {
                //设备用mac加密
                if (!g_accSer_ptr->IsTCPConnIsAPP(DevConn))
                {
                    DEVICE_SETTING device_setting;
                    if (g_accSer_ptr->GetDeviceSettingFromConnList(DevConn, &device_setting) != 0)
                    {
                        AK_LOG_WARN << "dev offline, RequestArming failed.";
                        continue;
                    }
                    if (resp_action == REPORT_ARMING_ACTION_TYPE_DEV_SET && strcmp(stArmingMsg.mac, device_setting.mac) == 0)
                    {
                        AK_LOG_INFO << stArmingMsg.mac << " dev handle arming,respond Arming, multicast message. but it is youself,do not send youself.ignore. ";
                        continue;
                    }
                    else if (strcmp(stArmingMsg.mac, device_setting.mac) == 0)
                    {
                        AK_LOG_INFO << stArmingMsg.mac << " respond Arming, multicast message. but it is youself, do not send youself.ignore. ";
                        continue;
                    }
                    //组装消息
                    SOCKET_MSG stSocketMsg2;
                    memset(&stSocketMsg2, 0, sizeof(stSocketMsg2));
                    if (GetMsgControlInstance()->OnBuildRespArmingToDev(&stSocketMsg2, stArmingMsg, device_setting.mac) != 0)
                    {
                        AK_LOG_WARN << "BuildReqArming failed";
                        continue;
                    }
                    if (GetDeviceControlInstance()->SendTcpMsg(DevConn, stSocketMsg2.data, stSocketMsg2.size) < 0)
                    {
                        AK_LOG_WARN << "Send personnal ReportArming to dev failed.";
                        continue;
                    }
                    //更新设备arming状态
                    GetDeviceControlInstance()->UpdateDeviceArming(device_setting.mac, stArmingMsg.mode, device_setting.is_personal);
                }
                else if (resp_action == REPORT_ARMING_ACTION_TYPE_FORBID) //是app且是异常
                {
                    //不知道app操作哪个设备,全部发出去
                    //广播给app需要指定mac，如果是异常的布防，需要告诉给mac布防失败。比如：给设备1布防，但是设备2布防异常(会在上报布撤防信息)，那么需要返回给app布防不了。
                    AK_LOG_WARN << "Set Arming FORBID, multicast message to app.";
                    std::vector<DEVICE_SETTING> devs;
                    GetDeviceControlInstance()->GetDevicesByNode(msg.node(), -1/*unknown*/, devs);
                    for (auto& dev : devs)
                    {
                        if (dev.type == DEVICE_TYPE_INDOOR)
                        {
                            SOCKET_MSG_DEV_ARMING stArmingMsg;
                            memset(&stArmingMsg, 0, sizeof(stArmingMsg));
                            snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), "%s", dev.mac);
                            stArmingMsg.mode = msg.mode();

                            //组装消息
                            SOCKET_MSG stSocketMsg,dy_iv_msg;
                            memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                            if (GetMsgControlInstance()->OnBuildRespArming(stSocketMsg, dy_iv_msg, stArmingMsg) != 0)
                            {
                                AK_LOG_WARN << "BuildReqArming failed";
                                return;
                            }

                            if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_msg) < 0)
                            {
                                AK_LOG_WARN << "Send personnal ReportArming to dev failed.";
                                continue;
                            }

                        }
                    }
                }
                else
                {
                    //正常情况广播给所有的app
                    if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_msg) < 0)
                    {
                        AK_LOG_WARN << "Send personnal ReportArming to dev failed.";
                        continue;
                    }
                }
            }
            return;
        }
    }
    evpp::TCPConnPtr uid_conn;
    if (g_accSer_ptr->GetDevConnByMainUid(msg.main_site(), uid_conn) != 0)
    {
        AK_LOG_WARN << "the tcp conn of uid: " << stArmingMsg.uid << "  is offline, ReportArming failed";
        return;
    }
    if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(uid_conn, stSocketMsg, dy_iv_msg) < 0)
    {
        AK_LOG_WARN << "Send personnal ReportArming to dev failed.";
        return;
    }

}


void CGroupMsgMng::HandleGroupAlexaLogin(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptAlexaLoginMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] alexa login, uid is " << msg.node();
    g_accSer_ptr->AlexaLoginAddConnDetect(msg.node());

}


void CGroupMsgMng::HandleP2PAlexaSetArming(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptAlexaSetArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] alexa set arming, mac is :" << msg.mac() << " mode:" << msg.mode();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "dev offline, RequestArming failed.";
        return;
    }
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    ::snprintf(stArmingMsg.uid, sizeof(stArmingMsg.uid), "Alexa%s", msg.mac().c_str());
    ::snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), "%s", msg.mac().c_str());
    ::snprintf(stArmingMsg.szAction, sizeof(stArmingMsg.szAction), "%s", "Set");
    stArmingMsg.mode = msg.mode();
    //组装消息
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildReqArming(&stSocketMsg, stArmingMsg) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal RequestArming to dev failed.";
        return;
    }
    return;
}

void CGroupMsgMng::HandleP2PVisitorAuthorize(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainHandleVisitorAuth msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] visitor authorize, mac is " << msg.mac();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac().c_str(), dev_conn) != 0)
    {
        AK_LOG_WARN << msg.mac() << " dev offline, Visitor authorize failed.";
        return;
    }

    //组装消息
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildVisitorAuth(&stSocketMsg, msg.count(), msg.mac()) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal RequestArming to dev failed.";
        return;
    }
}

void CGroupMsgMng::HandleP2PForwardFaceData(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainHandleForwardFaceData msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    //将消息组装完成,写入csmain的notify消息队列
    SOCKET_MSG_DEV_REPORT_FACE_DATA face_data_msg;
    memset(&face_data_msg, 0, sizeof(SOCKET_MSG_DEV_REPORT_FACE_DATA));
    Snprintf(face_data_msg.model_url, sizeof(face_data_msg.model_url), msg.model_url().c_str());

    CFaceDataNotifyMsg cNotifyMsg(face_data_msg, msg.mac_list());
    GetNotifyMsgControlInstance()->AddFaceDataNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleP2PCreateRemoteDevContorl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptCreateRemoteDevContorlMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] create remote device contorl, mac is:" << msg.mac() << " port:" << msg.port();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "dev offline, CreateRemoteDevContorl failed.";
        return;
    }
    std::string mac = msg.mac();

    SOCKET_MSG_REMOTE_DEV_CONTORL remote_config;
    memset(&remote_config, 0, sizeof(remote_config));
    ::snprintf(remote_config.user, sizeof(remote_config.user), "%s", msg.user().c_str());
    ::snprintf(remote_config.password, sizeof(remote_config.password), "%s", msg.password().c_str());
    ::snprintf(remote_config.mac, sizeof(remote_config.mac), "%s", mac.c_str());
    remote_config.port = msg.port();
    Snprintf(remote_config.ssh_proxy_domain, sizeof(remote_config.ssh_proxy_domain), msg.ssh_proxy_domain().c_str());

    //组装消息
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildRemoteDeviceContorl(&stSocketMsg, remote_config) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal RequestArming to dev failed.";
        return;
    }
    return;
}

void CGroupMsgMng::HandleP2PChangeRelayReq(const std::unique_ptr<CAkcsPdu>& pdu)
{   
    AK::Server::P2PMainChangeRelay msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] app request change relay: " << msg.DebugString();
    int relay_type = msg.relay_type();
    if (relay_type == IndoorRelayType::TYPE_LOCAL) //旧的relay走之前的
    {
        GetDeviceControlInstance()->SendRequestKeepChangeRelay(msg.mac(), msg.relay_id(), msg.relay_switch());
    }
    else
    {
        GetDeviceControlInstance()->SendRequestToDevChangeIndoorRelay(msg.mac(), msg.relay_id(), msg.relay_switch(), msg.relay_type());
    }
    return;
}

void CGroupMsgMng::HandleNotifyFaceServerPicDownloadMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicDownloadNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] FaceServerPicDownloadMsg:" << msg.DebugString();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << msg.mac();
        return;
    }

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "RegisterFace";
    tag_map[csmain::xmltag::MAC] = msg.mac();
    tag_map[csmain::xmltag::URL] = msg.url();
    tag_map[csmain::xmltag::PIC_MD5] = msg.picmd5();
    tag_map[csmain::xmltag::NAME] = msg.name();
    tag_map[csmain::xmltag::DOOR_NUM] = msg.doornum();
    tag_map[csmain::xmltag::WEEK] = msg.week();
    tag_map[csmain::xmltag::TIME_START] = msg.time_start();
    tag_map[csmain::xmltag::TIME_END] = msg.time_end();
    tag_map[csmain::xmltag::ID] = msg.id();


    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_REGISTER_FACE, tag_map) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }

    return;
}

void CGroupMsgMng::HandleNotifyFaceServerPicModifyMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] FaceServerPicModifyMsg:" << msg.DebugString();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << msg.mac();
        return;
    }

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "ModifyFace";
    tag_map[csmain::xmltag::MAC] = msg.mac();
    tag_map[csmain::xmltag::URL] = msg.url();
    tag_map[csmain::xmltag::PIC_MD5] = msg.picmd5();
    tag_map[csmain::xmltag::NAME] = msg.name();
    tag_map[csmain::xmltag::DOOR_NUM] = msg.doornum();
    tag_map[csmain::xmltag::WEEK] = msg.week();
    tag_map[csmain::xmltag::TIME_START] = msg.time_start();
    tag_map[csmain::xmltag::TIME_END] = msg.time_end();
    tag_map[csmain::xmltag::ID] = msg.id();


    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_MODIFY_FACE, tag_map) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }

    return;
}

void CGroupMsgMng::HandleNotifyFaceServerPicDeleteMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicDeleteNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] FaceServerPicDeleteMsg:" << msg.DebugString();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << msg.mac();
        return;
    }

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "DeleteFace";
    tag_map[csmain::xmltag::MAC] = msg.mac();
    tag_map[csmain::xmltag::NAME] = msg.name();
    tag_map[csmain::xmltag::ID] = msg.id();

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_DELETE_FACE, tag_map) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }

    return;
}

void CGroupMsgMng::HandleP2PSendDeliveryReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainSendDelivery msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request send delivery account:" << msg.account();  

    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    if(CPerTextNotifyMsg::DELIVERY_BOX_MSG == msg.type())
    {
        ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "%s", msg.content().c_str());
    }
    else
    {
        ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "%d", msg.amount());
    }

    int ret_msg = csmain::DaoInsertDeliveryMsg(msg.account(), text_send.text_message.content, msg.type());
    if (ret_msg < 0)
    {
        AK_LOG_INFO << "DeliveryNotifyMsg, maybe dclient < 6000, account is " <<  msg.account();
        return;
    }
    
    text_send.client_type = CPerTextNotifyMsg::APP_SEND;
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), "Delivery");
    text_send.text_message.type = msg.type();
    text_send.text_message.id = ret_msg;
    CPerTextNotifyMsg cNotifyMsg(text_send, msg.account());
    GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleP2PSendTmpkeyUsedReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainSendTmpkeyUsed msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request send Tmpkey Used account:" << msg.account() << " name:" << msg.name();  
    int ret_msg = csmain::DaoInsertMessage(msg.account(), msg.name());
    if (ret_msg < 0)
    {
        AK_LOG_INFO << "TmpkeyUsedNotifyMsg, maybe dclient < 6100, account is " <<  msg.account();
        return;
    }
    
    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    text_send.client_type = CPerTextNotifyMsg::APP_SEND;
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), "TempKey Used");
    ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "%s", msg.name().c_str());
    text_send.text_message.type = CPerTextNotifyMsg::TMPKEY_MSG;
    text_send.text_message.id = ret_msg;
    CPerTextNotifyMsg cNotifyMsg(text_send, msg.account());
    GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
}

void CGroupMsgMng::HandleP2PNotifyRefreshConnCache(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptNotifyRefreshConnCache msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request notify refresh conn cache: " << msg.DebugString();

    if(REFRESH_CACHE_TYPE::DELETE_USER ==  msg.type())    
    {
        CAkUserManager::GetInstance()->RemoveUselessUserTokenByNode(msg.node());
    }
    else if(REFRESH_CACHE_TYPE::MODIFY_DEV ==  msg.type())
    {
        evpp::TCPConnPtr dev_conn;
        if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
        {
            return;
        }
        DEVICE_SETTING dev;
        memset(&dev, 0, sizeof(dev));
        GetDeviceControlInstance()->GetDeviceSettingByMac(msg.mac(), &dev);
        g_accSer_ptr->UpdateTcpConnSetting(dev_conn, &dev);
    }
}

void CGroupMsgMng::HandleP2PChangeMainSite(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptNotifyChangeMainSite msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] change main site: " << msg.DebugString();
    std::string main_account = msg.after_main_site();
    CAkUserManager::GetInstance()->AddMainSiteToNodes(msg.after_main_site());
}


void CGroupMsgMng::HandleNotifyConfigUpdate(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Adapt::DevConfigUpdateNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] ConfigUpdate msg:" << msg.DebugString();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << msg.mac();
        return;
    }

    SOCKET_MSG_CONFIG socket_msg_config;
    memset(&socket_msg_config, 0, sizeof(socket_msg_config));
    Snprintf(socket_msg_config.mac, sizeof(socket_msg_config.mac), msg.mac().c_str());
    Snprintf(socket_msg_config.protocal, sizeof(socket_msg_config.protocal), "1.0");
    ParseConfigItem(msg.config(), socket_msg_config);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->BuildUpdateConfigMsg(&socket_msg, &socket_msg_config) < 0)
    {
        AK_LOG_WARN << "BuildUpdateConfigMsg failed.";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return;
    }
}

void CGroupMsgMng::HandleNotifyDoorControl(const std::unique_ptr<CAkcsPdu>& pdu, int type)
{
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] DoorControl type=" << type <<";msg:" << msg.DebugString();
    GetDeviceControlInstance()->SendRequestKeepChangeRelay(msg.mac(), msg.relay(), type);
    return;
}

void CGroupMsgMng::HandleNotifyDevFileChange(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PAdaptNotifyFileChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    GIVEN_KEY_SEND keysend;
    Snprintf(keysend.mac, sizeof(keysend.mac), msg.mac().c_str());
    Snprintf(keysend.file_md5, sizeof(keysend.file_md5), msg.file_md5().c_str());
    Snprintf(keysend.file_path, sizeof(keysend.file_path), msg.file_path().c_str());
    
    keysend.type = msg.type();
    keysend.traceid = msg.msg_traceid();
    
#ifdef AKCS_EVENT_FILTER
        //added by chenyc, 2022.01.19,发送给拦截器,压测用
        if(gstAKCSConf.stress_test)
        {
           g_event_filter->DealEvent(EF_TEST_RESP_USER_INFO_UPDATE, keysend);
        }
#endif

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << msg.mac();
        return;
    }

    keysend.weak_conn = dev_conn;

    dbinterface::DevUpdateUserLog::UpdateFilePath(keysend.mac, keysend.traceid, keysend.file_path);
    if (GetKeyControlInstance()->AddGivenKeySend(keysend) < 0)
    {
        AK_LOG_WARN << "Add given KeySend failed.";
        return;
    }
}

void CGroupMsgMng::HandleP2POfflineResendMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PStorageHandleOfflineAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] HandleP2POfflineResendMsgAckReq:" << msg.DebugString();

    std::string sequence = dbinterface::OfflineResendLog::GetOfflineResendLogSeq(msg.mac(), msg.tar_filename());
    GetDeviceControlInstance()->SendAck(msg.mac(), sequence, MSG_FROM_DEVICE_SYNC_ACTIVITY);
}

void CGroupMsgMng::HandleP2PVoiceMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] HandleP2PVoiceResendMsgAckReq:" << msg.DebugString();

    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac), msg.mac().c_str());
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id), msg.filename().c_str());
    common_ack.result = msg.result();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), dev_conn) != 0)
    {
        AK_LOG_WARN << msg.mac() << " dev offline, SendVoiceMsg failed.";
        return;
    }

    if (GetMsgControlInstance()->OnDeviceUploadVoiceMsg(common_ack, dev_conn, msg.project_type()) != 0)
    {
        AK_LOG_WARN << "OnDeviceUploadVoiceMsg failed";
        return;
    }
}

void CGroupMsgMng::HandleP2PSendVoiceMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendVoiceMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request send voice msg:" << msg.DebugString();  

    if (msg.receiver_type() == DEVICE_TYPE_APP)
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(msg.receiver_uuid(), per_account))
        {
            SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
            memset(&text_send.text_message, 0, sizeof(text_send.text_message));

            text_send.client_type = CPerTextNotifyMsg::APP_SEND;
            Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), "VOICE_MSG");
            text_send.text_message.id = msg.msg_id();
            ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "You have a voice message from %s", msg.location().c_str());
            text_send.text_message.type = CPerTextNotifyMsg::VOICE_MSG;
            CPerTextNotifyMsg cNotifyMsg(text_send, per_account.account);
            GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
        }
    }
    else if (msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
        memset(&online_msg, 0, sizeof(online_msg));
        online_msg.unread_voice_count = msg.count();
        ResidentDev dev;
        if (0 == dbinterface::ResidentDevices::GetUUIDDev(msg.receiver_uuid(), dev))
        {
            Snprintf(online_msg.mac, sizeof(online_msg.mac), dev.mac);
        }

        evpp::TCPConnPtr dev_conn;
        if (g_accSer_ptr->GetDevConnByUUID(msg.receiver_uuid(), dev_conn) != 0)
        {
            AK_LOG_WARN << msg.receiver_uuid() << " dev offline, SendVoiceMsg failed.";
            return;
        }

        if (GetMsgControlInstance()->OnSendOnlineNotifyMsg(dev_conn, online_msg) != 0)
        {
            AK_LOG_WARN << "OnSendOnlineNotifyMsg failed";
            return;
        }
    }
    
}

void CGroupMsgMng::HandleRequestDevDelLog(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendRequestDevDelLog msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request send dev del log msg:" << msg.DebugString();

    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(msg.mac(), conn) == 0)
    {
        GetMsgControlInstance()->OnSendRequestDevDelLog(conn, msg.mac());
    }
    
}


