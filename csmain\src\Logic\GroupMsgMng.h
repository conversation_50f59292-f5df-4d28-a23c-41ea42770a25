#ifndef __CSMAIN_GROUP_MSG_MNG_H__
#define __CSMAIN_GROUP_MSG_MNG_H__

#include <boost/noncopyable.hpp>
#include "AkcsPduBase.h"
#include "AK.Server.pb.h"
#include "MsgControl.h"

//P2P:指的是从csroute->csmain是单播的
//Group:指的是从csroute->csmain是广播的
class CGroupMsgMng : public boost::noncopyable
{
public:
    CGroupMsgMng() {}
    virtual ~CGroupMsgMng() {}
    static CGroupMsgMng* Instance();
    //下面的消息来自route对csmain的转发
    void HandleGroupCommAlarmReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupCommAlarmDealReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupPerAlarmReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupPerAlarmDealReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupMotionMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupMngTextMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAppGetArmingReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAppGetArmingResp(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PVisitorAuthorize(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PForwardFaceData(const std::unique_ptr<CAkcsPdu>& pdu);
    //下面的消息来自route对csvrtspd的转发
    void HandleP2PStartRtspReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PStopRtspReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PRtspKeepAliveMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    //下面的消息来自route对pbx的转发
    void HandleP2PPbxWakeUpAppMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PPbxQueryAppStatusReq(const std::unique_ptr<CAkcsPdu>& pdu, const evpp::TCPConnPtr& conn);
    //下面的消息来自route对csadapt的转发
    void HandleP2PAdaptRebootDevMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptPerNodeChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptCommNodeChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptPerAlarmDealMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptCommAlarmDealMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAdaptReportStatusMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAdaptDevLogOutMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAdaptUidLogOutMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptTextMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptConfFileChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptDevAppExpireMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptDevNotExpireMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAdaptOneCleanDeviceCodeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAdaptDevChangeMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptAddVsSchedMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAdaptDelVsSchedMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PUpgradeDevReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleGroupAlexaLogin(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PAlexaSetArming(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PCreateRemoteDevContorl(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PPmEmergencyDoorControlReq(const std::unique_ptr<CAkcsPdu>& pdu);

    //faceserver部分
    //发消息通知AKCS让设备去下载图片
    void HandleNotifyFaceServerPicDownloadMsg(const std::unique_ptr<CAkcsPdu>& pdu);
    //发消息通知AKCS让设备修改人脸数据
    void HandleNotifyFaceServerPicModifyMsg(const std::unique_ptr<CAkcsPdu>& pdu);
    //发消息通知AKCS让设备删除人脸数据
    void HandleNotifyFaceServerPicDeleteMsg(const std::unique_ptr<CAkcsPdu>& pdu);
    //发送快递消息
    void HandleP2PSendDeliveryReq(const std::unique_ptr<CAkcsPdu>& pdu);
    //刷新设备conn缓存
    void HandleP2PNotifyRefreshConnCache(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleNotifyConfigUpdate(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleNotifyDoorControl(const std::unique_ptr<CAkcsPdu>& pdu, int type);
    void HandleP2PSendTmpkeyUsedReq(const std::unique_ptr<CAkcsPdu>& pdu);
    
    void HandleP2PChangeRelayReq(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleNotifyDevFileChange(const std::unique_ptr<CAkcsPdu> &pdu);
    void HandleP2POfflineResendMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu);
    //6.5
    void HandleP2PSendVoiceMsg(const std::unique_ptr<CAkcsPdu>& pdu);
    void HandleP2PVoiceMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu);

    void HandleP2PAdaptResetDevMsgReq(const std::unique_ptr<CAkcsPdu>& pdu);
    
    void HandleP2PChangeMainSite(const std::unique_ptr<CAkcsPdu>& pdu);    
    
    void HandleRequestDevDelLog(const std::unique_ptr<CAkcsPdu>& pdu);
    
private:
    static CGroupMsgMng* s_group_msg_mng_instance_;

};

#endif /* __CSMAIN_GROUP_MSG_MNG_H__ */

