#include <algorithm>

#include "evpp/http/http_server.h"
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "json/json.h"

#include "http/HttpMsgControl.h"
#include "HttpOuterServer.h"
#include "AkLogging.h"
#include "Md5.h"
#include "BasicDefine.h"
#include "DclientMsgSt.h"
#include "SDMCMsg.h"
#include "util.h"


const std::string   LINTERN_ADDR = "";                  // 监听地址
const int           SERVER_THREAD_NUM = 0;              // 线程数
const int           HTTP_OUTER_SERVER_PORT = 8814;      // 监听端口

const int           MAC_LENGTH = 12;                    // mac地址长度

std::vector<HTTP_MSG_UPDATE_TIMEZONE_DATE> automation_test_timezone_update_device_list;

bool Authenticate(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The request parameter is invalid";
        return false;
    }

    // 获取和检查参数
    std::string remote_ip = ctx->remote_ip();
    std::string token = ctx->GetQuery("token");
    std:: string mac = root["mac"].asString();
    std:: string serveraddr = root["serveraddr"].asString();
    if (mac.length() != MAC_LENGTH || token.empty() || remote_ip.empty())
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The request parameter is invalid";
        return false;
    }

    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: remote_ip=" << remote_ip 
             << ", mac=" << mac ;

    // 验证token
    std::string mac_key_str = mac + TEST_API_PRIVATE_MD5_KEY;
    std::string md5_str = akuvox_encrypt::MD5(mac_key_str).toStr();

    std::transform(token.begin(), token.end(), token.begin(), ::tolower);
    std::transform(md5_str.begin(), md5_str.end(), md5_str.begin(), ::tolower);
    if (md5_str != token)
    {
        LOG_INFO << "http req is [ " << ctx->original_uri() << " ]: The token parameter is invalid";
        return false;
    }

    return true;
}

// 重新走rps
//curl -H "Content-Type:application/json" -X POST --data '{"mac":"xxx","serveraddr":"xxxx"}' http://localhost:9998/reconnectRps
void HttpOuterReqDevReconnectRpsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if (Authenticate(ctx, cb) == false)
    {
        cb(buildErrorHttpMsg(-1, "Http request failed: invalid request"));
        return;
    }

    HttpReqDevReconnectRpsCallback(loop, ctx, cb);
}

// 重新走网关
//curl -H "Content-Type:application/json" -X POST --data '{"mac":"xxx","serveraddr":"xxxx:9999"}' http://localhost:9998/reconnectGateway
void HttpOuterReqDevReconnectGateWayCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if (Authenticate(ctx, cb) == false)
    {
        cb(buildErrorHttpMsg(-1, "Http request failed: invalid request"));
        return;
    }

    HttpReqDevReconnectGateWayCallback(loop, ctx, cb);
}

// 重新走接入服务器
//curl -H "Content-Type:application/json" -X POST --data '{"mac":"xxx","serveraddr":"xxxx:8051"}' http://localhost:9998/reconnectAccessServer
void HttpOuterReqDevReconnectAccessServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if (Authenticate(ctx, cb) == false)
    {
        cb(buildErrorHttpMsg(-1, "Http request failed: invalid request"));
        return;
    }

    HttpReqDevReconnectAccessServerCallback(loop, ctx, cb);
}

//curl -H "Content-Type:application/json" -X POST --data '{"mac":"ABCDABCD0001","type":"TzData/TzXml", "timezone_md5":"文件md5", "timezone_url":"http://自动化环境本地下载时区文件地址"}'
void HttpNotifyUpdateTzDataCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if (Authenticate(ctx, cb) == false)
    {
        cb(buildErrorHttpMsg(-1, "Http request failed: invalid request"));
        return;
    }
    
    Json::Reader reader;
    Json::Value root;

    if (!reader.parse(ctx->body().ToString(), root)) 
    {
        cb(buildErrorHttpMsg(-1, "param error"));
        return;
    }

    if (!root.isMember("mac") || !root["mac"].isString() ||
        !root.isMember("type") || !root["type"].isString() ||
        !root.isMember("timezone_md5") || !root["timezone_md5"].isString() ||
        !root.isMember("timezone_url") || !root["timezone_url"].isString()) {
        cb(buildErrorHttpMsg(-1, "param error"));
        return;
    }

    std::string mac = root["mac"].asString();
    std::string type = root["type"].asString();
    std::string timezone_md5 = root["timezone_md5"].asString();
    std::string timezone_url = root["timezone_url"].asString();

    LOG_INFO << "Received parameters: mac=" << mac
             << ", type=" << type
             << ", timezone_md5=" << timezone_md5
             << ", timezone_url=" << timezone_url;
        
    HTTP_MSG_UPDATE_TIMEZONE_DATE device_data;
    Snprintf(device_data.mac, sizeof(device_data.mac), mac.c_str());
    Snprintf(device_data.type, sizeof(device_data.type), type.c_str());
    Snprintf(device_data.tz_data_md5, sizeof(device_data.tz_data_md5), timezone_md5.c_str());
    if(type == DEVICE_TIME_ZONE_TYPE_TZDATA)
    {
        Snprintf(device_data.tz_data_url, sizeof(device_data.tz_data_url), timezone_url.c_str());
    }
    else
    {
        Snprintf(device_data.tz_xml_url, sizeof(device_data.tz_xml_url), timezone_url.c_str());
    }

    // 查找是否已经存在相同的 mac
    auto it = std::find_if(automation_test_timezone_update_device_list.begin(), automation_test_timezone_update_device_list.end(),
                           [&mac](const HTTP_MSG_UPDATE_TIMEZONE_DATE& device_data) {
                               return std::string(device_data.mac) == mac;
                           });

    if (it != automation_test_timezone_update_device_list.end()) {
        // 如果找到相同的 mac，则更新
        *it = device_data;
        LOG_INFO << "Updated existing device_data for mac: " << mac;
    } else {
        // 如果没有找到，则插入新的数据
        automation_test_timezone_update_device_list.push_back(device_data);
        LOG_INFO << "Inserted new device_data for mac: " << mac;
    }

    cb(buildErrorHttpMsg(0, "success"));
    return;
}


void startHttpOuterServer()
{
    evpp::http::Server server(LINTERN_ADDR, SERVER_THREAD_NUM, false);
    server.RegisterDefaultHandler(&DefaultHandler);

    //设备交互相关
    server.RegisterHandler("/reconnectRps", HttpOuterReqDevReconnectRpsCallback);                    //重新走rps
    server.RegisterHandler("/reconnectGateway", HttpOuterReqDevReconnectGateWayCallback);            //重新走网关
    server.RegisterHandler("/reconnectAccessServer", HttpOuterReqDevReconnectAccessServerCallback);  //重新走接入服务器

    //时区更新自动化
    server.RegisterHandler("/NotifyUpdateTzData", HttpNotifyUpdateTzDataCallback);
    server.Init(HTTP_OUTER_SERVER_PORT);
    server.Start();

    while (!server.IsStopped())
    {
        usleep(1000 * 1000 * 10);
    }
    return;
}

