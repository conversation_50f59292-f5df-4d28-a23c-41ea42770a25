#include <iostream>
#include <string>
#include <map>
#include <curl/curl.h>
#include <curl/types.h>
#include <curl/easy.h>
#include <sstream>
#include <stdlib.h>
#include <string.h>
#include "json/json.h"
#include "HttpRequest.h"
#include "AkLogging.h"
#include "InnerSt.h"

using namespace Akcs;

#ifdef __cplusplus
extern "C" {
#endif


extern AKCS_CONF gstAKCSConf; //全局配置信息

typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespuestKV;
#define HTTP_HEAD_AUTH "api-auth:aws550!Aku690vox13Mess@age"
#define HTTP_URL_AWS_MESSAGE "https://%s/InsertAwsMessage"
#define HTTP_URL_AWS_ALARM "https://%s/InsertAwsAlarm"
#define HTTP_URL_AWS_PER_ALARM "https://%s/InsertAwsPersonalAlarm"


struct MemoryStruct 
{
    char *memory;
    size_t size;
    MemoryStruct()
    {
        memory = (char *)malloc(1);
        size = 0;
    }
    ~MemoryStruct()
    {
        free(memory);
        memory = NULL;
        size = 0;
    }
};

size_t WriteMemoryCallback(void *ptr, size_t size, size_t nmemb, void *data)
{
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *)data;

    mem->memory = (char *)realloc(mem->memory, mem->size + realsize + 1);
    if (mem->memory) 
    {
        memcpy(&(mem->memory[mem->size]), ptr, realsize);
        mem->size += realsize;
        mem->memory[mem->size] = 0;
    }
    return realsize;
}

static int HttpPostRequest(const std::string &url, const std::string &data,  std::string &respone)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *cur_url = NULL;
    cur_url = curl_easy_init();

    if( NULL == cur_url)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    AK_LOG_INFO << "http post:" << url << " datas:" << data;
    
    curl_easy_setopt(cur_url, CURLOPT_CUSTOMREQUEST, "POST");
    curl_easy_setopt(cur_url, CURLOPT_TIMEOUT, 3L);//请求超时时长
    curl_easy_setopt(cur_url, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长 
    curl_easy_setopt(cur_url, CURLOPT_SSL_VERIFYPEER, false);
    curl_easy_setopt(cur_url, CURLOPT_SSL_VERIFYHOST, false);
    curl_easy_setopt(cur_url, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(cur_url, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数
    curl_easy_setopt(cur_url, CURLOPT_POSTFIELDS, data.c_str());

    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(cur_url, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(cur_url, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(cur_url, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(cur_url, CURLOPT_URL, url.c_str() ); //需要获取的URL地址

    curl_slist *list = NULL;
    list = curl_slist_append(list,"Accept-Encoding:gzip, deflate, sdch"); 
    list = curl_slist_append(list,"Connection:keep-alive");
    list = curl_slist_append(list,"Content-Type:application/json");
    list = curl_slist_append(list, HTTP_HEAD_AUTH);
    curl_easy_setopt(cur_url, CURLOPT_HTTPHEADER, list); 

    res = curl_easy_perform(cur_url);  //执行请求

    long res_code=0;
    res=curl_easy_getinfo(cur_url, CURLINFO_RESPONSE_CODE, &res_code);

    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201))
    {
        respone = data_trunk.memory;
        ret = 0;
    }
    
    curl_slist_free_all(list); 
    curl_easy_cleanup(cur_url);
    curl_global_cleanup();
    return ret;
}

int AwsInsertMessage(std::vector<SOCKET_MSG_SEND_TEXT_MESSAGE>& text_messages)
{
    if (0 == text_messages.size())
    {
        return 0;
    }
    int msg_id = 0;
    int status = -1;
    std::string message;
    std::string respone;
    std::string data;
    char url[64] = {0};
    snprintf(url, sizeof(url), HTTP_URL_AWS_MESSAGE, gstAKCSConf.apiurl);

    Json::Value item;
    Json::Value item_data;
    Json::Value client_obj;
    Json::FastWriter w;
    item["title"] = text_messages[0].text_message.title;
    item["content"] = text_messages[0].text_message.content;
    item["type"] = text_messages[0].text_message.type;  
    item["per_manager_id"] = text_messages[0].per_manager_id;
    for (const auto& msg : text_messages)
    {
        item_data["account"] = msg.account;
        item_data["client_type"] = msg.client_type;
        client_obj.append(item_data);
    }
    item["client"] = client_obj;
    data = w.write(item);

    for(int cnt =0; cnt < 3; cnt++) //最多尝试三次
    {
        if(0 == HttpPostRequest(url, data, respone))
        {
            break;
        }
    }

    Json::Reader reader;
    Json::Value root;  
    if (!reader.parse(respone, root))
    {
        AK_LOG_WARN << "respone error,data:" << respone;
        return 0;
    }    
    status = root["result"].asInt();
    message = root["message"].asString();
    if(0 == status)
    {
        msg_id = root["msg_id"].asInt();
        for (auto& text_send : text_messages)
        {
            text_send.text_message.id = msg_id;
        }
    }
    else
    {     
        AK_LOG_WARN << "respone error,message:" << message;
    }
    return msg_id;
}


int AwsInsertAlarm(ALARM* alarm)
{
    if (nullptr == alarm)
    {
        return 0;
    }
    int status = -1;
    std::string message;
    std::string respone;
    std::string data;
    char url[64] = {0};
    snprintf(url, sizeof(url), HTTP_URL_AWS_ALARM, gstAKCSConf.apiurl);

    Json::Value item;
    Json::Value item_data;
    Json::Value client_obj;
    Json::FastWriter w;
    item["alarm_type"] = alarm->alarm_type;
    item["manager_account_id"] = alarm->manager_account_id;
    item["unit_id"] = alarm->unit_id;  
    item["devices_mac"] = alarm->mac;
    item["node"] = alarm->device_node;
    item["status"] = alarm->status;  
    item["alarm_code"] = alarm->alarm_code;
    item["alarm_location"] = alarm->alarm_location;
    item["alarm_zone"] = alarm->alarm_zone;
    item["alarm_customize"] = alarm->alarm_customize;
    data = w.write(item);

    for(int cnt =0; cnt < 3; cnt++) //最多尝试三次
    {
        if(0 == HttpPostRequest(url, data, respone))
        {
            break;
        }
    }

    Json::Reader reader;
    Json::Value root;  
    if (!reader.parse(respone, root))
    {
        AK_LOG_WARN << "respone error,data:" << respone;
        return 0;
    }    
    status = root["result"].asInt();
    message = root["message"].asString();
    if(0 == status)
    {
        alarm->id = root["alarm_id"].asInt();
    }
    else
    {     
        AK_LOG_WARN << "respone error,message:" << message;
    }
    return alarm->id;
}


int AwsInsertPersonalAlarm(PERSONNAL_ALARM& alarm)
{
    int status = -1;
    std::string message;
    std::string respone;
    std::string data;
    char url[64] = {0};
    snprintf(url, sizeof(url), HTTP_URL_AWS_PER_ALARM, gstAKCSConf.apiurl);

    Json::Value item;
    Json::Value item_data;
    Json::Value client_obj;
    Json::FastWriter w;
    item["alarm_type"] = alarm.alarm_type;
    item["community"] = alarm.community;
    item["node"] = alarm.device_node;  
    item["extension"] = alarm.extension;
    item["status"] = alarm.status;  
    item["devices_mac"] = alarm.mac;
    item["alarm_code"] = alarm.alarm_code;
    item["alarm_location"] = alarm.alarm_location;
    item["alarm_zone"] = alarm.alarm_zone;
    item["alarm_customize"] = alarm.alarm_customize;
    data = w.write(item);

    for(int cnt =0; cnt < 3; cnt++) //最多尝试三次
    {
        if(0 == HttpPostRequest(url, data, respone))
        {
            break;
        }
    }

    Json::Reader reader;
    Json::Value root;  
    if (!reader.parse(respone, root))
    {
        AK_LOG_WARN << "respone error,data:" << respone;
        return 0;
    }    
    status = root["result"].asInt();
    message = root["message"].asString();
    if(0 == status)
    {
        alarm.id = root["alarm_id"].asInt();
    }
    else
    {     
        AK_LOG_WARN << "respone error,message:" << message;
    }
    return alarm.id;
}


#ifdef __cplusplus
}
#endif

