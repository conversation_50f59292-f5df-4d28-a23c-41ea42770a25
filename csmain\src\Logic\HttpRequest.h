#ifndef HTTP_REQUEST_H_
#define HTTP_REQUEST_H_
//AWS接口(同步请求)临时使用，后面移除掉
#include "SDMCMsg.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/PersonalAlarm.h"


#ifdef __cplusplus
extern "C" {
#endif

int AwsInsertMessage(std::vector<SOCKET_MSG_SEND_TEXT_MESSAGE>& text_messages);
int AwsInsertAlarm(ALARM* alarm);
int AwsInsertPersonalAlarm(PERSONNAL_ALARM& alarm);

#ifdef __cplusplus
}
#endif


#endif

