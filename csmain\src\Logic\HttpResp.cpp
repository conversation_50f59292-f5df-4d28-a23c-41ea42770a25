#include "stdafx.h"
#include <sstream>
#include "DeviceSetting.h"
#include "HttpResp.h"
#include "util.h"
#include "csmainserver.h"
#include "DeviceControl.h"

extern AccessServer* g_accSer_ptr;

namespace operation_http
{
static const std::string V31 = "3.1";

HTTPRespCallback ReqEchoHandlerV31 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    cb(ctx->body().ToString());
};

//TcpCli
operation_http::HTTPRespCallback ReqTcpCliHandlerV31 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    //int num = GetDeviceControlInstance()->GetTcpCliNum();
    std::size_t num = g_accSer_ptr->GetTcpCliNum();
    std::stringstream oss;
    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << TCP_CLIENT_NUM_SUCCESS << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << TCP_CLIEN << ": " << num << "\n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
};

//MsgQueue
operation_http::HTTPRespCallback ReqMsgQueueHandlerV31 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int num = g_accSer_ptr->GetTcpCliNum();
    std::stringstream oss;
    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << MSG_QUEUU_LENGTH_SUCCESS << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << MSG_LENGTH << ": " << num << "\n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
};

operation_http::HTTPRespVerCallbackMap HTTPEchoMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V31] = ReqEchoHandlerV31;
    return OMap;
}

operation_http::HTTPRespVerCallbackMap HTTPTcpCliMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V31] = ReqTcpCliHandlerV31;
    return OMap;
}

operation_http::HTTPRespVerCallbackMap HTTPMsgQueueMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V31] = ReqMsgQueueHandlerV31;
    return OMap;
}

operation_http::HTTPAllRespCallbackMap HTTPAllRespMapInit()
{
    operation_http::HTTPAllRespCallbackMap OMap;
    OMap[operation_http::ECHO] = HTTPEchoMap();
    OMap[operation_http::TCP_CLI] = HTTPTcpCliMap();//获取tcp cli连接数量的接口
    OMap[operation_http::MSG_QUEUE] = HTTPMsgQueueMap();//获取业务消息未消费队列长度的接口
    return OMap;
}
}
