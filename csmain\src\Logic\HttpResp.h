#ifndef __CSMAIN_HTTP_RESP_H__
#define __CSMAIN_HTTP_RESP_H__
#include <functional>
#include <evpp/http/context.h>

#define USER_NOT_EXIT            "\"user name does not exist\""
#define PASSWD_INVALID           "\"passwd is invalid\""
#define TOKEN_INVALID            "\"token is invalid\""
#define RESULT                   "\"result\""
#define MESSAGE                  "\"message\""
#define DATAS                    "\"datas\""
#define ACCESS_SERVER            "\"access_server\""
#define REST_SERVER              "\"rest_server\""
#define LOGIN_SUCCESS            "\"login success\""
#define API_VERSION              "api-version"
#define TOKEN                    "\"token\""
#define TCP_CLIENT_NUM_SUCCESS   "\"get tcp client num success\""
#define TCP_CLIEN                "\"cli\""

#define MSG_QUEUU_LENGTH_SUCCESS "\"get msg queue length success\""
#define MSG_LENGTH                "\"length\""

#define CPU_LAOD_AVERAGE_SUCCESS "\"get cpu load average success\""
#define LOAD_AVE                 "\"load\""

#define LIMIT_SWITCH_TAG           "\"limit_switch\""
#define RATE_TAG                   "\"rate\""

namespace operation_http
{

//http路由
enum HTTP_ROUTE
{
    ECHO = 0,
    TCP_CLI,
    MSG_QUEUE,
};

typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
typedef std::map<int, HTTPRespVerCallbackMap> HTTPAllRespCallbackMap;
HTTPAllRespCallbackMap HTTPAllRespMapInit();

}
#endif //__CSMAIN_HTTP_RESP_H__