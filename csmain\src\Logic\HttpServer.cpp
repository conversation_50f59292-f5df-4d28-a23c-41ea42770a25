#include <stdio.h>
#include <stdlib.h>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include <evpp/tcp_client.h>
#include "HttpServer.h"
#include "HttpResp.h"
#include "stdafx.h"
#include "csmainserver.h"
#include "DeviceControl.h"
#include "Device.h"
#include "DeviceSetting.h"
#include "json/json.h"
#include "Utility.h"
#include "Dao.h"
#include "util.h"
#include "ConfigFileReader.h"
#include "AKUpgradeMonitor.h"
#include "AkcsMsgDef.h"
#include "NotifyMsgControl.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket_interface.h"
#include "MsgControl.h"
#include "ConfigFileReader.h"
#include "PushClient.h"
#include "RouteClient.h"
#include "PushClientMng.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/MacPool.h"
#include "redis/CachePool.h"
#include "EtcdCliMng.h"
#include "MsgRateLimiterConf.h"
#include "AkcsServer.h"
#include "AkcsRequestRecorder.h"
#include "MsgIdToMsgName.h"
#include "MetricService.h"

using namespace Akcs;
extern AKCS_CONF gstAKCSConf;
extern AccessServer* g_accSer_ptr;
extern CAkEtcdCliManager* g_etcd_cli_mng ;
extern MessageRateLimitMap gstAKCSMsgRateLimitConf;

extern evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter;

//全局变量
static operation_http::HTTPAllRespCallbackMap httpRespCbs;

const static char MAC_STATUS[] = "\"get dev online status success\"";
const static char MAC_ACCESS[] = "\"get dev access server success\"";

const static int HTTP_RET_RESULT_SUCCESS_CODE = 0;
const static int HTTP_RET_RESULT_FAILE_CODE = -1;
const static int HTTP_RET_RESULT_MAC_NOT_EXIST_CODE = -2;
const static int HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE = -3;

const static char HTTP_RET_MSG_MAC_NOT_EXIST[] = "\"mac is not exist\"";
const static char HTTP_RET_MSG_MAC_NOT_CONNECT[] = "\"mac is not connect\"";
const static char HTTP_RET_MSG_PARAM_FAILE[] = "\"param error\"";
const static char HTTP_RET_MSG_SUCCESS[] = "\"success\"";
const static char HTTP_RET_MSG_NOT_SUPPORT[] = "\"not support\"";

const static char BEGIN_VIDEO_STORAGE[] = "\"start video storage\"";
const static char STOP_VIDEO_STORAGE[] = "\"stop video storage\"";
const static char HTTP_RET_MSG_OPS_FAILE[] = "\"operater failed\"";

std::string getReqResponData(int result, const char* msg)
{
    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": " << result << "," << "\n"
        << MESSAGE << ": " << msg << ",\n"
        << DATAS << ":[]" << "\n"
        << "}" << "\n";
    return oss.str();
}

std::string getReqResponDataWithCount(int result, const char* msg, int count)
{
    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": \"" << result << "\"," << "\n"
        << MESSAGE << ": " << msg << ",\n"
        << DATAS << ":{\"count\":\"" << count << "\"}"  << "\n"
        << "}" << "\n";
    return oss.str();
}

std::string getReqResponUpgradeData(int result, const char* msg, int upgrade_status)
{
    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": " << result << "," << "\n"
        << MESSAGE << ": " << msg << ",\n"
        << DATAS << ":{\"upgrade_status\":\"" << upgrade_status << "\"}"  << "\n"
        << "}" << "\n";
    return oss.str();
}

std::string getReqResponData(int result, const std::string& msg, const Json::Value& datas)
{
    Json::Value item;
    Json::FastWriter writer;

    item["result"] = result;
    item["message"] = msg;
    item["datas"] = datas;

    std::string msg_json = writer.write(item);
    return msg_json;
}


//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}

void HttpTcpCliCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->body().ToString() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //AK_LOG_WARN << "http req head 'api-version' is null";
        // cb("http req head 'api-version' is null");
        // return;
        head = "3.1";
    }
    auto httpRespTcpCliMap = httpRespCbs[operation_http::TCP_CLI];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespTcpCliMap.find(head);
    if (it ==  httpRespTcpCliMap.end())
    {
        AK_LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

void HttpReqMsgLenCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->body().ToString() << " ]";

    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //AK_LOG_WARN << "http req head 'api-version' is null";
        //cb("http req head 'api-version' is null");
        //return;
        head = "3.1";
    }

    auto httpRespMsgLenMap = httpRespCbs[operation_http::MSG_QUEUE];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMsgLenMap.find(head);
    if (it == httpRespMsgLenMap.end())
    {
        AK_LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}


//设备在线状态 /dev_status?mac=xxxxxxx
void HttpReqDevStatusCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string mac = ctx->GetQuery("mac");
    if (mac.length() == 0)
    {
        AK_LOG_WARN << "http req dev_status fialed, mac is null";
        cb("http req dev_status fialed, mac is null");
        return;
    }
    int online_status = -1;
    if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
    {
        online_status = 0;
    }
    else
    {
        online_status = 1;
    }

    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << MAC_STATUS << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << "\"status\"" << ": " << online_status << " \n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
}

//设备接入服务器 /dev_accsrv?mac=xxxxxxx
void HttpReqDevAccsrvCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string mac = ctx->GetQuery("mac");
    if (mac.length() == 0)
    {
        AK_LOG_WARN << "http req dev_accsrv fialed, mac is null";
        cb("http req dev_accsrv fialed, mac is null");
        return;
    }
    if (!HttpCheckSqlParam(mac))
    {
        AK_LOG_WARN << "http req dev_accsrv fialed, mac is error";
        cb("http req dev_accsrv fialed, mac is error");
        return;
    }

    ResidentDev dev;
    if(0 != GetDeviceSettingInstance()->GetDevInfoByMac(mac, dev))
    {
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
        return;
    }

    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << MAC_ACCESS << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << "\"server_ip\"" << ": \"" << dev.acc_srv_id << "\", \n"
        << "\"dcli_ver\"" << ": " << dev.dclient_ver << ", \n" 
        << "\"firmware\"" << ": \"" << dev.sw_ver << "\", \n"
        << "\"dev_outer_ip\"" << ": \"" << dev.outer_ip << "\" \n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
}

void HttpReqDevFirmwareCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string firmware = ctx->GetQuery("firmware");
    if (firmware.length() == 0)
    {
        AK_LOG_WARN << "http req dev_firmware fialed, firmware is null";
        cb("http req dev_firmware fialed, firmware is null");
        return;
    }
    if (!HttpCheckSqlParam(firmware))
    {
        AK_LOG_WARN << "http req dev_firmware fialed, firmware is error";
        cb("http req dev_firmware fialed, firmware is error");
        return;
    }

    std::vector<std::string> macs;
    if(0 != dbinterface::ResidentDevices::GetMacByFirmware(firmware, macs))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
        return;
    }
    
    Json::Value item_data;
    Json::Value array_obj;
    std::string mac;
    for(const auto& tmpmac : macs)
    {
        array_obj.append(tmpmac);
        mac = tmpmac;
    }
    item_data["mac"] = mac;
    item_data["macs"] = array_obj;
    
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, "success", item_data));
    return;
}


//获取连接信息 /getConnectInfo
void HttpReqConnectedCountCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->body().ToString() << " ]";
    int device_count = 0;
    int app_count = 0;
    int all_device = 0;
    g_accSer_ptr->getDevAppOnlineCount(device_count, app_count);
    all_device = dbinterface::ResidentDevices::GetAllDeviceSettingCount();

    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": \"0\"," << "\n"
        << MESSAGE << ": " << "\"success\"" << ",\n"
        << DATAS << ": " << "\n"
        << "[{" <<  "\n"
        << "\"dev\"" << ":"  << device_count << ", \n"
        << "\"app\"" << ":"  << app_count << ", \n"
        << "\"alldev\"" << ":"  << all_device << " \n"
        << "}]" << "\n"
        << "}" << "\n";
    cb(oss.str());

    return;
}

//curl -H "Content-Type:application/json" -X POST --data '{"xxx":"xxx","sss":"xxxx"}' http://localhost:9998/startPcap
void HttpReqDevStartPcapCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    HTTP_MSG_DEV_GET_FILE_COMMON dev_file;
    memset(&dev_file, 0, sizeof(dev_file));

    ::snprintf(dev_file.mac, sizeof(dev_file.mac), "%s", root["mac"].asString().c_str());
    dev_file.druation = root["duration"].asInt();

    if (g_accSer_ptr->IsDevOnline(dev_file.mac) != 0) //不在线
    {
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    std::string ret;
    std::stringstream message;
    message << "pcapstart " << dev_file.druation;
    if (g_accSer_ptr->SendCommandToDevice(dev_file.mac, message.str(), ret) != 0)
    {
        AK_LOG_WARN << "CLI: error " << ret;
    }

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqDevGetFileCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    HTTP_MSG_DEV_GET_FILE_COMMON dev_file;
    memset(&dev_file, 0, sizeof(dev_file));

    ::snprintf(dev_file.mac, sizeof(dev_file.mac), "%s", root["mac"].asString().c_str());
    ::snprintf(dev_file.username, sizeof(dev_file.username), "%s", root["user"].asString().c_str());
    ::snprintf(dev_file.password, sizeof(dev_file.password), "%s", root["passwd"].asString().c_str());
    ::snprintf(dev_file.server_url, sizeof(dev_file.server_url), "%s:%s", root["ip"].asString().c_str(),root["port"].asString().c_str());
    ::snprintf(dev_file.file_name, sizeof(dev_file.file_name), "%s", root["file_name"].asString().c_str());
    if(root["location"].isString()) //可能为空
    {
        ::snprintf(dev_file.location, sizeof(dev_file.location), "%s", root["location"].asString().c_str());
    }
    int file_type = root["file_type"].asInt(); 

    if (g_accSer_ptr->IsDevOnline(dev_file.mac) != 0) //不在线
    {
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    int result = GetDeviceControlInstance()->OnOldDevMaintenanceReq(dev_file, file_type);
    if(-1 == result)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_NOT_SUPPORT));
        return;
    }
    else if(1 == result)    //新版本运维指令
    {
        std::string ret;
        std::stringstream message;

        switch (file_type)
        {
            case csmain::DEV_PCAP:
                message << "pcapstopEx -P -C -s "
                        << dev_file.server_url
                        << " -r "
                        << dev_file.file_name
                        << " -u "
                        << dev_file.username
                        << " -p "
                        << dev_file.password;
                break;
            case csmain::DEV_LOG:
                message << "putfileEx -g -C -s " 
                        << dev_file.server_url
                        << " -r "
                        << dev_file.file_name
                        << " -u "
                        << dev_file.username
                        << " -p "
                        << dev_file.password;
                break;
            case csmain::DEV_AUTOP:
                message << "putfileEx -a -C -s " 
                        << dev_file.server_url
                        << " -r "
                        << dev_file.file_name
                        << " -u "
                        << dev_file.username
                        << " -p "
                        << dev_file.password;
                break;
            case csmain::DEV_ANY:
                message << "putfileEx -C -l " 
                        << dev_file.location
                        << " -s "
                        << dev_file.server_url
                        << " -r "
                        << dev_file.file_name
                        << " -u "
                        << dev_file.username
                        << " -p "
                        << dev_file.password;
                break;

            default:
                {
                    cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, "file_type error"));
                    return;
                }
        }
        
        if (g_accSer_ptr->SendCommandToDevice(dev_file.mac, message.str(), ret) != 0)
        {
            AK_LOG_WARN << "CLI: error " << ret;
            cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, ret.c_str()));
            return;
        }
    }
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqDevAlarmSwitchCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    char mac[32];
    ::snprintf(mac, sizeof(mac), "%s", root["mac"].asString().c_str());
    int alarm_switch;
    
    alarm_switch = root["switch"].asInt();  

    if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
    {
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    std::string ret;
    std::stringstream message;
    message << "setcfg Config.DoorSetting.CLOUDSERVER.MaintenanceAlarm="
            << alarm_switch;
    if (g_accSer_ptr->SendCommandToDevice(mac, message.str(), ret) != 0)
    {
        AK_LOG_WARN << "CLI: error " << ret;
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, ret.c_str()));
        return;
    }

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}


void HttpReqDevSetMonitorCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    
    Json::Value item;
    Json::FastWriter data_writer;
    std::string mac = root["mac"].asString();
    item["switch"] = root["switch"].asInt();
    item["ftp_ip"] = root["ip"].asString();
    item["ftp_port"] = root["port"].asInt();
    item["user"] = root["user"].asString();
    item["passwd"] = root["passwd"].asString();
    item["actions"] = root["actions"].asInt();
    item["days"] = root["days"].asInt();
    item["uuid"] = root["uuid"].asString();

    std::string message = "Config.DoorSetting.SECURITY.ActionMonitor=" + data_writer.write(item);
    AK_LOG_INFO << "data_json is [ " << message << " ]";

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    SOCKET_MSG_CONFIG socket_msg_config;
    memset(&socket_msg_config, 0, sizeof(socket_msg_config));
    Snprintf(socket_msg_config.mac, sizeof(socket_msg_config.mac), mac.c_str());
    Snprintf(socket_msg_config.protocal, sizeof(socket_msg_config.protocal), "1.0");
    ParseConfigItem(message, socket_msg_config);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->BuildUpdateConfigMsg(&socket_msg, &socket_msg_config) < 0)
    {
        AK_LOG_WARN << "BuildUpdateConfigMsg failed.";
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqDevExistenceCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    std::string dev_list;
    std::string mac_list;

    if (root.isMember("macs")) {
        mac_list = root["macs"].asString();
    } else {
        AK_LOG_WARN << "mac list does not exist";
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if(!dbinterface::MacPool::FindExistDevByMac(mac_list, dev_list))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, dev_list.c_str()));
    return;
}

void HttpReqDevDelAuthCodeCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    Json::Reader reader;
    Json::Value root;

    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    std::string mac_list;

    if (root.isMember("macs")) {
        mac_list = root["macs"].asString();
    } else {
        AK_LOG_WARN << "mac list does not exist";
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if(!dbinterface::MacPool::DelAuthcodeByMac(mac_list))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqDevReportStatusCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string mac = ctx->GetQuery("mac");
    if (mac.length() == 0)
    {
        AK_LOG_WARN << "http req requestDevReportStatus fialed, mac is null";
        cb("http req requestDevReportStatus fialed, mac is null");
        return;
    }
    
    if(0 != GetDeviceControlInstance()->SendRequestStatus(mac))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}


//curl -H "Content-Type:application/json" -X POST --data '{"mac":"xxx","serveraddr":"xxxx"}' http://localhost:9998/reconnectRps
void HttpReqDevReconnectRpsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    HTTP_MSG_DEV_RECONNECT_COMMON stReconnectd;
    memset(&stReconnectd, 0, sizeof(stReconnectd));

    ::snprintf(stReconnectd.mac, sizeof(stReconnectd.mac), "%s", root["mac"].asCString());
    ::snprintf(stReconnectd.server_addr, sizeof(stReconnectd.server_addr), "%s", root["serveraddr"].asCString());

    if (strlen(stReconnectd.mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }


    if (0 == strcasecmp(stReconnectd.mac, "all"))
    {
        stReconnectd.mac[0] = 0;
    }
    if (strlen(stReconnectd.mac) != 0)
    {
        if (g_accSer_ptr->IsDevOnline(stReconnectd.mac) != 0) //不在线
        {
            if (dbinterface::ResidentDevices::IsMacDeviceExist(stReconnectd.mac))
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
                return;
            }
            else
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
                return;
            }
        }
    }

    g_accSer_ptr->SendDevReconnectRps(&stReconnectd);

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}


//curl -H "Content-Type:application/json" -X POST --data '{"mac":"xxx","serveraddr":"xxxx:9999"}' http://localhost:9998/reconnectGateway
void HttpReqDevReconnectGateWayCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    HTTP_MSG_DEV_RECONNECT_COMMON stReconnectd;
    memset(&stReconnectd, 0, sizeof(stReconnectd));

    ::snprintf(stReconnectd.mac, sizeof(stReconnectd.mac), "%s", root["mac"].asCString());
    ::snprintf(stReconnectd.server_addr, sizeof(stReconnectd.server_addr), "%s", root["serveraddr"].asCString());

    if (strlen(stReconnectd.mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }


    if (0 == strcasecmp(stReconnectd.mac, "all"))
    {
        stReconnectd.mac[0] = 0;
    }

    if (strlen(stReconnectd.mac) != 0)
    {
        if (g_accSer_ptr->IsDevOnline(stReconnectd.mac) != 0) //不在线
        {
            if (dbinterface::ResidentDevices::IsMacDeviceExist(stReconnectd.mac))
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
                return;
            }
            else
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
                return;
            }
        }
    }

    g_accSer_ptr->SendDevReconnectGateWay(&stReconnectd);

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));

    return;
}

//curl -H "Content-Type:application/json" -X POST --data '{"mac":"xxx","serveraddr":"xxxx:8051"}' http://localhost:9998/reconnectAccessServer
void HttpReqDevReconnectAccessServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    HTTP_MSG_DEV_RECONNECT_COMMON stReconnectd;
    memset(&stReconnectd, 0, sizeof(stReconnectd));

    ::snprintf(stReconnectd.mac, sizeof(stReconnectd.mac), "%s", root["mac"].asCString());
    ::snprintf(stReconnectd.server_addr, sizeof(stReconnectd.server_addr), "%s", root["serveraddr"].asCString());

    if (strlen(stReconnectd.mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (0 == strcasecmp(stReconnectd.mac, "all"))
    {
        stReconnectd.mac[0] = 0;
    }

    if (strlen(stReconnectd.mac) != 0)
    {
        if (g_accSer_ptr->IsDevOnline(stReconnectd.mac) != 0) //不在线
        {
            if (dbinterface::ResidentDevices::IsMacDeviceExist(stReconnectd.mac))
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
                return;
            }
            else
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
                return;
            }
        }
    }

    g_accSer_ptr->SendDevReconnectAccessServer(&stReconnectd);

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));

    return;
}


// /rebootDev {"mac":"all"}
void HttpReqDevRebootCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    char mac[32] = {0};
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    ::snprintf(mac, sizeof(mac), "%s", root["mac"].asCString());

    if (strlen(mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }


    if (0 == strcasecmp(mac, "all"))
    {
        mac[0] = 0;
    }

    if (strlen(mac) != 0)
    {
        if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
        {
            if (dbinterface::ResidentDevices::IsMacDeviceExist(mac))
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
                return;
            }
            else
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
                return;
            }
        }
    }

    GetDeviceControlInstance()->SendRequestReboot(mac);

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));

    return;
}

void HttpReqCloseDevTcpCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    char mac[32] = {0};
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    ::snprintf(mac, sizeof(mac), "%s", root["mac"].asCString());

    if (strlen(mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (strlen(mac) != 0)
    {
        if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
        {
            if (dbinterface::ResidentDevices::IsMacDeviceExist(mac))
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
                return;
            }
            else
            {
                cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
                return;
            }
        }
    }

    GetDeviceControlInstance()->SendRequestCloseTcp(mac);
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

//开启tcp发送log /setsendlog?enable=1/0
void HttpReqSetSendLogCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string enable = ctx->GetQuery("enable");
    if (enable.length() == 0)
    {
        AK_LOG_WARN << "http req error";
        cb("http req setsendlog fialed, enable is null");
        return;
    }
    if (enable == "1")
    {
        g_accSer_ptr->enable_send_log = 1;
    }
    else if (enable == "0")
    {
        g_accSer_ptr->enable_send_log = 0;
    }

    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": 1," << "\n"
        << MESSAGE << ": " << "\"success\"" << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << "\"enable\""  << ": \"" << g_accSer_ptr->enable_send_log << "\" \n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
}

void HttpReqSetLimitingTimeoutCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string timeout = ctx->GetQuery("timeout");
    if (timeout.length() == 0)
    {
        cb("http req set limiting timeout fialed, timeout is null");
        return;
    }

    gstAKCSConf.limiting_timeout = ATOI(timeout.c_str());

    std::stringstream oss;
    oss << "set limiting timeout ok. timeout:" << gstAKCSConf.limiting_timeout;
    cb(oss.str());
    return;
}


//重新写联动配置文件  /UpdateConfig {mac:""}如果mac有联动就更新联动，如果mac没有联动，就更新自己
void HttpReqUpdateConfigCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    char mac[32] = {0};
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    ::snprintf(mac, sizeof(mac), "%s", root["mac"].asCString());

    if (strlen(mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_MAINTANCE, mac);

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));

    return;
}


void HttpReqMacKensendCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    char mac[512] = {0};
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    ::snprintf(mac, sizeof(mac), "%s", root["mac"].asString().c_str());

    if (strlen(mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    
    std::vector<std::string> mac_vec;
    std::string macs = mac;
    SplitString(macs, ";", mac_vec);

    for(auto& mac : mac_vec)
    {
        if (mac.size() != 0)
        {
            if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
            {
                if (dbinterface::ResidentDevices::IsMacDeviceExist(mac.c_str()))
                {
                    continue;
                }
                else
                {
                    cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
                    return;
                }
            }            
            GetDeviceControlInstance()->SendMacKeysend(mac.c_str());
        }
    }
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

/*
mode的值为 ARMED_AWAY,(AwayMode) 3
 ARMED_STAY,(HomeMode) 1
 ARMED_NIGHT,(SleepMode) 2
 DISARMED(Arming off) 0
*/
// /setArmStatu {"mac":"xxxxxx", "mode":2}
void HttpReqSetArmStatuCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    Json::Reader reader;
    Json::Value root;
    char mac[32] = {0};
    int mode = -1;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    ::snprintf(mac, sizeof(mac), "%s", root["mac"].asCString());
    mode = root["mode"].asInt();

    if (strlen(mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
    {
        if (dbinterface::ResidentDevices::IsMacDeviceExist(mac))
        {
            cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
            return;
        }
        else
        {
            cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
            return;
        }
    }

    g_accSer_ptr->setDevArmStatuReq(mac, mode);

    std::stringstream oss;
    oss << "{" << "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << "\"success\"" << ",\n"
        << DATAS << ": [" << "\n"
        << "{" <<  "\n"
        << "\"mode\""  << ": " << mode << "\n"
        << "}" << "\n"
        << "]}" << "\n";

    cb(oss.str());


    return;
}

void isAlexaArmingOk()
{

}
// /getArmStatu?mac=vvvvvvv
void HttpReqGetArmStatuCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    Json::Reader reader;
    Json::Value root;
    char mac[32] = {0};
    std::string stmac = ctx->GetQuery("mac");
    ::snprintf(mac, sizeof(mac), "%s", stmac.c_str());

    if (strlen(mac) <= 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
    {
        if (dbinterface::ResidentDevices::IsMacDeviceExist(mac))
        {
            cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
            return;
        }
        else
        {
            cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
            return;
        }
    }

    int mode = GetDeviceSettingInstance()->GetDeviceArmingStatus(mac);

    std::stringstream oss;
    oss << "{" << "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << "\"success\"" << ",\n"
        << DATAS << ": [" << "\n"
        << "{" <<  "\n"
        << "\"mode\""  << ": " << mode << "\n"
        << "}" << "\n"
        << "]}" << "\n";

    cb(oss.str());

    return;
}

// /notifyDevUpdateServer?type=ftp&mac=xxxxxxxxxx; type目前支持:ftp/pbx/rtsp/access/web
void HttpReqNotifyDevUpdateServer(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string mac = ctx->GetQuery("mac");
    std::string type = ctx->GetQuery("type");
    
    if (mac.length() == 0 || type.length() == 0)
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (g_accSer_ptr->IsDevOnline(mac) != 0) //不在线
    {
        if (dbinterface::ResidentDevices::IsMacDeviceExist(mac.c_str()))
        {
            cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
            return;
        }
        else
        {
            cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_EXIST_CODE, HTTP_RET_MSG_MAC_NOT_EXIST));
            return;
        }
    }

    g_accSer_ptr->NotifyDevUpdateServer(mac, type);

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));


    return;
}


// /HttpReqNotifyAllDevsUpdateServer?type=ftp; type:ftp/pbx/rtsp/access/web
void HttpReqNotifyAllDevsUpdateServer(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string type = ctx->GetQuery("type");
    g_accSer_ptr->NotifyAllDevsUpdateServer(type);
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqChangeUpgradeStatus(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    gstAKCSConf.upgrade_status = fileExist("/tmp/system.upgrade") ? 1 : 0;

    if (AKCS::Singleton<UpgradeMonitor>::instance().ChangeUpgradeStatus(gstAKCSConf.upgrade_status) != 0)
    {
        cb(getReqResponUpgradeData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_OPS_FAILE, gstAKCSConf.upgrade_status));
        return;
    }
    cb(getReqResponUpgradeData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS, gstAKCSConf.upgrade_status));
    return;
}

void HttpReqSetTzMdCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string tz_md5 = ctx->GetQuery("tz_md5");
    std::string tz_data_md5 = ctx->GetQuery("tz_data_md5");
    ///改写配置文件
    CConfigFileReader config_file("/usr/local/akcs/csmain/conf/csmain.conf");
    if(tz_md5.size()>0)
    {
        config_file.SetConfigValue("tz_md5", tz_md5.c_str());
        Snprintf(gstAKCSConf.tz_md5, sizeof(gstAKCSConf.tz_md5), tz_md5.c_str());
        LOG_INFO << "change config value tz_md5= " << tz_md5;
    }
    if(tz_data_md5.size()>0)
    {
        config_file.SetConfigValue("tz_data_md5", tz_data_md5.c_str());
        Snprintf(gstAKCSConf.tz_data_md5, sizeof(gstAKCSConf.tz_data_md5), tz_data_md5.c_str());
        LOG_INFO << "change config value tz_data_md5= " << tz_data_md5;
    }
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}


void HttpReqSetOfflineNotifyCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string offline_notify = ctx->GetQuery("offline_notify");
    if(offline_notify.size() > 0)
    {
        gstAKCSConf.offline_notify = ATOI(offline_notify.c_str());
    }
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqNotifyAllDevsReboot(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
	 g_accSer_ptr->NotifyAllDevsReboot();
    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpReqSvnVersionCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string svn_version;
    svn_version = gstAKCSConf.svn_version;
    svn_version += "\n";
    cb(svn_version);
    return;
}

void HttpReqUpdateLimitRateCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string limit_switch = ctx->GetQuery("limit_switch");
    std::string rate = ctx->GetQuery("rate");
    AK_LOG_INFO << "limit_switch=" << limit_switch << ";rate=" << rate;

    int limit_switch_flag = std::stoi(limit_switch);
    if (limit_switch_flag == evpp::rate_limiter::NO_LIMIT || limit_switch_flag == evpp::rate_limiter::LIMIT_FLOW)
    {
        gstAKCSConf.limit_switch = limit_switch_flag;
        double rate_value = std::stod(rate);
        if (rate_value > 0)
        {
            gstAKCSConf.rate = rate_value;
            g_rate_limiter->SetRate(gstAKCSConf.rate);
        }
    }

    std::stringstream oss;
    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << "\"update success\"" << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << LIMIT_SWITCH_TAG << ": " << "\"" << gstAKCSConf.limit_switch << "\",\n"
        << RATE_TAG << ": " << "\"" << gstAKCSConf.rate << "\"\n"
        << "}" << "\n"
        << "}" << "\n";

    cb(oss.str());
    return;
};


void HttpTestReqUserInfoCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    if(gstAKCSConf.stress_test != 1)
    {
        AK_LOG_WARN << "The stress test switch is not open";
        return;
    }
    std::string mac = ctx->GetQuery("mac");
    std::string users_list = ctx->GetQuery("users_list");
    AK_LOG_INFO << "HttpTestReqUserInfoCallback, mac=" << mac << "; users_list=" << users_list;

    //触发内部逻辑:
    GetMsgControlInstance()->OnTestReqUserInfo(mac, users_list);
    std::stringstream oss;
    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << "\"trigger stress test success\"" << ",\n"
        << "}" << "\n";

    cb(oss.str());
    return;
};

//针对运行中需要调整的参数,提供reloadconf的接口,注意:为了安全起见，不是所有的参数都会reload的，有需要的参数自行添加
void HttpReloadConfCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    CConfigFileReader config_file("/usr/local/akcs/csmain/conf/csmain.conf");
    const uint32_t kDefaultRateLimitQps = 200;
    const char *limit_switch = config_file.GetConfigName("limit_switch");
    if (strlen(limit_switch) > 0)
    {
        gstAKCSConf.limit_switch = ATOI(limit_switch);
    }
    else
    {
        gstAKCSConf.limit_switch = evpp::rate_limiter::NO_LIMIT;
    }

    const char *rate = config_file.GetConfigName("rate");
    if (strlen(rate) > 0)
    {
        gstAKCSConf.rate = (ATOI(rate) <= 0) ? kDefaultRateLimitQps : ATOI(rate);
    }
    else
    {
        gstAKCSConf.rate = kDefaultRateLimitQps;
    }

}



// 测试离线推送服务是否正常 /triggerCspushOffline?msg=xxxx
void HttpReqTriggerPushOfflineCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string msg = ctx->GetQuery("msg");
    if (msg.length() == 0)
    {
        AK_LOG_WARN << "http req error";
        cb("http req fialed, msg is null");
        return;
    }

    AppOfflinePushKV kv;
    kv.insert(map<std::string, std::string>::value_type("msg", msg));

    PushClientMap push_client_map = CPushClientMng::Instance()->GetAllPushSrv();
    for (const auto& push_client : push_client_map)
    {
        if (push_client.second)
        {
            push_client.second->buildPushMsg(csmain::APP_IOS, "TRIGGER_CSPUSH_TEST", csmain::PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST, kv, gstAKCSConf.oem_name);
        }
    }

    // incloud aliyun推送
    PushClientPtr outer_push_client = CPushClientMng::Instance()->GetOuterPushSrv();
    if (outer_push_client != nullptr)
    {
        outer_push_client->buildPushMsg(csmain::APP_IOS, "TRIGGER_CSPUSH_TEST", csmain::PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST, kv, gstAKCSConf.oem_name);
    }
    
    std::stringstream oss;

    oss << "{" << "\n"
        << RESULT << ": 1," << "\n"
        << MESSAGE << ": " << "\"success\"" << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << "\"msg\""  << ": \"" << msg << "\" \n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
}

void HttpReqRtspCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    std::string http_body = ctx->body().ToString();    
    AK_LOG_INFO << http_body;

    Json::Reader reader;
    Json::Value root;
    Json::Value param;
    
    std::string mac,rtp_ip,command;
    int rtp_port;
    
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        return;
    }
    
    command = root["data"]["command"].asString();


    if(! ::strcasecmp(command.c_str(),"check_rtsp_access"))
    {
        std::string passwd = "1671HF1P5x7if888";
        
        std::stringstream ret;
        ret << "{" << "\n"
            << "\"success\": true,\n"
            << "\"result\": " << "\n"
            << "{" <<  "\n"
            << "\"password\""  << ": \"" << passwd << "\" \n"
            << "}" << "\n"
            << "}" << "\n";
        cb(ret.str());
        return;

    }
    else if(! ::strcasecmp(command.c_str(),"open_rtsp"))
    {
        param = root["data"]["param"];
        mac = param["device_id"].asString();
        rtp_ip = param["rtsp_url"].asString();
        rtp_port = param["rtsp_port"].asInt();
        
        CRtspActionNotifyMsg CRtspMsg(rtp_ip, rtp_port, mac, csmain::kRtspStart);
        
        CRtspMsg.setDevMac(mac);
        GetRtspMsgControlInstance()->AddRtspActionNotifyMsg(CRtspMsg);
    }
    else if(! ::strcasecmp(command.c_str(),"close_rtsp"))
    {
        param = root["data"]["param"];
        std::string mac;
        mac = param["device_id"].asString();
        
        CRtspActionNotifyMsg CRtspMsg("", 0, mac, csmain::kRtspStop);

        CRtspMsg.setDevMac(mac);
        GetRtspMsgControlInstance()->AddRtspActionNotifyMsg(CRtspMsg);
    }
    else if(! ::strcasecmp(command.c_str(),"keepalive_rtsp"))
    {
        param = root["data"]["param"];
        std::string mac;
        mac = param["device_id"].asString();
        
        CRtspKeepNotifyMsg CRtspMsg(mac, mac, 0, 0);
        GetNotifyMsgControlInstance()->AddRtspKeepNotifyMsg(CRtspMsg);
    }
    

    
    std::stringstream ret;
    ret << "{" << "\n"
        << "\"success\": true\n"
        << "}" << "\n";
    cb(ret.str());
    return;

}

void HttpReqSetDevRemoteLogServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(ctx->body().ToString(), root))
    {
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    
    std::string mac = root["mac"].asString();
    std::string log_server_addr = root["log_server_addr"].asString();
    int enable = root["enable"].asInt();
    int log_server_port = 0;
    if(Json::intValue == root["log_server_port"].type())
    {
        log_server_port = root["log_server_port"].asInt();
    }
    
    std::stringstream message;
    message << "Config.Settings.LOGLEVEL.Level=7\n"
            << "Config.Settings.LOGLEVEL.RemoteSyslog=" << enable << "\n"
            << "Config.Settings.LOGLEVEL.RemoteServer=" << log_server_addr;
    if(log_server_port)
    {
        message << "\nConfig.Settings.LOGLEVEL.RemoteServerPort=" << log_server_port;
    }
    AK_LOG_INFO << "set " << mac << " autop :" << message.str();

    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << mac;
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    SOCKET_MSG_CONFIG socket_msg_config;
    memset(&socket_msg_config, 0, sizeof(socket_msg_config));
    snprintf(socket_msg_config.mac, sizeof(socket_msg_config.mac), "%s", mac.c_str());
    snprintf(socket_msg_config.protocal, sizeof(socket_msg_config.protocal), "%s", "1.0");
    ParseConfigItem(message.str(), socket_msg_config);

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->BuildUpdateConfigMsg(&socket_msg, &socket_msg_config) < 0)
    {
        AK_LOG_WARN << "BuildUpdateConfigMsg failed.";
        cb(getReqResponData(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        cb(getReqResponData(HTTP_RET_RESULT_MAC_NOT_CONNECT_CODE, HTTP_RET_MSG_MAC_NOT_CONNECT));
        return;
    }

    cb(getReqResponData(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}

void HttpSetMsgRateLimiterConfCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string msg_id = ctx->GetQuery("msg_id");
    uint64_t seconds = static_cast<uint64_t>(ATOI(ctx->GetQuery("seconds").c_str()));
    uint64_t requests = static_cast<uint64_t>(ATOI(ctx->GetQuery("requests").c_str()));

    gstAKCSMsgRateLimitConf[msg_id] = {seconds, requests};
    AK_LOG_INFO << "set msg rate limiter success, msg_id = " << msg_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(ATOI(msg_id.c_str())) << ", seconds = " << seconds << ", requests = " << requests;
    
    std::stringstream oss;
    oss << "set msg rate limiter success, msg_id = " << msg_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(ATOI(msg_id.c_str())) <<", seconds = " << seconds << ", requests = " << requests << "\n";
    cb(oss.str());
    return;
}

void HttpReloadMsgRateLimiterConfCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
	// 重置当前配置
	gstAKCSMsgRateLimitConf.clear();
	
	// 重新读取配置
	MsgRateLimitConfInit();

	// 返回当前配置
    std::stringstream relaod_conf;
	relaod_conf << "reload msg rate limit conf success !!! \n";
	for (const auto& msg_rate_limit : gstAKCSMsgRateLimitConf)
	{
		relaod_conf << "msg_id = " << msg_rate_limit.first << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(ATOI(msg_rate_limit.first.c_str())) << ", seconds = " << msg_rate_limit.second.Seconds() << ", requests = " << msg_rate_limit.second.Requests() << ", mac list = "; 

		auto mac_list = msg_rate_limit.second.MacList();
		for (const auto& mac :  mac_list) 
		{
			relaod_conf << mac << ";";
		}
	}

	relaod_conf << "\n";
    cb(relaod_conf.str());
	return;
}

void HttpGetMsgRateLimiterConfCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    std::stringstream msg_limit_conf;
    msg_limit_conf << "switch = " << gstAKCSConf.msg_id_limit_switch << "\n";

    for (const auto& pair : gstAKCSMsgRateLimitConf) 
    {
        const std::string& msg_id = pair.first;               
        const MessageRateLimitConf& conf = pair.second;
        msg_limit_conf << "msg_id = " << msg_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(ATOI(msg_id.c_str()))<< ", seconds = " << conf.Seconds()  << ", requests = " << conf.Requests() << "\n";
    }

    AK_LOG_INFO << msg_limit_conf;
    cb(msg_limit_conf.str());
    return;
}

void HttpSetMsgRateLimiterSwitchCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    int switch_value  = ATOI(ctx->GetQuery("switch").c_str());
    
    gstAKCSConf.msg_id_limit_switch = switch_value;
    AK_LOG_INFO << "set msg rate switch success, switch = " << switch_value;
    
    std::stringstream oss;
    oss << "set msg rate switch success, switch = " << switch_value << "\n";
    cb(oss.str());
    return;
}

void HttpGetMsgIDRequestStaticsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int top_number  = ATOI(ctx->GetQuery("top_number").c_str());

    auto sorted_requests = CAkcsRequestRecorder::getInstance().GetSortedRequestCounts();

    std::stringstream statics_ret;
    statics_ret << "Top " << top_number << ", most frequent requests: \n";

    for (size_t i = 0; i < min(sorted_requests.size(), size_t(top_number)); ++i) 
    {
        const auto& request = sorted_requests[i];
        statics_ret << "Client =  " << get<0>(request) << ", Message ID = " << get<1>(request) << ", Requests Counts = " << get<2>(request) << "\n";
    }

    statics_ret << "Dclient MsgID confluence query:  http://**************:8071/pages/viewpage.action?pageId=16189713 "<< "\n";
    
    cb(statics_ret.str());
    return;
}

void HttpSetRequestStaticsSwitchCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req url =  [ " << ctx->original_uri() << " ], ip = " << ctx->remote_ip();

    gstAKCSConf.request_statics_switch = ATOI(ctx->GetQuery("switch").c_str());

    std::stringstream switch_ret;
    switch_ret << "set_request_statics_switch success, switch = " << gstAKCSConf.request_statics_switch;

    AK_LOG_INFO << switch_ret.str();
    cb(switch_ret.str());
    return;
}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void startHttpServer()
{
    httpRespCbs = operation_http::HTTPAllRespMapInit();
    const int port = 9998;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr();
    evpp::http::Server server(addr, thread_num, false); //不需要使能ipv6
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/access_server/tcp_cli", HttpTcpCliCallback);
    server.RegisterHandler("/access_server/msg_queue", HttpReqMsgLenCallback);

    //////以下开始没有版本号 /////
    server.RegisterHandler("/dev_status", HttpReqDevStatusCallback); //检测某台设备是否tcp在线状态
    server.RegisterHandler("/dev_accsrv", HttpReqDevAccsrvCallback); //检测某台设备的接入服务器
    //获取某版本的在线设备 
    server.RegisterHandler("/dev_firmware", HttpReqDevFirmwareCallback); 

    //连接相关
    server.RegisterHandler("/getConnectedCount", HttpReqConnectedCountCallback);

    //设备交互相关
    server.RegisterHandler("/reconnectRps", HttpReqDevReconnectRpsCallback);//重新走rps
    server.RegisterHandler("/reconnectGateway", HttpReqDevReconnectGateWayCallback);//重新走网关
    server.RegisterHandler("/reconnectAccessServer", HttpReqDevReconnectAccessServerCallback);//重新走接入服务器
    server.RegisterHandler("/rebootDev", HttpReqDevRebootCallback);//重启设备

    server.RegisterHandler("/closeMacTcp", HttpReqCloseDevTcpCallback);//重新走网关
    //测试调试相关
    server.RegisterHandler("/setsendlog", HttpReqSetSendLogCallback); //设置是否TCP开启发送log
    server.RegisterHandler("/triggerMacKeysend", HttpReqMacKensendCallback); //让设备从新下载配置文件

    server.RegisterHandler("/UpdateConfig", HttpReqUpdateConfigCallback); //发送刷新设备配置请求，并且通知设备更新
    server.RegisterHandler("/UpdateAllFile", HttpReqSetSendLogCallback);//未实现
    server.RegisterHandler("/getSvnVersion", HttpReqSvnVersionCallback);  //获取服务的svn打包版本号

    //alexa 对接接口
    server.RegisterHandler("/getArmStatu", HttpReqGetArmStatuCallback); //获取布防状态
    server.RegisterHandler("/setArmStatu", HttpReqSetArmStatuCallback);//设置布防状态

    server.RegisterHandler("/updateDevServer", HttpReqNotifyDevUpdateServer);//通知某一台设备更新业务服务器地址
    //added by chenyc,2019-08-06,v4.6 http://inner_ip:9998/updateAllDevsServer?type=pbx;  type:ftp/pbx/rtsp/access/web
    server.RegisterHandler("/updateAllDevsServer", HttpReqNotifyAllDevsUpdateServer);//通知该接入服务器下面所有设备更新业务服务器地址
    //added by chenyc,2020-05-20,v5.3 http://inner_ip:9998/upgradeStatus
    server.RegisterHandler("/upgradeStatus", HttpReqChangeUpgradeStatus);//变更csmain是否处于云平台升级过程的状态
    server.RegisterHandler("/requestDevReportStatus", HttpReqDevReportStatusCallback); //要求设备上报状态

    //网管系统
    server.RegisterHandler("/startPcap", HttpReqDevStartPcapCallback); //检测某台设备是否tcp在线状态
    server.RegisterHandler("/getDevFile", HttpReqDevGetFileCallback); //检测某台设备的接入服务器
    server.RegisterHandler("/setDevAlarmSwitch", HttpReqDevAlarmSwitchCallback); //设备告警通道开关
    server.RegisterHandler("/setAction", HttpReqDevSetMonitorCallback); //设置某台设备的监控动作
    server.RegisterHandler("/checkDevAssgined", HttpReqDevExistenceCallback);//检测某台设备是否分配给用户
    server.RegisterHandler("/delDevAuthCode", HttpReqDevDelAuthCodeCallback);//删除设备所在服务器下的mac-authcode映射关系信息
    
    server.RegisterHandler("/setTzMd", HttpReqSetTzMdCallback);//变更csmain的tz_md5和tz_data_md5

    //added by chenyc,2020-08-25,v5.3 http://inner_ip:9998/NotifyAllDevsReboot
    server.RegisterHandler("/notifyAllDevsReboot", HttpReqNotifyAllDevsReboot);//通知csmain下面的所有设备全部重启

    //added by chenyc,2022.01.25,组件内部的维护通道同意不再提供set命令,全部更改配置文件后走reloadConf的接口
    //server.RegisterHandler("/setRate", HttpReqUpdateLimitRateCallback);//设置csmain对外限流的速率, 单位是每秒几个tcp链接
    
    // added by chenyc,2021.12.22 csmain向外暴露的压测接口,只有在配置项打开时才启用，生产环境一定不能打开该配置项
    ///http://inner_ip:9998/triggerReqUserInfo?mac=xxxx&users_list=yyyy;mmm;nnn
    server.RegisterHandler("/triggerReqUserInfo", HttpTestReqUserInfoCallback);//触发请求用户具体信息的压测接口

    ///http://inner_ip:9998/reloadConf,配置文件生效.
    server.RegisterHandler("/reloadConf", HttpReloadConfCallback);//reload部分参数,不再提供set参数的接口
    //end added by chenyc,2021.12.22 

    //离线推送探测
    server.RegisterHandler("/triggerCspushOffline",HttpReqTriggerPushOfflineCallback); 

    //smarthome rtsp test
    server.RegisterHandler("/rtsp", HttpReqRtspCallback); 
    
    //配置设备远程日志
    server.RegisterHandler("/maintenance/setDevRemoteLogServer", HttpReqSetDevRemoteLogServerCallback); 

    server.RegisterHandler("/metrics", HttpReqMetricsCallback);

    server.RegisterHandler("/set_limiting_timeout", HttpReqSetLimitingTimeoutCallback); 
	
    server.RegisterHandler("/setMsgRateLimiterConf", HttpSetMsgRateLimiterConfCallback); 
    server.RegisterHandler("/getMsgRateLimiterConf", HttpGetMsgRateLimiterConfCallback); 
    server.RegisterHandler("/setMsgRateLimiterSwitch", HttpSetMsgRateLimiterSwitchCallback); 
    server.RegisterHandler("/reloadMsgRateLimiterConf", HttpReloadMsgRateLimiterConfCallback); 
    server.RegisterHandler("/getReuestStatics", HttpGetMsgIDRequestStaticsCallback); 
    server.RegisterHandler("/setRequestStaticsSwitch", HttpSetRequestStaticsSwitchCallback);
	
    server.Init(port);
    server.Start();
    while (!server.IsStopped())
    {
        usleep(1000 * 1000 * 10);
    }
    return ;
}

