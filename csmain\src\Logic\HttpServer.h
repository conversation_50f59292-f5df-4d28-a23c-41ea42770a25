#ifndef __CSMAIN_HTTP_SERVER_H__
#define __CSMAIN_HTTP_SERVER_H__
#include "evpp/http/context.h"

// HttpOuterServer复用函数实现，将函数定义向外暴露
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void HttpReqDevReconnectRpsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void HttpReqDevReconnectGateWayCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);
void HttpReqDevReconnectAccessServerCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb);

void startHttpServer();

#endif // __CSMAIN_HTTP_SERVER_H__
