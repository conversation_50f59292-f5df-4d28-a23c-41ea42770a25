#ifndef __CSMAIN_INNER_ENUM_H__
#define __CSMAIN_INNER_ENUM_H__


enum OEM_TYPE
{
    OEM_AKUVOX = 1,
    OEM_DISCREET = 2,
};

typedef enum
{
    AKCS_DEVICE_TYPE_NORMAL = 0,
    AKCS_DEVICE_TYPE_APP,
    AKCS_DEVICE_TYPE_APP_SDK,
} SWITCH_AKCS_DEVICE_TYPE;          //PBX调用WakeUp传过来的app_type

enum ExtErrCode
{
    EXT_ERR_SUCCEED = 0,
    EXT_ERR_GET_DEV_LIST = 8, //获取联动单元的设备列表失败
    EXT_ERR_DAO = 9, //数据库查询失败
    EXT_ERR_PARSE_XML_MSG = 10, //解释xml消息失败
    EXT_ERR_PARSE_JSON_MSG = 11, //解释json消息失败
};

#endif // __CSMAIN_INNER_ENUM_H__
