#ifndef __CSMAIN_INNER_ST_H__
#define __CSMAIN_INNER_ST_H__

#define CSMAIN_CONF_COMMON_LEN 64

typedef struct AKCS_CONF_T
{
    // csmain本机配置信息 
    char cloud_env[CSMAIN_CONF_COMMON_LEN];
    char csmain_outer_ip[CSMAIN_CONF_COMMON_LEN];
    char csmain_outer_domain[CSMAIN_CONF_COMMON_LEN];
    char csmain_outer_ipv6[CSMAIN_CONF_COMMON_LEN];
    int enable_ipv6; //是否使能ipv6
    int log_level; //日志打印级别 LOG_LEVEL_E
    char log_file[CSMAIN_CONF_COMMON_LEN];

    // DB配置项 
    char akcs_db_ip[CSMAIN_CONF_COMMON_LEN];
    char log_db_ip[CSMAIN_CONF_COMMON_LEN];
    char mapping_db_ip[CSMAIN_CONF_COMMON_LEN];
    char db_username[CSMAIN_CONF_COMMON_LEN];
    char db_password[CSMAIN_CONF_COMMON_LEN];
    char akcs_db_database[CSMAIN_CONF_COMMON_LEN];
    char log_db_database[CSMAIN_CONF_COMMON_LEN];
    char mapping_db_database[CSMAIN_CONF_COMMON_LEN];
    int akcs_db_port;
    int log_db_port;
    int mapping_db_port;
    // 推送服务器net信息,格式=ip:port
    char push_server_addr[CSMAIN_CONF_COMMON_LEN];
    // web服务器net信息,格式=ip
    char web_server_addr[CSMAIN_CONF_COMMON_LEN];
    // web服务器net信息,格式=ipv6
    char web_server_ipv6_addr[CSMAIN_CONF_COMMON_LEN];
    // 视频回放服务器net信息,格式=ip
    char video_server_addr[CSMAIN_CONF_COMMON_LEN];
    // Etcd服务器net信息,格式=ip1:port1;ip2:port2;...
    char etcd_server_addr[128];
    // 会话服务器net信息,格式=ip:port
    char session_server_addr[CSMAIN_CONF_COMMON_LEN];

    // 网关的编号
    char gateway_code[16];
    // OEM名称,用于区分推送服务器
    char oem_name[32];
    short oem_num;
    // OEM push AESkey
    char push_AESkey[33];

    // Client端口
    int client_server_port;
    char client_server_allow_ip[256];

    // 视频录制时长
    int video_length;
    char nsq_topic[CSMAIN_CONF_COMMON_LEN];
    // svn版本号
    char svn_version[CSMAIN_CONF_COMMON_LEN];
    // Besnatalkd服务配置信息 
    char beanstalk_ip[CSMAIN_CONF_COMMON_LEN];
    int beanstalk_port;
    // oss相关参数
    char oss_role_arn[CSMAIN_CONF_COMMON_LEN];
    char oss_outer_endpoint[CSMAIN_CONF_COMMON_LEN];
    char oss_sts_endpoint[CSMAIN_CONF_COMMON_LEN];
    char oss_region_id[CSMAIN_CONF_COMMON_LEN];
    char oss_bucket[CSMAIN_CONF_COMMON_LEN];

    int reg_etcd;
    int upgrade_status;//云是否处于升级过程中,该表示主要用于监控升级过程中的信令交互监控,升级无事故的相关措施之一
    char tz_md5[CSMAIN_CONF_COMMON_LEN];
    char tz_data_md5[CSMAIN_CONF_COMMON_LEN];
    char tz_data_md5_old[CSMAIN_CONF_COMMON_LEN];
    int offline_notify;

    // 是否开启限流;0:关闭限流; 1:开启限流
    int limit_switch;

    // 限流速率
    double rate;

    char config_server_ipv4[CSMAIN_CONF_COMMON_LEN];
    char config_server_ipv6[CSMAIN_CONF_COMMON_LEN];
    int  config_server_port;
    int  config_server_tlshigh_port;
    char config_server_domain[CSMAIN_CONF_COMMON_LEN];
    int  config_server_domain_gray_percentage; // 下发域名灰度比例

    char apiurl[CSMAIN_CONF_COMMON_LEN];
    int is_aws;

    int download_user_timeout;//设备下载详细数据去重的超时时间

    int stress_test;//压测开关是否打开,0:关(默认，生产环境一定不能开),1:开

    char linker_nsq_topic[CSMAIN_CONF_COMMON_LEN];
    char voice_server_ipv4[64];
    char voice_server_ipv6[64];
    char linker_nsq_ip[CSMAIN_CONF_COMMON_LEN];
    
    char server_tag[CSMAIN_CONF_COMMON_LEN];
    int server_area;

    //不进行呼叫限制,线上场景可能有同个小区可以相互呼叫的场景，加个开关可以随时调整
    int call_no_limit;

    // smg alexa
    char smg_alexa_addr[CSMAIN_CONF_COMMON_LEN];

    char use_config_ip_mng[CSMAIN_CONF_COMMON_LEN];
    char csconfig_ip[CSMAIN_CONF_COMMON_LEN];

    int limiting_timeout;
    int sl20_opendoor_expire;
    char ssh_proxy_domain[128];

    int msg_id_limit_switch;
    int request_statics_switch;

    int msg_lantency_metric;
} AKCS_CONF;

#endif // __CSMAIN_INNER_ST_H__
