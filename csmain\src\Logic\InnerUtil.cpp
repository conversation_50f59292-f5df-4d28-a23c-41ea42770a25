#include "InnerUtil.h"

static const char* kIllegalIPStrList[] = {"Obtaining IP address..."}; //上报的非法ip地址拦截

bool IsValidIPAddress(const std::string &ip_address)
{
    if(ip_address.size() == 0)
    {
        return false;
    }
    for(const auto& illegal_ip_str : kIllegalIPStrList)
    {
        if(!ip_address.compare(illegal_ip_str))
        {
            return false;
        }
    }
    return true;
}

bool IsErrorFirmwareForChangeRelay(const std::string &firmware)
{
	if (firmware == "***********")
	{
		return true;
	}
    return false;
}