#include "stdafx.h"
#include "KeyControl.h"
#include "DeviceControl.h"
#include "MsgControl.h"
#include "AkcsCommonDef.h"
#include <evpp/tcp_conn.h>
#include "Md5.h"
#include "csmainserver.h"
#include "util.h"
#include "AKUpgradeMonitor.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/Shadow.h"
#include "util_judge.h"

const static int kMd5Len = 36;
const static char def_setting_md5[kMd5Len] = "8f7c1042a1bb4fa142a4723404a7d684";
extern AKCS_CONF gstAKCSConf;

//如果csconfig过来的keysend没办法判断类型，那么不会发时区的md5，导致第一次添加的设备不会下载到最新的时区文件
const std::list<int> g_timezone_xml_list = std::initializer_list<int>{33,102,312,216,320,912,18,112,213,212,103,17,12,16,106,105,101,92,220,111,221,226,227,28,21,313,27,26,20,110};
const std::list<int> g_timezone_tzdata_list = std::initializer_list<int>{88,49,119,915,933,916,117,48,47,115,81,82,83,29,539,567};
//因为其他phone没有实现，这样就会导致dclient每次重连都会下载时区文件
const std::list<int> g_timezone_tzdata_support_list = std::initializer_list<int>{29,916};
extern std::vector<HTTP_MSG_UPDATE_TIMEZONE_DATE> automation_test_timezone_update_device_list;

void ChangeTimeZoneType(int &type, int firmware_num)
{
    if(type ==  TIME_ZONE_NONE)
    {
        if (InList(g_timezone_tzdata_list, firmware_num))
        {
            type = TIME_ZONE_DATA;
        } 
        else if (InList(g_timezone_xml_list, firmware_num))
        {
            type = TIME_ZONE_XML;
        }
    }
}



bool CKeyControl::IsDevSupportTzData(const std::string& sw_ver, int func_bit)
{
    //R29在做ACMS时区同步需求 改到了云的逻辑
    //R29V10.2: 29.30.10.206~29.30.10.238
    //R29V10.3: 29.30.10.301~29.30.10.328
    int model = 0;
    int big_ver = 0;
    int small_ver = 0;
    if (false == GetFirmwareInfo(sw_ver, model, big_ver, small_ver))
    {
        return false;
    }

    if (model == SOFTWARE_R29 
       && big_ver == 10 
       && ( 
           (small_ver >= 206 && small_ver <= 238) || (small_ver >= 301 && small_ver <= 328) 
         )
    )
    {
        return false;
    }

    //判断是否是已经是支持func判断时区
    if (SwitchHandle(func_bit, FUNC_DEV_TIME_ZONE))
    {
        return true;
    }

    //旧设备不支持tzdata更新的型号不进行tzdata判断
    if (!InList(g_timezone_tzdata_support_list, model))
    {
        return false;
    } 
    return true;
}

/*重新组装url，加入token和过期时间，Add by czw*/
static int ModifyUrlAddToken(std::string& url)
{
    std::string token;
    std::string uri;
    size_t pos = url.find("/group");
    if (pos != std::string::npos)
    {
        uri = url.substr(pos);  //截取出uri
    }
    else
    {
        pos = url.find("/download");
        if (pos != std::string::npos)
        {
            uri = url.substr(pos);
        }
        else
        {
            AK_LOG_WARN << "ModifyUrlAddToken failed,url=" << url;
            return false;
        }
    }

    time_t timer = time(nullptr) + 600; //过期时间暂定为600s 
    char time_sec[16] = {0};
    ::snprintf(time_sec, sizeof(time_sec), "%ld", timer);

    /* token生成方式：base64Encode(Md5(ak_download:path:time_overdue)) */
    std::string form_token = "ak_download:";
    form_token += uri;
    form_token += ":";
    form_token += time_sec;

    char* token_pointer = akuvox_encrypt::MD5(form_token).GetBase64Md5();
    if (!token_pointer)
    {
        AK_LOG_WARN << "GetBase64Md5 failed.";
        return false;
    }
    else
    {
        token = token_pointer;
        free(token_pointer);
    }

    /* 最终的url：https://192.168.11.112:443/download/personal/node_60/10000100004/Config/0C110507C7FF.cfg?token=oV_3cnoyCQ6VxbFRLFGd1Q==&e=1577811600 */
    StringReplace(token, "+", "-");
    StringReplace(token, "/", "_");  //nginx防盗链要求token中+/转换为-_
    url += "?token=";
    url += token;
    url += "&e=";
    url += time_sec;

    return true;
}


void CKeyControl::UpdateTimezoneUrlAndMd5ForAutomationTest(const std::string& mac, SOCKET_MSG_KEY_SEND* pSendKeyMsg) 
{
    std::string temp_url;
    // 遍历 automation_test_timezone_update_device_list ，查找是否存在指定的 MAC 地址
    for (const auto& device : automation_test_timezone_update_device_list) 
    {
        if (std::string(device.mac) == mac) 
        {
            if (std::strcmp(device.type, DEVICE_TIME_ZONE_TYPE_TZXML) == 0) 
            {   
                temp_url = device.tz_xml_url;
                Snprintf(pSendKeyMsg->tz_url, sizeof(pSendKeyMsg->tz_url), temp_url.c_str());
                Snprintf(pSendKeyMsg->tz_md5, sizeof(pSendKeyMsg->tz_md5), device.tz_data_md5);
            }
            else
            {   
                temp_url = device.tz_data_url;
                Snprintf(pSendKeyMsg->tz_data_url, sizeof(pSendKeyMsg->tz_data_url), temp_url.c_str());
                Snprintf(pSendKeyMsg->tz_data_md5, sizeof(pSendKeyMsg->tz_data_md5), device.tz_data_md5);
            }
        }
    }
    return; 
}


CKeyControl* GetKeyControlInstance()
{
    return CKeyControl::GetInstance();
}

CKeyControl::CKeyControl()
{
    deque_.clear();
    comm_deque_.clear();
    given_deque_.clear();
}

CKeyControl::~CKeyControl()
{

}

CKeyControl* CKeyControl::instance = NULL;

CKeyControl* CKeyControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CKeyControl();
    }

    return instance;
}

//增加keysend消息到数据库中
int CKeyControl::AddKeySend(const KEY_SEND& KeySend)
{
    std::lock_guard<std::mutex> lock_(commKeySendMtx_);
    comm_deque_.push_back(KeySend);
    AK_LOG_INFO << "Add Community KeySend,Mac=" << KeySend.mac << ";Community=" << KeySend.community
                << ";DeviceNode=" << KeySend.device_node << ";TzType=" << KeySend.tz_type;

    return 0;
}

//增加keysend消息到数据库中
int CKeyControl::AddOfficeKeySend(const KEY_SEND& KeySend)
{
    std::lock_guard<std::mutex> lock_(officeKeySendMtx_);
    office_deque_.push_back(KeySend);
    AK_LOG_INFO << "Add Office KeySend,Mac=" << KeySend.mac << ";Office=" << KeySend.community
                << ";DeviceNode=" << KeySend.device_node << ";TzType=" << KeySend.tz_type;

    return 0;
}


//增加keysend消息到队列中
int CKeyControl::AddPersonalKeySend(const PERSONAL_KEY_SEND& KeySend)
{
    std::lock_guard<std::mutex> lock_(keySendMtx_);
    deque_.push_back(KeySend);
    AK_LOG_INFO << "Add Personal KeySend,Mac=" << KeySend.mac << ";Node=" << KeySend.node << ";TzType=" << KeySend.tz_type;
    return 0;
}

//增加keysend消息到队列中
int CKeyControl::AddGivenKeySend(const GIVEN_KEY_SEND& keysend)
{
    std::lock_guard<std::mutex> lock_(given_key_send_mtx_);
    given_deque_.push_back(keysend);
    AK_LOG_INFO << "Add given KeySend,Mac=" << keysend.mac << ";filepath=" << keysend.file_path << ";traceid=" << keysend.traceid;
    return 0;
}


void CKeyControl::ClearUserKeySend(SOCKET_MSG_KEY_SEND& keysend)
{
    keysend.schedule_md5[0] = 0;
    keysend.schedule_url[0] = 0;
    keysend.user_meta_md5[0] = 0;
    keysend.user_meta_url[0] = 0;  
}

std::string GetConfigServerDomain(const std::string& mac)
{
    if (strlen(gstAKCSConf.config_server_domain) > 0
    && (static_cast<int>(std::hash<std::string>{}(mac) % 100) < gstAKCSConf.config_server_domain_gray_percentage))
    {
        return gstAKCSConf.config_server_domain;
    }
    else
    {
        return gstAKCSConf.config_server_ipv4;
    }
}

//add by chenzhx 20200927 设备端到5.4版本 只要md5值是空，那么就好通知设备清空对应的配置
//检查是否有需要发送出去的KEY消息，如果有则发送第一条出去
int CKeyControl::CheckKeySend()
{
    KEY_SEND keySend;
    std::deque<KEY_SEND> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(commKeySendMtx_);
        comm_deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        int continue_flag = 0;
        keySend = tmp_deque.front();
        tmp_deque.pop_front();
        for (auto iter = tmp_deque.begin(); iter != tmp_deque.end(); iter++)
        {
            //通过连接判断去重，不能用mac，因为mac对应的连接信息有可能是旧的连接
            if (keySend.weak_conn.lock() == iter->weak_conn.lock())
            {
                continue_flag = 1;
                break;   //去重，用最后面的更新
            }
        }
        
        if (continue_flag)
        {
            continue;
        }
        evpp::TCPConnPtr conn(keySend.weak_conn.lock());
        if (!conn) //检查是否还存在
        {
            continue;
        }
        //根据Community和DeviceNode和Extension获取最新的PrivatekeyMd5和RfidMd5
        DEVICE_SETTING deviceSetting;
        memset(&deviceSetting, 0, sizeof(deviceSetting));
        if (GetDeviceControlInstance()->GetDeviceSettingByMac(keySend.mac, &deviceSetting) < 0)
        {
            AK_LOG_WARN << "CheckKeySend failed. mac:" << keySend.mac;
            continue;
        }

        // 转流门口机不下发keysend
        if (deviceSetting.repost && (deviceSetting.type == DEVICE_TYPE_STAIR || deviceSetting.type == DEVICE_TYPE_DOOR || deviceSetting.type == DEVICE_TYPE_ACCESS))
        {
            // 即插即用项目下的门口机要下发keysend
            CommunityInfo comm_info(deviceSetting.manager_account_id);
            if (!akjudge::IsInstallerKitProject(comm_info.GetProjectPlanType()))
            {
                AK_LOG_WARN << "repost devices does not need keysend, mac:" << keySend.mac;
                continue;
            }
        }
        
        ChangeTimeZoneType(keySend.tz_type, deviceSetting.firmware);

        //获取本机IP地址,2017-08-29,后面要改成文件服务器的地址, added by chenyc, 2019-03-14,分布式系统改造,
        //暂时web服务器充当文件服务器(其实描述成是csadapt服务所在的机器更加合理),且只有一台.直接在csmain的配置文件写.
        char csconfig_ip[64];
        int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
        if (!ipv6)
        {
            if(CheckStrInFilter(gstAKCSConf.use_config_ip_mng, to_string(deviceSetting.manager_account_id)))
            {
                snprintf(csconfig_ip, sizeof(csconfig_ip), "%s", gstAKCSConf.csconfig_ip);
                AK_LOG_INFO << "use config ip: " << csconfig_ip;
            }
            else
            {
                std::string config_server_domain = GetConfigServerDomain(deviceSetting.mac);
                Snprintf(csconfig_ip, sizeof(csconfig_ip), config_server_domain.c_str());
            }
        }
        else
        {
            snprintf(csconfig_ip, sizeof(csconfig_ip), "[%s]", gstAKCSConf.config_server_ipv6);
        }

        //根据DeviceNode和Extension组织消息并发给设备，通知设备来更新最新文件
        SOCKET_MSG_KEY_SEND keySendMsg;
        memset(&keySendMsg, 0, sizeof(keySendMsg));
        Snprintf(keySendMsg.protocal, sizeof(keySendMsg.protocal) / sizeof(TCHAR), PROTOCAL_NAME_DEFAULT);

        std::string tz_url =  GetDownloadDSTTimeZoneXmlPath(csconfig_ip);
        ModifyUrlAddToken(tz_url);
        std::string tz_data_url = GetDownloadDSTTimeZoneDataPath(csconfig_ip);
        ModifyUrlAddToken(tz_data_url);

        std::string private_key_url;
        std::string rf_id_url;
        std::string config_url;
        std::string face_url;
        std::string user_meta_url;
        std::string schedule_url;
        std::string contact_url;  

        DevShadow shadow = {0};
        if(dbinterface::Shadow::GetAllShadowByMac(deviceSetting.mac, shadow) == 0)
        {
            int is_support_tls_high_version = SwitchHandle(deviceSetting.fun_bit, FUNC_DEV_SUPPORT_DOWNLOAD_TLS12);
            std::string config_server =  GetConfigDownloadServer(csconfig_ip, is_support_tls_high_version);
            if (strlen(shadow.prikey_storage_path) > 0)
            {
                private_key_url = config_server + shadow.prikey_storage_path;
            }
            if (strlen(shadow.rfkey_storage_path) > 0)
            {
                rf_id_url = config_server + shadow.rfkey_storage_path;
            }      
            if (strlen(shadow.config_storage_path) > 0)
            {
                config_url = config_server + shadow.config_storage_path;
            }
            if (strlen(shadow.contac_storage_path) > 0)
            {
                contact_url = config_server + shadow.contac_storage_path;
            }        
            if (strlen(shadow.face_storage_path) > 0)
            {
                face_url = config_server + shadow.face_storage_path;
            }
            if (strlen(shadow.usermeta_storage_path) > 0)
            {
                user_meta_url = config_server + shadow.usermeta_storage_path;
            }        
            if (strlen(shadow.schedule_storage_path) > 0)
            {
                schedule_url = config_server + shadow.schedule_storage_path;
            }
        }
        else
        {
            AK_LOG_INFO << "Get shadow error. mac:" << deviceSetting.mac;
            continue;
        }

        Snprintf(keySendMsg.private_key_md5, sizeof(keySendMsg.private_key_md5), deviceSetting.private_key_md5);
        Snprintf(keySendMsg.rf_id_md5, sizeof(keySendMsg.rf_id_md5), deviceSetting.rf_id_md5);
        Snprintf(keySendMsg.config_md5, sizeof(keySendMsg.config_md5), deviceSetting.config_md5);

        if (strlen(keySendMsg.config_md5) > 0)
        {
            ModifyUrlAddToken(config_url);
            Snprintf(keySendMsg.config_url, sizeof(keySendMsg.config_url), config_url.c_str());
        }
        if (strlen(keySendMsg.rf_id_md5) > 0)
        {
            ModifyUrlAddToken(rf_id_url);
            Snprintf(keySendMsg.rf_id_url, sizeof(keySendMsg.rf_id_url), rf_id_url.c_str());
        }
        if (strlen(keySendMsg.private_key_md5) > 0)
        {
            ModifyUrlAddToken(private_key_url);    //新增nginx防盗链，故重组url
            Snprintf(keySendMsg.private_key_url, sizeof(keySendMsg.private_key_url), private_key_url.c_str());
        }
        if (keySend.tz_type == TIME_ZONE_XML)
        {
            Snprintf(keySendMsg.tz_url, sizeof(keySendMsg.tz_url), tz_url.c_str());
            Snprintf(keySendMsg.tz_md5, sizeof(keySendMsg.tz_md5), gstAKCSConf.tz_md5);
        }
        else if (keySend.tz_type == TIME_ZONE_DATA)
        {
            if (IsDevSupportTzData(deviceSetting.SWVer, deviceSetting.fun_bit))
            {
                Snprintf(keySendMsg.tz_data_url, sizeof(keySendMsg.tz_data_url), tz_data_url.c_str());
                Snprintf(keySendMsg.tz_data_md5, sizeof(keySendMsg.tz_data_md5), gstAKCSConf.tz_data_md5);
            }/* 对于旧的就没必要下发了 因为tzdata文件没有区分新旧，这时候下载了新的md5值校验不过相当于没下载
            else
            {
                Snprintf(keySendMsg.tz_data_url, sizeof(keySendMsg.tz_data_url), tz_data_url.c_str());
                Snprintf(keySendMsg.tz_data_md5, sizeof(keySendMsg.tz_data_md5), gstAKCSConf.tz_data_md5_old);
            }*/
        }

        if (deviceSetting.dclient_version >= D_CLIENT_VERSION_5400)
        {
            Snprintf(keySendMsg.face_md5, sizeof(keySendMsg.face_md5), deviceSetting.face_md5);
            if (strlen(deviceSetting.face_md5) > 0)
            {
                ModifyUrlAddToken(face_url);
                Snprintf(keySendMsg.face_url, sizeof(keySendMsg.face_url), face_url.c_str());
            }
        }

        if (deviceSetting.dclient_version >= D_CLIENT_VERSION_6100 )
        {
            Snprintf(keySendMsg.schedule_md5, sizeof(keySendMsg.schedule_md5), deviceSetting.schedule_md5);
            if (strlen(deviceSetting.schedule_md5) > 0)
            {
                ModifyUrlAddToken(schedule_url);
                Snprintf(keySendMsg.schedule_url, sizeof(keySendMsg.schedule_url), schedule_url.c_str());
            }

            Snprintf(keySendMsg.user_meta_md5, sizeof(keySendMsg.user_meta_md5), deviceSetting.user_meta_md5);
            if (strlen(deviceSetting.user_meta_md5) > 0)
            {
                ModifyUrlAddToken(user_meta_url);
                Snprintf(keySendMsg.user_meta_url, sizeof(keySendMsg.user_meta_url), user_meta_url.c_str());
            }            
        }

        if (deviceSetting.dclient_version >= D_CLIENT_VERSION_1_0)
        {
            ModifyUrlAddToken(contact_url);
            Snprintf(keySendMsg.contact_url, sizeof(keySendMsg.contact_url), contact_url.c_str());
            Snprintf(keySendMsg.contact_md5, sizeof(keySendMsg.contact_md5), deviceSetting.contact_md5);
        }

        if (deviceSetting.type == DEVICE_TYPE_INDOOR || deviceSetting.type == DEVICE_TYPE_MANAGEMENT)
        {
            keySendMsg.private_key_md5[0] = 0;
            keySendMsg.private_key_url[0] = 0;
            keySendMsg.rf_id_md5[0] = 0;
            keySendMsg.rf_id_url[0] = 0;

            ClearUserKeySend(keySendMsg);
        }

        //TODO:放入内存 mngid->new/old
        CommunityInfo community_info(deviceSetting.manager_account_id);
        if (community_info.GetIsNew() == IsNewFlag::NEW_COMMUNITY)
        {
            /*
            if(!keySend.need_keysend)
            {
                AK_LOG_INFO << "new community device:" << deviceSetting.mac << " on pin/rf change, there is no need to send keysend.";
                continue;
            }
            */
            if (deviceSetting.dclient_version >= D_CLIENT_VERSION_6100)
            {
                keySendMsg.private_key_md5[0] = 0;
                keySendMsg.private_key_url[0] = 0;
                keySendMsg.rf_id_md5[0] = 0;
                keySendMsg.rf_id_url[0] = 0;
                keySendMsg.face_md5[0] = 0;
                keySendMsg.face_url[0] = 0;    
            }
        }
        else
        {
            // 旧小区 user_meta/schedule 置空
            ClearUserKeySend(keySendMsg);
        }
        //测试环境，时区文件更新自动化测试
        if(IsTestServer(gstAKCSConf.cloud_env))
        {
            UpdateTimezoneUrlAndMd5ForAutomationTest(keySend.mac, &keySendMsg);
        }
        SOCKET_MSG socket_msg;
        SOCKET_MSG* socket_message = &socket_msg;
        memset(socket_message, 0, sizeof(SOCKET_MSG));
        int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
        if (GetMsgControlInstance()->BuildKeySendMsg(socket_message, &keySendMsg, ver, deviceSetting.mac) < 0) //mac地址加密
        {
            AK_LOG_WARN << "Build KeySendMsg failed.";
            continue;
        }

        AK_LOG_INFO << "[KeySend] Mac[" << deviceSetting.mac << "], ip:port is:" << conn->remote_addr() << " type:" << deviceSetting.type;
        if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
        {
            AK_LOG_WARN << "Send KeySendMsg failed.";
            continue;
        }
        //added by chenyc,2020-05-20,设备端的配置文件的信令监控结果写入到升级过程监控文件中
        //add by chenzhx 直接通过nginx下载日志判断,
        //add by zhangjq 文件上传到fdfs后不能通过nginx下载日志判断了
        AKCS::Singleton<UpgradeMonitor>::instance().WriteUpgradeKeyMsg(deviceSetting.mac, keySendMsg);
    }
    return 0;
}

//个人终端用户,key send队列
//add by chenzhx 20200927 设备端到5.4版本 只要md5值是空，那么就好通知设备清空对应的配置
int CKeyControl::CheckPersonalKeySend()
{
    PERSONAL_KEY_SEND PersonalkeySend;
    std::deque<PERSONAL_KEY_SEND> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(keySendMtx_);
        deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        int continue_flag = 0;
        PersonalkeySend = tmp_deque.front();
        tmp_deque.pop_front();
        for (auto iter = tmp_deque.begin(); iter != tmp_deque.end(); iter++)
        {
            if (PersonalkeySend.weak_conn.lock() == iter->weak_conn.lock())
            {
                continue_flag = 1;
                break;   //去重，用最后面的更新
            }
        }
        if (continue_flag)
        {
            continue;
        }
        evpp::TCPConnPtr conn(PersonalkeySend.weak_conn.lock());
        if (!conn) //检查是否还存在
        {
            continue;
        }

        //获取本机IP地址,2017-08-29,后面要改成文件服务器的地址
        char csconfig_ip[64];
        int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
        if (!ipv6)
        {
            std::string config_server_domain = GetConfigServerDomain(PersonalkeySend.mac);
            Snprintf(csconfig_ip, sizeof(csconfig_ip), config_server_domain.c_str());
        }
        else
        {
            snprintf(csconfig_ip, sizeof(csconfig_ip), "[%s]", gstAKCSConf.config_server_ipv6);
        }

        //根据mac地址获取最新的PrivatekeyMd5和RfidMd5
        DEVICE_SETTING deviceSetting;
        memset(&deviceSetting, 0, sizeof(deviceSetting));
        if (GetDeviceControlInstance()->GetDeviceSettingByMac(PersonalkeySend.mac, &deviceSetting) < 0)
        {
            AK_LOG_WARN << "Get personal DeviceSettingByNode failed. mac:" << PersonalkeySend.mac;
            continue;
        }


        // 转流门口机不下发keysend
        if (deviceSetting.repost && (deviceSetting.type == DEVICE_TYPE_STAIR || deviceSetting.type == DEVICE_TYPE_DOOR || deviceSetting.type == DEVICE_TYPE_ACCESS))
        {
            AK_LOG_WARN << "repost devices does not need keysend, mac:" << PersonalkeySend.mac;
            continue;
        }

        ChangeTimeZoneType(PersonalkeySend.tz_type, deviceSetting.firmware);

        //根据联动单元+设备mac地址组织消息并发给设备,通知设备来更新最新文件
        SOCKET_MSG_KEY_SEND keySendMsg;
        memset(&keySendMsg, 0, sizeof(keySendMsg));
        Snprintf(keySendMsg.protocal, sizeof(keySendMsg.protocal), PROTOCAL_NAME_DEFAULT);
        
        std::string private_key_url;
        std::string rf_id_url;
        std::string config_url;
        std::string contact_url;
        std::string face_url;
        std::string tz_url =  GetDownloadDSTTimeZoneXmlPath(csconfig_ip);
        ModifyUrlAddToken(tz_url);
        std::string tz_data_url = GetDownloadDSTTimeZoneDataPath(csconfig_ip);
        ModifyUrlAddToken(tz_data_url);


        DevShadow shadow = {0};
        if(dbinterface::Shadow::GetAllShadowByMac(deviceSetting.mac, shadow) == 0)
        {
            int is_support_tls_high_version = SwitchHandle(deviceSetting.fun_bit, FUNC_DEV_SUPPORT_DOWNLOAD_TLS12);
            std::string config_server =  GetConfigDownloadServer(csconfig_ip, is_support_tls_high_version);

            if (strlen(shadow.prikey_storage_path) > 0)
            {
                private_key_url = config_server + shadow.prikey_storage_path;
            }
            if (strlen(shadow.rfkey_storage_path) > 0)
            {
                rf_id_url = config_server + shadow.rfkey_storage_path;
            }      
            if (strlen(shadow.config_storage_path) > 0)
            {
                config_url = config_server + shadow.config_storage_path;
            }
            if (strlen(shadow.contac_storage_path) > 0)
            {
                contact_url = config_server + shadow.contac_storage_path;
            }        
            if (strlen(shadow.face_storage_path) > 0)
            {
                face_url = config_server + shadow.face_storage_path;
            }
        }
        else
        {
            AK_LOG_INFO << "Get shadow error. mac:" << deviceSetting.mac;
            continue;
        }

        Snprintf(keySendMsg.private_key_md5, sizeof(keySendMsg.private_key_md5), deviceSetting.private_key_md5);
        if (strlen(deviceSetting.private_key_md5) > 0)
        {
            ModifyUrlAddToken(private_key_url);    //新增nginx防盗链，故重组url
            Snprintf(keySendMsg.private_key_url, sizeof(keySendMsg.private_key_url), private_key_url.c_str());
        }

        Snprintf(keySendMsg.rf_id_md5, sizeof(keySendMsg.rf_id_md5), deviceSetting.rf_id_md5);
        if (strlen(deviceSetting.rf_id_md5) > 0)
        {
            ModifyUrlAddToken(rf_id_url);
            Snprintf(keySendMsg.rf_id_url, sizeof(keySendMsg.rf_id_url), rf_id_url.c_str());
        }

        Snprintf(keySendMsg.config_md5, sizeof(keySendMsg.config_md5), deviceSetting.config_md5);
        if (strlen(deviceSetting.config_md5) > 0)
        {

            ModifyUrlAddToken(config_url);
            Snprintf(keySendMsg.config_url, sizeof(keySendMsg.config_url), config_url.c_str());
        }

        if (PersonalkeySend.tz_type == TIME_ZONE_XML)
        {
            Snprintf(keySendMsg.tz_url, sizeof(keySendMsg.tz_url), tz_url.c_str());
            Snprintf(keySendMsg.tz_md5, sizeof(keySendMsg.tz_md5), gstAKCSConf.tz_md5);
        }
        else if (PersonalkeySend.tz_type == TIME_ZONE_DATA)
        {
            if (IsDevSupportTzData(deviceSetting.SWVer, deviceSetting.fun_bit))
            {
                Snprintf(keySendMsg.tz_data_url, sizeof(keySendMsg.tz_data_url), tz_data_url.c_str());
                Snprintf(keySendMsg.tz_data_md5, sizeof(keySendMsg.tz_data_md5), gstAKCSConf.tz_data_md5);
            }
            /* 对于旧的就没必要下发了 因为tzdata文件没有区分新旧，这时候下载了新的md5值校验不过相当于没下载
            else 
            {
                Snprintf(keySendMsg.tz_data_url, sizeof(keySendMsg.tz_data_url), tz_data_url.c_str());
                Snprintf(keySendMsg.tz_data_md5, sizeof(keySendMsg.tz_data_md5), gstAKCSConf.tz_data_md5_old);
            }*/

        }

        Snprintf(keySendMsg.face_md5, sizeof(keySendMsg.face_md5), deviceSetting.face_md5);
        if (strlen(deviceSetting.face_md5) > 0 && deviceSetting.dclient_version >= D_CLIENT_VERSION_5400)
        {
            ModifyUrlAddToken(face_url);
            Snprintf(keySendMsg.face_url, sizeof(keySendMsg.face_url), face_url.c_str());
        }


        if (deviceSetting.dclient_version >= D_CLIENT_VERSION_1_0)
        {
            if (strlen(deviceSetting.contact_md5) > 0)
            {
                ModifyUrlAddToken(contact_url);
                Snprintf(keySendMsg.contact_url, sizeof(keySendMsg.contact_url), contact_url.c_str());
            }
            Snprintf(keySendMsg.contact_md5, sizeof(keySendMsg.contact_md5), deviceSetting.contact_md5);
        }
        if (deviceSetting.type == DEVICE_TYPE_INDOOR || deviceSetting.type == DEVICE_TYPE_MANAGEMENT)
        {
            keySendMsg.private_key_md5[0] = 0;
            keySendMsg.private_key_url[0] = 0;
            keySendMsg.rf_id_md5[0] = 0;
            keySendMsg.rf_id_url[0] = 0;
        }

        //测试环境，时区文件更新自动化测试
        if(IsTestServer(gstAKCSConf.cloud_env))
        {
            UpdateTimezoneUrlAndMd5ForAutomationTest(PersonalkeySend.mac, &keySendMsg);
        }
        
        SOCKET_MSG socket_msg;
        SOCKET_MSG* socket_message = &socket_msg;
        memset(socket_message, 0, sizeof(SOCKET_MSG));
        int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
        if (GetMsgControlInstance()->BuildKeySendMsg(socket_message, &keySendMsg, ver, PersonalkeySend.mac) < 0)
        {
            AK_LOG_WARN << "Build personal KeySendMsg failed.";
            continue;
        }

        AK_LOG_INFO << "[KeySend] Mac[" << PersonalkeySend.mac << "], ip:port is:" << conn->remote_addr().c_str() << " type:" << deviceSetting.type;
        if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
        {
            AK_LOG_WARN << "Send KeySendMsg failed.";
            continue;
        }
        //added by chenyc,2020-05-20,设备端的配置文件的信令监控结果写入到升级过程监控文件中
        //add by chenzhx 直接通过nginx下载日志判断
        //add by zhangjq 文件上传到fdfs后不能通过nginx下载日志判断了
        AKCS::Singleton<UpgradeMonitor>::instance().WriteUpgradeKeyMsg(deviceSetting.mac, keySendMsg);
    }
    return 0;
}

//office
int CKeyControl::CheckOfficeKeySend()
{
    KEY_SEND keySend;
    std::deque<KEY_SEND> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(officeKeySendMtx_);
        office_deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        int continue_flag = 0;
        keySend = tmp_deque.front();
        tmp_deque.pop_front();
        for (auto iter = tmp_deque.begin(); iter != tmp_deque.end(); iter++)
        {
            if (keySend.weak_conn.lock() == iter->weak_conn.lock())
            {
                continue_flag = 1;
                break;   //去重，用最后面的更新
            }
        }
        if (continue_flag)
        {
            continue;
        }
        evpp::TCPConnPtr conn(keySend.weak_conn.lock());
        if (!conn) //检查是否还存在
        {
            continue;
        }
        
        //根据Community和DeviceNode和Extension获取最新的PrivatekeyMd5和RfidMd5
        DEVICE_SETTING deviceSetting;
        memset(&deviceSetting, 0, sizeof(deviceSetting));
        if (GetDeviceControlInstance()->GetDeviceSettingByMac(keySend.mac, &deviceSetting) < 0)
        {
            AK_LOG_WARN << "CheckKeySend failed. mac:" << keySend.mac;
            continue;
        }

        /*
        if(!keySend.need_keysend)
        {
            AK_LOG_INFO << "office device:" << deviceSetting.mac << " on pin/rf change, there is no need to send keysend.";
            continue;
        }
        */
        ChangeTimeZoneType(keySend.tz_type, deviceSetting.firmware);
        
        //获取本机IP地址,2017-08-29,后面要改成文件服务器的地址, added by chenyc, 2019-03-14,分布式系统改造,
        //暂时web服务器充当文件服务器(其实描述成是csadapt服务所在的机器更加合理),且只有一台.直接在csmain的配置文件写.
        char csconfig_ip[64];
        int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
        if (!ipv6)
        {
            std::string config_server_domain = GetConfigServerDomain(keySend.mac);
            Snprintf(csconfig_ip, sizeof(csconfig_ip), config_server_domain.c_str());
        }
        else
        {
            snprintf(csconfig_ip, sizeof(csconfig_ip), "[%s]", gstAKCSConf.config_server_ipv6);
        }

        //根据DeviceNode和Extension组织消息并发给设备，通知设备来更新最新文件
        SOCKET_MSG_KEY_SEND keySendMsg;
        memset(&keySendMsg, 0, sizeof(keySendMsg));
        Snprintf(keySendMsg.protocal, sizeof(keySendMsg.protocal) / sizeof(TCHAR), PROTOCAL_NAME_DEFAULT);

        std::string tz_url =  GetDownloadDSTTimeZoneXmlPath(csconfig_ip);
        ModifyUrlAddToken(tz_url);
        std::string tz_data_url = GetDownloadDSTTimeZoneDataPath(csconfig_ip);
        ModifyUrlAddToken(tz_data_url);
        std::string config_url;
        std::string face_url;
        std::string user_meta_url;
        std::string schedule_url;
        std::string contact_url;   

        DevShadow shadow = {0};
        if(dbinterface::Shadow::GetAllShadowByMac(deviceSetting.mac, shadow) == 0)
        {             
            int is_support_tls_high_version = SwitchHandle(deviceSetting.fun_bit, FUNC_DEV_SUPPORT_DOWNLOAD_TLS12);
            std::string config_server =  GetConfigDownloadServer(csconfig_ip, is_support_tls_high_version);

            if (strlen(shadow.config_storage_path) > 0)
            {
                config_url = config_server + shadow.config_storage_path;
            }       
            if (strlen(shadow.face_storage_path) > 0)
            {
                face_url = config_server+ shadow.face_storage_path;
            }
            if (strlen(shadow.usermeta_storage_path) > 0)
            {
                user_meta_url = config_server+ shadow.usermeta_storage_path;
            }        
            if (strlen(shadow.schedule_storage_path) > 0)
            {
                schedule_url = config_server + shadow.schedule_storage_path;
            }
            if (strlen(shadow.contac_storage_path) > 0)
            {
                contact_url = config_server + shadow.contac_storage_path;
            }
        }
        else
        {
            AK_LOG_INFO << "Get shadow error. mac:" << deviceSetting.mac;
            continue;
        }


        Snprintf(keySendMsg.config_md5, sizeof(keySendMsg.config_md5), deviceSetting.config_md5);

        if (strlen(keySendMsg.config_md5) > 0)
        {
            ModifyUrlAddToken(config_url);
            Snprintf(keySendMsg.config_url, sizeof(keySendMsg.config_url), config_url.c_str());
        }
        if (keySend.tz_type == TIME_ZONE_XML)
        {
            Snprintf(keySendMsg.tz_url, sizeof(keySendMsg.tz_url), tz_url.c_str());
            Snprintf(keySendMsg.tz_md5, sizeof(keySendMsg.tz_md5), gstAKCSConf.tz_md5);
        }
        else if (keySend.tz_type == TIME_ZONE_DATA)
        {
            if (IsDevSupportTzData(deviceSetting.SWVer, deviceSetting.fun_bit))
            {
                Snprintf(keySendMsg.tz_data_url, sizeof(keySendMsg.tz_data_url), tz_data_url.c_str());
                Snprintf(keySendMsg.tz_data_md5, sizeof(keySendMsg.tz_data_md5), gstAKCSConf.tz_data_md5);
            }/* 对于旧的就没必要下发了 因为tzdata文件没有区分新旧，这时候下载了新的md5值校验不过相当于没下载
            else
            {
                Snprintf(keySendMsg.tz_data_url, sizeof(keySendMsg.tz_data_url), tz_data_url.c_str());
                Snprintf(keySendMsg.tz_data_md5, sizeof(keySendMsg.tz_data_md5), gstAKCSConf.tz_data_md5_old);
            }*/
        }

        Snprintf(keySendMsg.schedule_md5, sizeof(keySendMsg.schedule_md5), deviceSetting.schedule_md5);
        if (strlen(deviceSetting.schedule_md5) > 0)
        {
            ModifyUrlAddToken(schedule_url);
            Snprintf(keySendMsg.schedule_url, sizeof(keySendMsg.schedule_url), schedule_url.c_str());
        }

        Snprintf(keySendMsg.user_meta_md5, sizeof(keySendMsg.user_meta_md5), deviceSetting.user_meta_md5);
        if (strlen(deviceSetting.user_meta_md5) > 0)
        {
            ModifyUrlAddToken(user_meta_url);
            Snprintf(keySendMsg.user_meta_url, sizeof(keySendMsg.user_meta_url), user_meta_url.c_str());
        }            


        Snprintf(keySendMsg.contact_md5, sizeof(keySendMsg.contact_md5), deviceSetting.contact_md5);
        if (strlen(deviceSetting.contact_md5) > 0)
        {
            ModifyUrlAddToken(contact_url);
            Snprintf(keySendMsg.contact_url, sizeof(keySendMsg.contact_url), contact_url.c_str());
        }

        if (deviceSetting.type == DEVICE_TYPE_INDOOR || deviceSetting.type == DEVICE_TYPE_MANAGEMENT)
        {
            ClearUserKeySend(keySendMsg);            
        }
        
        //测试环境，时区文件更新自动化测试
        if(IsTestServer(gstAKCSConf.cloud_env))
        {
            UpdateTimezoneUrlAndMd5ForAutomationTest(keySend.mac, &keySendMsg);
        }
        
        SOCKET_MSG socket_msg;
        SOCKET_MSG* socket_message = &socket_msg;
        memset(socket_message, 0, sizeof(SOCKET_MSG));
        int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
        if (GetMsgControlInstance()->BuildOfficeKeySendMsg(socket_message, &keySendMsg, ver, deviceSetting.mac) < 0) //mac地址加密
        {
            AK_LOG_WARN << "Build KeySendMsg failed.";
            continue;
        }

        AK_LOG_INFO << "[KeySend] Mac[" << deviceSetting.mac << "], ip:port is:" << conn->remote_addr() << " type:" << deviceSetting.type;
        if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
        {
            AK_LOG_WARN << "Send KeySendMsg failed.";
            continue;
        }
    }
    return 0;
}


int CKeyControl::CheckGivenKeySend()
{
    GIVEN_KEY_SEND keysend;
    std::deque<GIVEN_KEY_SEND> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(given_key_send_mtx_);
        given_deque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        int continue_flag = 0;
        keysend = tmp_deque.front();
        tmp_deque.pop_front();
        for (auto iter = tmp_deque.begin(); iter != tmp_deque.end(); iter++)
        {
            if (keysend.weak_conn.lock() == iter->weak_conn.lock())
            {
                continue_flag = 1;
                break;   //去重，用最后面的更新
            }
        }
        if (continue_flag)
        {
            continue;
        }
        evpp::TCPConnPtr conn(keysend.weak_conn.lock());
        if (!conn) //检查是否还存在
        {
            continue;
        }

        DEVICE_SETTING device_setting;
        memset(&device_setting, 0, sizeof(device_setting));
        if (GetDeviceControlInstance()->GetDeviceSettingByMac(keysend.mac, &device_setting) < 0)
        {
            AK_LOG_WARN << "GetDeviceSettingByMac failed. mac:" << keysend.mac;
        }

        //获取本机IP地址,2017-08-29,后面要改成文件服务器的地址, added by chenyc, 2019-03-14,分布式系统改造,
        //暂时web服务器充当文件服务器(其实描述成是csadapt服务所在的机器更加合理),且只有一台.直接在csmain的配置文件写.
        TCHAR config_ipaddr[256];
        int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
        if (!ipv6)
        {
            std::string config_server_domain = GetConfigServerDomain(keysend.mac);
            Snprintf(config_ipaddr, sizeof(config_ipaddr), config_server_domain.c_str());
            AK_LOG_INFO << "use keysend ipv4 address: " << config_ipaddr;
        }
        else
        {
            snprintf(config_ipaddr, sizeof(config_ipaddr), "[%s]", gstAKCSConf.config_server_ipv6);
            AK_LOG_INFO << "use keysend ipv6 address: " << config_ipaddr;
        }
        
        
        SOCKET_MSG_KEY_SEND keysend_msg;
        memset(&keysend_msg, 0, sizeof(keysend_msg));
        Snprintf(keysend_msg.protocal, sizeof(keysend_msg.protocal) / sizeof(TCHAR), PROTOCAL_NAME_DEFAULT);

        int is_support_tls_high_version = SwitchHandle(device_setting.fun_bit, FUNC_DEV_SUPPORT_DOWNLOAD_TLS12);
        std::string config_server = GetConfigDownloadServer(config_ipaddr, is_support_tls_high_version);
        std::string url = config_server + keysend.file_path;
        ModifyUrlAddToken(url);
        
        if (keysend.type == DEV_FILE_CHANGE_NOTIFY_USER_INFO)
        {
            Snprintf(keysend_msg.user_info_md5, sizeof(keysend_msg.user_info_md5), keysend.file_md5);
            Snprintf(keysend_msg.user_info_url, sizeof(keysend_msg.user_info_url), url.c_str());
        }
        keysend_msg.keysend_type = keysend.type;
        
        SOCKET_MSG socket_msg;
        SOCKET_MSG* p_socket_msg = &socket_msg;
        memset(p_socket_msg, 0, sizeof(SOCKET_MSG));
        int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
        if (GetMsgControlInstance()->BuildGivenKeySendMsg(p_socket_msg, &keysend_msg, ver, keysend.mac) < 0) //mac地址加密
        {
            AK_LOG_WARN << "Build KeySendMsg failed.";
            continue;
        }

        AK_LOG_INFO << "[KeySend] Mac[" << keysend.mac << "], ip:port is:" << conn->remote_addr() << " type:" << keysend.type << " traceid:" << keysend.traceid;
        if (GetDeviceControlInstance()->SendTcpMsg(conn, p_socket_msg->data, p_socket_msg->size) < 0)
        {
            AK_LOG_WARN << "Send KeySendMsg failed.";
            continue;
        }
    }
    return 0;
}


//add by chenzhx 20200927 设备端到5.4版本 只要md5值是空，那么就好通知设备清空对应的配置
void CKeyControl::PerDelDevKeySend(const evpp::TCPConnPtr& conn, const std::string& mac)
{
    //获取本机IP地址,2017-08-29,后面要改成文件服务器的地址
    char config_server_addr[64];

    int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
    if (!ipv6)
    {
        snprintf(config_server_addr, sizeof(config_server_addr), "%s", gstAKCSConf.config_server_ipv4);
    }
    else
    {
        snprintf(config_server_addr, sizeof(config_server_addr), "%s", gstAKCSConf.config_server_ipv6);
    }

    //根据联动单元+设备mac地址组织消息并发给设备,通知设备来更新最新文件
    SOCKET_MSG_KEY_SEND keySendMsg;
    memset(&keySendMsg, 0, sizeof(keySendMsg));
    Snprintf(keySendMsg.protocal, sizeof(keySendMsg.protocal), PROTOCAL_NAME_DEFAULT);

    std::string config_url = GetPerDelDevDownloadWebConfPath(config_server_addr);
    ModifyUrlAddToken(config_url);
    Snprintf(keySendMsg.config_url, sizeof(keySendMsg.config_url), config_url.c_str());
    Snprintf(keySendMsg.config_md5, sizeof(keySendMsg.config_md5), "48acc743846aeb97c19291d32ab1a23f");

    std::string contact_url = GetPerDelDevDownloadWebContactPath(config_server_addr);
    ModifyUrlAddToken(contact_url);
    Snprintf(keySendMsg.contact_url, sizeof(keySendMsg.contact_url), contact_url.c_str());
    Snprintf(keySendMsg.contact_md5, sizeof(keySendMsg.contact_md5), "a87b7a6864442138d05bd2c4fa079e63");

    std::string face_url = GetPerDelDevDownloadWebFacePath(config_server_addr);
    ModifyUrlAddToken(face_url);
    Snprintf(keySendMsg.face_url, sizeof(keySendMsg.face_url), face_url.c_str());
    Snprintf(keySendMsg.face_md5, sizeof(keySendMsg.face_md5), "2b0b290a13bc2095e036bdc15ab4395b");

    std::string rfcard_url = GetPerDelDevDownloadWebRfcardPath(config_server_addr);
    ModifyUrlAddToken(rfcard_url);
    Snprintf(keySendMsg.rf_id_url, sizeof(keySendMsg.rf_id_url), rfcard_url.c_str());
    Snprintf(keySendMsg.rf_id_md5, sizeof(keySendMsg.rf_id_md5), "2b0b290a13bc2095e036bdc15ab4395b");

    std::string private_key_url = GetPerDelDevDownloadWebPrivateKeyPath(config_server_addr);
    ModifyUrlAddToken(private_key_url);
    Snprintf(keySendMsg.private_key_url, sizeof(keySendMsg.private_key_url), private_key_url.c_str());
    Snprintf(keySendMsg.private_key_md5, sizeof(keySendMsg.private_key_md5), "2b0b290a13bc2095e036bdc15ab4395b");

    std::string user_meta_url = GetPerDelDevDownloadWebUserPath(config_server_addr);
    ModifyUrlAddToken(user_meta_url);
    Snprintf(keySendMsg.user_meta_url, sizeof(keySendMsg.user_meta_url), user_meta_url.c_str());
    Snprintf(keySendMsg.user_meta_md5, sizeof(keySendMsg.user_meta_md5), "3ca8a053d7012f15f77bc8f3a7e08f59");

    std::string schedule_url = GetPerDelDevDownloadWebScheduelPath(config_server_addr);
    ModifyUrlAddToken(schedule_url);
    Snprintf(keySendMsg.schedule_url, sizeof(keySendMsg.schedule_url), schedule_url.c_str());
    Snprintf(keySendMsg.schedule_md5, sizeof(keySendMsg.schedule_md5), "3ca8a053d7012f15f77bc8f3a7e08f59"); 

    SOCKET_MSG socket_msg;
    SOCKET_MSG* socket_message = &socket_msg;
    memset(socket_message, 0, sizeof(SOCKET_MSG));
    int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
    if (GetMsgControlInstance()->BuildKeySendMsg(socket_message, &keySendMsg, ver, mac) < 0)
    {
        AK_LOG_WARN << "Build personal KeySendMsg failed.";
        return ;
    }

    AK_LOG_INFO << "[DelKeySend] Mac[" << mac << "], ip:port is:" << conn->remote_addr();
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "Send KeySendMsg failed.";
        return ;
    }
}


//处理定时器
int CKeyControl::ProcessBaseTimer()
{
    CheckKeySend();
    //个人终端用户
    CheckPersonalKeySend();

    CheckGivenKeySend();
    CheckOfficeKeySend();
    return 0;
}

