#ifndef __KEY_CONTROL_H__
#define __KEY_CONTROL_H__
#include <mutex>
#include <deque>
#include <string>


//如果csconfig过来的keysend没有判断类型，那么不会发时区的md5，导致第一次添加的设备不会下载到最新的时区文件
extern const std::list<int> g_timezone_xml_list;
extern const std::list<int> g_timezone_tzdata_list;
//因为其他phone没有实现，这样就会导致dclient每次重连都会下载时区文件
extern const std::list<int> g_timezone_tzdata_support_list;
class CKeyControl
{
public:
    CKeyControl();
    ~CKeyControl();

    static CKeyControl* GetInstance();

    //增加keysend消息到数据库中
    int AddKeySend(const KEY_SEND& KeySend);

    int AddOfficeKeySend(const KEY_SEND& KeySend);

    //增加keysend消息到队列中
    int AddPersonalKeySend(const PERSONAL_KEY_SEND& KeySend);
    int AddGivenKeySend(const GIVEN_KEY_SEND& keysend);

    //检查是否有需要发送出去的KEY消息
    int CheckKeySend();
    int CheckGivenKeySend();
    int CheckPersonalKeySend();
    int CheckOfficeKeySend();
    //发送给被管理员从界面上删除的设备,里面的配置文件配置项都是空的.这样设备自然会注销掉sip的一切与原
    //联动系统相关的信息
    void PerDelDevKeySend(const evpp::TCPConnPtr& conn, const std::string& mac);

    //处理定时器
    int ProcessBaseTimer();
    void ClearUserKeySend(SOCKET_MSG_KEY_SEND& keysend);
    //检查设备是否支持tz_data
    bool IsDevSupportTzData(const std::string& sw_ver, int func_bit);
    void UpdateTimezoneUrlAndMd5ForAutomationTest(const std::string& mac, SOCKET_MSG_KEY_SEND* pSendKeyMsg);

private:
    static CKeyControl* instance;
    std::mutex commKeySendMtx_;   //社区用户的锁
    std::deque<KEY_SEND> comm_deque_; //社区用户的待发送keysend

    std::mutex keySendMtx_;   //个人终端用户的锁
    std::deque<PERSONAL_KEY_SEND> deque_; //个人终端用户的待发送keysend

    std::mutex given_key_send_mtx_;   //特定的
    std::deque<GIVEN_KEY_SEND> given_deque_; //特定的key

    std::mutex officeKeySendMtx_;   //office
    std::deque<KEY_SEND> office_deque_; //office待发送keysend    
};

CKeyControl* GetKeyControlInstance();

#endif
