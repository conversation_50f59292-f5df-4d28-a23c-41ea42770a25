//
//  LogicSerInfo.h
//  路由服务器下挂接的逻辑服务器
//
//  Created by chenyc on 2017-11-15.
//  Copyright (c) 2017年 chenyc. All rights reserved.
//

#ifndef __CSROUTE_SERVER_LOGICSER_INFO_H__
#define __CSROUTE_SERVER_LOGICSER_INFO_H__

#include <iostream>
#include <set>
#include <map>
#include <evpp/tcp_conn.h>
#include <evpp/tcp_server.h>
#include <evpp/any.h>

class CLogicSerInfo
{
public:
    explicit CLogicSerInfo(const std::string& logic_ser_uid)
    {
        logic_ser_uid_ = logic_ser_uid;
    }
    ~CLogicSerInfo()
    {}
    int type() const
    {
        return types_;
    }
    void SetType(int t)
    {
        types_ = t;
    }

private:
    int types_ = csbase::CSLOGIC_NONE;
    std::string logic_ser_uid_;
};

#endif //__CSROUTE_SERVER_LOGICSER_INFO_H__
