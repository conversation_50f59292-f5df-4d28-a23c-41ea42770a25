#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <sys/msg.h>
#include "Main2ResidHandle.h"
#include "AkLogging.h"
#include "AkcsPduBase.h"
#include <evpp/tcp_conn.h>
#include "csmainserver.h"
#include "SnowFlakeGid.h"
#include "PushClientMng.h"
#include "MsgControl.h"
#include "CsmainAES256.h"
#include "DeviceControl.h"
#include "ThreadLocalSingleton.h"
#include "util.h"
#include "DclientMsgDef.h"
#include "MetricService.h"

extern AccessServer* g_accSer_ptr;

Main2ResidMsgHandle* Main2ResidMsgHandle::instance_ = nullptr;
const size_t kResid2MainMsgKeyStart = 8500;
const size_t kOffice2MainMsgKeyStart = 8600;
const size_t kMqueue_number = 2;

Main2ResidMsgHandle* Main2ResidMsgHandle::Instance()
{
    if (!instance_)
    {
        instance_ = new Main2ResidMsgHandle(kMqueue_number);
    }
    return instance_;
}

Main2ResidMsgHandle::Main2ResidMsgHandle(int mqueue_num)
{
    mqueue_num_ = mqueue_num;
    Init();
}
void Main2ResidMsgHandle::Start()
{
    for(auto &id : consumer_msg_id_)
    {
        std::thread thread1 = std::thread(&Main2ResidMsgHandle::InnerRecvThread, this, id);
        thread1.detach();
    }
    
    for(auto &id : office_consumer_msg_id_)
    {
        std::thread thread1 = std::thread(&Main2ResidMsgHandle::InnerRecvThread, this, id);
        thread1.detach();
    }

}
void Main2ResidMsgHandle::Init()
{
    int i = 0;
    for(i = 0; i < mqueue_num_; i++)
    {
        //这个和resid相反
        size_t msgid = kResid2MainMsgKeyStart + i * 2;
        int id = ::msgget((key_t)msgid, 0666 | IPC_CREAT);
        if (id == -1)
        {
            AK_LOG_FATAL << "Init error, key " << msgid;
        }
        consumer_msg_id_.push_back(id);
        AK_LOG_INFO << "Init consumer mqueue, key " << msgid << " ret:" << id;
        
        msgid = kResid2MainMsgKeyStart + (i * 2 + 1);
        id = ::msgget((key_t)msgid, 0666 | IPC_CREAT);
        if (id == -1)
        {
            AK_LOG_FATAL << "Init error, key " << msgid;
        }
        produce_msg_id_.push_back(id);
        AK_LOG_INFO << "Init produce mqueue, key " << msgid << " ret:" << id;


        //这个和office相反
        msgid = kOffice2MainMsgKeyStart + i * 2;
        id = ::msgget((key_t)msgid, 0666 | IPC_CREAT);
        if (id == -1)
        {
            AK_LOG_FATAL << "Init error, key " << msgid;
        }
        office_consumer_msg_id_.push_back(id);
        AK_LOG_INFO << "Init consumer mqueue, key " << msgid << " ret:" << id;
        
        msgid = kOffice2MainMsgKeyStart + (i * 2 + 1);
        id = ::msgget((key_t)msgid, 0666 | IPC_CREAT);
        if (id == -1)
        {
            AK_LOG_FATAL << "Init error, key " << msgid;
        }
        office_produce_msg_id_.push_back(id);
        AK_LOG_INFO << "Init produce mqueue, key " << msgid << " ret:" << id;        
    }

}


void Main2ResidMsgHandle::CreateMsg(const std::string& client, const csmain::DeviceType type, bool conn_change, 
const char *data, int size, MsgStruct &msg)
{
    msg.client_type = type;
    msg.conn_change = conn_change;

    msg.traceid = ThreadLocalSingleton::GetInstance().GetTraceID();    
    Snprintf(msg.client, sizeof(msg.client), client.c_str());
    msg.msg_len = size;
    ::memcpy(&msg.msg_data, data, (uint32_t)size <= sizeof(msg.msg_data) ? size : sizeof(msg.msg_data));
    if ((uint32_t)size > sizeof(msg.msg_data))
    {
        AK_LOG_WARN << "Send to mq sizeof too big!  size:" << size;
    }
   
}


void Main2ResidMsgHandle::OfficeSend(const std::string& client, const csmain::DeviceType type, bool conn_change, const char *data, int size)
{
    MsgStruct msg = {csmain::COMMUNITY_NONE};

    CreateMsg(client, type, conn_change, data, size, msg);

    int hash = StrHash(client, mqueue_num_);
    int msg_id = office_produce_msg_id_[hash];

    AK_LOG_INFO << "Main2OfficeMsgHandle send msg. mqueue id: " << msg_id;    
    InnerSend(msg_id, (char *)&msg, sizeof(msg));    
}


void Main2ResidMsgHandle::Send(const std::string& client, const csmain::DeviceType type, bool conn_change, const char *data, int size)
{
    MsgStruct msg = {csmain::COMMUNITY_NONE};

    CreateMsg(client, type, conn_change, data, size, msg);

    int hash = StrHash(client, mqueue_num_);
    int msg_id = produce_msg_id_[hash];

    AK_LOG_INFO << "Main2ResidMsgHandle send msg. mqueue id: "<< msg_id;    
    InnerSend(msg_id, (char *)&msg, sizeof(msg));    
}

void Main2ResidMsgHandle::InnerSend(int msg_id, const char *data, int size)
{
    if(uint32_t(size) > MsgHelper::kMsgSize)
    {
        AK_LOG_WARN << "Msg size is larger than MsgHelper::kMsgSize, size " << size;
        return;
    }
    MsgHelper helper_send = {0};
    helper_send.msg_type = 1; //固定都是1
    ::memcpy(helper_send.data, data, size);
    if (::msgsnd(msg_id, (void *)&helper_send, sizeof(helper_send.data), IPC_NOWAIT) == -1)//IPC_NOWAIT 保证是非阻塞的
    {
        AK_LOG_WARN << "The msgsnd failed, erron is " << errno << " mqueue id:" << msg_id;

        // 发送失败计数 ++
        MetricService* metric_service = MetricService::GetInstance();
        if(metric_service) {
            metric_service->AddValue("csmain_linux_msgsnd_failed_count", 1);
        }
    }
    return;
}

//起线程,处理从csmain传回的消息
void Main2ResidMsgHandle::InnerRecvThread(int msg_id)
{
    MsgHelper helper_recv = {0};
    long msg_type = 1; //固定都是1

    while(1)
    {
        ssize_t msg_recv_size = ::msgrcv(msg_id, (void *)&helper_recv, sizeof(helper_recv.data), msg_type, 0);
        if (msg_recv_size == -1)
        {
            // 接收失败计数 ++
            MetricService* metric_service = MetricService::GetInstance();
            if(metric_service) {
                metric_service->AddValue("csmain_linux_msgrcv_failed_count", 1);
            }
            
            AK_LOG_WARN << "The msgsnd failed, erron is " << errno;
            continue;
        }
        
        MsgStruct* resid_msg = (MsgStruct*)helper_recv.data;
        ThreadLocalSingleton::GetInstance().SetTraceID(resid_msg->traceid);
        AK_LOG_INFO << "Main2ResidMsgHandle recv msg, mqueue id = "<< msg_id << ", send_type = " << resid_msg->send_type 
                    << ", client_type = " << resid_msg->client_type <<", client = " << resid_msg->client << ", msg_id = " << resid_msg->msg_id;

        //在线、离线的 加密、发送 统一在csmain处理
        if(resid_msg->send_type != TransP2PMsgType::TRAN_TYPE_NONE)
        {
            SendMsgHandle(resid_msg);
            continue;
        }
        
        if (resid_msg->client_type == csmain::DeviceType::COMMUNITY_DEV
            || resid_msg->client_type == csmain::DeviceType::PERSONNAL_DEV
            || resid_msg->client_type == csmain::DeviceType::OFFICE_DEV)
        {
            /*
            if (resid_msg->msg_id == MSG_FROM_DEVICE_REQUEST_OPENDOOR) {
                SOCKET_MSG socket_msg;
                SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
                char* pay_load = (char*)msg_normal->data;
                ::snprintf(pay_load, sizeof(msg_normal->data), "%s", resid_msg->msg_data);
                uint32_t data_size = strlen(pay_load);
                AesEncryptByMacV2(pay_load, pay_load, resid_msg->client, &data_size, sizeof(msg_normal->data));
                GetMsgControlInstance()->BuildNormalMsgHeader(&socket_msg, resid_msg->msg_id, VERSION_2_0, data_size);

                // 获取家居锁信息，实际设备未上云
                DEVICE_SETTING deviceSetting;
                memset(&deviceSetting, 0, sizeof(deviceSetting));
                if (GetDeviceControlInstance()->GetDeviceSettingByMac(resid_msg->client, &deviceSetting) < 0)
                {
                    AK_LOG_WARN << "MSG_FROM_DEVICE_REQUEST_OPENDOOR failed. mac:" << resid_msg->client;
                    continue;
                }

                // 触发远程开门
                SOCKET_MSG_DEV_REQUEST_OPEN request_open;
                memset(&request_open, 0, sizeof(request_open));
                std::string open_door_type;
                if (0 > GetMsgControlInstance()->GetParseRequestOpenData(msg_normal, deviceSetting, request_open, open_door_type)) {
                    continue;
                }
                GetMsgControlInstance()->SendDevRequestOpen(request_open, deviceSetting, open_door_type);
                continue;
            }
            */
           
            evpp::TCPConnPtr conn;
            if (g_accSer_ptr->GetDevConnByMac(resid_msg->client, conn) != 0)
            {
                AK_LOG_INFO << "GetDevConnByMac failed, mac = " << resid_msg->client;
                continue;
            }

            if (GetDeviceControlInstance()->SendTcpMsg(conn, (unsigned char*)resid_msg->msg_data, resid_msg->msg_len) < 0)
            {
                AK_LOG_WARN << "SendTcpMsg failed, mac = " << resid_msg->client << ", ip:port = " << conn->remote_addr();
                continue;
            }    
        }
        else if (resid_msg->client_type == csmain::DeviceType::COMMUNITY_APP
            || resid_msg->client_type == csmain::DeviceType::PERSONNAL_APP
            || resid_msg->client_type ==  csmain::DeviceType::OFFICE_APP)
        {
            evpp::TCPConnPtr conn;
            if (g_accSer_ptr->GetDevConnByUid(resid_msg->client, conn) != 0)
            {
                AK_LOG_INFO << "GetDevConnByUid failed, uid = " << resid_msg->client;
                continue;
            }

            if (GetDeviceControlInstance()->SendTcpMsg(conn, (unsigned char*)resid_msg->msg_data, resid_msg->msg_len) < 0)
            {
                AK_LOG_WARN << "SendTcpMsg failed, , uid = " << resid_msg->client << ", ip:port = " << conn->remote_addr();
                continue;
            }             
        }
    }
}


void Main2ResidMsgHandle::SendMsgHandle(MsgStruct* msg)
{
    evpp::TCPConnPtr conn; 
    int online = 1;
    if(msg->send_type == TransP2PMsgType::TO_DEV_MAC)
    {
        if (g_accSer_ptr->GetDevConnByMac(msg->client, conn) != 0)
        {
           online = 0; 
           AK_LOG_INFO << "SendMsgHandle send msg to dev, dev is offline, mac = " << msg->client << ", trace_id = " << msg->traceid;
        }
    }
    else if(msg->send_type == TransP2PMsgType::TO_DEV_UUID)
    {
        if (g_accSer_ptr->GetDevConnByUUID(msg->client, conn) != 0)
        {
            online = 0;
        }
    }
    //两种send_type，msg->client在后端业务都转为主站点account了
    else if(msg->send_type == TransP2PMsgType::TO_APP_UID || msg->send_type == TransP2PMsgType::TO_APP_UUID)
    {
        if (g_accSer_ptr->GetDevConnByUid(msg->client, conn) != 0)
        {
            online = 0;
            AK_LOG_INFO << "SendMsgHandle send msg to app, app is offline, account = " << msg->client << ", trace_id = " << msg->traceid;
        }
    }
    else if (msg->send_type == TransP2PMsgType::TO_APP_UID_ONLINE)
    {
        if (g_accSer_ptr->GetDevConnByUid(msg->client, conn) != 0)
        {
            AK_LOG_WARN << "APP Online Msg Send failed, conn not found, account = " << msg->client << ", trace_id = " << msg->traceid;
            return;
        }
    }
    
    else
    {
        AK_LOG_WARN << "backend send type no support, type: " << msg->send_type << ", client = " << msg->client << ", trace_id = " << msg->traceid;
        online = 0;
    }
    
    //在线且连接存在
    if(online && conn)
    {
        SOCKET_MSG socket_msg;    
        SOCKET_MSG dy_iv_socket_msg;
        SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
        char* pay_load = (char*)msg_normal->data;
        ::snprintf(pay_load, sizeof(msg_normal->data), "%s", msg->msg_data);
        uint32_t data_size = strlen(pay_load);

        if(msg->enc_type == MsgEncryptType::TYEP_MAC_ENCRYPT)
        {
            AesEncryptByMacV2(pay_load, pay_load, msg->client, &data_size, sizeof(msg_normal->data)); 
            GetMsgControlInstance()->BuildNormalMsgHeader(&socket_msg,  msg->msg_id, VERSION_2_0, data_size);
            socket_msg.size = data_size + SOCKET_MSG_NORMAL_HEADER_SIZE;
            GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size); 
        }
        else if(msg->enc_type == MsgEncryptType::TYEP_DEFAULT_ENCRYPT)
        {
            GetMsgControlInstance()->EncryptDefalutMsg(socket_msg, dy_iv_socket_msg, msg->msg_id);            
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socket_msg, dy_iv_socket_msg);
        }
        else if(msg->enc_type == MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT)
        {
            GetMsgControlInstance()->EncryptDefalutMacMsg(socket_msg, dy_iv_socket_msg, msg->msg_id);    
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socket_msg, dy_iv_socket_msg);
        }

        AK_LOG_INFO << "SendMsgHandle send online msg, client = " << msg->client << ", msg_id = " << msg->msg_id << ", trace_id = " << msg->traceid;
    }

    // 离线
    if((!online || msg->force_push))
    {
        if(msg->push_msg_len != 0)
        {
            PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
            if (push_cli_ptr)
            {
                push_cli_ptr->PushMsg(msg->push_msg_data);
            }
            
            AK_LOG_INFO << "SendMsgHandle send offline msg, force_push = " << msg->force_push << ", client = " << msg->client << ", msg_id = " << msg->msg_id << ", trace_id = " << msg->traceid;
        }
        else
        {
            AK_LOG_WARN << "SendMsgHandle send offline msg, msg_len = 0 not send, force_push = " << msg->force_push << ", client = " << msg->client << ", msg_id = " << msg->msg_id << ", trace_id = " << msg->traceid;
        }
   }

    return;
}
