#ifndef __CSResid_2_MAIN_HANDLE_H__
#define __CSResid_2_MAIN_HANDLE_H__

#include <boost/noncopyable.hpp>
#include "AkcsCommonDef.h"
#include "backend/MsgStruct.h"
#include <thread>

//用于csResid与csmain的消息通信管理
class Main2ResidMsgHandle: public boost::noncopyable
{
public:
    Main2ResidMsgHandle(int mqueue_num);
    ~Main2ResidMsgHandle(){}
    static Main2ResidMsgHandle* Instance();
    void Init();
    void Start();
    void Send(const std::string& client, const csmain::DeviceType type, bool isipv6, const char *data, int size);
    void OfficeSend(const std::string& client, const csmain::DeviceType type, bool isipv6, const char *data, int size);
    
private:
    void InnerSend(int msg_id, const char *data, int size);
    void InnerRecvThread(int msg_id);
    void CreateMsg(const std::string& client, const csmain::DeviceType type, bool conn_change, const char *data, int size, MsgStruct &msg);
    void SendMsgHandle(MsgStruct* msg);
    
private:
    static Main2ResidMsgHandle* instance_;
    std::thread msg_recv_handle_thread_;

    int mqueue_num_;
    //原则上,只要区分消息类型 mtype,就可以在一个队列里面实现全双工，但是考虑到后面用ipc命令管理队列的方便(例如清空消息等)，还是拆成两个队列进行管理    
    std::vector<size_t> produce_msg_id_;
    std::vector<size_t> consumer_msg_id_;

    std::vector<size_t> office_produce_msg_id_;
    std::vector<size_t> office_consumer_msg_id_;  
};

#endif // __CSResid_2_MAIN_HANDLE_H__
