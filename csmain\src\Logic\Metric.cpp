#include "Metric.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "RouteClientMng.h"
#include "ConnectionPool.h"
#include "NotifyMsgControl.h"
#include "ConfigFileReader.h"
#include "BusinessThreadPool.h"
extern CAkEtcdCliManager* g_etcd_cli_mng;
#define VERSION_CONF_FILE "/usr/local/akcs/csmain/conf/version.conf"

extern BusinessThreadPool g_business_pool;

void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csmain_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "logdb_get_conn_failed_count",
        "LOGDB GetConnection failed count",
        "csmain_logdb_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csmain_linux_msgsnd_failed_count",
        "csmain linux msgsnd failed count",
        "csmain_linux_msgsnd_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csmain_linux_msgrcv_failed_count",
        "csmain linux msgrcv failed count",
        "csmain_linux_msgrcv_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csmain_notify_queue_length",
        " The length of csmain notify queue",
        "csmain_notify_queue_length",
        []() -> long { return (long)(GetNotifyMsgControlInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "csmain_http_req_queue_length",
        " The length of csmain http req queue",
        "csmain_http_req_queue_length",
        []() -> long { return (long)(GetHttpReqMsgControlInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "csmain_motion_queue_length",
        "The length of csmain motion queue",
        "csmain_motion_queue_length",
        []() -> long { return (long)(GetMotionMsgControlInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "csmain_rtsp_queue_length",
        " The length of csmain rtsp queue",
        "csmain_rtsp_queue_length",
        []() -> long { return (long)(GetRtspMsgControlInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "csmain_redis_check_error",
        "redis server status",
        "csmain_redis_check_error",
        []() -> long { return (long)(CacheManager::getInstance()->CheckRedisNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csmain_db_conn_check_error",
        "db conn status",
        "csmain_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csmain_etcd_check_error",
        "etcd server status",
        "csmain_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "RpcCallFrequency_QueryUidStatus_total",
        "beanstalk server status",
        "RpcCallFrequency_QueryUidStatus_total",
        nullptr
    );
    metric_service->AddMetric(
        "csmain_tcp_conn_total",
        "access server tcp connection count",
        "csmain_tcp_conn_total",
        nullptr
    );

    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );

    metric_service->AddMetric(
        "csmain_dev_msg_queue_max_count",
        "dev message queue max count",
        "csmain_dev_msg_queue_max_count",
        []() -> long { return (long)g_business_pool.MaxQueueSize(); }
    );

    std::vector<uint32_t> csmain_devmsg_handle_latencies = {
        1000,2000,3000,4000,5000,6000,7000,8000,9000
    };
    
    //具体消息的处理时间
    MetricLatencyPtr csmain_devmsg_only_handle_latency = std::make_shared<MetricLatency>(csmain_devmsg_handle_latencies, "queue_handle_latency");
    metric_service->AddLatencyMetric(
        "queue_handle_latency",
        csmain_devmsg_only_handle_latency
    );

    //从接收到处理完的时间
    std::vector<uint32_t> csmain_devmsg_all_handle_latencies = {
        1000,2000,3000,4000,5000,6000,7000,8000,9000
    };

    MetricLatencyPtr csmain_devmsg_all_handle_latency = std::make_shared<MetricLatency>(csmain_devmsg_all_handle_latencies, "devmsg_handle_latency");
    metric_service->AddLatencyMetric(
        "devmsg_handle_latency",
        csmain_devmsg_all_handle_latency
    );

}
