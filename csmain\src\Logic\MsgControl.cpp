#include <tuple>
#include <ctime>
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "Md5.h"
#include "util.h"
#include "util_string.h"
#include "stdafx.h"
#include "MsgControl.h"
#include "MsgHandle.h"
#include "Utility.h"
#include "SDMCMsg.h"
#include "InnerUtil.h"
#include "DeviceControl.h"
#include "NotifyMsgControl.h"
#include "PersonnalAlarm.h"
#include "PersonnalDeviceSetting.h"
#include "KeyControl.h"
#include "csmainserver.h"
#include <evpp/any.h>
#include "CsmainAES256.h"
#include "CachePool.h"
#include "AlarmControl.h"
#include "AkcsWebMsgSt.h"
#include "PushClient.h"
#include "AppPushToken.h"
#include "rpc_client.h"
#include "PersonalDevices.h"
#include "CliServer.h"
#include "DeviceSetting.h"
#include "AKDevMng.h"
#include "NodeTimeZone.h"
#include "PersonalAccount.h"
#include "doorlog/UserInfo.h"
#include "session_rpc_client.h"
#include "RouteMqProduce.h"
#include "AkcsPduBase.h"
#include "RouteClient.h"
#include "AkcsOemDefine.h"
#include "PersonnalTmpKey.h"
#include "AkcsServer.h"
#include "XmlTagDefine.h"
#include "AkcsMonitor.h"
#include "AuditLog.h"
#include "AppCallStatus.h"
#include "DevOnlineMng.h"
#include "AkcsMonitor.h"
#include "SnowFlakeGid.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"
#include "DevUpdateUserLog.h"
#include "AkcsHttpRequest.h"
#include "PersonalDevices.h"
#include "EventFilterInterface.h"
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"
#include "PushClientMng.h"
#include "AK.Linker.pb.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "csvs.grpc.pb.h"
#include "dbinterface/Shadow.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Sip.h"
#include "dbinterface/ThirdParty.h"
#include "dbinterface/Message.h"
#include "dbinterface/AppCallDndDB.h"
#include "dbinterface/ErrorConnectDB.h"
#include "Main2ResidHandle.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/PersonalAccountSingleInfo.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/DormakabaLock.h"
#include "dbinterface/SaltoLock.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/CommunityRoom.h"
#include "InnerUtil.h"
#include "dbinterface/AmenityReservation.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityRoom.h"
#include "BookingTmpkeyHandler.h"
#include "redis/SafeCacheConn.h"
#include "dbinterface/SL20Lock.h"
#include "util_relay.h"
#include "util_judge.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityInfo.h"
#include "MsgIdToMsgName.h"
#include "dbinterface/UUID.h"
#include "dbinterface/ITecLock.h"
#include "dbinterface/TtLock.h"


extern AccessServer* g_accSer_ptr;
extern CliServer* g_cliSer_prt;
extern SmRpcClient* g_sm_client_ptr;
extern std::string g_logic_srv_id;
extern RouteMQProduce* g_nsq_producer;
extern AKCS_CONF gstAKCSConf; //全局配置信息
extern VideoStorageClient* g_vs_client_ptr;
extern std::map<string, AKCS_DST> g_time_zone_DST;
extern akcs::CEventFilterInterface *g_event_filter;
extern const char* g_redis_db_userdetail;
extern const char *g_redis_db_appstat;
extern const char *g_redis_db_dev_outerip;
extern const char *g_redis_db_weather;
extern LOG_DELIVERY gstAKCSLogDelivery;
extern const char *g_redis_db_backend_limiting;
extern const char *g_redis_db_sl20_lock;



CMsgControl* GetMsgControlInstance()
{
    return CMsgControl::GetInstance();
}

CMsgControl::CMsgControl()
{

}

CMsgControl::~CMsgControl()
{

}

CMsgControl* CMsgControl::instance = NULL;

CMsgControl* CMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgControl();
    }

    return instance;
}

int CMsgControl::BuildNormalMsgHeader(SOCKET_MSG* socket_message, uint16_t message_id, int ver, uint32_t data_size)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    msg_normal->magic[0] = SOCKET_MSG_MAGIC_MSB;
    msg_normal->magic[1] = SOCKET_MSG_MAGIC_LSB;
    msg_normal->message_id = message_id & SOCKET_MSG_ID_MASK;  //放空版本号
    if (ver == VERSION_1_0)
    {
        msg_normal->message_id = message_id | SOCKET_MSG_VERSION_01;  //不加密
    }
    else
    {
        msg_normal->message_id = message_id | SOCKET_MSG_VERSION_02;
    }
    msg_normal->head_size = HTONS(SOCKET_MSG_NORMAL_HEADER_SIZE);
    msg_normal->data_size = HTONS(data_size);

    return 0;
}


int CMsgControl::ParseReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_REPORT_STATUS* report_status_message, uint32_t data_size, uint32_t version)
{
    if ((pNormalMsg == NULL) || (report_status_message == NULL))
    {
        return -1;
    }

    char* payload = (char*)pNormalMsg->data;

    if (GetMsgHandleInstance()->ParseReportStatusMsg(payload, report_status_message, data_size, version) < 0)
    {
        return -1;
    }

    return 0;
}


int CMsgControl::processPersonnalAlarmMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_ALARM& alarmMsg, const DEVICE_SETTING& deviceSetting)
{
    //写入数据库
    PERSONNAL_ALARM personnal_alarm;
    memset(&personnal_alarm, 0, sizeof(personnal_alarm));
    Snprintf(personnal_alarm.community, sizeof(personnal_alarm.community), deviceSetting.community);
    Snprintf(personnal_alarm.device_node, sizeof(personnal_alarm.device_node), deviceSetting.device_node);
    Snprintf(personnal_alarm.alarm_type, sizeof(personnal_alarm.alarm_type), alarmMsg.type);
    Snprintf(personnal_alarm.mac, sizeof(personnal_alarm.mac), deviceSetting.mac);  //2017-11-02,增加告警设备的mac地址
    personnal_alarm.extension = deviceSetting.extension;
    personnal_alarm.status = ALARM_STATUS_UNDEALED;
    personnal_alarm.alarm_code = alarmMsg.alarm_code;
    personnal_alarm.alarm_zone = alarmMsg.alarm_zone;
    personnal_alarm.alarm_location = alarmMsg.alarm_location;
    personnal_alarm.alarm_customize = alarmMsg.alarm_customize;
    std::string NodeTime = getNodeCurrentTimeString(deviceSetting.device_node);
    //Snprintf(personnal_alarm.alarm_time, sizeof(personnal_alarm.alarm_time), GetCurTime().GetBuffer());
    Snprintf(personnal_alarm.alarm_time, sizeof(personnal_alarm.alarm_time), NodeTime.c_str());
    uint64_t trace_id = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    personnal_alarm.trace_id = trace_id;
    //added by chenyc,插入数据库并将最新的自增id查询出来.
    if (GetPersonnalAlarmInstance()->AddAlarm(personnal_alarm) < 0)
    {
        AK_LOG_WARN << "Add personnal Alarm failed.";
        return -1;
    }

    SOCKET_MSG_ALARM_SEND recvAlarmMsg;
    memset(&recvAlarmMsg, 0, sizeof(SOCKET_MSG_ALARM_SEND));
    _tcscpy_s(recvAlarmMsg.protocal, sizeof(recvAlarmMsg.protocal), PROTOCAL_NAME_DEFAULT);

    Snprintf(recvAlarmMsg.type, sizeof(recvAlarmMsg.type), alarmMsg.type);
    Snprintf(recvAlarmMsg.msg_seq, sizeof(recvAlarmMsg.msg_seq), alarmMsg.msg_seq);
    recvAlarmMsg.alarm_code = alarmMsg.alarm_code;
    recvAlarmMsg.alarm_zone = alarmMsg.alarm_zone;
    recvAlarmMsg.alarm_location = alarmMsg.alarm_location;
    recvAlarmMsg.alarm_customize = alarmMsg.alarm_customize;
    //recvAlarmMsg.device_type = deviceSetting.type;
    //recvAlarmMsg.extension = deviceSetting.extension;
    //modify by chenyc,2017-06-02,数据库中的实际id要传给app
    recvAlarmMsg.id = personnal_alarm.id;
    Snprintf(recvAlarmMsg.community, sizeof(recvAlarmMsg.community), deviceSetting.community);
    //联动单元
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address), deviceSetting.device_node);
    Snprintf(recvAlarmMsg.time, sizeof(recvAlarmMsg.time), personnal_alarm.alarm_time);
    Snprintf(recvAlarmMsg.from_local, sizeof(recvAlarmMsg.from_local) / sizeof(TCHAR), deviceSetting.location);
    Snprintf(recvAlarmMsg.mac, sizeof(recvAlarmMsg.mac) / sizeof(TCHAR), deviceSetting.mac);
    recvAlarmMsg.trace_id = personnal_alarm.trace_id;

    //先给对端发送alarm-ack消息
    SOCKET_MSG socketMsg, dy_iv_socket;
    memset(&socketMsg, 0, sizeof(socketMsg));  
    int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
    if (GetMsgControlInstance()->BuildAlarmSendMsg(socketMsg, dy_iv_socket, &recvAlarmMsg, ver) < 0)
    {
        AK_LOG_WARN << "BuildAlarmSendMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socketMsg, dy_iv_socket) < 0)
    {
        AK_LOG_WARN << "send alarm ack failed.";
        return -1;
    }
    //将告警通知消息压入队列中
    //CPersonnalAlarmNotifyMsg cNotifyMsg(recvAlarmMsg, recvAlarmMsg.mac,
    //                                   recvAlarmMsg.from_local, recvAlarmMsg.type);
    //GetNotifyMsgControlInstance()->AddPersonnalAlarmNotifyMsg(cNotifyMsg);
    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupPerAlarmMsg msg;
    msg.set_alarm_type(recvAlarmMsg.type);
    msg.set_address(recvAlarmMsg.address);
    msg.set_time(recvAlarmMsg.time);
    msg.set_msg_seq(recvAlarmMsg.msg_seq);
    msg.set_from_local(recvAlarmMsg.from_local);
    msg.set_mac(recvAlarmMsg.mac);
    msg.set_extension(recvAlarmMsg.extension);
    msg.set_device_type(recvAlarmMsg.device_type);
    msg.set_id(recvAlarmMsg.id);
    msg.set_community(recvAlarmMsg.community);
    msg.set_alarm_code(recvAlarmMsg.alarm_code);
    msg.set_alarm_zone(recvAlarmMsg.alarm_zone);
    msg.set_alarm_location(recvAlarmMsg.alarm_location);
    msg.set_alarm_customize(recvAlarmMsg.alarm_customize);
    msg.set_trace_id(recvAlarmMsg.trace_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_PER_ALARM_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}


int CMsgControl::processCommunityAlarmMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_ALARM& alarmMsg, const DEVICE_SETTING& deviceSetting)
{
    //写入数据库
    ALARM alarm;
    memset(&alarm, 0, sizeof(alarm));
    Snprintf(alarm.device_node, sizeof(alarm.device_node) / sizeof(TCHAR), deviceSetting.device_node);
    Snprintf(alarm.alarm_type, sizeof(alarm.alarm_type) / sizeof(TCHAR), alarmMsg.type);
    alarm.alarm_code = alarmMsg.alarm_code;
    alarm.alarm_location = alarmMsg.alarm_location;
    alarm.alarm_zone = alarmMsg.alarm_zone;
    alarm.alarm_customize = alarmMsg.alarm_customize;

    alarm.status = ALARM_STATUS_UNDEALED;
    Snprintf(alarm.mac, sizeof(alarm.mac) / sizeof(TCHAR), deviceSetting.mac);
    alarm.unit_id = deviceSetting.unit_id;
    alarm.manager_account_id = deviceSetting.manager_account_id;
    std::string NodeTime = getNodeCurrentTimeString(deviceSetting.device_node);
    Snprintf(alarm.alarm_time, sizeof(alarm.alarm_time), NodeTime.c_str());
    //Snprintf(alarm.alarm_time, sizeof(alarm.alarm_time), GetCurTime().GetBuffer());
    uint64_t trace_id = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    alarm.trace_id = trace_id;
    //added by chenyc,插入数据库并将最新的自增id查询出来.
    if (GetAlarmControlInstance()->AddAlarm(&alarm) < 0)
    {
        AK_LOG_WARN << "AddAlarm failed.";
        return -1;
    }

    SOCKET_MSG_ALARM_SEND recvAlarmMsg;
    memset(&recvAlarmMsg, 0, sizeof(SOCKET_MSG_ALARM_SEND));
    _tcscpy_s(recvAlarmMsg.protocal, sizeof(recvAlarmMsg.protocal) / sizeof(TCHAR), PROTOCAL_NAME_DEFAULT);

    Snprintf(recvAlarmMsg.type, sizeof(recvAlarmMsg.type) / sizeof(TCHAR), alarmMsg.type);
    Snprintf(recvAlarmMsg.from_local, sizeof(recvAlarmMsg.from_local) / sizeof(TCHAR), deviceSetting.location);
    Snprintf(recvAlarmMsg.mac, sizeof(recvAlarmMsg.mac) / sizeof(TCHAR), deviceSetting.mac);
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address) / sizeof(TCHAR), deviceSetting.device_node);
    Snprintf(recvAlarmMsg.msg_seq, sizeof(recvAlarmMsg.msg_seq) / sizeof(TCHAR), alarmMsg.msg_seq);
    recvAlarmMsg.device_type = deviceSetting.type;
    recvAlarmMsg.manager_account_id = deviceSetting.manager_account_id;
    recvAlarmMsg.unit_id = deviceSetting.unit_id;
    recvAlarmMsg.grade = deviceSetting.grade;
    //recvAlarmMsg.extension = deviceSetting.extension; // modify by chenzhx
    //modify by chenyc,2017-06-02,数据库中的实际id要传给app
    //recvAlarmMsg.id = 0; //ID暂时没用，统一发0
    recvAlarmMsg.id = alarm.id;
    recvAlarmMsg.alarm_code = alarm.alarm_code;
    recvAlarmMsg.alarm_zone = alarm.alarm_zone;
    recvAlarmMsg.alarm_location = alarm.alarm_location;
    recvAlarmMsg.alarm_customize = alarm.alarm_customize;
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address), deviceSetting.device_node);
    Snprintf(recvAlarmMsg.time, sizeof(recvAlarmMsg.time), alarm.alarm_time);
    recvAlarmMsg.trace_id = alarm.trace_id;
    
    SOCKET_MSG socketMsg, dy_iv_socket;
    memset(&socketMsg, 0, sizeof(SOCKET_MSG));
    int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
    if (GetMsgControlInstance()->BuildAlarmSendMsg(socketMsg, dy_iv_socket, &recvAlarmMsg, ver) < 0)
    {
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socketMsg, dy_iv_socket) < 0)
    {
        return -1;
    }
    //将告警通知消息压入队列中
    ////recvAlarmMsg.type == alarm.alarm_type;recvAlarmMsg.mac==alarm.mac;recvAlarmMsg.from_local==deviceSetting.location
    //CAlarmNotifyMsg cNotifyMsg(recvAlarmMsg);
    //GetNotifyMsgControlInstance()->AddAlarmNotifyMsg(cNotifyMsg);

    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupCommAlarmMsg group_comm_alarm_msg;
    group_comm_alarm_msg.set_alarm_type(recvAlarmMsg.type);
    group_comm_alarm_msg.set_community(recvAlarmMsg.community);
    group_comm_alarm_msg.set_address(recvAlarmMsg.address);
    group_comm_alarm_msg.set_time(recvAlarmMsg.time);
    group_comm_alarm_msg.set_msg_seq(recvAlarmMsg.msg_seq);
    group_comm_alarm_msg.set_mac(recvAlarmMsg.mac);
    group_comm_alarm_msg.set_grade(recvAlarmMsg.grade);
    group_comm_alarm_msg.set_mng_account_id(recvAlarmMsg.manager_account_id);
    group_comm_alarm_msg.set_unit_id(recvAlarmMsg.unit_id);
    group_comm_alarm_msg.set_extension(recvAlarmMsg.extension);
    group_comm_alarm_msg.set_device_type(recvAlarmMsg.device_type);
    group_comm_alarm_msg.set_id(recvAlarmMsg.id);
    group_comm_alarm_msg.set_from_local(recvAlarmMsg.from_local);
    group_comm_alarm_msg.set_alarm_code(recvAlarmMsg.alarm_code);
    group_comm_alarm_msg.set_alarm_zone(recvAlarmMsg.alarm_zone);
    group_comm_alarm_msg.set_alarm_location(recvAlarmMsg.alarm_location);
    group_comm_alarm_msg.set_alarm_customize(recvAlarmMsg.alarm_customize);
    group_comm_alarm_msg.set_alarm_uuid(alarm.uuid);
    group_comm_alarm_msg.set_trace_id(recvAlarmMsg.trace_id);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&group_comm_alarm_msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_COMM_ALARM_REQ);
    pdu2.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);
    return 0;
}
int CMsgControl::processCommunityAlarmDealMsg(SOCKET_MSG_NORMAL* pNormalMsg, const DEVICE_SETTING&  deviceSetting, const evpp::Any& personnalAppSetting, const int type)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }
    SOCKET_MSG_ALARM_DEAL alarm_deal_info;
    memset(&alarm_deal_info, 0, sizeof(alarm_deal_info));
    if (type == csmain::COMMUNITY_APP)
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp(boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(personnalAppSetting));
        Snprintf(alarm_deal_info.area_node, sizeof(alarm_deal_info.area_node), stApp.node);
    }
    else
    {
        Snprintf(alarm_deal_info.area_node, sizeof(alarm_deal_info.area_node), deviceSetting.device_node);
    }
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, AES_KEY_DEFAULT_MAC, data_size);
    }

    if (GetMsgHandleInstance()->ParseAlarmDealMsg(payload, &alarm_deal_info) < 0)
    {
        AK_LOG_WARN << "Parse personnal AlarmDealMsg failed.";
        return -1;
    }

    ::snprintf(alarm_deal_info.device_name, sizeof(alarm_deal_info.device_name), "%s", deviceSetting.location);
    std::string NodeTime = getNodeCurrentTimeString(deviceSetting.device_node);
    ::snprintf(alarm_deal_info.time, sizeof(alarm_deal_info.time), "%s", NodeTime.c_str());
    AK_LOG_INFO << deviceSetting.mac << " put alarm deal. alarmid:" << alarm_deal_info.alarm_id << " user:" << alarm_deal_info.user;
    if (DaoDealAlarmStatus(alarm_deal_info) != 0)
    {
        AK_LOG_WARN << "Deal personnal AlarmStatus failed.";
        return -1;
    }
    SOCKET_MSG_ALARM_DEAL_OFFLINE stCommuityAlarmInfo;
    if (DaoCommGetAlarmInfoById(alarm_deal_info.alarm_id, stCommuityAlarmInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id:" << alarm_deal_info.alarm_id;
    }
    alarm_deal_info.manager_account_id = stCommuityAlarmInfo.manager_account_id;
    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupCommAlarmDealMsg group_comm_alarm_deal_msg;
    group_comm_alarm_deal_msg.set_node(alarm_deal_info.area_node);
    group_comm_alarm_deal_msg.set_alarm_id(alarm_deal_info.alarm_id);
    group_comm_alarm_deal_msg.set_deal_user(alarm_deal_info.user);
    group_comm_alarm_deal_msg.set_deal_result(alarm_deal_info.result);
    group_comm_alarm_deal_msg.set_deal_type(alarm_deal_info.type);
    group_comm_alarm_deal_msg.set_deal_time(alarm_deal_info.time);
    group_comm_alarm_deal_msg.set_mac(stCommuityAlarmInfo.mac);
    group_comm_alarm_deal_msg.set_dev_location(stCommuityAlarmInfo.device_location);
    group_comm_alarm_deal_msg.set_alarm_type(stCommuityAlarmInfo.alarm_type);
    group_comm_alarm_deal_msg.set_mng_account_id(stCommuityAlarmInfo.manager_account_id);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&group_comm_alarm_deal_msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);
    return 0;
}


////////////////////////////////////////////////////////////////

int CMsgControl::ParseAlarmMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_ALARM* alarm_msg, uint32_t nVer)
{
    if ((pNormalMsg == NULL) || (alarm_msg == NULL))
    {
        return -1;
    }

    if (GetMsgHandleInstance()->ParseAlarmMsg(pNormalMsg, alarm_msg, nVer) < 0)
    {
        return -1;
    }

    return 0;
}

int CMsgControl::ParseTextMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_TEXT_MESSAGE* text_msg)
{
    if ((pNormalMsg == NULL) || (text_msg == NULL))
    {
        return -1;
    }

    char* payload = (char*)pNormalMsg->data;

    if (GetMsgHandleInstance()->ParseTextMsg(payload, text_msg) < 0)
    {
        return -1;
    }

    return 0;
}

int CMsgControl::ProcessCheckPersonnalPublicDevTmpKeyMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, stTmpKeyInfo.mac, data_size);
    }

    if (GetMsgHandleInstance()->ParseCheckTmpKeyMsg(payload, &stTmpKeyInfo) < 0)
    {
        AK_LOG_WARN << "ParseCheckTmpKeyMsg failed.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }
    //校验数据库中临时秘钥
    if (GetPersonnalTmpKeyInstance()->CheckTmpKeyBySingleTenantPubDev(stTmpKeyInfo) != TRUE)
    {
        AK_LOG_WARN << "Check personnal TmpKey failed.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }

    return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
}


int CMsgControl::ProcessCheckPersonnalTmpKeyMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, stTmpKeyInfo.mac, data_size);
    }

    if (GetMsgHandleInstance()->ParseCheckTmpKeyMsg(payload, &stTmpKeyInfo) < 0)
    {
        AK_LOG_WARN << "ParseCheckTmpKeyMsg failed.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }
    //校验数据库中临时秘钥
    if (GetPersonnalTmpKeyInstance()->CheckPersonalAppTmpKeyByPerDev(stTmpKeyInfo) != TRUE)
    {
        if (GetPersonnalTmpKeyInstance()->CheckPubAppTmpKey(stTmpKeyInfo) != TRUE)
        {
            AK_LOG_WARN << "Check personnal TmpKey failed.";
            return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
        }
    }

    return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
}

int CMsgControl::ProcessCheckCommunityTmpKeyMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, stTmpKeyInfo.mac, data_size);
    }

    if (GetMsgHandleInstance()->ParseCheckTmpKeyMsg(payload, &stTmpKeyInfo) < 0)
    {
        AK_LOG_WARN << "ParseCheckTmpKeyMsg failed.";
        return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }

    //校验数据库中临时秘钥
    if (GetPersonnalTmpKeyInstance()->CheckPersonalAppTmpKeyByPubDev(stTmpKeyInfo) != TRUE)
    {
        if (GetPersonnalTmpKeyInstance()->CheckPubAppTmpKey(stTmpKeyInfo) != TRUE)
        {
            //AK_LOG_WARN << "Check personnal TmpKey failed.";
            return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
        }
    }

    return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
}


int CMsgControl::ProcessAlarmDealMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_ALARM_DEAL& alarm_deal_info)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, AES_KEY_DEFAULT_MAC, data_size);
    }
    if (GetMsgHandleInstance()->ParseAlarmDealMsg(payload, &alarm_deal_info) < 0)
    {
        AK_LOG_WARN << "ParseAlarmDealMsg failed.";
        return -1;
    }
    if (DaoDealAlarmStatus(alarm_deal_info) != 0)
    {
        AK_LOG_WARN << "DealAlarmStatus failed.";
        return -1;
    }

    return 0;
}

//个人终端用户告警处理消息上报
int CMsgControl::processPersonnalAlarmDealMsg(SOCKET_MSG_NORMAL* pNormalMsg, const DEVICE_SETTING&  deviceSetting, const evpp::Any& personnalAppSetting, const int type)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "processPersonnalAlarmDealMsg The input params is null.";
        return -1;
    }
    SOCKET_MSG_PERSONNAL_ALARM_DEAL personnal_alarm_deal_info;
    memset(&personnal_alarm_deal_info, 0, sizeof(personnal_alarm_deal_info));
    if (type == csmain::PERSONNAL_APP)
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp(boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(personnalAppSetting));
        Snprintf(personnal_alarm_deal_info.area_node, sizeof(personnal_alarm_deal_info.area_node), stApp.node);
    }
    else
    {
        Snprintf(personnal_alarm_deal_info.area_node, sizeof(personnal_alarm_deal_info.area_node), deviceSetting.device_node);
    }
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, AES_KEY_DEFAULT_MAC, data_size);
    }

    if (GetMsgHandleInstance()->ParseAlarmDealMsg(payload, &personnal_alarm_deal_info) < 0)
    {
        AK_LOG_WARN << "Parse personnal AlarmDealMsg failed.";
        return -1;
    }

    ::snprintf(personnal_alarm_deal_info.device_name, sizeof(personnal_alarm_deal_info.device_name), "%s", deviceSetting.location);
    std::string NodeTime = getNodeCurrentTimeString(deviceSetting.device_node);
    ::snprintf(personnal_alarm_deal_info.time, sizeof(personnal_alarm_deal_info.time), "%s", NodeTime.c_str());
    AK_LOG_INFO << deviceSetting.mac << " put alarm deal. alarmid:" << personnal_alarm_deal_info.alarm_id << " user:" << personnal_alarm_deal_info.user;
    if (DaoPersonnalDealAlarmStatus(personnal_alarm_deal_info) != 0)
    {
        AK_LOG_WARN << "Deal personnal AlarmStatus failed.";
        return -1;
    }
    SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE stPerAlarmInfo;
    if (DaoPerGetAlarmInfoById(personnal_alarm_deal_info.alarm_id, stPerAlarmInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id:" << personnal_alarm_deal_info.alarm_id;
    }

    ::snprintf(stPerAlarmInfo.community, sizeof(stPerAlarmInfo.community), "%s", personnal_alarm_deal_info.community);

    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupPerAlarmDealMsg msg;
    msg.set_node(personnal_alarm_deal_info.area_node);
    msg.set_alarm_id(personnal_alarm_deal_info.alarm_id);
    msg.set_deal_user(personnal_alarm_deal_info.user);
    msg.set_deal_result(personnal_alarm_deal_info.result);
    msg.set_deal_type(personnal_alarm_deal_info.type);
    msg.set_deal_time(personnal_alarm_deal_info.time);
    msg.set_mac(stPerAlarmInfo.mac);
    msg.set_dev_location(stPerAlarmInfo.device_location);
    msg.set_alarm_type(stPerAlarmInfo.alarm_type);
    msg.set_community(stPerAlarmInfo.community);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);
    return 0;
}

//个人终端用户APP状态上报
int CMsgControl::ProcessAppReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_APP_CONF& app_config)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        int dy_iv = 0;
        AesDecryptByDefaultForReportStatus(payload, payload, data_size, dy_iv);
        app_config.dynamics_iv = dy_iv;
    }

    if (GetMsgHandleInstance()->ParseAppReportStatusMsg(payload, &app_config) < 0)
    {
        AK_LOG_WARN << "ParseAppReportStatusMsg failed.";
        return -1;
    }

    //转换为主站点再获取信息
    std::string main_user_account, report_user_account;
    GetUserAccount(app_config.user, main_user_account, report_user_account);
    if(main_user_account.size() > 0)
    {
        memset(app_config.user, 0, sizeof(app_config.user));    //替换user为主站点
        snprintf(app_config.user, sizeof(app_config.user), "%s", main_user_account.c_str());
        snprintf(app_config.report_user, sizeof(app_config.report_user), "%s", report_user_account.c_str());
    }
    
    ResidentPerAccount report_user_info;
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(report_user_account, report_user_info)) 
    {
        app_config.report_user_role = report_user_info.role;
    }
    
    //通过数据库获取账号对应的联动系统
    if (DaoGetNodeByAppUser(app_config) != 0)
    {
        AK_LOG_WARN << "DaoGetNodeByAppUser failed.";
        return -1;
    }
    return 0;
}


//个人终端用户设备请求获取联动单元设备列表
int CMsgControl::ProcessReqDevListMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                      SOCKET_MSG_PERSONNAL_DEV_LIST& stDevList,
                                      const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, mac, data_size);
    }

    if (GetMsgHandleInstance()->ParseReqDevListMsg(payload, &stDevList) < 0)
    {
        AK_LOG_WARN << "Parse personnal ReqDevListMsg failed.";
        return -1;
    }
    return 0;
}

int CMsgControl::ParseMotionAlertMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                     SOCKET_MSG_MOTION_ALERT& stMotionAlert,
                                     const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    char payload_out[SOCKET_MSG_DATA_SIZE + 1];
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload_out, mac, data_size);
        //AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseMotionAlertMsg(payload_out, &stMotionAlert) < 0)
    {
        AK_LOG_WARN << "ParseMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}

//app上报对设备进行布防、撤防的信令
int CMsgControl::ParseReqArmingMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                   SOCKET_MSG_DEV_ARMING& stReqArming)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseReqArmingMsg(payload, &stReqArming) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.MsgVersion=" << msg_version << ";DataSize=" << data_size << "PayLoadLen=" << strlen(payload);
        return -1;
    }

    return 0;
}


//设备对设备进行布防、撤防的信令
int CMsgControl::ParseReqArmingMsgFromDev(SOCKET_MSG_NORMAL* pNormalMsg,
        SOCKET_MSG_DEV_ARMING& stReqArming, const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, mac, data_size);
    }
    if (GetMsgHandleInstance()->ParseReqArmingMsg(payload, &stReqArming) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.MsgVersion=" << msg_version << ";DataSize=" << data_size << ";PayLoadLen=" << strlen(payload);
        return -1;
    }

    return 0;
}

//app请求对某个设备的rtsp流进行截图
int CMsgControl::ParseReqCaptureMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                    SOCKET_MSG_REQ_CAPTURE& request_capture)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseReqCaptureMsg(payload, request_capture) < 0)
    {
        AK_LOG_WARN << "ParseReqCaptureMsg failed.";
        return -1;
    }

    return 0;
}

//视频存储的动作请求
int CMsgControl::ParseVideoStorageMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                      SOCKET_MSG_VIDEO_STORAGE& video_storage)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseVideoStorageMsg(payload, video_storage) < 0)
    {
        AK_LOG_WARN << "ParseVideoStorageMsg failed.";
        return -1;
    }

    return 0;
}


//dtmf校验
int CMsgControl::ParseReqCheckDtmfMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                      SOCKET_MSG_CHECK_DTMF& check_dtmf)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseReqCheckDtmfMsg(payload, check_dtmf) < 0)
    {
        AK_LOG_WARN << "ParseReqCaptureMsg failed.";
        return -1;
    }

    return 0;
}


//dtmf校验消息返回
int CMsgControl::OnBuildRespCheckDtmf(SOCKET_MSG &socket_message,SOCKET_MSG &dy_iv_socket_message, const SOCKET_MSG_CHECK_DTMF& check_dtmf)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildRespCheckDtmfMsg(payload, sizeof(msg_normal->data), check_dtmf);

    EncryptDefalutMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_CHECK_DTMF_ACK);
    return 0;
}

//设备(室内机)上报当前状态给平台
int CMsgControl::ParseReportArmingMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                      SOCKET_MSG_DEV_ARMING& stReqArming,
                                      const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //if(strlen(payload) + 1 < data_size)//+1 给'/0'腾空间
    //{
    //  AK_LOG_WARN << "The msg fomat from device is error. DataSize=" << data_size << "PayLoadLen=" << strlen(payload);
    //  return -1;
    //}
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, mac, data_size);
    }
    if (GetMsgHandleInstance()->ParseReportArmingMsg(payload, &stReqArming) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.MsgVersion=" << msg_version << ";DataSize=" << data_size << "PayLoadLen=" << strlen(payload);
        return -1;
    }

    return 0;
}


//设备(室内机)上报当前状态给平台
int CMsgControl::ParseCommandRespMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                     SOCKET_MSG_COMMAND_RESP& stRespCommand,
                                     const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, mac, data_size);
        //AesDecryptByDefault(payload, payload, data_size);
    }

    ::snprintf(stRespCommand.message, sizeof(stRespCommand.message), "%s", payload);
    /* 不做xml解析，因为内容有可能也会含有现在定的标签
    if(GetMsgHandleInstance()->ParseCommandRespMsg(payload, &stRespCommand) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.";
        return -1;
    }*/

    return 0;
}


int CMsgControl::ParseSetMotionAlertMsg(SOCKET_MSG_NORMAL* pNormalMsg, int& type)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseSetMotionAlertMsg(payload, type) < 0)
    {
        AK_LOG_WARN << "ParseMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::ParseReportActMsg(SOCKET_MSG_NORMAL* pNormalMsg,
                                   SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg,
                                   const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, mac, data_size);
        //AesDecryptByDefault(payload, payload, data_size);
    }
    if (GetMsgHandleInstance()->ParseReportActMsg(payload, act_msg) < 0)
    {
        AK_LOG_WARN << "ParseMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::ParseReportFileMd5Msg(SOCKET_MSG_NORMAL* msg,SOCKET_MSG_REPORT_FILEMD5& filemd5, const std::string& mac)
{
    if (msg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, mac, data_size);
    }
    if (GetMsgHandleInstance()->ParseReportFileMd5Msg(payload, filemd5) < 0)
    {
        AK_LOG_WARN << "ParseReportFileMd5Msg failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::BuildReqStatusMsg(SOCKET_MSG* socket_message, SOCKET_MSG_REQ_STATUS* request_status_msg)
{
    if ((socket_message == NULL) || (request_status_msg == NULL))
    {
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildReqStatusMsg(payload, sizeof(msg_normal->data), request_status_msg) < 0)
    {
        return -1;
    }

    int data_size = strlen((char*)msg_normal->data);
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_REQUEST_STATUS, VERSION_1_0, data_size) < 0) //这一条不能加密
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildRemoteControlMsg(SOCKET_MSG* socket_message, SOCKET_MSG_REMOTE_CONTROL* remote_control_msg, const std::string mac)
{
    if ((socket_message == NULL) || (remote_control_msg == NULL))
    {
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildRemoteControlMsg(payload, sizeof(msg_normal->data), remote_control_msg) < 0)
    {
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_REMOTE_CONTROL, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildKeySendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_KEY_SEND* pSendKeyMsg,
                                 int ver, const std::string mac)
{
    if ((socket_message == NULL) || (pSendKeyMsg == NULL))
    {
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildKeySendMsg(payload, sizeof(msg_normal->data), pSendKeyMsg) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    if (ver == VERSION_2_0)
    {
        AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    }

    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_KEY_SEND, ver, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildOfficeKeySendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_KEY_SEND* pSendKeyMsg,
                                 int ver, const std::string mac)
{
    if ((socket_message == NULL) || (pSendKeyMsg == NULL))
    {
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildOfficeKeySendMsg(payload, sizeof(msg_normal->data), pSendKeyMsg) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    if (ver == VERSION_2_0)
    {
        AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    }

    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_KEY_SEND, ver, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;

    return 0;
}


int CMsgControl::BuildGivenKeySendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_KEY_SEND* pSendKeyMsg,
                                 int ver, const std::string mac)
{
    if ((socket_message == NULL) || (pSendKeyMsg == NULL))
    {
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildGivenKeySendMsg(payload, sizeof(msg_normal->data), pSendKeyMsg) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    if (ver == VERSION_2_0)
    {
        AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    }

    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_KEY_SEND, ver, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;

    return 0;
}


int CMsgControl::BuildUpgradeSendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_UPGRADE_SEND* pUpgradeKeyMsg, std::string mac)
{
    if ((socket_message == NULL) || (pUpgradeKeyMsg == NULL))
    {
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildUpgradeSendMsg(payload, sizeof(msg_normal->data), pUpgradeKeyMsg) < 0)
    {
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_UPGRADE_SEND, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildAlarmSendMsg(SOCKET_MSG& socket_message, SOCKET_MSG &dy_iv_socket_message, SOCKET_MSG_ALARM_SEND* alarm_msg, int ver)
{
    if (alarm_msg == NULL)
    {
        return -1;
    }

    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildAlarmSendMsg(payload, sizeof(msg_normal->data), alarm_msg) < 0)
    {
        return -1;
    }
    EncryptDefalutMacMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_NOTIFY_ALARM_ACK);
    return 0;
}

int CMsgControl::BuildCheckTmpKeyAckMsg(SOCKET_MSG* socket_message,
                                        const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& szTmpKeyInfo,
                                        int ver)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildCheckTmpKeyAckMsg(payload, sizeof(msg_normal->data), &szTmpKeyInfo) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    if (ver == VERSION_2_0)
    {
        AesEncryptByMacV2(payload, payload, szTmpKeyInfo.mac, &data_size, sizeof(msg_normal->data));
    }
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_CHECK_TMP_KEY_ACK, ver, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildSendDevListChangeMsg(SOCKET_MSG* socket_message, int ver)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    int data_size = 0;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE, ver, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}
//个人终端用户获取设备列表明细
int CMsgControl::BuildReqDevListAckMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_PERSONNAL_DEV_LIST& stDevList,
                                       const std::vector<PERSONNAL_DEVICE_SIP>& oDec, std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    //这个长度不能固定,因为设备端的长度也是固定最长4096,所以长度不能放开,在里面限制长度,超过长度的就截断
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    // '- 16'的目的是防止加密后超过4096, '-10'是因为dclient那边总的消息长度不能超过4096，所以要再减去消息头长度10
    if (GetMsgHandleInstance()->BuildReqDevListAckMsg(payload, sizeof(msg_normal->data) - 16 - 10, &stDevList, oDec) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_SEND_DEVICE_LIST, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;

    return 0;
}



//社区个人终端用户获取设备列表明细
int CMsgControl::BuildReqDevListAckMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_PERSONNAL_DEV_LIST& stDevList,
                                       const std::vector<COMMUNITY_DEVICE_SIP>& oDec, std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    //这个长度不能固定,因为设备端的长度也是固定最长4096,所以长度不能放开,在里面限制长度,超过长度的就截断
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    // '- 16'的目的是防止加密后超过4096, '-10'是因为dclient那边总的消息长度不能超过4096，所以要再减去消息头长度10
    if (GetMsgHandleInstance()->BuildReqDevListAckMsg(payload, sizeof(msg_normal->data) - 16 - 10, &stDevList, oDec) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_SEND_DEVICE_LIST, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildReqRtspMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_REQ_RTSP& request_rtsp_msg, std::string& mac)
{
    if (socket_message == NULL)
    {
        AK_LOG_WARN << "The param socket_message is null";
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildReqRtspMsg(payload, sizeof(msg_normal->data), request_rtsp_msg) < 0)
    {
        AK_LOG_WARN << "Failed to build ReqRtspMsg";
        return -1;
    }

    AK_LOG_INFO << "Build Req Rtsp Msg:\n" << payload;

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));  //使用欲监控的室外机mac地址加密
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    //停止监控
    if (request_rtsp_msg.type == csmain::kRtspStop)
    {
        if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_STOP_RTSP, VERSION_2_0, data_size) < 0)
        {
            AK_LOG_WARN << "Failed to build StopRtsp MsgHeader";
            return -1;
        }
    }
    else
    {
        if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_START_RTSP, VERSION_2_0, data_size) < 0)
        {
            AK_LOG_WARN << "Failed to build StartRtsp MsgHeader";
            return -1;
        }
    }
    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::BuildKeepRtspMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_REQ_RTSP& keepalive_rtsp_msg, const std::string& mac)
{
    if (socket_message == NULL)
    {
        AK_LOG_WARN << "The param socket_message is null";
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildKeepRtspMsg(payload, sizeof(msg_normal->data), keepalive_rtsp_msg) < 0)
    {
        AK_LOG_WARN << "Failed to build KeepRtspMsg";
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));  //使用欲监控的室外机mac地址加密
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_KEEP_RTSP, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "Failed to build KeepRtsp MsgHeader";
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::BuildVisitorTmpKeyAckMsg(SOCKET_MSG* socket_message, const std::string& mac, int temp_key_code)
{
    if (socket_message == NULL)
    {
        AK_LOG_WARN << "The param socket_message is null";
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildVisitorTempKeyAckMsg(payload, sizeof(msg_normal->data), temp_key_code) < 0)
    {
        AK_LOG_WARN << "Failed to build VisitorTmpKeyAckMsg";
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_TEMP_KEY_MSG, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "Failed to build VisitorTmpKeyAckMsg MsgHeader";
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::BuildOssStsMsg(SOCKET_MSG* socket_message, const std::string& mac, const SOCKET_MSG_DEV_OSS_STS& oss_sts)
{
    if (socket_message == NULL)
    {
        AK_LOG_WARN << "The param socket_message is null";
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildOssStsMsg(payload, sizeof(msg_normal->data), oss_sts) < 0)
    {
        AK_LOG_WARN << "Failed to build OssStsMsg";
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_SEND_OSS_STS, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "Failed to build VisitorTmpKeyAckMsg MsgHeader";
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::BuildOpenDoorAckMsg(SOCKET_MSG* socket_message, const std::string& mac, int result)
{
    if (socket_message == NULL)
    {
        AK_LOG_WARN << "The param socket_message is null";
        return -1;
    }

    memset(socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildOpenDoorAckMsg(payload, sizeof(msg_normal->data), result) < 0)
    {
        AK_LOG_WARN << "Failed to build OpenDoorAckMsg";
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_OPENDOOR_ACK, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "Failed to build OpenDoorAckMsg MsgHeader";
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::OnBuildAlarmNotifyMsg2MngDev(SOCKET_MSG& socket_message, SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_ALARM_SEND& stAlarm)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildAlarmNotifyMsg2MngDev(payload, sizeof(msg_normal->data), &stAlarm) < 0)
    {
        return -1;
    }
    
    EncryptDefalutMacMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_MANAGE_ALARM);
    return 0;
}


int CMsgControl::OnBuildAlarmNotifyMsg(SOCKET_MSG& socket_message, SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_ALARM_SEND& stAlarm)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildAlarmNotifyMsg(payload, sizeof(msg_normal->data), &stAlarm) < 0)
    {
        return -1;
    }

    EncryptDefalutMacMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED);    
    return 0;
}

//个人终端用户,组装告警解除通知消息
int CMsgControl::OnBuildPersonnalAlarmDealNotifyMsg(SOCKET_MSG &socket_message, SOCKET_MSG &dy_iv_socket_message, const SOCKET_MSG_PERSONNAL_ALARM_DEAL& stAlarmDeal)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildPersonnalAlarmDealNotifyMsg(payload, sizeof(msg_normal->data), &stAlarmDeal) < 0)
    {
        return -1;
    }
    
    EncryptDefalutMacMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_NOTIFY_ALARM_DEAL);
    return 0;
}

//默认密码正常加密和动态iv加密
int CMsgControl::EncryptDefalutMacMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id)
{
    memcpy(&dy_iv_socket_message, &socket_message, sizeof(socket_message));

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    int data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefaultMac(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(&socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message.size = data_size + head_size;

    msg_normal = (SOCKET_MSG_NORMAL*)dy_iv_socket_message.data;
    payload = (char*)msg_normal->data;
    data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefaultMacDynamicsIV(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(&dy_iv_socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    dy_iv_socket_message.size = data_size + head_size;
    return 0;
}


int CMsgControl::EncryptDefalutMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id)
{
    memcpy(&dy_iv_socket_message, &socket_message, sizeof(socket_message));

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    int data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefault(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(&socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message.size = data_size + head_size;

    msg_normal = (SOCKET_MSG_NORMAL*)dy_iv_socket_message.data;
    payload = (char*)msg_normal->data;
    data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefaultForDynamicsIV(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(&dy_iv_socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    dy_iv_socket_message.size = data_size + head_size;
    return 0;
}


//社区终端用户,组装告警解除通知消息
int CMsgControl::OnBuildCommunityAlarmDealNotifyMsg(SOCKET_MSG& socket_message, SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_ALARM_DEAL& stAlarmDeal)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildCommunityAlarmDealNotifyMsg(payload, sizeof(msg_normal->data), &stAlarmDeal) < 0)
    {
        return -1;
    }
    EncryptDefalutMacMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_NOTIFY_ALARM_DEAL);
    return 0;
}

int CMsgControl::OnBuildMotionNotifyMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const std::string& proto, const std::string& mac,
                                        int id,  const std::string& sip_account, const std::string& localtion, const std::string& time, const std::string& node)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildPerMotionNotifyMsg(payload, sizeof(msg_normal->data), proto, mac,
            id, sip_account, localtion, time, node);

    EncryptDefalutMsg(socket_message, dy_iv_socket_message, MSG_TO_APP_NOTIFY_MOTION_OCCURED);
    return 0;
}

int CMsgControl::OnBuildMotionToDevNotifyMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const std::string& proto, const std::string& mac,
        int id,  const std::string& sip_account, const std::string& localtion, const std::string& time, const std::string& node)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildPerMotionNotifyMsg(payload, sizeof(msg_normal->data), proto, mac,
            id, sip_account, localtion, time, node);
    EncryptDefalutMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_DOOR_MOTION_ALERT);
    return 0;
}

int CMsgControl::OnBuildRequestSensorTriggerMsg(SOCKET_MSG& socket_message, const SOCKET_MSG_SENSOR_TRIGGER& sensor_trigger)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;

    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildRequestSensorTriggerMsg(payload, sizeof(msg_normal->data), sensor_trigger);
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, sensor_trigger.mac, &data_size, sizeof(msg_normal->data)); 

    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(&socket_message,  MSG_TO_DEVICE_REQUEST_SENSOR_TRIGGER, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message.size = data_size + head_size;
    return 0;
}

//平台给设备（室内机）下发布防、撤防的信令
int CMsgControl::OnBuildReqArming(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_ARMING& arming)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildBuildReqArmingMsg(payload, sizeof(msg_normal->data), arming);
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, arming.mac, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_HANDLE_ARMING, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

//平台给app响应设备布防、撤防状态的消息
int CMsgControl::OnBuildRespArming(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_DEV_ARMING& arming)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildRespArmingMsg(payload, sizeof(msg_normal->data), arming);
    EncryptDefalutMsg(socket_message, dy_iv_socket_message, MSG_TO_APP_RESP_DEV_ARMING_STATUS);
    return 0;
}

//平台给app响应设备布防、撤防状态的消息
int CMsgControl::OnBuildRespArmingToDev(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_ARMING& arming, const std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildRespArmingMsg(payload, sizeof(msg_normal->data), arming);
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_FROM_DEVICE_REPORT_ARMING_STATUS, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}


int CMsgControl::OnBuildVisitorAuth(SOCKET_MSG* socket_message, int count, const std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildVisitorAuthMsg(payload, sizeof(msg_normal->data), count);
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_VISITOR_AUTH_ACK, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::OnBuildFaceDataNotifyMsg(SOCKET_MSG& socket_message, SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_DEV_REPORT_FACE_DATA& face_data)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildFaceDataMsg(payload, sizeof(msg_normal->data), face_data);
    EncryptDefalutMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_FACE_DATA_FORWARD);
    return 0;
}

///////////////////////////////////////////////////////////////////////////////////

/* Begin added by chenyc,2017-05-24,云平台接入app开发 */
/*added by czw,2022.05.31 新增TmpKey可选relay需求
TmpKey 可开的设备relay校验规则：
1.单住户TmpKey：TmpKeyList.Relay & Device.Relay
2.社区/办公TmpKey：
2-1 pm创建的TmpKey：TmpKeyList.Relay & Device.Relay
2-2 终端用户创建的TmpKey：TmpKeyList.Relay & Device.Relay & AccessGroup.Relay
*/
//Noted by czw: result 0-校验成功 1-校验失败
int CMsgControl::OnCheckTmpKeyMsg(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    //1.获取设备信息
    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed,please make sure the client device had registered.";
        return -1;
    }

    int check_res = BookingTmpkeyCheckRes::CHECK_RES_TMPKEY_NOT_FOUND;
    //若是社区公共设施设备，则走booking相关校验逻辑
    if (deviceSetting.device_type == csmain::COMMUNITY_DEV&&dbinterface::AmenityDevice::IsAmenityDevice(deviceSetting.uuid))
    {
        AK_LOG_INFO << "check booking tmp key. mac=" <<  deviceSetting.mac;
        check_res = OnCheckBookingTmpKey(pNormalMsg, conn, deviceSetting);
    }
    //booking校验通过或tmpkey已经存在booking，无需校验普通tmpkey
    if (check_res != BookingTmpkeyCheckRes::CHECK_RES_TMPKEY_NOT_FOUND)
    {
        return 0;
    }

    AK_LOG_INFO << "check tmp key mac=" <<  deviceSetting.mac;
    //2.获取relay信息
    std::string dev_relay;
    std::string security_relay;
    //只能去数据库查，如果查缓存的话，网页更新后TmpKey返回的relay还是旧的
    if (GetDeviceSettingInstance()->GetAllRelayByMac(deviceSetting.mac, dev_relay, security_relay) < 0)
    {
        AK_LOG_WARN << "GetRelayByMac failed,please make sure the device relay is normal";
        return -1;
    }
    int default_relay_value = 0;
    int default_serelay_value = 0;
    GetValueByRelay(dev_relay, default_relay_value);  //获取设备已开启的relay值
    GetValueByRelay(security_relay, default_serelay_value); 

    //3.校验TmpKey合法性
    SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY stTmpKeyInfo;
    memset(&stTmpKeyInfo, 0, sizeof(stTmpKeyInfo));
    stTmpKeyInfo.mngaccount_id = deviceSetting.manager_account_id;
    
    //3-1.单住户设备校验TmpKey
    if (deviceSetting.device_type == csmain::PERSONNAL_DEV)
    {
        Snprintf(stTmpKeyInfo.area_node, sizeof(stTmpKeyInfo.area_node), deviceSetting.device_node);
        Snprintf(stTmpKeyInfo.mac, sizeof(stTmpKeyInfo.mac), deviceSetting.mac);
        stTmpKeyInfo.personal_public_device_id = deviceSetting.id;
        if (deviceSetting.flag & DEVICE_SETTING_FLAG_PER_PUBLIC)//单住户公共
        {
            stTmpKeyInfo.result = ProcessCheckPersonnalPublicDevTmpKeyMsg(pNormalMsg, stTmpKeyInfo);
        }
        else    //单住户用户家庭设备
        {
            stTmpKeyInfo.result = ProcessCheckPersonnalTmpKeyMsg(pNormalMsg, stTmpKeyInfo);
        }
        //将relay_value转成dclient协议中的doornum的形式 如1234
        std::string door_num = RelayToString(stTmpKeyInfo.relay_value & default_relay_value);
        std::string security_door_num = RelayToString(stTmpKeyInfo.security_relay_value & default_serelay_value);
        Snprintf(stTmpKeyInfo.relay, sizeof(stTmpKeyInfo.relay), door_num.c_str());
        Snprintf(stTmpKeyInfo.security_relay, sizeof(stTmpKeyInfo.security_relay), security_door_num.c_str());
    }
    
    //3-2.社区、办公项目设备校验TmpKey
    else
    {   
        Snprintf(stTmpKeyInfo.area_node, sizeof(stTmpKeyInfo.area_node), deviceSetting.device_node);
        stTmpKeyInfo.area_node[AREA_NODE_SIZE - 1] = 0;
        Snprintf(stTmpKeyInfo.mac, sizeof(stTmpKeyInfo.mac), deviceSetting.mac);
        stTmpKeyInfo.unit_id = deviceSetting.unit_id;
        stTmpKeyInfo.manager_account_id = deviceSetting.manager_account_id;
        
        if (deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            stTmpKeyInfo.result = ProcessCheckPersonnalTmpKeyMsg(pNormalMsg, stTmpKeyInfo);
        }
        else if (deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            stTmpKeyInfo.result = ProcessCheckCommunityTmpKeyMsg(pNormalMsg, stTmpKeyInfo);
        }
        else if (deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
        {      
            stTmpKeyInfo.result = ProcessCheckCommunityTmpKeyMsg(pNormalMsg, stTmpKeyInfo);

            //如果已经校验失败,则不用校验设备的分building管理
            if (dbinterface::PersonalAppTmpKey::CHECK_SUCCESS == stTmpKeyInfo.result 
                && stTmpKeyInfo.personal_unit_id > 0 
                && CDeviceSetting::GetInstance()->DeviceIsManageBuilding(deviceSetting.type) 
                && 0 == CDeviceSetting::GetInstance()->IsManageBuilding(stTmpKeyInfo.mac, stTmpKeyInfo.personal_unit_id))
            {
                AK_LOG_INFO << "Unit=" << stTmpKeyInfo.personal_unit_id << " is not managed by Device which mac=" << deviceSetting.mac << ";type=" << deviceSetting.type;
                stTmpKeyInfo.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
            }
        }
        CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(deviceSetting.manager_account_id);
        
        //add bu xuzr,检查高级功能
        if ( dbinterface::PersonalAppTmpKey::CHECK_SUCCESS == stTmpKeyInfo.result 
            && 0 == stTmpKeyInfo.is_pmcreate 
            && comm_info->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_TMPKEY) 
            && !comm_info->IsExpire())
        {
            ResidentPerAccount per_account;
            memset(&per_account, 0, sizeof(per_account));
            int tmpkey_permission;
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(stTmpKeyInfo.node, per_account))
            {
                tmpkey_permission = per_account.is_show_tmpkey;
            }

            if (!tmpkey_permission)
            {
                stTmpKeyInfo.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
                AK_LOG_INFO << "MngAccount:" << deviceSetting.manager_account_id << " TmpKeyPermission check result is 0. " 
                << "Don't check tmpkey.";
            }
        }
        AccessGroupRelayValues access_relays = GetTmpKeyAgRelay(stTmpKeyInfo, deviceSetting, comm_info->GetIsNew());
        AK_LOG_INFO << "CheckTmpKey: " <<", AccessGroupRelay: " << access_relays.access_group_relay_value << ", AccessGroupSerelay: " << access_relays.access_group_serelay_value;


        std::string door_num = RelayToString(stTmpKeyInfo.relay_value & default_relay_value & access_relays.access_group_relay_value);
        std::string security_door_num = RelayToString(stTmpKeyInfo.security_relay_value & default_serelay_value & access_relays.access_group_serelay_value);
        Snprintf(stTmpKeyInfo.relay, sizeof(stTmpKeyInfo.relay), door_num.c_str());
        Snprintf(stTmpKeyInfo.security_relay, sizeof(stTmpKeyInfo.security_relay), security_door_num.c_str());        

        //新社区多楼层需求;pm创的PubTmpKey,不需要查询access_floor
        if (comm_info->GetIsNew() && strlen(stTmpKeyInfo.account) != 0)
        {       
            ResidentPerAccount account_info;
            memset(&account_info, 0, sizeof(account_info));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(stTmpKeyInfo.account, account_info))
            {
                std::string apt_floor = stTmpKeyInfo.floor;
                std::string apt_user_floor = kDefaultFloor;
                
                if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == deviceSetting.grade || csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == deviceSetting.grade)
                {
                    apt_user_floor = dbinterface::PersonalAccountCommunityInfo::GetAptBuildingAccessFloorInfoListByUUID(account_info.uuid, account_info.unit_uuid, apt_floor, deviceSetting);
                }

                // pm创的PubTmpKey,跟随用户楼层
                if (stTmpKeyInfo.is_pmcreate) {
                    snprintf(stTmpKeyInfo.floor, sizeof(stTmpKeyInfo.floor), "%s", apt_user_floor.c_str());
                    AK_LOG_INFO << "pm create PubTmpKey, apt_floor " << apt_floor << " apt_user_floor " << apt_user_floor;
                } else {
                    int is_follow_my_access = 0;
                    std::string temp_key_floor = dbinterface::PersonalAppTmpKey::GetAccessFloorByTmpkey(stTmpKeyInfo.tmpkey, stTmpKeyInfo.node, is_follow_my_access, deviceSetting, account_info.unit_uuid);
                    std::string accessible_floor = GetIntersectionFloor(temp_key_floor, apt_user_floor, is_follow_my_access);
                    snprintf(stTmpKeyInfo.floor, sizeof(stTmpKeyInfo.floor), "%s", accessible_floor.c_str());
                    AK_LOG_INFO << "apt_floor " << apt_floor << " apt_user_floor " << "\n"
                    << apt_user_floor << " temp_key_floor " << temp_key_floor <<" accessible_floor " << accessible_floor;
                }
            }
        }
    }

    if(strlen(stTmpKeyInfo.relay) == 0 && strlen(stTmpKeyInfo.security_relay) == 0)
    {
        //兼容旧设备relay为空会全开的问题
        stTmpKeyInfo.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }
    AK_LOG_INFO << "CheckTmpKey: " << stTmpKeyInfo.tmpkey 
                << ", KeyRelay: " << stTmpKeyInfo.relay_value 
                << ", DefaultRelay: " << default_relay_value 
                << ", Result: " << stTmpKeyInfo.result
                << ", UnitApt: " << stTmpKeyInfo.unit_apt;
    //4.返回校验结果给设备
    SOCKET_MSG socketMsg;
    memset(&socketMsg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socketMsg;
    int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
    if (BuildCheckTmpKeyAckMsg(socket_message, stTmpKeyInfo, ver) < 0)
    {
        AK_LOG_WARN << "BuildCheckTmpKeyAckMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "Send BuildCheckTmpKeyAckMsg failed.";
        return -1;
    }
    
    //5.更新TmpKey可用次数
    GetPersonnalTmpKeyInstance()->UpdateAccessTimes(stTmpKeyInfo);
    return 0;
}

AccessGroupRelayValues CMsgControl::GetTmpKeyAgRelay(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& st_tmp_key_info, const DEVICE_SETTING& device_setting, int is_new_community) 
{
    AccessGroupRelayValues relay_values;
    // 旧社区，使用默认 relay 
    if (!is_new_community)
    {
        relay_values.access_group_relay_value = RELAY_DEFAULT;
        relay_values.access_group_serelay_value = RELAY_DEFAULT;
        return relay_values;
    }
    
    // 新社区 需考虑权限组的relay情况
    relay_values = GetNewCommunityTmpKeyAgRelay(st_tmp_key_info, device_setting);
    return relay_values;
}


AccessGroupRelayValues CMsgControl::GetNewCommunityTmpKeyAgRelay(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& st_tmp_key_info, const DEVICE_SETTING& device_setting)
{
    AccessGroupRelayValues relay_values;
    // pm创建，使用默认 relay
    if (st_tmp_key_info.is_pmcreate)
    {   
        relay_values.access_group_relay_value = RELAY_DEFAULT;
        relay_values.access_group_serelay_value = RELAY_DEFAULT;
        return relay_values;
    }

     // 终端用户创建+有默认权限组且未移除默认权限组权限，使用默认 relay
    if (dbinterface::AccessGroup::HaveDefaultAG(st_tmp_key_info.account, device_setting.unit_id) && 
       (!dbinterface::AmenityDevice::IsRemoveDefaultAccess(device_setting.uuid)))
    {
        relay_values.access_group_relay_value = RELAY_DEFAULT;
        relay_values.access_group_serelay_value = RELAY_DEFAULT;
        return relay_values;
    }
    // 终端用户创建+没有默认权限组 需考虑权限组的relay情况
    dbinterface::AccessGroup::GetUserAGRelayByMac(st_tmp_key_info.account, st_tmp_key_info.mac, relay_values);
    return relay_values;
}      


//设备推送告警处理完毕的消息给服务器
int CMsgControl::OnPutAlarmDealMsg(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }
    DEVICE_SETTING dev_setting;
    memset(&dev_setting, 0, sizeof(dev_setting));
    evpp::Any personnalAppSetting;
    int type;
    //TODO: chenzhx 所有的app 都是personnalAppSetting 和PERSONNAL_APP，并没有和社区区分
    //TODO chenzhx ******** app处理告警走rest
    if (g_accSer_ptr->GetDevSetDiffFromConnList(conn, &dev_setting, personnalAppSetting, type) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed,please make sure the client device had registered.";
        return -1;
    }

    // 推送deal alarm 给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (strlen(dev_setting.node_uuid) > 0 && 0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(dev_setting.node_uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatus(dev_setting.mac, traceid);
        AK_LOG_INFO << "alexa device deal alarm notify web , mac :" << dev_setting.mac << ", traceid : " << traceid;
    }

    //如果是个人终端用户
    if (type == csmain::PERSONNAL_DEV || type == csmain::PERSONNAL_APP)
    {
        if (GetMsgControlInstance()->processPersonnalAlarmDealMsg(pNormalMsg, dev_setting, personnalAppSetting, type) != 0)
        {
            return -1;
        }
        return 0;
    }
    else if (type == csmain::COMMUNITY_DEV || type == csmain::COMMUNITY_APP)
    {
        if (GetMsgControlInstance()->processCommunityAlarmDealMsg(pNormalMsg, dev_setting, personnalAppSetting, type) != 0)
        {
            return -1;
        }
        return 0;
    }
    return 0;
}


void CMsgControl::FoceLogoutHandle(const SOCKET_MSG_PERSONNAL_APP_NODE& appnode, int is_office)
{
    //逻辑拆分到csredis和csoffice去做 
    SOCKET_MSG_NORMAL msg;
    memset(&msg, 0, sizeof(msg));
    InnerConnInfoApp info;
    Snprintf(info.node, sizeof(info.node), appnode.node);
    Snprintf(info.main_site, sizeof(info.main_site), appnode.user);
    Snprintf(info.account, sizeof(info.account), appnode.report_user);
    info.account_role = appnode.role;
    info.real_site_role = appnode.report_user_role;
    info.manager_id = appnode.manager_account_id;
    info.unit_id = appnode.unit_id;
    AK_LOG_INFO << "msg logout site: " << info.account << " site_role: " << info.real_site_role; 
    GetMsgControlInstance()->BuildInnerMsg(msg, MSG_FROM_INNER_CONN_APP_FORCE_LOGOUT, (char*)&info, sizeof(info));
    
    if (is_office)
    {
        Main2ResidMsgHandle::Instance()->OfficeSend(info.account, csmain::DeviceType::OFFICE_APP, false, (char*)&msg,  msg.data_size);  
    }
    else 
    {
        csmain::DeviceType type = csmain::DeviceType::COMMUNITY_NONE;
        if (info.account_role == ACCOUNT_ROLE_COMMUNITY_MAIN || info.account_role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            type = csmain::DeviceType::COMMUNITY_APP;
        }
        else if(info.account_role == ACCOUNT_ROLE_PERSONNAL_MAIN)
        {
            type = csmain::DeviceType::PERSONNAL_APP;
        }
        Main2ResidMsgHandle::Instance()->Send(info.account, type, false, (char*)&msg,  msg.data_size); 
    }    
}

//个人终端用户App-android上报身份识别给接入服务器
int CMsgControl::OnAndroidReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, int msg_len, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The param pRecvMsg is null.";
        return -1;
    }
    
    SOCKET_MSG_NORMAL msg;
    memcpy(&msg, pNormalMsg, msg_len);
    
    SOCKET_MSG_PERSONNAL_APP_CONF app_config; //
    memset(&app_config, 0, sizeof(app_config));

    if (ProcessAppReportStatusMsg(pNormalMsg, app_config) < 0)
    {
        AK_LOG_WARN << "ProcessAppReportStatusMsg failed.";
        return -1;
    }
    
    evpp::Any any_tmp_iv(app_config.dynamics_iv);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_DY_AES_IV_INDEX, any_tmp_iv);
    AK_LOG_INFO << "OnAndroidReportStatusMsg. user:" << app_config.user << " is aes dynamics iv:" << app_config.dynamics_iv;
    
    if (strlen(app_config.user) == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return -1;
    }
    //兼容旧版本
    if (strlen(app_config.language) == 0)
    {
        Snprintf(app_config.language, sizeof(app_config.language), "en");
    }

    SOCKET_MSG_PERSONNAL_APP_NODE stAppNode;
    memset(&stAppNode, 0, sizeof(stAppNode));
    Snprintf(stAppNode.node, sizeof(stAppNode.node), app_config.node);
    Snprintf(stAppNode.user, sizeof(stAppNode.user), app_config.user);  //app_config.szUser有可能是邮箱,所以内层函数要容错
    Snprintf(stAppNode.password, sizeof(stAppNode.password), app_config.password);
    Snprintf(stAppNode.token, sizeof(stAppNode.token), app_config.token);
    Snprintf(stAppNode.fcm_token, sizeof(stAppNode.fcm_token), app_config.fcm_token);
    Snprintf(stAppNode.app_token, sizeof(stAppNode.app_token), app_config.app_token);
    Snprintf(stAppNode.username, sizeof(stAppNode.username), app_config.username);
    Snprintf(stAppNode.app_version, sizeof(stAppNode.app_version), app_config.app_version);
    Snprintf(stAppNode.language, sizeof(stAppNode.language), app_config.language);
    Snprintf(stAppNode.uuid, sizeof(stAppNode.uuid), app_config.uuid);
    Snprintf(stAppNode.user_info_uuid, sizeof(stAppNode.user_info_uuid), app_config.user_info_uuid);
    //report_user可用于确定当前上报状态的CONN_TYPE -- 社区/办公/单住户
    Snprintf(stAppNode.report_user, sizeof(stAppNode.report_user), app_config.report_user);
    stAppNode.mobile_type = app_config.mobile_type;
    stAppNode.role = app_config.role;
    stAppNode.report_user_role = app_config.report_user_role;
    stAppNode.motion_recv_type = csmain::MotionRecvType::kNone;
    stAppNode.version = app_config.version;
    stAppNode.id_active = app_config.id_active;
    stAppNode.manager_account_id = app_config.manager_account_id;
    stAppNode.unit_id = app_config.unit_id;

    AK_LOG_INFO << stAppNode.user << " android report status. node:" << app_config.node
                << "FcmToken:" << app_config.fcm_token << " AppToken:" << app_config.app_token;

    //看下apptoken是否合法，因为目前dclient连接上来，平台会主动让app上报状态，但是这个时候有可能app 从网关返回的token就是非法的了，不应该通过
    if (strlen(stAppNode.app_token) > 0 &&  !gstAKCSConf.is_aws)
    {
        std::string stCsgateAppToken;
        GetAppPushTokenInstance()->getAppTokenByUid(stAppNode.user, stCsgateAppToken);
        if (strncmp(stCsgateAppToken.c_str(), stAppNode.app_token, strlen(stAppNode.app_token)) != 0)
        {
            AK_LOG_WARN << stAppNode.user << "android report status. but apptoken is error. report: " << stAppNode.app_token << " csgate: " << stCsgateAppToken;
            return -1;
        }
    }

    DailyAndMonthlyActiveUsers(stAppNode.user, app_config.is_office);

    //chenzhx20180829 判断这个账号的AppToken是否变了，如果变了说明被之前的要挤掉
    ConnectionList conn_devs;
    g_accSer_ptr->GetAppByNode(stAppNode.node, conn_devs);
    ConnectionListIter it = conn_devs.begin();
    std::string stNowAppToken = stAppNode.app_token;
    std::string stNowUid = stAppNode.user;
    for (; it != conn_devs.end(); ++it) //todo chenyc,2018-12-21,通过 uid_conns_ 来判断即可
    {
        std::string uid;
        std::string apptoken;
        it->second->GetAppUidAndAppToken(uid, apptoken);//conn_devs出来的值已经是app 不用判断
        if (stNowUid == uid)
        {
            //app退出后，并没有马上清除tcp连接,此时,如果app再一次执行login接口,那么会误报从其他地方登陆,实际是同一台手机而已.
            //根据统一手机两次调用login接口并与csmain建立tcp conn时3,用的端口和ip是一样,来进行这种情况的容错.
            //added by chenyc 2018-11-26,因为同一台app会上报多次状态,如果是这种情况,那么app token是一样的...
            //反过来，如果要判断是否为多机登录,那么通过这个app token也可以判断出来...
            if (stNowAppToken != apptoken && conn->remote_addr() != it->first->remote_addr())
            {
                SOCKET_MSG stSocketMsg;
                memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                if (OnBuildAppForceLogoutMsg(&stSocketMsg) != 0)
                {
                    AK_LOG_WARN << "OnBuildAppForceLogoutMsg failed";
                    continue;
                }
                GetDeviceControlInstance()->SendTcpMsg(it->first, stSocketMsg.data, stSocketMsg.size);
                g_accSer_ptr->RemoveAppConnFromConnections(it->first);
                AK_LOG_WARN << "Account: " << uid << " is login from another place,force to logout!,conn:" << it->first->remote_addr();
                FoceLogoutHandle(stAppNode, app_config.is_office);
            }
        }

    }
    //如果有一个相同的uid,且不在线,//added by chenyc, 2018-12-21,则端外通知,其实一般这种情况是邮件通知即可
    CMobileToken uidOldToken;
    if (CAkUserManager::GetInstance()->GetAkOfflineUserTokenByUid(stAppNode.user, uidOldToken) == 0)
    {
        //2019-03-14,不在线的app,且该uid已经被另一个app登陆了,那么就端外推送,实际上这种业务用邮件通知也ok.
        if (stNowAppToken != uidOldToken.AppToken() && !uidOldToken.MobileOnline())
        {
            AppOfflinePushKV kv;
            kv.insert(map<std::string, std::string>::value_type("language", uidOldToken.Language()));
            kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(uidOldToken.CommonVersion())));
            kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(uidOldToken.AppOem())));

            if (uidOldToken.MobileType() != csmain::AppType::APP_IOS)  //如果账号被顶掉的app不是ios
            {
                if (stAppNode.fcm_token != uidOldToken.FcmToken()) //以FcmToken作为某台安卓手机的标识值,避免重新登录也被判断为异地登陆
                {
                    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
                    if (push_cli_ptr)
                    {
                        push_cli_ptr->buildPushMsg(uidOldToken.MobileType(), uidOldToken.FcmToken(), csmain::PUSH_MSG_TYPE_FORCE_LOGOUT, kv, uidOldToken.OemName());
                        FoceLogoutHandle(stAppNode, app_config.is_office);
                    }

                    AK_LOG_WARN << "app logout other where, Account: " << stAppNode.user << " offline push";
                }
            }
            else//根据uidOldToken结果,如果账号被顶掉的app是ios
            {
                if (stAppNode.token != uidOldToken.Token())   //IOS以上报的DeviceToken为标识值
                {
                    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
                    {
                        push_cli_ptr->buildPushMsg(uidOldToken.MobileType(), uidOldToken.Token(), csmain::PUSH_MSG_TYPE_FORCE_LOGOUT, kv, uidOldToken.OemName());
                        FoceLogoutHandle(stAppNode, app_config.is_office);
                    }
                    AK_LOG_WARN << "app logout other where, Account: " << stAppNode.user << " offline push";
                }
            }
        }
    }


    g_accSer_ptr->RemoveReportStatuConn(conn);//删除上报状态超时检测

    //获取多套房所有房间的node
    PersonalAccountNodeInfoMap nodes;
    dbinterface::ResidentPersonalAccount::GetNodesByUserInfoUUID(stAppNode.user_info_uuid, nodes);

    //更新连接列表中的相关设备信息,刷新个人终端用户app的信息到内存客户端链表节点上
    if (g_accSer_ptr->UpdatePersonnalAppNodeToLocal(conn, stAppNode, nodes) < 0)
    {
        AK_LOG_WARN << "UpdatePersonnalAppNodeToLocal failed.";
        return -1;
    }
    //插入uid-token列表中,提升后续端外推送的效率
    CMobileToken token(stAppNode.user, stAppNode.token, stAppNode.mobile_type, 1, "");
    token.setFcmToken(stAppNode.fcm_token);
    token.setAppToken(stAppNode.app_token);
    token.setCommonVersion(stAppNode.version);
    token.setAppVersion(stAppNode.app_version);
    token.setUidNode(stAppNode.node);
    token.setLanguage(stAppNode.language);
    CMobileToken tmp_token;
    GetAppPushTokenInstance()->getAppPushTokenByUid(stAppNode.user, tmp_token);
    token.setAppOem(tmp_token.AppOem());
    token.setIsDyIv(app_config.dynamics_iv);
    token.setIsMultiSite(tmp_token.IsMultiSite());

    CAkUserManager::GetInstance()->AddAkUserByNodeId(nodes, stAppNode.user, token);

    //更新sip-uid列表
    CAkUserManager::GetInstance()->UpdateSipUidList(app_config.sip, stAppNode.user);

    //app登陆后返回一些系統信息 chenzhx ********
    SOCKET_MSG_RESP_APPLOGIN stRespLogin = {0};
    dbinterface::Message::GetTextMsgLatestIDAndUnReadNum(stAppNode.user, stAppNode.node, app_config.lastread_message_id, 
        stAppNode.manager_account_id, stRespLogin.last_msg_id, stRespLogin.unread_number);
    SOCKET_MSG socket_msg,dy_iv_socket;
    memset(&socket_msg, 0, sizeof(socket_msg));
    stRespLogin.last_msg_id = app_config.lastread_message_id;
    stRespLogin.is_expire = app_config.is_expire;
    stRespLogin.id_active = app_config.id_active;

    if(stAppNode.version < 6100)   //6.1版本已经在userconf接口返回给app
    {
        if (OnBuildAppLoginResponseMsg(socket_msg, dy_iv_socket,  &stRespLogin) != 0)
        {
            AK_LOG_WARN << "OnBuildAppLoginResponseMsg failed";
            return -1;
        }
        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socket_msg, dy_iv_socket);
    }

    //根据上报语言更新用户语言
    dbinterface::ResidentPersonalAccount::UpdateLanguageByUserInfoUUID(stAppNode.language, stAppNode.user_info_uuid);
    
    //added by chenyc,2018-12-12
    //注册接口兼容uuid
    g_sm_client_ptr->RegUid(stAppNode.user, g_logic_srv_id, stAppNode.uuid);
    
    evpp::Any status(true);
    evpp::Any client(std::string(stAppNode.user));
    evpp::Any is_app(true);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX, status);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX, client);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_IS_APP, is_app);

    if (stAppNode.role == ACCOUNT_ROLE_COMMUNITY_PM
        || stAppNode.role == ACCOUNT_ROLE_COMMUNITY_MAIN
        || stAppNode.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT
        || stAppNode.role == ACCOUNT_ROLE_PERSONNAL_MAIN
        || stAppNode.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        csmain::DeviceType conn_type = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
        Main2ResidMsgHandle::Instance()->Send(stAppNode.user, conn_type, false, 
         (char*)&msg,  SOCKET_MSG_NORMAL_HEADER_SIZE + NTOHS(msg.data_size));         
    }
    else
    {
        csmain::DeviceType conn_type = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
        Main2ResidMsgHandle::Instance()->OfficeSend(stAppNode.user, conn_type, false, 
         (char*)&msg,  SOCKET_MSG_NORMAL_HEADER_SIZE + NTOHS(msg.data_size));         
    }
    return 0;
}

void CMsgControl::DailyAndMonthlyActiveUsers(const std::string& account, int is_office)
{
    if (0 == account.size())
    {
        return;
    }

    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_appstat);
    if (cache_conn)
    {
        time_t t = time(0);
        char current_day[64];
        char current_month[64];
        strftime(current_day, sizeof(current_day), "%Y%m%d", localtime(&t));
        strftime(current_month, sizeof(current_month), "%Y%m", localtime(&t));
        
        // 根据is_office添加前缀区分办公和住宅
        std::string type_prefix = is_office ? "office_" : "resident_";
        std::string daily_key = type_prefix + "daily_" + std::string(current_day);
        std::string monthly_key = type_prefix + "monthly_" + std::string(current_month);
        
        // 先检查key是否存在，不存在则设置过期时间
        if (!cache_conn->isExists(daily_key))
        {
            cache_conn->sadd(daily_key, account);
            cache_conn->expire(daily_key, 15 * 24 * 3600);  // 日活key有效期15天
        }
        else
        {
            cache_conn->sadd(daily_key, account);
        }
        
        if (!cache_conn->isExists(monthly_key))
        {
            cache_conn->sadd(monthly_key, account);
            cache_conn->expire(monthly_key, 45 * 24 * 3600);  // 月活key有效期45天
        }
        else
        {
            cache_conn->sadd(monthly_key, account);
        }
        cache_manager->RelCacheConn(cache_conn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csmain " << g_redis_db_appstat;
    }
}

//个人终端用户App-IOS上报身份识别给接入服务器
int CMsgControl::OnIOSReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, int msg_len, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The param pRecvMsg is null.";
        return -1;
    }
    SOCKET_MSG_NORMAL msg;
    memcpy(&msg, pNormalMsg, msg_len);
    
    SOCKET_MSG_PERSONNAL_APP_CONF app_config;
    memset(&app_config, 0, sizeof(app_config));

    if (ProcessAppReportStatusMsg(pNormalMsg, app_config) < 0)
    {
        AK_LOG_WARN << "ProcessAppReportStatusMsg failed.";
        return -1;
    }
    if (strlen(app_config.user) == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return -1;
    }

    evpp::Any any_tmp_iv(app_config.dynamics_iv);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_DY_AES_IV_INDEX, any_tmp_iv);
    AK_LOG_INFO << "OnIOSReportStatusMsg. user:" << app_config.user << " is aes dynamics iv:" << app_config.dynamics_iv;
    
    //兼容旧版本
    if (strlen(app_config.language) == 0)
    {
        Snprintf(app_config.language, sizeof(app_config.language), "en");
    }
    SOCKET_MSG_PERSONNAL_APP_NODE stAppNode;
    memset(&stAppNode, 0, sizeof(stAppNode));
    Snprintf(stAppNode.node, sizeof(stAppNode.node), app_config.node);
    Snprintf(stAppNode.user, sizeof(stAppNode.user), app_config.user);
    Snprintf(stAppNode.password, sizeof(stAppNode.password), app_config.password);
    Snprintf(stAppNode.token, sizeof(stAppNode.token), app_config.token);
    Snprintf(stAppNode.voip_token, sizeof(stAppNode.voip_token), app_config.voip_token);
    Snprintf(stAppNode.app_token, sizeof(stAppNode.app_token), app_config.app_token);
    Snprintf(stAppNode.app_version, sizeof(stAppNode.app_version), app_config.app_version);
    Snprintf(stAppNode.username, sizeof(stAppNode.username), app_config.username);
    Snprintf(stAppNode.language, sizeof(stAppNode.language), app_config.language);
    Snprintf(stAppNode.uuid, sizeof(stAppNode.uuid), app_config.uuid);
    Snprintf(stAppNode.user_info_uuid, sizeof(stAppNode.user_info_uuid), app_config.user_info_uuid);
    //report_user可用于确定当前上报状态的CONN_TYPE -- 社区/办公/单住户
    Snprintf(stAppNode.report_user, sizeof(stAppNode.report_user), app_config.report_user);

    stAppNode.mobile_type = app_config.mobile_type;
    stAppNode.role = app_config.role;
    stAppNode.motion_recv_type = csmain::MotionRecvType::kNone;
    stAppNode.version = app_config.version;
    stAppNode.id_active = app_config.id_active;
    stAppNode.manager_account_id = app_config.manager_account_id;
    stAppNode.unit_id = app_config.unit_id;

    //看下apptoken是否合法，因为目前dclient连接上来，平台会主动让app上报状态，但是这个时候有可能app 从网关返回的token就是非法的了，不应该通过
    if (strlen(stAppNode.app_token) > 0 &&  !gstAKCSConf.is_aws)
    {
        std::string stCsgateAppToken = "";
        GetAppPushTokenInstance()->getAppTokenByUid(stAppNode.user, stCsgateAppToken);
        if (strcmp(stCsgateAppToken.c_str(), stAppNode.app_token))
        {
            AK_LOG_WARN << stAppNode.user << "ios report status. but apptoken is error. push: " << stAppNode.app_token << " csgate: " << stCsgateAppToken.c_str();
            return 0;
        }
    }
    AK_LOG_INFO << stAppNode.user << " ios report status."
                << "DeviceToken:" << app_config.token << " AppToken:" << app_config.app_token << " VoipToken:" << app_config.voip_token;

    DailyAndMonthlyActiveUsers(stAppNode.user, app_config.is_office);

    //chenzhx20180829 判断这个账号的AppToken是否变了，如果变了说明被之前的要挤掉
    ConnectionList conn_devs;
    g_accSer_ptr->GetAppByNode(stAppNode.node, conn_devs);
    
    ConnectionListIter it = conn_devs.begin();
    std::string stNowAppToken = stAppNode.app_token;
    std::string stNowUid = stAppNode.user;

    for (; it != conn_devs.end(); ++it)
    {
        std::string uid;
        std::string apptoken;
        it->second->GetAppUidAndAppToken(uid, apptoken);//conn_devs出来的值已经是app 不用判断
        
        if (stNowUid == uid)
        {
            //app退出后，并没有马上清除连接。下次用的连接的端口和ip是一样，需要过滤调
            if (stNowAppToken != apptoken && conn->remote_addr() != it->first->remote_addr())
            {
                SOCKET_MSG stSocketMsg;
                memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                if (OnBuildAppForceLogoutMsg(&stSocketMsg) != 0)
                {
                    AK_LOG_WARN << "OnBuildAppForceLogoutMsg failed";
                }
                else
                {
                    std::string ip;
                    int port = 0;
                    AkParseAddr(it->first->remote_addr(), ip, port);
                    GetDeviceControlInstance()->SendTcpMsg(it->first, stSocketMsg.data, stSocketMsg.size);
                    g_accSer_ptr->RemoveAppConnFromConnections(it->first);
                    AK_LOG_WARN << "Account: " << uid.c_str() << " is login other where,make logout!,conn:" << ip << ":" << port;
                }
            }
            //break;
        }

    }

    CMobileToken uidOldToken;
    if (CAkUserManager::GetInstance()->GetAkOfflineUserTokenByUid(stAppNode.user, uidOldToken) == 0)//modified by chenyc,v5.0
    {
        std::string token = stAppNode.token;
        if (stNowAppToken != uidOldToken.AppToken())
        {
            AppOfflinePushKV kv;
            kv.insert(map<std::string, std::string>::value_type("language", uidOldToken.Language()));
            kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(uidOldToken.CommonVersion())));
            kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(uidOldToken.AppOem())));
            AK_LOG_WARN << "Account: " << stAppNode.user << " is login other where,make offline push!";

            PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
            if (push_cli_ptr)
            {
                if (uidOldToken.MobileType() != csmain::AppType::APP_IOS)  //根据uidOldToken结果,如果账号被顶掉的app是android
                {
                    push_cli_ptr->buildPushMsg(uidOldToken.MobileType(), uidOldToken.FcmToken(), csmain::PUSH_MSG_TYPE_FORCE_LOGOUT, kv, uidOldToken.OemName());
                }
                else if (uidOldToken.Token() != token) //根据uidOldToken结果,如果账号被顶掉的app是ios且token有变化
                {
                    push_cli_ptr->buildPushMsg(uidOldToken.MobileType(), uidOldToken.Token(), csmain::PUSH_MSG_TYPE_FORCE_LOGOUT, kv, uidOldToken.OemName());
                }
            }
        }
    }

    g_accSer_ptr->RemoveReportStatuConn(conn);//删除上报状态超时检测
    
    //获取多套房所有房间的node
    PersonalAccountNodeInfoMap nodes;
    dbinterface::ResidentPersonalAccount::GetNodesByUserInfoUUID(stAppNode.user_info_uuid, nodes); 
    
    //更新连接列表中的相关设备信息,刷新个人终端用户app的信息到内存客户端链表节点上
    if (g_accSer_ptr->UpdatePersonnalAppNodeToLocal(conn, stAppNode, nodes) < 0)
    {
        AK_LOG_WARN << "UpdatePersonnalAppNodeToLocal failed.";
        return -1;
    }
    //插入端外推送列表中
    CMobileToken token(stAppNode.user, stAppNode.token, stAppNode.mobile_type, 1, stAppNode.voip_token);
    token.setAppToken(stAppNode.app_token);
    token.setCommonVersion(stAppNode.version);
    token.setAppVersion(stAppNode.app_version);
    token.setUidNode(stAppNode.node);
    token.setLanguage(stAppNode.language);
    token.setOemName(app_config.oem_name);

    CMobileToken tmp_token;
    GetAppPushTokenInstance()->getAppPushTokenByUid(stAppNode.user, tmp_token);
    token.setAppOem(tmp_token.AppOem());
    token.setIsDyIv(app_config.dynamics_iv);
    token.setIsMultiSite(tmp_token.IsMultiSite());

    CAkUserManager::GetInstance()->AddAkUserByNodeId(nodes, stAppNode.user, token);
    //更新sip-uid列表
    CAkUserManager::GetInstance()->UpdateSipUidList(app_config.sip, stAppNode.user);
    //刷新logout状态
    //CAkUserManager::GetInstance()->RemoveLogOutList(stAppNode.user);
    //app登陆后返回一些系統信息 chenzhx ********
    SOCKET_MSG_RESP_APPLOGIN stRespLogin = {0};
    dbinterface::Message::GetTextMsgLatestIDAndUnReadNum(stAppNode.user, stAppNode.node, app_config.lastread_message_id, stAppNode.manager_account_id, stRespLogin.last_msg_id, stRespLogin.unread_number);
    stRespLogin.last_msg_id = app_config.lastread_message_id;
    stRespLogin.is_expire = app_config.is_expire;
    stRespLogin.id_active = app_config.id_active;
    SOCKET_MSG stSocketMsg, dy_iv_socket;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    
    if(stAppNode.version < 6100)   //6.1版本已经在userconf接口返回给app
    {
        if (OnBuildAppLoginResponseMsg(stSocketMsg, dy_iv_socket, &stRespLogin) != 0)
        {
            AK_LOG_WARN << "OnBuildAppLoginResponseMsg failed";
            return -1;
        }
        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_socket);
    }

    
    dbinterface::ResidentPersonalAccount::UpdateLanguageByUserInfoUUID(stAppNode.language, stAppNode.user_info_uuid);
    
    //注册接口兼容uuid
    g_sm_client_ptr->RegUid(stAppNode.user, g_logic_srv_id, stAppNode.uuid);

    evpp::Any status(true);
    evpp::Any client(std::string(stAppNode.user));
    evpp::Any is_app(true);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX, status);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX, client);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_IS_APP, is_app);

    if (stAppNode.role == ACCOUNT_ROLE_COMMUNITY_PM
        || stAppNode.role == ACCOUNT_ROLE_COMMUNITY_MAIN
        || stAppNode.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT
        || stAppNode.role == ACCOUNT_ROLE_PERSONNAL_MAIN
        || stAppNode.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        csmain::DeviceType conn_type = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
        Main2ResidMsgHandle::Instance()->Send(stAppNode.user, conn_type, false, 
         (char*)&msg,  SOCKET_MSG_NORMAL_HEADER_SIZE + NTOHS(msg.data_size));         
    }
    else
    {
        csmain::DeviceType conn_type = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
        Main2ResidMsgHandle::Instance()->OfficeSend(stAppNode.user, conn_type, false, 
         (char*)&msg,  SOCKET_MSG_NORMAL_HEADER_SIZE + NTOHS(msg.data_size));         
    }        
    return 0;
}


//设备平台请求同一联动单元内的设备列表,用于个人终端用户
int CMsgControl::OnReqDevListMsg(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The param pRecvMsg is null.";
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed,please make sure the client device had registered.";
        return -1;
    }
    //dclient ver >=1 丢掉获取联系人
    if (deviceSetting.dclient_version >= D_CLIENT_VERSION_1_0)
    {
        AK_LOG_WARN << "Dclient Ver >=1 get device list,please use new way;";
        return -1;
    }
    AK_LOG_INFO << deviceSetting.mac << " get contact list.";
    TCHAR szIPAddress2[IP_SIZE];
    GetDeviceControlInstance()->GetCurrentIPAddr(szIPAddress2, sizeof(szIPAddress2) / sizeof(TCHAR));
    std::string ip_address = szIPAddress2;

    //社区的r27 29要获取整个单元或者社区主账号的信息。sip是发送组播号。
    if ((deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            && (!strncmp(deviceSetting.HWVer, "29.", 3) || !strncmp(deviceSetting.HWVer, "27.", 3)))
    {
        std::string mac = deviceSetting.mac;
        AK_LOG_INFO << "req dev list ,mac is " << deviceSetting.mac << " ,communit R27/29 ip is " <<  deviceSetting.outer_ip;
        SOCKET_MSG_PERSONNAL_DEV_LIST stDevList;
        memset(&stDevList, 0, sizeof(stDevList));

        std::vector<COMMUNITY_DEVICE_SIP> device;
        //不能通过tcp连接列表来查询联动单元的设备,要通过查询数据库才行,只查询到从账号,主账号在后面添加
        if (DaoGetCommunityPublicAppMaster(deviceSetting.grade, deviceSetting.manager_account_id, deviceSetting.unit_id, device) != 0)
        {
            AK_LOG_WARN << "Get personnal device list failed.";
            //return -1;
            stDevList.result = EXT_ERR_DAO;
        }
        //szSIPAccount改为组播号
        //std::string& trim(std::string &);
        for (auto& dev : device)
        {
            std::string strSipGroup = dev.sip_account;
            //strSipGroup = strSipGroup.trim();
            strSipGroup = strSipGroup.substr(0, strSipGroup.length() - 2);
            strSipGroup += "00";
            ::snprintf(dev.sip_account, sizeof(dev.sip_account), "%s", const_cast<char*>(strSipGroup.c_str()));
        }
        {
            SOCKET_MSG socketMsg;
            memset(&socketMsg, 0, sizeof(SOCKET_MSG));
            SOCKET_MSG* socket_message = &socketMsg;
            if (BuildReqDevListAckMsg(socket_message, stDevList, device, mac) < 0) //默认出去的都是用对应的mac地址加密的
            {
                AK_LOG_WARN << "BuildReqDevListAckMsg failed.";
                return -1;
            }
            if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
            {
                AK_LOG_WARN << "Send personnal ReqDevListAckMsg failed.";
                return -1;
            }
        }
        return 1;
    }


    if (deviceSetting.device_type == csmain::PERSONNAL_DEV)//个人终端
    {
        std::string mac = deviceSetting.mac;
        AK_LOG_INFO << "req dev list ,mac is " << deviceSetting.mac << ", node is " <<  deviceSetting.device_node << ", ip is " << deviceSetting.outer_ip;
        SOCKET_MSG_PERSONNAL_DEV_LIST stDevList;
        memset(&stDevList, 0, sizeof(stDevList));
        if (ProcessReqDevListMsg(pNormalMsg, stDevList, mac) < 0)
        {
            AK_LOG_WARN << "ProcessReqDevListMsg failed.";
            //return -1;
            stDevList.result = EXT_ERR_PARSE_XML_MSG;
        }

        std::vector<PERSONNAL_DEVICE_SIP> device;
        //不能通过tcp连接列表来查询联动单元的设备,要通过查询数据库才行,只查询到从账号,主账号在后面添加
        if (DaoGetPeronnalDevListByNode(deviceSetting.device_node, deviceSetting.type, device) != 0)
        {
            AK_LOG_WARN << "Get personnal device list failed.";
            //return -1;
            stDevList.result = EXT_ERR_DAO;
        }
        {
            SOCKET_MSG socketMsg;
            memset(&socketMsg, 0, sizeof(SOCKET_MSG));
            SOCKET_MSG* socket_message = &socketMsg;
            if (BuildReqDevListAckMsg(socket_message, stDevList, device, mac) < 0) //默认出去的都是用对应的mac地址加密的
            {
                AK_LOG_WARN << "BuildReqDevListAckMsg failed.";
                return -1;
            }
            if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
            {
                AK_LOG_WARN << "Send personnal ReqDevListAckMsg failed.";
                return -1;
            }
        }
    }
    else if (deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)//社区
    {
        std::string mac = deviceSetting.mac;
        AK_LOG_INFO << "req dev list ,mac is " << deviceSetting.mac << ", node is " << deviceSetting.device_node << ",  ip is " << deviceSetting.outer_ip;
        SOCKET_MSG_PERSONNAL_DEV_LIST stDevList; //TODO: SOCKET_MSG_PERSONNAL_DEV_LIST社区和个人目前公用
        memset(&stDevList, 0, sizeof(stDevList));
        if (ProcessReqDevListMsg(pNormalMsg, stDevList, mac) < 0)
        {
            AK_LOG_WARN << "ProcessReqDevListMsg failed.";
            //return -1;
            stDevList.result = EXT_ERR_PARSE_XML_MSG;
        }

        std::vector<COMMUNITY_DEVICE_SIP> device;
        //不能通过tcp连接列表来查询联动单元的设备,要通过查询数据库才行,只查询到从账号,主账号在后面添加
        if (DaoGetCommunityDevListByNode(deviceSetting.device_node, deviceSetting.type, device) != 0)
        {
            AK_LOG_WARN << "Get personnal device list failed.";
            //return -1;
            stDevList.result = EXT_ERR_DAO;
        }
        {
            SOCKET_MSG socketMsg;
            memset(&socketMsg, 0, sizeof(SOCKET_MSG));
            SOCKET_MSG* socket_message = &socketMsg;
            if (BuildReqDevListAckMsg(socket_message, stDevList, device, mac) < 0) //默认出去的都是用对应的mac地址加密的
            {
                AK_LOG_WARN << "BuildReqDevListAckMsg failed.";
                return -1;
            }
            if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
            {
                AK_LOG_WARN << "Send personnal ReqDevListAckMsg failed.";
                return -1;
            }
        }
    }

    return 0;

}

//app设置是否接收平台下发的motion alert的通知消息
int CMsgControl::OnSetRecvMotionStatus(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    int type;
    if (ParseSetMotionAlertMsg(pNormalMsg, type) < 0)
    {
        AK_LOG_WARN << "ParseSetMotionAlertMsg failed.";
        return -1;
    }

    DevicePtr dev;
    if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
    {
        AK_LOG_WARN << "GetClientFromConn failed.";
        return -1;
    }
    if (!dev->IsApp())
    {
        AK_LOG_WARN << "the tcp conn of app still not report status yet. close connect!";
        conn->Close();
        return -1;
    }

    std::string uid;
    if (dev->GetPerUid(uid) != 0)
    {
        AK_LOG_WARN << "Not App, so is error when geting UID. Disconnect make it reconnect";
        conn->Close();
        return -1;
    }

    AK_LOG_INFO << uid.c_str() << " app set recv motion alert. statu:" << type;
    //先写redis,再写应用缓存
    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn("appconf"); //获取与redis实例的tcp连接
    if (cache_conn)
    {
        std::string key = "motion";
        long ret = cache_conn->hset(key, uid, Int2String(type));
        if (ret == -1)
        {
            AK_LOG_WARN << "hset motion failed, uid is " << uid.c_str() << ", value is " <<  type;
        }
        cache_manager->RelCacheConn(cache_conn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csmain motion";
    }

    return 0;
}

//app上报对设备进行布防、撤防的信令
int CMsgControl::OnHandleDevArming(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "OnHandleDevArming param null.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    if (ParseReqArmingMsg(pNormalMsg, stArmingMsg) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.";
        return -1;
    }
    DevicePtr dev;
    if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
    {
        AK_LOG_WARN << "GetClientFromConn failed.";
        return -1;
    }
    if (!dev->IsApp())
    {
        AK_LOG_WARN << "the tcp conn of app is not personnal uid. close connect!";
        conn->Close();
        return -1;
    }
    std::string uid;
    if (dev->GetPerUid(uid) != 0)
    {
        AK_LOG_WARN << "Not App, so is error when geting UID. Disconnect make it reconnect";
        conn->Close();
        return -1;
    }

    AK_LOG_INFO << uid  << " app handle arming. mode:" << stArmingMsg.mode << " mac:" << stArmingMsg.mac;

    //通过nsq,通知csroute进行消息广播
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(stArmingMsg.mac);
    msg.set_action(stArmingMsg.szAction);
    msg.set_uid(uid);
    msg.set_mode(stArmingMsg.mode);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}

//设备(室内机)上报当前布防、撤防的状态给平台
int CMsgControl::OnReportArmingStatus(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    if (ParseReportArmingMsg(pNormalMsg, stArmingMsg, device_setting.mac) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.";
        return -1;
    }
    int oem  = device_setting.oem_id;

    if (oem == OEMID_ROBERT  || oem == OEMID_ROBERT2 )
    {
        if (stArmingMsg.resp_action == REPORT_ARMING_ACTION_TYPE_REBOOT)
        {
            AK_LOG_INFO << device_setting.mac << " device report arming. mode:" << stArmingMsg.mode << " but action type is reboot.ignore!";
            return -1;
        }
    }

    AK_LOG_INFO << device_setting.mac << " device report arming. mode:" << stArmingMsg.mode << " oem id:" << oem;
    Snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), device_setting.mac);

    //更新设备arming状态
    GetDeviceControlInstance()->UpdateDeviceArming(device_setting.mac, stArmingMsg.mode, device_setting.is_personal);

    //判断是否是旧版本平台要求上报状态的
    if (!strncmp(stArmingMsg.uid, "OldReq", 6))
    {
        return 0;
    }
    
    // 推送arming状态给 Alexa    
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (strlen(device_setting.node_uuid) > 0 && 0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(device_setting.node_uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatus(device_setting.mac, traceid);
        AK_LOG_INFO << "alexa device arming notify web , mac :" << device_setting.mac << ", traceid : " << traceid;
    }

    std::string main_site;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(stArmingMsg.uid, main_site);
    
    //通过nsq,通知csroute进行消息广播
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(stArmingMsg.mac);
    msg.set_mode(stArmingMsg.mode);
    msg.set_uid(stArmingMsg.uid);
    msg.set_oem(oem);
    msg.set_resp_action(stArmingMsg.resp_action);
    msg.set_node(device_setting.device_node);
    msg.set_main_site(main_site);
    msg.set_home_sync(stArmingMsg.home_sync);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}

int CMsgControl::GetLogProjectUUID(const DEVICE_SETTING& device_setting, std::string& project_uuid)
{
    if (device_setting.init_status == 0)
    {
        ProjectInfo log_project;
        log_project.GetLogCaptureProjectUUID(device_setting, project_uuid);
    }
    else
    {
        if (device_setting.is_personal)
        {
            project_uuid = device_setting.node_uuid;
        }
        else
        {
            project_uuid = device_setting.project_uuid;
        }
    }

    return 0;
}

/*
//设备(室外机)上报动作消息(即Logs：呼叫，输入密码，卡开门等开门动作)给平台
// int CMsgControl::OnReportActLog(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
// {
//     if (NULL == pNormalMsg)
//     {
//         return -1;
//     }

//     DEVICE_SETTING device_setting;
//     if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
//     {
//         AK_LOG_WARN << "dev offline, OnReportActLog failed.";
//         return -1;
//     }

//     // 解析上报的log
//     SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;
//     memset(&act_msg, 0, sizeof(act_msg));
//     act_msg.mng_type = device_setting.is_personal;
//     act_msg.mng_id = device_setting.manager_account_id;
//     act_msg.unit_id = device_setting.unit_id;
//     act_msg.is_public = device_setting.is_public;
//     Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account), device_setting.sip_account);
//     Snprintf(act_msg.location, sizeof(act_msg.location), device_setting.location);
//     if (ParseReportActMsg(pNormalMsg, act_msg, device_setting.mac) < 0) //mac加密的
//     {
//         AK_LOG_WARN << "ParseReportActMsg failed.";
//         return -1;
//     }

//     // 回复ack
//     GetDeviceControlInstance()->SendAck(device_setting.mac, act_msg.msg_seq, MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS);

//     // initiator为空不往下处理
//     if (strlen(act_msg.initiator) == 0)
//     {
//         AK_LOG_WARN << "parameter error, Initiator is null. mac is" << act_msg.mac;
//         return -1;
//     }
    
<<<<<<< HEAD
//     // 个人查询时候 是根据mac或node属于它的过滤。那么对于公共设备的开门，应该记录对应开门人的node, 而对于物业mac就可以过滤出记录
//     Snprintf(act_msg.account, sizeof(act_msg.account), device_setting.device_node);//node
=======
    //解决设备未上报picName时，三方锁开门记录和设备开门记录无法关联的问题
    if (strlen(act_msg.pic_name) == 0)
    {
        Snprintf(act_msg.pic_name, sizeof(act_msg.pic_name), dbinterface::PersonalCapture::GetRandomPicName(device_setting.mac, gstAKCSConf.server_tag).c_str());
    }

    // 个人查询时候 是根据mac或node属于它的过滤。那么对于公共设备的开门，应该记录对应开门人的node, 而对于物业mac就可以过滤出记录
    Snprintf(act_msg.account, sizeof(act_msg.account), device_setting.device_node);//node
>>>>>>> d774842e49a0ef1e0671260d39dabf973f899c39

//     Snprintf(act_msg.mac, sizeof(act_msg.mac), device_setting.mac);
//     Snprintf(act_msg.dev_uuid, sizeof(act_msg.dev_uuid), device_setting.uuid);

//     // 所用的key
//     Snprintf(act_msg.key, sizeof(act_msg.key), act_msg.initiator);

//     //linker msg init
//     LINKER_NORMAL_MSG linker_msg;
//     memset(&linker_msg, 0, sizeof(linker_msg));
//     if (device_setting.init_status == 0)
//     {
//         ProjectInfo project(device_setting.mac, device_setting.is_personal, linker_msg);
//     }
//     else
//     {
//         GetLinkerMsgFromDevice(device_setting, linker_msg);
//     }

//     //获取日志分片所需的project_uuid
//     std::string log_project_uuid;
//     GetLogProjectUUID(device_setting, log_project_uuid);
//     Snprintf(act_msg.project_uuid, sizeof(act_msg.project_uuid), log_project_uuid.c_str());

//     int handle_mode = RecordActLog::GetInstance().HandleMode(act_msg, device_setting);
//     AK_LOG_INFO << device_setting.mac << " device report activity logs. type:" << act_msg.act_type << " initiator:" << act_msg.initiator << " handle_mode:" << handle_mode;

//     if (handle_mode == NEW_COMMUNITY_NEW_DEVICE)
//     {
//         if (strlen(act_msg.per_id) < 2)
//         {
//             //自动开门及快递开门类型
//             if(RecordActLog::GetInstance().EmergencyType(act_msg) || act_msg.act_type == ACT_OPEN_DOOR_TYPE::DELIVERY_UNLOCK)
//             {
//                 Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.initiator);
//                 Snprintf(act_msg.key, sizeof(act_msg.key), "--");
//             }
//             else
//             {
//                 Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");
//             }
//             RecordActLog::GetInstance().SetCaptureAction(act_msg);
//         }
//         else
//         {
//             RecordActLog::GetInstance().NewModeHandle(act_msg, device_setting);
//         }
//         //新社区个人设备记录三方锁日志
//         if (act_msg.is_public == ACT_LOG_PERSONAL_DEV)
//         {
//             PostThirdPartyLog(act_msg, device_setting);
//         }

//         //家居开门通知
//         PushLinKerOpenDoor(act_msg, linker_msg);
//         if (act_msg.act_type == CMsgControl::ActOpenDoorType::INWARD_UNLOCK)  //内开门
//         {
//             AK_LOG_INFO << device_setting.mac << " inward unlock";
//             // 直接返回，后续有需要在扩展
//             return 0;
//         }

//         // dormakaba开门通知
//         OpenDormakabaLockNotify(act_msg, device_setting);

<<<<<<< HEAD
//         // SL20锁开门通知
//         //OpenSL20LockNotify(act_msg, device_setting);
//         // salto开门通知
//         OpenSaltoLockNotify(act_msg, device_setting);
=======
        // SL20锁开门通知
        //OpenSL20LockNotify(act_msg, device_setting);
        // salto开门通知
        OpenSaltoLockNotify(act_msg, device_setting);
        //ITec
        OpenItecLockNotify(act_msg, device_setting);
        //TT
        OpenTTLockNotify(act_msg, device_setting);
>>>>>>> d774842e49a0ef1e0671260d39dabf973f899c39

//         if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
//         {
//             AK_LOG_WARN << "Add personnal motion capture failed.";
//             return -1;
//         }
//         return 0;
//     }
//     else if (handle_mode == NEW_COMMUNITY_OLD_DEVICE)
//     {
//         RecordActLog::GetInstance().NewCommunityOldDeviceHandle(act_msg, device_setting);
        
//         //家居开门通知
//         PushLinKerOpenDoor(act_msg, linker_msg);
        
//         // dormakaba开门通知
//         OpenDormakabaLockNotify(act_msg, device_setting);

//         // SL20锁开门通知
//         //OpenSL20LockNotify(act_msg, device_setting);

<<<<<<< HEAD
//         // salto开门通知
//         OpenSaltoLockNotify(act_msg, device_setting);
=======
        // salto开门通知
        OpenSaltoLockNotify(act_msg, device_setting);
        //ITec
        OpenItecLockNotify(act_msg, device_setting);
        //TT
        OpenTTLockNotify(act_msg, device_setting);
>>>>>>> d774842e49a0ef1e0671260d39dabf973f899c39

        
//         if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
//         {
//             AK_LOG_WARN << "Add personnal motion capture failed.";
//             return -1;
//         }
//         return 0;
//     }
//     else
//     {
//         //OLD_MODE
//     }

//     if (act_msg.act_type == CMsgControl::ActOpenDoorType::CALL)
//     {
//         RecordActLog::GetInstance().RecordCallLog(act_msg, device_setting);
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::TMPKEY)
//     {
//         //写死'visitor'
//         Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");
//         PersonalTempKeyUserInfo tempkey_user_info;

//         RecordActLog::GetInstance().RecordTmpKeyLog(act_msg, device_setting, tempkey_user_info);
//         if ((!act_msg.resp) && (tempkey_user_info.creator.size()  > 0))
//         {
//             //Tmpkey使用通知创建者
//             AK::Server::P2PMainSendTmpkeyUsed msg;
//             msg.set_account(tempkey_user_info.creator);
//             msg.set_name(tempkey_user_info.name);
//             //6.6一人多套房修改，新增主站点字段
//             std::string main_site;
//             dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(tempkey_user_info.creator, main_site);
//             msg.set_main_site(main_site);
//             CAkcsPdu pdu;
//             pdu.SetMsgBody(&msg);
//             pdu.SetHeadLen(sizeof(PduHeader_t));
//             pdu.SetVersion(50);
//             pdu.SetCommandId(AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ);
//             pdu.SetSeqNum(0);
//             g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

//             //tmpkey使用通知家居
//             PushLinKerTmpKey(act_msg, tempkey_user_info, linker_msg);
//         }
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::LOCALKEY)
//     {
//         //写死'visitor'
//         Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");
//         RecordActLog::GetInstance().RecordLocalKeyLog(act_msg, device_setting);
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::RFCARD)
//     {
//         RecordActLog::GetInstance().RecordRfCardLog(act_msg, device_setting);
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::FACE)
//     {
//         RecordActLog::GetInstance().RecordFaceLog(act_msg, device_setting);
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::REMOTE_OPEN_DOOR)
//     {
//         RecordActLog::GetInstance().RecordRemoteLog(act_msg);
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::TEMP_CAPTURE)  //测温上报
//     {
//         Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.initiator);
//         if (dbinterface::PersonalCapture::AddTemperatureCapture(act_msg) < 0)
//         {
//             AK_LOG_WARN << "Add  temperature capture failed.";
//             return -1;
//         }
//         return 0;
//     }
//     else if (act_msg.act_type == CMsgControl::ActOpenDoorType::INWARD_UNLOCK)  //内开门
//     {
//         AK_LOG_INFO << device_setting.mac << " inward unlock";
//         // 直接通知家居开门通知，后续
//         PushLinKerOpenDoor(act_msg, linker_msg);
//         return 0;
//     }
//     else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::HANDSET_UNLOCK)
//     {
//         RecordActLog::GetInstance().RecordHandsetLog(act_msg);
//     }
//     else
//     {
//         //add by chenzhx 20230104
//         //门禁E16旧版本会上报一个type=7CALL_UNLOCK_INDOOR,但是这个其实是他们本地input开门方式。
//         //这里进行兼容也是为了防止设备上报了个区分不了的类型，然后因为没有记录日志，csstorage根据picname更新不到导致告警。
//         //去掉，不然网页记录会有问题。csstorage先不进行这部分的告警
//         //Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.initiator);
//         //Snprintf(act_msg.key, sizeof(act_msg.key), "--");
//         //Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");        
//         AK_LOG_WARN << "invalid open door active type: " << act_msg.act_type;
//         return -1;
//     }

//     CommunityInfo community_info(device_setting.manager_account_id);
//     int is_new = community_info.GetIsNew();
//     //单住户及新社区个人设备记录三方锁日志
//     if (act_msg.is_public == ACT_LOG_PERSONAL_DEV && (act_msg.mng_type || is_new))
//     {
//         PostThirdPartyLog(act_msg, device_setting);
//     }

//     // 家居开门通知
//     PushLinKerOpenDoor(act_msg, linker_msg);

//     // dormakaba开门通知
//     OpenDormakabaLockNotify(act_msg, device_setting);

//     // SL20锁开门通知
//     //OpenSL20LockNotify(act_msg, device_setting);

<<<<<<< HEAD
//     // salto开门通知
//     OpenSaltoLockNotify(act_msg, device_setting);
=======
    // salto开门通知
    OpenSaltoLockNotify(act_msg, device_setting);
    //ITec
    OpenItecLockNotify(act_msg, device_setting);
    //TT
    OpenTTLockNotify(act_msg, device_setting);
>>>>>>> d774842e49a0ef1e0671260d39dabf973f899c39


//     if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
//     {
//         AK_LOG_WARN << "Add personnal motion capture failed.";
//         return -1;
//     }
//     return 0;
// }
*/

//三方锁日志处理
int CMsgControl::PostThirdPartyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& device_setting)
{
    ThirdPartyLockDevList third_devlist;
    int default_relay = DoornumToRelayStatus(act_msg.relay);
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(device_setting.device_node, account))
    {
        return -1;
    }

    if (0 == dbinterface::ThirdPartyLockDevice::GetThirdPartyLockDevlistByMac(act_msg.mac, third_devlist))
    {
        int qrio_relay = 0;
        int yale_relay = 0;

        for (const auto& third_dev : third_devlist)
        {
            if (third_dev.lock_type == ThirdPartyLockType::QRIO && 
                strcmp(account.uuid, third_dev.personal_uuid) == 0)
            {
                qrio_relay = third_dev.relay;
                AK_LOG_INFO << "Qrio_relay_value: " << qrio_relay << ", DefaultRelay: " << default_relay 
                    << ", personal_account_uuid: " << third_dev.personal_uuid;

                if (qrio_relay & default_relay)
                {
                    //设备绑定第三方锁且本次开门有开第三方锁绑定的relay
                    PushLinKerThirdPartyLog(act_msg, third_dev);
                }
            }
            else if (third_dev.lock_type == ThirdPartyLockType::YALE &&
                strcmp(account.uuid, third_dev.personal_uuid) == 0)
            {
                yale_relay = third_dev.relay;
                AK_LOG_INFO << "Yale_relay_value: " << yale_relay << ", DefaultRelay: " << default_relay 
                    << ", personal_account_uuid: " << third_dev.personal_uuid;

                if (yale_relay & default_relay)
                {
                    //设备绑定第三方锁且本次开门有开第三方锁绑定的relay
                    PushLinKerThirdPartyLog(act_msg, third_dev);
                }
            }
        } 
    }

    return 0;
}

void CMsgControl::OpenSaltoLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting)
{
    // salto lock link设备后,权限同设备绑定,设备开启对应relay成功,才通知salto lock开门
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return;
    }

    SaltoLockInfoList salto_lock_list;
    dbinterface::SaltoLock::GetSaltoLockListByDeviceUUID(dev_setting.uuid, salto_lock_list);
    if (salto_lock_list.size() == 0)
    {
        return;
    }

    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // salto lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& salto_lock : salto_lock_list)
    {
        if (salto_lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;
            item["link_mac"] = dev_setting.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = salto_lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = salto_lock.third_uuid;
            item["initiator"] = act_msg.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::SALTO;
            item["personal_account_uuid"] = initiator_account.uuid;
            AK_LOG_INFO << "open saltolock success, link mac=" << dev_setting.mac
                        << ", saltolock bind relay=" << salto_lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << salto_lock.name << ", lock uuid=" << salto_lock.third_uuid
                        << ", personal_account_uuid=" << initiator_account.uuid;

            std::string data_json = fast_writer.write(item);
            SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_SALTO_OPEN_DOOR, data_json, salto_lock.third_uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open saltolock failed, maybe bind other lock, saltolock bind relay=" << salto_lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }
}

void CMsgControl::OpenDormakabaLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting)
{
    // dormakaba lock link设备后,权限同设备绑定,设备开启对应relay成功,才通知dormakaba lock开门
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE) 
    {
        return;
    }

    DormakabaLockInfoList dormakaba_lock_list;
    dbinterface::DormakabaLock::GetDormakabaLockListByDeviceUUID(dev_setting.uuid, dormakaba_lock_list);
    if (dormakaba_lock_list.size() == 0)
    {
        return;
    }
    
    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // dormakaba lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& dormakaba_lock : dormakaba_lock_list)
    {
        if (dormakaba_lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;

            item["role"] = initiator_account.role;
            item["link_mac"] = dev_setting.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = dormakaba_lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = dormakaba_lock.third_uuid;
            item["initiator"] = act_msg.initiator_sql;
            item["account"] = initiator_account.account;
            item["lock_type"] = ThirdPartyLockType::DORMAKABA;
            item["personal_account_uuid"] = initiator_account.uuid;

            AK_LOG_INFO << "open dormakabalock success, link mac = " << dev_setting.mac 
                        << ", dormakabalock bind relay = " << dormakaba_lock.relay << ", report_open_relay = " << report_open_relay 
                        << ", lock name = " << dormakaba_lock.name << ", lock uuid = " << dormakaba_lock.third_uuid 
                        << ", personal_account_uuid = " << initiator_account.uuid << ", account = " << initiator_account.account << ", role = " << initiator_account.role;
            
            std::string data_json = fast_writer.write(item);
            SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_DORMAKABA_OPEN_DOOR, data_json, dormakaba_lock.third_uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open dormakabalock failed, maybe bind other lock, dormakabalock bind relay = " << dormakaba_lock.relay << ", report_open_relay = " << report_open_relay << ", personal_account_uuid = " << initiator_account.uuid;
        }
    }
}

void CMsgControl::OpenItecLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting)
{
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return;
    }

    ITecLockInfoList lock_list;
    dbinterface::ITecLock::GetItecLockListByDeviceUUID(dev_setting.uuid, lock_list);
    if (lock_list.size() == 0)
    {
        return;
    }

    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // 绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& lock : lock_list)
    {
        if (lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;
            item["link_mac"] = dev_setting.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = lock.uuid;            
            item["lock_id"] = lock.lock_id;
            item["initiator"] = act_msg.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::ITEC;
            item["personal_account_uuid"] = initiator_account.uuid;
            item["role"] = initiator_account.role;
            item["account"] = initiator_account.account;

            AK_LOG_INFO << "open itec lock success, link mac=" << dev_setting.mac
                        << ", itec lock bind relay=" << lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << lock.name << ", lock uuid=" << lock.uuid << ", lock_id=" << lock.lock_id
                        << ", personal_account_uuid=" << initiator_account.uuid
                        << ", account = " << initiator_account.account << ", role = " << initiator_account.role;

            std::string data_json = fast_writer.write(item);
            SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_ITEC_OPEN_DOOR, data_json, lock.uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open itec failed, maybe bind other lock, itec lock bind relay=" << lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }
}

void CMsgControl::OpenTTLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting)
{
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return;
    }

    TtLockInfoList lock_list;
    dbinterface::TtLock::GetTtLockListByDeviceUUID(dev_setting.uuid, lock_list);
    if (lock_list.size() == 0)
    {
        return;
    }

    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // 绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& lock : lock_list)
    {
        if (lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;
            item["link_mac"] = dev_setting.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = lock.uuid;            
            item["lock_id"] = lock.lock_id;
            item["initiator"] = act_msg.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::TT;
            item["personal_account_uuid"] = initiator_account.uuid;
            item["role"] = initiator_account.role;
            item["account"] = initiator_account.account;
            
            AK_LOG_INFO << "open tt lock success, link mac=" << dev_setting.mac
                        << ", tt lock bind relay=" << lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << lock.name << ", lock uuid=" << lock.uuid << ", lock_id=" << lock.lock_id
                        << ", personal_account_uuid=" << initiator_account.uuid
                        << ", account = " << initiator_account.account << ", role = " << initiator_account.role;

            std::string data_json = fast_writer.write(item);
            SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_TT_OPEN_DOOR, data_json, lock.uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open tt failed, maybe bind other lock, tt lock bind relay=" << lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }
}



void CMsgControl::UpdateSL20Lock(const std::string& lock_uuid, const std::string& mac, const std::string& pic_name)
{
    SafeCacheConn cache_conn(g_redis_db_sl20_lock);
    if (!cache_conn.isConnect())
    {
        return;
    }
    if (!cache_conn.isExists(lock_uuid))
    {
        std::string pic = "picname_"+ pic_name;
        cache_conn.hset(lock_uuid, mac, pic);
        AK_LOG_INFO <<"UpdateSL20Lock [lock_uuid] "<<lock_uuid;
        cache_conn.expire(lock_uuid, gstAKCSConf.sl20_opendoor_expire);
    }
    return;
}

void CMsgControl::OpenSL20LockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting)
{   
    // SL20 lock link设备后,权限同设备绑定,设备开启对应relay成功,才通知SL20 lock开门
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE) 
    {
        return;
    }
    SL20LockInfoList sl20_lock_list;
    dbinterface::SL20Lock::GetSL20LockListByDeviceUUID(dev_setting.uuid, sl20_lock_list);
    if (sl20_lock_list.size() == 0)
    {
        return;
    }       
    // sl20 lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const SL20LockInfo& sl20_lock : sl20_lock_list)
    {  
        int sl20_lock_relay = ConvertDoornumToRelayStatus(sl20_lock.relay);
        if (sl20_lock_relay & report_open_relay)
        {
            UpdateSL20Lock(sl20_lock.uuid, sl20_lock.mac, act_msg.pic_name);
        }
        else
        {
            AK_LOG_INFO << "open sl20lock failed, maybe bind other lock, sl20lock bind relay = " << sl20_lock.relay << ", report_open_relay = " << report_open_relay ;
        }
    }
}

int CMsgControl::PushLinKerThirdPartyLog(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ThirdPartyLockDeviceInfo& third_dev)
{
    Json::Value item;
    Json::FastWriter w;
    
    item["uuid"] = third_dev.dev_uuid;
    item["lock_type"] = third_dev.lock_type;
    item["capture_type"] = act_msg.act_type;
    item["initiator"] = act_msg.initiator_sql;
    item["pic_name"] = act_msg.pic_name;
    item["personal_account_uuid"] = third_dev.personal_uuid;
    std::string data_json = w.write(item);
    int msg_type;
    if (third_dev.lock_type == ThirdPartyLockType::QRIO)
    {
        msg_type = LinkerPushMsgType::LINKER_MSG_TYPE_QRIO_OPEN_DOOR;
    }
    else if (third_dev.lock_type == ThirdPartyLockType::YALE)
    {
        msg_type = LinkerPushMsgType::LINKER_MSG_TYPE_YALE_OPEN_DOOR;
    }

    SendLinKerCommonMsg(msg_type, data_json, third_dev.dev_uuid);
    return 0;
}

int CMsgControl::GetLinkerMsgFromDevice(const DEVICE_SETTING& device_setting, LINKER_NORMAL_MSG &linker_msg)
{
    Snprintf(linker_msg.dev_uuid, sizeof(linker_msg.dev_uuid), device_setting.uuid);
    Snprintf(linker_msg.dev_name, sizeof(linker_msg.dev_name), device_setting.location);
    linker_msg.dev_grade = device_setting.grade;
    linker_msg.dev_type = device_setting.type;
    linker_msg.project_type = device_setting.project_type;
    linker_msg.enable_smarthome = device_setting.enable_smarthome;
    Snprintf(linker_msg.project_uuid, sizeof(linker_msg.project_uuid), device_setting.project_uuid);
    Snprintf(linker_msg.ins_uuid, sizeof(linker_msg.ins_uuid), device_setting.ins_uuid);
    return 0;
}

int CMsgControl::PushLinKerOpenDoor(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    // 室内机开门
    if (act_msg.act_type == CMsgControl::ActOpenDoorType::CLOUD_REMOTE_UNLOCK_INDOOR) {
        item["indoor_sip"] = act_msg.initiator;
    }
    item["account_uuid"] = act_msg.account_uuid;
    item["open_type"] = act_msg.act_type;
    item["key"] = act_msg.key;
    item["response"] = act_msg.resp;
    item["mac"] = act_msg.mac;
    //act_msg.account是主账户
    if (strlen(act_msg.account))
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if(0 == dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, account))
        {
            item["node_uuid"] = account.uuid;
        }
    }
    item["account_name"] = act_msg.initiator_sql;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_OPENDOOR, data_json, linker_msg.dev_uuid);
    return 0;
}

int CMsgControl::PushLinKerTmpKey(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const PersonalTempKeyUserInfo &tmpkey_info, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    item["tmpkey_name"] = tmpkey_info.name;
    //act_msg.account是主账户
    if (strlen(act_msg.account))
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, account))
        {
            item["node_uuid"] = account.uuid;
            item["language"] = account.language;
        }
    }
    ResidentPerAccount creator_account;
    memset(&creator_account, 0, sizeof(creator_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(tmpkey_info.creator, creator_account))
    {
        item["account_uuid"] = creator_account.uuid;
        // 后续通知也用到
        Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), creator_account.uuid);
    }
    
    item["account_name"] = act_msg.initiator_sql;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_TMPKEY, data_json, linker_msg.dev_uuid);
    return 0;
}


int CMsgControl::PushLinKerDelivery(SOCKET_MSG_DEV_SEND_DELIVERY* delivery, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    item["amount"] = delivery->amount;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_DELIVERY, data_json, linker_msg.dev_uuid);
    return 0;
}

int CMsgControl::PushLinKerText(const PersoanlMessageSend& text_msg, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    item["title"] = text_msg.text_message.title;
    item["content"] = text_msg.text_message.content;
    item["notice_name"] = linker_msg.dev_name;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_MESSAGE, data_json, linker_msg.account_uuid);
    return 0;
}

void CMsgControl::FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item)
{
    item["dev_uuid"] = linker_msg.dev_uuid;
    item["dev_name"] = linker_msg.dev_name;
    item["dev_type"] = linker_msg.dev_type;
    item["dev_grade"] = linker_msg.dev_grade;
    item["account_uuid"] = linker_msg.account_uuid;
    item["node_uuid"] = linker_msg.node_uuid;
    item["account_name"] = linker_msg.account_name;
    item["language"] = linker_msg.language;
    item["project_type"] = linker_msg.project_type;
    item["project_uuid"] = linker_msg.project_uuid;
    item["timestamp"] = GetCurrentMilliTimeStamp();
    item["ins_uuid"] = linker_msg.ins_uuid;
    item["enable_smarthome"] = linker_msg.enable_smarthome;
}

int CMsgControl::SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key)
{
    AK::Linker::P2PRouteLinker msg;
    msg.set_message_type(msg_type);
    msg.set_msg_json(data_json);
    msg.set_key(key);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}

//app上报logout的状态通知给平台,无消息体
int CMsgControl::OnReportLogOut(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn, const char* remote_ip)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    DevicePtr dev;
    if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
    {
        AK_LOG_WARN << "GetClientFromConn failed.";
        return -1;
    }
    if (!dev->IsApp())
    {
        AK_LOG_WARN << "the tcp conn of app is not personnal uid. close connect!";
        conn->Close();
        return -1;
    }

    //设置uid处于logout状态
    std::string main_site;
    //获取主站点user
    if (dev->GetPerMainSiteUid(main_site) != 0)
    {
        AK_LOG_WARN << "Not App, so is error when geting UID.";
    }
    
    AK_LOG_INFO << main_site.c_str() << " app logout.";
    CAkUserManager::GetInstance()->SetLogOutStatus(main_site);

    std::string user_info_uuid;
    dev->GetUserInfoUUID(user_info_uuid);
    PersonalAccountNodeInfoMap nodes;
    dbinterface::ResidentPersonalAccount::GetNodesByUserInfoUUID(user_info_uuid, nodes);
    CAkUserManager::GetInstance()->RemoveAkUserByNodeId(nodes, main_site);    

    // 通知csresid,app下线
    g_accSer_ptr->NotifyConnInfoMsg(dev, CONN_INFO_TYPE::LOGOUT);

    // 发送ack给app,通知logout成功
    GetDeviceControlInstance()->SendAppLogoutAckMsg(conn);

    // 关闭对端连接
    conn->Close();
    model::AuditLogInfo audit_log_info;
    memset(&audit_log_info, 0, sizeof(audit_log_info));
    Snprintf(audit_log_info.ip, sizeof(audit_log_info.ip), remote_ip);
    Snprintf(audit_log_info.audit_operator, sizeof(audit_log_info.audit_operator), main_site.c_str());
    audit_log_info.type = model::AUDIT_TYPE_LOG_OUT;
    snprintf(audit_log_info.key_info, sizeof(audit_log_info.key_info), "[%s]", main_site.c_str());

    SOCKET_MSG_PERSONNAL_APP_CONF app_conf;
    memset(&app_conf, 0, sizeof(app_conf));
    Snprintf(app_conf.user, sizeof(app_conf.user), main_site.c_str());
    //如果找不到账户信息,那么不写审计日志
    if (DaoGetNodeByAppUser(app_conf) < 0)
    {
        return 0;
    }
    const char* opera_type = model::AuditLog::GetInstance().GetOperaType(app_conf.role);
    Snprintf(audit_log_info.opera_type, sizeof(audit_log_info.opera_type), opera_type);
    if (model::AuditLog::GetInstance().GetDistributor(audit_log_info, app_conf.role, app_conf.manager_account_id) < 0)
    {
        AK_LOG_WARN << "GetDistributor Failed,role=" << app_conf.role << ";parent_id=" << app_conf.manager_account_id;
        return 0;
    }

    model::AuditLog::GetInstance().InsertAuditLog(audit_log_info);

    return 0;
}

//设备要求校验dtmf是否允许开门
int CMsgControl::OnCheckDtmf(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    SOCKET_MSG_CHECK_DTMF stCheckDtmfMsg;
    memset(&stCheckDtmfMsg, 0, sizeof(stCheckDtmfMsg));
    if (ParseReqCheckDtmfMsg(pNormalMsg, stCheckDtmfMsg) < 0)
    {
        AK_LOG_WARN << "ParseReqCaptureMsg failed.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not dev.";
        return -1;
    }
    AK_LOG_INFO << device_setting.mac << " check dtmf allow to open door.";

    //check
    stCheckDtmfMsg.result = 0;
    if (strcmp(stCheckDtmfMsg.sip, device_setting.sip_account) == 0)
    {
        AK_LOG_WARN << "the dtmf'sip is same youself.";
        stCheckDtmfMsg.result = 0;
    }
    else
    {
        ACCOUNT_MSG stAccountMsg;
        memset(&stAccountMsg, 0, sizeof(stAccountMsg));

        char node[32] = {0};
        uint32_t manager_id = 0;
        uint32_t unit_id = 0;
        if (DaoAccountMsgBySipAccount(stCheckDtmfMsg.sip, stAccountMsg) < 0)
        {
            DEVICE_SETTING SipDev;
            memset(&SipDev, 0, sizeof(SipDev));
            if (GetDeviceControlInstance()->GetDeviceSettingBySip(stCheckDtmfMsg.sip, &SipDev) < 0)
            {
                AK_LOG_WARN << "the dtmf'sip is not in PersonnalAccount and Device list.";
            }
            else
            {
                ::snprintf(node, sizeof(node), "%s", SipDev.device_node);
                manager_id = SipDev.manager_account_id;
                unit_id = SipDev.unit_id;
            }
        }
        else
        {
            ::snprintf(node, sizeof(node), "%s", stAccountMsg.node);
            manager_id = stAccountMsg.manager_id;
            unit_id = stAccountMsg.unit_id;
        }

        if (device_setting.type == csmain::PERSONNAL_DEV ||
                (device_setting.type == csmain::COMMUNITY_DEV && device_setting.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL))//个人的设备，只有联动能开
        {
            if (strcmp(node, device_setting.device_node) == 0)
            {
                stCheckDtmfMsg.result = 1;
            }
            else
            {
                stCheckDtmfMsg.result = 0;
            }
        }
        else if (device_setting.type == csmain::COMMUNITY_DEV)
        {
            if (device_setting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                if (device_setting.manager_account_id == manager_id)
                {
                    stCheckDtmfMsg.result = 1;
                }
                else
                {
                    stCheckDtmfMsg.result = 0;
                }
            }
            else if (device_setting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                if (device_setting.unit_id == unit_id)
                {
                    stCheckDtmfMsg.result = 1;
                }
                else
                {
                    stCheckDtmfMsg.result = 0;
                }
            }
        }
    }
    //send result
    SOCKET_MSG stSocketMsg, dy_iv_socket;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (OnBuildRespCheckDtmf(stSocketMsg, dy_iv_socket,  stCheckDtmfMsg) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_socket) < 0)
    {
        AK_LOG_WARN << "Send personnal ReportArming to dev failed.";
        return -1;
    }
    return 0;
}

//app 上报视频存储信令
int CMsgControl::OnVideoStorageAct(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_VIDEO_STORAGE stVideoStorageMsg;
    memset(&stVideoStorageMsg, 0, sizeof(stVideoStorageMsg));
    if (ParseVideoStorageMsg(pNormalMsg, stVideoStorageMsg) < 0)
    {
        AK_LOG_WARN << "ParseVideoStorageMsg failed.";
        return -1;
    }
    std::string uid = stVideoStorageMsg.uid;
    std::size_t mac_pos = uid.find('_');
    if (mac_pos == std::string::npos)
    {
        AK_LOG_WARN << "Video Storage Msg is invalid,Vid is " << uid;
        return -1;
    }
    std::string mac = uid.substr(0, mac_pos);

    std::string rtsp_pwd;
    DevType type = CAkDevManager::GetInstance()->GetTypeByMac(mac);
    if (type == PER_DEV)
    {
        if (GetPersonalDevicesInstance()->DaoGetMacRtspPwd(mac, rtsp_pwd) != 0)
        {
            AK_LOG_WARN << "get rtsp pwd of personal device fialed, mac is:" << mac.c_str();
            return -1;
        }
    }
    else if (type == PUB_DEV)
    {
        if (GetDeviceSettingInstance()->DaoGetMacRtspPwd(mac, rtsp_pwd) != 0)
        {
            AK_LOG_WARN << "get rtsp pwd of public device fialed, mac is:" << mac.c_str();
            return -1;
        }
    }
    else
    {
        AK_LOG_WARN << "get rtsp pwd of device fialed, mac is not exit " << mac.c_str();
        return -1;
    }
    VideoStorage::VideoStorageAction act = (stVideoStorageMsg.is_start_storage == 1) \
                                           ? VideoStorage::START_VIDEO_STORAGE : VideoStorage::STOP_VIDEO_STORAGE;

    std::string node;
    if (g_accSer_ptr->GetNodeByConn(conn, node) != 0)
    {
        LOG_WARN << "GetNodeByConn fialed";
        return -1;
    }

    //send rpc request for video storage action
    g_vs_client_ptr->VideoStorageAct(gstAKCSConf.csmain_outer_ip, stVideoStorageMsg.uid, rtsp_pwd, node, act);

    return 0;
}


//设备返回命令发送的数据
int CMsgControl::OnCommandResp(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_COMMAND_RESP stCommandRespMsg;
    memset(&stCommandRespMsg, 0, sizeof(stCommandRespMsg));
    if (ParseCommandRespMsg(pNormalMsg, stCommandRespMsg, device_setting.mac) < 0)
    {
        AK_LOG_WARN << "ParseCommandRespMsg failed.";
        return -1;
    }
    g_cliSer_prt->onDevRespContent(device_setting.mac, stCommandRespMsg);
    return 0;
}
//设备或者App推送告警处理完毕的消息给服务器
int CMsgControl::OnHeartBeatMsg(const evpp::TCPConnPtr& conn)
{
    /*
    DEVICE_SETTING device_setting;
    if(g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }*/
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (BuildHttpMaintenanceNoParamCmd(&stSocketMsg, NULL, MSG_TO_DEVICE_HEARBEAT_ACK) != 0)
    {
        AK_LOG_WARN << "BuildHttpMaintenanceNoParamCmd failed";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send OnHeartBeatMsg ACK to dev failed.";
        return -1;
    }

    DevicePtr dev;
    //通过连接从connections_获取devptr对象
    if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
    {
        AK_LOG_WARN << "get app or dev from conn failed.";
        return 0;
    }
    g_accSer_ptr->NotifyConnInfoMsg(dev, CONN_INFO_TYPE::HEARTBEAT);
    
    return 0;
}



//云平台下发同一联动单元内的设备列表发生变化的通知,
int CMsgControl::OnSendDevListChangeMsg(const evpp::TCPConnPtr& conn)
{
    int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));  //获取通信版本号,决定是否加密
    SOCKET_MSG socketMsg;
    memset(&socketMsg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socketMsg;
    if (BuildSendDevListChangeMsg(socket_message, ver) < 0)
    {
        AK_LOG_WARN << "BuildSendDevListChangeMsg failed.";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "Send personnal ReqDevListAckMsg failed.";
        return -1;
    }
    return 0;
}

//组装注销sip通知消息
int CMsgControl::BuildReqLogOutSipMsg(SOCKET_MSG* socket_message)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    int data_size = 0;
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_APP_LOGOUT_SIP, VERSION_1_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;
    return 0;
}


//组装设备退出联动系统的通知消息
int CMsgControl::BuildReQuitNodeMsg(SOCKET_MSG* socket_message)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    int data_size = 0;
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_QUIT_NODE, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;
    return 0;
}

//平台发送文本消息给设备\app
int CMsgControl::BuildTextMessageMsg(SOCKET_MSG &socket_message, SOCKET_MSG &dy_iv_socket_message, SOCKET_MSG_TEXT_MESSAGE* text_message)
{
    if (text_message == NULL)
    {
        return -1;
    }
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildTextMessageMsg(payload, sizeof(msg_normal->data), text_message) < 0)
    {
        return -1;
    }
    
    EncryptDefalutMacMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_SEND_TEXT_MESSAGE);    
    return 0;
}



//app 登陆后发送系统信息给app
int CMsgControl::OnBuildAppLoginResponseMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, SOCKET_MSG_RESP_APPLOGIN* pRespLogin)
{
    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildRespAppLoginMsg(payload, sizeof(msg_normal->data), pRespLogin) < 0)
    {
        AK_LOG_WARN << "BuildRespAppLoginMsg failed,PayLoad=" << payload;
        return -1;
    }

    EncryptDefalutMsg(socket_message, dy_iv_socket_message, MSG_TO_DEVICE_APP_LOGIN_RESP);
    return 0;
}


int CMsgControl::OnBuildAppForceLogoutMsg(SOCKET_MSG* socket_message)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    int data_size = 0;
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_APP_FORCE_LOGOUT, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::ChangeOpenDoorTypeRF2NfcBle(char* pcode, int& nOpenType)
{
    if (pcode && pcode[0] == 'F' && pcode[1] == '0'  && strlen(pcode) == 16 )
    {
        nOpenType = ActOpenDoorType::CLOUD_NFC;
    }
    else if (pcode && pcode[0] == 'B' && strlen(pcode) == 16 )
    {
        nOpenType = ActOpenDoorType::CLOUD_BLE;
    }
    return 0;
}
int CMsgControl::BuildHttpMaintenanceGetFileCommonCmd(SOCKET_MSG* socket_message, const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* stGetFile, uint16_t nCmd)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildReqGetFileMsg(payload, sizeof(msg_normal->data), stGetFile) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  nCmd, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::BuildHttpMaintenanceReconnectCmd(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const char* mac, const HTTP_MSG_DEV_RECONNECT_COMMON* stReconn, uint16_t nCmd)
{
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildReqDevReconnectMsg(payload, sizeof(msg_normal->data), stReconn) < 0)
    {
        return -1;
    }
    EncryptDefalutMsg(socket_message, dy_iv_socket_message, nCmd);
    return 0;
}

int CMsgControl::BuildHttpMaintenanceNoParamCmd(SOCKET_MSG* socket_message, const char* mac, uint16_t nCmd)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    //SOCKET_MSG_NORMAL *msg_normal = (SOCKET_MSG_NORMAL *)socket_message->data;

    //char *payload = (char *)msg_normal->data;
    int data_size = 0;
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  nCmd, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}



int CMsgControl::OnForwardFaceDataMsg(const SOCKET_MSG_DEV_REPORT_FACE_DATA& dev_face_data, const std::string dev_mac_list)
{
    AK::Server::P2PMainHandleForwardFaceData msg;
    msg.set_model_url(dev_face_data.model_url);
    msg.set_mac_list(dev_mac_list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_FACE_DATA_FORWARD_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}

//个人终端用户获取设备列表明细
int CMsgControl::BuildReqDevCodeMsg(SOCKET_MSG* socket_message, const std::string& code, const std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    //这个长度不能固定,因为设备端的长度也是固定最长4096,所以长度不能放开,在里面限制长度,超过长度的就截断
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    // '- 16'的目的是防止加密后超过4096, '-10'是因为dclient那边总的消息长度不能超过4096，所以要再减去消息头长度10
    if (GetMsgHandleInstance()->BuildRespDevCodeMsg(payload, sizeof(msg_normal->data) - 16 - 10, code) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_DEVICE_CODE, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;

    return 0;
}


//云平台下发设备码
int CMsgControl::OnSendDevCodeMsg(const evpp::TCPConnPtr& conn, const std::string& code, const std::string& mac)
{
    SOCKET_MSG socketMsg;
    memset(&socketMsg, 0, sizeof(SOCKET_MSG));
    // SOCKET_MSG *socket_message = &socketMsg;
    if (BuildReqDevCodeMsg(&socketMsg, code, mac) < 0)
    {
        AK_LOG_WARN << "BuildSendDevListChangeMsg failed.";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socketMsg.data, socketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal ReqDevListAckMsg failed.";
        return -1;
    }
    return 0;
}

int CMsgControl::BuildCleanDeviceCodeMsg(SOCKET_MSG* socket_message, std::string mac)
{
    if ((socket_message == NULL))
    {
        return -1;
    }
    memset(socket_message, 0, sizeof(SOCKET_MSG));
    int data_size = 0;
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message, MSG_TO_DEVICE_CLEAR_DEVICE_CODE, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message->size = data_size + head_size;
    return 0;
}


int CMsgControl::BuildDevCommandMsg(SOCKET_MSG* socket_message, const std::string& stCmd, const char* mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildSendDevCommandMsg(payload, sizeof(msg_normal->data), stCmd) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_CLI_COMMAND, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::ParseCallCaptureMsg(SOCKET_MSG_NORMAL* pNormalMsg, const std::string& mac, SOCKET_MSG_CALL_CAPTURE& call_capture)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    //int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;

    AesDecryptByMac(payload, payload, mac.c_str(), data_size);
    if (GetMsgHandleInstance()->ParseCallCaptureMsg(payload, call_capture) < 0)
    {
        AK_LOG_WARN << "ParseMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}


//通话截图
// int CMsgControl::OnCallCaptureReport(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
// {
//     if (NULL == pNormalMsg)
//     {
//         AK_LOG_WARN << "The input params is null.";
//         return -1;
//     }

//     DEVICE_SETTING device_setting;
//     if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
//     {
//         AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
//         return -1;
//     }
//     //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
//     SOCKET_MSG_CALL_CAPTURE stCallCaptureMsg;
//     memset(&stCallCaptureMsg, 0, sizeof(stCallCaptureMsg));
//     if (ParseCallCaptureMsg(pNormalMsg, device_setting.mac, stCallCaptureMsg) < 0)
//     {
//         AK_LOG_WARN << "ParseCallCaptureMsg failed.";
//         return -1;
//     }

//     SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;
//     memset(&act_msg, 0, sizeof(act_msg));
//     act_msg.mng_type = device_setting.is_personal;
//     act_msg.mng_id = device_setting.manager_account_id;
//     act_msg.unit_id = device_setting.unit_id;
//     act_msg.is_public = device_setting.is_public;
//     Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account), device_setting.sip_account);
//     Snprintf(act_msg.location, sizeof(act_msg.location), device_setting.location);
//     Snprintf(act_msg.account, sizeof(act_msg.account), device_setting.device_node);//node
//     Snprintf(act_msg.call_trace_id, sizeof(act_msg.call_trace_id), stCallCaptureMsg.call_trace_id);

//     AK_LOG_INFO << device_setting.mac << " device report call logs. callee = " << stCallCaptureMsg.callee << ", caller = " << stCallCaptureMsg.callee 
//                 << ", dialout = " << stCallCaptureMsg.dialog_out << ", call traceid = " << stCallCaptureMsg.call_trace_id;

//     Snprintf(act_msg.key, sizeof(act_msg.key), "--");
//     Snprintf(act_msg.mac, sizeof(act_msg.mac), device_setting.mac);
//     act_msg.act_type = CMsgControl::ActOpenDoorType::CALL_CAPTURE;
//     Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), "Call");
//     Snprintf(act_msg.pic_name, sizeof(act_msg.pic_name), stCallCaptureMsg.picture_name);
//     Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");//可能含有特殊字符

//     // call_trace_id 的前缀必须为 当前设备的sip
//     if (strlen(act_msg.call_trace_id) > 0 && !strstr(act_msg.call_trace_id, device_setting.sip_account))
//     {
//         Snprintf(act_msg.call_trace_id, sizeof(act_msg.call_trace_id), "");
//         AK_LOG_WARN << "device report call logs, call trace id error, mac = " << device_setting.mac << ", call_trace_id = " << act_msg.call_trace_id << ", sip = " << device_setting.sip_account;
//     }
    
//     std::string node;
//     std::string stName;
//     if (stCallCaptureMsg.dialog_out && strlen(stCallCaptureMsg.callee) != 0) //设备主叫
//     {
//         //被叫可能是群组号
//         node = dbinterface::Sip::GetNodeByGroupFromSip2(stCallCaptureMsg.callee);//先检查群组
//         if (node.empty())
//         {
//             int manager_id = 0;
//             CAkUserManager::GetInstance()->GetNickNameAndNodeAndMngIDByUid(stCallCaptureMsg.callee, stName, node, manager_id);
//             if (node.empty())
//             {
//                 CAkDevManager::GetInstance()->GetLocationAndNodeAndMngIDBySip(stCallCaptureMsg.callee, stName, node, manager_id);
//                 if (node.empty())
//                 {
//                     //找手机号码
//                     std::string nick_name;
//                     PersonalPhoneInfo phone_info;
//                     if (!device_setting.is_personal)
//                     {
//                         dbinterface::ResidentPersonalAccount::GetPhoneInfoByMngID(stCallCaptureMsg.callee, device_setting.manager_account_id, phone_info);
//                         nick_name = phone_info.name;
//                     }
//                     else
//                     {
//                         dbinterface::ResidentPersonalAccount::GetPhoneInfoByNode(stCallCaptureMsg.callee, device_setting.device_node, phone_info);
//                         nick_name = phone_info.name;
//                     }

//                     if (!nick_name.empty())
//                     {
//                         //name 改为name(手机号码)
//                         //nick_name +="(";
//                         //nick_name +=stCallCaptureMsg.callee;
//                         //nick_name +=")";
//                     }
//                     else
//                     {
//                         AK_LOG_WARN << "dailout:there is inlegal call, callee sip [" << stCallCaptureMsg.callee << "] caller " << stCallCaptureMsg.caller;
//                     }
//                 }
//             }
//         }
//         CNodeInfo cNodeCfg(node);
//         Snprintf(act_msg.room_num, sizeof(act_msg.room_num), cNodeCfg.getRoomNumber().c_str());
//         Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
//     }
//     else if (strlen(stCallCaptureMsg.caller) > 0)//当门口机是被叫时候，这时候能明确主叫的名称
//     {
//         int manager_id = 0;
//         CAkUserManager::GetInstance()->GetNickNameAndNodeAndMngIDByUid(stCallCaptureMsg.caller, stName, node, manager_id);
//         if (node.empty())
//         {
//             CAkDevManager::GetInstance()->GetLocationAndNodeAndMngIDBySip(stCallCaptureMsg.caller, stName, node, manager_id);
//             if (node.empty())
//             {
//                 AK_LOG_WARN << "dail in:there is inlegal call, callee sip [" << stCallCaptureMsg.callee << "] caller " << stCallCaptureMsg.caller;
//             }
//         }
//         if (!stName.empty())
//         {
//             Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), stName.c_str());//可能含有特殊字符
//         }

//         CNodeInfo cNodeCfg(node);
//         Snprintf(act_msg.room_num, sizeof(act_msg.room_num), cNodeCfg.getRoomNumber().c_str());
//         Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
//     }

//     //获取日志分片所需的project_uuid
//     std::string log_project_uuid;
//     GetLogProjectUUID(device_setting, log_project_uuid);
//     Snprintf(act_msg.project_uuid, sizeof(act_msg.project_uuid), log_project_uuid.c_str());
    
//     if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
//     {
//         AK_LOG_WARN << "Add personnal motion capture failed.";
//         return -1;
//     }

//     return 0;
// }

int CMsgControl::ParseMngDevReportMsg(SOCKET_MSG_NORMAL* pNormalMsg, const std::string& mac, SOCKET_MSG_MNG_DEV_REPORT_MSG& mng_msg)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    //int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;

    AesDecryptByMac(payload, payload, mac.c_str(), data_size);
    if (GetMsgHandleInstance()->ParseMngDevReportMsg(payload, mng_msg) < 0)
    {
        AK_LOG_WARN << "ParseMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}


//管理机广播消息
int CMsgControl::OnMngDevReportMsg(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_MNG_DEV_REPORT_MSG mng_dev_msg;
    memset(&mng_dev_msg, 0, sizeof(mng_dev_msg));
    if (ParseMngDevReportMsg(pNormalMsg, device_setting.mac, mng_dev_msg) < 0)
    {
        AK_LOG_WARN << "ParseCallCaptureMsg failed.";
        return -1;
    }
    AK_LOG_INFO << "manage devices send text msg";
    if (strlen(mng_dev_msg.nodes) == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return -1;
    }
    
    PerMsgSendList text_messages;
    if(dbinterface::Message::AddTextMsgByMngDev(mng_dev_msg.nodes, mng_dev_msg.title, mng_dev_msg.content, 
        device_setting.manager_account_id, text_messages, gstAKCSConf.is_aws) < 0)
    {
        AK_LOG_WARN << "AddTextMsgByMngDev failed.";
        return -1;
    }

    if (gstAKCSConf.is_aws)
    {
        GetMsgControlInstance()->PostAwsInsertMessageHttpReq(text_messages);
    }
    
    for (auto& text_send : text_messages)
    {
        if (text_send.client_type == CPerTextNotifyMsg::APP_SEND)
        {
            LINKER_NORMAL_MSG linker_msg;
            memset(&linker_msg, 0, sizeof(linker_msg));
            if (device_setting.init_status == 0)
            {
                ProjectInfo project(device_setting.mac, device_setting.is_personal, linker_msg);
            }
            else
            {
                GetLinkerMsgFromDevice(device_setting, linker_msg);
            }

            ResidentPerAccount per_account;
            memset(&per_account, 0, sizeof(per_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(text_send.account, per_account))
            {
                Snprintf(linker_msg.account_name, sizeof(linker_msg.account_name), per_account.name);
                Snprintf(linker_msg.account_uuid, sizeof(linker_msg.account_uuid), per_account.uuid);
                Snprintf(linker_msg.language, sizeof(linker_msg.language), per_account.language);
                if (per_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
                {
                    Snprintf(linker_msg.node_uuid, sizeof(linker_msg.account_uuid), per_account.uuid);
                }
                else if (per_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
                {
                    ResidentPerAccount main_account;
                    memset(&main_account, 0, sizeof(main_account));
                    if(0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, main_account))
                    {
                        Snprintf(linker_msg.node_uuid, sizeof(linker_msg.node_uuid), per_account.parent_uuid);
                    }
                }
            }
            Snprintf(linker_msg.dev_name, sizeof(linker_msg.dev_name), device_setting.location);
            Snprintf(linker_msg.dev_uuid, sizeof(linker_msg.dev_uuid), device_setting.uuid);
            PushLinKerText(text_send, linker_msg);
        }

        AK::Server::GroupAdaptTextMsg msg;
        msg.add_node_list(text_send.account);
        msg.set_client_type(text_send.client_type);
        msg.set_title(text_send.text_message.title);
        msg.set_content(text_send.text_message.content);
        msg.set_time(text_send.text_message.time);
        msg.set_from(text_send.text_message.from);
        msg.set_to(text_send.text_message.to);
        msg.set_id(text_send.text_message.id);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }
    return 0;
}

int CMsgControl::ParseDevReportVisitorInfo(SOCKET_MSG_NORMAL* pNormalMsg, const std::string& mac, SOCKET_MSG_DEV_REPORT_VISITOR& stVisitorInfo)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;

    AesDecryptByMac(payload, payload, mac.c_str(), data_size);
    if (GetMsgHandleInstance()->ParseDevReportVisitorInfo(payload, stVisitorInfo) < 0)
    {
        AK_LOG_WARN << "ParseDevReportVisitorInfo failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::OnDevReportVisitorInfo(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{

    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }

    SOCKET_MSG_DEV_REPORT_VISITOR dev_visitor_info;
    SOCKET_MSG_DEV_REPORT_FACE_DATA dev_face_data;
    memset(&dev_visitor_info, 0, sizeof(SOCKET_MSG_DEV_REPORT_VISITOR));
    memset(&dev_face_data, 0, sizeof(SOCKET_MSG_DEV_REPORT_FACE_DATA));
    if (ParseDevReportVisitorInfo(pNormalMsg, device_setting.mac, dev_visitor_info) < 0)
    {
        AK_LOG_WARN << "ParseDevReportVisitorInfo failed.";
        return -1;
    }

    std::vector<std::string> oDevMac;
    dbinterface::ResidentDevices::GetRootCommunityDeviceListByAccount(dev_visitor_info.account, device_setting.manager_account_id, oDevMac);   //通过拨打的用户找对应的门口机列表

    //人脸开门
    /*
    std::string mac_list;
    for (size_t count = 0; count < oDevMac.size(); count++)
    {
        mac_list += oDevMac[count];
        mac_list += "_";
    }
    std::string model_url;
    if (0 == GetPersonnalCaptureInstance()->GetModelUrlByMacAndName(device_setting.mac, dev_visitor_info.model_name, model_url))
    {
        Snprintf(dev_face_data.model_url, sizeof(dev_face_data.model_url), model_url.c_str());
        OnForwardFaceDataMsg(dev_face_data, mac_list);
    }
    else
    {
        AK_LOG_WARN << "GetModelUrlByMacAndName failed.";
    }
    */
    
    //TempKey开门
    if (0 == dev_visitor_info.count)
    {
        dev_visitor_info.count = oDevMac.size() * 2;   //如果次数为0,则设为默认次数 门口机*2
    }
    dev_visitor_info.tempkey_code = ******** + GetRandomNum(********);
    int sql_id = GetPersonnalTmpKeyInstance()->DaoAddTempKey(dev_visitor_info, oDevMac);
    if (sql_id > 0)
    {
        g_accSer_ptr->OnSendAdaptReportVisitorMsg(sql_id);
        SOCKET_MSG socketMsg;
        memset(&socketMsg, 0, sizeof(SOCKET_MSG));
        SOCKET_MSG* socket_message = &socketMsg;
        if (BuildVisitorTmpKeyAckMsg(socket_message, device_setting.mac, dev_visitor_info.tempkey_code) < 0)
        {
            AK_LOG_WARN << "BuildVisitorTmpKeyAckMsg failed.";
            return -1;
        }
        if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
        {
            AK_LOG_WARN << "Send VisitorTmpKeyAckMsg failed.";
            return -1;
        }
    }
    return 0;
}

int CMsgControl::ParseDevReportVisitorAuth(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_DEV_REPORT_VISITOR_AUTH& stVisitorAuth)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    AesDecryptByDefault(payload, payload, data_size); //设备和app都会上报访客授权信息，这边统一约定用默认加密方式

    if (GetMsgHandleInstance()->ParseDevReportVisitorAuth(payload, stVisitorAuth) < 0)
    {
        AK_LOG_WARN << "ParseDevReportVisitorInfo failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::ParseRequestOpen(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_DEV_REQUEST_OPEN& message, const std::string& mac)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;

    AesDecryptByMac(payload, payload, mac, data_size);
    if (GetMsgHandleInstance()->ParseRequestOpen(payload, message) < 0)
    {
        AK_LOG_WARN << "ParseRemoteAck failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::OnDevReportVisitorAuth(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{

    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    SOCKET_MSG_DEV_REPORT_VISITOR_AUTH visitor_auth;
    memset(&visitor_auth, 0, sizeof(SOCKET_MSG_DEV_REPORT_VISITOR_AUTH));
    if (ParseDevReportVisitorAuth(pNormalMsg, visitor_auth) < 0)
    {
        AK_LOG_WARN << "ParseDevReportVisitorAuth failed.";
        return -1;
    }

    CString strSipAccount = _T(visitor_auth.sip_account);
    DEVICE_SETTING SipDev;
    memset(&SipDev, 0, sizeof(SipDev));
    if (GetDeviceControlInstance()->GetDeviceSettingBySip(strSipAccount, &SipDev) < 0)
    {
        Snprintf(SipDev.mac, sizeof(SipDev.mac), visitor_auth.sip_account); //ip直播上报的是mac
    }

    //通过nsq,通知csroute进行精准投递
    AK::Server::P2PMainHandleVisitorAuth msg;
    msg.set_count(visitor_auth.count);
    msg.set_mac(SipDev.mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;

}

int CMsgControl::OnDevRequestOssSts(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    AK_LOG_INFO << device_setting.
                mac << " Device Request Oss Sts";

    std::string php_ret;
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "php /usr/local/oss_control_client/oss_sts/oss_sts.php \"%s\" \"%s\" \"LTAILDY6u3uotCJm\" \"46EL7LJInh6xvJ6fh5KTvEyXEvqYCb\" \"%s\"", gstAKCSConf.oss_region_id, gstAKCSConf.oss_sts_endpoint, gstAKCSConf.oss_role_arn);
    AkSystem(cmd, php_ret);

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(php_ret, root))
    {
        AK_LOG_WARN << "json parse failed, cmd:" << cmd << " php_ret:" << php_ret;
        return -1;
    }

    SOCKET_MSG_DEV_OSS_STS oss_sts;
    memset(&oss_sts, 0, sizeof(SOCKET_MSG_DEV_OSS_STS));
    Snprintf(oss_sts.secret, sizeof(oss_sts.secret), root["Credentials"]["AccessKeySecret"].asString().c_str());
    Snprintf(oss_sts.key_id, sizeof(oss_sts.key_id), root["Credentials"]["AccessKeyId"].asString().c_str());
    Snprintf(oss_sts.token, sizeof(oss_sts.token), root["Credentials"]["SecurityToken"].asString().c_str());
    Snprintf(oss_sts.oss_bucket, sizeof(oss_sts.oss_bucket), gstAKCSConf.oss_bucket);
    Snprintf(oss_sts.endpoint, sizeof(oss_sts.endpoint), gstAKCSConf.oss_outer_endpoint);

    SOCKET_MSG socketMsg;
    memset(&socketMsg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socketMsg;
    if (BuildOssStsMsg(socket_message, device_setting.mac, oss_sts) < 0)
    {
        AK_LOG_WARN << "BuildOssStsMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "Send OssStsMsg failed.";
        return -1;
    }

    return 0;
}

/*
int CMsgControl::OnDevRequestOpen(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    int result = REQUEST_OPEN_DOOR_SUCCESS; //先写死开门成功
    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetLocalDeviceSetting failed.";
        return -1;
    }
    SOCKET_MSG_DEV_REQUEST_OPEN request_open;
    memset(&request_open, 0, sizeof(request_open));
    std::string open_door_type;
    if (0 > GetParseRequestOpenData(normal_msg, deviceSetting, request_open, open_door_type)) {
        return -1;
    }
    //权限校验
    DEVICE_SETTING dev_info;
    memset(&dev_info, 0, sizeof(dev_info));
    if (GetDeviceSettingInstance()->GetDeviceSettingByMac(request_open.mac, &dev_info) < 0)
    {
        AK_LOG_WARN << "The device requested to open does not exist";
        return -1;
    }
    //基本开门校验
    if(!CheckDevCanOpenDoor(deviceSetting, dev_info))
    {
        AK_LOG_WARN << "The device does not have permission. Indoor MAC:" << deviceSetting.mac << " request open MAC:" << request_open.mac;
        return -1;
    }
    //室内机和门口机Dclient版有小于6.5的直接回成功
    if(deviceSetting.dclient_version < D_CLIENT_VERSION_6520 || !akjudge::DevSupportRemoteOpenDoorAck(dev_info.fun_bit))
    {
        SOCKET_MSG socketMsg;
        memset(&socketMsg, 0, sizeof(SOCKET_MSG));
        SOCKET_MSG* socket_message = &socketMsg;
        if (BuildOpenDoorAckMsg(socket_message, deviceSetting.mac, result) < 0)
        {
            AK_LOG_WARN << "BuildOpenDoorAckMsg failed.";
            return -1;
        }
        if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
        {
            AK_LOG_WARN << "Send OpenDoorAckMsg failed.";
            return -1;
        }
    }
    
    return SendDevRequestOpen(request_open, deviceSetting, open_door_type);
}
*/

int CMsgControl::GetParseRequestOpenData(SOCKET_MSG_NORMAL* normal_msg, DEVICE_SETTING deviceSetting, SOCKET_MSG_DEV_REQUEST_OPEN& request_open, std::string& open_door_type)
{
    //权限校验
    if (ParseRequestOpen(normal_msg, request_open, deviceSetting.mac) < 0)
    {
        AK_LOG_WARN << "ParseRemoteAck failed.";
        return -1;
    }

    if (strcmp(request_open.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_DOOR) == 0)
    {
        AK_LOG_INFO << "Open Door Type:" << request_open.type;
        open_door_type = SOCKET_MSG_TYPE_NAME_OPENDOOR;
    }
    else if (strcmp(request_open.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_SECURITY_RELAY) == 0)
    {
        AK_LOG_INFO << "Open Door Type:" << request_open.type;
        open_door_type = SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY;
    } 
    else
    {
        AK_LOG_WARN << "Invalid Open Door Type:" << request_open.type;
        return -1;
    }
    return 0;
}

/*
int CMsgControl::SendDevRequestOpen(SOCKET_MSG_DEV_REQUEST_OPEN request_open, DEVICE_SETTING deviceSetting,const std::string& open_door_type)
{
    std::string mac = deviceSetting.mac;
    std::string trace_id = request_open.trace_id;
    trace_id = "dev_" + mac + "_" + trace_id;
    AK::Server::P2PMainRequestOpenDoor msg;
    msg.set_uid(deviceSetting.sip_account);
    msg.set_mac(request_open.mac);
    msg.set_relay(request_open.relay);
    msg.set_open_door_type(open_door_type);
    msg.set_msg_traceid(trace_id);

    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OPEN_DOOR_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}
*/

int CMsgControl::BuildNotifyDevUpdateServer(SOCKET_MSG* socket_message, const std::string& mac, const std::string& type)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildReqUpdateServerMsg(payload, sizeof(msg_normal->data), type) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_MAINTENANCE_SERVER_CHANGE, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;
    return 0;
}

int CMsgControl::SendHearbeatToDev(const evpp::TCPConnPtr& conn)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (BuildHttpMaintenanceNoParamCmd(&stSocketMsg, NULL, MSG_TO_DEVICE_SERVER_HEARTBEAT) != 0)
    {
        AK_LOG_WARN << "BuildHttpMaintenanceNoParamCmd failed";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send OnHeartBeatMsg ACK to dev failed.";
        return -1;
    }
    return 0;
}

int CMsgControl::OnServerHearbeatAck(const evpp::TCPConnPtr& conn)
{
    g_accSer_ptr->AlexaDevHearbeatAck(conn);
    return 1;
}

void CMsgControl::SendRequestSensorTrigger(const evpp::TCPConnPtr& conn, const SOCKET_MSG_SENSOR_TRIGGER& sensor_trigger)
{
    //组装消息
    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));
    if (GetMsgControlInstance()->OnBuildRequestSensorTriggerMsg(socket_msg, sensor_trigger) != 0)
    {
        AK_LOG_WARN << "BuildRequestSensorTriggerMsg failed";
        return;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "SendRequestSensorTrigger to dev failed";
        return;
    }
}

int CMsgControl::OnRespondSensorTrigger(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }

    SOCKET_MSG_SENSOR_TIRGGER_MSG msg;
    memset(&msg, 0, sizeof(msg));
    if (ParseRespondSensoTirggerMsg(pNormalMsg, device_setting.mac, msg) < 0)
    {
        AK_LOG_WARN << "OnRespondSensorTrigger failed.";
        return -1;
    }
    if (device_setting.is_personal)
    {
        dbinterface::ResidentPerDevices::SetDeviceSensorTirggerInfo(device_setting.mac, msg.home, msg.away, msg.sleep);
    }
    else
    {
        dbinterface::ResidentDevices::SetDeviceSensorTirggerInfo(device_setting.mac, msg.home, msg.away, msg.sleep);
    }
    return 0;
}


int CMsgControl::ParseRespondSensoTirggerMsg(SOCKET_MSG_NORMAL* pNormalMsg, const std::string& mac, SOCKET_MSG_SENSOR_TIRGGER_MSG& msg)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;

    AesDecryptByMac(payload, payload, mac.c_str(), data_size);
    if (GetMsgHandleInstance()->ParseSensorTirggerMsg(payload, msg) < 0)
    {
        AK_LOG_WARN << "ParseMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}
//罗伯特门口机刷卡布撤防，只需要同步给联动系统
int CMsgControl::OnHandleDevArmingFromDev(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        AK_LOG_WARN << "OnHandleDevArming param null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }

    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    if (ParseReqArmingMsgFromDev(pNormalMsg, stArmingMsg, device_setting.mac) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.";
        return -1;
    }

    //目前只有罗伯特版本有支持，别的版本直接忽略
    int oem  = device_setting.oem_id;
    if (!(oem == OEMID_ROBERT || oem == OEMID_ROBERT2 || stArmingMsg.home_sync == ARMING_HOME_SYNC_TYPE_ON || 
        stArmingMsg.home_sync == ARMING_HOME_SYNC_TYPE_NOTIFY_OFF_CONFIG || stArmingMsg.home_sync == ARMING_HOME_SYNC_TYPE_NOTIFY_ON_CONFIG))
    {
        AK_LOG_INFO << device_setting.mac << " dev handle arming. mode:" << stArmingMsg.mode << " but is oem!=207,ignore!";
        return -1;
    }

    //设备 Action有Get/Set，Get处理在上报状态时候会下发，这里直接忽略
    //Get
    if (strcasecmp(stArmingMsg.szAction, "Get") == 0)
    {
        GetMsgControlInstance()->SendSyncArmingMsg(conn, device_setting);
        return 0;
    }
    //Set
    AK_LOG_INFO << device_setting.mac  << " dev handle arming. mode:" << stArmingMsg.mode << " multicast message to device under node: " << device_setting.device_node;


    stArmingMsg.resp_action = REPORT_ARMING_ACTION_TYPE_DEV_SET;
    Snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), device_setting.mac);

    //更新设备arming状态
    //GetDeviceControlInstance()->UpdateDeviceArming(device_setting.mac, stArmingMsg.mode, device_setting.is_personal);

    //通过nsq,通知csroute进行消息广播
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(stArmingMsg.mac);//设置设备的mac
    msg.set_mode(stArmingMsg.mode);
    msg.set_uid(stArmingMsg.uid);
    msg.set_oem(oem);
    msg.set_resp_action(stArmingMsg.resp_action);
    msg.set_node(device_setting.device_node);
    msg.set_home_sync(stArmingMsg.home_sync);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}


/*
<Msg>??
????<Type>GSFaceLogin</Type>??
????<Params>??
????????<HttpApi>http://192.168.12.214/gsface/v1/login?ID=xxx</HttpApi>??
????</Params>??
</Msg>
*/
int CMsgControl::SendGsFaceLoginMsg(const evpp::TCPConnPtr& conn, const DEVICE_SETTING& deviceSetting)
{
    ThirdPartyInfo third_party_info;
    memset(&third_party_info, 0x0, sizeof(third_party_info));

    int ret = dbinterface::ThirdParty::GetThirdPartyInfo(deviceSetting.mac, third_party_info);
    if (ret != 0)
    {
        AK_LOG_WARN << "DaoGetThirdPartyInfo failed, Mac=" << deviceSetting.mac;
        return ret;
    }

    if (0 == strlen(third_party_info.gs_face_login_api))
    {
        AK_LOG_INFO << "This device don't need to send gs face login msg,FirmWare is " << deviceSetting.SWVer;
        return 0;
    }

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "GSFaceLogin";
    tag_map[csmain::xmltag::MAC] = deviceSetting.mac;
    std::stringstream gs_face_login_api;
    gs_face_login_api << third_party_info.gs_face_login_api << "/" << deviceSetting.id;
    tag_map[csmain::xmltag::HTTP_API] = gs_face_login_api.str();

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_GSFACE_HTTPAPI_LOGIN, tag_map) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return -1;
    }

    return 0;
}

//只能在上报状态时候处理
int CMsgControl::SendSyncArmingMsg(const evpp::TCPConnPtr& conn, const DEVICE_SETTING& deviceSetting)
{
    int oem  = deviceSetting.oem_id;
    if (oem == OEMID_ROBERT || oem == OEMID_ROBERT2)
    {
        std::vector<DEVICE_SETTING> devs;
        std::string self_mac = deviceSetting.mac;
        //直接查询数据库获取
        if (GetDeviceControlInstance()->GetDevicesByNode(deviceSetting.device_node, deviceSetting.is_personal, devs) == 0)
        {
            int is_send = 0;
            for (auto& dev : devs)
            {
                //同步在线的
                std::string mac = dev.mac;
                if (dev.type == DEVICE_TYPE_INDOOR && mac != self_mac && dev.status == 1)
                {
                    SOCKET_MSG_DEV_ARMING stArmingMsg;
                    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
                    stArmingMsg.mode = dev.indoor_arming;
                    stArmingMsg.resp_action = REPORT_ARMING_ACTION_TYPE_SELF_SET; //同步别人的,相当于自己设置

                    //组装消息
                    SOCKET_MSG stSocketMsg;
                    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                    if (GetMsgControlInstance()->OnBuildRespArmingToDev(&stSocketMsg, stArmingMsg, deviceSetting.mac) == 0)
                    {
                        if (GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size) < 0)
                        {
                            AK_LOG_WARN << "Send  ReportArming to dev failed. mac=" << self_mac;
                            continue;
                        }
                        //更新设备arming状态
                        GetDeviceControlInstance()->UpdateDeviceArming(deviceSetting.mac, stArmingMsg.mode, deviceSetting.is_personal);
                        is_send = 1;
                        break;
                    }
                }
            }
            if (is_send == 0) //如果都没有找到，就用平台设置,相当于重置也能同步到状态
            {
                SOCKET_MSG_DEV_ARMING stArmingMsg;
                memset(&stArmingMsg, 0, sizeof(stArmingMsg));
                stArmingMsg.mode = deviceSetting.indoor_arming;
                stArmingMsg.resp_action = REPORT_ARMING_ACTION_TYPE_SELF_SET; //同步别人的,相当于自己设置

                //组装消息
                SOCKET_MSG stSocketMsg;
                memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                if (GetMsgControlInstance()->OnBuildRespArmingToDev(&stSocketMsg, stArmingMsg, deviceSetting.mac) == 0)
                {
                    if (GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size) < 0)
                    {
                        AK_LOG_WARN << "Send  ReportArming to dev failed." << self_mac;
                    }
                }
            }
        }
    }

    return 0;
}

int CMsgControl::OnBuildNewCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, XmlKV& tag_map, XmlKeyAttrKv& attr_map, int need_mac)
{
    if (socket_msg == NULL)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, socket_message is null";
        return -1;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg->data;
    char* pay_load = (char*)msg_normal->data;

    if (0 == tag_map.count(csmain::xmltag::MAC))
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Mac key";
        return -1;
    }
    string mac = tag_map[csmain::xmltag::MAC];
    if (need_mac == MsgParamControl::NO_NEED_MAC)
    {
        tag_map.erase(csmain::xmltag::MAC);
    }

    string xml_msg = GetMsgHandleInstance()->BuildNewCommonMsg(tag_map, attr_map);
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << "Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    uint32_t data_size = strlen(pay_load);

    AesEncryptByMacV2(pay_load, pay_load, mac, &data_size, sizeof(msg_normal->data));

    if (BuildNormalMsgHeader(socket_msg,  msg_id, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "BuildNormalMsgHeader failed";
        return -1;
    }

    socket_msg->size = data_size + SOCKET_MSG_NORMAL_HEADER_SIZE;
    return 0;
}

int CMsgControl::OnBuildCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac)
{
    if (socket_msg == NULL)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, socket_message is null";
        return -1;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg->data;
    char* pay_load = (char*)msg_normal->data;

    if (0 == tag_map.count(csmain::xmltag::MAC))
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Mac key";
        return -1;
    }
    string mac = tag_map[csmain::xmltag::MAC];
    if (need_mac == MsgParamControl::NO_NEED_MAC)
    {
        tag_map.erase(csmain::xmltag::MAC);
    }

    string xml_msg = GetMsgHandleInstance()->BuildCommonMsg(tag_map);
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << "Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    uint32_t data_size = strlen(pay_load);

    AesEncryptByMacV2(pay_load, pay_load, mac, &data_size, sizeof(msg_normal->data));

    if (BuildNormalMsgHeader(socket_msg,  msg_id, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "BuildNormalMsgHeader failed";
        return -1;
    }

    socket_msg->size = data_size + SOCKET_MSG_NORMAL_HEADER_SIZE;
    return 0;
}

int CMsgControl::OnBuildCommonEncDefaultMsg(SOCKET_MSG& socket_msg,SOCKET_MSG& dy_iv_socket_message, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac)
{
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
    char* pay_load = (char*)msg_normal->data;

    string xml_msg = GetMsgHandleInstance()->BuildCommonMsg(tag_map);
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << "Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    EncryptDefalutMsg(socket_msg, dy_iv_socket_message, msg_id);
    return 0;
}

int CMsgControl::OnBuildNewCommonEncDefaultMsg(SOCKET_MSG& socket_msg,SOCKET_MSG& dy_iv_socket_message, uint16_t msg_id, XmlBuilder& xml)
{
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
    char* pay_load = (char*)msg_normal->data;

    string xml_msg = xml.generateXML();
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << "Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    EncryptDefalutMsg(socket_msg, dy_iv_socket_message, msg_id);
    return 0;
}


//远程设备访问
int CMsgControl::OnBuildRemoteDeviceContorl(SOCKET_MSG* socket_message, const SOCKET_MSG_REMOTE_DEV_CONTORL& remote)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    char* payload = (char*)msg_normal->data;
    GetMsgHandleInstance()->BuildRemoteDeviceWebContorlMsg(payload, sizeof(msg_normal->data), remote);
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, remote.mac, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_REMOTE_DEV_WEB_CONTORL, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::SetDeviceSettingByReportStatusMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REPORT_STATUS& report_status_msg, const std::string& ip, const int& port, DEVICE_SETTING& device_setting)
{
    device_setting.dclient_version = report_status_msg.dclient_version;
    device_setting.indoor_arming = report_status_msg.indoor_arming;
    device_setting.relay_status = report_status_msg.relay_status;

    Snprintf(device_setting.outer_ip, sizeof(device_setting.outer_ip), ip.c_str());
    device_setting.port = port;

    if (!IsReservedIp(report_status_msg.ip_addr) && strcmp(report_status_msg.ip_addr, device_setting.ip_addr))
    {
        device_setting.is_ip_addr_change = 1;
    }

    //有线ip地址变化或掩码变化也需要刷配置
    if (strcmp(report_status_msg.wired_ip_addr, device_setting.wired_ip_addr) 
        || strcmp(report_status_msg.wired_subnet_mask, device_setting.wired_subnet_mask))
    {
        device_setting.is_ip_addr_change = 1;
    }

    if (strcmp(report_status_msg.SWVer, device_setting.SWVer))
    {
        device_setting.is_dev_upgrade = 1;
    }

    //存储设备OEM
    Snprintf(device_setting.SWVer, sizeof(device_setting.SWVer), report_status_msg.SWVer);
    std::string sw_ver = device_setting.SWVer;
    
    auto pos = sw_ver.find(".");
    if (pos != std::string::npos)
    {
        device_setting.oem_id = ATOI(sw_ver.substr(pos + 1).c_str());
        device_setting.firmware = ATOI(sw_ver.substr(0,pos).c_str());
    }
    else
    {
        AK_LOG_WARN << "MAC " << device_setting.mac << " sw ver is error.sw_ver: " << device_setting.SWVer;
        device_setting.oem_id = 0;
        device_setting.firmware = 0;
    }
    

    device_setting.status = DEVICE_STATUS_IDLE;
    Snprintf(device_setting.ip_addr, sizeof(device_setting.ip_addr), report_status_msg.ip_addr);   
    Snprintf(device_setting.wired_ip_addr, sizeof(device_setting.wired_ip_addr), report_status_msg.wired_ip_addr);
    Snprintf(device_setting.wired_subnet_mask, sizeof(device_setting.wired_subnet_mask), report_status_msg.wired_subnet_mask);
    Snprintf(device_setting.gateway, sizeof(device_setting.gateway), report_status_msg.gateway);
    Snprintf(device_setting.primary_dns, sizeof(device_setting.primary_dns), report_status_msg.primary_dns);
    Snprintf(device_setting.secondary_dns, sizeof(device_setting.secondary_dns), report_status_msg.secondary_dns);
    Snprintf(device_setting.subnet_mask, sizeof(device_setting.subnet_mask), report_status_msg.subnet_mask);
    Snprintf(device_setting.SWVer, sizeof(device_setting.SWVer), report_status_msg.SWVer);
    Snprintf(device_setting.HWVer, sizeof(device_setting.HWVer), report_status_msg.HWVer);
    Snprintf(device_setting.last_connection, sizeof(device_setting.last_connection), GetCurTime().GetBuffer());

    device_setting.fun_bit = report_status_msg.func_bit;


    if (device_setting.device_type == csmain::PERSONNAL_DEV)
    {
        device_setting.is_personal = 1;
        if (dbinterface::ResidentPerDevices::UpdateDeviceInfo(&device_setting) < 0)
        {
            char error_msg[1024] = "";
            ::snprintf(error_msg, sizeof(error_msg), "[Personal]When update Mac=%s in DB status,return error!", device_setting.mac);
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEV_CONNECT_STATUS);
            return -1;
        }
        else
        {
            evpp::Any any_tmp_conn_version(device_setting.conn_version);
            conn->set_context(EVPP_CONN_ANY_CONTEXT_DEV_CONN_VERSION, any_tmp_conn_version);
        }
    }
    else
    {
        device_setting.is_personal = 0;
        if (dbinterface::ResidentDevices::UpdateDeviceInfo(&device_setting) < 0)
        {
            char error_msg[1024] = "";
            ::snprintf(error_msg, sizeof(error_msg), "[Community]When update Mac=%s in DB status,return error!", device_setting.mac);
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEV_CONNECT_STATUS);
            return -1;
        }
        else
        {
            evpp::Any any_tmp_conn_version(device_setting.conn_version);
            conn->set_context(EVPP_CONN_ANY_CONTEXT_DEV_CONN_VERSION, any_tmp_conn_version);
        }        
    }

    return 0;
}

static int CheckThirdPartyCenterControl(const DEVICE_SETTING& dev_setting, int report_is_third)
{
    int is_third_party = (dev_setting.brand == 0 ? 0 : 1); //brand 0-akuvox 1-third_party
    int is_active = 1;
    int third_party_ret = 0;
    if(is_third_party)  //apt已激活，第三方设备才能上线
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if(0 == dbinterface::ResidentPersonalAccount::GetUidAccount(dev_setting.device_node, account))
        {
            is_active = account.active;
        }
        else
        {
            is_active = 0;
        }
    }
    //第三方设备不能加到ak设备的类型下
    if(is_third_party != report_is_third || !is_active)
    {
        third_party_ret = -1;
        AK_LOG_WARN << "Device MAC " << dev_setting.mac << " add error, is_third_party:" << is_third_party << " is_active:" << is_active;
    }   

    return third_party_ret;
}

void CMsgControl::AfterReportStatusBindConnSendMsg(const evpp::TCPConnPtr& conn, const std::string& mac, const DEVICE_SETTING& dev_setting)
{
    //added by chenyc,2020-01-06,如果csmain能够在设备表上查询到该设备，证明设备已经被绑定到某个enduser上面了，那么清空设备的decivecode,不允许该设备上再去注册账号了。
    GetMsgControlInstance()->OnSendDevCodeMsg(conn, "", mac);
    
    //发送平台记录的arming状态给设备
    GetMsgControlInstance()->SendSyncArmingMsg(conn, dev_setting);
    if(dev_setting.project_type != project::OFFICE )
    {
        //通知设备过来请求同一联动单元的设备列表
        if (dev_setting.dclient_version < D_CLIENT_VERSION_1_0)
        {
            GetMsgControlInstance()->OnSendDevListChangeMsg(conn);
        }
        if (dev_setting.dclient_version < D_CLIENT_VERSION_4400 && dev_setting.type == DEVICE_TYPE_INDOOR)
        {
            g_accSer_ptr->getDevArmStatuReq(dev_setting.mac);
        }
    }     
}

int CMsgControl::OnDeviceReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, int msg_len, const evpp::TCPConnPtr& conn)
{
    SOCKET_MSG_NORMAL msg;
    memcpy(&msg, pNormalMsg, msg_len);
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);

    std::string ip = evpp::any_cast<std::string>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_IP));
    int port = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_PORT));

    SOCKET_MSG_REPORT_STATUS reportStatusMsg;
    memset(&reportStatusMsg, 0, sizeof(SOCKET_MSG_REPORT_STATUS));
    if (GetMsgControlInstance()->ParseReportStatusMsg(pNormalMsg, &reportStatusMsg, data_size, msg_version) < 0)
    {
        AK_LOG_WARN << "ParseReportStatusMsg failed.";
        return -1;
    }
    evpp::Any any_tmp_iv(reportStatusMsg.dynamics_iv);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_DY_AES_IV_INDEX, any_tmp_iv);
    AK_LOG_INFO << "OnDeviceReportStatusMsg. mac:" << reportStatusMsg.mac << " is aes dynamics iv:" << reportStatusMsg.dynamics_iv;
    
    //TODO
    //从设备数据库中搜索与该MAC匹配的设备，如没有则不处理，如有则判断该设备的ID/IP/SUBNETMASK/GATEWAY/PRIMARYDNS/SECONDARYDNS是否一致
    //如果不一致则发送消息去更新设备
    //如果一致则保存状态到数据库, 并记录最后一次连接时间，然后判断设备上报的KeyMD5/RfidMD5/AddrMD5，如果不一致则发送命令给设备要求其更新

    char device_id[DEVICE_ID_SIZE];
    memset(device_id, 0, sizeof(device_id));
    CString mac;
    mac.Format("%s", reportStatusMsg.mac);
    mac.Remove(":");
    //Added by chenyc,提升MAC的字符为大写字母
    mac.MakeUpper();

    ::memset(reportStatusMsg.mac, 0, sizeof(reportStatusMsg.mac));
    Snprintf(reportStatusMsg.mac, sizeof(reportStatusMsg.mac), mac.GetBuffer());
    
    //读取数据库中与该MAC匹配的设备信息, 找不到则不做任务处理
    DEVICE_SETTING dev_setting;
    memset(&dev_setting, 0, sizeof(dev_setting));
    //add by chenzhx 20200629要把查询失败和本来没有登记在设备表的区分开
    int require_mac_ret = 0;
    require_mac_ret = GetDeviceControlInstance()->GetDeviceSettingByMac(mac, &dev_setting);

    bool conn_change = false;
    bool ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX)); 
    bool dyiv = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DY_AES_IV_INDEX)); 
    if (ipv6 != dev_setting.is_ipv6 || dyiv != dev_setting.dynamics_iv)
    {
        conn_change = true;
    }
    dev_setting.dynamics_iv = reportStatusMsg.dynamics_iv;
    dev_setting.is_ipv6 = ipv6;
    
    //第一次上线也需要通知后端，因为后端可能缓存了旧的信息
    if (dev_setting.dclient_version == 0  &&  strlen(dev_setting.SWVer) == 0)
    {
        conn_change = true;
    }

    int third_party_ret = 0;
    third_party_ret = CheckThirdPartyCenterControl(dev_setting, reportStatusMsg.is_third_party);
            
    if (require_mac_ret < 0 || third_party_ret < 0)
    {
        g_sm_client_ptr->RegDev(mac.GetBuffer(), g_logic_srv_id);
        g_accSer_ptr->SetTcpConnMac(conn, mac);

        // hager kit设备未添加到云上,设置mac传递到后端业务处理
        evpp::Any client(std::string(mac.GetBuffer()));
        conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX, client);
        
        //判断是否登记在平台
        if (!dbinterface::ResidentDevices::DevRegistInCloud(mac))
        {
            AK_LOG_WARN << "Device MAC can not regist on cloud. mac:" << mac.GetBuffer();
            GetErrorConnectInstance()->InsertUnRegistError(conn->AddrToString(), mac);
        }
        //modify by chenyc,mac地址不对的时候让设备挂着tcp连接,但不进行消息交互
        //added by chenyc,2018-01-30,v3.2 此时让设备去下载空的配置文件,以及清空联系人
        //add by chenzhx,20191231不能去清除配置，如果连接上的瞬间数据库不可用，那么就会有问题
        //GetKeyControlInstance()->PerDelDevKeySend(conn, mac);//发送空的配置文件给设备,让设备清除所有状态
        if (dev_setting.dclient_version < D_CLIENT_VERSION_1_0)
        {
            GetMsgControlInstance()->OnSendDevListChangeMsg(conn); //通知设备过来请求同一联动单元的设备列表,刷新联系人列表
        }
        //add by chenzhx,20191231 不移除上报状态的，让它在超时时候重新上报状态。因为连接的瞬间有可能数据库连接出错，导致和设备连接僵死
        //add by chenzhx 20200616 如果查数据库失败，或者本身不登记在平台上，让他5分钟后断开重连
        if (require_mac_ret == -1 && GetDBConnPollInstance()->CheckDBConnNormal())//若数据库异常情况导致的查询设备失败，不能移除，要让它在超时后重新上报
        {
            g_accSer_ptr->RemoveReportStatuConn(conn);//删除上报状态超时检测
        }
        return -1;
    }

        
    std::string authcode;
    //add by chenzhx 20211207 set dev type
    evpp::Any any_tmp(dev_setting.device_type);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX, any_tmp);
    
    //设备是否拦截空authcode判断
    if(strlen(reportStatusMsg.auth_code) == 0 && GetDeviceSettingInstance()->CanDevSupportInterceptEmptyAuthCode(reportStatusMsg.func_bit))
    {
        AK_LOG_WARN << "device report empty authcode, no need to handle. device mac=" << mac;
        GetErrorConnectInstance()->InsertEmptyAuthCodeError(conn->AddrToString(), mac);
        return -1;
    }

    if (reportStatusMsg.dclient_version < D_CLIENT_VERSION_6200 || !dbinterface::MacPool::CheckAuthcodeExist(mac, authcode))
    {
        //验证 校验码是否正确  不正确断开连接
        if (strlen(dev_setting.auth_code) == 0 && strlen(reportStatusMsg.auth_code) > 0)
        {
            //登记码
            ::snprintf(dev_setting.auth_code, sizeof(dev_setting.auth_code), "%s", reportStatusMsg.auth_code);
            dbinterface::ResidentDevices::SetDevAuthCode(&dev_setting);
        }
        else if (strcmp(dev_setting.auth_code, reportStatusMsg.auth_code))
        {
            //AuthCode码错误
            AK_LOG_WARN << "Device MAC check code error. database mac:" << mac.GetBuffer() << " checkcode:"
                         << dev_setting.auth_code << " report checkcode:" << reportStatusMsg.auth_code;
            GetErrorConnectInstance()->InsertCheckCodeError(conn->AddrToString(), mac, reportStatusMsg.auth_code, dev_setting.auth_code);
            //conn->Close(); 不关闭连接 不然狂刷log
            return -1;
        }
    }
    else if (strcmp(authcode.c_str(), reportStatusMsg.auth_code))
    {
        //AuthCode码错误
        AK_LOG_WARN << "Device MAC check code error. mac pool database mac:" << mac.GetBuffer() << " checkcode:"
                     << authcode << " report checkcode:" << reportStatusMsg.auth_code;
        GetErrorConnectInstance()->InsertCheckCodeError(conn->AddrToString(), mac, reportStatusMsg.auth_code, authcode);
        //conn->Close(); 不关闭连接 不然狂刷log
        return -1;
    }                         

    //added by chenyc,2022.11.01,确定固件版本号 是否有出现降级的情况（参考Dclient版本号）,如果出现Dclient版本号降级了,高概率是因为多台设备的MAC地址重复了
     if(dev_setting.dclient_version > reportStatusMsg.dclient_version && strncmp(dev_setting.outer_ip,ip.c_str(), ip.length()) != 0)
     {
          AK_LOG_WARN << "Dclient ver is degraded, mac:" << mac.GetBuffer() << ", report status dclient ver:"
                      << reportStatusMsg.dclient_version << ", db dclient ver:" << dev_setting.dclient_version;
          int abnormal_num = dbinterface::AbnormalMacStatus::InsertAbnormalMacStatus(mac.GetBuffer(), ip, reportStatusMsg.dclient_version, reportStatusMsg.SWVer);
          if(abnormal_num >= 3)
          {
             AK_LOG_WARN << "AbnormalStatus more than allowed times, ignore status report,mac:" << mac.GetBuffer() << ", report status dclient ver:"
                      << reportStatusMsg.dclient_version << ", db dclient ver:" << dev_setting.dclient_version;
             char msg[128] = {0};
             ::snprintf(msg, 128, "There maybe happen mac duplicate, mac is %s", mac.GetBuffer());
             AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", msg, AKCS_MONITOR_ALARM_MAC_DUPLICATE_CSMAIN);
             return -1;
          }
     }

    if (!IsValidIPAddress(reportStatusMsg.ip_addr))
    {
        //若上报非法ip，则ip不变
        Snprintf(reportStatusMsg.ip_addr, sizeof(reportStatusMsg.ip_addr), dev_setting.ip_addr);
        AK_LOG_WARN << "report illegal ip address, mac:" << dev_setting.mac << " ip:" << reportStatusMsg.ip_addr;
    }

    //authcode校验通过后再登记到session
    g_sm_client_ptr->RegDev(mac.GetBuffer(), g_logic_srv_id, dev_setting.uuid);
    if (SetDeviceSettingByReportStatusMsg(conn, reportStatusMsg, ip, port, dev_setting) != 0)
    {
        return -1;
    }
    
    //更新连接列表中的相关设备信息,刷新设备设置信息到本地客户端链表节点上
    //todo,chenyc,2022.11.03,如果mac重复了,怎样保持云端只有链接其中一台设备的conn,保证两台设备会互相顶，快速触发上一步版本检测的动作
    g_accSer_ptr->SetTcpConnMac(conn, mac, dev_setting.uuid);
    g_accSer_ptr->RemoveReportStatuConn(conn);//删除上报状态超时检测

    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_dev_outerip); //获取与redis实例的tcp连接
    if (cache_conn)
    {
        std::string ret = cache_conn->set(ip, mac);
        cache_conn->expire(ip, WEEK_SECOND);
        if (ret.size() == 0)
        {
            AK_LOG_WARN << "set dev_outerip failed, mac is " << mac << ", ip is " <<  ip;
        }
        cache_manager->RelCacheConn(cache_conn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csmain dev_outerip";
    }

    //刷新sip-mac的关系，方便后续pbx唤醒
    CAkDevManager::GetInstance()->UpdateSipMacList(dev_setting.sip_account, dev_setting.mac);


    AK_LOG_INFO << "[" << mac.GetBuffer() << "] report status";

    //add by chenzhx ******** 从这里开启区分业务类型，上面也是住宅和办公都需要的
    if (dev_setting.project_type == project::OFFICE)
    {
        OnOfficeDeviceReportStatusMsg(conn, reportStatusMsg, dev_setting);
        //解析处理完在更新缓存
        g_accSer_ptr->UpdateTcpConnSetting(conn, &dev_setting);
        g_accSer_ptr->SetTcpConnList(conn, dev_setting);
        AfterReportStatusBindConnSendMsg(conn, mac, dev_setting);
        CAkDevManager::GetInstance()->UpdateMacTypeList(dev_setting.mac, PUB_DEV);
        //2023 chenyc，csmain拆分,   从这里开始转发给csmain去做逻辑业务,后面所有的业务都不能放在这个代码下面进行处理
        evpp::Any status(true);
        evpp::Any client(std::string(dev_setting.mac));
        conn->set_context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX, status);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX, client);        
        Main2ResidMsgHandle::Instance()->OfficeSend(dev_setting.mac, csmain::DeviceType::OFFICE_DEV, conn_change, 
         (char*)&msg, data_size + SOCKET_MSG_NORMAL_HEADER_SIZE);        
        return 1;
    }
    
    //单住户
    if (dev_setting.device_type == csmain::PERSONNAL_DEV)
    {
        evpp::Any status(true);
        evpp::Any client(std::string(dev_setting.mac));
        conn->set_context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX, status);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX, client);

        //2023-10-12 czw, csmain拆分，投递到后端服务处理消息。
        //一定要投递到后端服务，所以这个要放在逻辑最上方
        Main2ResidMsgHandle::Instance()->Send(dev_setting.mac, csmain::DeviceType::PERSONNAL_DEV, conn_change, (char*)&msg, msg_len);
        
        //add by chenyc,v4.2,2018-10-09,缓存设备mac跟类型的关系
        CAkDevManager::GetInstance()->UpdateMacTypeList(dev_setting.mac, PER_DEV);
        if (g_accSer_ptr->processPersonnalDevStatusMsg(conn, reportStatusMsg, dev_setting) != 0)
        {
            AK_LOG_WARN << "processPersonnalDevStatusMsg failed";
            return -1;
        }
        //解析处理完在更新缓存
        g_accSer_ptr->UpdateTcpConnSetting(conn, &dev_setting);
        g_accSer_ptr->SetTcpConnList(conn, dev_setting);
        AfterReportStatusBindConnSendMsg(conn, mac, dev_setting);       
        return 0;
    }

    g_accSer_ptr->UpdateTcpConnSetting(conn, &dev_setting);
    g_accSer_ptr->SetTcpConnList(conn, dev_setting);
    AfterReportStatusBindConnSendMsg(conn, mac, dev_setting);
    if (gstAKCSConf.server_area == ServerArea::ccloud)
    {
        //发送沈阳嘉里的通知设备下载人脸文件信息
        if (dbinterface::ThirdParty::IsFaceServerDevice(dev_setting.SWVer))
        {
            GetMsgControlInstance()->SendGsFaceLoginMsg(conn, dev_setting);
        }
    }

    //add by chenyc,v4.2,2018-10-09,缓存设备mac跟类型的关系
    CAkDevManager::GetInstance()->UpdateMacTypeList(dev_setting.mac, PUB_DEV);

    //对比MD5，先处理PRIVATEKEY和RFID的MD5，TODO:地址MD5
    CString privatekey_md5 = reportStatusMsg.private_key_md5;
    CString rfid_md5 = reportStatusMsg.rf_id_md5;
    CString config_md5 = reportStatusMsg.config_md5;
    CString strAddressMD5 = reportStatusMsg.address_md5;
    CString contact_md5 = reportStatusMsg.contact_md5;
    std::string tz_md5 = reportStatusMsg.tz_md5;
    std::string tz_data_md5 = reportStatusMsg.tz_data_md5;
    std::string face_md5 = reportStatusMsg.face_md5;
    std::string user_meta_md5 = reportStatusMsg.user_meta_md5;
    std::string schedule_md5 = reportStatusMsg.schedule_md5;
    std::string sw_ver = reportStatusMsg.SWVer;
    
    //不支持tzdata更新的设备不进行tzdata判断
    if (!GetKeyControlInstance()->IsDevSupportTzData(sw_ver, reportStatusMsg.func_bit))
    {
        tz_data_md5 = "";      
    }
    
    //版本升级和ip变化 从新写配置后一定会通知这台设备从新下载配置
    if (dev_setting.is_ip_addr_change == 0 && dev_setting.is_dev_upgrade == 0 && (privatekey_md5 != dev_setting.private_key_md5
            || rfid_md5 != dev_setting.rf_id_md5
            || config_md5 != dev_setting.config_md5
            || (contact_md5 != dev_setting.contact_md5 && dev_setting.dclient_version >= D_CLIENT_VERSION_1_0)
            || (tz_md5 != gstAKCSConf.tz_md5 && tz_md5.size() > 0) || (tz_data_md5 != gstAKCSConf.tz_data_md5 && tz_data_md5.size() > 0)
            || face_md5 != dev_setting.face_md5
            || user_meta_md5 != dev_setting.user_meta_md5
            || schedule_md5 != dev_setting.schedule_md5))
    {
        KEY_SEND keySend;
        Snprintf(keySend.community, sizeof(keySend.community) / sizeof(TCHAR), dev_setting.community);
        Snprintf(keySend.mac, sizeof(keySend.mac) / sizeof(TCHAR), dev_setting.mac);
        Snprintf(keySend.device_node, sizeof(keySend.device_node) / sizeof(TCHAR), dev_setting.device_node);
        keySend.extension = dev_setting.extension;
        if (tz_md5.size() > 0)
        {
            keySend.tz_type = TIME_ZONE_XML;
        }
        else if (tz_data_md5.size() > 0)
        {
            keySend.tz_type = TIME_ZONE_DATA;
        }
        
        //判断新小区设备是否需要发keysend(TODO:设备上报时判断没问题,但是网页下发pin/rf时is)
        //CheckNewCommunityIsNeedkeySend(reportStatusMsg, deviceSetting, keySend);
        
        keySend.weak_conn = conn;
        //需要设备过来下载的消息需要放入数据库发送
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        GetKeyControlInstance()->AddKeySend(keySend);
    }
    //设备的ip有变化时候，要通知联动重新获取联系人。
    //社区公共设备不需要通知
    //TODO:如果公共设备和个人之间是内网，那么就要考虑
    //if (nIsAddrChange && deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    if (dev_setting.is_ip_addr_change) //公共设备变化也需要，因为公共设备和室内机可以走ip直播
    {
        AK_LOG_INFO << "[IPChange] Device Communit Mac:" << dev_setting.mac << " Notify Node rewrite contactlist";
        g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_IP_CHANGE, dev_setting.mac, dev_setting.manager_account_id, project::RESIDENCE, dev_setting.ip_addr);
    }

    //升级后重新写配置文件
    //TODO：keysend ipchange nIsDevUpgrade应该有重叠部分
    //ip变化从新写配置的范围比升级的要多，包括相关的设备
    if (dev_setting.is_ip_addr_change == 0 && dev_setting.is_dev_upgrade)//升级时候只管自己，因为两台设备间的配置文件并没有版本判断
    {
        AK_LOG_INFO << "[DevUpgrade] Device Mac:" << dev_setting.mac << ", Notify Node rewrite config contactlist,only this mac";
        g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_UPGRADE, dev_setting.mac, dev_setting.manager_account_id);
    }

    MacInfo mac2;
    CreateOnlineMacInfo(mac2, dev_setting);
    DevOnlineMng::GetInstance()->AddCommunityMac(mac2);
    
    evpp::Any status(true);
    evpp::Any client(std::string(dev_setting.mac));
    conn->set_context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX, status);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX, client);
    //2023 chenyc，csmain拆分,   从这里开始转发给csmain去做逻辑业务,后面所有的业务都不能放在这个代码下面进行处理，否则csresid跟csmain的逻辑会发生混乱
    Main2ResidMsgHandle::Instance()->Send(dev_setting.mac, csmain::DeviceType::COMMUNITY_DEV, conn_change, 
     (char*)&msg, msg_len);
    return 0;
}

int CMsgControl::OnOfficeDeviceReportStatusMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REPORT_STATUS& reportStatusMsg, DEVICE_SETTING& deviceSetting)
{
    int need_distinguish_project_type = project::OFFICE;
    OfficeInfo office_info(deviceSetting.project_uuid);
    deviceSetting.is_new_office = false;
    if (office_info.IsNew())
    {
        deviceSetting.is_new_office = true;
        need_distinguish_project_type = project::OFFICE_NEW;
    }
    //对比MD5，先处理PRIVATEKEY和RFID的MD5，TODO:地址MD5
    CString config_md5 = reportStatusMsg.config_md5;
    CString contact_md5 = reportStatusMsg.contact_md5;
    std::string tz_md5 = reportStatusMsg.tz_md5;
    std::string tz_data_md5 = reportStatusMsg.tz_data_md5;
    std::string face_md5 = reportStatusMsg.face_md5;
    std::string user_meta_md5 = reportStatusMsg.user_meta_md5;
    std::string schedule_md5 = reportStatusMsg.schedule_md5;
    std::string sw_ver = reportStatusMsg.SWVer;
    int func_bit = reportStatusMsg.func_bit;

    //不支持tzdata更新的设备不进行tzdata判断
    if (!GetKeyControlInstance()->IsDevSupportTzData(sw_ver, func_bit))
    {
        tz_data_md5 = "";
    }

    //版本升级和ip变化 从新写配置后一定会通知这台设备从新下载配置
    if (deviceSetting.is_ip_addr_change == 0 && deviceSetting.is_dev_upgrade == 0 
        && (config_md5 != deviceSetting.config_md5
            || contact_md5 != deviceSetting.contact_md5
            || (tz_md5 != gstAKCSConf.tz_md5 && tz_md5.size() > 0) || (tz_data_md5 != gstAKCSConf.tz_data_md5 && tz_data_md5.size() > 0)
            || face_md5 != deviceSetting.face_md5
            || user_meta_md5 != deviceSetting.user_meta_md5
            || schedule_md5 != deviceSetting.schedule_md5))
    {
        KEY_SEND keySend;
        Snprintf(keySend.community, sizeof(keySend.community) / sizeof(TCHAR), deviceSetting.community);
        Snprintf(keySend.mac, sizeof(keySend.mac) / sizeof(TCHAR), deviceSetting.mac);
        Snprintf(keySend.device_node, sizeof(keySend.device_node) / sizeof(TCHAR), deviceSetting.device_node);
        if (tz_md5.size() > 0)
        {
            keySend.tz_type = TIME_ZONE_XML;
        }
        else if (tz_data_md5.size() > 0)
        {
            keySend.tz_type = TIME_ZONE_DATA;
        }
        //CheckNewCommunityIsNeedkeySend(reportStatusMsg, deviceSetting, keySend);
        keySend.weak_conn = conn;
        //需要设备过来下载的消息需要放入数据库发送
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        GetKeyControlInstance()->AddOfficeKeySend(keySend);
    }
    if (deviceSetting.is_ip_addr_change) //公共设备变化也需要，因为公共设备和室内机可以走ip直播
    {
        AK_LOG_INFO << "[IPChange] Device Office Mac:" << deviceSetting.mac << " Notify Node rewrite contactlist";
        g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_IP_CHANGE, deviceSetting.mac, deviceSetting.manager_account_id, need_distinguish_project_type, deviceSetting.ip_addr);
    }

    //升级后重新写配置文件
    //TODO：keysend ipchange nIsDevUpgrade应该有重叠部分
    //ip变化从新写配置的范围比升级的要多，包括相关的设备
    if (deviceSetting.is_ip_addr_change == 0 && deviceSetting.is_dev_upgrade)//升级时候只管自己，因为两台设备间的配置文件并没有版本判断
    {
        AK_LOG_INFO << "[DevUpgrade] Device Mac:" << deviceSetting.mac << ", Notify Node rewrite config contactlist,only this mac";
        g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_UPGRADE, deviceSetting.mac, deviceSetting.manager_account_id, need_distinguish_project_type, deviceSetting.ip_addr);
    }

    MacInfo mac2;
    CreateOnlineMacInfo(mac2, deviceSetting);
    DevOnlineMng::GetInstance()->AddOfficeMac(mac2);
    return 0;
}

int CMsgControl::OnDeviceAlarmMsg(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密

    AK_LOG_WARN << "OnDeviceAlarmMsg msg ID11 = 0x" << hex << pNormalMsg->message_id;
    AK_LOG_WARN << "OnDeviceAlarmMsg msg ID22 = 0x" << hex << (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK);
    AK_LOG_WARN << "OnDeviceAlarmMsg msg ID33 = 0x" << hex << msg_version;

    SOCKET_MSG_ALARM alarmMsg;
    if (GetMsgControlInstance()->ParseAlarmMsg(pNormalMsg, &alarmMsg, msg_version) < 0)
    {
        AK_LOG_WARN << "ParseAlarmMsg failed.";
        return -1;
    }
    //V5.2兼容旧设备
    if (alarmMsg.alarm_code == 0)
    {
        alarmMsg.alarm_customize = 1;//自定义
    }
    /*
    //兼容门口机防拆/阿联酋门未关告警/SOS没有告警位置和防区 这部分兼容直接在推送服务器处理
    if (alarmMsg.alarm_code == 8 || alarmMsg.alarm_code == 1 || alarmMsg.alarm_code == 7)
    {
        alarmMsg.alarm_zone = 1;//防区1
        alarmMsg.alarm_location = 2;//告警类型DOOR
    }*/

    if (alarmMsg.alarm_code == (int)ALARM_CODE::EMERGENCY || alarmMsg.alarm_code == (int)ALARM_CODE::BREAK_IN)
    {
        AK_LOG_INFO << "Ignore alarm type: " <<  alarmMsg.alarm_code ;
        return 0;
    }

    //从本地读取DEVICE_SETTING
    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetLocalDeviceSetting failed.";
        return -1;
    }
    AK_LOG_INFO << "mac [" << deviceSetting.mac << "] receve alarm msg. localtion " << deviceSetting.location;
    if (deviceSetting.device_type == csmain::COMMUNITY_NONE)
    {
        AK_LOG_WARN << "The device has never report status msg, we can not make sure which type of it.";
        return -1;
    }
    
    // 推送alarm状态给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (strlen(deviceSetting.node_uuid) > 0 && 0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(deviceSetting.node_uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatus(deviceSetting.mac, traceid);
        AK_LOG_INFO << "alexa device alarm notify web , mac :" << deviceSetting.mac << ", traceid : " << traceid;
    }

    //apt和旧小区的emergency_alarm不插入数据库
    CommunityInfo community_info(deviceSetting.manager_account_id);
    if(alarmMsg.alarm_code == (int)ALARM_CODE::EMERGENCY && (deviceSetting.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL || !community_info.GetIsNew()))
    {
        return -1;
    }
    
    //如果是个人终端设备
    if (deviceSetting.device_type == csmain::PERSONNAL_DEV)
    {
        if (GetMsgControlInstance()->processPersonnalAlarmMsg(conn, alarmMsg, deviceSetting) != 0)
        {
            AK_LOG_WARN << "processPersonnalAlarmMsg Failed";
            return -1;
        }
        return 0;
    }
    else if (deviceSetting.device_type == csmain::COMMUNITY_DEV)
    {
        if (GetMsgControlInstance()->processCommunityAlarmMsg(conn, alarmMsg, deviceSetting) != 0)
        {
            AK_LOG_WARN << "processCommunityAlarmMsg Failed";
            return -1;
        }
    }
    return 0;
}

int CMsgControl::OnDevSendDelivery(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(deviceSetting.manager_account_id);
    if (!deviceSetting.is_personal 
        && comm_info->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_DELIVERY) 
        && !comm_info->IsExpire())
    {
        
        SOCKET_MSG_DEV_SEND_DELIVERY* delivery_msg = NULL;
        uint32_t data_size = NTOHS(pNormalMsg->data_size);
        char* payload = (char*)pNormalMsg->data;
        AesDecryptByMac(payload, payload, deviceSetting.mac, data_size);
        if (GetMsgHandleInstance()->ParseSendDelivery(payload, &delivery_msg) < 0)
        {
            AK_LOG_WARN << "ParseSendDelivery failed.";
            return -1;
        }

        SOCKET_MSG_DEV_SEND_DELIVERY* tmp_msg = delivery_msg;
        while (tmp_msg != NULL)
        {
            //家居对接
            LINKER_NORMAL_MSG linker_msg;
            memset(&linker_msg, 0, sizeof(linker_msg));
            if (deviceSetting.init_status == 0)
            {
                ProjectInfo project(deviceSetting.mac, deviceSetting.is_personal, linker_msg);
            }
            else
            {
                GetLinkerMsgFromDevice(deviceSetting, linker_msg);
            }

            ResidentPerAccount per_account;
            memset(&per_account, 0, sizeof(per_account));
            if (dbinterface::ResidentPersonalAccount::GetUidAccount(tmp_msg->account, per_account) == 0)
            {
                Snprintf(linker_msg.account_name, sizeof(linker_msg.account_name), per_account.name);
                Snprintf(linker_msg.account_uuid, sizeof(linker_msg.account_uuid), per_account.uuid);
                Snprintf(linker_msg.language, sizeof(linker_msg.language), per_account.language);
                if (per_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
                {
                    Snprintf(linker_msg.node_uuid, sizeof(linker_msg.account_uuid), per_account.uuid);
                }
                else if (per_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
                {
                    ResidentPerAccount main_account;
                    memset(&main_account, 0, sizeof(main_account));
                    if (dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, main_account) == 0)
                    {
                        Snprintf(linker_msg.node_uuid, sizeof(linker_msg.node_uuid), per_account.parent_uuid);
                    }
                }
            }
            PushLinKerDelivery(tmp_msg, linker_msg);
    
            AK::Server::P2PMainSendDelivery msg;
            msg.set_account(tmp_msg->account);
            msg.set_amount(tmp_msg->amount);
            msg.set_type(CPerTextNotifyMsg::DELIVERY_MSG);
            CAkcsPdu pdu;
            pdu.SetMsgBody(&msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(AKCS_M2R_P2P_SEND_DELIVERY_REQ);
            pdu.SetSeqNum(0);
            g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
            tmp_msg = tmp_msg->next;
        }

        SOCKET_MSG_DEV_SEND_DELIVERY* cur_msg = NULL;
        while (NULL != delivery_msg)    //结构体链表 内存释放
        {
            cur_msg = delivery_msg;
            delivery_msg = delivery_msg->next;
            delete cur_msg;
        }
    }
    else
    {
        AK_LOG_INFO << "MngAccount:" << deviceSetting.manager_account_id << " Deliverysend check result is 0. " 
            << "Don't send delivery msg.";
    }
    return 0;
}

int CMsgControl::OnDevSendDeliveryBox(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    SOCKET_MSG_DEV_SEND_DELIVERY_OEM delivery_msg;
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    AesDecryptByMac(payload, payload, deviceSetting.mac, data_size);
    if (GetMsgHandleInstance()->ParseSendDeliveryOem(payload, delivery_msg) < 0)
    {
        AK_LOG_WARN << "ParseSendDeliveryOem failed.";
        return -1;
    }

    Json::Value item;
    Json::FastWriter w;    
    item["AptNum"] = delivery_msg.apt_num;
    item["BoxNum"] = delivery_msg.box_num;
    item["BoxPwd"] = delivery_msg.box_pwd;
    std::string content = w.write(item);

    AK::Server::P2PMainSendDelivery msg;
    msg.set_account(delivery_msg.account);
    msg.set_content(content);
    msg.set_type(CPerTextNotifyMsg::DELIVERY_BOX_MSG);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_SEND_DELIVERY_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int CMsgControl::BuildUpdateConfigMsg(SOCKET_MSG* socket_msg, SOCKET_MSG_CONFIG* update_config_msg)
{
    if ((socket_msg == NULL) || (update_config_msg == NULL))
    {
        return -1;
    }

    SOCKET_MSG_NORMAL *msg_normal = (SOCKET_MSG_NORMAL *)socket_msg->data;

    char* pay_load = (char*)msg_normal->data;

    if (GetMsgHandleInstance()->BuildUpdateConfigMsg(pay_load, sizeof(msg_normal->data) - 1, update_config_msg) < 0)
    {
        AK_LOG_WARN << "BuildUpdateConfigMsg failed";
        return -1;
    }

    uint32_t data_size = strlen((char*)msg_normal->data);
    uint32_t head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    //modified by chenyc, 20230517,原函数AesEncryptByMac存在栈溢出的风险，弃用，改用安全的v2函数
    //AesEncryptByMac(pay_load, pay_load, update_config_msg->mac, &data_size); //
    if (AesEncryptByMacV2(pay_load, pay_load, update_config_msg->mac, &data_size, sizeof(msg_normal->data)) < 0)
    {
        AK_LOG_WARN << "Encrypt msg failed";
        return -1;
    }
    
    if (BuildNormalMsgHeader(socket_msg, MSG_TO_DEVICE_UPDATE_CONFIG, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "BuildNormalMsgHeader failed";
        return -1;
    }

    socket_msg->size = data_size + head_size;

    return 0;
}

int CMsgControl::OnFlowOutOfLimit(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    SOCKET_MSG_FLOW_OUT_LIMIT socket_msg_flow_out_of_limit;
    memset(&socket_msg_flow_out_of_limit, 0,  sizeof(socket_msg_flow_out_of_limit));
    if (GetMsgHandleInstance()->ParseFlowOutOfLimit(pay_load, &socket_msg_flow_out_of_limit) < 0)
    {
        AK_LOG_WARN << "ParseFlowOutOfLimit failed.";
        return -1;
    }

    CommunityInfo community_info(device_setting.manager_account_id);
    if (community_info.LimitFlowRemind() == FlowOutLimitRemind::NO_REMIND)
    {
        AK_LOG_INFO << "Community=" << device_setting.manager_account_id << " Is Set To No Remind OutOfLimitFlow";
        return 0;
    }


    if (community_info.MobileNumber().size() == 0 || community_info.PhoneCode().size() == 0)
    {
        AK_LOG_INFO << "Community=" << device_setting.manager_account_id << " MobileNumber=" << community_info.MobileNumber() << " PhoneCode=" << community_info.PhoneCode() << " is not set,send sms failed.";
        return -1;
    }
    std::tuple<std::string, std::string> lang_location = community_info.GetPMAccountLanguageLocation();
    std::string language = std::get<0>(lang_location);
    if (language.size() == 0)
    {
        AK_LOG_INFO << "Community=" << device_setting.manager_account_id << " language is not set,send sms failed.";
        return -1;
    }
    std::string community_name = std::get<1>(lang_location);

    const int SMS_FLOW_OUT_OF_LIMIT = 3;
    AK::Server::SendSmsRemindFlowOutofLimit msg;
    msg.set_type(SMS_FLOW_OUT_OF_LIMIT);
    msg.set_phone(community_info.MobileNumber());
    msg.set_area_code(community_info.PhoneCode());
    msg.set_location(device_setting.location);
    msg.set_grade(device_setting.grade);
    msg.set_language(language);
    msg.set_community_name(community_name);

    if (device_setting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        std::string unit_name = community_info.GetCommunityUnitName(device_setting.unit_id);
        msg.set_unit_name(unit_name);
    }
    else if (device_setting.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        //do nothing
    }
    else
    {
        AK_LOG_INFO << "Device MAC=" << device_setting.mac << " Grade=" << device_setting.grade << " do not need remind outoflimitflow";
        return 0;
    }

    const unsigned int ONE_GB = 1073741824;
    double remind_flow = socket_msg_flow_out_of_limit.limit * socket_msg_flow_out_of_limit.percent / 100.0 / ONE_GB;
    char flow_gb[20];
    snprintf(flow_gb, sizeof(flow_gb), "%.02f", remind_flow);
    msg.set_flow(flow_gb);

    const int FLOW_EXHAUSTED = 1;
    const int FLOW_REMAINED = 0;
    if ((int)socket_msg_flow_out_of_limit.percent >= 100)
    {
        msg.set_out_of_flow(FLOW_EXHAUSTED);
    }
    else
    {
        msg.set_out_of_flow(FLOW_REMAINED);
    }    

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_SEND_REMIND_FLOW_OUT_OF_LIMIT);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int CMsgControl::OnDevReqChangeRelay(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn)
{
    if (NULL == pNormalMsg)
    {
        return -1;
    }

    SOCKET_MSG_DEV_RELAY_CHANGE relay_msg;
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    AesDecryptByDefault(payload, payload, data_size);
    if (GetMsgHandleInstance()->ParseReqChangeRelay(payload, relay_msg) < 0)
    {
        AK_LOG_WARN << "ParseReqChangeRelay failed.";
        return -1;
    }

    AK::Server::P2PMainChangeRelay msg;
    msg.set_relay_switch(relay_msg.relay_switch);
    msg.set_relay_id(relay_msg.relay_id);
    msg.set_mac(relay_msg.mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_CHANGE_RELAY_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int CMsgControl::OnRequestUserInfo(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    SOCKET_MSG_USER_INFO socket_msg_user_infos;
    memset(&socket_msg_user_infos, 0,  sizeof(socket_msg_user_infos));
    if (GetMsgHandleInstance()->ParseUserInfos(pay_load, &socket_msg_user_infos) < 0)
    {
        AK_LOG_WARN << "ParseUserInfos failed.";
        return -1;
    }

    std::string accounts_key = device_setting.mac;
    accounts_key += akuvox_encrypt::MD5(socket_msg_user_infos.uuids).toStr();
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Server::P2PMainRequestWriteUserinfo msg;
    msg.set_mac(device_setting.mac);
    msg.set_uuids(socket_msg_user_infos.uuids);
    msg.set_msg_traceid(traceid);
    msg.set_accounts_key(accounts_key);
    msg.set_dclient_ver(device_setting.dclient_version);
    msg.set_project_uuid(device_setting.project_uuid);
    std::time_t t = std::time(0);
    msg.set_timestamp(t);

    socket_msg_user_infos.traceid = traceid;
    snprintf(socket_msg_user_infos.mac, sizeof(socket_msg_user_infos.mac), "%s", device_setting.mac);
    DevUpdateUserLog::InsertLog(socket_msg_user_infos);
    
    //add to redis。这个防止adapt处理不过来，而设备一直重复请求，导致永远处理不了的的问题
    int already_handle = 0;    
    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail); //获取与redis实例的tcp连接
    if (cache_conn)
    {
        std::string key = accounts_key;
        if (!cache_conn->isExists(key))
        {
            std::string ret = cache_conn->set(key, device_setting.mac);
            cache_conn->expire(key, gstAKCSConf.download_user_timeout);
        }
        else
        {
            already_handle = 1;
        }
        cache_mng->RelCacheConn(cache_conn);
    }
    if (!already_handle)
    {
        AK_LOG_INFO << "Mac:" << device_setting.mac << " request user info, userinfo:" << socket_msg_user_infos.uuids << " traceid:" << traceid;    
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_S2C_DEV_REQ_USER_INFO);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }
    else
    {
        AK_LOG_INFO << "Mac:" << device_setting.mac << " request user info already in queue!";
    }

    return 0;
}

int CMsgControl::OnDeviceReportAccessTimesMsg(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    SOCKET_MSG_DEV_REPORT_ACCESS_TIMES dev_report_access_times;
    memset(&dev_report_access_times, 0, sizeof(dev_report_access_times));

    if (GetMsgHandleInstance()->ParseReportAccessTimesMsg(pay_load, &dev_report_access_times) < 0)
    {
        AK_LOG_WARN << "ParseReportAccessTimesMsg failed.";
        return -1;
    }


    if (GetPersonnalTmpKeyInstance()->UpdateAccessTimes(dev_report_access_times) < 0)
    {
        char error_msg[1024] = "";
        ::snprintf(error_msg, sizeof(error_msg), "[ReportAccessTimes]When update Mac=%s,unique_id=%s,temp_key=%s in DB,return error!", device_setting.mac, dev_report_access_times.unique_id, dev_report_access_times.temp_key);
        AK_LOG_WARN << error_msg;
        return -1;
    }
    AK_LOG_INFO << "UpdateAccessTimes Success";

    return 0;
}
/*
int CMsgControl::OnDeviceRequestRegEndUser(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING cache_dev;
    memset(&cache_dev, 0, sizeof(cache_dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &cache_dev) < 0)
    {
        AK_LOG_WARN << "OnDeviceRequestRegEndUser GetDeviceSetting failed.";
        return -1;
    }

    // 必须从数据库查, hager设备的flags不能从缓存拿
    ResidentDev dev;
    if (0 != dbinterface::ResidentPerDevices::GetMacDev(cache_dev.mac, dev) 
      && 0 != dbinterface::ResidentDevices::GetMacDev(cache_dev.mac, dev))
    {
        AK_LOG_WARN << "OnDeviceRequestRegEndUser GetMacDev failed, mac = " << cache_dev.mac;
        return -1;
    }

    if (dev.project_type == project::PERSONAL)
    {
        HandlePersonalKitRegister(dev);
    }
    else if (dev.project_type == project::RESIDENCE)
    {
        HandleCommunityKitRegister(dev);
    }

    return 0;
}

void CMsgControl::HandlePersonalKitRegister(const ResidentDev& dev)
{
    // 判断是否为kit设备
    if (!dbinterface::SwitchHandle(dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "HandlePersonalKitRegister, Indoor is Not Kit device, cannot resquest this interface, mac = " << dev.mac;
        return;
    }

    // 获取kit注册信息
    RegEndUserInfo reg_user_info;
    reg_user_info.user_projcet_type = dev.project_type;
    if (0 != dbinterface::PendingRegUser::GetPendingRegUserInfoByMac(dev.mac, reg_user_info))
    {
        AK_LOG_WARN << "HandlePersonalKitRegister GetPendingRegUserInfoByMac failed, mac = " << dev.mac;
        return;
    }

    // 更新token
    std::string token = GetNbitRandomString(18);
    Snprintf(reg_user_info.token, sizeof(reg_user_info.token), token.c_str());
    if (0 != dbinterface::PendingRegUser::UpdatePendingRegUserToken(dev.mac, token))
    {
        AK_LOG_WARN << "HandlePersonalKitRegister UpdatePendingRegUserToken failed, mac = " << dev.mac;
        return;
    }
    
    AK_LOG_INFO << "personal indoor request kit register success, mac = " << dev.mac << ", account = " << reg_user_info.account << ", token = " << reg_user_info.token;
    SendRegEndUserUrl(dev, reg_user_info);
    return;
}

void CMsgControl::HandleCommunityKitRegister(const ResidentDev& dev)
{
    RegEndUserInfo reg_user_info;
    Snprintf(reg_user_info.mac, sizeof(reg_user_info.mac), dev.mac);
    Snprintf(reg_user_info.account, sizeof(reg_user_info.account), dev.node);

    //兼容网页上填写邮箱/手机号注册的场景
    ResidentPerAccount per_account;
    reg_user_info.user_projcet_type = dev.project_type;
    dbinterface::ResidentPersonalAccount::GetUidAccount(reg_user_info.account, per_account);
    if (strlen(per_account.getEmail()) > 0 || strlen(per_account.getMobileNumber()) > 0)
    {
        //用户填写了邮箱或手机号都下发用户信息
        reg_user_info.status = (int)KitEndUserDisplayStatus::USER_REG_INFO;
    }
    else 
    {
        //都未填写的,根据社区"Scan indoor monitor QR code to register app account"开关判断是否下发
        CommunityInfo comm_info(dev.project_mng_id);
        if (!comm_info.IsSupportScanIndoorQRCodeToReg())
        {
            reg_user_info.status = (int)KitEndUserDisplayStatus::CLEAR_USER_INFO; //通过下发空url清空原有的二维码
            AK_LOG_WARN << "not support scan indoor qr code to register app account. mac = " << dev.mac;
        }
    }

    //需要用户通过室内机二维码注册时，插入信息到PendingRegUser表中,相当于导入kit用户
    if ((int)KitEndUserDisplayStatus::USER_REG_CODE == reg_user_info.status 
        && 0 != dbinterface::CommunityPendingRegUser::InsertCommunityPendingRegUser(reg_user_info))
    {
        AK_LOG_WARN << "HandleCommunityKitRegister InserPendingRegUser fail, mac = " << dev.mac << ", account = " << reg_user_info.account;
        return;
    }

    AK_LOG_INFO << "community indoor request kit register success, mac = " << dev.mac << ", account = " << reg_user_info.account << ", token = " << reg_user_info.token;
    SendRegEndUserUrl(dev, reg_user_info);
    return;
}

void CMsgControl::GetRegisterUrl(const short oem_id, RegEndUserInfo& user_info)
{
    if (oem_id == OEMID_HAGER)
    {
        snprintf(user_info.reg_url, sizeof(user_info.reg_url), "https://intercom.hager-iot.com/?account=%s&token=%s", user_info.account, user_info.token);
    }
    else if (oem_id == OEMID_NICE)
    {
        ResidentPerAccount per_account;
        if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(user_info.account, per_account))
        {
            AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, account = " << user_info.account;
            return;
        }

        if (per_account.role != ACCOUNT_ROLE_COMMUNITY_MAIN)
        {
            AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, node role not match, role = " << per_account.role;
            return;
        }

        dbinterface::AccountInfo project_info;
        if (0 != dbinterface::Account::GetAccountByUUID(per_account.parent_uuid, project_info))
        {
            AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, Get Project Info Failed, account = " << per_account.account;
            return;
        }

        CommunityUnitInfo unit_info;
        if (0 != dbinterface::CommunityUnit::GetCommunityUnitByID(per_account.unit_id, unit_info))
        {
            AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, Get Unit Info Failed, account = " << per_account.account;
            return;
        }
        
        CommunityRoomInfo room_info;
        if (0 != dbinterface::CommunityRoom::GetCommunityRoomByID(per_account.room_id, room_info))
        {
            AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, Get Unit Info Failed, account = " << per_account.account;
            return;
      }

        snprintf(user_info.reg_url, sizeof(user_info.reg_url), "https://mybell.yubii.com/kit?account=%s&token=%s&community=%s&building=%s&apt=%s", 
                                        user_info.account, user_info.token, project_info.location, unit_info.unit_name, room_info.room_number);
    }
    else
    {
        if (user_info.user_projcet_type == project::PERSONAL)
        {
            snprintf(user_info.reg_url, sizeof(user_info.reg_url), "http://%s/smartplus/Kit.html?account=%s&token=%s", gstAKCSConf.web_domain, user_info.account, user_info.token);
        }
        else if (user_info.user_projcet_type == project::RESIDENCE)
        {
            snprintf(user_info.reg_url, sizeof(user_info.reg_url), "http://%s/smartplus/CommunityKit.html?account=%s&token=%s", gstAKCSConf.web_domain, user_info.account, user_info.token);
        }
    }

    std::string reg_url_encode = URLEncode(user_info.reg_url);
    Snprintf(user_info.reg_url, sizeof(user_info.reg_url), reg_url_encode.c_str());
    return;
}

void CMsgControl::SendRegEndUserUrl(const ResidentDev& dev, RegEndUserInfo& reg_user_info)
{
    //主线设备会先去判断url是否为空，因此即使用户已注册也要去获取url
    GetRegisterUrl(dev.oem_id, reg_user_info);
    if (reg_user_info.status == (int)KitEndUserDisplayStatus::CLEAR_USER_INFO)
    {
        memset(reg_user_info.reg_url, 0, sizeof(reg_user_info.reg_url));
        //status为该类型且url为空，实现清空用户信息
        reg_user_info.status = (int)KitEndUserDisplayStatus::USER_REG_CODE;
    }
    
    SOCKET_MSG_PERSONNAL_APP_CONF app_conf;
    memset(&app_conf, 0, sizeof(app_conf));
    Snprintf(app_conf.user, sizeof(app_conf.user), reg_user_info.account);
    
    GetPersonalAccountInstance()->DaoGetNodeByAppUser(app_conf);
    Snprintf(reg_user_info.email, sizeof(reg_user_info.email), app_conf.email);
    Snprintf(reg_user_info.account_name, sizeof(reg_user_info.account_name), app_conf.username);
    Snprintf(reg_user_info.mobile_number, sizeof(reg_user_info.mobile_number), app_conf.mobile_number);

    GetDeviceControlInstance()->SendRegEndUserUrl(dev.mac, reg_user_info);
    
    AK_LOG_INFO << "DeviceRequestRegEndUser success, mac = " << dev.mac << ", account = " << app_conf.user;
    return;
}


bool CMsgControl::AddKitDeviceDefaultValue(SOCKET_MSG_DEV_KIT_DEVICE &kit_device)
{
    int type = dbinterface::VersionModel::GetDeviceType(kit_device.version);
    if (type == DEVICE_TYPE_STAIR || type == DEVICE_TYPE_DOOR || type == DEVICE_TYPE_ACCESS)
    {
        Snprintf(kit_device.location, sizeof(kit_device.location), "Door");
        Snprintf(kit_device.relay, sizeof(kit_device.relay), model::DEFAULT_DOOR_REALY.c_str());
        if (type == DEVICE_TYPE_STAIR)
        {
            type = DEVICE_TYPE_DOOR;
        }
    }
    else if (type == DEVICE_TYPE_INDOOR)
    {
        Snprintf(kit_device.location, sizeof(kit_device.location), "Room");
        Snprintf(kit_device.relay, sizeof(kit_device.relay), model::DEFAULT_INDOOR_REALY.c_str());
    }
    else
    {
        return false;
    }

    kit_device.type = type;
    return true;
}

int CMsgControl::OnDeviceReportKitDevices(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    //家庭是否为Kit方案
    if (!dbinterface::SwitchHandle(device_setting.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "Indoor is Not Kit device, did not resquest this interface. mac: " << device_setting.mac;
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    std::vector<SOCKET_MSG_DEV_KIT_DEVICE> kit_devices;
    if (GetMsgHandleInstance()->ParseReportKitDevices(pay_load, kit_devices) < 0)
    {
        AK_LOG_WARN << "ParseReportKitDevices failed.";
        return -1;
    }

    AK_LOG_INFO << "Begin Auto Add Kit Devices,size=" << kit_devices.size();
    for (auto kit_device : kit_devices)
    {
        if (0 == strlen(kit_device.version) || 0 == strlen(kit_device.mac))
        {
            continue;
        }

        if (!dbinterface::ResidentPerDevices::CheckKitDevice(kit_device.mac, device_setting.device_node))
        {
            continue;
        }

        if (!AddKitDeviceDefaultValue(kit_device))
        {
            AK_LOG_INFO << "Kit Device MAC=" << kit_device.mac << " firmware type=" << kit_device.type << " is error";
            continue;
        }

        AK_LOG_INFO << kit_device.Print();
        PostAddKitDeviceHttpReq(kit_device, device_setting.device_node, MSG_FROM_DEVICE_REPORT_KIT_DEVICES);
    }
    AK_LOG_INFO << "Auto Add Kit Devices Success.";

    return 0;
}


int CMsgControl::OnDeviceAddKitDevices(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }
    
    //家庭是否为Kit方案
    if (!dbinterface::SwitchHandle(device_setting.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "Indoor is Not Kit device, did not resquest this interface. mac: " << device_setting.mac;
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    std::vector<SOCKET_MSG_DEV_KIT_DEVICE> kit_devices;
    if (GetMsgHandleInstance()->ParseReportKitDevices(pay_load, kit_devices) < 0)
    {
        AK_LOG_WARN << "ParseReportKitDevices failed.";
        return -1;
    }

    AK_LOG_INFO << "Begin Munual Add Kit Devices,size=" << kit_devices.size();
    for (auto kit_device : kit_devices)
    {
        if (0 == strlen(kit_device.mac))
        {
            continue;
        }

        if (!dbinterface::ResidentPerDevices::CheckKitDevice(kit_device.mac, device_setting.device_node))
        {
            continue;
        }

        int device_type = kit_device.type;
        if (device_type == DEVICE_TYPE_STAIR || device_type == DEVICE_TYPE_DOOR || device_type == DEVICE_TYPE_ACCESS)
        {
            Snprintf(kit_device.relay, sizeof(kit_device.relay), model::DEFAULT_DOOR_REALY.c_str());
        }
        else if (device_type == DEVICE_TYPE_INDOOR)
        {
            Snprintf(kit_device.relay, sizeof(kit_device.relay), model::DEFAULT_INDOOR_REALY.c_str());
        }
        else
        {
            AK_LOG_INFO << "Kit Device MAC=" << kit_device.mac << " device_type=" << kit_device.type << " is error";
            continue;
        }
        AK_LOG_INFO << kit_device.Print();
        PostAddKitDeviceHttpReq(kit_device, device_setting.device_node, MSG_FROM_DEVICE_ADD_KIT_DEVICES);
    }

    AK_LOG_INFO << "Munual Add Kit Devices Success.";
    return 0;
}

int CMsgControl::OnDeviceRequestKitDevices(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "OnDeviceRequestKitDevices GetDeviceSetting failed.";
        return -1;
    }

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (dev.project_type == project::PERSONAL)
    {
        HandlePersonalKitRequestDevices(dev, socket_msg);
    }
    else if (dev.project_type == project::RESIDENCE)
    {
        HandleCommunityKitRequestDevices(dev, socket_msg);
    }

    SendKitRequestDevices(conn, dev, socket_msg);
    
    AK_LOG_INFO << "OnDeviceRequestKitDevices success, node = " << dev.device_node << ", mac = " << dev.mac;
    return 0;
}


void CMsgControl::HandlePersonalKitRequestDevices(const DEVICE_SETTING& dev, SOCKET_MSG& socket_msg)
{    
    //家庭是否为Kit方案
    if (!dbinterface::SwitchHandle(dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "HandlePersonalKitRequestDevices, Not Kit device, can not resquest this interface. mac: " << dev.mac;
        return;
    }

    std::vector<PERSONNAL_DEVICE_SIP> kit_devices;
    if (GetPersonnalDevSettingInstance()->GetDeviceSettingByNode(dev.device_node, DEVICE_TYPE_INDOOR, kit_devices) < 0)
    {
        AK_LOG_WARN << "HandlePersonalKitRequestDevices GetDeviceSettingByNode failed,node=" << dev.device_node;
        return;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
    GetMsgHandleInstance()->BuildReqPersonalKitDevices((char*)msg_normal->data, sizeof(msg_normal->data), kit_devices);

    AK_LOG_INFO << "HandlePersonalKitRequestDevices Node=" << dev.device_node << " has [" << kit_devices.size() << "] kit devices.";
    return;
}

void CMsgControl::HandleCommunityKitRequestDevices(const DEVICE_SETTING& dev, SOCKET_MSG& socket_msg)
{
    if (dev.type != DEVICE_TYPE_INDOOR || dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        AK_LOG_WARN << "HandleCommunityKitRequestDevices failed, dev type or grade error, mac = " << dev.mac << ", type = " << dev.type << ", grade = " << dev.grade;
        return;
    }

    std::vector<COMMUNITY_DEVICE_SIP> kit_devices;
    if (GetDeviceSettingInstance()->GetDeviceSettingByNode(dev.device_node, DEVICE_TYPE_INDOOR, kit_devices))
    {
        AK_LOG_WARN << "HandleCommunityKitRequestDevices GetDeviceSettingByNode failed, mac = " << dev.mac << ", node = " << dev.device_node;
        return;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
    GetMsgHandleInstance()->BuildReqCommunityKitDevices((char*)msg_normal->data, sizeof(msg_normal->data), kit_devices);
    
    AK_LOG_INFO << "HandleCommunityKitRequestDevices Node=" << dev.device_node << " has [" << kit_devices.size() << "] kit devices.";
    return;    
}

void CMsgControl::SendKitRequestDevices(const evpp::TCPConnPtr &conn, const DEVICE_SETTING& dev, SOCKET_MSG& socket_msg)
{
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
    char* pay_load = (char*)msg_normal->data;
    uint32_t data_size = strlen(pay_load);
    
    AesEncryptByMacV2(pay_load, pay_load, dev.mac, &data_size, sizeof(msg_normal->data));
    
    if (BuildNormalMsgHeader(&socket_msg,  MSG_TO_DEVICE_REPORT_KIT_DEVICES, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "BuildNormalMsgHeader failed";
        return;
    }
    
    socket_msg.size = data_size + SOCKET_MSG_NORMAL_HEADER_SIZE;
    
    GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size);

    return;
}





int CMsgControl::OnDeviceModifyLocation(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    SOCKET_MSG_DEV_KIT_DEVICE kit_device;
    memset(&kit_device, 0, sizeof(kit_device));

    if (GetMsgHandleInstance()->ParseReqModifyLocation(pay_load, &kit_device) < 0)
    {
        AK_LOG_WARN << "ParseReqModifyLocation failed.";
        return -1;
    }

    AK_LOG_INFO << "OnDeviceModifyLocation " << kit_device.Print(); 
    PostModifyLocationHttpReq(kit_device.mac, kit_device.location, device_setting.device_node, device_setting.project_type);
    return 0;
}

void CMsgControl::PostAddKitDeviceHttpReq(const SOCKET_MSG_DEV_KIT_DEVICE &kit_device, const char *node, int command_id)
{
    std::string data;
    char key_value[3000];

    snprintf(key_value, sizeof(key_value), "MAC=%s", kit_device.mac);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Node=%s", node);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Location=%s", URLEncode(kit_device.location).c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Type=%d", kit_device.type);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Relay=%s", kit_device.relay);
    data += key_value;

    char url[1024];
    snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/single/device/add", gstAKCSConf.web_domain);
    
    Json::Value item;
    Json::FastWriter w;
    item["data"] = data;
    item["url"] = url;
    std::string data_json = w.write(item);

    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_KIT, data_json, node);
}

void CMsgControl::PostModifyLocationHttpReq(const std::string &mac, const std::string &location, const std::string &node, int project_type)
{
    std::string url;
    std::string data = "MAC=" + mac + "&Node=" + node + "&Location=" + URLEncode(location);

    if (project_type == project::PERSONAL)
    {
        url = "https://" + std::string(gstAKCSConf.web_domain) + "/web-server/v3/basic/single/device/editLocation";
    }
    else if (project_type == project::RESIDENCE)
    {
        url = "https://" + std::string(gstAKCSConf.web_domain) + "/web-server/v3/basic/community/device/editLocation";
    }

    Json::Value item;
    Json::FastWriter fast_writer;
    item["data"] = data;
    item["url"] = url;
    std::string data_json = fast_writer.write(item);

    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_KIT, data_json, node);
}

*/

void CMsgControl::PostAwsPushTokenHttpReq(const std::string& uid, const CMobileToken& push_token)
{
    std::string data;
    char key_value[512];

    snprintf(key_value, sizeof(key_value), "%s=%s", "uid", uid.c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%d", "mobile_type", push_token.MobileType());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "fcm_token", URLEncode(push_token.FcmToken()).c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "token", URLEncode(push_token.Token()).c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "voip_tpken", URLEncode(push_token.VoipToken()).c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "uid_node", push_token.UidNode().c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%d", "common_version", push_token.CommonVersion());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "app_version", URLEncode(push_token.AppVersion()).c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "language", URLEncode(push_token.Language()).c_str());
    data += key_value;

    char url[128];
    snprintf(url, sizeof(url), "https://%s/InsertAwsPushToken", gstAKCSConf.apiurl);

    CHttpReqNotifyMsg notify_msg(url, data);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}


void CMsgControl::PostAwsTmpKeyHttpReq(int access_times, int id, const std::string& table)
{
    std::string data;
    char key_value[256];

    snprintf(key_value, sizeof(key_value), "&%s=%d", "access_times", access_times);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%d", "id", id);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "table", table.c_str());
    data += key_value;

    char url[128];
    snprintf(url, sizeof(url), "https://%s/UpdateAwsTmpKey", gstAKCSConf.apiurl);

    CHttpReqNotifyMsg notify_msg(url, data);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

void CMsgControl::PostAwsDealAlarmHttpReq(const std::string& table, const std::string& user, int id)
{
    std::string data;
    char key_value[256];

    snprintf(key_value, sizeof(key_value), "&%s=%s", "deal_user", URLEncode(user).c_str());
    data += key_value;    
    snprintf(key_value, sizeof(key_value), "&%s=%d", "id", id);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%s", "table", table.c_str());
    data += key_value;

    char url[128];
    snprintf(url, sizeof(url), "https://%s/UpdateAwsAlarm", gstAKCSConf.apiurl);

    CHttpReqNotifyMsg notify_msg(url, data);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

void CMsgControl::PostAwsDelPushTokenHttpReq(const std::string& uid)
{
    std::string data;
    char key_value[512];

    snprintf(key_value, sizeof(key_value), "%s=%s", "uid", uid.c_str());
    data += key_value;
    
    char url[128];
    snprintf(url, sizeof(url), "https://%s/DeleteAwsPushToken", gstAKCSConf.apiurl);

    CHttpReqNotifyMsg notify_msg(url, data);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

void CMsgControl::PostAwsInsertMessageHttpReq(PerMsgSendList& text_messages)
{
    if (0 == text_messages.size())
    {
        return;
    }
    std::string data;
    Json::Value item;
    Json::Value item_data;
    Json::Value client_obj;
    Json::FastWriter w;
    item["title"] = text_messages[0].text_message.title;
    item["content"] = text_messages[0].text_message.content;
    item["type"] = text_messages[0].text_message.type;  
    item["per_manager_id"] = text_messages[0].per_manager_id;
    for (const auto& msg : text_messages)
    {
        item_data["account"] = msg.account;
        item_data["client_type"] = msg.client_type;
        client_obj.append(item_data);
    }
    item["client"] = client_obj;
    data = w.write(item);

    char url[128];
    snprintf(url, sizeof(url), "https://%s/InsertAwsMessage", gstAKCSConf.apiurl);

    CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

void CMsgControl::PostAwsIndoorSche(const std::string& mac, int is_per)
{
    std::string data;
    char key_value[256];

    snprintf(key_value, sizeof(key_value), "&%s=%s", "mac", mac.c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&%s=%d", "is_per", is_per);
    data += key_value;

    char url[128];
    snprintf(url, sizeof(url), "https://%s/UpdateAwsIndoorSche", gstAKCSConf.apiurl);

    CHttpReqNotifyMsg notify_msg(url, data);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

// 推送状态变化给alexa
void CMsgControl::PostAlexaChangeStatus(const std::string& mac, uint64_t traceid)
{
    std::string data;
    Json::Value item;
    Json::FastWriter fast_writer;

    item["MAC"] = mac;
    item["TraceId"] = std::to_string(traceid);
    data = fast_writer.write(item);

    char url[128];
    snprintf(url, sizeof(url), "http://%s/alexaInner/v1/device/changeStatus", gstAKCSConf.smg_alexa_addr);

    CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

void CMsgControl::OnTestReqUserInfo(const std::string& mac, const std::string& users_list)
{
    SOCKET_MSG_USER_INFO socket_msg_user_infos;
    memset(&socket_msg_user_infos, 0,  sizeof(socket_msg_user_infos));
    Snprintf(socket_msg_user_infos.mac, sizeof(socket_msg_user_infos.mac), mac.c_str());
    Snprintf(socket_msg_user_infos.uuids, sizeof(socket_msg_user_infos.uuids), users_list.c_str());

    std::string accounts_key = mac;
    accounts_key += akuvox_encrypt::MD5(socket_msg_user_infos.uuids).toStr();
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Server::P2PMainRequestWriteUserinfo msg;
    msg.set_mac(mac);
    msg.set_uuids(users_list);
    msg.set_msg_traceid(traceid);
    msg.set_accounts_key(accounts_key);
    std::time_t t = std::time(0);
    msg.set_timestamp(t);

    socket_msg_user_infos.traceid = traceid;
    DevUpdateUserLog::InsertLog(socket_msg_user_infos);
    
    //add to redis 
    int already_handle = 0;    
    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail); //获取与redis实例的tcp连接
    if (cache_conn)
    {
        std::string key = accounts_key;
        if (!cache_conn->isExists(key))
        {
            std::string ret = cache_conn->set(key, socket_msg_user_infos.mac);
            cache_conn->expire(key, gstAKCSConf.download_user_timeout);
            cache_mng->RelCacheConn(cache_conn);
        }
        else
        {
            already_handle = 1;
        }
    }
    if (!already_handle)
    {
        
        AK_LOG_INFO << "OnTestReqUserInfo, mac:" << socket_msg_user_infos.mac << " request user info, userinfo:" << socket_msg_user_infos.uuids << " traceid:" << traceid;    
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_S2C_DEV_REQ_USER_INFO);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

#ifdef AKCS_EVENT_FILTER
        //added by chenyc, 2022.01.19,发送给拦截器,压测用
        if(gstAKCSConf.stress_test)
        {
           g_event_filter->DealEvent(EF_TEST_REQ_USER_INFO_UPDATE, socket_msg_user_infos);
        }
#endif
    }
    else
    {
        AK_LOG_INFO << "OnTestReqUserInfo, mac:" << socket_msg_user_infos.mac<< " request user info already in queue";
    }

}
int CMsgControl::OnOfflineActiveMessage(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    SOCKET_MSG_DEV_OFFLINE_ACTIVE msg;
    memset(&msg, 0, sizeof(msg));

    if (GetMsgHandleInstance()->ParseOfflineActiveMsg(pay_load, &msg) < 0)
    {
        AK_LOG_WARN << "ParseReqModifyLocation failed.";
        return -1;
    }
    dbinterface::OfflineResendLog::InsertOfflineResendLog(device_setting.mac, msg.file_name, msg.sequence);
    return 0;
}

int CMsgControl::OnReportFileMD5Message(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    SOCKET_MSG_REPORT_FILEMD5 msg;
    memset(&msg, 0, sizeof(msg));
    if (ParseReportFileMd5Msg(normal_msg, msg, device_setting.mac) < 0) //mac加密的
    {
        AK_LOG_WARN << "ParseReportFileMd5Msg failed.";
        return -1;
    }
    
    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    int ret = 0;
    if ((ret = GetDeviceControlInstance()->GetDeviceSettingByMac(device_setting.mac, &dev)) < 0)
    {

    }

    std::string privatekey_md5 = msg.private_key_md5;
    std::string rfid_md5 = msg.rf_id_md5;
    std::string config_md5 = msg.config_md5;
    std::string contact_md5 = msg.contact_md5;
    std::string user_meta_md5 = msg.user_meta_md5;
    std::string schedule_md5 = msg.schedule_md5;

    //单住户
    if (dev.device_type == csmain::PERSONNAL_DEV)
    {
        if (privatekey_md5 != dev.private_key_md5
                || rfid_md5 != dev.rf_id_md5
                || config_md5 != dev.config_md5
                || contact_md5 != dev.contact_md5)
        {

            PERSONAL_KEY_SEND key;
            snprintf(key.mac, sizeof(key.mac), "%s", device_setting.mac);
            snprintf(key.node, sizeof(key.node), "%s", device_setting.device_node);
            key.weak_conn = conn;
            GetKeyControlInstance()->AddPersonalKeySend(key);
       }
    }
    else
    {
        if (config_md5 != dev.config_md5
                || contact_md5 != dev.contact_md5
                || user_meta_md5 != dev.user_meta_md5
                || schedule_md5 != dev.schedule_md5)
        {
            KEY_SEND key;
            snprintf(key.mac, sizeof(key.mac), "%s", device_setting.mac);
            key.weak_conn = conn;
           
            if (dev.device_type == csmain::OFFICE_DEV)
            {
                GetKeyControlInstance()->AddOfficeKeySend(key);
            }
            else 
            {
                GetKeyControlInstance()->AddKeySend(key);
            }
        }

    }
    
    return 0;
}


//只做在社区，语音留言上报
int CMsgControl::OnDeviceReportVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    PersonalVoiceMsgInfo per_voice_msg;
    memset(&per_voice_msg, 0, sizeof(per_voice_msg));
    if (GetMsgHandleInstance()->ParseReportVoiceMsg(payload, &per_voice_msg) < 0)
    {
        AK_LOG_WARN << "ParseReportVoiceMsg failed.";
        return -1;
    }

    //插入PersonalVoiceMsg
    
    dbinterface::AccountInfo account;
    if (0 == dbinterface::Account::GetAccountById(dev.manager_account_id, account))
    {
        snprintf(per_voice_msg.project_uuid, sizeof(per_voice_msg.project_uuid), "%s", account.uuid);
    }
    std::string prefix = gstAKCSConf.server_tag;
    prefix = prefix + "-";
    std::string msg_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, msg_uuid);
    snprintf(per_voice_msg.uuid, sizeof(per_voice_msg.uuid), "%s", msg_uuid.c_str());
    snprintf(per_voice_msg.mac, sizeof(per_voice_msg.mac), "%s", dev.mac);
    snprintf(per_voice_msg.dev_uuid, sizeof(per_voice_msg.dev_uuid), "%s", dev.uuid);
    snprintf(per_voice_msg.location, sizeof(per_voice_msg.location), "%s", dev.location);
    if (0 != dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsg(per_voice_msg))
    {
        AK_LOG_WARN << "InsertPersonalVoiceMsg failed.";
        return -1;
    }
    
    //插入插入PersonalVoiceMsgList
    if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_FAMILY)
    {
        //查找家庭所有用户和室内机的uuid
        std::vector<COMMUNITY_DEVICE_SIP> apps;
        std::vector<PERSONNAL_DEVICE_SIP> per_apps;
        if (dev.is_personal)
        {
            GetPersonalAppAndIndoorDevListByNode(per_voice_msg.uid, per_apps);
            for (const auto& app : per_apps)
            {
                AddPersonalVoiceMsgNode(prefix, app.uuid, per_voice_msg, app.type);
            }
        }
        else
        {
            DaoGetCommunityDevListByNode(per_voice_msg.uid, 0, apps);
            for (const auto& app : apps)
            {
                AddPersonalVoiceMsgNode(prefix, app.uuid, per_voice_msg, app.type);
            }
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_INDOOR)
    {
        //下发给室内机
        ResidentDev per_dev;
        ResidentDev comm_dev;
        if (0 == dbinterface::ResidentDevices::GetSipDev(per_voice_msg.uid, comm_dev))
        {
            AddPersonalVoiceMsgNode(prefix, comm_dev.uuid, per_voice_msg, DEVICE_TYPE_INDOOR);
        }
        else if (0 == dbinterface::ResidentPerDevices::GetSipDev(per_voice_msg.uid, per_dev))
        {
            AddPersonalVoiceMsgNode(prefix, per_dev.uuid, per_voice_msg, DEVICE_TYPE_INDOOR);
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_APP)
    {
        //下发给app
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(per_voice_msg.uid, per_account))
        {
            AddPersonalVoiceMsgNode(prefix, per_account.uuid, per_voice_msg, DEVICE_TYPE_APP);
        }
    }
    return 0;
}

void CMsgControl::AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, const PersonalVoiceMsgInfo &per_voice_msg, int type)
{
    PersonalVoiceMsgNode node;
    memset(&node, 0, sizeof(node));
    std::string tmp_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, tmp_uuid);

    snprintf(node.receiver_uuid, sizeof(node.receiver_uuid), "%s", receiver_uuid.c_str());
    snprintf(node.uuid, sizeof(node.uuid), "%s", tmp_uuid.c_str());
    snprintf(node.msg_uuid, sizeof(node.msg_uuid), "%s", per_voice_msg.uuid);
    node.type = type;

    dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsgList(node);
}


int CMsgControl::OnSendOnlineNotifyMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg)
{
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "OnlineNotifyMsg";
    tag_map[csmain::xmltag::MAC] = online_msg.mac;
    tag_map[csmain::xmltag::UUID] = online_msg.uuid;
    tag_map[csmain::xmltag::VOICE_UNREAD] = online_msg.unread_voice_count;

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_ONLINE_NOTIFY_MSG, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return -1;
    }
    
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send OnlineNotifyMsg to dev failed.";
        return -1;
    }
    return 0;
}

//只做在社区，语音留言上报
int CMsgControl::OnDeviceRequestVoiceMsgList(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    SOCKET_MSG_DEV_VOICE_MSG_LIST msg_list;
    memset(&msg_list, 0, sizeof(msg_list));
    if (GetMsgHandleInstance()->ParseRequestVoiceMsgList(payload, &msg_list) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgList failed.";
        return -1;
    }
    int page_size = msg_list.page_size;
    int page_index = msg_list.page_index;

    PersonalVoiceMsgSendList send_list;
    msg_list.msg_count = dbinterface::PersonalVoiceMsg::GetVoicePageListByIndoorUUID(dev.uuid, page_size, page_index, send_list);
    if (msg_list.msg_count > 0)
    {
        OnSendVoiceMsgListMsg(conn, send_list, msg_list, dev.mac);
    }
    return 0;
}

int CMsgControl::OnSendVoiceMsgListMsg(const evpp::TCPConnPtr& conn, const PersonalVoiceMsgSendList &send_list, 
                            const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg, const std::string &mac)
{
    //组装消息
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildVoiceMsgListNotifyMsg(&socket_message, send_list, voice_msg, mac) != 0)
    {
        AK_LOG_WARN << "BuildVoiceMsgListNotifyMsg failed";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "Send OnlineNotifyMsg to dev failed.";
        return -1;
    }
    return 0;
}

int CMsgControl::BuildVoiceMsgListNotifyMsg(SOCKET_MSG* socket_message, const PersonalVoiceMsgSendList &send_list, 
                        const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg, const std::string &mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildVoiceMsgListNotifyMsg(payload, sizeof(msg_normal->data), send_list, voice_msg) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_REPORT_VOICE_MSG_LIST, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

//只做在社区，语音留言上报
/*
int CMsgControl::OnDeviceRequestVoiceMsgUrl(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));
    if (GetMsgHandleInstance()->ParseRequestVoiceMsgUrl(payload, &url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgUrl failed.";
        return -1;
    }

    PersonalVoiceMsgInfo per_voice_msg;
    int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
    std::string file_url;
    //TODO msg uuid直接获取语音文件会存在越权问题
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByUUID(url_msg.uuid, per_voice_msg))
    {
        file_url = per_voice_msg.file_url;
        std::size_t pos2 =  file_url.find("/group");
        if (pos2 == std::string::npos)
        {
            //存oss的流程

            model::HttpRespuestKV parma_kv;
            parma_kv.insert(map<std::string, std::string>::value_type("Node", "SuperManage"));
            parma_kv.insert(map<std::string, std::string>::value_type("Path", file_url));
            
            char url[1024];
            snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/common/capture/getLink", gstAKCSConf.web_domain);

            AkcsKv kv;
            kv.insert(map<std::string, std::string>::value_type("mac", dev.mac));
            kv.insert(map<std::string, std::string>::value_type("mac_uuid", dev.uuid));
            kv.insert(map<std::string, std::string>::value_type("voice_uuid", url_msg.uuid));
            CHttpReqNotifyMsg notify_msg(url, parma_kv, kv, CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL);
            GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);            
            return 0;
        }
        
        //以下是存fdfs的流程
        size_t pos = file_url.find("/M");
        if (std::string::npos != pos)
        {
            //获取到 /M00/05/CB/rBIp3GMpNwqADMrHAAEqVhPHnOw417.wav
            std::string file_remote = file_url.substr(pos + 1);
            time_t timer = time(nullptr);
            char time_sec[16] = {0};
            snprintf(time_sec, 16, "%ld", timer);
            file_remote += "ak_fdfs";
            file_remote += time_sec;
            std::string token = akuvox_encrypt::MD5(file_remote).toStr();
            if (!ipv6)
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv4, file_url.c_str(), token.c_str(), time_sec);
            }
            else
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv6, file_url.c_str(), token.c_str(), time_sec);
            }
        }
        OnSendVoiceMsgUrl(dev.mac, dev.uuid, url_msg.uuid, url_msg.url);
    }

    
    return 0;
}
*/

int CMsgControl::OnSendVoiceMsgUrl(const std::string &mac,const std::string &mac_uuid, const std::string &uuid, const std::string &url)
{
    evpp::TCPConnPtr conn;
    g_accSer_ptr->GetDevConnByMac(mac, conn);

    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));
    snprintf(url_msg.uuid, sizeof(url_msg.uuid), "%s", uuid.c_str());
    snprintf(url_msg.url, sizeof(url_msg.url), "%s", url.c_str());
    
    //组装消息
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildVoiceMsgUrlNotifyMsg(&socket_message, url_msg, mac) != 0)
    {
        AK_LOG_WARN << "BuildVoiceMsgListNotifyMsg failed";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "Send OnlineNotifyMsg to dev failed.";
        return -1;
    }

    //更新已读未读状态
    if (0 != dbinterface::PersonalVoiceMsg::UpdateVoiceMsgStatus(url_msg.uuid, mac_uuid))
    {
        AK_LOG_WARN << "UpdateVoiceMsgStatus failed";
        return -1;
    }    
    return 0;
}

int CMsgControl::BuildVoiceMsgUrlNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_VOICE_MSG_URL &url_msg, const std::string &mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildVoiceMsgUrlNotifyMsg(payload, sizeof(msg_normal->data), url_msg) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_REPORT_VOICE_MSG_URL, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

int CMsgControl::OnDeviceRequestDelVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));
    if (GetMsgHandleInstance()->ParseRequestDelVoiceMsg(payload, &url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestDelVoiceMsg failed.";
        return -1;
    }

    if (0 != dbinterface::PersonalVoiceMsg::DelVoiceMsgInfoByIndoorUUID(url_msg.uuid, dev.uuid))
    {
        AK_LOG_WARN << "DelVoiceMsgInfoByUUID failed.";
        return -1;
    }

    return 0;
}

//设备上报摄像头媒体信息
int CMsgControl::OnDeviceReportThirdCameraInfo(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    ThirdPartyCamreaInfo camera_info;
    if (GetMsgHandleInstance()->ParseReportThirdCameraInfo(payload, &camera_info) < 0)
    {
        AK_LOG_WARN << "ParseReportThirdCameraInfo failed.";
        return -1;
    }

    //更新三方摄像头媒体信息
    if (dev.is_personal)
    {
        if (0 != dbinterface::PersonalThirdPartyCamrea::UpdatePersonalThirdPartyCameraVideoInfo(camera_info))
        {
            AK_LOG_WARN << "UpdatePersonalThirdPartyCameraVideoInfo failed.";
            return -1;
        }
    }
    else
    {
        if (0 != dbinterface::ThirdPartyCamrea::UpdateThirdPartyCameraVideoInfo(camera_info))
        {
            AK_LOG_WARN << "UpdateThirdPartyCameraVideoInfo failed.";
            
        }
    }
    
    AK_LOG_INFO << "Update ThirdCameraInfo succeed, VideoType: " << camera_info.video_type << " VideoPt: " << camera_info.video_pt << " CameraUUID: " << camera_info.camera_uuid;
    return 0;
}

/*
void CMsgControl::CheckNewCommunityIsNeedkeySend(const SOCKET_MSG_REPORT_STATUS& report_status_msg, const DEVICE_SETTING& device_setting, KEY_SEND& keySend)
{
    if(((strncmp(report_status_msg.tz_md5, gstAKCSConf.tz_md5, strlen(report_status_msg.tz_md5)) == 0) ||
        (strncmp(report_status_msg.tz_data_md5, gstAKCSConf.tz_data_md5, strlen(report_status_msg.tz_data_md5)) == 0)) && 
        (strncmp(report_status_msg.config_md5, device_setting.config_md5, strlen(device_setting.config_md5)) == 0) && 
        (strncmp(report_status_msg.contact_md5, device_setting.contact_md5, strlen(device_setting.contact_md5)) == 0) && 
        (strncmp(report_status_msg.schedule_md5, device_setting.schedule_md5, strlen(device_setting.schedule_md5)) == 0) && 
        (strncmp(report_status_msg.user_meta_md5, device_setting.user_meta_md5, strlen(device_setting.user_meta_md5)) == 0)
      )
    {
        keySend.need_keysend = 0;
    }
    else
    {
        keySend.need_keysend = 1;
    }
}
*/

/*
//kit账户注销
int CMsgControl::OnKitRequestAccountLogout(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    AK_LOG_INFO << "OnKitRequestAccountLogout mac = " << dev.mac;
    
    // 请求web注销账号
    PostAccountLogoutHttpReq(dev);

    // 删除apt内设备日志
    HandleKitDelAptDevLog(dev.project_type, dev.device_node);

    return 0;
}

void CMsgControl::PostAccountLogoutHttpReq(const DEVICE_SETTING& dev)
{
    char data[64];
    char url[1024];
    
    snprintf(data, sizeof(data), "Node=%s", dev.device_node);
    
    if (dev.project_type == project::PERSONAL)
    {
        snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/single/user/clearUserData", gstAKCSConf.web_domain);
    }
    else if (dev.project_type == project::RESIDENCE)
    {
        snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/community/user/resetRoom", gstAKCSConf.web_domain);
    }
        
    Json::Value item;
    Json::FastWriter fast_writer;
    item["url"] = url;
    item["data"] = data;
    std::string data_json = fast_writer.write(item);

    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_KIT, data_json, dev.device_node);
}



void CMsgControl::HandleKitDelAptDevLog(int project_type, const std::string& node)
{
    ResidentDeviceList dev_list;
    if (project_type == project::PERSONAL)
    {
        dbinterface::ResidentPerDevices::GetNodeDevList(node, dev_list);
    }
    else if (project_type == project::RESIDENCE)
    {
        dbinterface::ResidentDevices::GetNodeDevList(node, dev_list);
    }

    // 通知node下的设备删除日志 要分别走route
    for (const auto& dev : dev_list)
    {
        AK::Server::P2PSendRequestDevDelLog msg;
        msg.set_mac(dev.mac);
        
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_C2S_REQUEST_DEV_DEL_LOG);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }
}
*/


int CMsgControl::OnSendRequestDevDelLog(const evpp::TCPConnPtr& conn, const std::string &mac)
{
    //组装消息
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildReportDelLogNotifyMsg(&socket_message, mac) != 0)
    {
        AK_LOG_WARN << "BuildReportDelLogNotifyMsg failed";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "Send OnSendRequestDevDelLog to dev failed.";
        return -1;
    }
    return 0;
}

int CMsgControl::BuildReportDelLogNotifyMsg(SOCKET_MSG* socket_message, const std::string &mac)
{
    if (socket_message == nullptr)
    {
        return -1;
    }
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;

    char* payload = (char*)msg_normal->data;
    if (GetMsgHandleInstance()->BuildReportDelLogNotifyMsg(payload, sizeof(msg_normal->data)) < 0)
    {
        return -1;
    }
    uint32_t data_size = strlen((char*)msg_normal->data);
    AesEncryptByMacV2(payload, payload, mac, &data_size, sizeof(msg_normal->data));
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_KIT_DEL_LOG, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;
}

//6.5.4给设备回ack
int CMsgControl::SendCommonAckMsg(uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(common_ack.mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "GetDevConnByMac failed,MAC=" << common_ack.mac;
        return -1;
    }

    char msg_id_str[9];
    snprintf(msg_id_str, sizeof(msg_id_str), "%x", msg_id);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = common_ack.mac;
    tag_map[csmain::xmltag::RESULT] = to_string(common_ack.result);
    tag_map[csmain::xmltag::TRACE_ID] = common_ack.trace_id;
    tag_map[csmain::xmltag::MSG_ID] = msg_id_str;
    tag_map[csmain::xmltag::TYPE] = "Ack";

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(socket_msg));

    if (CMsgControl::GetInstance()->OnBuildCommonMsg(&socket_msg, MSG_TO_DEVICE_ACK, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed.";
        return -1;
    }

    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, socket_msg.data, socket_msg.size) < 0)
    {
        AK_LOG_WARN << "Send TcpMsg failed.";
        return -1;
    }

    return 0;
}

int CMsgControl::OnDeviceUploadVoiceMsg(const SOCKET_MSG_COMMON_ACK &common_ack, const evpp::TCPConnPtr& conn, int project_type)
{
    //获取留言信息
    PersonalVoiceMsgInfo voice_msg_info;
    PersonalVoiceMsgSendList send_list;
    std::string location;
    std::string msg_uuid;
    memset(&voice_msg_info, 0, sizeof(voice_msg_info));
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByMacAndFileName(common_ack.mac, common_ack.trace_id, voice_msg_info))
    {
        location = voice_msg_info.location;
        msg_uuid = voice_msg_info.uuid;
    }

    //获取发送列表
    dbinterface::PersonalVoiceMsg::GetVoiceMsgListInfoByMsgUUID(msg_uuid, send_list);

    for (const auto & send_node: send_list)
    {
        int type;
        int count;
        int msg_id;
        std::string receiver_uuid;
        if (strlen(send_node.indoor_uuid) > 0)
        {
            receiver_uuid = send_node.indoor_uuid;
            count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(send_node.indoor_uuid);
            type = DEVICE_TYPE_INDOOR;
        }
        else if (strlen(send_node.personal_uuid) > 0)
        {
            receiver_uuid = send_node.personal_uuid;
            msg_id = send_node.id;
            type = DEVICE_TYPE_APP;
        }

        AK::Server::P2PSendVoiceMsg msg;
        msg.set_msg_id(msg_id);
        msg.set_count(count);
        msg.set_location(location);
        msg.set_receiver_uuid(receiver_uuid);
        msg.set_receiver_type(type);
        msg.set_project_type(project_type);
        //6.6一人多套房修改，新增主站点字段
        std::string main_site;
        dbinterface::PersonalAccountUserInfo::GetMainAccountByAccountUUID(receiver_uuid, main_site);
        msg.set_main_site(main_site);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_M2R_P2P_SEND_VOICE_MSG);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }

    //回ack
    if (SendCommonAckMsg(MSG_FROM_DEVICE_REPORT_VOICE_MSG, common_ack) != 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed";
        return -1;
    }

    return 0;
}

//长连接和内部业务的通信。没有加密
int CMsgControl::BuildInnerMsg(SOCKET_MSG_NORMAL &socket_message, int msg_id, char *data, int len)
{
    char* payload = (char*)socket_message.data;;
    memcpy(payload, data, len);
    int data_size = len + SOCKET_MSG_NORMAL_HEADER_SIZE;
    socket_message.magic[0] = SOCKET_MSG_MAGIC_MSB;
    socket_message.magic[1] = SOCKET_MSG_MAGIC_LSB;
    socket_message.message_id = msg_id & SOCKET_MSG_ID_MASK;  //放空版本号
    socket_message.message_id = msg_id | SOCKET_MSG_VERSION_01;  //不加密
    socket_message.head_size = HTONS(SOCKET_MSG_NORMAL_HEADER_SIZE);
    socket_message.data_size = data_size;
    return 0;
}

bool CMsgControl::CheckDevCanOpenDoor(const DEVICE_SETTING& src_dev, const DEVICE_SETTING& target_dev)
{
    //目标设备为项目设备
    if(target_dev.device_type == csmain::COMMUNITY_DEV || target_dev.device_type == csmain::OFFICE_DEV)
    {
        //跨项目无法开门
        if(src_dev.manager_account_id != target_dev.manager_account_id)
        {
            return false;
        }
        return true;
    }
    //目标设备为个人设备
    if(target_dev.device_type == csmain::PERSONNAL_DEV)
    {
        if(0 != strncmp(target_dev.device_node, src_dev.device_node, sizeof(target_dev.device_node)))
        {
            //不为同一单住户下的设备，不允许开门
            return false;
        }
        return true;
    }
    //类型错误 无法开门
    return false;

}

int CMsgControl::OnCheckBookingTmpKey(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn, const DEVICE_SETTING& dev_setting)
{
    //tmpkey解析
    SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY tmpkey_info;
    memset(&tmpkey_info, 0, sizeof(tmpkey_info));
    Snprintf(tmpkey_info.mac, sizeof(tmpkey_info.mac), dev_setting.mac);
    if (0 != ParseCheckTmpKeyRawMsg(pNormalMsg, tmpkey_info))
    {
        AK_LOG_WARN << "ParseCheckTmpKeyMsg failed.";
        return false;
    }

    BookingTmpkeyHandler booking_tmpkey(dev_setting.mac);
    int check_res = booking_tmpkey.CheckBookingTmpKeyValid(tmpkey_info.tmpkey);

    //若tmpkey不能在booking找到，直接返回
    if (check_res == BookingTmpkeyCheckRes::CHECK_RES_TMPKEY_NOT_FOUND)
    {
        return check_res;
    }

    tmpkey_info.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    if (check_res == BookingTmpkeyCheckRes::CHECK_RES_SUCCESS)
    {
        //校验结果
        tmpkey_info.result = dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
        //tmpkey info相关信息获取
        booking_tmpkey.GetBookingTmpkeyInfo(tmpkey_info);
    }

    //回复设备校验结果
    SOCKET_MSG socketMsg;
    memset(&socketMsg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socketMsg;
    int ver = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER));
    if (BuildCheckTmpKeyAckMsg(socket_message, tmpkey_info, ver) < 0)
    {
        AK_LOG_WARN << "BuildCheckTmpKeyAckMsg failed.";
        return check_res;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "Send BuildCheckTmpKeyAckMsg failed.";
        return check_res;
    }

    //校验成功 更新使用次数
    if (tmpkey_info.result == dbinterface::PersonalAppTmpKey::CHECK_SUCCESS)
    {
        //更新booking tmpkey已使用次数
        if (0 != dbinterface::AmenityReservation::UpdateTmpKeyUsedCounts(tmpkey_info.amenity_reservation_uuid))
        {
            AK_LOG_WARN << "update booking tmpkey used count failed. uuid=" << tmpkey_info.amenity_reservation_uuid;
        }
    }

    return check_res;
}

int CMsgControl::ParseCheckTmpKeyRawMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo)
{
    if (pNormalMsg == nullptr)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }
    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    char* payload_out = new char[data_size + 1];
    memset(payload_out, 0, data_size + 1);
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload_out, stTmpKeyInfo.mac, data_size);
    }

    if (GetMsgHandleInstance()->ParseCheckTmpKeyMsg(payload_out, &stTmpKeyInfo) < 0)
    {
        AK_LOG_WARN << "ParseCheckTmpKeyMsg failed.";
        delete []payload_out;
        return -1;
    }
    delete []payload_out;
    return 0;
}

 int CMsgControl::OnReportMotionAlert(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
 {
     if (nullptr == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetLocalDeviceSetting failed.";
        return -1;
    }
    if (deviceSetting.device_type == csmain::COMMUNITY_NONE)
    {
        AK_LOG_WARN << "The device has never report status msg, we can not make sure which type of it.";
        return -1;
    }

    SOCKET_MSG_MOTION_ALERT stMotionAlertMsg;
    memset(&stMotionAlertMsg, 0, sizeof(stMotionAlertMsg));
    if (GetMsgControlInstance()->ParseMotionAlertMsg(normal_msg, stMotionAlertMsg, deviceSetting.mac) < 0)
    {
        AK_LOG_WARN << "ProcessMotionAlertMsg failed.";
        return -1;
    }
    AK_LOG_INFO << deviceSetting.mac << " motion alert. pic:" << stMotionAlertMsg.picture_name << " detection_type:" << stMotionAlertMsg.detection_type;
    if (stMotionAlertMsg.detection_type == 1)
    {
        //包裹检测跳过限流
        return 0;
    }

    //限流判断
    char limiting_key[128];
    snprintf(limiting_key, sizeof(limiting_key), "%s_%lu", gstAKCSConf.csmain_outer_ip, conn->id());
    if(CheckMotionLimiting(limiting_key))
    {
        AddMotionPicLimiting(stMotionAlertMsg.picture_name);
        AK_LOG_INFO << "motion limiting. limiting_key: " << limiting_key << " timeout:" << gstAKCSConf.limiting_timeout;
        return -1;
    }

    return 0;
 }

 bool CMsgControl::CheckMotionLimiting(const std::string& key)
{
    bool ret = false;
    SafeCacheConn cache_conn(g_redis_db_backend_limiting);
    if (!cache_conn.isConnect())
    {
        return false;
    }
    if (!cache_conn.isExists(key))
    {
        std::string set_ret = cache_conn.set(key, "1");
        cache_conn.expire(key, gstAKCSConf.limiting_timeout);
    }
    else
    {
        //存在key则说明在限流中
        ret = true;
    }

    return ret;
}

void CMsgControl::AddMotionPicLimiting(const std::string& key)
{
    SafeCacheConn cache_conn(g_redis_db_backend_limiting);
    if (!cache_conn.isConnect())
    {
        return;
    }
    if (!cache_conn.isExists(key))
    {
        std::string set_ret = cache_conn.set(key, "1");
        cache_conn.expire(key, gstAKCSConf.limiting_timeout);
    }
    return;
}
