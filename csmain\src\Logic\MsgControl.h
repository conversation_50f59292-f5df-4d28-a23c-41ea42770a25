#ifndef __MSG_CONTROL_H__
#define __MSG_CONTROL_H__

#include <vector>
#include "SDMCMsg.h"
#include "json/json.h"
#include "AKUserMng.h"
#include "XmlMsgBuild.h"
#include "InnerUtil.h"
#include "dbinterface/Sip.h"
#include "dbinterface/Shadow.h"
#include "dbinterface/Account.h"
#include "dbinterface/Message.h"
#include "dbinterface/MacPool.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Message.h"
#include "dbinterface/ThirdParty.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/AppCallDndDB.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/ErrorConnectDB.h"
#include "dbinterface/PendingRegUser.h"
#include "dbinterface/OfflineResendLog.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/AbnormalMacStatus.h" 
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/CommunityPendingRegUser.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/PersonalAccountSingleInfo.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/PendingRegUser.h"
#include "dbinterface/resident/AmenityDevice.h"

enum MsgParamControl
{
    NO_NEED_MAC = 0,
    NEED_MAC = 1
};

enum MsgParamEncrypt
{
    MAC_KEY = 0,
    DEFAULT_KEY = 1
};


typedef std::map<std::string/*key*/, std::string/*value*/> XmlKV;
typedef std::map<std::string/*key*/, XmlKV > XmlKeyAttrKv;
typedef std::map<std::string, std::vector<XmlKV>> XmlChildKVList;

//class evpp::Any;
class CMsgControl
{
public:
    enum ActOpenDoorType
    {
        CALL = 0,//call开门截图——社区这里代表app 开门  个人还未改
        TMPKEY = 1,
        LOCALKEY = 2,  //私钥
        RFCARD = 3,
        FACE = 4,
        REMOTE_OPEN_DOOR = 5,//dclient开门 app首页开门
        
        PM_UNLOCK = 9,     //PM一键开门
        AUTO_UNLOCK = 10,  //设备告警自动开门

        INWARD_UNLOCK = 14,  //内开门

        CALL_CAPTURE = 103,//通话截图，在呼叫时候就截图，不管是否接听            
        TEMP_CAPTURE = 104,//测温截图
        PM_LOCK = 105,      //PM一键关门
        
        //以下三个由设备CALL=0 转为平台要求的类型
        CLOUD_CALL_UNLOCK_APP = 6,
        CLOUD_CALL_UNLOCK_INDOOR = 7,
        CLOUD_CALL_UNLOCK_GUARD_PHONE = 8,
        
        //以下三个由设备REMOTE_OPEN_DOOR=5 转为平台要求的类型
        CLOUD_REMOTE_UNLOCK_APP = 5,
        CLOUD_REMOTE_UNLOCK_INDOOR = 7,
        CLOUD_REMOTE_UNLOCK_GUARD_PHONE = 8,        
        CLOUD_NFC = 100,
        CLOUD_BLE = 101,//小于 102 全部是开门截图(web需要根据这个标识过滤所有开门)
        CLOUD_APP_MANUAL = 102,//app手动截图 放在这里是因为截图放的表在一起
    };

public:
    CMsgControl();
    ~CMsgControl();
    int OnReportMotionAlert(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseAckMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_ACK* pAckMsg, uint32_t data_size, uint32_t msg_version, const DEVICE_SETTING& deviceSetting);
    int ParseBootupMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_BOOTUP* pBootupMsg);
    int ParseReportStatusMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_REPORT_STATUS* report_status_message, uint32_t data_size, uint32_t version);
    int ParseReportConfigMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_CONFIG* pReportConfigMsg);
    int ParseAlarmMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_ALARM* alarm_msg, uint32_t nVer);
    int ParseTextMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_TEXT_MESSAGE* text_msg);
    int ParseAccessInfo(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_ACCESS_INFO* pAccessInfo);
    int ParseFlowOutOfLimit(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_FLOW_OUT_LIMIT* socket_msg_flow_out_of_limit);

    int BuildReqStatusMsg(SOCKET_MSG* socket_message, SOCKET_MSG_REQ_STATUS* request_status_msg);
    int BuildRemoteControlMsg(SOCKET_MSG* socket_message, SOCKET_MSG_REMOTE_CONTROL* remote_control_msg, const std::string  mac);
    int BuildReqConfigMsg(SOCKET_MSG* socket_message, SOCKET_MSG_CONFIG* pReqConfigMsg);
    int BuildAckMsg(SOCKET_MSG* socket_message, SOCKET_MSG_ACK* pAckMsg);
    int BuildUpdateConfigMsg(SOCKET_MSG* socket_msg, SOCKET_MSG_CONFIG* update_config_msg);
    int BuildKeySendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_KEY_SEND* pSendKeyMsg, int ver, const std::string mac);
    int BuildUpgradeSendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_UPGRADE_SEND* pUpgradeKeyMsg, std::string mac);
    int BuildAlarmSendMsg(SOCKET_MSG& socket_message, SOCKET_MSG &dy_iv_socket_message, SOCKET_MSG_ALARM_SEND* alarm_msg, int ver);

    /* Begin added by chenyc,2017-05-24,云平台接入app开发 */
    int OnCheckTmpKeyMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnBuildAlarmNotifyMsg(SOCKET_MSG& socket_message, SOCKET_MSG &dy_iv_msg, const SOCKET_MSG_ALARM_SEND& stAlarm);
    int OnPutAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnBuildPersonnalAlarmDealNotifyMsg(SOCKET_MSG& socket_message, SOCKET_MSG &dyiv_socket_message, const SOCKET_MSG_PERSONNAL_ALARM_DEAL& stAlarmDeal);
    int OnBuildAppConfNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_APP_CONF& stAppInfo);
    int BuildReqRtspMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_REQ_RTSP& request_rtsp_msg, std::string& mac);
    int BuildKeepRtspMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_REQ_RTSP& keepalive_rtsp_msg, const std::string& mac);
    int OnPostAppIdentify(SOCKET_MSG* pRecvMsg);
    int processPersonnalAlarmMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_ALARM& alarmMsg, const DEVICE_SETTING& deviceSetting);
    int OnAndroidReportStatusMsg(SOCKET_MSG_NORMAL* normal_msg, int msg_len, const evpp::TCPConnPtr& conn);
    int OnIOSReportStatusMsg(SOCKET_MSG_NORMAL* normal_msg, int msg_len, const evpp::TCPConnPtr& conn);
    int OnReqDevListMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //云平台下发同一联动单元内的设备列表发生变化的通知,
    int OnSendDevListChangeMsg(const evpp::TCPConnPtr& conn);
    int BuildSendDevListChangeMsg(SOCKET_MSG* socket_message, int ver);

    int BuildReqLogOutSipMsg(SOCKET_MSG* socket_message);
    int OnBuildRequestSensorTriggerMsg(SOCKET_MSG& socket_message,  const SOCKET_MSG_SENSOR_TRIGGER& sensor_trigger);
    int OnBuildMotionNotifyMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const std::string& proto, const std::string& mac,
                               int id,  const std::string& sip_account, const std::string& localtion, const std::string& time, const std::string& node);
    int OnBuildMotionToDevNotifyMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const std::string& proto, const std::string& mac,
                                    int id,  const std::string& sip_account, const std::string& localtion, const std::string& time, const std::string& node);
    int OnBuildRespArming(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_DEV_ARMING& arming);
    int OnBuildVisitorAuth(SOCKET_MSG* socket_message, int count, const std::string& mac);
    int OnBuildFaceDataNotifyMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_DEV_REPORT_FACE_DATA& face_data);
    int OnSetRecvMotionStatus(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnHandleDevArming(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnReportArmingStatus(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnReportActLog(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnReportLogOut(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn, const char* remote_ip);
    int BuildReQuitNodeMsg(SOCKET_MSG* socket_message);
    int BuildTextMessageMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dysocket_message, SOCKET_MSG_TEXT_MESSAGE* text_message);
    static CMsgControl* GetInstance();
    //add by chenzhx V4.0
    int processCommunityAlarmMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_ALARM& alarmMsg, const DEVICE_SETTING& deviceSetting);
    int processCommunityAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const DEVICE_SETTING&  deviceSetting, const evpp::Any& personnalAppSetting, const int type);
    int OnBuildCommunityAlarmDealNotifyMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_socket_message, const SOCKET_MSG_ALARM_DEAL& stAlarmDeal);
    int ParseReqCheckDtmfMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_CHECK_DTMF& check_dtmf);
    int OnBuildRespCheckDtmf(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const SOCKET_MSG_CHECK_DTMF& check_dtmf);
    int OnCheckDtmf(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnVideoStorageAct(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseVideoStorageMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_VIDEO_STORAGE& video_storage);
    int OnBuildReqArming(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_ARMING& arming);
    //v4.4
    int OnBuildAlarmNotifyMsg2MngDev(SOCKET_MSG &socket_message, SOCKET_MSG &dy_msg, const SOCKET_MSG_ALARM_SEND& stAlarm);
    int OnDevSendDelivery(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnDevSendDeliveryBox(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnFlowOutOfLimit(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);

    int ChangeOpenDoorTypeRF2NfcBle(char* stCode, int& nOpenType);
public:
    /* Begin added by chenyc,2017-05-24,云平台接入app开发 */
    int BuildCheckTmpKeyAckMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo, int ver);
    int ProcessAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_ALARM_DEAL& alarm_deal_info);
    int ProcessAppReportStatusMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_PERSONNAL_APP_CONF& app_config);
    int ProcessCheckPersonnalTmpKeyMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo);
    //个人社区校验
    int ProcessCheckPersonnalPublicDevTmpKeyMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo);
    int ProcessReqDevListMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_PERSONNAL_DEV_LIST& stDevList, const std::string& mac);
    int BuildReqDevListAckMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_PERSONNAL_DEV_LIST& stDevList, const std::vector<PERSONNAL_DEVICE_SIP>& oDec, std::string& mac);
    int BuildReqDevListAckMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_PERSONNAL_DEV_LIST& stDevList, const std::vector<COMMUNITY_DEVICE_SIP>& oDec, std::string& mac);
    //个人终端用户告警处理消息上报
    int processPersonnalAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const DEVICE_SETTING&  deviceSetting, const evpp::Any& personnalAppSetting, const int type);
    int ParseMotionAlertMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_MOTION_ALERT& stMotionAlert, const std::string& mac);
    int ParseReqArmingMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_DEV_ARMING& stReqArming);
    int ParseSetMotionAlertMsg(SOCKET_MSG_NORMAL* normal_msg, int& type);
    int ParseReportArmingMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_DEV_ARMING& stReqArming, const std::string& mac);
    int ParseReportActMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const std::string& mac);
     int ParseReqCaptureMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_REQ_CAPTURE& request_capture);
    // int ParseMotionAlertMsg(SOCKET_MSG_NORMAL *normal_msg, SOCKET_MSG_MOTION_ALERT &stMotionAlert,CString &mac);

    // 社区公共设备 临时密码校验
    int ProcessCheckCommunityTmpKeyMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo);
    int OnBuildAppLoginResponseMsg(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, SOCKET_MSG_RESP_APPLOGIN* pRespLogin);
    int OnBuildAppForceLogoutMsg(SOCKET_MSG* socket_message);
public:
    int OnHeartBeatMsg(const evpp::TCPConnPtr& conn);
    int OnDeviceReportStatusMsg(SOCKET_MSG_NORMAL* normal_msg, int msg_len, const evpp::TCPConnPtr& conn);
    int OnDeviceReportAccessTimesMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnDeviceAlarmMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //int OnDeviceRequestRegEndUser(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //int OnDeviceReportKitDevices(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //int OnDeviceAddKitDevices(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //int OnDeviceRequestKitDevices(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //void SendKitRequestDevices(const evpp::TCPConnPtr &conn, const DEVICE_SETTING& dev, SOCKET_MSG& socket_msg);
    //void HandlePersonalKitRequestDevices(const DEVICE_SETTING& dev, SOCKET_MSG& socket_msg);
    //void HandleCommunityKitRequestDevices(const DEVICE_SETTING& dev, SOCKET_MSG& socket_msg);
    //int OnDeviceModifyLocation(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
public:
    //csadapt 通信消息
    int OnSendAdaptBuildContactList(const DEVICE_SETTING& deviceSetting);
    int OnSendAdaptReBuildConfigure(const DEVICE_SETTING& deviceSetting);
    int OnSendAdaptReportVisitorMsg(int sql_id);
public:
    //http 维护接口
    int BuildHttpMaintenanceGetFileCommonCmd(SOCKET_MSG* socket_message, const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file, uint16_t nCmd);
    int BuildHttpMaintenanceReconnectCmd(SOCKET_MSG& socket_message,SOCKET_MSG& dy_iv_socket_message, const char* mac, const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection, uint16_t nCmd);
    int BuildHttpMaintenanceNoParamCmd(SOCKET_MSG* socket_message, const char* mac, uint16_t nCmd);

    //命令控制
    int BuildDevCommandMsg(SOCKET_MSG* socket_message, const std::string& stCmd, const char* mac);
    int ParseCommandRespMsg(SOCKET_MSG_NORMAL* normal_msg,
                            SOCKET_MSG_COMMAND_RESP& stRespCommand,
                            const std::string& mac);
    int OnCommandResp(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    // int OnCallCaptureReport(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseCallCaptureMsg(SOCKET_MSG_NORMAL* normal_msg, const std::string& mac, SOCKET_MSG_CALL_CAPTURE& call_capture);

    //v4.4
    int OnMngDevReportMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseMngDevReportMsg(SOCKET_MSG_NORMAL* normal_msg, const std::string& mac, SOCKET_MSG_MNG_DEV_REPORT_MSG& call_capture);

    //访客系统
    int OnDevReportVisitorInfo(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseDevReportVisitorInfo(SOCKET_MSG_NORMAL* normal_msg, const std::string& mac, SOCKET_MSG_DEV_REPORT_VISITOR& stVisitorInfo);
    int OnDevReportVisitorAuth(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseDevReportVisitorAuth(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_DEV_REPORT_VISITOR_AUTH& stVisitorAuth);
    int OnForwardFaceDataMsg(const SOCKET_MSG_DEV_REPORT_FACE_DATA& dev_face_data, const std::string dev_mac_list);
    int BuildVisitorTmpKeyAckMsg(SOCKET_MSG* socket_message, const std::string& mac, int temp_key_code);

    int BuildNotifyDevUpdateServer(SOCKET_MSG* socket_message, const std::string& mac, const std::string& type);
    //V4.6
    int SendHearbeatToDev(const evpp::TCPConnPtr& conn);
    int OnServerHearbeatAck(const evpp::TCPConnPtr& conn);
    void SendRequestSensorTrigger(const evpp::TCPConnPtr& conn, const SOCKET_MSG_SENSOR_TRIGGER& sensor_trigger);
    int OnRespondSensorTrigger(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ParseRespondSensoTirggerMsg(SOCKET_MSG_NORMAL* normal_msg, const std::string& mac, SOCKET_MSG_SENSOR_TIRGGER_MSG& msg);

    int SendSyncArmingMsg(const evpp::TCPConnPtr& conn, const DEVICE_SETTING& deviceSetting);
    /**
     * 通知设备下载人脸文件信息
     *
     * <AUTHOR> (2020/6/15)
     *
     * @param conn
     * @param deviceSetting
     *
     * @return int
     */
    int SendGsFaceLoginMsg(const evpp::TCPConnPtr& conn, const DEVICE_SETTING& deviceSetting);
    int OnBuildRespArmingToDev(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_ARMING& arming, const std::string& mac);
    int OnHandleDevArmingFromDev(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);

    //设备对设备进行布防、撤防的信令
    int ParseReqArmingMsgFromDev(SOCKET_MSG_NORMAL* normal_msg,
                                 SOCKET_MSG_DEV_ARMING& stReqArming, const std::string& mac);
    //5.0
    int OnDevRequestOssSts(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int BuildOssStsMsg(SOCKET_MSG* socket_message, const std::string& mac, const SOCKET_MSG_DEV_OSS_STS& oss_sts);

    //v5.2
    int OnBuildRemoteDeviceContorl(SOCKET_MSG* socket_message, const SOCKET_MSG_REMOTE_DEV_CONTORL& remote);
    // int OnDevRequestOpen(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int GetParseRequestOpenData(SOCKET_MSG_NORMAL* normal_msg, DEVICE_SETTING deviceSetting, SOCKET_MSG_DEV_REQUEST_OPEN& request_open, std::string& open_door_type);
    // int SendDevRequestOpen(SOCKET_MSG_DEV_REQUEST_OPEN request_open, DEVICE_SETTING deviceSetting,const std::string& open_door_type);
    int ParseRequestOpen(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_DEV_REQUEST_OPEN& message, const std::string& mac);
    int BuildOpenDoorAckMsg(SOCKET_MSG* socket_message, const std::string& mac, int result);

    int OnBuildCommonMsg(SOCKET_MSG *socket_msg, uint16_t msg_id, std::map<std::string, std::string> &tag_map, int need_mac = MsgParamControl::NO_NEED_MAC);
    int OnBuildCommonEncDefaultMsg(SOCKET_MSG& socket_msg,SOCKET_MSG& dy_iv_socket_message, uint16_t msg_id,         std::map<std::string, std::string>& tag_map, int need_mac);    
    int OnBuildNewCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, XmlKV& tag_map, XmlKeyAttrKv& attr_map, int need_mac = MsgParamControl::NO_NEED_MAC);
    int OnBuildNewCommonEncDefaultMsg(SOCKET_MSG& socket_msg,SOCKET_MSG& dy_iv_socket_message, uint16_t msg_id, XmlBuilder& xml);    

    int OnDevReqChangeRelay(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);

    int BuildGivenKeySendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_KEY_SEND* pSendKeyMsg,
                                 int ver, const std::string mac);

    int OnRequestUserInfo(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn);  
    int OnOfflineActiveMessage(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn);
    int OnDeviceReportVoiceMsg(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn);
    void AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, const PersonalVoiceMsgInfo &per_voice_msg, int type);
    int OnDeviceRequestVoiceMsgList(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    //int OnDeviceRequestVoiceMsgUrl(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    int OnDeviceRequestDelVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    int OnDeviceReportThirdCameraInfo(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    
public:
    int OnSendDevCodeMsg(const evpp::TCPConnPtr& conn, const std::string& code, const std::string& mac);
    int BuildCleanDeviceCodeMsg(SOCKET_MSG* socket_message, std::string mac);
    
    void PostAwsPushTokenHttpReq(const std::string& uid, const CMobileToken& push_token);    
    void PostAwsTmpKeyHttpReq(int access_times, int id, const std::string& table);
    void PostAwsDealAlarmHttpReq(const std::string& table, const std::string& user, int id);
    void PostAwsDelPushTokenHttpReq(const std::string& uid);    
    void PostAwsInsertMessageHttpReq(PerMsgSendList& text_messages);
    void PostAwsIndoorSche(const std::string& mac, int is_per);
    void PostAlexaChangeStatus(const std::string& mac, uint64_t traceid);

    //added by chenyc,2021.12.22,下面是压测接口,后续新的压测接口放在这里
public:
    void OnTestReqUserInfo(const std::string& mac, const std::string& users_list);
    //end added by chenyc 2021.12.22
    //v7.0 
    int BuildOfficeKeySendMsg(SOCKET_MSG* socket_message, SOCKET_MSG_KEY_SEND* pSendKeyMsg,int ver, const std::string mac);
    int OnOfficeDeviceReportStatusMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REPORT_STATUS& reportStatusMsg, DEVICE_SETTING& deviceSetting);
    int OnSendOnlineNotifyMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg);
    int OnSendVoiceMsgListMsg(const evpp::TCPConnPtr& conn, const PersonalVoiceMsgSendList &send_list, 
                            const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg, const std::string &mac);
    int BuildVoiceMsgListNotifyMsg(SOCKET_MSG* socket_message, const PersonalVoiceMsgSendList &send_list, 
                        const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg, const std::string &mac);
    int OnSendVoiceMsgUrl(const std::string &mac,const std::string &mac_uuid, const std::string &uuid, const std::string &url);
    int BuildVoiceMsgUrlNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_VOICE_MSG_URL& url_msg, const std::string &mac);   
    int ParseReportFileMd5Msg(SOCKET_MSG_NORMAL* msg,SOCKET_MSG_REPORT_FILEMD5& filemd5, const std::string& mac);
    int OnReportFileMD5Message(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn);

    //int OnKitRequestAccountLogout(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    int OnSendRequestDevDelLog(const evpp::TCPConnPtr& conn, const std::string &mac);
    int BuildReportDelLogNotifyMsg(SOCKET_MSG* socket_message, const std::string &mac);
    int GetLinkerMsgFromDevice(const DEVICE_SETTING& device_setting, LINKER_NORMAL_MSG &linker_msg);
    int PushLinKerOpenDoor(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const LINKER_NORMAL_MSG &linker_msg);
    int PushLinKerTmpKey(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const PersonalTempKeyUserInfo &tmpkey_info, const LINKER_NORMAL_MSG &linker_msg);
    int PushLinKerDelivery(SOCKET_MSG_DEV_SEND_DELIVERY* delivery, const LINKER_NORMAL_MSG &linker_msg);
    int PushLinKerText(const PersoanlMessageSend& text_msg, const LINKER_NORMAL_MSG &linker_msg);
    int SendCommonAckMsg(uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack);
    int OnDeviceUploadVoiceMsg(const SOCKET_MSG_COMMON_ACK &common_ack, const evpp::TCPConnPtr& conn, int project_type);

    int EncryptDefalutMacMsg(SOCKET_MSG &socket_message, SOCKET_MSG &dy_iv_socket_message, int msg_id);
    int EncryptDefalutMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id);
    int GetLogProjectUUID(const DEVICE_SETTING& device_setting, std::string& project_uuid);
    int BuildInnerMsg(SOCKET_MSG_NORMAL &socket_message, int msg_id, char *data, int len);
    int BuildNormalMsgHeader(SOCKET_MSG* socket_message, uint16_t message_id, int ver, uint32_t data_size);
    bool CheckDevCanOpenDoor(const DEVICE_SETTING& src_dev, const DEVICE_SETTING& target_dev);
    //motion限流检查
    bool CheckMotionLimiting(const std::string& key);
    //motion 配合csstorage对图片处理进行限流
    void AddMotionPicLimiting(const std::string& key);
    void UpdateSL20Lock(const std::string& lock_uuid, const std::string& mac, const std::string& pic_name);
private:
    int BuildReqDevCodeMsg(SOCKET_MSG* socket_message, const std::string& code, const std::string& mac);
    int SetDeviceSettingByReportStatusMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REPORT_STATUS& report_status_msg, const std::string& ip, const int& port, DEVICE_SETTING& device_setting);
private:
    void DailyAndMonthlyActiveUsers(const std::string& account, int is_office);
    void FoceLogoutHandle(const SOCKET_MSG_PERSONNAL_APP_NODE& appnode, int is_office);
    //bool AddKitDeviceDefaultValue(SOCKET_MSG_DEV_KIT_DEVICE &kit_device);
    //void PostAddKitDeviceHttpReq(const SOCKET_MSG_DEV_KIT_DEVICE &kit_device, const char *node, int command_id);
    //void PostModifyLocationHttpReq(const std::string &mac, const std::string &location, const std::string &node, int project_type);
    //void CheckNewCommunityIsNeedkeySend(const SOCKET_MSG_REPORT_STATUS& report_status_msg, const DEVICE_SETTING& device_setting, KEY_SEND& keySend);
    //void PostAccountLogoutHttpReq(const DEVICE_SETTING& dev);
    int PostThirdPartyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& device_setting);
    int PushLinKerThirdPartyLog(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ThirdPartyLockDeviceInfo& third_dev);
    void FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item);
    int SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key);
    //void GetRegisterUrl(const short oem_id, uint64_t trace_id, RegEndUserInfo& reg_end_user_info);
    void OpenSaltoLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting);
    void OpenDormakabaLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting); 
    void OpenItecLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting);
    void OpenTTLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const DEVICE_SETTING& dev_setting);
    int OnCheckBookingTmpKey(SOCKET_MSG_NORMAL* pNormalMsg, const evpp::TCPConnPtr& conn, const DEVICE_SETTING& dev_setting);
    int ParseCheckTmpKeyRawMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& stTmpKeyInfo);
    AccessGroupRelayValues GetTmpKeyAgRelay(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& st_tmp_key_info, const DEVICE_SETTING& device_setting, int is_new_community);
    AccessGroupRelayValues GetNewCommunityTmpKeyAgRelay(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& st_tmp_key_info, const DEVICE_SETTING& device_setting);
private:
    // kit注册
    // void GetRegisterUrl(const short oem_id, RegEndUserInfo& reg_end_user_info);
    //void SendRegEndUserUrl(const ResidentDev& dev, RegEndUserInfo& reg_user_info);
    //void HandlePersonalKitRegister(const ResidentDev& dev);
    //void HandleCommunityKitRegister(const ResidentDev& dev);
    // kit注销
    //void HandleKitDelAptDevLog(int project_type, const std::string& node);

    void AfterReportStatusBindConnSendMsg(const evpp::TCPConnPtr& conn, const std::string& mac, const DEVICE_SETTING& dev_setting);
private:
    static CMsgControl* instance; 
};

CMsgControl* GetMsgControlInstance();

#endif

