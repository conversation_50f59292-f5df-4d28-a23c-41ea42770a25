#include "stdafx.h"
#include "MsgControl.h"
#include "DeviceControl.h"
#include "csmainserver.h"
#include "NotifyMsgControl.h"
#include "CachePool.h"
#include "Dao.h"
#include "util_judge.h"
#include "DeviceSetting.h"
#include "VideoSchedMng.h"
#include "session_rpc_client.h"
#include "doorlog/UserInfo.h"
#include "MsgControl.h"
#include "AkcsMonitor.h"
#include "AppCallStatus.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "PushClientMng.h"
#include "dbinterface/Message.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/DevRtspLog.h"
#include "dbinterface/Token.h"

extern const char* g_redis_db_appconf;
extern AKCS_CONF gstAKCSConf;
extern AccessServer* g_accSer_ptr;
extern SmRpcClient* g_sm_client_ptr;
extern LOG_DELIVERY gstAKCSLogDelivery;

CNotifyMsgControl::CNotifyMsgControl(): m_MsgCount(0)
{

}

CNotifyMsgControl::~CNotifyMsgControl()
{
    //curl_easy_cleanup(m_pCurl);
    m_NotifyMsgList.clear();
}

CNotifyMsgControl* CNotifyMsgControl::instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::http_req_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::motion_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::rtsp_instance = NULL;


CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CNotifyMsgControl();
    }

    return instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetHttpReqInstance()
{
    if (http_req_instance == NULL)
    {
        http_req_instance = new CNotifyMsgControl();
    }

    return http_req_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetMotionInstance()
{
    if (motion_instance == NULL)
    {
        motion_instance = new CNotifyMsgControl();
    }

    return motion_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetRtspInstance()
{
    if (rtsp_instance == NULL)
    {
        rtsp_instance = new CNotifyMsgControl();
    }

    return rtsp_instance;
}


CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}

CNotifyMsgControl* GetHttpReqMsgControlInstance()
{
    return CNotifyMsgControl::GetHttpReqInstance();
}

CNotifyMsgControl* GetMotionMsgControlInstance()
{
    return CNotifyMsgControl::GetMotionInstance();
}

CNotifyMsgControl* GetRtspMsgControlInstance()
{
    return CNotifyMsgControl::GetRtspInstance();
}


//在主线程初始化,注意m_pCurl不是线程安全的.
int CNotifyMsgControl::Init()
{
    m_t = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success,thread_id=" << m_t.get_id();
    return 0;
}

int CNotifyMsgControl::GetNotifyMsgListSize()
{
    std::unique_lock<std::mutex> lock(m_mtx);
    return m_NotifyMsgList.size();
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    int is_next_wait = 0;
    while (1)
    {
        NotifyMsgPrt TmpPtr;
        {
            std::unique_lock<std::mutex> lock(m_mtx);
            while (m_NotifyMsgList.size() == 0)
            {
                m_cv.wait(lock);
                is_next_wait = 1;
            }
            //检测队列长度
            //modified by czw,2021-09-09,当社区群发message时队列会达到大几百,故调整为1000告警
            if (is_next_wait == 1 && m_NotifyMsgList.size() > 1000) //added by chenyc,2019-08-13,500只是估算值,具体的数值需要测试:消费能力TPS*允许的延迟时间来决定.
            {
                is_next_wait = 0;//只告警一次
                std::string msg = "notify msg queue overflow, the length now is:";
                msg += std::to_string(m_NotifyMsgList.size());
                std::string worker_node = "csmain_";
                worker_node += gstAKCSConf.csmain_outer_ip;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, msg, AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSMAIN_NOTIFY);
                AK_LOG_WARN << msg;
            }
            TmpPtr = m_NotifyMsgList.back();
            m_NotifyMsgList.pop_back();
        }
        TmpPtr->NotifyMsg();
    }
    return 0;
}
int CNotifyMsgControl::AddAlarmNotifyMsg(const CAlarmNotifyMsg& pCMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CAlarmNotifyMsg(pCMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddPersonnalAlarmNotifyMsg(const CPersonnalAlarmNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CPersonnalAlarmNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddPerMotionNotifyMsg(const CPersonnalMotionNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CPersonnalMotionNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddPersonnalAlarmDealNotifyMsg(const CPersonnalAlarmDealMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CPersonnalAlarmDealMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddAlarmDealNotifyMsg(const CAlarmDealNotifyMsg& pCMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CAlarmDealNotifyMsg(pCMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddRtspActionNotifyMsg(const CRtspActionNotifyMsg& CRtspMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CRtspActionNotifyMsg(CRtspMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddRtspKeepNotifyMsg(const CRtspKeepNotifyMsg& CRtspMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CRtspKeepNotifyMsg(CRtspMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddPersonnalNodeChangeNotifyMsg(const CPersonnalNodeChangeNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CPersonnalNodeChangeNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddCommunityNodeChangeNotifyMsg(const CCommunityNodeChangeNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CCommunityNodeChangeNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddTextNotifyMsg(const CPerTextNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CPerTextNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddFaceDataNotifyMsg(const CFaceDataNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CFaceDataNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddHttpReqNotiyMsg(const CHttpReqNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CHttpReqNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddAwsAlarmNotiyMsg(const CAwsAlarmNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CAwsAlarmNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

void CAlarmNotifyMsg::PushOfflineCommonMsg(AppOfflinePushKV& kv, CMobileToken mobile_token)
{
    kv.insert(map<std::string, std::string>::value_type("language", mobile_token.Language()));
    kv.insert(map<std::string, std::string>::value_type("mac_sip", mac_));
    kv.insert(map<std::string, std::string>::value_type("device_name", dev_location_));
    kv.insert(map<std::string, std::string>::value_type("alarm_msg", alarm_msg_));
    kv.insert(map<std::string, std::string>::value_type("alarm_id", std::to_string(m_stAlarmMsg.id)));
    kv.insert(map<std::string, std::string>::value_type("alarm_code", std::to_string(m_stAlarmMsg.alarm_code)));
    kv.insert(map<std::string, std::string>::value_type("alarm_zone", std::to_string(m_stAlarmMsg.alarm_zone)));
    kv.insert(map<std::string, std::string>::value_type("alarm_location", std::to_string(m_stAlarmMsg.alarm_location)));
    kv.insert(map<std::string, std::string>::value_type("alarm_customize", std::to_string(m_stAlarmMsg.alarm_customize)));
    kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(mobile_token.CommonVersion())));
    kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(mobile_token.AppOem())));
    kv.insert(map<std::string, std::string>::value_type("traceid", std::to_string(m_stAlarmMsg.trace_id)));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
        {
            push_cli_ptr->buildPushMsg(mobile_token.MobileType(), mobile_token.Token(), csmain::PUSH_MSG_TYPE_ALARM, kv, mobile_token.OemName());
            AK_LOG_INFO << "offline app msg must be sent by offline_msg, token is " << mobile_token.Token().c_str();

        }
        else if (mobile_token.MobileType() != csmain::AppType::APP_IOS)  //fcm 推送
        {
            push_cli_ptr->buildPushMsg(mobile_token.MobileType(), mobile_token.FcmToken(), csmain::PUSH_MSG_TYPE_ALARM, kv, mobile_token.OemName());
            AK_LOG_INFO << "offline android fcm app msg must be sent by offline_msg, fcm token is " << mobile_token.FcmToken().c_str();
        }
    }
}
//社区告警通知pm
void CAlarmNotifyMsg::NotifyPmMsg()
{
    //在线推送
    //获取pm_app和主站点列表
    ResidentPerAccountList pm_app_list;
    //pm所有站点列表<主站点site, 该社区对应实际pm站点的site>
    std::map<std::string, std::string> pm_all_sites;
    dbinterface::ResidentPersonalAccount::GetCommPmApplistByMngID(m_stAlarmMsg.manager_account_id, pm_app_list);

    if (pm_app_list.size() == 0)
    {
        AK_LOG_INFO << "pm app list is empty.";
        return;
    }

    dbinterface::PersonalAccountUserInfo::GetPmAllSitesByAppList(pm_app_list, pm_all_sites);
    OfflinePushUserInfo offline_user;
    OfflinePush::GetPmAlarmPushInfoByNode(m_stAlarmMsg.address, offline_user);
    AK_LOG_INFO << "GetPmAlarmPushInfoByNode, pm_online_title is " << offline_user.pm_online_title;

    auto it = pm_all_sites.begin();
    for (; it != pm_all_sites.end(); ++it)
    {
        std::string main_site = it->first;
        std::string pm_site = it->second;
        //校验实际站点账号是否异常
        if (dbinterface::ProjectUserManage::MultiSiteLimit(pm_site)) 
        {
            continue;
        }
        CMobileToken pm_mobile;
        CAkUserManager::GetInstance()->GetAkOfflineUserTokenByUid(main_site, pm_mobile);

        //pm app推送需要传alarm对应社区pm的site，需要重新构造消息
        SOCKET_MSG pm_socket_msg, dy_iv_pm_socket_msg;
        SOCKET_MSG_ALARM_SEND pm_alarm_msg;
        pm_alarm_msg = m_stAlarmMsg;
        memset(&pm_socket_msg, 0, sizeof(pm_socket_msg));
        memset(&dy_iv_pm_socket_msg, 0, sizeof(dy_iv_pm_socket_msg));
        Snprintf(pm_alarm_msg.address, sizeof(pm_alarm_msg.address), pm_site.c_str());
        if (pm_mobile.IsMultiSite())
        {
            Snprintf(pm_alarm_msg.title, sizeof(pm_alarm_msg.title), offline_user.pm_online_title);
        }
        GetMsgControlInstance()->OnBuildAlarmNotifyMsg(pm_socket_msg, dy_iv_pm_socket_msg, pm_alarm_msg);

        evpp::TCPConnPtr pm_conn;
        g_accSer_ptr->GetDevConnByMainUid(main_site, pm_conn);
        if (g_accSer_ptr->IsTCPConnIsAPP(pm_conn))
        {
            AK_LOG_INFO << "Notify pm AlarmMsg, ip:port is " << pm_conn->remote_addr().c_str();
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(pm_conn, pm_socket_msg, dy_iv_pm_socket_msg);
        }

        
        if (!pm_mobile.MobileOnline())
        {
            int alarm_reminder_status = 0;
            bool is_pm = true;
            dbinterface::ProjectUserManage::GetCurrentLoginSiteAlarmReminderStatusByMainSite(main_site, alarm_reminder_status, is_pm);
            AppOfflinePushKV kv;
            kv.insert(map<std::string, std::string>::value_type("enable_strong_reminder", std::to_string(alarm_reminder_status)));
            //6.6多套房场景下，标题增加前缀
            if (pm_mobile.IsMultiSite())
            {
               kv.insert(map<std::string, std::string>::value_type("title_prefix", offline_user.pm_offline_title));
               kv.insert(map<std::string, std::string>::value_type("site", pm_site));
            }
            PushOfflineCommonMsg(kv, pm_mobile);
        }
    }

}

//社区用户告警通知
int CAlarmNotifyMsg::NotifyMsg()
{
    int ret = -1;
    std::vector<evpp::TCPConnPtr> oDevConn;
    SOCKET_MSG stSocketMsg, dy_iv_socket_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    memset(&dy_iv_socket_msg, 0, sizeof(dy_iv_socket_msg));
    ret = GetMsgControlInstance()->OnBuildAlarmNotifyMsg(stSocketMsg, dy_iv_socket_msg, m_stAlarmMsg);
    if (ret != 0)
    {
        AK_LOG_WARN << "BuildAlarmNotifyMsg failed";
        return -1;
    }
    //add by chenzhx 如果是社区\单元公共设备，目前不处理告警
    if (m_stAlarmMsg.grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        AK_LOG_WARN << "NotifyCommunityAlarmMsg, but alarm is from public device,ignore!!!";
        return -1;
    }

    AK_LOG_INFO << "NotifyCommunityAlarmMsg, node is " << m_stAlarmMsg.address;

    //推送到pm app
    NotifyPmMsg();
    
    g_accSer_ptr->GetDevListByNode(m_stAlarmMsg.address, oDevConn);
    //遍历同一节点的所有设备-App,发送通知消息
    for (const auto& DevConn : oDevConn)
    {
        if (g_accSer_ptr->IsTCPConnIsAPP(DevConn))
        {
            DevicePtr app;
            if(g_accSer_ptr->GetClientFromConn(DevConn, app) == 0)
            {
                //根据node获取真实站点
                std::string real_site;
                app->GetPerUidByNode(real_site, m_stAlarmMsg.address);
                if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
                {
                    continue;
                }
            }
            AK_LOG_INFO << "Notify personnal AlarmMsg, ip:port is " << DevConn->remote_addr().c_str();
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_socket_msg);
            continue;
        }

        DEVICE_SETTING device_setting;
        memset(&device_setting, 0, sizeof(device_setting));
        if (g_accSer_ptr->GetDeviceSettingFromConnList(DevConn, &device_setting) == 0)
        {
            if (device_setting.type != DEVICE_TYPE_DOOR && device_setting.type != DEVICE_TYPE_STAIR && device_setting.type != DEVICE_TYPE_ACCESS)
            {
                AK_LOG_INFO << "Notify personnal AlarmMsg, ip:port is " << DevConn->remote_addr().c_str();
                GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_socket_msg);
            }
        }
    }

    //管理中心机
    //查找是否存在管理中心机
    std::vector<DEVICE_SETTING> mng_devs;
    if (dbinterface::ResidentDevices::GetCommunityMngDevByMngID(m_stAlarmMsg.manager_account_id, mng_devs) == 0)
    {
        CNodeInfo cNodeCfg(m_stAlarmMsg.address);
        Snprintf(m_stAlarmMsg.APT, sizeof(m_stAlarmMsg.APT), cNodeCfg.getRoomNumber().c_str());
        ret = GetMsgControlInstance()->OnBuildAlarmNotifyMsg2MngDev(stSocketMsg, dy_iv_socket_msg, m_stAlarmMsg);
        if (ret != 0)
        {
            AK_LOG_WARN << "OnBuildAlarmNotifyMsg2MngDev failed";
            //继续处理app推送
        }

        DEVICE_SETTING deviceSetting;
        if (GetDeviceControlInstance()->GetDeviceSettingByMac(mac_, &deviceSetting) >= 0)
        {
            for (auto& dev : mng_devs)
            {
                //发给有相关的管理中心机，同一栋的，或者最外层的
                if (!(dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
                        || (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && dev.unit_id == deviceSetting.unit_id)))
                {
                    continue;
                }
                if (dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || GetDeviceSettingInstance()->IsManageBuilding(dev.mac, m_stAlarmMsg.unit_id))
                {
                    evpp::TCPConnPtr conn;
                    if (g_accSer_ptr->GetDevConnByMac(dev.mac, conn) == 0)
                    {
                        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_socket_msg);
                    }
                }
            }
        }

    }

    //端外推送业务
    if (dev_location_.empty())
    {
        dev_location_ = mac_;
    }

    // std::string uid;//对应离线用户account
    std::vector<CMobileToken> oVec;
    CAkUserManager::GetInstance()->GetAkOfflineUsersByNode(m_stAlarmMsg.address, oVec); //获取下线的app列表
    
    for (const auto& mobileToken : oVec)
    {
        std::string real_site;
        CAkUserManager::GetInstance()->GetRealSiteByNodeAndMainSite(m_stAlarmMsg.address, mobileToken.Uid(), real_site);
        //校验实际站点账号是否为多套房账户且状态异常
        if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
        {
            continue;
        }
        int alarm_reminder_status = 0;
        dbinterface::ProjectUserManage::GetCurrentLoginSiteAlarmReminderStatusByMainSite(mobileToken.Uid(), alarm_reminder_status);
        AppOfflinePushKV kv;
        kv.insert(map<std::string, std::string>::value_type("enable_strong_reminder", to_string(alarm_reminder_status)));
        //6.6多套房场景下，标题增加前缀
        std::string title;
        if (mobileToken.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(m_stAlarmMsg.address, title) == 0 )
        {
           kv.insert(map<std::string, std::string>::value_type("title_prefix", title));
           kv.insert(map<std::string, std::string>::value_type("site", real_site));
        }
        
        PushOfflineCommonMsg(kv, mobileToken);
    }
    return 0;
}

//个人终端用户告警通知,需端外推送
int CPersonnalAlarmNotifyMsg::NotifyMsg()
{
    int ret = -1;
    std::vector<evpp::TCPConnPtr> oDevConn;
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));   
    ret = GetMsgControlInstance()->OnBuildAlarmNotifyMsg(stSocketMsg, dy_iv_msg, m_stAlarmMsg);
    if (ret != 0)
    {
        AK_LOG_WARN << "BuildAlarmNotifyMsg failed";
        return -1;
    }

    AK_LOG_INFO << "NotifyPersonnalAlarmMsg, node is " << m_stAlarmMsg.address << " mng:" << m_stAlarmMsg.community;
    g_accSer_ptr->GetDevListByNode(m_stAlarmMsg.address, oDevConn);

    //遍历同一节点的所有设备-App,发送通知消息
    for (const auto& DevConn : oDevConn)
    {
        if (g_accSer_ptr->IsTCPConnIsAPP(DevConn))
        {
            DevicePtr app;            
            if(g_accSer_ptr->GetClientFromConn(DevConn, app) == 0)
            {
                //根据node获取真实站点
                std::string real_site;
                app->GetPerUidByNode(real_site, m_stAlarmMsg.address);
                if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
                {
                    continue;
                }
            }
            AK_LOG_INFO << "Notify personnal AlarmMsg, ip:port is " << DevConn->remote_addr().c_str();
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_msg);
            continue;
        }
        
        DEVICE_SETTING device_setting;
        memset(&device_setting, 0, sizeof(device_setting));
        if (g_accSer_ptr->GetDeviceSettingFromConnList(DevConn, &device_setting) == 0)
        {
            if (device_setting.type != DEVICE_TYPE_DOOR && device_setting.type != DEVICE_TYPE_STAIR && device_setting.type != DEVICE_TYPE_ACCESS)
            {
                AK_LOG_INFO << "Notify personnal AlarmMsg, ip:port is " << DevConn->remote_addr().c_str();
                GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_msg);
            }
        }
    }

    //端外推送业务
    if (dev_location_.empty())
    {
        dev_location_ = mac_;
    }
    std::vector<CMobileToken> oVec;
    CAkUserManager::GetInstance()->GetAkOfflineUsersByNode(m_stAlarmMsg.address, oVec); //获取下线的app列表
    for (const auto& mobileToken : oVec)
    {
        std::string real_site;
        CAkUserManager::GetInstance()->GetRealSiteByNodeAndMainSite(m_stAlarmMsg.address, mobileToken.Uid(), real_site);
        //校验实际站点账号是否为多套房账户且状态异常
        if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
        {
            continue;
        }
        int alarm_reminder_status = 0;
        dbinterface::ProjectUserManage::GetCurrentLoginSiteAlarmReminderStatusByMainSite(mobileToken.Uid(), alarm_reminder_status);
        AppOfflinePushKV kv;
        kv.insert(map<std::string, std::string>::value_type("enable_strong_reminder", to_string(alarm_reminder_status)));
        std::string title;
        if (mobileToken.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(m_stAlarmMsg.address, title) == 0 )
        {
            //6.6多套房场景下，标题增加前缀
           kv.insert(map<std::string, std::string>::value_type("title_prefix", title));
           kv.insert(map<std::string, std::string>::value_type("site", m_stAlarmMsg.address));
        }

        kv.insert(map<std::string, std::string>::value_type("language", mobileToken.Language()));
        kv.insert(map<std::string, std::string>::value_type("mac_sip", mac_));
        kv.insert(map<std::string, std::string>::value_type("device_name", dev_location_));
        kv.insert(map<std::string, std::string>::value_type("alarm_msg", alarm_msg_));
        kv.insert(map<std::string, std::string>::value_type("alarm_code", std::to_string(m_stAlarmMsg.alarm_code)));
        kv.insert(map<std::string, std::string>::value_type("alarm_zone", std::to_string(m_stAlarmMsg.alarm_zone)));
        kv.insert(map<std::string, std::string>::value_type("alarm_location", std::to_string(m_stAlarmMsg.alarm_location)));
        kv.insert(map<std::string, std::string>::value_type("alarm_customize", std::to_string(m_stAlarmMsg.alarm_customize)));
        kv.insert(map<std::string, std::string>::value_type("alarm_id", std::to_string(m_stAlarmMsg.id)));
        kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(mobileToken.CommonVersion())));
        kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(mobileToken.AppOem())));
        kv.insert(map<std::string, std::string>::value_type("traceid", std::to_string(m_stAlarmMsg.trace_id)));
        PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
        if (push_cli_ptr)
        {
            if (mobileToken.MobileType() == csmain::AppType::APP_IOS)
            {
                push_cli_ptr->buildPushMsg(mobileToken.MobileType(), mobileToken.Token(), csmain::PUSH_MSG_TYPE_ALARM, kv, mobileToken.OemName());
                AK_LOG_INFO << "offline app msg must be sent by offline_msg, token is " << mobileToken.Token().c_str();

            }
            else if (mobileToken.MobileType() != csmain::AppType::APP_IOS)  //fcm 推送
            {
                push_cli_ptr->buildPushMsg(mobileToken.MobileType(), mobileToken.FcmToken(), csmain::PUSH_MSG_TYPE_ALARM, kv, mobileToken.OemName());
                AK_LOG_INFO << "offline android fcm app msg must be sent by offline_msg, fcm token is " <<  mobileToken.FcmToken().c_str();
            }
        }
    }
    return 0;
}

//个人终端用户告警解除,需端外推送
int CPersonnalAlarmDealMsg::NotifyMsg()
{
    int ret = -1;
    std::vector<evpp::TCPConnPtr> oDevConn;
    SOCKET_MSG socketmsg, dy_iv_socketmsg;
    memset(&socketmsg, 0, sizeof(socketmsg));
    memset(&dy_iv_socketmsg, 0, sizeof(dy_iv_socketmsg));
    ret = GetMsgControlInstance()->OnBuildPersonnalAlarmDealNotifyMsg(socketmsg, dy_iv_socketmsg, m_stAlarmDealMsg);  //不管怎样默认都加密了
    if (ret != 0)
    {
        AK_LOG_WARN << "Build personnal AlarmDealNotifyMsg failed";
        return -1;
    }

    AK_LOG_INFO << "[DealAlarmMsg] personnal NotifyAlarmMsg, Node is " <<  m_stAlarmDealMsg.area_node;
    g_accSer_ptr->GetDevListByNode(m_stAlarmDealMsg.area_node, oDevConn);

    //遍历同一节点的所有设备-App,发送通知消息
    for (const auto& DevConn : oDevConn)
    {
        DevicePtr app;
        if(g_accSer_ptr->GetClientFromConn(DevConn, app) == 0 && g_accSer_ptr->IsTCPConnIsAPP(DevConn))
        {
            //根据node获取真实站点
            std::string real_site;
            app->GetPerUidByNode(real_site, m_stAlarmDealMsg.area_node);
            if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
            {
                continue;
            }
        }
        AK_LOG_INFO << "[DealAlarm] Notify personnal ip:port is " << DevConn->remote_addr().c_str();
        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, socketmsg, dy_iv_socketmsg);
    }

    //端外推送业务
    if (dev_location_.empty())
    {
        dev_location_ = mac_;
    }

    std::vector<CMobileToken> oVec;
    CAkUserManager::GetInstance()->GetAkOfflineUsersByNode(m_stAlarmDealMsg.area_node, oVec); //获取下线的app列表
    for (const auto& mobileToken : oVec)
    {    
        std::string real_site;
        CAkUserManager::GetInstance()->GetRealSiteByNodeAndMainSite(m_stAlarmDealMsg.area_node, mobileToken.Uid(), real_site);
        //校验实际站点账号是否为多套房账户且状态异常
        if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
        {
            continue;
        }
        AppOfflinePushKV kv;
        //6.6多套房场景下，标题增加前缀
        std::string title;
        if (mobileToken.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(m_stAlarmDealMsg.area_node, title) == 0 )
        {
           kv.insert(map<std::string, std::string>::value_type("title_prefix", title));
           kv.insert(map<std::string, std::string>::value_type("site", m_stAlarmDealMsg.area_node));
        }        
        kv.insert(map<std::string, std::string>::value_type("language", mobileToken.Language()));
        kv.insert(map<std::string, std::string>::value_type("mac_sip", mac_));
        kv.insert(map<std::string, std::string>::value_type("device_name", dev_location_));
        kv.insert(map<std::string, std::string>::value_type("alarm_msg", alarm_msg_));
        kv.insert(map<std::string, std::string>::value_type("alarm_code", std::to_string(m_stAlarmDealMsg.alarm_code)));
        kv.insert(map<std::string, std::string>::value_type("alarm_zone", std::to_string(m_stAlarmDealMsg.alarm_zone)));
        kv.insert(map<std::string, std::string>::value_type("alarm_location", std::to_string(m_stAlarmDealMsg.alarm_location)));
        kv.insert(map<std::string, std::string>::value_type("alarm_customize", std::to_string(m_stAlarmDealMsg.alarm_customize)));
        kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(mobileToken.CommonVersion())));
        kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(mobileToken.AppOem())));
        kv.insert(map<std::string, std::string>::value_type("traceid", std::to_string(m_stAlarmDealMsg.trace_id)));
        PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
        if (push_cli_ptr)
        {
            if (mobileToken.MobileType() == csmain::AppType::APP_IOS)
            {
                push_cli_ptr->buildPushMsg(mobileToken.MobileType(), mobileToken.Token(), csmain::PUSH_MSG_TYPE_DEALALARM, kv, mobileToken.OemName());
            }
            else if (mobileToken.MobileType() != csmain::AppType::APP_IOS)  //fcm 推送
            {
                push_cli_ptr->buildPushMsg(mobileToken.MobileType(), mobileToken.FcmToken(), csmain::PUSH_MSG_TYPE_DEALALARM, kv, mobileToken.OemName());
            }
        }
    }
    return 0;
}

void CAlarmDealNotifyMsg::PushOfflineCommonMsg(AppOfflinePushKV& kv, CMobileToken mobile_token)
{
    kv.insert(map<std::string, std::string>::value_type("language", mobile_token.Language()));
    kv.insert(map<std::string, std::string>::value_type("mac_sip", mac_));
    kv.insert(map<std::string, std::string>::value_type("device_name", dev_location_));
    kv.insert(map<std::string, std::string>::value_type("alarm_msg", alarm_msg_));
    kv.insert(map<std::string, std::string>::value_type("alarm_code", std::to_string(m_stAlarmDealMsg.alarm_code)));
    kv.insert(map<std::string, std::string>::value_type("alarm_zone", std::to_string(m_stAlarmDealMsg.alarm_zone)));
    kv.insert(map<std::string, std::string>::value_type("alarm_location", std::to_string(m_stAlarmDealMsg.alarm_location)));
    kv.insert(map<std::string, std::string>::value_type("alarm_customize", std::to_string(m_stAlarmDealMsg.alarm_customize)));
    kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(mobile_token.CommonVersion())));
    kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(mobile_token.AppOem())));
    kv.insert(map<std::string, std::string>::value_type("traceid", std::to_string(m_stAlarmDealMsg.trace_id)));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
        {
            push_cli_ptr->buildPushMsg(mobile_token.MobileType(), mobile_token.Token(), csmain::PUSH_MSG_TYPE_DEALALARM, kv, mobile_token.OemName());
        }
        else if (mobile_token.MobileType() != csmain::AppType::APP_IOS)  //fcm 推送
        {
            push_cli_ptr->buildPushMsg(mobile_token.MobileType(), mobile_token.FcmToken(), csmain::PUSH_MSG_TYPE_DEALALARM, kv, mobile_token.OemName());
        }
    }
}

//社区告警解决通知pm
void CAlarmDealNotifyMsg::NotifyPmMsg()
{
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(mac_, dev))
    {
        return;
    }

    if (akjudge::IsCommunityPublicDev(dev.grade) && akjudge::DevIndoorType(dev.dev_type))
    {
        AK_LOG_INFO << "NotifyPmMsg, dev is community public indoor, mac: " << mac_ << ", dev_type: " << dev.dev_type;
        return;
    }

    //在线推送
    //获取pm_app和主站点列表
    ResidentPerAccountList pm_app_list;
    //pm所有站点列表<主站点site, 该社区对应实际pm站点的site>
    std::map<std::string, std::string> pm_all_sites;
    dbinterface::ResidentPersonalAccount::GetCommPmApplistByMngID(m_stAlarmDealMsg.manager_account_id, pm_app_list);
    if (pm_app_list.size() == 0)
    {
        AK_LOG_INFO << "pm app list is empty.";
        return;
    }
    dbinterface::PersonalAccountUserInfo::GetPmAllSitesByAppList(pm_app_list, pm_all_sites);
    OfflinePushUserInfo offline_user;
    OfflinePush::GetPmAlarmPushInfoByNode(m_stAlarmDealMsg.area_node, offline_user);

    auto it = pm_all_sites.begin();
    for (; it != pm_all_sites.end(); ++it)
    {
        std::string main_site = it->first;
        std::string pm_site = it->second;
        //校验实际站点账号是否异常
        if (dbinterface::ProjectUserManage::MultiSiteLimit(pm_site)) 
        {
            continue;
        }
        CMobileToken pm_mobile;
        CAkUserManager::GetInstance()->GetAkOfflineUserTokenByUid(main_site, pm_mobile);
        
        //pm app在线推送需要传alarm对应社区pm的site，需要重新构造消息
        SOCKET_MSG pm_socket_msg, dy_iv_pm_socket_msg;
        SOCKET_MSG_ALARM_DEAL pm_alarm_deal_msg;
        pm_alarm_deal_msg = m_stAlarmDealMsg;
        memset(&pm_socket_msg, 0, sizeof(pm_socket_msg));
        memset(&dy_iv_pm_socket_msg, 0, sizeof(dy_iv_pm_socket_msg));
        Snprintf(pm_alarm_deal_msg.area_node, sizeof(pm_alarm_deal_msg.area_node), pm_site.c_str());
        if (pm_mobile.IsMultiSite())
        {
            Snprintf(pm_alarm_deal_msg.title, sizeof(pm_alarm_deal_msg.title), offline_user.pm_online_title);
        }
        GetMsgControlInstance()->OnBuildCommunityAlarmDealNotifyMsg(pm_socket_msg, dy_iv_pm_socket_msg, pm_alarm_deal_msg);

        evpp::TCPConnPtr pm_conn;
        g_accSer_ptr->GetDevConnByMainUid(main_site, pm_conn);
        if (g_accSer_ptr->IsTCPConnIsAPP(pm_conn))
        {
            AK_LOG_INFO << "Deal Notify pm AlarmMsg, ip:port is " << pm_conn->remote_addr().c_str();
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(pm_conn, pm_socket_msg, dy_iv_pm_socket_msg);
        }

        //离线推送
        if (!pm_mobile.MobileOnline())
        {
            AppOfflinePushKV kv;
            //6.6多套房场景下，标题增加前缀
            
            if (pm_mobile.IsMultiSite())
            {
               kv.insert(map<std::string, std::string>::value_type("title_prefix", offline_user.pm_offline_title));
               kv.insert(map<std::string, std::string>::value_type("site", pm_site));
            }
            
            PushOfflineCommonMsg(kv, pm_mobile);
        }
    }
}

//社区用户告警解除通知消息
int CAlarmDealNotifyMsg::NotifyMsg()
{
    int ret = -1;
    std::vector<evpp::TCPConnPtr> oDevConn;
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    ret = GetMsgControlInstance()->OnBuildCommunityAlarmDealNotifyMsg(stSocketMsg, dy_iv_msg, m_stAlarmDealMsg);  //不管怎样默认都加密了
    if (ret != 0)
    {
        AK_LOG_WARN << "Build personnal AlarmDealNotifyMsg failed";
        return -1;
    }

    AK_LOG_INFO << "Deal Alarm NotifyAlarmMsg, Node is " <<  m_stAlarmDealMsg.area_node;    
    NotifyPmMsg();
    
    g_accSer_ptr->GetDevListByNode(m_stAlarmDealMsg.area_node, oDevConn);
    
    //遍历同一节点的所有设备-App,发送通知消息
    for (const auto& DevConn : oDevConn)
    {
        DevicePtr app;
        if(g_accSer_ptr->GetClientFromConn(DevConn, app) == 0 && g_accSer_ptr->IsTCPConnIsAPP(DevConn))
        {
            //根据node获取真实站点
            std::string real_site;
            app->GetPerUidByNode(real_site, m_stAlarmDealMsg.area_node);
            if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
            {
                continue;
            }
        }
        AK_LOG_INFO << "Deal Notify  Alarm Msg, ip:port is " << DevConn->remote_addr().c_str();
        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(DevConn, stSocketMsg, dy_iv_msg);
    }

    //管理中心机
    //查找是否存在管理中心机
    std::vector<DEVICE_SETTING> mng_devs;
    if (dbinterface::ResidentDevices::GetCommunityMngDevByMngID(m_stAlarmDealMsg.manager_account_id, mng_devs) == 0)
    {
        for (auto& dev : mng_devs)
        {
            evpp::TCPConnPtr conn;
            if (!(dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
                    || (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && dev.unit_id == m_stAlarmDealMsg.unit_id)))
            {
                continue;
            }
            if (g_accSer_ptr->GetDevConnByMac(dev.mac, conn) == 0)
            {
                AK_LOG_INFO << "Deal Notify  Alarm Msg to manage device, ip:port is " << conn->remote_addr().c_str();
                GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
            }
        }
    }

    //端外推送业务
    if (dev_location_.empty())
    {
        dev_location_ = mac_;
    }

    std::vector<CMobileToken> oVec;
    CAkUserManager::GetInstance()->GetAkOfflineUsersByNode(m_stAlarmDealMsg.area_node, oVec); //获取下线的app列表

    for (const auto& mobileToken : oVec)
    {
        std::string real_site;
        CAkUserManager::GetInstance()->GetRealSiteByNodeAndMainSite(m_stAlarmDealMsg.area_node, mobileToken.Uid(), real_site);
        //校验实际站点账号是否为多套房账户且状态异常
        if(dbinterface::ProjectUserManage::MultiSiteLimit(real_site))
        {
            continue;
        }
        AppOfflinePushKV kv;
        //6.6多套房场景下，标题增加前缀
        std::string title;
        if (mobileToken.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(m_stAlarmDealMsg.area_node, title) == 0 )
        {
           kv.insert(map<std::string, std::string>::value_type("title_prefix", title));
           kv.insert(map<std::string, std::string>::value_type("site", real_site));
        }        

        PushOfflineCommonMsg(kv, mobileToken);
    }

    return 0;
}

//app请求监控设备的RTSP
int CRtspActionNotifyMsg::NotifyMsg()
{
    //获取app做NAT转换后的地址信息
    uint64_t traceid = getTraceId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);
    
    std::string app_nat_ip = getRemoteIP();
    int app_nat_port = getRemotePort();
    std::string dev_mac = getDevMac();
    std::string mac = getMac();
    int is_third = getIsthird();
    int type = getType();
    int video_pt = getVideoPt();
    std::string video_type = getVideoType();
    std::string video_fmtp = getVideoFmtp();
    std::string str_ssrc = getSSRC();
    std::string transfer_door_uuid = getTransferDoorUUID();
    std::string indoor_mac = getTransferIndoorMac();
    std::string srtp_key = getSrtpKey();
    int rtp_confuse = GetRtpConfuse();
    std::string camera_name = GetCameraName();
    int stream_id = GetStreamID();

    // dev_mac为csmain发给设备的mac,转流时使用indoor_mac(室内机)
    dev_mac = indoor_mac.size() > 0 ? indoor_mac : dev_mac;

    //根据MAC地址获取设备的IP和端口
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(dev_mac.c_str(), conn) < 0)
    {
        AK_LOG_INFO << "device(" << dev_mac.c_str() << ") not connected.";
        return -1;
    }
    
    //如果连接是ipv6 那么传ipv6的rtsp地址
    int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
    if (ipv6)
    {
        app_nat_ip = getRemoteIPV6();
    }

    //记录监控记录
    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) == 0)
    {
        dbinterface::DevRtspLog::InsertDevRtspLog(deviceSetting.device_node, mac);
    }
    AK_LOG_INFO << "begin to notify rtsp, Mac:" << mac.c_str() << ", rtsp action type<1:start> :" << type 
                << ",ssrc:" << str_ssrc << ",server:" << app_nat_ip << ",port:" << app_nat_port << ",video_type:" << video_type
                << ",camera:" << camera_name << ",stream:" << stream_id
                << ",video_pt:" << video_pt << ",transfer_door_uuid:" << transfer_door_uuid << ",srtp_key:" << srtp_key << ",rtp_confuse:" << rtp_confuse;

    SOCKET_MSG_REQ_RTSP req_rtsp_msg;
    ::memset(&req_rtsp_msg, 0, sizeof(req_rtsp_msg));
    Snprintf(req_rtsp_msg.protocal, sizeof(req_rtsp_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(req_rtsp_msg.remote_ip, sizeof(req_rtsp_msg.remote_ip), app_nat_ip.c_str());
    Snprintf(req_rtsp_msg.SSRC, sizeof(req_rtsp_msg.SSRC), str_ssrc.c_str());
    Snprintf(req_rtsp_msg.transfer_door_uuid, sizeof(req_rtsp_msg.transfer_door_uuid), transfer_door_uuid.c_str());
    Snprintf(req_rtsp_msg.srtp_key, sizeof(req_rtsp_msg.srtp_key), srtp_key.c_str());
    Snprintf(req_rtsp_msg.camera_name, sizeof(req_rtsp_msg.camera_name), camera_name.c_str());
    req_rtsp_msg.stream_id = stream_id;
    if (is_third)
    {
        Snprintf(req_rtsp_msg.camera_uuid, sizeof(req_rtsp_msg.camera_uuid), mac.c_str());
        Snprintf(req_rtsp_msg.video_type, sizeof(req_rtsp_msg.video_type), video_type.c_str());
        Snprintf(req_rtsp_msg.video_fmtp, sizeof(req_rtsp_msg.video_fmtp), video_fmtp.c_str());
        req_rtsp_msg.video_pt = video_pt;
    }

    req_rtsp_msg.remote_port = app_nat_port;
    req_rtsp_msg.type = type;
    req_rtsp_msg.have_third_camera = is_third;
    req_rtsp_msg.rtp_confuse = rtp_confuse;

    GetDeviceControlInstance()->SendRequestRtsp(conn, req_rtsp_msg, dev_mac);
    return 0;
}

//app请求保持设备的RTSP链路
int CRtspKeepNotifyMsg::NotifyMsg()
{
    uint64_t traceid = getTraceId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);
    
    int is_third = getIsthird();
    std::string mac = getMac();
    std::string dev_mac = getDevMac();
    std::string transfer_door_uuid = getTransferDoorUUID();
    std::string indoor_mac = getTransferIndoorMac();
    std::string camera = GetCameraName();
    int stream_id = GetStreamID();
    // dev_mac为csmain发给设备的mac,转流时使用indoor_mac(室内机)
    dev_mac = indoor_mac.size() > 0 ? indoor_mac : dev_mac;
    
    //根据MAC地址获取设备的IP和端口
    evpp::TCPConnPtr conn;
    if (g_accSer_ptr->GetDevConnByMac(dev_mac, conn) < 0)
    {
        AK_LOG_INFO << "CRtspKeepNotifyMsg device(" << dev_mac.c_str() << ") not connected.";
        return -1;
    }
    SOCKET_MSG_REQ_RTSP keepalive_rtsp_msg;
    ::memset(&keepalive_rtsp_msg, 0, sizeof(keepalive_rtsp_msg));
    if (is_third)
    {
        Snprintf(keepalive_rtsp_msg.camera_uuid, sizeof(keepalive_rtsp_msg.camera_uuid), mac.c_str());
    }
    keepalive_rtsp_msg.have_third_camera = is_third;
    Snprintf(keepalive_rtsp_msg.transfer_door_uuid, sizeof(keepalive_rtsp_msg.transfer_door_uuid), transfer_door_uuid.c_str());
    Snprintf(keepalive_rtsp_msg.camera_name, sizeof(keepalive_rtsp_msg.camera_name), camera.c_str());
    keepalive_rtsp_msg.stream_id = stream_id;
    GetDeviceControlInstance()->SendKeepRtsp(conn, keepalive_rtsp_msg, dev_mac);
    return 0;
}

//个人终端用户,联动单元设备、app配置相关信息发生变更,通知所有该单元中的设备、app
int CPersonnalNodeChangeNotifyMsg::NotifyMsg()
{
    std::string node = getNode();
    std::vector<evpp::TCPConnPtr> oDevConn;
    g_accSer_ptr->GetDevListByNode(node, oDevConn);

    for (const auto& DevConn : oDevConn)
    {
        AK_LOG_INFO << "PersonnalNodeChange NotifyMsg, dev ip:port is " << DevConn->remote_addr().c_str();
        if (!g_accSer_ptr->IsTCPConnIsAPP(DevConn))
        {
            //V4.0不再让设备上报状态，才看变化下载
            GetDeviceControlInstance()->SendConnKeysend(DevConn);
        }
        else
        {
            GetMsgControlInstance()->OnSendDevListChangeMsg(DevConn);//如果是app,只需要通知联系人变更，不需要重新上报状态
        }
    }
    return 0;
}



/*
1、网页修改配置
   a 个人变化 通知自己联动/公共设备/单元设备
   b 单元变化 通知自己和这个单元的所有联动
   c 公共设备变化        通知自己/所有联动
2、ip变化
   a 个人变化 通知自己联动/公共设备/单元设备
   b 单元变化 这个单元的所有联动
   c 公共设备变化        通知所有联动
*/
int CCommunityNodeChangeNotifyMsg::NotifyMsg()
{
    if (m_stUpdateNode.change_type == 2 || m_stUpdateNode.change_type == 3)  //升级时候只要通知自己 autop
    {
        if (strlen(m_stUpdateNode.mac) > 0)
        {
            evpp::TCPConnPtr DevConn;
            uint32_t ret = g_accSer_ptr->GetDevConnByMac(m_stUpdateNode.mac, DevConn);
            if (0 == ret)
            {
                GetDeviceControlInstance()->SendConnKeysend(DevConn);
            }
        }
        return 0;
    }

    AK_LOG_INFO << "community node Change NotifyMsg, dev type is " << m_stUpdateNode.nUpdateDevType;
    if (WEB_UPDATE_DEV_TYPE_COMMUNITY_PER == m_stUpdateNode.nUpdateDevType)
    {
        //个人更新,对应的公共设备和单元公共设备也要更新
        std::vector<evpp::TCPConnPtr> oDevConn;
        g_accSer_ptr->GetDevListCommunityPublicAndPersonnal(m_stUpdateNode.node, m_stUpdateNode.unit_id, m_stUpdateNode.manager_account_id, oDevConn);

        for (const auto& DevConn : oDevConn)
        {
            if (!g_accSer_ptr->IsTCPConnIsAPP(DevConn))
            {
                GetDeviceControlInstance()->SendConnKeysend(DevConn);
            }
            else
            {
                GetMsgControlInstance()->OnSendDevListChangeMsg(DevConn);//如果是app,只需要通知联系人变更，不需要重新上报状态
            }
        }
    }
    else if (WEB_UPDATE_DEV_TYPE_COMMUNITY_PUBLIC_UNIT == m_stUpdateNode.nUpdateDevType)
    {
        std::vector<evpp::TCPConnPtr> oDevConn;
        //梯口下的用户+V4.4有管理中心机时候所有公共设备
        g_accSer_ptr->GetDevListCommunityUnderUnitDevAndAllPubDev(m_stUpdateNode.manager_account_id, m_stUpdateNode.unit_id, oDevConn);

        for (const auto& DevConn : oDevConn)
        {
            if (!g_accSer_ptr->IsTCPConnIsAPP(DevConn))
            {
                GetDeviceControlInstance()->SendConnKeysend(DevConn);
            }
            else
            {
                GetMsgControlInstance()->OnSendDevListChangeMsg(DevConn);//如果是app,只需要通知联系人变更，不需要重新上报状态
            }

        }
        /*
        //单元更新，主张账号下的设备联系人要更新，所以门口机不需要更新
        evpp::TCPConnPtr DevConn;
        DEVICE_SETTING *pDeviceSettingList = GetDeviceSettingInstance()->GetRootCommunityDeviceListUnderPublicUnit(m_stUpdateNode.unit_id);
        DEVICE_SETTING *pCurDeviceSetting = pDeviceSettingList;
        while(pCurDeviceSetting != nullptr)
        {
            if (pCurDeviceSetting->type == DEVICE_TYPE_DOOR)
            {
                pCurDeviceSetting = pCurDeviceSetting->next;
                continue;
            }
            std::string mac = pCurDeviceSetting->mac;
            int ret = g_accSer_ptr->GetDevConnByMac(mac, DevConn);
            if (0 == ret)
            {
                GetDeviceControlInstance()->SendRequestStatus(DevConn);
            }
            pCurDeviceSetting = pCurDeviceSetting->next;
        }
        GetDeviceSettingInstance()->DestoryDeviceSettingList(pDeviceSettingList);
        */
        //发送给自己
        if (strlen(m_stUpdateNode.mac) > 0)
        {
            evpp::TCPConnPtr DevConn;
            uint32_t ret = g_accSer_ptr->GetDevConnByMac(m_stUpdateNode.mac, DevConn);
            if (0 == ret)
            {
                GetDeviceControlInstance()->SendConnKeysend(DevConn);
            }
        }
    }
    else //公共设备目前只需要通知自己+V4.4管理中心机 需要通知所有的包括梯口公共设备
    {
        std::vector<evpp::TCPConnPtr> oDevConn;
        g_accSer_ptr->GetDevListCommunityAllDevAndAPP(m_stUpdateNode.manager_account_id, oDevConn);

        for (const auto& DevConn : oDevConn)
        {
            if (!g_accSer_ptr->IsTCPConnIsAPP(DevConn))
            {
                GetDeviceControlInstance()->SendConnKeysend(DevConn);
            }
            else
            {
                GetMsgControlInstance()->OnSendDevListChangeMsg(DevConn);//如果是app,只需要通知联系人变更，不需要重新上报状态
            }
        }
        //发送给自己
        if (strlen(m_stUpdateNode.mac) > 0)
        {
            evpp::TCPConnPtr DevConn;
            uint32_t ret = g_accSer_ptr->GetDevConnByMac(m_stUpdateNode.mac, DevConn);
            if (0 == ret)
            {
                GetDeviceControlInstance()->SendConnKeysend(DevConn);
            }
        }
    }
    return 0;
}

//包括社区
int CPersonnalMotionNotifyMsg::NotifyMsg()
{
    int ret = -1;
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    //在线/离线通知app时带上node信息，app通过node调用slim的/siteconf接口，在/siteconf内部通过node再找出实际站点的信息返回app
    ret = GetMsgControlInstance()->OnBuildMotionNotifyMsg(stSocketMsg, dy_iv_msg, proto_, mac_, id_, sip_account_, dev_location_, time_, node_);
    if (ret != 0)
    {
        AK_LOG_WARN << "BuildMotionNotifyMsg failed";
        return -1;
    }
    std::vector<evpp::TCPConnPtr> conns; //待发送通知的conns
    ConnectionList conn_apps;
    g_accSer_ptr->GetAppByNode(node_, conn_apps);
    AK_LOG_INFO << "motion alert notify,there are " << conn_apps.size() << " online app conns";

    ConnectionListIter it = conn_apps.begin();
    for (; it != conn_apps.end(); ++it)
    {
        std::string uid;
        if (it->second->GetPerUidByNode(uid, node_) != 0)
        {
            AK_LOG_WARN << "Uid=" << uid << " is not app, so it is error when geting UID. Disconnect. make it reconnect";
            it->first->Close();
            continue;
        }

        //校验实际站点账号是否为多套房账户且状态异常
        if(dbinterface::ProjectUserManage::MultiSiteLimit(uid))
        {
            continue;
        }

        int app_state = AppCallStatus::GetInstance().GetAppState(uid);
        if (APP_STATE_DND == app_state)
        {
            AK_LOG_INFO << "Uid=" << uid << " set dnd, ignore motion alert message.";
            continue;
        }

        //先确定redis中是否有设置过了
        CacheManager* cache_manager = CacheManager::getInstance();
        CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_appconf); //获取与redis实例的tcp连接
        if (cache_conn)
        {
            std::string key = "motion";
            std::string type;

            type = cache_conn->hget(key, uid);
            if (type.empty()) //如果该uid从来没有设置过,设置为默认接收motion
            {
                cache_conn->hset(key, uid, Int2String(csmain::MotionRecvType::kRecv));
                conns.push_back(it->first);
                AK_LOG_INFO << "Uid=" << uid << " need receive motion alert message.";
            }
            else //redis里面有记录
            {
                uint32_t type2 = String2Int(type);
                if (type2 == csmain::MotionRecvType::kRecv)
                {
                    conns.push_back(it->first);
                    AK_LOG_INFO << "Uid=" << uid << " need receive motion alert message.";
                }
            }
            cache_manager->RelCacheConn(cache_conn);//TODO:2019-01-18,后续用guard的形式,避免忘记释放
        }

    }

    //遍历同一节点的所有设备-App,发送通知消息
    for (const auto& conn : conns)
    {
        AK_LOG_INFO << "send motion alert Msg, ip:port is " << conn->remote_addr().c_str();
        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
    }

    //if (gstAKCSConf.nOemNum == OEM_DISCREET)
    // {
    //     SOCKET_MSG stSocketMsg, dy_iv_msg;
    //     memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    //     //在线/离线通知app时带上node信息，app通过node调用slim的/siteconf接口，在/siteconf内部通过node再找出实际站点的信息返回app
    //     ret = GetMsgControlInstance()->OnBuildMotionToDevNotifyMsg(stSocketMsg, dy_iv_msg, proto_, mac_, id_, sip_account_, dev_location_, time_, node_);
    //     if (ret == 0)
    //     {
    //         std::vector<evpp::TCPConnPtr> conns; //待发送通知的conns
    //         g_accSer_ptr->GetDevListByNodeOnlyDev(node_, conns);
    //         for (const auto& conn : conns)
    //         {
    //             AK_LOG_INFO << "send motion alert Msg to dev, ip:port is " <<  conn->remote_addr().c_str();
    //             GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
    //         }
    //     }
    // }

    //端外推送业务
    std::vector<CMobileToken> oVec;
    UIdTokenList uid_tokens;
    CAkUserManager::GetInstance()->GetAkOfflineUidTokensByNode(node_, uid_tokens);
    UIdTokenListIter iter_token = uid_tokens.begin();
    for (; iter_token != uid_tokens.end(); ++iter_token)
    {
        std::string uid;
        std::string main_site;
        main_site = iter_token->first;
        //多套房查询node_uids_中保存的真实站点
        if (iter_token->second.IsMultiSite())
        {
            if (0 != CAkUserManager::GetInstance()->GetRealSiteByNodeAndMainSite(node_, main_site, uid))
            {
                continue;
            }
            
            //校验实际站点账号是否为多套房账户且状态异常
            if(dbinterface::ProjectUserManage::MultiSiteLimit(uid))
            {
                continue;
            }
        }
        else
        {
            uid = main_site;
        }

        int app_state = AppCallStatus::GetInstance().GetAppState(uid);
        if (APP_STATE_DND == app_state)
        {
            AK_LOG_INFO << "Uid=" << uid << " set dnd, ignore motion alert message.";
            continue;
        }

        //先确定redis中是否有设置过了
        CacheManager* cache_manager = CacheManager::getInstance();
        CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_appconf); //获取与redis实例的tcp连接
        if (cache_conn)
        {
            std::string key = "motion";
            std::string type;
            type = cache_conn->hget(key, uid);
            if (type.empty()) //如果该uid从来没有设置过,设置为默认接收motion
            {
                cache_conn->hset(key, uid, Int2String(csmain::MotionRecvType::kRecv));
                oVec.push_back(iter_token->second);
            }
            else
            {
                uint32_t type2 = String2Int(type);
                if (type2 == csmain::MotionRecvType::kRecv)
                {
                    oVec.push_back(iter_token->second);
                }
            }
            cache_manager->RelCacheConn(cache_conn);
        }
    }

    for (const auto& mobileToken : oVec)
    {
        AppOfflinePushKV kv;
        //6.6多套房场景下，标题增加前缀
        std::string title;
        if (mobileToken.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(node_, title) == 0 )
        {
           kv.insert(map<std::string, std::string>::value_type("title_prefix", title));
           kv.insert(map<std::string, std::string>::value_type("site", node_));
        }
        
        kv.insert(map<std::string, std::string>::value_type("language", mobileToken.Language()));
        kv.insert(map<std::string, std::string>::value_type("mac_sip", mac_));
        kv.insert(map<std::string, std::string>::value_type("device_name", dev_location_));
        kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(mobileToken.CommonVersion())));
        kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(mobileToken.AppOem())));
        PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
        if (push_cli_ptr)
        {
            if (mobileToken.MobileType() == csmain::AppType::APP_IOS)
            {
                push_cli_ptr->buildPushMsg(mobileToken.MobileType(), mobileToken.Token(), csmain::PUSH_MSG_TYPE_MOTION, kv, mobileToken.OemName());
            }
            else if (mobileToken.MobileType() != csmain::AppType::APP_IOS)  //fcm 推送
            {
                push_cli_ptr->buildPushMsg(mobileToken.MobileType(), mobileToken.FcmToken(), csmain::PUSH_MSG_TYPE_MOTION, kv, mobileToken.OemName());
            }
        }
    }
    // //added by chenyc,4.2 ver,for video storage check.
    // if (CVideoSchedMng::Instance()->IsNeedTriggerVS(mac_))
    // {
    //     LOG_INFO << "need to tringer video storage,mac is " << mac_.c_str();
    //     CVideoSchedMng::Instance()->TriggerVS(mac_, node_);
    // }
    return 0;
}

std::string CPerTextNotifyMsg::GetMsgId(uint32_t msg_id, int role)
{
    return std::to_string(msg_id) + "_" + std::to_string(role);
}

int CPerTextNotifyMsg::NotifyMsg()
{
    int ret = -1;
    SOCKET_MSG socket_msg, dy_iv_socket;
    memset(&socket_msg, 0, sizeof(socket_msg));
    ret = GetMsgControlInstance()->BuildTextMessageMsg(socket_msg, dy_iv_socket, &text_msg_info_);
    if (ret != 0)
    {
        AK_LOG_WARN << "BuildTextMessageMsg failed";
        return -1;
    }
    std::vector<evpp::TCPConnPtr> dev_conns; //待发送通知的设备 conns
    evpp::TCPConnPtr app_conn; //待发送通知的app conn

    if (client_type_ == CPerTextNotifyMsg::DEV_SEND)
    {
        g_accSer_ptr->GetDevListByNodeOnlyDev(account_, dev_conns);
        
        for (auto& conn : dev_conns)
        {
            AK_LOG_INFO << "Send text message, dev ip:port is " << conn->remote_addr().c_str();
            if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, socket_msg, dy_iv_socket) < 0)
            {
                AK_LOG_WARN << "Send text message failed, dev ip:port is " << conn->remote_addr().c_str();
            }
        }
    }
    else if (client_type_ == CPerTextNotifyMsg::APP_SEND)
    {
        char main_user_account[64] = {0};
        PerAccountUserInfo user_info;
        if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByAccount(account_, user_info))
        {
            Snprintf(main_user_account, sizeof(main_user_account), user_info.main_user_account);
        }
        else
        {
            //没找到可能是link的pm app.
            UserInfoAccount pm_user_info;
            std::string userinfo_uuid = dbinterface::ResidentPersonalAccount::GetUserInfoUUIDByAccount(account_);
            if (0 != dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(userinfo_uuid, pm_user_info))
            {
                AK_LOG_WARN << "Send text message failed, account not found main account. account: " << account_;
                return -1;
            }
            Snprintf(main_user_account, sizeof(main_user_account), pm_user_info.main_user_account);
        }
        

        //校验实际站点账号是否为多套房账户且状态异常
        if(dbinterface::ProjectUserManage::MultiSiteLimit(account_))
        {
            return -1;
        }
        
        int online = 0;
        if (0 == g_accSer_ptr->GetDevConnByMainUid(main_user_account, app_conn))
        {
            AK_LOG_INFO << "Send text message, app ip:port is " << app_conn->remote_addr().c_str();
            online = 1;
            if (GetDeviceControlInstance()->SendTcpFormateDyIvMsg(app_conn, socket_msg, dy_iv_socket) < 0)
            {
                AK_LOG_WARN << "Send text message failed, app ip:port is " << app_conn->remote_addr().c_str();
            }

            //return 0;  iOS在线也需要走推送弹框
        }

        
        CMobileToken mobile_token;
        AppOfflinePushKV kv;
        int push_type;
        CAkUserManager::GetInstance()->GetAkOfflineUserTokenByUid(main_user_account, mobile_token); //获取下线的app列表            
        //6.6多套房场景下，标题增加前缀
        std::string title;
        if (mobile_token.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(account_, title) == 0 )//要用实际的站点获取信息
        {
           kv.insert(map<std::string, std::string>::value_type("title_prefix", title));
        }
/*
        // 未读消息总数 : notice_msg + voice_msg + call_history . 目前只有推给ios才能生效 (等app方案成熟再推)
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
        {
            int unread_messages_count = dbinterface::Message::getUnReadMessageCount(user_info.uuid, user_info.create_time);
            int unread_callhistory_count = dbinterface::CallHistory::GetUnReadCallHistoryCount(account_ ,gstAKCSLogDelivery.call_history_delivery);
            int unread_all_count = unread_messages_count + unread_callhistory_count;
            kv.insert(map<std::string, std::string>::value_type("unread_msg", std::to_string(unread_all_count)));
        }
*/
        int role = 0;
        if (0 != dbinterface::ProjectUserManage::GetRoleByAccount(account_, role))
        {
            AK_LOG_WARN << "GetRoleByAccount failed, account = " << account_;
            return -1;
        }

        kv.insert(map<std::string, std::string>::value_type("language", mobile_token.Language()));
        kv.insert(map<std::string, std::string>::value_type("dclient", std::to_string(mobile_token.CommonVersion())));
        kv.insert(map<std::string, std::string>::value_type("app_oem", std::to_string(mobile_token.AppOem())));
        kv.insert(map<std::string, std::string>::value_type("msg_id", GetMsgId(text_msg_info_.id, role)));
        if(text_msg_info_.type == CPerTextNotifyMsg::TEXT_MSG)
        {
            kv.insert(map<std::string, std::string>::value_type("title", text_msg_info_.title));
            kv.insert(map<std::string, std::string>::value_type("content", text_msg_info_.content));
            push_type = csmain::PUSH_MSG_TYPE_TEXT;
        }
        else if(text_msg_info_.type == CPerTextNotifyMsg::DELIVERY_MSG)
        {
            kv.insert(map<std::string, std::string>::value_type("amount", text_msg_info_.content));
            push_type = csmain::PUSH_MSG_TYPE_DELIVERY;
        }
        else if(text_msg_info_.type == CPerTextNotifyMsg::DELIVERY_BOX_MSG)
        {
            kv.insert(map<std::string, std::string>::value_type("content", text_msg_info_.content));
            push_type = csmain::PUSH_MSG_TYPE_DELIVERY_BOX;
            kv["language"] = "ja";
        }
        else if(text_msg_info_.type == CPerTextNotifyMsg::TMPKEY_MSG)
        {           
            kv.insert(map<std::string, std::string>::value_type("name", text_msg_info_.content));
            push_type = csmain::PUSH_MSG_TYPE_TMPKEY;
        }
        else if(text_msg_info_.type == CPerTextNotifyMsg::VOICE_MSG)
        {
            kv.insert(map<std::string, std::string>::value_type("title", "Voice_Message"));
            kv.insert(map<std::string, std::string>::value_type("content", text_msg_info_.content));
            push_type = csmain::PUSH_MSG_TYPE_VOICE_MSG;
		}
        else if (text_msg_info_.type == CPerTextNotifyMsg::BOOKING_MSG)
        {
            kv.insert(map<std::string, std::string>::value_type("title", text_msg_info_.title));
            kv.insert(map<std::string, std::string>::value_type("content", text_msg_info_.content));
            push_type = csmain::PUSH_MSG_TYPE_BOOKING;
        }
        else if(text_msg_info_.type == CPerTextNotifyMsg::YALE_BATTERY_1WEEK \
            || text_msg_info_.type == CPerTextNotifyMsg::YALE_BATTERY_2WEEK \
            || text_msg_info_.type == CPerTextNotifyMsg::YALE_BATTERY_LOW)
        {           
            kv.insert(map<std::string, std::string>::value_type("name", text_msg_info_.content));
            kv.insert(map<std::string, std::string>::value_type("battery_type", std::to_string(text_msg_info_.type)));
            push_type = csmain::PUSH_MSG_TYPE_YALE_BATTERY;
        }
        else if (text_msg_info_.type == CPerTextNotifyMsg::DOAMAKABA_BATTERY_LOW)
        {
            push_type = csmain::PUSH_MSG_TYPE_DORMAKABA_BATTERY;
            kv.insert(map<std::string, std::string>::value_type("name", text_msg_info_.content));
        }
        
        else if (text_msg_info_.type == CPerTextNotifyMsg::ITEC_BATTERY_LOW_5 \
                || text_msg_info_.type == CPerTextNotifyMsg::ITEC_BATTERY_LOW_10 \
                || text_msg_info_.type == CPerTextNotifyMsg::ITEC_BATTERY_LOW_15)
        {
            push_type = csmain::PUSH_MSG_TYPE_ITEC_BATTERY;
            kv.insert(map<std::string, std::string>::value_type("name", text_msg_info_.content));
            kv.insert(map<std::string, std::string>::value_type("battery_type", std::to_string(text_msg_info_.type)));
        }
        PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
        if (push_cli_ptr)
        {
            if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
            {
                //为booking消息且App在线，无需离线推送，与Android保持一致
                if (push_type == csmain::PUSH_MSG_TYPE_BOOKING && online)
                {
                    AK_LOG_INFO << "no need to push offline booking msg, app is online";
                    return 0;
                }
                push_cli_ptr->buildPushMsg(mobile_token.MobileType(), mobile_token.Token(), push_type, kv, mobile_token.OemName());
            }      
            //安卓只有离线要走推送,需求规定
            else if(!online || push_type == csmain::PUSH_MSG_TYPE_YALE_BATTERY)   
            {
                push_cli_ptr->buildPushMsg(mobile_token.MobileType(), mobile_token.FcmToken(), push_type, kv, mobile_token.OemName());
            }
        }
    }
    
    return 0;
}

int CFaceDataNotifyMsg::NotifyMsg()
{
    int ret = -1;
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    ret = GetMsgControlInstance()->OnBuildFaceDataNotifyMsg(stSocketMsg, dy_iv_msg, face_data_msg_); //加密暂定默认。否则设备多的话要加密好几次
    if (ret != 0)
    {
        AK_LOG_WARN << "BuildFaceDataNotifyMsg failed";
        return -1;
    }

    std::vector<std::string> oMac;
    SplitString(mac_list_, "_", oMac);
    for (const auto& mac : oMac)
    {
        evpp::TCPConnPtr conn;
        if (0 == g_accSer_ptr->GetDevConnByMac(mac, conn))
        {
            GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
        }
    }
    return 0;
}


int CHttpReqNotifyMsg::NotifyMsg()
{
    //最多重试三次
    for (int i = 0; i < 3; i++)
    {
        std::string respone;
        if (http_type_ == HTTP_REQ_TYPE::POST)
        {
            if (model::HttpRequest::GetInstance().Post(url_, data_, respone, data_type_) != 0)
            {
                continue;
            }
        }
        else
        {
            if (model::HttpRequest::GetInstance().Get(url_, parma_kv_, respone) != 0)
            {
                continue;
            }
        }

        Json::Reader reader;
        Json::Value root;
        if (!reader.parse(respone, root))
        {
            AK_LOG_WARN << "respone error," << " data:" << respone;
            continue;
        }

        if (!root.isMember("code"))
        {
            continue;
        }

        int code = root["code"].asInt();
        if (0 == code)
        {
            if (CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL == req_type_)
            {
                Json::Value data;
                data = root["data"];
                std::string url;
                if (data.isMember("Link"))
                {
                    url = data["Link"].asString();
                }
                std::string mac = context_kv_["mac"];
                std::string mac_uuid = context_kv_["mac_uuid"];
                std::string voice_uuid = context_kv_["voice_uuid"];
                GetMsgControlInstance()->OnSendVoiceMsgUrl(mac, mac_uuid, voice_uuid, url);
            }
            return 0;
        }
        else
        {
            AK_LOG_WARN << "respone error," << " data:" << respone;
        }        
    }

    return -1;
}

int CAwsAlarmNotifyMsg::NotifyMsg()
{
    evpp::TCPConnPtr conn = conn_.lock();
    if (conn)
    {
        GetMsgControlInstance()->OnDeviceAlarmMsg(&msg_, conn);
    }
    return 0;
}


