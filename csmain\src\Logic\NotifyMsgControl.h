#ifndef __NOTIFY_MSG_CONTROL_H__
#define __NOTIFY_MSG_CONTROL_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AkcsWebMsgSt.h"
#include "AK.Base.pb.h"
#include "AkcsHttpRequest.h"
#include "PushClient.h"
#include "AKUserMng.h"



class CNotifyMsg; //前置声明
class CAlarmNotifyMsg;
class CAlarmDealNotifyMsg;
class CKeyChangeNotifyMsg;
class CRtspActionNotifyMsg;
class CPersonnalAlarmNotifyMsg;
class CPersonnalNodeChangeNotifyMsg;
class CPersonnalAlarmDealMsg;
class CPersonnalMotionNotifyMsg;
class CRtspKeepNotifyMsg;
class CPerTextNotifyMsg;
class CCommunityNodeChangeNotifyMsg;
class CFaceDataNotifyMsg;
class CHttpReqNotifyMsg;
class CAwsAlarmNotifyMsg;

typedef std::map<std::string/*key*/, std::string/*value*/> AkcsKv;

void AddMultiSiteUserTitle(const std::string &uid, AppOfflinePushKV& kv);

class CNotifyMsgControl
{
public:
    typedef std::shared_ptr<CNotifyMsg> NotifyMsgPrt;

public:
    CNotifyMsgControl();
    ~CNotifyMsgControl();
    //added by chenyc,2019-03-05,这个消息处理只需要关注本机即可,
    //所有的消息都通过csroute走一遍,即使是本机所挂载的终端消息之间的投递.
    static CNotifyMsgControl* GetInstance();
    static CNotifyMsgControl* GetHttpReqInstance();
    static CNotifyMsgControl* GetMotionInstance();
    static CNotifyMsgControl* GetRtspInstance();

    //初始化
    int Init();
    int GetNotifyMsgListSize();
    //处理消息
    int ProcessNotifyMsg();
    //增加一个新的消息
    //TODO:chenyc,2017-06-29,统一用: int AddNotifyMsg(const NotifyMsgPrt &pCMsg),调用的时候传入各个继承类对象的智能指针.
    //added by chenyc, 2017-09-03,智能指针无法使用资源的继承关系(但是资源本身是可行的),上述方案不行
    int AddAlarmNotifyMsg(const CAlarmNotifyMsg& pCMsg);
    int AddPersonnalAlarmNotifyMsg(const CPersonnalAlarmNotifyMsg& CMsg);
    int AddAlarmDealNotifyMsg(const CAlarmDealNotifyMsg& pCMsg);
    int AddPersonnalAlarmDealNotifyMsg(const CPersonnalAlarmDealMsg& CMsg);
    ///从这里开始
    int AddKeyChangeNotifyMsg(const CKeyChangeNotifyMsg* pCMsg);//ipc 广播

    int AddPersonnalNodeChangeNotifyMsg(const CPersonnalNodeChangeNotifyMsg& CMsg); //ipc 广播

    //不需要广播
    int AddPerMotionNotifyMsg(const CPersonnalMotionNotifyMsg& CMsg);//广播
    
    //不需要广播
    int AddRtspActionNotifyMsg(const CRtspActionNotifyMsg& CRtspMsg);
    int AddRtspKeepNotifyMsg(const CRtspKeepNotifyMsg& CMsg);

    int AddTextNotifyMsg(const CPerTextNotifyMsg& CMsg); //ipc的广播

    //社区设备状态改变通知
    int AddCommunityNodeChangeNotifyMsg(const CCommunityNodeChangeNotifyMsg& CMsg);//ipc 广播
    //人脸数据转发
    int AddFaceDataNotifyMsg(const CFaceDataNotifyMsg& CMsg);  

    int AddHttpReqNotiyMsg(const CHttpReqNotifyMsg& msg);
    int AddAwsAlarmNotiyMsg(const CAwsAlarmNotifyMsg& msg);
private:
    std::list<NotifyMsgPrt> m_NotifyMsgList;
    std::mutex m_mtx;
    std::condition_variable m_cv;
    std::thread m_t;
    uint32_t m_MsgCount;  //通知消息队列中未消费的消息个数
    static CNotifyMsgControl* instance;
    static CNotifyMsgControl* http_req_instance;
    static CNotifyMsgControl* motion_instance;
    static CNotifyMsgControl* rtsp_instance;
};

CNotifyMsgControl* GetNotifyMsgControlInstance();
CNotifyMsgControl* GetHttpReqMsgControlInstance();
CNotifyMsgControl* GetAppWakeupMsgControlInstance();
CNotifyMsgControl* GetMotionMsgControlInstance();
CNotifyMsgControl* GetRtspMsgControlInstance();



class CNotifyMsg
{
public:
    CNotifyMsg()
    {

    }
    virtual ~CNotifyMsg()
    {

    }
    int virtual NotifyMsg() = 0;
};


//alarm改变通知消息
class CAlarmNotifyMsg : public CNotifyMsg
{
public:
    CAlarmNotifyMsg()
    {
        memset(&m_stAlarmMsg, 0, sizeof(m_stAlarmMsg));
    }
    CAlarmNotifyMsg(const CAlarmNotifyMsg* other)
    {
        memcpy(&m_stAlarmMsg, &(other->m_stAlarmMsg), sizeof(m_stAlarmMsg));
        mac_ = m_stAlarmMsg.mac;
        dev_location_ = m_stAlarmMsg.from_local;
        alarm_msg_ = m_stAlarmMsg.type;
    }
    CAlarmNotifyMsg(const SOCKET_MSG_ALARM_SEND& AlarmMsg)
        : mac_(AlarmMsg.mac)
        , dev_location_(AlarmMsg.from_local)
        , alarm_msg_(AlarmMsg.type)
    {
        memcpy(&m_stAlarmMsg, &AlarmMsg, sizeof(m_stAlarmMsg));
    }

    ~CAlarmNotifyMsg()
    {
    }
    int NotifyMsg();
    void PushOfflineCommonMsg(AppOfflinePushKV& kv, CMobileToken mobile_token);
    void NotifyPmMsg();
private:
    SOCKET_MSG_ALARM_SEND m_stAlarmMsg;
    std::string mac_;
    std::string dev_location_;//发出alarm设备的location
    std::string alarm_msg_;//type

};

//个人终端用户alarm改变通知消息
class CPersonnalAlarmNotifyMsg : public CNotifyMsg
{
public:
    CPersonnalAlarmNotifyMsg()
    {
        memset(&m_stAlarmMsg, 0, sizeof(m_stAlarmMsg));
    }
    CPersonnalAlarmNotifyMsg(const SOCKET_MSG_ALARM_SEND& AlarmMsg, const std::string& mac,
                             const std::string& location, const std::string& alarm)
        : mac_(mac)
        , dev_location_(location)
        , alarm_msg_(alarm)
    {
        memcpy(&m_stAlarmMsg, &AlarmMsg, sizeof(m_stAlarmMsg));
    }
    CPersonnalAlarmNotifyMsg(const CPersonnalAlarmNotifyMsg& other)
    {
        memcpy(&m_stAlarmMsg, &(other.m_stAlarmMsg), sizeof(m_stAlarmMsg));
        mac_ = other.mac_ ;
        dev_location_ = other.dev_location_;
        alarm_msg_ = other.alarm_msg_;
    }
    ~CPersonnalAlarmNotifyMsg()
    {

    }
    int NotifyMsg();
private:
    SOCKET_MSG_ALARM_SEND m_stAlarmMsg;
    std::string mac_;
    std::string dev_location_;//发出alarm设备的location
    std::string alarm_msg_;//type
};


//alarm解除通知消息
class CAlarmDealNotifyMsg : public CNotifyMsg
{
public:
    CAlarmDealNotifyMsg()
    {
        memset(&m_stAlarmDealMsg, 0, sizeof(m_stAlarmDealMsg));
    }
    CAlarmDealNotifyMsg(const SOCKET_MSG_ALARM_DEAL& AlarmMsg,
                        const std::string& mac,
                        const std::string& location,
                        const std::string& alarm)
        : mac_(mac)
        , dev_location_(location)
        , alarm_msg_(alarm)
    {
        memcpy(&m_stAlarmDealMsg, &AlarmMsg, sizeof(m_stAlarmDealMsg));
    }

    CAlarmDealNotifyMsg(const CAlarmDealNotifyMsg& other)
    {
        memcpy(&m_stAlarmDealMsg, &(other.m_stAlarmDealMsg), sizeof(m_stAlarmDealMsg));
        mac_ = other.mac_ ;
        dev_location_ = other.dev_location_;
        alarm_msg_ = other.alarm_msg_;
    }
    ~CAlarmDealNotifyMsg()
    {

    }
    int NotifyMsg();
    void PushOfflineCommonMsg(AppOfflinePushKV& kv, CMobileToken mobile_token);
    void NotifyPmMsg();
private:
    std::string mac_;//发出alarm设备的mac
    std::string dev_location_;//发出alarm设备的location
    std::string alarm_msg_;
    SOCKET_MSG_ALARM_DEAL m_stAlarmDealMsg;
};


//rtsp开始/停止监控的通知消息
class CRtspActionNotifyMsg : public CNotifyMsg
{
public:

    CRtspActionNotifyMsg() = default;

    CRtspActionNotifyMsg(const std::string& ip, const int port, const std::string& mac, const int type)
        :   port_(port) 
        , remote_ip_(ip) 
        , mac_(mac)
        , type_(type)
    {
        rtp_confuse_ = 0;
    }

    CRtspActionNotifyMsg(const CRtspActionNotifyMsg& other)
    {
        port_ = other.port_;
        remote_ip_ = other.remote_ip_;
        remote_ipv6_ = other.remote_ipv6_;
        mac_ = other.mac_;
        dev_mac_ = other.dev_mac_;
        type_ = other.type_;
        ssrc_ = other.ssrc_;
        is_third_ = other.is_third_;
        video_pt_ = other.video_pt_;
        video_type_ = other.video_type_;
        transfer_door_uuid_ = other.transfer_door_uuid_;
        transfer_indoor_mac_ = other.transfer_indoor_mac_;
        srtp_key_ = other.srtp_key_;
        traceid_ = other.traceid_;
        rtp_confuse_ = other.rtp_confuse_;
        camera_name_ = other.camera_name_;
        stream_id_ = other.stream_id_;
    }

    ~CRtspActionNotifyMsg()
    {

    }
    int NotifyMsg();

    //以下各接口为vrtsp上报的app做NAT转换后的相关信息
    std::string getRemoteIP() const
    {
        return remote_ip_;
    }
    
    void setRemoteIP(const std::string& ip)
    {
        remote_ip_ = ip;
    }
    
    //以下各接口为vrtsp上报的app做NAT转换后的相关信息
    std::string getRemoteIPV6() const
    {
        return remote_ipv6_;
    }
    
    void setRemoteIPV6(const std::string& ip)
    {
        remote_ipv6_ = ip;
    }

    void setRemotePort(const int port)
    {
        port_ = port;
    }
    
    int getRemotePort() const
    {
        return port_;
    }

    void setDevMac(const std::string& mac)
    {
        dev_mac_ = mac;
    }
    
    std::string getDevMac() const
    {
        return dev_mac_;
    }

    int getType() const
    {
        return type_;
    }
    
    void setType(int type)
    {
        type_ = type;
    }
    
    void setSSRC(const std::string& ssrc)
    {
        ssrc_ = ssrc;
    }
    
    std::string getSSRC() const
    {
        return ssrc_;
    }
    
    int getIsthird() const
    {
        return is_third_;
    }
    
    void setIsthird(int is_third)
    {
        is_third_ = is_third;
    }
    
    void setMac(const std::string& mac)
    {
        mac_ = mac;
    }
    
    std::string getMac() const
    {
        return mac_;
    }
    
    int getVideoPt() const
    {
        return video_pt_;
    }
    
    void setVideoPt(int video_pt)
    {
        video_pt_ = video_pt;
    }
    
    void setVideoType(const std::string& video_type)
    {
        video_type_ = video_type;
    }
    
    std::string getVideoType() const
    {
        return video_type_;
    }
    void setTransferDoorUUID(const std::string& transfer_door_uuid)
    {
        transfer_door_uuid_ = transfer_door_uuid;
    }
    
    std::string getTransferDoorUUID() const
    {
        return transfer_door_uuid_;
    }
    
    void setTransferIndoorMac(const std::string& transfer_indoor_mac)
    {
        transfer_indoor_mac_ = transfer_indoor_mac;
    }
    
    std::string getTransferIndoorMac() const
    {
        return transfer_indoor_mac_;
    }
    
    void setVideoFmtp(const std::string& video_fmtp)
    {
        video_fmtp_ = video_fmtp;
    }
    
    std::string getVideoFmtp() const
    {
        return video_fmtp_;
    }
    
    void setSrtpKey(const std::string& srtp_key)
    {
        srtp_key_ = srtp_key;
    }
    
    std::string getSrtpKey() const
    {
        return srtp_key_;
    }
    
    void setTraceId(uint64_t traceid)
    {
        traceid_ = traceid;
    }
    
    uint64_t getTraceId() const
    {
        return traceid_;
    }

    void SetRtpConfuse(int rtp_confuse)
    {
        rtp_confuse_ = rtp_confuse;
    }
    
    int GetRtpConfuse() const
    {
        return rtp_confuse_;
    }

    void SetCameraName(const std::string& camera_name)
    {
        camera_name_ = camera_name;
    }
    
    std::string GetCameraName() const
    {
        return camera_name_;
    }

    void SetStreamID(int stream_id)
    {
        stream_id_ = stream_id;
    }
    
    int GetStreamID() const
    {
        return stream_id_;
    }

private:
    int port_;
    std::string remote_ip_;
    std::string remote_ipv6_;
    
    std::string mac_;     //设备mac或三方摄像头的uuid
    std::string dev_mac_; //设备mac,绑定三方摄像头时即为对应门口机的mac
    int type_;//csmain::RtspType, kRtspStop = 0,
    std::string ssrc_;

    int is_third_;
    int video_pt_;        //三方摄像头pt值
    std::string video_type_; //三方摄像头媒体格式
    std::string video_fmtp_; //三方摄像头fmtp值
    
    std::string transfer_door_uuid_;  //西班牙转流为门口机的sip, hager转流为为门口机的mac
    std::string transfer_indoor_mac_; // 转流室内机的mac

    std::string srtp_key_; // rtp加密的类型和key

    uint64_t traceid_;
    int rtp_confuse_;
    std::string camera_name_; //Main = 主摄  Auxiliary = 辅摄
    int stream_id_; //流标识，从1开始
};

class CRtspKeepNotifyMsg : public CNotifyMsg
{
public:

    CRtspKeepNotifyMsg() = default;

    CRtspKeepNotifyMsg(const std::string& mac, const std::string& dev_mac, int is_third, uint64_t traceid)
        : is_third_(is_third), mac_(mac), dev_mac_(dev_mac),traceid_(traceid)
    {

    }

    CRtspKeepNotifyMsg(const CRtspKeepNotifyMsg& other)
    {
        is_third_ = other.is_third_;
        mac_ = other.mac_;
        dev_mac_ = other.dev_mac_;
        transfer_door_uuid_ = other.transfer_door_uuid_;
        transfer_indoor_mac_ = other.transfer_indoor_mac_;
        traceid_ = other.traceid_;
    }

    ~CRtspKeepNotifyMsg()
    {

    }
    int NotifyMsg();

    std::string getDevMac() const
    {
        return dev_mac_;
    }
    
    void setDevMac(const std::string& dev_mac)
    {
        dev_mac_ = dev_mac;
    }
    
    int getIsthird() const
    {
        return is_third_;
    }
    
    void setIsthird(int is_third)
    {
        is_third_ = is_third;
    }
    
    void setMac(const std::string& mac)
    {
        mac_ = mac;
    }
    
    std::string getMac() const
    {
        return mac_;
    }
    
    void setTransferDoorUUID(const std::string& transfer_door_uuid)
    {
        transfer_door_uuid_ = transfer_door_uuid;
    }
    
    std::string getTransferDoorUUID() const
    {
        return transfer_door_uuid_;
    }
    
    void setTransferIndoorMac(const std::string& transfer_indoor_mac)
    {
        transfer_indoor_mac_ = transfer_indoor_mac;
    }
    
    std::string getTransferIndoorMac() const
    {
        return transfer_indoor_mac_;
    }
    
    uint64_t getTraceId() const
    {
        return traceid_;
    }
    
    void SetCameraName(const std::string& camera_name)
    {
        camera_name_ = camera_name;
    }
    
    std::string GetCameraName() const
    {
        return camera_name_;
    }

    void SetStreamID(int stream_id)
    {
        stream_id_ = stream_id;
    }
    
    int GetStreamID() const
    {
        return stream_id_;
    }

private:
    int is_third_;
    std::string mac_;     //设备mac或三方摄像头的uuid
    std::string dev_mac_; //三方摄像头绑定的设备mac
    std::string transfer_door_uuid_;  //西班牙转流为门口机的sip, hager转流为为门口机的mac
    std::string transfer_indoor_mac_; // 转流室内机的mac
    std::string camera_name_; //Main = 主摄  Auxiliary = 辅摄
    int stream_id_; //流标识，从1开始
    uint64_t traceid_;
};


//个人终端用户,联动单元设备、app发生变更
class CPersonnalNodeChangeNotifyMsg : public CNotifyMsg
{
public:
    CPersonnalNodeChangeNotifyMsg() = default;
    CPersonnalNodeChangeNotifyMsg(const std::string& node)
    {
        node_ = node;
    }
    CPersonnalNodeChangeNotifyMsg(const CPersonnalNodeChangeNotifyMsg& other)
    {
        node_ = other.node_;
    }

    ~CPersonnalNodeChangeNotifyMsg()
    {

    }
    std::string getNode() const
    {
        return node_;
    }
    int NotifyMsg();
private:
    std::string node_;
};

//社区 用户,联动单元设备、app发生变更
class CCommunityNodeChangeNotifyMsg : public CNotifyMsg
{
public:
    CCommunityNodeChangeNotifyMsg() = default;

    CCommunityNodeChangeNotifyMsg(const CSP2A_COMMUNITY_UPDATE_NODE* updateNode)
    {
        memcpy(&m_stUpdateNode, updateNode, sizeof(m_stUpdateNode));
    }

    ~CCommunityNodeChangeNotifyMsg()
    {

    }
    int NotifyMsg();
private:
    CSP2A_COMMUNITY_UPDATE_NODE m_stUpdateNode;
};



//个人终端用户alarm处理通知消息
class CPersonnalAlarmDealMsg : public CNotifyMsg
{
public:
    CPersonnalAlarmDealMsg()
    {
        memset(&m_stAlarmDealMsg, 0, sizeof(m_stAlarmDealMsg));
    }
    CPersonnalAlarmDealMsg(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& AlarmMsg,
                           const std::string& mac,
                           const std::string& location,
                           const std::string& alarm)
        : mac_(mac)
        , dev_location_(location)
        , alarm_msg_(alarm)
    {
        memcpy(&m_stAlarmDealMsg, &AlarmMsg, sizeof(m_stAlarmDealMsg));
    }
    CPersonnalAlarmDealMsg(const CPersonnalAlarmDealMsg& other)
    {
        memcpy(&m_stAlarmDealMsg, &(other.m_stAlarmDealMsg), sizeof(m_stAlarmDealMsg));
        mac_ = other.mac_;
        dev_location_ = other.dev_location_;
        alarm_msg_ = other.alarm_msg_;
    }
    ~CPersonnalAlarmDealMsg()
    {

    }
    int NotifyMsg();
private:
    SOCKET_MSG_PERSONNAL_ALARM_DEAL m_stAlarmDealMsg;
    std::string mac_;
    std::string dev_location_;//发出alarm设备的location
    std::string alarm_msg_;

};

class CPersonnalMotionNotifyMsg : public CNotifyMsg
{
public:
    CPersonnalMotionNotifyMsg() = default;


    CPersonnalMotionNotifyMsg(const SOCKET_MSG_MOTION_ALERT_SEND& other)
    {
        proto_ = other.protocal;
        mac_ = other.mac;
        node_ = other.node;
        dev_location_ = other.location;
        id_ = other.id;
        time_ = other.capture_time;
        sip_account_ = other.sip_account;
    }
    ~CPersonnalMotionNotifyMsg()
    {

    }
    int NotifyMsg();
private:
    std::string proto_;
    std::string mac_;
    std::string node_;
    std::string dev_location_;//设备的location
    std::string time_;//生成capture时间
    std::string sip_account_;
    uint32_t id_;//capture 插入数据库的id
};

class CPerTextNotifyMsg : public CNotifyMsg
{
public:
    enum ClientType
    {
        DEV_SEND = 1,  //设备发送
        APP_SEND = 2,  //app发送
    };
    enum MessageType
    {
        TEXT_MSG = 0,  
        DELIVERY_MSG = 1,  
        TMPKEY_MSG = 2,
        DELIVERY_BOX_MSG = 3,  //用于JTS
        YALE_BATTERY_2WEEK = 4,
        YALE_BATTERY_1WEEK = 5,
        YALE_BATTERY_LOW = 6,
        VOICE_MSG = 7,
        BOOKING_MSG = 8, //booking预约信息
        DOAMAKABA_BATTERY_LOW = 9, //dormakaba低电量通知
        AKUBELA_LOCK_BATTERY_NOTICE = 10, //家居智能锁电量通知
        TRAILERROR_NOTICE =11, //连续试错密码通知
        ITEC_BATTERY_LOW_5 = 12, //itec锁电量低于百分5通知
        ITEC_BATTERY_LOW_10 = 13, //itec锁电量低于百分10通知
        ITEC_BATTERY_LOW_15 = 14, //itec锁电量低于百分15通知

        
    };
public:
    CPerTextNotifyMsg() = default;

    CPerTextNotifyMsg(const CPerTextNotifyMsg& other)
    {
        client_type_ = other.client_type_;
        account_ = other.account_;
        text_msg_info_ = other.text_msg_info_;
    }

    CPerTextNotifyMsg(const SOCKET_MSG_SEND_TEXT_MESSAGE& text_msg, const std::string& account)
    {
        client_type_ = text_msg.client_type;
        account_ = account;
        text_msg_info_ = text_msg.text_message;
    }
    ~CPerTextNotifyMsg()
    {

    }
    int NotifyMsg();
    std::string GetMsgId(uint32_t msg_id, int role);
private:
    int client_type_;//联动系统里面设备的类型
    std::string account_;
    SOCKET_MSG_TEXT_MESSAGE text_msg_info_;
};

class CFaceDataNotifyMsg : public CNotifyMsg
{
public:
    CFaceDataNotifyMsg() = default;
    CFaceDataNotifyMsg(const SOCKET_MSG_DEV_REPORT_FACE_DATA& text_msg, const std::string& mac_list)
    {
        memcpy(&face_data_msg_, &text_msg, sizeof(face_data_msg_));
        mac_list_ = mac_list;
    }
    CFaceDataNotifyMsg(const CFaceDataNotifyMsg& other)
    {
        memcpy(&face_data_msg_, &other.face_data_msg_, sizeof(face_data_msg_));
        mac_list_ = other.mac_list_;
    }
    ~CFaceDataNotifyMsg()
    {

    }

    int NotifyMsg();
private:
    SOCKET_MSG_DEV_REPORT_FACE_DATA face_data_msg_;
    std::string mac_list_;
};

class CHttpReqNotifyMsg : public CNotifyMsg
{
public:
    enum HTTP_DATA_TYPE
    {
        FORM = 0,  
        JSON = 1,  
    };

    enum NOTIFY_HTTP_REQ_TYPE
    {
        GET_S3_URL = 1,    
    };
    enum HTTP_REQ_TYPE
    {
        GET = 1,
        POST = 2,    
    };


public:
    CHttpReqNotifyMsg() = default;
    CHttpReqNotifyMsg(const std::string &url, const std::string &data, int data_type = CHttpReqNotifyMsg::FORM)
    {
        url_ = url;
        data_ = data;
        data_type_ = data_type;
        http_type_ = HTTP_REQ_TYPE::POST;
    }

    CHttpReqNotifyMsg(const std::string &url, model::HttpRespuestKV &parma_kv, const AkcsKv &context_kv, NOTIFY_HTTP_REQ_TYPE type)
    {
        url_ = url;
        context_kv_ = context_kv;
        req_type_ = type;
        http_type_ = HTTP_REQ_TYPE::GET;
        parma_kv_ = parma_kv;
    }
    

    CHttpReqNotifyMsg(const CHttpReqNotifyMsg& other)
    {
        url_ = other.url_;
        data_ = other.data_;
        data_type_ = other.data_type_;
        context_kv_ = other.context_kv_;
        req_type_ = other.req_type_;
        http_type_ = other.http_type_;
        parma_kv_ = other.parma_kv_;
    }

    ~CHttpReqNotifyMsg()
    {

    }

    int NotifyMsg();
private:
    std::string url_;
    std::string data_;
    int data_type_;
    AkcsKv context_kv_;
    NOTIFY_HTTP_REQ_TYPE req_type_; 
    HTTP_REQ_TYPE http_type_;
    model::HttpRespuestKV parma_kv_;
};

class CAwsAlarmNotifyMsg : public CNotifyMsg
{
public:
    CAwsAlarmNotifyMsg() = default;
    CAwsAlarmNotifyMsg(const SOCKET_MSG_NORMAL& msg, const evpp::TCPConnPtr& conn)
    {
        memcpy(&msg_, &msg, sizeof(msg_));
        conn_ = conn;
    }

    CAwsAlarmNotifyMsg(const CAwsAlarmNotifyMsg& other)
    {
        memcpy(&msg_, &other.msg_, sizeof(msg_));
        conn_ = other.conn_;
    }

    ~CAwsAlarmNotifyMsg()
    {

    }

    int NotifyMsg();
private:
    SOCKET_MSG_NORMAL msg_;
    WeakTCPConnPtr conn_;
};






#endif //__NOTIFY_MSG_CONTROL_H__

