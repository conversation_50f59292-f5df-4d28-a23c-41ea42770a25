#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "SDMCMsg.h"
#include "PushClient.h"
#include "AkcsServer.h"
#include "AES128.h"
#include "AkcsWebMsgSt.h"
#include "PersonalAccount.h"
#include <boost/algorithm/string.hpp>
#include "InnerSt.h"

extern AKCS_CONF gstAKCSConf;
CPushClient::CPushClient(evpp::EventLoop* loop,
                         const std::string& serverAddr/*ip:port*/,
                         const std::string& name)
    : client_(loop, serverAddr, name)
    , addr_(serverAddr)
{

    client_.SetConnectionCallback(
        std::bind(&CPushClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&CPushClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(5.0));
    client_.set_auto_reconnect(true);
}

bool CPushClient::IsConnStatus()
{
    return connect_status_ == true;
}

std::string CPushClient::GetAddr()
{
    return addr_;
}

void CPushClient::buildPushMsg(int MobileTyp, const std::string& token, int msgType, const AppOfflinePushKV& kv, std::string oem)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }
    item["ver"] = PUSH_SERVER_VER;
    if (MobileTyp == csmain::APP_IOS)
    {
        item["app_type"] = "ios";
    }
    else if (MobileTyp == csmain::APP_ANDROID_FCM)
    {
        item["app_type"] = "fcm";
    }
    else if (MobileTyp == csmain::APP_ANDROID_HUAWEI)
    {
        item["app_type"] = "android_huawei";
    }
    else if (MobileTyp == csmain::APP_ANDROID_XIAOMI)
    {
        item["app_type"] = "android_xiaomi";
    }
    else if (MobileTyp == csmain::APP_ANDROID_OTHERS)
    {
        item["app_type"] = "fcm";   //兼容原先上报fcm用的此枚举
    }
    else if (MobileTyp == csmain::APP_ANDROID_OPPO)
    {
        item["app_type"] = "android_oppo";
    }
    else if (MobileTyp == csmain::APP_ANDROID_VIVO)
    {
        item["app_type"] = "android_vivo";
    }
    else if (MobileTyp == csmain::APP_ANDROID_FLYME)
    {
        item["app_type"] = "android_flyme";
    }
    else if (MobileTyp == csmain::APP_ANDROID_JPUSH)
    {
        item["app_type"] = "android_jpush";
    }

    itemData["token"] = token;

    if (msgType == csmain::PUSH_MSG_TYPE_CALL)
    {
        itemData["msg_type"] = "CALL";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_ALARM)
    {
        itemData["msg_type"] = "ALARM";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_DEALALARM)
    {
        itemData["msg_type"] = "DEALALARM";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_MOTION)
    {
        itemData["msg_type"] = "MOTION";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_FORCE_LOGOUT)
    {
        itemData["msg_type"] = "FORCELOGOUT";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_DELIVERY || msgType == csmain::PUSH_MSG_TYPE_DELIVERY_BOX)
    {
        itemData["msg_type"] = "DELIVERY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_TMPKEY)
    {
        itemData["msg_type"] = "TMPKEY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_TEXT || msgType == csmain::PUSH_MSG_TYPE_BOOKING)
    {
        itemData["msg_type"] = "TEXT_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_VOICE_MSG)
    {
        itemData["msg_type"] = "VOICE_MSG";
	}
    else if (msgType == csmain::PUSH_MSG_TYPE_YALE_BATTERY)
    {
        itemData["msg_type"] = "YALE_BATTERY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST)
    {
        itemData["msg_type"] = "TRIGGER_CSPUSH_TEST";
    }    
    else if (msgType == csmain::PUSH_MSG_TYPE_DORMAKABA_BATTERY)
    {
        itemData["msg_type"] = "DORMAKABA_BATTERY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_ITEC_BATTERY)
    {
        itemData["msg_type"] = "BATTERY_MSG";
    }
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }

    std::stringstream logs;
    logs << "[PushMsg] type:" << item["app_type"] << " msgtype:" << itemData["msg_type"] << " token:" << token << " strong alarm switch:" << itemData["enable_strong_reminder"] << " traceid:" << itemData["traceid"];
    std::string log = logs.str();
    boost::algorithm::replace_all(log, "\n", "");
    LOG_INFO << log;
    if (token.empty())
    {
        return;
    }

    std::string data_json = wData.write(itemData);
    LOG_INFO << "[PushMsg] data_json is:" << data_json;
    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }

    std::string msg_json = w.write(item);

    PushMsg(msg_json);
    return;
}

void CPushClient::buildPushMsgCall(const CMobileToken &apptoken, int is_voip, const uint64_t traceid, const AppOfflinePushKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    int app_type = apptoken.MobileType();
    int dclientver = apptoken.CommonVersion();
    std::string oem = apptoken.OemName();
    std::string token = apptoken.Token();
    std::string language = apptoken.Language();
    
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }

    if (app_type == csmain::APP_IOS)
    {
        if (is_voip)
        {
            token = apptoken.VoipToken();
        }
        else
        {
            token = apptoken.Token();
        }
    }
    else
    {
        token = apptoken.FcmToken();
    }

    item["ver"] = PUSH_SERVER_VER;
    if (app_type == csmain::APP_IOS)
    {
        item["app_type"] = "ios";
        if (is_voip)
        {
            token = apptoken.VoipToken();
        }
        else
        {
            token = apptoken.Token();
        }
    }
    else if (app_type == csmain::APP_ANDROID_FCM)
    {
        item["app_type"] = "fcm";
        token = apptoken.FcmToken();
    }
    else if (app_type == csmain::APP_ANDROID_HUAWEI)
    {
        item["app_type"] = "android_huawei";
    }
    else if (app_type == csmain::APP_ANDROID_XIAOMI)
    {
        item["app_type"] = "android_xiaomi";
    }
    else if (app_type == csmain::APP_ANDROID_OTHERS)
    {
        item["app_type"] = "fcm";   ////兼容原先上报类型
    }
    else if (app_type == csmain::APP_ANDROID_OPPO)
    {
        item["app_type"] = "android_oppo";
    }
    else if (app_type == csmain::APP_ANDROID_VIVO)
    {
        item["app_type"] = "android_vivo";
    }
    else if (app_type == csmain::APP_ANDROID_FLYME)
    {
        item["app_type"] = "android_flyme";
    }
    else if (app_type == csmain::APP_ANDROID_JPUSH)
    {
        item["app_type"] = "android_jpush";
    }

    itemData["token"] = token;
    itemData["msg_type"] = "CALL";
    itemData["is_voip"] =  is_voip;
    itemData["dclient"] =  dclientver;
    itemData["language"] = language;
    itemData["app_oem"] = apptoken.AppOem();

    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    
    //生成traceid:
    char traceid_tmp[20] = {0};
    ::snprintf(traceid_tmp, 20, "%ld", traceid);
    itemData["traceid"] = traceid_tmp;

    std::stringstream logs;
    logs << "[PushMsg] voip call type:" << item["app_type"] << " msgtype: CALL" << " token:" << token << " language:" << language << " app_oem:" << itemData["app_oem"] << ",calltime:" << itemData["timestamp"] << " traceid:" << traceid;
    std::string log = logs.str();
    boost::algorithm::replace_all(log, "\n", "");
    LOG_INFO << log;
    if (token.empty())
    {
        return;
    }

    std::string data_json = wData.write(itemData);
    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }

    std::string msg_json = w.write(item);

    PushMsg(msg_json);
    return;
}

void CPushClient::buildPushMsgHangup(const CMobileToken &apptoken, const uint64_t traceid, const AppOfflinePushKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    
    std::string token;
    if(apptoken.MobileType() == csmain::AppType::APP_IOS)
    {
        token = apptoken.Token();        
        item["app_type"] = "ios";
    }
    //sdk需支持安卓的hangup
    else
    {
        token = apptoken.FcmToken();        
        item["app_type"] = "fcm";
    }
    
    int dclientver = apptoken.CommonVersion();
    std::string language = apptoken.Language();
    std::string oem = apptoken.OemName();

    if (token.empty())
    {
        LOG_WARN << "push hangup, token is empty";
        return;
    }
    
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }

    item["ver"] = PUSH_SERVER_VER;
    itemData["token"] = token;
    itemData["msg_type"] = "HANGUP";
    itemData["is_voip"] =  0;
    itemData["dclient"] =  dclientver;
    itemData["language"] = language;
    itemData["app_oem"] = apptoken.AppOem();

    //生成traceid:
    char traceid_tmp[20] = {0};
    ::snprintf(traceid_tmp, 20, "%ld", traceid);
    itemData["traceid"] = traceid_tmp;
    
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
   
    std::string data_json = wData.write(itemData);
    LOG_INFO << "[PushMsg] data_json is:" << data_json;

    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }

    std::string msg_json = w.write(item);

    PushMsg(msg_json);
    return;
}


void CPushClient::PushMsg(const std::string& msg_json)
{  
    auto c = client_.conn();
    if (c && c->IsConnected())
    {
        c->Send(msg_json);
    }
    else
    {
        AK_LOG_INFO << "[PushMsg] conn is error";
    }
}

