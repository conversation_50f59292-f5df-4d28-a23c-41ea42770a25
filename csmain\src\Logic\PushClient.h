#ifndef __CSMAIN_PUSH_CLIENT_H__
#define __CSMAIN_PUSH_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkLogging.h"
#include "AKUserMng.h"

namespace csmain
{
enum PushMsgType
{
    PUSH_MSG_TYPE_CALL = 0,
    PUSH_MSG_TYPE_ALARM,
    PUSH_MSG_TYPE_DEALALARM,
    PUSH_MSG_TYPE_MOTION,
    PUSH_MSG_TYPE_FORCE_LOGOUT,
    PUSH_MSG_TYPE_DELIVERY,
    PUSH_MSG_TYPE_TMPKEY,
    PUSH_MSG_TYPE_TEXT,
    PUSH_MSG_TYPE_DELIVERY_BOX, //用于JTS
    PUSH_MSG_TYPE_VOICE_MSG,
    PUSH_MSG_TYPE_YALE_BATTERY,
    PUSH_MSG_TYPE_HUNGUP,
    PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST,//测试push服务是否正常
    PUSH_MSG_TYPE_BOOKING, //用于booking预约的消息
    PUSH_MSG_TYPE_DORMAKABA_BATTERY,
    PUSH_MSG_TYPE_ITEC_BATTERY,  //itec锁低电量通知
};

}
#define PUSH_SERVER_VER "1"


typedef std::map<std::string/*key*/, std::string/*value*/> AppOfflinePushKV;
class CPushClient;
typedef std::shared_ptr<CPushClient> PushClientPtr;

class CPushClient
{
public:
    CPushClient(evpp::EventLoop* loop,
                const std::string& serverAddr/*ip:port*/,
                const std::string& name);

    void Start()
    {
        client_.Connect();
        client_.set_auto_reconnect(true);
    }

    void Stop()
    {
        client_.set_auto_reconnect(false);    
        if (connect_status_ == true)
        {
            client_.Disconnect();
            connect_status_ = false;
        }
    }


    void ReConnectByNewSeverAddr(const std::string& serverAddr)
    {
        addr_ = serverAddr;
        client_.ReconnectByNewServerAddr(serverAddr);
    }

    bool IsConnStatus();
    std::string GetAddr();

    void buildPushMsgCall(const CMobileToken &apptoken, int is_voip, const uint64_t traceid, const AppOfflinePushKV& kv);
    void buildPushMsgHangup(const CMobileToken &apptoken, const uint64_t traceid, const AppOfflinePushKV& kv);
    void buildPushMsg(int MobileTyp, const std::string& token, int msgType, const AppOfflinePushKV& kv, std::string oem = "");
    void PushMsg(const std::string& msg_json);

private:
    void OnConnection(const evpp::TCPConnPtr& conn)
    {
        if (conn->IsConnected())
        {
            connect_status_ = true;
            AK_LOG_INFO << "connect to push server " << addr_ << " successful.";
        }
        else
        {
            connect_status_ = false;
            AK_LOG_WARN << "disconnect to push server ";
        }
    }
    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
    {

    }

private:
    evpp::TCPClient client_;
    std::atomic<bool> connect_status_;
    std::string addr_;
};

#endif // __CSMAIN_PUSH_CLIENT_H__
