#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "SDMCMsg.h"
#include "AkcsServer.h"
#include "RouteClient.h"
#include "GroupMsgMng.h"
#include <boost/algorithm/string.hpp>
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "DeviceControl.h"
#include "OfficeRouteMessage.h"
#include "AkcsCommonDef.h"
#include "zipkin/ZipkinConf.h"
#include "zipkin/AkcsZipkin.h"
#include "InnerSt.h"
#include "MsgIdToMsgName.h"


extern AKCS_ZIPKIN_CONF g_zipkin_conf;
extern AKCS_CONF gstAKCSConf;

CRouteClient::CRouteClient(evpp::EventLoop* loop,
                           const std::string& serverAddr/*ip:port*/,
                           const std::string& name,
                           const std::string& logic_srv_id)
    : client_(loop, serverAddr, name)
    , addr_(serverAddr)
    , route_codec_(std::bind(&CRouteClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2))
    , logic_srv_id_(logic_srv_id)
    , ping_status_(true)

{

    client_.SetConnectionCallback(
        std::bind(&CRouteClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&AkcsIpcMsgCodec::OnMessage, &route_codec_, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(2.0));
    client_.set_auto_reconnect(true);
    loop->RunEvery(evpp::Duration(121.0), std::bind(&CRouteClient::onRoutePingCheckTimer, this));//121.0:保证csroute有两个周期的ping
}

void CRouteClient::Stop()
{
    if (connect_status_ == true)
    {
        client_.Disconnect();
        connect_status_ = false;
    }
    client_.set_auto_reconnect(false);
}

void CRouteClient::OnConnection(const evpp::TCPConnPtr& conn)
{
    if (conn->IsConnected())
    {
        AK_LOG_INFO << "connect to route server successful, " << conn->AddrToString();
        connect_status_ = true;
        //注册srv-id
        AK::Server::LogicSrvReg msg_logic_srv_reg;
        msg_logic_srv_reg.set_logic_srv_uid(logic_srv_id_);
        msg_logic_srv_reg.set_srv_type(AK::Base::LOGIC_CLIENT_TYPE_MAIN);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg_logic_srv_reg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50); //ver=50
        pdu.SetCommandId(AKCS_MSG_L2R_REG_UID_REQ);
        pdu.SetSeqNum(0);
        conn->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    else //参考: CRouteClientMng::UpdateRouteSrv 的逻辑处理
    {
        AK_LOG_WARN << "disconnect to route server " << conn->AddrToString();        
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", "csmain connect csroute error", AKCS_MONITOR_ALARM_MODULE_CONNECT_ERROR);

        if (connect_status_ == true)//对端先断开
        {
            connect_status_ = false;
            //modified by chenyc, 2019-05-10,因为逻辑服务器在etcd中，变成永久有效的了,所以不能再主动调用： client_.Disconnect(). 需要不断重试，
            // 只有在etcd下线的时候，才能通过stop调用，设置.set_auto_reconnect(false);
            //client_.Disconnect();//当对端主动关闭的时候,本段立马执行关闭.
        }
        else
        {
            //本端先断开的情况,也会回调这个函数,暂时不需要业务处理
        }
    }
}

void CRouteClient::OnRoutePing()
{
    ping_status_ = true;
}
void CRouteClient::onRoutePingCheckTimer()
{
    if ((ping_status_ == false) && (connect_status_ == true))
    {
        AK_LOG_WARN << "in one ping check loop, i donnot have received any ping msg from csroute, reconnect to csroute ";
        client_.Disconnect();
        client_.Connect();
        client_.set_auto_reconnect(true);
    }
    ping_status_ = false;
}

bool CRouteClient::IsConnStatus()
{
    return connect_status_ == true;
}

std::string CRouteClient::GetAddr()
{
    return addr_;
}

//csmain与csroute的tcp长连接消息,都是csroute的消息发往csmain
void CRouteClient::OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu)
{
    uint64_t traceid = pdu->GetTraceId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    uint32_t msg_id = pdu->GetCommandId();
    uint16_t project = pdu->GetProjectType();
    AK_LOG_INFO << "receive csroute srv msg, pdumsg id : [" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(msg_id)<< ", project type : [" << project << "]";
    
    //通用
    switch (msg_id)
    {
        //csroute主动发往各个逻辑服务器的消息
        case AKCS_MSG_R2L_PING_REQ:
        {
            OnRoutePing();
            return;
        }
        case AKCS_R2M_UPGRADE_DEV_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PUpgradeDevReq(pdu);
            return;
        }
        case AKCS_R2S_P2P_OFFLINE_MSG_ACK_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2POfflineResendMsgAckReq(pdu);
            return;
        }        
        case AKCS_R2S_P2P_VOICE_MSG_ACK_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PVoiceMsgAckReq(pdu);
            return;
        }
   }

    //办公
    if (project == project::OFFICE)
    {
        //办公有处理过就直接退出
        if (OfficeRouteMsgMng::Instance()->OnMessage(conn, pdu))
        {
            return;
        }
    }

    //住宅
    switch (msg_id)
    {
        //以下是route对各个逻辑服务器请求的转发
        case AKCS_M2R_GROUP_COMM_ALARM_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupCommAlarmReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupCommAlarmDealReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PER_ALARM_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupPerAlarmReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupPerAlarmDealReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PER_MOTION_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupMotionMsgReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupMngTextMsgReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PAppGetArmingReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP:
        {
            CGroupMsgMng::Instance()->HandleP2PAppGetArmingResp(pdu);
            break;
        }
        case AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PVisitorAuthorize(pdu);
            break;
        }
        case AKCS_M2R_P2P_FACE_DATA_FORWARD_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PForwardFaceData(pdu);
            break;
        }
        case AKCS_M2R_P2P_CHANGE_RELAY_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PChangeRelayReq(pdu);
            break;
        }  
        case AKCS_M2R_P2P_SEND_VOICE_MSG:
        {
            CGroupMsgMng::Instance()->HandleP2PSendVoiceMsg(pdu);
            break;
        }
        //////////rtsp；csvrtsp->csroute->csmain,消息id在route转发的地方有修改
        case AKCS_R2M_START_RTSP_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PStartRtspReq(pdu);
            break;
        }
        case AKCS_R2M_STOP_RTSP_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PStopRtspReq(pdu);
            break;
        }
        case AKCS_R2M_KEEPALIVE_RTSP_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PRtspKeepAliveMsgReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_SEND_DELIVERY_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PSendDeliveryReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PSendTmpkeyUsedReq(pdu);
            break;
        }
        //////////csadapt////////////
        case MSG_C2S_REBOOT_DEVICE:
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptRebootDevMsgReq(pdu);
            break;
        }
        case MSG_C2S_RESET_DEVICE:
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptResetDevMsgReq(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_UPDATE_NODE:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptPerNodeChangeMsgReq(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptCommNodeChangeMsgReq(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptPerAlarmDealMsgReq(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptCommAlarmDealMsgReq(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_REPORT_STATUS://对应 csadapt:MSG_P2A_PERSONNAL_ADD_DEV
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptReportStatusMsgReq(pdu);
            break;
        }
        case MSG_C2S_DEV_CHANGE:
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptDevChangeMsgReq(pdu);
            break;
        }
        case AKCS_R2M_DEL_DEV_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptDevLogOutMsgReq(pdu);
            break;
        }
        case AKCS_R2M_DEL_UID_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptUidLogOutMsgReq(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_TEXT_MSG:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptTextMsgReq(pdu);
            break;
        }   
        case MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptConfFileChangeMsgReq(pdu);
            break;
        }
        case MSG_C2S_DEV_APP_EXPIRE:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptDevAppExpireMsgReq(pdu);
            break;
        }
        case MSG_C2S_DEV_NOT_EXPIRE:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptDevNotExpireMsgReq(pdu);
            break;
        }
        case AKCS_R2M_CLEAN_DEV_CODE_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PAdaptOneCleanDeviceCodeMsgReq(pdu);
            break;
        }
        case MSG_C2S_ADD_VIDEO_STORAGE_SCHED:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptAddVsSchedMsgReq(pdu);
            break;
        }
        case MSG_C2S_DEL_VIDEO_STORAGE_SCHED:
        {
            CGroupMsgMng::Instance()->HandleGroupAdaptDelVsSchedMsgReq(pdu);
            break;
        }
        case MSG_C2S_PM_EMERGENCY_DOOR_CONTROL:
        {
            CGroupMsgMng::Instance()->HandleP2PPmEmergencyDoorControlReq(pdu);
            break;
        }
        case MSG_C2S_ALEXA_LOGIN_MSG:
        {
            CGroupMsgMng::Instance()->HandleGroupAlexaLogin(pdu);
            break;
        }
        case MSG_C2S_ALEXA_SET_ARMING_MSG:
        {
            CGroupMsgMng::Instance()->HandleP2PAlexaSetArming(pdu);
            break;
        }
        case MSG_C2S_CREATE_REMOTE_DEV_CONTORL:
        {
            CGroupMsgMng::Instance()->HandleP2PCreateRemoteDevContorl(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD:
        {
            CGroupMsgMng::Instance()->HandleNotifyFaceServerPicDownloadMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY:
        {
            CGroupMsgMng::Instance()->HandleNotifyFaceServerPicModifyMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE:
        {
            CGroupMsgMng::Instance()->HandleNotifyFaceServerPicDeleteMsg(pdu);
            break;
        }
        case MSG_C2S_REFRESH_CONN_CACHE:
        {
            CGroupMsgMng::Instance()->HandleP2PNotifyRefreshConnCache(pdu);
            break;
        }
        case MSG_C2S_CHANGE_MAIN_SITE:
        {
            CGroupMsgMng::Instance()->HandleP2PChangeMainSite(pdu);
            break;
        }
        case MSG_C2S_UPDATE_TO_DEVICE:
        {
            CGroupMsgMng::Instance()->HandleNotifyConfigUpdate(pdu);
            break;
        }
        case MSG_C2S_KEEP_OPEN_RELAY:
        {
            CGroupMsgMng::Instance()->HandleNotifyDoorControl(pdu, DoorControlType::OPEN);
            break;
        }
        case MSG_C2S_KEEP_CLOSE_RELAY:
        {
            CGroupMsgMng::Instance()->HandleNotifyDoorControl(pdu, DoorControlType::CLOSE);
            break;
        }
        case MSG_C2S_NOTIFY_FILE_CHANGE:
        {
            CGroupMsgMng::Instance()->HandleNotifyDevFileChange(pdu);
            break;            
        }        
        case MSG_C2S_REQUEST_DEV_DEL_LOG:
        {
            CGroupMsgMng::Instance()->HandleRequestDevDelLog(pdu);
            break;            
        }
        default:
        {
            AK_LOG_WARN << "csroute srv msg,invalid pdumsg id " << msg_id <<". msgname = " << MsgIdToMsgName::GetAkcsMessageName(msg_id);
        }
    }

}

