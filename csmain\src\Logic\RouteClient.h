#ifndef __CSMAIN_ROUTE_CLIENT_H__
#define __CSMAIN_ROUTE_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"

namespace csmain
{
enum RtspType
{
    kRtspStop = 0, //
    kRtspStart,
};
}

class CRouteClient;
typedef std::shared_ptr<CRouteClient> RouteClientPtr;

class CRouteClient
{
public:
    CRouteClient(evpp::EventLoop* loop,
                 const std::string& serverAddr/*ip:port*/,
                 const std::string& name,
                 const std::string& logic_srv_id);

    void Start()
    {
        client_.Connect();
    }

    void Stop();
    void ReConnectByNewSeverAddr(const std::string& serverAddr)
    {
        addr_ = serverAddr;
        client_.ReconnectByNewServerAddr(serverAddr);
    }

    bool IsConnStatus();
    void OnRoutePing();
    void onRoutePingCheckTimer();
    std::string GetAddr();
private:
    void OnConnection(const evpp::TCPConnPtr& conn);
    void OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);

private:
    evpp::TCPClient client_;
    std::string addr_;
    AkcsIpcMsgCodec route_codec_;
    std::string logic_srv_id_;
    std::atomic<bool> connect_status_;//与tcp服务器是否连接的状态标示符
    std::atomic<bool> ping_status_;//ping的状态标记
};

#endif // __CSMAIN_Route_CLIENT_H__
