#include <map>
#include <set>
#include <mutex>
#include "VideoSchedule.h"
#include "VideoSchedMng.h"
#include "BasicDefine.h"
#include "AkcsWebMsgSt.h"
#include "rpc_client.h"
#include "PersonalDevices.h"
#include "AkLogging.h"
#include "AKDevMng.h"
#include "DeviceSetting.h"

extern AKCS_CONF gstAKCSConf; //全局配置信息

extern VideoStorageClient* g_vs_client_ptr;

CVideoSchedMng* CVideoSchedMng::pInstance_ = NULL;

CVideoSchedMng* CVideoSchedMng::Instance()
{
    if (!pInstance_)
    {
        pInstance_ = new CVideoSchedMng();
    }
    return pInstance_;
}

int CVideoSchedMng::Init()
{
    return CVideoScheds::GetInstance()->DaoGetAllSchedList(once_scheds_, daily_scheds_, weekly_scheds_);
}
inline unsigned int CVideoSchedMng::DayOfWeek2Flag(unsigned int day_of_week)
{
    //assert(0 <= day_of_week <= 6);
    return week_day[day_of_week];
}

void CVideoSchedMng::TriggerVS(const std::string& mac, const std::string& node)
{
    time_t timer;
    timer = time(nullptr);
    char time_sec[16] = {0};
    ::snprintf(time_sec, 16, "%ld", timer);
    std::string uid = mac;
    uid += "_";
    uid += time_sec;
    std::string rtsp_pwd;
    DevType type = CAkDevManager::GetInstance()->GetTypeByMac(mac);
    if (type == PER_DEV)
    {
        if (GetPersonalDevicesInstance()->DaoGetMacRtspPwd(mac, rtsp_pwd) != 0)
        {
            AK_LOG_WARN << "get rtsp pwd of personal device fialed, mac is:" << mac.c_str();
            return;
        }
    }
    else if (type == PUB_DEV)
    {
        if (GetDeviceSettingInstance()->DaoGetMacRtspPwd(mac, rtsp_pwd) != 0)
        {
            AK_LOG_WARN << "get rtsp pwd of public device fialed, mac is:" << mac.c_str();
            return;
        }
    }
    else
    {
        AK_LOG_WARN << "get rtsp pwd of device fialed, mac is not exit " << mac.c_str();
        return;
    }

    LOG_INFO << "send rpc request for video storage action, mac is:" << mac.c_str()
             << ", uid is " << uid.c_str()  << ", video srv is: " << gstAKCSConf.video_server_addr;
    g_vs_client_ptr->VideoStorageAct(gstAKCSConf.csmain_outer_ip, uid,
                                     rtsp_pwd, node, VideoStorage::START_VIDEO_STORAGE);

}

//用户实时增加存储计划
int CVideoSchedMng::AddVsSched(const CSP2A_ADD_VIDEO_STORAGE_SCHED* add_video_storage_sched)
{
    uint32_t type = add_video_storage_sched->sched_type;
    std::string mac = add_video_storage_sched->mac;
    uint32_t id = add_video_storage_sched->id;
    switch (type)
    {
        case CVideoScheds::ONCE_SCHED:
        {
            LOG_INFO << "AddVsSched realtime, type is ONCE_SCHED ,begin time is " << add_video_storage_sched->begin_time
                     << " endtime is " << add_video_storage_sched->end_time << "mac is " << mac;
            OnceSched once_sched = {0};
            Snprintf(once_sched.begin_time, sizeof(once_sched.begin_time), add_video_storage_sched->begin_time);
            Snprintf(once_sched.end_time, sizeof(once_sched.end_time), add_video_storage_sched->end_time);
            std::lock_guard<std::mutex> lock(once_mutex_);
            OnceSchedIter iter = once_scheds_.find(mac);
            if (iter != once_scheds_.end())
            {
                std::map<uint32_t, OnceSched>& once_sched_pair = iter->second;
                once_sched_pair.insert(std::pair<uint32_t, OnceSched>(id, once_sched));
            }
            else
            {
                std::map<uint32_t, OnceSched> once_sched_pair;
                once_sched_pair[id] = once_sched;
                once_scheds_.insert(std::pair<std::string, std::map<uint32_t, OnceSched> >(mac, once_sched_pair));
            }

            break;
        }
        case CVideoScheds::DAILY_SCHED:
        {
            LOG_INFO << "AddVsSched realtime, type is DAILY_SCHED ,begin time is " << add_video_storage_sched->begin_time
                     << " endtime is " << add_video_storage_sched->end_time << "mac is " << mac;
            DailySched daily_sched = {0};
            Snprintf(daily_sched.begin_time, sizeof(daily_sched.begin_time), add_video_storage_sched->begin_time);
            Snprintf(daily_sched.end_time, sizeof(daily_sched.end_time), add_video_storage_sched->end_time);
            std::lock_guard<std::mutex> lock(daily_mutex_);
            DailySchedIter iter = daily_scheds_.find(mac);
            if (iter != daily_scheds_.end())
            {
                std::map<uint32_t, DailySched>& daily_sched_pair = iter->second;
                daily_sched_pair.insert(std::pair<uint32_t, DailySched>(id, daily_sched));
            }
            else
            {
                std::map<uint32_t, DailySched> daily_sched_pair;
                daily_sched_pair[id] = daily_sched;
                daily_scheds_.insert(std::pair<std::string, std::map<uint32_t, DailySched> >(mac, daily_sched_pair));
            }

            break;
        }
        case CVideoScheds::WEEKLY_SCHED:
        {
            LOG_INFO << "AddVsSched realtime, type is WEEKLY_SCHED ,begin time is " << add_video_storage_sched->begin_time
                     << " endtime is " << add_video_storage_sched->end_time << "date flag is " << add_video_storage_sched->date_flag
                     << "mac is " << mac;
            WeeklySched weekly_sched = {0};
            Snprintf(weekly_sched.begin_time, sizeof(weekly_sched.begin_time), add_video_storage_sched->begin_time);
            Snprintf(weekly_sched.end_time, sizeof(weekly_sched.end_time), add_video_storage_sched->end_time);
            weekly_sched.date_flag = add_video_storage_sched->date_flag;
            std::lock_guard<std::mutex> lock(weekly_mutex_);
            WeeklySchedIter iter = weekly_scheds_.find(mac);
            if (iter != weekly_scheds_.end())
            {
                std::map<uint32_t, WeeklySched>& weekly_sched_pair = iter->second;
                weekly_sched_pair.insert(std::pair<uint32_t, WeeklySched>(id, weekly_sched));//追加容器
            }
            else
            {
                std::map<uint32_t, WeeklySched> weekly_sched_pair;
                weekly_sched_pair[id] = weekly_sched;//新增容器
                weekly_scheds_.insert(std::pair<std::string, std::map<uint32_t, WeeklySched> >(mac, weekly_sched_pair));
            }

            break;
        }
        default:
        {
            ///
        }
    }
    return 0;

}

//用户实时删除存储计划
int CVideoSchedMng::DelVsSched(const CSP2A_DEL_VIDEO_STORAGE_SCHED*  del_video_storage_sched)
{
    uint32_t type = del_video_storage_sched->sched_type;
    std::string mac = del_video_storage_sched->mac;
    uint32_t id = del_video_storage_sched->id;
    switch (type)
    {
        case CVideoScheds::ONCE_SCHED:
        {
            std::lock_guard<std::mutex> lock(once_mutex_);
            OnceSchedIter iter = once_scheds_.find(mac);
            if (iter != once_scheds_.end())
            {
                std::map<uint32_t, OnceSched>& once_sched_pair = iter->second;
                once_sched_pair.erase(id);
            }
            else
            {
                ///
            }
            break;
        }
        case CVideoScheds::DAILY_SCHED:
        {
            std::lock_guard<std::mutex> lock(daily_mutex_);
            DailySchedIter iter = daily_scheds_.find(mac);
            if (iter != daily_scheds_.end())
            {
                std::map<uint32_t, DailySched>& daily_sched_pair = iter->second;
                daily_sched_pair.erase(id);
            }
            else
            {
                ///
            }
            break;
        }

        case CVideoScheds::WEEKLY_SCHED:
        {
            std::lock_guard<std::mutex> lock(weekly_mutex_);
            WeeklySchedIter iter = weekly_scheds_.find(mac);
            if (iter != weekly_scheds_.end())
            {
                std::map<uint32_t, WeeklySched>& weekly_sched_pair = iter->second;
                weekly_sched_pair.erase(id);
            }
            else
            {
                ///
            }
            break;
        }

        default:
        {

        }
    }
    return 0;
}


int CVideoSchedMng::DelVs(const uint32_t video_id)
{
    //启动rpc客户端进行删除动作.
    g_vs_client_ptr->DelVideoStorage(video_id);
    //刷新数据库记录由web端完成.刷新视频时长由本端完成
    return 0;
}

bool CVideoSchedMng::IsNeedTriggerVS(const std::string& mac)
{
    //按顺序校验一次性、每日、每周
    DateTime now = {0};
    DayTime time_now = {0};
    GetCurrentDateTime(&now);
    time_now.nHour = now.nHour;
    time_now.nMin = now.nMin;
    time_now.nSec = now.nSec;
    unsigned int flag = DayOfWeek2Flag(now.nDayOfWeek);
    if (InOnceSched(mac, now) || InDailySched(mac, time_now) || InWeeklySched(mac, time_now, flag))
    {
        return true;
    }
    return false;
}

bool CVideoSchedMng::InOnceSched(const std::string& mac, const DateTime& now)
{
    DateTime once_begin_time = {0};
    DateTime once_end_time = {0};
    std::lock_guard<std::mutex> lock(once_mutex_);
    OnceSchedIter iter = once_scheds_.find(mac);
    std::map<uint32_t, OnceSched>::iterator iter_one_sched;
    if (iter != once_scheds_.end())
    {
        iter_one_sched = iter->second.begin();
        for (; iter_one_sched != iter->second.end(); ++iter_one_sched)
        {
            OnceSched tmp_sched = iter_one_sched->second;
            DateTimeStr2DataTime(tmp_sched.begin_time, &once_begin_time);
            DateTimeStr2DataTime(tmp_sched.end_time, &once_end_time);
            if (CDateTimeCmp(once_begin_time) < CDateTimeCmp(now) && CDateTimeCmp(now) < CDateTimeCmp(once_end_time))
            {
                return true;
            }
        }
    }

    return false;
}

bool CVideoSchedMng::InDailySched(const std::string& mac, const DayTime& now)
{
    DayTime daily_begin_time = {0};
    DayTime daily_end_time = {0};
    std::lock_guard<std::mutex> lock(daily_mutex_);
    std::map<uint32_t, DailySched>::iterator iter_daily_sched;
    DailySchedIter iter = daily_scheds_.find(mac);
    if (iter != daily_scheds_.end())
    {
        iter_daily_sched = iter->second.begin();
        for (; iter_daily_sched != iter->second.end(); ++iter_daily_sched)
        {
            DailySched tmp_sched = iter_daily_sched->second;
            DayTimeStr2DayTime(tmp_sched.begin_time, &daily_begin_time);
            DayTimeStr2DayTime(tmp_sched.end_time, &daily_end_time);
            if (CTimeCmp(daily_begin_time) < CTimeCmp(now) && CTimeCmp(now) < CTimeCmp(daily_end_time))
            {
                return true;
            }
        }
    }

    return false;
}

bool CVideoSchedMng::InWeeklySched(const std::string& mac, const DayTime& now, const unsigned int weekly_date_flag)
{
    DayTime Weekly_begin_time = {0};
    DayTime Weekly_end_time = {0};
    std::lock_guard<std::mutex> lock(weekly_mutex_);
    std::map<uint32_t, WeeklySched>::iterator iter_weekly_sched;
    WeeklySchedIter iter = weekly_scheds_.find(mac);
    if (iter != weekly_scheds_.end())
    {
        iter_weekly_sched = iter->second.begin();
        for (; iter_weekly_sched != iter->second.end(); ++iter_weekly_sched)
        {
            WeeklySched tmp_sched = iter_weekly_sched->second;
            //确定是否命中每周的日期.
            if ((weekly_date_flag & tmp_sched.date_flag) == weekly_date_flag)
            {
                DayTimeStr2DayTime(tmp_sched.begin_time, &Weekly_begin_time);
                DayTimeStr2DayTime(tmp_sched.end_time, &Weekly_end_time);
                if (CTimeCmp(Weekly_begin_time) < CTimeCmp(now) && CTimeCmp(now) < CTimeCmp(Weekly_end_time))
                {
                    return true;
                }
            }
        }
    }
    return false;
}

