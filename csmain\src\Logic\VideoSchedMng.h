#ifndef __AKCS_VIDEO_SCHED_MNG_H__
#define __AKCS_VIDEO_SCHED_MNG_H__

#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "stdafx.h"
#include "util.h"
#include "AkcsWebMsgSt.h"

//unsigned int week_flags = 0xFF;

//const unsigned int Sun = 0x01;
//const unsigned int Mon = 0x02;
//const unsigned int Tues = 0x04;
//const unsigned int Wed = 0x08;
//const unsigned int Thur = 0x10;
//const unsigned int Fri = 0x20;
//const unsigned int Sat = 0x40;



typedef struct ONCE_SCHED
{
    char begin_time[24]; //YYYY-MM-DD HH:MM:SS
    char end_time[24];
} OnceSched;

typedef struct DAILY_SCHED
{
    char begin_time[16];//HH:MM:SS
    char end_time[16];

} DailySched;

typedef struct WEEKLY_SCHED
{
    unsigned int date_flag;
    char begin_time[16];//HH:MM:SS
    char end_time[16];//HH:MM:SS
} WeeklySched;

typedef struct VS_ADD_SCHED
{
    uint32_t id;
    uint32_t sched_type;
    uint32_t date_flag;
    char mac[16];
    char begin_time[24];//HH:MM:SS or YYYY-MM-DD HH:MM:SS
    char end_time[24];//HH:MM:SS or YYYY-MM-DD HH:MM:SS
} VsAddSched;

typedef struct VS_DEL_SCHED
{
    uint32_t id;
    uint32_t sched_type;
    char mac[16];
} VsDelSched;


typedef std::map<std::string /*mac*/, std::map<uint32_t  /*sched_id*/, OnceSched> > OnceSchedMap;
typedef OnceSchedMap::iterator OnceSchedIter;

typedef std::map<std::string /*mac*/, std::map<uint32_t, DailySched> > DailySchedMap;
typedef DailySchedMap::iterator DailySchedIter;

typedef std::map<std::string /*mac*/, std::map<uint32_t, WeeklySched> > WeeklySchedMap;
typedef WeeklySchedMap::iterator WeeklySchedIter;

class CVideoSchedMng : public boost::noncopyable
{
public:
    CVideoSchedMng()
    {}
    ~CVideoSchedMng()
    {}
    static CVideoSchedMng* Instance();
    int Init();
    bool IsNeedTriggerVS(const std::string& mac);
    unsigned int DayOfWeek2Flag(unsigned int day_of_week);
    void TriggerVS(const std::string& mac, const std::string& node);
    int AddVsSched(const CSP2A_ADD_VIDEO_STORAGE_SCHED* add_video_storage_sched);
    int DelVsSched(const CSP2A_DEL_VIDEO_STORAGE_SCHED*  del_video_storage_sched);
    int DelVs(const uint32_t video_id);

private:
    bool InOnceSched(const std::string& mac, const DateTime& now);
    bool InDailySched(const std::string& mac, const DayTime& now);
    bool InWeeklySched(const std::string& mac, const DayTime& now, const unsigned int weekly_date_flag);

private:
    static CVideoSchedMng*  pInstance_;
    std::mutex once_mutex_;
    std::mutex daily_mutex_;
    std::mutex weekly_mutex_;
    OnceSchedMap once_scheds_;
    DailySchedMap daily_scheds_;
    WeeklySchedMap weekly_scheds_;
    const uint32_t week_day[7] = {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40}; // 从周日开始，到周六结束
};


#endif //__AKCS_VIDEO_SCHED_MNG_H__

