#include <memory>
#include <iostream>
#include <string>
#include <thread>
#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include "PersonalAccount.h"
#include "AkLogging.h"
#include "CachePool.h"
#include "AkcsMsgDef.h"
#include "csmain_rpc_server.h"
#include "util.h"
#include "NotifyMsgControl.h"
#include "AKUserMng.h"
#include "AKDevMng.h"
#include "AppPushToken.h"
#include "AkcsCommonSt.h"
#include "PersonalAccount.h"
#include "dbinterface/Sip.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "MetricService.h"


extern LOG_DELIVERY gstAKCSLogDelivery;



void MainRpcServer::Run()
{
    std::string listen_net = GetEth0IPAddr() + std::string(":");
    listen_net += rpc_port_;
    std::string server_address(listen_net);
    ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service_);
    cq_ = builder.AddCompletionQueue();//可以多个的.
    server_ = builder.BuildAndStart();
    AK_LOG_INFO << "csmain grpc server listening on " << server_address;

    // Proceed to the server's main loop. 在构造函数里面触发CallData::Proceed()
    //这样不会触发多个线程,只是会触发:CallData::Proceed 中的这个流程而已 if (status_ == CREATE),
    //所以每个rpc接口都需要这个
    new CallData(&service_, cq_.get(), CSMAIN_RPC_SERVER_TYPE::QUERY_UID_STATUS);
    std::thread HandleRpcsThread(std::bind(&MainRpcServer::HandleRpcs, this));
    //新增两个rpc服务线程,避免高峰期处理能力不足
    std::thread HandleRpcsThread1(std::bind(&MainRpcServer::HandleRpcs, this));    
    std::thread HandleRpcsThread2(std::bind(&MainRpcServer::HandleRpcs, this));
    HandleRpcs();
}

void MainRpcServer::CallData::Proceed()
{
    if (status_ == CREATE)
    {
        // Make this instance progress to the PROCESS state.
        status_ = PROCESS;
        switch (s_type_)
        {
            case CSMAIN_RPC_SERVER_TYPE::QUERY_UID_STATUS:
            {
                service_->RequestQueryAppDclientStatusHandle(&ctx_, &query_uid_status_request_, &query_uid_status_responder_, cq_, cq_, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }
    }
    else if (status_ == PROCESS)
    {
        status_ = FINISH;
        new CallData(service_, cq_, this->s_type_);
        switch (s_type_)
        {
            case CSMAIN_RPC_SERVER_TYPE::QUERY_UID_STATUS:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("RpcCallFrequency_QueryUidStatus_total", 1);
                }
                
                int uid_status = CAkUserManager::GetInstance()->IsUidOnLine(query_uid_status_request_.uid());
                AK_LOG_INFO <<  "[pbx] query uid status, main site = " << query_uid_status_request_.uid() << ", trace id = " << query_uid_status_request_.msg_traceid();
                query_uid_status_reply_.set_ret(uid_status);
                query_uid_status_responder_.Finish(query_uid_status_reply_, Status::OK, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }

    }
    else
    {
        GPR_ASSERT(status_ == FINISH);
        // Once in the FINISH state, deallocate ourselves (CallData).
        delete this;
    }
}

// This can be run in multiple threads if needed.
void MainRpcServer::HandleRpcs()
{
    //TODO 当开启多线程的时候,这个必须挪到业务线程之前?
    //new CallData(&service_, cq_.get());
    void* tag;
    bool ok;
    while (true)
    {
        {
            std::lock_guard<std::mutex> lock(mtx_cq_);
            //modified by chenyc,2021-10-19,原先的代码写得不严谨,在一些场景下ok可能为false,具体可参考:gRPC源码中CompletionQueue的描述
            //TODO: 每个HandleRpcs线程单独一个cq,避免加锁与消息干扰.
            if(cq_->Next(&tag, &ok) != true || !ok)
            {
                AK_LOG_WARN << "gRPC HandleRpcs cq next operation error ";
                continue;
            }
            //GPR_ASSERT(cq_->Next(&tag, &ok));
            //GPR_ASSERT(ok);//这里断言会概率失败
        }
        static_cast<CallData*>(tag)->Proceed();
    }
}

