#include <memory>
#include <iostream>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>

#include "AK.Main.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"

using grpc::Server;
using grpc::ServerAsyncResponseWriter;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::ServerCompletionQueue;
using grpc::Status;

using AK::Main::QueryAppDclientStatusRequest;
using AK::Main::QueryAppDclientStatusReply;

using AK::Main::MainRpcSrv; //rpc服务名

class MainRpcServer
{
public:

    MainRpcServer(const std::string& port)
    {
        rpc_port_ = port;
    }
    ~MainRpcServer()
    {
        server_->Shutdown();
        // Always shutdown the completion queue after the server.
        cq_->Shutdown();
    }
    // There is no shutdown handling in this code.
    void Run();
private:
    // Class encompasing the state and logic needed to serve a request.
    class CallData
    {
    public:

    public:
        CallData(MainRpcSrv::AsyncService* service, ServerCompletionQueue* cq, CSMAIN_RPC_SERVER_TYPE s_type)
            : service_(service), cq_(cq), s_type_(s_type), query_uid_status_responder_(&ctx_), status_(CREATE)
        {
            // Invoke the serving logic right away.
            Proceed();
        }

        void Proceed();

    private:
        // The means of communication with the gRPC runtime for an asynchronous
        // server.
        MainRpcSrv::AsyncService* service_;
        // The producer-consumer queue where for asynchronous server notifications.  客户端用的是:CompletionQueue,都是生产者消费者的模型
        ServerCompletionQueue* cq_;
        // Context for the rpc, allowing to tweak aspects of it such as the use
        // of compression, authentication, as well as to send metadata back to the
        // client.
        ServerContext ctx_;
        //多个接口服务用这个来标示
        CSMAIN_RPC_SERVER_TYPE s_type_;

        QueryAppDclientStatusRequest query_uid_status_request_;
        QueryAppDclientStatusReply query_uid_status_reply_;

        // The means to get back to the client.
        //对于客户端则是: ClientAsyncResponseReader 读，都是针对reply而言的..
        ServerAsyncResponseWriter<QueryAppDclientStatusReply> query_uid_status_responder_;
        
        // Let's implement a tiny state machine with the following states.
        enum CallStatus { CREATE, PROCESS, FINISH };
        CallStatus status_;  // The current serving state.
    };

private:
    // This can be run in multiple threads if needed.
    void HandleRpcs();
private:
    std::unique_ptr<ServerCompletionQueue> cq_;//一个服务可以有多个CompletionQueue
    MainRpcSrv::AsyncService service_;//指服务接口
    std::unique_ptr<Server> server_;//指服务器
    std::mutex mtx_cq_;
    std::string rpc_port_;
};


