#include "stdafx.h"
#include <functional>
#include "DeviceControl.h"
#include "MsgControl.h"
#include "AlarmControl.h"
#include "NotifyMsgControl.h"
#include "AKUserMng.h"
#include "util.h"
#include "AKDevMng.h"
#include "csmainserver.h"
#include "CsmainAES256.h"
#include "rpc_client.h"
#include "PersonalDevices.h"
#include "csvs.grpc.pb.h"
#include "session_rpc_client.h"
#include "RouteMqProduce.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AkcsPduBase.h"
#include "AkcsOemDefine.h"
#include "CachePool.h"
#include "AkcsMonitor.h"
#include "beanstalk.hpp"
#include "AkcsServer.h"
#include "PersonnalDeviceSetting.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket_interface.h"
#include "OfficeMessageHandle.h"
#include "Main2ResidHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "DclientMsgDef.h"
#include "SafeCacheConn.h"
#include "MsgIdToMsgName.h"
#include "MetricService.h"
#include "BusinessThreadPool.h"
#include <chrono>

const char AkcsMsgCodec::ipc_ip[] = "127.0.0.1";
const char AkcsMsgCodec::ipc_ipv6[] = "[::ffff:127.0.0.1";
extern VideoStorageClient* g_vs_client_ptr;
extern AKCS_CONF gstAKCSConf;
extern SmRpcClient* g_sm_client_ptr;
extern std::string g_logic_srv_id;
extern RouteMQProduce* g_nsq_producer;
Beanstalk::Client* g_bs_client_ptr = nullptr;
extern evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter;
const static uint32_t KMaxTcpConnNum =100000;

extern BusinessThreadPool g_business_pool;

AccessServer::AccessServer(evpp::EventLoop* loop, const std::string& addr, const std::string& name, uint32_t thread_num)
    : server_(loop, addr, name, thread_num)
    , codec_(this)
{
    //codec_.SetMsgCallback(  //codec_要先初始化,才能用SetMsgCallback
    //    std::bind(&AccessServer::OnStringMessage, this, std::placeholders::_1, std::placeholders::_2));
    server_.SetRateLimiter(g_rate_limiter, gstAKCSConf.limit_switch);
    server_.SetConnectionCallback(
        std::bind(&AccessServer::OnConnection, this, std::placeholders::_1));

    //tcp应用层有消息的时候,就调用该函数
    server_.SetMessageCallback(
        std::bind(&AkcsMsgCodec::OnMessage, &codec_, std::placeholders::_1, std::placeholders::_2));
    loop->RunEvery(evpp::Duration(10.0), std::bind(&AccessServer::onHeartBeatTimer, this));
    loop->RunEvery(evpp::Duration(1.0), std::bind(&AccessServer::onOneSecondTimer, this));
    loop->RunEvery(evpp::Duration(2.0), std::bind(&AccessServer::onKeySendTimer, this));
    connectionBuckets_.resize(20); //保活200s = 10*20 设备端180s发心跳包
    connectionReportStatuBuckets_.resize(30);

    connection_alexa_dev_hb_buckets_.resize(1);
    connection_alexa_dev_hb_status_buckets_.resize(3);

    enable_send_log = 0;
}

void AccessServer::Start()
{
    server_.Init();
    server_.Start();
    //设置tcp服务被攻击时的回调函数
    codec_.Init();
}
//保活失败,服务端执行conn->close(),也会回调该函数
//11-29:TCPConn::HandleClose()中的 conn_fn_(conn)会回调该函数
void AccessServer::OnConnection(const evpp::TCPConnPtr& conn) //
{
    AK_LOG_INFO << conn->AddrToString() << " fd =  " << conn->fd() << " is "  << (conn->IsConnected() ? "UP" : "DOWN");
    if (conn->IsConnected())
    {
        //added by chenyc,2020-03-09,对服务进行硬编码的限流,csmain限制一个最大的客户端数量,超过该数值,考虑被定点攻击了
        if (connections_.size() > KMaxTcpConnNum)
        {
            conn->Close();
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", "There are more than KMaxTcpConnNum tcp clients", AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_CSMAIN);
            return;
        }

        DevicePtr dev = std::make_shared<CDevice>(conn->remote_addr());
        {
            std::lock_guard<std::mutex> lock(mutex_);
            ConnectionListIter it = connections_.find(conn); //防止app的消息回调比连接回调早到,查看:UpdatePersonnalAppNodeToLocal
            if (it == connections_.end())
            {
                connections_[conn] = dev;
            }
        }
        EntryPtr entry(new Entry(conn));
        {
            std::lock_guard<std::mutex> lock(buckets_mutex_);
            connectionBuckets_.back().insert(entry);
        }

        EntryPtr2 entry2(new Entry2(conn));
        {
            std::lock_guard<std::mutex> lock(reportstatu_buckets_mutex_);
            connectionReportStatuBuckets_.back().insert(entry2);
        }

        WeakEntryPtr weakEntry(entry);
        evpp::Any any_tmp(weakEntry);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_HB_INDEX, any_tmp);

        if (conn->remote_addr().find("[::ffff:") != std::string::npos)
        {
            evpp::Any any_tmp(0);
            conn->set_context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX, any_tmp);
        }
        else
        {
            evpp::Any any_tmp(1);
            conn->set_context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX, any_tmp);
        }

        std::string ip;
        int port = 0;
        AkParseAddr(conn->remote_addr(), ip, port);
        evpp::Any any_tmp_ip(ip);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_IP, any_tmp_ip);
        evpp::Any any_tmp_port(port);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_PORT, any_tmp_port);

        //当在TCPConn::HandleClose()中再次回调这个:conn_fn_(conn)时, conn->set_context(any_tmp)还未执行,导致
        //assert(!conn->context().IsEmpty()) 断言失败
        GetControlInstance()->OnTcpConnMsg(conn);

        //加入reportstatus记录
        {
            std::lock_guard<std::mutex> lock(reportstatu_mutex_);
            reportstatu_conns_.insert(conn);
        }
    }
    else
    {
        if (conn->context(EVPP_CONN_ANY_CONTEXT_HB_INDEX).IsEmpty())
        {
            AK_LOG_WARN << "it is impossible!";
            //return
        }
        std::string ip = evpp::any_cast<std::string>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_IP));
        //assert(!conn->context().IsEmpty());
        WeakEntryPtr weakEntry(evpp::any_cast<WeakEntryPtr>(conn->context()));
        //AK_DLOG_INFO << "now Entry use_count = " << weakEntry.use_count();
        DevicePtr dev;
        {
            std::lock_guard<std::mutex> lock(mutex_);
            dev = connections_[conn];
            connections_.erase(conn); //心跳的轮盘中保存的是conn的弱引用,不需要特殊理会,当保活时间到的时候自然会删除掉
        }
        //删除reportstatus记录
        {
            std::lock_guard<std::mutex> lock(reportstatu_mutex_);
            reportstatu_conns_.erase(conn);
        }

        if (!dev) //状态还未上报
        {
            return;
        }
        if (dev->IsDev())
        {
            //GetDeviceControlInstance()->OnDeviceDisconnectedByMac(dev->GetMAC()); //设置过csmain::PERSONNAL_DEV,一定有mac信息了
            //2017-11-15,不再使用mac来执行设备上下线的状态改变,因为当设备不是正常断开tcp连接时,服务器只能通过保活失败来判断设备下线，
            //在此之前,设备可能已经又重现上线了,造成状态又被置为下线.
            //2018-04-19,v3.3,modify by chenyc
            //GetDeviceControlInstance()->OnDeviceDisconnectedByConn(conn); //设置过csmain::PERSONNAL_DEV,一定有mac信息了
            //从mac_conns_弱引用容器中删除
            std::string mac = dev->GetMAC();
            std::lock_guard<std::mutex> lock(mac_mutex_);
            if (mac_conns_[mac].lock() == conn)  //防止设备第二次上线比第一次断网心跳结束之间的交叉
            {
                mac_conns_.erase(mac);
                //TODO 2018-01-18,设置mac下线,上面的方法是通过ip:port来判断的效率会比通过mac来判断低
                //2018-04-19,v3.3,modify by chenyc,通过mac来设置设备下线
                //GetDeviceControlInstance()->OnDeviceDisconnectedByConn(conn); //设置过csmain::PERSONNAL_DEV,一定有mac信息了

                uint64_t conn_version = evpp::any_cast<uint64_t>(conn->context(EVPP_CONN_ANY_CONTEXT_DEV_CONN_VERSION));
                int fd = conn->fd();
                g_business_pool.Enqueue(ip, [fd, conn_version, mac]() {
                    GetDeviceControlInstance()->OnDeviceDisconnectedByMac(mac, conn_version);
                    AK_LOG_INFO << "Device " << mac << " status is DOWN: fd=" << fd;

                });
            }
        }
        else if (dev->IsApp())
        {
            //从uid_conns_弱引用容器中删除
            std::lock_guard<std::mutex> lock(uid_mutex_);
            std::string uid;
            if (dev->GetPerMainSiteUid(uid) != 0)
            {
                AK_LOG_WARN << "Not App, so is error when geting UID.";
            }
            //2017-12-15,支持同一个uid对应多条tcp链路,但只持有最新的一个
            //确定当前断线的tcp连接是否与uid对应的最新一个状态上报成功的链接是否是同一个,若是,表示uid下线
            if (uid_conns_[uid].lock() == conn)
            {
                uid_conns_.erase(uid);
                g_business_pool.Enqueue(ip, [uid]() {
                    //设置app下线,执行端外推送
                    CAkUserManager::GetInstance()->SetAkUserOfflineById(uid);
                    //NotifyConnInfoMsg(dev, CONN_INFO_TYPE::DISCONNECT); //chenzhx 20250318这个状态通知后端业务没有什么用。如果开启来要注意dev的生命周期
                });
            }
        }

        std::string node_tmp;
        if (0 == dev->GetPerNodeDev(node_tmp))
        {
            std::lock_guard<std::mutex> lock(node_mutex_);
            NodeConnListIter iter = node_conns_.find(node_tmp);
            if (iter != node_conns_.end())
            {
                node_conns_[node_tmp].erase(conn);
            }
        }

        //多套房conn移除
        PersonalAccountNodeInfoMap node_infos;
        if(0 == dev->GetAppNodes(node_infos))
        {            
            std::lock_guard<std::mutex> lock(node_mutex_);
            for(const auto& node_info : node_infos)
            {                
                std::string node = node_info.second.node;
                NodeConnListIter iter = node_conns_.find(node);
                if (iter != node_conns_.end())
                {
                    node_conns_[node].erase(conn);
                }
            }
        }
        
        if (dev->Type() == csmain::COMMUNITY_APP
            || dev->Type() == csmain::COMMUNITY_DEV
            || dev->Type() == csmain::OFFICE_APP
            || dev->Type() == csmain::OFFICE_DEV)
        {
            std::lock_guard<std::mutex> lock(manage_mutex_);
            int mng_id = dev->GetMngAccountID();
            ManageConnListIter iter = manage_conns_.find(mng_id);
            if (iter != manage_conns_.end())
            {
                manage_conns_[mng_id].erase(conn);
            }
        }
    }

    // 长连接数量
    MetricService* metric_service = MetricService::GetInstance();
    if(metric_service) {
        metric_service->SetValue("csmain_tcp_conn_total", connections_.size());
    }
}

void AccessServer::ClearDevConnMap(const evpp::TCPConnPtr& conn)
{   
    DevicePtr dev;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter iter = connections_.find(conn);
        if (iter != connections_.end())
        {
            dev = connections_[conn];
        }
        else
        {
            return;     //设备连接信息已清除
        }
    }
    AK_LOG_INFO << " clear " << conn->AddrToString() << " conn map, mac is " << dev->GetMAC();
    
    std::string node_tmp;
    if (0 == dev->GetPerNodeDev(node_tmp))
    {
        std::lock_guard<std::mutex> lock(node_mutex_);
        NodeConnListIter iter = node_conns_.find(node_tmp);
        if (iter != node_conns_.end())
        {
            node_conns_[node_tmp].erase(conn);
        }
    }
    if (dev->Type() == csmain::COMMUNITY_DEV || dev->Type() == csmain::OFFICE_DEV)
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        int mng_id = dev->GetMngAccountID();
        ManageConnListIter iter = manage_conns_.find(mng_id);
        if (iter != manage_conns_.end())
        {
            manage_conns_[mng_id].erase(conn);
        }
    }
}

//alexa账号登陆，通知平台需要开启主动的心跳检测
void AccessServer::AlexaLoginAddConnDetect(const std::string& uid)
{
    std::vector<evpp::TCPConnPtr> dev_conns;
    GetDevListByNodeOnlyDev(uid, dev_conns);
    DEVICE_SETTING device_setting;
    for (auto&  conn : dev_conns)
    {
        //设备删除时连接还在但dev_setting置空，所以需要判断mac是否为空
        if (GetDeviceSettingFromConnList(conn, &device_setting) == 0 && strlen(device_setting.mac) != 0)
        {
            if (device_setting.type == DEVICE_TYPE_INDOOR && device_setting.dclient_version >= D_CLIENT_VERSION_4600)
            {
                {
                    std::lock_guard<std::mutex> lock(alexa_dev_hb_buckets_mutex_);
                    EntryAlexaHeartBeatPtr entry_hb(new EntryAlexaSendHb(conn));
                    {
                        connection_alexa_dev_hb_buckets_.back().insert(entry_hb);
                    }
                    WeakEntryAlexaHeartBeatPtr weakEntry2(entry_hb);
                    evpp::Any any_tmp2(weakEntry2);
                    conn->set_context(EVPP_CONN_ANY_CONTEXT_ALEXA_HB_INDEX, any_tmp2);
                }

                {
                    std::lock_guard<std::mutex> lock(alexa_dev_hb_status_buckets_mutex_);
                    EntryAlexaDevStatusPtr entry_status(new EntryAlexaDevStatu(conn));
                    {
                        connection_alexa_dev_hb_status_buckets_.back().insert(entry_status);
                    }
                    WeakEntryAlexaDevStatusPtr weakEntry(entry_status);
                    evpp::Any any_tmp(weakEntry);
                    conn->set_context(EVPP_CONN_ANY_CONTEXT_ALEXA_STATUS_INDEX, any_tmp);
                }
            }
        }
    }
}

//设备上线，判断是不是在有alexa用户的账号下，如果是，开启主动的心跳检测
void AccessServer::AlexaDevConnDetect(const evpp::TCPConnPtr& conn)
{
    {
        std::lock_guard<std::mutex> lock(alexa_dev_hb_buckets_mutex_);
        EntryAlexaHeartBeatPtr entry_hb(new EntryAlexaSendHb(conn));
        {
            connection_alexa_dev_hb_buckets_.back().insert(entry_hb);
        }
        WeakEntryAlexaHeartBeatPtr weakEntry2(entry_hb);
        evpp::Any any_tmp2(weakEntry2);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_ALEXA_HB_INDEX, any_tmp2);
    }

    {
        //这个锁要包括set_context的内容
        std::lock_guard<std::mutex> lock(alexa_dev_hb_status_buckets_mutex_);
        EntryAlexaDevStatusPtr entry_status(new EntryAlexaDevStatu(conn));
        {
            connection_alexa_dev_hb_status_buckets_.back().insert(entry_status);
        }
        WeakEntryAlexaDevStatusPtr weakEntry(entry_status);
        evpp::Any any_tmp(weakEntry);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_ALEXA_STATUS_INDEX, any_tmp);
    }
}

//alexa设备回复平台主动发的心跳
void AccessServer::AlexaDevHearbeatAck(const evpp::TCPConnPtr& conn)
{
    //TODO:chenzhx 耗性能，并且可能会产生内存碎片
    {
        std::lock_guard<std::mutex> lock(alexa_dev_hb_buckets_mutex_);
        EntryAlexaHeartBeatPtr entry_hb(new EntryAlexaSendHb(conn));
        {
            connection_alexa_dev_hb_buckets_.back().insert(entry_hb);
        }
        WeakEntryAlexaHeartBeatPtr weakEntry2(entry_hb);
        evpp::Any any_tmp2(weakEntry2);
        conn->set_context(EVPP_CONN_ANY_CONTEXT_ALEXA_HB_INDEX, any_tmp2);
    }

    {
        std::lock_guard<std::mutex> lock(alexa_dev_hb_status_buckets_mutex_);
        WeakEntryAlexaDevStatusPtr weakEntry(evpp::any_cast<WeakEntryAlexaDevStatusPtr>(conn->context(EVPP_CONN_ANY_CONTEXT_ALEXA_STATUS_INDEX)));
        EntryAlexaDevStatusPtr entry(weakEntry.lock());
        if (entry)
        {
            connection_alexa_dev_hb_status_buckets_.back().insert(entry);
        }
    }

}

void AccessServer::RemoveReportStatuConn(const evpp::TCPConnPtr& conn)
{
    std::lock_guard<std::mutex> lock(reportstatu_mutex_);
    reportstatu_conns_.erase(conn);
}

int AccessServer::ExistReportStatuConn(const evpp::TCPConnPtr& conn)
{
    std::lock_guard<std::mutex> lock(reportstatu_mutex_);
    std::set<evpp::TCPConnPtr>::iterator it;
    it = reportstatu_conns_.find(conn);
    if (it != reportstatu_conns_.end())
    {
        return 1;
    }
    return 0;
}



//message已经是一条完整的消息了
void AccessServer::OnStringMessage(const evpp::TCPConnPtr& conn, std::string& message)
{
    std::string ip = evpp::any_cast<std::string>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_IP));
    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)message.data();
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;
    if (MSG_FROM_DEVICE_HEART_BEAT == message_id)
    {
        GetMsgControlInstance()->OnHeartBeatMsg(conn);//心跳直接回复，业务才丢到业务队列处理。下面connectionBuckets_心跳的维护一定要处理到
    }
    else
    {
        std::chrono::steady_clock::time_point  msg_in_start = std::chrono::steady_clock::now();
        g_business_pool.Enqueue(ip, [conn, message, msg_in_start,message_id]() {

            std::chrono::steady_clock::time_point  msg_out_start = std::chrono::steady_clock::now();
            if (conn->IsConnected())
            {
                g_accSer_ptr->OnSocketMsg(conn, message);
            }
            else
            {
                AK_LOG_INFO << "conn is already disconnect. fd=" << conn->fd();
            }

            if(gstAKCSConf.msg_lantency_metric)
            {
                std::chrono::steady_clock::time_point msg_handle_end = std::chrono::steady_clock::now();
                auto duration_only_handle = std::chrono::duration_cast<std::chrono::milliseconds>(msg_handle_end - msg_out_start);
                auto duration_all = std::chrono::duration_cast<std::chrono::milliseconds>(msg_handle_end - msg_in_start);
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    std::string msg_id_name = MsgIdToMsgName::GetDeclientMessageName(message_id);
                    metric_service->AddLatencyLatencyValue("queue_handle_latency", duration_only_handle.count(), "CsmainMsg");
                    metric_service->AddLatencyLatencyValue("devmsg_handle_latency", duration_all.count(), msg_id_name);
                } 
            }  
        });        
    }


    //刷新保活时间,原则上是要在codec_里面的消息回调中保活,为避免造成类结构的侵入,挪到这里.
    //在客户端传输消息很慢时,即一个保活周期内无法完成一条完整的消息的传输时,会造成假性保活失败
    //assert(!conn->context().IsEmpty());
    WeakEntryPtr weakEntry(evpp::any_cast<WeakEntryPtr>(conn->context(EVPP_CONN_ANY_CONTEXT_HB_INDEX)));
    EntryPtr entry(weakEntry.lock());//原子操作,把它提升为强引用 EntryPtr，然后放到当前的 timing wheel 队尾。
    //保证~Entry()不被调用->不会析构,从而保证不执行conn->shutdown()->fd不会被关闭
    if (entry)
    {
        //AK_LOG_INFO << "tcp keepalive %s successful", __FUNCTIONW__, conn->remote_addr().c_str());
        {
            std::lock_guard<std::mutex> lock(buckets_mutex_); //加锁,从原理上是不需要的。。。
            connectionBuckets_.back().insert(entry);
        }
    }
}
//tcp新连接,面向设备
void AccessServer::SetTcpConnMac(const evpp::TCPConnPtr& conn, const std::string& mac, const std::string& uuid)
{
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter it = connections_.find(conn);
        if (it != connections_.end())
        {
            connections_[conn]->SetMAC(mac);
        }
    }
    //加入mac_conns_弱引用容器中
    {
        std::lock_guard<std::mutex> lock(mac_mutex_);
        //added by chenyc,2022.11.03,如果原先mac上面已经对应一个conn了且两个conn不相等,那么此时需要关闭上一个conn的连接
        MacConnListIter it = mac_conns_.find(mac);
        if(it != mac_conns_.end())
        {
            //如果这个mac持有的弱引用对应的conn跟本次上报状态的conn不是同一个conn，证明该mac已经新起了一个tcp连接，那么马上关闭旧连接
            //这样做有两点意义：1、避免同一台设备有两个tcp连接;2、更重要的是,如果发生mac地址重复,那么云上只允许保留最后上报状态的那台设备的tcp连接
            //这样就很容易出现两台mac重复的设备快速互相顶的现象,为业务上快速发现这个问题创造条件
            evpp::TCPConnPtr conn_tmp = it->second.lock();
            if (conn_tmp && conn_tmp != conn)
            {
                conn_tmp->Close();
                std::string logic_srv_ip = GetEth0IPAddr();
                dbinterface::ResidentDevices::SetDeviceDisConnTime(mac, logic_srv_ip);
                dbinterface::ResidentPerDevices::SetPerDeviceDisConnTime(mac, logic_srv_ip);
                AK_LOG_INFO << "Device " << mac << " status is DOWN: fd=" << conn_tmp->fd();
            }
        }
        mac_conns_[mac] = conn;
        AK_LOG_INFO << "Device " << mac << " status is UP: fd=" << conn->fd();
    }
    //加入dev_uuid_conns_弱引用容器中
    if(uuid.size() > 0) //为空，则是设备还未绑定到具体位置或用户
    {
        std::lock_guard<std::mutex> lock(dev_uuid_mutex_);
        dev_uuid_conns_[uuid] = conn;
    }    
}

void AccessServer::SetTcpConnList(const evpp::TCPConnPtr& conn, const DEVICE_SETTING& device_setting)
{
    if (strlen(device_setting.device_node) > 0)
    {
        std::lock_guard<std::mutex> lock(node_mutex_);
        if (!conn->IsConnected()) //因为现在io线程和业务线程分离，当前这个是在业务线程处理，和io线程的down会有并发问题，io线程down处理完了，但是业务线程后面才加入导致一直持有强引用
        {
            AK_LOG_INFO << "conn is already disconnect. fd=" << conn->fd();
            return;
        }        
        node_conns_[device_setting.device_node].insert(conn);
    }
    if (csmain::COMMUNITY_DEV == device_setting.device_type || csmain::OFFICE_DEV == device_setting.device_type)
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        if (!conn->IsConnected())
        {
            AK_LOG_INFO << "conn is already disconnect. fd=" << conn->fd();
            return;
        }        
        manage_conns_[device_setting.manager_account_id].insert(conn);
    }
}

void AccessServer::UpdateTcpConnSetting(const evpp::TCPConnPtr& conn, const DEVICE_SETTING* device_setting)
{
    std::lock_guard<std::mutex> lock(mutex_);
    ConnectionListIter it = connections_.find(conn);
    if (it != connections_.end())
    {
        if (device_setting->device_type == csmain::COMMUNITY_DEV)
        {
            connections_[conn]->SetDeviceSetting(device_setting);
            connections_[conn]->SetType(csmain::COMMUNITY_DEV); //2017-08-29,设置设备类型
        }
        else if (device_setting->device_type == csmain::PERSONNAL_DEV)
        {
            connections_[conn]->SetDeviceSetting(device_setting); //个人终端用户设备
            connections_[conn]->SetType(csmain::PERSONNAL_DEV); //标记该节点的类型
        }
        else
        {
            connections_[conn]->SetDeviceSetting(device_setting); //个人终端用户设备
            connections_[conn]->SetType(csmain::OFFICE_DEV); //标记该节点的类型
        }        
    }
}

void AccessServer::UpdateDeviceProjectInfo(const evpp::TCPConnPtr& conn, const MacInfo &mac_info)
{
    std::lock_guard<std::mutex> lock(mutex_);
    ConnectionListIter it = connections_.find(conn);
    if (it != connections_.end())
    {
        connections_[conn]->UpdateProjectInfo(mac_info);
    }
}

int AccessServer::GetDeviceSettingFromConnList(const evpp::TCPConnPtr& conn, DEVICE_SETTING* device_setting)
{
    std::lock_guard<std::mutex> lock(mutex_);
    ConnectionListIter it = connections_.find(conn);
    if (it != connections_.end())
    {
        connections_[conn]->GetDeviceSetting(device_setting);
        //设备删除时连接还在但dev_setting置空，所以需要判断mac是否为空
        if (strlen(device_setting->mac) == 0)
        {
            AK_LOG_WARN << "Dev is online but removed from cloud";
            return -1;
        }
        return 0;
    }
    return -1;
}

//更新设备的一些变动信息，目前只有location
int AccessServer::UpdateDevSomeMsgFromConnList(const evpp::TCPConnPtr& conn, DEVICE_SETTING* device_setting)
{
    std::lock_guard<std::mutex> lock(mutex_);
    ConnectionListIter it = connections_.find(conn);
    if (it != connections_.end())
    {
        if (connections_[conn]->SetDevLocation(device_setting->location) != 0)
        {
            AK_LOG_WARN << "connect is not Dev, not set location";
        }
        return 0;
    }
    return -1;
}


//获取本地的设备信息,综合社区与终端个人用户,对于alarm要分开处理
int AccessServer::GetDevSetDiffFromConnList(const evpp::TCPConnPtr& conn, DEVICE_SETTING* device_setting, evpp::Any& personnalAppSetting, int& type)
{
    DevicePtr dev;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter it = connections_.find(conn);
        if (it != connections_.end())
        {
            //dev.reset(connections_[conn]);   不能直接对智能指针reset
            dev = connections_[conn];
        }
        else
        {
            return -1;
        }
    }   //释放锁,其他线程不会影响到本device

    type = dev->Type();
    if (type == csmain::PERSONNAL_APP || type == csmain::COMMUNITY_APP)
    {
        personnalAppSetting = dev->GetContext();
    }
    else
    {
        //设备删除时连接还在但dev_setting置空，所以需要判断mac是否为空
        dev->GetDeviceSetting(device_setting);
        if (strlen(device_setting->mac) == 0)
        {
            return -1;
        }
    }
    return 0;

}

//该app账号上线了
int AccessServer::UpdatePersonnalAppNodeToLocal(const evpp::TCPConnPtr& conn, const SOCKET_MSG_PERSONNAL_APP_NODE& stAppNode, PersonalAccountNodeInfoMap& node_infos)
{    
    DevicePtr dev;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter it = connections_.find(conn);
        if (it != connections_.end())
        {
            //dev.reset(connections_[conn]);
            dev = connections_[conn];  //找到表示同一个tcp连接的app状态改变了
            dev->SetContext(stAppNode);
            //标记该节点的类型
            if (stAppNode.role == ACCOUNT_ROLE_COMMUNITY_MAIN || stAppNode.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT ||
                stAppNode.role == ACCOUNT_ROLE_COMMUNITY_PM)
            {
                dev->SetType(csmain::COMMUNITY_APP); //注意这两个的先后顺序
            }
            else if (IsOfficeRole(stAppNode.role))
            {
                dev->SetType(csmain::OFFICE_APP); 
            }
            else
            {
                dev->SetType(csmain::PERSONNAL_APP); //注意这两个的先后顺序
            }
            dev->SetAppNodes(node_infos);
        }
        else  //找不到表示tcpconn的消息回调比tcpconn的连接回调还要早,需要处理
        {
            dev = std::make_shared<CDevice>(conn->remote_addr());
            dev->SetContext(stAppNode);

            //标记该节点的类型
            if (stAppNode.role == ACCOUNT_ROLE_COMMUNITY_MAIN || stAppNode.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT ||
                stAppNode.role == ACCOUNT_ROLE_COMMUNITY_PM)
            {
                dev->SetType(csmain::COMMUNITY_APP); //注意这两个的先后顺序
            }
            else if (IsOfficeRole(stAppNode.role))
            {
                dev->SetType(csmain::OFFICE_APP); 
            }            
            else
            {
                dev->SetType(csmain::PERSONNAL_APP); //注意这两个的先后顺序
            }
            dev->SetAppNodes(node_infos);
            connections_[conn] = dev;
        }

        evpp::Any any_tmp(dev->Type());
        conn->set_context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX, any_tmp);

        auto iter = node_infos.find(stAppNode.report_user);
        if(iter != node_infos.end())
        {
            //站点切换后重新上报状态，角色可能变换，需更新conn_type
            evpp::Any any_tmp(node_infos[stAppNode.report_user].conn_type);
            conn->set_context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX, any_tmp);
        }
        
    }   //释放锁,其他线程不会影响到本device

    //加入uid_conns_弱引用容器中
    {
        std::lock_guard<std::mutex> lock(uid_mutex_);
        uid_conns_[stAppNode.user] = conn;
    }
    {
        //插入多套房的所有node中
        std::lock_guard<std::mutex> lock(node_mutex_);
        for(const auto& node_info : node_infos)
        {
            node_conns_[node_info.second.node].insert(conn);
        }
    }
    /*add by chenzhx ******** 目前不需要社区和app的关联关系，如果这里加入则dev->GetMngAccountID需要支持app
    if (csmain::COMMUNITY_APP == dev->Type() || csmain::OFFICE_APP == dev->Type())
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_[stAppNode.manager_account_id].insert(conn);
    }
    */
    return 0;
}

//以下开始为通知消息遍历客户端链接列表的函数
// todo:后续需要起一个 map<node, std::set<weak_conn> > 的容器,以加快查找统一节点下设备的效率
//查找所有的连接设备，不区别社区和个人
int AccessServer::GetDevListByNode(const std::string& node, std::vector<evpp::TCPConnPtr>& device)
{
    if (node.length() == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    NodeConnList node_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(node_mutex_);
        node_conns_tmp = node_conns_;
    }

    NodeConnListIter iter = node_conns_tmp.find(node);
    if (iter != node_conns_tmp.end())
    {
        for (auto& node_conn : node_conns_tmp[node])
        {
            device.push_back(node_conn);
        }
    }
    return 0;
}

//一下开始为通知消息遍历客户端链接列表的函数
// todo:后续需要起一个 map<node, std::set<weak_conn> > 的容器,以加快查找统一节点下设备的效率
//查找所有的连接设备，不区别社区和个人
int AccessServer::GetDevListByNodeOnlyDev(const std::string& node, std::vector<evpp::TCPConnPtr>& device)
{
    if (node.length() == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }
    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    NodeConnList node_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(node_mutex_);
        node_conns_tmp = node_conns_;
    }

    NodeConnListIter iter = node_conns_tmp.find(node);
    if (iter != node_conns_tmp.end())
    {
        for (auto& node_conn : node_conns_tmp[node])
        {
            ConnectionListIter oIter = connections_tmp.find(node_conn);
            
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->IsDev())
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}

//把连接从conns中移出,在app被挤出时候调用，因为被挤出不能调用logout，会把登陆中的uid信息擦除
//挤出时候调用这个，防止信息还好推送到
int AccessServer::RemoveAppConnFromConnections(const evpp::TCPConnPtr& conn)
{
    AK_LOG_INFO << "remove conn from conns, " << conn->remote_addr();
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_.erase(conn);
    }
    return 0;
}


//一下开始为通知消息遍历客户端链接列表的函数
// todo:后续需要起一个 map<node, std::set<weak_conn> > 的容器,以加快查找统一节点下设备的效率
//个人+梯口+最外层
int AccessServer::GetDevListCommunityPublicAndPersonnal(const std::string& node, uint32_t unit_id, uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (node.length() == 0 || unit_id == 0 || manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if ((Device->GetAreaNode() == node)
                        || (Device->GetUnitID() == unit_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                        || (Device->GetMngAccountID() == manager_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC))
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}

int AccessServer::GetDevListCommunityUnitPublic(uint32_t manager_id, uint32_t unit_id, std::vector<std::string>& mac_vec)
{
    if (unit_id == 0 || manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->GetUnitID() == unit_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                {
                    std::string mac = Device->GetMAC();
                    mac_vec.push_back(mac);
                }
            }
        }
    }
    return 0;
}

/*获取单元下的个人设备+V4.4管理中心机下的所有的公共设备 TODO:目前只管是公共设备不管是否是管理中心机*/
int AccessServer::GetDevListCommunityUnderUnitDevAndAllPubDev(uint32_t manager_id, uint32_t unit_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (unit_id == 0 || manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {        
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if ((Device->GetUnitID() == unit_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                        || (Device->GetMngAccountID() == manager_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
                        || (Device->GetUnitID() == unit_id && Device->GetDevType() == DEVICE_TYPE_INDOOR)
                        || (Device->GetUnitID() == unit_id && Device->IsApp()))
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}
/*获取整个小区的个人设备*/
int AccessServer::GetDevListCommunityAllDevAndAPP(uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            device.push_back(community_conn);
        }
    }
    return 0;
}

/*获取整个单元的设备*/
int AccessServer::GetDevListCommunityAllUnitDevAndAPP(uint32_t manager_id, uint32_t unit_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (unit_id == 0 || manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->GetUnitID() == unit_id)
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}

int AccessServer::GetDevListCommunityUnitPublicDev(uint32_t manager_id, uint32_t unit_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (unit_id == 0 || manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->GetUnitID() == unit_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}

//获取最外围公共设备
int AccessServer::GetDevListCommunityPublicDev(uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->GetMngAccountID() == manager_id && Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}


//所有公共设备：最外层+所有楼栋公共设备
int AccessServer::GetDevListCommunityAllPubDev(uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device)
{
    if (manager_id == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    ManageConnList manage_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(manage_mutex_);
        manage_conns_tmp = manage_conns_;
    }

    ManageConnListIter iter = manage_conns_tmp.find(manager_id);
    if (iter != manage_conns_tmp.end())
    {
        for (auto& community_conn : manage_conns_tmp[manager_id])
        {
            ConnectionListIter oIter = connections_tmp.find(community_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->GetMngAccountID() == manager_id && (
                    Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || Device->GetDevGrade() == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT))
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}


//获取统一联动系统下,app的conn
int AccessServer::GetAppListByNode(const std::string& node, std::vector<evpp::TCPConnPtr>& device)
{
    if (node.length() == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    NodeConnList node_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(node_mutex_);
        node_conns_tmp = node_conns_;
    }

    NodeConnListIter iter = node_conns_tmp.find(node);
    if (iter != node_conns_tmp.end())
    {
        for (auto& node_conn : node_conns_tmp[node])
        {
            ConnectionListIter oIter = connections_tmp.find(node_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->IsApp())
                {
                    device.push_back(oIter->first);
                }
            }
        }
    }
    return 0;
}

//获取统一联动系统下,app的conn-app 关联表
//改成和个人终端无关的 chenzhx
int AccessServer::GetAppByNode(const std::string& node, ConnectionList& conn_apps)
{
    if (node.length() == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return 0;
    }

    ConnectionList connections_tmp;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connections_tmp = connections_;
    }
    NodeConnList node_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(node_mutex_);
        node_conns_tmp = node_conns_;
    }

    NodeConnListIter iter = node_conns_tmp.find(node);
    if (iter != node_conns_tmp.end())
    {
        for (auto& node_conn : node_conns_tmp[node])
        {
            ConnectionListIter oIter = connections_tmp.find(node_conn);
            if (oIter != connections_tmp.end())
            {
                DevicePtr& Device = oIter->second;
                if (Device->IsApp())
                {
                    conn_apps.insert(std::pair<evpp::TCPConnPtr, DevicePtr>(oIter->first, oIter->second));
                }
            }
        }

    }
    return 0;
}

int AccessServer::GetDevConnByMainUid(const std::string& main_uid, evpp::TCPConnPtr& conn)
{
    int ret = -1;
    std::lock_guard<std::mutex> lock(uid_mutex_);
    WeakTCPConnPtrIter it = uid_conns_.find(main_uid);
    if (it != uid_conns_.end())
    {
        conn = it->second.lock();
        //该链接已经保活失败了,注意保活失败跟tcp断开连接,对于uid_conns_的元素删除时不一样的,tcp断开连接,uid_conns_的
        //元素删除发生在OnConnect()的回调中,保活失败则在这里删除
        if (!conn)
        {
            uid_conns_.erase(it);
            ret = -1;
        }
        else
        {
            ret = 0;
        }
    }
    return ret;
}

int AccessServer::GetDevConnByUid(const std::string& uid, evpp::TCPConnPtr& conn)
{
    //获取主站点uid
    PerAccountUserInfo user_info;
    std::string main_uid;
    if(0 != dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(uid, main_uid))
    {
        main_uid = uid;
    }

    return GetDevConnByMainUid(main_uid, conn);
}

int AccessServer::GetDevConnByMac(const std::string& mac, evpp::TCPConnPtr& conn)
{
    int ret = -1;
    std::lock_guard<std::mutex> lock(mac_mutex_);
    WeakTCPConnPtrIter it = mac_conns_.find(mac);
    if (it != mac_conns_.end())
    {
        conn = it->second.lock();
        //该链接已经保活失败了,注意保活失败跟tcp断开连接,对于mac_conns_的元素删除时不一样的,tcp断开连接,mac_conns_的
        //元素删除发生在OnConnect()的回调中,保活失败则在这里删除
        if (!conn)
        {
            mac_conns_.erase(it); //注意erase之后,conn失效,所以调用者一定要注意判断返回值
            ret = -1;
        }
        else
        {
            ret = 0;
        }
    }
    return ret;
}

int AccessServer::GetDevConnByUUID(const std::string& uuid, evpp::TCPConnPtr& conn)
{
    int ret = -1;
    std::lock_guard<std::mutex> lock(dev_uuid_mutex_);
    WeakTCPConnPtrIter it = dev_uuid_conns_.find(uuid);
    if (it != dev_uuid_conns_.end())
    {
        conn = it->second.lock();
        if (!conn)
        {
            dev_uuid_conns_.erase(it);
            ret = -1;
        }
        else
        {
            ret = 0;
        }
    }
    return ret;
}

//设备、APP均通过此方法获取devptr
int AccessServer::GetClientFromConn(const evpp::TCPConnPtr& conn, DevicePtr& dev)
{
    std::lock_guard<std::mutex> lock(mutex_);
    ConnectionListIter it = connections_.find(conn);
    if (it != connections_.end())
    {
        dev = it->second;
        return 0;
    }
    return -1;
}

int AccessServer::IsTCPConnIsAPP(const evpp::TCPConnPtr& conn)
{
    DevicePtr dev;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter it = connections_.find(conn);
        if (it != connections_.end())
        {
            dev = connections_[conn];
            if (dev->IsApp())
            {
                return 1;
            }
            else
            {
                return 0;
            }
        }
    }   //释放锁,其他线程不会影响到本device
    return 0;
}

int AccessServer::GetNodeByConn(const evpp::TCPConnPtr& conn, std::string& node)
{
    DevicePtr dev;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter it = connections_.find(conn);
        if (it != connections_.end())
        {
            dev = connections_[conn];
            if (dev->IsDev())
            {
                return -1;
            }
            else
            {
                dev->GetPerNodeApp(node);
                return 0;
            }
        }
    }   //释放锁,其他线程不会影响到本device
    return 0;
}


//uid:mac_timestamp
int AccessServer::VideoStorageAct(const std::string& uid, bool is_start_storage)
{
    //查询出mac对应的rtsp监控密码
    std::size_t mac_pos = uid.find('_');
    std::string mac = uid.substr(0, mac_pos);
    std::string rtsp_pwd;
    if (GetPersonalDevicesInstance()->DaoGetMacRtspPwd(mac, rtsp_pwd) != 0)
    {
        AK_LOG_INFO << "get rtsp pwd of device fialed, mac is:" << mac;
        return -1;
    }
    AK_LOG_INFO << "begin to video storage, mac is:" << mac;
    //node以000指代
    g_vs_client_ptr->VideoStorageAct(gstAKCSConf.csmain_outer_ip, uid, rtsp_pwd, "000", VideoStorage::START_VIDEO_STORAGE);
    return 0;
}


/////// http维护通道
int AccessServer::IsDevOnline(const std::string& mac)
{
    evpp::TCPConnPtr conn;
    return GetDevConnByMac(mac, conn);
}

int AccessServer::getDevAppOnlineCount(int& device_count, int& nAppCount)
{
    std::lock_guard<std::mutex> lock(mutex_);
    ConnectionListIter oIter = connections_.begin();
    int nTmpDevCount = 0;
    int nTmpAppCount = 0;
    for (; oIter != connections_.end(); ++oIter)
    {
        DevicePtr& Device = oIter->second;
        if (Device->IsApp())
        {
            nTmpAppCount++;
        }
        else
        {
            nTmpDevCount++;
        }
    }
    device_count = nTmpDevCount;
    nAppCount = nTmpAppCount;
    return 1;
}

int AccessServer::SendGetDevLog(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->BuildHttpMaintenanceGetFileCommonCmd(&stSocketMsg, mac, get_file, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_GETLOG) != 0)
    {
        AK_LOG_WARN << "BuildHttpMaintenanceGetFileCommonCmd failed";
        return -1;
    }

    evpp::TCPConnPtr conn;
    if (GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    return 0;
}

int AccessServer::SendCommandToDevice(const char* mac, const std::string& stCMD, std::string& ret)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->BuildDevCommandMsg(&stSocketMsg, stCMD, mac) != 0)
    {
        ret = "BuildHttpMaintenanceCmd failed";
        return -1;
    }

    evpp::TCPConnPtr conn;
    if (GetDevConnByMac(mac, conn) != 0)
    {
        ret =  "device not connected.";
        return -1;
    }

    GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    return 0;
}



int AccessServer::SendDevStartPcap(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->BuildHttpMaintenanceGetFileCommonCmd(&stSocketMsg, mac, get_file, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_START_PCAP) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return -1;
    }

    evpp::TCPConnPtr conn;
    if (GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    return 0;
}

int AccessServer::SendDevStopPcap(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->BuildHttpMaintenanceGetFileCommonCmd(&stSocketMsg, mac, get_file, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_STOP_PCAP) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return -1;
    }

    evpp::TCPConnPtr conn;
    if (GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    return 0;
}

int AccessServer::SendDevGetConfigFile(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->BuildHttpMaintenanceGetFileCommonCmd(&stSocketMsg, mac, get_file, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_GET_DEV_CONFIG) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return -1;
    }

    evpp::TCPConnPtr conn;
    if (GetDevConnByMac(mac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << mac << ") not connected.";
        return -1;
    }

    GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    return 0;
}


int AccessServer::SendDevReconnectRps(const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection)
{
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (strlen(reconnection->mac) > 0)
    {
        if (GetMsgControlInstance()->BuildHttpMaintenanceReconnectCmd(stSocketMsg, dy_iv_msg, reconnection->mac, reconnection, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS) != 0)
        {
            AK_LOG_WARN << "SendDevReconnectRps failed";
            return -1;
        }

        evpp::TCPConnPtr conn;
        if (GetDevConnByMac(reconnection->mac, conn) != 0)
        {
            AK_LOG_WARN << "device(" << reconnection->mac << ") not connected.";
            return -1;
        }

        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
    }
    else
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter oIter = connections_.begin();
        for (; oIter != connections_.end(); ++oIter)
        {
            DevicePtr& Device = oIter->second;
            if (!Device->IsApp())
            {
                SOCKET_MSG stSocketMsg, dy_iv_msg;
                memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                if (GetMsgControlInstance()->BuildHttpMaintenanceReconnectCmd(stSocketMsg, dy_iv_msg, Device->GetMAC().c_str(), reconnection, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS) != 0)
                {
                    AK_LOG_WARN << "SendDevReconnectRps failed mac=" << Device->GetMAC();
                }
                GetDeviceControlInstance()->SendTcpFormateDyIvMsg(oIter->first, stSocketMsg, dy_iv_msg);
            }
        }
    }
    return 0;

}


int AccessServer::SendDevReconnectGateWay(const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection)
{
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (strlen(reconnection->mac) > 0)
    {
        if (GetMsgControlInstance()->BuildHttpMaintenanceReconnectCmd(stSocketMsg, dy_iv_msg, reconnection->mac, reconnection, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY) != 0)
        {
            AK_LOG_WARN << "SendDevReconnectRps failed";
            return -1;
        }

        evpp::TCPConnPtr conn;
        if (GetDevConnByMac(reconnection->mac, conn) != 0)
        {
            AK_LOG_WARN << "device(" << reconnection->mac << ") not connected.";
            return -1;
        }

        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
    }
    else
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter oIter = connections_.begin();
        for (; oIter != connections_.end(); ++oIter)
        {
            DevicePtr& Device = oIter->second;
            if (!Device->IsApp())
            {
                SOCKET_MSG stSocketMsg, dy_iv_msg;
                memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                if (GetMsgControlInstance()->BuildHttpMaintenanceReconnectCmd(stSocketMsg, dy_iv_msg, Device->GetMAC().c_str(), reconnection, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY) != 0)
                {
                    AK_LOG_WARN << "SendDevReconnectRps failed mac=" << Device->GetMAC().c_str();
                }
                GetDeviceControlInstance()->SendTcpFormateDyIvMsg(oIter->first, stSocketMsg, dy_iv_msg);
            }
        }
    }
    return 0;

}


int AccessServer::SendDevReconnectAccessServer(const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection)
{
    SOCKET_MSG stSocketMsg, dy_iv_msg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (strlen(reconnection->mac) > 0)
    {
        if (GetMsgControlInstance()->BuildHttpMaintenanceReconnectCmd(stSocketMsg, dy_iv_msg, reconnection->mac, reconnection, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESSSERVER) != 0)
        {
            AK_LOG_WARN << "SendDevReconnectRps failed";
            return -1;
        }

        evpp::TCPConnPtr conn;
        if (GetDevConnByMac(reconnection->mac, conn) != 0)
        {
            AK_LOG_WARN << "device(" << reconnection->mac << ") not connected.";
            return -1;
        }

        GetDeviceControlInstance()->SendTcpFormateDyIvMsg(conn, stSocketMsg, dy_iv_msg);
    }
    else
    {
        std::lock_guard<std::mutex> lock(mutex_);
        ConnectionListIter oIter = connections_.begin();
        for (; oIter != connections_.end(); ++oIter)
        {
            DevicePtr& Device = oIter->second;
            if (!Device->IsApp())
            {
                SOCKET_MSG stSocketMsg, dy_iv_msg;
                memset(&stSocketMsg, 0, sizeof(stSocketMsg));
                if (GetMsgControlInstance()->BuildHttpMaintenanceReconnectCmd(stSocketMsg, dy_iv_msg, Device->GetMAC().c_str(), reconnection, (uint16_t)MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESSSERVER) != 0)
                {
                    AK_LOG_WARN << "SendDevReconnectRps failed mac=" << Device->GetMAC().c_str();
                }
                GetDeviceControlInstance()->SendTcpFormateDyIvMsg(oIter->first, stSocketMsg, dy_iv_msg);
            }
        }
    }
    return 0;

}

int AccessServer::getDevArmStatuReq(const char* mac)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "dev offline, RequestArming failed.";
        return -1;
    }
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    ::snprintf(stArmingMsg.uid, sizeof(stArmingMsg.uid), "OldReq%s", mac);//旧版本的设备要求上报arming状态
    ::snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), "%s", mac);
    ::snprintf(stArmingMsg.szAction, sizeof(stArmingMsg.szAction), "%s", "Get");
    //组装消息
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildReqArming(&stSocketMsg, stArmingMsg) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal RequestArming to dev failed.";
        return -1;
    }
    return 0;
}

int AccessServer::setDevArmStatuReq(const char* mac, int mode)
{
    evpp::TCPConnPtr dev_conn;
    if (g_accSer_ptr->GetDevConnByMac(mac, dev_conn) != 0)
    {
        AK_LOG_WARN << "dev offline, RequestArming failed.";
        return -1;
    }
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    ::snprintf(stArmingMsg.uid, sizeof(stArmingMsg.uid), "Alexa%s", mac);
    ::snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), "%s", mac);
    ::snprintf(stArmingMsg.szAction, sizeof(stArmingMsg.szAction), "%s", "Set");
    stArmingMsg.mode = mode;
    //组装消息
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->OnBuildReqArming(&stSocketMsg, stArmingMsg) != 0)
    {
        AK_LOG_WARN << "BuildReqArming failed";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(dev_conn, stSocketMsg.data, stSocketMsg.size) < 0)
    {
        AK_LOG_WARN << "Send personnal RequestArming to dev failed.";
        return -1;
    }
    return 0;
}


int AccessServer::NotifyDevUpdateServer(const std::string& stmac, const std::string& type)
{
    SOCKET_MSG stSocketMsg;
    memset(&stSocketMsg, 0, sizeof(stSocketMsg));
    if (GetMsgControlInstance()->BuildNotifyDevUpdateServer(&stSocketMsg, stmac, type) != 0)
    {
        AK_LOG_WARN << "BuildNotifyDevUpdateServer failed";
        return -1;
    }

    evpp::TCPConnPtr conn;
    if (GetDevConnByMac(stmac, conn) != 0)
    {
        AK_LOG_WARN << "device(" << stmac << ") not connected.";
        return -1;
    }

    GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    return 0;
}
//added by chenyc,2019-08-26,v4.6,下发给所有设备进行业务服务器地址更新
int AccessServer::NotifyAllDevsUpdateServer(const std::string& type)
{
    //先获取所有在线的设备
    MacConnList mac_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(mac_mutex_);
        mac_conns_tmp = mac_conns_;
    }
    uint32_t conn_num = mac_conns_tmp.size();
    uint32_t conn_notify_num = 0;
    for (auto& mac_conn : mac_conns_tmp)
    {
        //added by chenyc,2022.03.01,每通知100个设备,就休息1s
        if (conn_notify_num % 100 == 0 && conn_notify_num > 0)
        {
            AK_LOG_INFO << "the num of online dev to notify is " << conn_num << ", now we have notified " << conn_notify_num;
            sleep(1);
        }
        ++conn_notify_num;
        //提升为强引用
        evpp::TCPConnPtr conn = mac_conn.second.lock();
        if (!conn)
        {
            continue;
        }
        std::string mac = mac_conn.first;
        SOCKET_MSG stSocketMsg;
        memset(&stSocketMsg, 0, sizeof(stSocketMsg));
        if (GetMsgControlInstance()->BuildNotifyDevUpdateServer(&stSocketMsg, mac, type) != 0)
        {
            AK_LOG_WARN << "BuildNotifyDevUpdateServer failed";
            continue;
        }
        AK_LOG_INFO << "send update " << type << " server addr for mac " << mac;
        GetDeviceControlInstance()->SendTcpMsg(conn, stSocketMsg.data, stSocketMsg.size);
    }
    return 0;
}

void AccessServer::NotifyAllDevsReboot()
{
    //先获取所有在线的设备
    MacConnList mac_conns_tmp;
    {
        std::lock_guard<std::mutex> lock(mac_mutex_);
        mac_conns_tmp = mac_conns_;
    }
    uint32_t conn_num = mac_conns_tmp.size();
    uint32_t conn_notify_num = 0;
    for (auto& mac_conn : mac_conns_tmp)
    {
        //modified by chenyc, 2022.03.01,每通知100台设备,暂停1s，避免服务器压力过大
        if (conn_notify_num % 100 == 0 && conn_notify_num > 0)
        {
            AK_LOG_INFO << "the num of online dev to reboot is " << conn_num << ", now we have notified " << conn_notify_num;
            sleep(1);
        }
        ++conn_notify_num;
        //提升为强引用
        evpp::TCPConnPtr conn = mac_conn.second.lock();
        if (!conn)
        {
            continue;
        }
        GetDeviceControlInstance()->SendRequestReboot(conn, mac_conn.first);
    }
}

void AccessServer::CliDoorRtspTest(const std::string& ip, int port, const std::string& mac)
{

    CRtspActionNotifyMsg CRtspMsg(ip, port, mac, /*csmain::kRtspStart*/1);
    GetRtspMsgControlInstance()->AddRtspActionNotifyMsg(CRtspMsg);
}

int AccessServer::OnSendAdaptReportVisitorMsg(int sql_id)
{
    AK::Server::P2PMainDevReportVisitorMsg msg;
    msg.set_id(sql_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_S2C_DEV_REPORT_VISITOR);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}

int AccessServer::SendUpdateConfig(int changetype, const char* mac, int project_id, int project_type, const std::string& ip)
{
    AK::Server::P2PMainDevConfigRewriteMsg msg;
    msg.set_mac(mac);
    msg.set_ip(ip);
    msg.set_type(changetype);
    msg.set_project_id(project_id);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_S2C_DEV_CONFIG_REWRITE);
    pdu2.SetSeqNum(0);
    pdu2.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);
    return 0;
}

int AccessServer::OnSocketMsg(const evpp::TCPConnPtr& conn, const std::string& message)
{    
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)message.data();
    //判断类型
    //TODO,added by chenyc,2020-06-15,现在云端跟设备端都是小端序,所以默认两边都没有处理网络序的问题,后面设备如果架构切换了,需要注意
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;
    int msg_version = (normal_msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(normal_msg->data_size);
    int msg_len = SOCKET_MSG_NORMAL_HEADER_SIZE + data_size;
    //int crc= normal_msg->crc;
    //设置该条session是否时加密的,即同一台设备在不同的session也可能由不同的加密形式.
    evpp::Any any_tmp(msg_version);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER, any_tmp);

    csmain::DeviceType contype = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
    std::string ip = evpp::any_cast<std::string>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_IP));
    int port = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_PORT));
    
    if (message_id != MSG_FROM_DEVICE_HEART_BEAT && message_id != MSG_FROM_DEVICE_ACK_HEARTBEAT) //不是心跳包才打印
    {
        AK_LOG_WARN << "recv tcp client msg from (" << ip << ":" << port 
                    << " , ID=0x" << hex << message_id << " , msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id) 
                    << " , msg_version=" << msg_version << " , contype=" << contype;
    }

    //通用
    switch (message_id)
    {
        case MSG_FROM_DEVICE_REPORT_STATUS:
        {
            GetMsgControlInstance()->OnDeviceReportStatusMsg(normal_msg, msg_len, conn);
            return 1;
        }
        //ANDROID向平台上报状态信息 //告警字符串加密
        case MSG_FROM_ANDROID_REPORT_STATUS:
        {
            GetMsgControlInstance()->OnAndroidReportStatusMsg(normal_msg, msg_len, conn);
            return 1;
        }
        //IOS向平台上报状态信息  //告警字符串加密
        case MSG_FROM_IOS_REPORT_STATUS:
        {
            GetMsgControlInstance()->OnIOSReportStatusMsg(normal_msg, msg_len, conn);
            return 1;
        }        
        /* Added by chenyc, 2017-01-04,终端设备心跳消息 */
        //已经在io线程处理
//        case MSG_FROM_DEVICE_HEART_BEAT:  //不用加密  2017-10-09,不需要任何操作,已经在消息接受层处理掉了
//        {
//            GetMsgControlInstance()->OnHeartBeatMsg(conn);
//            return 1;
//        }
        case MSG_FROM_DEVICE_CLI_COMMAND_RESP:
        {
            GetMsgControlInstance()->OnCommandResp(normal_msg, conn);
            return 1;
        }
        //app设置是否接收平台下发的motion alert的通知消息
        case MSG_FROM_APP_SET_RECV_MOTION_ALERT_STATUS:
        {
            GetMsgControlInstance()->OnSetRecvMotionStatus(normal_msg, conn);
            return 1;
        }
        case MSG_FROM_DEVICE_SYNC_ACTIVITY:
        {
            GetMsgControlInstance()->OnOfflineActiveMessage(normal_msg, conn);
            return 1;
        }
        break;
        case MSG_FROM_DEVICE_REPORT_FILE_MD5:
        {
            GetMsgControlInstance()->OnReportFileMD5Message(normal_msg, conn);
            return 1;
        }
        break;
        case MSG_FROM_DEVICE_ACK_HEARTBEAT:
        {
            GetMsgControlInstance()->OnServerHearbeatAck(conn);
            return 1;
        }
        break;        
   }
    //办公
    if ((contype == csmain::OFFICE_APP|| contype == csmain::OFFICE_DEV ) && OfficeMessageHandle::Instance()->OnSocketMsg(conn, message))
    {
        //办公处理过直接退出
        return 1;
    }

    switch (message_id)
    {      
        case MSG_FROM_DEVICE_ALARM:  //alarm加密
        {
            if(gstAKCSConf.is_aws)
            {
               SOCKET_MSG_NORMAL msg = {0}; 
               memcpy(&msg, normal_msg, sizeof(msg));
               CAwsAlarmNotifyMsg notify_msg(msg, conn);
               GetHttpReqMsgControlInstance()->AddAwsAlarmNotiyMsg(notify_msg);
            }
            else
            {
                GetMsgControlInstance()->OnDeviceAlarmMsg(normal_msg, conn);
            }
            
            //因未迁移完全，将消息发到后端业务处理
            SendMsgToResid(conn, normal_msg, msg_len);
        }
        break;
        /* Begin added by chenyc,2017-05-24,云平台接入app开发 */
        //校验临时秘钥,暂时只有社区用,added by chenyc, 2017-09-02,个人终端用户的梯口机也开始支持
        case MSG_FROM_DEVICE_CHECK_TMP_KEY: //mac加密
        {
            GetMsgControlInstance()->OnCheckTmpKeyMsg(normal_msg, conn);
        }
        break;

        //设备主动向平台推送告警处理的消息 //告警字符串加密
        case MSG_FROM_DEVICE_PUT_ALARM_DEAL:
        {
            GetMsgControlInstance()->OnPutAlarmDealMsg(normal_msg, conn);
        }
        break;
        //设备平台请求统一联动单元内的设备列表,用于个人终端用户//mac加密--添加社区 2018-4-9
        case MSG_FROM_DEVICE_REQUEST_DEVICE_LIST:
        {
            GetMsgControlInstance()->OnReqDevListMsg(normal_msg, conn);
        }
        break;

        ////////////////////////////////////// v3.2 /////////////////////////////

        //平台接受设备端上传的motion alert的消息,mac加密
        case MSG_FROM_DEVICE_MOTION_ALERT:
        {
            if (0 == GetMsgControlInstance()->OnReportMotionAlert(normal_msg, conn))
            {
                SendMsgToResid(conn, normal_msg, msg_len);
            }
        }
        break;
        //app上报对设备进行布防、撤防的信令
        case MSG_FROM_APP_HANDLE_DEV_ARMING:
        {
            GetMsgControlInstance()->OnHandleDevArming(normal_msg, conn);
        }
        break;
        //设备（室内机）上报当前布防、撤防的状态给平台
        case MSG_FROM_DEVICE_REPORT_ARMING_STATUS:
        {
            GetMsgControlInstance()->OnReportArmingStatus(normal_msg, conn);
        }
        break;
        //设备（室外机）上报动作消息（即Logs：呼叫，输入密码，卡开门等开门动作）给平台
        // case MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS:
        // {
        //     GetMsgControlInstance()->OnReportActLog(normal_msg, conn);
        // }
        break;
        //app通知平台logout,完成app端退出的时候,通过tcp长连接模块通知平台,平台剔除掉该app的端外推送
        case MSG_FROM_APP_REPORT_LOGOUT:
        {
            GetMsgControlInstance()->OnReportLogOut(normal_msg, conn, ip.c_str());
        }
        break;
        // dtmf按键判断是否允许开启设备
        case MSG_FROM_DEVICE_CHECK_DTMF:
        {
            GetMsgControlInstance()->OnCheckDtmf(normal_msg, conn);
        }
        break;
        ///////////4.2 ///////////////
        case MSG_FROM_DEVICE_VIDEO_STORAGE_ACTION:
        {
            GetMsgControlInstance()->OnVideoStorageAct(normal_msg, conn);
        }
        break;
        // case MSG_FROM_DEVICE_REPORT_CALL_CAPTURE:
        // {
        //     GetMsgControlInstance()->OnCallCaptureReport(normal_msg, conn);
        // }
        break;
        case MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG:
        {
            GetMsgControlInstance()->OnMngDevReportMsg(normal_msg, conn);
        }
        break;
        case MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER:
        {
            GetMsgControlInstance()->OnRespondSensorTrigger(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_HANDLE_ARMING://罗伯特门口机刷卡布撤防
        {
            SOCKET_MSG_NORMAL tSocketMsgNormal;
            memset(&tSocketMsgNormal, 0x0, sizeof(tSocketMsgNormal));
            memcpy(&tSocketMsgNormal, normal_msg, message.size());
            GetMsgControlInstance()->OnHandleDevArmingFromDev(&tSocketMsgNormal, conn);
        }
        break;
        case MSG_FROM_DEVICE_REPORT_VISITOR_MSG:
        {
            GetMsgControlInstance()->OnDevReportVisitorInfo(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG:
        {
            GetMsgControlInstance()->OnDevReportVisitorAuth(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_OSS_STS:
        {
            GetMsgControlInstance()->OnDevRequestOssSts(normal_msg, conn);
        }
        break;
        // case MSG_FROM_DEVICE_REQUEST_OPENDOOR:
        // {
        //     SendMsgToResid(conn, normal_msg, msg_len);
        //     GetMsgControlInstance()->OnDevRequestOpen(normal_msg, conn);
        // }
        // break;
        case MSG_FROM_DEVICE_SEND_DELIVERY_MSG:
        {
            GetMsgControlInstance()->OnDevSendDelivery(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_SEND_DELIVERY_BOX_MSG:
        {
            GetMsgControlInstance()->OnDevSendDeliveryBox(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT:
        {
            GetMsgControlInstance()->OnFlowOutOfLimit(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_ACINFO:
        {
            GetMsgControlInstance()->OnRequestUserInfo(normal_msg, conn);
        }
        break;  
        case MSG_FROM_DEVICE_REPORT_ACCESS_TIMES:
        {
            /*离线tempkey*/
            GetMsgControlInstance()->OnDeviceReportAccessTimesMsg(normal_msg, conn);
        }
        break; 
        /*kit相关*/
        /*
        case MSG_FROM_DEVICE_REQUEST_END_USER_REG:
        {
            GetMsgControlInstance()->OnDeviceRequestRegEndUser(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REPORT_KIT_DEVICES:
        {
            GetMsgControlInstance()->OnDeviceReportKitDevices(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_ADD_KIT_DEVICES:
        {
            GetMsgControlInstance()->OnDeviceAddKitDevices(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_KIT_DEVICES:
        {
            GetMsgControlInstance()->OnDeviceRequestKitDevices(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_MODIFY_LOCATION:
        {
            GetMsgControlInstance()->OnDeviceModifyLocation(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_ACCOUNT_LOGOUT:
        {
            GetMsgControlInstance()->OnKitRequestAccountLogout(normal_msg, conn);
        }
        break;
        */
        /*kit相关 end*/
#if 0  //将6.5.3的功能移动到csresdi中,后续csmain不能再加业务功能，保证基本不用升级
        case MSG_FROM_DEVICE_REPORT_VOICE_MSG:
        {
            GetMsgControlInstance()->OnDeviceReportVoiceMsg(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST:
        {
            GetMsgControlInstance()->OnDeviceRequestVoiceMsgList(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL:
        {
            GetMsgControlInstance()->OnDeviceRequestVoiceMsgUrl(normal_msg, conn);
        }
        break;
        case MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG:
        {
            GetMsgControlInstance()->OnDeviceRequestDelVoiceMsg(normal_msg, conn);
        }
        break;
#endif
        case MSG_FROM_DEVICE_THIRD_CAMERA_MEDIA_INFO:
        {
            GetMsgControlInstance()->OnDeviceReportThirdCameraInfo(normal_msg, conn);
        }
        break;
        /* added by chenyc, 2016-12-26, 增加判断分支 */
        default:
        {
            // 如果conn已经上报过状态，则将消息转移到csresid中处理,否则就是非法消息
            SendMsgToResid(conn, normal_msg, msg_len);
        }
    }
    return 0;
}

int AccessServer::processPersonnalDevStatusMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REPORT_STATUS& reportStatusMsg, DEVICE_SETTING& deviceSetting)
{
    //对比MD5，先处理PRIVATEKEY和RFID的MD5
    CString privatekey_md5 = reportStatusMsg.private_key_md5;
    CString rfid_md5 = reportStatusMsg.rf_id_md5;
    CString config_md5 = reportStatusMsg.config_md5;
    CString contact_md5 = reportStatusMsg.contact_md5;
    std::string tz_md5 = reportStatusMsg.tz_md5;
    std::string tz_data_md5 = reportStatusMsg.tz_data_md5;
    std::string face_md5 = reportStatusMsg.face_md5;
    std::string user_meta_md5 = reportStatusMsg.user_meta_md5;
    std::string schedule_md5 = reportStatusMsg.schedule_md5;    
    int func_bit = reportStatusMsg.func_bit;
    std::string sw_ver = reportStatusMsg.SWVer;

    //不支持tzdata更新的设备不进行tzdata判断
    if (!GetKeyControlInstance()->IsDevSupportTzData(sw_ver, func_bit))
    {
        tz_data_md5 = "";
    }

    //版本升级和ip变化 从新写配置后一定会通知这台设备从新下载配置
    if (deviceSetting.is_ip_addr_change == 0 && deviceSetting.is_dev_upgrade == 0 && (privatekey_md5 != deviceSetting.private_key_md5
            || rfid_md5 != deviceSetting.rf_id_md5
            || config_md5 != deviceSetting.config_md5
            || face_md5 != deviceSetting.face_md5
            || (contact_md5 != deviceSetting.contact_md5 && deviceSetting.dclient_version >= D_CLIENT_VERSION_1_0)
            || (tz_md5 != gstAKCSConf.tz_md5 && tz_md5.size() > 0) || (tz_data_md5 != gstAKCSConf.tz_data_md5 && tz_data_md5.size() > 0)
            || user_meta_md5 != deviceSetting.user_meta_md5
            || schedule_md5 != deviceSetting.schedule_md5))
    {
        AK_LOG_INFO <<  "Need to update personal key's MD5 info to " << conn->remote_addr().c_str();

        PERSONAL_KEY_SEND PersonalkeySend;
        Snprintf(PersonalkeySend.node, sizeof(PersonalkeySend.node), deviceSetting.device_node);
        Snprintf(PersonalkeySend.mac, sizeof(PersonalkeySend.mac), deviceSetting.mac);
        if (tz_md5.size() > 0)
        {
            PersonalkeySend.tz_type = TIME_ZONE_XML;
        }
        else if (tz_data_md5.size() > 0)
        {
            PersonalkeySend.tz_type = TIME_ZONE_DATA;
        }
        PersonalkeySend.weak_conn = conn;

        //todo:需要设备过来下载的消息需要放入队列中等待发送，目前没有实现条件变量通知,仅仅使用定时器来刷数据
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        if (GetKeyControlInstance()->AddPersonalKeySend(PersonalkeySend) < 0)
        {
            AK_LOG_WARN << "Add personal KeySend failed. Mac:" << PersonalkeySend.mac;
            return -1;
        }
    }

    //设备的ip有变化时候，要通知联动重新获取联系人。和配置文件 因为有pushbutton
    if (deviceSetting.is_ip_addr_change)
    {
        AK_LOG_INFO << "[IPChange] Device Mac:" << deviceSetting.mac << ", Notify Node rewrite contactlist and config";
        g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_IP_CHANGE, deviceSetting.mac, deviceSetting.manager_account_id, project::RESIDENCE, deviceSetting.ip_addr);
    }

    //升级后重新写配置文件
    if (deviceSetting.is_ip_addr_change == 0 && deviceSetting.is_dev_upgrade) //ip变化从新写配置的范围比升级的要多，包括相关的设备
    {
        AK_LOG_INFO << "[DevUpgrade] Device Mac:" << deviceSetting.mac << ", Notify Node rewrite config and contactlist,only this mac";
        g_accSer_ptr->SendUpdateConfig(CSMAIN_UPDATE_CONFIG_UPGRADE, deviceSetting.mac, deviceSetting.manager_account_id);
    }

    MacInfo mac2;
    CreateOnlineMacInfo(mac2, deviceSetting);
    DevOnlineMng::GetInstance()->AddPerMac(mac2);
    return 0;
}

void AccessServer::NotifyConnInfoMsg(const DevicePtr& dev, CONN_INFO_TYPE type)
{
    std::string uid;
    InnerConnInfo info;
    info.type = type;
    info.conn_type = (csmain::DeviceType)dev->Type();
    
    if (dev->IsApp())
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& app = boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(dev->GetContext());
        if (app.role == ACCOUNT_ROLE_COMMUNITY_PM
            || app.role == ACCOUNT_ROLE_COMMUNITY_MAIN
            || app.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT
            || app.role == ACCOUNT_ROLE_PERSONNAL_MAIN
            || app.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT
            || IsOfficeRole(app.role))
        {
            
            info.role = app.role;
            snprintf(info.uid, sizeof(info.uid), "%s", app.user);
            snprintf(info.username, sizeof(info.username), "%s", app.username);
        }
#if 0            
    } 
    else
    {
        DEVICE_SETTING devsetting;
        memset(&devsetting, 0, sizeof(devsetting));
        dev->GetDeviceSetting(&devsetting);
        
        if (devsetting.project_type == project::RESIDENCE
            || devsetting.project_type == project::PERSONAL)
        {
            snprintf(info.uid, sizeof(info.uid), "%s", devsetting.mac);       
        }
    }
#else
        SOCKET_MSG_NORMAL msg;
        memset(&msg, 0, sizeof(msg));
        GetMsgControlInstance()->BuildInnerMsg(msg, MSG_FROM_INNER_CONN_INFO, (char*)&info, sizeof(info));
        if (dev->Type() == csmain::OFFICE_APP)
        {
            Main2ResidMsgHandle::Instance()->OfficeSend(info.uid, (csmain::DeviceType)dev->Type(), false, (char*)&msg,  msg.data_size);  
        }
        else 
        {
            Main2ResidMsgHandle::Instance()->Send(info.uid, (csmain::DeviceType)dev->Type(), false, (char*)&msg,  msg.data_size);  
        }
    }
#endif
    //设备
    else
    {
        snprintf(info.uid, sizeof(info.uid), "%s", dev->GetMAC().c_str());
        SOCKET_MSG_NORMAL msg;
        memset(&msg, 0, sizeof(msg));
        GetMsgControlInstance()->BuildInnerMsg(msg, MSG_FROM_INNER_CONN_INFO, (char*)&info, sizeof(info));
        if (dev->Type() == csmain::OFFICE_DEV)
        {
            Main2ResidMsgHandle::Instance()->OfficeSend(info.uid, (csmain::DeviceType)dev->Type(), false, (char*)&msg,  msg.data_size);  
        }
        else 
        {
            Main2ResidMsgHandle::Instance()->Send(info.uid, (csmain::DeviceType)dev->Type(), false, (char*)&msg,  msg.data_size);  
        }
    }
}

void AccessServer::SendMsgToResid(const evpp::TCPConnPtr& conn, SOCKET_MSG_NORMAL* normal_msg, int msg_len)
{
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;
    bool conn_reported_status = evpp::any_cast<bool>(conn->context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX));
    
    // hager kit 室内机未添加到云上,允许创建房间msgid传递到后端业务
    if (conn_reported_status || message_id == MSG_FROM_DEVICE_REQUEST_CREATE_ROOM)
    {
        csmain::DeviceType conn_type = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
        std::string conn_client = evpp::any_cast<std::string>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX));
        bool ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
        
        //将消息透传到后端的csresid服务
        Main2ResidMsgHandle::Instance()->Send(conn_client, conn_type, ipv6, (char *)normal_msg, msg_len);
        AK_LOG_INFO << "send msg to backend, conn_type:" << conn_type << ",conn_client:" << conn_client;
        return;
    }
    
    std::string ip;
    int port = 0;
    AkParseAddr(conn->remote_addr(), ip, port); //ip对于ipv6，则左右带有[]

    AK_LOG_WARN << "tcp client msg from ("<< ip << ":" << port << ") has not been reported status,drop the msg";
    return;
}
