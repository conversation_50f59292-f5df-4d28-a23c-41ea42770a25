#ifndef __CSMAIN_SERVER_H__
#define __CSMAIN_SERVER_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include <evpp/tcp_server.h>
#include <evpp/any.h>
#include <boost/circular_buffer.hpp>
#include "SDMCMsg.h"
#include "KeyControl.h"
#include "SnowFlakeGid.h"
#include "AkcsMsgCodec.h"
#include "ErrorConnect.h"
#include "doorlog/UserInfo.h"
#include "DeviceControl.h"
#include "MsgControl.h"
#include "DevOnlineMng.h"
#include "dbinterface/ErrorConnectDB.h"
#include "dbinterface/AlexaToken.h"

class OfficeAccessServer;
class CDevice;
struct Entry2;
struct Entry;
struct EntryAlexaDevStatu;
struct EntryAlexaSendHb;

class CDevToken;
typedef std::shared_ptr<CDevice> DevicePtr;
typedef std::map<evpp::TCPConnPtr, DevicePtr> ConnectionList; //tcp长连接的数据
typedef ConnectionList::iterator ConnectionListIter;
typedef std::map<std::string, WeakTCPConnPtr> MacConnList;  //先用map,后续数据量达到10万级别以上,内存提升时用hash_map
typedef std::map<std::string, WeakTCPConnPtr> UidConnList;  //后续改成uid->uint_64
typedef std::map<std::string, std::set<evpp::TCPConnPtr> >NodeConnList;   //Node下的的连接容器
typedef std::map<int, std::set<evpp::TCPConnPtr> >ManageConnList;  //社区的连接容器
typedef NodeConnList::iterator NodeConnListIter;
typedef ManageConnList::iterator ManageConnListIter;
typedef UidConnList::iterator WeakTCPConnPtrIter;
typedef MacConnList::iterator MacConnListIter;

typedef std::map<std::string, WeakTCPConnPtr> DevUUIDConnList; //设备uuid和conn的关系

//typedef std::function<void(const evpp::TCPConnPtr&, const std::string&)> StringMessageCallback;

//todo:这种设置和获取一定要确保已经设置了，不然获取时候会出现断言失败
#define EVPP_CONN_ANY_CONTEXT_HB_INDEX  0
#define EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER 1//设备版本号 加密
#define EVPP_CONN_ANY_CONTEXT_ALEXA_HB_INDEX 2//服务器主动发送心跳
#define EVPP_CONN_ANY_CONTEXT_ALEXA_STATUS_INDEX 3//服务器检测alexa心跳
#define EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX 4//是否是ipv6
#define EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX 5//连接服务类型
#define EVPP_CONN_ANY_CONTEXT_DY_AES_IV_INDEX 6//动态iv
#define EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX 7//该conn已经上报过状态的标志位,csmain拆分使用
#define EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX 8  //该conn对应的client标识,mac or uid
#define EVPP_CONN_ANY_CONTEXT_CLIENT_IS_APP 9  //是否是APP
#define EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_IP 10  //连接的ip
#define EVPP_CONN_ANY_CONTEXT_CLIENT_OUTER_PORT 11  //连接的port
#define EVPP_CONN_ANY_CONTEXT_DEV_CONN_VERSION 12  //conn dev version



#define WEEK_SECOND 604800
#define WEEK_SECOND 604800

//2017-11-1,后面接入服务器跟逻辑服务器分开的时候，就是从这里分开的.接入服务器只负责解码一条完整的消息，并将消息发送给逻辑服务器.
class AccessServer
{
public:
    AccessServer(evpp::EventLoop* loop, const std::string& addr, const std::string& name, uint32_t thread_num);

    void Start();

public:
    //friend struct Entry;
    //message已经是一条完整的消息了
    void OnStringMessage(const evpp::TCPConnPtr& conn, std::string& message);
    std::size_t GetTcpCliNum()
    {
        return connections_.size();
    }
    int GetDevConnByUid(const std::string& uid, evpp::TCPConnPtr& conn); 
    int GetDevConnByMainUid(const std::string& main_uid, evpp::TCPConnPtr& conn);
    //mac->weakconn的映射,用于快速查找设备tcp连接的弱引用
    int GetDevConnByMac(const std::string& mac, evpp::TCPConnPtr& weakConn);
    int GetDevConnByUUID(const std::string& uuid, evpp::TCPConnPtr& conn);
    int UpdateDevSomeMsgFromConnList(const evpp::TCPConnPtr& conn, DEVICE_SETTING* device_setting);
public:
    //对于akcs来说,就是在这里讲请求设备上报状态的消息下发了,同时保存设备的相关状态信息(含地址信息\联动系统等)
    void OnConnection(const evpp::TCPConnPtr& conn);
    
    void ClearDevConnMap(const evpp::TCPConnPtr& conn);

    void onHeartBeatTimer()
    {
        {
            std::lock_guard<std::mutex> lock(buckets_mutex_);
            connectionBuckets_.push_back(Bucket());
        }
        {
            std::lock_guard<std::mutex> lock(reportstatu_buckets_mutex_);
            connectionReportStatuBuckets_.push_back(Bucket2());
        }
    }
    void onOneSecondTimer()
    {
        {
            std::lock_guard<std::mutex> lock1(alexa_dev_hb_buckets_mutex_);
            connection_alexa_dev_hb_buckets_.push_back(BucketAlexaHeartBeat());
        }
        {
            std::lock_guard<std::mutex> lock2(alexa_dev_hb_status_buckets_mutex_);
            connection_alexa_dev_hb_status_buckets_.push_back(BucketAlexaDevStatus());
        }
        
    }

    void onKeySendTimer()
    {
        GetKeyControlInstance()->ProcessBaseTimer();
    }
    int OnSocketMsg(const evpp::TCPConnPtr& conn, const std::string& message);
    void SetTcpConnMac(const evpp::TCPConnPtr& conn, const std::string& mac, const std::string& uuid="");
    void SetTcpConnList(const evpp::TCPConnPtr& conn, const DEVICE_SETTING &device_setting);
    void UpdateTcpConnSetting(const evpp::TCPConnPtr& conn, const DEVICE_SETTING* device_setting);
    void UpdateDeviceProjectInfo(const evpp::TCPConnPtr& conn, const MacInfo &mac_info);
    int GetDeviceSettingFromConnList(const evpp::TCPConnPtr& conn, DEVICE_SETTING* device_setting);
    int GetDevSetDiffFromConnList(const evpp::TCPConnPtr& conn, DEVICE_SETTING* device_setting, evpp::Any& personnalAppSetting, int& type);
    int UpdatePersonnalAppNodeToLocal(const evpp::TCPConnPtr& conn, const SOCKET_MSG_PERSONNAL_APP_NODE& stAppNode, PersonalAccountNodeInfoMap& node_infos);
    int GetDevListByNode(const std::string& node, std::vector<evpp::TCPConnPtr>& device);
    int GetDevListByNodeOnlyDev(const std::string& node, std::vector<evpp::TCPConnPtr>& device);
    int GetDevListCommunityUnitPublicDev(uint32_t manager_id, uint32_t unit_id, std::vector<evpp::TCPConnPtr>& device);
    int GetDevListCommunityAllUnitDevAndAPP(uint32_t manager_id, uint32_t unit_id, std::vector<evpp::TCPConnPtr>& device);
    int GetDevListCommunityPublicDev(uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device);
    int GetDevListCommunityAllPubDev(uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device);    
    int GetAppListByNode(const std::string& node, std::vector<evpp::TCPConnPtr>& device);
    int processPersonnalDevStatusMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_REPORT_STATUS& reportStatusMsg, DEVICE_SETTING& deviceSetting);
    int GetClientFromConn(const evpp::TCPConnPtr& conn, DevicePtr& dev);
    int GetAppByNode(const std::string& node, ConnectionList& app_apps);
    //v4.0 chenzhx
    int GetDevListCommunityPublicAndPersonnal(const std::string& node, uint32_t unit_id, uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device);
    int GetAppByPerNode(const std::string& node, ConnectionList& app_devs);

    int GetDevListCommunityUnitPublic(uint32_t manager_id, uint32_t unit_id, std::vector<std::string>& mac_vec);

    int IsTCPConnIsAPP(const evpp::TCPConnPtr& conn);//V3.3判断连接是否是app连接

    /*获取单元下的个人设备*/
    int GetDevListCommunityUnderUnitDevAndAllPubDev(uint32_t manager_id, uint32_t unit_id, std::vector<evpp::TCPConnPtr>& device);
    /*获取整个小区的设备和app*/
    int GetDevListCommunityAllDevAndAPP(uint32_t manager_id, std::vector<evpp::TCPConnPtr>& device);

    int GetNodeByConn(const evpp::TCPConnPtr& conn, std::string& node);
    int VideoStorageAct(const std::string& uid, bool is_start_storage);

    int getDevArmStatuReq(const char* mac);
    int setDevArmStatuReq(const char* mac, int mode);

    void AlexaLoginAddConnDetect(const std::string& uid);
    void AlexaDevConnDetect(const evpp::TCPConnPtr& conn);
    void AlexaDevHearbeatAck(const evpp::TCPConnPtr& conn);
    //added by chenyc,2019-11-26,防网络攻击回调接口
    void TcpAttackCallback(const std::string& bussiness, const std::string& key);
public://控制设备发送命令
    int SendCommandToDevice(const char* mac, const std::string& stCMD, std::string& ret);
    
public: //以下接口为http维护通道用
    int IsDevOnline(const std::string& mac);
    int getDevAppOnlineCount(int& devCount, int& appCount);
    int SendGetDevLog(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file);
    int SendDevStartPcap(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file);
    int SendDevStopPcap(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file);
    int SendDevGetConfigFile(const char* mac, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file);

    int SendDevReconnectRps(const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection);
    int SendDevReconnectGateWay(const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection);
    int SendDevReconnectAccessServer(const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection);
    int RemoveAppConnFromConnections(const evpp::TCPConnPtr& conn);
    void RemoveReportStatuConn(const evpp::TCPConnPtr& conn);
    int ExistReportStatuConn(const evpp::TCPConnPtr& conn);
    int NotifyDevUpdateServer(const std::string& stmac, const std::string& type);
    int NotifyAllDevsUpdateServer(const std::string& type);
	void NotifyAllDevsReboot();

    void NotifyConnInfoMsg(const DevicePtr& dev, CONN_INFO_TYPE type);
public: //http调试接口
    int SendUpdateConfig(int type, const char* mac,  int project_id = 0, int project_type = 0, const std::string& ip = "");
    int OnSendAdaptReportVisitorMsg(int sql_id);
    //cli 接口测试
    void CliDoorRtspTest(const std::string& ip, int port, const std::string& mac);
public:
    typedef std::shared_ptr<Entry> EntryPtr;
    typedef std::weak_ptr<Entry> WeakEntryPtr;
    typedef std::unordered_set<EntryPtr> Bucket;
    typedef boost::circular_buffer<Bucket> WeakConnectionList;
    int enable_send_log;
private:
    void SendMsgToResid(const evpp::TCPConnPtr& conn, SOCKET_MSG_NORMAL* normal_msg, int msg_len);

    std::mutex buckets_mutex_;
    WeakConnectionList connectionBuckets_;

    evpp::TCPServer server_;
    AkcsMsgCodec codec_;
    std::mutex mutex_;
    ConnectionList connections_;
    std::mutex mac_mutex_;
    MacConnList mac_conns_;   //设备跟tcpconn的弱引用
    std::mutex dev_uuid_mutex_;
    DevUUIDConnList dev_uuid_conns_;
    std::mutex uid_mutex_;
    UidConnList uid_conns_;  //终端用户跟tcpconn的弱引用,当conn掉线的时候会第一时间去erase掉弱引用,以释放智能指针自己,但是原始指针所指向的内存随着强应用释放而释放
    std::mutex node_mutex_;
    NodeConnList node_conns_;   //社区和单住户的 某个node下 的连接
    std::mutex manage_mutex_;
    ManageConnList manage_conns_; //某个社区/办公下的连接

    //connect 处理未按时上报状态
    typedef std::shared_ptr<Entry2> EntryPtr2;
    typedef std::unordered_set<EntryPtr2> Bucket2;
    typedef boost::circular_buffer<Bucket2> WeakReportStatuConnectionList;
    std::mutex reportstatu_mutex_;
    std::set<evpp::TCPConnPtr> reportstatu_conns_;
    std::mutex reportstatu_buckets_mutex_;
    WeakReportStatuConnectionList connectionReportStatuBuckets_;

    //V4.6 alexa维护和设备的一秒心跳的发送
    typedef std::shared_ptr<EntryAlexaSendHb> EntryAlexaHeartBeatPtr;
    typedef std::weak_ptr<EntryAlexaSendHb> WeakEntryAlexaHeartBeatPtr;
    typedef std::unordered_set<EntryAlexaHeartBeatPtr> BucketAlexaHeartBeat;
    typedef boost::circular_buffer<BucketAlexaHeartBeat> WeakAlexaDevHeartBeatConnectionList;
    //心跳发送
    std::mutex alexa_dev_hb_buckets_mutex_;
    WeakAlexaDevHeartBeatConnectionList connection_alexa_dev_hb_buckets_;

    typedef std::shared_ptr<EntryAlexaDevStatu> EntryAlexaDevStatusPtr;
    typedef std::weak_ptr<EntryAlexaDevStatu> WeakEntryAlexaDevStatusPtr;
    typedef std::unordered_set<EntryAlexaDevStatusPtr> BucketAlexaDevStatus;
    typedef boost::circular_buffer<BucketAlexaDevStatus> WeakAlexaDevStatusConnectionList;
    //alexa心跳的检测
    std::mutex alexa_dev_hb_status_buckets_mutex_;
    WeakAlexaDevStatusConnectionList connection_alexa_dev_hb_status_buckets_;
};

extern AccessServer* g_accSer_ptr;

struct Entry
{
    explicit Entry(const WeakTCPConnPtr& weakConn)
        : weakConn_(weakConn)
          // ,  paccServer(server)
    {
    }

    ~Entry()
    {
        evpp::TCPConnPtr conn = weakConn_.lock(); //提升为强引用
        if (conn)
        {
            conn->Close(); //会回调OnConnection,即使对端时非正常断开tcp连接
            //paccServer->connections_.erase(conn); //关闭掉应用层的conn,2017-10-19,这个不需要处理,因为网络库会回调OnConnection,在哪里已经关闭了
            //当客户端保活失败时,uid_conns跟mac_conns_不需要处理,只在要发送消息时,转换成强引用失败,再去删除这两个容器中的元素
            AK_LOG_WARN << "client " << conn->remote_addr() << " keepalive failed";
        }
    }

    WeakTCPConnPtr weakConn_;
    //AccessServer *paccServer; //设置成静态成员,节省内存
};


//用于连接上后多久没有上报状态，断开连接的操作
struct Entry2
{
    explicit Entry2(const WeakTCPConnPtr& weakConn)
        : weakConn_(weakConn)
    {
    }

    ~Entry2()
    {
        evpp::TCPConnPtr conn = weakConn_.lock(); //提升为强引用
        if (conn)
        {
            if (g_accSer_ptr->ExistReportStatuConn(conn))
            {
                //TODO: 这部分不止没有上报状态，还包括上报状态时候数据库没有连接上导致判断错误，需要关闭连接重新操作
                GetErrorConnectInstance()->InsertUnReportStatuError(conn->AddrToString());
                //TODO:先去掉，因为app在登陆页面时候Dclient会连接平台，但是不会上报状态
                AK_LOG_WARN << "client " << conn->remote_addr().c_str() << " report status timeout!";
                //add by chenzhx 20200106先不处理这个问题 防止刷日志，后期在用个比较好得方式
                //add by chenzhx 20200616开启，5分钟没有上报状态关闭连接(防止挂着连接/因为查数据库时候刚好失败)
                conn->Close();

            }
        }
    }

    WeakTCPConnPtr weakConn_;
};

//跟开启alexa的设备主动发送心跳
struct EntryAlexaSendHb
{
    explicit EntryAlexaSendHb(const WeakTCPConnPtr& weakConn)
        : weakConn_(weakConn)
    {
    }

    ~EntryAlexaSendHb()
    {
        evpp::TCPConnPtr conn = weakConn_.lock(); //提升为强引用
        if (conn)
        {
            GetMsgControlInstance()->SendHearbeatToDev(conn);
        }

    }

    WeakTCPConnPtr weakConn_;
};


struct EntryAlexaDevStatu
{
    explicit EntryAlexaDevStatu(const WeakTCPConnPtr& weakConn)
        : weakConn_(weakConn)
    {
    }

    ~EntryAlexaDevStatu()
    {
        evpp::TCPConnPtr conn = weakConn_.lock(); //提升为强引用
        if (conn)
        {
            //paccServer->connections_.erase(conn); //关闭掉应用层的conn,2017-10-19,这个不需要处理,因为网络库会回调OnConnection,在哪里已经关闭了
            //当客户端保活失败时,uid_conns跟mac_conns_不需要处理,只在要发送消息时,转换成强引用失败,再去删除这两个容器中的元素
            AK_LOG_WARN << "client " << conn->remote_addr() << " for alexa keepalive failed";
            
            DEVICE_SETTING device_setting;
            memset(&device_setting, 0, sizeof(device_setting));
            if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) == 0)
            {
                uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
                GetMsgControlInstance()->PostAlexaChangeStatus(device_setting.mac, traceid);
                
                AK_LOG_INFO << "dev " << device_setting.mac << " relate to alexa, notify dev offline status, traceid : " << traceid;
            }

            //会回调OnConnection,即使对端时非正常断开tcp连接
            conn->Close(); 
        }
    }
    WeakTCPConnPtr weakConn_;
};

#endif //__CSMAIN_SERVER_H__
