/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>

#include "csvs.grpc.pb.h"
#include "InnerSt.h"
#include "AkLogging.h"
#include "dbinterface/VideoList.h"
#include "dbinterface/VideoLength.h"

extern AKCS_CONF gstAKCSConf;


using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using VideoStorage::VsReply;
using VideoStorage::VsRequest;
using VideoStorage::VsDelReply;
using VideoStorage::VsDelRequest;

using VideoStorage::VideoStorageMsg; //rpc服务名

//对rpc内部接口的封装
class VideoStorageClient
{
public:
    explicit VideoStorageClient(std::shared_ptr<Channel> channel)
        : stub_(VideoStorageMsg::NewStub(channel)) {}

    // Assembles the client's payload and sends it to the server.
    void VideoStorageAct(const std::string& rtsp_srv_ip, const std::string& uid,
                         const std::string& pwd, const std::string& node, VideoStorage::VideoStorageAction act)
    {
        //确定是否有视频超过时间,需要自动删除.
        uint32_t time = dbinterface::VideoLength::GetVideoStorageTime(node);
        std::vector<uint32_t> timeout_vids;
        dbinterface::VideoList::DelVideoRecord(time, timeout_vids, node, gstAKCSConf.video_length);
        for (const auto& vid :  timeout_vids)
        {
            DelVideoStorage(vid); //TODO 用流式rpc接口, 失败后,怎样重试,这些都要考虑...
        }
        //先检查空间是否满足30秒存储需求
        bool is_space_available = dbinterface::VideoLength::IsSpaceAvailabe(node);
        if (!is_space_available) //存储空间已满
        {
            AK_LOG_WARN << "there is not enough video storage space in node " << node;
            return;
        }
        VsRequest request;
        request.set_rtsp_srv_ip(rtsp_srv_ip);
        request.set_storage_uid(uid);
        request.set_dev_rtsp_pwd(pwd);
        request.set_storage_node(node);
        request.set_act(act);

        // Call object to store rpc data
        AsyncClientCall* call = new AsyncClientCall;
        call->request_type = 0;
        // stub_->PrepareAsyncSayHello() creates an RPC object, returning
        // an instance to store in "call" but does not actually start the RPC
        // Because we are using the asynchronous API, we need to hold on to
        // the "call" instance in order to get updates on the ongoing RPC.
        call->response_reader =
            stub_->PrepareAsyncVideoStorageHandle(&call->context, request, &cq_); //PrepareAsyncAddTwoInt 异步的rpc请求接口

        // StartCall initiates the RPC call
        call->response_reader->StartCall();

        // Request that, upon completion of the RPC, "reply" be updated with the
        // server's response; "status" with the indication of whether the operation
        // was successful. Tag the request with the memory address of the call object.
        call->response_reader->Finish(&call->reply, &call->status, (void*)call);
    }

    void DelVideoStorage(uint32_t vid)
    {
        // Data we are sending to the server.
        VsDelRequest del_request;
        del_request.set_global_video_id(vid);
        // Call object to store rpc data
        AsyncClientCall* call = new AsyncClientCall;
        call->request_type = 1;
        // stub_->PrepareAsyncSayHello() creates an RPC object, returning
        // an instance to store in "call" but does not actually start the RPC
        // Because we are using the asynchronous API, we need to hold on to
        // the "call" instance in order to get updates on the ongoing RPC.
        call->del_response_reader =
            stub_->PrepareAsyncDelVideoStorageHandle(&call->context, del_request, &cq_);

        // StartCall initiates the RPC call
        call->del_response_reader->StartCall();

        // Request that, upon completion of the RPC, "reply" be updated with the
        // server's response; "status" with the indication of whether the operation
        // was successful. Tag the request with the memory address of the call object.
        call->del_response_reader->Finish(&call->del_reply, &call->status, (void*)call);

    }

    // Loop while listening for completed responses.
    // Prints out the response from the server.
    void AsyncCompleteRpc()
    {
        void* got_tag;
        bool ok = false;

        // Block until the next result is available in the completion queue "cq".
        while (cq_.Next(&got_tag, &ok))
        {
            // The tag in this example is the memory location of the call object
            AsyncClientCall* call = static_cast<AsyncClientCall*>(got_tag);

            // Verify that the request was completed successfully. Note that "ok"
            // corresponds solely to the request for updates introduced by Finish().
            GPR_ASSERT(ok);

            if (call->status.ok())
            {

                if (call->request_type == 0)
                {
                    //std::cout << "VideoStorage received video url is: " << call->reply.hls_uri() << std::endl;
                    //写入数据库
                    std::string resp_hls_uri = call->reply.hls_uri();
                    uint32_t global_vid = call->reply.global_video_id();
                    std::string resp_node = call->reply.resp_storage_node();
                    std::string resp_mac = call->reply.resp_storage_mac();
                    //判断是否已经有同一台设备在录制了,通过resp_hls_uri是否为空来判断
                    if (!resp_hls_uri.empty())
                    {
                        //写入数据库VideoList,录制时长内部函数写死30s了...
                        dbinterface::VideoList::AddVideoRecord(resp_node, resp_mac, resp_hls_uri, global_vid, gstAKCSConf.video_length);
                        //更新视频总时长
                        dbinterface::VideoLength::UpdateVideoStorageLength(resp_node);
                    }
                }
                else if (call->request_type == 1)
                {
                    //TODO,视频删除的响应,暂时不需要处理，后续需要考虑rpc客户端重试机制,同时rpc服务端需要保证幂等性
                }

            }
            else
            {
                AK_LOG_WARN << "RPC failed, please check rpc server";
            }

            delete call; //记得要析构掉自己
        }
    }

private:

    // struct for keeping state and data information
    // TODO,通过多态来处理AsyncClientCall的逻辑
    struct AsyncClientCall
    {

        int request_type; //0:视频存储; 1:视频片段删除
        // Container for the data we expect from the server.
        VsReply reply;

        //暂时使用多个reply来承载,会有内存占用过多的问题，后续处理
        VsDelReply del_reply;
        // Context for the client. It could be used to convey extra information to
        // the server and/or tweak certain RPC behaviors.
        ClientContext context;

        // Storage for the status of the RPC upon completion.
        Status status;

        //ClientAsyncResponseReader<HelloReply> 客户端异步响应读取对象
        std::unique_ptr<ClientAsyncResponseReader<VsReply>> response_reader;

        std::unique_ptr<ClientAsyncResponseReader<VsDelReply>> del_response_reader;
        int32_t msg_id;
    };

    // Out of the passed in Channel comes the stub, stored here, our view of the
    // server's exposed services.
    std::unique_ptr<VideoStorageMsg::Stub> stub_;

    // The producer-consumer queue we use to communicate asynchronously with the
    // gRPC runtime.
    CompletionQueue cq_;
};

