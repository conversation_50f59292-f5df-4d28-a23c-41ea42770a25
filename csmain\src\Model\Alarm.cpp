#include "stdafx.h"
#include "Alarm.h"
#include <boost/algorithm/string.hpp>
#include "ConnectionPool.h"
#include "HttpRequest.h"
#include "MsgControl.h"
#include "util.h"
#include "dbinterface/UUID.h"


#define TABLE_NAME_ALARMS   _T("Alarms")

extern AKCS_CONF gstAKCSConf;

CAlarm* GetAlarmInstance()
{
    return CAlarm::GetInstance();
}

CAlarm::CAlarm()
{

}

CAlarm::~CAlarm()
{

}

CAlarm* CAlarm::instance = NULL;

CAlarm* CAlarm::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAlarm();
    }

    return instance;
}

//获取ALARM详细信息
int CAlarm::GetAlarm(uint32_t id, ALARM* alarm)
{
    return dbinterface::Alarm::GetAlarm(id, alarm);
}

//处理ALARM
int CAlarm::DealAlarm(ALARM* alarm)
{
    return dbinterface::Alarm::DealAlarm(alarm);
}

//添加ALARM
int CAlarm::AddAlarm(ALARM* alarm)
{
    if(gstAKCSConf.is_aws)
    {
        AwsInsertAlarm(alarm);
        return 0;
    }

    return dbinterface::Alarm::AddAlarm(alarm, gstAKCSConf.server_tag);
}

//更新告警状态
int CAlarm::DealAlarmStatus(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info)
{
    std::string user = alarm_deal_info.user;
    boost::replace_all(user, "'", "\\'");
    if(gstAKCSConf.is_aws)
    {
    
        //http到阿里云
        GetMsgControlInstance()->PostAwsDealAlarmHttpReq(TABLE_NAME_ALARMS, user, ATOI(alarm_deal_info.alarm_id));
        return 0;
    }
    ALARM_DEAL_INFO deal_info;
    Snprintf(deal_info.result, sizeof(deal_info.result), alarm_deal_info.result);
    Snprintf(deal_info.user, sizeof(deal_info.user), alarm_deal_info.user);
    Snprintf(deal_info.alarm_id, sizeof(deal_info.alarm_id), alarm_deal_info.alarm_id);
    return dbinterface::Alarm::DealAlarmStatus(deal_info);
}


//通过alarm id获取到告警解除时的相关信息
int CAlarm::GetAlarmInfo(const std::string& id, SOCKET_MSG_ALARM_DEAL_OFFLINE& alarm_info)
{
    ALARM_DEAL_OFFLINE_INFO offline_info;
    memset(&offline_info, 0, sizeof(offline_info));
    if (0 == dbinterface::Alarm::GetAlarmInfo(id, offline_info))
    {
        Snprintf(alarm_info.alarm_type, sizeof(alarm_info.alarm_type), offline_info.alarm_type);
        Snprintf(alarm_info.mac, sizeof(alarm_info.mac), offline_info.mac);
        Snprintf(alarm_info.device_location, sizeof(alarm_info.device_location), offline_info.device_location);
        alarm_info.manager_account_id = offline_info.manager_account_id;
        alarm_info.alarm_code = offline_info.alarm_code;
        alarm_info.alarm_zone = offline_info.alarm_zone;
        alarm_info.alarm_location = offline_info.alarm_location;
        alarm_info.alarm_customize = offline_info.alarm_customize;
        alarm_info.unit_id = offline_info.unit_id;
        alarm_info.trace_id = offline_info.trace_id;
        return 0;
    }
    return -1;
}

