#ifndef __ALARM_H__
#define __ALARM_H__
#pragma once
#include "dbinterface/AlarmDB.h"



class CAlarm
{
public:
    CAlarm();
    ~CAlarm();
    enum eAlarmStatus
    {
        UNDEAL = 0,
        DEALED,
    };

    enum eDealType
    {
        MANUAL = 0,
        DEAL,
        REVACTION,
    };

    //获取ALARM数量
    uint32_t GetAlarmCount(uint32_t alarm_status);

    //根据状态获取ALARM列表
    ALARM* GetAlarmList(uint32_t alarm_status, uint32_t item_from, uint32_t item_count);
    //added by yicong.chen
    void DestoryAlarmList(ALARM* alarm_header);

    //获取ALARM详细信息
    int GetAlarm(uint32_t id, ALARM* alarm);

    //删除ALARM
    int DeleteAlarm(uint32_t id);

    //处理ALARM
    int DealAlarm(ALARM* alarm);

    //添加ALARM
    int AddAlarm(ALARM* alarm);

    int DealAlarmStatus(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info);
    int GetAlarmInfo(const std::string& id, SOCKET_MSG_ALARM_DEAL_OFFLINE& alarm_info);

    static CAlarm* GetInstance();

private:
    static CAlarm* instance;

};

CAlarm* GetAlarmInstance();
#endif

