﻿#include "AppCallStatus.h"
#include <sstream>
#include "util.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "SDMCMsg.h"
#include "PersonalAccount.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/AppCallDndDB.h"
#include "AppPushToken.h"
#include "InnerSt.h"



extern std::map<string, AKCS_DST> g_time_zone_DST;
extern AKCS_CONF gstAKCSConf;
extern const char *g_redis_db_appdnd;
AppCallStatus& AppCallStatus::GetInstance()
{
	static AppCallStatus call_dnd;
	return call_dnd;
}

/**
 * xx:xx:xx转换成分钟数
 * 
 * <AUTHOR> (2021/1/19)
 * 
 * @param current_time 
 * 
 * @return int 
 */
int AppCallStatus::GetElapsedMinutes(std::string current_time) 
{
	int elapsed_minutes = 0;
	CStrExplode time_split(current_time.c_str(), ':');
	for (uint32_t i = 0; i < time_split.GetItemCnt(); i++)
	{
		char* time_unit = time_split.GetItem(i);
		switch (i)
		{
		case 0:
			elapsed_minutes += ATOI(time_unit) * 60;
			break;
		case 1:
			elapsed_minutes += ATOI(time_unit);
			break;
		default:
			break;
		}
	}

	return elapsed_minutes;
}

/**
 * StartTime和EndTime代表从零点开始到对应时间点的分钟数。
 * 假设StartTime用S，EndTime用E，要判断的时间用X标识。那么
 * 当E > S时候， S < X < E判断成立。
 * 当E< S时候，X< E 或者 X > S判断成立
 * 
 * <AUTHOR> (2021/1/19)
 * 
 * @param account 
 * 
 * @return int 
 */
APP_STATE AppCallStatus::GetAppState(const std::string& account)
{    
	APP_STATE app_state = APP_STATE_OFFLINE;
	AppDndInfo app_dnd_info;
    if(gstAKCSConf.is_aws == 0)
    {
        app_dnd_info = GetCacheDndInfo(account);
    }
    else    //只有在亚马逊迁移阶段去查db，避免缓存击穿
    {
        app_dnd_info = GetDbDndInfo(account);
    }
	AK_LOG_INFO << "GetAppState account=" << account << ";status=" << app_dnd_info.status << ";start_time=" << app_dnd_info.start_time << ";end_time=" << app_dnd_info.end_time;
    
	if (APP_CALL_STATUS_ENABLED == app_dnd_info.status)
	{
		if (app_dnd_info.start_time < 0 || app_dnd_info.start_time >= 1440)
		{
			return app_state;
		}

		if (app_dnd_info.end_time < 0 || app_dnd_info.end_time >= 1440)
		{
			return app_state;
		}

		string current_time;
		std::string time_zone;
        std::string node;

        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(account, per_account))
        {
            if (per_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || per_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
            {
                ResidentPerAccount main_account;
                memset(&main_account, 0, sizeof(main_account));
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, main_account))
                {
                    node = main_account.account;
                }
                else
                {
                    AK_LOG_WARN << "DaoGetNodeByAppUser failed,user=" << account;
                    return app_state;
                }
            }
            else
            {
                node = account;
            }
        }

        time_zone = dbinterface::ResidentPersonalAccount::GetNodeTimeZoneStr(node);
        GetWeekDayAndTimeByTimeZoneStr(time_zone, current_time, g_time_zone_DST);

		int elapsed_minutes = GetElapsedMinutes(current_time);
		AK_LOG_INFO << "Node=" << node << ";current_time=" << current_time << ";time_zone=" << time_zone << ";current_minutes=" << elapsed_minutes;

		if (app_dnd_info.start_time < app_dnd_info.end_time)
		{
			if (app_dnd_info.start_time < elapsed_minutes &&
				app_dnd_info.end_time > elapsed_minutes)
			{
				return APP_STATE_DND;
			}
		}
		else if (app_dnd_info.start_time > app_dnd_info.end_time)
		{
			if (elapsed_minutes < app_dnd_info.end_time ||
				elapsed_minutes > app_dnd_info.start_time)
			{
				return APP_STATE_DND;
			}
		}
        else if (app_dnd_info.start_time == app_dnd_info.end_time)
        {
            return APP_STATE_DND;
        }
		else
		{
			return app_state;
		}
	} 

	return app_state;
}

/**
 * 从redis缓存获取dnd信息
 * 查询不到缓存不再查询mysql，因为假设用户没有设置dnd，那么每通电话都要查询一次mysql，性能反倒不高
 * key = [sip_account][dndinfo]
 * 
 * <AUTHOR> (2021/1/19)
 * 
 * @param account 
 * 
 * @return AppDndInfo 
 */
AppDndInfo AppCallStatus::GetCacheDndInfo(string account)
{
	AppDndInfo app_dnd_info;
	memset(&app_dnd_info, 0, sizeof(app_dnd_info));

	CacheManager* cache_manager = CacheManager::getInstance();
	CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_appdnd); //获取与redis实例的tcp连接
	if (NULL == cache_conn)
	{
		return app_dnd_info;
	}

	std::string dnd_info;
	dnd_info = cache_conn->hget(account, "dndinfo");
	if (dnd_info.empty())
	{
		cache_manager->RelCacheConn(cache_conn);
		return app_dnd_info;
	}

	Snprintf(app_dnd_info.account, sizeof(app_dnd_info.account), account.c_str());
	CStrExplode explode(dnd_info.c_str(), '-');
	for (uint32_t i = 0; i < explode.GetItemCnt(); i++)
	{
		int value = ATOI(explode.GetItem(i));
		app_dnd_info.status = (i == 0) ? value : app_dnd_info.status;
		app_dnd_info.start_time = (i == 1) ? value : app_dnd_info.start_time;
		app_dnd_info.end_time = (i == 2) ? value : app_dnd_info.end_time;
	}

	cache_manager->RelCacheConn(cache_conn);
	return app_dnd_info;
}

AppDndInfo AppCallStatus::GetDbDndInfo(string account)
{
    AppDndInfo app_dnd_info;
    memset(&app_dnd_info, 0, sizeof(app_dnd_info));
    dbinterface::AppCallDnd::GetAppDndInfo(account, app_dnd_info);
    return app_dnd_info;
}