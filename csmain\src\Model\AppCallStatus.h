﻿#ifndef _APP_DND_H_
#define _APP_DND_H_

#include <boost/noncopyable.hpp>
#include <string>
#include "AkcsCommonDef.h"
#include "dbinterface/AppCallDndDB.h"


enum AppCallStats
{
	APP_CALL_STATUS_DISABLED = 0, //未开启
	APP_CALL_STATUS_ENABLED = 1 //已开启
};

class AppCallStatus : private boost::noncopyable
{
public:
    static AppCallStatus& GetInstance();

	APP_STATE GetAppState(const std::string& account);

	AppDndInfo GetCacheDndInfo(std::string account);

	AppDndInfo GetDbDndInfo(std::string account);
	
	int GetElapsedMinutes(std::string current_time);
};

#endif

