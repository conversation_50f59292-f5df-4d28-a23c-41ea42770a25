#include "stdafx.h"
#include "AppPushToken.h"
#include "AKUserMng.h"
#include "ConnectionPool.h"
#include "MsgControl.h"
#include "util.h"
#include "dbinterface/Token.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


#define TABLE_NAME_APP_PUSH_TOKEN   "AppPushToken"

extern AKCS_CONF gstAKCSConf;

CAppPushToken* GetAppPushTokenInstance()
{
    return CAppPushToken::GetInstance();
}

CAppPushToken* CAppPushToken::instance = NULL;

CAppPushToken::CAppPushToken()
{

}

CAppPushToken::~CAppPushToken()
{

}

CAppPushToken* CAppPushToken::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAppPushToken();
    }

    return instance;
}

int CAppPushToken::updateAppPushInfo(const std::string strUid, const CMobileToken& cToken)
{
    if(gstAKCSConf.is_aws)
    {
        //http到阿里云
        GetMsgControlInstance()->PostAwsPushTokenHttpReq(strUid, cToken);
        return 0;
    }

    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    token_info.app_oem = cToken.AppOem();
    token_info.mobile_type = cToken.MobileType();
    token_info.common_version = cToken.CommonVersion();
    Snprintf(token_info.fcm_token, sizeof(token_info.fcm_token), cToken.FcmToken().c_str());
    Snprintf(token_info.token, sizeof(token_info.token), cToken.Token().c_str());
    Snprintf(token_info.voip_token, sizeof(token_info.voip_token),cToken.VoipToken().c_str());
    Snprintf(token_info.language, sizeof(token_info.language),cToken.Language().c_str());
    Snprintf(token_info.oem_name, sizeof(token_info.oem_name),cToken.OemName().c_str());
    Snprintf(token_info.node, sizeof(token_info.node),cToken.UidNode().c_str());
    Snprintf(token_info.app_version, sizeof(token_info.app_version),cToken.AppVersion().c_str());
    token_info.is_dy_iv = cToken.IsDyIv();

    int ret = dbinterface::AppPushToken::UpdateAppPushInfo(strUid, token_info);
    return ret;
}
int CAppPushToken::deleteAppPushToken(const std::string strUid)
{
    if(gstAKCSConf.is_aws)
    {
        //http到阿里云
        GetMsgControlInstance()->PostAwsDelPushTokenHttpReq(strUid);
        return 0;
    }

    int ret = dbinterface::AppPushToken::DeleteAppPushToken(strUid);
    return ret;
}

bool CAppPushToken::isAppPushTokenExist(const std::string strUid)
{
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(strUid, token_info))
    {
        return true;
    }
    return false;
}

int CAppPushToken::getAppPushTokenByUid(const std::string strUid, CMobileToken& cToken)
{
    int ret = 0;
    int is_multi_site = dbinterface::ProjectUserManage::IsAccountMultiSite(strUid);
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(strUid, token_info))
    {
        cToken.setMobileType(token_info.mobile_type);
        cToken.setFcmToken(token_info.fcm_token);
        cToken.setToken(token_info.token);
        cToken.setVoipToken(token_info.voip_token);
        cToken.setCommonVersion(token_info.common_version);
        cToken.setLanguage(token_info.language);
        cToken.setOemName(token_info.oem_name);
        cToken.setAppOem(token_info.app_oem);
        cToken.setIsDyIv(token_info.is_dy_iv);
        cToken.setIsMultiSite(is_multi_site);
        AK_LOG_INFO << "Uid:" << strUid << " is_multi_site_ :" << cToken.IsMultiSite();
        ret = 0;
    }
    else
    {
        ret = -1;
    }

    return ret;
}

int CAppPushToken::getAppDcliVerByUid(const std::string& uid)
{
    int ver = 0;
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(uid, token_info))
    {
        ver = token_info.common_version;
    }
    return ver;
}


int CAppPushToken::getAppTokenByUid(const std::string uid, std::string& app_token)
{
    int ret = 0;
    TokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::Token::GetTokenInfoByAccount(uid, token_info))
    {
        app_token = token_info.app_token;
    }
    else
    {
        ret = -1;
    }

    return ret;
}



int CAppPushToken::getUidsApptokenByNode(const std::string node, std::vector<CMobileToken>& oVec)
{
    AppPushTokenList token_list;
    if (0 == dbinterface::AppPushToken::GetUidsAppTokenByNode(node, token_list))
    {
        for (const auto token_info : token_list)
        {
            CMobileToken cToken;
            cToken.setMobileType(token_info.mobile_type);
            cToken.setFcmToken(token_info.fcm_token);
            cToken.setToken(token_info.token);
            cToken.setVoipToken(token_info.voip_token);
            cToken.setCommonVersion(token_info.common_version);
            cToken.setLanguage(token_info.language);
            cToken.setOemName(token_info.oem_name);
            cToken.setAppOem(token_info.app_oem);
            oVec.push_back(cToken);
        }
    }

    return 0;
}



