#ifndef __APP_PUSH_TOKEN_H__
#define __APP_PUSH_TOKEN_H__
#include "AKUserMng.h"
#include "dbinterface/AppPushTokenDB.h"

#include <boost/noncopyable.hpp>
class CAppPushToken : boost::noncopyable
{
public:
    CAppPushToken();
    ~CAppPushToken();

    static CAppPushToken* GetInstance();
    int updateAppPushInfo(const std::string strUid, const CMobileToken& cToken);
    int deleteAppPushToken(const std::string strUid);
    bool isAppPushTokenExist(const std::string strUid);
    int getAppPushTokenByUid(const std::string strUid, CMobileToken& cToken);
    //获取appToken
    int getAppTokenByUid(const std::string uid, std::string& app_token);
    int getUidsApptokenByNode(const std::string node, std::vector<CMobileToken>& oVec); 
    int getAppDcliVerByUid(const std::string& uid);
private:

    static CAppPushToken* instance;

};

CAppPushToken* GetAppPushTokenInstance();

#endif //__APP_TOKEN_H__

