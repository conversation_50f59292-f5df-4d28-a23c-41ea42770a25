#ifndef __CSMAIN_AES_INCLUDED__
#define __CSMAIN_AES_INCLUDED__

#include "AES256.h"

//added by chenyc,2023.05.17,该函数存在栈溢出的风险，新业务禁止再使用,统一使用V2新接口
int AesEncryptByMac(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize);
//added by chenyc,2023.05.17,该函数存在栈溢出的风险，新业务禁止再使用,统一使用V2新接口
int AesEncryptByDefault(char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);

//max_size:加密后消息的最大允许长度
int AesEncryptByMacV2(const char* pIn, char* pOut, const std::string& strMac, uint32_t* pdata_size, const uint32_t max_allowed_size);

int AesDecryptByMac(char* pIn, char* pOut, const std::string& strMac, int nDataSize);
int AesDecryptByDefault(char* pIn, char* pOut, int nDataSize);
int AesDecryptByDefaultForReportStatus(char* pIn, char* pOut, int nDataSize, int &dy_iv);

//TODO:用于解密发送给设备得信令内容，用AesDecryptByMac会段错误，先简单处理
int AesDecryptByMac2(char* pIn, char* pOut, const std::string& strMac, int nDataSize);
int AES_256_ENCRYPT_With_IV(unsigned char* in, unsigned char* out, unsigned char* key, int nSize);
int AesEncryptByDefaultMac(const char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);
int AesEncryptByDefaultMacDynamicsIV(const char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);
int AesEncryptByDefaultForDynamicsIV(char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);


#endif
