#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "BasicDefine.h"
#include "AkLogging.h"
#include "DevUpdateUserLog.h"
#include "dbinterface/DevUpdateUserLogDB.h"

int DevUpdateUserLog::InsertLog(const SOCKET_MSG_USER_INFO &info)
{
    DevUpdateUserLogInfo log_info;
    memset(&log_info, 0, sizeof(log_info));
    log_info.traceid = info.traceid;
    ::snprintf(log_info.mac, sizeof(log_info.mac), "%s", info.mac);
    ::snprintf(log_info.uuids, sizeof(log_info.uuids), "%s", info.uuids);
    
    return dbinterface::DevUpdateUserLog::InsertLog(log_info);
}

