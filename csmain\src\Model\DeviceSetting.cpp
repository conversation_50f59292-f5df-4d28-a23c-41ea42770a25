#include "stdafx.h"
#include "util.h"
#include "DeviceSetting.h"
#include "PersonnalDeviceSetting.h"
#include "AkcsMysqlSegFlag.h"
#include "ConnectionPool.h"
#include "dbinterface/PubDevMngList.h"



#define TABLE_NAME_DEVICES  "Devices"

CDeviceSetting* GetDeviceSettingInstance()
{
    return CDeviceSetting::GetInstance();
}

CDeviceSetting::CDeviceSetting()
{

}

CDeviceSetting::~CDeviceSetting()
{

}

CDeviceSetting* CDeviceSetting::instance = NULL;

CDeviceSetting* CDeviceSetting::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceSetting();
    }

    return instance;
}

int CDeviceSetting::GetDeviceSettingFromDev(ResidentDev dev, DEVICE_SETTING* device_setting)
{
    if (strlen(dev.mac))
    {
        device_setting->id = dev.id;
        device_setting->type = dev.dev_type;
        device_setting->manager_account_id = dev.project_mng_id;
        device_setting->unit_id = dev.unit_id;
        Snprintf(device_setting->device_node, sizeof(device_setting->device_node), dev.node);
        Snprintf(device_setting->mac, sizeof(device_setting->mac), dev.mac);
        device_setting->grade = dev.grade;
        if (device_setting->grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            device_setting->is_public = 1;
        }
        device_setting->is_personal = 0;

        Snprintf(device_setting->ip_addr, sizeof(device_setting->ip_addr), dev.ipaddr);
        Snprintf(device_setting->wired_ip_addr, sizeof(device_setting->wired_ip_addr), dev.wired_ipaddr);
        Snprintf(device_setting->gateway, sizeof(device_setting->gateway), dev.gateway);
        Snprintf(device_setting->subnet_mask, sizeof(device_setting->subnet_mask), dev.subnet_mask);
            Snprintf(device_setting->wired_subnet_mask, sizeof(device_setting->wired_subnet_mask), dev.wired_subnet_mask);
        Snprintf(device_setting->primary_dns, sizeof(device_setting->primary_dns), dev.primary_dns);
        Snprintf(device_setting->secondary_dns, sizeof(device_setting->secondary_dns), dev.secondary_dns);
        Snprintf(device_setting->SWVer, sizeof(device_setting->SWVer), dev.sw_ver);
        Snprintf(device_setting->HWVer, sizeof(device_setting->HWVer), dev.hw_ver);
        device_setting->status = dev.status;
        Snprintf(device_setting->outer_ip, sizeof(device_setting->outer_ip), dev.outer_ip);
        device_setting->port = dev.port;
        Snprintf(device_setting->last_connection, sizeof(device_setting->last_connection), dev.last_connection);
        Snprintf(device_setting->private_key_md5, sizeof(device_setting->private_key_md5), dev.private_key_md5);
        Snprintf(device_setting->rf_id_md5, sizeof(device_setting->rf_id_md5), dev.rf_id_md5);
        Snprintf(device_setting->config_md5, sizeof(device_setting->config_md5), dev.config_md5);
        Snprintf(device_setting->sip_account, sizeof(device_setting->sip_account), dev.sip);
        Snprintf(device_setting->contact_md5, sizeof(device_setting->contact_md5), dev.contact_md5);
        device_setting->dclient_version = dev.dclient_ver;
        Snprintf(device_setting->location, sizeof(device_setting->location), dev.location);
        Snprintf(device_setting->auth_code, sizeof(device_setting->auth_code), dev.auth_code);
        Snprintf(device_setting->face_md5, sizeof(device_setting->face_md5), dev.face_md5);
        Snprintf(device_setting->schedule_md5, sizeof(device_setting->schedule_md5), dev.schedule_md5);
        Snprintf(device_setting->user_meta_md5, sizeof(device_setting->user_meta_md5), dev.user_mate_md5);        
        device_setting->flags = dev.flags;
        device_setting->project_type = dev.project_type;
        Snprintf(device_setting->uuid, sizeof(device_setting->uuid), dev.uuid);
        device_setting->brand = dev.brand;
        device_setting->is_ipv6 = dev.is_ipv6;
        device_setting->dynamics_iv = dev.is_dy_iv;
        device_setting->repost = dev.repost; 
        device_setting->firmware = dev.firmware;
        device_setting->fun_bit = dev.fun_bit;
        Snprintf(device_setting->uuid, sizeof(device_setting->uuid), dev.uuid);
        Snprintf(device_setting->project_uuid, sizeof(device_setting->project_uuid), dev.project_uuid); 
        device_setting->conn_version = dev.conn_version;
        return 0;
    }

    return -1;
}

int CDeviceSetting::GetPerDeviceSettingFromDev(ResidentDev dev, DEVICE_SETTING* device_setting)
{
    if (strlen(dev.mac))
    {
        device_setting->id = dev.id;
        device_setting->type = dev.dev_type;
        //插入社区
        Snprintf(device_setting->community, sizeof(device_setting->community), dev.community);
        //modify bu chenyc,2017-07-07,不再补齐地址节点
        //Snprintf(device_setting->device_node, sizeof(device_setting->device_node)/sizeof(TCHAR), device_node.GetBuffer());
        Snprintf(device_setting->device_node, sizeof(device_setting->device_node), dev.node);
        device_setting->extension = dev.extension;
        Snprintf(device_setting->ip_addr, sizeof(device_setting->ip_addr), dev.ipaddr);
        Snprintf(device_setting->wired_ip_addr, sizeof(device_setting->wired_ip_addr), dev.wired_ipaddr);
        Snprintf(device_setting->gateway, sizeof(device_setting->gateway), dev.gateway);
        Snprintf(device_setting->subnet_mask, sizeof(device_setting->subnet_mask), dev.subnet_mask);
        Snprintf(device_setting->wired_subnet_mask, sizeof(device_setting->wired_subnet_mask), dev.wired_subnet_mask);
        Snprintf(device_setting->primary_dns, sizeof(device_setting->primary_dns), dev.primary_dns);
        Snprintf(device_setting->secondary_dns, sizeof(device_setting->secondary_dns), dev.secondary_dns);
        Snprintf(device_setting->mac, sizeof(device_setting->mac), dev.mac);
        Snprintf(device_setting->SWVer, sizeof(device_setting->SWVer), dev.sw_ver);
        Snprintf(device_setting->HWVer, sizeof(device_setting->HWVer), dev.hw_ver);
        device_setting->status = dev.status;
        Snprintf(device_setting->outer_ip, sizeof(device_setting->outer_ip), dev.outer_ip);
        device_setting->port = dev.port;
        Snprintf(device_setting->last_connection, sizeof(device_setting->last_connection), dev.last_connection);
        Snprintf(device_setting->private_key_md5, sizeof(device_setting->private_key_md5), dev.private_key_md5);
        Snprintf(device_setting->rf_id_md5, sizeof(device_setting->rf_id_md5), dev.rf_id_md5);
        Snprintf(device_setting->config_md5, sizeof(device_setting->config_md5), dev.config_md5);
        //把sip账号也查出来,方便后续组装个人终端用户的联动单元
        Snprintf(device_setting->sip_account, sizeof(device_setting->sip_account), dev.sip);
        Snprintf(device_setting->sip_password, sizeof(device_setting->sip_password), dev.sippwd);
        Snprintf(device_setting->rtsp_password, sizeof(device_setting->rtsp_password), dev.rtsppwd);
        Snprintf(device_setting->location, sizeof(device_setting->location), dev.location);
        Snprintf(device_setting->contact_md5, sizeof(device_setting->contact_md5), dev.contact_md5);
        device_setting->dclient_version = dev.dclient_ver;
        device_setting->flag = dev.flag;
        if (device_setting->flag == 1)
        {
            device_setting->is_public = 1;
        }
        Snprintf(device_setting->auth_code, sizeof(device_setting->auth_code), dev.auth_code);
        Snprintf(device_setting->face_md5, sizeof(device_setting->face_md5), dev.face_md5);
        Snprintf(device_setting->schedule_md5, sizeof(device_setting->schedule_md5), dev.schedule_md5);
        Snprintf(device_setting->user_meta_md5, sizeof(device_setting->user_meta_md5), dev.user_mate_md5); 
        device_setting->flags = dev.flags;
        Snprintf(device_setting->uuid, sizeof(device_setting->uuid), dev.uuid);
        device_setting->brand = dev.brand;
        device_setting->is_personal = 1;
        device_setting->is_ipv6 = dev.is_ipv6;
        device_setting->dynamics_iv = dev.is_dy_iv;
        device_setting->repost = dev.repost;
        device_setting->firmware = dev.firmware;
        device_setting->fun_bit = dev.fun_bit;

        //查询管理员id
        dbinterface::AccountInfo account;
        if (dbinterface::Account::GetAccountInfoByAccount(dev.community, account) == 0)
        {
            device_setting->manager_account_id = account.id;
        }

        device_setting->conn_version = dev.conn_version;
        return 0;
    }

    return -1;
}

//根据ID获取设备设置信息
int CDeviceSetting::GetDeviceSetting(uint32_t id, DEVICE_SETTING* device_setting)
{
    ResidentDev dev;
    dbinterface::ResidentDevices::GetDevByID(id, dev);
    return GetDeviceSettingFromDev(dev, device_setting);
}

//根据MAC获取设备设置信息
int CDeviceSetting::GetDeviceSettingByMac(CString mac, DEVICE_SETTING* device_setting)
{
    ResidentDev dev;
    if (dbinterface::ResidentDevices::GetMacDev(mac.GetBuffer(), dev) == 0)
    {
        GetDeviceSettingFromDev(dev, device_setting);
        if (device_setting->project_type == project::OFFICE)
        {
            device_setting->device_type = csmain::OFFICE_DEV;  //住宅设备
        }
        else 
        {
            device_setting->device_type = csmain::COMMUNITY_DEV;  //社区设备
        }
        return 0;
    }
    else if (dbinterface::ResidentPerDevices::GetMacDev(mac.GetBuffer(), dev) == 0)
    {
        GetPerDeviceSettingFromDev(dev, device_setting);
        device_setting->project_type = project::PERSONAL;
        device_setting->device_type = csmain::PERSONNAL_DEV;  //个人终端设备
        return 0;
    }

    return -1;//查询到空值
}

int CDeviceSetting::GetDevInfoByMac(const std::string& mac, ResidentDev& dev)
{
    if (0 != dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        if(0 != dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
        {
            return -1;
        }
    }

    return 0;
}

int CDeviceSetting::GetRelayByMac(const std::string& mac, std::string& dev_relay, int dev_type)
{
    ResidentDev dev;
    ResidentDev per_dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        dev_relay = dev.relay;
        return 0;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        dev_relay = per_dev.relay;
        return 0;
    }

    return -1;
}

int CDeviceSetting::GetAllRelayByMac(const std::string& mac, std::string& dev_relay, std::string& security_relay)
{
    ResidentDev dev;
    ResidentDev per_dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        dev_relay = dev.relay;
        security_relay = dev.security_relay;
        return 0;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        dev_relay = per_dev.relay;
        security_relay = per_dev.security_relay;
        return 0;
    }

    return -1;
}

int CDeviceSetting::GetDevicesByNode(std::string node, int is_per, std::vector<DEVICE_SETTING>& devs)
{
    if (node.empty())
    {
        AK_LOG_WARN << "parameter error! node [" << node << "] is null!";
        return -1;
    }

    ResidentDeviceList devlist;
    ResidentDeviceList per_devlist;
    if (0 == dbinterface::ResidentDevices::GetNodeDevList(node, devlist))
    {
        for (const auto dev : devlist)
        {
            DEVICE_SETTING device_setting = {0};
            memset(&device_setting, 0, sizeof(device_setting));
            Snprintf(device_setting.mac, sizeof(device_setting.mac), dev.mac);
            device_setting.indoor_arming = dev.arming;
            device_setting.type = dev.dev_type;
            device_setting.status = dev.status;
            devs.push_back(device_setting);
        }
        return 0;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetNodeDevList(node, per_devlist))
    {
        for (const auto dev : per_devlist)
        {
            DEVICE_SETTING device_setting = {0};
            memset(&device_setting, 0, sizeof(device_setting));
            Snprintf(device_setting.mac, sizeof(device_setting.mac), dev.mac);
            device_setting.indoor_arming = dev.arming;
            device_setting.type = dev.dev_type;
            device_setting.status = dev.status;
            devs.push_back(device_setting);
        }
        return 0;
    }

    return -1;
}


//根据MAC获取设备设置信息
int CDeviceSetting::GetDeviceSettingByID(int nID, int is_personal, DEVICE_SETTING* device_setting)
{
    if (device_setting == NULL)
    {
        return -1;
    }

    ResidentDev dev;
    if (!is_personal)
    {
        if (0 == dbinterface::ResidentDevices::GetDevByID(nID ,dev))
        {
            GetDeviceSettingFromDev(dev, device_setting);
            device_setting->device_type = csmain::COMMUNITY_DEV;
            return 0;
        }
    }
    else
    {
        if (0 == dbinterface::ResidentPerDevices::GetDevByID(nID ,dev))
        {
            GetPerDeviceSettingFromDev(dev, device_setting);
            device_setting->device_type = csmain::PERSONNAL_DEV;
            return 0;
        }
    }
    return -1;
}


//根据Sip获取设备设置信息
int CDeviceSetting::GetDeviceSettingBySip(const CString& sip, DEVICE_SETTING* device_setting)
{
    if (device_setting == NULL)
    {
        return -1;
    }

    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetSipDev(sip.GetBuffer() ,dev))
    {
        GetDeviceSettingFromDev(dev, device_setting);
        device_setting->device_type = csmain::COMMUNITY_DEV;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetSipDev(sip.GetBuffer() ,dev))
    {
        GetPerDeviceSettingFromDev(dev, device_setting);
        device_setting->device_type = csmain::PERSONNAL_DEV;
        return 0;
    }
    return -1;
}

//社区个人终端用户
//TODO: 这个接口应该是没有用了
int CDeviceSetting::GetDeviceSettingByNode(const std::string& strNode, int type, std::vector<COMMUNITY_DEVICE_SIP>& oDev)
{
    if (strNode.empty())
    {
        AK_LOG_WARN << "parameter error! node [" << strNode << "] is null!";
        return -1;
    }

    ResidentDeviceList devlist;
    if (type != DEVICE_TYPE_INDOOR)
    {
        if (0 == dbinterface::ResidentDevices::GetNodeIndoorDevList(strNode, devlist))
        {
            for (const auto dev : devlist)
            {
                COMMUNITY_DEVICE_SIP tmp_device;
                memset(&tmp_device, 0, sizeof(tmp_device));
                Snprintf(tmp_device.name, sizeof(tmp_device.name), dev.location);
                Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), dev.sip);
                Snprintf(tmp_device.ip, sizeof(tmp_device.ip), dev.ipaddr);
                tmp_device.type = dev.dev_type;
                Snprintf(tmp_device.mac, sizeof(tmp_device.mac), dev.mac);
                Snprintf(tmp_device.rtsp_password, sizeof(tmp_device.rtsp_password), dev.rtsppwd);
                Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), dev.uuid);
                oDev.push_back(tmp_device);
            }
        }
    }
    else //只有室内机才查出室外机
    {
        if (0 == dbinterface::ResidentDevices::GetNodeDevList(strNode, devlist))
        {
            for (const auto dev : devlist)
            {
                COMMUNITY_DEVICE_SIP tmp_device;
                memset(&tmp_device, 0, sizeof(tmp_device));
                Snprintf(tmp_device.name, sizeof(tmp_device.name), dev.location);
                Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), dev.sip);
                Snprintf(tmp_device.ip, sizeof(tmp_device.ip), dev.ipaddr);
                tmp_device.type = dev.dev_type;
                Snprintf(tmp_device.mac, sizeof(tmp_device.mac), dev.mac);
                Snprintf(tmp_device.rtsp_password, sizeof(tmp_device.rtsp_password), dev.rtsppwd);
                Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), dev.uuid);
                oDev.push_back(tmp_device);
            }
        }
    }

    return 0;

}

int CDeviceSetting::GetLocationAndNodeBySip(const std::string& Sip, std::string& location, std::string& node)
{
    ResidentDev dev;
    int is_pub = 0;
    if (0 == dbinterface::ResidentDevices::GetSipDev(Sip, dev))
    {
        if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC 
            || dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            is_pub = 1;
        }
        location = dev.location;
        node = dev.node;
    }

    return is_pub;
}

int CDeviceSetting::GetLocationAndNodeAndMngIDBySip(const std::string& Sip, std::string& location, std::string& node, int& nMngID)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetSipDev(Sip, dev))
    {
        nMngID = dev.project_mng_id;
        location = dev.location;
        node = dev.node;
        return 0;
    }

    return -1;
}

bool CDeviceSetting::DeviceIsManageBuilding(uint32_t type)
{

    if(DEVICE_TYPE_MANAGEMENT == type ||
        DEVICE_TYPE_DOOR == type ||
        DEVICE_TYPE_STAIR == type ||
        DEVICE_TYPE_ACCESS == type) 
    {
        return true;
    }

    return false;
}

int CDeviceSetting::IsManageBuilding(const std::string& mac, int unit)
{
    int dev_id = 0;
    int flag = 0;
    int ret = 0;
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        int tmp_dlag = dev.flags;
        flag = tmp_dlag&(1 << 3)?1:0;   //是否全管理
        dev_id = dev.id;
    }

    if(flag)
    {
        ret = 1;
    }
    else
    {
        return dbinterface::PubDevMngList::IsManageBuilding(dev_id, unit);
    }
    return ret;
}

int CDeviceSetting::DaoGetMacRtspPwd(const std::string& mac, std::string& pwd)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        pwd = dev.rtsppwd;
    }
    else 
    {
        return -1;
    }

    return 0;
}

int CDeviceSetting::GetDeviceArmingStatus(const std::string& mac)
{
    int mode = 0;
    ResidentDev dev;
    ResidentDev per_dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        mode = dev.arming;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        mode = per_dev.arming;
    }

    return mode;
}

int CDeviceSetting::GetDevTypeBySip(const std::string& sip, int& dev_type)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetSipDev(sip, dev))
    {
        dev_type = dev.dev_type;
        return 0;
    }

    return -1;
}

bool CDeviceSetting::CanDevSupportInterceptEmptyAuthCode(uint64_t func_bit)
{
    //设备是否支持拦截空authcode
    return SwitchHandle(func_bit, FUNC_DEV_SUPPORT_INTERCEPT_EMPTY_AUTHCODE) == 1;
}

