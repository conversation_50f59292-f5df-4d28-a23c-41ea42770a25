#ifndef __DEVICE_SETTING_H__
#define __DEVICE_SETTING_H__
#include <string>
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Account.h"


enum
{
    TAB_DEVICES_INDEX_ID = 0,
    TAB_DEVICES_INDEX_TYPE,
    TAB_DEVICES_INDEX_COMMUNITY,
    TAB_DEVICES_INDEX_DEVICENODE,
    TAB_DEVICES_INDEX_EXTENSION,
    TAB_DEVICES_INDEX_IPADDRESS,
    TAB_DEVICES_INDEX_GATEWAY,
    TAB_DEVICES_INDEX_SUBNETMASK,
    TAB_DEVICES_INDEX_PRIMARYDNS,
    TAB_DEVICES_INDEX_SECONDARYDNS,
    TAB_DEVICES_INDEX_MAC,
    TAB_DEVICES_INDEX_FIRMWARE,
    TAB_DEVICES_INDEX_HARDWARE,
    TAB_DEVICES_INDEX_STATUS,
    TAB_DEVICES_INDEX_OUTERIP, //增加外网IP
    TAB_DEVICES_INDEX_PORT,
    TAB_DEVICES_INDEX_LASTCONN,
    TAB_DEVICES_INDEX_PRIVATEKEYMD5,
    TAB_DEVICES_INDEX_RFIDMD5,
    TAB_DEVICES_INDEX_CONFIGSETTINGS,
    TAB_DEVICES_INDEX_CONFIGMD5,
    TAB_DEVICES_INDEX_SIPACCOUNT,
    TAB_DEVICES_INDEX_BINDAPPCOUNT,
};

class CDeviceSetting
{
public:
    CDeviceSetting();
    ~CDeviceSetting();

    int GetDeviceSettingFromDev(ResidentDev dev, DEVICE_SETTING* device_setting);
    int GetPerDeviceSettingFromDev(ResidentDev dev, DEVICE_SETTING* device_setting);

    //根据ID获取设备设置信息
    int GetDeviceSetting(uint32_t id, DEVICE_SETTING* device_setting);

    //根据MAC获取设备设置信息
    int GetDeviceSettingByMac(CString mac, DEVICE_SETTING* device_setting);

    //根据DeviceNode和Extension获取设备设置信息
    int GetDeviceSettingByNode(const std::string& node, int type, std::vector<COMMUNITY_DEVICE_SIP>& device);

    static CDeviceSetting* GetInstance();

    int GetLocationAndNodeBySip(const std::string& Sip, std::string& location, std::string& node);

    int GetDeviceSettingBySip(const CString& sip, DEVICE_SETTING* device_setting);
    //http 维护通道
    int GetAllDeviceSettingCount();

    int DaoGetMacRtspPwd(const std::string& mac, std::string& pwd);
    int GetDeviceSettingByID(int id, int is_personal, DEVICE_SETTING* device_setting);
    int GetLocationAndNodeAndMngIDBySip(const std::string& Sip, std::string& location, std::string& node, int& manager_id);
    int GetDeviceArmingStatus(const std::string& mac);
    //只有获取所需的字段
    int GetDevicesByNode(std::string node, int is_per, std::vector<DEVICE_SETTING>& devs);
    //获取某设备的信息
    int GetDevInfoByMac(const std::string& mac, ResidentDev& dev);
    int GetDevTypeBySip(const std::string& sip, int& dev_type);
    int IsManageBuilding(const std::string& mac, int unit);
    bool DeviceIsManageBuilding(uint32_t type);
    //获取设备Relay
    int GetRelayByMac(const std::string& mac, std::string& dev_relay, int dev_type);
    int GetAllRelayByMac(const std::string& mac, std::string& dev_relay, std::string& security_relay);

    //当前设备是否支持拦截空authcode
    bool CanDevSupportInterceptEmptyAuthCode(uint64_t func_bit);

private:
    static CDeviceSetting* instance;

};
CDeviceSetting* GetDeviceSettingInstance();

#endif
