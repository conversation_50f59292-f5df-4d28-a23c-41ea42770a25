#ifndef __ERROR_CONNECT_H__
#define __ERROR_CONNECT_H__

#include <boost/noncopyable.hpp>
#include "SDMCMsg.h"
#include "dbinterface/ErrorConnectDB.h"


enum ECONNECT_TYPE
{
    E_CONNECT_UNREPORTSTATU = 1, //未上报状态或上报状态时出错
    E_CONNECT_MAC_CHECKCODE = 2, //authcode校验不通过
    E_CONNECT_UN_REGIST = 3, //设备未登记在云上
    E_CONNECT_REPORT_EMPTY_AUTHCODE = 4 //空authcode拦截
};

class CErrorConnect : public boost::noncopyable
{
public:
    CErrorConnect()
    {
    }
    ~CErrorConnect()
    {
    }
    int InsertEmptyAuthCodeError(const std::string& ip, const std::string& mac)
    {
        return dbinterface::ErrorConnect::InsertErrorConnect(ip, E_CONNECT_REPORT_EMPTY_AUTHCODE, mac);
    }
    int InsertCheckCodeError(const std::string& ip, const std::string& mac, const std::string& report_auth_code, const std::string& mac_pool_auth_code)
    {
        return dbinterface::ErrorConnect::InsertAuthCodeErrorConnect(ip, E_CONNECT_MAC_CHECKCODE, mac, report_auth_code, mac_pool_auth_code);
    }
    int InsertUnRegistError(const std::string& ip, const std::string& mac)
    {
        return dbinterface::ErrorConnect::InsertErrorConnect(ip, E_CONNECT_UN_REGIST, mac);
    }
    int InsertUnReportStatuError(const std::string& ip)
    {
        return dbinterface::ErrorConnect::InsertErrorConnect(ip, E_CONNECT_UNREPORTSTATU, "");
    }
    static CErrorConnect* GetInstance();
private:

    static CErrorConnect* instance;

};

CErrorConnect* GetErrorConnectInstance();

#endif //__PERSONNAL_ACCOUNT_H__
