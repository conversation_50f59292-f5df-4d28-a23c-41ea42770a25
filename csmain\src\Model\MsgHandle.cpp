#include "stdafx.h"
#include "CsmainAES256.h"
#include "RouteClient.h"
#include "MsgHandle.h"
#include "AkcsServer.h"
#include "AkcsWebMsgSt.h"
#include "util.h"
#include "XmlTagDefine.h"
#include "XmlMsgBuild.h"
#include "stdlib.h"
#include "DclientMsgDef.h"
#include "InnerSt.h"

extern AKCS_CONF gstAKCSConf;

#define XML_NODE_NAME_MSG                   "Msg"
#define XML_NODE_NAME_MSG_TYPE              "Type"
#define XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY "CheckTmpKey"
#define XML_NODE_NAME_MSG_TYPE_BIND_CODE    "GetBindCode"
#define XML_NODE_NAME_MSG_TYPE_UNBIND_CODE  "UnBindCode"
#define XML_NODE_NAME_MSG_TYPE_BIND_LIST    "GetBindList"
#define XML_NODE_NAME_MSG_TYPE_POST_APP_CODE   "PostAppBindCode"
#define XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL  "DealAlarm"
#define XML_NODE_NAME_MSG_TYPE_REPORT_STATUS   "ReportStatus"

#define XML_NODE_NAME_MSG_TYPE_REQUEST_DEV_LIST   "RequestDevList" //个人终端用户设备获取联动单元设备列表
#define XML_NODE_NAME_MSG_TYPE_REPORT_MOTION_ALERT   "MotionAlert" //设备上报motion alert
#define XML_NODE_NAME_MSG_TYPE_SET_MOTION_ALERT      "SetMotionAlert" //设备上报是否接受motion alert的状态
#define XML_NODE_NAME_MSG_TYPE_REQ_ARMING            "RequestArming" //设备进行布防、撤防的信令
#define XML_NODE_NAME_MSG_TYPE_REPORT_ARMING         "ReportArming" //设备进行布防、撤防的信令
#define XML_NODE_NAME_MSG_TYPE_REPORT_ACTIVITY       "ReportAct" //设备上报动作(开门)记录
#define XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE       "ReqCapture" //app请求对某个设备的rtsp流进行截图
#define XML_NODE_NAME_MSG_TYPE_REQUEST_VIDEO_STORAGE      "ReqVideoStorage" //app请求对某个设备的rtsp流进行截图

#define XML_NODE_NAME_MSG_PROTOCAL          "Protocal"
#define XML_NODE_NAME_MSG_THIRD_PARTY       "ThirdParty"
#define XML_NODE_NAME_MSG_PARAM             "Params"
#define XML_NODE_NAME_MSG_PARAM_IP          "IP"
#define XML_NODE_NAME_MSG_PARAM_SUBNETMASK  "SM"
#define XML_NODE_NAME_MSG_PARAM_GATEWAY     "GW"
#define XML_NODE_NAME_MSG_PARAM_PRIMARYDNS  "DNS1"
#define XML_NODE_NAME_MSG_PARAM_SECONDARYDNS    "DNS2"
#define XML_NODE_NAME_MSG_PARAM_PORT        "Port"
#define XML_NODE_NAME_MSG_PARAM_DEVICEID    "DeviceID"
#define XML_NODE_NAME_MSG_PARAM_EXTENSION   "Extension"
#define XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER  "DownloadServer"
#define XML_NODE_NAME_MSG_PARAM_MAC         "MAC"
#define XML_NODE_NAME_MSG_PARAM_STATUS      "Status"
#define XML_NODE_NAME_MSG_PARAM_SWVER       "SWVer"
#define XML_NODE_NAME_MSG_PARAM_HWVER       "HWVer"
#define XML_NODE_NAME_MSG_PARAM_PRIKEYURL   "PrikeyUrl"
#define XML_NODE_NAME_MSG_PARAM_PRIKEYMD5   "PrikeyMD5"
#define XML_NODE_NAME_MSG_PARAM_RFIDURL     "RfidUrl"
#define XML_NODE_NAME_MSG_PARAM_RFIDMD5     "RfidMD5"
#define XML_NODE_NAME_MSG_PARAM_CONFIGURL   "ConfigUrl"
#define XML_NODE_NAME_MSG_PARAM_CONFIGMD5   "ConfigMD5"
#define XML_NODE_NAME_MSG_PARAM_ADDRURL     "AddrUrl"
#define XML_NODE_NAME_MSG_PARAM_ADDRMD5     "AddrMD5"
#define XML_NODE_NAME_MSG_PARAM_CONFIGMD5   "ConfigMD5"
#define XML_NODE_NAME_MSG_PARAM_ITEM        "Item"
#define XML_NODE_NAME_MSG_PARAM_MODULE      "Module"
#define XML_NODE_NAME_MSG_PARAM_PATH        "Path"
#define XML_NODE_NAME_MSG_PARAM_SIZE        "Size"
#define XML_NODE_NAME_MSG_PARAM_MD5         "MD5"
#define XML_NODE_NAME_MSG_PARAM_KEYTYPE     "KeyType"
#define XML_NODE_NAME_MSG_PARAM_KEYVALUE    "KeyValue"
#define XML_NODE_NAME_MSG_PARAM_MSGID       "MsgID"
#define XML_NODE_NAME_MSG_PARAM_MSGCRC      "MsgCRC"
#define XML_NODE_NAME_MSG_PARAM_RESULT      "Result"
#define XML_NODE_NAME_MSG_PARAM_INFO        "Info"
#define XML_NODE_NAME_MSG_PARAM_FROM        "From"
#define XML_NODE_NAME_MSG_PARAM_TO          "To"
#define XML_NODE_NAME_MSG_PARAM_MODE        "Mode"
#define XML_NODE_NAME_MSG_PARAM_FIRMWAREVER "FirmwareVer"
#define XML_NODE_NAME_MSG_PARAM_FIRMWAREURL "FirmwareUrl"
#define XML_NODE_NAME_MSG_PARAM_TITLE       "Titel"
#define XML_NODE_NAME_MSG_PARAM_CONTENT     "Content"
#define XML_NODE_NAME_MSG_PARAM_TIME        "Time"
#define XML_NODE_NAME_MSG_PARAM_TYPE        "Type"
#define XML_NODE_NAME_MSG_PARAM_URL         "Url"
#define XML_NODE_NAME_MSG_PARAM_URL2         "URL"
#define XML_NODE_NAME_MSG_PARAM_DURATION    "Duration"
#define XML_NODE_NAME_MSG_PARAM_COUNT       "Count"
#define XML_NODE_NAME_MSG_PARAM_ID          "ID"
#define XML_NODE_NAME_MSG_PARAM_ADDRESS     "Address"
#define XML_NODE_NAME_MSG_PARAM_DEVICETYPE  "DeviceType"
#define XML_NODE_NAME_MSG_PARAM_FORCE       "Force"
#define XML_NODE_NAME_MSG_PARAM_HEARTBEAT   "HeartBeatPeriod"
#define XML_NODE_NAME_MSG_DEV_NAME          "DevName"
#define XML_NODE_NAME_MSG_PARAM_MSGUUID     "MsgUUID" //pm一键开门,pmlog的uuid

#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_FILE        "File"
#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_SHOWTIME        "ShowTime"
#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_DURATION        "Duration"

#define XML_NODE_NAME_MSG_PARAM_USER        "User"
#define XML_NODE_NAME_MSG_PARAM_CODE        "Code"

/* Begin added by chenyc,2017-01-09,增加手机APP与云平台的通信 */
#define XML_NODE_NAME_MSG_PARAM_APP_NAME   "AppName"
#define XML_NODE_NAME_MSG_PARAM_DEV_MAC    "DevMAC"
#define XML_NODE_NAME_MSG_PARAM_APP_NO     "AppNo"
/* End added by chenyc,2017-01-09,增加手机APP与云平台的通信 */

#define XML_NODE_NAME_MSG_PARAM_TMPKEY          "TmpKey"
#define XML_NODE_NAME_MSG_PARAM_MSGSEQ          "MsgSeq"
#define XML_NODE_NAME_MSG_PARAM_BINDCODE        "BindCode"
#define XML_NODE_NAME_MSG_PARAM_BINDNUM         "BindNum"
#define XML_NODE_NAME_MSG_BIND                  "Bind"
#define XML_NODE_NAME_MSG_BIND_BINDCODE         "BindCode"
#define XML_NODE_NAME_MSG_BIND_TIME             "Time"
#define XML_NODE_NAME_MSG_BIND_STATUS           "Status"
#define XML_NODE_NAME_MSG_BIND_DEVICECODE       "DeviceCode"
#define XML_NODE_NAME_MSG_ALARM_ID              "ID"
#define XML_NODE_NAME_MSG_ALARM_TYPE            "Type"
#define XML_NODE_NAME_MSG_ALARM_TIME            "Time"
#define XML_NODE_NAME_MSG_ALARM_DEV_TYPE        "DevType"
#define XML_NODE_NAME_MSG_ALARM_DEAL_USER       "User"
#define XML_NODE_NAME_MSG_ALARM_DEAL_RESULT     "Result"
#define XML_NODE_NAME_MSG_REMOTE_IP             "RemoteIP"
#define XML_NODE_NAME_MSG_REMOTE_PORT           "RemotePort"
#define XML_NODE_NAME_MSG_PARAM_PASSWD          "Passwd"
#define XML_NODE_NAME_MSG_PARAM_DEVNUM          "DevNum"
#define XML_NODE_NAME_MSG_PARAM_DEV             "Dev"
#define XML_NODE_NAME_MSG_PARAM_NAME            "Name"
#define XML_NODE_NAME_MSG_PARAM_SIP             "Sip"
#define XML_NODE_NAME_MSG_PARAM_TOKEN           "DeviceToken"   //IOS手机唯一标识
#define XML_NODE_NAME_MSG_PARAM_FCM_TOKEN       "FcmToken"      //Androi手机唯一标识
#define XML_NODE_NAME_MSG_PARAM_VOIP_TOKEN       "VoipToken"
#define XML_NODE_NAME_MSG_PARAM_APP_TOKEN       "AppToken"      //app从网关获取的token
#define XML_NODE_NAME_MSG_PARAM_MODELID         "ModuleID"
#define XML_NODE_NAME_MSG_PARAM_RTSP_PWD        "RtspPwd"
#define XML_NODE_NAME_MSG_PIC_NAME              "PicName"
#define XML_NODE_NAME_MSG_DETECTION_TYPE        "DetectionType"
#define XML_NODE_NAME_MSG_THIRD_CAMERA_PIC_NAME "ThirdCameraPicName"
#define XML_NODE_NAME_MSG_ACTION                "Action"
#define XML_NODE_NAME_MSG_INITIATOR             "Initiator"
#define XML_NODE_NAME_MSG_RESP                  "Response"
#define XML_NODE_NAME_MSG_PER_ID                "PerID"

#define XML_NODE_NAME_MSG_MODE                  "Mode"
#define XML_NODE_NAME_MSG_TO_NAME               "ToName"
#define XML_NODE_NAME_MSG_FROM_NAME             "FromName"
#define XML_NODE_NAME_MSG_EXPIRE                "Expire"
#define XML_NODE_NAME_MSG_HOME_SYNC             "HomeSync"


#define XML_NODE_NAME_MSG_LAST_ID               "LastID"
#define XML_NODE_NAME_MSG_UNREAD_COUNT          "UnReadMsgCnt"
#define XML_NODE_NAME_MSG_IS_EXPIRE         "IsExpire"
#define XML_NODE_NAME_MSG_IS_ACTIVE         "Active"


#define XML_NODE_NAME_MSG_PARAM_LOCALTION       "Location"
#define XML_NODE_NAME_MSG_CAP_TIME              "Time"
#define XML_NODE_NAME_MSG_PARAM_CAP_ID          "CaptureID"

#define XML_NODE_NAME_MSG_ROOM_NUM              "RoomNumber"

#define XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION "DclientVer"
#define XML_NODE_NAME_MSG_TYPE_CONTACT_URL      "ContactsUrl"
#define XML_NODE_NAME_MSG_CHECK_DTMF            "RemoteSipUser"
#define XML_NODE_NAME_MSG_CHECK_DTMF_RESULT     "Result"

#define XML_NODE_NAME_MSG_PARAM_CONTACTMD5  "ContactMD5"
#define XML_NODE_NAME_MSG_PARAM_CONTACTURL  "ContactUrl"
#define XML_NODE_NAME_MSG_PARAM_TZMD5       "TzMD5"
#define XML_NODE_NAME_MSG_PARAM_TZURL       "TzUrl"
#define XML_NODE_NAME_MSG_PARAM_TZDATAMD5   "TzDataMD5"
#define XML_NODE_NAME_MSG_PARAM_TZDATAURL   "TzDataUrl"
#define XML_NODE_NAME_MSG_PARAM_FACEMD5   "FaceSyncMD5"
#define XML_NODE_NAME_MSG_PARAM_FACEURL   "FaceSyncUrl"

#define XML_NODE_NAME_MSG_PARAM_MATEMD5   "ACMetaMD5"
#define XML_NODE_NAME_MSG_PARAM_USERMATEMD5   "UserMetaMD5"
#define XML_NODE_NAME_MSG_PARAM_MATEURL   "ACMetaUrl"
#define XML_NODE_NAME_MSG_PARAM_INFOMD5   "ACInfoMD5"
#define XML_NODE_NAME_MSG_PARAM_INFOURL  "ACInfoUrl"
#define XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5   "ScheduleMD5"
#define XML_NODE_NAME_MSG_PARAM_SCHEDULEURL  "ScheduleUrl"

#define XML_NODE_NAME_MSG_PARAM_FUNC       "Funcs"

#define XML_NODE_NAME_MSG_DEVICE_CODE               "DeviceCode"
#define XML_NODE_NAME_MSG_LOCAL_IP                  "LocalIP"

#define XML_NODE_NAME_MSG_PARAM_VIDEO_ID            "Vid"
#define XML_NODE_NAME_MSG_COMMAND               "Command"
#define XML_NODE_NAME_MSG_PARAM_SEQ             "Seq"
#define XML_NODE_NAME_MSG_PARAM_CONTENT             "Content"

#define XML_NODE_NAME_MSG_PARAM_COMMON_VERSION             "Version"
#define XML_NODE_NAME_MSG_PARAM_CHECK_CODE         "AuthCode"

#define XML_NODE_NAME_MSG_PARAM_APP_VERSION             "AppVersion"
#define XML_NODE_NAME_MSG_PARAM_APP_LANGUAGE             "Language"

//运维上传文件
#define XML_NODE_NAME_MSG_SERVERURL     "ServerUrl"
#define XML_NODE_NAME_MSG_USERNAME      "Username"
#define XML_NODE_NAME_MSG_PASSWORD      "Password"
#define XML_NODE_NAME_MSG_FILENAME      "Filename"
#define XML_NODE_NAME_MSG_FILENAME2      "FileName"
#define XML_NODE_NAME_MSG_FILEMD5      "FileMD5"
#define XML_NODE_NAME_MSG_DURATION      "Duration"
#define XML_NODE_NAME_MSG_SERVER_ADDR   "ServerAddr"

//V4.3
#define XML_NODE_NAME_MSG_CALLER        "Caller"
#define XML_NODE_NAME_MSG_CALLEE        "Callee"
#define XML_NODE_NAME_MSG_DAIL_OUT      "DailOut"
#define XML_NODE_NAME_MSG_CALL_TRACE_ID "CallTraceID"

#define XML_NODE_NAME_MSG_PARAM_NODES               "Nodes"
#define XML_NODE_NAME_MSG_PARAM_SITE                "Site"
#define XML_NODE_NAME_MSG_PARAM_ARMINGTYPE          "Arming"
#define XML_NODE_NAME_MSG_PARAM_ALARMCODE           "AlarmCode"
#define XML_NODE_NAME_MSG_PARAM_VISITOR             "Visitor"
#define XML_NODE_NAME_MSG_PARAM_EMAIL               "Email"
#define XML_NODE_NAME_MSG_PARAM_ACCOUNT             "Account"
#define XML_NODE_NAME_MSG_PARAM_MODEL_NAME          "ModelName"

#define XML_NODE_NAME_MSG_PARAM_SECRET              "AccessKeySecret"
#define XML_NODE_NAME_MSG_PARAM_KEYID               "AccessKeyId"
#define XML_NODE_NAME_MSG_PARAM_SECRET_TOKEN        "SecurityToken"
#define XML_NODE_NAME_MSG_PARAM_BUCKET              "Bucket"
#define XML_NODE_NAME_MSG_PARAM_ENDPOINT            "Endpoint"

#define XML_NODE_NAME_MSG_PARAM_OEM_NAME            "OEM"
#define XML_NODE_NAME_MSG_PARAM_ALARMZONE           "AlarmZone"
#define XML_NODE_NAME_MSG_PARAM_ALARMLOCATION           "AlarmLocation"
#define XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE          "AlarmCustomize"

#define XML_NODE_NAME_MSG_PARAM_SSH_PORT            "SSHPort"
#define XML_NODE_NAME_MSG_PARAM_TRACE_ID            "TraceID"
#define XML_NODE_NAME_MSG_PARAM_RELAY               "Relay"
#define XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY      "SecurityRelay"
#define XML_NODE_NAME_MSG_PARAM_UNITAPT             "UnitApt"

#define XML_NODE_NAME_MSG_PARAM_NUMBER              "Number"
#define XML_NODE_NAME_MSG_PARAM_DELIVERY            "Delivery"
#define XML_NODE_NAME_MSG_PARAM_RELAY_STATUS        "RelayStatus"
#define XML_NODE_NAME_MSG_PARAM_SWITCH              "Switch"

#define XML_NODE_NAME_MSG_PARAM_PERCENT             "Percent"
#define XML_NODE_NAME_MSG_PARAM_LIMIT               "Limit"

#define XML_NODE_NAME_MSG_PARAM_ACID             "ACID"

#define XML_NODE_NAME_MSG_PARAM_ACCESS_TIMES        "AccessTimes"
#define XML_NODE_NAME_MSG_PARAM_UNIQUE_ID           "UniqueID"
#define XML_NODE_NAME_MSG_PARAM_APT_NUM             "AptNum"
#define XML_NODE_NAME_MSG_PARAM_BOX_NUM             "BoxNum"
#define XML_NODE_NAME_MSG_PARAM_BOX_PWD             "BoxPwd"


#define XML_NODE_NAME_MSG_PARAM_PERID             "PerID"
#define XML_NODE_NAME_MSG_PARAM_MSG_TYPE             "MsgType"
#define XML_NODE_NAME_MSG_PARAM_UID             "UID"
#define XML_NODE_NAME_MSG_PARAM_VOICE_UNREAD             "VoiceUnread"
#define XML_NODE_NAME_MSG_PARAM_PAGE_INDEX       "PageIndex"
#define XML_NODE_NAME_MSG_PARAM_PAGE_SIZE        "PageSize"
#define XML_NODE_NAME_MSG_PARAM_MSG_COUNT        "MsgCount"
#define XML_NODE_NAME_MSG_PARAM_UUID             "UUID"
#define XML_NODE_NAME_MSG_PARAM_FRONTDOOR_MAC    "FrontDoorMAC"
#define XML_NODE_NAME_MSG_PARAM_FLOOR           "LiftFloorNum"

//6.5
#define XML_NODE_NAME_MSG_IS_THIRD                "IsThird"
#define XML_NODE_NAME_MSG_CAMERA_UUID             "CamerUUID"
#define XML_NODE_NAME_MSG_VIDEO_PT                "VideoPt"
#define XML_NODE_NAME_MSG_VIDEO_TYPE              "VideoType"
#define XML_NODE_NAME_MSG_VIDEO_FMTP              "VideoAfmtp"
#define XML_NODE_NAME_MSG_PARAM_DOOR_LOG          "DoorLog"
#define XML_NODE_NAME_MSG_PARAM_CALL_LOG          "CallLog"

//6.6
#define XML_NODE_NAME_MSG_RESET             "Reset"
#define XML_NODE_NAME_MSG_SITE             "Site"
#define XML_NODE_NAME_MSG_TITLE             "Title"
#define XML_NODE_NAME_MSG_DOOR_UUID         "DoorUUID"

//6.7
#define XML_NODE_NAME_MSG_MANUAL_UPDATE       "ManualUpdate"

//6.7.1
#define XML_NODE_NAME_MSG_SRTP_KEY          "SrtpKey"
#define XML_NODE_NAME_REMOTE_CONFIG_SRV     "RemoteConfigSrv"

#define XML_NODE_NAME_MSG_RTP_CONFUSE       "RtpConfuse"

#define XML_NODE_VALUE_SIZE         1024
#define XML_NODE_TEXT_SIZE          1024
#define XML_NODE_LINE_SIZE          4096
#define URL_LEN                     256

#define CHECH_SIZE_TOO_LONG(n1, n2)\
    {\
        if ((n1) > (n2))\
        {\
            (n1) -= (n2);\
        }\
        else\
        {\
            AK_LOG_WARN << "xml size is too long, the memory we hold only [ "<< (n1) <<" ], but xml msg need [ "<< (n2) <<" ]";\
            return 0;\
        }\
    }

#define CHECK_BUFFER_OVER_FLOW(n1, n2) \
    do { if ((int)(n1) >= (n2)) { AK_LOG_WARN << "xml buffer is overflow, real size is [" << (n1) << "], but define size is [" << (n2) << "]";} } while (0)

extern AKCS_CONF gstAKCSConf;

CMsgHandle* GetMsgHandleInstance()
{
    return CMsgHandle::GetInstance();
}

CMsgHandle::CMsgHandle()
{

}

CMsgHandle::~CMsgHandle()
{

}

CMsgHandle* CMsgHandle::instance = NULL;

CMsgHandle* CMsgHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgHandle();
    }

    return instance;
}

static int rl_strcat_s(char* dst, const unsigned long src_len, const char* src)
{
    if (dst == NULL || src == NULL)
    {
        return -1;
    }

    std::size_t copy_size = strlen(src);
    std::size_t cur_len = strlen(dst);
    if (copy_size > ((src_len - 1) - cur_len))
    {
        copy_size = ((src_len - 1) - cur_len);
    }

    Snprintf(dst + cur_len, copy_size + 1, src);

    return 0;
}

//只能用在这个消息构造里面n - strlen(dest)
static char* strcat_s(char* dest, size_t n, const char* src)
{
    return strncat(dest, src, n - strlen(dest));
}


std::string XmlTrans(const char* pszItem)
{
    char item[4096] = "";
    if (pszItem == NULL)
    {
        return item;
    }
    int size = strlen(pszItem);
    int item_pos = 0;
    for (int i = 0; i < size; i++)
    {
        char tmp = pszItem[i];
        switch (tmp)
        {
            case '&':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&amp;");
                item_pos += 5;
            }
            break;
            case '<':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&lt;");
                item_pos += 4;
            }
            break;
            case '>':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&gt;");
                item_pos += 4;
            }
            break;
            case '\'':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&apos;");
                item_pos += 6;
            }
            break;
            case '\"':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&quot;");
                item_pos += 6;
            }
            break;
            default:
                rl_strcat_s(item + item_pos, 2, pszItem + i);
                item_pos += 1;
                break;
        }
    }
    std::string strItem = item;
    return strItem;
}

/*
<Msg>
  <Type>ReportStatus</Type>
  <Protocal>1.0</Protocal>
  <ThirdParty>1</ThirdParty>
  <Params>
    <DeviceID>*******.2</DeviceID>
    <Extension>1</Extension>
    <Type>1</Type>
    <IP>*************</IP>
    <SM>*************</SM>
    <GW>***********</GW>
    <DNS1>***********</DNS1>
    <DNS2>***********</DNS2>
    <MAC>0C1105000001</MAC>
    <Status>Idle</Status>
    <SWVer>**********</SWVer>
    <HWVer>********.0.0.0.0</HWVer>
    <PrikeyMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</PrikeyMD5>
    <RfidMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</RfidMD5>
    <AddrMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</AddrMD5>
    <ConfigMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</ConfigMD5>
    <ContactMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</ContactMD5>
    <DClientVer>1</DClientVer> dclient版本号，目前版本定为1
    <Funcs>1</Funcs>  按位标识上报设备功能
  </Params>
</Msg>
*/
int CMsgHandle::ParseReportStatusMsg(char* buf, SOCKET_MSG_REPORT_STATUS* report_status_message, uint32_t data_size, uint32_t version)
{
    if ((buf == NULL) || (report_status_message == NULL))
    {
        return -1;
    }
    char buf_out[SOCKET_MSG_DATA_SIZE + 1];
    int dy_iv = 0;
    //新版本需要先进行AES解密处理
    if (version == VERSION_2_0)
    {
        AesDecryptByDefaultForReportStatus(buf, buf_out, data_size, dy_iv);
    }
    memset(report_status_message, 0, sizeof(SOCKET_MSG_REPORT_STATUS));
    report_status_message->dynamics_iv = dy_iv;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf_out))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.buf is " <<  buf_out;
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), report_status_message->protocal, sizeof(report_status_message->protocal) / sizeof(TCHAR));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_THIRD_PARTY) == 0)
        {
            report_status_message->is_third_party = ATOI(node->GetText());
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DEVICEID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->device_id, sizeof(report_status_message->device_id) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_EXTENSION) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->extension, sizeof(report_status_message->extension) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->download_server, sizeof(report_status_message->download_server) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->type, sizeof(report_status_message->type) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_IP) == 0)
                {
                    const char* wired_ip = sub_node->Attribute(XML_NODE_NAME_MSG_WIRED_IP);
                    if (wired_ip)
                    {
                        Snprintf(report_status_message->wired_ip_addr, sizeof(report_status_message->wired_ip_addr), wired_ip);
                    }
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->ip_addr, sizeof(report_status_message->ip_addr) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SUBNETMASK) == 0)
                {
                    const char* wired_subnet_mask = sub_node->Attribute(XML_NODE_NAME_MSG_WIRED_IP);
                    if (wired_subnet_mask)
                    {
                        Snprintf(report_status_message->wired_subnet_mask, sizeof(report_status_message->wired_subnet_mask), wired_subnet_mask);
                    }
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->subnet_mask, sizeof(report_status_message->subnet_mask) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_GATEWAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->gateway, sizeof(report_status_message->gateway) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PRIMARYDNS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->primary_dns, sizeof(report_status_message->primary_dns) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECONDARYDNS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->secondary_dns, sizeof(report_status_message->secondary_dns) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->mac, sizeof(report_status_message->mac) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_STATUS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->status, sizeof(report_status_message->status) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SWVER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->SWVer, sizeof(report_status_message->SWVer) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_HWVER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->HWVer, sizeof(report_status_message->HWVer) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PRIKEYMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->private_key_md5, sizeof(report_status_message->private_key_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RFIDMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->rf_id_md5, sizeof(report_status_message->rf_id_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ADDRMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->address_md5, sizeof(report_status_message->address_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->config_md5, sizeof(report_status_message->config_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TZMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->tz_md5, sizeof(report_status_message->tz_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TZDATAMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->tz_data_md5, sizeof(report_status_message->tz_data_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FACEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->face_md5, sizeof(report_status_message->face_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION) == 0)
                {
                    report_status_message->dclient_version = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->contact_md5, sizeof(report_status_message->contact_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CHECK_CODE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->auth_code, sizeof(report_status_message->auth_code) / sizeof(TCHAR));
                    if (!strcasecmp(report_status_message->auth_code, "NULL"))
                    {
                        ::snprintf(report_status_message->auth_code, sizeof(report_status_message->auth_code), "%s", "");
                    }
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ARMINGTYPE) == 0)
                {
                    report_status_message->indoor_arming = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY_STATUS) == 0)
                {
                    char doornum[RELAY_NUM+1] = {0};
                    TransUtf8ToTchar(sub_node->GetText(), doornum, sizeof(doornum));                  
                    report_status_message->relay_status = DoornumToRelayStatus(doornum);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->schedule_md5, sizeof(report_status_message->schedule_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MATEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->user_meta_md5, sizeof(report_status_message->user_meta_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FUNC) == 0)
                {
                    report_status_message->func_bit = strtoul(sub_node->GetText(), nullptr, 10);
                }                
            }
        }
    }
    AK_LOG_INFO << "Mac " << report_status_message->mac << " Report Statu:" << buf_out;

    return 0;
}

/*
<Msg>
  <Type>Alarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Type>Normal</Type>
    <MsgSeq>0123233000<MsgSeq> //10位数字
  </Params>
</Msg>
*/
int CMsgHandle::ParseAlarmMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_ALARM* alarm_msg, uint32_t nVer)
{
    if ((normal_msg == NULL) || (alarm_msg == NULL))
    {
        return -1;
    }

    char* buf = (char*)normal_msg->data;
    char buf_out[SOCKET_MSG_DATA_SIZE + 1] = {0};
    uint32_t data_size = 0;

    //新版本需要先进行AES解密处理
    if (nVer == VERSION_2_0)
    {
        data_size = NTOHS(normal_msg->data_size);
        AesDecryptByMac(buf, buf_out, AES_KEY_DEFAULT_MAC, data_size);
    }
    //TCHAR text[4096]; //added by chenyc,不需要
    //TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseAlarmMsg " << buf_out;
    memset(alarm_msg, 0, sizeof(SOCKET_MSG_ALARM));

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf_out))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), alarm_msg->protocal, sizeof(alarm_msg->protocal) / sizeof(TCHAR));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_msg->type, sizeof(alarm_msg->type) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_msg->msg_seq, sizeof(alarm_msg->msg_seq) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMCODE) == 0)
                {
                    alarm_msg->alarm_code = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMZONE) == 0)
                {
                    alarm_msg->alarm_zone = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMLOCATION) == 0)
                {
                    alarm_msg->alarm_location = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE) == 0)
                {
                    alarm_msg->alarm_customize = ATOI(sub_node->GetText());
                }
            }
        }
    }
    return 0;
}

/*
<Msg>
  <Type>OwnerMessage</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Title>Hello!</Title>
    <Content>Hello,Everyone!</Content>
    <From></From>
    <To></To>
    <Time>2016-08-26 10:00:00</Time>
  </Params>
</Msg>
*/
int CMsgHandle::ParseTextMsg(char* buf, SOCKET_MSG_TEXT_MESSAGE* text_msg)
{
    if ((buf == NULL) || (text_msg == NULL))
    {
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "text=" <<  text;

    memset(text_msg, 0, sizeof(SOCKET_MSG_TEXT_MESSAGE));

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), text_msg->protocal, sizeof(text_msg->protocal) / sizeof(TCHAR));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TITLE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), text_msg->title, sizeof(text_msg->title) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTENT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), text_msg->content, sizeof(text_msg->content) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), text_msg->from, sizeof(text_msg->from) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TO) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), text_msg->to, sizeof(text_msg->to) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), text_msg->time, sizeof(text_msg->time) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>CheckTmpKey</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <TmpKey>01010102</TmpKey>
    <MsgSeq>429496795</MsgSeq>
  </Params>
</Msg>
*/

int CMsgHandle::ParseCheckTmpKeyMsg(char* buf, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY* tmpkey_info)
{
    if ((buf == NULL) || (tmpkey_info == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "CheckTmpKeyMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY, strlen(XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), tmpkey_info->protocal, sizeof(tmpkey_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TMPKEY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), tmpkey_info->tmpkey, sizeof(tmpkey_info->tmpkey));
                }

                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), tmpkey_info->msg_seq, sizeof(tmpkey_info->msg_seq));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>ReportStatus</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <user>ccccc</user>
    <Passwd>dsfsf</Passwd> (做一次MD5)
    <DeviceToken>xxxx</DeviceToken>
    <FcmToken>xxxx</FcmToken>   //google推送的token
    <ModuleID>0</ModuleID>  (机型，涉及到安卓的品牌手机的划分等)
    <MsgSeq>429496702</MsgSeq>
    <OEM>Akuvox<OEM>
  </Params>
</Msg>
*/
int CMsgHandle::ParseAppReportStatusMsg(char* buf, SOCKET_MSG_PERSONNAL_APP_CONF* personnal_info)
{
    if ((buf == NULL) || (personnal_info == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    AK_LOG_INFO << "ParseAppReportStatusMsg is " << buf;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REPORT_STATUS, strlen(XML_NODE_NAME_MSG_TYPE_REPORT_STATUS)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REPORT_STATUS, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), personnal_info->protocal, sizeof(personnal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_USER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->user, sizeof(personnal_info->user));
                }

                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PASSWD) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->password, sizeof(personnal_info->password));
                }

                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->token, sizeof(personnal_info->token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FCM_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->fcm_token, sizeof(personnal_info->fcm_token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_VOIP_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->voip_token, sizeof(personnal_info->voip_token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MODELID) == 0)
                {
                    personnal_info->mobile_type = ATOI(sub_node->GetText() ? sub_node->GetText() : "");
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APP_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->app_token, sizeof(personnal_info->app_token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_COMMON_VERSION) == 0)
                {
                    personnal_info->version = ATOI(sub_node->GetText() ? sub_node->GetText() : "");
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->msg_seq, sizeof(personnal_info->msg_seq));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APP_VERSION) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->app_version, sizeof(personnal_info->app_version));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_OEM_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->oem_name, sizeof(personnal_info->oem_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APP_LANGUAGE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->language, sizeof(personnal_info->language));
                    if (strlen(personnal_info->language) == 0)
                    {
                        TransUtf8ToTchar("en", personnal_info->language, sizeof(personnal_info->language));
                    }
                }

            }
        }
    }

    return 0;
}
/*
<Msg>
  <Type>RequestDevList</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MsgSeq>429496702</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReqDevListMsg(char* buf, SOCKET_MSG_PERSONNAL_DEV_LIST* device_list)
{
    if ((buf == NULL) || (device_list == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "Parse personnal ReqDevListMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REQUEST_DEV_LIST, strlen(XML_NODE_NAME_MSG_TYPE_REQUEST_DEV_LIST)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REQUEST_DEV_LIST, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), device_list->protocal, sizeof(device_list->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), device_list->msg_seq, sizeof(device_list->msg_seq));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>DealAlarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AlarmID>00000042949</AlarmID> (alarmid， 11位数字)
     <user>XXX</user>        (告警的处理人，室内机可以先填写本身的地址节点，eg:*******.1-2)
     <Result>XXX</Result>    (告警的处理内容)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
  </Params>
</Msg>
*/
int CMsgHandle::ParseAlarmDealMsg(char* buf, SOCKET_MSG_ALARM_DEAL* alarm_deal_info)
{
    if ((buf == NULL) || (alarm_deal_info == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseAlarmDealMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL, strlen(XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), alarm_deal_info->protocal, sizeof(alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->alarm_id, sizeof(alarm_deal_info->alarm_id));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_DEAL_USER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->user, sizeof(alarm_deal_info->user));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_DEAL_RESULT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->result, sizeof(alarm_deal_info->result));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->type, sizeof(alarm_deal_info->type));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TIME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->time, sizeof(alarm_deal_info->time));
                }
            }
        }
    }

    return 0;
}

//个人终端用户告警处理消息
/*
<Msg>
  <Type>DealAlarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AlarmID>00000042949</AlarmID> (alarmid， 11位数字)
     <user>XXX</user>        (告警的处理人，个人终端用户的室内机可以先填写本身的location，eg:door phone)
     <Result>XXX</Result>    (告警的处理内容)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
  </Params>
</Msg>
*/

int CMsgHandle::ParseAlarmDealMsg(char* buf, SOCKET_MSG_PERSONNAL_ALARM_DEAL* personnal_alarm_deal_info)
{
    if ((buf == NULL) || (personnal_alarm_deal_info == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseAlarmDealMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL, strlen(XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL, node->GetText());
                // return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_alarm_deal_info->alarm_id, sizeof(personnal_alarm_deal_info->alarm_id));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_DEAL_USER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_alarm_deal_info->user, sizeof(personnal_alarm_deal_info->user));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_DEAL_RESULT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_alarm_deal_info->result, sizeof(personnal_alarm_deal_info->result));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_alarm_deal_info->type, sizeof(personnal_alarm_deal_info->type));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TIME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_alarm_deal_info->time, sizeof(personnal_alarm_deal_info->time));
                }
            }
        }
    }

    return 0;
}

//个人终端用户设备motion alert上报
/*
<Msg>
  <Type>MotionAlert</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AlertID>042949</AlertID> (Alertid， 由设备递增，最大11位数字)
     <PicName>0C110000000_10256546_1.jpg</PicName> (eg:0C1100000001_1513232303_0.jpg)
     <DetectionType>0</DetectionType> 0:移动侦测1:包裹检测2声音检测,没这个类型时都为移动侦测
     <DetectionInfo>0</DetectionInfo> 侦测类型为1时，这里的0包裹放入，1包裹放出；侦测类型为2时，这里的0枪声 1狗叫声 2孩子哭声 3玻璃破碎 4警笛
  </Params>
</Msg>
*/
int CMsgHandle::ParseMotionAlertMsg(char* buf, SOCKET_MSG_MOTION_ALERT* motion_alert)
{
    if ((buf == NULL) || (motion_alert == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseMotionAlertMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REPORT_MOTION_ALERT, strlen(XML_NODE_NAME_MSG_TYPE_REPORT_MOTION_ALERT)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REPORT_MOTION_ALERT, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), motion_alert->picture_name, sizeof(motion_alert->picture_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DETECTION_TYPE) == 0)
                {
                    motion_alert->detection_type = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

//个人终端用户设备motion alert上报
/*
<Msg>
  <Type>RequestArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <ToName>0C110000000</ToName>  (app要处理的设备mac地址)
      <Action>Get</Action>   （Action: Get=获取布防状态， Set=布防、撤防）
      <Mode>0</Mode>         （布防类型Mode: 0=Disarm, 1=Indoor, 2=Sleeping, 3=Outdoor；注：Mode只有当Action =Set时有效 ）
  </Params>
</Msg>

*/
int CMsgHandle::ParseReqArmingMsg(char* buf, SOCKET_MSG_DEV_ARMING* arming_msg)
{
    if ((buf == NULL) || (arming_msg == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    AK_LOG_INFO << "ParseReqArmingMsg text=" <<  buf;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REQ_ARMING, strlen(XML_NODE_NAME_MSG_TYPE_REQ_ARMING)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REQ_ARMING, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_TO_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), arming_msg->mac, sizeof(arming_msg->mac));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ACTION) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), arming_msg->szAction, sizeof(arming_msg->szAction));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_MODE) == 0)
                {
                    arming_msg->mode = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_HOME_SYNC) == 0)
                {
                    arming_msg->home_sync = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}


/*
<Msg>
  <Type>ReqCapture</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <MAC>0C110000000</MAC>  (app要截图的设备mac地址)
     <UUID>xxxx</UUID>
     <Site>站点账号 userconf请求到的当前站点的账号 (sip字段)64字节字符串</Site>
     <RecordVideo>1</RecordVideo>     //RecordVideo 1开启视频录制
  </Params>
</Msg>
*/
int CMsgHandle::ParseReqCaptureMsg(char* buf, SOCKET_MSG_REQ_CAPTURE& request_capture)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReqCaptureMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE, strlen(XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE)) != 0)
            //{
            //    //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE, node->GetText());
            //    return -1;
            //}
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.mac, sizeof(request_capture.mac));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.uuid, sizeof(request_capture.uuid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SITE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.site, sizeof(request_capture.site));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_RECORD_VIDEO) == 0)
                {
                    request_capture.record_video = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CAMERA) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.camera, sizeof(request_capture.camera));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_STREAM_ID) == 0)
                {
                    request_capture.stream_id = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}
/*
<Msg>
  <Type>ReqVideoStorage</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Vid>0C110000000_01231486942</Vid>  (vid:格式为:mac_timestamp)
     <Action>0</Action> (视频存储的动作,0:stop,;1:start)
  </Params>
</Msg>
*/
int CMsgHandle::ParseVideoStorageMsg(char* buf, SOCKET_MSG_VIDEO_STORAGE& video_storage)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseVideoStorageMsg text=%s " << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REQUEST_VIDEO_STORAGE, strlen(XML_NODE_NAME_MSG_TYPE_REQUEST_VIDEO_STORAGE)) != 0)
            //{
            //    return -1;
            //}
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_VIDEO_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), video_storage.uid, sizeof(video_storage.uid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ACTION) == 0)
                {
                    video_storage.is_start_storage = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}



/*
<Msg>
  <Params>
     <SendDtmfSip>0C110000000</SendDtmfSip>  (发出dtmf键的sip账号)
     <MsgSeq>消息序列号</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReqCheckDtmfMsg(char* buf, SOCKET_MSG_CHECK_DTMF& check_dtmf)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReqCaptureMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE, strlen(XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE)) != 0)
            //{
            //    //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE, node->GetText());
            //    return -1;
            //}
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CHECK_DTMF) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), check_dtmf.sip, sizeof(check_dtmf.sip));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), check_dtmf.check_seq, sizeof(check_dtmf.check_seq));
                }
            }
        }
    }

    return 0;
}



//个人终端用户设备motion alert上报
/*
<Msg>
  <Type>ReportArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <ToName>App uid</ToName> （uid:11位数字）
      <Mode>0</Mode>  （mode: 0=Disarm, 1=Indoor, 2=Sleeping, 3=Outdoor）
      <HomeSync>0</HomeSync> (0=关 1=开 2=同步关配置给其他室内机和门口机  3=同步开配置给其他室内机和门口机)
  </Params>
</Msg>
*/
int CMsgHandle::ParseReportArmingMsg(char* buf, SOCKET_MSG_DEV_ARMING* arming_msg)
{
    if ((buf == NULL) || (arming_msg == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportArmingMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REPORT_ARMING, strlen(XML_NODE_NAME_MSG_TYPE_REPORT_ARMING)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REPORT_ARMING, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_MODE) == 0)
                {
                    arming_msg->mode = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_TO_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), arming_msg->uid, sizeof(arming_msg->uid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ACTION) == 0)
                {
                    arming_msg->resp_action = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_HOME_SYNC) == 0)
                {
                    arming_msg->home_sync = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

//发送命令后返回的数据
/*
<Msg>
  <Params>
      <Seq>1/2</Seq>(8)
      <Content>xxxxxxx</Content>分段看dclient多少合适，平台最大放4096
  </Params>
</Msg>

*/
int CMsgHandle::ParseCommandRespMsg(char* buf, SOCKET_MSG_COMMAND_RESP* command_resp)
{
    if ((buf == NULL) || (command_resp == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    //AK_LOG_INFO << "ParseCommandRespMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), command_resp->sequence, sizeof(command_resp->sequence));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTENT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), command_resp->message, sizeof(command_resp->message));
                }
            }
        }
    }

    return 0;
}

//个人终端用户设备motion alert上报
/*
<Msg>
  <Type>SetMotionAlertStatus</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Status>0</Status> (0:停止接收motion alert；1:开始接收motion alert；默认时接收的)
  </Params>
</Msg>
*/
int CMsgHandle::ParseSetMotionAlertMsg(char* buf, int& type)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseSetMotionAlertMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_SET_MOTION_ALERT, strlen(XML_NODE_NAME_MSG_TYPE_SET_MOTION_ALERT)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_SET_MOTION_ALERT, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_BIND_STATUS) == 0)
                {
					char szType[2] = {0};
					TransUtf8ToTchar(sub_node->GetText(), szType, sizeof(szType));
					type = ATOI(szType);
                }
            }
        }
    }

    return 0;
}

//个人终端用户设备motion alert上报
/*
<Msg>
  <Type>ReportAct</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Type>0</Type> (Type，目前开门类型支持：0=CALL, 1=INPUTPWD, 2=CARD)
     <PicName>0C110000000-10256546_1.jpg</PicName> (图片名称统一用，eg:0C1100000001-1513232303_0.jpg)
     <Initiator>xxxxx</Initiator> (跟type字段挂钩，Type=Initiator的关系如下：CALL=sip(账号); INPUTPWD=visitor(写死即可); CARD=card code)
     <Response>0</Response> (0:success; 1:Fail)
  </Params>
</Msg>
*/
int CMsgHandle::ParseReportActMsg(char* buf, SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportActMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REPORT_ACTIVITY, strlen(XML_NODE_NAME_MSG_TYPE_REPORT_ACTIVITY)) != 0)
            {
                ////AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REPORT_ACTIVITY, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            //TransUtf8ToTchar(node->GetText(), personnal_alarm_deal_info->protocal, sizeof(personnal_alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TYPE) == 0)
                {
                    char type[8] = {0};
                    TransUtf8ToTchar(sub_node->GetText(), type, sizeof(type));
                    act_msg.act_type = ATOI(type);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg.pic_name, sizeof(act_msg.pic_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_THIRD_CAMERA_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg.third_camera_pic_name, sizeof(act_msg.third_camera_pic_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_INITIATOR) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg.initiator, sizeof(act_msg.initiator));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_RESP) == 0)
                {
                    char szResp[2] = {0};
                    TransUtf8ToTchar(sub_node->GetText(), szResp, sizeof(szResp));
                    act_msg.resp = ATOI(szResp);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PER_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg.per_id, sizeof(act_msg.per_id));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg.msg_seq, sizeof(act_msg.msg_seq));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                     TransUtf8ToTchar(sub_node->GetText(), act_msg.relay, sizeof(act_msg.relay));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                     TransUtf8ToTchar(sub_node->GetText(), act_msg.srelay, sizeof(act_msg.srelay));
                }      
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_RECORD_NAME) == 0)
                {
                     TransUtf8ToTchar(sub_node->GetText(), act_msg.video_record_name, sizeof(act_msg.video_record_name));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
<Type>ChangeRelay</Type>
<Params>
<MAC>0Cxxxxxxx</MAC>
<Relay>1</Relay> //relay id
<Switch>1</Switch> //开关 1-开 0-关
</Params>
</Msg>
*/
int CMsgHandle::ParseReqChangeRelay(char* buf, SOCKET_MSG_DEV_RELAY_CHANGE& msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReqChangeRelay text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                 {
                     char tmp[8] = {0};
                     TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                     msg.relay_id = ATOI(tmp);
                 }
                 else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                 {
                     TransUtf8ToTchar(sub_node->GetText(), msg.mac, sizeof(msg.mac));
                 }
                 else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SWITCH) == 0)
                 {
                     char tmp[8] = {0};
                     TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                     msg.relay_switch = ATOI(tmp);
                 }
            }
        }
    }
    return 0;
}
/*
<Msg>
  <Type>RequestKeepOpenRelay</Type>
  <Params>
    <MsgUUID>123xxxxxxxxxxxxxxxx4</MsgUUID> //64位
    <Relay>01-1</Relay> //每位代表一个relay, 最高位为第一个relay，0代表失败 1代表成功 -代表不处理该relay。开门relay为124 返回为1-10,代表relay1成功relay2不处理relay3成功relay4失败
    <SecurityRelay>1-1</SecurityRelay> 
  </Params>
</Msg>
*/
int CMsgHandle::ParseResponseEmergencyControlMsg(char *buf, SOCKET_MSG_EMERGENCY_CONTROL *emergency_control_msg)
{	
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportEmergencyControlMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGUUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), emergency_control_msg->msg_uuid, sizeof(emergency_control_msg->msg_uuid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), emergency_control_msg->relay, sizeof(emergency_control_msg->relay));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), emergency_control_msg->security_relay, sizeof(emergency_control_msg->security_relay));
                }
            }
        }
    }
    return 0;
}

/*
<Msg>
<Type>ReportFileMD5</Type>
<Params>
<UserMetaMD5>xxxx</UserMetaMD5> 
<ScheduleMD5>xxxx</ScheduleMD5> 
<ADModuleMD5>xxxx</ADModuleMD5>  
<AddressMD5>xxxx</AddressMD5>  //SDMC、ACMS的地址簿
<ConfigMD5>xxxx</ConfigMD5>  
<ContactMD5>xxxx</ContactMD5>  //云的地址簿
</Params>

</Msg>

*/
int CMsgHandle::ParseReportFileMd5Msg(char* buf, SOCKET_MSG_REPORT_FILEMD5& msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportFileMd5Msg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PRIKEYMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.private_key_md5, sizeof(msg.private_key_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RFIDMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.rf_id_md5, sizeof(msg.rf_id_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.config_md5, sizeof(msg.config_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.contact_md5, sizeof(msg.contact_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.schedule_md5, sizeof(msg.schedule_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_USERMATEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.user_meta_md5, sizeof(msg.user_meta_md5) / sizeof(TCHAR));
                }

            }
        }
    }
    return 0;
}


/*
<Msg>
  <Type>RequestConnection</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <IP>**************:3021</IP>
    <Port>3021</Port>
    <Force>1</Force>
    <HeartBeatPeriod>60</HeartBeatPeriod>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReqConnMsg(char* buf, int size, SOCKET_MSG_REQ_CONN* request_conn_msg)
{
    if ((buf == NULL) || (request_conn_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REQ_CONN, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(request_conn_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(request_conn_msg->ip_addr, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_IP);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PORT, request_conn_msg->port, XML_NODE_NAME_MSG_PARAM_PORT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FORCE, request_conn_msg->force_connect, XML_NODE_NAME_MSG_PARAM_FORCE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_HEARTBEAT, 60, XML_NODE_NAME_MSG_PARAM_HEARTBEAT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>RequestStatus</Type>
  <Protocal>1.0</Protocal>
</Msg>
*/
int CMsgHandle::BuildReqStatusMsg(char* buf, int size, SOCKET_MSG_REQ_STATUS* request_status_msg)
{
    if ((buf == NULL) || (request_status_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REQ_STATUS, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(request_status_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
    <Type>StartRTSP</Type>  or  <Type>StopRTSP</Type>
    <Protocal>1.0</Protocal>
    <Params>
        <RemoteIP>************</ RemoteIP >
        <RemotePort>1232</ RemotePort >
    </Params>
</Msg>
*/
int CMsgHandle::BuildReqRtspMsg(char* buf, int size, const SOCKET_MSG_REQ_RTSP& request_rtsp_msg)
{
    if (buf == NULL)
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);
    if (request_rtsp_msg.type == csmain::kRtspStop) //停止监控
    {
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_STOP_DEV_RTSP, XML_NODE_NAME_MSG_TYPE);
        strcat_s(buf, size, tmp_line);
    }
    else
    {
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_START_DEV_RTSP, XML_NODE_NAME_MSG_TYPE);
        strcat_s(buf, size, tmp_line);
    }

    TransTcharToUtf8(request_rtsp_msg.protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(request_rtsp_msg.remote_ip, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_REMOTE_IP,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_REMOTE_IP);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_REMOTE_PORT, request_rtsp_msg.remote_port, XML_NODE_NAME_MSG_REMOTE_PORT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", "SSRC", request_rtsp_msg.SSRC, "SSRC");
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_IS_THIRD, request_rtsp_msg.have_third_camera, XML_NODE_NAME_MSG_IS_THIRD);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_CAMERA_UUID, request_rtsp_msg.camera_uuid, XML_NODE_NAME_MSG_CAMERA_UUID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_VIDEO_PT, request_rtsp_msg.video_pt, XML_NODE_NAME_MSG_VIDEO_PT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_VIDEO_TYPE, request_rtsp_msg.video_type, XML_NODE_NAME_MSG_VIDEO_TYPE);
    strcat_s(buf, size, tmp_line);
    
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_VIDEO_FMTP, request_rtsp_msg.video_fmtp, XML_NODE_NAME_MSG_VIDEO_FMTP);
    strcat_s(buf, size, tmp_line);
    
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_DOOR_UUID, request_rtsp_msg.transfer_door_uuid, XML_NODE_NAME_MSG_DOOR_UUID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SRTP_KEY, request_rtsp_msg.srtp_key, XML_NODE_NAME_MSG_SRTP_KEY);
    strcat_s(buf, size, tmp_line);
    
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_RTP_CONFUSE, request_rtsp_msg.rtp_confuse, XML_NODE_NAME_MSG_RTP_CONFUSE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_CAMERA, request_rtsp_msg.camera_name, XML_NODE_NAME_MSG_CAMERA);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_STREAM_ID, request_rtsp_msg.stream_id, XML_NODE_NAME_MSG_STREAM_ID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;

}

/*
<Msg>
  <Type>KeepRtsp</Type>
  <Protocal>2.0</Protocal>
  <Params>
     <Expire>60</Expire>
  </Params>
</Msg>
*/
int CMsgHandle::BuildKeepRtspMsg(char* buf, int size, const SOCKET_MSG_REQ_RTSP& keepalive_rtsp_msg)
{
    if (buf == NULL)
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_KEEP_RTSP, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);
    //先写死
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_EXPIRE, "60", XML_NODE_NAME_MSG_EXPIRE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_IS_THIRD, keepalive_rtsp_msg.have_third_camera, XML_NODE_NAME_MSG_IS_THIRD);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_CAMERA_UUID, keepalive_rtsp_msg.camera_uuid, XML_NODE_NAME_MSG_CAMERA_UUID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_DOOR_UUID, keepalive_rtsp_msg.transfer_door_uuid, XML_NODE_NAME_MSG_DOOR_UUID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_CAMERA, keepalive_rtsp_msg.camera_name, XML_NODE_NAME_MSG_CAMERA);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_STREAM_ID, keepalive_rtsp_msg.stream_id, XML_NODE_NAME_MSG_STREAM_ID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;

}

/*
<Msg>
  <Type>Reboot</Type>
  <Protocal>1.0</Protocal>
</Msg>

或

<Msg>
  <Type>OpenDoor</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Item>0</Item>
  </Params>
</Msg>
*/
int CMsgHandle::BuildRemoteControlMsg(char* buf, int size, SOCKET_MSG_REMOTE_CONTROL* remote_control_msg)
{
    if ((buf == NULL) || (remote_control_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(remote_control_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(remote_control_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    //判断是否插入Params
    if (_tcslen(remote_control_msg->item[0]) > 0)
    {
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
        strcat_s(buf, size, tmp_line);

        for (int i = 0; i < REMOTE_CONTROL_ITEM_NUM; i++)
        {
            if (_tcslen(remote_control_msg->item[i]) == 0)
            {
                break;
            }
            TransTcharToUtf8(remote_control_msg->item[i], tmp_buffer, XML_NODE_LINE_SIZE);
            sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_ITEM);
            strcat_s(buf, size, tmp_line);
        }

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
        strcat_s(buf, size, tmp_line);
    }
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>UpgradeStart</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Path>http://ip/sdfsd.bin</Path>
    <Size>1024</Size>
    <MD5>01ab1298afb23021</MD5>
  </Params>
</Msg>
*/
int CMsgHandle::BuildUpgradeStartMsg(char* buf, int size, SOCKET_MSG_UPGRADE_START* upgrade_start_msg)
{
    if ((buf == NULL) || (upgrade_start_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_UPGRADE_START, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(upgrade_start_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(upgrade_start_msg->file_path, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PATH, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_PATH);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SIZE, upgrade_start_msg->file_size, XML_NODE_NAME_MSG_PARAM_SIZE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(upgrade_start_msg->file_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MD5);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>KeySend</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <PrikeyUrl>http://**************/Download/Privatekey/privatekey_*******.1-2.xml</PrikeyURL>
    <PrikeyMD5>01ab1298afb23021</PrikeyMD5>
    <RfidUrl>http://**************/Download/Rfid/rfid_*******.1-2.xml</RfidUrl>
    <RfidMD5>01ab1298afb23021</RfidMD5>
    <SettingUrl>http://**************/Download/Config/config__*******.1-2.xml</SettingUrl>
    <SettingMD5>01ab1298afb23021</SettingMD5>
    <AddrUrl>http://**************/Download/Addr/addr.xml</AddrUrl>
    <AddrMD5>01ab1298afb23021</AddrMD5>
    <ContactUrl>http://**************/Download/Addr/addr.xml</ContactUrl>
    <ContactMD5>01ab1298afb23021</ContactMD5>
  </Params>
</Msg>
*/
int CMsgHandle::BuildKeySendMsg(char* buf, int size, SOCKET_MSG_KEY_SEND* key_send_msg)
{
    if ((buf == NULL) || (key_send_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_KEY_SEND, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->private_key_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PRIKEYURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_PRIKEYURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->private_key_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PRIKEYMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_PRIKEYMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->rf_id_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RFIDURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_RFIDURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->rf_id_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RFIDMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_RFIDMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->config_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONFIGURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONFIGURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->config_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONFIGMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5);
    strcat_s(buf, size, tmp_line);
    /*
        TransTcharToUtf8(key_send_msg->addr_url, tmp_buffer, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ADDRURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_ADDRURL);
        strcat_s(buf, size, tmp_line);

        TransTcharToUtf8(key_send_msg->addr_md5, tmp_buffer, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ADDRMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_ADDRMD5);
        strcat_s(buf, size, tmp_line);
    */
    TransTcharToUtf8(key_send_msg->contact_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTACTURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONTACTURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->contact_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTACTMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_data_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZDATAURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZDATAURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_data_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZDATAMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZDATAMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->face_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FACEURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_FACEURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->face_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FACEMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_FACEMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->user_meta_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MATEURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MATEURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->user_meta_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MATEMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MATEMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->schedule_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SCHEDULEURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_SCHEDULEURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->schedule_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5);
    strcat_s(buf, size, tmp_line);


    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

int CMsgHandle::BuildOfficeKeySendMsg(char* buf, int size, SOCKET_MSG_KEY_SEND* key_send_msg)
{
    if ((buf == NULL) || (key_send_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_KEY_SEND, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->config_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONFIGURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONFIGURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->config_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONFIGMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->contact_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTACTURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONTACTURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->contact_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTACTMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_data_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZDATAURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZDATAURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->tz_data_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TZDATAMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TZDATAMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->user_meta_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MATEURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MATEURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->user_meta_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MATEMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MATEMD5);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->schedule_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SCHEDULEURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_SCHEDULEURL);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->schedule_md5, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5);
    strcat_s(buf, size, tmp_line);


    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}


int CMsgHandle::BuildGivenKeySendMsg(char* buf, int size, SOCKET_MSG_KEY_SEND* key_send_msg)
{
    if ((buf == NULL) || (key_send_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_KEY_SEND, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(key_send_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);


    if (key_send_msg->keysend_type == DEV_FILE_CHANGE_NOTIFY_USER_INFO)
    {
        TransTcharToUtf8(key_send_msg->user_info_md5, tmp_buffer, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_INFOMD5, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_INFOMD5);
        strcat_s(buf, size, tmp_line);

        TransTcharToUtf8(key_send_msg->user_info_url, tmp_buffer, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_INFOURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_INFOURL);
        strcat_s(buf, size, tmp_line);

    }
    else if (1)
    {

    }

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}


/*
<Msg>
  <Type>UpgradeSend</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <FirmwareVer>26.0.0.1</FirmwareVer>
    <FirmwareUrl>http://**************/Download/Firmware/R26/26.0.0.1.rom</FirmwareUrl>
  </Params>
</Msg>
*/
int CMsgHandle::BuildUpgradeSendMsg(char* buf, int size, SOCKET_MSG_UPGRADE_SEND* upgrade_send_msg)
{
    if ((buf == NULL) || (upgrade_send_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_UPGRADE_SEND, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(upgrade_send_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(upgrade_send_msg->firmware_version, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FIRMWAREVER, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_FIRMWAREVER);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(upgrade_send_msg->firmware_url, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FIRMWAREURL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_FIRMWAREURL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_RESET, upgrade_send_msg->is_need_reset, XML_NODE_NAME_MSG_RESET);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}


/*
<Msg>
  <Type>TextMessage</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MsgID>这条消息对应数据的ID</MsgID>
    <Title>Hello!</Title>
    <Content>Hello,Everyone!</Content>
    <From></From>
    <To></To>
    <Time>2016-08-26 10:00:00</Time>
  </Params>
</Msg>
*/
int CMsgHandle::BuildTextMessageMsg(char* buf, int size, SOCKET_MSG_TEXT_MESSAGE* text_message)
{
    if ((buf == NULL) || (text_message == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_TEXT_MESSAGE, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_TYPE, text_message->type, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    char tmp[32];
    ::snprintf(tmp, sizeof(tmp), "%d", text_message->id);
    NoTransTcharToUtf8(tmp, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGID, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGID);
    strcat_s(buf, size, tmp_line);


    NoTransTcharToUtf8(text_message->title, tmp_buffer, XML_NODE_LINE_SIZE);
    std::string payload = XmlTrans(tmp_buffer);
    //TranslateXmlFormat(payload);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TITLE, payload.c_str(), XML_NODE_NAME_MSG_PARAM_TITLE);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->content, tmp_buffer, XML_NODE_LINE_SIZE);
    payload = XmlTrans(tmp_buffer);
    //TranslateXmlFormat(payload);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTENT, payload.c_str(), XML_NODE_NAME_MSG_PARAM_CONTENT);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->from, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_FROM);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->to, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TO);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>AlarmAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>0000042949</ID> (alarmid， 10位数字)
     <MsgSeq>0123233000<MsgSeq> //10位数字
  </Params>
</Msg>

*/
int CMsgHandle::BuildAlarmSendMsg(char* buf, int size, SOCKET_MSG_ALARM_SEND* alarm_msg)
{
    if ((buf == NULL) || (alarm_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_ALARM_ACK, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%.10u</%s>\r\n", XML_NODE_NAME_MSG_ALARM_ID, alarm_msg->id, XML_NODE_NAME_MSG_ALARM_ID);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->msg_seq, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCODE, alarm_msg->alarm_code, XML_NODE_NAME_MSG_PARAM_ALARMCODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}


/*
<Msg>
  <Type>CheckTmpKeyAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>1</Result>  (校验不成功：0)
    <MsgSeq>429496795</MsgSeq>
    <Relay>123</Relay>
    <UnitApt>12-101</UnitApt>
  </Params>
</Msg>
*/
int CMsgHandle::BuildCheckTmpKeyAckMsg(char* buf, int size, const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY* tmpkey_msg)
{
    if ((buf == NULL) || (tmpkey_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_CHECK_TMPKEY_ACK, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(tmpkey_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, tmpkey_msg->result, XML_NODE_NAME_MSG_PARAM_RESULT);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(tmpkey_msg->msg_seq, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RELAY, tmpkey_msg->relay, XML_NODE_NAME_MSG_PARAM_RELAY);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY, tmpkey_msg->security_relay, XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_UNITAPT, tmpkey_msg->unit_apt, XML_NODE_NAME_MSG_PARAM_UNITAPT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PERID, tmpkey_msg->account, XML_NODE_NAME_MSG_PARAM_PERID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FLOOR, tmpkey_msg->floor, XML_NODE_NAME_MSG_PARAM_FLOOR);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

int CMsgHandle::BuildAlarmNotifyMsg2MngDev(char* buf, int size, const SOCKET_MSG_ALARM_SEND* alarm_msg)
{
    if ((buf == NULL) || (alarm_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_ALARM_TRANSLATE, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);


    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%.10u</%s>\r\n", XML_NODE_NAME_MSG_ALARM_ID, alarm_msg->id, XML_NODE_NAME_MSG_ALARM_ID);
    strcat_s(buf, size, tmp_line);


    TransTcharToUtf8(alarm_msg->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FROM_NAME, alarm_msg->from_local, XML_NODE_NAME_MSG_FROM_NAME);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARM_MSG, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_ALARM_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_APT, alarm_msg->APT, XML_NODE_NAME_MSG_PARAM_APT);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->msg_seq, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, alarm_msg->mac, XML_NODE_NAME_MSG_PARAM_MAC);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCODE, alarm_msg->alarm_code, XML_NODE_NAME_MSG_PARAM_ALARMCODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMLOCATION, alarm_msg->alarm_location, XML_NODE_NAME_MSG_PARAM_ALARMLOCATION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMZONE, alarm_msg->alarm_zone, XML_NODE_NAME_MSG_PARAM_ALARMZONE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE, alarm_msg->alarm_customize,
              XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}


/*
<Msg>
  <Type>AlarmNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AlarmID>042949</AlarmID> (alarmid， 最大11位数字，在rest api中的处理消息： PUT /alarm/xx)
     <Type>429496702</Type>   (alarm类型，这个是一个字符串，长度最大为64，内容就是具体的告警内容，没有用枚举，根据室内机消息透传)
     <Address>*******.5</Address>   (alarm主机所在房间地址)
     <Extension>429496702</Extension>  (alarm主机所在地址的序列号,一般是：0-10)
     <DevType>0</DevType>   (alarm主机的类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>
  </Params>
</Msg>
*/
int CMsgHandle::BuildAlarmNotifyMsg(char* buf, int size, const SOCKET_MSG_ALARM_SEND* alarm_msg)
{
    if ((buf == NULL) || (alarm_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_NOTIFY_ALARM, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%.10u</%s>\r\n", XML_NODE_NAME_MSG_ALARM_ID, alarm_msg->id, XML_NODE_NAME_MSG_ALARM_ID);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_TYPE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_TYPE);
    strcat_s(buf, size, tmp_line);

    //change chenzhx20180807 app用这个显示在状态栏，改为localtion
    TransTcharToUtf8(alarm_msg->from_local, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ADDRESS, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_ADDRESS);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%u</%s>\r\n", XML_NODE_NAME_MSG_PARAM_EXTENSION, alarm_msg->extension, XML_NODE_NAME_MSG_PARAM_EXTENSION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%u</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEV_TYPE, alarm_msg->device_type, XML_NODE_NAME_MSG_ALARM_DEV_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FROM_NAME, alarm_msg->from_local, XML_NODE_NAME_MSG_FROM_NAME);
    strcat_s(buf, size, tmp_line);
    //TODO:chenzhx 这里可以直接添加accountid unitid 传送

    TransTcharToUtf8(alarm_msg->msg_seq, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCODE, alarm_msg->alarm_code, XML_NODE_NAME_MSG_PARAM_ALARMCODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMLOCATION, alarm_msg->alarm_location, XML_NODE_NAME_MSG_PARAM_ALARMLOCATION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMZONE, alarm_msg->alarm_zone, XML_NODE_NAME_MSG_PARAM_ALARMZONE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE, alarm_msg->alarm_customize,
              XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, alarm_msg->mac, XML_NODE_NAME_MSG_PARAM_MAC);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SITE, alarm_msg->address, XML_NODE_NAME_MSG_SITE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_msg->title, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TITLE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_TITLE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}
/*
<Msg>
  <Type>DealAlarmNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>00000042949</ID>    (alarmid， 10位数字)
     <user>XXX</user>        (告警的处理人)
     <Result>XXX</Result>    (告警的处理内容,长度最大512字节)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
     <DevName>xxx设备的location</DevName> //added by chenyc
  </Params>
</Msg>
*/
int CMsgHandle::BuildAlarmDealNotifyMsg(char* buf, int size, const SOCKET_MSG_ALARM_DEAL* alarm_deal_msg)
{
    if ((buf == NULL) || (alarm_deal_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_DEAL_NOTIFY_ALARM, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->alarm_id, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_ID, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_ID);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->user, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEAL_USER, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_DEAL_USER);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->result, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEAL_RESULT, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_DEAL_RESULT);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->device_name, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_DEV_NAME, tmp_buffer, XML_NODE_NAME_MSG_DEV_NAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>DealAlarmNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>00000042949</ID>    (alarmid， 10位数字)
     <user>XXX</user>        (告警的处理人)
     <Result>XXX</Result>    (告警的处理内容,长度最大512字节)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
     <DevName>xxx设备的location</DevName> //added by chenyc
  </Params>
</Msg>
*/
//个人终端用户
int CMsgHandle::BuildPersonnalAlarmDealNotifyMsg(char* buf, int size, const SOCKET_MSG_PERSONNAL_ALARM_DEAL* alarm_deal_msg)
{
    if ((buf == NULL) || (alarm_deal_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_DEAL_NOTIFY_ALARM, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->alarm_id, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_ID, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_ID);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->user, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEAL_USER, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_DEAL_USER);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->result, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEAL_RESULT, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_DEAL_RESULT);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->device_name, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_DEV_NAME, tmp_buffer, XML_NODE_NAME_MSG_DEV_NAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCODE, alarm_deal_msg->alarm_code, XML_NODE_NAME_MSG_PARAM_ALARMCODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMZONE, alarm_deal_msg->alarm_zone, XML_NODE_NAME_MSG_PARAM_ALARMZONE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMLOCATION, alarm_deal_msg->alarm_location, XML_NODE_NAME_MSG_PARAM_ALARMLOCATION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE, alarm_deal_msg->alarm_customize,
              XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SITE, alarm_deal_msg->area_node, XML_NODE_NAME_MSG_SITE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}
/*
<Msg>
  <Type>DealAlarmNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>00000042949</ID>    (alarmid， 10位数字)
     <user>XXX</user>        (告警的处理人)
     <Result>XXX</Result>    (告警的处理内容,长度最大512字节)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
     <DevName>xxx设备的location</DevName> //added by chenyc
  </Params>
</Msg>
*/
//社区终端用户
int CMsgHandle::BuildCommunityAlarmDealNotifyMsg(char* buf, int size, const SOCKET_MSG_ALARM_DEAL* alarm_deal_msg)
{
    if ((buf == NULL) || (alarm_deal_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_DEAL_NOTIFY_ALARM, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->alarm_id, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_ID, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_ID);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->user, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEAL_USER, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_DEAL_USER);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->result, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ALARM_DEAL_RESULT, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ALARM_DEAL_RESULT);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->device_name, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_DEV_NAME, tmp_buffer, XML_NODE_NAME_MSG_DEV_NAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCODE, alarm_deal_msg->alarm_code, XML_NODE_NAME_MSG_PARAM_ALARMCODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMZONE, alarm_deal_msg->alarm_zone, XML_NODE_NAME_MSG_PARAM_ALARMZONE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMLOCATION, alarm_deal_msg->alarm_location, XML_NODE_NAME_MSG_PARAM_ALARMLOCATION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE, alarm_deal_msg->alarm_customize,
              XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SITE, alarm_deal_msg->area_node, XML_NODE_NAME_MSG_SITE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(alarm_deal_msg->title, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TITLE, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_TITLE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>MotionAlert</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <MAC>***********</MAC> (检测到motion的设备MAC)
     <CaptureID>1343</CaptureID> (这条capture mysql id)
     <Sip>1033</Sip> (sip账号)
     <Time>***********</Time> (上报时间)
     <Location>R26</Location>
  </Params>
</Msg>
*/
int CMsgHandle::BuildPerMotionNotifyMsg(char* buf, int size, const std::string& proto,
                                        const std::string& mac, int id, const std::string& sip_account, const std::string& localtion, const std::string& time, const std::string& node)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_MOTION_ALERT, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, proto.c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, mac.c_str(), XML_NODE_NAME_MSG_PARAM_MAC);
    strcat_s(buf, size, tmp_line);

    //v4.0
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CAP_ID, id, XML_NODE_NAME_MSG_PARAM_CAP_ID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SIP, sip_account.c_str(), XML_NODE_NAME_MSG_PARAM_SIP);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_LOCALTION, XmlTrans(localtion.c_str()).c_str(), XML_NODE_NAME_MSG_PARAM_LOCALTION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_CAP_TIME, time.c_str(), XML_NODE_NAME_MSG_CAP_TIME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SITE, node.c_str(), XML_NODE_NAME_MSG_SITE);
    strcat_s(buf, size, tmp_line);
    //end

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

//个人
/*
<Msg>
    <Params>
        <DevNum></DevNum>
        <Result></Result>
        <Dev>
            <Type></Type>
            <Name></Name>
            <Sip></Sip>
            <IP></IP>
            <MAC></MAC>
            <RtspPwd></RtspPwd>
            <RoomNumber></RoomNumber>
            <Name></Name>
        </Dev>
    </Params>
</Msg>

*/
int CMsgHandle::BuildReqDevListAckMsg(char* buf, unsigned int size, const SOCKET_MSG_PERSONNAL_DEV_LIST* devive_list_msg, const std::vector<PERSONNAL_DEVICE_SIP>& oVec)
{
    if ((buf == NULL) || (devive_list_msg == NULL))
    {
        return -1;
    }

    char tmp_line[XML_NODE_LINE_SIZE] = {0};
    char tmp_buffer[XML_NODE_LINE_SIZE] = {0};

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_DEV_LIST, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    TransTcharToUtf8(devive_list_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, devive_list_msg->result, XML_NODE_NAME_MSG_PARAM_RESULT);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    int nDevNum = oVec.size();
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVNUM, nDevNum, XML_NODE_NAME_MSG_PARAM_DEVNUM);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    for (const auto& dev : oVec)
    {
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEV);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_TYPE, dev.type, XML_NODE_NAME_MSG_TYPE);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.name), dev.name);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_NAME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_NAME);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.sip_account), dev.sip_account);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SIP, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_SIP);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.ip), dev.ip);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_IP);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.mac), dev.mac);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MAC);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.rtsp_password), dev.rtsp_password);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RTSP_PWD, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_RTSP_PWD);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEV);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));
    }

    TransTcharToUtf8(devive_list_msg->msg_seq, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    //delete []tmp_line;
    //delete []tmp_buffer;

    return 0;
}


//社区
int CMsgHandle::BuildReqDevListAckMsg(char* buf, unsigned int size, const SOCKET_MSG_PERSONNAL_DEV_LIST* devive_list_msg, const std::vector<COMMUNITY_DEVICE_SIP>& oVec)
{
    if ((buf == NULL) || (devive_list_msg == NULL))
    {
        return -1;
    }

    char tmp_line[XML_NODE_LINE_SIZE] = {0};
    char tmp_buffer[XML_NODE_LINE_SIZE] = {0};

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_DEV_LIST, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    TransTcharToUtf8(devive_list_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, devive_list_msg->result, XML_NODE_NAME_MSG_PARAM_RESULT);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    int nDevNum = oVec.size();
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVNUM, nDevNum, XML_NODE_NAME_MSG_PARAM_DEVNUM);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    for (const auto& dev : oVec)
    {
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEV);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_TYPE, dev.type, XML_NODE_NAME_MSG_TYPE);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.name), dev.name);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_NAME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_NAME);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.sip_account), dev.sip_account);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SIP, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_SIP);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.ip), dev.ip);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_IP);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.mac), dev.mac);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MAC);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.rtsp_password), dev.rtsp_password);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RTSP_PWD, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_RTSP_PWD);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

        Snprintf(tmp_buffer, sizeof(dev.room_num), dev.room_num);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ROOM_NUM, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_ROOM_NUM);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));


        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEV);
        strcat_s(buf, size, tmp_line);
        CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));
    }

    TransTcharToUtf8(devive_list_msg->msg_seq, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);
    CHECH_SIZE_TOO_LONG(size, ::strlen(tmp_line));

    //delete []tmp_line;
    //delete []tmp_buffer;

    return 0;
}


/*
<Msg>
  <Type>RequestArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>App uid</FromName> （uid:11位数字）
      <Action>Get</Action>   （Action: Get=获取布防状态， Set=布防、撤防）
      <Mode>0</Mode>         （布防类型Mode: 0=Disarm, 1=Indoor, 2=Sleeping, 3=Outdoor；注：Mode只有当Action =Set时有效 ）
  </Params>
</Msg>

*/
int CMsgHandle::BuildBuildReqArmingMsg(char* buf, int size, const SOCKET_MSG_DEV_ARMING& arming)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_REQ_ARMING, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FROM_NAME, arming.uid, XML_NODE_NAME_MSG_FROM_NAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_ACTION, arming.szAction, XML_NODE_NAME_MSG_ACTION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_MODE, arming.mode, XML_NODE_NAME_MSG_MODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}


/*
<Msg>
  <Type>ReportArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>0C110000000</FromName>  (app要处理的设备mac地址)
      <Mode>0</Mode>  （mode: 0=Disarm, 1=Indoor, 2=Sleeping, 3=Outdoor）
  </Params>
</Msg>
*/
int CMsgHandle::BuildRespArmingMsg(char* buf, int size, const SOCKET_MSG_DEV_ARMING& arming)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_REP_ARMING, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FROM_NAME, arming.mac, XML_NODE_NAME_MSG_FROM_NAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_MODE, arming.mode, XML_NODE_NAME_MSG_MODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", "Sync", 1, "Sync"); //这个是罗伯特兼容设备别的模式。平台直接写死转发
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_HOME_SYNC, arming.home_sync, XML_NODE_NAME_MSG_HOME_SYNC);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}



/*
<Msg>
  <Type>AppLoginResp</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <LastID>123</LastID> 最后一次读到的消息id
      <UnReadMsgCnt>2</UnReadMsgCnt> 当前未读的个数
  </Params>
</Msg>
*/
int CMsgHandle::BuildRespAppLoginMsg(char* buf, int size, const SOCKET_MSG_RESP_APPLOGIN* app_loing_resp)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_LOGIN_RESP, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_LAST_ID, app_loing_resp->last_msg_id, XML_NODE_NAME_MSG_LAST_ID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_UNREAD_COUNT, app_loing_resp->unread_number, XML_NODE_NAME_MSG_UNREAD_COUNT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_IS_EXPIRE, app_loing_resp->is_expire, XML_NODE_NAME_MSG_IS_EXPIRE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_IS_ACTIVE, app_loing_resp->id_active, XML_NODE_NAME_MSG_IS_ACTIVE);
    strcat_s(buf, size, tmp_line);


    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}


/*
<Msg>
  <Params>
      <ServerUrl>ftp://severip:port/logDir/</ServerUrl>(上传url)
      <Username>xxx</Username>
      <Password>xxx</Password>
      <Filename>fffffff.log</filFilenameename>(上传的名称)
      <Duration>120</Duration>(抓包时长定义)
  </Params>
</Msg>
*/
int CMsgHandle::BuildReqGetFileMsg(char* buf, int size, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SERVERURL, get_file->server_url, XML_NODE_NAME_MSG_SERVERURL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_USERNAME, XmlTrans(get_file->username).c_str(), XML_NODE_NAME_MSG_USERNAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PASSWORD, get_file->password, XML_NODE_NAME_MSG_PASSWORD);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FILENAME, get_file->file_name, XML_NODE_NAME_MSG_FILENAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_DURATION, get_file->druation, XML_NODE_NAME_MSG_DURATION);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    return 0;
}

int CMsgHandle::BuildReqDevReconnectMsg(char* buf, int size, const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_SERVER_ADDR, reconnection->server_addr, XML_NODE_NAME_MSG_SERVER_ADDR);
    strcat_s(buf, size, tmp_line);

    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*
<Msg>
  <Type>SendDtmfCheck</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <Result>1</Result> 1 校验成功 0失败
  <SendDtmfSip>sip账号</SendDtmfSip>------最好判断跟这个sip的通话是否还在
  <RandomCode>这次校验的随机码</RandomCode>---跟之前发送的一致


  </Params>
</Msg>
*/
int CMsgHandle::BuildRespCheckDtmfMsg(char* buf, int size, const SOCKET_MSG_CHECK_DTMF& check_dtmf)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_CHECK_DTMF_RESULT, check_dtmf.result, XML_NODE_NAME_MSG_CHECK_DTMF_RESULT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, check_dtmf.check_seq, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_CHECK_DTMF, check_dtmf.sip, XML_NODE_NAME_MSG_CHECK_DTMF);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}


/*
<Msg>
  <Params>
      <DeviceCode>0311000000</DeviceCode>  (10位纯数字的字符串)
  </Params>
</Msg>
*/
int CMsgHandle::BuildRespDevCodeMsg(char* buf, int size, const std::string& code)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_DEVICE_CODE, code.c_str(), XML_NODE_NAME_MSG_DEVICE_CODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}


/*
<Msg>
  <Params>
      <Command>cat /xx/xx/xxx</Command>(256字节)
  </Params>
</Msg>

*/
int CMsgHandle::BuildSendDevCommandMsg(char* buf, int size, const std::string& command)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_COMMAND, command.c_str(), XML_NODE_NAME_MSG_COMMAND);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}



//个人终端用户设备motion alert上报
/*
<Msg>
  <Params>
     <PicName>0C110000000-10256546_1_CALL.jpg</PicName>(长度64) (图片名称统一用，eg:0C1100000001-1513232303_0_CALL.jpg)
     <Caller>712012511</Caller>(长度32)//主叫 呼出=自己的sip 呼入=对方sip
     <Callee>712012000</Callee>(长度32)//被叫 呼出=对方sip(是自己真实呼出号码，不管Remote-party-id) 呼入=自己sip
     <DailOut>1</DailOut>//呼出=1  呼入=0
     <CallTraceID>sip账号+ timestamp(10位) + randstring(10位)</CallTraceID>//呼叫唯一id
  </Params>
</Msg>

*/
int CMsgHandle::ParseCallCaptureMsg(char* buf, SOCKET_MSG_CALL_CAPTURE& call_capture)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseCallCaptureMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture.picture_name, sizeof(call_capture.picture_name));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture.caller, sizeof(call_capture.caller));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLEE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture.callee, sizeof(call_capture.callee));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALL_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture.call_trace_id, sizeof(call_capture.call_trace_id));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DAIL_OUT) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    call_capture.dialog_out =  ATOI(tmp);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_RECORD_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture.video_record_name, sizeof(call_capture.video_record_name));
                }
            }
        }
    }

    return 0;
}

//管理机 广播消息
/*
<Msg>
  <Type>BroadCastMsg</Type>
  <Params>
    <Titel>通知</Titel>
    <Content>明天不用上班</Content>
    <Time>2019-03-12 12:12:12</Time>
    <Nodes>301000121,3011021541</Nodes>//发送给家庭的标识，联系人需要在添加节点标识
  </Params>
</Msg>

*/
int CMsgHandle::ParseMngDevReportMsg(char* buf, SOCKET_MSG_MNG_DEV_REPORT_MSG& mng_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseMngDevReportMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TITLE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.title, sizeof(mng_msg.title));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTENT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.content, sizeof(mng_msg.content));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.time, sizeof(mng_msg.time));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_NODES) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.nodes, sizeof(mng_msg.nodes));
                }
            }
        }
    }

    return 0;
}

/*<Msg>
  <Type>ReportVisitorInfo</Type>
  <Params>
    <Visitor>访客名字</Visitor>
    <Account>*********</Account>    //到访用户账号
    <Email><EMAIL></Emali>
    <ID>********</ID>   //身份证号码
    <Count>5</Count>        //TempKey可用次数
    <From>China</From>  //来自哪里
    <ModelName>0C110503B92D-1571037856_0.tar.gz</ModelName> //模型压缩包名称
  </Params>
</Msg>*/
int CMsgHandle::ParseDevReportVisitorInfo(char* buf, SOCKET_MSG_DEV_REPORT_VISITOR& mng_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseDevReportVisitorInfo text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_VISITOR) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.visitor, sizeof(mng_msg.visitor));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_EMAIL) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.email, sizeof(mng_msg.email));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.id_number, sizeof(mng_msg.id_number));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.from, sizeof(mng_msg.from));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ACCOUNT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.account, sizeof(mng_msg.account));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MODEL_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.model_name, sizeof(mng_msg.model_name));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_COUNT) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    mng_msg.count =  ATOI(tmp);
                }
            }
        }
    }

    return 0;
}

/*<Msg>
  <Type>ReportVisitorAuth</Type>
  <Params>
    <Sip>*********</Sip> //通话对端的Sip账号
    <Count>5</Count> //授权TempKey可用次数
  </Params>
</Msg>*/
int CMsgHandle::ParseDevReportVisitorAuth(char* buf, SOCKET_MSG_DEV_REPORT_VISITOR_AUTH& mng_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseDevReportVisitorAuth text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SIP) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.sip_account, sizeof(mng_msg.sip_account));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_COUNT) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    mng_msg.count =  ATOI(tmp);
                }
            }
        }
    }

    return 0;
}


int CMsgHandle::BuildReqUpdateServerMsg(char* buf, int size, const std::string& type)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<SrvType>%s</SrvType>\r\n",  type.c_str());
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

//触发器信息，切换arming模式，是否允许设置
/*
<Msg>
  <Params>
    <home>0</home>
    <sleep>1</sleep>
    <away>0</away>
  </Params>
</Msg>
*/
int CMsgHandle::ParseSensorTirggerMsg(char* buf, SOCKET_MSG_SENSOR_TIRGGER_MSG& msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseSensorTirggerMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), "Sleep") == 0)
                {
                    msg.sleep = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), "Home") == 0)
                {
                    msg.home = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), "Away") == 0)
                {
                    msg.away = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*<Msg>
  <Type>VisitorAuthAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Count>5</Count> //授权TempKey可用次数
  </Params>
</Msg>*/
int CMsgHandle::BuildVisitorAuthMsg(char* buf, int size, int count)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_VISITOR_AUTH, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_COUNT, count, XML_NODE_NAME_MSG_PARAM_COUNT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*<Msg>
  <Type>FaceDataForWard</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Url>http://10.66.95.45:8090/group1/M00/00/E3/CkJfLV2kRn2AAZAnAAACFIWl0DU.tar.gz</Url> //人脸base64数据
  </Params>
</Msg>*/
int CMsgHandle::BuildFaceDataMsg(char* buf, int size, const SOCKET_MSG_DEV_REPORT_FACE_DATA& face_data)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_FACE_DATA_MSG, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_URL, XmlTrans(face_data.model_url).c_str(), XML_NODE_NAME_MSG_PARAM_URL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*<Msg>
  <Type>VisitorTempKeyAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <TmpKey>91234567</TmpKey>
  </Params>
</Msg>*/
int CMsgHandle::BuildVisitorTempKeyAckMsg(char* buf, int size, int temp_key_code)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_VISITOR_KEY, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TMPKEY, temp_key_code, XML_NODE_NAME_MSG_PARAM_TMPKEY);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*<Msg>
  <Type>SendOssSts</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AccessKeySecret>FWhYyABStQjhzCN2GEJkcYjdo74HorwiHRwGwpdyL5oE</AccessKeySecret>
     <AccessKeyId>STS.NUTpZMxReR9PRA9noet1DC2ZZ</AccessKeyId>
     <SecurityToken>CAISpgJ1q6Ft1beuu3ORPHm3/2aIZmLOvhWXPWyduIMIXvtAc0ybPqsYrwnrAagAmvMMESsZoNB8Enw==</SecurityToken>
     <Bucket>server-log-back</Bucket>
     <Endpoint>https://oss-ap-southeast-1.aliyuncs.com</Endpoint>
  </Params>
</Msg>*/
int CMsgHandle::BuildOssStsMsg(char* buf, int size, const SOCKET_MSG_DEV_OSS_STS& oss_sts)
{

    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_VISITOR_KEY, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SECRET_TOKEN, XmlTrans(oss_sts.token).c_str(), XML_NODE_NAME_MSG_PARAM_SECRET_TOKEN);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_BUCKET, XmlTrans(oss_sts.oss_bucket).c_str(), XML_NODE_NAME_MSG_PARAM_BUCKET);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ENDPOINT, XmlTrans(oss_sts.endpoint).c_str(), XML_NODE_NAME_MSG_PARAM_ENDPOINT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_KEYID, XmlTrans(oss_sts.key_id).c_str(), XML_NODE_NAME_MSG_PARAM_KEYID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SECRET, XmlTrans(oss_sts.secret).c_str(), XML_NODE_NAME_MSG_PARAM_SECRET);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*
<Msg>
  <Type>OpenDoorAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <result>1</result>  //是否开门成功
  </Params>
</Msg>
*/
int CMsgHandle::BuildOpenDoorAckMsg(char* buf, int size, int result)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>OpenDoorAck</%s>\r\n", XML_NODE_NAME_MSG_TYPE, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, result, XML_NODE_NAME_MSG_PARAM_RESULT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*
<Msg>
  <Params>
      <DeviceCode>0311000000</DeviceCode>  (10位纯数字的字符串)
  </Params>
</Msg>
*/
int CMsgHandle::BuildRemoteDeviceWebContorlMsg(char* buf, int size, const SOCKET_MSG_REMOTE_DEV_CONTORL& remote)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_USERNAME, remote.user, XML_NODE_NAME_MSG_USERNAME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PASSWORD, remote.password, XML_NODE_NAME_MSG_PASSWORD);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PORT, remote.port, XML_NODE_NAME_MSG_PARAM_PORT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SSH_PORT, AKCS_REMOTE_DEV_CONTORL_SSH_PORT, XML_NODE_NAME_MSG_PARAM_SSH_PORT);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_REMOTE_CONFIG_SRV, remote.ssh_proxy_domain, XML_NODE_NAME_REMOTE_CONFIG_SRV);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*
<Msg>
  <Type>RequestSensorTrigger</Type>
      <Params>
            <Mode>0</Mode>
      </Params>
</Msg>
*/
int CMsgHandle::BuildRequestSensorTriggerMsg(char* buf, int size, const SOCKET_MSG_SENSOR_TRIGGER& sensor_trigger)
{   
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_MODE, sensor_trigger.mode, XML_NODE_NAME_MSG_MODE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
    

}

/*<Msg>
  <Type>RequestOpenDoor</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MAC>0C11050A72E4</MAC>
    <Relay>0</Relay>    //relay_id，开哪个门
    <TraceID>2123dfasd23223gdd</TraceID>    //新增：时间戳+8位随机字符串(数字+字母大小写字符)
  </Params>
</Msg>*/
int CMsgHandle::ParseRequestOpen(char* buf, SOCKET_MSG_DEV_REQUEST_OPEN& mng_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    AK_LOG_INFO << "ParseRequestOpen Msg: " << text;
    
    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            TransUtf8ToTchar(node->GetText(), mng_msg.type, sizeof(mng_msg.type)); //开门的类型,是普通relay还是security relay
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.mac, sizeof(mng_msg.mac));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    mng_msg.relay =  ATOI(tmp);
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mng_msg.trace_id, sizeof(mng_msg.trace_id));
                }
            }
        }
    }

    return 0;
}

int CMsgHandle::ParseSendDelivery(char* buf, SOCKET_MSG_DEV_SEND_DELIVERY** msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_DEV_SEND_DELIVERY* cur_msg = NULL;
    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;

    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* pTmpNode = NULL;
            for (pTmpNode = node->FirstChildElement(); pTmpNode; pTmpNode = pTmpNode->NextSiblingElement())
            {
                if (strcmp(pTmpNode->Value(), XML_NODE_NAME_MSG_PARAM_DELIVERY) == 0)
                {
                    SOCKET_MSG_DEV_SEND_DELIVERY* node = new SOCKET_MSG_DEV_SEND_DELIVERY;
                    memset(node, 0, sizeof(SOCKET_MSG_DEV_SEND_DELIVERY));
                    TiXmlElement* sub_node = NULL;
                    for (sub_node = pTmpNode->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
                    {

                        if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ACCOUNT) == 0)
                        {
                            TransUtf8ToTchar(sub_node->GetText(), node->account, sizeof(node->account));
                        }
                        if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_NUMBER) == 0)
                        {
                            char tmp[8] = "";
                            TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                            node->amount =  ATOI(tmp);
                        }
                    }
                    if (*msg == NULL)
                    {
                        *msg = node;
                    }
                    else
                    {
                        cur_msg->next = node;
                    }
                    cur_msg = node;
                }
            }
        }
    }

    AK_LOG_INFO << "SendDeliveryMsg :" << buf;
    return 0;
}

int CMsgHandle::ParseSendDeliveryOem(char* buf, SOCKET_MSG_DEV_SEND_DELIVERY_OEM& msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ACCOUNT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.account, sizeof(msg.account));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APT_NUM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.apt_num, sizeof(msg.apt_num));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_BOX_NUM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.box_num, sizeof(msg.box_num));
                }               
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_BOX_PWD) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg.box_pwd, sizeof(msg.box_pwd));
                }
            }
        }
    }

    return 0;
}


/*<Msg>
  <Type>xxx</Type>
  <Params>
    <Tag1>0C11050A72E4</Tag1>
    <Tag2>0</Tag2>
  </Params>
</Msg>*/
std::string CMsgHandle::BuildCommonMsg(std::map<std::string, std::string>& tag_map)
{
    if (tag_map.size() <= 1 || tag_map.count(csmain::xmltag::TYPE) == 0)
    {
        AK_LOG_WARN << "tag not have type or tag map empty";
        return "";
    }

    TiXmlElement root_node(XML_NODE_NAME_MSG);
    TiXmlElement* type_node = new TiXmlElement(XML_NODE_NAME_MSG_TYPE);
    string msg_type = tag_map[csmain::xmltag::TYPE];
    tag_map.erase(csmain::xmltag::TYPE);
    TiXmlText* type_text = new TiXmlText(msg_type.c_str());
    type_node->LinkEndChild(type_text);
    root_node.LinkEndChild(type_node);

    TiXmlElement* msg_param_node = new TiXmlElement(XML_NODE_NAME_MSG_PARAM);
    for (auto it = tag_map.begin(); it != tag_map.end(); it++)
    {
        string tag = it->first;
        TiXmlElement* node = new TiXmlElement(tag.c_str());

        string value = it->second;
        TiXmlText* text = new TiXmlText(value.c_str());
        node->LinkEndChild(text);

        msg_param_node->LinkEndChild(node);
    }
    root_node.LinkEndChild(msg_param_node);

    TiXmlPrinter printer;
    root_node.Accept(&printer);

    return printer.CStr();
}

std::string CMsgHandle::BuildNewCommonMsg(XmlKV& tag_map, XmlKeyAttrKv &attr_map)
{
    if (tag_map.size() <= 1 || tag_map.count(csmain::xmltag::TYPE) == 0)
    {
        AK_LOG_WARN << "tag not have type or tag map empty";
        return "";
    }

    TiXmlElement root_node(XML_NODE_NAME_MSG);
    TiXmlElement* type_node = new TiXmlElement(XML_NODE_NAME_MSG_TYPE);
    string msg_type = tag_map[csmain::xmltag::TYPE];
    tag_map.erase(csmain::xmltag::TYPE);
    TiXmlText* type_text = new TiXmlText(msg_type.c_str());
    type_node->LinkEndChild(type_text);
    root_node.LinkEndChild(type_node);

    TiXmlElement* msg_param_node = new TiXmlElement(XML_NODE_NAME_MSG_PARAM);
    for (auto it = tag_map.begin(); it != tag_map.end(); it++)
    {
        string tag = it->first;
        TiXmlElement* node = new TiXmlElement(tag.c_str());

        string value = it->second;
        TiXmlText* text = new TiXmlText(value.c_str());
        node->LinkEndChild(text);
        auto it_attrs = attr_map.find(tag);
        if (it_attrs != attr_map.end())
        {
            for (auto attr_it = it_attrs->second.begin(); attr_it != it_attrs->second.end(); ++attr_it)
            {
                node->SetAttribute(attr_it->first.c_str(),  attr_it->second.c_str());
            }
        }

        msg_param_node->LinkEndChild(node);
    }
    root_node.LinkEndChild(msg_param_node);

    TiXmlPrinter printer;
    root_node.Accept(&printer);

    return printer.CStr();
}

int CMsgHandle::BuildUpdateConfigMsg(char* buf, int size, SOCKET_MSG_CONFIG* update_config_msg)
{
    if ((buf == NULL) || (update_config_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    memset(buf, 0, size);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);

    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_UPDATE_CONFIG, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(update_config_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, tmp_buffer, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    for (unsigned int i = 0; i < sizeof(update_config_msg->module.item) / sizeof(update_config_msg->module.item[0]); i++)
    {
        if (strlen(update_config_msg->module.item[i]) == 0)
        {
            break;
        }
        TransTcharToUtf8(update_config_msg->module.item[i], tmp_buffer, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, tmp_buffer, XML_NODE_NAME_MSG_PARAM_ITEM);
        strcat_s(buf, size, tmp_line);
    }

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

int CMsgHandle::ParseFlowOutOfLimit(char* buf, SOCKET_MSG_FLOW_OUT_LIMIT *socket_msg_flow_out_limt)
{
    if (buf == NULL || socket_msg_flow_out_limt == NULL)
    {
        AK_LOG_WARN << "ParseFlowOutOfLimit failed,cause null pointer.";
        return -1;
    }
    AK_LOG_INFO << "ParseFlowOutOfLimit MSG=\n" << buf;

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    TiXmlElement* element_node = NULL;
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (element_node = root_node->FirstChildElement(); element_node; element_node = element_node->NextSiblingElement())
    {
        if (strcmp(element_node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = element_node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PERCENT) == 0)
                {
                    socket_msg_flow_out_limt->percent = atof(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_LIMIT) == 0)
                {
                    socket_msg_flow_out_limt->limit = atoll(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

int CMsgHandle::ParseUserInfos(char* buf, SOCKET_MSG_USER_INFO *socket_msg_user_infos)
{
    if (buf == NULL || socket_msg_user_infos == NULL)
    {
        AK_LOG_WARN << "ParseUserInfos failed,cause null pointer.";
        return -1;
    }
    AK_LOG_INFO << "ParseUserInfos text=" << buf;

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    TiXmlElement* element_node = NULL;
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (element_node = root_node->FirstChildElement(); element_node; element_node = element_node->NextSiblingElement())
    {
        if (strcmp(element_node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = element_node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ACID) == 0)
                {
                    Snprintf(socket_msg_user_infos->uuids, sizeof(socket_msg_user_infos->uuids), sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

int CMsgHandle::ParseReportAccessTimesMsg(char *buf, SOCKET_MSG_DEV_REPORT_ACCESS_TIMES  *dev_report_access_times)
{
    if (buf == NULL || dev_report_access_times == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportAccessTimesMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TMPKEY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), dev_report_access_times->temp_key, sizeof(dev_report_access_times->temp_key) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ACCESS_TIMES) == 0)
                {
                    dev_report_access_times->access_times = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UNIQUE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), dev_report_access_times->unique_id, sizeof(dev_report_access_times->unique_id) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}


int CMsgHandle::ParseReportKitDevices(char *buf, std::vector<SOCKET_MSG_DEV_KIT_DEVICE> &kit_devices)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportKitDevices text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) != 0)
        {
            continue;
        }

        TiXmlElement* sub_node = NULL;
        for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
        {
            if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) != 0)
            {
                continue;
            }

            SOCKET_MSG_DEV_KIT_DEVICE kit_device;
            memset(&kit_device, 0, sizeof(kit_device));
            TiXmlElement* item_node = NULL;
            for (item_node = sub_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
            {
                if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(item_node->GetText(), kit_device.mac, sizeof(kit_device.mac) / sizeof(TCHAR));
                }
                else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_LOCALTION) == 0)
                {
                    std::string decode_location = URLDecode(item_node->GetText());
                    Snprintf(kit_device.location, sizeof(kit_device.location), decode_location.c_str());
                }
                else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
                {
                    kit_device.type = ATOI(item_node->GetText());
                }
                else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_SWVER) == 0)
                {
                    TransUtf8ToTchar(item_node->GetText(), kit_device.version, sizeof(kit_device.version) / sizeof(TCHAR));
                }
            }
            kit_devices.push_back(kit_device);
        }
    }

    return 0;
}

int CMsgHandle::ParseReqModifyLocation(char *buf, SOCKET_MSG_DEV_KIT_DEVICE *kit_device)
{
    if (buf == NULL || kit_device == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReqModifyLocation text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_LOCALTION) == 0)
                {
                    std::string decode_location = URLDecode(sub_node->GetText());
                    Snprintf(kit_device->location, sizeof(kit_device->location), decode_location.c_str());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), kit_device->mac, sizeof(kit_device->mac) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}

int CMsgHandle::BuildReqPersonalKitDevices(char *buf, int size, const std::vector<PERSONNAL_DEVICE_SIP> &kit_devices)
{
    if (buf == NULL)
    {
        return -1;
    }

    char* temp_line = new char[XML_NODE_LINE_SIZE];
    char* temp_buffer = new char[XML_NODE_LINE_SIZE];

    memset(buf, 0, size);
    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, temp_line);

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportKitDevices", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, temp_line);


    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, temp_line);

    for (auto kit_device : kit_devices)
    {
        memset(temp_buffer, 0, XML_NODE_LINE_SIZE);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_MAC, kit_device.mac, XML_NODE_NAME_MSG_PARAM_MAC);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);

        std::string encode_location = URLEncode(kit_device.name);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_LOCALTION, encode_location.c_str(), XML_NODE_NAME_MSG_PARAM_LOCALTION);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, kit_device.type, XML_NODE_NAME_MSG_PARAM_TYPE);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);

        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\t\t\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, temp_buffer, XML_NODE_NAME_MSG_PARAM_ITEM);
        strcat_s(buf, size, temp_line);
    }

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, temp_line);

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, temp_line);

    delete []temp_line;
    delete []temp_buffer;

    return 0;
}

int CMsgHandle::BuildReqCommunityKitDevices(char *buf, int size, const std::vector<COMMUNITY_DEVICE_SIP> &kit_devices)
{
    if (buf == NULL)
    {
        return -1;
    }

    char* temp_line = new char[XML_NODE_LINE_SIZE];
    char* temp_buffer = new char[XML_NODE_LINE_SIZE];

    memset(buf, 0, size);
    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, temp_line);

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportKitDevices", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, temp_line);


    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, temp_line);

    for (auto kit_device : kit_devices)
    {
        memset(temp_buffer, 0, XML_NODE_LINE_SIZE);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_MAC, kit_device.mac, XML_NODE_NAME_MSG_PARAM_MAC);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);

        std::string encode_location = URLEncode(kit_device.name);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_LOCALTION, encode_location.c_str(), XML_NODE_NAME_MSG_PARAM_LOCALTION);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, kit_device.type, XML_NODE_NAME_MSG_PARAM_TYPE);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);

        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\t\t\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, temp_buffer, XML_NODE_NAME_MSG_PARAM_ITEM);
        strcat_s(buf, size, temp_line);
    }

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, temp_line);

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, temp_line);

    delete []temp_line;
    delete []temp_buffer;

    return 0;
}

int CMsgHandle::ParseOfflineActiveMsg(char *buf, SOCKET_MSG_DEV_OFFLINE_ACTIVE  *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseOfflineActiveMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->sequence, sizeof(msg->sequence) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_FILENAME2) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->file_name, sizeof(msg->file_name) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_FILEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->file_md5, sizeof(msg->file_md5) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}

int CMsgHandle::ParseReportVoiceMsg(char *buf, PersonalVoiceMsgInfo *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSG_TYPE) == 0)
                {
                    msg->msg_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
                {
                    msg->dev_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->uid, sizeof(msg->uid) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_FILENAME2) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->file_name, sizeof(msg->file_name) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->pic_name, sizeof(msg->pic_name) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}


/*
<Msg>
  <Type>RequestVoiceMsgList</Type>
  <Params>
    <PageIndex>0</PageIndex>  //当前页数；0,1,2...
    <PageSize>5</PageSize>  //单页数目
  </Params>
</Msg>
*/
int CMsgHandle::ParseRequestVoiceMsgList(char *buf, SOCKET_MSG_DEV_VOICE_MSG_LIST *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PAGE_INDEX) == 0)
                {
                    msg->page_index = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PAGE_SIZE) == 0)
                {
                    msg->page_size = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>ReportVoiceMsgList</Type>
  <Params>
    <PageIndex>0</PageIndex>  //当前页数
    <PageSize>5</PageSize>  //单页数目
    <MsgCount>20</MsgCount>  //消息总数
    <Item>   
      <UUID>xxxx</UUID>   //语音留言在云端保存的UUID
      <Time>2022-08-24 10:00:00</Time>   //留言时间，以家庭的时区为准
      <Location>R29</Location>   //门口机的Location
      <FrontDoorMAC>AAAA00000001</FrontDoorMAC>   //门口机的Mac
      <Status>0</Status>   //0-未读；1-已读
    </Item>
    ...
  </Params>
</Msg>
*/
int CMsgHandle::BuildVoiceMsgListNotifyMsg(char* buf, int size, const PersonalVoiceMsgSendList &send_list, const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportVoiceMsgList", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PAGE_INDEX, voice_msg.page_index, XML_NODE_NAME_MSG_PARAM_PAGE_INDEX);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PAGE_SIZE, voice_msg.page_size, XML_NODE_NAME_MSG_PARAM_PAGE_SIZE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSG_COUNT, voice_msg.msg_count, XML_NODE_NAME_MSG_PARAM_MSG_COUNT);
    strcat_s(buf, size, tmp_line);

    for (auto voice_info : send_list)
    {
        memset(tmp_buffer, 0, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_UUID, voice_info.uuid, XML_NODE_NAME_MSG_PARAM_UUID);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_TIME, voice_info.time, XML_NODE_NAME_MSG_PARAM_TIME);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_LOCALTION, voice_info.location, XML_NODE_NAME_MSG_PARAM_LOCALTION);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_FRONTDOOR_MAC, voice_info.front_mac, XML_NODE_NAME_MSG_PARAM_FRONTDOOR_MAC);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_STATUS, voice_info.status, XML_NODE_NAME_MSG_PARAM_STATUS);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\t\t\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, tmp_buffer, XML_NODE_NAME_MSG_PARAM_ITEM);
        strcat_s(buf, size, tmp_line);
    }

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
  <Type>RequestVoiceMsgURL</Type>
  <Params>
    <UUID>xxxx</UUID>  //语音信息UUID
  </Params>
  </Msg>
*/
int CMsgHandle::ParseRequestVoiceMsgUrl(char *buf, SOCKET_MSG_DEV_VOICE_MSG_URL *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->uuid, sizeof(msg->uuid) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}


/*
<Msg>
  <Type>ReportVoiceMsgURL</Type>
  <Params>
    <UUID>xxxx</UUID>  //语音信息UUID
    <URL>https://xxxxx</URL>  //下载地址
  </Params>
  </Msg>
*/
int CMsgHandle::BuildVoiceMsgUrlNotifyMsg(char* buf, int size, const SOCKET_MSG_DEV_VOICE_MSG_URL& url_msg)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportVoiceMsgURL", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_UUID, url_msg.uuid, XML_NODE_NAME_MSG_PARAM_UUID);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_URL2, XmlTrans(url_msg.url).c_str(), XML_NODE_NAME_MSG_PARAM_URL2);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}

/*
<Msg>
  <Type>RequestDelVoiceMsg</Type>
  <Params>
    <UUID>xxxx</UUID>  //语音信息UUID
  </Params>
</Msg>
*/
int CMsgHandle::ParseRequestDelVoiceMsg(char *buf, SOCKET_MSG_DEV_VOICE_MSG_URL *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->uuid, sizeof(msg->uuid) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}


int CMsgHandle::ParseReportThirdCameraInfo(char *buf, ThirdPartyCamreaInfo *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportThirdCameraInfo text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->camera_uuid, sizeof(msg->camera_uuid) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->video_type, sizeof(msg->video_type) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_PT) == 0)
                {
                    msg->video_pt = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_FMTP) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->video_fmtp, sizeof(msg->video_fmtp) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}

int CMsgHandle::BuildReportDelLogNotifyMsg(char* buf, int size)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportDelLog", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DOOR_LOG, 1, XML_NODE_NAME_MSG_PARAM_DOOR_LOG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CALL_LOG, 1, XML_NODE_NAME_MSG_PARAM_CALL_LOG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;

    return 0;
}
