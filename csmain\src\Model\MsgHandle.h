#ifndef __MSG_HANDLE_H__
#define __MSG_HANDLE_H__
#include <string>
#include <map>
#include <vector>
#include "DclientMsgSt.h"
#include "SDMCMsg.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "XmlTagDefine.h"
#include "MsgControl.h"

class CMsgHandle
{
public:
    CMsgHandle();
    ~CMsgHandle();
    int ParseAlarmMsg(SOCKET_MSG_NORMAL* normal_msg, SOCKET_MSG_ALARM* alarm_msg, uint32_t nVer);
    int ParseTextMsg(char* buf, SOCKET_MSG_TEXT_MESSAGE* text_msg);
    /* Begin added by chenyc,2017-05-24,ÔÆÆ½Ì¨½ÓÈëapp¿ª·¢ */
    int ParseCheckTmpKeyMsg(char* buf, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY* tmpkey_info);
    int ParseMotionAlertMsg(char* buf, SOCKET_MSG_MOTION_ALERT* motion_alert);
    int ParseReqArmingMsg(char* buf, SOCKET_MSG_DEV_ARMING* arming_msg);
    int ParseReportArmingMsg(char* buf, SOCKET_MSG_DEV_ARMING* arming_msg);
    int ParseReportActMsg(char* buf, SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    int ParseReqCaptureMsg(char* buf, SOCKET_MSG_REQ_CAPTURE& request_capture);
    int ParseSetMotionAlertMsg(char* buf, int& type);
    int ParseReportStatusMsg(char* buf, SOCKET_MSG_REPORT_STATUS* report_status_message, uint32_t data_size, uint32_t version);
    int ParseReportAccessTimesMsg(char *buf, SOCKET_MSG_DEV_REPORT_ACCESS_TIMES *dev_report_access_times);
    int ParseReportKitDevices(char *buf, std::vector<SOCKET_MSG_DEV_KIT_DEVICE> &kit_devices);
    int ParseReqModifyLocation(char *buf, SOCKET_MSG_DEV_KIT_DEVICE *kit_device);
    int BuildReqConnMsg(char* buf, int size, SOCKET_MSG_REQ_CONN* request_conn_msg);
    int BuildReqStatusMsg(char* buf, int size, SOCKET_MSG_REQ_STATUS* request_status_msg);
    int BuildRemoteControlMsg(char* buf, int size, SOCKET_MSG_REMOTE_CONTROL* remote_control_msg);

    int BuildUpgradeStartMsg(char* buf, int size, SOCKET_MSG_UPGRADE_START* upgrade_start_msg);
    int BuildKeySendMsg(char* buf, int size, SOCKET_MSG_KEY_SEND* key_send_msg);
    int BuildUpgradeSendMsg(char* buf, int size, SOCKET_MSG_UPGRADE_SEND* upgrade_send_msg);
    int BuildTextMessageMsg(char* buf, int size, SOCKET_MSG_TEXT_MESSAGE* text_message);
    int BuildAlarmSendMsg(char* buf, int size, SOCKET_MSG_ALARM_SEND* alarm_msg);
    int BuildAlarmNotifyMsg(char* buf, int size, const SOCKET_MSG_ALARM_SEND* alarm_msg);
    int BuildAlarmDealNotifyMsg(char* buf, int size, const SOCKET_MSG_ALARM_DEAL* alarm_deal_msg);

    /* added by chenyc,2017-05-24,ÔÆÆ½Ì¨½ÓÈëapp¿ª·¢ */
    int BuildCheckTmpKeyAckMsg(char* buf, int size, const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY* tmpkey_msg);
    int ParseAlarmDealMsg(char* buf, SOCKET_MSG_PERSONNAL_ALARM_DEAL* personnal_alarm_deal_info);
    int ParseAlarmDealMsg(char* buf, SOCKET_MSG_ALARM_DEAL* alarm_deal_info);
    int ParseAppReportStatusMsg(char* buf, SOCKET_MSG_PERSONNAL_APP_CONF* personnal_info);
    int BuildReqRtspMsg(char* buf, int size, const SOCKET_MSG_REQ_RTSP& request_rtsp_msg);
    int ParseReqDevListMsg(char* buf, SOCKET_MSG_PERSONNAL_DEV_LIST* device_list);
    //ÉçÇø
    int BuildReqDevListAckMsg(char* buf, unsigned int size, const SOCKET_MSG_PERSONNAL_DEV_LIST* devive_list_msg, const std::vector<COMMUNITY_DEVICE_SIP>& oVec);
    int BuildReqDevListAckMsg(char* buf, unsigned int size, const SOCKET_MSG_PERSONNAL_DEV_LIST* devive_list_msg, const std::vector<PERSONNAL_DEVICE_SIP>& oVec);
    int BuildPersonnalAlarmDealNotifyMsg(char* buf, int size, const SOCKET_MSG_PERSONNAL_ALARM_DEAL* alarm_deal_msg);
    int BuildPerMotionNotifyMsg(char* buf, int size, const std::string& proto,   const std::string& mac, int id, const std::string& sip_account, const std::string& localtion, const std::string& time, const std::string& node);
    int BuildBuildReqArmingMsg(char* buf, int size, const SOCKET_MSG_DEV_ARMING& arming);
    int BuildRespArmingMsg(char* buf, int size, const SOCKET_MSG_DEV_ARMING& arming);
    int BuildKeepRtspMsg(char* buf, int size, const SOCKET_MSG_REQ_RTSP& keepalive_rtsp_msg);
    //v3.4
    int BuildRespDevCodeMsg(char* buf, int size, const std::string& code);
    //chenzhx 4.0
    int BuildCommunityAlarmDealNotifyMsg(char* buf, int size, const SOCKET_MSG_ALARM_DEAL* alarm_deal_msg);
    int BuildRespAppLoginMsg(char* buf, int size, const SOCKET_MSG_RESP_APPLOGIN* app_loing_resp);
    int ParseReqCheckDtmfMsg(char* buf, SOCKET_MSG_CHECK_DTMF& check_dtmf);
    int BuildRespCheckDtmfMsg(char* buf, int size, const SOCKET_MSG_CHECK_DTMF& check_dtmf);
    //4.2
    int ParseVideoStorageMsg(char* payload, SOCKET_MSG_VIDEO_STORAGE& video_storage);
    //ÔËÎ¬
    int BuildReqGetFileMsg(char* buf, int size, const HTTP_MSG_DEV_GET_FILE_COMMON* get_file);
    int BuildReqDevReconnectMsg(char* buf, int size, const HTTP_MSG_DEV_RECONNECT_COMMON* reconnection);
    int BuildSendDevCommandMsg(char* buf, int size, const std::string& command);
    int ParseCommandRespMsg(char* buf, SOCKET_MSG_COMMAND_RESP* command_resp);
    //V4.3
    int ParseCallCaptureMsg(char* buf, SOCKET_MSG_CALL_CAPTURE& call_capture);

    //v4.4
    int BuildAlarmNotifyMsg2MngDev(char* buf, int size, const SOCKET_MSG_ALARM_SEND* alarm_msg);
    int ParseMngDevReportMsg(char* buf, SOCKET_MSG_MNG_DEV_REPORT_MSG& call_capture);
    int BuildReqUpdateServerMsg(char* buf, int size, const std::string& type);

    //v4.6
    int ParseSensorTirggerMsg(char* buf, SOCKET_MSG_SENSOR_TIRGGER_MSG& msg);

    int ParseDevReportVisitorInfo(char* buf, SOCKET_MSG_DEV_REPORT_VISITOR& mng_msg);
    int ParseDevReportVisitorAuth(char* buf, SOCKET_MSG_DEV_REPORT_VISITOR_AUTH& mng_msg);
    int BuildVisitorAuthMsg(char* buf, int size, int count);
    int BuildFaceDataMsg(char* buf, int size, const SOCKET_MSG_DEV_REPORT_FACE_DATA& face_data);
    int BuildVisitorTempKeyAckMsg(char* buf, int size, int temp_key_code);
    //5.0
    int BuildOssStsMsg(char* buf, int size, const SOCKET_MSG_DEV_OSS_STS& oss_sts);

    //v5.2
    int ParseRemoteAck(char* buf, SOCKET_MSG_DEV_REMOTE_ACK& mng_msg);
    int ParseRequestOpen(char* buf, SOCKET_MSG_DEV_REQUEST_OPEN& mng_msg);
    int BuildOpenDoorAckMsg(char* buf, int size, int result);
    int BuildRemoteDeviceWebContorlMsg(char* buf, int size, const SOCKET_MSG_REMOTE_DEV_CONTORL& remote);
    //v6.0
    int ParseSendDelivery(char* buf, SOCKET_MSG_DEV_SEND_DELIVERY** msg);
    int ParseSendDeliveryOem(char* buf, SOCKET_MSG_DEV_SEND_DELIVERY_OEM& msg);
    int ParseFlowOutOfLimit(char *buf, SOCKET_MSG_FLOW_OUT_LIMIT *socket_msg_flow_out_limt);

    int BuildUpdateConfigMsg(char* buf, int size, SOCKET_MSG_CONFIG* update_config_msg);
    int ParseUserInfos(char* buf, SOCKET_MSG_USER_INFO *socket_msg_user_infos);
    int ParseReqChangeRelay(char* buf, SOCKET_MSG_DEV_RELAY_CHANGE& msg);
    int ParseReportRelayStatus(char* buf, char* doornum);

    int BuildGivenKeySendMsg(char* buf, int size, SOCKET_MSG_KEY_SEND* key_send_msg);

    int BuildReqPersonalKitDevices(char *buf, int size, const std::vector<PERSONNAL_DEVICE_SIP> &kit_devices);
    int BuildReqCommunityKitDevices(char *buf, int size, const std::vector<COMMUNITY_DEVICE_SIP> &kit_devices);

    int ParseReportFileMd5Msg(char* buf, SOCKET_MSG_REPORT_FILEMD5& msg);

    //v7.0
    int BuildOfficeKeySendMsg(char* buf, int size, SOCKET_MSG_KEY_SEND* key_send_msg);
    //离线重传消息(压缩包)
    int ParseOfflineActiveMsg(char *buf, SOCKET_MSG_DEV_OFFLINE_ACTIVE  *msg);
    //语音留言上传
    int ParseReportVoiceMsg(char *buf, PersonalVoiceMsgInfo *msg);
    int BuildOnlineNotifyMsg(char* buf, int size, const SOCKET_MSG_DEV_ONLINE_NOTIFY& voice_msg);
    int ParseRequestVoiceMsgList(char *buf, SOCKET_MSG_DEV_VOICE_MSG_LIST *msg);
    int BuildVoiceMsgListNotifyMsg(char* buf, int size, const PersonalVoiceMsgSendList &send_list, const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg);
    int ParseRequestVoiceMsgUrl(char *buf, SOCKET_MSG_DEV_VOICE_MSG_URL *msg);
    int BuildVoiceMsgUrlNotifyMsg(char* buf, int size, const SOCKET_MSG_DEV_VOICE_MSG_URL& url_msg);
    int ParseRequestDelVoiceMsg(char *buf, SOCKET_MSG_DEV_VOICE_MSG_URL *msg);
    int ParseResponseEmergencyControlMsg(char *buf,SOCKET_MSG_EMERGENCY_CONTROL *emergency_control_msg);
    int ParseReportThirdCameraInfo(char *buf, ThirdPartyCamreaInfo *msg);
	int BuildReportDelLogNotifyMsg(char* buf, int size);
    int BuildRequestSensorTriggerMsg(char* buf, int size, const SOCKET_MSG_SENSOR_TRIGGER& sensor_trigger);
    /**
     *
     * <AUTHOR> (2020/6/5)
     *
     * @param tag_map
     * 必须包含KEY:Type作为Type标签,其余的KEY放在Params标签下面 
     *
     * @return std::string
     */

    std::string BuildCommonMsg(std::map<std::string, std::string>& tag_map);
    std::string BuildNewCommonMsg(XmlKV& tag_map, XmlKeyAttrKv &attr_map);
    static CMsgHandle* GetInstance();
private:

    static CMsgHandle* instance;

};

CMsgHandle* GetMsgHandleInstance();

#endif

