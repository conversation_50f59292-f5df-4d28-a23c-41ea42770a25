#include "NodeTimeZone.h"
#include <chrono>
#include <ctime>
#include <iostream>
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "OpRedis.h"
#include "util.h"

extern std::map<string, AKCS_DST> g_time_zone_DST;

/*获取主账号设置的时区对应的时间*/
std::string getNodeCurrentTimeString(const std::string& node)
{
    //TODO:如果网页修改时候更新失败怎么处理，一直用旧的吗？定时更新？设置超时时间？
    //TODO:先全部读数据
    std::string timezone;
    timezone = dbinterface::ResidentPersonalAccount::GetNodeTimeZoneStr(node);

    return GetNodeNowDateTimeByTimeZoneStr(timezone, g_time_zone_DST);
}


