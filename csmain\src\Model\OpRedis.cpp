#include "OpRedis.h"
#include "CachePool.h"
#include <stdio.h>
#include <stdlib.h>
#include<string>

#define HASH_KEY_FOR_NODETIMEZONE "nodetimezone"
#define CACHE_CONN_FOR_NODETIMEZONE "nodetimezone"

int redisGetNodeTimezone(const std::string& node, int& time)
{
    int ret = OP_REDIS_RET_CONN_FAILE;
    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(CACHE_CONN_FOR_NODETIMEZONE); //获取与redis实例的tcp连接
    if (cache_conn)
    {
        std::string key = HASH_KEY_FOR_NODETIMEZONE;
        std::string time;
        time = cache_conn->hget(key, node);
        if (time.empty())
        {
            ret = OP_REDIS_RET_KEY_NOEXIST;
        }
        else //redis里面有记录
        {
            time = String2Int(time);
            ret = OP_REDIS_RET_OK;
        }
        cache_manager->RelCacheConn(cache_conn);//TODO:2019-01-18,后续用guard的形式,避免忘记释放
    }
    return ret;

}

int redisSetNodeTimezone(const std::string& node, int time)
{
    int ret = OP_REDIS_RET_CONN_FAILE;
    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(CACHE_CONN_FOR_NODETIMEZONE);
    if (cache_conn)
    {
        std::string key = HASH_KEY_FOR_NODETIMEZONE;
        cache_conn->hset(key, node, Int2String(time));
        cache_manager->RelCacheConn(cache_conn);
        ret = OP_REDIS_RET_OK;
    }
    return ret;
}




