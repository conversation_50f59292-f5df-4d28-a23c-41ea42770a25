#include "stdafx.h"
#include <sstream>
#include "PersonalAccount.h"
#include "util.h"
#include "ConnectionPool.h"
#include "dbinterface/CommunityInfo.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


#define TABLE_NAME_BIND_CODE    "BindCode"
#define BIND_CODE_LIST          "Code,BindTime,IMEI,Status"
#define COMMUNITY_AREANODE      "Community,AreaNode"

CPersonalAccount* GetPersonalAccountInstance()
{
    return CPersonalAccount::GetInstance();
}

CPersonalAccount* CPersonalAccount::instance = NULL;

CPersonalAccount* CPersonalAccount::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonalAccount();
    }

    return instance;
}

enum
{
    TAB_PERSONNAL_ACCOUNT_INDEX_ID = 0,
    TAB_PERSONNAL_ACCOUNT_INDEX_ACCOUNT,
    TAB_PERSONNAL_ACCOUNT_INDEX_PASSWD,
    TAB_PERSONNAL_ACCOUNT_INDEX_ROLE,
    TAB_PERSONNAL_ACCOUNT_INDEX_PARENTID,
    TAB_PERSONNAL_ACCOUNT_INDEX_EMAIL,
    TAB_PERSONNAL_ACCOUNT_INDEX_INFO,
    TAB_PERSONNAL_ACCOUNT_INDEX_NAME,
    TAB_PERSONNAL_ACCOUNT_INDEX_SIPACCOUNT,
    TAB_PERSONNAL_ACCOUNT_INDEX_CREATETIME,

};

int CPersonalAccount::DaoGetNodeByAppUser(SOCKET_MSG_PERSONNAL_APP_CONF& app_config)
{
    //先查询是否为主账号
    int role;
    char account[64] = {0};
    unsigned int cur_time = 0;
    unsigned int expire_time = 0;
    app_config.is_expire = 0;
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUserAccount(app_config.user, per_account))
    {
        role = per_account.role;
        cur_time = per_account.cur_stamp;
        expire_time = per_account.exp_stamp;
        app_config.unit_id = per_account.unit_id;
        app_config.manager_account_id = per_account.parent_id;
        app_config.lastread_message_id = per_account.lastread_message_id;

        Snprintf(account, sizeof(account), per_account.account);
        Snprintf(app_config.uuid, sizeof(app_config.uuid), per_account.uuid);
        Snprintf(app_config.sip, sizeof(app_config.sip), per_account.sip_account);
        Snprintf(app_config.username, sizeof(app_config.username), per_account.name);
        Snprintf(app_config.email, sizeof(app_config.email), per_account.getEmail());
        Snprintf(app_config.user_info_uuid, sizeof(app_config.user_info_uuid), per_account.user_info_uuid);
        Snprintf(app_config.mobile_number, sizeof(app_config.mobile_number), per_account.getMobileNumber());
        
        if (role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            //expire_time的时间戳 可能大于2038年，int包含不了,MySQL出来是0
            if (expire_time != 0 && expire_time < cur_time)
            {
                app_config.is_expire = 1;
            }
        }
        
        if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_MAIN ||  role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            app_config.id_active = per_account.active;
        }
        else
        {
            app_config.id_active = 1;
        }

        if (IsOfficeRole(role))
        {
            app_config.is_office = 1;
        }
    }
    else
    {
        AK_LOG_WARN << "There is not same account " << app_config.user << " in db.";
        return -1;
    }

    app_config.role = role;
    if (role == ACCOUNT_ROLE_PERSONNAL_MAIN || role == ACCOUNT_ROLE_COMMUNITY_MAIN 
        || IsOfficeRole(role) 
        || role == ACCOUNT_ROLE_COMMUNITY_PM) //主账号
    {
        Snprintf(app_config.node, sizeof(app_config.node), account);
        return 0;
    }

    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(per_account.parent_id, main_account))
    {
        Snprintf(app_config.node, sizeof(app_config.node), main_account.account);
        app_config.lastread_message_id = main_account.lastread_message_id;
        if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_MAIN)
        {
            app_config.id_active = main_account.active;//从账号的激活跟随主账号
        }
        else
        {
            app_config.id_active = 1;
        }
        app_config.unit_id = main_account.unit_id;
        app_config.manager_account_id = main_account.parent_id;
    }
    else
    {
        AK_LOG_WARN << "There is not main account belong to " << app_config.user << " in db.";
        return -1;
    }

    return 0;
}

int CPersonalAccount::DaoGetApplistByNode(const std::string node, std::vector<PERSONNAL_DEVICE_SIP>& device)
{
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    ResidentPerAccountList account_list;
    PERSONNAL_DEVICE_SIP tmp_device;
    dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_PERSONNAL_ATTENDANT, account_list);
    for (const auto account : account_list)
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        Snprintf(tmp_device.name, sizeof(tmp_device.name), account.name);
        Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), account.sip_account);
        Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }

    //再查主账号
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, main_account))
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        Snprintf(tmp_device.name, sizeof(tmp_device.name), main_account.name);
        Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), main_account.sip_account);
        Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), main_account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }
    else
    {
        //主账户不存在返回-1
        return -1;
    }

    return 0;
}

int CPersonalAccount::DaoGetCommunityApplistByNode(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    ResidentPerAccountList account_list;
    COMMUNITY_DEVICE_SIP tmp_device;
    dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_COMMUNITY_ATTENDANT, account_list);
    for (const auto account : account_list)
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        Snprintf(tmp_device.name, sizeof(tmp_device.name), account.name);
        Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), account.sip_account);
        Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }

    //再查主账号
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, main_account))
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        Snprintf(tmp_device.name, sizeof(tmp_device.name), main_account.name);
        Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), main_account.sip_account);
        Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), main_account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }
    else
    {
        //主账户不存在返回-1
        return -1;
    }

    return 0;
}

//获取社区的主账号 如果是社区公共的就是全部主账号，如果是单元，就是单元内的主账号
int CPersonalAccount::DaoGetCommunityAppMaster(const int nGrade, const int manager_id, const int nUnitID, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    ResidentPerAccountList account_list;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == nGrade)
    {
        dbinterface::ResidentPersonalAccount::GetCommPubMainList(manager_id, account_list);
    }
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == nGrade)
    {
        dbinterface::ResidentPersonalAccount::GetCommUnitMainList(nUnitID, account_list);
    }

    COMMUNITY_DEVICE_SIP tmp_device;
    for (const auto account : account_list)
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        Snprintf(tmp_device.name, sizeof(tmp_device.name), account.name);
        Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), account.sip_account);
        Snprintf(tmp_device.room_num, sizeof(tmp_device.room_num), account.room_number);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }

    return 0;

}


int CPersonalAccount::DaoChangeEmail2Uid(char* eamil, int nSize)
{
    //assert(eamil);
    //先查询是否为主账号
    char account[64] = {0};
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetEmailAccount(eamil, per_account))
    {
        Snprintf(account, sizeof(account), per_account.account);
        Snprintf(eamil, nSize, account);
    }
    else
    {
        AK_LOG_WARN << "There is not same account email " << eamil << " in db.";
        return -1;
    }

    return 0;
}

//uid->nick name
int CPersonalAccount::DaoGetNickNameByUid(const std::string& uid, std::string& name)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        name = account.name;
    }
    else
    {
        return -1;
    }
    return 0;
}


//uid->nick name node
//社区/单住户
int CPersonalAccount::DaoGetNickNameAndNodeByUid(const std::string& uid, std::string& name, std::string& node)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            name = account.name;
            node = uid;
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
        {
            name = account.name;
            node = main_account.account;
        }
    }
    else
    {
        return -1;
    }
    
    return 0;
}

//社区/单住户
int CPersonalAccount::DaoGetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& name, std::string& node, int& manager_id)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            node = uid;
            name = account.name;
            manager_id = account.parent_id;
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
        {
            name = account.name;
            node = main_account.account;
            manager_id = main_account.parent_id;
        }
    }
    else
    {
        return -1;
    }

    return 0;
}

int CPersonalAccount::DaoGetAccountBySipAccount(const char* sip, ACCOUNT_MSG& account)
{
    //先查询是否为主账号
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(sip, per_account))
    {
        account.role = per_account.role;
        if (per_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || per_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || per_account.role == ACCOUNT_ROLE_COMMUNITY_PM) //主账号
        {
            Snprintf(account.node, sizeof(account.node), per_account.account);
            account.manager_id = per_account.parent_id;
            account.unit_id = per_account.unit_id;
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, main_account))
        {
            Snprintf(account.node, sizeof(account.node), main_account.account);
            account.manager_id = per_account.parent_id;
            account.unit_id = per_account.unit_id;
        }
    }
    else
    {
        return -1;
    }

    return 0;
}

int CPersonalAccount::DaoGetNickNameByAccount(const std::string& account, std::string& name)
{
    ResidentPerAccount user_account;
    memset(&user_account, 0, sizeof(user_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(account, user_account))
    {
        name = user_account.name;
    }
    else
    {
        return -1;
    }
    return 0;
}

int CPersonalAccount::IsAccountExpire(const std::string& caller, const std::string& callee, int& uid_status)
{
    if(dbinterface::ResidentPersonalAccount::CheckAccountIsExpire(caller))
    {
        uid_status = APP_STATE::APP_STATE_CALLER_EXPIRE;
        return 1;
    }
    else if(dbinterface::ResidentPersonalAccount::CheckAccountIsExpire(callee))
    {
        uid_status = APP_STATE::APP_STATE_CALLEE_EXPIRE;
        return 1;
    }
    else
    {
        return 0;
    }
}
