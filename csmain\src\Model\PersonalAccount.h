#ifndef __PERSONNAL_ACCOUNT_H__
#define __PERSONNAL_ACCOUNT_H__

#include <boost/noncopyable.hpp>
#include "SDMCMsg.h"
#include <set>

namespace csmain
{
enum SipAccountType
{
    PERSONAL_SIP,
    GROUP_SIP,    
};

/*
新：
SmartPlus and indoor monitors：直接呼室内机+App
Phone and indoor monitors：直接呼室内机+Phone
SmartPlus and indoor monitors, with phone as backup：先呼室内机+App，无应答再呼Phone
Indoor monitors with SmartPlus as backup：先呼室内机，再呼App
Indoor monitors with phone as backup：先呼室内机，再呼Phone
Indoor monitors with SmartPlus as backup, finally the phone：先呼室内机，再呼App，无应答再呼Phone

*/
enum NODE_CALL_TYPE
{
    //callloop=0
    NODE_CALL_TYPE_APP_INDOOR = 0,//旧：呼叫该家庭下的SmartPlus+室内机(同时呼叫)app主账号不移出群组响铃
    //V4.3 落地号码+室内机+APP
    //V4.4 改为室内机+phone 用dclient版本判断
    //callloop=2
    NODE_CALL_TYPE_INDOOR_PHONE = 1,//旧：呼叫该家庭（主账户）的落地号码+室内机(同时呼叫)app主账号移出群组响铃(相当之前的开启落地)
    //callloop=1
    NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE = 2,//先呼叫SmartPlus+室内机(同时呼叫)，若未接听，再呼叫落地号码。app主账号不移出群组响铃
    //callloop=2以下
    NODE_CALL_TYPE_INDOOR_BACK_APP = 3,
    NODE_CALL_TYPE_INDOOR_BACK_PHONE = 4,
    NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE = 5,


    NODE_CALL_TYPE_INDOOR_PHONE_OLD = 100,//设备是V4.4时候，代码NODE_CALL_TYPE_INDOOR_PHONE=2 替换为这个
};

}

typedef struct PhoneInfo_T{
    char name[256];
    char node[16];
    char account[16];
    char phone[32];    
    unsigned int mng_id;//社区id或者个人管理员id
    unsigned int unit_id;
    int role;
    int match_num;//匹配phone的长度
}PhoneInfo;


class CPersonalAccount : public boost::noncopyable
{
public:
    CPersonalAccount()
    {
    }
    ~CPersonalAccount()
    {
    }
    enum SwitchType
    {
        ALLOW_PIN = 0,
        FEATURE_PLAN = 1,
    };
    int DaoGetNodeByAppUser(SOCKET_MSG_PERSONNAL_APP_CONF& app_config);
    int DaoGetApplistByNode(const std::string node, std::vector<PERSONNAL_DEVICE_SIP>& device);
    //add by chenzhx ********
    int DaoGetCommunityApplistByNode(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device);
    int DaoGetCommunityAppMaster(const int grade, const int manager_id, const int unit_id, std::vector<COMMUNITY_DEVICE_SIP>& device);
    int DaoChangeEmail2Uid(char* eamil, int size);
    int DaoGetNickNameByUid(const std::string& uid, std::string& name);

    int DaoGetNickNameAndNodeByUid(const std::string& uid, std::string& name, std::string& node);
    int DaoGetAccountBySipAccount(const char* sip, ACCOUNT_MSG& account);

    int DaoGetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& name, std::string& node, int& manager_id);

    int DaoGetNickNameByAccount(const std::string& account, std::string& name);
    int DaoGetNodeActiveByPhone(const std::string& phone, const std::string& caller);
    //V5.4
    int DaoGetPhoneInfoList(const std::string& phone, std::vector<PhoneInfo> &phone_info_list);
    int DaoGetPhoneInfoByMngID(const std::string& phone, unsigned int mng_id, PhoneInfo &phone_info);
    int DaoGetPhoneInfoByNode(const std::string& phone, const std::string &node, PhoneInfo &phone_info);
    std::string  DaoGetPhoneBySip(const std::string& sip, int type, std::string &phone_code);
    int IsAccountExpire(const std::string& caller, const std::string& callee, int& uid_status);

    static CPersonalAccount* GetInstance();
private:

    static CPersonalAccount* instance;

};

CPersonalAccount* GetPersonalAccountInstance();

#endif //__PERSONNAL_ACCOUNT_H__
