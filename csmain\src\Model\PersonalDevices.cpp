#include <sstream>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "PersonalDevices.h"
#include "ConnectionPool.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"


PersonalDevices* GetPersonalDevicesInstance()
{
    return PersonalDevices::GetInstance();
}

PersonalDevices* PersonalDevices::instance = NULL;

PersonalDevices* PersonalDevices::GetInstance()
{
    if (instance == NULL)
    {
        instance = new PersonalDevices();
    }

    return instance;
}

int PersonalDevices::DaoGetMacRtspPwd(const std::string& mac, std::string& pwd)
{
   ResidentDev dev;
    if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
    {
        pwd = dev.rtsppwd;
    }
    else 
    {
        return -1;
    }

    return 0;
}

