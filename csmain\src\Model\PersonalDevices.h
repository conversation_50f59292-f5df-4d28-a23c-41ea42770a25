#ifndef __CSVS_PERSONNAL_DEVICES_H__
#define __CSVS_PERSONNAL_DEVICES_H__

#include <string>
#include <boost/noncopyable.hpp>

class PersonalDevices : public boost::noncopyable
{
public:
    PersonalDevices()
    {
    }
    ~PersonalDevices()
    {
    }
    int DaoGetMacRtspPwd(const std::string& mac, std::string& pwd);
    int DaoGetID(const std::string& mac, uint32_t& id);
    static PersonalDevices* GetInstance();
private:

    static PersonalDevices* instance;

};

PersonalDevices* GetPersonalDevicesInstance();

#endif //__CSVS_PERSONNAL_DEVICES_H__
