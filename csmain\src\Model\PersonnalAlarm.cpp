#include "stdafx.h"
#include "PersonnalAlarm.h"
#include <boost/algorithm/string.hpp>
#include "ConnectionPool.h"
#include "HttpRequest.h"
#include "MsgControl.h"
#include "util.h"
#include "dbinterface/UUID.h"

#define TABLE_NAME_PERSONNAL_ALARMS "PersonalAlarms"

extern AKCS_CONF gstAKCSConf;

CPersonnalAlarm* GetPersonnalAlarmInstance()
{
    return CPersonnalAlarm::GetInstance();
}

CPersonnalAlarm::CPersonnalAlarm()
{

}

CPersonnalAlarm::~CPersonnalAlarm()
{

}

CPersonnalAlarm* CPersonnalAlarm::instance = NULL;

CPersonnalAlarm* CPersonnalAlarm::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonnalAlarm();
    }

    return instance;
}

enum
{
    TAB_ALARM_INDEX_ID = 0,
    TAB_ALARM_INDEX_ALARMTYPE,
    TAB_ALARM_INDEX_COMMUNITY,
    TAB_ALARM_INDEX_DEVICENODE,
    TAB_ALARM_INDEX_EXTENSION,
    TAB_ALARM_INDEX_ALARMTIME,
    TAB_ALARM_INDEX_STATUS,
    TAB_ALARM_INDEX_DEALTIME,
    TAB_ALARM_INDEX_DEALUSER,
    TAB_ALARM_INDEX_DEALTYPE,
    TAB_ALARM_INDEX_DEALRESULT,
};

//添加ALARM
int CPersonnalAlarm::AddAlarm(PERSONNAL_ALARM& personnal_alarm)
{
    if(gstAKCSConf.is_aws)
    {
        AwsInsertPersonalAlarm(personnal_alarm);
        return 0;
    }
    
    return dbinterface::PersonalAlarm::AddAlarm(personnal_alarm, gstAKCSConf.server_tag);
}
//更新告警状态
int CPersonnalAlarm::DealAlarmStatus(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& alarm_deal_info)
{
    std::string user = alarm_deal_info.user;
    boost::replace_all(user, "'", "\\'");

    if(gstAKCSConf.is_aws)
    {
        //http到阿里云
        GetMsgControlInstance()->PostAwsDealAlarmHttpReq(TABLE_NAME_PERSONNAL_ALARMS, user, ATOI(alarm_deal_info.alarm_id));
        return 0;
    }
    PERSONAL_ALARM_DEAL_INFO deal_info;
    Snprintf(deal_info.result, sizeof(deal_info.result) - 1, alarm_deal_info.result);
    Snprintf(deal_info.user, sizeof(deal_info.user) - 1, alarm_deal_info.user);
    Snprintf(deal_info.alarm_id, sizeof(deal_info.alarm_id) - 1, alarm_deal_info.alarm_id);
    return dbinterface::PersonalAlarm::DealAlarmStatus(deal_info);
}

//通过alarm id获取到告警解除时的相关信息
int CPersonnalAlarm::GetAlarmInfo(const std::string& id, SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE& alarm_info)
{
    PERSONAL_ALARM_DEAL_OFFLINE_INFO offline_info;
    if (0 == dbinterface::PersonalAlarm::GetAlarmInfo(id, offline_info))
    {
        Snprintf(alarm_info.alarm_type, sizeof(alarm_info.alarm_type) - 1, offline_info.alarm_type);
        Snprintf(alarm_info.mac, sizeof(alarm_info.mac) - 1, offline_info.mac);
        Snprintf(alarm_info.device_location, sizeof(alarm_info.device_location) - 1, offline_info.device_location);
        Snprintf(alarm_info.community, sizeof(alarm_info.community) - 1, offline_info.community);
        alarm_info.alarm_code = offline_info.alarm_code;
        alarm_info.alarm_zone = offline_info.alarm_zone;
        alarm_info.alarm_location = offline_info.alarm_location;
        alarm_info.alarm_customize = offline_info.alarm_customize;
        alarm_info.trace_id = offline_info.trace_id;
        return 0;
    }
    return -1;
}



