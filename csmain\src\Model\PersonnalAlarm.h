#ifndef __PERSONNAL_ALARM_H__
#define __PERSONNAL_ALARM_H__

#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/AlarmDB.h"


class CPersonnalAlarm
{
public:
    CPersonnalAlarm();
    ~CPersonnalAlarm();
    enum eAlarmStatus
    {
        UNDEAL = 0,
        DEALED,
    };

    enum eDealType
    {
        MANUAL = 0,
        DEAL,
    };

    //添加ALARM
    int AddAlarm(PERSONNAL_ALARM& Alarm);

    int DealAlarmStatus(const SOCKET_MSG_PERSONNAL_ALARM_DEAL& alarm_deal_info);
    int GetAlarmInfo(const std::string& id, SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE& alarm_info);
    static CPersonnalAlarm* GetInstance();

private:
    static CPersonnalAlarm* instance;

};

CPersonnalAlarm* GetPersonnalAlarmInstance();
#endif //__PERSONNAL_ALARM_H__

