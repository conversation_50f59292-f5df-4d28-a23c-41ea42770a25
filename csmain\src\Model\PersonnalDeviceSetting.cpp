#include "stdafx.h"
#include "util.h"
#include <sstream>
#include "PersonnalDeviceSetting.h"
#include "ConnectionPool.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"


CPersonnalDeviceSetting* GetPersonnalDevSettingInstance()
{
    return CPersonnalDeviceSetting::GetInstance();
}

CPersonnalDeviceSetting::CPersonnalDeviceSetting()
{

}

CPersonnalDeviceSetting::~CPersonnalDeviceSetting()
{

}

CPersonnalDeviceSetting* CPersonnalDeviceSetting::instance = NULL;

CPersonnalDeviceSetting* CPersonnalDeviceSetting::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonnalDeviceSetting();
    }

    return instance;
}


//个人终端用户
int CPersonnalDeviceSetting::GetDeviceSettingByNode(const std::string strNode, int type, std::vector<PERSONNAL_DEVICE_SIP>& oDev)
{
    ResidentDeviceList devlist;
    if (type != DEVICE_TYPE_INDOOR)
    {
        if (0 == dbinterface::ResidentPerDevices::GetNodeIndoorDevList(strNode, devlist))
        {
            for (const auto dev : devlist)
            {
                PERSONNAL_DEVICE_SIP tmp_device;
                memset(&tmp_device, 0, sizeof(tmp_device));
                Snprintf(tmp_device.name, sizeof(tmp_device.name), dev.location);
                Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), dev.sip);
                Snprintf(tmp_device.ip, sizeof(tmp_device.ip), dev.ipaddr);
                tmp_device.type = dev.dev_type;
                Snprintf(tmp_device.mac, sizeof(tmp_device.mac), dev.mac);
                Snprintf(tmp_device.rtsp_password, sizeof(tmp_device.rtsp_password), dev.rtsppwd);
                Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), dev.uuid);
                oDev.push_back(tmp_device);
            }
            return 0;
        }
    }
    else //只有室内机才查出室外机
    {
        if (0 == dbinterface::ResidentPerDevices::GetNodeDevList(strNode, devlist))
        {
            for (const auto dev : devlist)
            {
                PERSONNAL_DEVICE_SIP tmp_device;
                memset(&tmp_device, 0, sizeof(tmp_device));
                Snprintf(tmp_device.name, sizeof(tmp_device.name), dev.location);
                Snprintf(tmp_device.sip_account, sizeof(tmp_device.sip_account), dev.sip);
                Snprintf(tmp_device.ip, sizeof(tmp_device.ip), dev.ipaddr);
                tmp_device.type = dev.dev_type;
                Snprintf(tmp_device.mac, sizeof(tmp_device.mac), dev.mac);
                Snprintf(tmp_device.rtsp_password, sizeof(tmp_device.rtsp_password), dev.rtsppwd);
                Snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), dev.uuid);
                oDev.push_back(tmp_device);
            }
            return 0;
        }
    }
    
    return -1;

}

