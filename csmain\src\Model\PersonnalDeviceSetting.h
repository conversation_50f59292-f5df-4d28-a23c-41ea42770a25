#ifndef __PERSONAL_DEVICE_SETTING_H__
#define __PERSONAL_DEVICE_SETTING_H__

#define TABLE_NAME_PERSONNAL_DEVICES    "PersonalDevices"

enum
{
    TAB_PER_DEVICES_INDEX_ID = 0,
    TAB_PER_DEVICES_INDEX_TYPE,
    TAB_PER_DEVICES_INDEX_COMMUNITY,
    TAB_PER_DEVICES_INDEX_DEVICENODE,
    TAB_PER_DEVICES_INDEX_EXTENSION,
    TAB_PER_DEVICES_INDEX_IPADDRESS,
    TAB_PER_DEVICES_INDEX_GATEWAY,
    TAB_PER_DEVICES_INDEX_SUBNETMASK,
    TAB_PER_DEVICES_INDEX_PRIMARYDNS,
    TAB_PER_DEVICES_INDEX_SECONDARYDNS,
    TAB_PER_DEVICES_INDEX_MAC,
    TAB_PER_DEVICES_INDEX_FIRMWARE,
    TAB_PER_DEVICES_INDEX_HARDWARE,
    TAB_PER_DEVICES_INDEX_STATUS,
    TAB_PER_DEVICES_INDEX_OUTERIP, //增加外网IP
    TAB_PER_DEVICES_INDEX_PORT,
    TAB_PER_DEVICES_INDEX_LASTCONN,
    TAB_PER_DEVICES_INDEX_PRIVATEKEYMD5,
    TAB_PER_DEVICES_INDEX_RFIDMD5,
    TAB_PER_DEVICES_INDEX_CONFIGSETTINGS,
    TAB_PER_DEVICES_INDEX_CONFIGMD5,
    TAB_PER_DEVICES_INDEX_SIPACCOUNT,
    TAB_PER_DEVICES_INDEX_RTSPPWD,
    TAB_PER_DEVICES_INDEX_LOCATION,
    TAB_PER_DEVICES_INDEX_CREATETIME,
};

class CPersonnalDeviceSetting
{
public:
    CPersonnalDeviceSetting();
    ~CPersonnalDeviceSetting();
    static CPersonnalDeviceSetting* GetInstance();
    int GetDeviceSettingByNode(const std::string node, int type, std::vector<PERSONNAL_DEVICE_SIP>& device);

private:
    static CPersonnalDeviceSetting* instance;

};

CPersonnalDeviceSetting* GetPersonnalDevSettingInstance();

#endif //__PERSONAL_DEVICE_SETTING_H__
