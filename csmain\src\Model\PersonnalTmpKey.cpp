#include "stdafx.h"
#include <sstream>
#include <util.h>
#include "PersonnalTmpKey.h"
#include "MsgControl.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/PerNodeDevices.h"
#include "dbinterface/CommunityRoom.h"


#define TABLE_NAME_PERSONNAL_TMP_KEY    "PersonalAppTmpKey"
#define TABLE_NAME_PUB_TMP_KEY          "PubAppTmpKey"

extern std::map<string, AKCS_DST> g_time_zone_DST;
extern AKCS_CONF gstAKCSConf;

CPersonnalTmpKey* GetPersonnalTmpKeyInstance()
{
    return CPersonnalTmpKey::GetInstance();
}

CPersonnalTmpKey::CPersonnalTmpKey()
{

}

CPersonnalTmpKey::~CPersonnalTmpKey()
{

}

CPersonnalTmpKey* CPersonnalTmpKey::instance = NULL;

CPersonnalTmpKey* CPersonnalTmpKey::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonnalTmpKey();
    }

    return instance;
}

    
//PersonalAppTmpKey表：用户家庭设备 校验临时秘钥
bool CPersonnalTmpKey::CheckPersonalAppTmpKeyByPerDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey)
{
    return dbinterface::PersonalAppTmpKey::CheckPersonalAppTmpKeyByPerDev(check_tmpkey, g_time_zone_DST);
}


//PersonalAppTmpKey表：社区公共设备 校验临时秘钥
bool CPersonnalTmpKey::CheckPersonalAppTmpKeyByPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey)
{
    return dbinterface::PersonalAppTmpKey::CheckPersonalAppTmpKeyByPubDev(check_tmpkey, g_time_zone_DST);
}


//PubAppTmpKey表：社区设备 校验临时秘钥 
bool CPersonnalTmpKey::CheckPubAppTmpKey(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey)
{
    return dbinterface::PersonalAppTmpKey::CheckPubAppTmpKey(check_tmpkey, g_time_zone_DST);
}

//单住户公共设备 校验临时秘钥
//Noted by czw: 现已不可添加单住户公共设备,故基本不维护了,尽量不修改到
bool CPersonnalTmpKey::CheckTmpKeyBySingleTenantPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey)
{
    return dbinterface::PersonalAppTmpKey::CheckTmpKeyBySingleTenantPubDev(check_tmpkey, g_time_zone_DST);
}

int CPersonnalTmpKey::GetNodesByPublicDevID(int public_device_id, std::vector<PER_NODE_DEVICES>& device_ids)
{
    return dbinterface::PerNodeDevices::GetNodesByPublicDevID(public_device_id, device_ids);
}

int CPersonnalTmpKey::DaoAddTempKey(const SOCKET_MSG_DEV_REPORT_VISITOR& dev_visitor_info, const std::vector<std::string>& dev)
{
    return dbinterface::PersonalAppTmpKey::AddTempKey(dev_visitor_info, dev, g_time_zone_DST);
}

//离线二维码，根据设备上报 更新可用次数
int CPersonnalTmpKey::UpdateAccessTimes(const SOCKET_MSG_DEV_REPORT_ACCESS_TIMES& report_access_times)
{
    return dbinterface::PersonalAppTmpKey::UpdateAccessTimes(report_access_times);
}

//在线二维码，根据云端校验成功后 更新可用次数
void CPersonnalTmpKey::UpdateAccessTimes(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& tmp_key)
{
    
    if(tmp_key.result == 1)
    {
        //只有开门成功，即result=0时，才能增加使用次数
        return;
    }
    
    if(dbinterface::PersonalAppTmpKey::ONCE_SCHED == tmp_key.sche_type || dbinterface::PersonalAppTmpKey::EACH_DOOR_ONCE_SCHED == tmp_key.sche_type)
    {
        if(gstAKCSConf.is_aws)
        {
            //http到阿里云
            std::string table = tmp_key.key_table_type == dbinterface::PersonalAppTmpKey::PER_TMP_KEY ? "PersonalAppTmpKey" : "PubAppTmpKey";
            GetMsgControlInstance()->PostAwsTmpKeyHttpReq(tmp_key.access_times, tmp_key.key_id, table);
        }
        else
        {
            dbinterface::PersonalAppTmpKey::UpdateAccessTimes(tmp_key);
        }
    }
}

bool CPersonnalTmpKey::IsPersonalAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone)
{
    return dbinterface::PersonalAppTmpKey::IsPersonalAppTmpKeyValid(rldb_conn, check_tmpkey, time_zone, g_time_zone_DST);
}

bool CPersonnalTmpKey::IsPubAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone)
{
    return dbinterface::PersonalAppTmpKey::IsPubAppTmpKeyValid(rldb_conn, check_tmpkey, time_zone, g_time_zone_DST);
}

