#ifndef __PERSONNAL_TMP_KEY_H__
#define __PERSONNAL_TMP_KEY_H__

#include <boost/noncopyable.hpp>
#include "dbinterface/PersonalAppTmpKey.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"


class CPersonnalTmpKey : public boost::noncopyable
{
public:
    CPersonnalTmpKey();
    ~CPersonnalTmpKey();

    static CPersonnalTmpKey* GetInstance();
    bool CheckPersonalAppTmpKeyByPerDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey);
    bool CheckPersonalAppTmpKeyByPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey);
    bool CheckPubAppTmpKey(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey);
    bool CheckTmpKeyBySingleTenantPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey);

    int DaoAddTempKey(const SOCKET_MSG_DEV_REPORT_VISITOR& dev_visitor_info, const std::vector<std::string>& dev);
    int UpdateAccessTimes(const SOCKET_MSG_DEV_REPORT_ACCESS_TIMES& report_access_times);
    void UpdateAccessTimes(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& tmp_key);
private:

    static CPersonnalTmpKey* instance;
    
    int GetNodesByPublicDevID(int public_device_id, std::vector<PER_NODE_DEVICES>& device_ids); 
    void GetUnitAptByID(CRldb* rldb_conn, int room_id,SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY&     check_tmpkey);
    bool IsPersonalAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone);    
    bool IsPubAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone);
    const uint32_t week_day[7] = {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40}; //从周日开始，到周六结束
    const uint32_t relay_num[4] = {0x01, 0x02, 0x04, 0x08}; //对应4个门控relay
};


CPersonnalTmpKey* GetPersonnalTmpKeyInstance();

#endif //__PERSONNAL_TMP_KEY_H__

