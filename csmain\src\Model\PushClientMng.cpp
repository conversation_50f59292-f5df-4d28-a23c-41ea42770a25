#include <map>
#include <set>
#include <mutex>
#include "PushClientMng.h"
#include "util.h"

CPushClientMng* CPushClientMng::pInstance_ = nullptr;

CPushClientMng* CPushClientMng::Instance()
{
    if (!pInstance_)
    {
        pInstance_ = new CPushClientMng();
    }
    return pInstance_;
}

void CPushClientMng::AddPushSrv(const std::string& push_addr, const PushClientPtr& push_cli)
{
    std::lock_guard<std::mutex> lock(push_clis_mutex_);
    push_clis_.insert(std::pair<std::string, PushClientPtr> (push_addr, push_cli));
}

void CPushClientMng::UpdatePushSrv(const std::set<std::string>& push_addrs, evpp::EventLoop* etcd_loop)
{
    //TODO,2019-02-28,后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(push_clis_mutex_);
    for (const auto& push_addr : push_addrs) //先检查新上线的Push srv
    {
        auto it = push_clis_.find(push_addr);
        if (it == push_clis_.end())
        {
            AK_LOG_INFO << "add Push_cli_ptr " << push_addr;
            PushClientPtr push_cli_ptr(new CPushClient(etcd_loop, push_addr, "csroute push client"));
            push_cli_ptr->Start();
            push_clis_.insert(std::pair<std::string, PushClientPtr>(push_addr, push_cli_ptr));
        }
        else//更新的tcp连接,也就是Push_addr不变,但是对应的与csPush的tcp连接已经改变了,如果是csPush主动先断开,同时马上又拉起注册进etcd了,
            //此时本端还没来得及删除掉对应的pair,那么此时需要刷新
        {
            if (!it->second->IsConnStatus())
            {
                AK_LOG_INFO << "update Push_cli_ptr, push_addr is:" << push_addr;
                //added by chenyc, 2019-05-24,由于etcd的key改成永久有效的，所以对于更新的tcp连接,不再需要处理了.后续改成续约的话,就要重新打开该开关
#if 0
                PushClientPtr Push_cli_ptr(new CPushClient(etcd_loop, Push_addr, "csmain client", logic_srv_id));
                Push_cli_ptr->Start();
                push_clis_[Push_addr] = Push_cli_ptr;
#endif
            }
        }
    }
    //再检查下线的Push srv
    if (push_clis_.size() == push_addrs.size())
    {
        return;
    }
    for (auto it = push_clis_.begin(); it != push_clis_.end();)
    {
        AK_LOG_INFO << "push_clis_ is " << push_clis_.size();
        auto it2 = push_addrs.find(it->first);
        if (it2 == push_addrs.end())
        {
            //AK_LOG_INFO << "del Push_cli_ptr";
            //1、当通过etcd的watch回调到这里的时候,对端已经关闭tcp很长时间了,所以CPushClient::OnConnection要增加
            //   client_.Disconnect();//当对端主动关闭的时候,本段立马执行关闭的动作
            //2、当对端并没有断开tcp连接，只是由于对端与etcd之间断开连接了,此时相当于本段主动断开连接的
            it->second->Stop();
            
            //先简单处理：等待3秒再来删除，不然路由线程和etcd线程同时处理会挂掉
            std::lock_guard<std::mutex> lock(push_clis_remove_mutex_);
            push_remove_clis_.push_back(it->second);
            etcd_loop->RunAfter(evpp::Duration(3.0), std::bind(&CPushClientMng::RemoveDisconnectCli, this));
            
            push_clis_.erase(it++);
        }
        else
        {
            it++;
        }
    }
}

void CPushClientMng::RemoveDisconnectCli()
{
    std::lock_guard<std::mutex> lock(push_clis_remove_mutex_);
    for(auto it = push_remove_clis_.begin(); it != push_remove_clis_.end(); it++)
    {
        AK_LOG_INFO << "Remote Push Push_cli_ptr:" << it->get()->GetAddr();
        it = push_remove_clis_.erase(it);
        if(it == push_remove_clis_.end())
        {
            break;
        }
    }
}

const PushClientPtr CPushClientMng::GetPushSrv()
{
    if (outer_push_ && outer_push_->IsConnStatus())
    {
        return outer_push_;
    }
    
	int srv_num = push_clis_.size();
    int index = GetRandomNum(srv_num);
    int i = 0;

    std::lock_guard<std::mutex> lock(push_clis_mutex_);
    std::map<std::string/*ip:port*/, PushClientPtr>::iterator it;
    for(it=push_clis_.begin();it!=push_clis_.end(); i++,it++)
    {
        if (i == index && it->second->IsConnStatus())
        {
            return it->second;
        }
    }

    //返回一个可用的
    for(it=push_clis_.begin();it!=push_clis_.end();it++)
    {
        if (it->second->IsConnStatus())
        {
            return it->second;
        }
    }    
    AK_LOG_WARN << "Can not found cspush client!";
    return nullptr;
}

const PushClientMap CPushClientMng::GetAllPushSrv()
{
    std::lock_guard<std::mutex> lock(push_clis_mutex_);
    return push_clis_;
}

const PushClientPtr CPushClientMng::GetOuterPushSrv()
{
    if (outer_push_ && outer_push_->IsConnStatus())
    {
        return outer_push_;
    }
    return nullptr;
}
