#include <sstream>
#include "VideoSchedule.h"
#include "VideoSchedMng.h"
#include "dbinterface/VideoScheduleDB.h"

CVideoScheds* CVideoScheds::instance = NULL;

CVideoScheds* CVideoScheds::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CVideoScheds();
    }
    return instance;
}


int CVideoScheds::DaoGetAllSchedList(OnceSchedMap& once_sched,
                                     DailySchedMap& daily_sched,
                                     WeeklySchedMap& weekly_sched)
{
    VideoScheduleList once_list;
    OnceSched sched = {0};
    if (0 == dbinterface::VideoSchedule::GetVideoScheduleInfo(ONCE_SCHED, once_list))
    {
        for (const auto video : once_list)
        {
            ::snprintf(sched.begin_time, sizeof(sched.begin_time), "%s %s", video.start_day, video.start_time);
            ::snprintf(sched.end_time, sizeof(sched.end_time), "%s %s", video.stop_day, video.stop_time);
            OnceSchedIter iter = once_sched.find(video.mac);
            if (iter != once_sched.end())
            {
                std::map<uint32_t, OnceSched>& once_sched_pair = iter->second;
                once_sched_pair.insert(std::pair<uint32_t, OnceSched>(video.id, sched));
            }
            else
            {
                std::map<uint32_t, OnceSched> once_sched_pair;
                once_sched_pair[video.id] = sched;
                once_sched.insert(std::pair<std::string, std::map<uint32_t, OnceSched> >(video.mac, once_sched_pair));
            }
            ::memset(&sched, 0, sizeof(sched));
        }
    }

    VideoScheduleList daily_list;
    DailySched daily_sched_one = {0};
    if (0 == dbinterface::VideoSchedule::GetVideoScheduleInfo(DAILY_SCHED, daily_list))
    {
        for (const auto video : daily_list)
        {
            Snprintf(daily_sched_one.begin_time, sizeof(daily_sched_one.begin_time), video.start_time);
            Snprintf(daily_sched_one.end_time, sizeof(daily_sched_one.end_time), video.stop_time);
            DailySchedIter iter = daily_sched.find(video.mac);
            if (iter != daily_sched.end())
            {
                std::map<uint32_t, DailySched>& daily_sched_pair = iter->second;
                daily_sched_pair.insert(std::pair<uint32_t, DailySched>(video.id, daily_sched_one));
            }
            else
            {
                std::map<uint32_t, DailySched> daily_sched_pair;
                daily_sched_pair[video.id] = daily_sched_one;
                daily_sched.insert(std::pair<std::string, std::map<uint32_t, DailySched> >(video.mac, daily_sched_pair));
            }
            //id_daily_schde.clear();
            ::memset(&daily_sched_one, 0, sizeof(daily_sched_one));
        }
    }

    VideoScheduleList weekly_list;
    WeeklySched weekly_sched_one = {0};
    if (0 == dbinterface::VideoSchedule::GetVideoScheduleInfo(WEEKLY_SCHED, weekly_list))
    {
        for (const auto video : weekly_list)
        {
           weekly_sched_one.date_flag = video.date_flag;
            Snprintf(weekly_sched_one.begin_time, sizeof(weekly_sched_one.begin_time), video.start_time);
            Snprintf(weekly_sched_one.end_time, sizeof(weekly_sched_one.end_time), video.stop_time);

            WeeklySchedIter iter = weekly_sched.find(video.mac);
            if (iter != weekly_sched.end())
            {
                std::map<uint32_t, WeeklySched>& weekly_sched_pair = iter->second;
                weekly_sched_pair.insert(std::pair<uint32_t, WeeklySched>(video.id, weekly_sched_one));
            }
            else
            {
                std::map<uint32_t, WeeklySched> weekly_sched_pair;
                weekly_sched_pair[video.id] = weekly_sched_one;
                weekly_sched.insert(std::pair<std::string, std::map<uint32_t, WeeklySched> >(video.mac, weekly_sched_pair));
            }
            ::memset(&weekly_sched_one, 0, sizeof(weekly_sched_one));
        }
    }
    
    return 0;

}


//TODO 设置一次性视频录制定时器状态
void CVideoScheds::DaoSetSchedStatus(int id)
{
    dbinterface::VideoSchedule::SetSchedStatus(id);
    return;
}

