#ifndef __AKCS_VIDEO_SCHED_H__
#define __AKCS_VIDEO_SCHED_H__

#include <vector>
#include <string>
#include <atomic>
#include <map>
#include "stdafx.h"
#include "VideoSchedMng.h"

class CVideoScheds
{
public:
    enum SchedType
    {
        ONCE_SCHED = 0,
        DAILY_SCHED,
        WEEKLY_SCHED,
    };

public:
    CVideoScheds()
    {
    }
    ~CVideoScheds()
    {
    }
    int DaoGetAllSchedList(OnceSchedMap& once_sched,
                           DailySchedMap& daily_sched,
                           WeeklySchedMap& weekly_sched);
    void DaoSetSchedStatus(int id);
    static CVideoScheds* GetInstance();
private:
    static CVideoScheds* instance;

};

#endif //__AKCS_VIDEO_SCHED_H__
