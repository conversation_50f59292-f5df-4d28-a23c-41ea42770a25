#ifndef __CSMAIN_MSG_BUILD_H__
#define __CSMAIN_MSG_BUILD_H__
#include <list>
#include <string>
#include <map>
#include <iostream>
#include <unordered_map>
#include <map>
#include "tinyxml.h"


class XmlBuilder {
private:
    TiXmlDocument xmlDoc;
    TiXmlElement* rootElement;
    TiXmlElement* paramsElement; // 新增一个成员变量

public:
    XmlBuilder(const std::string& msg_type_name) {
        rootElement = new TiXmlElement("Msg");
        xmlDoc.LinkEndChild(rootElement);

        TiXmlElement* keyElement = new TiXmlElement("Type");
        keyElement->LinkEndChild(new TiXmlText(msg_type_name.c_str()));
        rootElement->LinkEndChild(keyElement); // 添加到 Params 节点下

        paramsElement = new TiXmlElement("Params"); // 初始化 Params 节点
        rootElement->LinkEndChild(paramsElement);
    }

    void addKeyValue(const std::map<std::string, std::string>& map) {
        for (const auto& pair : map) {
            TiXmlElement* subElement = new TiXmlElement(pair.first.c_str());
            subElement->LinkEndChild(new TiXmlText(pair.second.c_str()));
            paramsElement->LinkEndChild(subElement); // 添加到 Params 节点下
        }
    }

    void addKeyListValue(const std::string &parent_key, const std::string &key, const std::map<std::string/*value*/, std::map<std::string, std::string> /*attr*/>& relayMap) {
        TiXmlElement* relayElement = new TiXmlElement(parent_key.c_str());
        paramsElement->LinkEndChild(relayElement); // 添加到 Params 节点下
        for (const auto& pair : relayMap) {
            TiXmlElement* relayNode = new TiXmlElement(key.c_str());
            relayNode->LinkEndChild(new TiXmlText(pair.first.c_str())); // 添加 relay 值
            const auto& relayAttrs = pair.second;
            for (const auto& attrPair : relayAttrs) {
                relayNode->SetAttribute(attrPair.first.c_str(), attrPair.second.c_str()); // 添加属性到 relay 节点
            }
            relayElement->LinkEndChild(relayNode); // 添加到 Relays 节点下
        }
    }

    std::string generateXML() const {
        TiXmlPrinter printer;
        xmlDoc.Accept(&printer);
        return printer.CStr();
    }

    ~XmlBuilder() {
        //delete rootElement;
    }
};



#endif //__CSROUTE_PUSH_CLIENT_MNG_H__


