#ifndef __CSMAIN_MSG_LIMIT_CONF_H__
#define __CSMAIN_MSG_LIMIT_CONF_H__

#include <string>
#include <vector>
#include <unordered_map>
#include "util_string.h"

class MessageRateLimitConf 
{
public:
    MessageRateLimitConf() : seconds_(0), requests_(0) {}
    MessageRateLimitConf(uint64_t seconds, uint64_t requests)
        : seconds_(seconds), requests_(requests) {}

    void AddMacList(const std::string& mac_string) {
        mac_list_.clear();  // 清空现有列表
        SplitString(mac_string, ";", mac_list_);
    }

    const std::vector<std::string>& MacList() const {
        return mac_list_;
    }
    
    bool HasMac(const std::string& mac) const {
        return std::find(mac_list_.begin(), mac_list_.end(), mac) != mac_list_.end();
    }

    uint64_t Seconds() const {
        return seconds_;
    }

    uint64_t Requests() const {
        return requests_;
    }

    // 没填写指定的mac时, 进行全局限流
    bool GlobalLimit() const {
        return mac_list_.size() == 0;
    }

private:
    uint64_t seconds_;
    uint64_t requests_;
    std::vector<std::string> mac_list_;
};

// key : msg id, value : rate limit info
using MessageRateLimitMap = std::unordered_map<std::string, MessageRateLimitConf>;

#endif
