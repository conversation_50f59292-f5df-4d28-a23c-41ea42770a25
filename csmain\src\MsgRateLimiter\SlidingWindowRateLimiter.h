#ifndef __CSMAIN_SLIDING_WINDOW_RATE_LIMITER_H__
#define __CSMAIN_SLIDING_WINDOW_RATE_LIMITER_H__

#include <deque>
#include "timeticker/akcs_time_util.h"
#include "MsgRateLimiterConf.h"

extern MessageRateLimitMap gstAKCSMsgRateLimitConf;

class SlidingWindowRateLimiter 
{
public:
    SlidingWindowRateLimiter() {}

    // 尝试获取许可，如果成功，则返回true，否则返回false
    bool TryAcquire(const std::string& msg_id, const std::string& mac) 
    {
        // 检查该消息ID是否存在于限流配置中
        if (gstAKCSMsgRateLimitConf.find(msg_id) == gstAKCSMsgRateLimitConf.end()) 
        {
            // 如果消息ID不存在于限流配置中，则直接返回true
            return true;
        }

        auto& rate_limit_conf = gstAKCSMsgRateLimitConf[msg_id];

        // 判断mac是否在配置限流列表中
        if (!rate_limit_conf.GlobalLimit() && !rate_limit_conf.HasMac(mac))
        {
            return true;
        }
        
        // 获取当前时间戳（单位为秒）
        uint64_t now = akcs_toolkit::getCurrentMillisecond() / 1000;

        // 获取该消息ID对应的请求历史
        auto& requests_history = requests_histories_[msg_id];
        
        AK_LOG_INFO << "SlidingWindowRateLimiter TryAcquire "<< ", mac = " << mac << ", msg_id = " << msg_id
					<< ", allowd request " << rate_limit_conf.Requests() << " times in " << rate_limit_conf.Seconds() << " seconds"
        			<< ", now requests count = " << requests_history.size();

        // 清理已过期的请求记录
        cleanOldRequests(msg_id, now);

        // 判断当前请求记录数是否小于允许的最大请求数
        if (requests_history.size() < (rate_limit_conf.Requests()))
        {
            // 如果小于最大请求数，将当前请求的时间戳加入记录中，并返回true
            requests_history.push_back(now);
            return true;
        }

        // 如果请求数已经达到了上限，则返回false
        return false;
    }

private:
    // 清理已经过期的请求记录，过期条件为：当前时间戳与最早的请求记录相差的时间大于配置的时间窗口
    void cleanOldRequests(const std::string& msg_id, uint64_t now) 
    {
        auto& requests_history = requests_histories_[msg_id];
            
        // 从前往后检查队列中的请求，移除所有超出时间窗口的请求
        while (!requests_history.empty() && (now - requests_history.front()) >= gstAKCSMsgRateLimitConf[msg_id].Seconds()) 
        {
            // 删除最早的请求记录
            requests_history.pop_front();
        }
    }
    
    // 存储每个消息ID对应的请求历史记录
    std::unordered_map<std::string, std::deque<uint64_t>> requests_histories_;
};

#endif
