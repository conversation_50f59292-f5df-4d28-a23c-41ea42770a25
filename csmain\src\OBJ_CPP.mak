##########################################################################################
## (C)Copyright 2012-2020 Ringslink .Ltd
##
##########################################################################################

OBJS:=$(patsubst %.cpp,$(MOD_OBJ_DIR)%.o,$(wildcard *.cpp))

ifeq ($(_LINUX), 1)
	export CPPFLAGS += -D_LINUX=1
endif
ifeq ($(_TESTMODE), 1)
	export CPPFLAGS += -D_TESTMODE=1
endif
ifeq ($(_XMLBROWSER_LOCAL), 1)
	export CPPFLAGS += -D_XMLBROWSER_LOCAL=1
endif

export CPPFLAGS += -DOEMID=$(OEMID) -DOEMID_EPIGY=129 -DOEMID_GREENACCESS=130

.PHONY: all clean

all: check $(OBJS)

check:
	chmod 777 -R $(MOD_DIR)

$(MOD_OBJ_DIR)%.o : %.cpp
	$(CXX) $(CPPFLAGS) $(GFLAG) $(OFLAG) -o $@  -c $<

clean: check
	-rm $(MOD_OBJ_DIR)*
