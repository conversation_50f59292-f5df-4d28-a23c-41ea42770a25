#include "NotifyMsgControl.h"
#include "AkcsWebMsgSt.h"
#include "OfficeHandleRouteMsg.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AK.Adapt.pb.h"
#include "AkcsMsgDef.h"
#include "AKUserMng.h"
#include "DeviceControl.h"
#include "NodeTimeZone.h"
#include "AKCSDao.h"
#include "RouteClient.h"
#include "csmainserver.h"
#include "MsgControl.h"
#include "AkcsOemDefine.h"
#include "XmlTagDefine.h"
#include "Dao.h"
#include "KeyControl.h"
#include "DevUpdateUserLog.h"
#include "EventFilterWriteFileImpl.h"


extern AccessServer* g_accSer_ptr;
extern akcs::CEventFilterInterface *g_event_filter;
COfficeGroupMsgMng* COfficeGroupMsgMng::group_msg_mng_instance_ = nullptr;
extern AKCS_CONF gstAKCSConf;

COfficeGroupMsgMng* COfficeGroupMsgMng::Instance()
{
    if (!group_msg_mng_instance_)
    {
        group_msg_mng_instance_ = new COfficeGroupMsgMng();
    }
    return group_msg_mng_instance_;
}
int COfficeGroupMsgMng::AddConnToKeySend(std::vector<evpp::TCPConnPtr> &dev_conns)
{
    for (const auto& conn : dev_conns)
    {
        if (g_accSer_ptr->IsTCPConnIsAPP(conn))
        {
            GetMsgControlInstance()->OnSendDevListChangeMsg(conn);//如果是app,只需要通知联系人变更
            continue;
        }
        DEVICE_SETTING DeviceSetting = {0};
        if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &DeviceSetting) != 0)
        {
            AK_LOG_WARN << "AddConnToKeySend The tcp conn of  dev not exist.";
            continue;
        }

        KEY_SEND keySend;
        memset(keySend.community, 0, sizeof(keySend.community));
        memset(keySend.device_node, 0, sizeof(keySend.device_node));
        Snprintf(keySend.mac, sizeof(keySend.mac) / sizeof(TCHAR), DeviceSetting.mac);
        Snprintf(keySend.device_node, sizeof(keySend.device_node) / sizeof(TCHAR), DeviceSetting.device_node);
        keySend.weak_conn = conn;
        //需要设备过来下载的消息需要放入数据库发送
        //added by chenyc,先保存到数据库中,另外一边会有一个定时器来刷数据，并发送给客户端,该表保留的是设备的外网IP
        if (GetKeyControlInstance()->AddOfficeKeySend(keySend) < 0)
        {
            AK_LOG_WARN << "AddKeySend failed.";
            return -1;
        }
    }           
	return 0;
}

void COfficeGroupMsgMng::HandleConfigFileChangeReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    uint32_t mng_id = msg.mng_id();
    uint32_t unit_id = msg.unit_id();
    uint32_t notify_type = msg.notify_type();
    std::string node = msg.node();
    std::string mac = msg.mac();
    
    char file_change_type_str[][32] = {
        "",
        "Node",
        "Mac",
        "AllCommunity",
        "AllUnit",
        "PubDev",
        "UnitDev",
        "NodeOnly"
    };
    
    std::string change_type = std::to_string(notify_type);
    if (notify_type <=7 && notify_type > 0)
    {
        change_type = file_change_type_str[notify_type];
    }
    
    AK_LOG_INFO << "[csadapt] config file change. node: " << node 
        << " mac: " << mac 
        << " notifytype: " << change_type
        << " mngid: " << mng_id 
        << " unitid: " << unit_id;

    switch(notify_type)
    {
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_NODE:
        {
            std::vector<evpp::TCPConnPtr> dev_conns1;
            g_accSer_ptr->GetDevListByNode(node, dev_conns1);
            AddConnToKeySend(dev_conns1); //包括设备
            
            //单元
            std::vector<evpp::TCPConnPtr> dev_conns2;
            if (unit_id <= 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The unit_id is = " << unit_id;
                return;
            }
            g_accSer_ptr->GetDevListCommunityUnitPublicDev(mng_id, unit_id, dev_conns2);
            AddConnToKeySend(dev_conns2); 

            //公共
            std::vector<evpp::TCPConnPtr> dev_conns3;
            if (mng_id <= 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The mng_Id is = " << mng_id;
                return;
            }
            g_accSer_ptr->GetDevListCommunityPublicDev(mng_id, dev_conns3);
            AddConnToKeySend(dev_conns3);   

            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_MAC:
        {
            evpp::TCPConnPtr conn;
            if (g_accSer_ptr->GetDevConnByMac(mac, conn) != 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The tcp conn of mac [" << mac << "] is offline.";
                return;
            }
            std::vector<evpp::TCPConnPtr> dev_conns;
            dev_conns.push_back(conn);
            AddConnToKeySend(dev_conns);       
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_COMMUNITY://整个office更新
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (mng_id <= 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The mng_id is = " << mng_id;
                return;
            }
            g_accSer_ptr->GetDevListCommunityAllDevAndAPP(mng_id, dev_conns);
            AddConnToKeySend(dev_conns);            
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_UNIT://整个单元更新
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (unit_id <= 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The unit_id is = " << unit_id;
                return;
            }
            g_accSer_ptr->GetDevListCommunityAllUnitDevAndAPP(mng_id, unit_id, dev_conns);
            g_accSer_ptr->GetDevListCommunityPublicDev(mng_id, dev_conns);
            AddConnToKeySend(dev_conns);
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_PUB_DEV://公共设备
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (mng_id <= 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The mng_id is = " << mng_id;
                return;
            }
            g_accSer_ptr->GetDevListCommunityPublicDev(mng_id, dev_conns);
            AddConnToKeySend(dev_conns);           
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_UNIT_DEV://单元公共设备
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            if (unit_id <= 0)
            {
                AK_LOG_WARN << "HandleConfigFileChangeReq The unit_id is = " << unit_id;
                return;
            }
            g_accSer_ptr->GetDevListCommunityUnitPublicDev(mng_id, unit_id, dev_conns);
            AddConnToKeySend(dev_conns);           
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_NODE_ONLY://只需要通知节点，不需要通知unit pub
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            g_accSer_ptr->GetDevListByNode(node, dev_conns);
            AddConnToKeySend(dev_conns);        
            break;
        }
        case CONFIG_FILE_CHANGE_NOTIFY_TYPE_ALL_PUB://所有公共设备
        {
            std::vector<evpp::TCPConnPtr> dev_conns;
            g_accSer_ptr->GetDevListCommunityAllPubDev(mng_id, dev_conns);
            AddConnToKeySend(dev_conns);        
            break;
        }         
    }
    return;
}


