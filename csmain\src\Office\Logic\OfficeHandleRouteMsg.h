#ifndef __CSMAIN_OFFICE_GROUP_MSG_MNG_H__
#define __CSMAIN_OFFICE_GROUP_MSG_MNG_H__

#include <boost/noncopyable.hpp>
#include "AkcsPduBase.h"

//P2P:指的是从csroute->csmain是单播的
//Group:指的是从csroute->csmain是广播的
class COfficeGroupMsgMng : public boost::noncopyable
{
public:
    COfficeGroupMsgMng() {}
    virtual ~COfficeGroupMsgMng() {}
    static COfficeGroupMsgMng* Instance();
    //下面的消息来自route对csmain的转发
    void HandleConfigFileChangeReq(const std::unique_ptr<CAkcsPdu>& pdu);
private:
    int AddConnToKeySend(std::vector<evpp::TCPConnPtr> &dev_conns);
    static COfficeGroupMsgMng* group_msg_mng_instance_;

};

#endif /* __CSMAIN_OFFICE_GROUP_MSG_MNG_H__ */

