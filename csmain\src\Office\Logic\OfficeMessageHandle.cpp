#include "stdafx.h"
#include <functional>
#include "OfficeMessageHandle.h"
#include "csmainserver.h"
#include "util.h"
#include "PersonnalAlarm.h"
#include "NodeTimeZone.h"
#include "AK.Linker.pb.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AkcsPduBase.h"
#include "CachePool.h"
#include "AppPushToken.h"
#include "rpc_client.h"
#include "session_rpc_client.h"
#include "RouteMqProduce.h"
#include "RouteClient.h"
#include "PushClient.h"
#include "AlarmControl.h"
#include "DeviceSetting.h"
#include "MsgHandle.h"
#include "CsmainAES256.h"
//#include "RecordActLog.h"
//#include "RecordOfficeLog.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"
#include "Md5.h"
#include "PersonalAccount.h"
#include "AKDevMng.h"
#include "NotifyMsgControl.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/CommunityInfo.h"
#include "SnowFlakeGid.h"
#include "DevUpdateUserLog.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "AkcsOemDefine.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AccessGroupDB.h"
#include "PersonnalTmpKey.h"
#include "dbinterface/Message.h"
#include "dbinterface/Sip.h"
#include "dbinterface/Account.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/PersonalMotion.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/ProjectInfo.h"
#include "Main2ResidHandle.h"
#include "DclientMsgDef.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "AkcsCommonDef.h"
#include "SafeCacheConn.h"
#include "MsgIdToMsgName.h"

#include "dbinterface/new-office/OfficeCompany.h"
#include "doorlog/RecordActLog.h"

extern AccessServer* g_accSer_ptr;
extern SmRpcClient* g_sm_client_ptr;
extern std::string g_logic_srv_id;
extern RouteMQProduce* g_nsq_producer;
extern AKCS_CONF gstAKCSConf; //全局配置信息
extern std::map<string, AKCS_DST> g_time_zone_DST;
extern const char* g_redis_db_userdetail;
extern LOG_DELIVERY gstAKCSLogDelivery;

OfficeMessageHandle* OfficeMessageHandle::office_message_instance_ = nullptr;

OfficeMessageHandle* OfficeMessageHandle::Instance()
{
    if (!office_message_instance_)
    {
        office_message_instance_ = new OfficeMessageHandle();
    }
    return office_message_instance_;
}

OfficeMessageHandle::OfficeMessageHandle()
{

}

int OfficeMessageHandle::OnSocketMsg(const evpp::TCPConnPtr& conn, const std::string& message)
{
    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)message.data();
    //判断MAGIC

    //判断CRC

    //判断类型
    //TODO,added by chenyc,2020-06-15,现在云端跟设备端都是小端序,所以默认两边都没有处理网络序的问题,后面设备如果架构切换了,需要注意
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;
    int msg_version = (normal_msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(normal_msg->data_size);
    //int crc= normal_msg->crc;
    //设置该条session是否时加密的,即同一台设备在不同的session也可能由不同的加密形式.
    evpp::Any any_tmp(msg_version);
    conn->set_context(EVPP_CONN_ANY_CONTEXT_DEV_ENCRYPT_VER, any_tmp);
    std::string ip;
    int port = 0;
    AkParseAddr(conn->remote_addr(), ip, port); //ip对于ipv6，则左右带有[]
    if (0x10B != message_id) //不是心跳包才打印
    {
        AK_LOG_INFO << "tcp client msg from (" << ip << ":" << port << ", ID = 0x" << hex << message_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id);
    }

    switch (message_id)
    {
       /* Begin added by chenyc,2017-05-24,云平台接入app开发 */
       //设备主动向平台推送告警处理的消息 //告警字符串加密
       // case MSG_FROM_DEVICE_PUT_ALARM_DEAL:
       // {
       //      OnPutAlarmDealMsg(normal_msg, conn);
       // }
       // break;
       //平台接受设备端上传的motion alert的消息,mac加密
       case MSG_FROM_DEVICE_MOTION_ALERT:
       {
            OnReportMotionAlert(normal_msg, conn);
       }
       break;
       //app上报对设备进行布防、撤防的信令
       case MSG_FROM_APP_HANDLE_DEV_ARMING:
       {
            OnHandleDevArming(normal_msg, conn);
       }
       break;
       //设备（室内机）上报当前布防、撤防的状态给平台
       case MSG_FROM_DEVICE_REPORT_ARMING_STATUS:
       {
            OnReportArmingStatus(normal_msg, conn);
       }
       break;
       //app请求对某个设备的rtsp流进行截图
       case MSG_FROM_APP_REQUEST_CAPTURE:
       {
            OnRequestCapture(normal_msg, conn);
       }
       break;
       //app通知平台logout,完成app端退出的时候,通过tcp长连接模块通知平台,平台剔除掉该app的端外推送
       case MSG_FROM_APP_REPORT_LOGOUT:
       {
            OnReportLogOut(normal_msg, conn, ip.c_str());
       }
       break;
       /*
       case MSG_FROM_DEVICE_REPORT_CALL_CAPTURE:
       {
            OnCallCaptureReport(normal_msg, conn);
       }
       break;
       */
       case MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG:
       {
            OnMngDevReportMsg(normal_msg, conn);
       }
       break;
       case MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER:
       {
            OnRespondSensorTrigger(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_REPORT_VISITOR_MSG:
       {
           GetMsgControlInstance()->OnDevReportVisitorInfo(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG:
       {
           OnDevReportVisitorAuth(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_REQUEST_OSS_STS:
       {
           GetMsgControlInstance()->OnDevRequestOssSts(normal_msg, conn);
       }
       break;
       // case MSG_FROM_DEVICE_REQUEST_OPENDOOR:
       // {
       //      OnDevRequestOpen(normal_msg, conn);
       // }
       break;
       case MSG_FROM_DEVICE_SEND_DELIVERY_MSG:
       {
            OnDevSendDelivery(normal_msg, conn);
       }
       break;
       case MSG_FROM_APP_REQUEST_CHANGE_RELAY:
       {
            OnDevReqChangeRelay(normal_msg, conn);
       }
       break;

       case MSG_FROM_DEVICE_REQUEST_ACINFO:
       {
            OnRequestUserInfo(normal_msg, conn);
       }
       break;
       /*
       case MSG_FROM_DEVICE_REPORT_VOICE_MSG:
       {
           OnDeviceReportVoiceMsg(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST:
       {
           OnDeviceRequestVoiceMsgList(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL:
       {
           OnDeviceRequestVoiceMsgUrl(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG:
       {
           OnDeviceRequestDelVoiceMsg(normal_msg, conn);
       }
       break;
       //设备（室外机）上报动作消息（即Logs：呼叫，输入密码，卡开门等开门动作）给平台
       case MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS:
       {
            OnReportActLog(normal_msg, conn);
       }
       break;
       case MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT:
       {
            OnFlowOutOfLimit(normal_msg, conn);
       }
       break;
       */
       default:
       {
           //如果conn已经上报过状态，则将消息转移到csoffice中处理,否则就是非法消息
           bool conn_reported_status = evpp::any_cast<bool>(conn->context(EVPP_CONN_ANY_CONTEXT_REPORT_STATUS_INDEX));
           if(conn_reported_status)
           {
               csmain::DeviceType conn_type = evpp::any_cast<csmain::DeviceType>(conn->context(EVPP_CONN_ANY_CONTEXT_CONN_TYPE_INDEX));
               std::string conn_client = evpp::any_cast<std::string>(conn->context(EVPP_CONN_ANY_CONTEXT_CLIENT_INDEX));
               bool ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
               //将消息透传到后端的csresid服务
               Main2ResidMsgHandle::Instance()->OfficeSend(conn_client, conn_type, ipv6, (char *)normal_msg, SOCKET_MSG_NORMAL_HEADER_SIZE + data_size);
               break;
           }
           
           AK_LOG_WARN << "tcp client msg from ("<< ip << ":" << port << ") has not been reported status,drop the msg";
           return -1;
       }

   }

   return 1;
}

int OfficeMessageHandle::GetLogProjectUUID(const DEVICE_SETTING& device_setting, std::string& project_uuid)
{
    if (device_setting.init_status == 0)
    {
        ProjectInfo log_project;
        log_project.GetLogCaptureProjectUUID(device_setting, project_uuid);
    }
    else
    {
        if (device_setting.is_personal)
        {
            project_uuid = device_setting.node_uuid;
        }
        else
        {
            project_uuid = device_setting.project_uuid;
        }
    }

    return 0;
}

int OfficeMessageHandle::ProcessCommunityAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const DEVICE_SETTING&  deviceSetting, const evpp::Any& personnalAppSetting, const int type)
{
    if (normal_msg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }
    SOCKET_MSG_ALARM_DEAL alarm_deal_info;
    memset(&alarm_deal_info, 0, sizeof(alarm_deal_info));
    if (type == csmain::OFFICE_APP)
    {
        const SOCKET_MSG_PERSONNAL_APP_NODE& stApp(boost::any_cast<const SOCKET_MSG_PERSONNAL_APP_NODE&>(personnalAppSetting));
        Snprintf(alarm_deal_info.area_node, sizeof(alarm_deal_info.area_node), stApp.node);
    }
    else
    {
        Snprintf(alarm_deal_info.area_node, sizeof(alarm_deal_info.area_node), deviceSetting.device_node);
    }
    int msg_version = (normal_msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = NTOHS(normal_msg->data_size);
    char* payload = (char*)normal_msg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        AesDecryptByMac(payload, payload, AES_KEY_DEFAULT_MAC, data_size);
    }

    if (GetMsgHandleInstance()->ParseAlarmDealMsg(payload, &alarm_deal_info) < 0)
    {
        AK_LOG_WARN << "Parse personnal AlarmDealMsg failed.";
        return -1;
    }

    ::snprintf(alarm_deal_info.device_name, sizeof(alarm_deal_info.device_name), "%s", deviceSetting.location);
    std::string NodeTime = getNodeCurrentTimeString(deviceSetting.device_node);
    ::snprintf(alarm_deal_info.time, sizeof(alarm_deal_info.time), "%s", NodeTime.c_str());
    AK_LOG_INFO << deviceSetting.mac << " put alarm deal. alarmid:" << alarm_deal_info.alarm_id << " user:" << alarm_deal_info.user;
    if (DaoDealAlarmStatus(alarm_deal_info) != 0)
    {
        AK_LOG_WARN << "Deal personnal AlarmStatus failed.";
        return -1;
    }
    SOCKET_MSG_ALARM_DEAL_OFFLINE stCommuityAlarmInfo;
    if (DaoCommGetAlarmInfoById(alarm_deal_info.alarm_id, stCommuityAlarmInfo) != 0)
    {
        AK_LOG_WARN << "get alarm info failed by alarm id:" << alarm_deal_info.alarm_id;
    }
    alarm_deal_info.manager_account_id = stCommuityAlarmInfo.manager_account_id;
    //通过nsq,通知csroute进行消息广播
    AK::Server::GroupCommAlarmDealMsg group_comm_alarm_deal_msg;
    group_comm_alarm_deal_msg.set_node(alarm_deal_info.area_node);
    group_comm_alarm_deal_msg.set_alarm_id(alarm_deal_info.alarm_id);
    group_comm_alarm_deal_msg.set_deal_user(alarm_deal_info.user);
    group_comm_alarm_deal_msg.set_deal_result(alarm_deal_info.result);
    group_comm_alarm_deal_msg.set_deal_type(alarm_deal_info.type);
    group_comm_alarm_deal_msg.set_deal_time(alarm_deal_info.time);
    group_comm_alarm_deal_msg.set_mac(stCommuityAlarmInfo.mac);
    group_comm_alarm_deal_msg.set_dev_location(stCommuityAlarmInfo.device_location);
    group_comm_alarm_deal_msg.set_alarm_type(stCommuityAlarmInfo.alarm_type);
    group_comm_alarm_deal_msg.set_mng_account_id(stCommuityAlarmInfo.manager_account_id);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&group_comm_alarm_deal_msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    pdu2.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.nsq_topic);

    return 0;
}

int OfficeMessageHandle::OnPutAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    //新版本APP已经是走rest了
    if (NULL == normal_msg)
    {
        return -1;
    }
    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    evpp::Any personnalAppSetting;
    int type;
    //TODO: chenzhx 所有的app 都是personnalAppSetting 和PERSONNAL_APP，并没有和社区区分
    //TODO chenzhx ******** app处理告警走rest
    if (g_accSer_ptr->GetDevSetDiffFromConnList(conn, &deviceSetting, personnalAppSetting, type) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed,please make sure the client device had registered.";
        return -1;
    }

    // 推送deal alarm 给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (strlen(deviceSetting.node_uuid) > 0  && 0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(deviceSetting.node_uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        GetMsgControlInstance()->PostAlexaChangeStatus(deviceSetting.mac, traceid);
        AK_LOG_INFO << "alexa device deal alarm notify web , mac :" << deviceSetting.mac << ", traceid : " << traceid;
    }

    if (type == csmain::OFFICE_DEV || type == csmain::OFFICE_APP)
    {
        if (ProcessCommunityAlarmDealMsg(normal_msg, deviceSetting, personnalAppSetting, type) != 0)
        {
            return -1;
        }
        return 0;
    }

    return 0;
}


int OfficeMessageHandle::ProcessPerMotionAlertMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_MOTION_ALERT& motionMsg, const DEVICE_SETTING& deviceSetting)
{
    //写入数据库
    PERSONNAL_CAPTURE personnalCapture;
    memset(&personnalCapture, 0, sizeof(personnalCapture));
    Snprintf(personnalCapture.mac, sizeof(personnalCapture.mac), deviceSetting.mac);
    Snprintf(personnalCapture.dev_uuid, sizeof(personnalCapture.dev_uuid), deviceSetting.uuid);
    Snprintf(personnalCapture.picture_name, sizeof(personnalCapture.picture_name), motionMsg.picture_name);
    Snprintf(personnalCapture.capture_time, sizeof(personnalCapture.capture_time), GetCurTime().GetBuffer());
    Snprintf(personnalCapture.account, sizeof(personnalCapture.account), deviceSetting.device_node);
    Snprintf(personnalCapture.location, sizeof(personnalCapture.location), deviceSetting.location);
    Snprintf(personnalCapture.sip_account, sizeof(personnalCapture.sip_account), deviceSetting.sip_account);
    personnalCapture.manager_type = deviceSetting.is_personal;
    personnalCapture.manager_id = deviceSetting.manager_account_id;
    personnalCapture.device_type = deviceSetting.is_public;

    
    if (RecordActLog::GetInstance().RewriteOfficeMotionProjectInfo(personnalCapture, deviceSetting.project_uuid) != 0 ) 
    {
        AK_LOG_WARN << "RewriteMotionProjectInfo error mac:" << deviceSetting.mac;
        return -1;
    }

    
    if (dbinterface::PersonalMotion::AddPersonalMotion(personnalCapture, gstAKCSLogDelivery.personal_motion_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }

    SOCKET_MSG_MOTION_ALERT_SEND recvMotionAlertMsg;
    memset(&recvMotionAlertMsg, 0, sizeof(recvMotionAlertMsg));
    Snprintf(recvMotionAlertMsg.protocal, sizeof(recvMotionAlertMsg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(recvMotionAlertMsg.mac, sizeof(recvMotionAlertMsg.mac), deviceSetting.mac);
    Snprintf(recvMotionAlertMsg.node, sizeof(recvMotionAlertMsg.node), deviceSetting.device_node);
    Snprintf(recvMotionAlertMsg.location, sizeof(recvMotionAlertMsg.location), deviceSetting.location);

    //add chenzhx v4.0
    Snprintf(recvMotionAlertMsg.sip_account, sizeof(recvMotionAlertMsg.sip_account), deviceSetting.sip_account);
    std::string nodetime = getNodeCurrentTimeString(recvMotionAlertMsg.node);//转换为主账号时区对应的时间
    Snprintf(recvMotionAlertMsg.capture_time, sizeof(recvMotionAlertMsg.capture_time), nodetime.c_str());
    recvMotionAlertMsg.id = personnalCapture.id;

    //将motion alert通知消息压入队列中
    //CPersonnalMotionNotifyMsg cNotifyMsg(recvMotionAlertMsg);
    //GetNotifyMsgControlInstance()->AddPerMotionNotifyMsg(cNotifyMsg);
    //通过nsq,通知csroute进行消息广播

    //added by chenyc,2020-08-04,如果deviceSetting.szDeviceNode为空，则表示是公共设备,不需要推动motion消息,直接退出即可
    if (strlen(deviceSetting.device_node) == 0)
    {
        return 0;
    }

    AK::Server::GroupPerMotionMsg msg;
    msg.set_mac(recvMotionAlertMsg.mac);
    msg.set_node(recvMotionAlertMsg.node);
    msg.set_dev_location(recvMotionAlertMsg.location);
    msg.set_motion_time(recvMotionAlertMsg.capture_time);
    msg.set_sip_account(recvMotionAlertMsg.sip_account);
    msg.set_id(recvMotionAlertMsg.id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_OFFICE_PER_MOTION_REQ);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int OfficeMessageHandle::OnReportMotionAlert(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetLocalDeviceSetting failed.";
        return -1;
    }
    if (deviceSetting.device_type == csmain::COMMUNITY_NONE)
    {
        AK_LOG_WARN << "The device has never report status msg, we can not make sure which type of it.";
        return -1;
    }

    SOCKET_MSG_MOTION_ALERT stMotionAlertMsg;
    memset(&stMotionAlertMsg, 0, sizeof(stMotionAlertMsg));
    if (GetMsgControlInstance()->ParseMotionAlertMsg(normal_msg, stMotionAlertMsg, deviceSetting.mac) < 0)
    {
        AK_LOG_WARN << "ProcessMotionAlertMsg failed.";
        return -1;
    }
    AK_LOG_INFO << deviceSetting.mac << " motion alert. pic:" << stMotionAlertMsg.picture_name;

    //限流判断
    char limiting_key[128];
    snprintf(limiting_key, sizeof(limiting_key), "%s_%lu", gstAKCSConf.csmain_outer_ip, conn->id());
    if(GetMsgControlInstance()->CheckMotionLimiting(limiting_key))
    {
        GetMsgControlInstance()->AddMotionPicLimiting(stMotionAlertMsg.picture_name);
        AK_LOG_INFO << "motion limiting. limiting_key: " << limiting_key << " timeout:" << gstAKCSConf.limiting_timeout;
        return -1;
    }

    //如果是个人终端设备
    if (ProcessPerMotionAlertMsg(conn, stMotionAlertMsg, deviceSetting) != 0)
    {
        AK_LOG_WARN << "processPerMotionAlertMsg failed.";
        return -1;
    }

    return 0;
}

int OfficeMessageHandle::OnHandleDevArming(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        AK_LOG_WARN << "OnHandleDevArming param null.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    if (GetMsgControlInstance()->ParseReqArmingMsg(normal_msg, stArmingMsg) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.";
        return -1;
    }
    DevicePtr dev;
    if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
    {
        AK_LOG_WARN << "GetClientFromConn failed.";
        return -1;
    }
    if (!dev->IsApp())
    {
        AK_LOG_WARN << "the tcp conn of app is not personnal uid. close connect!";
        conn->Close();
        return -1;
    }
    std::string uid;
    if (dev->GetPerUid(uid) != 0)
    {
        AK_LOG_WARN << "Not App, so is error when geting UID. Disconnect make it reconnect";
        conn->Close();
        return -1;
    }

    AK_LOG_INFO << uid  << " app handle arming. mode:" << stArmingMsg.mode << " mac:" << stArmingMsg.mac;

    //通过nsq,通知csroute进行消息广播
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(stArmingMsg.mac);
    msg.set_action(stArmingMsg.szAction);
    msg.set_uid(uid);
    msg.set_mode(stArmingMsg.mode);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int OfficeMessageHandle::OnReportArmingStatus(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_DEV_ARMING stArmingMsg;
    memset(&stArmingMsg, 0, sizeof(stArmingMsg));
    if (GetMsgControlInstance()->ParseReportArmingMsg(normal_msg, stArmingMsg, device_setting.mac) < 0)
    {
        AK_LOG_WARN << "ParseReqArmingMsg failed.";
        return -1;
    }

    int oem  = device_setting.oem_id;
    if (oem == OEMID_ROBERT || oem == OEMID_ROBERT2 )
    {
        if (stArmingMsg.resp_action == REPORT_ARMING_ACTION_TYPE_REBOOT)
        {
            AK_LOG_INFO << device_setting.mac << " device report arming. mode:" << stArmingMsg.mode << " but action type is reboot.ignore!";
            return -1;
        }
    }

    AK_LOG_INFO << device_setting.mac << " office device report arming. mode:" << stArmingMsg.mode;
    Snprintf(stArmingMsg.mac, sizeof(stArmingMsg.mac), device_setting.mac);

    //更新设备arming状态
    GetDeviceControlInstance()->UpdateDeviceArming(device_setting.mac, stArmingMsg.mode, device_setting.is_personal);

    //判断是否是旧版本平台要求上报状态的
    if (!strncmp(stArmingMsg.uid, "OldReq", 6))
    {
        return 0;
    }

    // 推送arming给Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (strlen(device_setting.node_uuid) > 0 && 0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(device_setting.node_uuid, alexa_token_info))
    {        
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        GetMsgControlInstance()->PostAlexaChangeStatus(device_setting.mac, traceid);
        AK_LOG_INFO << "alexa device arming notify web , mac :" << device_setting.mac << ", traceid : " << traceid;
    }

    std::string main_site;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(stArmingMsg.uid, main_site);

    //通过nsq,通知csroute进行消息广播
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(stArmingMsg.mac);
    msg.set_mode(stArmingMsg.mode);
    msg.set_uid(stArmingMsg.uid);
    msg.set_oem(oem);
    msg.set_resp_action(stArmingMsg.resp_action);
    msg.set_node(device_setting.device_node);
    msg.set_main_site(main_site);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    return 0;
}

int OfficeMessageHandle::OnRequestCapture(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }
    uint32_t role = 0;
    std::string node;
    std::string username;
    std::string personal_account_uuid;
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_REQ_CAPTURE stCaptureMsg;
    memset(&stCaptureMsg, 0, sizeof(stCaptureMsg));
    std::string uid;
    if (GetMsgControlInstance()->ParseReqCaptureMsg(normal_msg, stCaptureMsg) < 0)
    {
        AK_LOG_WARN << "ParseReqCaptureMsg failed.";
        return -1;
    }
    if (strlen(stCaptureMsg.mac) == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return -1;
    }
    {
        DevicePtr dev;
        if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
        {
            AK_LOG_WARN << "GetClientFromConn failed.";
            return -1;
        }

        //6.6新增site字段，来确定是截图哪个房间的
        uid = stCaptureMsg.site;
        if(uid.size() == 0)
        {
            if (dev->GetPerUid(uid) != 0)
            {
                AK_LOG_WARN << "Not App, so is error when geting UID. Disconnect make it reconnect";
                conn->Close();
                return -1;
            }
        }

        if (dev->GetPerNodeBySite(node, uid) != 0)
        {
            AK_LOG_WARN << "get node of app fialed";
            return -1;
        }

        // 新办公场景，先获取PersonAccountUUID，后面要查询CompanyUUID
        if (dev->GetUserRole(role) == 0 && IsOfficeNewRole(role) == true)
        {
            // 查询uuid失败 || 查询出来的uuid是空字符串
            if ( dev->GetPersonalAccountUUID(personal_account_uuid) != 0 || personal_account_uuid.empty())
            {
                AK_LOG_WARN << "Get personal account uuid failed: uid=" << uid;
                return -1;
            }
        }

        dev->GetAppUserNameBySite(username, uid);//不用判断，上面已经有是否是app的判断
        
        AK_LOG_INFO << uid.c_str() << " app request capture. Mac:" << stCaptureMsg.mac;
    }
    time_t timer;
    timer = time(nullptr);
    char time_sec[16] = {0};
    ::snprintf(time_sec, 16, "%ld", timer);

    /*新增图片名称带校验码，上传时csstorage做校验*/
    std::string temp_string = "ak_ftp:";
    temp_string += stCaptureMsg.mac;
    temp_string += ":";
    temp_string += time_sec;
    std::string picture_code = akuvox_encrypt::MD5(temp_string).toStr();

    std::string pic_name = stCaptureMsg.mac;
    pic_name += "-";
    pic_name += time_sec;
    pic_name += "_0_APP_";
    pic_name += picture_code;
    pic_name += ".jpg";

    std::string flow_uuid = GetFlowUUID(stCaptureMsg.mac, stCaptureMsg.camera, stCaptureMsg.stream_id);

    UIPC_MSG_CAPTURE_RTSP stCaptureRtsp;
    ::memset(&stCaptureRtsp, 0, sizeof(stCaptureRtsp));
    Snprintf(stCaptureRtsp.mac, sizeof(stCaptureRtsp.mac), stCaptureMsg.mac);
    Snprintf(stCaptureRtsp.picture_name, sizeof(stCaptureRtsp.picture_name), pic_name.c_str());
    Snprintf(stCaptureRtsp.node, sizeof(stCaptureRtsp.node), node.c_str());
    Snprintf(stCaptureRtsp.username, sizeof(stCaptureRtsp.username), username.c_str());
    Snprintf(stCaptureRtsp.flow_uuid, sizeof(stCaptureRtsp.flow_uuid), flow_uuid.c_str());

    CNodeInfo cNodeCfg(node);
    Snprintf(stCaptureRtsp.room_num, sizeof(stCaptureRtsp.room_num), cNodeCfg.getRoomNumber().c_str());

    stCaptureRtsp.capture_type = CMsgControl::ActOpenDoorType::CLOUD_APP_MANUAL;

    /*设备和app不一定连接同一个csmain,必须查数据库*/

    ResidentDev dev;
    if (GetDeviceSettingInstance()->GetDevInfoByMac(stCaptureRtsp.mac, dev) != 0)
    {
        AK_LOG_WARN << "OnRequestCapture GetDevInfoByMac error mac:" << dev.mac;
        return -1;
    }
    stCaptureRtsp.manager_type = dev.is_personal;
    stCaptureRtsp.device_type = dev.is_public;
    stCaptureRtsp.manager_id = dev.project_mng_id;
    Snprintf(stCaptureRtsp.dev_uuid, sizeof(stCaptureRtsp.dev_uuid), dev.uuid);
    Snprintf(stCaptureRtsp.location, sizeof(stCaptureRtsp.location), dev.location);
    Snprintf(stCaptureRtsp.sip_account, sizeof(stCaptureRtsp.sip_account), dev.sip);

    // 新办公项目，写入company_uuid
    if (IsOfficeNewRole(role) == true)
    {
        std::string office_company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(personal_account_uuid, role);
        Snprintf(stCaptureRtsp.company_uuid, sizeof(stCaptureRtsp.company_uuid), office_company_uuid.c_str());
    }
    
    if (RecordActLog::GetInstance().RewriteRtspCaptureProjectInfo(stCaptureRtsp, dev) != 0 ) 
    {
        AK_LOG_WARN << "RewriteRtspCaptureProjectInfo error mac:" << dev.mac;
        return -1;
    }

    //插入数据库即可
    if (dbinterface::PersonalCapture::AddUidReqCapture(stCaptureRtsp, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal app request capture failed.";
        return -1;
    }
    AK_LOG_INFO << "[Capture] App request capture, uid:" << uid << " name:" << username << " mac:" << stCaptureRtsp.mac;
    //发送截图信令给vrtsp
    //GetIPCUnixControlInstance()->IPCSendMsg2VRtsp(&stCaptureRtsp, sizeof(stCaptureRtsp));
    //通过nsq,通知csroute进行消息转发
    AK::Route::RtspCaptureReq msg;
    msg.set_mac(stCaptureMsg.mac);
    msg.set_pic_name(pic_name);
    msg.set_node(node);
    msg.set_user_name(username);
    msg.set_flow_uuid(flow_uuid);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_RTSP_CAPTURE_REQ);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int OfficeMessageHandle::OnReportLogOut(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn, const char* remote_ip)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DevicePtr dev;
    if (g_accSer_ptr->GetClientFromConn(conn, dev) != 0)
    {
        AK_LOG_WARN << "GetClientFromConn failed.";
        return -1;
    }
    if (!dev->IsApp())
    {
        AK_LOG_WARN << "the tcp conn of app is not personnal uid. close connect!";
        conn->Close();
        return -1;
    }

    //设置主站点处于logout状态
    std::string main_site;
    if (dev->GetPerMainSiteUid(main_site) != 0)
    {
        AK_LOG_WARN << "Not App, so is error when geting UID.";
    }
    AK_LOG_INFO << main_site.c_str() << " app logout.";
    CAkUserManager::GetInstance()->SetLogOutStatus(main_site);

    std::string user_info_uuid;
    dev->GetUserInfoUUID(user_info_uuid);
    PersonalAccountNodeInfoMap nodes;
    dbinterface::ResidentPersonalAccount::GetNodesByUserInfoUUID(user_info_uuid, nodes);
    CAkUserManager::GetInstance()->RemoveAkUserByNodeId(nodes, main_site);

    // 通知csoffice,app下线
    g_accSer_ptr->NotifyConnInfoMsg(dev, CONN_INFO_TYPE::LOGOUT);

    // 发送ack给app,通知logout成功
    GetDeviceControlInstance()->SendAppLogoutAckMsg(conn);
    
    conn->Close();
    return 0;
}

int OfficeMessageHandle::OnDevSendDelivery(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    OfficeInfo  office_info(deviceSetting.manager_account_id);
    if (!deviceSetting.is_personal
        && office_info.CheckFeature(OfficeInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_DELIVERY)
        && !office_info.IsExpire())
    {
        SOCKET_MSG_DEV_SEND_DELIVERY* delivery_msg = NULL;
        uint32_t data_size = NTOHS(normal_msg->data_size);
        char* payload = (char*)normal_msg->data;
        AesDecryptByMac(payload, payload, deviceSetting.mac, data_size);
        if (GetMsgHandleInstance()->ParseSendDelivery(payload, &delivery_msg) < 0)
        {
            AK_LOG_WARN << "ParseSendDelivery failed.";
            return -1;
        }

        SOCKET_MSG_DEV_SEND_DELIVERY* tmp_msg = delivery_msg;
        while (tmp_msg != NULL)
        {
            AK::Server::P2PMainSendDelivery msg;
            msg.set_account(tmp_msg->account);
            msg.set_amount(tmp_msg->amount);
            msg.set_type(CPerTextNotifyMsg::DELIVERY_MSG);
            CAkcsPdu pdu;
            pdu.SetMsgBody(&msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ);
            pdu.SetProjectType(project::OFFICE);
            pdu.SetSeqNum(0);
            g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
            tmp_msg = tmp_msg->next;
        }

        SOCKET_MSG_DEV_SEND_DELIVERY* cur_msg = NULL;
        while (NULL != delivery_msg)    //结构体链表 内存释放
        {
            cur_msg = delivery_msg;
            delivery_msg = delivery_msg->next;
            delete cur_msg;
        }
    }
    else
    {
        AK_LOG_INFO << "MngAccount:" << deviceSetting.manager_account_id << " Deliverysend check result is 0. "
            << "Don't send delivery msg.";
    }
    return 0;
}

/*
int OfficeMessageHandle::OnCallCaptureReport(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_CALL_CAPTURE stCallCaptureMsg;
    memset(&stCallCaptureMsg, 0, sizeof(stCallCaptureMsg));
    if (GetMsgControlInstance()->ParseCallCaptureMsg(normal_msg, device_setting.mac, stCallCaptureMsg) < 0)
    {
        AK_LOG_WARN << "ParseCallCaptureMsg failed.";
        return -1;
    }


    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;
    memset(&act_msg, 0, sizeof(act_msg));
    act_msg.mng_type = device_setting.is_personal;
    act_msg.mng_id = device_setting.manager_account_id;
    act_msg.unit_id = device_setting.unit_id;
    act_msg.is_public = device_setting.is_public;
    Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account), device_setting.sip_account);
    Snprintf(act_msg.location, sizeof(act_msg.location), device_setting.location);
    Snprintf(act_msg.account, sizeof(act_msg.account), device_setting.device_node);//node

    AK_LOG_INFO << device_setting.mac << " device report call logs. callee:" << stCallCaptureMsg.callee
                << " caller:" << stCallCaptureMsg.callee
                << " dialout:" << stCallCaptureMsg.dialog_out;

    Snprintf(act_msg.mac, sizeof(act_msg.mac), device_setting.mac);
    act_msg.act_type = CMsgControl::ActOpenDoorType::CALL_CAPTURE;
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), "Call");
    Snprintf(act_msg.pic_name, sizeof(act_msg.pic_name), stCallCaptureMsg.picture_name);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");//可能含有特殊字符
    Snprintf(act_msg.key, sizeof(act_msg.key), "--");

    std::string node;
    std::string stName;
    if (stCallCaptureMsg.dialog_out && strlen(stCallCaptureMsg.callee) != 0) //设备主叫
    {
        //被叫可能是群组号
        node = dbinterface::Sip::GetNodeByGroupFromSip2(stCallCaptureMsg.callee);//先检查群组
        if (node.empty())
        {
            OfficeAccount office_account;
            if (dbinterface::OfficePersonalAccount::GetUidAccount(stCallCaptureMsg.callee, office_account) == 0)
            {
                stName = office_account.name;
                node = office_account.account;
            }
            if (node.empty())
            {
                dbinterface::OfficeDevices::GetLocationAndNodeBySip(stCallCaptureMsg.callee, stName, node);
                if (node.empty())
                {
                    //找手机号码
                    std::string nick_name;
                    OfficeAccount office_account;
                    if (dbinterface::OfficePersonalAccount::GetPhoneAccountForOfficeid(stCallCaptureMsg.callee, device_setting.manager_account_id, office_account) == 0)
                    {
                        nick_name = office_account.name;
                    }

                    if (!nick_name.empty())
                    {
                        //name 改为name(手机号码)
                        //nick_name +="(";
                        //nick_name +=stCallCaptureMsg.callee;
                        //nick_name +=")";
                    }
                    else
                    {
                        AK_LOG_WARN << "dailout:there is inlegal call, callee sip [" << stCallCaptureMsg.callee << "] caller " << stCallCaptureMsg.caller;
                    }
                }
            }
        }
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");
        Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
    }
    else if (strlen(stCallCaptureMsg.caller) > 0)//当门口机是被叫时候，这时候能明确主叫的名称
    {
        OfficeAccount office_account;
        if (dbinterface::OfficePersonalAccount::GetUidAccount(stCallCaptureMsg.callee, office_account) == 0)
        {
            stName = office_account.name;
            node = office_account.account;
        }
        if (node.empty())
        {
            dbinterface::OfficeDevices::GetLocationAndNodeBySip(stCallCaptureMsg.caller, stName, node);
            if (node.empty())
            {
                AK_LOG_WARN << "dail in:there is inlegal call, callee sip [" << stCallCaptureMsg.callee << "] caller " << stCallCaptureMsg.caller;
            }
        }
        if (!stName.empty())
        {
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), stName.c_str());//可能含有特殊字符
        }

        Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");
        Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
    }

    dbinterface::AccountInfo account;
    if (0 == dbinterface::Account::GetAccountById(act_msg.mng_id, account))
    {
        Snprintf(act_msg.project_uuid, sizeof(act_msg.project_uuid), account.uuid);
    }

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }

    return 0;
}
*/
//v4.4
int OfficeMessageHandle::OnMngDevReportMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }
    //TODO:校验一下所操作的设备是否与app-uid在同一联动系统中
    SOCKET_MSG_MNG_DEV_REPORT_MSG mng_dev_msg;
    memset(&mng_dev_msg, 0, sizeof(mng_dev_msg));
    if (GetMsgControlInstance()->ParseMngDevReportMsg(normal_msg, device_setting.mac, mng_dev_msg) < 0)
    {
        AK_LOG_WARN << "ParseCallCaptureMsg failed.";
        return -1;
    }
    AK_LOG_INFO << "manage devices send text msg";
    if (strlen(mng_dev_msg.nodes) == 0)
    {
        AK_LOG_WARN << "parameter error!";
        return -1;
    }

    PerMsgSendList text_messages;
    if(dbinterface::Message::AddOfficeTextMsgByMngDev(mng_dev_msg.nodes, mng_dev_msg.title, mng_dev_msg.content, device_setting.manager_account_id, text_messages, gstAKCSConf.is_aws) < 0)
    {
        AK_LOG_WARN << "AddTextMsgByMngDev failed.";
        return -1;
    }

    if (gstAKCSConf.is_aws)
    {
        GetMsgControlInstance()->PostAwsInsertMessageHttpReq(text_messages);
    }
    for (auto& text_send : text_messages)
    {
        AK::Server::GroupAdaptTextMsg msg;
        msg.add_node_list(text_send.account);
        msg.set_client_type(text_send.client_type);
        msg.set_title(text_send.text_message.title);
        msg.set_content(text_send.text_message.content);
        msg.set_time(text_send.text_message.time);
        msg.set_from(text_send.text_message.from);
        msg.set_to(text_send.text_message.to);
        msg.set_id(text_send.text_message.id);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_M2R_GROUP_OFFICE_MNG_TEXT_MSG_REQ);
        pdu.SetSeqNum(0);
        pdu.SetProjectType(project::OFFICE);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }
    return 0;
}

//访客系统
int OfficeMessageHandle::OnDevReportVisitorInfo(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    return 0;

}

int OfficeMessageHandle::OnDevReportVisitorAuth(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    SOCKET_MSG_DEV_REPORT_VISITOR_AUTH visitor_auth;
    memset(&visitor_auth, 0, sizeof(SOCKET_MSG_DEV_REPORT_VISITOR_AUTH));
    if (GetMsgControlInstance()->ParseDevReportVisitorAuth(normal_msg, visitor_auth) < 0)
    {
        AK_LOG_WARN << "ParseDevReportVisitorAuth failed.";
        return -1;
    }

    CString strSipAccount = _T(visitor_auth.sip_account);
    DEVICE_SETTING SipDev;
    memset(&SipDev, 0, sizeof(SipDev));
    if (GetDeviceControlInstance()->GetDeviceSettingBySip(strSipAccount, &SipDev) < 0)
    {
        Snprintf(SipDev.mac, sizeof(SipDev.mac), visitor_auth.sip_account); //ip直播上报的是mac
    }

    //通过nsq,通知csroute进行精准投递
    AK::Server::P2PMainHandleVisitorAuth msg;
    msg.set_count(visitor_auth.count);
    msg.set_mac(SipDev.mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_VISITOR_AUTHORIZE_REQ);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int OfficeMessageHandle::OnRespondSensorTrigger(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    DEVICE_SETTING device_setting;
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) != 0)
    {
        AK_LOG_WARN << "the tcp conn of dev is not personnal dev.";
        return -1;
    }

    SOCKET_MSG_SENSOR_TIRGGER_MSG msg;
    memset(&msg, 0, sizeof(msg));
    if (GetMsgControlInstance()->ParseRespondSensoTirggerMsg(normal_msg, device_setting.mac, msg) < 0)
    {
        AK_LOG_WARN << "OnRespondSensorTrigger failed.";
        return -1;
    }

    dbinterface::ResidentDevices::SetDeviceSensorTirggerInfo(device_setting.mac, msg.home, msg.away, msg.sleep);
    return 0;
}

//5.0
int OfficeMessageHandle::OnDevRequestOssSts(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    return 0;

}

/*
int OfficeMessageHandle::OnDevRequestOpen(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    int result = REQUEST_OPEN_DOOR_SUCCESS; //先写死开门成功
    DEVICE_SETTING deviceSetting;
    memset(&deviceSetting, 0, sizeof(deviceSetting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &deviceSetting) < 0)
    {
        AK_LOG_WARN << "GetLocalDeviceSetting failed.";
        return -1;
    }

    SOCKET_MSG_DEV_REQUEST_OPEN stRequestOpen;
    memset(&stRequestOpen, 0, sizeof(stRequestOpen));
    if (GetMsgControlInstance()->ParseRequestOpen(normal_msg, stRequestOpen, deviceSetting.mac) < 0)
    {
        AK_LOG_WARN << "ParseRemoteAck failed.";
        return -1;
    }

    //权限校验
    DEVICE_SETTING dev_info;
    memset(&dev_info, 0, sizeof(dev_info));
    if (GetDeviceSettingInstance()->GetDeviceSettingByMac(stRequestOpen.mac, &dev_info) < 0)
    {
        AK_LOG_WARN << "The device requested to open does not exist";
        return -1;
    }
    //开门校验
    if(!GetMsgControlInstance()->CheckDevCanOpenDoor(deviceSetting, dev_info))
    {
        AK_LOG_WARN << "The device does not have permission. Indoor MAC:" << deviceSetting.mac << " request open MAC:" << stRequestOpen.mac;
        return -1;
    }

    SOCKET_MSG socket_msg;
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG* socket_message = &socket_msg;
    if (GetMsgControlInstance()->BuildOpenDoorAckMsg(socket_message, deviceSetting.mac, result) < 0)
    {
        AK_LOG_WARN << "BuildOpenDoorAckMsg failed.";
        return -1;
    }
    if (GetDeviceControlInstance()->SendTcpMsg(conn, socket_message->data, socket_message->size) < 0)
    {
        AK_LOG_WARN << "Send OpenDoorAckMsg failed.";
        return -1;
    }
    
     std::string open_door_type;
    if (strcmp(stRequestOpen.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_DOOR) == 0)
    {
        AK_LOG_INFO << "Open Door Type:" << stRequestOpen.type;
        open_door_type = SOCKET_MSG_TYPE_NAME_OPENDOOR;
    }
    else if (strcmp(stRequestOpen.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_SECURITY_RELAY) == 0)
    {
        AK_LOG_INFO << "Open Door Type:" << stRequestOpen.type;
        open_door_type = SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY;
    } 
    else
    {
        AK_LOG_WARN << "Invalid Open Door Type:" << stRequestOpen.type;
        return -1;
    }

    std::string mac = deviceSetting.mac;
    std::string trace_id = stRequestOpen.trace_id;
    trace_id = "dev_" + mac + "_" + trace_id;

    AK::Server::P2PMainRequestOpenDoor msg;
    msg.set_uid(deviceSetting.sip_account);
    msg.set_mac(stRequestOpen.mac);
    msg.set_relay(stRequestOpen.relay);
    msg.set_open_door_type(open_door_type);
    msg.set_msg_traceid(trace_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_OPEN_DOOR_REQ);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}
*/

int OfficeMessageHandle::OnDevReqChangeRelay(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    SOCKET_MSG_DEV_RELAY_CHANGE relay_msg;
    uint32_t data_size = NTOHS(normal_msg->data_size);
    char* payload = (char*)normal_msg->data;
    AesDecryptByDefault(payload, payload, data_size);
    if (GetMsgHandleInstance()->ParseReqChangeRelay(payload, relay_msg) < 0)
    {
        AK_LOG_WARN << "ParseReqChangeRelay failed.";
        return -1;
    }

    AK::Server::P2PMainChangeRelay msg;
    msg.set_relay_switch(relay_msg.relay_switch);
    msg.set_relay_id(relay_msg.relay_id);
    msg.set_mac(relay_msg.mac);
    msg.set_relay_type(IndoorRelayType::TYPE_LOCAL);//办公暂不支持Extern Relay
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_CHANGE_RELAY_REQ);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);

    return 0;
}

int OfficeMessageHandle::OnRequestUserInfo(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn)
{
    if (NULL == normal_msg)
    {
        return -1;
    }

    DEVICE_SETTING device_setting;
    memset(&device_setting, 0, sizeof(device_setting));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &device_setting) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t data_size = NTOHS(normal_msg->data_size);
    char *pay_load= (char *)normal_msg->data;
    AesDecryptByMac(pay_load, pay_load, device_setting.mac, data_size);

    SOCKET_MSG_USER_INFO socket_msg_user_infos;
    memset(&socket_msg_user_infos, 0,  sizeof(socket_msg_user_infos));
    if (GetMsgHandleInstance()->ParseUserInfos(pay_load, &socket_msg_user_infos) < 0)
    {
        AK_LOG_WARN << "ParseUserInfos failed.";
        return -1;
    }

    std::string accounts_key = device_setting.mac;
    accounts_key += akuvox_encrypt::MD5(socket_msg_user_infos.uuids).toStr();
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Server::P2PMainRequestWriteUserinfo msg;
    msg.set_mac(device_setting.mac);
    msg.set_uuids(socket_msg_user_infos.uuids);
    msg.set_msg_traceid(traceid);
    msg.set_accounts_key(accounts_key);
    msg.set_dclient_ver(device_setting.dclient_version);
    msg.set_project_uuid(device_setting.project_uuid);
    std::time_t t = std::time(0);
    msg.set_timestamp(t);

    socket_msg_user_infos.traceid = traceid;
    snprintf(socket_msg_user_infos.mac, sizeof(socket_msg_user_infos.mac), "%s", device_setting.mac);
    DevUpdateUserLog::InsertLog(socket_msg_user_infos);

    //add to redis
    int already_handle = 0;
    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail); //获取与redis实例的tcp连接
    if (cache_conn)
    {
        std::string key = accounts_key;
        if (!cache_conn->isExists(key))
        {
            std::string ret = cache_conn->set(key, device_setting.mac);
            cache_conn->expire(key, gstAKCSConf.download_user_timeout);
        }
        else
        {
            already_handle = 1;
        }
        cache_mng->RelCacheConn(cache_conn);
    }
    if (!already_handle)
    {
        AK_LOG_INFO << "Mac:" << device_setting.mac << " request user info, userinfo:" << socket_msg_user_infos.uuids << " traceid:" << traceid;
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_S2C_DEV_REQ_USER_INFO);
        if (device_setting.is_new_office)
        {
            pdu.SetProjectType(project::OFFICE_NEW);
        }
        else
        {
            pdu.SetProjectType(project::OFFICE);
        }
        
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }
    else
    {
        AK_LOG_INFO << "Mac:" << device_setting.mac << " request user info already in queue!";
    }

    return 0;
}

//办公语音留言上报
int OfficeMessageHandle::OnDeviceReportVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    PersonalVoiceMsgInfo per_voice_msg;
    memset(&per_voice_msg, 0, sizeof(per_voice_msg));
    if (GetMsgHandleInstance()->ParseReportVoiceMsg(payload, &per_voice_msg) < 0)
    {
        AK_LOG_WARN << "ParseReportVoiceMsg failed.";
        return -1;
    }

    //插入PersonalVoiceMsg
    dbinterface::AccountInfo account;
    if (0 == dbinterface::Account::GetAccountById(dev.manager_account_id, account))
    {
        snprintf(per_voice_msg.project_uuid, sizeof(per_voice_msg.project_uuid), "%s", account.uuid);
    }
    std::string prefix = gstAKCSConf.server_tag;
    prefix = prefix + "-";
    std::string msg_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, msg_uuid);
    snprintf(per_voice_msg.uuid, sizeof(per_voice_msg.uuid), "%s", msg_uuid.c_str());
    snprintf(per_voice_msg.mac, sizeof(per_voice_msg.mac), "%s", dev.mac);
    snprintf(per_voice_msg.dev_uuid, sizeof(per_voice_msg.dev_uuid), "%s", dev.uuid);
    snprintf(per_voice_msg.location, sizeof(per_voice_msg.location), "%s", dev.location);
    if (0 != dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsg(per_voice_msg))
    {
        AK_LOG_WARN << "InsertPersonalVoiceMsg failed.";
        return -1;
    }
    
    //插入插入PersonalVoiceMsgList
    if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_FAMILY)
    {
        //查找所有用户和室内机的uuid
        std::vector<COMMUNITY_DEVICE_SIP> apps;
        if (GetOfficeAppAndIndoorDevListByNode(per_voice_msg.uid, apps) == 0)
        {
            for (const auto& app : apps)
            {
                CMsgControl::GetInstance()->AddPersonalVoiceMsgNode(prefix, app.uuid, per_voice_msg, app.type);
            }
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_INDOOR)
    {
        //下发给室内机
        ResidentDev comm_dev;
        if (0 == dbinterface::ResidentDevices::GetSipDev(per_voice_msg.uid, comm_dev))
        {
            CMsgControl::GetInstance()->AddPersonalVoiceMsgNode(prefix, comm_dev.uuid, per_voice_msg, DEVICE_TYPE_INDOOR);
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_APP)
    {
        //下发给app
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(per_voice_msg.uid, per_account))
        {
            CMsgControl::GetInstance()->AddPersonalVoiceMsgNode(prefix, per_account.uuid, per_voice_msg, DEVICE_TYPE_APP);
        }
    }
    return 0;
}

//办公设备获取语音留言列表
int OfficeMessageHandle::OnDeviceRequestVoiceMsgList(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    SOCKET_MSG_DEV_VOICE_MSG_LIST msg_list;
    memset(&msg_list, 0, sizeof(msg_list));
    if (GetMsgHandleInstance()->ParseRequestVoiceMsgList(payload, &msg_list) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgList failed.";
        return -1;
    }
    int page_size = msg_list.page_size;
    int page_index = msg_list.page_index;

    PersonalVoiceMsgSendList send_list;
    msg_list.msg_count = dbinterface::PersonalVoiceMsg::GetVoicePageListByIndoorUUID(dev.uuid, page_size, page_index, send_list);
    if (msg_list.msg_count > 0)
    {
        CMsgControl::GetInstance()->OnSendVoiceMsgListMsg(conn, send_list, msg_list, dev.mac);
    }
    return 0;
}

//办公获取下载链接
/*
int OfficeMessageHandle::OnDeviceRequestVoiceMsgUrl(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));
    if (GetMsgHandleInstance()->ParseRequestVoiceMsgUrl(payload, &url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgUrl failed.";
        return -1;
    }

    PersonalVoiceMsgInfo per_voice_msg;
    int ipv6 = evpp::any_cast<int>(conn->context(EVPP_CONN_ANY_CONTEXT_IS_IPV6_INDEX));
    std::string file_url;
    //TODO msg uuid直接获取语音文件会存在越权问题
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByUUID(url_msg.uuid, per_voice_msg))
    {
        file_url = per_voice_msg.file_url;
        std::size_t pos2 =  file_url.find("/group");
        if (pos2 == std::string::npos)
        {
            //存oss的流程

            model::HttpRespuestKV parma_kv;
            parma_kv.insert(map<std::string, std::string>::value_type("Node", "SuperManage"));
            parma_kv.insert(map<std::string, std::string>::value_type("Path", file_url));
            
            char url[1024];
            //snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/common/capture/getLink", gstAKCSConf.web_domain);

            AkcsKv kv;
            kv.insert(map<std::string, std::string>::value_type("mac", dev.mac));
            kv.insert(map<std::string, std::string>::value_type("mac_uuid", dev.uuid));
            kv.insert(map<std::string, std::string>::value_type("voice_uuid", url_msg.uuid));
            CHttpReqNotifyMsg notify_msg(url, parma_kv, kv, CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL);
            GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);            
            return 0;
        }
        
        //以下是存fdfs的流程
        size_t pos = file_url.find("/M");
        if (std::string::npos != pos)
        {
            //获取到 /M00/05/CB/rBIp3GMpNwqADMrHAAEqVhPHnOw417.wav
            std::string file_remote = file_url.substr(pos + 1);
            time_t timer = time(nullptr);
            char time_sec[16] = {0};
            snprintf(time_sec, 16, "%ld", timer);
            file_remote += "ak_fdfs";
            file_remote += time_sec;
            std::string token = akuvox_encrypt::MD5(file_remote).toStr();
            if (!ipv6)
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv4, file_url.c_str(), token.c_str(), time_sec);
            }
            else
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv6, file_url.c_str(), token.c_str(), time_sec);
            }
        }
        CMsgControl::GetInstance()->OnSendVoiceMsgUrl(dev.mac, dev.uuid, url_msg.uuid, url_msg.url);
    }

    return 0;
}
*/

int OfficeMessageHandle::OnDeviceRequestDelVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn)
{
    if (NULL == msg)
    {
        return -1;
    }

    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    if (g_accSer_ptr->GetDeviceSettingFromConnList(conn, &dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed.";
        return -1;
    }

    uint32_t size = NTOHS(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));
    if (GetMsgHandleInstance()->ParseRequestDelVoiceMsg(payload, &url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestDelVoiceMsg failed.";
        return -1;
    }

    if (0 != dbinterface::PersonalVoiceMsg::DelVoiceMsgInfoByIndoorUUID(url_msg.uuid, dev.uuid))
    {
        AK_LOG_WARN << "DelVoiceMsgInfoByUUID failed.";
        return -1;
    }

    return 0;
}
