#ifndef __OFFICE_MEESSAGE_HANDLE_H__
#define __OFFICE_MEESSAGE_HANDLE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include <evpp/tcp_server.h>
#include <evpp/any.h>
#include <boost/circular_buffer.hpp>
#include "AkcsIpcMsgCodec.h"
#include "SDMCMsg.h"
#include "AKUserMng.h"

class OfficeMessageHandle
{
public:
    OfficeMessageHandle();
    int OnSocketMsg(const evpp::TCPConnPtr& conn, const std::string& message);    

    static OfficeMessageHandle* Instance();

    int ProcessCommunityAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const DEVICE_SETTING&  deviceSetting, const evpp::Any& personnalAppSetting, const int type);
    int OnPutAlarmDealMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int ProcessPerMotionAlertMsg(const evpp::TCPConnPtr& conn, const SOCKET_MSG_MOTION_ALERT& motionMsg, const DEVICE_SETTING& deviceSetting);
    int OnReportMotionAlert(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnHandleDevArming(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnReportArmingStatus(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnRequestCapture(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnReportLogOut(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn, const char* remote_ip);
    int OnDevSendDelivery(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //int OnCallCaptureReport(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);

    //v4.4
    int OnMngDevReportMsg(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);

    //访客系统
    int OnDevReportVisitorInfo(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnDevReportVisitorAuth(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnRespondSensorTrigger(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    //5.0
    int OnDevRequestOssSts(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);

    //v5.2
    int OnDevRemoteAck(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    // int OnDevRequestOpen(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);    
    int OnDevReqChangeRelay(SOCKET_MSG_NORMAL* normal_msg, const evpp::TCPConnPtr& conn);
    int OnRequestUserInfo(SOCKET_MSG_NORMAL *normal_msg, const evpp::TCPConnPtr &conn);

    //6.5.4办公语音留言
    int OnDeviceReportVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    int OnDeviceRequestVoiceMsgList(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    //int OnDeviceRequestVoiceMsgUrl(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);
    int OnDeviceRequestDelVoiceMsg(SOCKET_MSG_NORMAL* msg, const evpp::TCPConnPtr& conn);

    int GetLogProjectUUID(const DEVICE_SETTING& device_setting, std::string& project_uuid);

private:
    static OfficeMessageHandle* office_message_instance_;
};




#endif //__OFFICE_MEESSAGE_HANDLE_H__
