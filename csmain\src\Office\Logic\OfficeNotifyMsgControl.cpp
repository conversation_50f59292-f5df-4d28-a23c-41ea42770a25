#include "stdafx.h"
#include "MsgControl.h"
#include "DeviceControl.h"
#include "csmainserver.h"
#include "OfficeNotifyMsgControl.h"
#include "PushClient.h"
#include "AKUserMng.h"
#include "CachePool.h"
#include "Dao.h"
#include "DeviceSetting.h"
#include "VideoSchedMng.h"
#include "session_rpc_client.h"
#include "doorlog/UserInfo.h"
#include "MsgControl.h"
#include "AkcsMonitor.h"
#include "AppCallStatus.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"


extern AKCS_CONF gstAKCSConf;
extern AccessServer* g_accSer_ptr;

OfficeNotifyMsgControl::OfficeNotifyMsgControl(): m_MsgCount(0)
{

}

OfficeNotifyMsgControl::~OfficeNotifyMsgControl()
{
    //curl_easy_cleanup(m_pCurl);
    notify_msg_list_.clear();
}

OfficeNotifyMsgControl* OfficeNotifyMsgControl::instance = NULL;

OfficeNotifyMsgControl* OfficeNotifyMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new OfficeNotifyMsgControl();
    }

    return instance;
}

OfficeNotifyMsgControl* GetOfficeNotifyMsgControlInstance()
{
    return OfficeNotifyMsgControl::GetInstance();
}

//在主线程初始化,注意m_pCurl不是线程安全的.
int OfficeNotifyMsgControl::Init()
{
    m_t = std::thread(&OfficeNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success,thread_id=" << m_t.get_id();
    return 0;
}

int OfficeNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        int is_next_wait = 0;
        OfficeNotifyMsgPrt tmp_ptr;
        {
            std::unique_lock<std::mutex> lock(m_mtx);
            while (notify_msg_list_.size() == 0)
            {
                m_cv.wait(lock);
                is_next_wait = 1;
            }
            //检测队列长度
            //modified by czw,2021-09-09,当社区群发message时队列会达到大几百,故调整为1000告警
            if (is_next_wait == 1 && notify_msg_list_.size() > 1000) //added by chenyc,2019-08-13,500只是估算值,具体的数值需要测试:消费能力TPS*允许的延迟时间来决定.
            {
                std::string msg = "notify msg queue overflow, the length now is:";
                msg += std::to_string(notify_msg_list_.size());
                std::string worker_node = "csmain_";
                worker_node += gstAKCSConf.csmain_outer_ip;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, msg, AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSMAIN_NOTIFY);
                AK_LOG_WARN << msg;
            }
            tmp_ptr = notify_msg_list_.back();
            notify_msg_list_.pop_back();
        }
        tmp_ptr->NotifyMsg();
    }
    return 0;
}
