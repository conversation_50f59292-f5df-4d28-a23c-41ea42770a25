#ifndef __OFFICE_NOTIFY_MSG_CONTROL_H__
#define __OFFICE_NOTIFY_MSG_CONTROL_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AkcsWebMsgSt.h"
#include "AK.Base.pb.h"

class OfficeNotifyMsg; //前置声明

class OfficeNotifyMsgControl
{
public:
    typedef std::shared_ptr<OfficeNotifyMsg> OfficeNotifyMsgPrt;

public:
    OfficeNotifyMsgControl();
    ~OfficeNotifyMsgControl();
    //added by chenyc,2019-03-05,这个消息处理只需要关注本机即可,
    //所有的消息都通过csroute走一遍,即使是本机所挂载的终端消息之间的投递.
    static OfficeNotifyMsgControl* GetInstance();

    //初始化
    int Init();
    //处理消息
    int ProcessNotifyMsg();
private:
    std::list<OfficeNotifyMsgPrt> notify_msg_list_;
    std::mutex m_mtx;
    std::condition_variable m_cv;
    std::thread m_t;
    uint32_t m_MsgCount;  //通知消息队列中未消费的消息个数
    static OfficeNotifyMsgControl* instance;
};

OfficeNotifyMsgControl* GetOfficeNotifyMsgControlInstance();


class OfficeNotifyMsg
{
public:
    OfficeNotifyMsg()
    {

    }
    virtual ~OfficeNotifyMsg()
    {

    }
    int virtual NotifyMsg() = 0;
};


#endif //__OFFICE_NOTIFY_MSG_CONTROL_H__

