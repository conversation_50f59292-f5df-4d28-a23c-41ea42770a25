#include "OfficeRouteMessage.h"
#include "NotifyMsgControl.h"
#include "AkcsWebMsgSt.h"
#include "GroupMsgMng.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AK.Adapt.pb.h"
#include "AkcsMsgDef.h"
#include "AKUserMng.h"
#include "DeviceControl.h"
#include "NodeTimeZone.h"
#include "AKCSDao.h"
#include "RouteClient.h"
#include "csmainserver.h"
#include "MsgControl.h"
#include "AkcsOemDefine.h"
#include "XmlTagDefine.h"
#include "Dao.h"
#include "KeyControl.h"
#include "DevUpdateUserLog.h"
#include "OfficeHandleRouteMsg.h"

extern AccessServer* g_accSer_ptr;

OfficeRouteMsgMng* OfficeRouteMsgMng::office_route_msg_mng_instance_ = nullptr;

OfficeRouteMsgMng* OfficeRouteMsgMng::Instance()
{
    if (!office_route_msg_mng_instance_)
    {
        office_route_msg_mng_instance_ = new OfficeRouteMsgMng();
    }
    return office_route_msg_mng_instance_;
}

int OfficeRouteMsgMng::OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu)
{
    int already_handle = 1;
    uint32_t msg_id = pdu->GetCommandId();
    switch (msg_id)
    {
        //以下是route对各个逻辑服务器请求的转发
        case AKCS_M2R_GROUP_OFFICE_ALARM_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupCommAlarmReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupCommAlarmDealReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_OFFICE_PER_MOTION_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupMotionMsgReq(pdu);
            break;
        }
        case AKCS_M2R_GROUP_OFFICE_MNG_TEXT_MSG_REQ:
        {
            CGroupMsgMng::Instance()->HandleGroupMngTextMsgReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PAppGetArmingReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP:
        {
            CGroupMsgMng::Instance()->HandleP2PAppGetArmingResp(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_VISITOR_AUTHORIZE_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PVisitorAuthorize(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_RTSP_CAPTURE_REQ:
        {
            break;
        }
        case AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PSendDeliveryReq(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_CHANGE_RELAY_REQ:
        {
            CGroupMsgMng::Instance()->HandleP2PChangeRelayReq(pdu);
            break;
        }

        //csadapt->csroute->csmain
        case MSG_C2S_OFFICE_NOTIFY_CONFIG_FILE_CHANGE:
        {
            COfficeGroupMsgMng::Instance()->HandleConfigFileChangeReq(pdu);
            break;
        }
        default:
        {
            already_handle = 0;
        }
    }    
    return already_handle;
}

