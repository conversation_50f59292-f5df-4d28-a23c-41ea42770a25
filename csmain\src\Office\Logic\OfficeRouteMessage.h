#ifndef __OFFICE_ROUTE_MESSAGE_H__
#define __OFFICE_ROUTE_MESSAGE_H__

#include <boost/noncopyable.hpp>
#include "AkcsPduBase.h"
#include "AkcsIpcMsgCodec.h"

//P2P:指的是从csroute->csmain是单播的
//Group:指的是从csroute->csmain是广播的
class OfficeRouteMsgMng : public boost::noncopyable
{
public:
    OfficeRouteMsgMng() {}
    virtual ~OfficeRouteMsgMng() {}

    int OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);
    static OfficeRouteMsgMng* Instance();
private:
    static OfficeRouteMsgMng* office_route_msg_mng_instance_;

};


#endif //__OFFICE_ROUTE_MESSAGE_H__
