#include <boost/any.hpp>
#include "SDMCMsg.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "EventFilterWriteFileImpl.h"

namespace akcs {

CEventFilterWriteFileImpl::CEventFilterWriteFileImpl(const std::string& filename)
{
    file_.open(filename, std::ios::app);
    if (!file_.is_open())
    {
            AK_LOG_WARN << "CEventFilterWriteFileImpl failed to open file " << filename;
    }
}

CEventFilterWriteFileImpl::~CEventFilterWriteFileImpl()
{
    file_.close();
}

void CEventFilterWriteFileImpl::DealEvent(const uint32_t msg_id, const boost::any& context)
{
    switch (msg_id)
    {
        case EF_TEST_REQ_USER_INFO_UPDATE:
        {
            const SOCKET_MSG_USER_INFO& socket_msg_user_infos = boost::any_cast<SOCKET_MSG_USER_INFO>(context);
            char content[400] = {0};
            char time[32] = {0};
            GetNowTime(time, 32);
            ::snprintf(content, 200 - 1, "CEventFilterWriteFileImpl,Event: req_user_info_update, mac=[%s],trace_id=[%llu],time=[%s]", socket_msg_user_infos.mac, socket_msg_user_infos.traceid, time);
            WriteFile(content);
            break;
        }
        //csadapt更新配置文件成功后,返回给csmain
        case EF_TEST_RESP_USER_INFO_UPDATE:
        {
            const GIVEN_KEY_SEND& key_send_info = boost::any_cast<GIVEN_KEY_SEND>(context);
            char content[400] = {0};
            char time[32] = {0};
            GetNowTime(time, 32);
            ::snprintf(content, 200 - 1, "CEventFilterWriteFileImpl,Event: resp_user_info_update, mac=[%s],trace_id=[%lu],time=[%s]", key_send_info.mac, key_send_info.traceid, time);
            WriteFile(content);
            break;
        }
        default:
        {
            AK_LOG_WARN << "CEventFilterWriteFileImpl DealEvent,invalid msg id " << msg_id;
        }
   }
   return;
    
}

void CEventFilterWriteFileImpl::WriteFile(const std::string& content)
{
    file_ << content << std::endl;
}

}

