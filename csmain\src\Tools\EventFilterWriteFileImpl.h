#ifndef __CSMAIN_EVENT_FILTER_WRITE_FILE_H__
#define __CSMAIN_EVENT_FILTER_WRITE_FILE_H__

#include <fstream>
#include "util.h"
#include "EventFilterInterface.h"
namespace akcs {
    
class CEventFilterWriteFileImpl : public CEventFilterInterface
{
public:
    CEventFilterWriteFileImpl(const std::string& filename);
    ~CEventFilterWriteFileImpl();
    DISALLOW_COPY_MOVE_AND_ASSIGN(CEventFilterWriteFileImpl);
    void DealEvent(const uint32_t msg_id, const boost::any& context);
private:
    void WriteFile(const std::string& content);
private:
     std::ofstream file_;
};

}//akcs
#endif //__CSMAIN_EVENT_FILTER_WRITE_FILE_H__