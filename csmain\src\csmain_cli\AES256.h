#ifndef __AES_INCLUDED__
#define __AES_INCLUDED__

#define KEY_LENGTH 32
#define KEY_LENGTH 32
#define AES_ENCRYPT_KEY_V1           "Akuvox55069013!@Akuvox55069013!@"
void genKey(char* key, char* keyout, int nsize);

typedef struct AES_FILE_HEADER_T
{
#define AES_FILE_HEADER_MAGIC_MSB 0xAA
#define AES_FILE_HEADER_MAGIC_LSB 0xAE
    unsigned char byMagicMSB;
    unsigned char byMagicLSB;
    unsigned short version;
    unsigned int file_size;
    unsigned int nReserved1;
    unsigned int nReserved2;
} AES_FILE_HEADER;

int FileAESEncrypt(const char* pszFilePath, const char* pKey, const char* pszDstFilePath);
int FileAESDecrypt(const char* pszFileP<PERSON>, const char* pKey, const char* pDstFilePath);


#endif
