#undef sched_setscheduler
#undef setpriority
#define _GNU_SOURCE /* See feature_test_macros(7) */
#include <stdio.h>


#include <fcntl.h>
#include <signal.h>
#include <sched.h>
#include <sys/un.h>
#include <sys/wait.h>
#include <ctype.h>
#include <sys/resource.h>
#include <grp.h>
#include <pwd.h>
#include <sys/stat.h>
#include <sys/sysinfo.h>
#include <sys/param.h>
#include <sys/sysctl.h>
#include <regex.h>
#include <histedit.h>
#include <sys/prctl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include "vector.h"
#include<netinet/in.h>
#include<sys/socket.h>
#include<unistd.h>
#include<arpa/inet.h>
#include <poll.h>
#include "AES256.h"


#define AST_CLI_COMPLETE_EOF    "_EOF_"

#define AST_MAX_CONNECTS 128
#define NUM_MSGS 64


/*! \name Terminal Colors
*/
/*@{ */
#define COLOR_BLACK     30
#define COLOR_GRAY      (30 | 128)
#define COLOR_RED       31
#define COLOR_BRRED     (31 | 128)
#define COLOR_GREEN     32
#define COLOR_BRGREEN   (32 | 128)
#define COLOR_BROWN     33
#define COLOR_YELLOW    (33 | 128)
#define COLOR_BLUE      34
#define COLOR_BRBLUE    (34 | 128)
#define COLOR_MAGENTA   35
#define COLOR_BRMAGENTA (35 | 128)
#define COLOR_CYAN      36
#define COLOR_BRCYAN    (36 | 128)
#define COLOR_WHITE     37
#define COLOR_BRWHITE   (37 | 128)
/*@} */


/*! \brief Welcome message when starting a CLI interface */
#define WELCOME_MESSAGE \
    ast_verbose("Asterisk %s, Copyright (C) 1999 - 2016, Digium, Inc. and others.\n" \
                "Created by Mark Spencer <<EMAIL>>\n" \
                "Asterisk comes with ABSOLUTELY NO WARRANTY; type 'core show warranty' for details.\n" \
                "This is free software, with components licensed under the GNU General Public\n" \
                "License version 2 and other licenses; you are welcome to redistribute it under\n" \
                "certain conditions. Type 'core show license' for details.\n" \
                "=========================================================================\n", ast_get_version()) \

static int ast_consock = -1;        /*!< UNIX Socket for controlling another asterisk */
pid_t ast_mainpid;


struct timeval ast_startuptime;
struct timeval ast_lastreloadtime;

static History* el_hist;
static EditLine* el;
static char* remotehostname;


static int ast_el_add_history(const char*);
static int ast_el_read_history(const char*);
static int ast_el_write_history(const char*);

static void ast_el_read_default_histfile(void);
static void ast_el_write_default_histfile(void);


char g_addr[128] = "";
char g_prompt[128] = "csmain_cli> ";


typedef enum
{
    /*! Normal operation */
    NOT_SHUTTING_DOWN,
    /*! Committed to shutting down.  Final phase */
    SHUTTING_DOWN_FINAL,
    /*! Committed to shutting down.  Initial phase */
    SHUTTING_DOWN,
    /*!
     * Valid values for quit_handler() niceness below.
     * These shutdown/restart levels can be cancelled.
     *
     * Remote console exit right now
     */
    SHUTDOWN_FAST,
    /*! core stop/restart now */
    SHUTDOWN_NORMAL,
    /*! core stop/restart gracefully */
    SHUTDOWN_NICE,
    /*! core stop/restart when convenient */
    SHUTDOWN_REALLY_NICE
} shutdown_nice_t;


static struct
{
    unsigned int need_reload: 1;
    unsigned int need_quit: 1;
    unsigned int need_quit_handler: 1;
} sig_flags;

static char enddata[80] = "";
static char quitdata[80] = "";
#define ESC 0x1b
#define ATTR_RESET  0


void term_init()
{
    snprintf(enddata, sizeof(enddata), "%c[%dm", ESC, ATTR_RESET);
    snprintf(quitdata, sizeof(quitdata), "%c[%dm", ESC, ATTR_RESET);
}

const char* term_end(void)
{
    return enddata;
}

const char* term_quit(void)
{
    return quitdata;
}

const char* ast_term_reset(void)
{
    return term_end();
}

/*! Called when exiting is certain. */
static void really_quit(int num, shutdown_nice_t niceness, int restart)
{
    ast_el_write_default_histfile();
    if (el != NULL)
    {
        el_end(el);
    }
    if (el_hist != NULL)
    {
        history_end(el_hist);
    }
    if (ast_consock > -1)
    {
        close(ast_consock);
    }
    printf("%s", term_quit());
    exit(0);
}

static void quit_handler(int num, shutdown_nice_t niceness, int restart)
{
    really_quit(num, niceness, restart);
}


static  int  ast_strlen_zero(const char* s)
{
    return (!s || (*s == '\0'));
}


static int ast_all_zeros(const char* s)
{
    while (*s)
    {
        if (*s > 32)
        {
            return 0;
        }
        s++;
    }
    return 1;
}
static int fdsend(int fd, const char* s)
{
    return write(fd, s, strlen(s) + 1);
}


#if !defined(ast_strdupa) && defined(__GNUC__)
/*!
 * \brief duplicate a string in memory from the stack
 * \param s The string to duplicate
 *
 * This macro will duplicate the given string.  It returns a pointer to the stack
 * allocatted memory for the new string.
 */
#define ast_strdupa(s)                                                    \
    (__extension__                                                    \
    ({                                                                \
        const char *__old = (s);                                  \
        size_t __len = strlen(__old) + 1;                         \
        char *__new = __builtin_alloca(__len);                    \
        memcpy (__new, __old, __len);                             \
        __new;                                                    \
    }))
#endif


char*   ast_skip_blanks(const char* str)
{
    if (str)
    {
        while (*str && ((unsigned char) *str) < 33)
        {
            str++;
        }
    }

    return (char*) str;
}

char* ast_trim_blanks(char* str)
{
    char* work = str;

    if (work)
    {
        work += strlen(work) - 1;
        /* It's tempting to only want to erase after we exit this loop,
           but since ast_trim_blanks *could* receive a constant string
           (which we presumably wouldn't have to touch), we shouldn't
           actually set anything unless we must, and it's easier just
           to set each position to \0 than to keep track of a variable
           for it */
        while ((work >= str) && ((unsigned char) *work) < 33)
        {
            *(work--) = '\0';
        }
    }
    return str;
}


char* ast_strip(char* s)
{
    if ((s = ast_skip_blanks(s)))
    {
        ast_trim_blanks(s);
    }
    return s;
}


#ifndef HAVE_STRSEP
char* strsep(char** str, const char* delims)
{
    char* token;

    if (!*str)
    {
        /* No more tokens */
        return NULL;
    }

    token = *str;
    while (**str != '\0')
    {
        if (strchr(delims, **str))
        {
            **str = '\0';
            (*str)++;
            return token;
        }
        (*str)++;
    }

    /* There is no other token */
    *str = NULL;

    return token;
}
#endif

static int ast_tryconnect(void)
{
    struct sockaddr_in server_addr;
    int port = 1025;

    if ((ast_consock = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        fprintf(stderr, "Socket Error:%s\a\n", strerror(errno));
        exit(1);
    }
    struct timeval timeout = {5,0};
    //设置接收超时5s
    setsockopt(ast_consock, SOL_SOCKET, SO_RCVTIMEO, (char *)&timeout, sizeof(struct timeval));

    bzero(&server_addr, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    server_addr.sin_addr.s_addr = inet_addr(g_addr); 
    if (connect(ast_consock, (struct sockaddr*)(&server_addr), sizeof(struct sockaddr)) == -1)
    {
        fprintf(stderr, "Connect error:%s\n", strerror(errno));
        exit(1);
    }

#if 0
    struct sockaddr_un sunaddr;
    int res;
    ast_consock = socket(PF_LOCAL, SOCK_STREAM, 0);
    if (ast_consock < 0)
    {
        fprintf(stderr, "Unable to create socket: %s\n", strerror(errno));
        return 0;
    }
    memset(&sunaddr, 0, sizeof(sunaddr));
    sunaddr.sun_family = AF_LOCAL;
    ast_copy_string(sunaddr.sun_path, ast_config_AST_SOCKET, sizeof(sunaddr.sun_path));
    res = connect(ast_consock, (struct sockaddr*)&sunaddr, sizeof(sunaddr));
    if (res)
    {
        close(ast_consock);
        ast_consock = -1;
        return 0;
    }
    else
#endif
        return 1;
}



static int console_print(const char* s)
{
#if 1
    //struct console_state_data *state =
    //  ast_threadstorage_get(&console_state, sizeof(*state));

    char prefix[80];
    const char* c;
    int num, res = 0;

    do
    {
#if 0
        if (VERBOSE_HASMAGIC(s))
        {

            /* always use the given line's level, otherwise
               we'll use the last line's level */
            state->verbose_line_level = VERBOSE_MAGIC2LEVEL(s);

            /* move past magic */
            s++;

            set_header(prefix, sizeof(prefix), state->verbose_line_level);
        }
        else
        {
            *prefix = '\0';
        }
#endif
        *prefix = '\0';
        c = s;

        /* for a given line separate on verbose magic, newline, and eol */
        if ((s = strchr(c, '\n')))
        {
            ++s;
        }
        else
        {
            s = strchr(c, '\0');
        }


        if (!ast_strlen_zero(prefix))
        {
            fputs(prefix, stdout);
        }

        num = s - c;
        if (fwrite(c, sizeof(char), num, stdout) < num)
        {
            break;
        }

        if (!res)
        {
            /* if at least some info has been written
               we'll want to return true */
            res = 1;
        }
    }
    while (*s);


    if (res)
    {
        fflush(stdout);
    }
#endif
    return 1;
}


#if 0
/* This is the main console CLI command handler.  Run by the main() thread. */
static void consolehandler(const char* s)
{
    printf("%s", term_end());
    fflush(stdout);

    /* Called when readline data is available */
    if (!ast_all_zeros(s))
    {
        ast_el_add_history(s);
    }
    /* The real handler for bang */
    if (s[0] == '!')
    {
        /*
            if (s[1])
                ast_safe_system(s+1);
            else
                ast_safe_system(getenv("SHELL") ? getenv("SHELL") : "/bin/sh");*/
    }
    else
    {
        //ast_cli_command(STDOUT_FILENO, s);
    }
}
#endif

static int remoteconsolehandler(const char* s)
{
    int ret = 0;

    /* Called when readline data is available */
    if (!ast_all_zeros(s))
    {
        ast_el_add_history(s);
    }

    while (isspace(*s))
    {
        s++;
    }

    /* The real handler for bang */
    if (s[0] == '!')
    {
        /*if (s[1])
            ast_safe_system(s+1);
        else
            ast_safe_system(getenv("SHELL") ? getenv("SHELL") : "/bin/sh");
        ret = 1;*/
    }
    else if ((strncasecmp(s, "quit", 4) == 0 || strncasecmp(s, "exit", 4) == 0) &&
             (s[4] == '\0' || isspace(s[4])))
    {
        quit_handler(0, SHUTDOWN_FAST, 0);
        ret = 1;
    }

    return ret;
}


#define ASTERISK_PROMPT "*CLI> "


#ifdef HAVE_LIBEDIT_IS_UNICODE
#define CHAR_T_LIBEDIT wchar_t
#define CHAR_TO_LIBEDIT(c) btowc(c)
#else
#define CHAR_T_LIBEDIT char
#define CHAR_TO_LIBEDIT(c) c
#endif
#if 0
#define POLLIN      0x01
#define POLLPRI     0x02
#define POLLOUT     0x04
#define POLLERR     0x08
#define POLLHUP     0x10
#define POLLNVAL    0x20

struct pollfd
{
    int     fd;
    short   events;
    short   revents;
};
#endif
static int ast_el_read_char(EditLine* editline, CHAR_T_LIBEDIT* cp)
{
    int num_read = 0;
    int lastpos = 0;
    struct pollfd fds[2];
    int res;
    int max;
#define EL_BUF_SIZE 512
    char buf[EL_BUF_SIZE];

    for (;;)
    {
        max = 1;
        fds[0].fd = ast_consock;
        fds[0].events = POLLIN;
        if (1)
        {
            fds[1].fd = STDIN_FILENO;
            fds[1].events = POLLIN;
            max++;
        }
        res = poll(fds, max, -1);
        if (res < 0)
        {
            if (sig_flags.need_quit || sig_flags.need_quit_handler)
            {
                break;
            }
            if (errno == EINTR)
            {
                continue;
            }
            fprintf(stderr, "poll failed: %s\n", strerror(errno));
            break;
        }

        if (fds[1].revents)
        {
            char c = '\0';

            num_read = read(STDIN_FILENO, &c, 1);
            if (num_read < 1)
            {
                break;
            }
            *cp = CHAR_TO_LIBEDIT(c);

            return num_read;
        }

        if (fds[0].revents)
        {
            res = read(ast_consock, buf, sizeof(buf) - 1);
            /* if the remote side disappears exit */
            if (res < 1)
            {
                fprintf(stderr, "\nDisconnected from Csmain server\n");
                /*if (!ast_opt_reconnect) {
                    quit_handler(0, SHUTDOWN_FAST, 0);
                } else {
                    int tries;
                    int reconnects_per_second = 20;

                    fprintf(stderr, "Attempting to reconnect for 30 seconds\n");
                    for (tries = 0; tries < 30 * reconnects_per_second; tries++) {
                        if (ast_tryconnect()) {
                            fprintf(stderr, "Reconnect succeeded after %.3f seconds\n", 1.0 / reconnects_per_second * tries);
                            printf("%s", term_quit());
                            WELCOME_MESSAGE;
                            send_rasterisk_connect_commands();
                            break;
                        }

                        usleep(1000000 / reconnects_per_second);
                    }
                    if (tries >= 30 * reconnects_per_second) {
                        fprintf(stderr, "Failed to reconnect for 30 seconds.  Quitting.\n");
                        quit_handler(0, SHUTDOWN_FAST, 0);
                    }
                }*/
                quit_handler(0, SHUTDOWN_FAST, 0);
                exit(0);
                continue;
            }

            //buf[res + 1] = '\0';
            //buf[res] = '\n';
            //res += 1;
            buf[res] = '\0';
            /* Write over the CLI prompt */
            if (!lastpos)
            {
                if (write(STDOUT_FILENO, "\r[0K", 5) < 0)
                {
                }
            }

            console_print(buf);

            if ((res < EL_BUF_SIZE - 1) && ((buf[res - 1] == '\n') || (res >= 2 && buf[res - 2] == '\n')))
            {
                *cp = CHAR_TO_LIBEDIT(CC_REFRESH);

                return 1;
            }
            lastpos = 1;
        }
    }

    *cp = CHAR_TO_LIBEDIT('\0');

    return 0;
}


static char* cli_prompt(EditLine* editline)
{
    return g_prompt;
}

static struct ast_vector_string* ast_el_strtoarr(char* buf)
{
    char* retstr;
    char* bestmatch;
    struct ast_vector_string* vec = calloc(1, sizeof(*vec));

    if (!vec)
    {
        return NULL;
    }

    /* bestmatch must not be deduplicated */
    bestmatch = strsep(&buf, " ");
    if (!bestmatch || !strcmp(bestmatch, AST_CLI_COMPLETE_EOF))
    {
        goto vector_cleanup;
    }

    while ((retstr = strsep(&buf, " ")))
    {
        if (!strcmp(retstr, AST_CLI_COMPLETE_EOF))
        {
            break;
        }

        /* Older daemons sent duplicates. */
        if (AST_VECTOR_GET_CMP(vec, retstr, !strcasecmp))
        {
            continue;
        }

        retstr = strdup(retstr);
        /* Older daemons sent unsorted. */
        if (!retstr || AST_VECTOR_ADD_SORTED(vec, retstr, strcasecmp))
        {
            free(retstr);
            goto vector_cleanup;
        }
    }

    bestmatch = strdup(bestmatch);
    if (!bestmatch || AST_VECTOR_INSERT_AT(vec, 0, bestmatch))
    {
        free(bestmatch);
        goto vector_cleanup;
    }

    return vec;

vector_cleanup:
    AST_VECTOR_CALLBACK_VOID(vec, free);
    AST_VECTOR_PTR_FREE(vec);

    return NULL;
}
static void ast_cli_display_match_list(struct ast_vector_string* matches, int max)
{
    int idx = 1;
    /* find out how many entries can be put on one line, with two spaces between strings */
    int limit = /*ast_get_termcols(STDOUT_FILENO)*/80 / (max + 2);

    if (limit == 0)
    {
        limit = 1;
    }

    for (;;)
    {
        int numoutputline;

        for (numoutputline = 0; numoutputline < limit && idx < AST_VECTOR_SIZE(matches); idx++)
        {
            numoutputline++;
            fprintf(stdout, "%-*s  ", max, AST_VECTOR_GET(matches, idx));
        }

        if (!numoutputline)
        {
            break;
        }

        fprintf(stdout, "\n");
    }
}

#include <stdarg.h>
int ast_asprintf(char** ret, const char* fmt, ...)
{
    int res;
    va_list ap;

    va_start(ap, fmt);
    res = vasprintf(ret, fmt, ap);
    if (res < 0)
    {
        /*
         * *ret is undefined so set to NULL to ensure it is
         * initialized to something useful.
         */
        *ret = NULL;
    }
    va_end(ap);
    return res;
}



static char* cli_complete(EditLine* editline, int ch)
{
    int len = 0;
    char* ptr;
    struct ast_vector_string* matches;
    int retval = CC_ERROR;
    char savechr;
    int res;

    LineInfo* lf = (LineInfo*)el_line(editline);

    savechr = *(char*)lf->cursor;
    *(char*)lf->cursor = '\0';
    ptr = (char*)lf->cursor;
    if (ptr)
    {
        while (ptr > lf->buffer)
        {
            if (isspace(*ptr))
            {
                ptr++;
                break;
            }
            ptr--;
        }
    }

    len = lf->cursor - ptr;
    if (1/*ast_opt_remote*/)
    {
#if 0
        char* mbuf = malloc(100);
        char* new_mbuf = malloc(100);
        int mlen = 0;
        int maxmbuf = 100;

        if (maxmbuf == -1)
        {
            *((char*) lf->cursor) = savechr;

            return (char*)(CC_ERROR);
        }
#else
        //#define CMD_MATCHESARRAY "_COMMAND MATCHESARRAY \"%s\" \"%s\""
#define CMD_MATCHESARRAY "_COMMAND \"%s\""

        char* mbuf;
        char* new_mbuf;
        int mlen = 0;
        int maxmbuf = ast_asprintf(&mbuf, CMD_MATCHESARRAY, lf->buffer, ptr);
        //snprintf(mbuf, 100, "_COMMAND %s", lf->buffer);
        //int maxmbuf = 100;
        if (maxmbuf == -1)
        {
            *((char*) lf->cursor) = savechr;

            return (char*)(CC_ERROR);
        }

#endif
        fdsend(ast_consock, mbuf);
        res = 0;
        mlen = 0;
        mbuf[0] = '\0';

        while (!strstr(mbuf, AST_CLI_COMPLETE_EOF) && res != -1)
        {
            if (mlen + 1024 > maxmbuf)
            {
                /* Expand buffer to the next 1024 byte increment plus a NULL terminator. */
                maxmbuf = mlen + 1024;
                new_mbuf = realloc(mbuf, maxmbuf + 1);
                if (!new_mbuf)
                {
                    free(mbuf);
                    *((char*) lf->cursor) = savechr;

                    return (char*)(CC_ERROR);
                }
                mbuf = new_mbuf;
            }
            /* Only read 1024 bytes at a time */
            res = read(ast_consock, mbuf + mlen, 1024);
            if (res > 0)
            {
                mlen += res;
                mbuf[mlen] = '\0';
            }
        }
        mbuf[mlen] = '\0';

        matches = ast_el_strtoarr(mbuf);
        free(mbuf);
    }
    else
    {
        //matches = ast_cli_completion_vector((char *)lf->buffer, ptr);
    }

    if (matches)
    {
        int i;
        int maxlen, match_len;
        const char* best_match = AST_VECTOR_GET(matches, 0);

        if (!ast_strlen_zero(best_match))
        {
            el_deletestr(editline, (int) len);
            el_insertstr(editline, best_match);
            retval = CC_REFRESH;
        }

        if (AST_VECTOR_SIZE(matches) == 2)
        {
            /* Found an exact match */
            el_insertstr(editline, " ");
            retval = CC_REFRESH;
        }
        else
        {
            /* Must be more than one match */
            for (i = 1, maxlen = 0; i < AST_VECTOR_SIZE(matches); i++)
            {
                match_len = strlen(AST_VECTOR_GET(matches, i));
                if (match_len > maxlen)
                {
                    maxlen = match_len;
                }
            }

            fprintf(stdout, "\n");
            ast_cli_display_match_list(matches, maxlen);
            retval = CC_REDISPLAY;
        }
        AST_VECTOR_CALLBACK_VOID(matches, free);
        AST_VECTOR_PTR_FREE(matches);
    }

    *((char*) lf->cursor) = savechr;

    return (char*)(long)retval;
}

static int ast_el_initialize(void)
{
    HistEvent ev;
    char* editor, *editrc = getenv("EDITRC");

    if (!(editor = getenv("AST_EDITMODE")))
    {
        if (!(editor = getenv("AST_EDITOR")))
        {
            editor = "emacs";
        }
    }

    if (el != NULL)
    {
        el_end(el);
    }
    if (el_hist != NULL)
    {
        history_end(el_hist);
    }

    el = el_init("asterisk", stdin, stdout, stderr);
    el_set(el, EL_PROMPT, cli_prompt);

    el_set(el, EL_EDITMODE, 1);
    el_set(el, EL_EDITOR, editor);
    el_hist = history_init();
    if (!el || !el_hist)
    {
        return -1;
    }

    /* setup history with 100 entries */
    history(el_hist, &ev, H_SETSIZE, 100);

    el_set(el, EL_HIST, history, el_hist);

    el_set(el, EL_ADDFN, "ed-complete", "Complete argument", cli_complete);
    /* Bind <tab> to command completion */
    el_set(el, EL_BIND, "^I", "ed-complete", NULL);
    /* Bind ? to command completion */
    el_set(el, EL_BIND, "?", "ed-complete", NULL);
    /* Bind ^D to redisplay */
    el_set(el, EL_BIND, "^D", "ed-redisplay", NULL);
    /* Bind Delete to delete char left */
    el_set(el, EL_BIND, "\\e[3~", "ed-delete-next-char", NULL);
    /* Bind Home and End to move to line start and end */
    el_set(el, EL_BIND, "\\e[1~", "ed-move-to-beg", NULL);
    el_set(el, EL_BIND, "\\e[4~", "ed-move-to-end", NULL);
    /* Bind C-left and C-right to move by word (not all terminals) */
    el_set(el, EL_BIND, "\\eOC", "vi-next-word", NULL);
    el_set(el, EL_BIND, "\\eOD", "vi-prev-word", NULL);

    if (editrc)
    {
        el_source(el, editrc);
    }

    return 0;
}

#define MAX_HISTORY_COMMAND_LENGTH 256

static int ast_el_add_history(const char* buf)
{
    HistEvent ev;
    char* stripped_buf;

    if (el_hist == NULL || el == NULL)
    {
        ast_el_initialize();
    }
    if (strlen(buf) > (MAX_HISTORY_COMMAND_LENGTH - 1))
    {
        return 0;
    }

    stripped_buf = ast_strip(ast_strdupa(buf));

    /* HISTCONTROL=ignoredups */
    if (!history(el_hist, &ev, H_FIRST) && strcmp(ev.str, stripped_buf) == 0)
    {
        return 0;
    }

    return history(el_hist, &ev, H_ENTER, stripped_buf);
}

static int ast_el_write_history(const char* filename)
{
    HistEvent ev;

    if (el_hist == NULL || el == NULL)
    {
        ast_el_initialize();
    }

    return (history(el_hist, &ev, H_SAVE, filename));
}

static int ast_el_read_history(const char* filename)
{
    HistEvent ev;

    if (el_hist == NULL || el == NULL)
    {
        ast_el_initialize();
    }

    return history(el_hist, &ev, H_LOAD, filename);
}

static void ast_el_read_default_histfile(void)
{
    char histfile[80] = "";
    const char* home = getenv("HOME");

    if (!ast_strlen_zero(home))
    {
        snprintf(histfile, sizeof(histfile), "%s/.csmaincli_history", home);
        ast_el_read_history(histfile);
    }
}

static void ast_el_write_default_histfile(void)
{
    char histfile[80] = "";
    const char* home = getenv("HOME");

    if (!ast_strlen_zero(home))
    {
        snprintf(histfile, sizeof(histfile), "%s/.csmaincli_history", home);
        ast_el_write_history(histfile);
    }
}

static void __remote_quit_handler(int num)
{
    sig_flags.need_quit = 1;
}

static void ast_remotecontrol(char* data)
{
    char buf[256] = "";
    int res;
    char* hostname;
    //char *cpid;
    char* version;
    //int pid;
    char* stringp = NULL;

    char* ebuf;
    int num = 0;

    //ast_term_init();
    //printf("%s", term_end());
    //fflush(stdout);

    memset(&sig_flags, 0, sizeof(sig_flags));
    signal(SIGINT, __remote_quit_handler);
    signal(SIGTERM, __remote_quit_handler);
    signal(SIGHUP, __remote_quit_handler);

    //if (read(ast_consock, buf, sizeof(buf) - 1) < 0) {
    //ast_log(LOG_ERROR, "read() failed: %s\n", strerror(errno));
    //  return;
    //}
    if (data)
    {
        char tmp[1024];
        snprintf(tmp, 1024, "%s", data);
        if (write(ast_consock, tmp, strlen(tmp) + 1) < 0)
        {
            printf("write() failed\n");
            if (sig_flags.need_quit || sig_flags.need_quit_handler)
            {
                return;
            }
        }
        char tmp_buf[1024];
        res = read(ast_consock, tmp_buf, sizeof(tmp_buf) - 1);
        tmp_buf[res] = '\0';
        console_print(tmp_buf);
        return;
    }
    
    stringp = buf;
    hostname = strsep(&stringp, "/");
    //cpid = strsep(&stringp, "/");
    version = strsep(&stringp, "\n");
    if (!version)
    {
        version = "<Version Unknown>";
    }
    stringp = hostname;
    strsep(&stringp, ".");
    //if (cpid)
    //  pid = atoi(cpid);
    //else
    //  pid = -1;
    //if (!data) {
    //  send_rasterisk_connect_commands();
    //}

    remotehostname = hostname;
    if (el_hist == NULL || el == NULL)
    {
        ast_el_initialize();
    }
    ast_el_read_default_histfile();

    el_set(el, EL_GETCFN, ast_el_read_char);

    for (;;)
    {
        ebuf = (char*)el_gets(el, &num);

        if (sig_flags.need_quit || sig_flags.need_quit_handler)
        {
            break;
        }

        if (!ebuf && write(1, "", 1) < 0)
        {
            break;
        }

        if (!ast_strlen_zero(ebuf))
        {
            if (ebuf[strlen(ebuf) - 1] == '\n')
            {
                ebuf[strlen(ebuf) - 1] = '\0';
            }
            if (strlen(ebuf) == 0)
            {
                continue;
            }
            if (!remoteconsolehandler(ebuf))
            {
                if (strstr(ebuf, "putfile"))
                {
                    printf("cmd putfile is disable\n");
                    
                }
                else 
                {
                    res = write(ast_consock, ebuf, strlen(ebuf) + 1);
                    if (res < 1)
                    {
                        //ast_log(LOG_WARNING, "Unable to write: %s\n", strerror(errno));
                        break;
                    }
                }
            }
        }
    }
    printf("\nDisconnected from Csmain server\n");
}

//#include "aes.h"
#include <arpa/inet.h>
#include <openssl/aes.h>

#define HTONS   htons
#define NTOHS   ntohs
#define HTONL   htonl
#define NTOHL   ntohl


int FileAESDecrypt(const char* pszFilePath, const char* pKey, const char* pDstFilePath)
{
    if (pszFilePath == NULL || pKey == NULL || pDstFilePath == NULL)
    {
        return -1;
    }
    unsigned char* pOriFileBuf = NULL;
    unsigned char* pEncryptFileBuf = NULL;
    const char* pszKey = pKey;
    unsigned int nOriFileLen = 0;
    unsigned int nEncryptFileLen = 0;
    int version = 0;
    AES_KEY aes_key;
    unsigned char iv[17];
    int ret = 0;

    //读取文件数据
    FILE* pFile = fopen(pszFilePath, "rb");
    if (pFile == NULL)
    {
        return -1;
    }

    fseek(pFile, 0, SEEK_END);
    nEncryptFileLen = ftell(pFile);
    pEncryptFileBuf = malloc(nEncryptFileLen + 1);
    memset(pEncryptFileBuf, 0, nEncryptFileLen);
    rewind(pFile);
    fread(pEncryptFileBuf, 1, nEncryptFileLen, pFile);
    fclose(pFile);

    //判断MAGICSUM
    AES_FILE_HEADER* pHeader = (AES_FILE_HEADER*)pEncryptFileBuf;
    if ((pHeader->byMagicMSB != AES_FILE_HEADER_MAGIC_MSB) || (pHeader->byMagicLSB != AES_FILE_HEADER_MAGIC_LSB))
    {
        ret = -1;
        goto OpenfileAndDecrypt_Exit;
    }
    //判断version
    version = NTOHS(pHeader->version);
    if (version != 1)
    {
        ret = -1;
        goto OpenfileAndDecrypt_Exit;
    }
    //获取长度
    nOriFileLen = NTOHL(pHeader->file_size);

    //对收到的数据进行安全性判断
    if (nEncryptFileLen < nOriFileLen || nEncryptFileLen < sizeof(AES_FILE_HEADER))
    {
        ret = -1;
        //rl_log_err("receive data error");
        goto OpenfileAndDecrypt_Exit;
    }
    //根据version解密对应的数据
    pOriFileBuf = malloc(nEncryptFileLen - sizeof(AES_FILE_HEADER) + 1);
    memset(pOriFileBuf, 0, nEncryptFileLen - sizeof(AES_FILE_HEADER) + 1);

    memset(&aes_key, 0, sizeof(AES_KEY));
    AES_set_decrypt_key((unsigned char*)pszKey, 256, &aes_key);
    memset(iv, 0, sizeof(iv));
    AES_cbc_encrypt(pEncryptFileBuf + sizeof(AES_FILE_HEADER),  pOriFileBuf, nEncryptFileLen - sizeof(AES_FILE_HEADER), &aes_key, iv, AES_DECRYPT);

    //将解密后的文件数据写入文件
    FILE* pFile2 = fopen(pDstFilePath, "wb+");
    if (pFile2 != NULL)
    {
        if (nOriFileLen != fwrite(pOriFileBuf, 1, nOriFileLen, pFile2))
        {
            //rl_log_err("Write error!");
        }
        fclose(pFile2);
        pFile2 = NULL;
    }
OpenfileAndDecrypt_Exit:
    if (pOriFileBuf != NULL)
    {
        free(pOriFileBuf);
    }
    if (pEncryptFileBuf != NULL)
    {
        free(pEncryptFileBuf);
    }
    return ret;
}


int FileAESEncrypt(const char* pszFilePath, const char* pKey, const char* pszDstFilePath)
{
    if (pszFilePath == NULL || pKey == NULL || pszDstFilePath == NULL)
    {
        return -1;
    }

    int nEncipherTextLen = 0;
    unsigned char* key = (unsigned char*)pKey;
    unsigned char* pEncipherText;
    unsigned char* pOriFileBuf;  //定义文件指针
    unsigned char* pEncryptFileBuf;
    int nHeaderLen = sizeof(AES_FILE_HEADER);
    AES_KEY aes_key;
    unsigned char iv[16];
    memset(iv, 0, sizeof(iv));
    memset(&aes_key, 0, sizeof(AES_KEY));

    FILE* pFile = fopen(pszFilePath, "rb");
    if (NULL == pFile)
    {
        return -1;
    }
    fseek(pFile, 0, SEEK_END); //把指针移动到文件的结尾 ，获取文件长度
    int nOriFileLen = ftell(pFile); //获取文件长度
    int nFillOriFileLen = ((nOriFileLen - 1) / 16 + 1) * 16;

    unsigned int nEncryptFileLen = nFillOriFileLen + nHeaderLen;

    nEncipherTextLen = nFillOriFileLen;

    pOriFileBuf = (unsigned char*)malloc(nEncipherTextLen);
    pEncryptFileBuf = (unsigned char*)malloc(nEncryptFileLen);
    pEncipherText = (unsigned char*)malloc(nEncipherTextLen);
    memset(pOriFileBuf, 0, nEncipherTextLen);
    memset(pEncryptFileBuf, 0, nEncryptFileLen);
    memset(pEncipherText, 0, nEncipherTextLen);

    rewind(pFile); //把指针移动到文件开头 因为我们一开始把指针移动到结尾，如果不移动回来 会出错
    fread(pOriFileBuf, 1, nOriFileLen, pFile);
    fclose(pFile);

    //组织文件头数据
    AES_FILE_HEADER* pHeader = (AES_FILE_HEADER*)pEncryptFileBuf;
    pHeader->byMagicMSB = AES_FILE_HEADER_MAGIC_MSB;
    pHeader->byMagicLSB = AES_FILE_HEADER_MAGIC_LSB;
    pHeader->version = HTONS(1);
    pHeader->file_size = HTONL(nOriFileLen);

    //加密
    AES_set_encrypt_key(key, 256, &aes_key);
    memset(iv, 0, sizeof(iv));
    AES_cbc_encrypt(pOriFileBuf, pEncipherText, nFillOriFileLen, &aes_key, iv, AES_ENCRYPT);

    memcpy(pEncryptFileBuf + nHeaderLen, pEncipherText, nFillOriFileLen);
    //memcpy(pEncryptFileBuf, pEncipherText, nFillOriFileLen);

    //将文件头和加密后的文件数据写入文件
    FILE* fp = fopen(pszDstFilePath, "wb+");
    if (fp != NULL)
    {
        if (nEncryptFileLen != fwrite(pEncryptFileBuf, 1, nEncryptFileLen, fp))
        {
        }
        fclose(fp);
        fp = NULL;
    }
    free(pOriFileBuf);
    free(pEncryptFileBuf);
    free(pEncipherText);
    return 0;
}

int getMacServer(const      char* mac, char* server, int len)
{
    if (!mac || !server)
    {
        return -1;
    }

    FILE*    fpin;
    char cmd[256] = "";
    snprintf(cmd, sizeof(cmd), "/usr/local/bin/php /usr/local/akcs/csmain/bin/getDevServer.php %s", mac);
    if ((fpin = popen(cmd, "r")) == NULL)
    {
        printf("ERROR: %s  errno:%d\n", cmd, errno);
        return -1;
    }
    fgets(server, len, fpin);
    ferror(fpin);
    return 1;
}


int main(int argc, char* argv[])
{
    if (argc < 2)
    {
        printf("\ncsmain_cli mac\n");
        printf("\ncsmain_cli ip\n");
        return 1;
    }
    else if (!strcmp("-d", argv[1]))
    {
        if (argc < 4)
        {
            printf("\ncsmain_cli -d src dest\n");
            return 1;
        }
        FileAESDecrypt(argv[2], AES_ENCRYPT_KEY_V1, argv[3]);
        return 1;
    }
    else if (!strcmp("-e", argv[1]))
    {
        if (argc < 4)
        {
            printf("\ncsmain_cli -e src dest\n");
            return 1;
        }
        FileAESEncrypt(argv[2], AES_ENCRYPT_KEY_V1, argv[3]);
        return 1;
    }
    else if (!strcmp("-x", argv[1]))
    {
        if (argc < 3)
        {
            printf("\ncsmain_cli -x 'mac;cmd'\n");
            return 1;
        }
        char mac_cmd[512] = {0};
        char mac[256] = {0};
        char cmd[512] = {0};
        char set_mac[512] = {0};
        strncpy(mac_cmd, argv[2], sizeof(mac_cmd)-1);
        int i;
        for(i=0; i<strlen(mac_cmd); i++)
        {
            if(mac_cmd[i] == ';')
            {
                break;
            }
        }

        if(i < strlen(mac_cmd) - 1)
        {
            strncpy(mac, mac_cmd, i);
            strncpy(cmd, mac_cmd + i+1, sizeof(cmd)-1);
        }
        else
        {
            printf("\ncsmain_cli -x 'mac;cmd'\n");
            return 1;
        }
        
        snprintf(set_mac, sizeof(set_mac), "setmac %s", mac);        
        getMacServer(mac, g_addr, sizeof(g_addr));
        if (strlen(g_addr) == 0)
        {
            printf("\ncsmain_cli mac; can not get mac server\n");
            return -1;
        }
        if (ast_tryconnect())
        {
            ast_remotecontrol(set_mac);
            ast_remotecontrol(cmd);
            quit_handler(0, SHUTDOWN_FAST, 0);
        }        
        return 1;
    }
    else
    {
        if (strstr(argv[1], ".")) //csmain_cli ip
        {
            snprintf(g_addr, 128, "%s", argv[1]);
        }
        else
        {
            getMacServer(argv[1], g_addr, sizeof(g_addr));
        }

        if (strlen(g_addr) == 0)
        {
            printf("\ncsmain_cli mac; can not get mac server\n");
            return -1;
        }
        snprintf(g_prompt, sizeof(g_prompt), "%s> ", argv[1]);
    }
    term_init();
    /* DO NOT perform check for existing daemon if systemd has CLI socket activation */
    if (ast_tryconnect())
    {
        //ast_term_init();
        //printf("%s", term_end());
        //fflush(stdout);

        ast_remotecontrol(NULL);
        quit_handler(0, SHUTDOWN_FAST, 0);
    }
    return 0;
}

