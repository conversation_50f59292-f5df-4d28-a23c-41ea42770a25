Building this distribution in many cases is as simple as typing the following
while in the root directory of the source tree:

	./configure
	make

To install, do the above, then type:

	make install

Additional build targets of finer granularity include:

	lib_a
	lib_s
	install_hdr
	install_lib
	install_lib_a
	install_lib_s
	install_man
	test

Cleanup targets include:

	clean
	distclean

Optionally, pass any of the following (not a definitive list) arguments to
'configure':

--prefix=<install-root-dir>
	Set the base directory in which to install.  For example:

		./configure --prefix=/usr/local

	will cause files to be installed into /usr/local/bin, /usr/local/man,
	/usr/local/include, /usr/local/lib, and /usr/local/share.

--disable-readline
	By default, libedit is built and installed such that it works as a
	drop-in replacement for the readline library.  This option turns that
	behavior off.

--enable-debug
	Build debugging code (for libedit development).

Optionally, define environment variables, including (not exclusively):

CFLAGS="?"
	Pass these flags to the compiler.  You probably shouldn't define this
	unless you know what you are doing.

CPPFLAGS="?"
	Pass these flags to the C preprocessor.  Note that CFLAGS is not passed
	to 'cpp' when 'configure' is looking for include files, so you must use
	CPPFLAGS instead if you need to help 'configure' find header files.

LD_LIBRARY_PATH="?"
	'ld' uses this colon-separated list to find libraries.

LDFLAGS="?"
	Flags passed to 'gcc', which should normally be passed on to 'ld'.

PATH="?"
	'configure' uses this to find programs.
