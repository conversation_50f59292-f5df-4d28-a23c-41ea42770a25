#! /bin/sh
# Generated automatically by configure.
# Run this file to recreate the current configuration.
# This directory was configured as follows,
# on host 0f42b1f8f92b:
#
# ./configure 
#
# Compiler output produced by configure, useful for debugging
# configure, is in ./config.log if it exists.

ac_cs_usage="Usage: ./config.status [--recheck] [--version] [--help]"
for ac_option
do
  case "$ac_option" in
  -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    echo "running ${CONFIG_SHELL-/bin/sh} ./configure  --no-create --no-recursion"
    exec ${CONFIG_SHELL-/bin/sh} ./configure  --no-create --no-recursion ;;
  -version | --version | --versio | --versi | --vers | --ver | --ve | --v)
    echo "./config.status generated by autoconf version 2.13"
    exit 0 ;;
  -help | --help | --hel | --he | --h)
    echo "$ac_cs_usage"; exit 0 ;;
  *) echo "$ac_cs_usage"; exit 1 ;;
  esac
done

ac_given_srcdir=.
ac_given_INSTALL="/usr/bin/install -c"

trap 'rm -fr Makefile makelist config.h conftest*; exit 1' 1 2 15

# Protect against being on the right side of a sed subst in config.status.
sed 's/%@/@@/; s/@%/@@/; s/%g$/@g/; /@g$/s/[\\&%]/\\&/g;
 s/@@/%@/; s/@@/@%/; s/@g$/%g/' > conftest.subs <<\CEOF
/^[ 	]*VPATH[ 	]*=[^:]*$/d

s%@SHELL@%/bin/sh%g
s%@CFLAGS@%-Wall -pipe -g3 -O%g
s%@CPPFLAGS@% '-D__RCSID(x)=' '-D__COPYRIGHT(x)=' '-D__RENAME(x)=' '-D_DIAGASSERT(x)='%g
s%@CXXFLAGS@%%g
s%@FFLAGS@%%g
s%@DEFS@%-DHAVE_CONFIG_H%g
s%@LDFLAGS@%%g
s%@LIBS@%-ltermcap %g
s%@exec_prefix@%${prefix}%g
s%@prefix@%/usr/local%g
s%@program_transform_name@%s,x,x,%g
s%@bindir@%${exec_prefix}/bin%g
s%@sbindir@%${exec_prefix}/sbin%g
s%@libexecdir@%${exec_prefix}/libexec%g
s%@datadir@%${prefix}/share%g
s%@sysconfdir@%${prefix}/etc%g
s%@sharedstatedir@%${prefix}/com%g
s%@localstatedir@%${prefix}/var%g
s%@libdir@%${exec_prefix}/lib%g
s%@includedir@%${prefix}/include%g
s%@oldincludedir@%/usr/include%g
s%@infodir@%${prefix}/info%g
s%@mandir@%${prefix}/man%g
s%@CC@%gcc%g
s%@A_CFLAGS@%%g
s%@S_CFLAGS@%-fPIC -DPIC%g
s%@CPP@%gcc -E%g
s%@AWK@%mawk%g
s%@host@%x86_64-unknown-linux-gnu%g
s%@host_alias@%x86_64-unknown-linux-gnu%g
s%@host_cpu@%x86_64%g
s%@host_vendor@%unknown%g
s%@host_os@%linux-gnu%g
s%@INSTALL_PROGRAM@%${INSTALL}%g
s%@INSTALL_SCRIPT@%${INSTALL_PROGRAM}%g
s%@INSTALL_DATA@%${INSTALL} -m 644%g
s%@RANLIB@%ranlib%g
s%@AR@%/usr/bin/ar%g
s%@ACSRCS@%common.c emacs.c vi.c%g
s%@BCSRCS@%chared.c el.c hist.c key.c map.c parse.c prompt.c read.c refresh.c search.c sig.c term.c tty.c%g
s%@CCSRCS@% np/fgetln.c np/vis.c np/unvis.c np/strlcpy.c np/strlcat.c history.c tokenizer.c readline.c%g
s%@AGCSRCS@%fcns.c help.c%g
s%@BGCSRCS@%editline.c%g
s%@HDRS@%chared.h el.h hist.h key.h map.h parse.h prompt.h refresh.h search.h sig.h sys.h term.h tokenizer.h tty.h%g
s%@IHDRS@%histedit.h readline.h%g
s%@IHDR_LINKS@%readline.h readline/history.h%g
s%@AGHDRS@%common.h emacs.h vi.h%g
s%@BGHDRS@%fcns.h help.h%g
s%@HDR_DIRS@%include include/readline%g
s%@MAN3@%editline.3%g
s%@MAN5@%editrc.5%g
s%@MAN3_LINKS@% editline.3 el_init.3 editline.3 el_end.3 editline.3 el_reset.3 editline.3 el_gets.3 editline.3 el_getc.3 editline.3 el_push.3 editline.3 el_parse.3 editline.3 el_set.3 editline.3 el_get.3 editline.3 el_source.3 editline.3 el_resize.3 editline.3 el_line.3 editline.3 el_insertstr.3 editline.3 el_deletestr.3 editline.3 history_init.3 editline.3 history_end.3 editline.3 history.3%g
s%@MAN_DIRS@%man/man3 man/man5%g
s%@LIB_DIRS@%lib%g
s%@LIB_VER@%%g
s%@LIB_A@%libedit.a%g
s%@LIB_A_LINKS@% libedit.a libreadline.a%g
s%@LIB_S@%libedit.so.2%g
s%@LIB_S_LINKS@%libedit.so.2 libedit.so libedit.so libreadline.so%g
s%@S_LDFLAGS@%-shared%g
s%@TEST@%TEST/test%g
s%@TCSRCS@%TEST/test.c%g

CEOF

# Split the substitutions into bite-sized pieces for seds with
# small command number limits, like on Digital OSF/1 and HP-UX.
ac_max_sed_cmds=90 # Maximum number of lines to put in a sed script.
ac_file=1 # Number of current file.
ac_beg=1 # First line for current file.
ac_end=$ac_max_sed_cmds # Line after last line for current file.
ac_more_lines=:
ac_sed_cmds=""
while $ac_more_lines; do
  if test $ac_beg -gt 1; then
    sed "1,${ac_beg}d; ${ac_end}q" conftest.subs > conftest.s$ac_file
  else
    sed "${ac_end}q" conftest.subs > conftest.s$ac_file
  fi
  if test ! -s conftest.s$ac_file; then
    ac_more_lines=false
    rm -f conftest.s$ac_file
  else
    if test -z "$ac_sed_cmds"; then
      ac_sed_cmds="sed -f conftest.s$ac_file"
    else
      ac_sed_cmds="$ac_sed_cmds | sed -f conftest.s$ac_file"
    fi
    ac_file=`expr $ac_file + 1`
    ac_beg=$ac_end
    ac_end=`expr $ac_end + $ac_max_sed_cmds`
  fi
done
if test -z "$ac_sed_cmds"; then
  ac_sed_cmds=cat
fi

CONFIG_FILES=${CONFIG_FILES-"Makefile makelist"}
for ac_file in .. $CONFIG_FILES; do if test "x$ac_file" != x..; then
  # Support "outfile[:infile[:infile...]]", defaulting infile="outfile.in".
  case "$ac_file" in
  *:*) ac_file_in=`echo "$ac_file"|sed 's%[^:]*:%%'`
       ac_file=`echo "$ac_file"|sed 's%:.*%%'` ;;
  *) ac_file_in="${ac_file}.in" ;;
  esac

  # Adjust a relative srcdir, top_srcdir, and INSTALL for subdirectories.

  # Remove last slash and all that follows it.  Not all systems have dirname.
  ac_dir=`echo $ac_file|sed 's%/[^/][^/]*$%%'`
  if test "$ac_dir" != "$ac_file" && test "$ac_dir" != .; then
    # The file is in a subdirectory.
    test ! -d "$ac_dir" && mkdir "$ac_dir"
    ac_dir_suffix="/`echo $ac_dir|sed 's%^\./%%'`"
    # A "../" for each directory in $ac_dir_suffix.
    ac_dots=`echo $ac_dir_suffix|sed 's%/[^/]*%../%g'`
  else
    ac_dir_suffix= ac_dots=
  fi

  case "$ac_given_srcdir" in
  .)  srcdir=.
      if test -z "$ac_dots"; then top_srcdir=.
      else top_srcdir=`echo $ac_dots|sed 's%/$%%'`; fi ;;
  /*) srcdir="$ac_given_srcdir$ac_dir_suffix"; top_srcdir="$ac_given_srcdir" ;;
  *) # Relative path.
    srcdir="$ac_dots$ac_given_srcdir$ac_dir_suffix"
    top_srcdir="$ac_dots$ac_given_srcdir" ;;
  esac

  case "$ac_given_INSTALL" in
  [/$]*) INSTALL="$ac_given_INSTALL" ;;
  *) INSTALL="$ac_dots$ac_given_INSTALL" ;;
  esac

  echo creating "$ac_file"
  rm -f "$ac_file"
  configure_input="Generated automatically from `echo $ac_file_in|sed 's%.*/%%'` by configure."
  case "$ac_file" in
  *Makefile*) ac_comsub="1i\\
# $configure_input" ;;
  *) ac_comsub= ;;
  esac

  ac_file_inputs=`echo $ac_file_in|sed -e "s%^%$ac_given_srcdir/%" -e "s%:% $ac_given_srcdir/%g"`
  sed -e "$ac_comsub
s%@configure_input@%$configure_input%g
s%@srcdir@%$srcdir%g
s%@top_srcdir@%$top_srcdir%g
s%@INSTALL@%$INSTALL%g
" $ac_file_inputs | (eval "$ac_sed_cmds") > $ac_file
fi; done
rm -f conftest.s*

# These sed commands are passed to sed as "A NAME B NAME C VALUE D", where
# NAME is the cpp macro being defined and VALUE is the value it is being given.
#
# ac_d sets the value in "#define NAME VALUE" lines.
ac_dA='s%^\([ 	]*\)#\([ 	]*define[ 	][ 	]*\)'
ac_dB='\([ 	][ 	]*\)[^ 	]*%\1#\2'
ac_dC='\3'
ac_dD='%g'
# ac_u turns "#undef NAME" with trailing blanks into "#define NAME VALUE".
ac_uA='s%^\([ 	]*\)#\([ 	]*\)undef\([ 	][ 	]*\)'
ac_uB='\([ 	]\)%\1#\2define\3'
ac_uC=' '
ac_uD='\4%g'
# ac_e turns "#undef NAME" without trailing blanks into "#define NAME VALUE".
ac_eA='s%^\([ 	]*\)#\([ 	]*\)undef\([ 	][ 	]*\)'
ac_eB='$%\1#\2define\3'
ac_eC=' '
ac_eD='%g'

if test "${CONFIG_HEADERS+set}" != set; then
  CONFIG_HEADERS="config.h"
fi
for ac_file in .. $CONFIG_HEADERS; do if test "x$ac_file" != x..; then
  # Support "outfile[:infile[:infile...]]", defaulting infile="outfile.in".
  case "$ac_file" in
  *:*) ac_file_in=`echo "$ac_file"|sed 's%[^:]*:%%'`
       ac_file=`echo "$ac_file"|sed 's%:.*%%'` ;;
  *) ac_file_in="${ac_file}.in" ;;
  esac

  echo creating $ac_file

  rm -f conftest.frag conftest.in conftest.out
  ac_file_inputs=`echo $ac_file_in|sed -e "s%^%$ac_given_srcdir/%" -e "s%:% $ac_given_srcdir/%g"`
  cat $ac_file_inputs > conftest.in

  cat > conftest.frag <<CEOF
${ac_dA}HAVE_LIBTERMCAP${ac_dB}HAVE_LIBTERMCAP${ac_dC}1${ac_dD}
${ac_uA}HAVE_LIBTERMCAP${ac_uB}HAVE_LIBTERMCAP${ac_uC}1${ac_uD}
${ac_eA}HAVE_LIBTERMCAP${ac_eB}HAVE_LIBTERMCAP${ac_eC}1${ac_eD}
${ac_dA}HAVE_TERMCAP_H${ac_dB}HAVE_TERMCAP_H${ac_dC}1${ac_dD}
${ac_uA}HAVE_TERMCAP_H${ac_uB}HAVE_TERMCAP_H${ac_uC}1${ac_uD}
${ac_eA}HAVE_TERMCAP_H${ac_eB}HAVE_TERMCAP_H${ac_eC}1${ac_eD}
${ac_dA}HAVE_SYS_CDEFS_H${ac_dB}HAVE_SYS_CDEFS_H${ac_dC}1${ac_dD}
${ac_uA}HAVE_SYS_CDEFS_H${ac_uB}HAVE_SYS_CDEFS_H${ac_uC}1${ac_uD}
${ac_eA}HAVE_SYS_CDEFS_H${ac_eB}HAVE_SYS_CDEFS_H${ac_eC}1${ac_eD}
s%^[ 	]*#[ 	]*undef[ 	][ 	]*[a-zA-Z_][a-zA-Z_0-9]*%/* & */%
CEOF
  sed -f conftest.frag conftest.in > conftest.out
  rm -f conftest.in
  mv conftest.out conftest.in

  rm -f conftest.frag conftest.h
  echo "/* $ac_file.  Generated automatically by configure.  */" > conftest.h
  cat conftest.in >> conftest.h
  rm -f conftest.in
  if cmp -s $ac_file conftest.h 2>/dev/null; then
    echo "$ac_file is unchanged"
    rm -f conftest.h
  else
    # Remove last slash and all that follows it.  Not all systems have dirname.
      ac_dir=`echo $ac_file|sed 's%/[^/][^/]*$%%'`
      if test "$ac_dir" != "$ac_file" && test "$ac_dir" != .; then
      # The file is in a subdirectory.
      test ! -d "$ac_dir" && mkdir "$ac_dir"
    fi
    rm -f $ac_file
    mv conftest.h $ac_file
  fi
fi; done



exit 0
