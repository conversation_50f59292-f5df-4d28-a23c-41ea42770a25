dnl
dnl Process this file with autoconf to produce a configure script.
dnl
AC_INIT(Makefile.in)

dnl If CFLAGS isn't defined and using gcc, set CFLAGS to something reasonable.
dnl Otherwise, just prevent autoconf from molesting CFLAGS.
CFLAGS=$CFLAGS
AC_PROG_CC
if test "x$CFLAGS" = "x" ; then
  no_CFLAGS="yes"
fi
if test "x$no_CFLAGS" = "xyes" -a "x$GCC" = "xyes" ; then
	CFLAGS="-Wall -pipe -g3"
fi
A_CFLAGS=""
AC_SUBST(A_CFLAGS)
S_CFLAGS="-fPIC -DPIC"
AC_SUBST(S_CFLAGS)
AC_PROG_CPP
AC_PROG_AWK

dnl Platform-specific settings.  The ABI can probably be determined
dnl programmatically, but doing so is error-prone, which makes it generally
dnl not worth the trouble.
AC_CANONICAL_HOST
case "${host}" in
  *-*-darwin*)
	CFLAGS="$CFLAGS -fno-common -no-cpp-precomp"
	ABI="macho"
	;;
  *-*-freebsd*)
	ABI="elf"
	;;
  *-*-linux* | *cygwin*)
	if echo ${host} | grep -q cygwin ; then \
		echo "cygwin detected"; \
		S_CFLAGS=""; \
		echo "/* cygdef.h. Generated automatically by configure. */
#ifndef _CYGDEF_H_
#define _CYGDEF_H_ 1
#include <sys/ioctl.h>
#define __linux__ 1


typedef void (*sig_t)(int);


#endif /* _CYGDEF_H_ */" > cygdef.h; \
	echo "
 #define CYGWIN 1
" > confdefs.h; \
	fi
	ABI="elf"
	;;
  *-*-netbsd*)
	AC_MSG_CHECKING(ABI)
	AC_EGREP_CPP(yes,
[#ifdef __ELF__
  yes
#endif
],
		     ABI="elf",
		     ABI="aout")
	AC_MSG_RESULT($ABI)
	;;
  *-*-solaris2*)
	ABI="elf"
	AC_DEFINE(SUNOS)
	;;
  *)
	AC_MSG_RESULT(Unsupported operating system: ${host})
	ABI="elf"
	;;
esac

AC_PROG_INSTALL
AC_PROG_RANLIB
AC_PATH_PROGS(AR, ar gar, , $PATH)

dnl Search for termcap access routines in termcap, tinfo, curses, and ncurses.
AC_CHECK_LIB(termcap, tgetent, , \
  AC_CHECK_LIB(tinfo, tgetent, , \
    AC_CHECK_LIB(curses, tgetent, , \
      AC_CHECK_LIB(ncurses, tgetent, , \
        AC_MSG_ERROR(termcap support not found)))))

dnl Use termcap.h if it exists; otherwise we need both term.h and [n]curses.h.
AC_CHECK_HEADERS(termcap.h, , \
  AC_CHECK_HEADERS(term.h, , \
    AC_MSG_RESULT(Need term.h since termcap.h is missing))
  AC_CHECK_HEADERS(curses.h, , \
    AC_CHECK_HEADERS(ncurses.h, , \
      AC_MSG_RESULT(Need curses.h or ncurses.h))))

AC_CHECK_HEADERS(sys/cdefs.h vis.h)

AC_CHECK_FUNCS(issetugid)
AC_CHECK_FUNCS(fgetln, , CCSRCS="$CCSRCS np/fgetln.c")
AC_CHECK_FUNCS(strvis, , CCSRCS="$CCSRCS np/vis.c")
AC_CHECK_FUNCS(strunvis, , CCSRCS="$CCSRCS np/unvis.c")
AC_CHECK_FUNCS(strlcpy, , CCSRCS="$CCSRCS np/strlcpy.c")
AC_CHECK_FUNCS(strlcat, , CCSRCS="$CCSRCS np/strlcat.c")

AC_EGREP_CPP(yes,
[#include <sys/cdefs.h>
#ifdef __RCSID
  yes
#endif
], , [CPPFLAGS="$CPPFLAGS '-D__RCSID(x)='"])

AC_EGREP_CPP(yes,
[#include <sys/cdefs.h>
#ifdef __COPYRIGHT
  yes
#endif
], , [CPPFLAGS="$CPPFLAGS '-D__COPYRIGHT(x)='"])

AC_EGREP_CPP(yes,
[#include <sys/cdefs.h>
#ifdef __RENAME
  yes
#endif
], , [CPPFLAGS="$CPPFLAGS '-D__RENAME(x)='"])

AC_EGREP_CPP(yes,
[#include <sys/cdefs.h>
#ifdef _DIAGASSERT
  yes
#endif
], , [CPPFLAGS="$CPPFLAGS '-D_DIAGASSERT(x)='"])

dnl Enable readline compatibility by default.
AC_ARG_ENABLE(readline, [  --disable-readline      Disable readline compatibility],
if test "x$enable_readline" != "xyes" ; then
  enable_readline="no"
fi
,
enable_readline="yes"
)

dnl Optionally enable debugging.
AC_ARG_ENABLE(debug, [  --enable-debug          Enable debugging code],
if test "x$enable_debug" != "xyes" ; then
  enable_debug="no"
fi
,
enable_debug="no"
)
if test "x$enable_debug" = "xyes" ; then
  CPPFLAGS="$CPPFLAGS -DDEBUG_TTY -DDEBUG_KEY -DDEBUG_READ -DDEBUG"
  CPPFLAGS="$CPPFLAGS -DDEBUG_REFRESH -DDEBUG_PASTE"
else
  CFLAGS="$CFLAGS -O"
fi


dnl
dnl File lists.  This is done here instead of in the Makefile in order to avoid
dnl the need for conditionals.
dnl

dnl .c files.
ACSRCS="common.c emacs.c vi.c"
BCSRCS="chared.c el.c hist.c key.c map.c parse.c prompt.c read.c refresh.c search.c sig.c term.c tty.c"
CCSRCS="$CCSRCS history.c tokenizer.c"

dnl Generated .c files.
AGCSRCS="fcns.c help.c"
BGCSRCS="editline.c"

dnl .h files.
HDRS="chared.h el.h hist.h key.h map.h parse.h prompt.h refresh.h search.h sig.h sys.h term.h tokenizer.h tty.h"

dnl Installed .h files.
IHDRS="histedit.h"

dnl Installed headers for readline compatibility.
IHDR_LINKS=

dnl Generated .h files.
AGHDRS="common.h emacs.h vi.h"
BGHDRS="fcns.h help.h"

dnl Header installation directories.
HDR_DIRS="include"

dnl Man pages.
MAN3="editline.3"
MAN5="editrc.5"

MAN3_LINKS=
for i in  el_init.3 el_end.3 el_reset.3 el_gets.3 el_getc.3 el_push.3 \
          el_parse.3 el_set.3 el_get.3 el_source.3 el_resize.3 el_line.3 \
	  el_insertstr.3 el_deletestr.3 history_init.3 history_end.3 \
	  history.3 ; do
  MAN3_LINKS="$MAN3_LINKS editline.3 $i"
done

dnl Man page installation directories.
MAN_DIRS="man/man3 man/man5"

dnl Library settings.
LIB_DIRS="lib"
LIB_MAJOR="2"
LIB_MINOR="6"
LIB_A="libedit.a"
LIB_A_LINKS=

if test "x$ABI" = "xelf" ; then
  LIB_S="libedit.so.$LIB_MAJOR"
  LIB_S_LINK="libedit.so"
  LIB_S_LINKS="$LIB_S $LIB_S_LINK"
  S_LDFLAGS="-shared"
elif test "x$ABI" = "xaout" ; then
  LIB_S="libedit.so.$LIB_MAJOR.$LIB_MINOR"
  LIB_S_LINKS=
  S_LDFLAGS="-shared"
elif test "x$ABI" = "xmacho" ; then
  S_LDFLAGS="-shared"
  LIB_S="libedit.$LIB_MAJOR.dylib"
  LIB_S_LINK="libedit.dylib"
  LIB_S_LINKS="$LIB_S $LIB_S_LINK"
  if test "x$prefix" = "xNONE" ; then
    S_LDFLAGS="-undefined suppress -flat_namespace -dynamiclib -compatibility_version $LIB_MAJOR -current_version $LIB_MAJOR -install_name /usr/local/lib/$LIB_S"
  else
    S_LDFLAGS="-undefined suppress -flat_namespace -dynamiclib -compatibility_version $LIB_MAJOR -current_version $LIB_MAJOR -install_name $prefix/lib/$LIB_S"
  fi
fi

dnl Test program.
TEST="TEST/test"
TCSRCS="TEST/test.c"

dnl Add files to the lists if readline compatibility is enabled.
if test "x$enable_readline" = "xyes" ; then
  CCSRCS="$CCSRCS readline.c"
  IHDRS="$IHDRS readline.h"
  IHDR_LINKS="readline.h readline/history.h"
  HDR_DIRS="$HDR_DIRS include/readline"
  LIB_A_LINKS="$LIB_A_LINKS libedit.a libreadline.a"
  if test "x$ABI" = "xelf" ; then
    LIB_S_LINKS="$LIB_S_LINKS $LIB_S_LINK libreadline.so"
  elif test "x$ABI" = "xaout" ; then
    LIB_S_LINKS="$LIB_S_LINKS $LIB_S libreadline.so.$LIB_MAJOR.$LIB_MINOR"
  elif test "x$ABI" = "xmacho" ; then
    LIB_S_LINKS="$LIB_S_LINKS $LIB_S_LINK libreadline.dylib"
  fi
fi

AC_SUBST(ACSRCS)
AC_SUBST(BCSRCS)
AC_SUBST(CCSRCS)
AC_SUBST(AGCSRCS)
AC_SUBST(BGCSRCS)
AC_SUBST(HDRS)
AC_SUBST(IHDRS)
AC_SUBST(IHDR_LINKS)
AC_SUBST(AGHDRS)
AC_SUBST(BGHDRS)
AC_SUBST(HDR_DIRS)
AC_SUBST(MAN3)
AC_SUBST(MAN5)
AC_SUBST(MAN3_LINKS)
AC_SUBST(MAN_DIRS)
AC_SUBST(LIB_DIRS)
AC_SUBST(LIB_VER)
AC_SUBST(LIB_A)
AC_SUBST(LIB_A_LINKS)
AC_SUBST(LIB_S)
AC_SUBST(LIB_S_LINKS)
AC_SUBST(S_LDFLAGS)
AC_SUBST(TEST)
AC_SUBST(TCSRCS)

AC_CONFIG_HEADER(config.h)
AC_OUTPUT([Makefile makelist])
