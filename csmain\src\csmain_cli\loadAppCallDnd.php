<?php
require_once (dirname(__FILE__) . '/RedisManage.php');
/**
 * Load AppCallDND,write to redis
 */
date_default_timezone_set("PRC");


function getDB($dbhost) {
    $dbhost = $dbhost;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$csmainConf = parse_ini_file("/usr/local/akcs/csmain/conf/csmain.conf");
$dbHost = $csmainConf["db_ip"];
print_r("db_host=" . $dbHost . "\n");
$db = getDB($dbHost);

$csmainRedisConf = parse_ini_file("/usr/local/akcs/csmain/conf/csmain_redis.conf");
$redisIP = $csmainRedisConf["appdnd_host"];
$redisPort = $csmainRedisConf["appdnd_port"];
$redisSentinels = $csmainRedisConf["sentinels"];
print_r("redis_ip=" . $redisIP . ";redis_port=" . $redisPort . ";redis_sentinels=" . $redisSentinels . "\n");
$RedisManage = new RedisManage($redisIP, $redisPort, $redisSentinels);
$redis = $RedisManage->getRedisInstance();
if (null == $redis) {
    print_r("connect redis failed!\n");
    return;
}
$redis->select(3);

$sth = $db->prepare("select a.Status,a.StartTime,a.EndTime,a.Account from AppCallDND a");
$sth->execute();
$appDndList = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($appDndList as $row => $appDnd) {
    $data = $appDnd["Status"] . "-" . $appDnd["StartTime"] . "-" . $appDnd["EndTime"];
    $redis->hset($appDnd["Account"], "dndinfo", $data);
    print_r("redis set account=" . $appDnd["Account"] . ";value=" . $data . " success.\n");
}
?>
