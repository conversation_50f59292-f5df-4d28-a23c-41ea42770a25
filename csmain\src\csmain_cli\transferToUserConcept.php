<?php

/**
 * Transfer Old Community Data To User Concept Data
 */
date_default_timezone_set("PRC");
ini_set('memory_limit',  '512M');

const NEW_COMMUNITY = 1;
const SCHEDULE_ONCE = 0; //单次计划
const SCHEDULE_EVERY_DAY = 1; //每日计划
const SCHEDULE_EVERY_WEEK = 2; //每周计划
const DELIVERY_ACCESS = "delivery_access";
const STAFF_ACCESS = "staff_access";
const USER_ACCESS = "user_access";
const TRANSFER_TYPE_CARD = 1;
const TRANSFER_TYPE_KEY = 0;
const OWNER_STAFF = 0;
const OWNER_DELIVERY = 1;
const OWNER_USER = 2;
const COMMUNITY_DEVICE_TYPE_PUBLIC = 1;
const COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT = 2;
const COMMUNITY_DEVICE_TYPE_PERSONAL = 3;
const PUBLIC_ACCESS_GROUP_FILE_NAME = "public_access_group.json";
const USER_ACCESS_GROUP_FILE_NAME = "user_access_group.json";
const IGNORE_END_TIME = 1;
const NEED_END_TIME = 0;

function getDB($dbhost) {
    $dbhost = $dbhost;
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

//$csmainConf = parse_ini_file("/usr/local/akcs/csmain/conf/csmain.conf");
//$dbHost = $csmainConf["db_ip"];
$dbHost = "127.0.0.1";
$db = getDB($dbHost);
$userSchedulerMap = [];
$publicSchedulerMap = [];
$ignoreEndTime = NEED_END_TIME;


$db->beginTransaction();//开启事务处理
//$m->commit();//提交事务
//$m->rollBack();//事务回滚


function getCommunityInfo($communityId) {
    global $db;
    $sth = $db->prepare("SELECT  C.IsNew FROM  Account A left join CommunityInfo C on C.AccountID=A.ID where A.ID = :ID");
    $sth->bindParam(':ID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function updateCommunityToNew($communityId) {
    global $db;
    $sth = $db->prepare("update CommunityInfo set IsNew = 1 where AccountID = :ID");
    $sth->bindParam(':ID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    print_r("updateCommunityToNew success.\n");
}

function getAccessGroupName($accessGroupData) {
    $schedType = $accessGroupData['SchedulerType'];
    $name = $accessGroupData['Name'];
    if ($schedType == SCHEDULE_ONCE) {
        return $name . '_ONCE_ACCESS';
    } else if ($schedType == SCHEDULE_EVERY_DAY) {
        return $name . '_DAYILY_ACCESS';
    } else if ($schedType == SCHEDULE_EVERY_WEEK) {
        return $name . '_WEEKLY_ACCESS';
    } else {
        return $name . '_ACCESS';
    }
}

function getDataListKey($ownerType) {
    if ($ownerType == OWNER_DELIVERY) {
        return 'DeliveryDataList';
    } else if ($ownerType == OWNER_STAFF) {
        return 'StaffDataList';
    } else if ($ownerType == OWNER_USER) {
        return 'UserDataList';
    } else {
        return 'DataList';
    }
}

function transferEndTime($schedulerType, $endTime) {
    if ($schedulerType == SCHEDULE_ONCE) {
        if (strtotime("2023-10-10") < strtotime($endTime)) {
            $time = "2038-1-1 00:00:00"; //结束时间对齐、不计算开始时间
            return $time;
        }
    }

    return $endTime;
}

function setDefaultBeginTimeEndTime(&$data) {
    if (!$data['BeginTime']) {
        $data['BeginTime'] = '2021-01-01 00:00:00';
    }

    if (!$data['EndTime']) {
        $data['EndTime'] = '2299-01-01 00:00:00';
    }
}

function writeFile($fileName, $text) {
    if ($fpOut = fopen($fileName, 'w+')) {
        fwrite($fpOut, $text . "\n");
        fclose($fpOut);
    }
}

function writeJsonFile() {
    global $publicSchedulerMap;
    global $userSchedulerMap;

    writeFile(PUBLIC_ACCESS_GROUP_FILE_NAME, json_encode($publicSchedulerMap));
    writeFile(USER_ACCESS_GROUP_FILE_NAME, json_encode($userSchedulerMap));
}

function clearStaffData($communityId) {
    print_r("clearStaffData begin.\n");
    global $db;
    $sth = $db->prepare("select ID StaffID from Staff where CommunityID = :CommunityID");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dataList as $row => $data) {
        clearStaffAccess($data['StaffID']);
    }

    $sth = $db->prepare("delete from Staff where CommunityID = :CommunityID");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    print_r("clearStaffData end.\n");
}

function clearStaffAccess($staffID) {
    global $db;
    $sth = $db->prepare("delete from AccessGroup where ID in(select AccessGroupID from StaffAccess where StaffID = :StaffID)");
    $sth->bindParam(':StaffID', $staffID, PDO::PARAM_INT);
    $sth->execute();

    $sth = $db->prepare("delete from AccessGroupDevice where AccessGroupID in(select AccessGroupID from StaffAccess where StaffID = :StaffID)");
    $sth->bindParam(':StaffID', $staffID, PDO::PARAM_INT);
    $sth->execute();

    $sth = $db->prepare("delete from StaffAccess where StaffID = :StaffID");
    $sth->bindParam(':StaffID', $staffID, PDO::PARAM_INT);
    $sth->execute();
}

function clearDeliveryData($communityId) {
    print_r("clearDeliveryData begin.\n");
    global $db;
    $sth = $db->prepare("select ID DeliveryID from Delivery where CommunityID = :CommunityID");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dataList as $row => $data) {
        clearDeliveryAccess($data['DeliveryID']);
    }

    $sth = $db->prepare("delete from Delivery where CommunityID = :CommunityID");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    print_r("clearDeliveryData end.\n");
}

function clearUserData($communityId, $table) {
    print_r("clearUserData begin.\n");
    global $db;
    $sth = $db->prepare("select a.Account,b.ParentID,b.Role,t.ParentID MngAccountID from " . $table . " a 
                          LEFT JOIN PersonalAccount b on a.Account = b.Account
                          LEFT JOIN PersonalAccount t on b.ParentID = t.ID and b.Role in(11,21)
                          WHERE ((b.role in(10,20) and b.ParentID = :CommunityID) or (b.role in(11,21) and t.ParentID = :CommunityID))");

    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dataList as $row => $data) {
        $account = $data['Account'];
        clearAccountData($account);
    }

    print_r("clearUserData end.\n");
}

function clearAccountData($account) {
    //print_r("clearAccountData begin,account=$account\n");
    global $db;
    $sth = $db->prepare("delete from CommPerPrivateKey where Account = :Account");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();

    $sth = $db->prepare("delete from CommPerRfKey where Account = :Account");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();

    $sth = $db->prepare("delete from AccessGroupDevice where AccessGroupID in(select AccessGroupID from AccountAccess where Account = :Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();

    $sth = $db->prepare("delete from AccessGroup where ID in(select AccessGroupID from AccountAccess where Account = :Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();

    $sth = $db->prepare("delete from AccountAccess where Account = :Account");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();

    $sth = $db->prepare("delete from UserAccessGroupDevice where UserAccessGroupID in(select ID from UserAccessGroup where Account = :Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();

    $sth = $db->prepare("delete from UserAccessGroup where Account = :Account");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $sth->execute();
    //print_r("clearAccountData end,account=$account\n");
}

function clearDeliveryAccess($deliveryID) {
    global $db;
    $sth = $db->prepare("delete from AccessGroup where ID in(select AccessGroupID from DeliveryAccess where DeliveryID = :DeliveryID)");
    $sth->bindParam(':DeliveryID', $deliveryID, PDO::PARAM_INT);
    $sth->execute();

    $sth = $db->prepare("delete from AccessGroupDevice where AccessGroupID in(select AccessGroupID from DeliveryAccess where DeliveryID = :DeliveryID)");
    $sth->bindParam(':DeliveryID', $deliveryID, PDO::PARAM_INT);
    $sth->execute();

    $sth = $db->prepare("delete from DeliveryAccess where DeliveryID = :DeliveryID");
    $sth->bindParam(':DeliveryID', $deliveryID, PDO::PARAM_INT);
    $sth->execute();
}

function addDefaultUserAccessGroup($communityId) {
    print_r("addDefaultUserAccessGroup begin.\n");
    
    global $db;
    $sth = $db->prepare("select b.Account from PersonalAccount b " .
                        " LEFT JOIN PersonalAccount t on b.ParentID = t.ID and b.Role in(11,21)" .
                        " WHERE ((b.role in(10,20) and b.ParentID = :CommunityID) or (b.role in(11,21) and t.ParentID = :CommunityID))");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dataList as $row => $data) {
        $data['Name'] = 'DefaultUserAccess-' . $data['Account'];
        $data['SchedulerType'] = SCHEDULE_EVERY_DAY;
        $data['DateFlag'] = 0;
        $data['BeginTime'] = '2021-01-01 00:00:00';
        $data['EndTime'] = '2299-01-01 00:00:00';
        $data['StartTime'] = '00:00:00';
        $data['StopTime'] = '23:59:59';
        insertUserAccessGroup($data);
    }
	
    print_r("addDefaultUserAccessGroup end.size=" . count($dataList) . "\n");
}

function addDefaultAccessGroup($communityId) {
    print_r("addDefaultAccessGroup begin.\n");
    global $db;
    $sth = $db->prepare("delete from AccessGroup where CommunityID = :CommunityID and UnitID > 0");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();    
    
    $sth = $db->prepare("select ID UnitID, concat('Resident-Building ', UnitName) Name from CommunityUnit where MngAccountID = :CommunityID");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);
    $cnt = count($dataList);
    foreach ($dataList as $row => $data) {
        $data['CommunityID'] = $communityId;
        $data['SchedulerType'] = SCHEDULE_EVERY_DAY;
        $data['DateFlag'] = 0;
        $data['BeginTime'] = '2021-01-01 00:00:00';
        $data['EndTime'] = '2299-01-01 00:00:00';
        $data['StartTime'] = '00:00:00';
        $data['StopTime'] = '23:59:59';
        insertAccessGroup($data);
    }    
    print_r("addDefaultAccessGroup end.size=" . $cnt . "\n");
}

function transferStaffData($communityId) {
    print_r("transferStaffData begin-----------------------------------------------------------------.\n");
    clearStaffData($communityId);
    transferCommonData($communityId, TRANSFER_TYPE_CARD, OWNER_STAFF);
    print_r("transferStaffData end.\n");
}

function transferDeliveryData($communityId) {
    print_r("transferDeliveryData begin-------------------------------------------------------------.\n");
    clearDeliveryData($communityId);
    transferCommonData($communityId, TRANSFER_TYPE_KEY, OWNER_DELIVERY);
    transferCommonData($communityId, TRANSFER_TYPE_CARD, OWNER_DELIVERY);
    print_r("transferDeliveryData end.\n");
}

function transferDeliveryKeyData($communityId) {
    print_r("transferDeliveryKeyData begin-------------------------------------------------------------.\n");
    clearDeliveryData($communityId);
    transferCommonData($communityId, TRANSFER_TYPE_KEY, OWNER_DELIVERY);
    print_r("transferDeliveryKeyData end.\n");
}

function transferDeliveryCardData($communityId) {
    print_r("transferDeliveryCardData begin-------------------------------------------------------------.\n");
    clearDeliveryData($communityId);
    transferCommonData($communityId, TRANSFER_TYPE_CARD, OWNER_DELIVERY);
    print_r("transferDeliveryCardData end.\n");
}

function transferUserData($communityId) {
    print_r("transferUserData begin-------------------------------------------------------------.\n");
    clearUserData($communityId, "AccountAccess");
    clearUserData($communityId, "UserAccessGroup");
    transferUserCommonData($communityId, TRANSFER_TYPE_KEY);
    transferUserCommonData($communityId, TRANSFER_TYPE_CARD);
    insertPublicSchedulerMap();
    insertUserSchedulerMap();
    print_r("transferUserData end.\n");
}

function transferUserKeyData($communityId) {
    print_r("transferUserKeyData begin-------------------------------------------------------------.\n");
    clearUserData($communityId, "AccountAccess");
    clearUserData($communityId, "UserAccessGroup");
    transferUserCommonData($communityId, TRANSFER_TYPE_KEY);
    insertPublicSchedulerMap();
    insertUserSchedulerMap();
    print_r("transferUserKeyData end.\n");
}

function transferUserCardData($communityId) {
    print_r("transferUserCardData begin-------------------------------------------------------------.\n");
    clearUserData($communityId, "AccountAccess");
    clearUserData($communityId, "UserAccessGroup");
    transferUserCommonData($communityId, TRANSFER_TYPE_CARD);
    insertPublicSchedulerMap();
    insertUserSchedulerMap();
    print_r("transferUserCardData end.\n");
}

function insertAccessGroupDeviceList($accessGroupDeviceList, $accessGroupID) {
    foreach ($accessGroupDeviceList as $row => $accessGroupDevice) {
        $accessGroupDevice['AccessGroupID'] = $accessGroupID;
        insertAccessGroupDevice($accessGroupDevice);
    }
}

function insertUserAccessGroupDeviceList($accessGroupDeviceList, $accessGroupID) {
    foreach ($accessGroupDeviceList as $row => $accessGroupDevice) {
        $accessGroupDevice['UserAccessGroupID'] = $accessGroupID;
        insertUserAccessGroupDevice($accessGroupDevice);
    }
}

function changeCode($code, $transferType, &$data) {
    if ($transferType == TRANSFER_TYPE_CARD) {
        $data['CardCode'] = $code;
    } else if ($transferType == TRANSFER_TYPE_KEY) {
        $data['PinCode'] = $code;
    } else {
        return;
    }
}

function insertAccountAccessList($dataList, $accessGroupID) {
    $codeMap = [];
    foreach ($dataList as $row => $data) {
        $code = $data['Account'];
        $data['AccessGroupID'] = $accessGroupID;
        if (array_key_exists($code, $codeMap)) {
            continue;
        }
        $codeMap[$code] = 1;
        insertAccountAccess($data);
    }
}

function insertDataList($dataList, $accessGroupID, $transferType, $ownerType) {
    $codeMap = [];
    foreach ($dataList as $row => $data) {
        $code = $data['Code'];
        if (array_key_exists($code, $codeMap)) {
            continue;
        }

        $codeMap[$code] = 1;
        changeCode($code, $transferType, $data);

        if ($ownerType == OWNER_STAFF) {
            $staffID = insertStaff($data);
            $data['StaffID'] = $staffID;
            $data['AccessGroupID'] = $accessGroupID;
            insertStaffAccess($data);
        } else if ($ownerType == OWNER_DELIVERY) {
            $deliveryID = insertDelivery($data);
            $data['DeliveryID'] = $deliveryID;
            $data['AccessGroupID'] = $accessGroupID;
            insertDeliveryAccess($data);
        } else {
            continue;
        }
    }
}

function insertSchedulerMap($transferType, $ownerType) {
    global $publicSchedulerMap;
    print_r("insertSchedulerMap begin,access_group size=" . count($publicSchedulerMap) . ",transfer_type=$transferType,owner_type=$ownerType\n");
    //writeFile("public_access_group_" . $transferType . $ownerType . ".json", json_encode($publicSchedulerMap));
    foreach ($publicSchedulerMap as $row => $schedulerData) {
        $accessGroupData = $schedulerData['AccessGroup'];
        if (array_key_exists('AccessGroupID', $accessGroupData) && $accessGroupData['AccessGroupID']) {
            $accessGroupID = $accessGroupData['AccessGroupID'];
        } else {
            $accessGroupData['Name'] = getAccessGroupName($accessGroupData);
            $accessGroupID = insertAccessGroup($accessGroupData);
            $publicSchedulerMap[$row]['AccessGroup']['AccessGroupID'] = $accessGroupID;
            $accessGroupDeviceList = $schedulerData['AccessGroupDevice'];
            insertAccessGroupDeviceList($accessGroupDeviceList, $accessGroupID);
        }

        $dataListKey = getDataListKey($ownerType);
        if (array_key_exists($dataListKey, $schedulerData)) {
            $dataList = $schedulerData[$dataListKey];
            if (count($dataList) > 0) {
                insertDataList($dataList, $accessGroupID, $transferType, $ownerType);
            }
        }
    }
    print_r("insertSchedulerMap end\n");
}

function insertPublicSchedulerMap() {
    global $publicSchedulerMap;
    print_r("insertPublicSchedulerMap begin,access_group size=" . count($publicSchedulerMap) . ".\n");
    //print_r("public scheduler map=\n" . json_encode($publicSchedulerMap) . "\n");
    foreach ($publicSchedulerMap as $row => $schedulerData) {
        $accessGroupData = $schedulerData['AccessGroup'];
        if (array_key_exists('AccessGroupID', $accessGroupData) && $accessGroupData['AccessGroupID']) {
            $accessGroupID = $accessGroupData['AccessGroupID'];
        } else {
            $accessGroupData['Name'] = getAccessGroupName($accessGroupData);
            $accessGroupID = insertAccessGroup($accessGroupData);
            $publicSchedulerMap[$row]['AccessGroup']['AccessGroupID'] = $accessGroupID;
            $accessGroupDeviceList = $schedulerData['AccessGroupDevice'];
            insertAccessGroupDeviceList($accessGroupDeviceList, $accessGroupID);
        }

        $dataListKey = getDataListKey(OWNER_USER);
        if (array_key_exists($dataListKey, $schedulerData)) {
            $dataList = $schedulerData[$dataListKey];
            if (count($dataList) > 0) {
                insertAccountAccessList($dataList, $accessGroupID);
            }
        }
    }
    print_r("insertPublicSchedulerMap end\n");
}

/**
 * 同一个Account只能有1条UserAccessGroup记录
 * 如果存在多条的情况,则合并UserAccessGroupDevice的数据
 * @global type $userSchedulerMap
 */
function insertUserSchedulerMap() {
    global $userSchedulerMap;
    print_r("insertUserSchedulerMap begin,access_group size=" . count($userSchedulerMap) . ".\n");
    //print_r("user scheduler map=\n" . json_encode($userSchedulerMap) . "\n");

    $accountMap = [];
    foreach ($userSchedulerMap as $row => $schedulerData) {
        $accessGroupData = $schedulerData['AccessGroup'];
        $accessGroupData['Name'] = getAccessGroupName($accessGroupData);
        $account = $accessGroupData['Account'];
        if (array_key_exists($account, $accountMap)) {
            $accessGroupID = $accountMap[$account];
            print_r("Attention:exits same User Access Group.Exist AccessGroupId=$accessGroupID,UserAccessGroupData=" . json_encode($accessGroupData) . "\n");
        } else {
            $accessGroupID = insertUserAccessGroup($accessGroupData);
            $accountMap[$account] = $accessGroupID;
        }

        $accessGroupDeviceList = $schedulerData['AccessGroupDevice'];
        insertUserAccessGroupDeviceList($accessGroupDeviceList, $accessGroupID);
    }
    print_r("insertUserSchedulerMap end\n");
}

function getSchedulerKey($data) {
    $schedulerType = $data['SchedulerType'];
    if ($schedulerType == SCHEDULE_ONCE) {
        global $ignoreEndTime;
        if ($ignoreEndTime == NEED_END_TIME) {
            return $schedulerType . '_' . $data['EndTime'];
        } else {
            return $schedulerType . '_';
        }
    } else if ($schedulerType == SCHEDULE_EVERY_DAY) {
        return $schedulerType . '_' . $data['StartTime'] . '_' . $data['StopTime'];
    } else if ($schedulerType == SCHEDULE_EVERY_WEEK) {
        return $schedulerType . '_' . $data['StartTime'] . '_' . $data['StopTime'] . '_' . $data['DateFlag'];
    } else {
        return "error=" . $schedulerType;
    }
}

function getFullKey($schedulerKey, $mac, $relay) {
    return $schedulerKey . '_' . $mac . '_' . $relay;
}

class LastCommonHandler {

    var $lastCode;
    var $lastKey;
    var $lastArray;
    var $lastAccessGroup;
    var $lastAccessGroupDevice;

    function __construct() {
        $this->lastCode = "";
        $this->lastKey = "";
        $this->lastArray = [];
        $this->lastAccessGroup = [];
        $this->lastAccessGroupDevice = [];
    }

    public function setLastCode($code) {
        $this->lastCode = $code;
    }

    public function getLastCode() {
        return $this->lastCode;
    }

    public function lastCodeChange($code) {
        return $code != $this->lastCode;
    }

    public function initArrayData($keyData) {
        $this->lastArray = [];
        $this->lastArray[] = $keyData;
        $this->lastAccessGroupDevice = [];
        $this->lastAccessGroupDevice[] = $keyData;
    }

    public function combine(&$schedulerMap, $ownerType) {
        $dataListKey = getDataListKey($ownerType);
        if (array_key_exists($this->lastKey, $schedulerMap)) {
            if (array_key_exists($dataListKey, $schedulerMap[$this->lastKey])) {
                $schedulerMap[$this->lastKey][$dataListKey] = array_merge($schedulerMap[$this->lastKey][$dataListKey], $this->lastArray);
            } else {
                $schedulerMap[$this->lastKey][$dataListKey] = $this->lastArray;
            }
        } else {
            $schedulerMap[$this->lastKey]['AccessGroup'] = $this->lastAccessGroup;
            $schedulerMap[$this->lastKey]['AccessGroupDevice'] = $this->lastAccessGroupDevice;
            $schedulerMap[$this->lastKey][$dataListKey] = $this->lastArray;
        }
    }

    public function setLastAccessGroup($schedulerKey, $keyData) {
        $this->lastAccessGroup = $keyData;
        $this->lastKey = getFullKey($schedulerKey, $keyData['MAC'], $keyData['Relay']);
    }

    public function handleSameCode($keyData) {
        $this->lastArray[] = $keyData;
        $this->lastKey = getFullKey($this->lastKey, $keyData['MAC'], $keyData['Relay']);
        $this->lastAccessGroupDevice[] = $keyData;
    }

}

/**
 *  
  TRANSFER_TYPE_CARD = 1;
  TRANSFER_TYPE_KEY = 0;
  OWNER_STAFF = 0;
  OWNER_DELIVERY = 1;
 * @global type $db
 * @param type $communityId
 * @param type $transferType
 * @param type $ownerType
 */
function transferCommonData($communityId, $transferType, $ownerType) {
    $keyTables = ["PubPrivateKey", "PubRfcardKey"];
    $listTables = ["PubPrivateKeyList", "PubRfcardKeyList"];

    global $db;
    $sth = $db->prepare("SELECT a.Code, a.CreateTime, a.Name, a.SchedulerType, a.DateFlag,
               a.BeginTime, a.EndTime, a.StartTime, a.StopTime, L.MAC, L.Relay, unix_timestamp(now()) Version, D.UnitID
        FROM " . $keyTables[$transferType] . " a
        	LEFT JOIN " . $listTables[$transferType] . " L ON a.ID = L.KeyID
        	LEFT JOIN Devices D ON L.MAC = D.MAC
        	LEFT JOIN CommunityUnit U ON U.ID = D.UnitID
        WHERE a.MngAccountID = :CommunityID
        	AND a.OwnerType = :OwnerType 
                AND (a.SchedulerType = 0 or a.SchedulerType = 1 or a.SchedulerType = 2)
                order by a.Code, L.MAC");
    print_r("transferCommonData begin,main_table=" . $keyTables[$transferType] . ";list_table=" . $listTables[$transferType] . "\n");

    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->bindParam(':OwnerType', $ownerType, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);

    global $publicSchedulerMap;
    $lastCommonHandler = new LastCommonHandler();

    print_r("transferCommonData size=" . count($dataList) . "\n");
    foreach ($dataList as $row => $keyData) {
        $keyData['CommunityID'] = $communityId;
        $code = $keyData['Code'];
        if (!$keyData['Code']) {
            continue;
        }

        if (!$keyData['MAC'] || is_null($keyData['UnitID'])) {
            changeCode($code, $transferType, $keyData);
            if ($ownerType == OWNER_STAFF) {
                insertStaff($keyData);
            } else if ($ownerType == OWNER_DELIVERY) {
                insertDelivery($keyData);
            } else {
                
            }
            continue;
        }
        //$keyData['EndTime'] = transferEndTime($keyData['SchedulerType'], $keyData['EndTime']);
        setDefaultBeginTimeEndTime($keyData);
        $keyData['UnitID'] = 0;

        //print_r("card_code=$cardCode,last_code=$lastCode,last_key=$lastKey\n");
        $schedulerKey = getSchedulerKey($keyData);
        if ($lastCommonHandler->lastCodeChange($code)) {
            if (!$lastCommonHandler->getLastCode()) {
                $lastCommonHandler->initArrayData($keyData);
            } else {
                $lastCommonHandler->combine($publicSchedulerMap, $ownerType);
                $lastCommonHandler->initArrayData($keyData);
            }
            $lastCommonHandler->setLastCode($code);
            $lastCommonHandler->setLastAccessGroup($schedulerKey, $keyData);
        } else {
            $lastCommonHandler->handleSameCode($keyData);
        }
    }

    //print_r("last_code=$lastCode,last_key=$lastKey,last_array=" . json_encode($lastArray) . "\n");
    if ($lastCommonHandler->getLastCode()) {
        $lastCommonHandler->combine($publicSchedulerMap, $ownerType);
    }

    insertSchedulerMap($transferType, $ownerType);
    //print_r(json_encode($schedulerMap) . "\n");
    print_r("transferCommonData end\n");
}

class LastUserHandler {

    var $lastAccount;
    var $lastCode;
    var $lastPublicKey;
    var $lastPublicArray;
    var $lastAccessGroup;
    var $lastAccessGroupDevice;
    var $lastUserKey;
    var $lastUserArray;
    var $lastUserAccessGroup;
    var $lastUserAccessGroupDevice;
    var $lastGrade;

    function __construct() {
        $this->lastAccount = "";
        $this->lastCode = "";
        $this->lastPublicKey = "";
        $this->lastPublicArray = [];
        $this->lastAccessGroup = [];
        $this->lastAccessGroupDevice = [];
        $this->lastUserKey = "";
        $this->lastUserArray = [];
        $this->lastUserAccessGroup = [];
        $this->lastUserAccessGroupDevice = [];
        $this->lastGrade = 0;
    }

    public function setLastCode($code) {
        $this->lastCode = $code;
    }

    public function getLastCode() {
        return $this->lastCode;
    }

    public function lastCodeChange($code) {
        return $code != $this->lastCode;
    }

    public function setLastGrade($grade) {
        $this->lastGrade = $grade;
    }

    public function initArrayData($grade, $keyData) {
        $this->lastPublicArray = [];
        $this->lastAccessGroupDevice = [];
        $this->lastUserArray = [];
        $this->lastUserAccessGroupDevice = [];
        if ($grade == COMMUNITY_DEVICE_TYPE_PUBLIC || $grade == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT) {
            $this->lastPublicArray[] = $keyData;
            $this->lastAccessGroupDevice[] = $keyData;
        } else {
            $this->lastUserArray[] = $keyData;
            $this->lastUserAccessGroupDevice[] = $keyData;
        }
    }

    public function combine(&$publicSchedulerMap, &$userSchedulerMap) {
        $dataListKey = getDataListKey(OWNER_USER);
        if ($this->lastPublicKey) {
            if (array_key_exists($this->lastPublicKey, $publicSchedulerMap)) {
                if (array_key_exists($dataListKey, $publicSchedulerMap[$this->lastPublicKey])) {
                    $publicSchedulerMap[$this->lastPublicKey][$dataListKey] = array_merge($publicSchedulerMap[$this->lastPublicKey][$dataListKey], $this->lastPublicArray);
                } else {
                    $publicSchedulerMap[$this->lastPublicKey][$dataListKey] = $this->lastPublicArray;
                }
            } else {
                $publicSchedulerMap[$this->lastPublicKey]['AccessGroup'] = $this->lastAccessGroup;
                $publicSchedulerMap[$this->lastPublicKey]['AccessGroupDevice'] = $this->lastAccessGroupDevice;
                $publicSchedulerMap[$this->lastPublicKey][$dataListKey] = $this->lastPublicArray;
            }
        }

        if ($this->lastUserKey) {
            if (array_key_exists($this->lastUserKey, $userSchedulerMap)) {
                if (array_key_exists($dataListKey, $userSchedulerMap[$this->lastUserKey])) {
                    $userSchedulerMap[$this->lastUserKey][$dataListKey] = array_merge($userSchedulerMap[$this->lastUserKey][$dataListKey], $this->lastUserArray);
                } else {
                    $userSchedulerMap[$this->lastUserKey][$dataListKey] = $this->lastUserArray;
                }
            } else {
                $userSchedulerMap[$this->lastUserKey]['AccessGroup'] = $this->lastUserAccessGroup;
                $userSchedulerMap[$this->lastUserKey]['AccessGroupDevice'] = $this->lastUserAccessGroupDevice;
                $userSchedulerMap[$this->lastUserKey][$dataListKey] = $this->lastUserArray;
            }
        }
    }

    public function setLastAccessGroup($grade, $schedulerKey, $keyData) {
        if ($grade == COMMUNITY_DEVICE_TYPE_PUBLIC || $grade == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT) {
            $this->lastPublicKey = getFullKey($schedulerKey, $keyData['MAC'], $keyData['Relay']);
            $this->lastAccessGroup = $keyData;
        } else {
            $this->lastUserKey = getFullKey($schedulerKey, $keyData['MAC'], $keyData['Relay']);
            $this->lastUserAccessGroup = $keyData;
        }
    }

    public function handleSameCode($grade, $schedulerKey, $keyData) {
        if ($grade == COMMUNITY_DEVICE_TYPE_PUBLIC || $grade == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT) {
            $this->lastPublicArray[] = $keyData;
            $this->lastAccessGroupDevice[] = $keyData;
            $this->lastPublicKey = getFullKey($this->lastPublicKey, $keyData['MAC'], $keyData['Relay']);
        } else {
            if ($grade != $this->lastGrade) {
                $this->lastUserKey = getFullKey($schedulerKey, $keyData['MAC'], $keyData['Relay']);
                $this->lastUserAccessGroup = $keyData;
            } else {
                $this->lastUserKey = getFullKey($this->lastUserKey, $keyData['MAC'], $keyData['Relay']);
            }
            $this->lastUserArray[] = $keyData;
            $this->lastUserAccessGroupDevice[] = $keyData;
        }
    }

}

/**
 * 存在权限扩大的已知问题
 * @global type $db
 * @global type $userSchedulerMap
 * @global type $publicSchedulerMap
 * @param type $communityId
 * @param type $transferType
 */
function transferUserCommonData($communityId, $transferType) {
    $personalTables = ["PersonalPrivateKey", "PersonalRfcardKey"];
    $personalListTables = ["PersonalPrivateKeyList", "PersonalRfcardKeyList"];

    global $db;
    $sth = $db->prepare("SELECT a.Code, a.CreateTime, a.SchedulerType, a.DateFlag, D.Grade, P.Account, P.Name, a.Special,
               a.BeginTime, a.EndTime, a.StartTime, a.StopTime, L.MAC, L.Relay, unix_timestamp(now()) Version, a.UnitID, U.UnitName, D.ID DeviceID
        FROM " . $personalTables[$transferType] . " a
        	LEFT JOIN " . $personalListTables[$transferType] . " L ON a.ID = L.KeyID
        	LEFT JOIN Devices D ON L.MAC = D.MAC
        	LEFT JOIN CommunityUnit U ON U.ID = a.UnitID
		LEFT JOIN PersonalAccount P ON a.AccountID = P.ID
        WHERE a.MngAccountID = :CommunityID
                AND (a.SchedulerType = 0 or a.SchedulerType = 1 or a.SchedulerType = 2)
                order by P.Account, a.Code, D.Grade, L.MAC;");
    print_r("transferPersonalCommonData begin,main_table=" . $personalTables[$transferType] . ";list_table=" . $personalListTables[$transferType] . "\n");

    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);

    global $userSchedulerMap;
    global $publicSchedulerMap;
    $codeMap = [];
    $lastHandler = new LastUserHandler();

    print_r("transferPersonalCommonData size=" . count($dataList) . "\n");
    foreach ($dataList as $row => $keyData) {
        $keyData['CommunityID'] = $communityId;
        $code = $keyData['Code'];
        if (!$code) {
            continue;
        }

        if ($transferType == TRANSFER_TYPE_KEY && !array_key_exists($code, $codeMap)) {
            insertCommPerPrivateKey($keyData);
        } else if ($transferType == TRANSFER_TYPE_CARD && !array_key_exists($code, $codeMap)) {
            insertCommPerRfKey($keyData);
        } else {
            
        }
        $codeMap[$code] = 1;

        if (!$keyData['MAC'] || !$keyData['DeviceID']) {
            continue;
        }

        //$keyData['EndTime'] = transferEndTime($keyData['SchedulerType'], $keyData['EndTime']);
        setDefaultBeginTimeEndTime($keyData);
        $keyData['UnitID'] = 0;

        $schedulerKey = getSchedulerKey($keyData);
        $grade = $keyData['Grade'];

        if ($lastHandler->lastCodeChange($code)) {
            if (!$lastHandler->getLastCode()) {
                $lastHandler->initArrayData($grade, $keyData);
            } else {
                $lastHandler->combine($publicSchedulerMap, $userSchedulerMap);
                $lastHandler->initArrayData($grade, $keyData);
            }
            $lastHandler->setLastAccessGroup($grade, $schedulerKey, $keyData);
            $lastHandler->setLastCode($code);
        } else {
            $lastHandler->handleSameCode($grade, $schedulerKey, $keyData);
        }
        $lastHandler->setLastGrade($grade);
    }

    if ($lastHandler->getLastCode()) {
        $lastHandler->combine($publicSchedulerMap, $userSchedulerMap);
    }

    print_r("transferPersonalCommonData end\n");
}

function insertAccountAccess($data) {
    if (!is_array($data) || count($data) == 0) {
        print_r("insertAccountAccess check failed,data=" . json_encode($data) . "\n");
        return;
    }

    global $db;
    try {
        $sth = $db->prepare("insert into AccountAccess(Account,AccessGroupID) values(:Account,:AccessGroupID)");
        $sth->bindParam(':Account', $data['Account'], PDO::PARAM_STR);
        $sth->bindParam(':AccessGroupID', $data['AccessGroupID'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertAccountAccess data=" . json_encode($data) . "\n");
        print_r("insertAccountAccess exception=" . $e->getMessage() . "\n");
    }
}

function checkIntValue($data, $key) {
    if (!$data[$key] || $data[$key] <= 0) {
        return false;
    }

    return true;
}

function checkStrValueNotNull($data, $key) {
    if (!$data[$key] || strlen($data[$key]) == 0) {
        return false;
    }

    return true;
}

function getLastInsertID() {
    global $db;
    $sth = $db->prepare("select last_insert_id() ID");
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        return $data['ID'];
    }

    return -1;
}

function insertDelivery($data) {
    if (!is_array($data) || count($data) == 0 || !checkStrValueNotNull($data, 'Name')) {
        print_r("insertDelivery check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into Delivery(Name,CommunityID,PinCode,CardCode,CreateTime,Version) values(:Name,:CommunityID,:PinCode,:CardCode,:CreateTime,:Version)");
        $sth->bindParam(':Name', $data['Name'], PDO::PARAM_STR);
        $sth->bindParam(':CommunityID', $data['CommunityID'], PDO::PARAM_INT);
        $sth->bindParam(':PinCode', $data['PinCode'], PDO::PARAM_STR);
        $sth->bindParam(':CardCode', $data['CardCode'], PDO::PARAM_STR);
        $sth->bindParam(':CreateTime', $data['CreateTime'], PDO::PARAM_STR);
        $sth->bindParam(':Version', $data['Version'], PDO::PARAM_INT);
        $sth->execute();

        return getLastInsertID();
    } catch (PDOException $e) {
        print_r("insertDelivery data=" . json_encode($data) . "\n");
        print_r("insertDelivery exception=" . $e->getMessage() . "\n");
    }
}

function insertDeliveryAccess($data) {
    if (!is_array($data) || count($data) == 0 || !checkIntValue($data, 'DeliveryID') || !checkIntValue($data, 'AccessGroupID')) {
        print_r("insertDeliveryAccess check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into DeliveryAccess(DeliveryID,AccessGroupID) values(:DeliveryID,:AccessGroupID)");
        $sth->bindParam(':DeliveryID', $data['DeliveryID'], PDO::PARAM_INT);
        $sth->bindParam(':AccessGroupID', $data['AccessGroupID'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertDeliveryAccess data=" . json_encode($data) . "\n");
        print_r("insertDeliveryAccess exception=" . $e->getMessage() . "\n");
    }
}

function insertStaff($data) {
    if (!is_array($data) || count($data) == 0) {
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into Staff(Name,CommunityID,CardCode,CreateTime,Version) values(:Name,:CommunityID,:CardCode,:CreateTime,:Version)");
        $sth->bindParam(':Name', $data['Name'], PDO::PARAM_STR);
        $sth->bindParam(':CommunityID', $data['CommunityID'], PDO::PARAM_INT);
        $sth->bindParam(':CardCode', $data['CardCode'], PDO::PARAM_STR);
        $sth->bindParam(':CreateTime', $data['CreateTime'], PDO::PARAM_STR);
        $sth->bindParam(':Version', $data['Version'], PDO::PARAM_INT);
        $sth->execute();

        return getLastInsertID();
    } catch (PDOException $e) {
        print_r("insertStaff data=" . json_encode($data) . "\n");
        print_r("insertStaff exception=" . $e->getMessage() . "\n");
    }
}

function insertStaffAccess($data) {
    if (!is_array($data) || count($data) == 0) {
        print_r("insertStaffAccess check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into StaffAccess(StaffID,AccessGroupID) values(:StaffID,:AccessGroupID)");
        $sth->bindParam(':StaffID', $data['StaffID'], PDO::PARAM_INT);
        $sth->bindParam(':AccessGroupID', $data['AccessGroupID'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertStaffAccess data=" . json_encode($data) . "\n");
        print_r("insertStaffAccess exception=" . $e->getMessage() . "\n");
    }
}

function insertAccessGroup($data) {
    if (!is_array($data) || count($data) == 0) {
        print_r("insertAccessGroup check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into AccessGroup(Name,CreateTime,CommunityID,UnitID,SchedulerType,DateFlag,BeginTime,EndTime,StartTime,StopTime) "
                . "values(:Name,now(),:CommunityID,:UnitID,:SchedulerType,:DateFlag,:BeginTime,:EndTime,:StartTime,:StopTime)");
        $sth->bindParam(':Name', $data['Name'], PDO::PARAM_STR);
        $sth->bindParam(':CommunityID', $data['CommunityID'], PDO::PARAM_INT);
        $sth->bindParam(':UnitID', $data['UnitID'], PDO::PARAM_INT);
        $sth->bindParam(':SchedulerType', $data['SchedulerType'], PDO::PARAM_INT);
        $sth->bindParam(':DateFlag', $data['DateFlag'], PDO::PARAM_INT);
        $sth->bindParam(':BeginTime', $data['BeginTime'], PDO::PARAM_STR);
        $sth->bindParam(':EndTime', $data['EndTime'], PDO::PARAM_STR);
        $sth->bindParam(':StartTime', $data['StartTime'], PDO::PARAM_STR);
        $sth->bindParam(':StopTime', $data['StopTime'], PDO::PARAM_STR);
        $sth->execute();

        return getLastInsertID();
    } catch (PDOException $e) {
        print_r("insertAccessGroup data=" . json_encode($data) . "\n");
        print_r("insertAccessGroup exception=" . $e->getMessage() . "\n");
    }
}

function insertAccessGroupDevice($data) {
    if (!is_array($data) || count($data) == 0 || !checkIntValue($data, 'AccessGroupID') || !checkStrValueNotNull($data, 'MAC')) {
        print_r("insertAccessGroupDevice check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into AccessGroupDevice(AccessGroupID,MAC,Relay) values(:AccessGroupID,:MAC,:Relay)");
        $sth->bindParam(':AccessGroupID', $data['AccessGroupID'], PDO::PARAM_INT);
        $sth->bindParam(':MAC', $data['MAC'], PDO::PARAM_STR);
        $sth->bindParam(':Relay', $data['Relay'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertAccessGroupDevice data=" . json_encode($data) . "\n");
        print_r("insertAccessGroupDevice exception=" . $e->getMessage() . "\n");
    }
}

function insertUserAccessGroup($data) {
    if (!is_array($data) || count($data) == 0) {
        print_r("insertUserAccessGroup check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into UserAccessGroup(Account,SchedulerType,DateFlag,BeginTime,EndTime,StartTime,StopTime) "
                . "values(:Account,:SchedulerType,:DateFlag,:BeginTime,:EndTime,:StartTime,:StopTime)");
        $sth->bindParam(':Account', $data['Account'], PDO::PARAM_STR);
        $sth->bindParam(':SchedulerType', $data['SchedulerType'], PDO::PARAM_INT);
        $sth->bindParam(':DateFlag', $data['DateFlag'], PDO::PARAM_INT);
        $sth->bindParam(':BeginTime', $data['BeginTime'], PDO::PARAM_STR);
        $sth->bindParam(':EndTime', $data['EndTime'], PDO::PARAM_STR);
        $sth->bindParam(':StartTime', $data['StartTime'], PDO::PARAM_STR);
        $sth->bindParam(':StopTime', $data['StopTime'], PDO::PARAM_STR);
        $sth->execute();

        return getLastInsertID();
    } catch (PDOException $e) {
        print_r("insertUserAccessGroup data=" . json_encode($data) . "\n");
        print_r("insertUserAccessGroup exception=" . $e->getMessage() . "\n");
    }
}

function insertUserAccessGroupDevice($data) {
    if (!is_array($data) || count($data) == 0 || !$data['UserAccessGroupID']) {
        print_r("insertUserAccessGroupDevice check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("select ID from UserAccessGroupDevice 
                              where UserAccessGroupID = :UserAccessGroupID
                               and MAC = :MAC 
                               and Relay = :Relay");
        $sth->bindParam(':UserAccessGroupID', $data['UserAccessGroupID'], PDO::PARAM_INT);
        $sth->bindParam(':MAC', $data['MAC'], PDO::PARAM_STR);
        $sth->bindParam(':Relay', $data['Relay'], PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            return;
        }

        $sth = $db->prepare("insert into UserAccessGroupDevice(UserAccessGroupID,MAC,Relay) values(:UserAccessGroupID,:MAC,:Relay)");
        $sth->bindParam(':UserAccessGroupID', $data['UserAccessGroupID'], PDO::PARAM_INT);
        $sth->bindParam(':MAC', $data['MAC'], PDO::PARAM_STR);
        $sth->bindParam(':Relay', $data['Relay'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertUserAccessGroupDevice data=" . json_encode($data) . "\n");
        print_r("insertUserAccessGroupDevice exception=" . $e->getMessage() . "\n");
    }
}

function insertCommPerRfKey($data) {
    if (!is_array($data) || count($data) == 0) {
        print_r("insertCommPerRfKey check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into CommPerRfKey(Account,Code,CreateTime,CommunityID) values(:Account,:Code, now(), :CommunityID)");
        $sth->bindParam(':Account', $data['Account'], PDO::PARAM_STR);
        $sth->bindParam(':Code', $data['Code'], PDO::PARAM_STR);
        $sth->bindParam(':CommunityID', $data['CommunityID'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertCommPerRfKey data=" . json_encode($data) . "\n");
        print_r("insertCommPerRfKey exception=" . $e->getMessage() . "\n");
    }
}

function insertCommPerPrivateKey($data) {
    if (!is_array($data) || count($data) == 0) {
        print_r("insertCommPerPrivateKey check failed,data=" . json_encode($data) . "\n");
        return;
    }

    try {
        global $db;
        $sth = $db->prepare("insert into CommPerPrivateKey(Account,Code,CreateTime,CommunityID,Special) values(:Account,:Code, now(), :CommunityID, :Special)");
        $sth->bindParam(':Account', $data['Account'], PDO::PARAM_STR);
        $sth->bindParam(':Code', $data['Code'], PDO::PARAM_STR);
        $sth->bindParam(':CommunityID', $data['CommunityID'], PDO::PARAM_INT);
        $sth->bindParam(':Special', $data['Special'], PDO::PARAM_INT);
        $sth->execute();
    } catch (PDOException $e) {
        print_r("insertCommPerPrivateKey data=" . json_encode($data) . "\n");
        print_r("insertCommPerPrivateKey exception=" . $e->getMessage() . "\n");
    }
}

function transferAllCommunityData() {
    global $db;
    $sth = $db->prepare("select AccountID,IsNew from CommunityInfo");
    $sth->execute();
    $dataList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dataList as $row => $data) {
        $communityId = $data['AccountID'];
        global $userSchedulerMap;
        global $publicSchedulerMap;
        $userSchedulerMap = [];
        $publicSchedulerMap = [];
        transferOldCommunityData($communityId, $data);
    }
}

function clearCommunityData($communityId) {
    clearDeliveryData($communityId);
    clearStaffData($communityId);
    clearUserData($communityId, "AccountAccess");
    clearUserData($communityId, "UserAccessGroup");
}

function changeIsNewFlagToOld($communityId) {
    global $db;
    $sth = $db->prepare("update CommunityInfo set IsNew = 0 where AccountID = :CommunityID");
    $sth->bindParam(':CommunityID', $communityId, PDO::PARAM_INT);
    $sth->execute();
    print_r("changeIsNewFlagToOld success.\n");
}

function transferOldCommunityData($communityId, $communityData) {
    print_r("transferOldCommunityData begin,community_id=[$communityId]###############################################\n");

    if ($communityData['IsNew'] == NEW_COMMUNITY) {
        print_r("This Community=$communityId is already user concept community.\n");
        return;
    }

    transferDeliveryData($communityId);
    transferStaffData($communityId);
    transferUserData($communityId);
    addDefaultAccessGroup($communityId);
    addDefaultUserAccessGroup($communityId);
    updateCommunityToNew($communityId);
    print_r("transferOldCommunityData success.#######################################################################\n");
}

function showHelp() {
    print_r("Usage:  php transferToUserConcept.php [community_id] [ignore_end_time] [option...] {all|delivery|delivery_key|delivery_card|staff|user|user_key|user_card|clear|restore}\n");
    print_r("\t[community_id],if value=-1 transfer all community data;else transfer one community data.\n");
    print_r("\t[ignore_end_time], if value=0 need end time;else value=1 ignore end time in once schedule.\n");
    print_r("\tall, transfer all data.\n");
    print_r("\tdelivery, transfer only delivery data.\n");
    print_r("\tdelivery_key, transfer only delivery key data.\n");
    print_r("\tdelivery_card, transfer only delivery card data.\n");
    print_r("\tstaff, transfer only staff card data.\n");
    print_r("\tuser, transfer only user data.\n");
    print_r("\tuser_key, transfer only user key data.\n");
    print_r("\tuser_card, transfer only user card data.\n");
    print_r("\tclear, clear generated community data.\n");
    print_r("\trestore, resotre community is_new flag to old community.\n");
    exit(-1);
}

if ($argc == 4) {
    $communityId = $argv[1];
    $communityData = getCommunityInfo($communityId);
    if (!$communityData) {
        print_r("This Community=$communityId does not exist.\n");
        return;
    }
    global $ignoreEndTime;
    $ignoreEndTime = $argv[2];

    $option = $argv[3];
    if ($option == 'all') {
        transferOldCommunityData($communityId, $communityData);
    } else if ($option == 'delivery') {
        transferDeliveryData($communityId);
    } else if ($option == 'delivery_key') {
        transferDeliveryKeyData($communityId);
    } else if ($option == 'delivery_card') {
        transferDeliveryCardData($communityId);
    } else if ($option == 'staff') {
        transferStaffData($communityId);
    } else if ($option == 'user') {
        transferUserData($communityId);
    } else if ($option == 'user_key') {
        transferUserKeyData($communityId);
    } else if ($option == 'user_card') {
        transferUserCardData($communityId);
    } else if ($option == 'clear') {
        clearCommunityData($communityId);
    } else if ($option == 'restore') {
        changeIsNewFlagToOld($communityId);
    } else {
        showHelp();
        return;
    }
    writeJsonFile();
} else if ($argc == 3) {
    global $ignoreEndTime;
    $ignoreEndTime = $argv[2];

    if ($argv[1] == -1) {
		//很危险，注释掉
        //transferAllCommunityData();
    } else {
        showHelp();
    }
} else {
    showHelp();
}

$db->commit();

?>
