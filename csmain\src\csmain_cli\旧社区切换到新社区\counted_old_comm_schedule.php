<?php
date_default_timezone_set("PRC");

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}


$mngid=0;
if ($argc == 2) {
    $mngid = $argv[1];
}
else
{
	$php = $argv[0];
	print_r("Usage:  php $php [community_id]\n");
	exit(1);
}

//MngAccountID | UnitID | Grade | Type | Code | Status | Node       | CreateTime          | ExpireTime | Access | AccountID | Special | SchedulerType |// DateFlag | BeginTime           | EndTime             | StartTime | StopTime

//ID  | MAC          | KeyID | Relay  
function clearCapture($table, $tableList, $MngAccountID)
{

    $db = getDB();
    $sth = $db->prepare("select * From $table where MngAccountID=$MngAccountID  order by MngAccountID,SchedulerType;");
    $sth->execute();
    $key_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    $counted = array();
    $mng = 0;
    foreach ($key_list as $row => $key)
    {
        $ID = $key['ID'];
        if ($mng != $key['MngAccountID'])
        {
            $mng = $key['MngAccountID'];
            $counted[$mng] =array();
        }

        $time = array();
        $time["BeginTime"] = $key['BeginTime'];
        $time["EndTime"] = $key['EndTime'];
        $time["DateFlag"] = $key['DateFlag'];
        $time["StartTime"] = $key['StartTime'];
        $time["StopTime"] = $key['StopTime'];  
        $time["SchedulerType"] = $key['SchedulerType'];  

        //0:单次计划; 1:每日计划；2:周计划
        $str_time = "";
        $SchedulerType = $key['SchedulerType'];
        if ($SchedulerType == 0)
        {
            //$str_time=$SchedulerType.$key['BeginTime'].$key['EndTime'];
            $time = $key['EndTime'];
            if(strtotime("2023-10-10")<strtotime($time)){
                $time = "2038-1-1 00:00:00";//结束时间对齐、不计算开始时间
            }
            $str_time=$SchedulerType." ".$time;/*2030-10-06 10:34:59*/

        }
        else if ($SchedulerType == 1)
        {
            $str_time=$SchedulerType." ".$key['StartTime']." ".$key['StopTime'];
        }
        else if ($SchedulerType == 2)
        {
            $str_time=$SchedulerType." ".$key['DateFlag']." ".$key['StartTime']." ".$key['StopTime'];
        }
        else
        {
            echo "$SchedulerType error\n";
        }

        $sth2 = $db->prepare("select * From $tableList  where KeyID=:KeyID order by MAC,Relay;");
        $sth2->bindParam(':KeyID', $ID, PDO::PARAM_STR);
        $sth2->execute();
        $mac_list = $sth2->fetchALL(PDO::FETCH_ASSOC);
        $str_key = "";
        foreach ($mac_list as $row => $mac)
        {
            $str_key=$str_key.$mac["MAC"]." ".$mac["Relay"]." ";
        }
        $str = $str_time." ".$str_key;

        $var = array();
        $var["time"] = $time;
        $var["mac_list"] = $mac_list;
        $var["str"] = $str;
        array_push($counted[$mng], $var);
    }
    $select = array();
    foreach ($counted as $mng => $vals)
    {
        $check  = array();
		$select[$mng] =array();
        foreach ($vals as  $val)
        {
			$str = $val["str"];
			$check[$str] = 1;
        }
		array_push($select[$mng], $check);
    }
    foreach ($select as $key => $mngs)
    {
		$count = count($mngs[0]);
        //echo "mng:".$key." counted:".count($mngs[0])."\n";
        if ($count > 5)
        {
			echo "mng:".$key." counted:".count($mngs[0])."\n";
            foreach ($mngs[0] as $key2 => $val)
            {
                echo $key2."\n";
            }
        }
    }
	return $select;
    #var_dump($select);
}


echo "第一列 0:单次计划; 1:每日计划；2:周计划=========================================Rf Key\n";
$rf_ret = clearCapture("PubRfcardKey", "PubRfcardKeyList", $mngid);
echo "第一列 0:单次计划; 1:每日计划；2:周计划=========================================PIN\n";
$pri_ret = clearCapture("PubPrivateKey", "PubPrivateKeyList", $mngid);

echo "================================Counted=========================================\n";
$db = getDB();
$sth = $db->prepare("select ID From Account where Grade=21;");
$sth->execute();
$comm_list = $sth->fetchALL(PDO::FETCH_ASSOC);


foreach ($comm_list as $row => $comm)
{
	$mng_id = $comm["ID"];
	$str = "mng:".$mng_id;
	$rf_count = 0;
	$pin_count = 0;
	if (array_key_exists($mng_id, $rf_ret))
	{
		$rf_count = count($rf_ret[$mng_id][0]);
		$str = $str." rf counted:$rf_count";
	}
	if ( array_key_exists($mng_id, $pri_ret))
	{
		$pin_count = count($pri_ret[$mng_id][0]);
		$str = $str." pin counted:$pin_count";
	}
	if (strlen($str) > 8)
	{
		//echo $str."\n";
	}
	if ($rf_count + $pin_count > 5)
	{
		echo $str."\n";
	}
}	





?>

