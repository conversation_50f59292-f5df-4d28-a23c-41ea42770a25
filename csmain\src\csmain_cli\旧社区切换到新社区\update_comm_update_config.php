<?php
#使用前 Update AppPushToken SET LandlineNotifyStatus = 0

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$mngid=0;
if ($argc == 2) {
    $mngid = $argv[1];
}
else
{
	$php = $argv[0];
	print_r("Usage:  php $php [community_id]\n");
	exit(1);
}

$db = getDB();
$sth = $db->prepare("select MAC From Devices where MngAccountID=$mngid and type in(1,0,50);");

$sth->execute();
$alist = $sth->fetchALL(PDO::FETCH_ASSOC);


foreach ($alist as $row => $mac)
{
	$mac = $mac["MAC"];
	shell_exec("python3.4 /bin/configcat updatemac $mac");
	sleep(2);
}