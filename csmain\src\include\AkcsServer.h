#ifndef _AKCS_SERVER_H_
#define _AKCS_SERVER_H_

#ifdef __cplusplus
extern "C" {
#endif
const std::string ACC_TCP_BUSSINESS = "csmain_tcp";
const uint32_t ACC_TCP_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t ACC_TCP_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
const uint32_t ACC_TCP_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s
const std::string AKCS_ATTACK_IP_TUBE = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用
const uint32_t AKCS_ATTACK_IP_RELEASE = 172800; //2天后将攻击者的ip从iptables列表中删除掉,60 * 60 * 24 *2s，延迟时间长度
const std::string AKCS_DEV_OFFLINE = "akcs_dev_offline"; //beanstalkd的tube,专用于存放离线设备的社区ID
const uint32_t AKCS_DEV_OFFLINE_DELAY = 600; //设备离线延迟时间

//add by chenzhx
const uint32_t AKCS_REMOTE_DEV_CONTORL_SSH_PORT = 1300; //远程设备控制，本地sshd端口 1301是对接api端口 1302-1399是ssh远程端口

uint32_t ConfInit();
void MsgRateLimitConfInit();
void ConfSrvInit();
int DaoInit();
void SignalInit();
uint32_t ControlInit();
uint32_t DaoRelease();
uint32_t ConfReInitPushServer();
void EtcdSrvInit();
void EtcdRegCsmainOuterInfo();
void EtcdRegCsmainInnerInfo();
void VSRPCClientInit();
void SMRPCClientInit();
void MQProduceInit();
void UpdateOuterConfFromConfSrv();
void ServerTagInit();
int LogDeliveryInit();

#ifdef __cplusplus
}
#endif

#endif //_AKCS_SERVER_H_
