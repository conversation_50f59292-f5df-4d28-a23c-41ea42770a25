/** @file ipc.h
 * @brief ipc module interface
 * @date 2012-12-14
 * @note
 *
 *
 * Copyright(c) 2012-2020 Xiamen Ringslink telenology Co,.Ltd
 */

#ifndef __IPC_H__
#define __IPC_H__


#ifndef TRUE
#define TRUE    1
#endif

#ifndef FALSE
#define FALSE   0
#endif

#ifndef RL_TRUE
#define RL_TRUE 1
#endif

#ifndef RL_FALSE
#define RL_FALSE    0
#endif

#ifndef BOOL
typedef int                 BOOL;
#endif

enum
{
    IPC_ID_MIN = 0,
    IPC_ID_CLOUD,
    IPC_ID_VRTPSD,
    IPC_ID_VRECORD,
    IPC_ID_VIDEO, //视频存储服务器的unix_socket交互
    IPC_ID_MAX,
};

//VRTSPD 发给 CLOUD
#define MSG_VRTSP2C                 0xA000
#define MSG_VRTSP2C_START_RTSP      (MSG_VRTSP2C + 1)
#define MSG_VRTSP2C_STOP_RTSP       (MSG_VRTSP2C + 2)
//VRTSP主动发送心跳给监控设备
#define MSG_VRTSP2C_KEEP_RTSP    (MSG_VRTSP2C + 3)
//接入服务器发送app执行截图的信令给vrtsp,数据流如下:app->csmain->vrtsp

#define MSG_C2VRTSP              0xA100
#define MSG_C2VRTSP_CAPTURE_RTSP (MSG_C2VRTSP + 1)

//CSMAIN跟视频存储服务的交互
#define MSG_MAIN2VIDEO              0xA400
#define MSG_MAIN2VIDEO_START_STORAGE (MSG_MAIN2VIDEO + 1)
#define MSG_MAIN2VIDEO_STOP_STORAGE  (MSG_MAIN2VIDEO + 2)

#define MSG_VIDEO2MAIN              0xA500

typedef struct VIDEO_STORAGE_T
{
#define UID_SIZE        24
#define NODE_SIZE       32
    char uid[UID_SIZE];      //uid=mac_timestamp(时间戳精确到s)
    char node[NODE_SIZE];    //联动系统
} VIDEO_STORAGE;


#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    int len;
    int id;
    int from;  /*信息发端进程的ID*/
    int param1;
    int param2;

#define IPC_MSG_HEADER_SIZE 20
#define IPC_MSG_DATA_SIZE   4096  /*v4.0 2014->4096为了传qr的图片*/
#define IPC_MSG_MAX_SIZE    (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)

    unsigned char data[IPC_MSG_DATA_SIZE];
} UNIX_IPC_MSG;

void ipc_show_version();

/**
 * initialize IPC module
 */
void ipc_init();


/**
 * register a process to IPC manager
 *
 * @param[in] my_id -- process ID
 *
 * @return 0 on success, others on failed.
 */
int ipc_register(int my_id);

/**
 * thread function
 *
 * @param[in] listen_fd -- socket listen ID
 *
 * @return 0 on success, others on failed.
 */
void* read_thread(int listen_fd);


typedef void (*IPC_MSG_HANDLE)(UNIX_IPC_MSG* msg, void* data);


/**
 * run a process to IPC listen
 *
 * @param[in] msg_handle -- message process handle
 * @param[in] data -- private data
 *
 * @return 0 on success, others on failed.
 */
int ipc_run(IPC_MSG_HANDLE msg_handle, void* data);

/**
 * unregister a process to IPC manager
 *
 * @param[in] id -- process ID name
 *
 * @return 0 on success, others on failed.
 */
int ipc_unregister();


/**
 * send a ipc message
 *
 * @param[in] dest_id -- destination process ID
 * @param[in] id -- ipc message id
 * @param[in]param1 – first param  for ipc message
 * @param[in]param2 – second param for ipc message
 * @param[in]dat – extern data ptr
 * @param[in]dat_len – extern data size
 *
 * @return 0 on success, others on failed.
 */
int ipc_send(int dest_id, int id, int param1, int param2, void* dat, int dat_len);

/**
 * get the quit flag to see if we need to quit the process
 *
 * @param[in] none
 *
 * @return the quitflag. RL_TRUE if needs to quit. RL_FALSE if not.
 */
BOOL get_quitflag();

/**
 * set the quit flag. RL_TRUE means quit; RL_FALSE means not
 *
 * @param[in] quit flag
 *
 * @return none.
 */
void set_quitflag(BOOL quitflag);

#ifdef __cplusplus
}
#endif

#endif

