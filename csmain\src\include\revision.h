#ifndef __REVISION_H__
#define __REVISION_H__

/*************************************************************
*  New version  : 1.0.0.1
*  Old version  : 无
*  Author       : Dave/Dave
*  CommitType   : 其他
*  Date         : 2017/01/05
*  Reason       : 1)linux下的云平台首次上库
*  Modified     : 1)移植sdmc代码到linux下的云平台
*  Issue        :
*  Affected     : 无
*************************************************************/

/*************************************************************
*  New version  : 1.0.0.2
*  Old version  : 无
*  Author       : Dave/Dave
*  CommitType   : 其他
*  Date         : 2017/01/10
*  Reason       : 1)增加App与云平台的通信
*  Modified     : 1)增加App与云平台的通信
*  Issue        :
*  Affected     : 无
*************************************************************/

/*************************************************************
*  New version  : 1.0.0.3
*  Old version  : 无
*  Author       : Dave/Dave
*  CommitType   : 其他
*  Date         : 2017/03/02
*  Reason       : 1)首次与csadpt联调完成
*  Modified     : 1)首次与csadpt联调完成
*  Issue        :
*  Affected     : 无
*************************************************************/


/*************************************************************
*  New version  : 1.0.0.4
*  Old version  : 无
*  Author       : Dave/Dave
*  CommitType   : 其他
*  Date         : 2017/05/02
*  Reason       : 1)v1.0版本第一轮测试结束提交,其中重要的修改有：重写cstring、引入数据库连接池
*  Modified     : 1)v1.0版本第一轮测试结束提交
*  Issue        :
*  Affected     : 无
*************************************************************/

#define MOD_VERSION                 "1.0.0.1"
#define MOD_NAME                     "cloudserver"

/*工程升级模式*/
#define RL_OPEN_UPGRADE_MODE        0

#endif