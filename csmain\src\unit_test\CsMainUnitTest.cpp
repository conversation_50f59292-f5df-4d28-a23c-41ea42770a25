﻿#define CATCH_CONFIG_MAIN
#include <catch2/catch.hpp>
#include <catch2/catch_reporter_teamcity.hpp>
#include <catch2/catch_reporter_tap.hpp>
#include <catch2/catch_reporter_sonarqube.hpp>

/**
 * 该文件导入全局变量,函数以及main,无其他作用
 */
#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <signal.h>
#include <unistd.h>
#include <sys/stat.h>
#include <errno.h>
#include <signal.h>
#include "Control.h"
#include "AkcsServer.h"
#include "HttpServer.h"
#include <evpp/tcp_client.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "PushClient.h"
#include "rpc_client.h"
#include "VideoSchedMng.h"
#include "csmainserver.h"
#include "CliServer.h"
#include "DeviceControl.h"
#include "util.h"
#include "session_rpc_client.h"
#include "AKUserMng.h"
#include "CachePool.h"
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "grpc_balancer_service.h"
#include "csmain_rpc_server.h"
#include "EtcdCliMng.h"
#include "RouteMqProduce.h"
#include "ConfigFileReader.h"
#include "evpp/rate_limiter/rate_limiter_token_bucket.h"
#include "EventFilterWriteFileImpl.h"


AccessServer* g_accSer_ptr = nullptr;
CliServer*  g_cliSer_prt = nullptr;

AKCS_CONF gstAKCSConf; //全局配置信息
VideoStorageClient* g_vs_client_ptr = nullptr;
SmRpcClient* g_sm_client_ptr = nullptr;
std::string g_logic_srv_id;
Beanstalk::Client* g_beanstalkd_client_ptr = nullptr;
bool g_etcd_ready = 0;
bool g_access_srv_ready = 0;

evpp::rate_limiter::RateLimiterTokenBucketInterface *g_rate_limiter = nullptr;
akcs::CEventFilterInterface *g_event_filter = nullptr;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
RouteMQProduce* g_nsq_producer = nullptr;

static evpp::PipeEventWatcher* ev_csroute = nullptr;
static evpp::PipeEventWatcher* ev_csadapt = nullptr;
static evpp::PipeEventWatcher* ev_cssession = nullptr;
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);
static int64_t csmain_inner_srv_leaseid = 0;
static int64_t csmain_outer_srv_leaseid = 0;
std::string g_csadapt_client_ip = ""; //ip类型，没有port
std::map<string, AKCS_DST> g_time_zone_DST;  


uint32_t ConfReInitPushServer()
{
    CConfigFileReader config_file("/usr/local/akcs/csmain/conf/csmain.conf");

    Snprintf(gstAKCSConf.push_server_addr, sizeof(gstAKCSConf.push_server_addr), config_file.GetConfigName("cspush_net"));
    Snprintf(gstAKCSConf.oem_name, sizeof(gstAKCSConf.oem_name), config_file.GetConfigName("oem_name"));
    if (!strcasecmp(gstAKCSConf.oem_name, "AKUVOX"))
    {
        gstAKCSConf.oem_num = OEM_AKUVOX;
    }
    else if (!strcasecmp(gstAKCSConf.oem_name, "DISCREET"))
    {
        gstAKCSConf.oem_num = OEM_DISCREET;
    }
    Snprintf(gstAKCSConf.push_AESkey, sizeof(gstAKCSConf.push_AESkey), config_file.GetConfigName("push_aeskey"));
    return 0;

}

