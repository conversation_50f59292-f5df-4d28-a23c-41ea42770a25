#config pack_rom makefile

MOD_BIN_NAME = CsMainUnitTest
PWD := $(shell pwd)
export MOD_DIR := $(PWD)/../../../
export CSBASE_DIR := $(PWD)/../../../../csbase

PJ_DIR ?= $(MOD_DIR)

include $(PJ_DIR)/src/unit_test/build/PROJECT.mak
include $(PJ_DIR)/build/TOOL.mak
include $(PJ_DIR)/src/unit_test/build/MOD.mak

export CPPFLAGS := -I$(PJ_INC_DIR)/mysql
export CPPFLAGS += -I$(MOD_SRC_DIR)/include
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Utility
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Tinyxml
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Basic
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Character
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Character/cstring
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Lock
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Socket
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Dao
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Curl
export CPPFLAGS += -I$(MOD_SRC_DIR)/Logic
export CPPFLAGS += -I$(MOD_SRC_DIR)/Model
export CPPFLAGS += -I$(MOD_SRC_DIR)/Office/Model
export CPPFLAGS += -I$(MOD_SRC_DIR)/Office/Logic
export CPPFLAGS += -I$(MOD_SRC_DIR)/Tools
export CPPFLAGS += -I$(MOD_EXTERN_INC_DIR)
export CPPFLAGS += -I$(MOD_SRC_INC_DIR)
export CPPFLAGS += -I$(CSBASE_DIR)
export CPPFLAGS += -I$(CSBASE_DIR)/redis
export CPPFLAGS += -I$(CSBASE_DIR)/redis/hiredis
export CPPFLAGS += -I$(CSBASE_DIR)/grpc
export CPPFLAGS += -I$(CSBASE_DIR)/grpc/include
export CPPFLAGS += -I$(CSBASE_DIR)/etcd
export CPPFLAGS += -I$(CSBASE_DIR)/evpp
export CPPFLAGS += -I$(CSBASE_DIR)/session
export CPPFLAGS += -I$(CSBASE_DIR)/grpc/cssession
export CPPFLAGS += -I$(CSBASE_DIR)/protobuf
export CPPFLAGS += -I$(CSBASE_DIR)/Rldb
export CPPFLAGS += -I$(CSBASE_DIR)/encrypt
export CPPFLAGS += -I$(CSBASE_DIR)/grpc/csmain
export CPPFLAGS += -I$(CSBASE_DIR)/grpc/gens
export CPPFLAGS += -I$(CSBASE_DIR)/beanstalk-client
export CPPFLAGS += -I$(CSBASE_DIR)/gid
export CPPFLAGS += -I$(CSBASE_DIR)/catch2
export CPPFLAGS += -I$(CSBASE_DIR)/model
export CPPFLAGS += -I$(CSBASE_DIR)/jsoncpp0.5/include

export LDFLAGS := -L$(MOD_LIB_DIR) -L$(CSBASE_DIR)/evpp/lib/ -L$(CSBASE_DIR)/redis/hiredis/ -L$(CSBASE_DIR) -L$(CSBASE_DIR)/thirdlib -lpthread -lmysqlclient -liconv  -levpp -lglog -lssl -lcrypto -levent -Bstatic -lhiredis -lcsbase -lgpr -lgrpc -lgrpc++ -lprotobuf -lboost_system -lcpprest -letcd-cpp-api -levnsq -lcurl

export CXX = g++
CXX += -std=c++11 -DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -std=c++11 -fPIE -fsanitize=address

export STRIP = strip
RM = rm -rf
CP = cp -rf
OFLAG =

MOD_SRC_DIRS := $(MOD_SRC_DIR)/Common/Basic $(MOD_SRC_DIR)/Common/Character $(MOD_SRC_DIR)/Common/Character/cstring $(MOD_SRC_DIR)/Common/Lock \
     $(MOD_SRC_DIR)/unit_test $(MOD_SRC_DIR)/unit_test/grpc $(MOD_SRC_DIR)/unit_test/model $(MOD_SRC_DIR)/unit_test/common $(CSBASE_DIR)/Rldb \
	 $(MOD_SRC_DIR)/Common/Tinyxml $(MOD_SRC_DIR)/Common/Utility $(MOD_SRC_DIR)/Logic $(MOD_SRC_DIR)/Model $(MOD_SRC_DIR)/Common/Dao \
     $(CSBASE_DIR)/redis $(CSBASE_DIR)/grpc $(CSBASE_DIR)/etcd $(CSBASE_DIR)/protobuf $(CSBASE_DIR)/session $(CSBASE_DIR)/grpc/cssession $(CSBASE_DIR)/encrypt $(CSBASE_DIR)/grpc/csmain $(CSBASE_DIR)/csmain $(CSBASE_DIR)/beanstalk-client $(CSBASE_DIR)/model $(CSBASE_DIR)/dbinterface $(CSBASE_DIR)/dbinterface/office $(MOD_SRC_DIR)/Office/Model $(MOD_SRC_DIR)/Office/Logic $(MOD_SRC_DIR)/Tools
    
.PHONY: all $(MOD_SRC_DIRS) clean

all: check $(MOD_SRC_DIRS) $(MOD_BIN_NAME)

check:
ifeq ($(PJ_DIR),)
	@echo "Build failed: PJ_DIR is NULL"
	@exit 1
endif


CsMainUnitTestObjs := $(MOD_OBJ_DIR)*.o
$(MOD_BIN_NAME): $(MOD_SRC_DIRS)
	@echo "\n"
	$(CXX) -g -Wall -gstabs+ -o $(MOD_BIN_DIR)$(MOD_BIN_NAME) $(CsMainUnitTestObjs)  $(LDFLAGS)

$(MOD_SRC_DIRS):
	@echo "\n"
	@$(MAKE) --directory=$@

clean: check
	for d in $(MOD_SRC_DIRS); \
	do \
		cd $${d}; \
		$(MAKE) clean; \
		cd ..;	\
	done \

	-rm $(MOD_BIN_DIR)$(MOD_BIN_NAME)

