﻿#include "AkLogging.h"
#include <catch2/catch.hpp>
#include "AES256.h"
#include "MsgControl.h"
#include "MsgHandle.h"
#include <arpa/inet.h>

using namespace std;

TEST_CASE("AES256Test", "[AES_256_DECRYPT]")
{
    SOCKET_MSG_NORMAL* normal_msg = new SOCKET_MSG_NORMAL();
    memset(normal_msg, 0x0, sizeof(SOCKET_MSG_NORMAL));
    normal_msg->message_id = 0x1029;
    normal_msg->data_size = htons(256);

    SOCKET_MSG_DEV_ARMING arming_msg;
    memset(&arming_msg, 0, sizeof(arming_msg));

    CMsgControl* msg_control = GetMsgControlInstance();
    CMsgHandle* msg_handle = GetMsgHandleInstance();

    const char* mac = "0C11050945B8";

    SECTION("ParseReqArmingMsgFromDev")
    {
        SOCKET_MSG_DEV_ARMING arming;
        strcpy(arming.szAction, "GET");
        strcpy(arming.mac, "0C11050945B8");
        strcpy(arming.uid, "DOOR_1.1.1.1.1-1");
        arming.mode = 0;

        SOCKET_MSG_NORMAL socket_msg_normal;
        char* pay_load = (char*)socket_msg_normal.data;
        memset(&socket_msg_normal, 0x0, sizeof(SOCKET_MSG_NORMAL));
        msg_handle->BuildBuildReqArmingMsg(pay_load, sizeof(socket_msg_normal.data), arming);

        int data_size = strlen(pay_load);
        AesEncryptByMac(pay_load, pay_load, arming.mac, &data_size); //默认都加密了
        Snprintf((char*)normal_msg->data, data_size, pay_load);

        int ret = msg_control->ParseReqArmingMsgFromDev(normal_msg, arming_msg, mac);
        if (ret < 0)
        {
            AK_LOG_WARN << "ParseReqArmingMsg failed.";
            return;
        }
        REQUIRE(ret == 0);
    }

    SECTION("ParseError")
    {
        SOCKET_MSG_DEV_ARMING arming;
        strcpy(arming.szAction, "GET");
        strcpy(arming.mac, "0C11050945B9");
        strcpy(arming.uid, "DOOR_1.1.1.1.1-1");
        arming.mode = 0;

        SOCKET_MSG_NORMAL socket_msg_normal;
        char* pay_load = (char*)socket_msg_normal.data;
        memset(&socket_msg_normal, 0x0, sizeof(SOCKET_MSG_NORMAL));
        msg_handle->BuildBuildReqArmingMsg(pay_load, sizeof(socket_msg_normal.data), arming);

        int data_size = strlen(pay_load);
        AesEncryptByMac(pay_load, pay_load, arming.mac, &data_size); //默认都加密了
        Snprintf((char*)normal_msg->data, data_size, pay_load);

        for (int i = 0; i < 1;  i++)
        {
            memset(normal_msg, 0x0, sizeof(SOCKET_MSG_NORMAL));
            normal_msg->message_id = 0x1029;
            normal_msg->data_size = htons(256);

            int ret = msg_control->ParseReqArmingMsgFromDev(normal_msg, arming_msg, mac);
            if (ret < 0)
            {
                AK_LOG_WARN << "ParseReqArmingMsg failed.";
            }
            REQUIRE(ret < 0);
        }
    }

    SECTION("TransferToSocketMsg")
    {
        SOCKET_MSG_DEV_ARMING arming;
        strcpy(arming.szAction, "GET");
        strcpy(arming.mac, "0C11050945B8");
        strcpy(arming.uid, "DOOR_1.1.1.1.1-1");
        arming.mode = 0;

        SOCKET_MSG_NORMAL socket_msg_normal;
        socket_msg_normal.message_id = 0x1029;
        char* pay_load = (char*)socket_msg_normal.data;
        memset(&socket_msg_normal, 0x0, sizeof(SOCKET_MSG_NORMAL));
        msg_handle->BuildBuildReqArmingMsg(pay_load, sizeof(socket_msg_normal.data), arming);

        int data_size = strlen(pay_load);
        AesEncryptByMac(pay_load, pay_load, arming.mac, &data_size); //默认都加密了

        char* buf = new char[10 + data_size];
        memcpy(buf, &socket_msg_normal, 10 + data_size);
        string rawMsg(buf);

        int ret = msg_control->ParseReqArmingMsgFromDev(normal_msg, arming_msg, mac);
        REQUIRE(ret < 0);
        if (ret < 0)
        {
            AK_LOG_WARN << "ParseReqArmingMsg failed.";
            return;
        }
    }

    SECTION("ForceTransfer")
    {
        SOCKET_MSG_NORMAL socket_msg_normal;
        socket_msg_normal.message_id = 0x1029;
        socket_msg_normal.crc = 123;
        socket_msg_normal.head_size = 10;
        Snprintf((char*)socket_msg_normal.data, strlen(mac) + 1, mac);
        socket_msg_normal.data_size = strlen(mac) + 1;

        int len = socket_msg_normal.head_size + socket_msg_normal.data_size;
        //char* szBuf = new char[nLen];
        char* buf = new char[sizeof(socket_msg_normal)];
        memcpy(buf, &socket_msg_normal, len);

        SOCKET_MSG_NORMAL* temp = (SOCKET_MSG_NORMAL*)buf;
        AK_LOG_INFO << "pTemp=" << temp << ";szBuf=" << (void*)buf;
        AK_LOG_INFO << (char*)temp->data;
        REQUIRE(temp != nullptr);
    }
}

