﻿#include "AkLogging.h"
#include <catch2/catch.hpp>
#include <numeric> 
#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <boost/circular_buffer.hpp>

using namespace std;
typedef unordered_set<int> Bucket;

class TCPConnTest
{
public:
	TCPConnTest()
	{
		AK_LOG_INFO << "TCPConnTest is constructed:" << this;
	}

	~TCPConnTest()
	{
		AK_LOG_INFO << "TCPConnTest is destructed:" << this;
	}

	void Close()
	{
		AK_LOG_INFO << "TCPConnTest is closed:" << this;
	}
};
typedef std::weak_ptr<TCPConnTest> WeakTCPConnTestPtr;
typedef std::shared_ptr<TCPConnTest> TCPConnTestPtr;

struct EntryTest
{
    explicit EntryTest(const WeakTCPConnTestPtr& weak_conn)
        : weak_conn_(weak_conn)
    {
		AK_LOG_INFO << "EntryTest is constructed:" << this << ";useCount =" << weak_conn_.use_count();
		TCPConnTestPtr conn = weak_conn_.lock(); //提升为强引用
		if (conn)
		{
			AK_LOG_INFO << "hold conn:" << conn.get(); 
		}
    }

    ~EntryTest()
    {
		AK_LOG_INFO << "EntryTest is destructed:" << this << ";useCount =" << weak_conn_.use_count();
        TCPConnTestPtr conn = weak_conn_.lock(); //提升为强引用
        if (conn)
        {
            conn->Close(); 
        }
    }

	void Print() 
	{
		AK_LOG_INFO << "EntryTest is:" << this << ";EntryTet useCount = " << weak_conn_.use_count();
	}
    WeakTCPConnTestPtr weak_conn_;
};
typedef std::shared_ptr<EntryTest> EntryTestPtr;
typedef std::weak_ptr<EntryTest> WeakEntryTestPtr;
typedef unordered_set<EntryTestPtr> BucketEntry;

void PrintBuffer(const boost::circular_buffer<Bucket> &buffer)
{
	AK_LOG_INFO << "Print buffer size = " << buffer.size()  << ";is full=" << buffer.full() << ";capacity=" << buffer.capacity();
	for (auto it = buffer.begin(); it != buffer.end(); it++)
	{
		AK_LOG_INFO << "set size = " << it->size();
	}
	AK_LOG_INFO << "Print end";
}

void PrintConnectionBuckets(const boost::circular_buffer<BucketEntry> &connection_buckets) 
{
	AK_LOG_INFO << "PrintConnectionBuckets begin:" << connection_buckets.size() << ";capacity=" << connection_buckets.capacity() << ";is full=" << connection_buckets.full();
	for (auto it = connection_buckets.begin(); it != connection_buckets.end(); it++)
	{
		AK_LOG_INFO << "set size = " << it->size();
	}
	AK_LOG_INFO << "PrintConnectionBuckets end";
}

TEST_CASE("CircularBufferTest", "[Insert]")
{
	SECTION("push_back")
	{
		  // 创建一个容量为3的循环缓冲区  
		boost::circular_buffer<int> cb(3);  
		  
		  // 插入一些元素到循环缓冲区  
		cb.push_back(1);  
		cb.push_back(2);  
		  
		  // 断言  
		REQUIRE(cb[0] == 1);  
		REQUIRE(cb[1] == 2);  
		REQUIRE(!cb.full());  
		REQUIRE(cb.size() == 2);  
		REQUIRE(cb.capacity() == 3);  
		  
		  // 再插入其它元素  
		cb.push_back(3);  
		cb.push_back(4);  
		  
		  // 求和  
		int sum = std::accumulate(cb.begin(), cb.end(), 0);  
		  
		  // 断言  
		REQUIRE(cb[0] == 2);  
		REQUIRE(cb[1] == 3);  
		REQUIRE(cb[2] == 4);  
		REQUIRE(*cb.begin() == 2);  
		REQUIRE(cb.front() == 2);  
		REQUIRE(cb.back() == 4);  
		REQUIRE(sum == 9);  
		REQUIRE(cb.full());  
		REQUIRE(cb.size() == 3);  
		REQUIRE(cb.capacity() == 3);  
	}

	SECTION("simple back")
	{
		boost::circular_buffer<Bucket> cb(3);  
		cb.resize(3);
		PrintBuffer(cb);
	    cb.back().insert(1); 
		REQUIRE(cb.back().size() == 1);
		PrintBuffer(cb);
		cb.push_back(Bucket());
		PrintBuffer(cb);
		cb.push_back(Bucket());
		PrintBuffer(cb);
		cb.push_back(Bucket());
		PrintBuffer(cb);
	    cb.back().insert(1); 
		PrintBuffer(cb);
	}

	SECTION("EntryTest") 
	{
		boost::circular_buffer<EntryTestPtr> connection_buckets;
		connection_buckets.resize(3);

		AK_LOG_INFO << "EntryTest begin";
		for (int i = 0; i < 10; i++)
		{
			TCPConnTestPtr tcp_conn_test_ptr = std::make_shared<TCPConnTest>();
			EntryTestPtr entry(new EntryTest(tcp_conn_test_ptr));
			connection_buckets.push_back(entry);
		}
		AK_LOG_INFO << "EntryTest end";
	}

	SECTION("UnorderSet")
	{
		AK_LOG_INFO << "UnorderSet begin";
		TCPConnTestPtr tcp_conn_test_ptr1 = std::make_shared<TCPConnTest>();
		EntryTestPtr entry(new EntryTest(tcp_conn_test_ptr1 ));

		{
			AK_LOG_INFO << "bucket_entry begin";
			BucketEntry bucket_entry;
			bucket_entry.insert(entry);
			AK_LOG_INFO << "bucket_entry end";
		}

		AK_LOG_INFO << "UnorderSet end";
	}

	SECTION("TCPConnTest_OneConnection")
	{
		AK_LOG_INFO << "TCPConnTest_OneConnection begin";
		boost::circular_buffer<BucketEntry> connection_buckets;
		connection_buckets.resize(3);
		std::vector<WeakEntryTestPtr> weak_entry_vector;

		TCPConnTestPtr tcp_conn_test_ptr1 = std::make_shared<TCPConnTest>();
		{
			EntryTestPtr entry(new EntryTest(tcp_conn_test_ptr1 ));
			connection_buckets.back().insert(entry);
			PrintConnectionBuckets(connection_buckets);

			WeakEntryTestPtr weak_entry(entry);
			weak_entry_vector.push_back(entry);
		}

		{
			AK_LOG_INFO << "Insert empty bucket";
			connection_buckets.push_back(BucketEntry());
			PrintConnectionBuckets(connection_buckets);
		}

		{
			AK_LOG_INFO << "Insert empty bucket";
			connection_buckets.push_back(BucketEntry());
			PrintConnectionBuckets(connection_buckets);
		}

		{
			AK_LOG_INFO << "Insert empty bucket";
			connection_buckets.push_back(BucketEntry());
			PrintConnectionBuckets(connection_buckets);
		}
		AK_LOG_INFO << "TCPConnTest_OneConnection end";
	}

	SECTION("TCPConnTest_OneConnection_InsertMsg")
	{
		boost::circular_buffer<BucketEntry> connection_buckets;
		connection_buckets.resize(3);
		std::vector<WeakEntryTestPtr> weak_entry_vector;

		TCPConnTestPtr tcp_conn_test_ptr1 = std::make_shared<TCPConnTest>();
		{
			EntryTestPtr entry(new EntryTest(tcp_conn_test_ptr1 ));
			connection_buckets.back().insert(entry);

			WeakEntryTestPtr weak_entry(entry);
			weak_entry_vector.push_back(entry);
		}

		AK_LOG_INFO << "Insert empty bucket";
		connection_buckets.push_back(BucketEntry());
		PrintConnectionBuckets(connection_buckets);

		{
			//中途插入
			WeakEntryTestPtr &weak_entry = weak_entry_vector[0];
			EntryTestPtr entry2(weak_entry.lock());
			connection_buckets.back().insert(entry2);
		}
		PrintConnectionBuckets(connection_buckets);

		AK_LOG_INFO << "Insert empty bucket";
		connection_buckets.push_back(BucketEntry());
		PrintConnectionBuckets(connection_buckets);

		AK_LOG_INFO << "Insert empty bucket";
		connection_buckets.push_back(BucketEntry());
		PrintConnectionBuckets(connection_buckets);

		AK_LOG_INFO << "Insert empty bucket";
		connection_buckets.push_back(BucketEntry());
		PrintConnectionBuckets(connection_buckets);

		AK_LOG_INFO << "Insert empty bucket";
		connection_buckets.push_back(BucketEntry());
		PrintConnectionBuckets(connection_buckets);
	}

}

