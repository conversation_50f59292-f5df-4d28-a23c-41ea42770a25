﻿#include "AkLogging.h"
#include <catch2/catch.hpp>
#include <numeric> 
#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <unistd.h>
#include <stdlib.h>
#include <boost/pool/singleton_pool.hpp>

using namespace boost;

struct switch_jb_node_tag_t
{
};

struct switch_jb_node_t
{
	char buffer[16440];
};

struct switch_channel_tag_t
{
};

struct switch_channel_t
{
	char channel[1208];
};

struct switch_core_session_tag_t
{
};

struct switch_core_session_t
{
	char session[36096];
};


struct switch_rtp_tag_t
{
};

struct switch_rtp_t
{
	char rtp[93000];
};

typedef singleton_pool<switch_jb_node_tag_t, sizeof(switch_jb_node_t)> switch_jb_node_pool;
typedef singleton_pool<switch_channel_tag_t, sizeof(switch_channel_t)> switch_channel_pool;
typedef singleton_pool<switch_core_session_tag_t, sizeof(switch_core_session_t)> switch_session_pool;
typedef singleton_pool<switch_rtp_tag_t, sizeof(switch_rtp_t)> switch_rtp_pool;
const char *cmd = "ps aux|grep CsMainUnitTest|grep -v grep";


TEST_CASE("SinglePool", "[MallocTest]")
{
    SECTION("SwitchJbNode")
    {
		switch_jb_node_t *jb_node = (switch_jb_node_t *)switch_jb_node_pool::malloc();
		REQUIRE(switch_jb_node_pool::is_from(jb_node));
    }

    SECTION("Malloc")
    {
		int i = 0;
		system(cmd);
		while (1)
		{
			switch_jb_node_t *jb_node = (switch_jb_node_t *)switch_jb_node_pool::malloc();
			memset(jb_node, 0x0, sizeof(switch_jb_node_t));
			REQUIRE(switch_jb_node_pool::is_from(jb_node));

			switch_channel_t *channel_node = (switch_channel_t *)switch_channel_pool::malloc();
			memset(channel_node, 0x0,  sizeof(switch_channel_t));

			switch_rtp_t *rtp_node = (switch_rtp_t *)switch_rtp_pool::malloc();
			memset(rtp_node, 0x0, sizeof(switch_rtp_t));

			switch_core_session_t *session_node = (switch_core_session_t *)switch_session_pool::malloc();
			memset(session_node, 0x0, sizeof(switch_core_session_t));
			usleep(1000);
			++i;
			if (i == 600)
			{
				system(cmd);
				break;
			}
		}
    }

	SECTION("Free")
	{
		int i = 0;
		system(cmd);
		while (1)
		{
			switch_jb_node_t *jb_node = (switch_jb_node_t *)switch_jb_node_pool::malloc();
			memset(jb_node, 0x0, sizeof(switch_jb_node_t));
			REQUIRE(switch_jb_node_pool::is_from(jb_node));
			switch_jb_node_pool::free(jb_node);

			switch_channel_t *channel_node = (switch_channel_t *)switch_channel_pool::malloc();
			memset(channel_node, 0x0,  sizeof(switch_channel_t));
			switch_channel_pool::free(channel_node);

			switch_rtp_t *rtp_node = (switch_rtp_t *)switch_rtp_pool::malloc();
			memset(rtp_node, 0x0, sizeof(switch_rtp_t));
			switch_rtp_pool::free(rtp_node);

			switch_core_session_t *session_node = (switch_core_session_t *)switch_session_pool::malloc();
			memset(session_node, 0x0, sizeof(switch_core_session_t));
			switch_session_pool::free(session_node);

			usleep(1000);
			++i;
			if (i == 600)
			{
				system(cmd);
				break;
			}
		}
	}
}


