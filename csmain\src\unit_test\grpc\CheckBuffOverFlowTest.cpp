﻿#include <string.h>
#include <string>
#include "AkLogging.h"
#include <catch2/catch.hpp>

#define CHECK_BUFFER_OVER_FLOW(n1, n2) \
    do { if ((int)(n1) >= (n2)) { AK_LOG_WARN << "xml buffer is overflow, real size is [" << (n1) << "], but define size is [" << (n2) << "]";} } while (0)

TEST_CASE("CHECK_BUFFER_OVER_FLOW", "[check]")
{
    int size = 10;

    //section里面必须写东西
    SECTION("Overflow")
    {
        char s1[] = "0123456789";
        CHECK_BUFFER_OVER_FLOW(strlen(s1), size);
        int len = strlen(s1);
        REQUIRE(len >= size);

        std::string s11 = "hello,world!hello,Mimyy;";
        CHECK_BUFFER_OVER_FLOW(s11.size(), size);
        len = s11.size();
        REQUIRE(len >= size);
    }

    SECTION("Not overflow")
    {
        char s2[] = "0123";
        CHECK_BUFFER_OVER_FLOW(strlen(s2), size);
        int len = strlen(s2);
        REQUIRE(len < size);
    }
}


