#include "session_rpc_client.h"
#include "csmain/csmain_rpc_client_mng.h"
#include "util.h"
#include <catch2/catch.hpp>

TEST_CASE("SmRpcClient", "[.testQuery]")
{

    SmRpcClient* sm_client_ptr = new SmRpcClient();
    sm_client_ptr->RegisterNode("test");

    //std::string logic_srv_ip = GetEth0IPAddr();
    std::string logic_srv_ip = "*************";
    std::string logic_srv_id = "csmain" + logic_srv_ip;

    std::vector<AddressData> addresses;
    addresses.emplace_back(AddressData{9002, false, "", logic_srv_ip});//false 不是负载均衡器
    sm_client_ptr->SetNextResolution(addresses);

    //section里面必须写东西
    SECTION("Test QueryUid")
    {
        std::string sid = sm_client_ptr->QueryUid("6503100000");
        AK_LOG_INFO << "sid=" << sid;
        CHECK(sid == logic_srv_id);
    }

    SECTION("Test QueryDev")
    {
        std::string mac = "0C11050068F4";
        std::string result = sm_client_ptr->QueryDev(mac);
        AK_LOG_INFO << "result=" << result;
        REQUIRE(result == logic_srv_id);
    }

	SECTION("Test QueryUidStatus")
	{
		int status = 0;
		uint64_t traceid = 1718822334;
		std::string uid = "6504100231";
		std::string sid = sm_client_ptr->QueryUid(uid);
		AK_LOG_INFO << "sid=" << sid;

		std::set<std::string> csmain_inner_addrs;
		csmain_inner_addrs.insert("*************:8506");
		csmain_inner_addrs.insert("*************:8506");
		for (const auto& csmain : csmain_inner_addrs)
		{
			MainRpcClientPtr route_cli_ptr(new MainRpcClient(csmain));
			MainRpcClientMng::Instance()->AddCsmainRpcSrv(csmain, route_cli_ptr);
		}

		MainRpcClientPtr csmain_cli = MainRpcClientMng::Instance()->getRpcClientInstance(sid);
		if (csmain_cli)
		{
			status = csmain_cli->QueryUidStatus(uid, traceid);
		}
		AK_LOG_INFO << "status=" << status;
	}
}

