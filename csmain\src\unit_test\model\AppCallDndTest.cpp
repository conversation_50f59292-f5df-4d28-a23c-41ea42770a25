﻿#include <string>
#include <map>
#include "AppCallStatus.h"
#include "AkLogging.h"
#include "unistd.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "json/json.h"
#include <catch2/catch.hpp>

using namespace std;

TEST_CASE("AppCallDndTest", "[Dnd]")
{
    SECTION("JsonTest")
	{
		AppDndInfo app_dnd_info;
		memset(&app_dnd_info, 0, sizeof(app_dnd_info));

		string dnd_info = "{\"status\":1,\"start_time\":660,\"end_time\":180}";
		Json::Reader reader;
		Json::Value root;
		if (!reader.parse(dnd_info, root))
		{
			AK_LOG_WARN << "json parse failed, dnd_info=" << dnd_info;
			return;
		}


		app_dnd_info.status = root["status"].asInt();
		app_dnd_info.start_time = root["start_time"].asInt();
		app_dnd_info.end_time = root["end_time"].asInt();

		AK_LOG_INFO << "GetAppState status=" << app_dnd_info.status << ";start_time=" << app_dnd_info.start_time << ";end_time=" << app_dnd_info.end_time;
	}
}

