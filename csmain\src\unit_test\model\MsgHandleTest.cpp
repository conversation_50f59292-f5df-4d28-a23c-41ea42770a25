﻿#include <string>
#include <map>
#include "MsgHandle.h"
#include "AkLogging.h"
#include "XmlTagDefine.h"
#include "unistd.h"
#include <catch2/catch.hpp>
using namespace std;

TEST_CASE("MsgHandleTest", "[BuildCommonMsg]")
{
    map<string, string> tag_map;
    CMsgHandle* msg_handle = CMsgHandle::GetInstance();

    //section里面必须写东西
    SECTION("Only type")
    {
        tag_map[csmain::xmltag::TYPE] = "RegisterFace";

        string msg = msg_handle->BuildCommonMsg(tag_map);
        AK_LOG_INFO << "msg=\n" << msg;
        REQUIRE(msg.size() == 0);
    }

    //section里面必须写东西
    SECTION("RegisterFace")
    {
        tag_map[csmain::xmltag::TYPE] = "RegisterFace";
        tag_map[csmain::xmltag::URL] = "ftp://192.168.12.58/photo/xxx.jpg";
        tag_map[csmain::xmltag::PIC_MD5] = "xxxxxxxxxxxx";
        tag_map[csmain::xmltag::NAME] = "Jeffrey";
        tag_map[csmain::xmltag::DOOR_NUM] = "1234";
        tag_map[csmain::xmltag::WEEK] = "1111011";
        tag_map[csmain::xmltag::TIME_START] = "00:01";
        tag_map[csmain::xmltag::TIME_END] = "23:59";
        tag_map[csmain::xmltag::ID] = "123";

        string msg = msg_handle->BuildCommonMsg(tag_map);
        AK_LOG_INFO << "msg=\n" << msg;
        REQUIRE(msg.size() > 0);
    }

    //section里面必须写东西
    SECTION("ModifyFace")
    {
        tag_map[csmain::xmltag::TYPE] = "ModifyFace";
        tag_map[csmain::xmltag::URL] = "ftp://192.168.12.58/photo/xxx.jpg";
        tag_map[csmain::xmltag::PIC_MD5] = "xxxxxxxxxxxx";
        tag_map[csmain::xmltag::NAME] = "陈蔚蔚";
        tag_map[csmain::xmltag::DOOR_NUM] = "1234";
        tag_map[csmain::xmltag::WEEK] = "1111011";
        tag_map[csmain::xmltag::TIME_START] = "00:01";
        tag_map[csmain::xmltag::TIME_END] = "23:59";
        tag_map[csmain::xmltag::ID] = "123";

        string msg = msg_handle->BuildCommonMsg(tag_map);
        AK_LOG_INFO << "msg=\n" << msg;
        REQUIRE(msg.size() > 0);
    }

    //section里面必须写东西
    SECTION("DeleteFace")
    {
        tag_map[csmain::xmltag::TYPE] = "DeleteFace";
        tag_map[csmain::xmltag::NAME] = "陈蔚蔚";
        tag_map[csmain::xmltag::ID] = "123";

        string msg = msg_handle->BuildCommonMsg(tag_map);
        AK_LOG_INFO << "msg=\n" << msg;
        REQUIRE(msg.size() > 0);
    }
}

TEST_CASE("MsgHandleTestMemory", "[.MemoryLeak]")
{
    map<string, string> tag_map;
    CMsgHandle* msg_handle = CMsgHandle::GetInstance();

    //测试是否会内存泄漏
    SECTION("MemoryLeak")
    {
        tag_map[csmain::xmltag::TYPE] = "RegisterFace";
        tag_map[csmain::xmltag::URL] = "ftp://192.168.12.58/photo/xxx.jpg";
        tag_map[csmain::xmltag::PIC_MD5] = "xxxxxxxxxxxx";
        tag_map[csmain::xmltag::NAME] = "Jeffrey";
        tag_map[csmain::xmltag::DOOR_NUM] = "1234";
        tag_map[csmain::xmltag::WEEK] = "1111011";
        tag_map[csmain::xmltag::TIME_START] = "00:01";
        tag_map[csmain::xmltag::TIME_END] = "23:59";
        tag_map[csmain::xmltag::ID] = "123";

        for (int i = 0; i < 300000; i++)
        {
            string msg = msg_handle->BuildCommonMsg(tag_map);
            usleep(10);
        }
    }

}

