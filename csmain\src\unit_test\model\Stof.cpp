#define CATCH_CONFIG_MAIN
#include catch.hpp
#include "util.h"

TEST_CASE(STOF function tests, [STOF]) {
    SECTION(Null pointer input) {
        REQUIRE(STOF(nullptr) == Approx(0.0f));
    }

    SECTION(Empty string input) {
        REQUIRE(STOF() == Approx(0.0f));
    }

    SECTION(Valid numeric string input) {
        REQUIRE(STOF(123.45) == Approx(123.45f));
        REQUIRE(STOF(-987.65) == Approx(-987.65f));
    }

    SECTION(Invalid numeric string input) {
        REQUIRE(STOF(abc) == Approx(0.0f));
        REQUIRE(STOF(123abc) == Approx(0.0f));
    }

    SECTION(Whitespace handling) {
        REQUIRE(STOF(   456.78   ) == Approx(456.78f));
    }

    SECTION(Edge cases) {
        REQUIRE(STOF(7.0) == Approx(7.0f));
    }
}
