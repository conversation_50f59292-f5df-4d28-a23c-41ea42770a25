﻿#include <string>
#include <map>
#include "dbinterface/ThirdParty.h"
#include "AkLogging.h"
#include "unistd.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include <catch2/catch.hpp>

using namespace std;

extern AKCS_CONF gstAKCSConf;

int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    conn_pool->Init(gstAKCSConf.db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password,
                    gstAKCSConf.db_database, gstAKCSConf.db_port, MAX_RLDB_CONN, "csmain");
    return 0;
}

uint32_t ConfInit()
{
    memset(&gstAKCSConf, 0, sizeof(AKCS_CONF));

    CConfigFileReader config_file("/usr/local/akcs/csmain/conf/csmain.conf");

    Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip), config_file.GetConfigName("db_ip"));
    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username), config_file.GetConfigName("db_username"));
    Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), config_file.GetConfigName("db_passwd"));
    Snprintf(gstAKCSConf.db_database, sizeof(gstAKCSConf.db_database), config_file.GetConfigName("db_database"));
    return 0;
}


TEST_CASE("ThirdPartyTest", "[DaoTest]")
{
    ConfInit();
    DaoInit();

    ThirdPartyInfo third_party_info;
    memset(&third_party_info, 0x0, sizeof(third_party_info));

    SECTION("DaoGetThirdPartyInfo noresult")
    {
        const char* mac = "0C11050068F1";
        int ret = dbinterface::ThirdParty::GetThirdPartyInfo(mac, third_party_info);
        REQUIRE(ret == 0);
        REQUIRE(third_party_info.account_id == 0);
    }

    SECTION("DaoGetThirdPartyInfo exist")
    {
        const char* mac = "0C11050068F5";
        int ret = dbinterface::ThirdParty::GetThirdPartyInfo(mac, third_party_info);
        REQUIRE(ret == 0);
        REQUIRE(third_party_info.account_id == 36);
    }

	SECTION("IsFaceServerDevice")
	{
		bool is_face_server_device = dbinterface::ThirdParty::IsFaceServerDevice("29.30.102.301");
		REQUIRE(is_face_server_device);

		is_face_server_device = dbinterface::ThirdParty::IsFaceServerDevice("916.30.102.301");
		REQUIRE(is_face_server_device);

		is_face_server_device = dbinterface::ThirdParty::IsFaceServerDevice("293.30.102.301");
		REQUIRE(!is_face_server_device);

		is_face_server_device = dbinterface::ThirdParty::IsFaceServerDevice("926.30.102.301");
		REQUIRE(!is_face_server_device);
	}
}

