<?php


$link_unkown=array();
const YEAR = 2020;
$time_zone=array();

/**
* 多个连续空格只保留一个
*
* @param string $string 待转换的字符串
* @return unknown
*/
function merge_spaces ( $string )
{
    return preg_replace ( "/\s(?=\s)/","\\1", $string );
}

function time2sec($time1)
{
	$time = $time1;//'21:30:10';
	$parsed = date_parse($time);
	$seconds = $parsed['hour'] * 3600 + $parsed['minute'] * 60 + $parsed['second'];
	return $seconds;
}

/*
功能: 计算今日是当月的第几个星期几
*/
function getWeek($time)
{
	$datatime=$time;
	$wk_day=date('w', strtotime(date($datatime)));   //得到今天是星期几
	$date_now=date('j', strtotime(date($datatime))); //得到今天是几号
	$wkday_ar=array('日','一','二','三','四','五','六'); //规范化周日的表达
	$cal_result=ceil($date_now/7); //计算是第几个星期几
	$str=date($datatime)." 星期".$wkday_ar[$wk_day]." - 本月的第 ".$cal_result." 个星期".$wkday_ar[$wk_day];
	if ($cal_result==4) $cal_result=5;
	return $cal_result;
}

function getDST($year, $zone)
{
/*这两个时区比较特殊,没有夏令时的.格式如下
	root@chenzhx:/home/<USER>/Dawson -c 2020,2021
	America/Dawson  -9223372036854775808 = NULL
	America/Dawson  -9223372036854689408 = NULL
	America/Dawson  Sun Mar  8 09:59:59 2020 UT = Sun Mar  8 01:59:59 2020 PST isdst=0 gmtoff=-28800
	America/Dawson  Sun Mar  8 10:00:00 2020 UT = Sun Mar  8 03:00:00 2020 MST isdst=0 gmtoff=-25200
	America/Dawson  9223372036854689407 = NULL
	America/Dawson  9223372036854775807 = NULL
*/
	if ($zone == "America/Whitehorse" || $zone == "America/Dawson")
	{
		return array();
	}
	$start_year=$year;
	$end_year=$start_year+1;

	$wkday_ar=array("Sun"=>"7", "Sat"=>"6", "Fri"=>"5", "Thu"=>"4", "Wed"=>"3", "Tues"=>"2", "Mon"=>"1");
	$monthday_ar=array("Dec"=>"12", "Nov"=>"11", "Oct"=>"10", "Sep"=>"9", "Aug"=>"8", "Jul"=>"7", "Jun"=>"6", "May"=>"5", "Apr"=>"4", "Mar"=>"3", "Feb"=>"2", "Jan"=>"1");

	$cmd="zdump -c $start_year,$end_year -v $zone | grep $start_year | awk '{print $2}' | uniq  | wc -l | tr -d '\n'";
	$type = shell_exec($cmd);
	if ($type!=1)
	{
		if ($type==0)
		{
			//不支持夏令时
			//echo "$zone not support dst!\n";
			return array();
		}
		if ($zone == "Asia/Tehran")//Europe/Chisinau  Asia/Jerusalem 周六 周天切换
		{
			echo "$zone by date\n";
			//<TimeZone="GMT+3:30" Name="Tehran" Type="0" Start="3/22/0" End="9/21/23" Offset="60" />
			return array("Type"=>"0", "Start"=>"3/22/0", "End"=>"9/21/23", "Offset"=>"60");
		}
		echo "$zone maybe by date\n";
		//exit();
	}

	$cmd="zdump -c $start_year,$end_year -v $zone| grep $start_year";
	$handle = popen($cmd,"r");
	$line = "";
	$index = 0;
	$start_format = "";
	$end_format = "";
	$mode = 0;//3月份开始  9月份结束的模式
	while(!feof($handle)){
		$index++;
		$line=fgets($handle, 4096);
		if (strlen($line) == 0)
		{
			break;
		}
		$line = merge_spaces($line);
		$arr = explode(" ", $line);
		$dst=$arr[14];
		$hour=$arr[11];
		$week=$arr[8];
		$month=$arr[9];
		$day=$arr[10];
		//echo "$week,$month,$day\n";
		
		switch ($index) {
			case 1:
				if ("isdst=0" == $dst)
				{
					//开始
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $start_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $start_format."\n";
				}
				if ("isdst=1" == $dst)
				{
					//9月份开始 3月份结束的模式
					$mode = 1;
				}				
				break;
			case 2:
				if ("isdst=0" == $dst)
				{
					//开始
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $start_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $start_format."\n";
				}
				break;
			case 3:
				if ("isdst=0" == $dst)
				{
					//结束
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $end_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $end_format."\n";
				}
				break;
			case 4:
				if ("isdst=0" == $dst)
				{
					//结束
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $end_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $end_format."\n";
				}
				break;								
			default:

				break;
		}
	}
	pclose($handle);
	if ($mode == 1)
	{	
		return array("Type"=>"1", "Start"=>$end_format, "End"=>$start_format, "Offset"=>"60");
	}
	else
	{
		return array("Type"=>"1", "Start"=>$start_format, "End"=>$end_format, "Offset"=>"60");
	}
}
function getTimezone($file)
{
	global $link_unkown,$time_zone;
	$unuse = ["EST","MST","HST","EST5EDT","CST6CDT","MST7MDT","PST8PDT","WET","CET","MET","EET"];
	$timezone=array();
	
    $handle = fopen($file, 'r');
    $line = "";
    while(!feof($handle)){
    	if (!(substr($line, 0, strlen("Zone")) === "Zone"))
    	{
    		#读取时间时候会多读一行
    		$line=fgets($handle, 4096);
    	}
        
		if (substr($line, 0, strlen("Zone")) === "Zone")
		{
			$cmd="echo \"$line\" | awk '{print $2}' | tr -d '\n'";
			$zone = shell_exec($cmd);

			$continue = 0;
			foreach ($unuse as  $value) {
				if (substr($zone, 0, strlen($value)) === $value)
				{
					$continue = 1;
					break;
				}
			}
			if ($continue)
			{
				#为了开头Zone的判断
				$line=fgets($handle, 4096);
				continue;
			}

			//America/North_Dakota/Beulah
			$cmd="echo \"$line\" | awk '{print $2}' | awk -F '/' '{print \$NF}' | tr -d '\n'";
			#$cmd="echo \"$line\" | awk '{print $2}' | tr -d '\n'";
			$name = shell_exec($cmd);
			$tmp_name = explode("/", $zone);
			if (count($tmp_name) == 3)
			{
				if ("North_Dakota" == $tmp_name[1])//兼容旧的版本，别的还是按最后一个城市显示
				{
					$name=$tmp_name[1]."/".$tmp_name[2];
				}
			}
			
			#如果时区放在zone同一行就会有问题
			while ($line=fgets($handle, 4096))
			{
				if (substr($line, 0, strlen("\t")) === "\t")
				{
					$cmd="echo \"$line\" | awk '{print $1}' | tr -d '\n'";
					$time = shell_exec($cmd);
				}
				else
				{
					break;
				}
			}
			$data=array();
			if (strstr($time, "-"))
			{
				$sec = -(int)time2sec(substr($time,1,100));
				$time = "GMT".$time;
			}
			else
			{
				$sec = time2sec($time);
				$time = "GMT+".$time;
			}			
			$data["TimeZone"] =  $time;				
			$data["Zone"] =  $zone;
			$data["Name"] =  $name;
			$tmp=getDST(YEAR, $zone);
			$data = array_merge($data,$tmp);			
			$timezone[$zone] = $data;
			$time_zone[$zone] = $sec;
			//echo $zone."  ". $name. " $time ". "\n";
		}
		if (substr($line, 0, strlen("Link")) === "Link")
		{
			$data="";
			$linkname=shell_exec("echo \"$line\" | awk '{print $2}' | tr -d '\n'");
			$zone=shell_exec("echo \"$line\" | awk '{print $3}' | tr -d '\n'");
			$name=shell_exec("echo \"$zone\" | awk -F '/' '{print \$NF}' | tr -d '\n'");

			if (array_key_exists($linkname,$timezone))
			{
				$tmp = $timezone[$linkname];
				$data["TimeZone"] =  $tmp["TimeZone"];				
				$data["Zone"] =  $zone;
				$data["Name"] =  $name;
				$data["Link"] =  $linkname;	
				$tmp=getDST(YEAR, $zone);
				$data = array_merge($data,$tmp);								
				$timezone[$zone] = $data;
				$time_zone[$zone] = $time_zone[$linkname];
			}
			else
			{
				$data["Zone"] =  $zone;
				$data["Name"] =  $name;
				$data["Link"] =  $linkname;
				$link_unkown[$zone] = $data;
				//echo "error:unkonw name $zone link $linkname\n";
				//exit();
			}
		}	
    }
    fclose($handle);
    return $timezone;
}

$timezone=array();

$data = array();
$data["Zone"] =  "Asia/Calcutta";
$data["Name"] =  "Calcutta";
$data["Link"] =  "Asia/Kolkata";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Asia/Chongqing";
$data["Name"] =  "Chongqing";
$data["Link"] =  "Asia/Shanghai";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Asia/Harbin";
$data["Name"] =  "Harbin";
$data["Link"] =  "Asia/Shanghai";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Pacific/Johnston";
$data["Name"] =  "Johnston";
$data["Link"] =  "Pacific/Honolulu";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "America/Shiprock";
$data["Name"] =  "Shiprock";
$data["Link"] =  "America/Denver";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "America/Santa_Isabel";
$data["Name"] =  "Santa_Isabel";
$data["Link"] =  "America/Tijuana";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "America/Godthab";
$data["Name"] =  "Godthab";
$data["Link"] =  "America/Nuuk";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Asia/Katmandu";
$data["Name"] =  "Katmandu";
$data["Link"] =  "Asia/Kathmandu";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Asia/Kashgar";
$data["Name"] =  "Kashgar";
$data["Link"] =  "Asia/Urumqi";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Asia/Rangoon";
$data["Name"] =  "Rangoon";
$data["Link"] =  "Asia/Yangon";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Pacific/Truk";
$data["Name"] =  "Truk";
$data["Link"] =  "Pacific/Chuuk";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["Zone"] =  "Pacific/Ponape";
$data["Name"] =  "Ponape";
$data["Link"] =  "Pacific/Pohnpei";
$zone = $data["Zone"];
$link_unkown[$zone] = $data;

$data = array();
$data["TimeZone"] =  "GMT-5:00";				
$data["Zone"] =  "America/Montreal";
$data["Name"] =  "Montreal";
$zone = $data["Zone"];
$tmp=getDST(YEAR, $zone);
$data = array_merge($data,$tmp);			
$timezone[$zone] = $data;
$time_zone[$zone] = -(int)time2sec("5:00");

$data = array();
$data["TimeZone"] =  "GMT+0:00";				
$data["Zone"] =  "GMT";
$data["Name"] =  "GMT";
$zone = $data["Zone"];		
$timezone[$zone] = $data;
$time_zone[$zone] = 0;

$data = ["northamerica", "southamerica", "europe", "australasia", "asia", "africa", "antarctica"];
foreach ($data as  $value) {
	$cmd="sed -i '/^ *#/d' $value";
	shell_exec($cmd);
    
	$cmd="sed -i '/STDOFF/d' $value";
	shell_exec($cmd);	    
    
	$tmp = getTimezone($value);
	$timezone = array_merge($timezone,$tmp);

}
foreach ($link_unkown as $key => $value) {
	$data = array();
	$linkname = $value["Link"];
	$zone=$value["Zone"];
	$name=$value["Name"];
	if (array_key_exists($linkname, $timezone))
	{
		$tmp = $timezone[$linkname];
		$data["TimeZone"] =  $tmp["TimeZone"];		
		$data["Zone"] =  $zone;
		$data["Name"] =  $name;
		$data["Link"] =  $linkname;
		$tmp=getDST(YEAR, $zone);
		$data = array_merge($data,$tmp);			
		$timezone[$zone] = $data;
		$time_zone[$zone] = $time_zone[$linkname];
	}
	else
	{
		echo "error2:unkonw name $zone link $linkname\n";
		#exit();
	}    	
}
echo count($timezone)."\n";

//名称重复了
unset($timezone['Asia/Istanbul']);
unset($time_zone['Asia/Istanbul']);
unset($timezone['Asia/Nicosia']);
unset($time_zone['Asia/Nicosia']);

#新旧版本的变化需要手动调整文件
$old_xml = "TimeZone.xml";
$cmd="cat $old_xml  | awk -F 'Name=\"' '{print $2}' |  awk -F '\"' '{print $1}'";
$name = shell_exec($cmd);
$old_arr = explode("\n", $name);

asort($time_zone);
$names = array();
$last_value1 = "";
$asort_time_zone_name = array();
$next_time_zone="";
foreach ($time_zone as $key1 => $value1) {
  if ($next_time_zone != $value1)
  {
	  asort($names);
	  $asort_time_zone_name[$last_value1] = array();
	  $asort_time_zone_name[$last_value1] = $names;
	  $names = array();
	  array_push($names, $key1);
  }
  else
  {
	  array_push($names, $key1);
  }
  $next_time_zone = $value1;
  $last_value1 = $value1; 
}

$asort_time_zone_name[$last_value1] = array();
$asort_time_zone_name[$last_value1] = $names;

$xml = new XMLWriter();
$xml->openUri("new-TimeZone.xml");
// 设置缩进字符串
$xml->setIndentString("\t");
$xml->setIndent(true);
// xml文档开始
$xml->startDocument('1.0', 'utf-8');

$xml->startElement("TimeZone");
$next_time_zone="";
foreach ($time_zone as $key1 => $value1) {
	if ($next_time_zone == $value1)
	{
		continue;
	}
	$next_time_zone = $value1;
	$names=$asort_time_zone_name[$value1];
	foreach ($names as $key2 => $value2) {
		$s=$timezone[$value2];
		$xml->startElement("DST");
		$is_new=0;
		if (!in_array($s["Name"], $old_arr))
		{
			$is_new=1;
			echo "is new ".$s["Name"]."\n";
		}
		foreach ($s as $key => $value) {
			if ($key == "TimeZone")
			{
				$xml->writeAttribute($key, substr($value,3,100));  // 属性
			}
			else
			{
				$xml->writeAttribute($key, $value);  // 属性
			}

		}
		if ($is_new == 1)
		{
			$xml->writeAttribute("IsNew", 1);
		}	    
		$xml->endElement();				  
	}
}
$xml->endElement();	 
$xml->endDocument();

//for devices
{
	$xml = new XMLWriter();
	$xml->openUri("new-TimeZone-dev.xml");
	$xml->setIndentString("\t");
	$xml->setIndent(true);
	$xml->startDocument('1.0', 'utf-8');

	asort($time_zone);
	$xml->startElement("TimeZone");
	$next_time_zone="";
	foreach ($time_zone as $key1 => $value1) {
		if ($next_time_zone == $value1)
		{
			continue;
		}
		$next_time_zone = $value1;
		$names=$asort_time_zone_name[$value1];
		foreach ($names as $key2 => $value2) {
			$s=$timezone[$value2];
			$xml->startElement("DST");
			foreach ($s as $key => $value) {
				if ($key == "Zone" || $key == "Link")
				{
					continue;
				}
				$xml->writeAttribute($key, $value);  // 属性
			}
			$xml->endElement();	
		}		 	 
	}
	$xml->endElement();
	$xml->endDocument();
}

//for devices android
{
	$xml = new XMLWriter();
	$xml->openUri("new-TimeZone-dev-android.xml");
	$xml->setIndentString("\t");
	$xml->setIndent(true);
	$xml->startDocument('1.0', 'utf-8');

	$xml->startElement("TimeZone");
	$index = 0;
	$next_time_zone="";
	foreach ($time_zone as $key1 => $value1) {
		if ($next_time_zone == $value1)
		{
			continue;
		}
		$next_time_zone = $value1;
		$names=$asort_time_zone_name[$value1];
		foreach ($names as $key2 => $value2) {
			$s=$timezone[$value2];
			$xml->startElement("DST");
			$xml->writeAttribute("Id", (string)$index);  // 属性
			$index++;	
			foreach ($s as $key => $value) {
				if ($key == "Link" || $key == "Type" || $key == "Start" || $key == "End" || $key == "Offset")
				{
					continue;
				}
				if ($key == "Name")
				{
					$xml->writeAttribute("DisplayName", $value);  // 属性
				}
				else if ($key == "Zone")
				{
					$xml->writeAttribute("Name", $value);  // 属性
				}
				else
				{
					$xml->writeAttribute($key, $value);  // 属性
				}
			}		
			$xml->endElement();
		}
	}
	$xml->endElement();
	$xml->endDocument();
}




