<?php


$link_unkown=array();
const YEAR = 2025;
$timezone_list=array();

/**
* 多个连续空格只保留一个
*
* @param string $string 待转换的字符串
* @return unknown
*/
function merge_spaces ( $string )
{
    return preg_replace ( "/\s(?=\s)/","\\1", $string );
}

function time2sec($time1)
{
	$time = $time1;//'21:30:10';
	$parsed = date_parse($time);
	$seconds = $parsed['hour'] * 3600 + $parsed['minute'] * 60 + $parsed['second'];
	return $seconds;
}

function sec2GMT($totalSeconds)
{
    if (strstr($totalSeconds, "-"))
    {
        $totalSeconds = substr($totalSeconds,1,100);
        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);

        $timeFormat = sprintf("-%d:%02d", $hours, $minutes);
    }
    else
    {
        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);
        $timeFormat = sprintf("+%d:%02d", $hours, $minutes);
    }     
   
    return $timeFormat;
}



/*
功能: 计算今日是当月的第几个星期几
*/
function getWeek($time)
{
	$datatime=$time;
	$wk_day=date('w', strtotime(date($datatime)));   //得到今天是星期几
	$date_now=date('j', strtotime(date($datatime))); //得到今天是几号
	$wkday_ar=array('日','一','二','三','四','五','六'); //规范化周日的表达
	$cal_result=ceil($date_now/7); //计算是第几个星期几
	$str=date($datatime)." 星期".$wkday_ar[$wk_day]." - 本月的第 ".$cal_result." 个星期".$wkday_ar[$wk_day];
    if ($cal_result == 4)
    {
        $cal_result = 5;
    }
	return $cal_result;
}

function getDST($year, $zone)
{
/*这两个时区比较特殊,没有夏令时的.格式如下
	root@chenzhx:/home/<USER>/Dawson -c 2020,2021
	America/Dawson  -9223372036854775808 = NULL
	America/Dawson  -9223372036854689408 = NULL
	America/Dawson  Sun Mar  8 09:59:59 2020 UT = Sun Mar  8 01:59:59 2020 PST isdst=0 gmtoff=-28800
	America/Dawson  Sun Mar  8 10:00:00 2020 UT = Sun Mar  8 03:00:00 2020 MST isdst=0 gmtoff=-25200
	America/Dawson  9223372036854689407 = NULL
	America/Dawson  9223372036854775807 = NULL
*/
	if ($zone == "America/Whitehorse" || $zone == "America/Dawson")
	{
		return array();
	}
	$start_year=$year;
	$end_year=$start_year+1;

	$wkday_ar=array("Sun"=>"7", "Sat"=>"6", "Fri"=>"5", "Thu"=>"4", "Wed"=>"3", "Tues"=>"2", "Mon"=>"1");
	$monthday_ar=array("Dec"=>"12", "Nov"=>"11", "Oct"=>"10", "Sep"=>"9", "Aug"=>"8", "Jul"=>"7", "Jun"=>"6", "May"=>"5", "Apr"=>"4", "Mar"=>"3", "Feb"=>"2", "Jan"=>"1");


	$cmd="zdump -c $start_year,$end_year -v $zone | grep $start_year | awk '{print $2}' | uniq  | wc -l | tr -d '\n'";
	$type = shell_exec($cmd);	
	if ($type!=1)
	{
		if ($type==0)
		{
			//不支持夏令时
			//echo "$zone not support dst!\n";
			return array();
		}
		if ($zone == "Asia/Tehran")//Europe/Chisinau  Asia/Jerusalem 周六 周天切换
		{
			echo "$zone by date\n";
			//<TimeZone="GMT+3:30" Name="Tehran" Type="0" Start="3/22/0" End="9/21/23" Offset="60" />
			return array("Type"=>"0", "Start"=>"3/22/0", "End"=>"9/21/23", "Offset"=>"60");
		}
		echo "$zone maybe by date\n";
		//exit();
	}

	$cmd="zdump -c $start_year,$end_year -v $zone| grep $start_year";
	$handle = popen($cmd,"r");
	$line = "";
	$index = 0;
	$start_format = "";
	$end_format = "";
	$mode = 0;//3月份开始  9月份结束的模式
	$offset_hour1 = 0;
	$offset_hour2 = 0;
	$offset_hour3 = 0;
	$offset_hour4 = 0;
	while(!feof($handle)){
		$index++;
		$line=fgets($handle, 4096);
		if (strlen($line) == 0)
		{
			break;
		}
		$line = merge_spaces($line);
		$arr = explode(" ", $line);
		$dst=$arr[14];
		$hour=$arr[11];
		$week=$arr[8];
		$month=$arr[9];
		$day=$arr[10];
		$gmtoff=substr($arr[15], strlen("gmtoff=") , -1);
		//echo "$week,$month,$day\n";
		
		switch ($index) {
			case 1:
				$offset_hour1 = $gmtoff;
				if ("isdst=0" == $dst)
				{
					//开始
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $start_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $start_format."\n";
				}
				if ("isdst=1" == $dst)
				{
					//9月份开始 3月份结束的模式
					$mode = 1;
				}				
				break;
			case 2:
				$offset_hour2 = $gmtoff;
				if ("isdst=0" == $dst)
				{
					//开始
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $start_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $start_format."\n";
				}
						
				break;
			case 3:
				$offset_hour3 = $gmtoff;
				if ("isdst=0" == $dst)
				{
					//结束
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $end_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $end_format."\n";
				}
				break;
			case 4:
				$offset_hour4 = $gmtoff;
				if ("isdst=0" == $dst)
				{
					//结束
					$hour_tmp = intval((time2sec($hour)+1)/3600)%24;
				    $time=$start_year."-".$monthday_ar["$month"]."-".$day." 00:00:00";
				    $end_format = $monthday_ar["$month"]."/".getWeek($time)."/".$wkday_ar[$week]."/".$hour_tmp;
				    //echo $end_format."\n";
				}				
				break;								
			default:

				break;
		}
	}
	pclose($handle);
	if ($mode == 1)
	{	
		$offset_hour = ((int)$offset_hour4 - (int)$offset_hour3)/3600*60;	
		return array("Type"=>"1", "Start"=>$end_format, "End"=>$start_format, "Offset"=>"$offset_hour");
	}
	else
	{
		$offset_hour = ((int)$offset_hour2 - (int)$offset_hour1)/3600*60;	
		return array("Type"=>"1", "Start"=>$start_format, "End"=>$end_format, "Offset"=>"$offset_hour");
	}
}

function getTimezone($file)
{
	global $link_unkown,$timezone_list;
	$unuse = ["EST","MST","HST","EST5EDT","CST6CDT","MST7MDT","PST8PDT","WET","CET","MET","EET"];
	$timezone=array();
	
    $handle = fopen($file, 'r');
    $line = "";
    while(!feof($handle)){
    	if (!(substr($line, 0, strlen("Zone")) === "Zone"))
    	{
    		#读取时间时候会多读一行
    		$line=fgets($handle, 4096);
    	}
        
		if (substr($line, 0, strlen("Zone")) === "Zone")
		{
			$cmd="echo \"$line\" | awk '{print $2}' | tr -d '\n'";
			$zone = shell_exec($cmd);

			$continue = 0;
			foreach ($unuse as  $value) {
				if (substr($zone, 0, strlen($value)) === $value)
				{
					$continue = 1;
					break;
				}
			}
			if ($continue)
			{
				#为了开头Zone的判断
				$line=fgets($handle, 4096);
				continue;
			}

			//America/North_Dakota/Beulah
			$cmd="echo \"$line\" | awk '{print $2}' | awk -F '/' '{print \$NF}' | tr -d '\n'";
			#$cmd="echo \"$line\" | awk '{print $2}' | tr -d '\n'";
			$name = shell_exec($cmd);
			$tmp_name = explode("/", $zone);
			if (count($tmp_name) == 3)
			{
				if ("North_Dakota" == $tmp_name[1])//兼容旧的版本，别的还是按最后一个城市显示
				{
					$name=$tmp_name[1]."/".$tmp_name[2];
				}
			}
			
			#如果时区放在zone同一行就会有问题
			while ($line=fgets($handle, 4096))
			{
				if (substr($line, 0, strlen("\t")) === "\t")
				{
					$cmd="echo \"$line\" | awk '{print $1}' | tr -d '\n'";
					$time = shell_exec($cmd);
				}
				else
				{
					break;
				}
			}
			$data=array();
			if (strstr($time, "-"))
			{
				$sec = -(int)time2sec(substr($time,1,100));
				$time = "GMT".$time;
			}
			else
			{
				$sec = time2sec($time);
				$time = "GMT+".$time;
			}			
			$data["TimeZone"] =  $time;				
			$data["Zone"] =  $zone;
			$data["Name"] =  $name;
			$tmp=getDST(YEAR, $zone);
			$data = array_merge($data,$tmp);			
			$timezone[$zone] = $data;
			$timezone_list[$zone] = $sec;
			//echo $zone."  ". $name. " $time ". "\n";
		}
		if (substr($line, 0, strlen("Link")) === "Link")
		{
			$data="";
			$linkname=shell_exec("echo \"$line\" | awk '{print $2}' | tr -d '\n'");
			$zone=shell_exec("echo \"$line\" | awk '{print $3}' | tr -d '\n'");
			$name=shell_exec("echo \"$zone\" | awk -F '/' '{print \$NF}' | tr -d '\n'");

			if (array_key_exists($linkname,$timezone))
			{
				$tmp = $timezone[$linkname];
				$data["TimeZone"] =  $tmp["TimeZone"];				
				$data["Zone"] =  $zone;
				$data["Name"] =  $name;
				$data["Link"] =  $linkname;	
				$tmp=getDST(YEAR, $zone);
				$data = array_merge($data,$tmp);								
				$timezone[$zone] = $data;
				$timezone_list[$zone] = $timezone_list[$linkname];
			}
			else
			{
				$data["Zone"] =  $zone;
				$data["Name"] =  $name;
				$data["Link"] =  $linkname;
				$link_unkown[$zone] = $data;
				//echo "error:unkonw name $zone link $linkname\n";
				//exit();
			}
		}	
    }
    fclose($handle);
    return $timezone;
}


function getOldTimezone()
{
	global $timezone_old,$timezone;
	$xmlFile = 'TimeZoneOld.xml';
	$xml = simplexml_load_file($xmlFile);
	foreach ($xml->DST as $dst) {
		$zone = (string)$dst['Zone'];

		$data = array();
		$data["TimeZone"] =  (string)$dst['TimeZone'];
		$data["Zone"] =  (string)$dst['Zone'];
		$data["Name"] =  (string)$dst['Name'];
		
		$tmp=getDST(YEAR, $zone);
		$data = array_merge($data,$tmp);			
		$timezone[$zone] = $data;
		$timezone_old[$zone] = $data;
	}	
}

function getLinkTimezone()
{
	$filename = 'backward';
	global $link_timezone;
	if (file_exists($filename)) {
		$lines = file($filename, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
		foreach ($lines as $line) {
			if (substr($line, 0, 1) === '#') {
				// 跳过注释行
				continue;
			}
			// 使用正则表达式来匹配Link、TARGET和LINK-NAME
			if (preg_match('/Link\s+([^\s]+)\s+([^\s]+)/', $line, $matches)) {
				$target = $matches[1];
				$linkName = $matches[2];
				$link_timezone[$linkName] = $target;
			}
		}
	} else {
		echo "文件不存在: $filename";
	}

}

function WriteTimezone($for_linux_dev)
{
	global $link_timezone,$timezone_old,$timezone,$timezone_list;
	
	$xml = new XMLWriter();
	if ($for_linux_dev)
	{
		$xml->openUri("TimeZone-dev.xml");
	}
	else
	{
		$xml->openUri("TimeZone.xml");	
	}
	
	// 设置缩进字符串
	$xml->setIndentString("\t");
	$xml->setIndent(true);
	// xml文档开始
	$xml->startDocument('1.0', 'utf-8');

	$xml->startElement("TimeZone");

	foreach ($timezone_old as $timezone_key => $old_attr) {
		$xml->startElement("DST");
		
		$real_timezone = $timezone_key;
		#实际的时区
		$is_link = 0;
		if (array_key_exists($timezone_key, $link_timezone))
		{
			$real_timezone = $link_timezone[$timezone_key];
			$is_link = 1;
		}
		
		$s=$timezone[$real_timezone];
		foreach ($s as $key => $value) {  
            if ($for_linux_dev && ($key == "Zone" || $key == "Link"))
            {
                continue;
            }
			
			if ($key == "TimeZone")
			{
				$time = $timezone_list[$real_timezone];
				$time = sec2GMT($time);
				if ($for_linux_dev) 
				{
					$dev_time= "GMT".$time;
					$xml->writeAttribute($key, $dev_time);  // 属性
				}
				else
				{
					$xml->writeAttribute($key, $time);  // 属性
				}
			}
			else if ($is_link == 1)
			{
				if ($key == "Zone" || $key == "Name")
				{
					$xml->writeAttribute($key, $old_attr[$key]);  // 属性
				}
				else
				{
					$xml->writeAttribute($key, $value);  // 属性
				}
			}
			else
			{
				$xml->writeAttribute($key, $value);  // 属性
			}
		}	    
		$xml->endElement();				  
	}
	$xml->endElement();	 
	$xml->endDocument();
}

$timezone_old=array(); 
$timezone=array();
$link_timezone = array();
$data = ["northamerica", "southamerica", "europe", "australasia", "asia", "africa", "antarctica"];
foreach ($data as  $value) {
	$cmd="sed -i '/^ *#/d' $value";
	shell_exec($cmd);	

	$cmd="sed -i '/STDOFF/d' $value";
	shell_exec($cmd);	    
    
	$tmp = getTimezone($value);
	$timezone = array_merge($timezone,$tmp);

}

getOldTimezone();
echo count($timezone)."\n";
getLinkTimezone();

WriteTimezone(0);
WriteTimezone(1);

//for devices android
{
	$xml = new XMLWriter();
	$xml->openUri("TimeZone-dev-android.xml");
	$xml->setIndentString("\t");
	$xml->setIndent(true);
	$xml->startDocument('1.0', 'utf-8');

	$xml->startElement("TimeZone");
	$index = 0;
	$next_timezone="";
	foreach ($timezone_old as $key1 => $value1) {
        $s=$timezone[$key1];
        $xml->startElement("DST");
        $xml->writeAttribute("Id", (string)$index);  // 属性
        $index++;	
        foreach ($s as $key => $value) {
            if ($key == "Link" || $key == "Type" || $key == "Start" || $key == "End" || $key == "Offset")
            {
                continue;
            }
            if ($key == "Name")
            {
                $xml->writeAttribute("DisplayName", $value);  // 属性
            }
            else if ($key == "Zone")
            {
                $xml->writeAttribute("Name", $value);  // 属性
            }
            else
            {
                $xml->writeAttribute($key, $value);  // 属性
            }
        }		
        $xml->endElement();
	}
	$xml->endElement();
	$xml->endDocument();
}




