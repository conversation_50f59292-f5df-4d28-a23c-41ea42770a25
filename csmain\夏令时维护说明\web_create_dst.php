<?php
$xml = file_get_contents('TimeZone.xml');
$dom = new DOMDocument();
$dom->loadXML($xml);

$dsts = $dom->getElementsByTagName('DST');

$res = '';
foreach ($dsts as $dst) {
  if ($dst->hasAttribute('Type')) {
    $type = $dst->getAttribute('Type');
    $time = $dst->getAttribute('TimeZone');
    $name = $dst->getAttribute('Name');
    $timeZone = "$time $name";
    $start = $dst->getAttribute('Start');
    $end = $dst->getAttribute('End');
    $offset = $dst->getAttribute('Offset');
    // 获取其他属性值...
    $res .= "\"$timeZone\"=>[\"Type\"=>\"$type\", \"Start\"=>\"$start\", \"End\"=>\"$end\", \"Offset\"=>\"$offset\"],\n";
  }
}

echo "后端:";
echo $res;

$result = [];

foreach ($dsts as $dst) {
    $time = $dst->getAttribute('TimeZone');
    $name = $dst->getAttribute('Name');
    
    $label = 'GMT' . $time . ' ' . $name;
    $value = $time . ' ' . $name;
    $result[] = ['label' => $label, 'value' => $value];
}

$data = json_encode($result, JSON_PRETTY_PRINT);

$data = str_replace('"label"', 'label', $data);
$data = str_replace('"value"', 'value', $data);
$data = str_replace('"', '\'', $data);

echo "\n\n前端:";
echo $data;

