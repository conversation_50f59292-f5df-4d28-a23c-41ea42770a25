一、获取最新的时区信息
   1、tzdata获取看时区维护说明

   2、查看ubuntu tzdata版本apt show tzdata，一定要保证是最新的版本，如果ubuntu apt下载不到，可以下载deb进行更新

二、生成我们已有时区列表和对应的变化
   备注:是根据地区获取是否存在夏令时，以及时区是否变化，没有处理新增的时区或者移除的时区。
   1、在脚本目录放入当前我们的TimeZone.xml，重命名为TimeZoneOld.xml文件
   2、执行create_timezone.php---修改const YEAR = 2025（对应的年份）;
   3、比较新旧的TimeZone.xml变化
   校验地址：
   https://www.zeitverschiebung.net/cn/abbr/125
   https://time.is/MIST
   
   4、修改文件保证按时区排序
   5、把新的TimeZone.xml，重命名为TimeZoneOld.xml文件，重新执行create_timezone.php, 生成正确的TimeZone-dev.xml
    备注：注意下这个时区Casey，其实+8 +11都是对的(一个冬令时 一个夏令时)，但是之前我们都是+11所以不变
    6、平台文件替换
    6.1、通过TimeZone.xml和脚本web_create_dst.php生成web夏令时文件，提供给web开发
           替换：csexportlog\GatewayWorker\Applications\AppPush\exportlog\web\config\timeZone.xml
           替换 slim : framework/util/timeZone.php
           替换 slim-webman : app/util/timeZone.php 
    6.2、替换云的csmain\release\conf下的夏令时文件以及csmain.conf里面的md5
    6.3、替换csbas/common_conf下的TimeZone.xml文件，重新打包 csconfig、csconfig-office、csoffice、csresid、cspbxrpc 、csmain四个组件
    6.4、替换web下载目录的文件 web_backend\download\DST    

    
三、生成全新的全世界当前的时区列表(可能有新增/移除)
   1、在脚本目录放入当前我们的TimeZone.xml文件
   2、执行脚本create_new_timezone.php   
   3、生成全新的new-*.xml的文件列表
   
