﻿// DO NOT EDIT. This file (and "version") is generated by <PERSON><PERSON>ake.
// Run CMake configure step to update it.
#ifndef JSON_VERSION_H_INCLUDED
# define JSON_VERSION_H_INCLUDED

# define JSONCPP_VERSION_STRING "1.6.5"
# define JSONCPP_VERSION_MAJOR 1
# define JSONCPP_VERSION_MINOR 6
# define JSONCPP_VERSION_PATCH 5
# define JSO<PERSON>PP_VERSION_QUALIFIER
# define JSONCPP_VERSION_HEXA ((JSONCPP_VERSION_MAJOR << 24) | (JSO<PERSON><PERSON>_VERSION_MINOR << 16) | (JSONCPP_VERSION_PATCH << 8))

#endif // JSON_VERSION_H_INCLUDED
