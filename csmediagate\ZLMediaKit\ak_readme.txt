https://github.com/xia-chu/ZLMediaKit
https://github.com/ireader/media-server
https://github.com/xia-chu/ZLMediaKit/wiki/%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B


cd ZLMediaKit
vi CMakeLists.txt 关闭HLS/MP4/TEST/API
	option(ENABLE_HLS "Enable HLS" false)
	option(ENABLE_MP4 "Enable MP4" false)
	option(ENABLE_TESTS "Enable Tests" false)
	option(ENABLE_API "Enable C API SDK" false)
下载 https://github.com/xia-chu/ZLToolKit 到3rdpart/ZLToolKit目录
mkdir release/linux/Debug -p
mkdir build
cd build
cmake ..
make -j4