; auto-generated by mINI class {

[api]
apiDebug=1
defaultSnap=./www/logo.png
secret=035c73f7-bb6b-4889-a715-d9eb2d1925cc
snapRoot=./www/snap/

[ffmpeg]
bin=ffmpeg
cmd=%s -re -i %s -c:a aac -strict -2 -ar 44100 -ab 48k -c:v libx264 -f flv %s
log=./ffmpeg/ffmpeg.log
snap=%s -i %s -y -f mjpeg -t 0.001 %s

[general]
addMuteAudio=1
enableVhost=0
flowThreshold=1024
maxStreamWaitMS=15000
mediaServerId=jxIfxFT0WUyW4dL9
mergeWriteMS=0
modifyStamp=0
publishToHls=1
publishToMP4=0
resetWhenRePlay=1
streamNoneReaderDelayMS=20000

[hls]
broadcastRecordTs=0
fileBufSize=65536
filePath=./www
segDur=2
segNum=3
segRetain=5

[hook]
admin_params=secret=035c73f7-bb6b-4889-a715-d9eb2d1925cc
enable=0
on_flow_report=https://127.0.0.1/index/hook/on_flow_report
on_http_access=https://127.0.0.1/index/hook/on_http_access
on_play=https://127.0.0.1/index/hook/on_play
on_publish=https://127.0.0.1/index/hook/on_publish
on_record_mp4=https://127.0.0.1/index/hook/on_record_mp4
on_record_ts=https://127.0.0.1/index/hook/on_record_ts
on_rtsp_auth=https://127.0.0.1/index/hook/on_rtsp_auth
on_rtsp_realm=https://127.0.0.1/index/hook/on_rtsp_realm
on_server_started=https://127.0.0.1/index/hook/on_server_started
on_shell_login=https://127.0.0.1/index/hook/on_shell_login
on_stream_changed=https://127.0.0.1/index/hook/on_stream_changed
on_stream_none_reader=https://127.0.0.1/index/hook/on_stream_none_reader
on_stream_not_found=https://127.0.0.1/index/hook/on_stream_not_found
timeoutSec=10

[http]
charSet=utf-8
dirMenu=1
keepAliveSecond=15
maxReqSize=4096
notFound=<html><head><title>404 Not Found</title></head><body bgcolor="white"><center><h1>您访问的资源不存在！</h1></center><hr><center>ZLMediaKit-5.0(build in Nov 18 2020 20:36:59)</center></body></html>
port=80
rootPath=./www
sendBufSize=65536
sslport=443

[multicast]
addrMax=239.255.255.255
addrMin=239.0.0.0
udpTTL=64

[record]
appName=record
fastStart=0
fileBufSize=65536
filePath=./www
fileRepeat=0
fileSecond=3600
sampleMS=500

[rtmp]
handshakeSecond=15
keepAliveSecond=15
modifyStamp=0
port=553
sslport=19350

[rtp]
audioMtuSize=600
clearCount=10
cycleMS=46800000
maxRtpCount=50
videoMtuSize=1400

[rtp_proxy]
checkSource=1
dumpDir=
port=10000
timeoutSec=15

[rtsp]
authBasic=0
directProxy=1
handshakeSecond=15
keepAliveSecond=15
port=555
sslport=332

[shell]
maxReqSize=1024
port=9000

; } ---
