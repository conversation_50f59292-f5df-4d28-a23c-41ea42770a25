#!/bin/sh
ACMD="$1"
CSROUTE_BIN='/usr/local/akcs/csmediagate/bin/csmediagate'
CONFIG_PATH=/usr/local/akcs/csmediagate/conf/csmediagate.conf

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csmediagate()
{
    nohup $CSROUTE_BIN -c $CONFIG_PATH -d >/dev/null 2>&1 &
    echo "Start csmediagate successful"
    if [ -z "`ps -fe|grep "csmediagaterun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csmediagate/scripts/csmediagaterun.sh >/dev/null 2>&1 &
    fi
}
stop_csmediagate()
{
    echo "Begin to stop csmediagaterun.sh"
    kill -9 `ps aux | grep -w csmediagaterun.sh | grep -v grep | awk '{ print $(2) }'`
    echo "Begin to stop csmediagate"
    kill -9 `pidof csmediagate`
    sleep 2
    echo "Stop csmediagate successful"
}

case $ACMD in
  start)
    cnt=`netstat -alnp | grep 553 | grep csmediagate | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csmediagate
    else
        echo "csmediagate is already running"
    fi
    ;;
  stop)
    cnt=`netstat -alnp | grep 553 | grep csmediagate | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csmediagate is already stopping"
    else
        stop_csmediagate
    fi
    ;;
  restart)
    stop_csmediagate
    sleep 1
    start_csmediagate
    ;;
  status)
    cnt=`netstat -alnp | grep 553 | grep csmediagate | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csmediagate is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csmediagate is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

