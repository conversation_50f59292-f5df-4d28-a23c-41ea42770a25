#!/bin/sh
ACMD="$1"
PWD="/usr/local/akcs/csmediagate/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_akcs()
{   
    sh ${PWD}/csmediagate.sh start
}
stop_akcs()
{
    sh ${PWD}/csmediagate.sh stop
}

status_akcs()
{
    sh ${PWD}/csmediagate.sh status
}

uninstall_akcs()
{
    sh ${PWD}/csmediagate.sh stop
    kill -9 `ps aux | grep csmediagaterun.sh |grep -v grep | awk '{print $2}'`
    rm -rf /usr/local/akcs/csmediagate/
}

case $ACMD in
  start)
        start_akcs
    ;;
  stop)
        stop_akcs
    ;;
  uninstall)
        uninstall_akcs
    ;;
  restart)
    stop_akcs
    sleep 1
    start_akcs
    ;;    
  status)
    status_akcs
    ;;  
  *)  
    echo "Usage: sh `basename $0` start|stop|restart|status|uninstall"
    ;;
esac
exit

