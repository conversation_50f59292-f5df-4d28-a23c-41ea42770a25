#!/bin/sh
PROCESS_NAME=csmediagate
PROCESS_START_CMD="bash /usr/local/akcs/csmediagate/scripts/csmediagatectl.sh start"
PROCESS_PID_FILE=/var/run/csmediagate.pid
LOG_FILE=/var/log/csmediagate_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csmediagate/scripts/common.sh"


#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS

times=1
while [ 1 ]
do
    common_run_netstat_detect $PROCESS_NAME "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done

