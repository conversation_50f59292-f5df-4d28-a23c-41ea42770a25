#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   web 的远程启动脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

bash $RSYNC_PATH/install/install.sh $RSYNC_PATH $PROJECT_RUN_PATH