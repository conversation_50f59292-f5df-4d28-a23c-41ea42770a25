CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (csmsip C CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(CSBASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libhiredis.a libevent.so libmysqlclient.so libcppkafka.so librdkafka++.so librdkafka.so libdl.so libz.so libcrypto.so libssl.so libevpp.so libglog.so libprotobuf.so) 

AUX_SOURCE_DIRECTORY(./src SRC_LIST_PRODUCER)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/Rldb SRC_LIST_DB)
#AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/etcd SRC_LIST_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/jsoncpp0.5/src/json SRC_LIST_BASE_JSONCPP)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/redis SRC_LIST_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/metrics SRC_LIST_BASE_METRICS)
link_directories(${CSBASE_DIR} 
${CSBASE_DIR}/thirdlib 
${CSBASE_DIR}/redis/hiredis 
${CSBASE_DIR}/evpp/lib /usr/local/lib)


SET(BASE_LIST_INC ${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/mysql/include ${BASE_SOURCE_DIR}/csbase/Rldb 
                  ${BASE_SOURCE_DIR}/csbase/evpp ${BASE_SOURCE_DIR}/csbase/protobuf ${BASE_SOURCE_DIR}/csbase/etcd ${BASE_SOURCE_DIR}/csbase/session 
				  ${BASE_SOURCE_DIR}/csbase/grpc/cssession ${BASE_SOURCE_DIR}/csbase/redis ${BASE_SOURCE_DIR}/csbase/grpc ${BASE_SOURCE_DIR}/csbase/nsq
				  ${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/include ${BASE_SOURCE_DIR}/csbae/encrypt ${BASE_SOURCE_DIR}/csbase/grpc/gens ${CSBASE_DIR}/encrypt)

ADD_DEFINITIONS( -std=c++11 -g -W -Wall -Werror -Wno-implicit-fallthrough -Wno-unused-parameter -Wno-deprecated -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories( ${BASE_LIST_INC} ${CSBASE_DIR}/metrics
    ./src /usr/local/boost/include /usr/local/grpc/include /usr/local/protobuf/include)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
add_executable(csmsip ${SRC_LIST_PRODUCER} ${SRC_LIST_DB} ${SRC_LIST_BASE_JSONCPP} ${CSBASE_DIR}/nsq/RouteMqProduce.cpp ${SRC_LIST_REDIS} ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_BASE_METRICS})

set_source_files_properties(
   ./src/kafka_consumer.cpp
    PROPERTIES
    COMPILE_FLAGS "-Wno-ignored-qualifiers"
)

set_target_properties(csmsip PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csmsip/lib")
target_link_libraries(csmsip  ${DEPENDENT_LIBRARIES})
