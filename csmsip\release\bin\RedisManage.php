<?php

#redis相信息
const REDISPW = "Akcs#xm2610*"; //redis 密码

class RedisManage {

    public $redis_instance = null;

    function __construct($redisIP, $redisSocket, $redisSentinels) {
        if (strlen($redisSentinels) == 0) {
            $this->redis_instance = $this->ConnectRedis($redisIP, $redisSocket);
        } else {
            $sentinels = explode(',', $redisSentinels);
            //随机打乱数组
            shuffle($sentinels);
            foreach ($sentinels as $val) {
                $sentinel_redis = new Redis();
                $sentinel = explode(':', $val);
                //连接sentinel
                $ret = $sentinel_redis->connect($sentinel[0], $sentinel[1]);
                if ($ret) {
                    $result = $sentinel_redis->rawCommand('SENTINEL', 'masters');
                    $datas = $this->parseArrayResult($result);
                    //不考虑多主
                    $redis = $this->ConnectRedis($datas[0]["ip"], $datas[0]["port"]);
                    if ($redis) {
                        //判断sentinel告知的是不是真正的主库
                        $result = $redis->rawCommand('INFO', 'Replication');
                        if (strstr($result, "role:master")) {
                            $this->redis_instance = $redis;
                            break;
                        } else {
                            //从别的sentinel那里继续查找
                        }
                    }
                }
            }
        }
    }

    function connectRedis($ip, $port) {
        $redis = new Redis();
        $ret = $redis->connect($ip, $port);
        if ($ret) {
            $redis->auth(REDISPW);
            return $redis;
        } else {
            
        }
        return null;
    }

    function getRedisInstance() {
        return $this->redis_instance;
    }

    //这个方法可以将以上sentinel返回的信息解析为数组
    function parseArrayResult(array $data) {
        $result = array();
        $count = count($data);
        for ($i = 0; $i < $count;) {
            $record = $data[$i];
            if (is_array($record)) {
                $result[] = $this->parseArrayResult($record);
                $i++;
            } else {
                $result[$record] = $data[$i + 1];
                $i += 2;
            }
        }
        return $result;
    }

}
?>