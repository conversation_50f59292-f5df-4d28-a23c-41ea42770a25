<?php
require_once (dirname(__FILE__) . '/RedisManage.php');
/**
 * Load userinfo,write to redis
 */
date_default_timezone_set("PRC");


function getDB($dbhost) {
    $dbhost = $dbhost;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "freeswitch";
	$dbport = 3305;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$csmsipConf = parse_ini_file("/usr/local/akcs/csmsip/conf/csmsip.conf");
$dbHost = $csmsipConf["db_ip"];
print_r("db_host=" . $dbHost . "\n");
$db = getDB($dbHost);

$csmsipRedisConf = parse_ini_file("/usr/local/akcs/csmsip/conf/csmsip_redis.conf");
$redisIP = $csmsipRedisConf["userinfo_host"];
$redisPort = $csmsipRedisConf["userinfo_port"];
$redisDb = $csmsipRedisConf["userinfo_db"];
$redisSentinels = $csmsipRedisConf["sentinels"];
print_r("redis_ip=" . $redisIP . ";redis_port=" . $redisPort . ";redis_sentinels=" . $redisSentinels . "\n");
$RedisManage = new RedisManage($redisIP, $redisPort, $redisSentinels);
$redis = $RedisManage->getRedisInstance();
if (null == $redis) {
    print_r("connect redis failed!\n");
    return;
}
$redis->select($redisDb);

$sth = $db->prepare("select a.username,a.password from userinfo a");
$sth->execute();
$userInfoList = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($userInfoList as $row => $userInfo) {
    $redis->set($userInfo["username"], $userInfo["password"]);
    print_r("redis set userinfo,key=" . $userInfo["username"] . ";value=" . $userInfo["password"] . " success.\n");
}
?>
