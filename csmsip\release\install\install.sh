#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-12
# Filename      :   install.sh
# Version       :
# Description   :   csmsip 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME=csmsip

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csmsip    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csmsip
LOG_PATH=/var/log/csmsiplog
CTRL_SCRIPT=csmsipctl.sh
RUN_SCRIPT=csmsiprun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}


# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
FREESWITCH_MYSQL_INNER_IP=$(grep_conf 'FREESWITCH_MYSQL_INNER_IP' $INSTALL_CONF)
FREESWITCH_MYSQL_PORT=$(grep_conf 'FREESWITCH_MYSQL_PORT' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
FREESWITCH_MYSQL_CLUSTER1_INNER_IP=$(grep_conf 'FREESWITCH_DB_CLUSTER1_IP' $INSTALL_CONF || echo "")

# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
if [ -f $APP_HOME/scripts/$CTRL_SCRIPT ]; then
    bash "$APP_HOME"/scripts/$CTRL_SCRIPT stop
    sleep 2
fi


# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*db_ip=.*/db_ip=${FREESWITCH_MYSQL_INNER_IP}/g
    s/^.*db1_ip=.*/db1_ip=${FREESWITCH_MYSQL_CLUSTER1_INNER_IP}/g
    s/^.*db_port=.*/db_port=${FREESWITCH_MYSQL_PORT}/g
    s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g" "$PKG_ROOT"/conf/csmsip.conf

# redis 配置
sed -i "s/^.*userinfo_host=.*/userinfo_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csmsip_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csmsip_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csmsip_redis.conf
fi


echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

if [ -d /usr/local/akcs/csmsip_scripts ]; then
    rm -rf /usr/local/akcs/csmsip_scripts
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME
cp -f "$PKG_ROOT"/version $APP_HOME

cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi
ln -sf libcppkafka.so libcppkafka.so.0.4.0
ln -sf librdkafka.so librdkafka.so.1

cd "$PKG_ROOT"

# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts

# 添加到开机启动
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi

ulimit -c unlimited


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
ENV_CONF_PARAM="
-e KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
-e FREESWITCH_MYSQL_INNER_IP=$(grep_conf 'FREESWITCH_MYSQL_INNER_IP' $INSTALL_CONF)
-e FREESWITCH_MYSQL_PORT=$(grep_conf 'FREESWITCH_MYSQL_PORT' $INSTALL_CONF)
-e REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
-e ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
-e SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
-e FREESWITCH_MYSQL_CLUSTER1_INNER_IP=$(grep_conf 'FREESWITCH_DB_CLUSTER1_IP' $INSTALL_CONF || echo "")
"

if [ `docker ps -a | grep -w $CONTAINER_NAME | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 $APP_NAME"
    app_pids=$(pidof csmsip || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi
    sed -i '/csmsiprun.sh/d' /etc/init.d/rc.local
fi
docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} --restart=always --net=host -v /usr/share/zoneinfo:/usr/share/zoneinfo -v /var/log/csmsiplog:/var/log/csmsiplog -v /var/core:/var/core -v /etc/ip:/etc/ip -v /etc/kdc.conf:/etc/kdc.conf -v /bin/crypto:/bin/crypto --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csmsip/scripts/csmsiprun.sh
sleep 3


#守护进程中会进行环境变量替换配置文件中的内容
