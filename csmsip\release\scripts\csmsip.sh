#!/bin/sh
ACMD="$1"
csmsip_BIN='/usr/local/akcs/csmsip/bin/csmsip'
PROCESS_PID_FILE=/var/run/csmsip.pid
if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

if [ -f $PROCESS_PID_FILE ];then
    pid=`cat $PROCESS_PID_FILE`
else
    #重启之后没有这个pid文件
    pid="xxxxxxxxxx"
fi

start_csmsip()
{
    nohup $csmsip_BIN >/dev/null 2>&1 &
    echo "Start csmsip successful"
    if [ -z "`ps -fe|grep "csmsiprun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csmsip/scripts/csmsiprun.sh >/dev/null 2>&1 &
    fi
}
stop_csmsip()
{
    echo "Begin to stop csmsiprun.sh"
    kill -9 `ps aux | grep -w csmsiprun.sh | grep -v grep | awk '{ print $(2) }'`
    echo "Begin to stop csmsip"
    kill -9 `pidof csmsip`
    sleep 2
    echo "Stop csmsip successful"
}

case $ACMD in
  start)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csmsip
    else
        echo "csmsip is already running"
    fi
    ;;
  stop)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csmsip is already stopping"
    else
        stop_csmsip
    fi
    ;;
  restart)
    stop_csmsip
    sleep 1
    start_csmsip
    ;;
  status)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csmsip is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csmsip is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

