#!/bin/bash
#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/csmsip/scripts/sedconf.sh
PROCESS_NAME=csmsip
PROCESS_START_CMD="/usr/local/akcs/csmsip/scripts/csmsipctl.sh start"
PROCESS_PID_FILE=/var/run/csmsip.pid
LOG_FILE=/var/log/csmsip_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csmsip/scripts/common.sh"
chmod +x /usr/local/akcs/csmsip/scripts/*.sh
csmsip_BIN='/usr/local/akcs/csmsip/bin/csmsip'

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS

$csmsip_BIN >/dev/null 2>&1
