#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'
sed -i "
    s/^.*db_ip=.*/db_ip=${FREESWITCH_MYSQL_INNER_IP}/g
    s/^.*db1_ip=.*/db1_ip=${FREESWITCH_MYSQL_CLUSTER1_INNER_IP}/g
    s/^.*db_port=.*/db_port=${FREESWITCH_MYSQL_PORT}/g
    s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g" /usr/local/akcs/csmsip/conf/csmsip.conf

# redis 配置
sed -i "s/^.*userinfo_host=.*/userinfo_host=${REDIS_INNER_IP}/g" /usr/local/akcs/csmsip/conf/csmsip_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" /usr/local/akcs/csmsip/conf/csmsip_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" /usr/local/akcs/csmsip/conf/csmsip_redis.conf
fi