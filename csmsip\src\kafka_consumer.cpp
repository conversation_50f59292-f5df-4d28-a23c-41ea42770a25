#include <stdexcept>
#include <iostream>
#include <csignal>
#include "cppkafka/consumer.h"
#include "cppkafka/configuration.h"
#include "kafka_transaction_def.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPoolTemplate.h"
#include "AkLogging.h"
#include "kafka_transaction_db.h"
#include "kafka_consumer.h"
#include "AkcsMonitor.h"
#include "CachePool.h"

using cppkafka::Consumer;
using cppkafka::Configuration;
using cppkafka::Message;
using cppkafka::TopicPartitionList;


extern KAFKA_CONSUMER_CONF gstConsumerConf;


void kafka_consumer()
{
    // Construct the configuration
    //配置看rdkafka_conf.c
    Configuration config =
    {
        { "metadata.broker.list", gstConsumerConf.kafka_broker_ip },
        { "group.id", gstConsumerConf.kafka_consumer_group },
        // Disable auto commit
        { "enable.auto.commit", false },
        { "auto.offset.reset", "earliest"}
    };

    // Create the consumer
    Consumer consumer(config);

    // Print the assigned partitions on assignment
    consumer.set_assignment_callback([](const TopicPartitionList & partitions)
    {
        AK_LOG_INFO << "Got assigned: " << partitions;
    });

    // Print the revoked partitions on revocation
    consumer.set_revocation_callback([](const TopicPartitionList & partitions)
    {
        AK_LOG_WARN << "Got revoked: " << partitions;
    });

    // Subscribe to the topic
    consumer.subscribe({ gstConsumerConf.kafka_consumer_topic_name });

    // Now read lines and write them into kafka
    while (1)
    {
        // Try to consume a message
        Message msg = consumer.poll();
        if (msg)
        {
            // If we managed to get a message
            if (msg.get_error())
            {
                // Ignore EOF notifications from rdkafka
                if (!msg.is_eof())
                {
                    AK_LOG_INFO << "[+] Received error notification: " << msg.get_error();
                }
            }
            else
            {
                /*
                维护:
                查看消费信息
                /usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server *************:8520 --group sip_change_group --describe
                读取指定下标的信息
                /usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server *************:8520  --topic sip_change --offset 700  --partition 0 --max-messages 1(只读1条消息)
                */
                //用于运维，因为有可能某条消息处理导致程序崩了，那么就会一直崩掉重启
                if (gstConsumerConf.enable_unread_special_offset)
                {
                    char offset[32];
                    snprintf(offset, sizeof(offset), ",%ld,", msg.get_offset());
                    if (strstr(gstConsumerConf.unread_special_offset_list, offset))
                    {
                        AK_LOG_INFO << "unread special offset " << msg.get_offset();
                        consumer.commit(msg);
                        continue;
                    }
                }
                if (UpdateUserinfoMessage(msg.get_offset(), msg.get_key(), msg.get_payload()) == 0)
                {
                    // Now commit the message,提交到broker的__consumer_offsets topic
                    consumer.commit(msg);
                }
                else
                {
                    //通知运维哪个offset没有处理成功，但是可以继续执行
                    std::string worker_node = "csmsip";
                    std::stringstream alarm_msg;
                    alarm_msg << "massage offset=" << msg.get_offset() << " key=" << msg.get_key() << " payload=" << msg.get_payload();
                    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_CONSUME_KAFKA_SIP);
                    consumer.commit(msg);
                }
            }
        }
    }
}

