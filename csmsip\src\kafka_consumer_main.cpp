#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <iostream>
#include <sstream>
#include <thread>
#include <fcntl.h>
#include "kafka_transaction_def.h"
#include "ConfigFileReader.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPoolTemplate.h"
#include "AkLogging.h"
#include "kafka_consumer.h"
#include "RouteMqProduce.h"
#include "CachePool.h"
#include "AkcsAppInit.h"
#include "HttpServer.h"
#include <KdcDecrypt.h>
#include "Metric.h"


KAFKA_CONSUMER_CONF gstConsumerConf;

void ConfInit()
{
    memset(&gstConsumerConf, 0, sizeof(KAFKA_CONSUMER_CONF));
    CConfigFileReader config_file(CSMSIP_CONF_FILEPATH);

    Snprintf(gstConsumerConf.db_ip, sizeof(gstConsumerConf.db_ip),  config_file.GetConfigName("db_ip"));    
    Snprintf(gstConsumerConf.db1_ip, sizeof(gstConsumerConf.db1_ip),  config_file.GetConfigName("db1_ip"));
    Snprintf(gstConsumerConf.db_usernmae, sizeof(gstConsumerConf.db_usernmae),  config_file.GetConfigName("db_username"));
    
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstConsumerConf.db_password, sizeof(gstConsumerConf.db_password), decrypt_db_passwd.c_str());

    Snprintf(gstConsumerConf.db_database, sizeof(gstConsumerConf.db_database),  config_file.GetConfigName("db_database"));
    const char* db_port = config_file.GetConfigName("db_port");
    gstConsumerConf.db_port = ::atoi(db_port);


    Snprintf(gstConsumerConf.kafka_consumer_topic_name, sizeof(gstConsumerConf.kafka_consumer_topic_name),  config_file.GetConfigName("kafka_consumer_topic_name"));
    Snprintf(gstConsumerConf.kafka_consumer_group, sizeof(gstConsumerConf.kafka_consumer_group),  config_file.GetConfigName("kafka_consumer_group"));
    Snprintf(gstConsumerConf.kafka_broker_ip, sizeof(gstConsumerConf.kafka_broker_ip),  config_file.GetConfigName("kafka_broker_ip"));


    gstConsumerConf.enable_unread_special_offset = 0;
    const char* enable_unread_special_offset = config_file.GetConfigName("enable_unread_special_offset");
    gstConsumerConf.enable_unread_special_offset = ::atoi(enable_unread_special_offset);
    Snprintf(gstConsumerConf.unread_special_offset_list, sizeof(gstConsumerConf.unread_special_offset_list),  config_file.GetConfigName("unread_special_offset_list"));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));
}



/* 初始化数据库连接 */
int DaoInit()
{
    ConnPoolTemplate<DB_CONN_POOL_NUMBER::POOL_NUMBER_DEFAULT>::GetInstance().Init(gstConsumerConf.db_ip, gstConsumerConf.db_usernmae, gstConsumerConf.db_password,
                    gstConsumerConf.db_database, gstConsumerConf.db_port, 1, "csmsip");
    gstConsumerConf.db_num = 1;

    if(strlen(gstConsumerConf.db1_ip) > 0)
    {
        ConnPoolTemplate<DB_CONN_POOL_NUMBER::POOL_NUMBER_1>::GetInstance().Init(gstConsumerConf.db1_ip, gstConsumerConf.db_usernmae, gstConsumerConf.db_password,
                        gstConsumerConf.db_database, gstConsumerConf.db_port, 1, "csmsip");
        
        gstConsumerConf.db_num = 2;
    }
    return 0;
}

int main(int argc, char* argv[])
{

    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2(CSMSIP_PIDFILE))
    {
        printf("another csmsip has been running in this sytem.");
        return -1;
    }


    /* 读取配置文件 */
    ConfInit();
    GlogInit2(argv[0], "csmsiplog");

    /* 初始化数据库连接 */
    int nRet = DaoInit();
    if (0 != nRet)
    {
        AK_LOG_WARN << "DaoInit fialed.";
        GlogClean2();
        return -1;
    }

    nRet = CacheManager::getInstance()->Init("/usr/local/akcs/csmsip/conf/csmsip_redis.conf", "csmsipCacheInstances");
    if (0 != nRet)
    {
        AK_LOG_WARN << "Redis Init failed.";
        return -1;
    }

    std::thread mqProduceThread = std::thread(MQProduceInit);
    std::thread kafka_consumer_thread(kafka_consumer);
    //起http服务线程
    std::thread httpThread(startHttpServer);

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csmsip is starting";

    mqProduceThread.join();
    kafka_consumer_thread.join();
    GlogClean2();
    return 0;
}




