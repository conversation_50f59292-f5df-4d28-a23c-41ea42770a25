#include <stdexcept>
#include <iostream>
#include <csignal>
#include <vector>
#include "kafka_transaction_def.h"
#include "kafka_transaction_db.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPoolTemplate.h"
#include "AkLogging.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "json/json.h"
#include "AkcsMonitor.h"
#include "CachePool.h"

const int g_akcs_app_type = 6;
extern KAFKA_CONSUMER_CONF gstConsumerConf;
void UpdateRedisUserInfo(const KAFKA_CONSUMER_MSG_DETAIL& msg_detail);


int parseSipTransactionMessage(const char* pData, KAFKA_CONSUMER_MSG_DETAIL* msg)
{

    if (!pData)
    {
        AK_LOG_WARN << "parse json pData is NULL";
        return -1;
    }
    Json::Reader reader;
    Json::Value root;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader.parse(pData, root))
    {
        AK_LOG_WARN << "parse json error.data=" << pData;
        return -1;
    }

    int message_type = ATOI(root["messageType"].asString().c_str());
    if (message_type == CONSUMER_MSG_TYPE_INSERT)
    {
        Snprintf(msg->username, sizeof(msg->username),  root["sip"].asString().c_str());
        Snprintf(msg->pwd, sizeof(msg->pwd),  root["pwd"].asString().c_str());
        Snprintf(msg->group, sizeof(msg->group),  root["group"].asString().c_str());
        Snprintf(msg->devicenode, sizeof(msg->devicenode),  root["devicenode"].asString().c_str());
        msg->groupring = ATOI(root["groupring"].asString().c_str());
        msg->type = ATOI(root["type"].asString().c_str());
        msg->communityid = ATOI(root["communityid"].asString().c_str());
        msg->community_type = ATOI(root["communityType"].asString().c_str());
        msg->device_attribute = ATOI(root["deviceAttribute"].asString().c_str());
        msg->message_type = ATOI(root["messageType"].asString().c_str());
        msg->sip_enable = 1; //nullValue时sip应开启
        if(Json::intValue == root["sipEnable"].type())
        {
            msg->sip_enable = root["sipEnable"].asInt();
        }
        else if(Json::stringValue == root["sipEnable"].type())
        {
            msg->sip_enable = ATOI(root["sipEnable"].asString().c_str());
        }
    }
    else if (message_type == CONSUMER_MSG_TYPE_UPDATE_GROUP)
    {
        Snprintf(msg->username, sizeof(msg->username),  root["sip"].asString().c_str());
        msg->groupring = ATOI(root["groupring"].asString().c_str());
        msg->message_type = ATOI(root["messageType"].asString().c_str());
    }
    else if (message_type == CONSUMER_MSG_TYPE_UPDATE_SIPENABLE)
    {
        Snprintf(msg->username, sizeof(msg->username),  root["sip"].asString().c_str());        
        msg->sip_enable = 1; //nullValue时sip应开启
        if(Json::intValue == root["sipEnable"].type())
        {
            msg->sip_enable = root["sipEnable"].asInt();
        }
        else if(Json::stringValue == root["sipEnable"].type())
        {
            msg->sip_enable = ATOI(root["sipEnable"].asString().c_str());
        }
        msg->message_type = ATOI(root["messageType"].asString().c_str());
    }
    else if (message_type == CONSUMER_MSG_TYPE_DELETE_SIP)
    {
        msg->message_type = ATOI(root["messageType"].asString().c_str());
        Snprintf(msg->username, sizeof(msg->username),  root["sip"].asString().c_str());
    }
    else
    {
        AK_LOG_WARN << "parse json error, type error.data= " << pData;
        return -1;
    }
    return 0;

}

int LocalTransactionMessageErrorNotify(const std::string& key, uint64_t offset)
{
    std::string worker_node = "csmsip";
    std::stringstream alarm_msg;
    alarm_msg << "message key=" << key << ",offset=" << offset << " is handle error.";

    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_CONSUME_KAFKA_SIP);
    return 0;
}

int GetUserSipType(const std::string& username)
{
    int sip_type;
    RldbPtr conn = ConnPoolTemplate<POOL_NUMBER_DEFAULT>::GetInstance().GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_FATAL << "Get DB conn failed.";
        return -1;
    }
    
    std::stringstream streamsql;
    streamsql << "select type from userinfo where username = '" << username << "'";

    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        sip_type = ATOI(query.GetRowData(0));
    }
    AK_LOG_INFO << "on delete sip message, sip = "<< username <<  " sip_type = " << sip_type;

    ConnPoolTemplate<POOL_NUMBER_DEFAULT>::GetInstance().ReleaseConnection(conn);
    return sip_type;
}

//TODO:数据库连接的错误 要让程序重启，消息才能马上继续消费，不然要等新的消息过来才能继续被读取
int UpdateUserinfoMessage(uint64_t offset, const std::string& key, const std::string& msg)
{
    char db_cmd[2048] = "";
    KAFKA_CONSUMER_MSG_DETAIL msg_detail;
    memset(&msg_detail, 0, sizeof(msg_detail));
    AK_LOG_INFO << "msg:" << msg;
    if (parseSipTransactionMessage(msg.c_str(), &msg_detail) != 0)
    {
        AK_LOG_WARN << "message key=" << key << ",offset=" << offset << " parse json error. msg=" << msg;
        LocalTransactionMessageErrorNotify(key, offset);
        return 0;
    }

    if (msg_detail.message_type == CONSUMER_MSG_TYPE_INSERT)//不支持数组，因为并发消费处理时候不能保证顺序
    {
        snprintf(db_cmd, sizeof(db_cmd), "INSERT INTO userinfo (username,password,groupname,devicenode,groupring,type,communityid,\
           communityType,deviceAttribute,updatedate,sipEnable) VALUES ('%s','%s','%s','%s',%d,%d,%d,%d,%d,now(),%d);", msg_detail.username, msg_detail.pwd, msg_detail.group, msg_detail.devicenode,
                 msg_detail.groupring, msg_detail.type, msg_detail.communityid, msg_detail.community_type, msg_detail.device_attribute, msg_detail.sip_enable);
    }
    else if (msg_detail.message_type == CONSUMER_MSG_TYPE_UPDATE_GROUP)//不支持数组，因为并发消费处理时候不能保证顺序
    {
        snprintf(db_cmd, sizeof(db_cmd), "UPDATE userinfo set groupring=%d where username='%s'", msg_detail.groupring, msg_detail.username);
    }
    else if (msg_detail.message_type == CONSUMER_MSG_TYPE_UPDATE_SIPENABLE)
    {
        snprintf(db_cmd, sizeof(db_cmd), "UPDATE userinfo set sipEnable=%d where username='%s'", msg_detail.sip_enable, msg_detail.username);
    }
    else if (msg_detail.message_type == CONSUMER_MSG_TYPE_DELETE_SIP)//支持数组，sip复用不会发生
    {
        //判断sip类型,若为app则直接删除,若为设备则更新deleted状态
        if (g_akcs_app_type == GetUserSipType(msg_detail.username))
        {
            snprintf(db_cmd, sizeof(db_cmd), "delete from userinfo where username = '%s'", msg_detail.username);
        }
        else
        {
            snprintf(db_cmd, sizeof(db_cmd), "update userinfo set deleted = 1 where username = '%s'", msg_detail.username);
        }
    }
    else
    {
        AK_LOG_WARN << "message offset=" << offset << " is unkown message type.";
        LocalTransactionMessageErrorNotify(key, offset);
        return 0;
    }

    //UpdateRedisUserInfo(msg_detail);


    for(int db_num = 0; db_num < gstConsumerConf.db_num; db_num++)
    {
        RldbPtr conn = ConnPoolTemplateGetConnection(db_num);
        if (nullptr == conn)
        {
            AK_LOG_FATAL << "Get DB conn failed.";
            return -1;
        }
        conn->BeginTransAction();
        int nRet = conn->Execute(db_cmd);
        if (nRet == -1)
        {
            if (msg_detail.message_type == CONSUMER_MSG_TYPE_INSERT)
            {
                //消费消息时候并不会马上提交信息给kafka，而是自动在合适时间提交，这时候如果程序挂掉，起来重新消费，插入的消息就不具有幂等性
                char cmd[128] = "";
                snprintf(cmd, sizeof(cmd), "select username from userinfo where username='%s'", key.c_str());
                if (conn->IsDataExist(cmd))
                {
                    AK_LOG_WARN << "message key=" << key << ",offset=" << offset << " insert duplicate key!";
                    nRet = 0;
                }
                else
                {
                    //如果因为语法错误，人工介入
                    AK_LOG_WARN << "message key=" << key << ",offset=" << offset << " exec sql error. sql=" << db_cmd;
                    LocalTransactionMessageErrorNotify(key, offset);
                }
            }
            else
            {
                AK_LOG_WARN << "message key=" << key << ",offset=" << offset << " exec sql error. sql=" << db_cmd;
                LocalTransactionMessageErrorNotify(key, offset);
            }
        }
        conn->EndTransAction();
        ConnPoolTemplateReleaseConnection(conn, db_num);
    }
    
    return 0;
}

void UpdateRedisUserInfo(const KAFKA_CONSUMER_MSG_DETAIL& msg_detail)
{
    const char* USER_INFO_INSTANCE = "userinfo";
    CacheManager* pCacheManager = CacheManager::getInstance();
    CacheConn* pCacheConn = pCacheManager->GetCacheConn(USER_INFO_INSTANCE);
    if (!pCacheConn)
    {
        AK_LOG_WARN << "no cache connection for csmsip:" << USER_INFO_INSTANCE;
        return;
    }

    if (msg_detail.message_type == CONSUMER_MSG_TYPE_INSERT)
    {
        pCacheConn->set(msg_detail.username, msg_detail.pwd);
    }
    else if (msg_detail.message_type == CONSUMER_MSG_TYPE_DELETE_SIP)
    {
        pCacheConn->del(msg_detail.username);
    }
    else
    {
        //错误日志在UpdateUserinfoMessage打印过了
    }

    pCacheManager->RelCacheConn(pCacheConn);
}
