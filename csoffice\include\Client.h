#ifndef __CLIENT_H__
#define __CLIENT_H__

//用于描述设备/app的通用类

#include <boost/any.hpp>
#include "AkcsCommonDef.h"
#include "util_cstring.h"
#include "AkcsCommonSt.h"
#include "DevOnlineMng.h"
#include "AKUserMng.h"
#include "OfficeDb.h"
#include "InternalBussinessLimit.h"
#include "dbinterface/office/OfficePersonalAccount.h"

class CClient
{
public:
    CClient();
    ~CClient();
    csmain::DeviceType GetType() const;
    void SetType(csmain::DeviceType t);
    ResidentDev GetDeviceSetting();
    void SetDeviceSetting(const ResidentDev& device_setting);
    void SetAppSetting(const OfficeAccount& app_setting);
    OfficeAccount GetAppSetting();
    void SetDevMacInfo(const MacInfo& mac_info);
    MacInfo GetDevMacInfo();

    void SetAppToken(const CMobileToken& mac_info);
    CMobileToken GetAppToken();
    
    int GetMngAccountID();

    void SetConnOnline();
    void SetConnOffline();
    bool IsOnline(); 
    void GetBussinessLimit(InternalBussinessLimit::BussinessType type, InternalBussinessLimitPtr& bussness_limit);
private:
    void SetContext(const boost::any& context);
    const boost::any& GetContext() const;
    bool IsApp() const;
    bool IsDev() const;

    
private:
    csmain::DeviceType types_ = csmain::COMMUNITY_NONE;
    boost::any context_;
    boost::any mac_info_;
    boost::any app_token_;
    bool conn_online_;
    BussinessLimitMap bussiness_limits_; //业务限流缓存信息
};

typedef std::shared_ptr<CClient> CClientPtr;

#endif//__CLIENT_H__
