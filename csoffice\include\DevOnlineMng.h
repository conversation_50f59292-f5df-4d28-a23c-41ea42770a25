#ifndef __CSOFFICE_ONLINE_MANAGE_H__
#define __CSOFFICE_ONLINE_MANAGE_H__
#include <string>
#include <mutex>
#include <deque>
#include <pthread.h>
#include <unistd.h>
#include "OfficeDb.h"
typedef struct MacInfo_t
{
    char  mac[16];
    int type;
    int flags;
    int firmware_number; 
    int is_personal;
    char uuid[64];
    int init_status;
    int mng_id;
    char node[64];
    int project_type;
    char project_uuid[64];
    char ins_uuid[64];
    int enable_smarthome;
    int is_new_office;
    csmain::DeviceType conn_type;//office/comm/per

    MacInfo_t() {
        memset(this, 0, sizeof(*this));
    }
}MacInfo;
void CreateOnlineMacInfo(MacInfo &macinfo, const ResidentDev &dev_setting);


class DevOnlineMng
{
public:
    DevOnlineMng() {}
    ~DevOnlineMng();
    void InitMacInfo(const std::string &mac, ResidentDev &deviceSetting, MacInfo &macinfo);
    static DevOnlineMng* GetInstance();
    void AddOfficeMac(const MacInfo &msg);
    static void* DevOnlineThread(void* mng);
    int Init();
private:
    void CheckOfficeOnlineMsg();  
    void SendDevOnlineNotifyMsg(const MacInfo& macinfo);
    pthread_t thread_process;
    std::mutex per_online_mutex_;
    std::deque<MacInfo> per_eque_;

    std::mutex comm_online_mutex_;
    std::deque<MacInfo> comm_eque_;

    std::mutex office_online_mutex_;
    std::deque<MacInfo> office_eque_;    
};



#endif // __CSMAIN_ONLINE_MANAGE_H__
