#ifndef __DEVICE_CHECK_H__
#define __DEVICE_CHECK_H__
#include <string>





class CDeviceCheck
{
public:
    CDeviceCheck();
    ~CDeviceCheck();
	
	enum SwitchType
    {
        Landline = 0,
        OfflineNotify = 1,
        AllowPin = 2,
        SIMNotify = 3,
    };

    enum FeaturePlan
    {
        TAB_DEVICES_CHECK_INDEX_DELIVERY = 0,
		TAB_DEVICES_CHECK_INDEX_PIN,
		TAB_DEVICES_CHECK_INDEX_TMPKEY,
		TAB_DEVICES_CHECK_INDEX_FAMILYMEMBER,
    };

	static CDeviceCheck* GetInstance();
    
    bool CheckAuthcodeExist(const std::string & mac, std::string& authcode);
    bool CheckIndoorPlanFlags(const std::string &mac, int firmware_number, int is_single);
    bool CheckTmpKeyPermission(const std::string& node);
    int GetDeviceType(const std::string &firm_ware);
    bool CheckKitDevice(const std::string &mac, const std::string &node);
    bool IsErrorFirmwareForChangeRelay(const std::string &firmware);
private:
    static CDeviceCheck* instance;

};
CDeviceCheck* GetDeviceCheckInstance();

#endif
