#ifndef __EMERGENCY_MSG_CONTROL_H__
#define __EMERGENCY_MSG_CONTROL_H__
#include "AkcsCommonDef.h"
#include <map>
#include <set>
#include <unordered_set>
#include <boost/circular_buffer.hpp>
#include "AkLogging.h"
#include <memory>
#include <mutex>
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/Log/PersonalCapture.h"
struct EntryEmergency;

extern LOG_DELIVERY gstAKCSLogDelivery;

class EmergencyMsg
{
public:
    EmergencyMsg(const std::string& mac, const std::string& msg_uuid, const std::string& device_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type) 
        :mac_(mac), msg_uuid_(msg_uuid), device_uuid_(device_uuid), initiator_(initiator), act_type_(act_type)
    {
    }
    
    ~EmergencyMsg();
        
    std::string GetMac();
   
    std::string GetMsgUUID();

    std::string GetDeviceUUID();
        
    std::string GetInitiator();

    ACT_OPEN_DOOR_TYPE GetActType();
private:
    std::string mac_;
    std::string msg_uuid_;
    std::string device_uuid_;
    std::string initiator_;
    ACT_OPEN_DOOR_TYPE act_type_;
};

typedef std::shared_ptr<EmergencyMsg> EmergencyMsgPtr;
typedef std::weak_ptr<EmergencyMsg> WeakEmergencyMsgPtr;
typedef std::map<std::string, EmergencyMsgPtr> EmergencyMsgList; 
typedef EmergencyMsgList::iterator EmergencyMsgListIter;

typedef std::shared_ptr<EntryEmergency> EntryEmergencyPtr;
typedef std::weak_ptr<EntryEmergency> WeakEntryEmergencyControlPtr;
typedef std::unordered_set<EntryEmergencyPtr> BucketEmergencyStatus;
typedef boost::circular_buffer<BucketEmergencyStatus> WeakEmergencyMsgList;


class EmergencyMsgControl
{
public:
    EmergencyMsgControl();
    
    ~EmergencyMsgControl();
    
    static EmergencyMsgControl* GetInstance();
    
    void InitBucketSize(int size);

    void AddEntry(const EmergencyMsgPtr& msg);
    
    void AddBucketMsg();

    void AddEmergencyMsg(const std::string &key, EmergencyMsgPtr &msg);

    int ExistEmergencyControlMsg(const std::string& key);

    void RemoveEmergencyControlMsg(const std::string& key);

    void InsertIntoTimingWheel(const std::string& mac,const std::string& msg_uuid, const std::string& device_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type);
    
    std::string GenerateKey(const std::string& mac,const std::string& msg_uuid);
     
private:
    EmergencyMsgList emergency_msg_list_;
    std::mutex emergency_msg_mutex_;
    
    std::mutex emergency_msg_buckets_mutex_;
    WeakEmergencyMsgList emergency_msg_buckets_;
    
    static EmergencyMsgControl* instance_;
};
EmergencyMsgControl* GetEmergencyControlInstance();

struct EntryEmergency
{
    explicit EntryEmergency(const WeakEmergencyMsgPtr& weak_msg)
        : weak_msg_(weak_msg)
    {
    }
        
    ~EntryEmergency()
    {
        EmergencyMsgPtr msg_ptr = weak_msg_.lock(); //提升为强引用
        if (msg_ptr)
        {
            std::string mac = msg_ptr->GetMac();
            std::string msg_uuid = msg_ptr->GetMsgUUID();
            std::string initiator = msg_ptr->GetInitiator();
            std::string device_uuid = msg_ptr->GetDeviceUUID();
            ACT_OPEN_DOOR_TYPE act_type = msg_ptr->GetActType();
            std::string key = GetEmergencyControlInstance()->GenerateKey(mac, msg_uuid);
            if (GetEmergencyControlInstance()->ExistEmergencyControlMsg(key))
            {   
                AK_LOG_WARN << "emergency control :" << key << " report status timeout!";
                //记录超时doorlog  
                GetEmergencyControlInstance()->RemoveEmergencyControlMsg(key);
                dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(device_uuid, initiator, act_type, dbinterface::RelayStatus::TIMEOUT, gstAKCSLogDelivery.personal_capture_delivery);
                dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(msg_uuid, device_uuid, dbinterface::RelayStatus::TIMEOUT);
            }
        }
    }
    WeakEmergencyMsgPtr weak_msg_;
};

#endif
