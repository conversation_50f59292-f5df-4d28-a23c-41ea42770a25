#ifndef __Door_Open_MSG_H__
#define __Door_Open_MSG_H__

#include "ReportActLog.h"
#include "NotifyMsgControl.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "AkcsCommonSt.h"

class CNotifyMsg; 
class CDoorOpenMsg : public CNotifyMsg
{
private:
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg_;
    ResidentDev conn_dev_;

public:
    ~CDoorOpenMsg(){}
    CDoorOpenMsg() = default;
    CDoorOpenMsg(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& conn_dev) : act_msg_(act_msg), conn_dev_(conn_dev){}

    int NotifyMsg();

private:

};
#endif //__Door_Open_MSG_H__

