#ifndef __Door_Open_MSG_NEWOFFICE_H__
#define __Door_Open_MSG_NEWOFFICE_H__

#include "ReportActLog.h"
#include "NotifyMsgControl.h"
#include "AkcsCommonSt.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficeCompanyDoorList.h"

class CNotifyMsg; 
class CNewOfficeDoorOpenMsg : public CNotifyMsg
{
private:
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg_;
    ResidentDev conn_dev_;

public:
    ~CNewOfficeDoorOpenMsg(){}
    CNewOfficeDoorOpenMsg() = default;
    CNewOfficeDoorOpenMsg(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& conn_dev) : act_msg_(act_msg), conn_dev_(conn_dev){}

    int NotifyMsg();
    // bool IsUserActType(int act_type);

private:
    // 发送开门通知到cslinker(cslinker调用接口开三方锁并记录日志)
    int OpenSaltoLockNotify();
    int NewOfficeHandleTempkeyUsedNotify();
    int GenerateTmpkeyUsedNotifyJson(project::PROJECT_TYPE type, const string& message_uuid, string& json_str);
    void NotifyWebAccessDoorMsg();
    int GenerateAccessDoorMsg(AccessDoorNotifyMsg& access_door_notify_msg);
    std::string GetEntryExitModeByAccessMode();
    void NewOfficeSpiltFailureActLog(const CompanyDoorList& company_door_info_list, const OfficeCompanyDoorListInfoList& office_company_door_info_list, 
                    const DevicesDoorInfoList& devices_door_info_list, const std::string& report_relay, DoorRelayType relay_type);
    void NewOfficeHandleFailureActLog();
    bool IsPrivateDoor(const OfficeCompanyDoorListInfoList& office_company_door_info_list, const std::string& company_uuid, const std::string& door_uuid);
    std::string GetRelayBindedDoorUUID(const DevicesDoorInfoList& devices_door_info_list, DoorRelayType relay_type, const std::string& relay_index);
};
#endif //__Door_Open_MSG_H__

