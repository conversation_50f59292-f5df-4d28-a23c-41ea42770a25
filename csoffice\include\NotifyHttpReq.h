#ifndef __NOTIFY_HTTP_MSG_H__
#define __NOTIFY_HTTP_MSG_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AK.Base.pb.h"
#include "AkcsHttpRequest.h"
#include "NotifyMsgControl.h"

class CNotifyMsg; //前置声明
class CHttpReqNotifyMsg : public CNotifyMsg
{
public:
    enum HTTP_DATA_TYPE
    {
        FORM = 0,  
        JSON = 1,  
    };

    enum NOTIFY_HTTP_REQ_TYPE
    {
        GET_S3_URL = 1,    
    };
    enum HTTP_REQ_TYPE
    {
        GET = 1,
        POST = 2,    
    };


public:
    CHttpReqNotifyMsg() = default;
    CHttpReqNotifyMsg(const std::string &url, const std::string &data, int data_type = CHttpReqNotifyMsg::FORM)
    {
        url_ = url;
        data_ = data;
        data_type_ = data_type;
        http_type_ = HTTP_REQ_TYPE::POST;
    }

    CHttpReqNotifyMsg(const std::string &url, model::HttpRespuestKV &parma_kv, const AkcsKv &context_kv, NOTIFY_HTTP_REQ_TYPE type)
    {
        url_ = url;
        context_kv_ = context_kv;
        req_type_ = type;
        http_type_ = HTTP_REQ_TYPE::GET;
        parma_kv_ = parma_kv;
    }
    

    CHttpReqNotifyMsg(const CHttpReqNotifyMsg& other)
    {
        url_ = other.url_;
        data_ = other.data_;
        data_type_ = other.data_type_;
        context_kv_ = other.context_kv_;
        req_type_ = other.req_type_;
        http_type_ = other.http_type_;
        parma_kv_ = other.parma_kv_;
    }

    ~CHttpReqNotifyMsg()
    {

    }

    int NotifyMsg();
private:
    std::string url_;
    std::string data_;
    int data_type_;
    AkcsKv context_kv_;
    NOTIFY_HTTP_REQ_TYPE req_type_; 
    HTTP_REQ_TYPE http_type_;
    model::HttpRespuestKV parma_kv_;
};

#endif //__NOTIFY_HTTP_MSG_H__

