#ifndef __NOTIFY_MESSAGE_MSG_H__
#define __NOTIFY_MESSAGE_MSG_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AK.Base.pb.h"
#include "AkcsHttpRequest.h"
#include "AK.Resid.pb.h"
#include "DclientMsgSt.h"
#include "AK.BackendCommon.pb.h"
#include "NotifyMsgControl.h"
#include "AkcsCommonDef.h"
#include "NotifyMsgControl.h"

class CNotifyMsg; //前置声明

class CMessageNotifyMsg : public CNotifyMsg
{

public:
    CMessageNotifyMsg() = default;

    CMessageNotifyMsg(const CMessageNotifyMsg& other)
    {
        base_ = other.base_;
        msg_ = other.msg_;
    }

    CMessageNotifyMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const AK::Server::P2PCommonTxtMsgNotifyMsg &msg)
    {
        base_ = base;
        msg_ = msg;
    }
    ~CMessageNotifyMsg()
    {

    }
    int NotifyMsg();
    std::string GetMsgId(uint32_t msg_id, int role);
private:
    int NotifyDevMsg();
    int NotifyAppMsg();
    
    AK::BackendCommon::BackendP2PBaseMessage base_;
    AK::Server::P2PCommonTxtMsgNotifyMsg msg_;
};


#endif //__NOTIFY_PER_TEXT_MSG_H__

