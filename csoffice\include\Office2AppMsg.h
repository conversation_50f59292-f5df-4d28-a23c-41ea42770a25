#ifndef __OFFICE_2_APP_MSG_H__
#define __OFFICE_2_APP_MSG_H__
#include <string>

#include "util.h"
#include "MsgStruct.h"
#include "PushClient.h"
#include "OfficePushClient.h"

class COffice2AppMsg
{
private:
    MsgStruct msg_;
    AppOfflinePushKV offline_kv_;

public:
    COffice2AppMsg() { ClearMsg(); }
    ~COffice2AppMsg() {}

public:
    void ClearMsg() { ::memset(&msg_, 0, sizeof(msg_)); }
    void SetEncType(MsgEncryptType type) { msg_.enc_type = type; }
    void SetSendType(TransP2PMsgType type) { msg_.send_type = type; }
    void SetClientType(csmain::DeviceType type) { msg_.client_type = type; }
    void SetMsgId(int id) { msg_.msg_id = id; }
    void SetTraceId(uint64_t id) { msg_.traceid = id; }
    void SetClient(const std::string& str) { Snprintf(msg_.client, sizeof(msg_.client), str.c_str()); }
    void SetPushMsgData(const std::string& str) { Snprintf(msg_.push_msg_data, sizeof(msg_.push_msg_data), str.c_str()); }
    void SetOnlineMsgData(const std::string& str) { Snprintf(msg_.msg_data, sizeof(msg_.msg_data), str.c_str()); }
    void InsertOfflineMsgKV(const std::string& key, const std::string& value)
    {
        offline_kv_.insert(map<std::string, std::string>::value_type(key, value));
    }

    /**
     * @brief  发送消息到主站点的APP
     *
     * @param  msg_type     消息类型
     * @return int  0=成功，其他=失败
     */
    int SendMsg(int push_msg_type);

private:
    void SetForcePush(int push) { msg_.force_push = push; }
    // void SetMsgLen(uint32_t len) { msg_.msg_len = len; }
    // void SetOuterPort(int port) { msg_.outer_port = port; }
    // void SetPushMsgLen(uint32_t len) { msg_.push_msg_len = len; }
    // void SetConnChange(bool change) { msg_.conn_change = change; }
    // void SetOuterIp(const std::string& str) { Snprintf(msg_.outer_ip, sizeof(msg_.outer_ip), str.c_str()); }

    /**
     * @brief  一人多套房中 账号转成主站点账号
     *
     * @param   account  账号
     * @return  主站点账号
     */
    std::string AccountToMainSiteAccount(const std::string& account);

    /**
     * @brief  离线推送消息构造
     *
     * @param  msg_type     消息类型
     * @param  main_account 主账号
     * @return int  0=成功，其他=失败
     */
    int BuildOffilePushMsg(int msg_type, const CMobileToken& mobile_token);
};

#endif /* __OFFICE_2_APP_MSG_H__ */
