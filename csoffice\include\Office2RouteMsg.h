#ifndef __RESID_2_ROUTE_H__
#define __RESID_2_ROUTE_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "AK.BackendCommon.pb.h"
#include "DclientMsgSt.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/Log/LogSlice.h"
#include "AkcsCommonSt.h"

class COffice2RouteMsg
{
public:
    COffice2RouteMsg();
    ~COffice2RouteMsg();

    static void GroupVoiceMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void PushLinKerWeather(const SOCKET_MSG_DEV_WEATHER_INFO& weather_info);
    static void SendUpdateConfigByAccount(int type, const std::string &node, const std::string &account, int account_role, 
        int manager_id, int unit_id, const std::string &project_uuid, int project_type);
    static void  SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key);    
    static void GroupIndoorRelayStatusMsg(const std::string&node, const std::string& mac, int relay_status, int relay_type, int project_type);
    static void SendEmergencyNotifyWebMsg(const std::string& alarm_uuid, const std::string& project_uuid);
    static void SendRoute2WebCommonMsg(int msg_type, const std::string &data_json);
    static void SendP2PEmergencyDoorNotifyMsg(const OfficeAccountList& app_list, const std::string& timenow);
    static void SendP2PEmergencyDoorNotifyMsg(const OfficeDevList& dev_list, const std::string& timenow);
    static void SendP2PEmergencyDoorControlMsg(const dbinterface::PmEmergencyDoorLogInfoList& info_list, const std::string& msd_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type, int project_type);
    static void SendP2PAlarmNotifyMsg(project::PROJECT_TYPE project_type, TransP2PMsgType p2p_msg_type, csmain::DeviceType dev_type, const std::string& endpoint, const SOCKET_MSG_ALARM_SEND& alarm_msg);
    static void SendTmpkeyUsedNotify(const PersonalTempKeyUserInfo& tempkey_user_info);
    
    static void PushMsg2Route(const google::protobuf::MessageLite* msg);    
    static AK::BackendCommon::BackendP2PBaseMessage CreateP2PBaseMsg(int msgid, int type, const std::string &uid, csmain::DeviceType conntype, int projecttype);
    static csmain::DeviceType DevProjectTypeToDevType(int projecttype);

    static void SendGeneralData(project::PROJECT_TYPE project_type, uint32_t command_id, const void* data, size_t size);
    static void SendAttendanceClockNotifyWebMsg(const std::string& device_uuid, const std::string& node, uint32_t clock_time, int relay, int security_relay);
    static void SendMusterReportNotifyWebMsg(const std::string& user_uuid,const std::string& account_type, const std::string& office_uuid);
    static void SendAccessDoorNotifyWebMsg(const AccessDoorNotifyMsg& access_door_notify_msg);
};

#endif // __RESID_ROUTE_CLIENT_H__
