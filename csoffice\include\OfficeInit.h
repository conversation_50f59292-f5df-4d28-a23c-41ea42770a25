#ifndef __CSRESID_INIT_H__
#define __CSRESID_INIT_H__

#include <string>

#define MAX_RLDB_CONN 10
#define CONF_COMMON_LEN 64

typedef struct AKCS_CONF_T
{
    // csmain本机配置信息 
    char csmain_outer_ip[CONF_COMMON_LEN];
    char csmain_outer_domain[CONF_COMMON_LEN];
    char csmain_outer_ipv6[CONF_COMMON_LEN];
    int enable_ipv6; //是否使能ipv6
    int log_level; //日志打印级别 LOG_LEVEL_E
    char log_file[CONF_COMMON_LEN];

    // DB配置项 
    int  db_port;
    char db_ip[CONF_COMMON_LEN];
    char db_username[CONF_COMMON_LEN];
    char db_password[CONF_COMMON_LEN];
    char db_database[CONF_COMMON_LEN];

    int  log_db_port;
    char log_db_ip[CONF_COMMON_LEN];
    char log_db_database[CONF_COMMON_LEN];

    int  mapping_db_port;
    char mapping_db_ip[CONF_COMMON_LEN];
    char mapping_db_database[CONF_COMMON_LEN];
    
    // 推送服务器net信息,格式=ip:port
    char push_server_addr[CONF_COMMON_LEN];
    // web服务器net信息,格式=ip
    char web_server_addr[CONF_COMMON_LEN];
    // web服务器net信息,格式=ipv6
    char web_server_ipv6_addr[CONF_COMMON_LEN];
    // 视频回放服务器net信息,格式=ip
    char video_server_addr[CONF_COMMON_LEN];
    // Etcd服务器net信息,格式=ip1:port1;ip2:port2;...
    char etcd_server_addr[128];
    // 会话服务器net信息,格式=ip:port
    char session_server_addr[CONF_COMMON_LEN];

    // 网关的编号
    char gateway_code[16];
    // OEM名称,用于区分推送服务器
    char oem_name[32];
    short oem_num;
    // OEM push AESkey
    char push_AESkey[33];

    // Client端口
    int client_server_port;
    char client_server_allow_ip[256];

    // 视频录制时长
    int video_length;
    // svn版本号
    char svn_version[CONF_COMMON_LEN];
    // Besnatalkd服务配置信息 
    char beanstalk_ip[CONF_COMMON_LEN];
    int beanstalk_port;
    // oss相关参数
    char oss_role_arn[CONF_COMMON_LEN];
    char oss_outer_endpoint[CONF_COMMON_LEN];
    char oss_sts_endpoint[CONF_COMMON_LEN];
    char oss_region_id[CONF_COMMON_LEN];
    char oss_bucket[CONF_COMMON_LEN];

    int reg_etcd;
    int upgrade_status;//云是否处于升级过程中,该表示主要用于监控升级过程中的信令交互监控,升级无事故的相关措施之一
    char tz_md5[CONF_COMMON_LEN];
    char tz_data_md5[CONF_COMMON_LEN];
    int offline_notify;

    // 是否开启限流;0:关闭限流; 1:开启限流
    int limit_switch;

    // 限流速率
    double rate;

    char config_server_ipv4[CONF_COMMON_LEN];
    char config_server_ipv6[CONF_COMMON_LEN];
    int  config_server_port;
    
    char apiurl[CONF_COMMON_LEN];
    int is_aws;

    char web_backend_domain[512]; //WEB后端请求地址
    int download_user_timeout;//设备下载详细数据去重的超时时间

    int stress_test;//压测开关是否打开,0:关(默认，生产环境一定不能开),1:开

    char linker_nsq_topic[CONF_COMMON_LEN];
    char voice_server_ipv4[64];
    char voice_server_ipv6[64];
    char linker_nsq_ip[CONF_COMMON_LEN];
    
    int server_area;

    char route_topic[CONF_COMMON_LEN];
    char filter_msg_list[128];
    
    // smg alexa
    char smg_alexa_addr[CONF_COMMON_LEN];
}AKCS_CONF;
extern AKCS_CONF gstAKCSConf;
extern std::string g_srv_id;

int ConfInit();
int DaoInit();
int DaoReInit();
int DaoRelease();
int LogDeliveryInit();
void TimeTaskInit();

#endif
