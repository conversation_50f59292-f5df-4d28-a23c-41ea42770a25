#ifndef __RESID_ROUTE_CLIENT_H__
#define __RESID_ROUTE_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "RouteClient.h"
#include "RouteClientMng.h"

class CRouteClient;
class COfficeRouteClient;
extern std::string g_logic_srv_id;
typedef std::shared_ptr<COfficeRouteClient> COfficeRouteClientPtr;

class COfficeRouteClient:public CRouteClient
{
public:
    COfficeRouteClient(evpp::EventLoop* loop,
                 const std::string& serverAddr/*ip:port*/,
                 const std::string& name);
    ~COfficeRouteClient();

    static RouteClientPtr CreateClient(const std::string &addr, evpp::EventLoop* loop);
    static void UpdateClient();
    void SetServerInfo(const std::string& id, AK::Base::LogicClientType type);                
    void OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);
    void OnP2PMessage(const std::unique_ptr<CAkcsPdu>& pdu);
private:
    std::mutex route_clis_mutex_;
};

#endif // __RESID_ROUTE_CLIENT_H__