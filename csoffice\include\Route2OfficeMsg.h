#ifndef __ROUTE_2_REISD_H__
#define __ROUTE_2_REISD_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsWebMsgSt.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Resid.pb.h"
#include "BasicDefine.h"
#include "AK.BackendCommon.pb.h"
class CRoute2OfficeMsg
{
public:
    CRoute2OfficeMsg();
    ~CRoute2OfficeMsg();
    static void HandleP2PVoiceMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PSendVoiceMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const AK::Server::P2PSendVoiceMsg &msg);
    static void HandleP2PWeatherInfoMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PRefreshUserConfMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PRefreshDeviceIsAttendanceMsg(const std::unique_ptr<CAkcsPdu> &pdu);

    /*
    static void HandleP2PRemoteOpendoorMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PRemoteOpenSecurityRelayMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2POfficeFromDeviceOpenDoorReq(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO* open_door, const std::string &open_door_type);
    */

private:
};

#endif // __ROUTE_2_REISD_H__
