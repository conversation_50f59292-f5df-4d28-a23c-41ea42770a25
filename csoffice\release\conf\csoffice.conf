
#web sev conf, ip填写web服务器的外网ip,供客户端下载配置文件|rom包升级文件用
csweb_net=**************
csweb_net_ipv6=**************

#cspush conf
cspush_net=***********:8000

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#nsq conf
nsq_route_topic=ak_route

#akcs db conf
akcs_db_ip=***********
akcs_db_port=3306
akcs_db_database=AKCS

#log db conf
log_db_ip=***********
log_db_port=3306
log_db_database=LOG

#mapping db conf
mapping_db_ip=**************
mapping_db_port=3306
mapping_db_database=AKCSMapping

#common db conf
db_username=dbuser01

#配置OEM，用于推送服务的区分,空值就是akuvox
oem_name=Akuvox
#配置和cspush推送的aes加密密钥
push_aeskey=


#svn版本号
svn_version=


#csconfig ip
config_server_ipv4=
config_server_ipv6=
config_server_port=

is_aws=0

web_backend_domain=dev.akuvox.com

nsq_linker_topic=ak_linker
nsq_linker_ip=

server_area=1

#过滤消息ID列表，十六进制形式通过逗号分隔，不需要加0x。eg:124,10f
filter_id_list=

log_encrypt=0
log_trace=1