#!/bin/bash
ACMD="$1"
csoffice_BIN='/usr/local/akcs/csoffice/bin/csoffice'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csoffice()
{
    nohup $csoffice_BIN >/dev/null 2>&1 &
    echo "Start csoffice successful"
    if [ -z "`ps -fe|grep "csofficerun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csoffice/scripts/csofficerun.sh >/dev/null 2>&1 &
    fi
}
stop_csoffice()
{
    echo "Begin to stop csofficerun.sh"
    csofficerunid=`ps aux | grep -w csofficerun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csofficerunid}" ];then
	    echo "csofficerun.sh is running at ${csofficerunid}, will kill it first."
	    kill -9 ${csofficerunid}
    fi
    echo "Begin to stop csoffice"
    kill -9 `pidof csoffice`
    sleep 2
    echo "Stop csoffice successful"
}

case $ACMD in
  start)
    start_csoffice
    ;;
  stop)
    stop_csoffice
    ;;
  restart)
    stop_csoffice
    sleep 1
    start_csoffice
    ;;
  status)
    cnt=`ps -ef |grep -E "csoffice$" | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csoffice is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csoffice is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

