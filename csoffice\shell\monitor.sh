#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2024-02-04
# Filename      :   install.sh
# Version       :
# Description   :   远程启动脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码（git上的代码）同步到的目录（线上服务器）
PROJECT_RUN_PATH=$2        #项目运行路径（项目最终运行的目录）
MIDDLEWARE=$3

ENV=$4
HOST=$5
INNER_IP=$6
NICKNAME=$7
VENDOR=$8                  #云产商
HOSTNAME=$9
IPV6=${10}
LINE=${11}

METRIC_PORT=9992
CONTAINER_NAME=csoffice_exporter

INSTALL_CONF=${RSYNC_PATH}/app_backend_install.conf
IP_FILE=/etc/ip

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

register(){
    SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' ${IP_FILE})
    CONSUL_ADDRESS=$(grep_conf 'consul_address' ${INSTALL_CONF})
    IS_INTRANET=$(grep_conf 'prometheus_is_intranet' ${INSTALL_CONF})
    
    if [ "$CONSUL_ADDRESS" == "" ];then
        echo "===未配置Consul地址, 无法自动添加监控==="
        exit 0
    fi
    
    Instance_ip=''
    if [ "$IS_INTRANET" == "1" ];then
        Instance_ip=$SERVER_INNER_IP
    else
        Instance_ip=$HOST
    fi
    cd ${RSYNC_PATH}

cat << EOF > "reg_${CONTAINER_NAME}.json"
{
    "ID": "${CONTAINER_NAME}_${HOSTNAME}",
    "Name": "${CONTAINER_NAME}",
    "Address": "${Instance_ip}",
    "Port": ${METRIC_PORT},
    "meta": {
        "group": "${ENV}",
        "instance": "${Instance_ip}:${METRIC_PORT}",
        "hostname": "${HOSTNAME}"
    }
}
EOF


curl_cmd="curl -m 3 --request PUT --retry 3 --retry-delay 1 -d @reg_${CONTAINER_NAME}.json http://${CONSUL_ADDRESS}:8500/v1/agent/service/register?replace-existing-checks=1"
{ [[ "$ENV" == *"test"* || "$ENV" == *"dev"* ]] && ($curl_cmd || true) || $curl_cmd; }
        
}

register