#include <string.h>
#include "AKCSDao.h"
#include "OfficeDb.h"

int DaoGetOfficeDevListByNode(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    //获取设备
    ResidentDeviceList devlist;
    if (0 == dbinterface::ResidentDevices::GetNodeDevList(node, devlist))
    {
        for (const auto dev : devlist)
        {
            if (dev.dev_type != DEVICE_TYPE_INDOOR)
            {
                continue;
            }
            COMMUNITY_DEVICE_SIP tmp_device;
            memset(&tmp_device, 0, sizeof(tmp_device));
            tmp_device.type = dev.dev_type;
            snprintf(tmp_device.uuid, sizeof(tmp_device.uuid),  "%s", dev.uuid);
            device.push_back(tmp_device);
        }
    }

    OfficeAccount main_account;
    if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(node, main_account))
    {
        COMMUNITY_DEVICE_SIP tmp_device;
        memset(&tmp_device, 0, sizeof(tmp_device));
        snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), "%s", main_account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }

    
    return 0;
}

int DaoGetOfficeDevListNewOffice(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device)
{

    OfficeAccount main_account;
    if (0 != dbinterface::OfficePersonalAccount::GetUidAccount(node, main_account))
    {
        return 0;
    }
    
    COMMUNITY_DEVICE_SIP tmp_device;
    memset(&tmp_device, 0, sizeof(tmp_device));
    snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), "%s", main_account.uuid);
    tmp_device.type = DEVICE_TYPE_APP;
    device.push_back(tmp_device);

    //获取设备
    OfficeDeviceAssignDevList devlist;
    dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByPerUUID(main_account.uuid, devlist);
    for (auto &dev : devlist)
    {
        COMMUNITY_DEVICE_SIP tmp_device;
        memset(&tmp_device, 0, sizeof(tmp_device));
        tmp_device.type = DEVICE_TYPE_INDOOR;
        snprintf(tmp_device.uuid, sizeof(tmp_device.uuid),  "%s", dev.devices_uuid);
        device.push_back(tmp_device);        
    }
  
    return 0;
}

std::string DaoGetCompanyUUIDByUser(int role, const std::string& personal_account_uuid)
{
    std::string company_uuid;
    OfficeAccount main_account;
    if (ACCOUNT_ROLE_OFFICE_NEW_PER == role)
    {
        OfficePersonnelInfo office_personnel_info;
        if (0 != dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(personal_account_uuid, office_personnel_info))
        {
            AK_LOG_WARN << "Get OfficePersonnel failed: PersonalAccountUUID=" << personal_account_uuid;
        }
        company_uuid = office_personnel_info.office_company_uuid;
    }
    else if (ACCOUNT_ROLE_OFFICE_NEW_ADMIN == role)
    {
        OfficeAdminInfo office_admin_info;
        if (0 != dbinterface::OfficeAdmin::GetOfficeAdminByPersonalAccountUUID(personal_account_uuid, office_admin_info))
        {
            AK_LOG_WARN << "Get OfficeAdmin failed: PersonalAccountUUID=" << personal_account_uuid;
        }
        company_uuid = office_admin_info.office_company_uuid;
    }
    return company_uuid;
}



