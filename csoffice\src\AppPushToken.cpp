#include "AppPushToken.h"
#include "AKUserMng.h"
#include "MsgControl.h"
#include "util.h"
#include "dbinterface/AppPushTokenDB.h"
#include "dbinterface/Token.h"
#include "RldbQuery.h"
#include "ConnectionPool.h"
#include "dbinterface/ProjectUserManage.h"


#define TABLE_NAME_APP_PUSH_TOKEN   "AppPushToken"


CAppPushToken* GetAppPushTokenInstance()
{
    return CAppPushToken::GetInstance();
}

CAppPushToken* CAppPushToken::instance = NULL;

CAppPushToken::CAppPushToken()
{

}

CAppPushToken::~CAppPushToken()
{

}

CAppPushToken* CAppPushToken::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAppPushToken();
    }

    return instance;
}

int CAppPushToken::updateAppPushInfo(const std::string strUid, const CMobileToken& cToken)
{
/*
    if(gstAKCSConf.is_aws)
    {
        //http到阿里云
        GetMsgControlInstance()->PostAwsPushTokenHttpReq(strUid, cToken);
        return 0;
    }
*/
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    token_info.app_oem = cToken.AppOem();
    token_info.mobile_type = cToken.MobileType();
    token_info.common_version = cToken.CommonVersion();
    token_info.is_dy_iv = cToken.IsDyIv();
    snprintf(token_info.fcm_token, sizeof(token_info.fcm_token), "%s", cToken.FcmToken().c_str());
    snprintf(token_info.token, sizeof(token_info.token), "%s", cToken.Token().c_str());
    snprintf(token_info.voip_token, sizeof(token_info.voip_token), "%s", cToken.VoipToken().c_str());
    snprintf(token_info.language, sizeof(token_info.language), "%s", cToken.Language().c_str());
    snprintf(token_info.oem_name, sizeof(token_info.oem_name), "%s", cToken.OemName().c_str());
    snprintf(token_info.node, sizeof(token_info.node), "%s", cToken.UidNode().c_str());

    int ret = dbinterface::AppPushToken::UpdateAppPushInfo(strUid, token_info);
    return ret;
}
int CAppPushToken::deleteAppPushToken(const std::string strUid)
{
/*
    if(gstAKCSConf.is_aws)
    {
        //http到阿里云
        GetMsgControlInstance()->PostAwsDelPushTokenHttpReq(strUid);
        return 0;
    }
*/
    int ret = dbinterface::AppPushToken::DeleteAppPushToken(strUid);
    return ret;
}

int CAppPushToken::isAppPushTokenExist(const std::string strUid)
{
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    int ret = dbinterface::AppPushToken::GetAppPushTokenByUid(strUid, token_info);
    return ret;
}

int CAppPushToken::getAppPushTokenByUid(const std::string strUid, CMobileToken& cToken)
{
    int ret = -1;
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(strUid, token_info))
    {
        cToken.setMobileType(token_info.mobile_type);
        cToken.setFcmToken(token_info.fcm_token);
        cToken.setToken(token_info.token);
        cToken.setVoipToken(token_info.voip_token);
        cToken.setCommonVersion(token_info.common_version);
        cToken.setLanguage(token_info.language);
        cToken.setOemName(token_info.oem_name);
        cToken.setAppOem(token_info.app_oem);
        cToken.setIsDyIv(token_info.is_dy_iv);
        ret = 0;
    }
    return ret;
}

int CAppPushToken::getAppDcliVerByUid(const std::string& uid)
{
    int ver = 0;
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(uid, token_info))
    {
        ver = token_info.common_version;
    }
    return ver;
}


int CAppPushToken::getAppTokenByUid(const std::string uid, std::string& app_token)
{
    int ret = -1;
    TokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::Token::GetTokenInfoByAccount(uid, token_info))
    {
        app_token = token_info.app_token;
    }
    return ret;
}



int CAppPushToken::getUidsApptokenByNode(const std::string node, std::vector<CMobileToken>& oVec)
{
    AppPushTokenList token_list;
    if (0 == dbinterface::AppPushToken::GetUidsAppTokenByNode(node, token_list))
    {
        for (const auto token_info : token_list)
        {
            CMobileToken cToken;
            cToken.setMobileType(token_info.mobile_type);
            cToken.setFcmToken(token_info.fcm_token);
            cToken.setToken(token_info.token);
            cToken.setVoipToken(token_info.voip_token);
            cToken.setCommonVersion(token_info.common_version);
            cToken.setLanguage(token_info.language);
            cToken.setOemName(token_info.oem_name);
            cToken.setAppOem(token_info.app_oem);
            cToken.setIsDyIv(token_info.is_dy_iv);
            oVec.push_back(cToken);
        }
    }

    return 0;
}



