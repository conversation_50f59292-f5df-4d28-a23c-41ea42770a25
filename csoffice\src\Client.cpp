
#include "Client.h"

CClient::CClient()
: types_(csmain::COMMUNITY_NONE)
{
   conn_online_ = false;
}

CClient::~CClient()
{

}

csmain::DeviceType CClient::GetType() const
{
    return types_;
}
void CClient::SetType(csmain::DeviceType t)
{
    types_ = t;
}

void CClient::SetDeviceSetting(const ResidentDev& device_setting)
{
    SetContext(device_setting);
}

ResidentDev CClient::GetDeviceSetting()
{
    const ResidentDev& dev_setting(boost::any_cast<const ResidentDev&>(GetContext()));
    return dev_setting;
}

void CClient::SetAppSetting(const OfficeAccount& app_setting)
{
    SetContext(app_setting);
}

OfficeAccount CClient::GetAppSetting()
{
    const OfficeAccount& app_setting(boost::any_cast<const OfficeAccount&>(GetContext()));
    return app_setting;
}

void CClient::SetDevMacInfo(const MacInfo& mac_info)
{
    mac_info_ = mac_info;
}

MacInfo CClient::GetDevMacInfo()
{
    const MacInfo& mac_info(boost::any_cast<const MacInfo&>(mac_info_));
    return mac_info;
}

void CClient::SetAppToken(const CMobileToken& app_token)
{
    app_token_ = app_token;
}

CMobileToken CClient::GetAppToken()
{
    const CMobileToken& app_token(boost::any_cast<const CMobileToken&>(app_token_));
    return app_token;
}


int CClient::GetMngAccountID()
{
    if (IsDev())
    {
        //const ResidentDev& dev_setting(boost::any_cast<const ResidentDev&>(GetContext()));
        //return dev_setting.manager_account_id;
    }
    else if (IsApp())
    {
        //const ResidentPerAccount& stApp(boost::any_cast<const ResidentPerAccount&>(GetContext()));
        //return stApp.parent_id;//没有查从账号对应的管理员id
    }
    return -1;
}


bool CClient::IsApp() const
{
    if (types_ == csmain::PERSONNAL_APP || types_ == csmain::COMMUNITY_APP || types_ == csmain::OFFICE_APP)
    {
        return 1;
    }
    return 0;
}

bool CClient::IsDev() const
{
    if (types_ == csmain::PERSONNAL_DEV || types_ == csmain::COMMUNITY_DEV || types_ == csmain::OFFICE_DEV)
    {
        return 1;
    }
    return 0;    
}

void CClient::SetContext(const boost::any& context)
{
    context_ = context;
}

const boost::any& CClient::GetContext() const
{
    return context_;
}

void CClient::SetConnOnline()
{
    conn_online_ = true;
}

bool CClient::IsOnline()
{
    return conn_online_;
}

void CClient::SetConnOffline()
{
    conn_online_ = false;
}

void CClient::GetBussinessLimit(InternalBussinessLimit::BussinessType type, InternalBussinessLimitPtr& bussness_limit)
{
    auto iter = bussiness_limits_.find(type);
    if (iter != bussiness_limits_.end()) 
    {
        bussness_limit = bussiness_limits_[type];
    }
    else
    {
        bussness_limit = std::make_shared<InternalBussinessLimit>(type);
        bussiness_limits_[type] = bussness_limit;
    }
}

