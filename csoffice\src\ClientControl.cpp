#include "OfficeServer.h"
#include "ClientControl.h"

extern OfficeServer* g_office_srv_ptr;

CClientControl* CClientControl::instance_ = nullptr;

CClientControl* GetClientControlInstance()
{
    return CClientControl::GetInstance();
}

CClientControl::CClientControl()
{
}

CClientControl::~CClientControl()
{
}

CClientControl* CClientControl::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new CClientControl();
    }

    return instance_;
}

void CClientControl::Init()
{
}

//发送消息给csmain,csmain会透传给客户端
int CClientControl::SendTransferMsg(const std::string& client, const csmain::DeviceType type, const unsigned char* data, uint32_t size)
{
    g_office_srv_ptr->SendMsg2Main(client, type, data, size);
    return 0;
}

int CClientControl::SendTransferMsg(MsgStruct& msg)
{
    g_office_srv_ptr->SendMsg2Main(msg);
    return 0;
}

