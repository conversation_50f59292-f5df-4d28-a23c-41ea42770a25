#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "util.h"
#include "DevOnlineMng.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "MsgControl.h"
#include "MsgToControl.h"
#include "OfficeInit.h"
#include "OfficeServer.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"

extern OfficeServer* g_office_srv_ptr;

void CreateOnlineMacInfo(MacInfo &macinfo, const ResidentDev &dev_setting)
{
    macinfo.is_personal  = 0;
    macinfo.type  = dev_setting.dev_type;
    macinfo.flags  = dev_setting.flags;
    macinfo.firmware_number  = dev_setting.firmware;
    macinfo.init_status = dev_setting.init;
    macinfo.project_type = dev_setting.project_type;

    macinfo.conn_type = csmain::DeviceType::OFFICE_DEV;
    
    macinfo.mng_id = dev_setting.project_mng_id;
    snprintf(macinfo.node, sizeof(macinfo.node), "%s", dev_setting.node);
    snprintf(macinfo.mac, sizeof(macinfo.mac), "%s", dev_setting.mac);
    snprintf(macinfo.uuid, sizeof(macinfo.uuid), "%s", dev_setting.uuid);
}

DevOnlineMng::~DevOnlineMng()
{
    LOG_INFO << "Release DevOnlineMng.";
}

DevOnlineMng* DevOnlineMng::GetInstance()
{
    static DevOnlineMng dev_online_mng;
    return &dev_online_mng;
}

void DevOnlineMng::AddOfficeMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(office_online_mutex_);
    office_eque_.push_back(msg);    
}

void DevOnlineMng::InitMacInfo(const std::string &mac, ResidentDev &deviceSetting, MacInfo &macinfo)
{    
    CreateOnlineMacInfo(macinfo, deviceSetting);
 
    ProjectUserInfo project_info;
    ProjectInfo project;
    project.GetOfficeProjectInfo(macinfo.mng_id, macinfo.node, project_info);

    OfficeInfo office_info(macinfo.mng_id);
    macinfo.is_new_office = office_info.IsNew();

    macinfo.init_status = 1;
    macinfo.enable_smarthome = project_info.enable_smarthome;
    Snprintf(macinfo.project_uuid, sizeof(macinfo.project_uuid),  project_info.project_uuid);
    Snprintf(macinfo.ins_uuid, sizeof(macinfo.ins_uuid),  project_info.ins_uuid);
}

void DevOnlineMng::SendDevOnlineNotifyMsg(const MacInfo& macinfo)
{
    SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
    memset(&online_msg, 0, sizeof(online_msg));
    snprintf(online_msg.mac, sizeof(online_msg.mac),"%s", macinfo.mac);
    online_msg.unread_voice_count = 0;
    if (macinfo.type == DEVICE_TYPE_INDOOR)
    {
        int count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(macinfo.uuid);
        if (count > 0)
        {
            online_msg.unread_voice_count = count;
        }
    }
    snprintf(online_msg.uuid, sizeof(online_msg.uuid),"%s", macinfo.uuid);

    GetMsgToControlInstance()->SendOnlineNotifyMsg(macinfo.mac, online_msg);
}


void DevOnlineMng::CheckOfficeOnlineMsg()
{

    MacInfo macinfo;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(office_online_mutex_);
        if (office_eque_.size() == 0)
        {
            return;
        }
        office_eque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        macinfo = tmp_deque.front();
        tmp_deque.pop_front();

        //设备在线后返回通知消息
        SendDevOnlineNotifyMsg(macinfo);     
    }
}

int DevOnlineMng::Init()
{   
    pthread_create(&thread_process, NULL, &DevOnlineThread, (void*)this);
    LOG_INFO << "Create DevOnlineMng thread.";
    return 0;
}


void* DevOnlineMng::DevOnlineThread(void* this_mng)
{
    DevOnlineMng *mng = (DevOnlineMng*)this_mng;
    while (true)
    {
        mng->CheckOfficeOnlineMsg();
        sleep(2);
    }

    return NULL;
}


