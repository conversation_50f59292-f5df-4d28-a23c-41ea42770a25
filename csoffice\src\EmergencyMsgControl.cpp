#include "EmergencyMsgControl.h"

EmergencyMsgControl::EmergencyMsgControl()
{
}

EmergencyMsgControl::~EmergencyMsgControl()
{

}

EmergencyMsg::~EmergencyMsg()
{

}

std::string EmergencyMsg::GetMac() 
{
    return mac_;
}

std::string EmergencyMsg::GetMsgUUID() 
{
    return msg_uuid_;
}

std::string EmergencyMsg::GetDeviceUUID()
{
    return device_uuid_;
}

std::string EmergencyMsg::GetInitiator()
{
    return initiator_;
}

ACT_OPEN_DOOR_TYPE EmergencyMsg::GetActType()
{
    return act_type_;
}

EmergencyMsgControl* EmergencyMsgControl::instance_ = NULL;

EmergencyMsgControl* EmergencyMsgControl::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new EmergencyMsgControl();
    }

    return instance_;
}

EmergencyMsgControl* GetEmergencyControlInstance()
{
    return EmergencyMsgControl::GetInstance();
}

void EmergencyMsgControl::InitBucketSize(int size)
{
    emergency_msg_buckets_.resize(size);
}

void EmergencyMsgControl::AddEntry(const EmergencyMsgPtr& msg)
{
    std::lock_guard<std::mutex> lock(emergency_msg_buckets_mutex_);
    EntryEmergencyPtr entry_status(new EntryEmergency(msg));
    {
        emergency_msg_buckets_.back().insert(entry_status);
    }
}

void EmergencyMsgControl::AddBucketMsg()
{
    std::lock_guard<std::mutex> lock(emergency_msg_buckets_mutex_);
    emergency_msg_buckets_.push_back(BucketEmergencyStatus());
}

void EmergencyMsgControl::AddEmergencyMsg(const std::string &key, EmergencyMsgPtr &msg)
{
    std::lock_guard<std::mutex> lock(emergency_msg_mutex_);
    emergency_msg_list_[key] = msg;
} 

int EmergencyMsgControl::ExistEmergencyControlMsg(const std::string& key)
{
    std::lock_guard<std::mutex> lock(emergency_msg_mutex_);
    EmergencyMsgListIter it = emergency_msg_list_.find(key);
    if (it != emergency_msg_list_.end())
    {
        return 1;
    }
    return 0;
}

void EmergencyMsgControl::RemoveEmergencyControlMsg(const std::string& key)
{
    std::lock_guard<std::mutex> lock(emergency_msg_mutex_);
    emergency_msg_list_.erase(key);
}    

std::string EmergencyMsgControl::GenerateKey(const std::string& mac,const std::string& msg_uuid)
{
    std::string key = mac + "_" + msg_uuid;
    return key;
}

void EmergencyMsgControl::InsertIntoTimingWheel(const std::string& mac,const std::string& msg_uuid, 
        const std::string& device_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type)
{
    std::string key = GetEmergencyControlInstance()->GenerateKey(mac, msg_uuid);
    EmergencyMsgPtr msg = std::make_shared<EmergencyMsg>(mac, msg_uuid, device_uuid, initiator, act_type);
    GetEmergencyControlInstance()->AddEntry(msg);
    GetEmergencyControlInstance()->AddEmergencyMsg(key, msg);
}
