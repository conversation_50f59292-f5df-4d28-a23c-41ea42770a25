#include <stdlib.h>
#include <stdio.h>
#include <string>
#include <tuple>
#include <ctime>
#include <arpa/inet.h>
#include "AK.Linker.pb.h"
#include "CachePool.h"
#include "MsgControl.h"
#include "MsgToControl.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "AkcsPduBase.h"
#include "AkcsCommonDef.h"
#include "DevOnlineMng.h"
#include "OfficeServer.h"
#include "CsmainAES256.h"
#include "Account.h"
#include "ProjectUserManage.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "ClientControl.h"
#include "RouteMqProduce.h"
#include "Md5.h"
#include "AKCSDao.h"
#include "AkcsHttpRequest.h"
#include "NotifyMsgControl.h"
#include "OfficeInit.h"
#include "Office2RouteMsg.h"
#include "AKUserMng.h"
#include "OfficeDevices.h"
#include "AppPushToken.h"
#include "NotifyHttpReq.h"
#include "OfficeDb.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/ProjectUserManage.h"


extern OfficeServer* g_office_srv_ptr;
extern RouteMQProduce* g_nsq_producer;
extern AKCS_CONF gstAKCSConf;

CMsgControl* GetMsgControlInstance()
{
    return CMsgControl::GetInstance();
}

CMsgControl::CMsgControl()
{

}

CMsgControl::~CMsgControl()
{

}

CMsgControl* CMsgControl::instance = NULL;

CMsgControl* CMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgControl();
    }

    return instance;
}

int MsgParseByMac(const MsgStruct* acc_msg, ResidentDev &dev, ParseMacCallback cb, void *st)
{
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    std::string mac = acc_msg->client;
    
    memset(&dev, 0, sizeof(dev));
    if (g_office_srv_ptr->GetDevSetting(mac, dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed. mac is " << mac;
        return -1;
    }

    uint32_t size = ntohs(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    if (cb(payload, st) < 0)
    {
        AK_LOG_WARN << "ParseReportVoiceMsg failed.";
        return -1;
    }
    return 0;
}

static bool IsMsgLimit(const MsgStruct* acc_msg, InternalBussinessLimit::BussinessType limit_type)
{
    CClientPtr client;
    if (g_office_srv_ptr->GetDevClientFromMac(acc_msg->client, client) == 0)
    {
        InternalBussinessLimitPtr limitptr;
        client->GetBussinessLimit(limit_type, limitptr);
        if(limitptr != nullptr)
        {
            int limit_status = limitptr->AddBussiness(acc_msg);
            if(limit_status == InternalBussinessLimit::LimitStatus::LIMITED)
            {                
                AK_LOG_INFO << acc_msg->client << " enter current limiting, type: " << limit_type;
                return true;
            }
        }
    }
    return false;
}

int CMsgControl::OnDeviceReportStatusMsg(SOCKET_MSG_NORMAL* msg, const MsgStruct* acc_msg)
{
    SOCKET_MSG_REPORT_STATUS reportStatusMsg;
    memset(&reportStatusMsg, 0, sizeof(SOCKET_MSG_REPORT_STATUS));
    char* payload = (char*)msg->data;
    uint32_t msg_version = (msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET;
    uint32_t data_size = ::ntohs(msg->data_size);    
    if (GetMsgParseHandleInstance()->ParseReportStatusMsg(payload, &reportStatusMsg, data_size, msg_version) < 0)
    {
        AK_LOG_WARN << "ParseReportStatusMsg failed.";
        return -1;
    }

    //2023,chenyc,拆分csmain之后,在resid这边只需要处理一部分新的业务,后续的上报状态后的新业务也需要在这里处理，不能再在csmain处理了
    //每次设备重新上报状态信息,都需要从数据库获取最新的设备状态信息
    std::string mac = acc_msg->client;
    
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        AK_LOG_WARN << "ParseReportStatusMsg get Mac Fail. mac=" << mac;
        return -1;
    }
    dev.is_attendance = dbinterface::OfficeDevices::GetIsAttendanceByUUID(std::string(dev.uuid));
    if (DatabaseExistenceStatus::QUERY_ERROR == dev.is_attendance)
    {
        AK_LOG_WARN << "Failed to find device is attendance on db. mac: " << mac;
        // db错误也照常发考勤打卡通知
        dev.is_attendance = DatabaseExistenceStatus::EXIST;
    }

    g_office_srv_ptr->SetDevSetting(mac, dev);

    MacInfo mac2;
    CreateOnlineMacInfo(mac2, dev);
    if (dev.conn_type == csmain::DeviceType::OFFICE_DEV)
    {
        DevOnlineMng::GetInstance()->AddOfficeMac(mac2);
    }
    return 0;
}

//只做在社区，语音留言上报
int CMsgControl::OnDeviceReportVoiceMsg(const MsgStruct* acc_msg)
{
    PersonalVoiceMsgInfo per_voice_msg;
    memset(&per_voice_msg, 0, sizeof(per_voice_msg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseReportVoiceMsg, (void *)&per_voice_msg) < 0)
    {
        AK_LOG_WARN << "ParseReportVoiceMsg failed.";
        return -1;
    }

   
    MacInfo info;
    memset(&info, 0, sizeof(info));
    if (g_office_srv_ptr->GetMacInfo(dev.mac, info) < 0)
    {
        AK_LOG_WARN << "GetMacInfo failed. mac is " << dev.mac;
        return -1;
    }
    snprintf(per_voice_msg.project_uuid, sizeof(per_voice_msg.project_uuid), "%s", info.project_uuid);

    std::string msg_uuid;
    std::string prefix = dbinterface::ProjectUserManage::GetServerTag() + "-";
    dbinterface::ProjectUserManage::GetUUID(prefix, msg_uuid);
    snprintf(per_voice_msg.uuid, sizeof(per_voice_msg.uuid), "%s", msg_uuid.c_str());
    snprintf(per_voice_msg.mac, sizeof(per_voice_msg.mac), "%s", dev.mac);
    snprintf(per_voice_msg.dev_uuid, sizeof(per_voice_msg.dev_uuid), "%s", dev.uuid);
    snprintf(per_voice_msg.location, sizeof(per_voice_msg.location), "%s", dev.location);
    if (0 != dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsg(per_voice_msg))
    {
        AK_LOG_WARN << "InsertPersonalVoiceMsg failed.";
        return -1;
    }
    
    //插入插入PersonalVoiceMsgList
    if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_FAMILY)
    {
        //查找家庭所有用户和室内机的uuid
        std::vector<COMMUNITY_DEVICE_SIP> apps;
        std::vector<PERSONNAL_DEVICE_SIP> per_apps;
        DaoGetOfficeDevListByNode(per_voice_msg.uid, apps);
        for (const auto& app : apps)
        {
            AddPersonalVoiceMsgNode(prefix, app.uuid, per_voice_msg, app.type);
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_INDOOR)
    {
        //下发给室内机
        ResidentDev dev;
        if (0 == dbinterface::ResidentDevices::GetSipDev(per_voice_msg.uid, dev))
        {
            AddPersonalVoiceMsgNode(prefix, dev.uuid, per_voice_msg, DEVICE_TYPE_INDOOR);
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_APP)
    {
        //下发给app
        OfficeAccount account;
        if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(per_voice_msg.uid, account))
        {
            AddPersonalVoiceMsgNode(prefix, account.uuid, per_voice_msg, DEVICE_TYPE_APP);
        }
    }
    return 0;
}

void CMsgControl::AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, const PersonalVoiceMsgInfo &per_voice_msg, int type)
{
    PersonalVoiceMsgNode node;
    memset(&node, 0, sizeof(node));
    std::string tmp_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, tmp_uuid);

    snprintf(node.receiver_uuid, sizeof(node.receiver_uuid), "%s", receiver_uuid.c_str());
    snprintf(node.uuid, sizeof(node.uuid), "%s", tmp_uuid.c_str());
    snprintf(node.msg_uuid, sizeof(node.msg_uuid), "%s", per_voice_msg.uuid);
    node.type = type;

    dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsgList(node);
}


int CMsgControl::OnDeviceRequestVoiceMsgUrl(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestVoiceMsgUrl, (void *)&url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgUrl failed.";
        return -1;
    }

    PersonalVoiceMsgInfo per_voice_msg;
    std::string file_url;
    //TODO msg uuid直接获取语音文件会存在越权问题
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByUUID(url_msg.uuid, per_voice_msg))
    {
        file_url = per_voice_msg.file_url;
        std::size_t pos2 =  file_url.find("/group");
        if (pos2 == std::string::npos)
        {
            //存oss的流程

            model::HttpRespuestKV parma_kv;
            parma_kv.insert(map<std::string, std::string>::value_type("Node", "SuperManage"));
            parma_kv.insert(map<std::string, std::string>::value_type("Path", file_url));
            
            char url[1024];
            snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/common/capture/getLink", gstAKCSConf.web_backend_domain);

            AkcsKv kv;
            kv.insert(map<std::string, std::string>::value_type("mac", dev.mac));
            kv.insert(map<std::string, std::string>::value_type("mac_uuid", dev.uuid));
            kv.insert(map<std::string, std::string>::value_type("voice_uuid", url_msg.uuid));
            CHttpReqNotifyMsg notify_msg(url, parma_kv, kv, CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL);
            GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);            
            return 0;
        }
        
        //以下是存fdfs的流程
        size_t pos = file_url.find("/M");
        if (std::string::npos != pos)
        {
            //获取到 /M00/05/CB/rBIp3GMpNwqADMrHAAEqVhPHnOw417.wav
            std::string file_remote = file_url.substr(pos + 1);
            time_t timer = time(nullptr);
            char time_sec[16] = {0};
            snprintf(time_sec, 16, "%ld", timer);
            file_remote += "ak_fdfs";
            file_remote += time_sec;
            std::string token = akuvox_encrypt::MD5(file_remote).toStr();
            if (!dev.is_ipv6)
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv4, file_url.c_str(), token.c_str(), time_sec);
            }
            else
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv6, file_url.c_str(), token.c_str(), time_sec);
            }
        }
        GetMsgToControlInstance()->SendVoiceMsgUrl(dev.mac, dev.uuid, url_msg.uuid, url_msg.url);
    }
    return 0;
}

int CMsgControl::OnDeviceRequestDelVoiceMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestDelVoiceMsg, (void *)&url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgUrl failed.";
        return -1;
    }


    if (0 != dbinterface::PersonalVoiceMsg::DelVoiceMsgInfoByIndoorUUID(url_msg.uuid, dev.uuid))
    {
        AK_LOG_WARN << "DelVoiceMsgInfoByUUID failed.";
        return -1;
    }

    return 0;
}

//个人终端用户App-android上报身份识别给接入服务器
int CMsgControl::OnAndroidReportStatusMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL msg;
    memcpy(&msg, socket_msg, sizeof(SOCKET_MSG_NORMAL));
    
    SOCKET_MSG_PERSONNAL_APP_CONF app_config; //
    memset(&app_config, 0, sizeof(app_config));
    if (CMsgParseHandle::ProcessAppReportStatusMsg(socket_msg, app_config) < 0)
    {
        AK_LOG_WARN << "ProcessAppReportStatusMsg failed.";
        return -1;
    } 

    if (strlen(app_config.user) == 0)
    {
        AK_LOG_WARN << "parameter error report user is null!";
        return -1;
    }


    std::string main_user_account = app_config.user;
    std::string  report_user_account = app_config.user;
    if (strchr(app_config.user, '@') == nullptr)
    {
        report_user_account = app_config.user;
        dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(app_config.user, main_user_account);
    }
    else
    {
        PerAccountUserInfo user_info;
        //旧版本没有多套房，故两个account相同
        if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(app_config.user, user_info))
        {
            main_user_account = user_info.main_user_account;
            report_user_account = user_info.main_user_account;
        }
    }
    
    OfficeAccount account;
    if (0 != dbinterface::OfficePersonalAccount::GetUserAccount(main_user_account, account))
    {
        AK_LOG_WARN << "Can not found main_user_account:" << main_user_account;
        return -1;
    }
    

    CMobileToken token;
    GetAppPushTokenInstance()->getAppPushTokenByUid(account.account, token);
    
    g_office_srv_ptr->SetAppSetting(account.account, account, token);
    AK_LOG_INFO << "App reportstatus uid:" << account.account << " username:" << account.name << " aes dy iv: " << token.IsDyIv() ;
    return 0;
}

int CMsgControl::OnIOSReportStatusMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg)
{
    OnAndroidReportStatusMsg(socket_msg, acc_msg);
    return 0;
}

int CMsgControl::OnConnChange(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    InnerConnInfo* conninfo = (InnerConnInfo*)msg->data;
    if (conninfo->type == CONN_INFO_TYPE::DISCONNECT)
    {
        if (conninfo->conn_type == csmain::OFFICE_APP)
        {
            g_office_srv_ptr->SetAppOffline(conninfo->uid);
            AK_LOG_INFO << "App is offline  uid:" << conninfo->uid << " username:" << conninfo->username;
        }
    }
    else if (conninfo->type == CONN_INFO_TYPE::HEARTBEAT)
    {
        if (conninfo->conn_type == csmain::OFFICE_APP)
        {
            g_office_srv_ptr->SetAppOnline(conninfo->uid);
            AK_LOG_INFO << "App hearbeat  uid:" << conninfo->uid << " username:" << conninfo->username;
        }
        else    //设备
        {
            CClientPtr dev;
            if (g_office_srv_ptr->GetDevClientFromMac(conninfo->uid, dev) != 0)
            {
                return 0;
            }

            //REPORT_RELAY限流处理
            InternalBussinessLimitPtr limitptr;
            dev->GetBussinessLimit(InternalBussinessLimit::BussinessType::REPORT_RELAY, limitptr);
            if(limitptr != nullptr)
            {
                int limit_status = limitptr->CheckBussinessLimit();
                if(limit_status == InternalBussinessLimit::LimitStatus::LIMITED)
                {
                    AK_LOG_INFO << "Heartbeat handel bussiness limited";
                    MsgStruct msg_buf;
                    memset(&msg_buf, 0, sizeof(MsgStruct));
                    limitptr->GetMsgBuffer(msg_buf);
                    GetMsgControlInstance()->OnDeviceReportRelayStatusMsg(&msg_buf, 1);
                }
            }
            
        }
    }
    else if (conninfo->type == CONN_INFO_TYPE::LOGOUT)
    {
        if (conninfo->conn_type == csmain::OFFICE_APP)
        {
            g_office_srv_ptr->RemoteAppInfo(conninfo->uid);
            AK_LOG_INFO << "App is logout  uid:" << conninfo->uid << " username:" << conninfo->username;
        }
    }    
    
    return 0;
}

int CMsgControl::OnAppForceLogout(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    InnerConnInfoApp* appinfo = (InnerConnInfoApp*)msg->data;
    std::string node = appinfo->node;
    std::string user_account = appinfo->account; 
    std::string main_site = appinfo->main_site;
    std::string temp_nfc;
    std::string temp_ble;
    OfficeAccount account_info;
    //app强制登出刷新nfc和ble
    if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(user_account,account_info))
    {
        temp_nfc = account_info.nfc_code;
        temp_ble = account_info.ble_code;
        bool is_need_update = false;
        if (0 != temp_nfc.length())
        {
            std::string nfccode = GenerateNFCCode();
            AK_LOG_INFO << "updateNFCCode, Code: " << nfccode << "Node :" << node << " User Account :" << user_account;
            if(0 == dbinterface::ProjectUserManage::UpdateNfcCodeByAccount(user_account,nfccode))
            {
                is_need_update = true;
            }
        }
        
        if (0 != temp_ble.length())
        {
            std::string blecode = GenerateBLECode();
            AK_LOG_INFO << "updateBLECode, Code: " << blecode << "Node :" << node << " User Account :" << user_account;
            if(0 == dbinterface::ProjectUserManage::UpdateBleCodeByAccount(user_account,blecode))
            {
                is_need_update = true;
            }
        }

        if (is_need_update)
        {
            int project_type = project::OFFICE;
            OfficeInfo office_info(account_info.parent_uuid);
            if (office_info.IsNew())
            {
                project_type = project::OFFICE_NEW;
            }
            COffice2RouteMsg::SendUpdateConfigByAccount(CSMAIN_UPDATE_CONFIG_RF_CHANGE, node, appinfo->account, appinfo->real_site_role, 
                                appinfo->manager_id, appinfo->unit_id, office_info.UUID(), project_type);

        }
    }

    return 0;
}
//6.7办公接收设备relay状态上报并展示
int CMsgControl::OnDeviceReportRelayStatusMsg(const MsgStruct* acc_msg, int already_check)
{
    //限流判断
    if(already_check == 0)
    {
        if(IsMsgLimit(acc_msg, InternalBussinessLimit::BussinessType::REPORT_RELAY))
        {
            return 0;
        }
    }

    int ret = 0;
    SOCKET_MSG_RELAY_STATUS relay_status_msg;
    memset(&relay_status_msg, 0, sizeof(SOCKET_MSG_RELAY_STATUS));
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseReportRelayStatusMsg, (void *)&relay_status_msg) < 0)
    {
        AK_LOG_WARN << "ParseRelayStatusMsg failed.";
        return -1;
    }
    MacInfo info;
    memset(&info, 0, sizeof(info));
    if (g_office_srv_ptr->GetMacInfo(dev.mac, info) < 0)
    {
        AK_LOG_WARN << "GetMacInfo failed. mac is " << dev.mac;
        return -1;
    }
    //根据解析的TraceID向设备回ACK
    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac),  dev.mac);
    Snprintf(common_ack.msg_type, sizeof(common_ack.msg_type),  relay_status_msg.msg_type);
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id),  relay_status_msg.trace_id);
    common_ack.result = 1;


    //回ack
    if (GetMsgToControlInstance()->SendCommonAckMsg(MSG_FROM_DEVICE_REPORT_INPUT_STATUS, common_ack) != 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed";
        return -1;
    }
    
    //更新数据库的door relay status 
    ret = dbinterface::ResidentDevices::UpdateDoorRelayStatus(dev.mac,relay_status_msg.door_relay_status, relay_status_msg.door_se_relay_status);
    if(ret == -1)
    {
        AK_LOG_WARN << "Update Relay Status failed. mac is " << dev.mac; 
    }
    
    return 0;
    
}

int CMsgControl::OnDeviceRequestWeatherInfoMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_REQ_WEATHER_WAY req_weather_way;
    memset(&req_weather_way, 0, sizeof(req_weather_way));
    
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestWeatherMsg, (void *)&req_weather_way) < 0)
    {
        AK_LOG_WARN << "OnDeviceRequestWeatherInfoMsg failed.";
        return -1;
    }
    
    AK_LOG_INFO << dev.mac << " request weather, manual_update : " << req_weather_way.manual_update;
    
    // 获取设备的国家-城市-地区
    SOCKET_MSG_DEV_WEATHER_INFO weather_info;
    OfficeInfo office_info(dev.project_mng_id); 
    if (!office_info.InitSuccess()) 
    {
        AK_LOG_WARN << "OnDeviceRequestWeatherInfoMsg failed.";
        return -1;
    }
    
    ::snprintf(weather_info.mac, sizeof(weather_info.mac), "%s", dev.mac);
    ::snprintf(weather_info.city, sizeof(weather_info.city), "%s", office_info.City().c_str());
    ::snprintf(weather_info.states, sizeof(weather_info.states), "%s", office_info.States().c_str());
    ::snprintf(weather_info.country, sizeof(weather_info.country), "%s", office_info.Country().c_str());
    
    if (1 == req_weather_way.manual_update)
    {
        // 手动刷新 : 直接请求家居的天气接口获取
        COffice2RouteMsg::PushLinKerWeather(weather_info);
    }
    else
    {
        // 定时刷新 :     缓存存在直接返回
        CacheManager* cache_manager = CacheManager::getInstance();
        CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_weather); //获取与redis实例的tcp连接
        if (cache_conn)
        {
            //  redis key 格式 : 国家-城市-地区
            char weather_redis_key[256];
            ::snprintf(weather_redis_key, sizeof(weather_redis_key), "%s-%s-%s", weather_info.country, weather_info.states, weather_info.city);
            if (cache_conn->isExists(weather_redis_key))
            {
                std::vector<std::string> weather_vec;
                std::string weather_value = cache_conn->get(weather_redis_key);
                SplitString(weather_value, "!", weather_vec);
                
                // weather!temperature!humidity,判断weather_vec长度为3,防止取下标出现段错误
                if (weather_vec.size() == 3)
                {
                    ::snprintf(weather_info.weather, sizeof(weather_info.weather), "%s", weather_vec[0].c_str());
                    ::snprintf(weather_info.temperature, sizeof(weather_info.temperature), "%s", weather_vec[1].c_str());
                    ::snprintf(weather_info.humidity, sizeof(weather_info.humidity), "%s", weather_vec[2].c_str());

                    // 发送天气信息给设备
                    GetMsgToControlInstance()->SendDevWeatherInfoMsg(MSG_TO_DEVICE_REPORT_WEATHER_MSG, weather_info);

                    // redis key存在,释放redis连接后return
                    cache_manager->RelCacheConn(cache_conn);
                    return 0;
                }
            }
            // key不存在,释放redis连接,请求家居的天气接口
            cache_manager->RelCacheConn(cache_conn);
        }

        // 定时刷新 :     未查到缓存, 请求家居的天气接口
        COffice2RouteMsg::PushLinKerWeather(weather_info);
    }
    return 0;
}

int CMsgControl::OnSendDevListChangeMsg(const std::string& uid)
{
    GetMsgToControlInstance()->SendOfficeDevListChangeMsg(MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE, uid);
    return 0;
}
