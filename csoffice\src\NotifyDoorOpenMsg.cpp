#include <cstring>
#include "NotifyDoorOpenMsg.h"
#include "doorlog/RecordOfficeLog.h"
#include "doorlog/RecordNewOfficeLog.h"
#include "doorlog/RecordActLog.h"
#include "MsgControl.h"
#include "gid/SnowFlakeGid.h"
#include "dbinterface/OfficeMessage.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/SaltoLock.h"
#include "Office2RouteMsg.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/DevicesDoorList.h" 
#include "dbinterface/Log/ParkingLog.h"
#include "doorlog/RecordParkingLog.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

int CDoorOpenMsg::NotifyMsg()
{
    if (RecordActLog::GetInstance().IsUserActType(act_msg_.act_type))
    {
        if (strlen(act_msg_.per_id) < 2)
        {
            Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), "visitor");
            Snprintf(act_msg_.room_num, sizeof(act_msg_.room_num), "--");
            RecordActLog::GetInstance().SetCaptureAction(act_msg_);
        }
        else
        {
            RecordOfficeLog::GetInstance().OfficeModeHandle(act_msg_);
        }
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::CALL)
    {
        RecordOfficeLog::GetInstance().RecordOfficeCallLog(act_msg_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY)
    {
        PersonalTempKeyUserInfo tempkey_user_info;
        RecordOfficeLog::GetInstance().RecordOfficeTmpKeyLog(act_msg_, tempkey_user_info);
        
        // 开门成功才通知 tempkey使用
        if (act_msg_.resp == 0 && (tempkey_user_info.creator.size() > 0))
        {
            COffice2RouteMsg::SendTmpkeyUsedNotify(tempkey_user_info);
            AK_LOG_INFO << "SendTmpkeyUsedNotify tempkey = " << act_msg_.initiator << ", creator = " << tempkey_user_info.creator;
        }
        else{
            AK_LOG_INFO << "SendTmpkeyUsedNotify failed: resp=" << act_msg_.resp << ", creator = " << tempkey_user_info.creator;
        }
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR)
    {
        RecordOfficeLog::GetInstance().RecordOfficeRemoteLog(act_msg_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::TEMP_CAPTURE)
    {
        Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), act_msg_.initiator);
        if (dbinterface::PersonalCapture::AddTemperatureCapture(act_msg_) < 0)
        {
            AK_LOG_WARN << "Add temperature capture failed.";
            return -1;
        }
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)
    {
        // 内开门 Exit Button类型:Initiated By显示--，Key显示--，Company显示--
        Snprintf(act_msg_.key, sizeof(act_msg_.key), "--");
        Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), "--");
    }
    else
    {
        AK_LOG_WARN << "invalid open door active type: " << act_msg_.act_type;
        return -1;
    }
    AK_LOG_INFO << "office open door msg active type: " << act_msg_.act_type << " mac:" << conn_dev_.mac;
    dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery);

    return 0;
}
