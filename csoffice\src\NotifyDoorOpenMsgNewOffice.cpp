#include <cstring>
#include "NotifyDoorOpenMsgNewOffice.h"
#include "doorlog/RecordOfficeLog.h"
#include "doorlog/RecordNewOfficeLog.h"
#include "doorlog/RecordActLog.h"
#include "MsgControl.h"
#include "gid/SnowFlakeGid.h"
#include "dbinterface/OfficeMessage.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/SaltoLock.h"
#include "Office2RouteMsg.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/DevicesDoorList.h" 
#include "dbinterface/Log/ParkingLog.h"
#include "doorlog/RecordParkingLog.h"
#include "util_virtual_door.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

int CNewOfficeDoorOpenMsg::NotifyMsg()
{
    if (RecordActLog::GetInstance().IsUserActType(act_msg_.act_type))
    {
        if (strlen(act_msg_.per_id) < 2)
        {
            Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), "visitor");
            Snprintf(act_msg_.room_num, sizeof(act_msg_.room_num), "--");
            RecordActLog::GetInstance().SetCaptureAction(act_msg_);
        }
        else
        {
            RecordNewOfficeLog::GetInstance().NewOfficeModeHandle(act_msg_);
        }
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::CALL)
    {
        RecordNewOfficeLog::GetInstance().RecordNewOfficeCallLog(act_msg_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY)
    {
        (void)NewOfficeHandleTempkeyUsedNotify();
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR)
    {
        RecordNewOfficeLog::GetInstance().RecordNewOfficeRemoteLog(act_msg_);
    }
    else if (RecordActLog::GetInstance().EmergencyType(act_msg_))
    {
        RecordNewOfficeLog::GetInstance().RecordNewOfficeEmergencyControlLog(act_msg_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)
    {
        RecordNewOfficeLog::GetInstance().RecordOfficeInwardUnlockLog(act_msg_);
    }
    else
    {
        AK_LOG_WARN << "invalid open door active type: " << act_msg_.act_type;
        return -1;
    }
    AK_LOG_INFO << "newoffice open door msg active type: " << act_msg_.act_type  << " mac:" << conn_dev_.mac;

    // 设备lockdown时上报的是2, 记录下capture_action,把resp转换成失败
    if (act_msg_.resp == (int)CAPTURE_LOG_RET_TYPE::LOCKDOWN) 
    {
        act_msg_.resp = (int)CAPTURE_LOG_RET_TYPE::FAILURE;
        Snprintf(act_msg_.capture_action, sizeof(act_msg_.capture_action), open_door_lockdown_on);
    }

    std::time_t timestamp = std::time(nullptr);
    uint32_t clock_time = static_cast<uint32_t>(timestamp);
    act_msg_.capture_time = clock_time;

    if (act_msg_.is_attendance == DatabaseExistenceStatus::EXIST) {
        if (strlen(act_msg_.account) > 0 && RecordActLog::GetInstance().IsUserAttendanceActType(act_msg_.act_type)) {
            COffice2RouteMsg::SendAttendanceClockNotifyWebMsg(act_msg_.dev_uuid, act_msg_.account, clock_time, DoornumToRelayStatus(act_msg_.relay), DoornumToRelayStatus(act_msg_.srelay));
        }
    }

    // 开门成功  开三方锁，通知web进出门消息
    if(act_msg_.resp == 0)
    {
        OpenSaltoLockNotify();

        //只有进出门才通知web
        if (act_msg_.access_mode != (int)AntiPassbackAccessMode::NORMAL)
        {
            NotifyWebAccessDoorMsg();
        }
    }

    if (act_msg_.resp == 0)
    {
        dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery);
    }
    else
    {
        NewOfficeHandleFailureActLog();
    }

    if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK && act_msg_.mng_type == 0 && act_msg_.resp == CAPTURE_LOG_RET_TYPE::SUCCESS) {
        PARKING_LOG parking_log;
        RecordParkingLog::GetInstance().NewParkingHandle(act_msg_, parking_log, conn_dev_);
        RecordParkingLog::GetInstance().RecordParkingVehicleLog(parking_log);
    }
    return 0;
}

void CNewOfficeDoorOpenMsg::NewOfficeHandleFailureActLog()
{
    // 开门失败有上报relay，要拆成多条记录
    if (strlen(act_msg_.relay) > 0 || strlen(act_msg_.srelay) > 0)
    {
        // 获取Relay所在的Company信息
        CompanyDoorList company_door_info_list;
        dbinterface::DevicesDoorList::GetOfficeCompanyDoorByDevicesUUID(conn_dev_.uuid, company_door_info_list);

        // 获取Door所在的Company信息
        OfficeCompanyDoorListInfoList office_company_door_info_list;
        dbinterface::OfficeCompanyDoorList::GetOfficeCompanyDoorListByDevicesUUID(conn_dev_.uuid, office_company_door_info_list);

        // 获取设备Door和relay的绑定关系
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(conn_dev_.uuid, devices_door_info_list);

        // 处理relay失败日志
        NewOfficeSpiltFailureActLog(company_door_info_list, office_company_door_info_list, devices_door_info_list, act_msg_.relay, DoorRelayType::RELAY);

        // 处理security_relay失败日志
        NewOfficeSpiltFailureActLog(company_door_info_list, office_company_door_info_list, devices_door_info_list, act_msg_.srelay, DoorRelayType::SECURITY_RELAY);
    }
    else
    {
        // 开门失败 未上报relay 直接插入
        dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery);
    }
    return;
}


std::string CNewOfficeDoorOpenMsg::GetRelayBindedDoorUUID(const DevicesDoorInfoList& devices_door_info_list, DoorRelayType relay_type, const std::string& relay_index)
{
    std::string door_uuid;

    // 1 2 3 4 转换成 A B C D
    std::string controlled_relay = GetControlledRelayByRelayIndex(ATOI(relay_index.c_str()));
    for (const auto& door_info : devices_door_info_list)
    {
        if (door_info.enable && door_info.relay_type == relay_type && strcmp(door_info.controlled_relay, controlled_relay.c_str()) == 0)
        {
            door_uuid = door_info.uuid;
            break;
        }
    }
    return door_uuid;
}

bool CNewOfficeDoorOpenMsg::IsPrivateDoor(const OfficeCompanyDoorListInfoList& office_company_door_info_list, const std::string& company_uuid, const std::string& door_uuid)
{
    for (const auto& company_door_info : office_company_door_info_list)
    {
        // 判断door是否属于该company
        if (strcmp(company_door_info.devices_door_list_uuid, door_uuid.c_str()) != 0)
        {
            continue;
        }

        // 判断company是否匹配
        if (strcmp(company_door_info.office_company_uuid, company_uuid.c_str()) != 0)
        {
            continue;
        }

        // 判断door是否为私有door
        if (company_door_info.type == OfficeCompanyDoorListInfoType::PRIVATE && strlen(company_door_info.to_public_key) == 0)
        {
            return true;
        }
    }
    return false;
}

// 处理开门失败relay失败日志
void CNewOfficeDoorOpenMsg::NewOfficeSpiltFailureActLog(const CompanyDoorList& company_door_info_list, const OfficeCompanyDoorListInfoList& office_company_door_info_list, 
                    const DevicesDoorInfoList& devices_door_info_list, const std::string& report_relay, DoorRelayType relay_type)
{
    for (unsigned int index = 0; index < report_relay.size(); ++index)
    {
        // 初始化company_uuid 和 door_name_list
        Snprintf(act_msg_.company_uuid, sizeof(act_msg_.company_uuid), "");
        Snprintf(act_msg_.door_name_list, sizeof(act_msg_.door_name_list), "");

        // 那个relay失败了，report_relay[index] 1 2 3 4 => relay_pow_value 1 2 4 8
        int relay_pow_value = ATOI(std::string(1, report_relay[index]).c_str());

        for (const auto& compnay_door_info : company_door_info_list)
        {
            int company_relay_value = 0;
            if (relay_type == DoorRelayType::RELAY)
            {
                company_relay_value = compnay_door_info.relay;
            }
            else if (relay_type == DoorRelayType::SECURITY_RELAY)
            {
                company_relay_value = compnay_door_info.srelay;
            }

            // 匹配relay所在的company
            if (relay_pow_value & company_relay_value)
            {
                // 获取relay绑定的door
                std::string door_uuid = GetRelayBindedDoorUUID(devices_door_info_list, relay_type, std::string(1, report_relay[index]));

                // 判断是否为该公司的私有door
                if (IsPrivateDoor(office_company_door_info_list, compnay_door_info.office_company_uuid, door_uuid))
                {
                    Snprintf(act_msg_.company_uuid, sizeof(act_msg_.company_uuid), compnay_door_info.office_company_uuid);
                    break;
                }
            }
        }

        // 获取relay绑定的door名称
        std::string door_name;
        if (relay_type == DoorRelayType::RELAY)
        {
            door_name = dbinterface::DevicesDoorList::GetReportActLogDoorNameList(conn_dev_.uuid, std::string(1, report_relay[index]), "");
        }
        else if (relay_type == DoorRelayType::SECURITY_RELAY)
        {
            door_name = dbinterface::DevicesDoorList::GetReportActLogDoorNameList(conn_dev_.uuid, "", std::string(1, report_relay[index]));
        }
        Snprintf(act_msg_.door_name_list, sizeof(act_msg_.door_name_list), door_name.c_str());

        // 记录开门失败日志
        dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery);
    }
}

void CNewOfficeDoorOpenMsg::NotifyWebAccessDoorMsg()
{
    AccessDoorNotifyMsg access_door_notify;
    if (0 != GenerateAccessDoorMsg(access_door_notify))
    {
        return;
    }

    COffice2RouteMsg::SendAccessDoorNotifyWebMsg(access_door_notify);
}

int CNewOfficeDoorOpenMsg::GenerateAccessDoorMsg(AccessDoorNotifyMsg& access_door_notify_msg)
{
    if (act_msg_.per_id[0] == MODE_DELIVERY)
    {
        int delivery_id = ATOI(&act_msg_.per_id[1]);
        OfficeDeliveryInfo delivery_info;
        if (0 != dbinterface::OfficeDelivery::GetOfficeDeliveryByID(delivery_id, delivery_info))
        {
            return -1;
        } 
        Snprintf(access_door_notify_msg.account_type, sizeof(access_door_notify_msg.account_type), "delivery");
        Snprintf(access_door_notify_msg.user_uuid, sizeof(access_door_notify_msg.user_uuid), delivery_info.uuid);
    }
    //能够找到对应用户开门的，才通知web
    else if (strlen(act_msg_.account) > 0)
    {
        OfficeAccount account;
        if (0 != dbinterface::OfficePersonalAccount::GetUidAccount(act_msg_.account, account))
        {
            return -1;
        }
        if (account.role != ACCOUNT_ROLE_OFFICE_NEW_PER)
        {
            AK_LOG_WARN << "access door user is not personnel. no need to report. account=" << act_msg_.account;
            return -1;
        }

        Snprintf(access_door_notify_msg.account_type, sizeof(access_door_notify_msg.account_type), "personnel");
        Snprintf(access_door_notify_msg.user_uuid, sizeof(access_door_notify_msg.user_uuid), account.uuid);
    }
    else
    {
        return -1;
    }

    std::string access_mode = GetEntryExitModeByAccessMode();
    std::string door_uuid_list = dbinterface::DevicesDoorList::GetDoorUUIDListByRelayList(act_msg_.dev_uuid, act_msg_.relay, act_msg_.srelay);

    if (access_mode.size() == 0 || door_uuid_list.size() == 0)
    {
        return -1;
    }

    Snprintf(access_door_notify_msg.access_mode, sizeof(access_door_notify_msg.access_mode), access_mode.c_str());
    Snprintf(access_door_notify_msg.door_uuid_list, sizeof(access_door_notify_msg.door_uuid_list), door_uuid_list.c_str());
    access_door_notify_msg.timestamp = act_msg_.capture_time;

    return 0;
}

std::string CNewOfficeDoorOpenMsg::GetEntryExitModeByAccessMode()
{
    if (act_msg_.access_mode == (int)AntiPassbackAccessMode::ENTRY || act_msg_.access_mode == (int)AntiPassbackAccessMode::ENTRY_VIOLATION)
    {
        return "entry";
    } 
    else if (act_msg_.access_mode == (int)AntiPassbackAccessMode::EXIT || act_msg_.access_mode == (int)AntiPassbackAccessMode::EXIT_VIOLATION)
    {
        return "exit";
    }

    return "";
}


//// private function implementation
int CNewOfficeDoorOpenMsg::OpenSaltoLockNotify()
{
    // salto lock link设备后,权限同设备绑定,设备开启对应relay成功,才通知salto lock开门
    if (act_msg_.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return -1;
    }
    
    SaltoLockInfoList salto_lock_list;
    dbinterface::SaltoLock::GetSaltoLockListByDeviceUUID(conn_dev_.uuid, salto_lock_list);
    if (salto_lock_list.size() == 0)
    {
        return -1;
    }

    OfficeAccount initiator_account;
    if (strlen(act_msg_.account))
    {
        dbinterface::OfficePersonalAccount::GetUidAccount(act_msg_.account, initiator_account);
    }

    // salto lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg_.relay);
    for (const auto& salto_lock : salto_lock_list)
    {
        if (salto_lock.relay & report_open_relay)
        {
            // 判断relay对应的door是否激活过期
            if (!dbinterface::DevicesDoorList::IsSubscribedDevice(conn_dev_.uuid))
            {
                AK_LOG_INFO << "OpenSaltoLockNotify failed, linked devices door not active or expired, mac = " << conn_dev_.mac << ", realy = " << salto_lock.relay << ", lockuuid = " << salto_lock.third_uuid;
                continue;
            }
            
            Json::Value item;
            Json::FastWriter fast_writer;
            item["link_mac"] = act_msg_.mac;
            item["pic_name"] = act_msg_.pic_name;
            item["lock_name"] = salto_lock.name;
            item["capture_type"] = act_msg_.act_type;
            item["uuid"] = salto_lock.third_uuid;
            item["initiator"] = act_msg_.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::SALTO;
            item["personal_account_uuid"] = initiator_account.uuid;
            
            AK_LOG_INFO << "open saltolock success, link mac=" << act_msg_.mac
                        << ", saltolock bind relay=" << salto_lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << salto_lock.name << ", lock uuid=" << salto_lock.third_uuid
                        << ", personal_account_uuid=" << initiator_account.uuid;

            std::string data_json = fast_writer.write(item);
            COffice2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_SALTO_OPEN_DOOR, data_json, salto_lock.third_uuid);
        }
        else
        {
            AK_LOG_INFO << "open saltolock failed, maybe bind other lock, saltolock bind relay=" << salto_lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }

    return 0;
}

int CNewOfficeDoorOpenMsg::NewOfficeHandleTempkeyUsedNotify()
{
    // 实际没用
    // ResidentDev dev;
    // if(dbinterface::ResidentDevices::GetMacDev(act_msg_.mac, dev) != 0){
    //     AK_LOG_WARN << "NewOffice HandleTempkeyUsedNotify GetMacDev failed: mac=" << act_msg_.mac;
    //     return -1;
    // }

    OfficeTempKeyInfo tempkey_info;
    RecordNewOfficeLog::GetInstance().RecordNewOfficeTmpKeyLog(act_msg_, tempkey_info);
    AK_LOG_INFO << "NewOffice Tempkey used: mac=" << act_msg_.mac << "Code=" << act_msg_.initiator;
    // if(tempkey_info.creator_type != OfficeTempKeyCreatorType::ENDUSER){
    //     AK_LOG_INFO << "NewOffice Tempkey used: mac=" << act_msg_.mac << "Code=" << act_msg_.initiator;
    // }

    std::string reciver = tempkey_info.creator_personal_account_uuid;
    if (act_msg_.resp || (reciver.size() == 0))
    {
        AK_LOG_WARN << "NewOfficeHandleTempkeyUsedNotify failed: mac=" << act_msg_.mac
                     << ", resp=" << act_msg_.resp << ", reciver=" << reciver;
        return -1;
    }

    std::string message_uuid;
    std::string rbac_uuid = tempkey_info.rbac_datagroup_uuid;

    // insert tempkey used notify to database.
    if (dbinterface::OfficeMessage::InsertOfficeMessageAndReciver(reciver, tempkey_info.name, rbac_uuid, message_uuid) != 0)
    {
        AK_LOG_WARN << "InsertOfficeMessageAndReciver failed: reciver=" << reciver
            << ", name=" << tempkey_info.name << ", message_uuid=" << message_uuid;
        return -1;
    }

    std::string json_str;
    if (GenerateTmpkeyUsedNotifyJson(project::OFFICE_NEW, message_uuid, json_str) != 0)
    {
        AK_LOG_WARN << "GenerateTmpkeyUsedNotifyJson failed: reciver=" << reciver << ", message_uuid=" << message_uuid;
        return -1;
    }

    // send message: (device -> csoffice -> csroute -> kafka -> csadapt -> csroute -> app)
    COffice2RouteMsg::SendGeneralData(project::OFFICE_NEW, AKCS_M2R_P2P_TEMPKEY_USED_NOTIFY_MSG, json_str.data(), json_str.size());
    AK_LOG_INFO << "SendGeneralData json_str = " << json_str;
    return 0;
}

int CNewOfficeDoorOpenMsg::GenerateTmpkeyUsedNotifyJson(project::PROJECT_TYPE type, const string& message_uuid, string& json_str)
{
    /*
    消息格式定义, 详见：http://192.168.10.102:8071/pages/viewpage.action?pageId=75005998#v7.0.0应用后台研发设计-发送通知消息
    {
        "msg_type": "newoffice_send_message",
        "trace_id": "861241313096368128",
        "timestamp": 1720072154000000,
        "data": {
            "project_type": "3",
            "message_uuid": "8ahmy8z227j68zms336r56ill1pkhzn6"
        }
    }
    */

    json_str = "";
    Json::Value root;
    Json::Value data;
    Json::FastWriter writer;
    data["project_type"] = std::to_string(type);
    data["message_uuid"] = message_uuid;

    root["msg_type"] = "newoffice_send_message";
    root["trace_id"] = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    root["timestamp"] = (long long)(time(nullptr) * 1000000);
    root["data"] = data;

    json_str = writer.write(root);
    return 0;
}
