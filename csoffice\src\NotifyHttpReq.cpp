#include "NotifyHttpReq.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "MsgToControl.h"
#include "AkLogging.h"

int CHttpReqNotifyMsg::NotifyMsg()
{
    //最多重试三次
    for (int i = 0; i < 3; i++)
    {
        std::string respone;
        if (http_type_ == HTTP_REQ_TYPE::POST)
        {
            if (model::HttpRequest::GetInstance().Post(url_, data_, respone, data_type_) != 0)
            {
                continue;
            }
        }
        else
        {
            if (model::HttpRequest::GetInstance().Get(url_, parma_kv_, respone) != 0)
            {
                continue;
            }

        }

        Json::Reader reader;
        Json::Value root;
        if (!reader.parse(respone, root))
        {
            AK_LOG_WARN << "respone error," << " data:" << respone;
            continue;
        }

        if (!root.isMember("code"))
        {
            continue;
        }

        int code = root["code"].asInt();
        if (0 == code)
        {
            if (CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL == req_type_)
            {
                Json::Value data;
                data = root["data"];
                std::string url;
                if (data.isMember("Link"))
                {
                    url = data["Link"].asString();
                }
                std::string mac = context_kv_["mac"];
                std::string mac_uuid = context_kv_["mac_uuid"];
                std::string voice_uuid = context_kv_["voice_uuid"];
                GetMsgToControlInstance()->SendVoiceMsgUrl(mac, mac_uuid, voice_uuid, url);
            }
            return 0;
        }
        else
        {
            AK_LOG_WARN << "respone error," << " data:" << respone;
        }
    }

    return -1;
}


