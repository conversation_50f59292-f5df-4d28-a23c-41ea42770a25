#include <string.h>
#include "MsgControl.h"
#include "NotifyMsgControl.h"
#include "NotifyMessage.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "core/CoreUtil.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "OfficeInit.h"
#include "MsgToControl.h"
#include "AKUserMng.h"
#include "PushClientMng.h"
#include "OfficePushClient.h"
#include "OfficeServer.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "dbinterface/Message.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "util_judge.h"
#include "Office2AppMsg.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

int CMessageNotifyMsg::NotifyMsg()
{
    if (msg_.client_type() == (int)MessageClientType::APP_SEND)
    {
        NotifyAppMsg();
    }
    else if (msg_.client_type() == (int)MessageClientType::DEV_SEND)
    {
        NotifyDevMsg();
    }
    return 0;
}


int CMessageNotifyMsg::NotifyAppMsg()
{
    OfficeAccount office_account;
    if (dbinterface::OfficePersonalAccount::GetUUIDAccount(msg_.uuid(),office_account) != 0)
    {
        AK_LOG_WARN << "CMessageNotifyMsg::NotifyAppMsg account not found, uuid:" << msg_.uuid()
                     << ", msg:" << msg_.DebugString();
        return -1;
    }
    MessageContentType msg_type = (MessageContentType)msg_.msg_type();
    std::string account = office_account.account;

    COffice2AppMsg msg_sender;

    //在线dclient消息构造
    std::string xml_txt_msg;
    GetMsgBuildHandleInstance()->BuildMessageTxtInfoMsg(msg_, xml_txt_msg);

    //离线推送消息      构造   
    int push_type = 0;
    msg_sender.InsertOfflineMsgKV("msg_id", GetMsgId(msg_.recv_msg_id(), office_account.role));

    std::string title;
    if (OfflinePush::GetMultiSiteUserTitle(account, title) == 0)
    {
        msg_sender.InsertOfflineMsgKV("title_prefix", title);
        msg_sender.InsertOfflineMsgKV("site", account);
    }

    if (msg_type == MessageContentType::VOICE_MSG)
    {
        msg_sender.InsertOfflineMsgKV("title", "Voice_Message");
        msg_sender.InsertOfflineMsgKV("content", msg_.content());
        push_type = csmain::PUSH_MSG_TYPE_VOICE_MSG;
    }
    else if (msg_type == MessageContentType::TMPKEY_MSG)
    {
        msg_sender.InsertOfflineMsgKV("title", msg_.title());
        msg_sender.InsertOfflineMsgKV("name", msg_.content());
        push_type = csmain::PUSH_MSG_TYPE_TMPKEY;
    }
    else if (msg_type == MessageContentType::LOCKDOWN_ON_MSG)
    {
        msg_sender.InsertOfflineMsgKV("title", msg_.title());
        msg_sender.InsertOfflineMsgKV("content", msg_.content());
        push_type = csmain::PUSH_MSG_TYPE_LOCKDOWN_ON_NOTIFY;
    }
    else if (msg_type == MessageContentType::LOCKDOWN_OFF_MSG)
    {
        msg_sender.InsertOfflineMsgKV("title", msg_.title());
        msg_sender.InsertOfflineMsgKV("content", msg_.content());
        push_type = csmain::PUSH_MSG_TYPE_LOCKDOWN_OFF_NOTIFY;
    }
    else
    {
        msg_sender.InsertOfflineMsgKV("title", msg_.title());
        msg_sender.InsertOfflineMsgKV("content", msg_.content());
        push_type = csmain::PUSH_MSG_TYPE_TEXT;
    }

    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT);
    msg_sender.SetMsgId(MSG_TO_DEVICE_SEND_TEXT_MESSAGE);
    msg_sender.SetClient(account);
    msg_sender.SetOnlineMsgData(xml_txt_msg);
    msg_sender.SendMsg(push_type);
    return 0;

}

int CMessageNotifyMsg::NotifyDevMsg()
{
    MsgStruct send_msg;
    ::memset(&send_msg, 0, sizeof(send_msg));

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(msg_.uuid(), dev))
    {
        AK_LOG_WARN << "CMessageNotifyMsg::NotifyDevMsg GetUUIDDev failed: msg=" << msg_.uuid()
                     << ", msg:" << msg_.DebugString();
        return -1;
    }
    
    //在线dclient消息构造
    std::string xml_txt_msg;
    GetMsgBuildHandleInstance()->BuildMessageTxtInfoMsg(msg_, xml_txt_msg);

    SOCKET_MSG socket_msg;
    if (BuildDclientMacEncMsg(dev, xml_txt_msg, MSG_TO_DEVICE_SEND_TEXT_MESSAGE, socket_msg, MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT) != 0)
    {
        AK_LOG_WARN << "CMessageNotifyMsg::NotifyDevMsg BuildDclientMacEncMsg failed: DeviceUUID=" << base_.uid()
                     << ", msg:" << msg_.DebugString();
        return -1;
    }

    GetClientControlInstance()->SendTransferMsg(dev.mac, dev.conn_type, socket_msg.data, socket_msg.size);
    return 0;
}

std::string CMessageNotifyMsg::GetMsgId(uint32_t msg_id, int role)
{
    return std::to_string(msg_id) + "_" + std::to_string(role);
}