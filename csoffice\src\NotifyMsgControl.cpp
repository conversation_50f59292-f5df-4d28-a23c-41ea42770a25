#include "NotifyMsgControl.h"
#include "AkcsMonitor.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"
#include "OfficeInit.h"
#include "AkcsHttpRequest.h"
#include "NotifyHttpReq.h"
#include "AkLogging.h"
#include "NotifyDoorOpenMsg.h"
#include "NotifyDoorOpenMsgNewOffice.h"
#include "NotifyPerText.h"
#include "NotifyMessage.h"


CNotifyMsgControl::CNotifyMsgControl(): msg_count(0)
{

}

CNotifyMsgControl::~CNotifyMsgControl()
{
    //curl_easy_cleanup(m_pCurl);
    notify_msg_list.clear();
}

CNotifyMsgControl* CNotifyMsgControl::instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::http_req_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::door_open_log_instance = NULL;


CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CNotifyMsgControl();
    }

    return instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetHttpReqInstance()
{
    if (http_req_instance == NULL)
    {
        http_req_instance = new CNotifyMsgControl();
    }

    return http_req_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetDoorOpenInstance()
{
    if (door_open_log_instance == NULL)
    {
        door_open_log_instance = new CNotifyMsgControl();
    }

    return door_open_log_instance;
}

CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}

CNotifyMsgControl* GetHttpReqMsgControlInstance()
{
    return CNotifyMsgControl::GetHttpReqInstance();
}

CNotifyMsgControl* GetDoorOpenMsgProcessInstance()
{
    return CNotifyMsgControl::GetDoorOpenInstance();
}

//在主线程初始化,注意m_pCurl不是线程安全的.
int CNotifyMsgControl::Init()
{
    m_t = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success,thread_id=" << m_t.get_id();
    return 0;
}

int CNotifyMsgControl::GetNotifyMsgListSize()
{
    std::unique_lock<std::mutex> lock(m_mtx);
    return notify_msg_list.size();
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        int is_next_wait = 0;
        NotifyMsgPrt tmp_ptr;
        {
            std::unique_lock<std::mutex> lock(m_mtx);
            while (notify_msg_list.size() == 0)
            {
                m_cv.wait(lock);
                is_next_wait = 1;
            }
            //检测队列长度
            //modified by czw,2021-09-09,当社区群发message时队列会达到大几百,故调整为1000告警
            if (is_next_wait == 1 && notify_msg_list.size() > 1000) //added by chenyc,2019-08-13,500只是估算值,具体的数值需要测试:消费能力TPS*允许的延迟时间来决定.
            {
                std::string msg = "notify msg queue overflow, the length now is:";
                msg += std::to_string(notify_msg_list.size());
                std::string worker_node = "csoffice_";
                worker_node += gstAKCSConf.csmain_outer_ip;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, msg, AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSMAIN_NOTIFY);
                AK_LOG_WARN << msg;
            }
            tmp_ptr = notify_msg_list.back();
            notify_msg_list.pop_back();
        }
        tmp_ptr->NotifyMsg();
    }
    return 0;
}

int CNotifyMsgControl::AddHttpReqNotiyMsg(const CHttpReqNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CHttpReqNotifyMsg(msg));
        notify_msg_list.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

//旧办公
int CNotifyMsgControl::AddTextNotifyMsg(const CPerTextNotifyMsg& msg)
{

    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CPerTextNotifyMsg(msg));
        notify_msg_list.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

//新办公
int CNotifyMsgControl::AddNewCommonMessageNotifyMsg(const CMessageNotifyMsg &msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CMessageNotifyMsg(msg));
        notify_msg_list.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddDoorOpenMsg(const CDoorOpenMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CDoorOpenMsg(msg));
        notify_msg_list.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddNewOfficeDoorOpenMsg(const CNewOfficeDoorOpenMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CNewOfficeDoorOpenMsg(msg));
        notify_msg_list.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}


