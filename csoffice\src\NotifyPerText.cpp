#include "MsgControl.h"
#include "NotifyMsgControl.h"
#include "NotifyPerText.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "OfficeInit.h"
#include "MsgToControl.h"
#include "AKUserMng.h"
#include "PushClientMng.h"
#include "OfficePushClient.h"
#include "OfficeServer.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "Office2AppMsg.h"
#include "dbinterface/Message.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

int CPerTextNotifyMsg::NotifyMsg()
{
    if (client_type_ != CPerTextNotifyMsg::APP_SEND)
    {
        return -1;
    }

    if (dbinterface::ProjectUserManage::MultiSiteLimit(account_))
    {
        //账号异常 通知拦截
        return -1;
    }

    int role;
    if (0 != dbinterface::ProjectUserManage::GetRoleByAccount(account_, role))
    {
        AK_LOG_WARN << "GetRoleByAccount failed, account = " << account_;
        return -1;
    }
    
    COffice2AppMsg msg_sender;

    //在线dclient消息构造
    char xml_msg_buf[4096];
    memset(xml_msg_buf, 0, sizeof(xml_msg_buf));
    if (GetMsgBuildHandleInstance()->BuildTextMessageXmlMsg(xml_msg_buf, sizeof(xml_msg_buf), &text_msg_info_) != 0)
    {
        AK_LOG_WARN << "BuildTextMessageXmlMsg failed";
        return -1;
    }

    //离线推送消息 构造
    int push_msg_type = csmain::PUSH_MSG_TYPE_ONLY_ONLINE;
    msg_sender.InsertOfflineMsgKV("msg_id", GetMsgId(text_msg_info_.id, role));

    std::string title;
    if (OfflinePush::GetMultiSiteUserTitle(account_, title) == 0)
    {
        msg_sender.InsertOfflineMsgKV("title_prefix", title);
        msg_sender.InsertOfflineMsgKV("site", account_);
    }

    if (text_msg_info_.type == CPerTextNotifyMsg::TEXT_MSG)
    {
        push_msg_type = csmain::PUSH_MSG_TYPE_TEXT;

        msg_sender.InsertOfflineMsgKV("title", text_msg_info_.title);
        msg_sender.InsertOfflineMsgKV("content", text_msg_info_.content);
    }
    else if (text_msg_info_.type == CPerTextNotifyMsg::VOICE_MSG)
    {
        push_msg_type = csmain::PUSH_MSG_TYPE_VOICE_MSG;
        msg_sender.InsertOfflineMsgKV("title", "Voice_Message");
        msg_sender.InsertOfflineMsgKV("content", text_msg_info_.content);
    }
    else if (text_msg_info_.type == CPerTextNotifyMsg::TMPKEY_MSG)
    {
        push_msg_type = csmain::PUSH_MSG_TYPE_TMPKEY;
        msg_sender.InsertOfflineMsgKV("name", text_msg_info_.content);
    }

    msg_sender.SetSendType((TransP2PMsgType)base_.type());
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT);
    msg_sender.SetMsgId(MSG_TO_DEVICE_SEND_TEXT_MESSAGE);
    msg_sender.SetClient(base_.uid());
    msg_sender.SetOnlineMsgData(xml_msg_buf);
    msg_sender.SendMsg(push_msg_type);
    return 0;
}

/*
    // 未读消息总数 : notice_msg + voice_msg + call_history . 目前只有推给ios才能生效 (等app方案成熟再推)
    if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
    {
        PerAccountUserInfo user_info;
        dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByAccount(account_, user_info);
        int unread_messages_count = dbinterface::Message::getUnReadMessageCount(user_info.uuid, user_info.create_time);
        int unread_callhistory_count = dbinterface::CallHistory::GetUnReadCallHistoryCount(account_ ,gstAKCSLogDelivery.call_history_delivery);
        int unread_all_count = unread_messages_count + unread_callhistory_count;
        kv.insert(map<std::string, std::string>::value_type("unread_msg", std::to_string(unread_all_count)));
    }
*/

std::string CPerTextNotifyMsg::GetMsgId(uint32_t msg_id, int role)
{
    return std::to_string(msg_id) + "_" + std::to_string(role);
}