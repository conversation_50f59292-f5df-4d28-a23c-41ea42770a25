#include "Office2AppMsg.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "OfficeServer.h"
#include "ClientControl.h"
#include "PushClientMng.h"
#include "ProjectUserManage.h"
#include "PersonalAccountUserInfo.h"

extern OfficeServer* g_office_srv_ptr;

std::string COffice2AppMsg::AccountToMainSiteAccount(const std::string& account)
{
    if (account.empty())
    {
        return account;
    }

    std::string main_account;
    if (msg_.send_type == TransP2PMsgType::TO_APP_UID || msg_.send_type == TransP2PMsgType::TO_APP_UID_ONLINE)
    {
        if (dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(account, main_account) != 0)
        {
            return account;
        }
    }
    else if (msg_.send_type == TransP2PMsgType::TO_APP_UUID)
    {
        if (dbinterface::PersonalAccountUserInfo::GetMainAccountByAccountUUID(account, main_account) != 0)
        {
            return account;
        }
    }

    return main_account;
}

int COffice2AppMsg::SendMsg(int push_msg_type)
{
    // 1.校验实际站点账号是否异常
    if (dbinterface::ProjectUserManage::MultiSiteLimit(msg_.client))
    {
        AK_LOG_WARN << "SendMsg failed: multi site limit, account=" << msg_.client;
        return -1;
    }
    
    // 2.多套房转换成主站点
    std::string main_account = AccountToMainSiteAccount(msg_.client);
    if (main_account.empty())
    {
        AK_LOG_WARN << "SendMsg failed: main_account is empty, account=" << msg_.client;
        return -1;
    }

    // 3.构造离线推送消息
    CMobileToken mobile_token;
    g_office_srv_ptr->GetAppToken(main_account, mobile_token);
    BuildOffilePushMsg(push_msg_type, mobile_token);

    // 4.特殊消息再加工
    if (msg_.msg_id == MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY)      // 紧急消息
    {
        int alarm_reminder_status = 0;
        dbinterface::ProjectUserManage::GetCurrentLoginSiteAlarmReminderStatusByMainSite(
            main_account, alarm_reminder_status
        );

        InsertOfflineMsgKV("enable_strong_reminder", to_string(alarm_reminder_status));
    }
    else if (msg_.msg_id == MSG_TO_DEVICE_SEND_TEXT_MESSAGE)
    {
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS || push_msg_type == csmain::PUSH_MSG_TYPE_YALE_BATTERY)
        {
            if (push_msg_type != csmain::PUSH_MSG_TYPE_VOICE_MSG)
            {
                SetForcePush(1);
            }
        }
    }

    AK_LOG_INFO << "Send message to main site app: account=" << msg_.client
        << ", main_site=" << main_account << ", msg_id=" << msg_.msg_id
        << ", push_msg_type=" << push_msg_type 
        << ", send_type=" << msg_.send_type
        << ", online_msg=" << msg_.msg_data
        << ", offline_msg=" << msg_.push_msg_data;

    // 5.推送消息
    SetClient(main_account);
    msg_.msg_len = strlen(msg_.msg_data);
    msg_.push_msg_len = strlen(msg_.push_msg_data);
    GetClientControlInstance()->SendTransferMsg(msg_);
    return 0;
}

int COffice2AppMsg::BuildOffilePushMsg(int push_msg_type, const CMobileToken& mobile_token)
{
    std::string push_msg;
    TransP2PMsgType send_type = msg_.send_type;
    if (send_type == TransP2PMsgType::TO_ALL_APP || send_type == TransP2PMsgType::TO_APP_UID || send_type == TransP2PMsgType::TO_APP_UUID)
    {
        InsertOfflineMsgKV("language", mobile_token.Language());
        InsertOfflineMsgKV("app_oem", std::to_string(mobile_token.AppOem()));
        InsertOfflineMsgKV("dclient", std::to_string(mobile_token.CommonVersion()));

        std::string token = mobile_token.FcmToken();
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
        {
            token = mobile_token.Token();
        }

        AK_LOG_INFO << "BuildOfflinePushMsg:" 
            << ", mobile_type=" << mobile_token.MobileType()
            << ", fcm_token=" << mobile_token.FcmToken()
            << ", token=" << mobile_token.Token();
        COfficePushClient::buildPushMsg(mobile_token.MobileType(), token, push_msg_type, offline_kv_, mobile_token.OemName(), push_msg);
    }

    SetPushMsgData(push_msg);
    return 0;
}


