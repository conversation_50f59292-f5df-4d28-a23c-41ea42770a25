#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <sys/msg.h>
#include "util.h"
#include "Office2MainHandle.h"
#include "AkLogging.h"
#include "AkcsPduBase.h"
#include "OfficeServer.h"
#include "SnowFlakeGid.h"
#include "ThreadLocalSingleton.h"
#include "MsgIdToMsgName.h"
#include "MetricService.h"


extern OfficeServer* g_office_srv_ptr;

const size_t kOffice2MainMsgKeyStart = 8600;

Office2MainHandle::Office2MainHandle(int mqueue_num)
{
    mqueue_num_ = mqueue_num;
    Init();
}
void Office2MainHandle::Start()
{
    for(auto &id : consumer_msg_id_)
    {
        std::thread thread1 = std::thread(&Office2MainHandle::InnerRecvThread, this, id);
        thread1.detach();
    }
}
void Office2MainHandle::Init()
{
    int i = 0;
    for(i = 0; i < mqueue_num_; i++)
    {
        size_t msgid = kOffice2MainMsgKeyStart + i * 2;
        int id = ::msgget((key_t)msgid, 0666 | IPC_CREAT);
        if (id == -1)
        {
            AK_LOG_FATAL << "Init error, key " << msgid;
        }
        AK_LOG_INFO << "Init produce mqueue, key " << msgid << " ret:" << id;
        produce_msg_id_.push_back(id);
        
        msgid = kOffice2MainMsgKeyStart + (i * 2 + 1);
        id = ::msgget((key_t)msgid, 0666 | IPC_CREAT);
        if (id == -1)
        {
            AK_LOG_FATAL << "Init error, key " << msgid;
        }
        AK_LOG_INFO << "Init consumer mqueue, key2 " << msgid << " ret:" << id;
        consumer_msg_id_.push_back(id);
    }
}

void Office2MainHandle::Send(const std::string& client, const csmain::DeviceType type, const char *data, size_t size)
{
    MsgStruct msg;
    ::memset(&msg, 0 , sizeof(msg));
    msg.client_type = type;
    Snprintf(msg.client, sizeof(msg.client),  client.c_str());
    msg.msg_len = size;
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    msg.traceid = traceid;
    ::memcpy(&msg.msg_data, data, size <= sizeof(msg.msg_data) ? size : sizeof(msg.msg_data));

    int hash = StrHash(client, mqueue_num_);
    int msg_id = produce_msg_id_[hash];
    
    AK_LOG_INFO << "Office2MainHandle send msg. linux mqueue msgid = " << msg_id << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(msg_id) << ", client = " << client << ", traceid: " << traceid;    
    
    InnerSend(msg_id, (char *)&msg, sizeof(msg));
}

void Office2MainHandle::Send(MsgStruct& msg)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    msg.traceid = traceid;
    int hash = StrHash(msg.client, mqueue_num_);
    int msg_id = produce_msg_id_[hash];   
    AK_LOG_INFO << "Office2MainHandle send msg. mqueue msgid:" << msg_id << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(msg_id) << " traceid: " << traceid;    
    InnerSend(msg_id, (char *)&msg, sizeof(msg));
}

void Office2MainHandle::InnerSend(int msg_id, const char *data, size_t size)
{
    if(size > MsgHelper::kMsgSize)
    {
        AK_LOG_WARN << "Msg size is larger than MsgHelper::kMsgSize, size " << size;
        return;
    }
    MsgHelper helper_send;
    ::memset(&helper_send, 0, sizeof(helper_send));
    helper_send.msg_type = 1; //固定都是1
    ::memcpy(helper_send.data, data, size);

    if (::msgsnd(msg_id, (void *)&helper_send, sizeof(helper_send.data), IPC_NOWAIT) == -1)//IPC_NOWAIT 保证是非阻塞的
    {
        AK_LOG_WARN << "The msgsnd failed, erron is " << errno;
        // 发送失败计数 ++
        MetricService* metric_service = MetricService::GetInstance();
        if(metric_service) {
            metric_service->AddValue("csoffice_linux_msgsnd_failed_count", 1);
        }
    }
    return;
}

//起线程,处理从csmain传回的消息
void Office2MainHandle::InnerRecvThread(int msg_id)
{
    MsgHelper helper_recv;
    ::memset(&helper_recv, 0, sizeof(helper_recv));
    long msg_type = 1; //固定都是1

    while(1)
    {
        ssize_t msg_recv_size = ::msgrcv(msg_id, (void *)&helper_recv, sizeof(helper_recv.data), msg_type, 0);
        if (msg_recv_size == -1)
        {
            // msg接收失败计数 ++
            MetricService* metric_service = MetricService::GetInstance();
            if(metric_service) {
                metric_service->AddValue("csoffice_linux_msgrcv_failed_count", 1);
            }
            
            AK_LOG_WARN << "The msgrcv failed, msgid:"<< msg_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << " erron is " << errno;
            continue;
        }
        //在这里区分是状态消息,还是其他一般消息,一般消息都是csacc透传过来的,状态消息csacc已经有初步处理过了
        MsgStruct* acc_msg = (MsgStruct*)helper_recv.data;
        ThreadLocalSingleton::GetInstance().SetTraceID(acc_msg->traceid);
        AK_LOG_INFO << "Office2MainHandle recv msg. mqueue msgid:"<<  msg_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << " traceid:" << acc_msg->traceid;
        g_office_srv_ptr->OnMainMsg(acc_msg);
    }
}

