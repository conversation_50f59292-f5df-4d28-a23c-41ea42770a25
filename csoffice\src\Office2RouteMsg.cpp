#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "util.h"
#include <boost/algorithm/string.hpp>
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "Office2RouteMsg.h"
#include "OfficeInit.h"
#include "AkcsCommonSt.h"
#include "ProjectUserManage.h"
#include "OfficeServer.h"
#include "MsgBuild.h"
#include "RouteMqProduce.h"
#include "AK.Linker.pb.h"
#include "AK.Resid.pb.h"
#include "AK.BackendCommon.pb.h"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern RouteMQProduce* g_nsq_producer;

COffice2RouteMsg::COffice2RouteMsg()
{

}


COffice2RouteMsg::~COffice2RouteMsg()
{

}

AK::BackendCommon::BackendP2PBaseMessage COffice2RouteMsg::CreateP2PBaseMsg(int msgid, int type, const std::string& uid, csmain::DeviceType conntype, int projecttype)
{
    //默认client_uid=uid，如客户端是设备
    std::string client_uid = uid;

    /*
    if (type == TO_APP_UID)
    {
        //多套房转换
        if (dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(uid, client_uid) != 0)
        {
            AK_LOG_WARN << "change to main site error, uid:" << uid;
        }
    }
    else if (type == TO_APP_UUID)
    {
        //多套房转换
        if (dbinterface::PersonalAccountUserInfo::GetMainAccountUUIDByAccountUUID(uid, client_uid) != 0)
        {
            AK_LOG_WARN << "change to main site error, uuid:" << uid;
        }
    }
    */

    AK::BackendCommon::BackendP2PBaseMessage msg;
    msg.set_type(type);
    msg.set_uid(client_uid);
    msg.set_msgid(msgid);
    msg.set_conn_type(conntype);
    msg.set_project_type(projecttype);
    return msg;
}

csmain::DeviceType COffice2RouteMsg::DevProjectTypeToDevType(int projecttype)
{
    if (projecttype == project::PERSONAL)
    {
        return csmain::DeviceType::PERSONNAL_DEV;
    }
    else if (projecttype == project::RESIDENCE)
    {
        return csmain::DeviceType::COMMUNITY_DEV;
    }
    else if (projecttype == project::OFFICE)
    {
        return csmain::DeviceType::OFFICE_DEV;
    }
    return csmain::DeviceType::COMMUNITY_NONE;
}

void COffice2RouteMsg::PushMsg2Route(const google::protobuf::MessageLite* msg)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void COffice2RouteMsg::PushLinKerWeather(const SOCKET_MSG_DEV_WEATHER_INFO& weather_info)
{
    Json::Value item;
    Json::FastWriter w;
    item["mac"] = weather_info.mac;
    item["city"] = weather_info.city;
    item["state_province"] = weather_info.states;
    item["country"] = weather_info.country;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LINKER_MSG_TYPE_WEATHER, data_json, weather_info.mac);
}

void COffice2RouteMsg::SendLinKerCommonMsg(int msg_type, const std::string& data_json, const std::string& key)
{
    AK::Linker::P2PRouteLinker msg;
    msg.set_message_type(msg_type);
    msg.set_project_type(project::OFFICE);
    msg.set_msg_json(data_json);
    msg.set_key(key);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void COffice2RouteMsg::GroupVoiceMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    //获取留言信息
    PersonalVoiceMsgInfo voice_msg_info;
    PersonalVoiceMsgSendList send_list;
    std::string location;
    std::string msg_uuid;
    memset(&voice_msg_info, 0, sizeof(voice_msg_info));
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByMacAndFileName(msg.mac(), msg.filename(), voice_msg_info))
    {
        location = voice_msg_info.location;
        msg_uuid = voice_msg_info.uuid;
    }

    //获取发送列表
    dbinterface::PersonalVoiceMsg::GetVoiceMsgListInfoByMsgUUID(msg_uuid, send_list);

    for (const auto& send_node : send_list)
    {
        int type;
        int count;
        int msg_id;
        std::string receiver_uuid;
        AK::BackendCommon::BackendP2PBaseMessage base;
        if (strlen(send_node.indoor_uuid) > 0)
        {
            receiver_uuid = send_node.indoor_uuid;
            count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(send_node.indoor_uuid);
            type = DEVICE_TYPE_INDOOR;
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_VOICE_MSG, TransP2PMsgType::TO_DEV_UUID, receiver_uuid,
                DevProjectTypeToDevType(msg.project_type()), msg.project_type());
        }
        else if (strlen(send_node.personal_uuid) > 0)
        {
            receiver_uuid = send_node.personal_uuid;
            msg_id = send_node.id;
            type = DEVICE_TYPE_APP;
            std::string uid;
            dbinterface::OfficePersonalAccount::GetAccountByUUID(receiver_uuid, uid);
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_VOICE_MSG, TransP2PMsgType::TO_APP_UID, uid,
                DevProjectTypeToDevType(msg.project_type()), msg.project_type());
        }

        AK::Server::P2PSendVoiceMsg msg2;
        msg2.set_msg_id(msg_id);
        msg2.set_count(count);
        msg2.set_location(location);
        msg2.set_receiver_uuid(receiver_uuid);
        msg2.set_receiver_type(type);
        //通过project_type区分需要处理的消息的业务类型，通过base里面的conntype区别主站点的业务类型，用于发送
        msg2.set_project_type(msg.project_type());
        base.mutable_p2psendvoicemsg2()->CopyFrom(msg2);
        PushMsg2Route(&base);
    }
}

void COffice2RouteMsg::SendUpdateConfigByAccount(int type, const std::string& node, const std::string& account,
    int account_role, int manager_id, int unit_id, const std::string &project_uuid, int project_type)
{
    AK::Server::P2PMainAccountConfigRewriteMsg msg;
    msg.set_node(node);
    msg.set_account(account);
    msg.set_account_role(account_role);
    msg.set_type(type);
    msg.set_manager_id(manager_id);
    msg.set_unit_id(unit_id);
    msg.set_project_uuid(project_uuid);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_S2C_ACCOUNT_CONFIG_REWRITE);
    pdu2.SetSeqNum(0);
    pdu2.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.route_topic);
}

void COffice2RouteMsg::GroupIndoorRelayStatusMsg(const std::string& node, const std::string& mac, int relay_status, int relay_type, int project_type)
{
    AK::BackendCommon::BackendP2PBaseMessage base;
    base = CreateP2PBaseMsg(AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG, TransP2PMsgType::TO_APP_UID,
        node, DevProjectTypeToDevType(project_type), project_type
    );

    AK::Server::GroupMainReportRelayStatus msg;
    msg.set_relay_status(relay_status);
    msg.set_mac(mac);
    msg.set_account(node);
    msg.set_relay_type(relay_type);
    base.mutable_groupindoorrelaystatusmsg2()->CopyFrom(msg);
    PushMsg2Route(&base);
    return;
}

void COffice2RouteMsg::SendEmergencyNotifyWebMsg(const std::string& alarm_uuid, const std::string& project_uuid)
{
    Json::Value item;
    Json::FastWriter writer;
    item["alarm_uuid"] = alarm_uuid;
    item["project_uuid"] = project_uuid;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "SendEmergencyNotifyWebMsg : " << data_json;
    SendRoute2WebCommonMsg(PUSH_WEB_MSG_EMERGENCY, data_json);
}

void COffice2RouteMsg::SendRoute2WebCommonMsg(int msg_type, const std::string& data_json)
{
    AK::Server::P2PRouteToWebMsg msg;
    msg.set_message_type(msg_type);
    msg.set_msg_json(data_json);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_PUSH_WEB_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void COffice2RouteMsg::SendP2PEmergencyDoorControlMsg(const dbinterface::PmEmergencyDoorLogInfoList& info_list, const std::string& msg_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type, int project_type)
{
    for (auto const& info : info_list)
    {	
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(info.device_uuid, dev))
        {
            AK_LOG_WARN << "SendP2PEmergencyDoorControlMsg GetUUIDDev failed, uuid = " << info.device_uuid;
            continue;
        }
        
        dbinterface::RelayStatus status = dbinterface::PmEmergencyDoorLog::CheckDevStatus(dev);
        if (dbinterface::RelayStatus::PROCESSING == status)
        {
            AK::BackendCommon::BackendP2PBaseMessage base_msg;
            base_msg = CreateP2PBaseMsg(AKCS_M2R_EMERGENCY_DOOR_CONTROL, TransP2PMsgType::TO_DEV_UUID, dev.uuid, DevProjectTypeToDevType(project_type), project_type);

            // 设备在线消息推送
            AK::Server::P2PPmEmergencyDoorControlMsg control_msg;
            control_msg.set_mac(dev.mac);
            control_msg.set_msg_uuid(msg_uuid);
            control_msg.set_device_uuid(dev.uuid);
            control_msg.set_initiator(initiator);
            control_msg.set_auto_manual(OPERATE_TYPE::AUTO);
            control_msg.set_operation_type(CONTROL_TYPE::OPEN_DOOR);
            control_msg.set_relay(info.relay);
            control_msg.set_security_relay(info.security_relay);

            base_msg.mutable_p2ppmemergencydoorcontrolmsg2()->CopyFrom(control_msg);
            PushMsg2Route(&base_msg);
            
            AK_LOG_INFO << "SendP2PEmergencyDoorControlMsg auto open, mac = " << dev.mac << ", relay = " << info.relay << ", security_relay = " << info.security_relay;
        }
        else
        {
            dbinterface::PmEmergencyDoorLog::RecordAbnormalStatus(msg_uuid, dev.uuid, initiator, status, act_type, gstAKCSLogDelivery.personal_capture_delivery);
            AK_LOG_INFO << "RecordAbnormalStatus msg_uuid = " << msg_uuid << ", mac = " << dev.mac << ", relay = " << info.relay << ", security_relay = " << info.security_relay;
        }
    }

    return;
}

void COffice2RouteMsg::SendP2PEmergencyDoorNotifyMsg(const OfficeAccountList& app_list, const std::string& timenow)
{
    AK::BackendCommon::BackendP2PBaseMessage base;
    AK::Server::P2PSendEmergencyNotifyMsg p2p_msg;
    p2p_msg.set_timenow(timenow);
    p2p_msg.set_control_type(PmEmergencyDoorControlType::CONTROL_TYPE_OPEN);
    project::PROJECT_TYPE project_type = project::PROJECT_TYPE::OFFICE;

    for (const auto& app : app_list)
    {
        p2p_msg.set_receiver_uid(app.account);
        base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, app.account, csmain::DeviceType::OFFICE_APP, project_type);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);

        PushMsg2Route(&base);
        AK_LOG_INFO << "SendP2PEmergencyDoorNotifyMsg to app uid = " << app.account;
    }

    return;
}


void COffice2RouteMsg::SendP2PEmergencyDoorNotifyMsg(const OfficeDevList& dev_list, const std::string& timenow)
{
    AK::BackendCommon::BackendP2PBaseMessage base;
    AK::Server::P2PSendEmergencyNotifyMsg p2p_msg;
    p2p_msg.set_timenow(timenow);
    p2p_msg.set_control_type(PmEmergencyDoorControlType::CONTROL_TYPE_OPEN);
    project::PROJECT_TYPE project_type = project::PROJECT_TYPE::OFFICE;

    for (const auto& dev : dev_list)
    {
        if (dev->status)
        {
            p2p_msg.set_receiver_uid(dev->mac);
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_DEV_MAC, dev->mac, csmain::DeviceType::OFFICE_DEV, project_type);
            base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);

            PushMsg2Route(&base);
            AK_LOG_INFO << "SendP2PEmergencyDoorNotifyMsg to dev success, mac = " << dev->mac;
        }
        else
        {
            AK_LOG_INFO << "SendP2PEmergencyDoorNotifyMsg to dev failed, reason is offline, mac = " << dev->mac;
        }
    }

    return;
}

void COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE project_type, TransP2PMsgType p2p_msg_type, csmain::DeviceType dev_type, const std::string& endpoint, const SOCKET_MSG_ALARM_SEND& alarm_msg)
{
    AK_LOG_INFO << "SendP2PAlarmNotifyMsg id=" << alarm_msg.id << ", mac=" << alarm_msg.mac 
                << ", endpoint=" << endpoint << ", device_type=" << dev_type
                << ", alarm code:" << alarm_msg.alarm_code;

    AK::Server::P2PSendAlarmNotifyMsg p2p_msg;
    p2p_msg.set_id(alarm_msg.id);
    p2p_msg.set_mac(alarm_msg.mac);
    p2p_msg.set_time(alarm_msg.time);
    p2p_msg.set_grade(alarm_msg.grade);
    p2p_msg.set_receive_endpoint(endpoint);
    p2p_msg.set_alarm_type(alarm_msg.type);
    p2p_msg.set_address(alarm_msg.address);
    p2p_msg.set_msg_seq(alarm_msg.msg_seq);
    p2p_msg.set_unit_id(alarm_msg.unit_id);
    p2p_msg.set_trace_id(alarm_msg.trace_id);
    p2p_msg.set_community(alarm_msg.community);
    p2p_msg.set_extension(alarm_msg.extension);
    p2p_msg.set_from_local(alarm_msg.from_local);
    p2p_msg.set_alarm_code(alarm_msg.alarm_code);
    p2p_msg.set_alarm_zone(alarm_msg.alarm_zone);
    p2p_msg.set_device_type(alarm_msg.device_type);
    p2p_msg.set_alarm_location(alarm_msg.alarm_location);
    p2p_msg.set_alarm_customize(alarm_msg.alarm_customize);
    p2p_msg.set_mng_account_id(alarm_msg.manager_account_id);
    
    AK::BackendCommon::BackendP2PBaseMessage base;
    base = CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_SEND_ALARM_NOTIFY_MSG, p2p_msg_type, endpoint, dev_type, project_type);
    base.mutable_p2psendalarmnotifymsg2()->CopyFrom(p2p_msg);

    PushMsg2Route(&base);
    return;
}

void COffice2RouteMsg::SendTmpkeyUsedNotify(const PersonalTempKeyUserInfo& tempkey_user_info)
{
    std::string main_site;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(tempkey_user_info.creator, main_site);
    
    AK::Server::P2PMainSendTmpkeyUsed msg;
    msg.set_main_site(main_site);
    msg.set_name(tempkey_user_info.name);
    msg.set_account(tempkey_user_info.creator);

    AK::BackendCommon::BackendP2PBaseMessage base;
    base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ, TransP2PMsgType::TO_APP_UID, main_site, csmain::DeviceType::OFFICE_APP, project::OFFICE);
    base.mutable_p2pmainsendtmpkeyused2()->CopyFrom(msg);

    PushMsg2Route(&base);
    return;
}

void COffice2RouteMsg::SendGeneralData(project::PROJECT_TYPE project_type, uint32_t command_id, const void* data, size_t size)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(data, size);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(command_id);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void COffice2RouteMsg::SendAttendanceClockNotifyWebMsg(const std::string& device_uuid, const std::string& node, uint32_t clock_time, int relay, int security_relay)
{
    Json::Value item;
    Json::FastWriter writer;
    item["device_uuid"] = device_uuid;
    item["clock_time"] = clock_time;
    item["relay"] = relay;
    item["security_relay"] = security_relay;
    item["account"] = node;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "SendAttendanceClockNotifyWebMsg: " << data_json;
    SendRoute2WebCommonMsg(PUSH_WEB_MSG_ATTENDANCE, data_json);
}

void COffice2RouteMsg::SendMusterReportNotifyWebMsg(const std::string& user_uuid,const std::string& account_type, const std::string& office_uuid)
{
    Json::Value item;
    Json::FastWriter writer;
    item["account_type"] = account_type;
    item["user_uuid"] = user_uuid;
    item["office_uuid"] = office_uuid;
    item["message_id"] = (int)NotifyWebAccessDoorMessageID::MUSTER_READER;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "SendMusterReportNotifyWebMsg: " << data_json;
    SendRoute2WebCommonMsg(PUSH_WEB_MSG_ACCESS_DOOR, data_json);
}

void COffice2RouteMsg::SendAccessDoorNotifyWebMsg(const AccessDoorNotifyMsg& access_door_notify_msg)
{
    Json::Value item;
    Json::FastWriter writer;
    item["account_type"] = access_door_notify_msg.account_type;
    item["mode"] = access_door_notify_msg.access_mode;
    item["user_uuid"] = access_door_notify_msg.user_uuid;
    item["door_uuid"] = access_door_notify_msg.door_uuid_list;
    item["timestamp"] = access_door_notify_msg.timestamp;
    item["message_id"] = (int)NotifyWebAccessDoorMessageID::ENTRY_OR_EXIT_RECORD;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "SendAccessDoorNotifyWebMsg: " << data_json;
    SendRoute2WebCommonMsg(PUSH_WEB_MSG_ACCESS_DOOR, data_json);
}