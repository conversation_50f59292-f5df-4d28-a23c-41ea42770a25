#include <string.h>
#include <signal.h>
#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evnsq/producer.h>
#include "RouteClient.h"
#include "RouteClientMng.h"
#include "AkcsMonitor.h"
#include "OfficeInit.h"
#include "OfficeRoute.h"
#include "PushClientMng.h"
#include "OfficePushClient.h"
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include "rbac/rbac_rpc_client.h"
#include "loop/RouteLoopManager.h"

#define MAX_RLDB_CONN 10
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern RbacRpcClient* g_rbac_client_ptr;
extern const char *g_conf_db_addr;
extern const char *g_ak_srv_route;
extern const char *g_ak_srv_cspush;
extern const char *g_ak_srv_nsqlookupd;
extern const char *g_ak_srv_web_rpc_rbac;
extern const char *g_ak_srv_smg_alexa;
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);


//与所有的rbac建立tcp连接
static void RbacSrvConnInit(const std::set<std::string>& rbac_addrs)
{
    std::string rbac_addrs_str = "";
    std::vector<AddressData> addresses;
    AddressData addr_tmp;
    for (const auto& rbac_addr : rbac_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = rbac_addr.find(":");
        if (std::string::npos != pos)
        {
            ip = rbac_addr.substr(0, pos);
            port = rbac_addr.substr(pos + 1);
        }

        addresses.emplace_back(AddressData{ATOI(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
        rbac_addrs_str += rbac_addr + ",";
    }

    AK_LOG_INFO << "RbacSrvConnInit rbac list:" << rbac_addrs_str;
    if(g_rbac_client_ptr != nullptr){
        g_rbac_client_ptr->SetNextResolution(addresses);
    }
    else{
        AK_LOG_WARN << "rbac client is nullptr.";
    }
}


static void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.db_ip) != 0) || (conf_tmp.db_port != gstAKCSConf.db_port))
    {
        Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip),  conf_tmp.db_ip);
        gstAKCSConf.db_port = conf_tmp.db_port;
        DaoReInit();
    }
}

void UpdateRbacSrvList()
{
    std::set<std::string> csrbac_addrs;
    if (g_etcd_cli_mng->GetAllRbacSrvs(csrbac_addrs) == 0)
    {
        RbacSrvConnInit(csrbac_addrs);
    }
}

// alexa
void UpdateSmgAlexaConfSrv()
{
    std::string smg_alexa_addr;
    g_etcd_cli_mng->LoadSrvSmgAlexaConf(smg_alexa_addr);
    Snprintf(gstAKCSConf.smg_alexa_addr, sizeof(gstAKCSConf.smg_alexa_addr), smg_alexa_addr.c_str());
}

//含csroute cspush的连接
void EtcdSrvInit()
{    
    //csroute
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }

    RouteSrvConnInit(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), 
        std::bind(&COfficeRouteClient::CreateClient, std::placeholders::_1, std::placeholders::_2));

    GetRouteLoopManagerInstance()->StartLoop();

    //cspush
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    PushSrvConnInit(cspush_addrs, g_etcd_loop.get(), &COfficePushClient::CreateClient);

    if (strlen(gstAKCSConf.push_server_addr) > 5)
    {
        PushClientPtr push_cli_ptr = COfficePushClient::CreateClient(std::string(gstAKCSConf.push_server_addr), g_etcd_loop.get());
        push_cli_ptr->Start();
        CPushClientMng::Instance()->AddOuterPushSrv(push_cli_ptr);
    }

    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }

    UpdateSmgAlexaConfSrv();
    //等待接入服务启动完成,再启动csroute的loop,让csmain去建立与csroute的长连接
    // while (!g_access_srv_ready)
    // {
    //     usleep(100 * 1000);
    // }
    
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_route, std::bind(&COfficeRouteClient::UpdateClient));
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_cspush, std::bind(&COfficePushClient::UpdatePushSrvList));
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_web_rpc_rbac, UpdateRbacSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_smg_alexa, UpdateSmgAlexaConfSrv);
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get());


    //etcd_loop 目前只有route的连接在用  
    g_etcd_loop->Run();

}



