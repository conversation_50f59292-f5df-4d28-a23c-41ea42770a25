#include <string.h>
#include <signal.h>
#include <etcd/Client.hpp>
#include <evnsq/producer.h>
#include "util.h"
#include "util_time.h"
#include "catch.hpp"
#include "ConnectionPool.h"
#include "LogConnectionPool.h"
#include "MappingConnectionPool.h"
#include "ConfigFileReader.h"
#include "EtcdCliMng.h"
#include "RouteMqProduce.h"
#include "DevOnlineMng.h"
#include "OfficeInit.h"
#include "EmergencyMsgControl.h"
#include "antipassback/AntiPassbackBlock.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/SystemSettingTable.h"
#include "evpp/rate_limiter/rate_limiter_interface.h"
#include <KdcDecrypt.h>


extern CAkEtcdCliManager* g_etcd_cli_mng;
std::string g_srv_id = "";
AKCS_CONF gstAKCSConf;
LOG_DELIVERY gstAKCSLogDelivery;

int LogDeliveryInit()
{
    gstAKCSLogDelivery.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    gstAKCSLogDelivery.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    gstAKCSLogDelivery.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    if(gstAKCSLogDelivery.personal_capture_delivery == 0 || gstAKCSLogDelivery.personal_motion_delivery == 0 || gstAKCSLogDelivery.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}
int ConfInit()
{
    CConfigFileReader oss_config_file("/etc/oss_install.conf");
    Snprintf(gstAKCSConf.oss_bucket, sizeof(gstAKCSConf.oss_bucket),  oss_config_file.GetConfigName("FaceBucket"));
    Snprintf(gstAKCSConf.oss_role_arn, sizeof(gstAKCSConf.oss_role_arn),  oss_config_file.GetConfigName("RoleArn"));
    Snprintf(gstAKCSConf.oss_outer_endpoint, sizeof(gstAKCSConf.oss_outer_endpoint),  oss_config_file.GetConfigName("OuterEndpoint"));
    Snprintf(gstAKCSConf.oss_sts_endpoint, sizeof(gstAKCSConf.oss_sts_endpoint),  oss_config_file.GetConfigName("StsEndpoint"));
    Snprintf(gstAKCSConf.oss_region_id, sizeof(gstAKCSConf.oss_region_id),  oss_config_file.GetConfigName("RegionID"));

    CConfigFileReader config_file("/usr/local/akcs/csoffice/conf/csoffice.conf");
   //char* enable_ipv6 = config_file.GetConfigName("ipv6_enable");
    //gstAKCSConf.enable_ipv6 = ATOI(enable_ipv6);

    const char* log_level = config_file.GetConfigName("csmain_loglevel");
    gstAKCSConf.log_level = ATOI(log_level);
    Snprintf(gstAKCSConf.log_file, COMMON_STR_LEN,  "/var/log/csmainlog/csmain00.log");

    // 数据库账号密码
    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username),  config_file.GetConfigName("db_username"));
     //获取数据库密码
     CConfigFileReader kdc_config_file("/etc/kdc.conf");
     const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
     std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
     Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), decrypt_db_passwd.c_str());
     
    // 数据库ip
    Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip),  config_file.GetConfigName("akcs_db_ip"));
    Snprintf(gstAKCSConf.log_db_ip, sizeof(gstAKCSConf.log_db_ip),  config_file.GetConfigName("log_db_ip"));
    Snprintf(gstAKCSConf.mapping_db_ip, sizeof(gstAKCSConf.mapping_db_ip), config_file.GetConfigName("mapping_db_ip"));

    // 数据库端口
    const char* db_port = config_file.GetConfigName("akcs_db_port");
    gstAKCSConf.db_port = ATOI(db_port);
    const char* log_db_port = config_file.GetConfigName("log_db_port");
    gstAKCSConf.log_db_port = ATOI(log_db_port);
    const char* mapping_db_port = config_file.GetConfigName("mapping_db_port");
    gstAKCSConf.mapping_db_port = ATOI(mapping_db_port);

     // 数据库名称
    Snprintf(gstAKCSConf.db_database, sizeof(gstAKCSConf.db_database),  config_file.GetConfigName("akcs_db_database"));
    Snprintf(gstAKCSConf.log_db_database, sizeof(gstAKCSConf.log_db_database),  config_file.GetConfigName("log_db_database"));
    Snprintf(gstAKCSConf.mapping_db_database, sizeof(gstAKCSConf.mapping_db_database), config_file.GetConfigName("mapping_db_database"));
    
    Snprintf(gstAKCSConf.push_server_addr, sizeof(gstAKCSConf.push_server_addr),  config_file.GetConfigName("cspush_net"));

    //网关编号
    Snprintf(gstAKCSConf.gateway_code, sizeof(gstAKCSConf.gateway_code),  config_file.GetConfigName("gateway_code"));

    Snprintf(gstAKCSConf.web_server_addr, sizeof(gstAKCSConf.web_server_addr),  config_file.GetConfigName("csweb_net"));
    Snprintf(gstAKCSConf.web_server_ipv6_addr, sizeof(gstAKCSConf.web_server_ipv6_addr),  config_file.GetConfigName("csweb_net_ipv6"));
    Snprintf(gstAKCSConf.oem_name, sizeof(gstAKCSConf.oem_name),  config_file.GetConfigName("oem_name"));
    Snprintf(gstAKCSConf.push_AESkey, sizeof(gstAKCSConf.push_AESkey),  config_file.GetConfigName("push_aeskey"));

    Snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr),  config_file.GetConfigName("etcd_srv_net"));

    Snprintf(gstAKCSConf.route_topic, sizeof(gstAKCSConf.route_topic),  config_file.GetConfigName("nsq_route_topic"));
    Snprintf(gstAKCSConf.svn_version, sizeof(gstAKCSConf.svn_version),  config_file.GetConfigName("svn_version"));

    Snprintf(gstAKCSConf.apiurl, sizeof(gstAKCSConf.apiurl),  config_file.GetConfigName("apiurl"));
    
    const char* is_aws = config_file.GetConfigName("is_aws");
    gstAKCSConf.is_aws = ATOI(is_aws);

    Snprintf(gstAKCSConf.web_backend_domain, sizeof(gstAKCSConf.web_backend_domain),  config_file.GetConfigName("web_backend_domain"));

    Snprintf(gstAKCSConf.linker_nsq_topic, sizeof(gstAKCSConf.linker_nsq_topic),  config_file.GetConfigName("nsq_linker_topic"));

    //现阶段一样，后面会改
    Snprintf(gstAKCSConf.voice_server_ipv4, sizeof(gstAKCSConf.voice_server_ipv4),  config_file.GetConfigName("config_server_ipv4"));
    Snprintf(gstAKCSConf.voice_server_ipv6, sizeof(gstAKCSConf.voice_server_ipv6),  config_file.GetConfigName("config_server_ipv6"));
    
    Snprintf(gstAKCSConf.linker_nsq_ip, sizeof(gstAKCSConf.linker_nsq_ip),  config_file.GetConfigName("nsq_linker_ip"));

    //过滤消息ID列表
    Snprintf(gstAKCSConf.filter_msg_list, sizeof(gstAKCSConf.filter_msg_list), config_file.GetConfigName("filter_id_list"));

    const char* area = config_file.GetConfigName("server_area");
    gstAKCSConf.server_area = ATOI(area);

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));
    
    return 0;
}

int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip),  conf_tmp.db_ip);
    gstAKCSConf.db_port = conf_tmp.db_port;
    return 0;
}

/* 初始化数据库连接 */
int DaoInit()
{
    LogConnPool* log_conn_pool = GetLogDBConnPollInstance();
    if (NULL == log_conn_pool)
    {
        AK_LOG_WARN << "log DaoInit failed.";
        return -1;
    }
    log_conn_pool->Init(gstAKCSConf.log_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.log_db_database, gstAKCSConf.log_db_port, MAX_RLDB_CONN, "csoffice");

    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    LoadConfFromConfSrv();
    gConnPool->Init(gstAKCSConf.db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password,
                    gstAKCSConf.db_database, gstAKCSConf.db_port, MAX_RLDB_CONN, "csoffice");
                    
    MappingConnPool* mapping_conn_pool = GetMappingDBConnPollInstance();
    if (NULL == mapping_conn_pool)
    {
        AK_LOG_WARN << "Mapping DaoInit failed.";
        return -1;
    }
    mapping_conn_pool->Init(gstAKCSConf.mapping_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, 
                            gstAKCSConf.mapping_db_database, gstAKCSConf.mapping_db_port, MAX_RLDB_CONN, "csoffice");
    
    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstAKCSConf.db_ip, gstAKCSConf.db_port);
    return 0;
}

/* 主动断开数据库连接 */
int DaoRelease()
{
    return 0;
}

void TimeTaskInit()
{
    evpp::EventLoop loop;

    loop.RunEvery(evpp::Duration(30.0),  std::bind(&CAntiPassbackBlock::CheckBlockedPersonnelStatus));

    GetEmergencyControlInstance()->InitBucketSize(30);
    loop.RunEvery(evpp::Duration(1.0), []() { GetEmergencyControlInstance()->AddBucketMsg(); });
    
    loop.Run();
    
    return;
}
