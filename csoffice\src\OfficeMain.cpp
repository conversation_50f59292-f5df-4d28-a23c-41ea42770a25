#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <signal.h>
#include <unistd.h>
#include <sys/stat.h>
#include <errno.h>
#include <signal.h>
#include "ConfigFileReader.h"
#include "DevOnlineMng.h"
#include "Office2MainHandle.h"
#include "OfficeServer.h"
#include "CachePool.h"
#include "ClientControl.h"
#include "RouteMqProduce.h"
#include "OfficeInit.h"
#include "OfficeEtcd.h"
#include "EtcdDns.h"
#include "NotifyMsgControl.h"
#include "EtcdCliMng.h"
#include "AkcsAppInit.h"
#include "DevOnlineMng.h"
#include "HttpServer.h"
#include "util_time.h"
#include "rbac/rbac_rpc_client.h"
#include "dbinterface/ProjectUserManage.h"
#include "Metric.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;
const size_t kMqueue_number = 2;
std::map<string, AKCS_DST> g_time_zone_DST;
RbacRpcClient* g_rbac_client_ptr = nullptr;

void InstanceInit()
{
    CacheManager::getInstance()->Init("/usr/local/akcs/csoffice/conf/csoffice_redis.conf", "csofficeCacheInstances");
    CClientControl::GetInstance();

    GetNotifyMsgControlInstance()->Init();
    GetHttpReqMsgControlInstance()->Init();
    GetDoorOpenMsgProcessInstance()->Init();
    DevOnlineMng::GetInstance()->Init();
}

int EtcdConnInit()
{
    g_etcd_dns_mng = new CEtcdDnsManager(gstAKCSConf.etcd_server_addr);
    std::thread dnsThread = std::thread(&CEtcdDnsManager::StartDnsResolver, g_etcd_dns_mng);
    while(!g_etcd_dns_mng->DnsIsOk())
    {
        usleep(10);
    }
    dnsThread.detach();
    //域名解析完才能初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_dns_mng->GetAddrs());
    g_etcd_dns_mng->SetEtcdCli(g_etcd_cli_mng); 
    return 0;
}

void AkConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
}

void WebDomainServerWatch(){
    g_etcd_cli_mng->EnterWatchWebDomainServerChanges();
}

int main(int argc, char** argv)
{
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2("/var/run/csoffice.pid"))
    {
        printf("There is another csoffice running in this system.");
        return -1;
    }

    g_srv_id = GetEth0IPAddr();

    GlogInit2(argv[0], "csofficelog");
    
    ConfInit();

    ParseTimeZone("/usr/local/akcs/csoffice/conf/TimeZone.xml", g_time_zone_DST);

    EtcdConnInit();
    
    InstanceInit();
    if(DaoInit() != 0)
    {
        AK_LOG_FATAL << "DaoInit fialed.";
        return -1;
    }

    if (LogDeliveryInit() != 0)
    {
        AK_LOG_FATAL << "LogDeliveryInit fialed.";
        return -1;
    }
    
    dbinterface::ProjectUserManage::GetServerTag();

    // 初始化rbac
    g_rbac_client_ptr = new RbacRpcClient();
    UpdateRbacSrvList();

    //起http服务线程
    std::thread httpthread(startHttpServer);
    
    std::thread nsqthread2 = std::thread(MQProduceInit);
    
    std::thread etcdthread = std::thread(EtcdSrvInit);//里面启动消费路由的消息

    std::thread ak_conf_watch_thread = std::thread(AkConfWatch);
    std::thread web_domain_server_watch_thread = std::thread(WebDomainServerWatch);

    //要万事俱备了,才能启动这个服务,否则有些资源没有准备好,就启动核心业务逻辑,会有风险
    g_office_srv_ptr  = new OfficeServer(kMqueue_number); 
    g_office_srv_ptr->Start();

    std::thread timer_thread = std::thread(TimeTaskInit);

    //初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csoffice is starting";

    httpthread.join();
    etcdthread.join();
    nsqthread2.join();
    ak_conf_watch_thread.join();
    web_domain_server_watch_thread.join();
    CAkEtcdCliManager::destroyInstance();
    GlogClean2();
    return 0;
}

