#include "DevOnlineMng.h"
#include "MsgControl.h"
#include "OfficeServer.h"
#include "AppPushToken.h"
#include "DclientMsgDef.h"
#include "BackendFactory.h"
#include "MsgFilter.h"
#include "AkcsCommonDef.h"
#include "MsgIdToMsgName.h"

OfficeServer* g_office_srv_ptr = nullptr;
OfficeServer::OfficeServer(int mqueue_num)
{
    mqueue_num_ = mqueue_num;
    office_2_main_ptr_.reset(new Office2MainHandle(mqueue_num_));
}

void OfficeServer::Start()
{
    office_2_main_ptr_->Start();
}

//csmain传递过来的消息处理
int OfficeServer::OnMainMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;

    AK_LOG_INFO << "OnMainMsg recv msg. msgid:0x"<< std::hex <<  message_id <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id);
    
    if(FilterMsgManager::GetInstance()->CheckMsgIDInFilterList(message_id))
    {
        AK_LOG_INFO << "filter msg. msgid:0x" << std::hex << message_id <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id);
        return 0;
    }

    //通用
    if (BackendFactory::GetInstance()->DispatchMsg(acc_msg) == 0)
    {
        return 1;
    }
    
    //通用
    switch (message_id)
    {
        case MSG_FROM_INNER_CONN_INFO:
        {
            GetMsgControlInstance()->OnConnChange(acc_msg);
            break;
        }
        case MSG_FROM_INNER_CONN_APP_FORCE_LOGOUT:
        {
            GetMsgControlInstance()->OnAppForceLogout(acc_msg);
            break;
        }
        case MSG_FROM_DEVICE_REPORT_STATUS:
        {
            //如果设备从云删除重新添加 集群内部要广播
            GetMsgControlInstance()->OnDeviceReportStatusMsg(normal_msg, acc_msg);
            break;
        }
        
        case MSG_FROM_ANDROID_REPORT_STATUS:
        {
            GetMsgControlInstance()->OnAndroidReportStatusMsg(normal_msg, acc_msg);
            break;
        }
        //IOS向平台上报状态信息  //告警字符串加密
        case MSG_FROM_IOS_REPORT_STATUS:
        {
            GetMsgControlInstance()->OnIOSReportStatusMsg(normal_msg, acc_msg);
            break;
        }
        case MSG_FROM_DEVICE_REPORT_VOICE_MSG:
        {
            GetMsgControlInstance()->OnDeviceReportVoiceMsg(acc_msg);
            break;
        }
        case MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST:
        {
            //GetMsgControlInstance()->OnDeviceRequestVoiceMsgList(acc_msg);
            break;
        }
        case MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL:
        {
            GetMsgControlInstance()->OnDeviceRequestVoiceMsgUrl(acc_msg);
            break;
        }
        case MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG:
        {
            GetMsgControlInstance()->OnDeviceRequestDelVoiceMsg(acc_msg);
            break;
        }       
        case MSG_FROM_DEVICE_REQUEST_WEATHER:
        {
           GetMsgControlInstance()->OnDeviceRequestWeatherInfoMsg(acc_msg);
           break;
        }
        case MSG_FROM_DEVICE_REPORT_INPUT_STATUS:
        {
            GetMsgControlInstance()->OnDeviceReportRelayStatusMsg(acc_msg);
            break;
        }
        default:
        {
            AK_LOG_WARN << "Msg-ID 0x"<< std::hex  << message_id << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id) <<" error.";
            return -1;
        }
      
   }
    return 0;
}

void OfficeServer::SendMsg2Main(const std::string& client, const csmain::DeviceType type, const unsigned char *data, size_t size)
{
    office_2_main_ptr_->Send(client, type, (char*)data, size);
}

void OfficeServer::SendMsg2Main(MsgStruct& msg)
{
    office_2_main_ptr_->Send(msg);
}


//只能在上报状态时候调用
void OfficeServer::SetDevSetting(const std::string& mac, const ResidentDev& dev_client)
{
    std::lock_guard<std::mutex> lock(dev_clients_mutex_);
    ClientListIter it = dev_clients_.find(mac);
    if (it != dev_clients_.end())
    {
        dev_clients_.erase(mac);
    }

    dev_clients_[mac] = std::make_shared<CClient>();
    dev_clients_[mac]->SetType(static_cast<csmain::DeviceType>(dev_client.conn_type));
    dev_clients_[mac]->SetDeviceSetting(dev_client);

    //macinfo 在get时候再初始化
    MacInfo macinfo;
    memset(&macinfo, 0, sizeof(macinfo));    
    dev_clients_[mac]->SetDevMacInfo(macinfo);
}


int OfficeServer::GetDevSetting(const std::string& mac, ResidentDev& dev)
{
    std::lock_guard<std::mutex> lock(dev_clients_mutex_);
    ClientListIter it = dev_clients_.find(mac);
    if (it != dev_clients_.end())
    {
        dev = dev_clients_[mac]->GetDeviceSetting();
        return 0;
    }
    else //不存在就需要重建。
    {     
        //1、csmain过来的消息 都会调用这个接口
        //2、route过来的消息，通过session保证这个mac一定是挂在这个office下(route过来的消息不支持广播的消息，因为office不知道设备是否挂在自己的下面)
        if (0 != dbinterface::ResidentDevices::GetMacDev(mac, dev))
        {
            AK_LOG_WARN << "Failed to find device on db. mac: " << mac;
            return -1;
        }
        dev.conn_type = csmain::DeviceType::OFFICE_DEV;

        dev.is_attendance = dbinterface::OfficeDevices::GetIsAttendanceByUUID(std::string(dev.uuid));
        if (DatabaseExistenceStatus::QUERY_ERROR == dev.is_attendance)
        {
            AK_LOG_WARN << "Failed to find device is attendance on db. mac: " << mac;
            // db错误也照常发考勤打卡通知
            dev.is_attendance = DatabaseExistenceStatus::EXIST;
        }
        dev_clients_[mac] = std::make_shared<CClient>();
        dev_clients_[mac]->SetType(static_cast<csmain::DeviceType>(dev.conn_type));
        dev_clients_[mac]->SetDeviceSetting(dev);

        //macinfo 在get时候再初始化
        MacInfo macinfo;
        memset(&macinfo, 0, sizeof(macinfo));    
        dev_clients_[mac]->SetDevMacInfo(macinfo);
        return 0;
    }
    return -1;
}


int OfficeServer::GetMacInfo(const std::string& mac, MacInfo& info)
{
    {
        std::lock_guard<std::mutex> lock(dev_clients_mutex_);
        ClientListIter it = dev_clients_.find(mac);
        if (it != dev_clients_.end())
        {
            info = dev_clients_[mac]->GetDevMacInfo();
            if (info.init_status == 0)
            {
               //这个时候可能没有初始化devices setting
               ResidentDev dev = dev_clients_[mac]->GetDeviceSetting();
               DevOnlineMng::GetInstance()->InitMacInfo(mac, dev, info);
               dev_clients_[mac]->SetDevMacInfo(info);
            }
            return 0;
        }
    }
    return -1;
}


//只能在上报状态时候调用
void OfficeServer::SetAppSetting(const std::string& uid, 
   const OfficeAccount& app_client, CMobileToken& info)
{
    std::lock_guard<std::mutex> lock(app_clients_mutex_);
    ClientListIter it = app_clients_.find(uid);
    if (it != app_clients_.end())
    {
        app_clients_[uid]->SetType(app_client.conn_type);
        app_clients_[uid]->SetAppSetting(app_client);
        app_clients_[uid]->SetAppToken(info);
        app_clients_[uid]->SetConnOnline();
    }
    else
    {
        app_clients_[uid] = std::make_shared<CClient>();
        app_clients_[uid]->SetType(app_client.conn_type);
        app_clients_[uid]->SetAppSetting(app_client);
        app_clients_[uid]->SetAppToken(info);
        app_clients_[uid]->SetConnOnline();
    }
}

int OfficeServer::GetAppSetting(const std::string& uid, OfficeAccount& appinfo)
{
    {
        std::lock_guard<std::mutex> lock(dev_clients_mutex_);
        ClientListIter it = app_clients_.find(uid);
        if (it != app_clients_.end())
        {
            appinfo = app_clients_[uid]->GetAppSetting();
            return 0;
        }
        else
        {
            OfficeAccount account;
            if (0 != dbinterface::OfficePersonalAccount::GetUserAccount(uid, account))
            {
                AK_LOG_WARN << "Can not found uid " << uid;
                return -1;
            }
            CMobileToken token;
            if (GetAppPushTokenInstance()->getAppPushTokenByUid(uid, token) != 0)
            {
                AK_LOG_WARN << "the uid [" << uid << "] is invalide or not found";
                return -1;
            }
            app_clients_[uid] = std::make_shared<CClient>();
            app_clients_[uid]->SetType(account.conn_type);
            app_clients_[uid]->SetAppToken(token);
            app_clients_[uid]->SetAppSetting(account);
            app_clients_[uid]->SetConnOffline();//从数据库获取出来的数据都是离线的，等心跳周期到后变为在线
            
            memcpy(&appinfo, &account, sizeof(account));
            return 0;
        }
    }
    return -1;
}


int OfficeServer::GetAppToken(const std::string& uid, CMobileToken& token)
{
    //一人多套房下这里可能会出现跨业务类型的用户，比如主站点在office，住宅收到消息，这时候uid是office的,故这边直接查询数据库
    if (GetAppPushTokenInstance()->getAppPushTokenByUid(uid, token) != 0)
    {
        AK_LOG_WARN << "the uid [" << uid << "] is invalide or not found";
        return -1;
    }
    
    return 0;
}

void OfficeServer::SetAppOffline(const std::string& uid)
{
    std::lock_guard<std::mutex> lock(app_clients_mutex_);
    ClientListIter it = app_clients_.find(uid);
    if (it != app_clients_.end())
    {
        app_clients_[uid]->SetConnOffline();
    }
}

void OfficeServer::SetAppOnline(const std::string& uid)
{
    std::lock_guard<std::mutex> lock(app_clients_mutex_);
    ClientListIter it = app_clients_.find(uid);
    if (it != app_clients_.end())
    {
        app_clients_[uid]->SetConnOnline();
    }
}

bool OfficeServer::IsAppOnline(const std::string& uid)
{
    std::lock_guard<std::mutex> lock(app_clients_mutex_);
    ClientListIter it = app_clients_.find(uid);
    if (it != app_clients_.end())
    {
        return app_clients_[uid]->IsOnline();
    }
    return 0;
}

void OfficeServer::RemoteAppInfo(const std::string& uid)
{
    std::lock_guard<std::mutex> lock(app_clients_mutex_);
    ClientListIter it = app_clients_.find(uid);
    if (it != app_clients_.end())
    {
        app_clients_.erase(uid);
    }
}

int OfficeServer::GetDevClientFromMac(const std::string& mac, CClientPtr& client)
{
    {
        std::lock_guard<std::mutex> lock(dev_clients_mutex_);
        ClientListIter it = dev_clients_.find(mac);
        if (it != dev_clients_.end())
        {
            client = dev_clients_[mac];
            return 0;
        }
    }
    return -1;
}

