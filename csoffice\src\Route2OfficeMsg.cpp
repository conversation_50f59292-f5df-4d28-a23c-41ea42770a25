#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "CachePool.h"
#include "util.h"
#include <boost/algorithm/string.hpp>
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "Route2OfficeMsg.h"
#include "OfficeInit.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/ProjectInfo.h"
#include "AkcsCommonSt.h"
#include "ProjectUserManage.h"
#include "OfficeServer.h"
#include "MsgBuild.h"
#include "RouteMqProduce.h"
#include "MsgToControl.h"
#include "NotifyMsgControl.h"
#include "OfficeDb.h"
#include "Office2RouteMsg.h"
#include "AK.Resid.pb.h"
#include "AK.Adapt.pb.h"
#include "AK.Linker.pb.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "ClientControl.h"
#include "MsgControl.h"

CRoute2OfficeMsg::CRoute2OfficeMsg()
{

}


CRoute2OfficeMsg::~CRoute2OfficeMsg()
{

}

void CRoute2OfficeMsg::HandleP2PVoiceMsgAckReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] p2p handle voice msg:" << msg.DebugString();

    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac), msg.mac().c_str());
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id), msg.filename().c_str());
    common_ack.result = msg.result();

    //回ack
    if (GetMsgToControlInstance()->SendCommonAckMsg(MSG_FROM_DEVICE_REPORT_VOICE_MSG, common_ack) != 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed";
        return;
    }

    //通知接收人
    COffice2RouteMsg::GroupVoiceMsg(pdu);
}

void CRoute2OfficeMsg::HandleP2PSendVoiceMsg(const AK::BackendCommon::BackendP2PBaseMessage& base, const AK::Server::P2PSendVoiceMsg& msg)
{
    if (msg.receiver_type() == DEVICE_TYPE_APP)
    {
        OfficeAccount per_account;
        if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(msg.receiver_uuid(), per_account))
        {
            SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
            memset(&text_send.text_message, 0, sizeof(text_send.text_message));

            text_send.client_type = CPerTextNotifyMsg::APP_SEND;
            Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), "VOICE_MSG");
            text_send.text_message.id = msg.msg_id();
            ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "You have a voice message from %s", msg.location().c_str());
            text_send.text_message.type = CPerTextNotifyMsg::VOICE_MSG;
            CPerTextNotifyMsg cNotifyMsg(base, text_send, per_account.account);
            GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
        }
    }
    else if (msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
        memset(&online_msg, 0, sizeof(online_msg));
        online_msg.unread_voice_count = msg.count();
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(msg.receiver_uuid(), dev))
        {
            AK_LOG_WARN << "HandleP2PSendVoiceMsg failed uuid not found. uuid:" << msg.receiver_uuid();
            return;
        }
        snprintf(online_msg.uuid, sizeof(online_msg.uuid), "%s", dev.uuid);
        snprintf(online_msg.mac, sizeof(online_msg.mac), "%s", dev.mac);


        std::string xml_msg;
        GetMsgBuildHandleInstance()->BuildOnlineNotifyXmlMsg(online_msg, xml_msg);
        MsgStruct send_msg;
        ::memset(&send_msg, 0, sizeof(send_msg));
        send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
        send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
        send_msg.msg_id = MSG_TO_DEVICE_ONLINE_NOTIFY_MSG;
        snprintf(send_msg.client, sizeof(send_msg.client), "%s", dev.mac);
        snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), "%s", xml_msg.c_str());
        send_msg.msg_len = strlen(send_msg.msg_data);
        GetClientControlInstance()->SendTransferMsg(send_msg);
    }
}

void CRoute2OfficeMsg::HandleP2PWeatherInfoMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::LinkerWeatherNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] HandleP2PWeatherInfoMsg:" << msg.DebugString();

    SOCKET_MSG_DEV_WEATHER_INFO weather_msg;
    memset(&weather_msg, 0, sizeof(weather_msg));
    ::snprintf(weather_msg.mac, sizeof(weather_msg.mac), "%s", msg.mac().c_str());
    ::snprintf(weather_msg.city, sizeof(weather_msg.city), "%s", msg.city().c_str());
    ::snprintf(weather_msg.states, sizeof(weather_msg.states), "%s", msg.states().c_str());
    ::snprintf(weather_msg.country, sizeof(weather_msg.country), "%s", msg.country().c_str());
    ::snprintf(weather_msg.weather, sizeof(weather_msg.weather), "%s", msg.weather().c_str());
    ::snprintf(weather_msg.humidity, sizeof(weather_msg.humidity), "%s", msg.humidity().c_str());
    ::snprintf(weather_msg.temperature, sizeof(weather_msg.temperature), "%s", msg.temperature().c_str());

    if (GetMsgToControlInstance()->SendDevWeatherInfoMsg(MSG_TO_DEVICE_REPORT_WEATHER_MSG, weather_msg) != 0)
    {
        AK_LOG_WARN << "SendDevWeatherInfoMsg failed";
        return;
    }

    // 缓存到redis
    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_weather);
    if (cache_conn)
    {
        char weather_redis_key[256];
        char weather_redis_value[256];
        ::snprintf(weather_redis_key, sizeof(weather_redis_key), "%s-%s-%s", weather_msg.country, weather_msg.states, weather_msg.city);
        ::snprintf(weather_redis_value, sizeof(weather_redis_value), "%s!%s!%s", weather_msg.weather, weather_msg.temperature, weather_msg.humidity);

        cache_conn->set(weather_redis_key, weather_redis_value);
        cache_conn->expire(weather_redis_key, WEATHER_EXPIRE_SECOND);
        cache_manager->RelCacheConn(cache_conn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csoffice " << g_redis_db_weather;
    }
}

void CRoute2OfficeMsg::HandleP2PRefreshUserConfMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptNotifyAppRefreshConfigMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request refresh app conf msg:" << msg.DebugString();

    if (GetMsgControlInstance()->OnSendDevListChangeMsg(msg.account()) != 0)
    {
        AK_LOG_WARN << "OnSendDevListChangeMsg failed";
        return;
    }
}

void CRoute2OfficeMsg::HandleP2PRefreshDeviceIsAttendanceMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptNotifyDeviceIsAttendanceMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request refresh device attendance msg:" << msg.DebugString();

    ResidentDev dev;
    if ((int)DatabaseExistenceStatus::QUERY_ERROR == dbinterface::ResidentDevices::GetUUIDDev(msg.device_uuid(), dev))
    {
        AK_LOG_WARN << "HandleP2PRefreshDeviceIsAttendanceMsg get Mac uuid Fail. device_uuid=" << msg.device_uuid();
        return;
    }
    // 重置设备是否为考勤机
    dev.is_attendance = dbinterface::OfficeDevices::GetIsAttendanceByUUID(msg.device_uuid());
    if (DatabaseExistenceStatus::QUERY_ERROR == dev.is_attendance)
    {
        dev.is_attendance = DatabaseExistenceStatus::EXIST;
    }
    g_office_srv_ptr->SetDevSetting(dev.mac, dev);
}

/*
void CRoute2OfficeMsg::HandleP2PRemoteOpendoorMsg(const std::unique_ptr<CAkcsPdu>& pdu){
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "csresid receive open door msg from router:" << msg.DebugString();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), msg.msg_traceid().c_str());
    Snprintf(open_door.repost_mac, sizeof(open_door.repost_mac), msg.repost_mac().c_str());
    open_door.relay = static_cast<int>(msg.relay());

    HandleP2POpenDoorReq(&open_door, SOCKET_MSG_TYPE_NAME_OPENDOOR);
}

void CRoute2OfficeMsg::HandleP2PRemoteOpenSecurityRelayMsg(const std::unique_ptr<CAkcsPdu>& pdu){
    AK::Adapt::OpenSecurityRelayNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "csresid receive open SecurityRelay msg from router:" << msg.DebugString();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), msg.msg_traceid().c_str());
    Snprintf(open_door.repost_mac, sizeof(open_door.repost_mac), msg.repost_mac().c_str());
    open_door.relay = static_cast<int>(msg.security_relay());

    HandleP2POpenDoorReq(&open_door, SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY);
}

void CRoute2OfficeMsg::HandleP2POfficeFromDeviceOpenDoorReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainRequestOpenDoor msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "csresid receive open door msg from router:" << msg.DebugString();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), msg.msg_traceid().c_str());
    open_door.relay = static_cast<int>(msg.relay());

    HandleP2POpenDoorReq(&open_door, msg.open_door_type());
}

void CRoute2OfficeMsg::HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO* open_door, const std::string &open_door_type)
{
    if (open_door == NULL)
    {
        AK_LOG_WARN << "open_door is NULL";
        return;
    }

    int relay = open_door->relay;
    std::string uid = open_door->uid;
    std::string mac = open_door->mac;
    std::string accessible_floor = "0";             // 分号分割楼层, eg: "1;2;3;6;7;8;9;"
    std::string msg_traceid = open_door->trace_id;
    std::string receiver_mac = strlen(open_door->repost_mac) > 0 ? (open_door->repost_mac) : (open_door->mac);

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(uid, sip_info);
    
    if (sip_info.sip_type == csmain::OFFICE_APP)
    {
        if (sip_info.project_type == project::OFFICE)
        {
            accessible_floor = dbinterface::OfficePersonalAccount::GetFloorByAccount(uid);
        }
        else if (sip_info.project_type == project::OFFICE_NEW)
        {
            // 获取设备信息
            OfficeDevPtr dev;
            if (dbinterface::OfficeDevices::GetMacDev(mac, dev) == 0)
            {
                // 获取用户的UUID
                std::string personal_account_uuid;
                if (dbinterface::OfficePersonalAccount::GetUUIDByAccount(uid, personal_account_uuid) == 0)
                {
                    // 获取可达楼层
                    accessible_floor = dbinterface::OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(
                        personal_account_uuid, dev->unit_uuid, dev->uuid
                    );
                }
            }
        }
    }
    else if (sip_info.sip_type == csmain::OFFICE_DEV)
    {
        if (sip_info.project_type == project::OFFICE)
        {
            ResidentDev dev;
            if (dbinterface::ResidentDevices::GetSipDev(uid, dev) == 0)
            {
                accessible_floor = dbinterface::OfficePersonalAccount::GetFloorByAccount(dev.node);
            }
        }
        else if (sip_info.project_type == project::OFFICE_NEW)
        {
            // 获取设备信息
            OfficeDevPtr dev;
            if (dbinterface::OfficeDevices::GetSipDev(uid, dev) == 0)
            {
                // 获取关联人
                OfficeDeviceAssignInfo assign_info;
                if (dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(dev->uuid, assign_info) == 0)
                {
                    // 获取可达楼层
                    accessible_floor = dbinterface::OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(
                        assign_info.personal_account_uuid, dev->unit_uuid, dev->uuid
                    );
                }
            }
        }
    }

    if (accessible_floor.length() == 0)
    {
        accessible_floor = "0";
    }

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remote_control_msg;
    memset(&remote_control_msg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remote_control_msg.protocal, sizeof(remote_control_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remote_control_msg.type, sizeof(remote_control_msg.type), open_door_type.c_str());
    Snprintf(remote_control_msg.item[0], sizeof(remote_control_msg.item[0]), uid.c_str());
    ::snprintf(remote_control_msg.item[1], sizeof(remote_control_msg.item[1]), "%d", relay);
    Snprintf(remote_control_msg.item[2], sizeof(remote_control_msg.item[2]), msg_traceid.c_str());
    ::snprintf(remote_control_msg.item[3], sizeof(remote_control_msg.item[3]), "%s", accessible_floor.c_str());
    ::snprintf(remote_control_msg.item[4], sizeof(remote_control_msg.item[4]), "%s", mac.c_str());


    AK_LOG_INFO << "Request open door uid=" << uid
        << ", mac=" << mac << ", repost_mac=" << open_door->repost_mac
        << ", traceid=" << msg_traceid << ", open_door_type=" << open_door_type
        << ", accessible_floor=" << accessible_floor;

    char xml_msg[4096];
    memset(xml_msg, 0, sizeof(xml_msg));
    GetMsgBuildHandleInstance()->BuildRemoteControlMsg(xml_msg, sizeof(xml_msg), &remote_control_msg);
    
    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.msg_id = MSG_TO_DEVICE_REMOTE_CONTROL;
    send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
    send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    Snprintf(send_msg.client, sizeof(send_msg.client), receiver_mac.c_str());
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), xml_msg);
    send_msg.msg_len = strlen(send_msg.msg_data);
    
    GetClientControlInstance()->SendTransferMsg(send_msg);
    return;
}
*/
