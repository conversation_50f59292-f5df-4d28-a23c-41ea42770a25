#include "AlarmDealNotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "RouteMsg.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "OfficePushClient.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "Office2RouteMsg.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"

namespace old_office
{
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target,
        AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // target type 转换成 TransP2PMsgType
        TransP2PMsgType type = TransP2PMsgType::TO_APP_UID;
        if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT ||
            target_type == AlarmNotifyTargetType::DEV_INDOOR ||
            target_type == AlarmNotifyTargetType::DEV_OUTDOOR)
        {
            type = TransP2PMsgType::TO_DEV_MAC;
        }

        // 消息转发
        AK::BackendCommon::BackendP2PBaseMessage base = COffice2RouteMsg::CreateP2PBaseMsg(
            AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG,
            type,
            target,
            COffice2RouteMsg::DevProjectTypeToDevType(project::OFFICE),
            project::OFFICE
        );

        msg.set_target(target);
        msg.set_target_type((int)target_type);
        base.mutable_p2palarmdealnotifymsg2()->CopyFrom(msg);
        IP2PToRouteMsg(&base);
    }

    void ProcessAlarmDealNotify(OfficeInfo office_info, ALARM alarm_info, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        NotifyToIndoorDevByNode(msg.area_node(), msg);
        NotifyToMngDevByOfficeUUID(office_info.UUID(), msg);

        NotifyToPMAppByOfficeUUID(office_info.UUID(), msg);
        NotifyToUserAppByAccount(alarm_info.device_node, msg);
    }

    void NotifyToIndoorDevByNode(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历node下的室内机
        ResidentDeviceList dev_list;
        if (dbinterface::ResidentDevices::GetNodeIndoorDevList(node, dev_list) != 0)
        {
            AK_LOG_WARN << "GetNodeIndoorDevList failed: node=" << node;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_INDOOR, dev.mac, msg);
        }
    }

    void NotifyToMngDevByOfficeUUID(const std::string office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历项目下的所有管理机
        OfficeDevList dev_list;
        {
            if (dbinterface::OfficeDevices::GetAllMngDevList(office_uuid, dev_list) != 0)
                AK_LOG_WARN << "GetAllMngDevList failed: office_uuid=" << office_uuid;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_MANAGEMENT, dev->mac, msg);
        }
    }

    void NotifyToPMAppByOfficeUUID(const std::string office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历项目下的所有PM APP
        ResidentPerAccountList pm_app_list;
        if (dbinterface::OfficePersonalAccount::GetOfficePmApplistByMngID(office_uuid, pm_app_list) != 0)
        {
            AK_LOG_WARN << "GetPmApplistByOfficeUUID failed: office_uuid=" << office_uuid;
            return;
        }

        // 转发告警处理通知
        for (auto& pm_item : pm_app_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_PM, pm_item.account, msg);
        }
    }

    void NotifyToUserAppByAccount(const std::string& account, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 转发告警处理通知(办公没有主从，只需要通知当前的操作者)
        ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_USER, account, msg);
        AK_LOG_INFO << "OldOffice AlarmDealNotify App, account = " << account;
    }

}




