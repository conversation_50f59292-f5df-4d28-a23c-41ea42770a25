#ifndef _CONTROL_ALARM_DEAL_NOTIFY_MSG_H_
#define _CONTROL_ALARM_DEAL_NOTIFY_MSG_H_
#include <string>
#include "RouteBase.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Office2AppMsg.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/office/OfficeInfo.h"

namespace old_office
{
    // 消息转发到Router
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target,
        AK::Server::P2PAlarmDealNotifyMsg& msg);

    void ProcessAlarmDealNotify(OfficeInfo office_info, ALARM alarm_info, AK::Server::P2PAlarmDealNotifyMsg& msg);
    // 通知Node下的所有 室内机
    void NotifyToIndoorDevByNode(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg);
    // 通知项目下的所有 管理机
    void NotifyToMngDevByOfficeUUID(const std::string office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);
    // 通知项目下的所有 PMAPP
    void NotifyToPMAppByOfficeUUID(const std::string office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);
    // 通知 UserAPP
    void NotifyToUserAppByAccount(const std::string& account, AK::Server::P2PAlarmDealNotifyMsg& msg);
}

#endif
