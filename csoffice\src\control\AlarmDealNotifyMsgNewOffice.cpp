#include "AlarmDealNotifyMsgNewOffice.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "RouteMsg.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "OfficePushClient.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "Office2RouteMsg.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeAdmin.h"

namespace new_office
{
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // target type 转换成 TransP2PMsgType
        TransP2PMsgType type = TransP2PMsgType::TO_APP_UID;
        if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT ||
            target_type == AlarmNotifyTargetType::DEV_INDOOR ||
            target_type == AlarmNotifyTargetType::DEV_OUTDOOR)
        {
            type = TransP2PMsgType::TO_DEV_MAC;
        }

        // 消息转发
        AK::BackendCommon::BackendP2PBaseMessage base = COffice2RouteMsg::CreateP2PBaseMsg(
            AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG,
            type,
            target,
            COffice2RouteMsg::DevProjectTypeToDevType(project::OFFICE),
            project::OFFICE
        );

        msg.set_target(target);
        msg.set_target_type((int)target_type);
        base.mutable_p2palarmdealnotifymsg2()->CopyFrom(msg);
        IP2PToRouteMsg(&base);
    }

    void ProcessAlarmDealNotify(OfficeInfo office_info, ALARM alarm_info, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 获取告警设备信息        
        OfficeDevPtr alarm_dev_ptr;
        dbinterface::OfficeDevices::GetMacDev(alarm_info.mac, alarm_dev_ptr);
        std::string device_uuid = alarm_dev_ptr->uuid;

        // 告警处理
        int alarm_code = alarm_info.alarm_code;
        if (alarm_code <= (int)ALARM_CODE::SOS)
        {
            // 布撤防告警: 通知室内机归属人的公司
            SendArmingNotifyByDeviceUUID(device_uuid, msg);

            // 通知发出告警的设备
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_OUTDOOR, alarm_info.mac, msg);
        }
        else if (alarm_code == (int)ALARM_CODE::TAMPER)
        {
            // 防拆告警: 通知对设备有权限的所有的公司
            SendTampergNotifyByDeviceUUID(device_uuid, msg);

            // 通知发出告警的设备
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_OUTDOOR, alarm_info.mac, msg);
        }
        else if (alarm_code == (int)ALARM_CODE::EMERGENCY)
        {
            // 紧急告警: 通知项目下所有管理机、室内机、Admin APP、User APP
            SendEmergencygNotifyByOfficeUUID(office_info.UUID(), msg);
        }
        else if (alarm_code == (int)ALARM_CODE::DOOR_HELD_OPEN || alarm_code == (int)ALARM_CODE::BREAK_IN)
        {
            // 门常开、强闯: 通知对Relay有权限的所有公司
            SendBreakIngNotifyByDeviceUUID(device_uuid, alarm_info.relay_type, alarm_info.relay_num, msg);

            // 通知发出告警的设备
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_OUTDOOR, alarm_info.mac, msg);
        }
        else
        {
            AK_LOG_INFO << "NewOffice AlarmNotify failed: alarm_code = " << alarm_info.alarm_code;
        }
    }

    void SendArmingNotifyByDeviceUUID(const std::string& device_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 布撤防告警，只通知室内机归属人的公司
        std::set<std::string> notifyed_dev_set;
        OfficeDeviceAssignInfo office_device_assign_info;
        int ret = dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(device_uuid, office_device_assign_info);
        if (ret == 0 && strlen(office_device_assign_info.office_company_uuid) > 0)
        {
            NotifyToMngDevByCompanyUUID(office_device_assign_info.office_company_uuid, msg, notifyed_dev_set);
            NotifyToAdminAppByCompanyUUID(office_device_assign_info.office_company_uuid, msg);
        }
    }

    void SendTampergNotifyByDeviceUUID(const std::string& device_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        std::set<std::string> notifyed_dev_set;
        CompanyDoorList office_company_door_info_list;
        dbinterface::DevicesDoorList::GetOfficeCompanyDoorByDevicesUUID(device_uuid, office_company_door_info_list);

        for (const auto& door_info : office_company_door_info_list)
        {
            NotifyToMngDevByCompanyUUID(door_info.office_company_uuid, msg, notifyed_dev_set);
            // NotifyToAdminAppByCompanyUUID(door_info.office_company_uuid, msg);
        }
    }

    void SendEmergencygNotifyByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // NotifyToMngDevByOfficeUUID(office_uuid, msg);
        // NotifyToIndoorDevByOfficeUUID(office_uuid, msg);

        // NotifyToAdminAppByOfficeUUID(office_uuid, msg);
        // NotifyToUserAppByOfficeUUID(office_uuid, msg);
    }

    void SendBreakIngNotifyByDeviceUUID(const std::string& device_uuid, RelayType relay_type, int relay_num, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        std::set<std::string> notifyed_dev_set;
        CompanyDoorList office_company_door_info_list;
        dbinterface::DevicesDoorList::GetOfficeCompanyDoorByDevicesUUID(device_uuid, office_company_door_info_list);

        for (const auto& door_info : office_company_door_info_list)
        {
            if (!(relay_type == RelayType::RELAY && (relay_num & door_info.relay)) &&
                !(relay_type == RelayType::SECURITY_TYPE && (relay_num & door_info.srelay)))
            {
                continue;
            }

            NotifyToMngDevByCompanyUUID(door_info.office_company_uuid, msg, notifyed_dev_set);
            // NotifyToAdminAppByCompanyUUID(door_info.office_company_uuid, msg);
        }
    }

    void NotifyToMngDevByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历项目下的所有管理机
        OfficeDevList dev_list;
        {
            if (dbinterface::OfficeDevices::GetAllMngDevList(office_uuid, dev_list) != 0)
                AK_LOG_WARN << "GetAllMngDevList failed: office_uuid=" << office_uuid;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_MANAGEMENT, dev->mac, msg);
        }
    }

    void NotifyToMngDevByCompanyUUID(const std::string& company_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg
        , std::set<std::string>& notifyed_dev_set)
    {
        // 获取Company下所有管理机
        OfficeDevList device_list;
        dbinterface::OfficeDevices::GetAllMngDevListByCompanyUUID(company_uuid, device_list);

        // 转发告警处理通知
        for (const auto& mng_dev : device_list)
        {
            // 过滤已通知过的管理机
            if (notifyed_dev_set.find(mng_dev->uuid) != notifyed_dev_set.end())
            {
                continue;
            }

            // 标记已通知过的管理机
            notifyed_dev_set.insert(mng_dev->uuid);

            // 获取管理机设备信息
            ResidentDev dev;
            if (dbinterface::ResidentDevices::GetUUIDDev(mng_dev->uuid, dev) != 0)
            {
                AK_LOG_WARN << "NewOfficeCommonAlarmNotifyMngDev, get dev by uuid failed, uuid = " << mng_dev->uuid;
                continue;
            }

            // 通知管理机
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_MANAGEMENT, dev.mac, msg);
        }
    }

    void NotifyToIndoorDevByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历项目下的所有室内机
        OfficeDevList dev_list;
        {
            if (dbinterface::OfficeDevices::GetAllOfficeIndoorListByMngID(office_uuid, dev_list) != 0)
                AK_LOG_WARN << "GetAllOfficeIndoorListByMngID failed: office_uuid=" << office_uuid;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_INDOOR, dev->mac, msg);
        }
    }

    void NotifyToAdminAppByCompanyUUID(const std::string& company_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 获取Company下所有Admin APP列表
        OfficeAdminInfoList office_admin_info_list;
        dbinterface::OfficeAdmin::GetOfficeAdminInfoListByCompanyUUID(company_uuid, office_admin_info_list);

        // 转发告警处理通知
        for (const auto& admin_info : office_admin_info_list)
        {
            if (strlen(admin_info.personal_account_uuid) == 0 || admin_info.app_status == 0)
            {
                continue;
            }

            OfficeAccount per_account;
            if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(admin_info.personal_account_uuid, per_account))
            {
                // 通知Admin APP
                ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_ADMIN, per_account.account, msg);
            }
        }
    }

    void NotifyToAdminAppByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 获取项目下所有Admin APP列表
        OfficeAdminMap admin_info_map;
        OfficeCompanyAdminMap company_admin_map;
        dbinterface::OfficeAdmin::GetOfficeAdminPerMapByProjectUUID(office_uuid, admin_info_map, company_admin_map);

        // 转发告警处理通知
        for (const auto& admin_info : admin_info_map)
        {
            if (admin_info.first.length() == 0)
            {
                continue;
            }

            OfficeAccount per_account;
            if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(admin_info.first, per_account))
            {
                // 通知Admin APP
                ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_ADMIN, per_account.account, msg);
            }
        }
    }

    void NotifyToUserAppByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 获取项目下所User APP列表
        OfficePersonnelMap personal_map;
        dbinterface::OfficePersonnel::GetOfficePersonnelByProjectUUID(office_uuid, personal_map);

        // 转发告警处理通知
        for (const auto& person : personal_map)
        {
            if (person.first.length() == 0)
            {
                continue;
            }

            OfficeAccount per_account;
            if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(person.first, per_account))
            {
                // 通知Admin APP
                ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_USER, per_account.account, msg);
            }
        }
    }
}
