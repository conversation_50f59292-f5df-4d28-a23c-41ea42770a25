#ifndef _CONTROL_ALARM_DEAL_NEW_OFFICE_NOTIFY_MSG_H_
#define _CONTROL_ALARM_DEAL_NEW_OFFICE_NOTIFY_MSG_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Office2AppMsg.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeCompanyDoorList.h"

namespace new_office
{
    
    /**
     * @brief 转发告警处理通知消息到csrouter
     * @param target_type 告警通知的目标类型
     * @param target 目标的标识，如设备 UUID、用户账户等
     * @param msg 要转发的告警处理通知消息
     */
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target,
        AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 处理告警处理通知，调用各个通知函数
     * @param office_info 办公信息结构体
     * @param alarm_info 告警信息结构体
     * @param msg 告警处理通知消息
     */
    void ProcessAlarmDealNotify(OfficeInfo office_info, ALARM alarm_info, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 发送布防通知到指定设备
     * @param device_uuid 设备的唯一标识符
     * @param msg 告警处理通知消息
     */
    void SendArmingNotifyByDeviceUUID(const std::string& device_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 发送防拆告警通知到指定设备
     * @param device_uuid 设备的唯一标识符
     * @param msg 告警处理通知消息
     */
    void SendTampergNotifyByDeviceUUID(const std::string& device_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 发送紧急告警通知到指定办公区域
     * @param office_uuid 办公区域的唯一标识符
     * @param msg 告警处理通知消息
     */
    void SendEmergencygNotifyByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 发送入侵告警通知到指定设备
     * @param device_uuid 设备的唯一标识符
     * @param relay_type 继电器类型
     * @param realy_num 继电器编号
     * @param msg 告警处理通知消息
     */
    void SendBreakIngNotifyByDeviceUUID(const std::string& device_uuid, RelayType relay_type, int realy_num,
        AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 根据办公区域 UUID 通知用户 APP
     * @param office_uuid 办公区域的唯一标识符
     * @param msg 告警处理通知消息
     */
    void NotifyToUserAppByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 根据办公区域 UUID 通知管理员 APP
     * @param office_uuid 办公区域的唯一标识符
     * @param msg 告警处理通知消息
     */
    void NotifyToAdminAppByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 根据办公区域 UUID 通知室内设备
     * @param office_uuid 办公区域的唯一标识符
     * @param msg 告警处理通知消息
     */
    void NotifyToIndoorDevByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 根据办公区域 UUID 通知管理设备
     * @param office_uuid 办公区域的唯一标识符
     * @param msg 告警处理通知消息
     */
    void NotifyToMngDevByOfficeUUID(const std::string& office_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);

    /**
     * @brief 根据公司 UUID 通知管理设备
     * @param company_uuid 公司的唯一标识符
     * @param msg 告警处理通知消息
     * @param notifyed_dev_set 已通知的设备集合，避免重复通知
     */
    void NotifyToMngDevByCompanyUUID(const std::string& company_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg, std::set<std::string>& notifyed_dev_set);

    /**
     * @brief 根据公司 UUID 通知管理员 APP
     * @param company_uuid 公司的唯一标识符
     * @param msg 告警处理通知消息
     */
    void NotifyToAdminAppByCompanyUUID(const std::string& company_uuid, AK::Server::P2PAlarmDealNotifyMsg& msg);
};

// 结束防止头文件重复包含的条件编译
#endif //_ROUTE_ALARM_DEAL_NOTIFY_MSG_H_

