#include "Metric.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "RouteClientMng.h"
#include "RouteMqProduce.h"
#include "ConnectionPool.h"
#include "NotifyMsgControl.h"
#include "ConfigFileReader.h"
#include "MetricService.h"

extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern uint64_t g_openapi_health_check_count;
#define VERSION_CONF_FILE "/usr/local/akcs/csoffice/conf/version.conf"


void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csoffice_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "logdb_get_conn_failed_count",
        "LOGDB GetConnection failed count",
        "csoffice_logdb_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csoffice_linux_msgsnd_failed_count",
        "csoffice linux msgsnd failed count",
        "csoffice_linux_msgsnd_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csoffice_linux_msgrcv_failed_count",
        "csoffice linux msgrcv failed count",
        "csoffice_linux_msgrcv_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csoffice_notify_queue_length",
        " The length of csoffice notify queue",
        "csoffice_notify_queue_length",
        []() -> long { return (long)(CNotifyMsgControl::GetInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "nsq_check",
        "nsq producer status",
        "csoffice_nsq_check_error",
        []() -> long { return (long)(g_nsq_producer->Status() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "redis_check",
        "redis server status",
        "csoffice_redis_check_error",
        []() -> long { return (long)(CacheManager::getInstance()->CheckRedisNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "db_conn_check",
        "db conn status",
        "csoffice_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csroute_check",
        "route server status",
        "csoffice_csroute_check_error",
        []() -> long { return (long)(CRouteClientMng::Instance()->CheckRouteNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "etcd_check",
        "etcd server status",
        "csoffice_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );
}


