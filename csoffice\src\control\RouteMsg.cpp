#include "RouteMsg.h"
#include "AkcsMsgDef.h"
#include "OfficeInit.h"
#include "RouteMqProduce.h"

extern AKCS_CONF       gstAKCSConf;
extern RouteMQProduce* g_nsq_producer;

int IP2PToRouteMsg(const google::protobuf::MessageLite* msg)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
    return 0;
}
