#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AgentBase.h"
#include <random>
#include "Office2RouteMsg.h"
#include "OfficeServer.h"

extern OfficeServer *g_office_srv_ptr;


IBase::IBase()
{

}

//const boost::any& IBase::GetContext() const
//{
//    return context_;
//}

int IBase::IParseXml(char *msg)
{
    return 0;
}

int IBase::IControl()
{
    return 0;    
}


int IBase::IBuildReplyMsg(std::string &msg, uint16_t &msg_id)
{
    return 0;
}

int IBase::IPushNotify()
{
    return 0;
}

int IBase::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    return 0;
}

int IBase::IToRouteMsg()
{
    return 0;
}

//这里可以直接返回引用，因为处理流程都是在自己类里面
const ResidentDev& IBase::GetDevicesClient() 
{
    const ResidentDev& dev = boost::any_cast<const ResidentDev&>(context_);
    return dev;
}

void IBase::GetMacInfo(MacInfo &info) 
{
    ResidentDev dev = GetDevicesClient();
    memset(&info, 0, sizeof(info));
    if (g_office_srv_ptr->GetMacInfo(dev.mac, info) < 0)
    {
        std::string err = "GetMacInfo failed. mac is ";
        err  = err + dev.mac;
        throw MyException(err);
    }
} 

void IBase::SetContext(const boost::any& context)
{
    context_ = context;
}


void IBase::ReplyDevMsg(std::string&msg, uint16_t msgid)
{
    FactoryReplyDevMsg(GetDevicesClient(), msg, msgid, EncType());
}

void IBase::PushThirdNotifyMsg(std::string &msg, uint32_t &msg_id, std::string &key)
{
    COffice2RouteMsg::SendLinKerCommonMsg(msg_id, msg, key);                 
}
