#ifndef _AGENT_BASE_H_
#define  _AGENT_BASE_H_
#include <string>
#include <memory>
#include <boost/any.hpp>
#include "AkExcept.h"
#include "DevOnlineMng.h"
#include "BackendFactory.h"
#include "CoreUtil.h"
#include "AkcsCommonDef.h"

class IBase
{
public:
    IBase();
    virtual ~IBase() = default;
    virtual  int IParseXml(char *msg);
    virtual  int IControl(); 
    virtual  int IBuildReplyMsg(std::string &msg, uint16_t &msg_id);
    virtual  int IPushNotify();
    virtual  int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);
    virtual  int IToRouteMsg();
    virtual  IBasePtr NewInstance() = 0;
    virtual  MsgEncryptType EncType() = 0;
    virtual std::string FuncName() = 0;

    void SetContext(const boost::any& context);
    //这里可以直接返回引用，因为处理流程都是在自己类里面
    const ResidentDev& GetDevicesClient();
    void GetMacInfo(MacInfo &info);

    void ReplyDevMsg(std::string&msg, uint16_t msgid);
    void PushThirdNotifyMsg(std::string &msg, uint32_t &msg_id, std::string &key);
    
public:
    boost::any context_;
};



#endif
