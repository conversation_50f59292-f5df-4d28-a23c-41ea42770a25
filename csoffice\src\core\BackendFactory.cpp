#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "BackendFactory.h"
#include "OfficeServer.h"
#include "CsmainAES256.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "Office2RouteMsg.h"
#include "CoreUtil.h"
#include "MsgIdToMsgName.h"

BackendFactory* BackendFactory::GetInstance()
{
    static BackendFactory handle;
    return &handle;
}

void BackendFactory::AddFunc(IBasePtr &ptr, FUNC_TYPE type, uint16_t msgid)
{
    if (type == FUNC_TYPE::DEV)
    {
        dev_funcs_[msgid] = std::move(ptr);
    }
    else if (type == FUNC_TYPE::APP)
    {
        app_funcs_[msgid] = std::move(ptr);
    }
}

void BackendFactory::AddNewOfficeDevFunc(IBasePtr &ptr, uint16_t msgid)
{
    newoffice_dev_funcs_[msgid] = std::move(ptr);
}


int BackendFactory::HandleDevRequest(const MsgStruct* acc_msg, FuncList funcs, int message_id)
{
    ResidentDev dev;
    char *msg = nullptr;

    auto func_iter = funcs.find(message_id);
    MsgEncryptType encrypt_type = func_iter->second->EncType();

    // 解密Dclient消息
    if (DecryptDevMsgInfo(acc_msg, dev, &msg, encrypt_type) != 0)
    {
        AK_LOG_INFO <<  "DecryptDevMsgInfo error";
        return 0;
    }

    IBasePtr msg_handler = func_iter->second->NewInstance();
    msg_handler->SetContext(dev);

    // 解析设备消息
    if (msg_handler->IParseXml(msg) != 0)
    {
        return -1;
    }

    // 处理消息主流程
    if (msg_handler->IControl() != 0)
    {
        return -1;
    }

    // 构造reply_msg
    uint16_t msg_id = 0;
    std::string reply_msg;
    if (msg_handler->IBuildReplyMsg(reply_msg, msg_id) != 0)
    {
        return -1;
    }

    // 回复设备
    if (reply_msg.size() > 0 && msg_id > 0)
    {
        FactoryReplyDevMsg(dev, reply_msg, msg_id, msg_handler->EncType());
    }

    // 推送给cspush
    if (msg_handler->IPushNotify() != 0)
    {
        return -1;
    }

    // 构造cslinker消息
    std::string key;
    std::string link_msg;
    uint32_t link_id = 0;
    if (msg_handler->IPushThirdNotify(link_msg, link_id, key) != 0)
    {
        return -1;
    }

    // 推送给cslinker
    if (link_msg.size() > 0 && link_id)
    {
        COffice2RouteMsg::SendLinKerCommonMsg((int)link_id, link_msg, key);
    }            

    // 发送给csroute处理
    if (msg_handler->IToRouteMsg() != 0)
    {
        return -1;
    }

    return 0;
}

int BackendFactory::HandleAppRequest(const MsgStruct* acc_msg, int message_id)
{
    return 1;
}

int BackendFactory::DispatchMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;

    AK_LOG_INFO << "OnMainMsg recv msg. msgid:0x" << std::hex << message_id <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id);

    try 
    {
        if (acc_msg->client_type == csmain::DeviceType::OFFICE_DEV)
        {
            std::string mac = acc_msg->client;

            ResidentDev dev;
            if (g_office_srv_ptr->GetDevSetting(mac, dev) != 0)
            {
                AK_LOG_WARN << "GetDevSetting failed. mac is " << mac;
                return -1;
            }

            
            MacInfo info;
            memset(&info, 0, sizeof(info));
            if (g_office_srv_ptr->GetMacInfo(mac, info) < 0)
            {
                AK_LOG_WARN << "GetMacInfo failed. mac is " << mac;
                return -1;
            }
            
            // 处理设备请求
            if (info.is_new_office && newoffice_dev_funcs_.find(message_id) != newoffice_dev_funcs_.end())
            {
                return HandleDevRequest(acc_msg, newoffice_dev_funcs_, message_id);
            }
            else if (dev_funcs_.find(message_id) != dev_funcs_.end())
            {
                return HandleDevRequest(acc_msg, dev_funcs_, message_id);
            }
        }
        
        // 处理app请求
        if (app_funcs_.find(message_id) != app_funcs_.end())
        {
            return HandleAppRequest(acc_msg, message_id);
        }
    }
    catch(MyException& e)
    {
       AK_LOG_WARN <<  "MyException " << e.what();
       return -1;
    }
    catch(std::exception& e)
    {
       //其他的错误
       AK_LOG_WARN << "MyException1 " <<e.what();
       return -1;
    }
    
    return 1;
}

/*
程序启动时候自动注册到这里的类，是属于工具类，后续会为每个消息都重新new一个对象来处理
*/
void RegFunc(IBasePtr &f, BackendFactory::FUNC_TYPE type, uint16_t msgid)
{
    BackendFactory::GetInstance()->AddFunc(f, type, msgid);
}

void RegNewOfficeDevFunc(IBasePtr &f, uint16_t msgid)
{
    BackendFactory::GetInstance()->AddNewOfficeDevFunc(f, msgid);
}

