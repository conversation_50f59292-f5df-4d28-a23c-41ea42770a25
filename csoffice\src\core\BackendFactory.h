#ifndef _FACTORY_H_
#define _FACTORY_H_
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <cstdint>
#include "MsgStruct.h"
#include "AkLogging.h"

class IBase;
typedef std::shared_ptr<IBase> IBasePtr;
typedef std::map<uint16_t, IBasePtr> FuncList;
typedef FuncList::iterator FuncListIter;

class BackendFactory
{
public:
    BackendFactory() = default;
    enum FUNC_TYPE{APP, DEV};
    void AddFunc(IBasePtr &ptr, FUNC_TYPE type, uint16_t msgid);
    void AddNewOfficeDevFunc(IBasePtr &ptr,  uint16_t msgid);
    int DispatchMsg(const MsgStruct* acc_msg);
    static BackendFactory* GetInstance();

private:
    int HandleAppRequest(const MsgStruct* acc_msg, int message_id);
    int HandleDevRequest(const MsgStruct* acc_msg, FuncList funcs, int message_id);
private:
    FuncList app_funcs_;
    FuncList dev_funcs_;

    FuncList newoffice_app_funcs_;
    FuncList newoffice_dev_funcs_;

};

void RegFunc(IBasePtr &f, BackendFactory::FUNC_TYPE type, uint16_t msgid);
void RegNewOfficeDevFunc(IBasePtr &f, uint16_t msgid);

#endif
