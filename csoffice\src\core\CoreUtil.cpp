#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "CoreUtil.h"
#include "OfficeServer.h"
#include "CsmainAES256.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "Office2RouteMsg.h"


int DecryptDevMsgInfo(const MsgStruct* acc_msg, ResidentDev &dev, char **msg, MsgEncryptType enc_type)
{
    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);

    std::string mac = acc_msg->client;
    if (g_office_srv_ptr->GetDevSetting(mac, dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed. mac is " << mac;
        return -1;
    }

    uint32_t size = ntohs(normal_msg->data_size);
    char *payload = (char*)normal_msg->data;   

    // 版本号,根据这个来判断是否加密
    int msg_version = (normal_msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET;
    if (msg_version == VERSION_2_0 && size > 0)
    {
        if (enc_type == MsgEncryptType::TYEP_MAC_ENCRYPT)
        {
            AesDecryptByMac(payload, payload, dev.mac, size);
        }
        else if (enc_type == MsgEncryptType::TYEP_DEFAULT_ENCRYPT)
        {
            AesDecryptByDefault(payload, payload, size);
        }
        else if (enc_type == MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT)
        {
            AesDecryptByMac(payload, payload, AES_KEY_DEFAULT_MAC, size);
        }
        else
        {
            AK_LOG_WARN << "encrypt type is error. " << enc_type;
            return -1;        
        }
    }
    else
    {
        AK_LOG_INFO << "msg_version = " << msg_version << ", not need decrypt msg or size=0";
    }
    
    //这个msg 直接指向acc_msg里面的内存, 在使用中不会释放。
    *msg = payload;
    return 0;
}


int BuildDclientMacEncMsg(const ResidentDev &dev, const std::string&msg, uint16_t msgid, SOCKET_MSG &socket_message, MsgEncryptType enc_type)
{
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)&socket_message.data;
    memcpy(msg_normal->data, msg.c_str(), msg.size());

    char* payload = (char*)msg_normal->data;
    int data_size = strlen((char*)msg_normal->data);

    if (enc_type == MsgEncryptType::TYEP_MAC_ENCRYPT)
    {
        AesEncryptByMacNew(payload, payload, dev.mac, &data_size, dev.is_dy_iv, sizeof(msg_normal->data));
    }
    else if (enc_type == MsgEncryptType::TYEP_DEFAULT_ENCRYPT)
    {
        AesEncryptByDefaultNew(payload, payload, &data_size, dev.is_dy_iv, sizeof(msg_normal->data));
    }
    else if (enc_type == MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT)
    {
        AesEncryptByDefaultMacNew(payload, payload, &data_size, dev.is_dy_iv, sizeof(msg_normal->data));
    }
    else
    {
        AK_LOG_WARN << "encrypt type is error. " << enc_type;
        return -1;        
    }
    
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (GetMsgBuildHandleInstance()->BuildNormalMsgHeader(&socket_message,  msgid, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    socket_message.size = data_size + head_size;

    return 0;
}

int FactoryReplyDevMsg(const ResidentDev &dev, const std::string&msg, uint16_t msgid, MsgEncryptType enc_type)
{
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, msg, msgid, socket_message, enc_type) == 0)
    {
        if (GetClientControlInstance()->SendTransferMsg(dev.mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
        {
            AK_LOG_WARN << "SendTransferMsg failed. mac:" << dev.mac;
        }
    }    
    return 0;
}

