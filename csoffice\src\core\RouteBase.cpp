#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RouteBase.h"
#include <random>
#include "Office2RouteMsg.h"
#include "OfficeInit.h"
#include "RouteMqProduce.h"

 extern RouteMQProduce* g_nsq_producer;

 IRouteBase::IRouteBase()
 {

 }

//const boost::any& IBase::GetContext() const
//{
//    return context_;
//}



int IRouteBase::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    return 0;    
}


int IRouteBase::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    return 0;
}

int IRouteBase::IToRouteMsg(uint32_t msg_id, const google::protobuf::MessageLite* msg)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(msg_id);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);     
    return 0;
}

int IRouteBase::IP2PToRouteMsg(const google::protobuf::MessageLite* msg)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::OFFICE);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);      
    return 0;
}

int IRouteBase::ReplyToDevMsg(const std::string &to_mac, const std::string &msg, uint16_t msg_id, MsgEncryptType enc_type)
{
    if (to_mac.size() > 0 && msg.size() > 0 && msg_id > 0)
    {
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        if (g_office_srv_ptr->GetDevSetting(to_mac, dev) < 0)
        {
            AK_LOG_WARN << "GetDeviceSetting failed. mac is " << to_mac;
            return -1;
        }
        FactoryReplyDevMsg(dev, msg, msg_id, enc_type);
        return 0;
    }
    return -1;

}


void IRouteBase::GetMacInfo(MacInfo &info) 
{
    ResidentDev dev ;//= GetDevicesClient();
    memset(&info, 0, sizeof(info));
    if (g_office_srv_ptr->GetMacInfo(dev.mac, info) < 0)
    {
        std::string err = "GetMacInfo failed. mac is ";
        err  = err + dev.mac;
        throw MyException(err);
    }
} 



