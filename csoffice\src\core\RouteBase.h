#ifndef _ROUTE_BASE_H_
#define _ROUTE_BASE_H_
#include <string>
#include <memory>
#include <boost/any.hpp>
#include "OfficeDb.h"
#include "AkExcept.h"
#include "OfficeServer.h"
#include "AkcsPduBase.h"
#include "BackendFactory.h"
#include "CoreUtil.h"

class IRouteBase;

typedef std::shared_ptr<IRouteBase> IRouteBasePtr;

class IRouteBase
{
public:
    IRouteBase();

    virtual  int IControl(const std::unique_ptr<CAkcsPdu> &pdu); 
    virtual  int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    virtual  int IToRouteMsg(uint32_t msg_id, const google::protobuf::MessageLite* msg);
    virtual  int IP2PToRouteMsg(const google::protobuf::MessageLite* msg);
    
    virtual  IRouteBasePtr NewInstance() = 0;
    virtual std::string FuncName() = 0;

    void GetMacInfo(MacInfo &info);
 
    int ReplyToDevMsg(const std::string &to_mac, const std::string &msg, uint16_t msg_id, MsgEncryptType enc_type);
    
public:
    boost::any context_;
};


#endif