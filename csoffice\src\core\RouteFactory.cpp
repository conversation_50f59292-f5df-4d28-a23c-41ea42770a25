#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "RouteFactory.h"
#include "OfficeServer.h"
#include "CsmainAES256.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "Office2RouteMsg.h"
#include "CoreUtil.h"
#include "SnowFlakeGid.h"

RouteFactory* RouteFactory::GetInstance()
{
    static RouteFactory handle;
    return &handle;
}

void RouteFactory::AddRouteFunc(IRouteBasePtr& ptr, uint32_t msgid)
{
    funcs_[msgid] = std::move(ptr);

}

void RouteFactory::AddNewOfficeRouteFunc(IRouteBasePtr& ptr, uint32_t msgid)
{
    newoffice_funcs_[msgid] = std::move(ptr);
}

int RouteFactory::DispatchMsg(uint32_t message_id, const std::unique_ptr<CAkcsPdu>& pdu)
{    

    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "DispatchMsg recv msg. msgid:" << message_id
                << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(message_id)
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();


    // 获取消息处理的处理函数句柄
    RouteFuncListIter it = newoffice_funcs_.find(message_id);
    if (it == newoffice_funcs_.end())
    {
        it = funcs_.find(message_id);
        if (it == funcs_.end())
        {
            return 1;
        }
    }

    try
    {

        IRouteBasePtr p = std::move(it->second->NewInstance());

        if (p->IControl(pdu) != 0)
        {
            AK_LOG_WARN << "exec IControl error";
            return 0;
        }

        std::string msg;
        uint32_t msg_id = 0;
        std::string mac;
        MsgEncryptType enc_type = MsgEncryptType::ENC_TYPE_NONE;
        if (p->IReplyToDevMsg(mac, msg, msg_id, enc_type) != 0)
        {
            AK_LOG_WARN << "exec IReplyToDevMsg error";
            return 0;
        }

        if (mac.size() > 0 && msg.size() > 0 && msg_id > 0)
        {
            if (enc_type == MsgEncryptType::ENC_TYPE_NONE)
            {
                AK_LOG_WARN << "encrypt type is none";
                return 0;
            }

            ResidentDev dev;
            if (g_office_srv_ptr->GetDevSetting(mac, dev) < 0)
            {
                AK_LOG_WARN << "GetDeviceSetting failed. mac is " << mac;
                return 0;
            }
            FactoryReplyDevMsg(dev, msg, msg_id, enc_type);
        }

    }
    catch (MyException& e)
    {
        AK_LOG_WARN << "MyException " << e.what();
        return 0;
    }
    catch (std::exception& e)
    {
        //其他的错误
        AK_LOG_WARN << "MyException1 " << e.what();
        return 0;
    }

    return 0;

}

/*
程序启动时候自动注册到这里的类，是属于工具类，后续会为每个消息都重新new一个对象来处理
*/
void RegRouteFunc(IRouteBasePtr& f, uint32_t msgid)
{
    RouteFactory::GetInstance()->AddRouteFunc(f, msgid);
}

void RegRouteNewOfficeFunc(IRouteBasePtr& f, uint32_t msgid)
{
    RouteFactory::GetInstance()->AddNewOfficeRouteFunc(f, msgid);
}





