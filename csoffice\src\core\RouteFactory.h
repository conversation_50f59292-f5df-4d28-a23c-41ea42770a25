#ifndef _ROUTE_FACTORY_H_
#define _ROUTE_FACTORY_H_
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <cstdint>
#include "RouteBase.h"
#include "MsgStruct.h"
#include "AkcsPduBase.h"
#include "MsgIdToMsgName.h"
typedef std::map<uint32_t, IRouteBasePtr> RouteFuncList;
typedef RouteFuncList::iterator RouteFuncListIter;
class RouteFactory
{
public:
    RouteFactory() = default;
    void AddRouteFunc(IRouteBasePtr &ptr, uint32_t msgid);
    void AddNewOfficeRouteFunc(IRouteBasePtr &ptr, uint32_t msgid);
    int DispatchMsg(uint32_t message_id, const std::unique_ptr<CAkcsPdu>& pdu);
    static RouteFactory* GetInstance();
private:
    RouteFuncList funcs_;
    RouteFuncList newoffice_funcs_;
};


void RegRouteFunc(IRouteBasePtr &f, uint32_t msgid);
void RegRouteNewOfficeFunc(IRouteBasePtr &f, uint32_t msgid);
#endif