#ifndef _DEVICE_REQUEST_OPENDOOR_H_
#define _DEVICE_REQUEST_OPENDOOR_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"
#include "AgentBase.h"
#include "AkcsWebMsgSt.h"

class DeviceRequestOpenDoor : public IBase
{
public:
    DeviceRequestOpenDoor(){}
    ~DeviceRequestOpenDoor() = default;

    int IControl() override;
    int IParseXml(char *msg) override;
    int IBuildReplyMsg(std::string& msg, uint16_t& msg_id) override;
    IBasePtr NewInstance() {return std::make_shared<DeviceRequestOpenDoor>();}

    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    // 检查开门权限
    bool IfCanDevOpenDoor(const ResidentDev& src_dev, const ResidentDev& target_dev);

    ResidentDev conn_dev_;
    ResidentDev target_dev_;
    std::string func_name_ = "DeviceRequestOpenDoor";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_REQUEST_OPEN dev_request_open_;
};

#endif //_DEVICE_REQUEST_OPENDOOR_H_
