#include "MsgParse.h"
#include "MsgBuild.h"
#include "MsgControl.h"
#include "ReportActLog.h"
#include "Office2RouteMsg.h"
#include "dbinterface/Account.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "NotifyDoorOpenMsg.h"
#include "NotifyDoorOpenMsgNewOffice.h"
#include "rbac/rbac_rpc_client.h"
#include "dbinterface/ProjectUserManage.h"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern RbacRpcClient* g_rbac_client_ptr;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportActLog>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS);
};

int ReportActLog::IParseXml(char* msg)
{
    conn_dev_ = GetDevicesClient();

    if (0 != CMsgParseHandle::ParseReportActMsg(msg, &act_msg_))
    {
        AK_LOG_WARN << "ParseReportActMsg fail, mac = " << conn_dev_.mac;
        return -1;
    }

    if (strlen(act_msg_.initiator) == 0)
    {
        AK_LOG_WARN << "ReportActLog parameter error, Initiator is null, mac = " << conn_dev_.mac;
        return -1;
    }

    if (RecordActLog::GetInstance().RewriteProjectInfo(act_msg_, conn_dev_) != 0 ) 
    {
        AK_LOG_WARN << "RewriteProjectInfo error mac:" << conn_dev_.mac;
        return -1;
    }
    
    if (strlen(act_msg_.pic_name) == 0)
    {
        Snprintf(act_msg_.pic_name, sizeof(act_msg_.pic_name), dbinterface::PersonalCapture::GetRandomPicName(conn_dev_.mac, dbinterface::ProjectUserManage::GetServerTag()).c_str());
    }

    act_msg_.grade = conn_dev_.grade;
    act_msg_.unit_id = conn_dev_.unit_id;
    act_msg_.is_public = conn_dev_.is_public;  // 是否为公共设备
    act_msg_.mng_type = conn_dev_.is_personal; // 是否为单住户设备

    Snprintf(act_msg_.unit_uuid, sizeof(act_msg_.unit_uuid), conn_dev_.unit_uuid);
    Snprintf(act_msg_.mac, sizeof(act_msg_.mac), conn_dev_.mac);
    Snprintf(act_msg_.key, sizeof(act_msg_.key), act_msg_.initiator);
    Snprintf(act_msg_.location, sizeof(act_msg_.location), conn_dev_.location);
    Snprintf(act_msg_.sip_account, sizeof(act_msg_.sip_account), conn_dev_.sip);

    //个人查询时候 是根据mac或node属于它的过滤。那么对于公共设备的开门，应该记录对应开门人的node, 而对于物业mac就可以过滤出记录
    Snprintf(act_msg_.account, sizeof(act_msg_.account), conn_dev_.node);
    Snprintf(act_msg_.dev_uuid, sizeof(act_msg_.dev_uuid), conn_dev_.uuid);

    act_msg_.is_attendance = conn_dev_.is_attendance; // 是否考勤设备

    std::string door_name_list = dbinterface::DevicesDoorList::GetReportActLogDoorNameList(conn_dev_.uuid, act_msg_.relay, act_msg_.srelay);
    Snprintf(act_msg_.door_name_list, sizeof(act_msg_.door_name_list), door_name_list.c_str());
    
    AK_LOG_INFO << "office device report activity logs, mac = " << act_msg_.mac << ", act type = " << act_msg_.act_type
                << ", initiator:" << act_msg_.initiator << ", project_uuid = " << act_msg_.project_uuid2;
    return 0;
}

int ReportActLog::IControl()
{
    OfficeInfo office_info(act_msg_.mng_id);
    if (office_info.IsNew())
    {
        CNewOfficeDoorOpenMsg async_msg(act_msg_, conn_dev_);
        GetDoorOpenMsgProcessInstance()->AddNewOfficeDoorOpenMsg(async_msg);
    }
    else
    {
        CDoorOpenMsg async_msg(act_msg_, conn_dev_);
        GetDoorOpenMsgProcessInstance()->AddDoorOpenMsg(async_msg);
    }

    return 0;
}

int ReportActLog::IBuildReplyMsg(std::string& msg, uint16_t& msg_id)
{
    msg_id = MSG_TO_DEVICE_ACK;
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS, act_msg_.msg_seq, msg);
    return 0;
}
