#ifndef __REPORT_ACT_LOG_H__
#define __REPORT_ACT_LOG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "Office2RouteMsg.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficeCompanyDoorList.h"

class ReportActLog : public IBase
{
public:
    ReportActLog() {}
    ~ReportActLog() = default;

    int IParseXml(char* msg) override;
    int IControl() override;
    int IBuildReplyMsg(std::string& msg, uint16_t& msg_id) override;

    IBasePtr NewInstance() { return std::make_shared<ReportActLog>(); }
    std::string FuncName() { return func_name_; }
    MsgEncryptType EncType() { return enc_type_; }

public:
    std::string func_name_ = "ReportActLog";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    ResidentDev conn_dev_;
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg_;

};

#endif
