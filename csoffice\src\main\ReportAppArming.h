#ifndef _REPORT_APP_ARMING_H_
#define _REPORT_APP_ARMING_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonDef.h"
class ReportAppArming: public IBase
{
public:
    ReportAppArming(){
    }
    ~ReportAppArming() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportAppArming>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportAppArming";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_ENCRYPT;//默认加密方式
    SOCKET_MSG_DEV_ARMING arming_msg_;
    ResidentPerAccount conn_account_;
};

#endif //_REPORT_APP_ARMING_H_