#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>

#include "AKCSDao.h"
#include "OfficeDb.h"
#include "MsgBuild.h"
#include "MsgParse.h"
#include "MsgControl.h"
#include "OfficeServer.h"
#include "BackendFactory.h"
#include "ReportCallCapture.h"
#include "ProjectUserManage.h"

#include "dbinterface/Sip.h"
#include "dbinterface/Account.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "doorlog/RecordActLog.h"


extern LOG_DELIVERY gstAKCSLogDelivery;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportCallCapture>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_CALL_CAPTURE);
};

int ReportCallCapture::IParseXml(char* msg)
{
    memset(&call_capture_msg_, 0, sizeof(call_capture_msg_));
    return CMsgParseHandle::ParseReportCallCaptureMsg(msg, (void*)&call_capture_msg_);
}

int ReportCallCapture::IControl()
{
    // 被叫不记录,门口机不会上报; 视频存储会上报
    if(call_capture_msg_.dialog_out == 0 && strlen(call_capture_msg_.video_record_name) == 0){
        return 0;
    }
    // 被叫任何信息都不上报默认不符合格式则不记录（与社区保持一致）
    // if(strlen(call_capture_msg_.callee) == 0 && strlen(call_capture_msg_.department_uuid) == 0){
    //     return 0;
    // }

    ResidentDev dev = GetDevicesClient();
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;

    if (RecordActLog::GetInstance().RewriteProjectInfo(act_msg, dev) != 0 ) 
    {
        AK_LOG_WARN << "RewriteProjectInfo error mac:" << dev.mac;
        return -1;
    }

    act_msg.unit_id = dev.unit_id;
    act_msg.is_public = dev.is_public;
    act_msg.mng_type = dev.is_personal;
    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CALL_CAPTURE;

    Snprintf(act_msg.key, sizeof(act_msg.key), "--");
    Snprintf(act_msg.mac, sizeof(act_msg.mac), dev.mac);
    Snprintf(act_msg.dev_uuid, sizeof(act_msg.dev_uuid), dev.uuid);
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");
    Snprintf(act_msg.account, sizeof(act_msg.account), dev.node);               //node
    Snprintf(act_msg.location, sizeof(act_msg.location), dev.location);
    Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account), dev.sip);
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), "Call");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");  //可能含有特殊字符
    Snprintf(act_msg.pic_name, sizeof(act_msg.pic_name), call_capture_msg_.picture_name);

    std::string stName;
    std::string company_uuid;
    std::string node = dev.node;
    std::string callee = call_capture_msg_.callee;
    std::string department_uuid = call_capture_msg_.department_uuid;
    AK_LOG_INFO << "device " << dev.mac << " report call capture. dialout: "<< call_capture_msg_.dialog_out
                << ", caller:" << call_capture_msg_.caller << ", callee:" << callee
                << ", department:" << call_capture_msg_.department_uuid;


    MacInfo mac_info;
    GetMacInfo(mac_info);

    if (callee.length() > 0)
    {
        // 呼叫人，通过sip查找信息
        GetSipInfo(callee, node, stName, company_uuid);
    }
    else if (mac_info.is_new_office && department_uuid.length() > 0)
    {
        // 呼叫OfficeGroup 
        OfficeGroupInfo office_group_info;
        if (dbinterface::OfficeGroup::GetOfficeGroupByUUID(department_uuid, office_group_info) == 0)
        {
            company_uuid = office_group_info.office_company_uuid;
        }
    }

    if (node.empty() && company_uuid.empty())
    {
        AK_LOG_WARN << "there is inlegal call: " << ", node=" << node << ", company_uuid=" << company_uuid;
    }

    Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
    Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), company_uuid.c_str());
    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal call capture failed.";
        return -1;
    }

    return 0;
}

int ReportCallCapture::IBuildReplyMsg(std::string& msg, uint16_t& msg_id)
{
    return 0;
}

int ReportCallCapture::IPushNotify()
{
    return 0;
}

int ReportCallCapture::IToRouteMsg()
{
    return 0;
}



int ReportCallCapture::GetSipInfo(const std::string& sip, std::string& node, std::string& stName, std::string& company_uuid)
{
    node = "";
    stName = "";
    company_uuid = "";
    
    std::string device_uuid;
    std::string personal_account_uuid;
    int role;
    ResidentDev dev = GetDevicesClient();

    MacInfo mac_info;
    GetMacInfo(mac_info);

    //设备主叫
    if (sip.length() > 0)
    {
        //先检查群组
        node = dbinterface::Sip::GetNodeByGroupFromSip2(sip);
        if(!node.empty()){
            OfficeAccount office_account;
            if (dbinterface::OfficePersonalAccount::GetUidAccount(node, office_account) == 0)
            {
                stName = office_account.name;
                node = office_account.account;
                personal_account_uuid = office_account.uuid;
                role = office_account.role;
            }
        }

        // 查找人
        OfficeAccount office_account;
        if (node.empty())
        {
            if (dbinterface::OfficePersonalAccount::GetUidAccount(sip, office_account) == 0)
            {
                stName = office_account.name;
                node = office_account.account;
                personal_account_uuid = office_account.uuid;
                role = office_account.role;
            }
        }

        // 查找设备
        if (node.empty())
        {
            ResidentDev office_dev;
            if (dbinterface::ResidentDevices::GetDevicesBySip(sip, office_dev) == 0)
            {
                node = office_dev.node;
                stName = office_dev.location;
                // 公司只会和公共设备有关联关系，个人设备时只有和人的关系
                if (dbinterface::OfficePersonalAccount::GetUidAccount(node, office_account) == 0)
                {
                    personal_account_uuid = office_account.uuid;
                    role = office_account.role;
                }
            }
        }

        if (node.empty())
        {
            //找手机号码
            OfficeAccount office_account;
            if (dbinterface::OfficePersonalAccount::GetPhoneAccountForOfficeid(sip, dev.project_mng_id, office_account) == 0)
            {
                stName = office_account.name;
                node = office_account.account;
                personal_account_uuid = office_account.uuid;
                role = office_account.role;
            }
        }
    }

    if (node.empty())
    {
        AK_LOG_WARN << "there is inlegal call: " << ", sip=" << sip;
        return -1;
    }

    // 新办公需要区分公司
    if (mac_info.is_new_office)
    {
        if (personal_account_uuid.length() > 0)
        {
            company_uuid = DaoGetCompanyUUIDByUser(role, personal_account_uuid);
        }
    }

    return 0;
}


