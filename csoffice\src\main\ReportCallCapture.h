#ifndef REQ_REPORT_CALL_CAPTURE_H_
#define REQ_REPORT_CALL_CAPTURE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReportCallCapture: public IBase
{
public:
    SOCKET_MSG_CALL_CAPTURE call_capture_msg_;

    std::string func_name_ = "ReportCallCapture";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

public:
    ReportCallCapture(){}
    ~ReportCallCapture() = default;


    int IParseXml(char *msg);
    int IControl();
    int IBuildReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushNotify();
    int IToRouteMsg();

    IBasePtr NewInstance() {return std::make_shared<ReportCallCapture>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    
private:
    int GetSipInfo(const std::string& sip, std::string& node, std::string& stName, std::string& company_uuid);
};

#endif
