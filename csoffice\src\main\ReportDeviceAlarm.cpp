#include "util_virtual_door.h"
#include "ReportDeviceAlarm.h"
#include "OfficeInit.h"
#include "Office2RouteMsg.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;
static const char emergency_auto_control_initiator[] = "--";

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportDeviceAlarm>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_ALARM);
};

int ReportDeviceAlarm::IParseXml(char* msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != CMsgParseHandle::ParseAlarmMsg(msg, &report_alarm_msg_))
    {
        AK_LOG_WARN << "ReportDeviceAlarm parse alarm msg failed";
        return -1;
    }

    AK_LOG_INFO << "ReportDeviceAlarm handle parse alarm msg, mac = " << conn_dev_.mac
        << ", alarm code = " << report_alarm_msg_.alarm_code
        << ", location = " << report_alarm_msg_.alarm_location
        << ", customize = " << report_alarm_msg_.alarm_customize
        << ", input = " << report_alarm_msg_.input;
    return 0;
}

int ReportDeviceAlarm::IControl()
{
    dbinterface::ResidentDevices::GetMacDev(conn_dev_.mac, conn_dev_);

    belonged_company_set_.clear();
    office_info_ = OfficeInfo(conn_dev_.project_mng_id);

    // Alarm插入数据库
    AlarmMsgRecord();

    // 根据Alarm类型进行消息分发
    AlarmMsgNotify();

    return 0;
}

void ReportDeviceAlarm::AlarmMsgRecord()
{
    ALARM alarm;
    memset(&alarm, 0, sizeof(alarm));

    // 记录到数据库的alarm结构体赋值
    alarm.unit_id = conn_dev_.unit_id;
    alarm.status = ALARM_STATUS_UNDEALED;
    alarm.manager_account_id = conn_dev_.project_mng_id;
    alarm.alarm_code = report_alarm_msg_.alarm_code;
    alarm.alarm_zone = report_alarm_msg_.alarm_zone;
    alarm.alarm_location = report_alarm_msg_.alarm_location;
    Snprintf(alarm.mac, sizeof(alarm.mac), conn_dev_.mac);
    Snprintf(alarm.device_node, sizeof(alarm.device_node), conn_dev_.node);
    Snprintf(alarm.device_uuid, sizeof(alarm.device_uuid), conn_dev_.uuid);
    Snprintf(alarm.alarm_type, sizeof(alarm.alarm_type), report_alarm_msg_.type);
    alarm.trace_id = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    alarm.alarm_customize = alarm.alarm_code == 0 ? 1 : report_alarm_msg_.alarm_customize;
    Snprintf(alarm.alarm_time, sizeof(alarm.alarm_time), GetNodeNowDateTimeByTimeZoneStr(office_info_.TimeZone(), g_time_zone_DST).c_str());

    // 记录到数据库
    dbinterface::Alarm::AddAlarm(&alarm, dbinterface::ProjectUserManage::GetServerTag());

    report_alarm_msg_.alarm_id = alarm.id;
    Snprintf(report_alarm_msg_.alarm_uuid, sizeof(report_alarm_msg_.alarm_uuid), alarm.uuid);

    // 通知alarm结构体赋值
    send_alarm_msg_.id = alarm.id;
    send_alarm_msg_.grade = conn_dev_.grade;
    send_alarm_msg_.trace_id = alarm.trace_id;
    send_alarm_msg_.unit_id = conn_dev_.unit_id;
    send_alarm_msg_.alarm_code = alarm.alarm_code;
    send_alarm_msg_.alarm_zone = alarm.alarm_zone;
    send_alarm_msg_.device_type = conn_dev_.dev_type;
    send_alarm_msg_.manager_account_id = conn_dev_.project_mng_id;
    send_alarm_msg_.alarm_location = alarm.alarm_location;
    send_alarm_msg_.alarm_customize = alarm.alarm_customize;
    Snprintf(send_alarm_msg_.mac, sizeof(send_alarm_msg_.mac), conn_dev_.mac);
    Snprintf(send_alarm_msg_.time, sizeof(send_alarm_msg_.time), alarm.alarm_time);
    Snprintf(send_alarm_msg_.address, sizeof(send_alarm_msg_.address), conn_dev_.node);
    Snprintf(send_alarm_msg_.type, sizeof(send_alarm_msg_.type), report_alarm_msg_.type);
    Snprintf(send_alarm_msg_.protocal, sizeof(send_alarm_msg_.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(send_alarm_msg_.from_local, sizeof(send_alarm_msg_.from_local), conn_dev_.location);
    Snprintf(send_alarm_msg_.msg_seq, sizeof(send_alarm_msg_.msg_seq), report_alarm_msg_.msg_seq);

    return;
}


bool ReportDeviceAlarm::IsDoorHeldOpenAlarm()
{
    if (report_alarm_msg_.alarm_code == (int)ALARM_CODE::DOOR_HELD_OPEN && strlen(report_alarm_msg_.input) > 0)
    {
        return true;
    }

    return false;
}

void ReportDeviceAlarm::AlarmMsgNotify()
{
    // 旧办公限制personal下设备的alarm才进行处理
    if (conn_dev_.grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        AK_LOG_INFO << "OldOfficeAlarmNotify, not apt device, ignore this alarm, mac = " << conn_dev_.mac;
        return;
    }

    // 通知管理机
    NotifyMngDev();

    // 通知APT
    NotifyApt();
}


void ReportDeviceAlarm::NotifyMngDev()
{
    OfficeDevList mng_dev_list;
    dbinterface::OfficeDevices::GetAllMngDevList(conn_dev_.project_mng_id, mng_dev_list);

    for (const auto& mng_dev : mng_dev_list)
    {
        if (mng_dev->status == 0)
        {
            AK_LOG_INFO << "OldOfficeNotifyMngDev continue, dev offline, mac = " << mng_dev->mac;
            continue;
        }

        // 发给有相关的管理中心机: 同一栋 或者 最外层的
        if (!(mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || (mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && mng_dev->unit_id == conn_dev_.unit_id)))
        {
            AK_LOG_INFO << "OldOfficeNotifyMngDev continue, not correct grade, mac = " << mng_dev->mac;
            continue;
        }

        // 最外围的设备需要管理室内机所在的楼栋
        if (mng_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC && !dbinterface::PubDevMngList::IsManageBuildingByMac(mng_dev->mac, conn_dev_.unit_id))
        {
            AK_LOG_INFO << "OldOfficeNotifyMngDev continue, public dev not manage this building, mac = " << mng_dev->mac;
            continue;
        }

        // 通知管理机
        COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::OFFICE, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::OFFICE_DEV, mng_dev->mac, send_alarm_msg_);
    }

    return;
}

void ReportDeviceAlarm::NotifyApt()
{
    // 通知设备
    OfficeDevList dev_list;
    dbinterface::OfficeDevices::GetNodeDevList(conn_dev_.node, dev_list);

    for (const auto& dev : dev_list)
    {
        if (dev->status == 0)
        {
            AK_LOG_INFO << "OldOffice AlarmNotify Device, dev is offline, mac = " << dev->mac;
            continue;
        }

        if (dev->dev_type == DEVICE_TYPE_MANAGEMENT || dev->dev_type == DEVICE_TYPE_INDOOR)
        {
            COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::OFFICE, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::OFFICE_DEV, dev->mac, send_alarm_msg_);
            AK_LOG_INFO << "OldOffice AlarmNotify Device, mac = " << dev->mac;
        }
    }

    // 通知app
    OfficeAccount per_account;
    if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(conn_dev_.node, per_account))
    {
        // 校验实际站点账号是否为多套房账户且状态异常
        if (dbinterface::ProjectUserManage::MultiSiteLimit(per_account.account))
        {
            AK_LOG_INFO << "OldOffice AlarmNotify App, MultiSiteLimit stop send mag to app, account = " << conn_dev_.node;
            return;
        }

        COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::OFFICE, TransP2PMsgType::TO_APP_UID, csmain::DeviceType::OFFICE_APP, per_account.account, send_alarm_msg_);
        AK_LOG_INFO << "OldOffice AlarmNotify App, account = " << conn_dev_.node;
    }

    return;
}

int ReportDeviceAlarm::IBuildReplyMsg(std::string& msg, uint16_t& msg_id)
{
    msg_id = MSG_TO_DEVICE_NOTIFY_ALARM_ACK;
    GetMsgBuildHandleInstance()->BuildAlarmAckMsg(report_alarm_msg_, msg);
    return 0;
}

