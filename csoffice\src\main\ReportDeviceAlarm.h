#ifndef _REPORT_DEVICE_ALARM_H_
#define _REPORT_DEVICE_ALARM_H_

#include <set>
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "NotifyHttpReq.h"
#include "SnowFlakeGid.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/AlexaToken.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"


class ReportDeviceAlarm: public IBase
{
public:
    ReportDeviceAlarm(){
    }
    ~ReportDeviceAlarm() = default;

    int IParseXml(char *msg);
    
    int IControl();
    
    int IBuildReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportDeviceAlarm>();}
    
    std::string FuncName() {return func_name_;}
    
    MsgEncryptType EncType() {return enc_type_;}

private:
    void AlarmMsgRecord();
    void AlarmMsgNotify();

    void NotifyMngDev();
    void NotifyApt();

    bool IsDoorHeldOpenAlarm();
    bool NeedRecordToDB(const ALARM& alarm);

private:
    std::string func_name_ = "ReportDeviceAlarm";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;

    ResidentDev conn_dev_;
    
    OfficeInfo office_info_;
    
    SOCKET_MSG_ALARM report_alarm_msg_;
    
    SOCKET_MSG_ALARM_SEND send_alarm_msg_;
        
    std::set<std::string> belonged_company_set_;     // 新办公一个设备可以属于多个公司，这里记录归属的公司列表
};

#endif
