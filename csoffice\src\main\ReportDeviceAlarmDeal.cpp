#include <string>
#include "ReportDeviceAlarmDeal.h"

#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "json/json.h"
#include "util.h"
#include "DclientMsgSt.h"
#include "Singleton.h"
#include "NotifyHttpReq.h"
#include "CommunityInfo.h"
#include "DclientMsgDef.h"
#include "OfficeInit.h"
#include "OfficeInfo.h"
#include "MsgBuild.h"
#include "SnowFlakeGid.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "ProjectUserManage.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/AlexaToken.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "../control/AlarmDealNotifyMsgNewOffice.h"
#include "../control/AlarmDealNotifyMsg.h"


extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportDeviceAlarmDeal>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_PUT_ALARM_DEAL);
};

int ReportDeviceAlarmDeal::IParseXml(char* msg)
{
    if (msg == nullptr)
    {
        AK_LOG_WARN << "msg is null.";
        return -1;
    }

    if (CMsgParseHandle::ParseAlarmDealMsg(msg, &alarm_deal_info_) < 0)
    {
        AK_LOG_WARN << "Parse AlarmDealMsg failed.";
        return -1;
    }

    AK_LOG_INFO << "ReportDeviceAlarmDeal handle parse alarm msg: alarm_id=" << alarm_deal_info_.alarm_id
        << ", alarm_code=" << alarm_deal_info_.alarm_code
        << ", user=" << alarm_deal_info_.user
        << ", type=" << alarm_deal_info_.type
        << ", alarm_location=" << alarm_deal_info_.alarm_location;

    return 0;
}

int ReportDeviceAlarmDeal::IControl()
{
    // Alexa推送
    PostAlexaChangeStatus();

    // 告警处理
    ProcessAlarmDealMsg();
    return 0;
}

void ReportDeviceAlarmDeal::PostAlexaChangeStatus()
{
    ResidentDev dev = GetDevicesClient();
    if (strlen(dev.node) == 0)
    {
        return;
    }

    OfficeAccount account_info;
    memset(&account_info, 0, sizeof(account_info));
    if (0 != dbinterface::OfficePersonalAccount::GetUidAccount(dev.node, account_info))
    {
        AK_LOG_WARN << "find account by node failed. node=" << dev.node;
        return;
    }

    // 推送alarm状态给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(account_info.uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatusHttpReq(dev.mac, traceid);
        AK_LOG_INFO << "alexa device alarm notify web , mac :" << dev.mac << ", traceid : " << traceid;
    }
}

void ReportDeviceAlarmDeal::PostAlexaChangeStatusHttpReq(const std::string& mac, uint64_t traceid)
{
    std::string data;
    Json::Value item;
    Json::FastWriter fast_writer;

    item["MAC"] = mac;
    item["TraceId"] = std::to_string(traceid);
    data = fast_writer.write(item);

    char url[128];
    snprintf(url, sizeof(url), "http://%s/alexaInner/v1/device/changeStatus", gstAKCSConf.smg_alexa_addr);

    CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

int  ReportDeviceAlarmDeal::ProcessAlarmDealMsg()
{
    // 获取告警信息
    ALARM alarm_info;
    (void)memset(&alarm_info, 0, sizeof(alarm_info));
    int alarm_id = atoi(alarm_deal_info_.alarm_id);
    if (alarm_id <= 0 || dbinterface::Alarm::GetAlarm(alarm_id, &alarm_info) != 0)
    {
        AK_LOG_WARN << "Get alarm info failed: alarm_id=" << alarm_id;
        return -1;
    }

    if (alarm_info.alarm_code == 0)
    {
        alarm_info.alarm_customize = 1; //自定义类型
    }

    // 获取项目信息
    OfficeInfo office_info = OfficeInfo(alarm_info.manager_account_id);
    if (!office_info.InitSuccess())
    {
        AK_LOG_WARN << "Get Office ID error ID=" << alarm_info.manager_account_id;
        return -1;
    }

    // 处理告警状态
    alarm_info.status = 1;
    Snprintf(alarm_info.deal_user, sizeof(alarm_info.deal_user), alarm_deal_info_.user);
    Snprintf(alarm_info.deal_result, sizeof(alarm_info.deal_result), alarm_deal_info_.result);
    if (dbinterface::Alarm::DealAlarm(&alarm_info) != 0)
    {
        AK_LOG_WARN << "DealAlarmStatus failed: alarm_id=" << alarm_id;
        return -1;
    }

    std::string node = alarm_deal_info_.area_node;
    if (node.empty())
    {
        node = alarm_info.device_node;
    }

    // 构造告警状态
    AK::Server::P2PAlarmDealNotifyMsg alarm_deal_notify_msg;
    alarm_deal_notify_msg.set_area_node(node.c_str());
    alarm_deal_notify_msg.set_user(alarm_deal_info_.user);
    alarm_deal_notify_msg.set_alarm_id(alarm_deal_info_.alarm_id);
    alarm_deal_notify_msg.set_result(alarm_deal_info_.result);
    alarm_deal_notify_msg.set_alarm_time(alarm_deal_info_.time);
    alarm_deal_notify_msg.set_target_type(0);
    alarm_deal_notify_msg.set_target("");

    if (office_info.IsNew())
    {
        // 新办公处理
        new_office::ProcessAlarmDealNotify(office_info, alarm_info, alarm_deal_notify_msg);
    }
    else
    {
        // 旧办公处理
        old_office::ProcessAlarmDealNotify(office_info, alarm_info, alarm_deal_notify_msg);
    }

    return 0;
}
