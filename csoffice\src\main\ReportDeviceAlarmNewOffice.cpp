#include "OfficeInit.h"
#include "Office2RouteMsg.h"
#include "util_virtual_door.h"
#include "ReportDeviceAlarmNewOffice.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/new-office/OfficeEmergencyDoor.h"
#include "dbinterface/new-office/OfficeCompanyDoorList.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;
static const char emergency_auto_control_initiator[] = "--";

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportDeviceAlarmNewOffice>();
    RegNewOfficeDevFunc(p, MSG_FROM_DEVICE_ALARM);
};

int ReportDeviceAlarmNewOffice::IParseXml(char* msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != CMsgParseHandle::ParseAlarmMsg(msg, &report_alarm_msg_))
    {
        AK_LOG_WARN << "ReportDeviceAlarm parse alarm msg failed";
        return -1;
    }

    AK_LOG_INFO << "ReportDeviceAlarmNewOffice handle parse alarm msg, mac = " << conn_dev_.mac
        << ", conn_node = " << conn_dev_.node
        << ", alarm code = " << report_alarm_msg_.alarm_code
        << ", location = " << report_alarm_msg_.alarm_location
        << ", customize = " << report_alarm_msg_.alarm_customize
        << ", input = " << report_alarm_msg_.input;
    return 0;
}

int ReportDeviceAlarmNewOffice::IControl()
{
    dbinterface::ResidentDevices::GetMacDev(conn_dev_.mac, conn_dev_);

    notify_company_set_.clear();
    memset(&alarm_, 0, sizeof(alarm_));
    office_info_ = OfficeInfo(conn_dev_.project_mng_id);

    if (NeedToProcess(report_alarm_msg_.alarm_code))
    {
        // Alarm插入数据库
        AlarmMsgRecord();

        // 根据Alarm类型进行消息分发
        AlarmNotify();
    }

    return 0;
}

bool ReportDeviceAlarmNewOffice::NeedToProcess(int alarm_code)
{
    // 强拆告警，所有门都过期了，不通知任何公司
    if (alarm_code == (int)ALARM_CODE::TAMPER)
    {
        if (dbinterface::DevicesDoorList::IsAllDoorExpired(conn_dev_.uuid))
        {
            AK_LOG_INFO << "All door expired, not need process, mac = " << conn_dev_.mac << ", alarm code = " << alarm_code;
            return false;
        }
    }
    // 强闯和门超时未关 使用Input上报，转为RelayNum失败，说明可能是过期了，不再处理
    else if (alarm_code == (int)ALARM_CODE::BREAK_IN || alarm_code == (int)ALARM_CODE::DOOR_HELD_OPEN)
    {
        relay_name_ = "";
        if (DoorInputToActiveRelay(alarm_.relay_type, alarm_.relay_num, relay_name_) == false)
        {
            AK_LOG_WARN << "DoorInputToActiveRelay failed, mac = " << conn_dev_.mac << ", alarm code = " << alarm_code;
            return false;
        }
    }
    else{
        // 转换RelayNum
        DoorInputToActiveRelay(alarm_.relay_type, alarm_.relay_num, relay_name_);
    }

    return true;
}

void ReportDeviceAlarmNewOffice::AlarmMsgRecord()
{
    // 记录到数据库的alarm结构体赋值
    alarm_.unit_id = conn_dev_.unit_id;
    alarm_.status = ALARM_STATUS_UNDEALED;
    alarm_.manager_account_id = conn_dev_.project_mng_id;
    alarm_.alarm_code = report_alarm_msg_.alarm_code;
    alarm_.alarm_zone = report_alarm_msg_.alarm_zone;
    alarm_.alarm_location = report_alarm_msg_.alarm_location;
    Snprintf(alarm_.mac, sizeof(alarm_.mac), conn_dev_.mac);
    Snprintf(alarm_.device_node, sizeof(alarm_.device_node), conn_dev_.node);
    Snprintf(alarm_.device_uuid, sizeof(alarm_.device_uuid), conn_dev_.uuid);
    Snprintf(alarm_.alarm_type, sizeof(alarm_.alarm_type), report_alarm_msg_.type);
    alarm_.trace_id = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    alarm_.alarm_customize = alarm_.alarm_code == 0 ? 1 : report_alarm_msg_.alarm_customize;
    Snprintf(alarm_.alarm_time, sizeof(alarm_.alarm_time), GetNodeNowDateTimeByTimeZoneStr(office_info_.TimeZone(), g_time_zone_DST).c_str());

    // 获取company信息
    GetNewOfficeInfo(alarm_);

    // 记录到数据库
    if (NeedRecordToDB(alarm_))
    {
        if (notify_company_set_.empty())
        {
            dbinterface::Alarm::AddAlarm(&alarm_, dbinterface::ProjectUserManage::GetServerTag());
        }
        else
        {
            for (auto iter = notify_company_set_.begin(); iter != notify_company_set_.end(); iter++)
            {
                Snprintf(alarm_.company_uuid, sizeof(alarm_.company_uuid), iter->c_str());
                dbinterface::Alarm::AddAlarm(&alarm_, dbinterface::ProjectUserManage::GetServerTag());
            }
        }
    }

    report_alarm_msg_.alarm_id = alarm_.id;
    Snprintf(report_alarm_msg_.alarm_uuid, sizeof(report_alarm_msg_.alarm_uuid), alarm_.uuid);

    // 通知alarm结构体赋值
    send_alarm_msg_.id = alarm_.id;
    send_alarm_msg_.grade = conn_dev_.grade;
    send_alarm_msg_.trace_id = alarm_.trace_id;
    send_alarm_msg_.unit_id = conn_dev_.unit_id;
    send_alarm_msg_.alarm_code = alarm_.alarm_code;
    send_alarm_msg_.alarm_zone = alarm_.alarm_zone;
    send_alarm_msg_.device_type = conn_dev_.dev_type;
    send_alarm_msg_.manager_account_id = conn_dev_.project_mng_id;
    send_alarm_msg_.alarm_location = alarm_.alarm_location;
    send_alarm_msg_.alarm_customize = alarm_.alarm_customize;
    Snprintf(send_alarm_msg_.mac, sizeof(send_alarm_msg_.mac), conn_dev_.mac);
    Snprintf(send_alarm_msg_.time, sizeof(send_alarm_msg_.time), alarm_.alarm_time);
    Snprintf(send_alarm_msg_.address, sizeof(send_alarm_msg_.address), conn_dev_.node);
    Snprintf(send_alarm_msg_.type, sizeof(send_alarm_msg_.type), report_alarm_msg_.type);
    Snprintf(send_alarm_msg_.protocal, sizeof(send_alarm_msg_.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(send_alarm_msg_.from_local, sizeof(send_alarm_msg_.from_local), conn_dev_.location);
    Snprintf(send_alarm_msg_.msg_seq, sizeof(send_alarm_msg_.msg_seq), report_alarm_msg_.msg_seq);

    // BreakIn、门超时未关 告警需要拼接DoorName
    if (alarm_.alarm_code == (int)ALARM_CODE::BREAK_IN || alarm_.alarm_code == (int)ALARM_CODE::DOOR_HELD_OPEN)
    {
        std::string device_name = std::string(conn_dev_.location) + "-" + relay_name_;
        Snprintf(send_alarm_msg_.from_local, sizeof(send_alarm_msg_.from_local), device_name.c_str());
    }

    return;
}

void ReportDeviceAlarmNewOffice::GetNewOfficeInfo(ALARM& alarm_)
{
    if (conn_dev_.dev_type == DEVICE_TYPE_INDOOR)
    {
        OfficeDeviceAssignInfo office_device_assign_info;
        if (0 != dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(conn_dev_.uuid, office_device_assign_info))
        {
            return;
        }

        OfficeAccount account;
        if (0 != dbinterface::OfficePersonalAccount::GetUUIDAccount(office_device_assign_info.personal_account_uuid, account))
        {
            return;
        }

        Snprintf(alarm_.device_node, sizeof(alarm_.device_node), account.account);
        Snprintf(alarm_.company_uuid, sizeof(alarm_.company_uuid), office_device_assign_info.office_company_uuid);
        notify_company_set_.insert(office_device_assign_info.office_company_uuid);

        AK_LOG_INFO << "GetCompanyInfo mac = " << conn_dev_.mac << ", node = " << account.account
            << ", office_company_uuid = " << office_device_assign_info.office_company_uuid;
        return;
    }
    else if (akjudge::DevDoorType(conn_dev_.dev_type))
    {
        CompanyDoorList office_company_door_info_list;
        dbinterface::DevicesDoorList::GetOfficeCompanyDoorByDevicesUUID(conn_dev_.uuid, office_company_door_info_list);

        for (const auto& office_company_door_info : office_company_door_info_list)
        {
            if (alarm_.alarm_code == (int)ALARM_CODE::TAMPER)
            {
                // 防拆告警触发时，通知对 告警设备的任一Door有权限 的公司
                notify_company_set_.insert(office_company_door_info.office_company_uuid);
            }
            else if (alarm_.relay_type == RelayType::RELAY)
            {
                // 触发其他告警时，只通知对 告警设备当前Door有权限 的公司
                if (alarm_.relay_num & office_company_door_info.relay)
                {
                    Snprintf(alarm_.company_uuid, sizeof(alarm_.company_uuid), office_company_door_info.office_company_uuid);
                    notify_company_set_.insert(office_company_door_info.office_company_uuid);
                }
            }
            else if (alarm_.relay_type == RelayType::SECURITY_TYPE)
            {
                if (alarm_.relay_num & office_company_door_info.srelay)
                {
                    Snprintf(alarm_.company_uuid, sizeof(alarm_.company_uuid), office_company_door_info.office_company_uuid);
                    notify_company_set_.insert(office_company_door_info.office_company_uuid);
                }
            }
        }
    }

    return;
}

bool ReportDeviceAlarmNewOffice::DoorInputToActiveRelay(RelayType& relay_type, int& relay_num, std::string& relay_name)
{
    if (!akjudge::DevDoorType(conn_dev_.dev_type) || strlen(report_alarm_msg_.input) == 0)
    {
        relay_type = RelayType::NONE;
        relay_name = "";
        relay_num = 0;
        return false;
    }

    DevicesDoorInfoList devices_door_info_list;
    dbinterface::DevicesDoorList::GetDevicesDoorList(conn_dev_.uuid, devices_door_info_list);

    bool result = false;
    for (const auto& door_info : devices_door_info_list)
    {
        if (IsSubscribedDoor(door_info.enable, door_info.active, door_info.expire) &&
            strcmp(report_alarm_msg_.input, GetDoorStatusInput(door_info.door_status_input).c_str()) == 0)
        {
            relay_type = door_info.relay_type == DoorRelayType::RELAY ? RelayType::RELAY : RelayType::SECURITY_TYPE;
            relay_num = GetRelayIndexByControlledRelay(door_info.controlled_relay);
            relay_name = door_info.name;
            result = true;

            AK_LOG_INFO << "GetDoorOpenedRelay mac = " << conn_dev_.mac << ", sensor_alarm_input = "
                << report_alarm_msg_.input << ", relay = " << door_info.name;
            break;
        }
    }

    return result;
}

void ReportDeviceAlarmNewOffice::AlarmNotify()
{
    int alarm_code = report_alarm_msg_.alarm_code;
    if (alarm_code <= (int)ALARM_CODE::SOS)
    {
        // 布撤防：通知室内机归属人的公司
        ArmingAlarmNotify();
    }
    else if (alarm_code == (int)ALARM_CODE::EMERGENCY ||
        alarm_code == (int)ALARM_CODE::EMERGENCY_NOTIFY_OPEN ||
        alarm_code == (int)ALARM_CODE::EMERGENCY_NOTIFY_CLOSED)
    {
        // 紧急告警：通知项目下的所有人、管理机
        EmergencyAlarmNotify();
    }
    else if (alarm_code == (int)ALARM_CODE::DOOR_HELD_OPEN || alarm_code == (int)ALARM_CODE::BREAK_IN)
    {
        // 门超时未关、强闯：通知对Door有权限的所有公司
        CommonAlarmNotify();
    }
    else if (alarm_code == (int)ALARM_CODE::TAMPER)
    {
        // 防拆：通知对设备有权限的所有公司
        CommonAlarmNotify();
    }
    else
    {
        // 其他告警
        AK_LOG_INFO << "NewOffice AlarmNotify failed: alarm_code = " << alarm_code;
    }
}

// 通知项目下所有管理机
void ReportDeviceAlarmNewOffice::CommonAlarmNotifyMngDevByProjectUUID(std::string project_uuid)
{
    // 获取Company下所有管理机
    OfficeDevList device_list;
    dbinterface::OfficeDevices::GetAllMngDevList(project_uuid, device_list);
    AK_LOG_INFO << "CommonAlarmNotifyMngDevByProjectUUID, project_uuid = " << project_uuid << ", device_list size = " << device_list.size();

    for (const auto& mng_dev : device_list)
    {
        // 获取管理机设备信息
        ResidentDev dev;
        if (dbinterface::ResidentDevices::GetUUIDDev(mng_dev->uuid, dev) != 0)
        {
            AK_LOG_WARN << "NewOfficeCommonAlarmNotifyMngDev, get dev by uuid failed, uuid = " << mng_dev->uuid;
            continue;
        }

        // 通知管理机
        COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::OFFICE, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::OFFICE_DEV, dev.mac, send_alarm_msg_);
    }

    return;
}


// 通知管理机
void ReportDeviceAlarmNewOffice::CommonAlarmNotifyMngDev(std::string company_uuid, std::set<std::string>& notifyed_dev_set)
{
    // 获取Company下所有管理机
    OfficeDevList device_list;
    dbinterface::OfficeDevices::GetAllMngDevListByCompanyUUID(company_uuid, device_list);

    for (const auto& mng_dev : device_list)
    {
        // 每个管理机只通知一次
        if (notifyed_dev_set.insert(mng_dev->uuid).second == false)
        {
            continue;
        }

        // 获取管理机设备信息
        ResidentDev dev;
        if (dbinterface::ResidentDevices::GetUUIDDev(mng_dev->uuid, dev) != 0)
        {
            AK_LOG_WARN << "NewOfficeCommonAlarmNotifyMngDev, get dev by uuid failed, uuid = " << mng_dev->uuid;
            continue;
        }

        // 通知管理机
        COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::OFFICE, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::OFFICE_DEV, dev.mac, send_alarm_msg_);
    }

    return;
}

void ReportDeviceAlarmNewOffice::CommonAlarmNotifyAdminApp(std::string company_uuid)
{
    // 获取Company下所有Admin APP列表
    OfficeAdminInfoList office_admin_info_list;
    dbinterface::OfficeAdmin::GetOfficeAdminInfoListByCompanyUUID(company_uuid, office_admin_info_list);

    for (const auto& admin_info : office_admin_info_list)
    {
        if (strlen(admin_info.personal_account_uuid) == 0 || admin_info.app_status == 0)
        {
            continue;
        }

        OfficeAccount per_account;
        if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(admin_info.personal_account_uuid, per_account))
        {
            // 校验实际站点账号是否为多套房账户且状态异常
            if (dbinterface::ProjectUserManage::MultiSiteLimit(per_account.account))
            {
                AK_LOG_INFO << "NewOffice AlarmNotify App, MultiSiteLimit stop send mag to app, account = " << per_account.account;
                return;
            }

            // 通知Admin APP
            COffice2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::OFFICE, TransP2PMsgType::TO_APP_UID, csmain::DeviceType::OFFICE_APP, per_account.account, send_alarm_msg_);
        }
    }
}

// 布撤防告警通知
void ReportDeviceAlarmNewOffice::ArmingAlarmNotify()
{
    if (conn_dev_.dev_type != DEVICE_TYPE_INDOOR)
    {
        return;
    }

    // 布撤防告警，只通知室内机归属人的公司
    OfficeDeviceAssignInfo office_device_assign_info;
    int ret = dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(conn_dev_.uuid, office_device_assign_info);
    if (ret == 0 && strlen(office_device_assign_info.office_company_uuid) > 0)
    {
        std::set<std::string> notifyed_dev_set;
        CommonAlarmNotifyAdminApp(office_device_assign_info.office_company_uuid);
        CommonAlarmNotifyMngDev(office_device_assign_info.office_company_uuid, notifyed_dev_set);
    }
}

void ReportDeviceAlarmNewOffice::CommonAlarmNotify()
{
    // 1、通知AdminApp（只通知把Door设置成PrivateDoor的Company）
    std::set<std::string> company_set;
    dbinterface::OfficeCompanyDoorList::GePrivateDoorCompanyByDevicesUUID(conn_dev_.uuid, company_set);

    for (auto company_uuid : company_set)
    {
        // 只有私有door需要告警
        if (company_uuid.empty() || notify_company_set_.find(company_uuid) == notify_company_set_.end())
        {
            AK_LOG_INFO << "NewOffice CommonAlarmNotify: company_uuid = " << company_uuid << " need not notify.";
            continue;
        }

        // 通知AdminAPP
        CommonAlarmNotifyAdminApp(company_uuid);
        AK_LOG_INFO << "NewOffice CommonAlarmNotify: company_uuid = " << company_uuid << " need notify.";
    }

    // 2、通知管理机（无论是否设置成PrivateDoor的Company，都通知）
    std::set<std::string> notifyed_dev_set;

    for (auto company_uuid : notify_company_set_)
    {
        // 通知管理机(去重，每个设备只通知一次)
        CommonAlarmNotifyMngDev(company_uuid, notifyed_dev_set);
    }
}

void ReportDeviceAlarmNewOffice::EmergencyAlarmNotify()
{
    if (office_info_.EnableAutoEmergency())
    {
        // 推送消息给web
        COffice2RouteMsg::SendEmergencyNotifyWebMsg(report_alarm_msg_.alarm_uuid, conn_dev_.project_uuid);

        // 通知设备开门
        EmergencyAlarmControlNofity();

        // 通知app和indoor
        EmergencyAlarmMsgNofity();
        return;
    }

    AK_LOG_INFO << "NewOfficeEmergencyAlarmNotify Office " << office_info_.Name() << " not EnableAutoEmergency";

    return;
}

void ReportDeviceAlarmNewOffice::EmergencyAlarmControlNofity()
{
    MacInfo mac_info;
    GetMacInfo(mac_info);

    std::string msg_uuid;
    //插入PmEmergencyDoorLog表
    dbinterface::PmEmergencyDoorLog::InsertPmEmergencyDoorLog(dbinterface::ProjectUserManage::GetServerTag(), mac_info.project_uuid, msg_uuid);

    if (0 != dbinterface::PmEmergencyDoorLog::InsertPmEmergencyDoorLogList(msg_uuid, mac_info.project_uuid, project::PROJECT_TYPE::OFFICE, office_info_.IsAllEmergencyDoor()))
    {
        AK_LOG_WARN << "InsertPmEmergencyDoorLogList failed. msg_uuid=" << msg_uuid << ", project_uuid=" << mac_info.project_uuid;
        return;
    }

    //获取一键开门的设备信息
    dbinterface::PmEmergencyDoorLogInfoList info_list;
    dbinterface::PmEmergencyDoorLog::GetEmergencyDoorLogListByUUID(msg_uuid, info_list);

    COffice2RouteMsg::SendP2PEmergencyDoorControlMsg(info_list, msg_uuid, emergency_auto_control_initiator, ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK, project::PROJECT_TYPE::OFFICE);

    return;
}

void ReportDeviceAlarmNewOffice::EmergencyAlarmMsgNofity()
{
    if (!office_info_.EmergencyNeedNotify())
    {
        AK_LOG_INFO << "NewOfficeEmergencyAlarmMsgNofity Office " << office_info_.Name() << " not EmergencyNeedNotify";
        return;
    }

    // 记录alarm日志
    OfficeDevList dev_list;
    OfficeAccountList app_list;
    OfficeAccountList admin_list;
    std::string timenow = GetNodeNowDateTimeByTimeZoneStr(office_info_.TimeZone(), g_time_zone_DST);
    std::string server_tag = dbinterface::ProjectUserManage::GetServerTag();

    // 紧急告警通知项目下的所有室内机、用户app、PMAPP
    app_list.clear();
    dev_list.clear();
    dbinterface::OfficeDevices::GetAllOfficeIndoorListByMngID(conn_dev_.project_mng_id, dev_list);
    dbinterface::OfficePersonalAccount::GetNewOfficeAllPersonnelList(conn_dev_.project_uuid, app_list);
    dbinterface::Alarm::InsertEmegencyNotifyAlarmLog(app_list, conn_dev_.project_mng_id, PmEmergencyDoorControlType::CONTROL_TYPE_OPEN, server_tag);
    COffice2RouteMsg::SendP2PEmergencyDoorNotifyMsg(app_list, timenow);
    COffice2RouteMsg::SendP2PEmergencyDoorNotifyMsg(dev_list, timenow);

    // 紧急告警通知项目下的所有管理机、AdminAPP
    admin_list.clear();
    dbinterface::OfficePersonalAccount::GetNewOfficeAllAdminAppList(conn_dev_.project_uuid, admin_list);
    dbinterface::Alarm::InsertEmegencyNotifyAlarmLog(admin_list, conn_dev_.project_mng_id, PmEmergencyDoorControlType::CONTROL_TYPE_OPEN, server_tag);
    COffice2RouteMsg::SendP2PEmergencyDoorNotifyMsg(admin_list, timenow);
    CommonAlarmNotifyMngDevByProjectUUID(conn_dev_.project_uuid);

    return;
}

bool ReportDeviceAlarmNewOffice::NeedRecordToDB(const ALARM& alarm_)
{
    // 门常开告警 未查询到Input绑定的Relay 不入库
    if (alarm_.alarm_code == (int)ALARM_CODE::DOOR_HELD_OPEN && alarm_.relay_num == 0)
    {
        return false;
    }

    return true;
}

int ReportDeviceAlarmNewOffice::IBuildReplyMsg(std::string& msg, uint16_t& msg_id)
{
    msg_id = MSG_TO_DEVICE_NOTIFY_ALARM_ACK;
    GetMsgBuildHandleInstance()->BuildAlarmAckMsg(report_alarm_msg_, msg);
    return 0;
}

