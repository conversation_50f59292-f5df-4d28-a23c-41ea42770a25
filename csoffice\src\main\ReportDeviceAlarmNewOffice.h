#ifndef _REPORT_DEVICE_NEWOFFICE_ALARM_H_
#define _REPORT_DEVICE_NEWOFFICE_ALARM_H_

#include <set>
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "util_virtual_door.h"
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "NotifyHttpReq.h"
#include "SnowFlakeGid.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/AlexaToken.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"

class ReportDeviceAlarmNewOffice: public IBase
{
public:
    ReportDeviceAlarmNewOffice(){}
    ~ReportDeviceAlarmNewOffice() = default;

    int IControl();
    int IParseXml(char *msg);
    int IBuildReplyMsg(std::string &msg, uint16_t &msg_id);

    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    IBasePtr NewInstance() {return std::make_shared<ReportDeviceAlarmNewOffice>();}

private:
    void AlarmMsgRecord();

    void AlarmNotify();
    void CommonAlarmNotify();
    void ArmingAlarmNotify();
    void EmergencyAlarmNotify();
    void EmergencyAlarmMsgNofity();
    void EmergencyAlarmControlNofity();

    void CommonAlarmNotifyAdminApp(std::string company_uuid);
    void CommonAlarmNotifyMngDevByProjectUUID(std::string project_uuid);
    void CommonAlarmNotifyMngDev(std::string company_uuid, std::set<std::string>& notifyed_dev_set);
    
    bool NeedToProcess(int alarm_code);
    void GetNewOfficeInfo(ALARM& alarm);
    bool NeedRecordToDB(const ALARM& alarm);
    bool DoorInputToActiveRelay(RelayType& relay_type, int& relay_num, std::string& relay_name);

private:
    std::string func_name_ = "ReportDeviceAlarm";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;

    ALARM alarm_;
    ResidentDev conn_dev_;
    OfficeInfo office_info_;
    std::string relay_name_ = "";
    SOCKET_MSG_ALARM report_alarm_msg_;
    SOCKET_MSG_ALARM_SEND send_alarm_msg_;
    std::set<std::string> notify_company_set_;     // 新办公一个设备可以属于多个公司，这里记录需要通知的公司列表
};

#endif
