#include "ReportDeviceArmingStatus.h"
#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include <string>
#include "DclientMsgSt.h"
#include "Office2RouteMsg.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsOemDefine.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "SnowFlakeGid.h"
#include "NotifyHttpReq.h"
#include "dbinterface/AlexaToken.h"
#include "json/json.h"
#include "OfficeInit.h"
#include "util_judge.h"

extern AKCS_CONF gstAKCSConf;
__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportDeviceArmingStatus>();
    //设备（室内机）上报当前布防、撤防的状态给平台
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_ARMING_STATUS);
};

int ReportDeviceArmingStatus::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (strlen(conn_dev_.mac) == 0)
    {
        AK_LOG_ERROR <<  "parse device arming status msg get mac is null";
        return -1;
    }
    if (0 != CMsgParseHandle::ParseReportArmingMsg(msg, &arming_msg_))
    {
        AK_LOG_ERROR <<  "parse device arming status msg failed";
        return -1;
    }
    AK_LOG_INFO << conn_dev_.mac << " device report arming. mode:" << arming_msg_.mode << " oem id:" << conn_dev_.oem_id << " uid:" << arming_msg_.uid
    << " resp_action:" << arming_msg_.resp_action << " home_sync:" << arming_msg_.home_sync << " dev node:" << conn_dev_.node << " dev is personal:" << conn_dev_.is_personal;
    Snprintf(arming_msg_.mac, sizeof(arming_msg_.mac), conn_dev_.mac);
    return 0;
}

int ReportDeviceArmingStatus::IControl()
{
    int oem  = conn_dev_.oem_id;
    if (akjudge::IsSupportArmingSync(oem, conn_dev_.fun_bit, arming_msg_.home_sync))
    {
        if (arming_msg_.resp_action == REPORT_ARMING_ACTION_TYPE_REBOOT)
        {
            AK_LOG_INFO << conn_dev_.mac << " device report arming. mode:" << arming_msg_.mode << " but action type is reboot.ignore!";
            return -1;
        }
    }
    dbinterface::ResidentDevices::SetDeviceArmingStatus(conn_dev_.mac, arming_msg_.mode);
    //判断是否是旧版本平台要求上报状态的
    if (!strncmp(arming_msg_.uid, "OldReq", 6))
    {
        return 0;
    }
    
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(arming_msg_.mac);
    msg.set_mode(arming_msg_.mode);
    msg.set_uid(arming_msg_.uid);
    msg.set_oem(oem);
    msg.set_resp_action(arming_msg_.resp_action);
    msg.set_node(conn_dev_.node);
    msg.set_home_sync(arming_msg_.home_sync);
    PostAlexaChangeStatus();

    AK::BackendCommon::BackendP2PBaseMessage base;
    // 广播给所有设备
    if (akjudge::IsSupportArmingSync(oem, conn_dev_.fun_bit, arming_msg_.home_sync))
    {
         //别的情况广播到全部的联动,app获取,不需要同步
        if (arming_msg_.resp_action != REPORT_ARMING_ACTION_TYPE_GET) {
            // 设备列表
            ResidentDeviceList dev_list;
            dbinterface::ResidentDevices::GetNodeDevList(conn_dev_.node, dev_list);
            for(const auto& dev : dev_list)
            {
                if (arming_msg_.resp_action == REPORT_ARMING_ACTION_TYPE_DEV_SET && strcmp(dev.mac, arming_msg_.mac) == 0)
                {
                    AK_LOG_INFO << dev.mac << " dev handle arming,respond Arming, multicast message. but it is youself,do not send youself.ignore. ";
                    continue;
                }
                else if (strcmp(dev.mac, arming_msg_.mac) == 0)
                {
                    AK_LOG_INFO << dev.mac << " respond Arming, multicast message. but it is youself, do not send youself.ignore. ";
                    continue;
                }
                base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_DEV_MAC, dev.mac,
                                                                    csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
                base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
                COffice2RouteMsg::PushMsg2Route(&base);
            }

            // 办公用户
            // 布撤防失败
            if (arming_msg_.resp_action == REPORT_ARMING_ACTION_TYPE_FORBID)
            {
                for(const auto& dev : dev_list) {
                    if (dev.dev_type == DEVICE_TYPE_INDOOR) {
                        msg.set_mac(dev.mac);
                        base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_APP_UID, conn_dev_.node,
                                                                            csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
                        base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
                        COffice2RouteMsg::PushMsg2Route(&base);
                        continue;
                    }
                }
            } else {
                base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_APP_UID, conn_dev_.node,
                                                                csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
                base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
                COffice2RouteMsg::PushMsg2Route(&base);
            }
            return 0;
        }
    }
    // 通知指定app
    base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_APP_UID, arming_msg_.uid,
                                                    csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
    base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
    COffice2RouteMsg::PushMsg2Route(&base);

    return 0;
}

int ReportDeviceArmingStatus::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    return 0;
}

void ReportDeviceArmingStatus::PostAlexaChangeStatus()
{
    // 推送arming状态给 Alexa
    if (strlen(conn_dev_.node_uuid) == 0)
    {
        AK_LOG_INFO << "device node uuid is null";
        return;
    }

    // 推送alarm状态给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(conn_dev_.node_uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatusHttpReq(conn_dev_.mac, traceid);
        AK_LOG_INFO << "alexa device arming notify web , mac :" << conn_dev_.mac << ", traceid : " << traceid;
    }
}

void ReportDeviceArmingStatus::PostAlexaChangeStatusHttpReq(const std::string& mac, uint64_t traceid)
{
    std::string data;
    Json::Value item;
    Json::FastWriter fast_writer;

    item["MAC"] = mac;
    item["TraceId"] = std::to_string(traceid);
    data = fast_writer.write(item);

    char url[128];
    snprintf(url, sizeof(url), "http://%s/alexaInner/v1/device/changeStatus", gstAKCSConf.smg_alexa_addr);

    CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}