#include "ReportDeviceHandleArming.h"
#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include <string>
#include "DclientMsgSt.h"
#include "Office2RouteMsg.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsOemDefine.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "SnowFlakeGid.h"
#include "NotifyHttpReq.h"
#include "util_judge.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportDeviceHandleArming>();
    //罗伯特门口机刷卡布撤防
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_HANDLE_ARMING);
};

int ReportDeviceHandleArming::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (strlen(conn_dev_.node) == 0)
    {
        AK_LOG_ERROR <<  "parse device arming status msg get node is null";
        return -1;
    }
    if (0 != CMsgParseHandle::ParseReqArmingMsg(msg, &arming_msg_))
    {
        AK_LOG_ERROR <<  "parse device arming status msg failed";
        return -1;
    }
    AK_LOG_INFO << conn_dev_.mac << " device report arming. mode:" << arming_msg_.mode << " oem id:" << conn_dev_.oem_id << " uid:" << arming_msg_.uid
    << " resp_action:" << arming_msg_.resp_action << " home_sync:" << arming_msg_.home_sync << " dev node:" << conn_dev_.node << " dev is personal:" << conn_dev_.is_personal;
    Snprintf(arming_msg_.mac, sizeof(arming_msg_.mac), conn_dev_.mac);
    //目前已支持的有：罗伯特版本，ajax版本，室内机30版本
    int oem  = conn_dev_.oem_id;
    if (!akjudge::IsSupportArmingSync(oem, conn_dev_.fun_bit, arming_msg_.home_sync))
    {
        AK_LOG_INFO << conn_dev_.mac << " dev handle arming. mode:" << arming_msg_.mode << " but is oem!=207,ignore!";
        return -1;
    }
    return 0;
}

int ReportDeviceHandleArming::IControl()
{
    arming_msg_.resp_action = REPORT_ARMING_ACTION_TYPE_DEV_SET;
    Snprintf(arming_msg_.mac, sizeof(arming_msg_.mac), conn_dev_.mac);

    int oem  = conn_dev_.oem_id;
    // 广播给所有设备
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(arming_msg_.mac);
    msg.set_mode(arming_msg_.mode);
    msg.set_uid(arming_msg_.uid);
    msg.set_oem(oem);
    msg.set_resp_action(arming_msg_.resp_action);
    msg.set_node(conn_dev_.node);
    msg.set_home_sync(arming_msg_.home_sync);
    AK::BackendCommon::BackendP2PBaseMessage base;

    // 设备列表
    ResidentDeviceList dev_list;
    dbinterface::ResidentDevices::GetNodeDevList(conn_dev_.node, dev_list);
    //设备 Action有Get/Set，Get处理在上报状态时候会下发，这里直接忽略
    //Get
    if (strcasecmp(arming_msg_.szAction, "Get") == 0)
    {
        int is_send = 0;
        for(const auto& dev : dev_list)
        {
            if (dev.dev_type == DEVICE_TYPE_INDOOR && dev.mac != arming_msg_.mac && dev.status == 1)
            {
                msg.set_mode(dev.arming);
                msg.set_resp_action(REPORT_ARMING_ACTION_TYPE_SELF_SET); //同步别人的,相当于自己设置
            }
            base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_DEV_MAC, dev.mac,
                                                                csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
            base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
            COffice2RouteMsg::PushMsg2Route(&base);
            is_send = 1;
        }
        if (is_send == 0) //如果都没有找到，就用平台设置,相当于重置也能同步到状态
        {
            msg.set_mode(conn_dev_.arming);
            msg.set_resp_action(REPORT_ARMING_ACTION_TYPE_SELF_SET); //同步别人的,相当于自己设置
            base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_DEV_MAC, arming_msg_.mac,
                                                                csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
            base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
            COffice2RouteMsg::PushMsg2Route(&base);
        }
        return 0;
    }


    for(const auto& dev : dev_list)
    {
        base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_DEV_MAC, dev.mac,
                                                                csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
        base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
        COffice2RouteMsg::PushMsg2Route(&base);
    }

    // 办公用户列表
    // 布撤防失败
    if (arming_msg_.resp_action == REPORT_ARMING_ACTION_TYPE_FORBID)
    {
        for(const auto& dev : dev_list) {
            if (dev.dev_type == DEVICE_TYPE_INDOOR) {
                msg.set_mac(dev.mac);
                base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_APP_UID, conn_dev_.node,
                                                            csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
                base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
                COffice2RouteMsg::PushMsg2Route(&base);
                continue;
            }
        }
    } else {
        base = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, TransP2PMsgType::TO_APP_UID, conn_dev_.node,
                                                            csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
        base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
        COffice2RouteMsg::PushMsg2Route(&base);
    }
    

    return 0;
}

int ReportDeviceHandleArming::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    return 0;
}