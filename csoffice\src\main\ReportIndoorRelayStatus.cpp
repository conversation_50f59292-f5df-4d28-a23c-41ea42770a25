#include "ReportIndoorRelayStatus.h"
#include "DclientMsgDef.h"
#include "MsgParse.h"
#include "util_relay.h"
#include "DeviceCheck.h"
#include "Office2RouteMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"
#include "dbinterface/new-office/OfficeAdmin.h"
__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportIndoorRelayStatus>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_RELAY_STATUS);
};

int ReportIndoorRelayStatus::IParseXml(char *msg)
{
    memset(&indoor_relay_status_, 0, sizeof(indoor_relay_status_));
    if (0 != CMsgParseHandle::ParseReportIndoorRelayStatusMsg(msg, &indoor_relay_status_))
    {
        AK_LOG_WARN << "parse indoor relay status msg failed.";
        return -1;
    }
    AK_LOG_INFO << "report indoor relay status, report id list:" << indoor_relay_status_.relay_ids << " indoor relay type:" << indoor_relay_status_.relay_type;
    return 0;
}

int ReportIndoorRelayStatus::IControl()
{
    ResidentDev dev = GetDevicesClient();
    if (GetDeviceCheckInstance()->IsErrorFirmwareForChangeRelay(dev.sw_ver))
    {
        AK_LOG_WARN << "ReportIndoorRelayStatus Firmware(eg.***********) is abnormal version. do nothing";
		return -1;
    }

    //数据库状态字段更新
    if (0 != UpdateIndoorRelayStatus(indoor_relay_status_, dev.mac, dev.project_type))
    {
        AK_LOG_WARN << "indoor relay status update failed. dev mac:" << dev.mac;
        return -1;
    }
    return 0;
}

int ReportIndoorRelayStatus::IToRouteMsg()
{
    ResidentDev dev = GetDevicesClient();
    int relay_status = DoornumToRelayStatus(indoor_relay_status_.relay_ids);

    std::string node = dev.node;
    std::string personal_account_uuid;
    OfficeAdminInfoList office_admin_list;
    OfficeInfo office_info(dev.project_uuid);

    // 新办公, 通知给设备的关联人,员工以及admin
    if (office_info.IsNew() == true)
    {
        // 查找员工
        OfficeDeviceAssignInfo office_device_assign_info;
        if (dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(dev.uuid, office_device_assign_info) == 0)
        {
            personal_account_uuid = office_device_assign_info.personal_account_uuid;
        }

        if (!personal_account_uuid.empty())
        {
            OfficeAccount personal_account;
            if (dbinterface::OfficePersonalAccount::GetUUIDAccount(personal_account_uuid, personal_account) == 0)
            {
                node = personal_account.account;
            }
        }

        if (node.empty())
        {
            AK_LOG_WARN << "report relay status failed: node is empty, mac=" << dev.mac << ", relay=" << relay_status;
            return -1;
        }
        
        // 发送消息给员工
        COffice2RouteMsg::GroupIndoorRelayStatusMsg(node, dev.mac, relay_status, indoor_relay_status_.relay_type, project::OFFICE);
        
        // 查找管理员
        if (dbinterface::OfficeAdmin::GetOfficeAdminInfoListByCompanyUUID(office_device_assign_info.office_company_uuid, office_admin_list) == 0)
        {
            for (const auto& admin_info : office_admin_list)
            {
                OfficeAccount admin_personal_account;
                if (dbinterface::OfficePersonalAccount::GetUUIDAccount(admin_info.personal_account_uuid, admin_personal_account) == 0)
                {
                    std::string admin_node = admin_personal_account.account;
                    if (!admin_node.empty())
                    {
                        COffice2RouteMsg::GroupIndoorRelayStatusMsg(admin_node, dev.mac, relay_status, indoor_relay_status_.relay_type, project::OFFICE);
                    }
                    else
                    {
                        AK_LOG_WARN << "Admin node is empty for UUID: " << admin_info.personal_account_uuid;
                    }
                }
                else
                {
                    AK_LOG_WARN << "Failed to get admin account for UUID: " << admin_info.personal_account_uuid;
                }
            }
        }
    }
    
    return 0;
}


int ReportIndoorRelayStatus::UpdateIndoorRelayStatus(const SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS& indoor_relay_status, const std::string& mac, int project_type)
{
    int relay_status = DoornumToRelayStatus(indoor_relay_status.relay_ids);
    switch(indoor_relay_status.relay_type)
    {
        //本地relay
        case IndoorRelayType::TYPE_LOCAL:
        {
            return UpdateIndoorLocalRelayStatus(mac, project_type, relay_status);
        }    
        //办公暂不支持室内机外接relay
        case IndoorRelayType::TYPE_EXTERN:
            break;
    }
    AK_LOG_WARN <<  "report relay status type not support. type=" << indoor_relay_status.relay_type;
    return -1;
}

int ReportIndoorRelayStatus::UpdateIndoorLocalRelayStatus(const std::string& mac, int project_type, int relay_status)
{
    int ret = 0;
    if (project_type == project::OFFICE)
    {
        ret = dbinterface::ResidentDevices::SetDeviceRelayStatus(mac, relay_status);
    }
    else
    {
        AK_LOG_WARN << "project type no support, project type: " << project_type;
        ret = -1;
    }

    return ret;
}
