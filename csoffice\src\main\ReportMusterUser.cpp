#include "ReportMusterUser.h"
#include "msgparse/ParseReportMusterReaderUser.hpp"
#include "doorlog/RecordActLog.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeMusterReportSettingReaderList.h"
#include "Office2RouteMsg.h"
#include "AkcsCommonDef.h"

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportMusterUser>();
    RegNewOfficeDevFunc(p, MSG_FROM_DEVICE_MUSTER_REPORT_USER);
};

int ReportMusterUser::IParseXml(char *msg)
{
    memset(&report_muster_user_msg_, 0, sizeof(report_muster_user_msg_));
    if (0 != akcs_msgparse::ParseReportMusterReaderUser(msg, report_muster_user_msg_))
    {
        AK_LOG_WARN << "parse report muster reader user msg failed.";
        return -1;
    }

    return 0;
}

int ReportMusterUser::IControl()
{
    ResidentDev dev = GetDevicesClient();
    if (!dbinterface::OfficeMusterReportSettingReaderList::IsMusterReportDevice(dev.uuid))
    {
        AK_LOG_WARN << "report device is not muster report device. dev_uuid=" << dev.uuid;
        return -1;
    }

    if (report_muster_user_msg_.muster_type == MusterType::ILLEGAL)
    {
        AK_LOG_WARN << "report muster type illegal. process failed.";
        return -1;
    }

    // 获取用户类型和对应uuid
    GetUserInfo();

    if (muster_user_uuid_.empty() || muster_account_type_.empty() || strcmp(office_uuid_.c_str(), dev.project_uuid) != 0)
    {
        AK_LOG_WARN << "get muster user info failed. process failed.";
        return -1;
    }

    //人员点名打卡通知web
    COffice2RouteMsg::SendMusterReportNotifyWebMsg(muster_user_uuid_, muster_account_type_, office_uuid_);

    return 0;
}

void ReportMusterUser::GetUserInfo()
{
    if (report_muster_user_msg_.muster_user[0] == MODE_DELIVERY)
    {
        muster_account_type_ = "delivery";

        int delivery_id = ATOI(&report_muster_user_msg_.muster_user[1]);
        OfficeDeliveryInfo delivery_info;
        if (0 != dbinterface::OfficeDelivery::GetOfficeDeliveryByID(delivery_id, delivery_info))
        {
            AK_LOG_WARN << "muster delivery not found. delivery id=" << delivery_id;
            return;
        }
        
        muster_user_uuid_ = delivery_info.uuid;
        office_uuid_ = delivery_info.project_uuid;
    }
    else
    {
        muster_account_type_ = "personnel";

        OfficeAccount account;
        if (0 != dbinterface::OfficePersonalAccount::GetUidAccount(report_muster_user_msg_.muster_user, account))
        {
            AK_LOG_WARN << "muster per account not found. account=" << report_muster_user_msg_.muster_user;
            return;
        }

        if (account.role != ACCOUNT_ROLE_OFFICE_NEW_PER)
        {
            AK_LOG_WARN << "muster report user is not personnel. no need to report. account uuid=" << account.uuid;
            return;
        }

        muster_user_uuid_ = account.uuid;
        office_uuid_ = account.office_uuid; //办公没有从账号的概念，
    }

    return;
}
