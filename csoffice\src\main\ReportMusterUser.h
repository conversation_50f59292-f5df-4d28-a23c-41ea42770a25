#ifndef _REPORT_MUSTER_USER_H_
#define _REPORT_MUSTER_USER_H_

#include "AgentBase.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

class ReportMusterUser: public IBase
{
public:
    ReportMusterUser(){}
    ~ReportMusterUser() = default;

    int IParseXml(char *msg);
    int IControl();
    
    IBasePtr NewInstance() {return std::make_shared<ReportMusterUser>();}

    std::string FuncName() {return func_name_;}
    
    MsgEncryptType EncType() {return enc_type_;}

private:
    void GetUserInfo();

    std::string func_name_ = "ReportMusterUser";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_REPORT_MUSTER_USER report_muster_user_msg_;
    std::string muster_account_type_;
    std::string muster_user_uuid_;
    std::string office_uuid_;
};

#endif