#include "msgparse/ParseRemoteControlAck.hpp"
#include "ReportRemoteControlAck.h"
#include "DclientMsgDef.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "AkLogging.h"
#include "Office2RouteMsg.h"
#include "util_judge.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportRemoteControlAck>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REMOTE_CONTROL_ACK);
};

int ReportRemoteControlAck::IParseXml(char *msg)
{
    memset(&remote_ack_msg_, 0, sizeof(remote_ack_msg_));
    if (0 != akcs_msgparse::ParseRemoteControlAck(msg, remote_ack_msg_))
    {
        AK_LOG_WARN << "parse remote control ack msg failed.";
        return -1;
    }

    return 0;
}

void ReportRemoteControlAck::GetResponseObjectInfo()
{
    //app_account_traceid或dev_mac_traceid
    std::string trace_id(remote_ack_msg_.trace_id);
    
    auto flag_pos = trace_id.find_first_of("_");
    if (flag_pos == std::string::npos)
    {
        AK_LOG_WARN << "app or dev flag not found. open door trace_id=" << trace_id;
        return;
    }
    app_or_dev_flag_ = trace_id.substr(0, flag_pos);

    auto site_or_mac_pos = trace_id.find_last_of("_");
    if (site_or_mac_pos == std::string::npos)
    {
        AK_LOG_WARN << "site_or_mac not found. open door trace_id=" << trace_id;
        return;
    }

    if (site_or_mac_pos <= flag_pos)
    {
        AK_LOG_WARN << "trace id format wrong. open door trace_id=" << trace_id;
        return;
    }

    site_or_mac_ = trace_id.substr(flag_pos + 1, site_or_mac_pos - flag_pos - 1);
}

int ReportRemoteControlAck::IControl()
{
    GetResponseObjectInfo();

    ResidentDev dev = GetDevicesClient();

    if (!akjudge::DevSupportRemoteOpenDoorAck(dev.fun_bit))
    {
        AK_LOG_INFO << "device not support ack notify. mac = " << dev.mac << ", result = " << remote_ack_msg_.result;
        return 0;
    }

    auto type = GetAckResponseType();
    SendAckReponse(type);
    return 0;
}

RemoteControlAckResponseType ReportRemoteControlAck::GetAckResponseType()
{
    if (app_or_dev_flag_ == "app") //site
    {
        return RemoteControlAckResponseType::RESPONSE_TYPE_APP;
    }
    else if (app_or_dev_flag_ == "dev") //mac
    {
        return RemoteControlAckResponseType::RESPONSE_TYPE_DEV;
    }
    return RemoteControlAckResponseType::RESPONSE_TYPE_NULL;
}

void ReportRemoteControlAck::SendAckReponse(RemoteControlAckResponseType response_type)
{
    if (response_type == RemoteControlAckResponseType::RESPONSE_TYPE_NULL)
    {
        AK_LOG_WARN << "response ack trace id invalid. trace id=" << remote_ack_msg_.trace_id;
        return;
    }

    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    AK::Server::P2PMainResponseOpenDoorMsg p2p_msg;

    p2p_msg.set_msg_traceid(remote_ack_msg_.trace_id);
    p2p_msg.set_mac_or_uid(site_or_mac_.c_str());
    p2p_msg.set_result(remote_ack_msg_.result);
    p2p_msg.set_info(remote_ack_msg_.info);
    p2p_msg.set_response_type((int)response_type);

    int project_type = project::OFFICE;

    if (response_type == RemoteControlAckResponseType::RESPONSE_TYPE_DEV)
    {
        base_msg = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OPEN_DOOR_ACK, TransP2PMsgType::TO_DEV_MAC, site_or_mac_,
                            COffice2RouteMsg::DevProjectTypeToDevType(project_type), project_type);
    }
    else if (response_type == RemoteControlAckResponseType::RESPONSE_TYPE_APP)
    {
        base_msg = COffice2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OPEN_DOOR_ACK, TransP2PMsgType::TO_APP_UID_ONLINE, site_or_mac_,
                            COffice2RouteMsg::DevProjectTypeToDevType(project_type), project_type);
    }

    base_msg.mutable_p2pmainresponseopendoormsg2()->CopyFrom(p2p_msg);
    COffice2RouteMsg::PushMsg2Route(&base_msg);

    return;
}