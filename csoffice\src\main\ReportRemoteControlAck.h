#ifndef _REPORT_REPORT_CONTROL_ACK_H_
#define _REPORT_REPORT_CONTROL_ACK_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"
#include "AgentBase.h"

class ReportRemoteControlAck : public IBase
{
public:
    ReportRemoteControlAck(){}
    ~ReportRemoteControlAck() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ReportRemoteControlAck>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    RemoteControlAckResponseType GetAckResponseType();
    void SendAckReponse(RemoteControlAckResponseType response_type);
    void GetResponseObjectInfo();

    std::string func_name_ = "ReportRemoteControlAck";
    std::string site_or_mac_;
    std::string app_or_dev_flag_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEV_REMOTE_ACK remote_ack_msg_;

};

#endif //_REPORT_REPORT_CONTROL_ACK_H_