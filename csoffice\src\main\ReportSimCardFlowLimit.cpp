#include "MsgParse.h"
#include "OfficeInit.h"
#include "RouteMqProduce.h"
#include "ReportSimCardFlowLimit.h"

extern AKCS_CONF gstAKCSConf;
extern RouteMQProduce* g_nsq_producer;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportSimCardFlowLimit>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT);
};

int ReportSimCardFlowLimit::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != CMsgParseHandle::ParseReportSimCardFlowLimitMsg(msg, &sim_card_flow_msg_))
    {
        AK_LOG_WARN << "ReportSimCardFlowLimit ParseReportSimCardFlowLimitMsg failed";
        return -1;
    }
    
    AK_LOG_INFO << "ReportSimCardFlowLimit success, mac = " << conn_dev_.mac 
                << ", percent = " << sim_card_flow_msg_.percent << ", limit = " << sim_card_flow_msg_.limit;
    return 0;
}

int ReportSimCardFlowLimit::IControl()
{
    office_info_ = OfficeInfo(conn_dev_.project_mng_id);

    // 判断setting开关
    if (RemindSwitchOff())
    {
        AK_LOG_INFO << "Office " << conn_dev_.project_mng_id << " Is Set To No Remind OutOfLimitFlow";
        return -1; 
    }

    // 获取接收者信息
    if (!GetReceiverInfo())
    {
        return -1;
    }

    // 获取设备信息
    if (!GetDeviceLocation())
    {
        return -1;
    }

    // 获取流量信息
    GetSimCardFlow();

    return 0;
}

bool ReportSimCardFlowLimit::RemindSwitchOff()
{
    return office_info_.LimitFlowRemind() == FlowOutLimitRemind::NO_REMIND;
}

bool ReportSimCardFlowLimit::GetReceiverInfo()
{
    if (office_info_.MobileNumber().size() == 0 || office_info_.PhoneCode().size() == 0)
    {
        AK_LOG_INFO << "Office " << conn_dev_.project_mng_id << ", MobileNumber=" << office_info_.MobileNumber() << ", PhoneCode=" << office_info_.PhoneCode() << " is not set, send sms failed.";
        return false;
    }
    
    std::tuple<std::string, std::string> lang_location = office_info_.GetPMAccountLanguageLocation();
    std::string language = std::get<0>(lang_location);
    std::string office_name = std::get<1>(lang_location);
    
    if (language.size() == 0)
    {
        AK_LOG_INFO << "Office " << conn_dev_.project_mng_id << " not set language, send sms failed.";
        return false;
    }
    
    msg_.set_language(language);
    msg_.set_community_name(office_name);
    return true;
}

bool ReportSimCardFlowLimit::GetDeviceLocation()
{
    if (conn_dev_.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        std::string unit_name = office_info_.GetCommunityUnitName(conn_dev_.unit_id);
        msg_.set_unit_name(unit_name);
    }
    else if (conn_dev_.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        //do nothing
    }
    else
    {
        AK_LOG_INFO << "Device MAC=" << conn_dev_.mac << " Grade= " << conn_dev_.grade << " do not need remind outoflimitflow";
        return false;
    }
    return true;
}

void ReportSimCardFlowLimit::GetSimCardFlow()
{
    const unsigned int ONE_GB = 1073741824;
    double remind_flow = sim_card_flow_msg_.limit / ONE_GB / 100.0 * sim_card_flow_msg_.percent;
    
    char flow_gb[20];
    snprintf(flow_gb, sizeof(flow_gb), "%.02f", remind_flow);
    msg_.set_flow(flow_gb);

    const int FLOW_EXHAUSTED = 1;
    const int FLOW_REMAINED = 0;
    if ((int)sim_card_flow_msg_.percent >= 100)
    {
        msg_.set_out_of_flow(FLOW_EXHAUSTED);
    }
    else
    {
        msg_.set_out_of_flow(FLOW_REMAINED);
    }

    return;
}

int ReportSimCardFlowLimit::IToRouteMsg()
{
    const int SMS_FLOW_OUT_OF_LIMIT = 3;
    msg_.set_type(SMS_FLOW_OUT_OF_LIMIT);
    msg_.set_phone(office_info_.MobileNumber());
    msg_.set_area_code(office_info_.PhoneCode());
    msg_.set_location(conn_dev_.location);
    msg_.set_grade(conn_dev_.grade);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg_);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_OFFICE_SEND_REMIND_FLOW_OUT_OF_LIMIT);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
    return 0;
}
