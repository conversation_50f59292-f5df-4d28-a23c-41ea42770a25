#ifndef __REPORT_SIM_CARD_FLOW_LIMIT_H__
#define __REPORT_SIM_CARD_FLOW_LIMIT_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "Office2RouteMsg.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/resident/ResidentDevices.h"

class ReportSimCardFlowLimit: public IBase
{
public:
    ReportSimCardFlowLimit(){}
    ~ReportSimCardFlowLimit() = default;

    int IParseXml(char *msg);
    int IControl();
    int IToRouteMsg();

    IBasePtr NewInstance() {return std::make_shared<ReportSimCardFlowLimit>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    bool RemindSwitchOff();
    bool GetReceiverInfo();
    bool GetDeviceLocation();
    void GetSimCardFlow();

public:
    std::string func_name_ = "ReportSimCardFlowLimit";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    ResidentDev conn_dev_;
    OfficeInfo office_info_;
    SOCKET_MSG_FLOW_OUT_LIMIT sim_card_flow_msg_;
    AK::Server::SendSmsRemindFlowOutofLimit msg_;
};

#endif
