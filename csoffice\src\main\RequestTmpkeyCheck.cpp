#include "MsgParse.h"
#include "OfficeServer.h"
#include "ProjectUserManage.h"
#include "RequestTmpkeyCheck.h"
#include "dbinterface/new-office/DevicesDoorList.h"

extern std::map<string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void init(){
    IBasePtr p = std::make_shared<RequestTmpkeyCheck>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_CHECK_TMP_KEY);
};

int RequestTmpkeyCheck::IParseXml(char *msg)
{
    CMsgParseHandle::ParseCheckTmpKeyMsg(msg, &tmpkey_info_);
    return 0;
}

int RequestTmpkeyCheck::IControl()
{
    GetMacInfo(mac_info_);

    // 只能去数据库查,如果查缓存的话,网页更新后TmpKey返回的relay还是旧的
    if (0 != dbinterface::ResidentDevices::GetMacDev(mac_info_.mac, dev_))
    {
        AK_LOG_WARN << "RequestTmpkeyCheck GetMacDev falied, mac = " << mac_info_.mac;
        return -1;
    }

    tmpkey_info_.unit_id = dev_.unit_id;
    tmpkey_info_.manager_account_id = dev_.project_mng_id;
    tmpkey_info_.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    Snprintf(tmpkey_info_.mac, sizeof(tmpkey_info_.mac), dev_.mac);
    Snprintf(tmpkey_info_.area_node, sizeof(tmpkey_info_.area_node), dev_.node);

    office_info_ = OfficeInfo(mac_info_.mng_id);
    
    if (office_info_.IsNew())
    {
        NewOfficeTmpKeyCheck();
    }
    else
    {
        OldOfficeTmpKeyCheck();
    }

    return 0; 
}

void RequestTmpkeyCheck::OldOfficeTmpKeyCheck()
{
    if (dev_.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        tmpkey_info_.result = OldOfficeCheckPersonalDev();
    }
    else if (dev_.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        tmpkey_info_.result = OldOfficeCheckPublicUnitDev();
    }
    else if (dev_.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        tmpkey_info_.result = OldOfficeCheckPublicDev();
    }

    dbinterface::OfficePersonalAccount::GetUidAccount(tmpkey_info_.node, per_account_);

    // 校验高级功能
    OldOfficeCheckFeaturePlan();
    
    // 校验权限组
    OldOfficeCheckAccessGroup();

    // 获取梯控楼层
    if (strlen(tmpkey_info_.node) > 0)
    {
        std::string access_floor = dbinterface::OfficePersonalAccount::GetLiftFloorNum(per_account_);
        Snprintf(tmpkey_info_.floor, sizeof(tmpkey_info_.floor), access_floor.c_str());
    }
    
    // 更新使用次数
    dbinterface::PersonalAppTmpKey::UpdateAccessTimes(tmpkey_info_);
    
    return;
}

// apt内设备
int RequestTmpkeyCheck::OldOfficeCheckPersonalDev()
{
    if (true == dbinterface::PersonalAppTmpKey::CheckPersonalAppTmpKeyByPerDev(tmpkey_info_, g_time_zone_DST))
    {
        return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
    }
    else if (true == dbinterface::PersonalAppTmpKey::CheckPubAppTmpKey(tmpkey_info_, g_time_zone_DST))
    {
        return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
    }

    return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
}

// unit设备
int RequestTmpkeyCheck::OldOfficeCheckPublicUnitDev()
{
    if (true == dbinterface::PersonalAppTmpKey::CheckPersonalAppTmpKeyByPubDev(tmpkey_info_, g_time_zone_DST))
    {
        return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
    }
    else if (true == dbinterface::PersonalAppTmpKey::CheckPubAppTmpKey(tmpkey_info_, g_time_zone_DST))
    {
        return dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;
    }

    return dbinterface::PersonalAppTmpKey::CHECK_ERROR;
}

// pub设备
int RequestTmpkeyCheck::OldOfficeCheckPublicDev()
{    
    dbinterface::PersonalAppTmpKey::DcliCheckResult result = dbinterface::PersonalAppTmpKey::DcliCheckResult::CHECK_ERROR;
    
    if (true == dbinterface::PersonalAppTmpKey::CheckPersonalAppTmpKeyByPubDev(tmpkey_info_, g_time_zone_DST))
    {
        result = dbinterface::PersonalAppTmpKey::DcliCheckResult::CHECK_SUCCESS;
    }
    else if (true == dbinterface::PersonalAppTmpKey::CheckPubAppTmpKey(tmpkey_info_, g_time_zone_DST))
    {
        result = dbinterface::PersonalAppTmpKey::DcliCheckResult::CHECK_SUCCESS;
    }

    // 校验成功再判断楼栋管理
    if (result == dbinterface::PersonalAppTmpKey::DcliCheckResult::CHECK_SUCCESS)
    {
        if (tmpkey_info_.personal_unit_id > 0 
            && true == dbinterface::ResidentDevices::CIsManageBuildingType(dev_.dev_type) 
            && 0 == dbinterface::PubDevMngList::IsManageBuildingByMac(dev_.mac, tmpkey_info_.personal_unit_id))
        {
            result = dbinterface::PersonalAppTmpKey::DcliCheckResult::CHECK_ERROR;
        }
    }

    return result;
}

// 校验高级功能是否允许用户创建tmpkey
void RequestTmpkeyCheck::OldOfficeCheckFeaturePlan()
{
    // 前面校验失败了,就不用往下判断了
    if (tmpkey_info_.result == dbinterface::PersonalAppTmpKey::DcliCheckResult::CHECK_ERROR)
    {
        return;
    }

    // pm创的tmpkey不用判断
    if (tmpkey_info_.is_pmcreate)
    {
        AK_LOG_INFO << "OldOfficeCheckFeaturePlan not allow enduser create TmpKey, TmpKey = " << tmpkey_info_.tmpkey <<  ", MngAccountID = " << dev_.project_mng_id << ", node = " << per_account_.account;
        return;        
    }
        
    // 校验office高级功能,限制用户是否能创建tmpkey
    // 办公高级功能过期了,用户创建的tmpkey可以使用; 高级功能过期之后续费了,用户创建的tmpkey不能使用
    if (UnderFeaturePlanRestrict())
    {
        tmpkey_info_.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
        AK_LOG_INFO << "OldOfficeCheckFeaturePlan not allow enduser create TmpKey, TmpKey = " << tmpkey_info_.tmpkey <<  ", MngAccountID = " << dev_.project_mng_id << ", node = " << per_account_.account;
    }

    return;
}

void RequestTmpkeyCheck::OldOfficeCheckAccessGroup()
{
    int default_relay_value = 0;
    int default_serelay_value = 0;

    // 获取设备已开启的relay
    GetValueByRelay(dev_.relay, default_relay_value); 
    GetValueByRelay(dev_.security_relay, default_serelay_value); 

    // 权限组relay值默认与设备relay值一致
    int access_group_relay_value =  default_relay_value;   
    int access_group_serelay_value = default_serelay_value;

    // 终端用户创建+无默认权限组 需考虑权限组的relay情况
    if (0 == tmpkey_info_.is_pmcreate  && !dbinterface::AccessGroup::HaveDefaultAG(tmpkey_info_.account, dev_.unit_id))
    {
        std::map<std::string, int> mac_relay;
        std::map<std::string, int> mac_serelay;
        if(0 == dbinterface::AccessGroup::GetUserAGDeviceList(tmpkey_info_.account, mac_relay, mac_serelay))
        {
            access_group_relay_value = mac_relay[tmpkey_info_.mac];
            access_group_serelay_value = mac_serelay[tmpkey_info_.mac];
        }
    }
    
    std::string door_num = RelayToString(tmpkey_info_.relay_value & default_relay_value & access_group_relay_value);
    std::string security_door_num = RelayToString(tmpkey_info_.security_relay_value & default_serelay_value & access_group_serelay_value);
    Snprintf(tmpkey_info_.relay, sizeof(tmpkey_info_.relay), door_num.c_str());
    Snprintf(tmpkey_info_.security_relay, sizeof(tmpkey_info_.security_relay), security_door_num.c_str());

    // 兼容旧设备relay为空会全开的问题
    if (strlen(tmpkey_info_.relay) == 0 && strlen(tmpkey_info_.security_relay) == 0)
    {
        tmpkey_info_.result = dbinterface::PersonalAppTmpKey::CHECK_ERROR;
    }

    AK_LOG_INFO << "OldOfficeTmpKeyCheck TmpKey = " << tmpkey_info_.tmpkey << ", TempKey Relay = " << tmpkey_info_.relay_value 
                << ", Default Relay = " << default_relay_value << ", AccessGroup Relay = " << access_group_relay_value << ", Result = " << tmpkey_info_.result;

    AK_LOG_INFO << "OldOfficeTmpKeyCheck TmpKey = " << tmpkey_info_.tmpkey << ", TempKey SecurityRelay = " << tmpkey_info_.security_relay_value 
                << ", Default SecurityRelay = " << default_serelay_value << ", AccessGroup SecurityRelay = " << access_group_serelay_value << ", Result = " << tmpkey_info_.result;  
    return;
}

void RequestTmpkeyCheck::NewOfficeTmpKeyCheck()
{
    // 获取tmepkey信息
    OfficeTempKeyInfo office_tmpkey_info;
    if (0 != dbinterface::OfficeTempKey::GetOfficeTempKeyInfo(tmpkey_info_.tmpkey, mac_info_.project_uuid, office_tmpkey_info))
    {
        AK_LOG_INFO << "NewOfficeTmpKeyCheck GetOfficeTempKeyByCode Failed, tmpkey code = " << tmpkey_info_.tmpkey;
        return;
    }

    // 获取tempkey绑定的realy
    if (0 != dbinterface::OfficeTempKey::GetOfficeTempKeyListInfo(mac_info_.uuid, office_tmpkey_info))
    {
        AK_LOG_INFO << "NewOfficeTmpKeyCheck GetOfficeTempKeyListInfo Failed, tmpkey code = " << tmpkey_info_.tmpkey;
        return;
    }

    // 判断是否在时间范围内
    if (!dbinterface::OfficeTempKey::WithinValidTime(office_tmpkey_info, office_info_.TimeZone(), g_time_zone_DST))
    {
        AK_LOG_INFO << "NewOfficeTmpKeyCheck WithinValidTime Failed, tmpkey code = " << tmpkey_info_.tmpkey;
        return;
    }
    
    // 判断使用次数
    if (!WithinAllowedTimes(office_tmpkey_info))
    {
        AK_LOG_INFO << "NewOfficeTmpKeyCheck AccessTimes more than AllowedTimes, tmpkey code = " << tmpkey_info_.tmpkey;
        return;
    }

    // 判断高级功能
    if (NewOfficeUnderFeaturePlanRestrict(office_tmpkey_info))
    {
        AK_LOG_INFO << "NewOfficeTmpKeyCheck NewOfficeUnderFeaturePlanRestrict, tmpkey code = " << tmpkey_info_.tmpkey;
        return;
    }

    // 获取梯控楼层, enduser创建的才需要
    NewOfficeGetLiftFloorNum(office_tmpkey_info);

    // 转换relay格式回复设备, 由7 = 1 + 2 + 4 转为 123
    NewOfficeGetResponseDoorNum(office_tmpkey_info);

    // 判断company的holiday
    Snprintf(tmpkey_info_.company_uuid, sizeof(tmpkey_info_.company_uuid), office_tmpkey_info.office_company_uuid);

    // 更新使用次数
    dbinterface::OfficeTempKey::UpdateAccessTimes(office_tmpkey_info);

    // 设置结果
    tmpkey_info_.result = dbinterface::PersonalAppTmpKey::CHECK_SUCCESS;

    AK_LOG_INFO << "NewOfficeTmpKeyCheck success, tmpkey code = " << tmpkey_info_.tmpkey << ", relay = " << tmpkey_info_.relay << ", security_relay = " << tmpkey_info_.security_relay << " , access_floor" << tmpkey_info_.floor;
    return;
}

void RequestTmpkeyCheck::NewOfficeGetLiftFloorNum(const OfficeTempKeyInfo& office_tmpkey_info)
{
    if (office_tmpkey_info.creator_type == OfficeTempKeyCreatorType::ENDUSER)
    {
        std::string access_floor =dbinterface::OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(per_account_.uuid, dev_.unit_uuid, dev_.uuid, ACCOUNT_ROLE_OFFICE_NEW_PER);
        Snprintf(tmpkey_info_.floor, sizeof(tmpkey_info_.floor), access_floor.c_str());
    }
    else if (office_tmpkey_info.creator_type == OfficeTempKeyCreatorType::ADMIN)
    {
        std::string access_floor =dbinterface::OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(per_account_.uuid, dev_.unit_uuid, dev_.uuid, ACCOUNT_ROLE_OFFICE_NEW_ADMIN);
        Snprintf(tmpkey_info_.floor, sizeof(tmpkey_info_.floor), access_floor.c_str());
    }
    return;
}

void RequestTmpkeyCheck::NewOfficeGetCreatorInfo(const OfficeTempKeyInfo& office_tmpkey_info)
{
    if (office_tmpkey_info.creator_type == OfficeTempKeyCreatorType::ENDUSER)
    {
        dbinterface::OfficePersonalAccount::GetUUIDAccount(office_tmpkey_info.creator_personal_account_uuid, per_account_);
        Snprintf(tmpkey_info_.account, sizeof(tmpkey_info_.account), per_account_.account);
    }
    if (office_tmpkey_info.creator_type == OfficeTempKeyCreatorType::ADMIN)
    {
        OfficeAdminInfo admin_info;
        dbinterface::OfficeAdmin::GetOfficeAdminByAccountUUID(office_tmpkey_info.creator_account_uuid, admin_info);
        dbinterface::OfficePersonalAccount::GetUUIDAccount(admin_info.personal_account_uuid, per_account_);
        Snprintf(tmpkey_info_.account, sizeof(tmpkey_info_.account), per_account_.account);
    }
    return;
}

void RequestTmpkeyCheck::NewOfficeGetResponseDoorNum(const OfficeTempKeyInfo& office_tmpkey_info)
{
    // 获取设备实际配置的relay value
    int enabled_relay_value = 0;
    int enabled_security_relay_value = 0;
    dbinterface::DevicesDoorList::GetDevicesDoorEnableRelayValueByDevicesUUID(dev_.uuid, enabled_relay_value, enabled_security_relay_value);
    
    // 获取权限组的relay
    int access_group_relay_value =  enabled_relay_value;   
    int access_group_security_relay_value = enabled_security_relay_value;
    if (office_tmpkey_info.creator_type == OfficeTempKeyCreatorType::ENDUSER)
    {
        dbinterface::OfficeAccessGroupPersonnel::GetPersonnelAccessGroupDevRelay(office_tmpkey_info.creator_personal_account_uuid, mac_info_.uuid, access_group_relay_value, access_group_security_relay_value);
    }

    // 获取设备付费正常状态的relay value，要开付费正常的door
    int normal_relay_value = 0;
    int normal_security_relay_value = 0;
    dbinterface::DevicesDoorList::GetDevicesNormalRelayValue(dev_.uuid, normal_relay_value, normal_security_relay_value);

    // tempkey relay & 设备开启的relay & 权限组relay
    std::string door_num = RelayToString(office_tmpkey_info.relay & enabled_relay_value & access_group_relay_value & normal_relay_value);
    std::string security_door_num = RelayToString(office_tmpkey_info.security_relay & enabled_security_relay_value & access_group_security_relay_value & normal_security_relay_value);
    
    AK_LOG_INFO << "NewOfficeGetResponseDoorNum enabled_relay_value = " << enabled_relay_value << ", enabled_security_relay_value = " << enabled_security_relay_value;
    AK_LOG_INFO << "NewOfficeGetResponseDoorNum tempkey relay_value = " << office_tmpkey_info.relay << ", tempkey security_relay_value = " << office_tmpkey_info.security_relay;
    AK_LOG_INFO << "NewOfficeGetResponseDoorNum accessgroup relay_value = " << access_group_relay_value << ", accessgroup security_relay_value = " << access_group_security_relay_value;
    AK_LOG_INFO << "NewOfficeGetResponseDoorNum normal_relay_value = " << normal_relay_value << ", normal_security_relay_value = " << normal_security_relay_value;

    Snprintf(tmpkey_info_.relay, sizeof(tmpkey_info_.relay), door_num.c_str());
    Snprintf(tmpkey_info_.security_relay, sizeof(tmpkey_info_.security_relay), security_door_num.c_str());
    return;
}

// 校验office高级功能,限制用户是否能创建tempkey
// 办公高级功能过期了,用户创建的tempkey可以使用;
// 高级功能过期之后续费了,用户创建的tempkey不能使用
bool RequestTmpkeyCheck::NewOfficeUnderFeaturePlanRestrict(const OfficeTempKeyInfo& office_tmpkey_info)
{
    // 获取tmepkey创建者信息
    NewOfficeGetCreatorInfo(office_tmpkey_info);

    if (office_tmpkey_info.creator_type != OfficeTempKeyCreatorType::ENDUSER)
    {
        AK_LOG_INFO << "NewOfficeInFeaturePlanRestrict, creator is not enduer, tmpkey code = " << tmpkey_info_.tmpkey;
        return false;
    }
    
    if (UnderFeaturePlanRestrict())
    {
        AK_LOG_INFO << "NewOfficeInFeaturePlanRestrict, UnderFeaturePlanRestrict, tmpkey code = " << tmpkey_info_.tmpkey << ", account = " << per_account_.account;
        return true;
    }

    return false;
}

// 校验tempkey使用次数
bool RequestTmpkeyCheck::WithinAllowedTimes(const OfficeTempKeyInfo& office_tmpkey_info)
{
    // 每天和每周的没有使用次数限制
    if (office_tmpkey_info.scheduler_type == SchedType::DAILY_SCHED || office_tmpkey_info.scheduler_type == SchedType::WEEKLY_SCHED)
    {
        return true;
    }
    
    return office_tmpkey_info.access_times < office_tmpkey_info.allowed_times;
}

bool RequestTmpkeyCheck::UnderFeaturePlanRestrict()
{
    // 判断office高级功能是否过期, 高级功能未过期, 判断是否限制了用户tmpkey的生成
    if (!office_info_.IsExpire() && office_info_.CheckFeature(OfficeInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_TMPKEY))
    {
        // 不允许用户创建tmpkey
        if (per_account_.is_show_tmpkey == 0)
        {
            AK_LOG_INFO << "UnderFeaturePlanRestrict not allow enduser create TmpKey, TmpKey = " << tmpkey_info_.tmpkey <<  ", MngAccountID = " << dev_.project_mng_id << ", node = " << per_account_.account;
            return true;
        }
    }

    return false;
}

int RequestTmpkeyCheck::IBuildReplyMsg(std::string &msg, uint16_t &msg_id)
{
    msg_id = MSG_TO_DEVICE_CHECK_TMP_KEY_ACK;
    GetMsgBuildHandleInstance()->BuildCheckTmpKeyAckMsg(tmpkey_info_, msg);
    return 0;
}
