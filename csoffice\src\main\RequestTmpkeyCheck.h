#ifndef __REQUEST_TMPKEY_CHECK__
#define __REQUEST_TMPKEY_CHECK__

#include <string>
#include <memory>
#include <stdio.h>
#include <stdlib.h>
#include "MsgBuild.h"
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeAccessGroupPersonnel.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"

class RequestTmpkeyCheck: public IBase
{
public:
    RequestTmpkeyCheck() = default;
    ~RequestTmpkeyCheck() = default;

    int IParseXml(char *msg) override;
    int IControl() override;
    int IBuildReplyMsg(std::string &msg, uint16_t &msg_id) override;
    
    IBasePtr NewInstance() {return std::make_shared<RequestTmpkeyCheck>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    std::string func_name_ = "RequestTmpkeyCheck";
    ResidentDev dev_;
    MacInfo mac_info_;
    OfficeInfo office_info_;
    OfficeAccount per_account_;
    SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY tmpkey_info_;

    void OldOfficeTmpKeyCheck();
    int OldOfficeCheckPersonalDev();
    int OldOfficeCheckPublicDev();
    int OldOfficeCheckPublicUnitDev();
    void OldOfficeCheckFeaturePlan();
    void OldOfficeCheckAccessGroup();

    void NewOfficeTmpKeyCheck();
    void NewOfficeGetResponseDoorNum(const OfficeTempKeyInfo& office_tmpkey_info);
    void NewOfficeGetCreatorInfo(const OfficeTempKeyInfo& office_tmpkey_info);
    bool NewOfficeUnderFeaturePlanRestrict(const OfficeTempKeyInfo& office_tmpkey_info);
    void NewOfficeGetLiftFloorNum(const OfficeTempKeyInfo& office_tmpkey_info);
    
    bool UnderFeaturePlanRestrict();
    inline bool WithinAllowedTimes(const OfficeTempKeyInfo& office_tmpkey_info);

};


#endif
