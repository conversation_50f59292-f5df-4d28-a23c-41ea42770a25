#ifndef REQ_VOICE_DEL_H_
#define REQ_VOICE_DEL_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReqVoiceDel: public IBase
{
public:
    ReqVoiceDel(){}
    ~ReqVoiceDel() = default;


    int IParseXml(char *msg);
    int IControl();
    int IBuildReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushNotify();
    int IToRouteMsg();

    IBasePtr NewInstance() {return std::make_shared<ReqVoiceDel>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    
public:
    std::string func_name_ = "ReqVoiceDel";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_VOICE_MSG_URL del_msg_;
};

#endif
