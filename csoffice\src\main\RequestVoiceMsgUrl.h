#ifndef REQ_VOICE_URL_H_
#define REQ_VOICE_URL_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReqVoiceUrl: public IBase
{
public:
    ReqVoiceUrl(){}
    ~ReqVoiceUrl() = default;


    int IParseXml(char *msg);
    int IControl();
    int IPushNotify();
    int IToRouteMsg();

    int ReplyMsg();
    IBasePtr NewInstance() {return std::make_shared<ReqVoiceUrl>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    
public:
    std::string func_name_ = "ReqVoiceUrl";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg_;
};

#endif
