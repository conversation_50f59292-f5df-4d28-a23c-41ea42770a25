#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RequestWeatherInfoMsg.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "CachePool.h"
#include "json/json.h"
#include "SafeCacheConn.h"
#include "ProjectUserManage.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/PersonalAccountSingleInfo.h"


__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqWeatherInfo>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_WEATHER);
};


int ReqWeatherInfo::IParseXml(char *msg)
{
    memset(&req_weather_, 0, sizeof(req_weather_));
    CMsgParseHandle::ParseRequestWeatherMsg(msg, &req_weather_);
    AK_LOG_INFO << "request weather, manual_update : " << req_weather_.manual_update;
    return 0;
}

int ReqWeatherInfo::IControl()
{
    ResidentDev conn_dev = GetDevicesClient();  
    // 获取设备的国家-城市-地区
    if (0 != GetDevRegionInfo(conn_dev, weather_info_))
    {
        AK_LOG_WARN << "GetDevRegionInfo error";
        return -1;
    }

    // 先查缓存
    SafeCacheConn redis(g_redis_db_weather);
    if (redis.isConnect())
    {
        //  redis key 格式 : 国家-城市-地区
        char weather_redis_key[256];
        ::snprintf(weather_redis_key, sizeof(weather_redis_key), "%s-%s-%s", weather_info_.country, weather_info_.states, weather_info_.city);
        if (redis.isExists(weather_redis_key))
        {
            std::vector<std::string> weather_vec;
            std::string weather_value = redis.get(weather_redis_key);
            AK_LOG_INFO << "ReqWeatherInfo get key = " << weather_redis_key << ", value = " << weather_value;
            SplitString(weather_value, "!", weather_vec);
    
            // weather!temperature!humidity,判断weather_vec长度为3,防止取下标出现段错误
            if (weather_vec.size() == 3)
            {
                ::snprintf(weather_info_.weather, sizeof(weather_info_.weather), "%s", weather_vec[0].c_str());
                ::snprintf(weather_info_.temperature, sizeof(weather_info_.temperature), "%s", weather_vec[1].c_str());
                ::snprintf(weather_info_.humidity, sizeof(weather_info_.humidity), "%s", weather_vec[2].c_str());
    
                ReplyMsg();
                return 0;
            }
        }
    }
    
    // 未查到缓存, 请求家居的天气接口
    IPushThirdNotify();
    return 0;
}


int ReqWeatherInfo::ReplyMsg()
{
    std::string msg;
    GetMsgBuildHandleInstance()->BuildWeatherInfoMsg(weather_info_, msg);
    ReplyDevMsg(msg, MSG_TO_DEVICE_REPORT_WEATHER_MSG);
    return 0;
}

int ReqWeatherInfo::IPushNotify()
{
    return 0;
}

int ReqWeatherInfo::IToRouteMsg()
{   
    return 0;
}


int ReqWeatherInfo::IPushThirdNotify()
{
    uint32_t msg_id = LINKER_MSG_TYPE_WEATHER;
    std::string key = weather_info_.mac;
    Json::Value item;
    Json::FastWriter w;
    item["mac"] = weather_info_.mac;
    item["city"] = weather_info_.city;
    item["state_province"] = weather_info_.states;
    item["country"] = weather_info_.country;
    std::string msg = w.write(item);           
    PushThirdNotifyMsg(msg, msg_id, key);
    return 0;
}


int ReqWeatherInfo::GetDevRegionInfo(const ResidentDev& dev, SOCKET_MSG_DEV_WEATHER_INFO& weather_info)
{
    memset(&weather_info, 0, sizeof(weather_info));
    ::snprintf(weather_info.mac, sizeof(weather_info.mac), "%s", dev.mac);

    // 获取设备的国家-城市-地区
    OfficeInfo office_info(dev.project_mng_id); 
    ::snprintf(weather_info.city, sizeof(weather_info.city), "%s", office_info.City().c_str());
    ::snprintf(weather_info.states, sizeof(weather_info.states), "%s", office_info.States().c_str());
    ::snprintf(weather_info.country, sizeof(weather_info.country), "%s", office_info.Country().c_str());
    return 0;
}


