
#include "ResponseEmergencyCloseDoor.h"
#include "MsgParse.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AkcsCommonDef.h"
#include "json/json.h"
#include "util.h"
#include <string>
#include "DclientMsgSt.h"
#include "EmergencyMsgControl.h"
#include "dbinterface/PmEmergencyDoorLog.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ResponseEmergencyCloseDoor>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_RESPONSE_EMERGENCY_CLOSE_DOOR);
};

int ResponseEmergencyCloseDoor::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != CMsgParseHandle::ParseResponseEmergencyControlMsg(msg, &control_msg_))
    {
        AK_LOG_WARN <<  "ResponseEmergencyCloseDoor ParseResponseEmergencyControlMsg failed";
        return -1;
    }
    
    AK_LOG_INFO << "ParseResponseEmergencyControlMsg success, mac = " << conn_dev_.mac 
                << ", msg_uuid = " << control_msg_.msg_uuid 
                << ", relay = " << control_msg_.relay << ", security relay = " << control_msg_.security_relay;    

    return 0;
}

int ResponseEmergencyCloseDoor::IControl()
{
    //时间轮超时检测移出
    std::string key = GetEmergencyControlInstance()->GenerateKey(conn_dev_.mac, control_msg_.msg_uuid);
    GetEmergencyControlInstance()->RemoveEmergencyControlMsg(key);

    //数据库更新
    if (dbinterface::PmEmergencyDoorLog::UpdateDeviceRelayStatus(conn_dev_.uuid,control_msg_.msg_uuid,control_msg_.relay,control_msg_.security_relay) < 0)
    {
        AK_LOG_WARN << "ResponseEmergencyCloseDoor UpdateDeviceRelayStatus failed.";
        return -1;
    }
    return 0;
}
