#include "ResponseEmergencyOpenDoor.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ResponseEmergencyOpenDoor>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_RESPONSE_EMERGENCY_KEEP_OPEN_DOOR);
};

int ResponseEmergencyOpenDoor::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != CMsgParseHandle::ParseResponseEmergencyControlMsg(msg, &control_msg_))
    {
        AK_LOG_WARN << "ResponseEmergencyOpenDoor ParseResponseEmergencyControlMsg failed";
        return -1;
    }
    
    AK_LOG_INFO << "ParseResponseEmergencyControlMsg success, mac = " << conn_dev_.mac 
                << ", msg_uuid = " << control_msg_.msg_uuid << ", relay = " << control_msg_.relay 
                << ", security relay = " << control_msg_.security_relay;
    return 0;
}

int ResponseEmergencyOpenDoor::IControl()
{
    // 时间轮超时检测移出
    std::string key = GetEmergencyControlInstance()->GenerateKey(conn_dev_.mac, control_msg_.msg_uuid);
    GetEmergencyControlInstance()->RemoveEmergencyControlMsg(key);

    // 数据库更新
    if (dbinterface::PmEmergencyDoorLog::UpdateDeviceRelayStatus(conn_dev_.uuid, control_msg_.msg_uuid, control_msg_.relay, control_msg_.security_relay) < 0)
    {
        AK_LOG_WARN << "ResponseEmergencyOpenDoor UpdateDeviceRelayStatus failed.";
        return -1;
    }
    
    return 0;
}
