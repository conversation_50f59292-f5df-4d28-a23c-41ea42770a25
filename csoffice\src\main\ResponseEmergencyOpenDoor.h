#ifndef _RESPONSE_EMERGENCY_OPEN_DOOR_H_
#define _RESPONSE_EMERGENCY_OPEN_DOOR_H_

#include <string>
#include "util.h"
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"
#include "EmergencyMsgControl.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/resident/ResidentDevices.h"

class ResponseEmergencyOpenDoor: public IBase
{
public:
    ResponseEmergencyOpenDoor(){
    }
    ~ResponseEmergencyOpenDoor() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ResponseEmergencyOpenDoor>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    ResidentDev conn_dev_;
    std::string func_name_ = "ResponseEmergencyOpenDoor";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_EMERGENCY_CONTROL control_msg_;
};

#endif //_RESPONSE_EMERGENCY_OPEN_DOOR_H_
