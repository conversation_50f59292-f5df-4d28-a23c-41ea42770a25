#include "json/json.h"
#include "gid/SnowFlakeGid.h"
#include "Office2RouteMsg.h"
#include "util_virtual_door.h"
#include "ResponseLockDownDoor.h"
#include "dbinterface/OfficeMessage.h"
#include "msgparse/ParseResponseLockDown.hpp"
#include "dbinterface/new-office/LockDownControl.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ResponseLockDownDoor>();
    RegNewOfficeDevFunc(p, MSG_FROM_DEVICE_RESPONSE_LOCKDOWN_DOOR);
};

int ResponseLockDownDoor::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != akcs_msgparse::ParseResponseLockDownMsg(msg, control_msg_))
    {
        AK_LOG_WARN << "ResponseLockDownDoor ParseResponseLockDownMsg failed, mac = " << conn_dev_.mac;
        return -1;
    }
    AK_LOG_INFO << "ParseResponseLockDownMsg success, mac = " << conn_dev_.mac  << ", relay = " << control_msg_.relay << ", security relay = " << control_msg_.security_relay;
    return 0;
}

// lockdown_relay_status: 设备上报lockdown结果
void ResponseLockDownDoor::UpdateDoorLockDownStatus(DoorRelayType relay_type)
{
    // 获取lockdown模式
    int lockdown_mode = GetLockDownMode(control_msg_.mode);

    // 获取设备上报的lockdown结果
    std::string report_lockdown_relay_status = GetLockDownRelayStatus(relay_type);
    AK_LOG_INFO << "UpdateDoorLockDownStatus, lockdown_mode = " << lockdown_mode << ", relay_type = " << (int)relay_type 
                << ", report_lockdown_relay_status = " << report_lockdown_relay_status;

    // 遍历设备上报的lockdown结果
    for (unsigned int index = 0; index < report_lockdown_relay_status.size(); ++index)
    {
        int relay_index = index + 1; // 1 2 3 4
        std::string lockdown_status = std::string(1, report_lockdown_relay_status[index]);
        AK_LOG_INFO << "UpdateDoorLockDownStatus, relay_index = " << relay_index << ", relay_index_value = " << lockdown_status;

        // 如果lockdown_status为 - ，则跳过不处理
        if (strcmp(lockdown_status.c_str(), "-") == 0)
        {
            AK_LOG_INFO << "UpdateDoorLockDownStatus, relay_index_value is -, skip handle";
            continue;
        }
        
        // 获取lockdown relay对应的door
        std::string controlled_relay = GetControlledRelayByRelayIndex(relay_index);
        
        // 获取lockdown结果
        int is_lockdown_success = LockDownSuccess(lockdown_status);
        
        AK_LOG_INFO << "UpdateDoorLockDownStatus, is_lockdown_success = " << is_lockdown_success << ", controlled_relay = " << controlled_relay;

        if (is_lockdown_success)
        {
            // 更新Door的IsLocked字段
            dbinterface::DevicesDoorList::UpdateDoorLockDownMode(conn_dev_.uuid, relay_type, controlled_relay, lockdown_mode);
            
            // 进行Lockdown通知
            LockDownMessageNotify(relay_type, controlled_relay);
        }

        // 更新LockDownDoorList表的Status字段
        if (relay_type == DoorRelayType::RELAY)
        {
            dbinterface::LockDownControl::UpdateRelayLockDownStatus(control_msg_.uuid, conn_dev_.uuid, relay_index, is_lockdown_success);
        }
        else if (relay_type == DoorRelayType::SECURITY_RELAY)
        {
            dbinterface::LockDownControl::UpdateSecurityRelayLockDownStatus(control_msg_.uuid, conn_dev_.uuid, relay_index, is_lockdown_success);
        }
    }
    return;
}

void ResponseLockDownDoor::LockDownMessageNotify(DoorRelayType relay_type, const std::string& controlled_relay)
{   
    DevicesDoorInfo devices_door_info;
    GetDoorInfoByControlledRelay(relay_type, controlled_relay, devices_door_info);

    // 判断是否为私有Door
    std::string private_door_office_company_uuid;
    if (!IsOfficeCompanyPrivateDoor(devices_door_info.uuid, private_door_office_company_uuid)) 
    {
        AK_LOG_INFO << "Not office company private door, not notify admin app, mac = " 
                    << conn_dev_.mac << ", door_uuid = " << devices_door_info.uuid << ", door_type = " << (int)devices_door_info.relay_type
                    << ", controlled_relay = " << controlled_relay << ", door_name = " << devices_door_info.name;
        return;
    }

    // message通知content, location不能用缓存的，要用数据库实时的
    std::string content = GetLockDownMessageContent(db_dev_->location, devices_door_info.name);

    // 插入lockdown消息
    std::string message_uuid;
    dbinterface::OfficeMessage::InsertLockDownNotifyMessage(control_msg_.uuid, title_, content, message_type_, message_uuid);

    // 获取公司下的所有admin app
    OfficeAdminInfoList office_admin_info_list;
    dbinterface::OfficeAdmin::GetOfficeAdminInfoListByCompanyUUID(private_door_office_company_uuid, office_admin_info_list);

    // 插入lockdown消息的receiver
    for (const auto& admin_info : office_admin_info_list)
    {
        // 插入OfficeMessage表
        dbinterface::OfficeMessage::InsertLockDownMessageReceiver(admin_info.personal_account_uuid, message_uuid);
    }
    
    // 生成lockdown通知json 
    std::string notify_msg = GenerateLockDownNotifyJson(project::OFFICE_NEW, message_uuid);

    // 发送消息
    COffice2RouteMsg::SendGeneralData(project::OFFICE_NEW, AKCS_M2R_P2P_SEND_LOCKDOWN_NOTIFY_MSG, notify_msg.data(), notify_msg.size());
    return;
}

int ResponseLockDownDoor::IControl()
{
    // 获取lockdown模式信息
    GetLockDownModeInfo();

    // 获取设备实时信息
    dbinterface::OfficeDevices::GetMacDev(conn_dev_.mac, db_dev_);

    // 获取lockdown控制信息
    dbinterface::LockDownControl::GetLockDownControlInfoByUUID(control_msg_.uuid, lockdown_control_info_);

    // 获取设备company下的door list
    dbinterface::OfficeCompanyDoorList::GetDeviceOfficeCompanyDoorList(conn_dev_.uuid, office_company_door_list_info_list_);

    // 获取设备下的door list
    dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(conn_dev_.uuid, devices_door_info_list_);

    // 更新relay lockdown状态
    UpdateDoorLockDownStatus(DoorRelayType::RELAY);

    // 更新security relay lockdown状态
    UpdateDoorLockDownStatus(DoorRelayType::SECURITY_RELAY);
    return 0;
}

bool ResponseLockDownDoor::LockDownSuccess(const std::string& stuats)
{
    return stuats == "1";
}

// 获取message content : 设备名称 - 门名称 
std::string ResponseLockDownDoor::GetLockDownMessageContent(const std::string& device_name, const std::string& door_name)
{
    return device_name + " - " + door_name;
}

// 获取relay对应的door
void ResponseLockDownDoor::GetDoorInfoByControlledRelay(DoorRelayType relay_type, const std::string& controlled_relay, DevicesDoorInfo& devices_door_info)
{
    for (const auto& door_info : devices_door_info_list_)
    {
        if (door_info.relay_type == relay_type && strcmp(door_info.controlled_relay, controlled_relay.c_str()) == 0)
        {
            devices_door_info = door_info;
            break;
        }
    }
}   

bool ResponseLockDownDoor::IsOfficeCompanyPrivateDoor(const std::string& devices_door_list_uuid, std::string& private_door_office_company_uuid)
{
    for (const auto& door_info : office_company_door_list_info_list_)
    {
        if (strcmp(door_info.devices_door_list_uuid, devices_door_list_uuid.c_str()) == 0 
            && door_info.type == OfficeCompanyDoorListInfoType::PRIVATE)
        {
            private_door_office_company_uuid = door_info.office_company_uuid;
            return true;
        }
    }
    return false;
}

// 获取lockdown模式信息
void ResponseLockDownDoor::GetLockDownModeInfo()
{
    if (strcmp(control_msg_.mode, "ON") == 0)
    {
        title_ = "Lockdown On";
        message_type_ = MessageContentType::LOCKDOWN_ON_MSG;
    }
    else if (strcmp(control_msg_.mode, "OFF") == 0)
    {
        title_ = "Lockdown Off";
        message_type_ = MessageContentType::LOCKDOWN_OFF_MSG;
    }
    return;
}

// 获取lockdown relay
std::string ResponseLockDownDoor::GetLockDownRelayStatus(DoorRelayType relay_type)
{
    std::string lockdown_relay;
    if (relay_type == DoorRelayType::RELAY)
    {
        lockdown_relay = control_msg_.relay;
    }
    else if (relay_type == DoorRelayType::SECURITY_RELAY)
    {
        lockdown_relay = control_msg_.security_relay;
    }
    return lockdown_relay;
}

std::string ResponseLockDownDoor::GenerateLockDownNotifyJson(project::PROJECT_TYPE type, const string& message_uuid)
{
    std::string notify_msg;
    Json::Value data;
    Json::Value root;
    Json::FastWriter writer;
    data["project_type"] = std::to_string(type);
    data["message_uuid"] = message_uuid;

    root["msg_type"] = "newoffice_send_message";
    root["trace_id"] = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    root["timestamp"] = (long long)(time(nullptr) * 1000000);
    root["data"] = data;

    notify_msg = writer.write(root);
    return notify_msg;
}