#ifndef __RESPONSE_LOCKDOWN_DOOR_H__
#define __RESPONSE_LOCKDOWN_DOOR_H__

#include <string>
#include "util.h"
#include "MsgParse.h"
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/LockDownControl.h"
#include "dbinterface/new-office/OfficeCompanyDoorList.h"

class ResponseLockDownDoor : public IBase
{
public:
    ResponseLockDownDoor() {}
    ~ResponseLockDownDoor() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ResponseLockDownDoor>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    void GetLockDownModeInfo();
    bool LockDownSuccess(const std::string& stuats);
    std::string GetLockDownRelayStatus(DoorRelayType relay_type);
    bool IsOfficeCompanyPrivateDoor(const std::string& devices_door_list_uuid, std::string& private_door_office_company_uuid);
    void LockDownMessageNotify(DoorRelayType relay_type, const std::string& controlled_relay);
    void UpdateDoorLockDownStatus(DoorRelayType relay_type);
    std::string GenerateLockDownNotifyJson(project::PROJECT_TYPE type, const string& message_uuid);
    std::string GetLockDownMessageContent(const std::string& device_name, const std::string& door_name);
    void GetDoorInfoByControlledRelay(DoorRelayType relay_type, const std::string& controlled_relay, DevicesDoorInfo& devices_door_info);
private:
    std::string title_;
    OfficeDevPtr db_dev_;
    ResidentDev conn_dev_;
    MessageContentType message_type_;
    DevicesDoorInfoList devices_door_info_list_;
    SOCKET_MSG_RESPONSE_LOCKDOWN control_msg_;
    LockDownControlInfo lockdown_control_info_;
    std::string func_name_ = "ResponseLockDownDoor";
    OfficeCompanyDoorListInfoList office_company_door_list_info_list_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
};

#endif //_RESPONSE_EMERGENCY_OPEN_DOOR_H_
