#include "util.h"
#include "util_time.h"
#include "MsgBuild.h"
#include "OfficeInit.h"
#include "SafeCacheConn.h"
#include "RequestAntiPassbackOpen.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"
#include "dbinterface/ProjectUserManage.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

void AntiPassbackBase::ExecuteCommonCheck(const std::string& initiator_company_uuid)
{   
    if (!ReportMsgValid())
    {
        AK_LOG_INFO << "ReportMsg InValid, mac = " << dev_.mac << ", initiator = " << req_msg_.personnel_id;
        return;
    }
    
    if (!GetAntiPassbackDoor())
    {
        AK_LOG_INFO << "GetAntiPassBackDoor Falied, mac = " << dev_.mac << ", initiator = " << req_msg_.personnel_id;
        return;
    }

    if (!GetAntiPassbackArea())
    {
        AK_LOG_INFO << "GetAntiPassbackArea Falied, mac = " << dev_.mac << ", initiator = " << req_msg_.personnel_id;
        return;
    }

    if (!RestrictSwitchOn())
    {
        status_ = AntiPassbackStatus::AREA_SWITCH_OFF;
        AK_LOG_INFO << "ReqAntiPassbackOpen area restrict switch off, opendoor success mac = " << dev_.mac << ", initiator = " << req_msg_.personnel_id;
    }

    if (!InSameCompany(initiator_company_uuid))
    {
        status_ = AntiPassbackStatus::NOT_IN_SAME_COMPANY;
        AK_LOG_INFO << "ReqAntiPassbackOpen initiator and door not in same company, "
        << "initiator company = " << initiator_company_uuid << ", door company = " << area_info_.office_company_uuid
        << " mac = " << dev_.mac << ", initiator = " << req_msg_.personnel_id;
    }
    
    if (status_ == AntiPassbackStatus::FAILURE && !InRestrictTime())
    {
        status_ = AntiPassbackStatus::OUTSIDE_RESTRICT_TIME;
        AK_LOG_INFO << "ReqAntiPassbackOpen area not in restrict time, opendoor success mac = " << dev_.mac << ", initiator = " << req_msg_.personnel_id;
    }

    if (status_ == AntiPassbackStatus::FAILURE && ExecuteStatusCheck() == true)
    {
        status_ = AntiPassbackStatus::SUCCESS;
    }

    TransferResult();
    
    return;
}

bool AntiPassbackBase::ReportMsgValid()
{
    if (strlen(req_msg_.personnel_id) == 0)
    {
        AK_LOG_WARN << "handle anti passback personnel_id is null, mac = " << dev_.mac;
        return false;
    }

    // 不是反潜回开门方式
    if (req_msg_.access_mode != AntiPassbackAccessMode::ENTRY && req_msg_.access_mode != AntiPassbackAccessMode::EXIT)
    {
        AK_LOG_WARN << "request access mode do not need anti passback judge, mac = " << dev_.mac << ", access_mode = " << int(req_msg_.access_mode);
        return false;
    }

    return true;
}

void AntiPassbackBase::TransferResult()
{
    if (status_ == AntiPassbackStatus::SUCCESS)
    {
        resp_msg_.result = AntiPassbackResult::SUCCESS;
    }
    else if (status_ == AntiPassbackStatus::AREA_SWITCH_OFF)
    {
        resp_msg_.result = AntiPassbackResult::SUCCESS;
        resp_msg_.access_mode = AntiPassbackAccessMode::NORMAL;
    }
    else if (status_ == AntiPassbackStatus::OUTSIDE_RESTRICT_TIME)
    {
        resp_msg_.result = AntiPassbackResult::SUCCESS;
        resp_msg_.access_mode = AntiPassbackAccessMode::NORMAL;
    }
    else if (status_ == AntiPassbackStatus::NOT_IN_SAME_COMPANY)
    {
        resp_msg_.result = AntiPassbackResult::SUCCESS;
        resp_msg_.access_mode = AntiPassbackAccessMode::NORMAL;
    }
    else if (status_ == AntiPassbackStatus::FAILURE)
    {
        if (area_info_.restriction_type == AntiPassbackRestrictionType::LOG_VIOLATIONS_ONLY)
        {
            resp_msg_.result = AntiPassbackResult::SUCCESS;
            if (req_msg_.access_mode == AntiPassbackAccessMode::ENTRY)
            {
                resp_msg_.access_mode = AntiPassbackAccessMode::ENTRY_VIOLATION;
            }
            else if (req_msg_.access_mode == AntiPassbackAccessMode::EXIT)
            {
                resp_msg_.access_mode = AntiPassbackAccessMode::EXIT_VIOLATION;
            }
            AK_LOG_INFO << "TransferAccessMode, only record log mode, initiator = " << req_msg_.personnel_id;
        }
        else
        {
            resp_msg_.access_mode = req_msg_.access_mode;
            AK_LOG_INFO << "TransferAccessMode, deny access mode, initiator = " << req_msg_.personnel_id;
        }
    }
    
    return;
}

void AntiPassbackBase::BuildCommonBlockInfo()
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::ProjectUserManage::GetServerTag(), uuid);
    blocked_personnel_.is_blocked = 1;
    blocked_personnel_.block_time = area_info_.restriction_timeout;
    blocked_personnel_.initiator_type = AntiPassbackInitiatorType::TEMPKEY;
    Snprintf(blocked_personnel_.uuid, sizeof(blocked_personnel_.uuid), uuid.c_str());
    Snprintf(blocked_personnel_.initiator, sizeof(blocked_personnel_.initiator), req_msg_.personnel_id);
    Snprintf(blocked_personnel_.anti_passback_area_uuid, sizeof(blocked_personnel_.anti_passback_area_uuid), area_info_.uuid);

    if (anti_passback_door_.door_type == AntiPassbackDoorType::ENTRY)
    {
        blocked_personnel_.reason = AreaRestrictionBlockedReason::ENTRY;
    }
    else if (anti_passback_door_.door_type == AntiPassbackDoorType::EXIT)
    {
        blocked_personnel_.reason = AreaRestrictionBlockedReason::EXIT;
    }

    return;
}

void AntiPassbackBase::GetRequestRelayInfo(AntiPassbackRelayType& relay_type, int& relay_num)
{
    if (req_msg_.relay > 0)
    {
        relay_num = req_msg_.relay;
        relay_type = AntiPassbackRelayType::RELAY;
    }
    else if (req_msg_.security_relay > 0)
    {
        relay_num = req_msg_.security_relay;
        relay_type = AntiPassbackRelayType::SECURITY_RELAY;
    }

    return;
}

bool AntiPassbackBase::GetAntiPassbackDoor()
{
    int relay_num = 0;
    AntiPassbackRelayType relay_type;
    GetRequestRelayInfo(relay_type, relay_num);

    if (0 != dbinterface::AntiPassBackDoor::GetAntiPassBackDoorInfo(dev_.uuid, relay_type, relay_num, anti_passback_door_))
    {
        return false;
    }
    
    return true;
}

bool AntiPassbackBase::GetAntiPassbackArea()
{
    if (0 != dbinterface::AntiPassbackArea::GetAntiPassbackAreaByUUID(anti_passback_door_.area_uuid, area_info_))
    {
        return false;
    }
    return true;
}

bool AntiPassbackBase::RestrictSwitchOn()
{
    return area_info_.enable == 1;
}

bool AntiPassbackBase::InRestrictTime()
{
    return dbinterface::AntiPassbackArea::InRestrictionTime(area_info_, office_info_.TimeZone(), g_time_zone_DST);
}

bool AntiPassbackBase::InSameCompany(const std::string& initiator_company_uuid)
{
    // admin创建的反潜回区域,判断initiator和door是否在同个company下
    if (area_info_.creator_type == AntiPassbackAreaCreatorType::ADMIN)
    {
        return strcmp(area_info_.office_company_uuid, initiator_company_uuid.c_str()) == 0;
    }
    return true;
}

bool AntiPassbackBase::ExecuteStatusCheck()
{
    if ((req_msg_.access_mode == AntiPassbackAccessMode::ENTRY && CAntiPassbackCache::AddEntrySet(area_info_, req_msg_.personnel_id))
    || (req_msg_.access_mode == AntiPassbackAccessMode::EXIT && CAntiPassbackCache::AddExitSet(area_info_, req_msg_.personnel_id)) )
    {
        return true;
    }
    
    return false;
}

AntiPassbackAreaInfo AntiPassbackBase::AreaInfo()
{
    return area_info_;
}

std::string AntiPassbackBase::AreaEntryKey(const std::string& area_uuid)
{
    return area_uuid + ":Entry";
}

std::string AntiPassbackBase::AreaExitKey(const std::string& area_uuid)
{
    return area_uuid + ":Exit";
}

void AntiPassbackBase::ReplyDevMsg()
{
    std::string reply_msg;
    uint16_t msg_id = MSG_TO_DEVICE_RESPONSE_ANTIPASSBACK_OPEN_DOOR;
    
    GetMsgBuildHandleInstance()->BuildAntiPassbackRespMsg(resp_msg_, reply_msg);
    
    FactoryReplyDevMsg(dev_, reply_msg, msg_id, MsgEncryptType::TYEP_MAC_ENCRYPT);
    return;
}
