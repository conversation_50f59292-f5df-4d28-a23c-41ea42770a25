#ifndef __ANTI_PASSBACK_BASE_H__
#define __ANTI_PASSBACK_BASE_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/UUID.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/BlockedPersonnel.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"


enum class AntiPassbackStatus
{
    SUCCESS = 0,
    FAILURE = 1,
    AREA_SWITCH_OFF = 2,        // 反潜回开关关闭
    OUTSIDE_RESTRICT_TIME = 3,  // 在限制时间之外 
    NOT_IN_SAME_COMPANY = 4,    // door和initiator不在同个公司
};

class AntiPassbackBase
{
public:
    AntiPassbackBase(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg) 
                            : dev_(dev), office_info_(office_info), req_msg_(req_msg), resp_msg_(resp_msg) {}
                            
    static std::string AreaEntryKey(const std::string& area_uuid);
    static std::string AreaExitKey(const std::string& area_uuid);

protected:
    bool ReportMsgValid();
    void BuildCommonBlockInfo();
    bool ExecuteStatusCheck();
    void ReplyDevMsg();
    AntiPassbackAreaInfo AreaInfo();
    void ExecuteCommonCheck(const std::string& initiator_company_uuid);

private:
    bool GetAntiPassbackDoor();
    bool GetAntiPassbackArea();
    bool RestrictSwitchOn();
    bool InRestrictTime();
    bool InSameCompany(const std::string& initiator_company_uuid);
    AntiPassbackRelayType GetOpenRelayType();
    void GetRequestRelayInfo(AntiPassbackRelayType& relay_type, int& relay_num);
    void TransferResult();
    
protected:
    ResidentDev dev_;
    OfficeInfo office_info_;
    AntiPassbackAreaInfo area_info_;
    AntiPassBackDoorInfo anti_passback_door_;
    BlockedPersonnelInfo blocked_personnel_;

    SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN req_msg_;
    SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN resp_msg_;

    AntiPassbackStatus status_ = AntiPassbackStatus::FAILURE;
};


#endif
