#include "util.h"
#include "util_time.h"
#include "SafeCacheConn.h"
#include "AntiPassbackBase.h"
#include "AntiPassbackBlock.h"

void CAntiPassbackBlock::CheckBlockedPersonnelStatus()
{
    BlockedPersonnelInfoList blocked_personnel_list;
    dbinterface::BlockedPersonnel::GetBlockedPersonnelList(blocked_personnel_list);

    for (const auto& blocked_personnel : blocked_personnel_list)
    {
        if (0 == dbinterface::BlockedPersonnel::ReleaseBlockedPersonnelByUUID(blocked_personnel.uuid))
        {
            AK_LOG_INFO << "CheckBlockedPersonnelStatus ReleaseBlockedPersonnel initiator " << blocked_personnel.initiator << " success";
        }
    }

    return;
}
