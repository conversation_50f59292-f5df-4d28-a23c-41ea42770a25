#ifndef __ANTI_PASSBACK_BLOCK_H__
#define __ANTI_PASSBACK_BLOCK_H__

#include <ctime>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/BlockedPersonnel.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"


class CAntiPassbackBlock
{
public:
    // 检查block状态是否过期
    static void CheckBlockedPersonnelStatus();
};

#endif
