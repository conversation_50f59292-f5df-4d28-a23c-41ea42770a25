#include "util.h"
#include "util_time.h"
#include "AntiPassbackCache.h"
#include "AntiPassbackBase.h"

bool CAntiPassbackCache::AddEntrySet(const AntiPassbackAreaInfo& area_info, const std::string& initiator)
{
    std::string area_exit_key = AntiPassbackBase::AreaExitKey(area_info.uuid);
    std::string area_entry_key = AntiPassbackBase::AreaEntryKey(area_info.uuid);
    
    AK_LOG_INFO << "Add initiator = " << initiator << " to " << area_entry_key << " begin";
    
    SafeCacheConn redis(g_redis_db_antipassback);
    if (InitiatorInCache(redis, area_entry_key, area_info, initiator))
    {
        AK_LOG_INFO << "Add initiator " << initiator << " to " << area_entry_key << " failed, block this initiator = " << initiator;
        return false;
    }

    // 若人员在exit列表中,进行删除
    RemoveInitiatorFromCache(redis, area_exit_key, area_info, initiator);

    // 清除blocked的记录
    dbinterface::BlockedPersonnel::ReleaseBlockedPersonnelByInitiator(area_info.uuid, initiator);
    
    // 加入到entry列表中
    AddInitiatorToCache(redis, area_entry_key, area_info, initiator);

    return true;
}

bool CAntiPassbackCache::AddExitSet(const AntiPassbackAreaInfo& area_info, const std::string& initiator)
{
    std::string area_exit_key = AntiPassbackBase::AreaExitKey(area_info.uuid);
    std::string area_entry_key = AntiPassbackBase::AreaEntryKey(area_info.uuid);

    AK_LOG_INFO << "Add initiator = " << initiator << " to " << area_exit_key << " begin";
    
    SafeCacheConn redis(g_redis_db_antipassback);

    // 必须先entry才能exit
    if (redis.zscore(area_entry_key, initiator).size() == 0)
    {
        AK_LOG_INFO << "Add initiator " << initiator << " to " << area_exit_key << " failed, must entry before exit, initiator = " << initiator;
        return false;
    }

    if (InitiatorInCache(redis, area_exit_key, area_info, initiator))
    {
        AK_LOG_INFO << "Add initiator " << initiator << " to " << area_exit_key << " failed, initiator = " << initiator;
        return false;
    }

    // 若人员在entry列表中,则进行删除
    RemoveInitiatorFromCache(redis, area_entry_key, area_info, initiator);

    // 清除blocked的记录
    dbinterface::BlockedPersonnel::ReleaseBlockedPersonnelByInitiator(area_info.uuid, initiator);

    // 加入到exit列表中
    AddInitiatorToCache(redis, area_exit_key, area_info, initiator);

    return true;
}

bool CAntiPassbackCache::InitiatorInCache(SafeCacheConn& redis, const std::string& cache_key, const AntiPassbackAreaInfo& area_info, const std::string& initiator)
{
    std::time_t time_now = std::time(nullptr);
    std::string last_record_time = redis.zscore(cache_key, initiator);

    if (last_record_time.empty())
    {
        AK_LOG_INFO << "Initiator not in Cache, cache_key = " << cache_key << ", initiator = " << initiator;
        return false;
    }

    uint64_t restriction_expire_time = RestrictionExpireTime(last_record_time, area_info.restriction_timeout);
    if (restriction_expire_time < static_cast<uint64_t>(time_now))
    {
        AK_LOG_INFO << "Initiator in Cache but restriction is expire, cache_key = " << cache_key << ", initiator = " << initiator << ", now = " << time_now << ", restriction_expire_time = " << restriction_expire_time;
        return false;
    }
    else
    {
        AK_LOG_INFO << "Initiator in Cache and in restriction time, cache_key = " << cache_key << ", initiator = " << initiator << ", now = " << time_now << ", restriction_expire_time = " << restriction_expire_time;
    }
    
    return true;
}

void CAntiPassbackCache::RemoveInitiatorFromCache(SafeCacheConn& redis, const std::string& cache_key, const AntiPassbackAreaInfo& area_info, const std::string& initiator)
{
    if (redis.zscore(cache_key, initiator).size() == 0)
    {
        AK_LOG_INFO << "Initiator " << initiator << " not in " << cache_key << ", not need to delete";
        return;
    }
    
    if (redis.zrem(cache_key, initiator) > 0)
    {

        AK_LOG_INFO << "Delete Initiator " << initiator << " from " << cache_key << " success";
    }
    else
    {
        AK_LOG_INFO << "Delete Initiator " << initiator << " from " << cache_key << " failed";
    }
    
    return;
}

// 记录人员进出的时间
void CAntiPassbackCache::AddInitiatorToCache(SafeCacheConn& redis, const std::string& cache_key, const AntiPassbackAreaInfo& area_info, const std::string& initiator)
{
    std::time_t time_now = std::time(nullptr);
    std::string record_time = redis.zscore(cache_key, initiator);

    long ret = redis.zadd(cache_key, std::to_string(time_now), initiator);
    if (ret > 0) // 不在cache中, 第一次添加 成功 返回值为 1 
    {
        AK_LOG_INFO << "first add initiator " << initiator << " to " << cache_key << " success, now = " << time_now;
    }
    else if (ret == 0)  // 在cache中, 更新进出时间 成功 返回值为 0
    {
        AK_LOG_INFO << "update initiator " << initiator << " cache_record_time success, cache_key = " << cache_key << ", last record time = " << record_time << ", now = " << time_now;
    }
    else
    {
        AK_LOG_INFO << "add or update initiator " << initiator << " to " << cache_key << " failed, now = " << time_now << ", last record time = " << record_time;
    }
    
    return;
}

uint64_t CAntiPassbackCache::RestrictionExpireTime(std::string& record_time, int restriction_minutes)
{
    return ATOULL(record_time.c_str()) + restriction_minutes * 60;
}
