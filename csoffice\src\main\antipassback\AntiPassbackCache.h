#ifndef __ANTI_PASSBACK_CACHE_H__
#define __ANTI_PASSBACK_CACHE_H__

#include <ctime>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AgentBase.h"
#include "SafeCacheConn.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/BlockedPersonnel.h"

class CAntiPassbackCache
{
public:
    static bool AddExitSet(const AntiPassbackAreaInfo& area_info, const std::string& initiator);
    static bool AddEntrySet(const AntiPassbackAreaInfo& area_info, const std::string& initiator);
    static bool InitiatorInCache(SafeCacheConn& redis, const std::string& cache_key, const AntiPassbackAreaInfo& area_info, const std::string& initiator);
    static void AddInitiatorToCache(SafeCacheConn& redis, const std::string& cache_key, const AntiPassbackAreaInfo& area_info, const std::string& initiator);
    static void RemoveInitiatorFromCache(SafeCacheConn& redis, const std::string& cache_key, const AntiPassbackAreaInfo& area_info, const std::string& initiator);
    static uint64_t RestrictionExpireTime(std::string& record_time, int restriction_minutes);
};

#endif
