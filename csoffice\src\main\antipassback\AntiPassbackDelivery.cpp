#include <ctime>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "util.h"
#include "util_time.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "OfficeInit.h"
#include "SafeCacheConn.h"
#include "AntiPassbackDelivery.h"
#include "RequestAntiPassbackOpen.h"
#include "dbinterface/Account.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"

DeliveryAntiPassback::DeliveryAntiPassback(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg) 
                                                    : AntiPassbackBase(dev, office_info, req_msg, resp_msg)
{
    int delivery_id = ATOI(&req_msg_.personnel_id[1]);
    dbinterface::OfficeDelivery::GetOfficeDeliveryByID(delivery_id, delivery_info_);
}

void DeliveryAntiPassback::Check() 
{
    ExecuteCommonCheck(delivery_info_.office_company_uuid);
    return;
}

void DeliveryAntiPassback::Block() 
{
    if (resp_msg_.result == AntiPassbackResult::FAILURE)
    {
        if (0 == dbinterface::BlockedPersonnel::GetBlockedPersonnelByDeliveryUUID(delivery_info_.uuid, area_info_.uuid, blocked_personnel_))
        {
            AK_LOG_INFO << "DeliveryAntiPassback Already Blocked, area = " << area_info_.name << ", delivery = " << delivery_info_.name;
        }
        else
        {
            BuildCommonBlockInfo();
            blocked_personnel_.initiator_type = AntiPassbackInitiatorType::DELIVERY;
            Snprintf(blocked_personnel_.display_id, sizeof(blocked_personnel_.display_id), "--");
            Snprintf(blocked_personnel_.office_delivery_uuid, sizeof(blocked_personnel_.office_delivery_uuid), delivery_info_.uuid);
            
            dbinterface::BlockedPersonnel::InsertBlockedPersonnel(blocked_personnel_);
        }
    }

    return;
}

void DeliveryAntiPassback::Reply()
{
    ReplyDevMsg();
    return;    
}