#include <ctime>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "util.h"
#include "util_time.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "OfficeInit.h"
#include "SafeCacheConn.h"
#include "AntiPassbackFactory.h"
#include "RequestAntiPassbackOpen.h"
#include "dbinterface/Account.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"

void AntiPassbackFactory::SetStrategy(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg)
{
    GetInitiatorType(req_msg);
    
    if (req_msg.initiator_type == AntiPassbackInitiatorType::PERSONNEL)
    {
        strategy_ = std::make_shared<PersonnelAntiPassback>(dev, office_info, req_msg, resp_msg);
    }
    else if (req_msg.initiator_type == AntiPassbackInitiatorType::DELIVERY)
    {
        strategy_ = std::make_shared<DeliveryAntiPassback>(dev, office_info, req_msg, resp_msg);
    }
    else if (req_msg.initiator_type == AntiPassbackInitiatorType::TEMPKEY)
    {
        strategy_ = std::make_shared<TempkeyAntiPassback>(dev, office_info, req_msg, resp_msg);
    }

    return;
}

void AntiPassbackFactory::ExecuteCheck() 
{
    if (strategy_)
    {    
        strategy_->Check();
    }
    
    return;
}

void AntiPassbackFactory::ExecuteBlock() 
{
    if (strategy_)
    {
        strategy_->Block();
    }

    return;
}

void AntiPassbackFactory::ReplyDevMsg() 
{
    if (strategy_)
    {
        strategy_->Reply();
    }

    return;
}

void AntiPassbackFactory::GetInitiatorType(SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg)
{
    if (req_msg.personnel_id[0] == UuidMode::MODE_DELIVERY)
    {
        req_msg.initiator_type = AntiPassbackInitiatorType::DELIVERY;
    }
    else if (req_msg.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY)
    {
        req_msg.initiator_type = AntiPassbackInitiatorType::TEMPKEY;
    }
    else
    {
        req_msg.initiator_type = AntiPassbackInitiatorType::PERSONNEL;
    }
    
    return;
}
