#ifndef __ANTI_PASSBACK_FACTORY_H__
#define __ANTI_PASSBACK_FACTORY_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/UUID.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/BlockedPersonnel.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"


class AntiPassbackStrategy 
{
public:    
    virtual ~AntiPassbackStrategy() = default;
    
    virtual void Check() = 0;
    virtual void Block() = 0;
    virtual void Reply() = 0;
};

class AntiPassbackFactory 
{
public:
    AntiPassbackFactory() = default;

    void SetStrategy(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg);
    void ExecuteCheck();
    void ExecuteBlock();
    void ReplyDevMsg();
private:
    void GetInitiatorType(SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg);

    std::shared_ptr<AntiPassbackStrategy> strategy_;
};


#endif
