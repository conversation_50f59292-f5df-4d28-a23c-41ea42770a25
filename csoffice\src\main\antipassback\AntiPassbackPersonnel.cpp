#include "AntiPassbackPersonnel.h"
#include "RequestAntiPassbackOpen.h"

PersonnelAntiPassback::PersonnelAntiPassback(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg) 
                                                     : AntiPassbackBase(dev, office_info, req_msg, resp_msg)
{
    if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(req_msg_.personnel_id, per_account_))
    {
        if (0 == dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(per_account_.uuid, office_personnel_info_))
        {
            OfficePersonnelGroupInfoList office_personnel_group_info_list;
            if (0 == dbinterface::OfficePersonnelGroup::GetOfficePersonnelGroupListByPersonalAccountUUID(per_account_.uuid, office_personnel_group_info_list))
            {
                for (const auto& office_personnel_group_info : office_personnel_group_info_list)
                {
                    OfficeGroupInfo office_group_info;
                    if (0 == dbinterface::OfficeGroup::GetOfficeGroupByUUID(office_personnel_group_info.office_group_uuid, office_group_info))
                    {
                        office_group_info_list_.push_back(office_group_info);
                    }
                }
            }
        }
    }
}

bool PersonnelAntiPassback::ImmuneAntipassback() 
{
    for (const auto& office_group_info : office_group_info_list_)
    {
        if (office_group_info.is_immune_antipassback)
        {
            AK_LOG_INFO << "personnel immune anti passback success, initiator = " << req_msg_.personnel_id << ", office group = " << office_group_info.name;
            return true;
        }
    }

    return false;
}

void PersonnelAntiPassback::Check() 
{
    if (strlen(per_account_.account) == 0)
    {
        AK_LOG_INFO << "PersonnelAntiPassback personnel_id invalid, personnel_id = " << req_msg_.personnel_id;
        return;        
    }

    // 判断是否免疫反潜回
    if (ImmuneAntipassback())
    {
        resp_msg_.result = AntiPassbackResult::SUCCESS;
        return;
    }

    ExecuteCommonCheck(office_personnel_info_.office_company_uuid);

    return;
}

void PersonnelAntiPassback::Block() 
{
    if (resp_msg_.result == AntiPassbackResult::FAILURE)
    {
        if (0 == dbinterface::BlockedPersonnel::GetBlockedPersonnelByPersonalAccountUUID(per_account_.uuid, AreaInfo().uuid, blocked_personnel_))
        {
            AK_LOG_INFO << "PersonnelAntiPassback Already Blocked, area = " << AreaInfo().name << ", personnel = " << req_msg_.personnel_id;
        }
        else
        {
            BuildCommonBlockInfo();
            blocked_personnel_.initiator_type = AntiPassbackInitiatorType::PERSONNEL;
            Snprintf(blocked_personnel_.display_id, sizeof(blocked_personnel_.display_id), office_personnel_info_.id_no);
            Snprintf(blocked_personnel_.personal_account_uuid, sizeof(blocked_personnel_.personal_account_uuid), per_account_.uuid);
            
            dbinterface::BlockedPersonnel::InsertBlockedPersonnel(blocked_personnel_);
        }
    }

    return;
}

void PersonnelAntiPassback::Reply()
{
    ReplyDevMsg();
    return;    
}
