#ifndef __ANTI_PASSBACK_PERSONNEL_H__
#define __ANTI_PASSBACK_PERSONNEL_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/UUID.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/BlockedPersonnel.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "AntiPassbackBase.h"
#include "AntiPassbackFactory.h"

class PersonnelAntiPassback : public AntiPassbackBase, public AntiPassbackStrategy
{
public:
    PersonnelAntiPassback(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg);
    
    void Check() override;
    void Block() override;
    void Reply() override;
    
private:   
    bool ImmuneAntipassback();
 
    OfficeAccount per_account_;
    OfficePersonnelInfo office_personnel_info_;
    OfficeGroupInfoList office_group_info_list_;
};


#endif
