#include <ctime>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "util.h"
#include "util_time.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "OfficeInit.h"
#include "SafeCacheConn.h"
#include "RequestAntiPassbackOpen.h"
#include "dbinterface/Account.h"
#include "doorlog/RecordActLog.h"
#include "doorlog/RecordOfficeLog.h"

TempkeyAntiPassback::TempkeyAntiPassback(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg) 
                                                 : AntiPassbackBase(dev, office_info, req_msg, resp_msg)
{
    dbinterface::OfficeTempKey::GetOfficeTempKeyInfo(req_msg_.personnel_id, dev.project_uuid, tempkey_info_);
}

void TempkeyAntiPassback::Check() 
{
    ExecuteCommonCheck(tempkey_info_.office_company_uuid);
    return;
}

void TempkeyAntiPassback::Block() 
{
    if (resp_msg_.result == AntiPassbackResult::FAILURE)
    {
        if (0 == dbinterface::BlockedPersonnel::GetBlockedPersonnelByOfficeTempKeyUUID(tempkey_info_.uuid, area_info_.uuid, blocked_personnel_))
        {
            AK_LOG_INFO << "TempkeyAntiPassback Already Blocked, area = " << area_info_.name << ", tempkey = " << req_msg_.personnel_id;
        }
        else
        {
            BuildCommonBlockInfo();
            blocked_personnel_.initiator_type = AntiPassbackInitiatorType::TEMPKEY;
            Snprintf(blocked_personnel_.display_id, sizeof(blocked_personnel_.display_id), "--");
            Snprintf(blocked_personnel_.office_tempkey_uuid, sizeof(blocked_personnel_.office_tempkey_uuid), tempkey_info_.uuid);
            
            dbinterface::BlockedPersonnel::InsertBlockedPersonnel(blocked_personnel_);
        }
    }

    return;
}

void TempkeyAntiPassback::Reply()
{
    ReplyDevMsg();
    return;    
}

