#ifndef __ANTI_PASSBACK_TEMPKEY_H__
#define __ANTI_PASSBACK_TEMPKEY_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/BlockedPersonnel.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "AntiPassbackBase.h"
#include "AntiPassbackFactory.h"



class TempkeyAntiPassback : public AntiPassbackBase, public AntiPassbackStrategy
{
public:
    TempkeyAntiPassback(const ResidentDev& dev, const OfficeInfo& office_info, SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN& req_msg, SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg);
    
    void Check() override;
    void Block() override;
    void Reply() override;
private:
    OfficeTempKeyInfo tempkey_info_;
};


#endif
