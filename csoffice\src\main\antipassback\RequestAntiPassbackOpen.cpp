#include <ctime>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "util.h"
#include "util_time.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "OfficeInit.h"
#include "SafeCacheConn.h"
#include "RequestAntiPassbackOpen.h"
#include "dbinterface/Account.h"
#include "doorlog/RecordActLog.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqAntiPassbackOpen>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_ANTIPASSBACK_OPEN_DOOR);
};

int ReqAntiPassbackOpen::IParseXml(char *msg)
{
    conn_dev_ = GetDevicesClient();
    if (CMsgParseHandle::ParseRequestAntipassbackOpenDoorMsg(msg, &req_msg_))
    {
        AK_LOG_INFO << "ParseRequestAntipassbackOpenDoorMsg failed, mac = " << conn_dev_.mac;
        return -1;
    }

    resp_msg_.access_mode = req_msg_.access_mode;
    resp_msg_.result = AntiPassbackResult::FAILURE;
    Snprintf(resp_msg_.mac, sizeof(resp_msg_.mac), conn_dev_.mac);
    Snprintf(resp_msg_.trace_id, sizeof(resp_msg_.trace_id), req_msg_.trace_id);
    Snprintf(resp_msg_.personnel_id, sizeof(resp_msg_.personnel_id), req_msg_.personnel_id);
    
    AK_LOG_INFO << "ReqAntiPassbackOpen mac = " << conn_dev_.mac << ", act_type = " << req_msg_.act_type << ", per_id = " << req_msg_.personnel_id
                << ", access_mode = " << (int)req_msg_.access_mode << ", relay = " << req_msg_.relay << ", security relay = " << req_msg_.security_relay;
    return 0;
}

int ReqAntiPassbackOpen::IControl()
{    
    OfficeInfo office_info = OfficeInfo(conn_dev_.project_uuid);

    AntiPassbackFactory factory;
    
    factory.SetStrategy(conn_dev_, office_info, req_msg_, resp_msg_);

    factory.ExecuteCheck();
    
    factory.ExecuteBlock();

    factory.ReplyDevMsg();
    
    return 0;
}
