#ifndef _REQ_ANTI_PASSBACK_OPENDOOR_H_
#define _REQ_ANTI_PASSBACK_OPENDOOR_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/UUID.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/BlockedPersonnel.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "AntiPassbackBase.h"
#include "AntiPassbackCache.h"
#include "AntiPassbackBlock.h"
#include "AntiPassbackFactory.h"
#include "AntiPassbackPersonnel.h"
#include "AntiPassbackDelivery.h"
#include "AntiPassbackTempkey.h"


// 反潜回开门判断
class ReqAntiPassbackOpen: public IBase
{
public:
    ReqAntiPassbackOpen(){}
    ~ReqAntiPassbackOpen() = default;

    int IParseXml(char *msg) override;
    int IControl() override;

    IBasePtr NewInstance() {return std::make_shared<ReqAntiPassbackOpen>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
public:
    std::string func_name_ = "ReqAntiPassbackOpen";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    ResidentDev conn_dev_;
    SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN req_msg_;
    SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN resp_msg_;
};

#endif
