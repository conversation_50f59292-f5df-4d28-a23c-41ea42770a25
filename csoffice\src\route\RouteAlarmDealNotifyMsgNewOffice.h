#ifndef _ROUTE_ALARM_DEAL_NEW_OFFICE_NOTIFY_MSG_H_
#define _ROUTE_ALARM_DEAL_NEW_OFFICE_NOTIFY_MSG_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Office2AppMsg.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/office/OfficeInfo.h"


class NewOfficeAlarmDealHandle;
class RouteAlarmDealNotifyMsgNewOffice : public IRouteBase
{
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;

    std::set<std::string>                       notifyed_dev_set_;

public:
    RouteAlarmDealNotifyMsgNewOffice() {}
    ~RouteAlarmDealNotifyMsgNewOffice() = default;

    int IControl(const std::unique_ptr<CAkcsPdu>& pdu);
    std::string FuncName() { return "RouteAlarmDealNotifyMsgNewOffice"; }
    IRouteBasePtr NewInstance() { return std::make_shared<RouteAlarmDealNotifyMsgNewOffice>(); }
};


#endif //_ROUTE_ALARM_DEAL_NOTIFY_MSG_H_
