#include "RouteAlarmDealReplyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "OfficePushClient.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "Office2RouteMsg.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"

__attribute__((constructor)) static void init()
{
    IRouteBasePtr p = std::make_shared<RouteAlarmDealReplyMsg>();
    RegRouteFunc(p, AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG);
};

int RouteAlarmDealReplyMsg::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
        << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
        << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    alarm_notify_msg_ = base_msg.p2palarmdealnotifymsg2();
    ProcessAlarmReply();
    return 0;
}

void RouteAlarmDealReplyMsg::ProcessAlarmReply()
{
    if (alarm_notify_msg_.target().empty())
    {
        AK_LOG_WARN << "target is empty";
        return;
    }

    // 获取告警信息
    int alarm_id = atoi(alarm_notify_msg_.alarm_id().c_str());
    if (dbinterface::Alarm::GetAlarm(alarm_id, &alarm_info_) != 0)
    {
        AK_LOG_WARN << "Get alarm info failed: alarm_id=" << alarm_id;
        return;
    }

    // 获取告警设备信息
    if (dbinterface::ResidentDevices::GetMacDev(alarm_info_.mac, resident_dev_) != 0)
    {
        AK_LOG_WARN << "GetMacDev failed: mac=" << alarm_info_.mac;
        return;
    }

    std::map<std::string, AKCS_DST> dst;
    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountByUUID(resident_dev_.project_uuid, project_info))
    {
        AK_LOG_WARN << "get project info failed. project uuid=" << resident_dev_.project_uuid;
        return;
    }

    std::string NodeTime = GetNodeNowDateTimeByTimeZoneStr(project_info.timezone, dst);

    // 初始化告警信息
    std::string DeviceName = resident_dev_.location;
    Snprintf(alarm_deal_info_.type, sizeof(alarm_deal_info_.type), "0");
    Snprintf(alarm_deal_info_.protocal, sizeof(alarm_deal_info_.protocal), "1.0");
    Snprintf(alarm_deal_info_.user, sizeof(alarm_deal_info_.user), alarm_notify_msg_.user().c_str());
    Snprintf(alarm_deal_info_.result, sizeof(alarm_deal_info_.result), alarm_notify_msg_.result().c_str());
    Snprintf(alarm_deal_info_.alarm_id, sizeof(alarm_deal_info_.alarm_id), alarm_notify_msg_.alarm_id().c_str());
    Snprintf(alarm_deal_info_.time, sizeof(alarm_deal_info_.time), NodeTime.c_str());
    Snprintf(alarm_deal_info_.device_name, sizeof(alarm_deal_info_.device_name), DeviceName.c_str());
    Snprintf(alarm_deal_info_.area_node, sizeof(alarm_deal_info_.area_node), alarm_notify_msg_.area_node().c_str());
    // Snprintf(alarm_deal_info_.title, sizeof(alarm_deal_info_.title), );

    alarm_deal_info_.alarm_code = alarm_info_.alarm_code;
    alarm_deal_info_.alarm_zone = alarm_info_.alarm_zone;
    alarm_deal_info_.alarm_location = alarm_info_.alarm_location;
    alarm_deal_info_.alarm_customize = alarm_info_.alarm_customize;

    // 获取项目信息
    office_info_ = OfficeInfo(alarm_info_.manager_account_id);
    if (office_info_.IsNew())
    {
        NewOfficeSendAlarmDealReply();
    }
    else
    {
        OldOfficeSendAlarmDealReply();
    }
}


// ============= 旧办公逻辑 =============

void RouteAlarmDealReplyMsg::OldOfficeSendAlarmDealReply()
{
    AlarmNotifyTargetType target_type = (AlarmNotifyTargetType)alarm_notify_msg_.target_type();
    if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT || 
        target_type == AlarmNotifyTargetType::DEV_OUTDOOR || 
        target_type == AlarmNotifyTargetType::DEV_INDOOR)
    {
        SendAlarmDealReplyToDev(alarm_notify_msg_.target());
    }
    else if (target_type == AlarmNotifyTargetType::APP_USER)
    {
        SendAlarmDealReplyToApp(alarm_notify_msg_.target());
    }
    else if (target_type == AlarmNotifyTargetType::APP_PM)
    {
        OldOfficeSendAlarmDealReplyToPMApp();
    }
    else
    {
        AK_LOG_WARN << "Unknown target type: " << (int)target_type;
    }
}

void RouteAlarmDealReplyMsg::OldOfficeSendAlarmDealReplyToPMApp()
{
    std::string pm_site = alarm_notify_msg_.target(); // pm实际站点

    // 获取PM APP在线推送信息
    OfflinePushUserInfo offline_user;
    OfflinePush::GetPmAlarmPushInfoByNode(alarm_notify_msg_.area_node(), offline_user);
    Snprintf(alarm_deal_info_.title, sizeof(alarm_deal_info_.title), offline_user.pm_online_title);
    Snprintf(alarm_deal_info_.site, sizeof(alarm_deal_info_.site), pm_site.c_str());

    // 发送消息
    SendAlarmDealReplyToApp(pm_site);
}

// ============= 新办公逻辑 =============

void RouteAlarmDealReplyMsg::NewOfficeSendAlarmDealReply()
{
    AlarmNotifyTargetType target_type = (AlarmNotifyTargetType)alarm_notify_msg_.target_type();
    if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT || 
        target_type == AlarmNotifyTargetType::DEV_INDOOR || 
        target_type == AlarmNotifyTargetType::DEV_OUTDOOR)
    {
        SendAlarmDealReplyToDev(alarm_notify_msg_.target());
    }
    else if (target_type == AlarmNotifyTargetType::APP_ADMIN)
    {
        SendAlarmDealReplyToApp(alarm_notify_msg_.target());
    }
    else
    {
        AK_LOG_WARN << "Unknown target type: " << (int)target_type;
    }
}

// ============== 公共逻辑 ==============

void RouteAlarmDealReplyMsg::BuildOfflinePushNotifyMsg(COffice2AppMsg& msg_sender)
{
    std::string target = alarm_notify_msg_.target();    // 实际站点
    msg_sender.InsertOfflineMsgKV("mac_sip", alarm_info_.mac);
    msg_sender.InsertOfflineMsgKV("device_name", resident_dev_.location);
    msg_sender.InsertOfflineMsgKV("alarm_msg", alarm_info_.alarm_type);
    msg_sender.InsertOfflineMsgKV("alarm_id", std::to_string(alarm_info_.id));
    msg_sender.InsertOfflineMsgKV("alarm_code", std::to_string(alarm_info_.alarm_code));
    msg_sender.InsertOfflineMsgKV("alarm_zone", std::to_string(alarm_info_.alarm_zone));
    msg_sender.InsertOfflineMsgKV("alarm_location", std::to_string(alarm_info_.alarm_location));
    msg_sender.InsertOfflineMsgKV("alarm_customize", std::to_string(alarm_info_.alarm_customize));
    msg_sender.InsertOfflineMsgKV("traceid", std::to_string(alarm_info_.trace_id));

    // 查询当前站点
    std::string main_site;
    OfficeAccount main_account;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(target, main_site);
    if (0 != dbinterface::OfficePersonalAccount::GetUidAccount(main_site, main_account))
    {
        AK_LOG_INFO << "BuildOfflinePushNotifyMsg GetUidAccount failed, main_site = " << main_site;
        return;
    }

    // 一人多套房增加 title_prefix 和 site字段
    if (dbinterface::ProjectUserManage::IsMultiSiteUser(main_account.user_info_uuid))
    {
        // 标题使用当前站点查询
        std::string title_prefix;
        OfflinePush::GetMultiSiteUserTitle(main_site, title_prefix);
        msg_sender.InsertOfflineMsgKV("title_prefix", title_prefix);
        msg_sender.InsertOfflineMsgKV("site", target);
    }

    return;
}

void RouteAlarmDealReplyMsg::SendAlarmDealReplyToDev(const std::string target_mac)
{
    // 获取目标设备信息
    ResidentDev dev;
    if (g_office_srv_ptr->GetDevSetting(target_mac, dev) < 0)
    {
        AK_LOG_WARN << "SendAlarmNotifyToDev GetDevSetting failed, mac = " << target_mac;
        return;
    }

    // 构造通知消息
    std::string notify_msg;
    GetMsgBuildHandleInstance()->BuildAlarmDealReplyMsg(alarm_deal_info_, notify_msg);

    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, notify_msg, MSG_ID, socket_message, MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT) != 0)
    {
        AK_LOG_WARN << "SendAlarmNotifyToDev BuildDclientMacEncMsg failed, mac=" << target_mac;
        return;
    }

    // 发送消息
    GetClientControlInstance()->SendTransferMsg(target_mac, dev.conn_type, socket_message.data, socket_message.size);
    AK_LOG_INFO << "SendAlarmDealReplyToDev mac = " << target_mac << ", conn_type = " << dev.conn_type;
}

void RouteAlarmDealReplyMsg::SendAlarmDealReplyToApp(const std::string target_user)
{
    COffice2AppMsg msg_sender;

    // 构造在线消息
    std::string notify_msg;
    GetMsgBuildHandleInstance()->BuildAlarmDealReplyMsg(alarm_deal_info_, notify_msg);

    // 构造离线消息
    BuildOfflinePushNotifyMsg(msg_sender);
    AK_LOG_INFO << "SendAlarmDealReplyToApp, account = " << target_user;

    //消息发送给csmain
    msg_sender.SetMsgId(MSG_ID);
    msg_sender.SetClient(target_user);
    msg_sender.SetOnlineMsgData(notify_msg);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_DEALALARM);
    return;
}
