#include "RouteDevRequestArming.h"
#include "DclientMsgDef.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "AkcsMsgDef.h"
#include "MsgBuild.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteDevRequestArming>();
    RegRouteFunc(p, AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ);
};

int RouteDevRequestArming::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    auto msg = base_msg.p2pmainapphandlearmingmsg2();
    
    GetArmingInfo(msg);

    AK_LOG_INFO << "handle dev arming ,mac= " << arming_msg_.mac << " uid=" << arming_msg_.uid << " action=" << arming_msg_.szAction << " mode=" << arming_msg_.mode;

    return 0;
}

void RouteDevRequestArming::GetArmingInfo(const AK::Server::P2PMainAppHandleArmingMsg& msg)
{
    memset(&arming_msg_, 0, sizeof(arming_msg_));
    Snprintf(arming_msg_.mac, sizeof(arming_msg_.mac), msg.mac().c_str());
    Snprintf(arming_msg_.uid, sizeof(arming_msg_.uid), msg.uid().c_str());
    Snprintf(arming_msg_.szAction, sizeof(arming_msg_.szAction), msg.action().c_str());
    arming_msg_.mode = msg.mode();
}

int RouteDevRequestArming::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    msg_id = MSG_TO_DEVICE_HANDLE_ARMING;
    to_mac = arming_msg_.mac;
    enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    GetMsgBuildHandleInstance()->BuildReqArmingMsg(arming_msg_, msg);
    return 0;
}