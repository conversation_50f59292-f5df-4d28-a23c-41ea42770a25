#include "RouteDevResponseArming.h"
#include "DclientMsgDef.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "AkcsMsgDef.h"
#include "MsgBuild.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "ClientControl.h"
#include "Office2AppMsg.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteDevResponseArming>();
    RegRouteFunc(p, AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP);
};

int RouteDevResponseArming::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "RouteDevResponseArming BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    auto msg = base_msg.p2pmainapphandlearmingmsg2();
    
    GetArmingInfo(msg);

    int receiver_type = base_msg.type();
    std::string receiver_uid = base_msg.uid();
    if (receiver_type == TransP2PMsgType::TO_APP_UID)
    {
        if (0 != SendResponseArmingNotifyToApp(receiver_uid))
        {
            AK_LOG_ERROR << "send response arming notify to app failed. app account=" << receiver_uid;
            return -1;
        }
    } else if (receiver_type == TransP2PMsgType::TO_DEV_MAC)
    {
        if (0 != SendResponseArmingNotifyToDev(receiver_uid))
        {
            AK_LOG_ERROR << "send response arming notify to dev failed. dev mac=" << receiver_uid;
            return -1;
        }
        dbinterface::ResidentDevices::SetDeviceArmingStatus(receiver_uid, arming_msg_.mode);
    }
    return 0;
}

void RouteDevResponseArming::GetArmingInfo(const AK::Server::P2PMainAppHandleArmingMsg& msg)
{
    memset(&arming_msg_, 0, sizeof(arming_msg_));
    Snprintf(arming_msg_.mac, sizeof(arming_msg_.mac), msg.mac().c_str());
    Snprintf(arming_msg_.uid, sizeof(arming_msg_.uid), msg.uid().c_str());
    Snprintf(arming_msg_.szAction, sizeof(arming_msg_.szAction), msg.action().c_str());
    arming_msg_.mode = msg.mode();
    arming_msg_.resp_action = msg.resp_action();
}

int RouteDevResponseArming::SendResponseArmingNotifyToDev(const std::string& receiver_mac)
{
    ResidentDev dev;
    GetDevClientByMac(receiver_mac, dev);
    std::string msg;
    GetMsgBuildHandleInstance()->BuildRespArmingMsg(arming_msg_, msg);
    uint16_t msg_id = MSG_FROM_DEVICE_REPORT_ARMING_STATUS;
    MsgEncryptType enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, msg, msg_id, socket_message, enc_type) != 0)
    {
        AK_LOG_ERROR << "BuildDclientMacEncMsg failed. mac=" << receiver_mac;
        return -1;
    }
    if (GetClientControlInstance()->SendTransferMsg(receiver_mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_ERROR << "SendTransferMsg failed. mac=" << receiver_mac;
        return -1;
    }
    return 0;
}

int RouteDevResponseArming::SendResponseArmingNotifyToApp(const std::string& account)
{
    COffice2AppMsg msg_sender;
    //在线消息构造
    std::string dclient_msg;
    GetMsgBuildHandleInstance()->BuildRespArmingMsg(arming_msg_, dclient_msg);
    //消息发送给csmain
    msg_sender.SetClient(account);
    msg_sender.SetOnlineMsgData(dclient_msg);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SetMsgId(MSG_FROM_DEVICE_REPORT_ARMING_STATUS);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_ONLY_ONLINE);
    return 0;
}
