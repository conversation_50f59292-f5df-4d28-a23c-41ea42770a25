#ifndef _ROUTE_DEV_RESPONSE_ARMING_H_
#define _ROUTE_DEV_RESPONSE_ARMING_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AK.Server.pb.h"


class RouteDevResponseArming : public IRouteBase
{
public:
    RouteDevResponseArming() {}
    ~RouteDevResponseArming() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    IRouteBasePtr NewInstance() { return std::make_shared<RouteDevResponseArming>(); }

    std::string FuncName() { return func_name_; }
    MsgEncryptType EncType() { return enc_type_; }

private:

    std::string func_name_ = "P2PRouteDevResponseArming";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_ARMING arming_msg_;
    void GetArmingInfo(const AK::Server::P2PMainAppHandleArmingMsg& msg);
    int SendResponseArmingNotifyToApp(const std::string& account);
    int SendResponseArmingNotifyToDev(const std::string& receiver_mac);
};

#endif //_ROUTE_DEV_RESPONSE_ARMING_H_
