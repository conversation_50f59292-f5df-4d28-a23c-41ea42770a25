#include "RouteGroupIndoorRelayStatus.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "MsgControl.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "string.h"
#include "MsgStruct.h"
#include "AkcsCommonDef.h"
#include "Office2AppMsg.h"
#include "util.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteGroupIndoorRelayStatus>();
    RegRouteFunc(p, AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG);
};

int RouteGroupIndoorRelayStatus::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());;

    AK::Server::GroupMainReportRelayStatus relay_status_msg = base_msg.groupindoorrelaystatusmsg2();

    std::string mac = relay_status_msg.mac();
    uint64_t relay_status = relay_status_msg.relay_status();
    int relay_type = relay_status_msg.relay_type();
    std::string account = relay_status_msg.account();

    //通知App改变relay状态
    SendReqChangeAppRelayMsg(relay_status, relay_type, mac, account);

    return 0;
}

void RouteGroupIndoorRelayStatus::SendReqChangeAppRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, const std::string& account)
{
    std::string msg;
    uint16_t msg_id = 0;
    GetMsgBuildHandleInstance()->BuildReqChangeAppRelayMsg(relay_status, relay_type, mac, msg, msg_id);

    COffice2AppMsg msg_sender;
    msg_sender.SetOnlineMsgData(msg);
    msg_sender.SetMsgId(msg_id);
    msg_sender.SetClient(account);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID_ONLINE);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_ONLY_ONLINE);
}