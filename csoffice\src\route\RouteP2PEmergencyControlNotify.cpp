#include "RouteP2PEmergencyControlNotify.h"
#include "util.h"
#include "util_time.h"
#include "SnowFlakeGid.h"
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "Office2AppMsg.h"
#include "OfficePushClient.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficePersonalAccount.h"

extern std::map<string, AKCS_DST> g_time_zone_DST; 

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PEmergencyControlNotify>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG);
};

int RouteP2PEmergencyControlNotify::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    if (base_msg.type() == TransP2PMsgType::TO_APP_UID)
    {
        SendEmergencyNotifyToApp(base_msg);
    }
    else if (base_msg.type() == TransP2PMsgType::TO_DEV_MAC)
    {
        SendEmergencyNotifyToDev(base_msg);
    }

    return 0;
}

void RouteP2PEmergencyControlNotify::SendEmergencyNotifyToApp(const AK::BackendCommon::BackendP2PBaseMessage& base_msg)
{
    const AK::Server::P2PSendEmergencyNotifyMsg& msg = base_msg.p2psendemergencynotifymsg2();

    int control_type = msg.control_type();
    std::string time_now = msg.timenow();
    std::string receiver_site = msg.receiver_uid();

     if (dbinterface::ProjectUserManage::MultiSiteLimit(receiver_site))
    {
        AK_LOG_INFO << "multi site account abnormal. no need to notify. site=" << receiver_site;
        return;
    }
    
    COffice2AppMsg msg_sender;

    // 在线消息构造
    std::string dclient_msg;
    GetMsgBuildHandleInstance()->BuildEmergencyControlNotifyMsg(control_type, time_now, receiver_site, dclient_msg);

    AK_LOG_INFO << "Send Emergency Alarm Notify To App, receiver_site = " << receiver_site;

    // 离线消息构造
    std::string title;
    msg_sender.InsertOfflineMsgKV("control_type", std::to_string(control_type));
    if (OfflinePush::GetMultiSiteUserTitle(receiver_site, title) == 0)
    {
        msg_sender.InsertOfflineMsgKV("title_prefix", title);
        msg_sender.InsertOfflineMsgKV("site", receiver_site);
    }

    // 消息发送给csmain
    msg_sender.SetOnlineMsgData(dclient_msg);
    msg_sender.SetClient(receiver_site);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetMsgId(MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_EMERGENCY_NOTIFY);
    return;
}

void RouteP2PEmergencyControlNotify::SendEmergencyNotifyToDev(const AK::BackendCommon::BackendP2PBaseMessage& base_msg)
{
    const AK::Server::P2PSendEmergencyNotifyMsg& msg = base_msg.p2psendemergencynotifymsg2();
    int control_type = msg.control_type();
    std::string time_now = msg.timenow();
    std::string receiver_mac = msg.receiver_uid();
    
    ResidentDev dev;
    if (g_office_srv_ptr->GetDevSetting(receiver_mac, dev))
    {
        AK_LOG_WARN << "SendEmergencyNotifyToDev GetDevSetting failed, mac = " << receiver_mac;
        return;
    }
    
    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountById(dev.project_mng_id, project_info))
    {
        AK_LOG_WARN << "SendEmergencyNotifyToDev GetAccountById failed, mac = " << receiver_mac;
        return;
    }

    std::string notify_msg;
    uint16_t msg_id = MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY;
    MsgEncryptType enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    
    GetMsgBuildHandleInstance()->BuildEmergencyControlNotifyMsg(control_type, time_now, receiver_mac, notify_msg);
    AK_LOG_INFO << "SendEmergencyNotifyToDev mac = " << receiver_mac;
    
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, notify_msg, msg_id, socket_message, enc_type) != 0)
    {
        AK_LOG_WARN << "SendEmergencyNotifyToDev BuildDclientMacEncMsg failed, mac=" << receiver_mac;
        return;
    }
    
    GetClientControlInstance()->SendTransferMsg(receiver_mac, dev.conn_type, socket_message.data, socket_message.size);

    return;
}

