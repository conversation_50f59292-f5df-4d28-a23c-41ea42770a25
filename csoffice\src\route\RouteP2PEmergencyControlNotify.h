#ifndef _ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_
#define _ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/Account.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"

class RouteP2PEmergencyControlNotify : public IRouteBase
{
public:
    RouteP2PEmergencyControlNotify(){}
    ~RouteP2PEmergencyControlNotify() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PEmergencyControlNotify>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteP2PEmergencyControlNotify";
    void SendEmergencyNotifyToApp(const AK::BackendCommon::BackendP2PBaseMessage& base_msg);
    void SendEmergencyNotifyToDev(const AK::BackendCommon::BackendP2PBaseMessage& base_msg);
    void BuildOfflineEmergencyControlNotifyMsg(int control_type, const std::string& main_account, const std::string& receiver_account, OfflinePushInfo& offline_push_info);
};

#endif //_ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_
