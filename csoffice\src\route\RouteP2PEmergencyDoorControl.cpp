#include "RouteP2PEmergencyDoorControl.h"


__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PEmergencyDoorControl>();
    RegRouteFunc(p, AKCS_M2R_EMERGENCY_DOOR_CONTROL);
};

int RouteP2PEmergencyDoorControl::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    const AK::Server::P2PPmEmergencyDoorControlMsg& msg = base_msg.p2ppmemergencydoorcontrolmsg2();
    AK_LOG_INFO << "handle emergency door control, P2PPmEmergencyDoorControlMsg = " << msg.DebugString();

    GetEmergencyControlInfo(msg);
    
    GetEmergencyControlInstance()->InsertIntoTimingWheel(control_msg_.mac, control_msg_.msg_uuid, control_msg_.device_uuid, control_msg_.initiator, ACT_OPEN_DOOR_TYPE(control_msg_.act_type));

    return 0;
}

void RouteP2PEmergencyDoorControl::GetEmergencyControlInfo(const AK::Server::P2PPmEmergencyDoorControlMsg& msg)
{
    control_msg_.auto_manual = msg.auto_manual();
    control_msg_.operation_type = msg.operation_type();
    Snprintf(control_msg_.mac, sizeof(control_msg_.mac), msg.mac().c_str());
    Snprintf(control_msg_.msg_uuid, sizeof(control_msg_.msg_uuid), msg.msg_uuid().c_str());
    Snprintf(control_msg_.device_uuid, sizeof(control_msg_.device_uuid), msg.device_uuid().c_str());
    Snprintf(control_msg_.initiator, sizeof(control_msg_.initiator), msg.initiator().c_str());
    Snprintf(control_msg_.relay, sizeof(control_msg_.relay), msg.relay().c_str());
    Snprintf(control_msg_.security_relay, sizeof(control_msg_.security_relay), msg.security_relay().c_str());
    control_msg_.act_type = (control_msg_.auto_manual == OPERATE_TYPE::MANUAL) ? ((control_msg_.operation_type == CONTROL_TYPE::OPEN_DOOR) ? ACT_OPEN_DOOR_TYPE::PM_UNLOCK : ACT_OPEN_DOOR_TYPE::PM_LOCK) : ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK;

    return;
}

int RouteP2PEmergencyDoorControl::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    to_mac = control_msg_.mac;
    enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;

    if(control_msg_.operation_type == CONTROL_TYPE::OPEN_DOOR)
    {
        msg_id = MSG_TO_DEVICE_REQUEST_EMERGENCY_KEEP_OPEN_DOOR;
    }
    else
    {
        msg_id = MSG_TO_DEVICE_REQUEST_EMERGENCY_CLOSE_DOOR;
    }

    GetMsgBuildHandleInstance()->BuildEmergencyDoorControlMsg(control_msg_, to_mac, msg);
    return 0;
}
