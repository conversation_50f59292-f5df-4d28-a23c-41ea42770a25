#ifndef _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_
#define _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_

#include <string>
#include "util.h"
#include "RouteBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "RouteFactory.h"
#include "BasicDefine.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "EmergencyMsgControl.h"

class RouteP2PEmergencyDoorControl : public IRouteBase
{
public:
    RouteP2PEmergencyDoorControl(){}
    ~RouteP2PEmergencyDoorControl() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PEmergencyDoorControl>();}
    std::string FuncName() {return func_name_;}

private:
    void GetEmergencyControlInfo(const AK::Server::P2PPmEmergencyDoorControlMsg& msg);
    
    std::string func_name_ = "RouteP2PEmergencyDoorControl";
    SOCKET_MSG_EMERGENCY_CONTROL control_msg_;
};

#endif // _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_
