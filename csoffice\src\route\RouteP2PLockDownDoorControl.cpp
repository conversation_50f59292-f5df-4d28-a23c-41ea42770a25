#include "util_virtual_door.h"
#include "RouteP2PLockDownDoorControl.h"


__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PLockDownDoorControl>();
    RegRouteFunc(p, AKCS_M2R_P2P_LOCKDOWN_DOOR_CONTROL);
};

int RouteP2PLockDownDoorControl::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);

    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    const AK::Server::P2PLockDownDoorControlMsg& msg = base_msg.p2plockdowndoorcontrolmsg2();
    AK_LOG_INFO << "handle lockdown door control, P2PLockDownDoorControlMsg = " << msg.DebugString();

    GetLockDownControlInfo(msg);
    return 0;
}

void RouteP2PLockDownDoorControl::GetLockDownControlInfo(const AK::Server::P2PLockDownDoorControlMsg& msg)
{
    Snprintf(control_msg_.mac, sizeof(control_msg_.mac), msg.mac().c_str());
    Snprintf(control_msg_.relay, sizeof(control_msg_.relay), msg.relay().c_str());
    Snprintf(control_msg_.msg_uuid, sizeof(control_msg_.msg_uuid), msg.msg_uuid().c_str());
    Snprintf(control_msg_.device_uuid, sizeof(control_msg_.device_uuid), msg.device_uuid().c_str());
    Snprintf(control_msg_.mode, sizeof(control_msg_.mode), GetLockDownMode(msg.control_switch()).c_str());
    Snprintf(control_msg_.security_relay, sizeof(control_msg_.security_relay), msg.security_relay().c_str());
    return;
}

int RouteP2PLockDownDoorControl::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    to_mac = control_msg_.mac;
    enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    msg_id = MSG_TO_DEVICE_REQUEST_LOCKDOWN_DOOR;

    GetMsgBuildHandleInstance()->BuildLockDownControlMsg(control_msg_, msg);
    return 0;
}
