#ifndef __ROUTE_P2P_LOCKDOWN_DOOR_CONTROL_H__
#define __ROUTE_P2P_LOCKDOWN_DOOR_CONTROL_H__

#include <string>
#include "util.h"
#include "RouteBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "RouteFactory.h"
#include "BasicDefine.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "EmergencyMsgControl.h"

class RouteP2PLockDownDoorControl : public IRouteBase
{
public:
    RouteP2PLockDownDoorControl() {}
    ~RouteP2PLockDownDoorControl() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PLockDownDoorControl>();}
    std::string FuncName() {return func_name_;}

private:
    void GetLockDownControlInfo(const AK::Server::P2PLockDownDoorControlMsg& msg);
    
    std::string func_name_ = "RouteP2PLockDownDoorControl";
    SOCKET_MSG_REQUEST_LOCKDOWN control_msg_;
};

#endif // _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_
