#ifndef _ROUTE_P2P_MSG_H_
#define _ROUTE_P2P_MSG_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "DclientMsgDef.h"


class RouteP2PMsg: public IRouteBase
{
public:
    RouteP2PMsg(){}
    ~RouteP2PMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    int IPushNotify();
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PMsg>();}
    std::string FuncName() {return func_name_;}

private:

    std::string func_name_ = "RouteP2PMsg";
    SOCKET_MSG_COMMON_ACK common_ack_;
};


#endif



