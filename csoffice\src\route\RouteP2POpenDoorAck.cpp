#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "MsgBuild.h"
#include "util.h"
#include "NotifyPerText.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "Office2RouteMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "BasicDefine.h"
#include "RouteP2POpenDoorAck.h"
#include "Office2AppMsg.h"
#include "RouteFactory.h"
#include "ClientControl.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2POpenDoorAck>();
    RegRouteFunc(p, AKCS_M2R_P2P_OPEN_DOOR_ACK);
};

int RouteP2POpenDoorAck::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
            << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
            << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());
        
    auto p2p_msg = base_msg.p2pmainresponseopendoormsg2();

    GetDevRemoteMsgInfo(p2p_msg);

    NotifyAppOrDev();

    return 0;
}

void RouteP2POpenDoorAck::GetDevRemoteMsgInfo(const AK::Server::P2PMainResponseOpenDoorMsg& p2p_msg)
{
    memset(&response_ack_msg_, 0, sizeof(response_ack_msg_));
    Snprintf(response_ack_msg_.trace_id, sizeof(response_ack_msg_.trace_id), p2p_msg.msg_traceid().c_str());
    Snprintf(response_ack_msg_.info, sizeof(response_ack_msg_.info), p2p_msg.info().c_str());
    response_ack_msg_.result = p2p_msg.result();
    mac_or_uid_ = p2p_msg.mac_or_uid();
    response_ack_msg_.response_type = p2p_msg.response_type();

    return;
}

void RouteP2POpenDoorAck::NotifyAppOrDev()
{
    if (response_ack_msg_.response_type == (int)RemoteControlAckResponseType::RESPONSE_TYPE_APP)
    {
        NotifyApp();
    }
    else if (response_ack_msg_.response_type == (int)RemoteControlAckResponseType::RESPONSE_TYPE_DEV)
    {
        NotifyDev();
    }
    else
    {
        AK_LOG_WARN << "response ack type invalid. trace id=" << response_ack_msg_.trace_id;
    }
    return;
}

void RouteP2POpenDoorAck::NotifyApp()
{
    std::string msg_type = "RemoteOpenDoor";
    AppAsyncResponseMsg resp_msg;
    GenerateRespMsg(resp_msg);

    std::string msg;
    GetMsgBuildHandleInstance()->BuildAppAsyncResponseMsg(msg_type, response_ack_msg_.trace_id, resp_msg, msg);
    uint32_t msg_id = MSG_TO_APP_ASYNC_REQ_RESP;

    COffice2AppMsg msg_sender;
    msg_sender.SetOnlineMsgData(msg);
    msg_sender.SetMsgId(msg_id);
    msg_sender.SetClient(mac_or_uid_);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID_ONLINE);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_ONLY_ONLINE);
}

void RouteP2POpenDoorAck::NotifyDev()
{
    ResidentDev dev;
    if (g_office_srv_ptr->GetDevSetting(mac_or_uid_, dev))
    {
        AK_LOG_WARN << "RouteP2POpenDoorAck GetDevSetting failed, mac = " << mac_or_uid_;
        return;
    }

    std::string dclient_msg;
    GetMsgBuildHandleInstance()->BuildDclientOpenDoorAckMsg(response_ack_msg_, dclient_msg);

    uint16_t msg_id = MSG_TO_DEVICE_OPENDOOR_ACK;
    MsgEncryptType enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, dclient_msg, msg_id, socket_message, enc_type) != 0)
    {
        AK_LOG_WARN << "BuildDclientMacEncMsg failed. mac=" << mac_or_uid_;
        return;
    }
    if (GetClientControlInstance()->SendTransferMsg(mac_or_uid_, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendTransferMsg failed. mac=" << mac_or_uid_;
        return;
    }

    return;
}

void RouteP2POpenDoorAck::GenerateRespMsg(AppAsyncResponseMsg& resp_msg)
{
    std::string err_code;
    std::string message;
    if (response_ack_msg_.result == 0) //开门失败
    {
        err_code = APP_ERR_CODE_OPEN_DOOR_FAILED;
        message = "remote open actual dev door failed";
    } 
    else if (response_ack_msg_.result == 1)
    {
        err_code = APP_ERR_CODE_SUCCESS;
        message = "success";
    }

    Snprintf(resp_msg.err_code, sizeof(resp_msg.err_code), err_code.c_str());
    Snprintf(resp_msg.message, sizeof(resp_msg.message), message.c_str());

    return;
}