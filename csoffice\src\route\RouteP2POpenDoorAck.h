#ifndef _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_
#define _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AK.Server.pb.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"

class RouteP2POpenDoorAck : public IRouteBase
{
public:
    RouteP2POpenDoorAck(){}
    ~RouteP2POpenDoorAck() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2POpenDoorAck>();}
    std::string FuncName() {return func_name_;}

private:
    void GetDevRemoteMsgInfo(const AK::Server::P2PMainResponseOpenDoorMsg& p2p_msg);
    void GenerateRespMsg(AppAsyncResponseMsg& resp_msg);

    void NotifyAppOrDev();
    void NotifyApp();
    void NotifyDev();
    
    SOCKET_MSG_DEV_REMOTE_ACK response_ack_msg_;
    std::string func_name_ = "RouteP2POpenDoorAck";
    std::string mac_or_uid_;
};

#endif // _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_