#include "RouteP2PRemoteOpenDoor.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DclientMsgDef.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "RouteFactory.h"
#include "AkcsMsgDef.h"


const int OPEN_DOOR_RELAY = 0;
const int OPEN_DOOR_SECURITY_RELAY = 1;

__attribute__((constructor))  static void init()
{
    IRouteBasePtr p = std::make_shared<RouteP2PRemoteOpenDoor>();
    RegRouteFunc(p, AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG);
};

int RouteP2PRemoteOpenDoor::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
        << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
        << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    const AK::Server::P2POpenDoorNotifyMsg& opendoor_msg = base_msg.p2popendoornotifymsg2();
    AK_LOG_INFO << "csoffice receive open door msg from router:project_type=" << opendoor_msg.project_type()
        << ", mac=" << opendoor_msg.mac() << ", uid=" << opendoor_msg.uid()
        << ", relay_type=" << opendoor_msg.relay_type() << ", relay=" << opendoor_msg.relay()
        << ", repost_mac=" << opendoor_msg.repost_mac() << ", traceid" << opendoor_msg.msg_traceid();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), opendoor_msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), opendoor_msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), opendoor_msg.msg_traceid().c_str());
    Snprintf(open_door.repost_mac, sizeof(open_door.repost_mac), opendoor_msg.repost_mac().c_str());
    open_door.relay = static_cast<int>(opendoor_msg.relay());

    std::string open_door_type = "";
    if (opendoor_msg.relay_type() == OPEN_DOOR_SECURITY_RELAY)
    {
        open_door_type = SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY;
    }
    else
    {
        open_door_type = SOCKET_MSG_TYPE_NAME_OPENDOOR;
    }

    HandleP2POpenDoorReq(open_door, open_door_type);
    return 0;
}

std::string OldOfficeGetAccessFloors(int sip_type, const std::string sip_account)
{
    std::string accessible_floor = "0";

    if (sip_type == csmain::OFFICE_APP)
    {
        accessible_floor = dbinterface::OfficePersonalAccount::GetFloorByAccount(sip_account);
    }
    else if (sip_type == csmain::OFFICE_DEV)
    {
        ResidentDev dev;
        if (dbinterface::ResidentDevices::GetSipDev(sip_account, dev) == 0)
        {
            accessible_floor = dbinterface::OfficePersonalAccount::GetFloorByAccount(dev.node);
        }
    }

    return accessible_floor;
}

std::string NewOfficeGetAccessFloors(int sip_type, const std::string& sip_account, const std::string mac)
{
    std::string accessible_floor = "0";

    if (sip_type == csmain::OFFICE_APP)
    {
        // 获取设备信息
        OfficeDevPtr dev;
        if (dbinterface::OfficeDevices::GetMacDev(mac, dev) == 0)
        {
            // 获取用户的UUID
            OfficeAccount office_account;
            if (dbinterface::OfficePersonalAccount::GetUidAccount(sip_account, office_account) == 0)
            {
                // 获取可达楼层
                accessible_floor = dbinterface::OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(
                    office_account.uuid, dev->unit_uuid, dev->uuid, office_account.role
                );
            }
        }
    }
    else if (sip_type == csmain::OFFICE_DEV)
    {
        // 获取设备信息
        OfficeDevPtr dev;
        if (dbinterface::OfficeDevices::GetSipDev(sip_account, dev) == 0)
        {
            // 获取关联人
            OfficeDeviceAssignInfo assign_info;
            if (dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(dev->uuid, assign_info) == 0)
            {
                OfficeAccount office_account;
                if (dbinterface::OfficePersonalAccount::GetUUIDAccount(assign_info.personal_account_uuid, office_account) == 0)
                {
                    // 获取可达楼层
                    accessible_floor = dbinterface::OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(
                        assign_info.personal_account_uuid, dev->unit_uuid, dev->uuid, office_account.role
                    );
                }
            }
        }
    }

    return accessible_floor;
}

void RouteP2PRemoteOpenDoor::HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO& open_door, const std::string& open_door_type)
{
    int relay = open_door.relay;
    std::string uid = open_door.uid;
    std::string mac = open_door.mac;
    std::string accessible_floor = "0";             // 分号分割楼层, eg: "1;2;3;6;7;8;9;"
    std::string msg_traceid = open_door.trace_id;
    std::string receiver_mac = strlen(open_door.repost_mac) > 0 ? (open_door.repost_mac) : (open_door.mac);

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(uid, sip_info);

    // 获取楼层
    if (sip_info.project_type == project::OFFICE)
    {
        accessible_floor = OldOfficeGetAccessFloors(sip_info.sip_type, uid);
    }
    else if (sip_info.project_type == project::OFFICE_NEW)
    {
        accessible_floor = NewOfficeGetAccessFloors(sip_info.sip_type, uid, mac);
    }

    if (accessible_floor.length() == 0)
    {
        accessible_floor = "0";
    }

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remote_control_msg;
    memset(&remote_control_msg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remote_control_msg.protocal, sizeof(remote_control_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remote_control_msg.type, sizeof(remote_control_msg.type), open_door_type.c_str());
    Snprintf(remote_control_msg.item[0], sizeof(remote_control_msg.item[0]), uid.c_str());
    ::snprintf(remote_control_msg.item[1], sizeof(remote_control_msg.item[1]), "%d", relay);
    Snprintf(remote_control_msg.item[2], sizeof(remote_control_msg.item[2]), msg_traceid.c_str());
    ::snprintf(remote_control_msg.item[3], sizeof(remote_control_msg.item[3]), "%s", accessible_floor.c_str());
    ::snprintf(remote_control_msg.item[4], sizeof(remote_control_msg.item[4]), "%s", mac.c_str());


    AK_LOG_INFO << "Request open door uid=" << uid
        << ", mac=" << mac << ", repost_mac=" << open_door.repost_mac
        << ", traceid=" << msg_traceid << ", open_door_type=" << open_door_type
        << ", accessible_floor=" << accessible_floor;

    char xml_msg[4096];
    memset(xml_msg, 0, sizeof(xml_msg));
    GetMsgBuildHandleInstance()->BuildRemoteControlMsg(xml_msg, sizeof(xml_msg), &remote_control_msg);

    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.msg_id = MSG_TO_DEVICE_REMOTE_CONTROL;
    send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
    send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    Snprintf(send_msg.client, sizeof(send_msg.client), receiver_mac.c_str());
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), xml_msg);
    send_msg.msg_len = strlen(send_msg.msg_data);

    GetClientControlInstance()->SendTransferMsg(send_msg);
    return;
}
