#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteP2PSendTextNotifyMsg.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "OfficeServer.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "NotifyPerText.h"
#include "Office2RouteMsg.h"
#include "NotifyMsgControl.h"
#include "ProjectUserManage.h"
#include "NotifyMessage.h"

__attribute__((constructor))  static void init()
{
    IRouteBasePtr p = std::make_shared<RouteP2PSendTextNotifyMsg>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG);
};


int RouteP2PSendTextNotifyMsg::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage recive_base_msg;
    if (recive_base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false)
    {
        AK_LOG_WARN << "RouteP2PSendTextNotifyMsg parse pb msg failed.";
        return -1;
    }
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << recive_base_msg.type()
                << ", uid=" << recive_base_msg.uid() << ", project_type=" << recive_base_msg.project_type()
                << ", conn_type=" << recive_base_msg.conn_type() << ", msgid=" << recive_base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(recive_base_msg.msgid());

    const AK::Server::P2PCommonTxtMsgNotifyMsg msg = recive_base_msg.p2pcommontxtmsgnotifymsg2();

    std::string uuid = msg.uuid();
    AK_LOG_INFO << "RouteP2PSendTextNotifyMsg: type=" << msg.client_type() << ", uuid=" << uuid;;

    CMessageNotifyMsg notify_msg(recive_base_msg, msg);
    GetNotifyMsgControlInstance()->AddNewCommonMessageNotifyMsg(notify_msg);
    return 0;
}

int RouteP2PSendTextNotifyMsg::IReplyToDevMsg(std::string& to_mac, std::string& msg, uint32_t& msg_id, MsgEncryptType& enc_type)
{

    return 0;
}

