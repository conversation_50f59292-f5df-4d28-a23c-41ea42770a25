#include "RouteP2PTempUsedNotify.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "dbinterface/Message.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PTempUsedNotify>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ);
};

int RouteP2PTempUsedNotify::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    SendTempkeyUsedMsg(base_msg);
    return 0;
}

void RouteP2PTempUsedNotify::SendTempkeyUsedMsg(const AK::BackendCommon::BackendP2PBaseMessage& base_msg)
{
    AK::Server::P2PMainSendTmpkeyUsed msg = base_msg.p2pmainsendtmpkeyused2();
    
    text_msg_info_.account = msg.account();
    text_msg_info_.client_type = CPerTextNotifyMsg::APP_SEND;
    text_msg_info_.text_message.type = CPerTextNotifyMsg::TMPKEY_MSG;
    Snprintf(text_msg_info_.text_message.title, sizeof(text_msg_info_.text_message.title), "TempKey Used");
    Snprintf(text_msg_info_.text_message.content, sizeof(text_msg_info_.text_message.content), msg.name().c_str());

    int msg_list_id = dbinterface::Message::InsertMessage(text_msg_info_.account, text_msg_info_.text_message.content);
    text_msg_info_.text_message.id = msg_list_id;

    CPerTextNotifyMsg notify_msg(base_msg, text_msg_info_, msg.account());
    GetNotifyMsgControlInstance()->AddTextNotifyMsg(notify_msg);
    return;
}
