#ifndef __ROUTE_P2P_TEMPKEY_USED_NOTIFY_H__
#define __ROUTE_P2P_TEMPKEY_USED_NOTIFY_H__

#include <string>
#include "RouteBase.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "AK.BackendCommon.pb.h"

class RouteP2PTempUsedNotify : public IRouteBase
{
public:
    RouteP2PTempUsedNotify() = default;
    ~RouteP2PTempUsedNotify() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu) override;

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PTempUsedNotify>();}
    std::string FuncName() {return func_name_;}
    
private:
    void SendTempkeyUsedMsg(const AK::BackendCommon::BackendP2PBaseMessage& base_msg);
        
private:
    std::string func_name_ = "RouteP2PTempUsedNotify";
    SOCKET_MSG_SEND_TEXT_MESSAGE text_msg_info_;
};

#endif //_ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_
