CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (csouterapi  CXX)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libhiredis.a libevent.so libglog.so libmysqlclient.so libevpp.so libprotobuf.so libetcd-cpp-api.so libcpprest.so libboost_system.so libssl.so libcrypto.so libgpr.so libgrpc.so libgrpc++.so libcppkafka.so
 librdkafka.so librdkafka++.so libz.so libdl.so)
SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../)
LINK_DIRECTORIES(${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/thirdlib ${BASE_SOURCE_DIR}/csbase/redis/hiredis ${BASE_SOURCE_DIR}/csbase/evpp/lib /usr/local/lib)

AUX_SOURCE_DIRECTORY(./ SRC_LIST_ROUTE)
AUX_SOURCE_DIRECTORY(../csbase/protobuf SRC_LIST_BASE_PROTOBUF)
AUX_SOURCE_DIRECTORY(../csbase/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(../csbase/grpc SRC_LIST_BASE_GRPC)
AUX_SOURCE_DIRECTORY(../csbase/grpc/cssession SRC_LIST_BASE_GRPC_SESSION)
AUX_SOURCE_DIRECTORY(../csbase/session SRC_LIST_BASE_SESSION)
AUX_SOURCE_DIRECTORY(../csbase/jsoncpp0.5/src/json SRC_LIST_BASE_JSONCPP)
AUX_SOURCE_DIRECTORY(../csbase/grpc/csmain SRC_LIST_BASE_GRPC_CSMAIN)
AUX_SOURCE_DIRECTORY(../csbase/csmain SRC_LIST_BASE_CSMAIN)

SET(BASE_LIST_INC  ${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/mysql/include ${BASE_SOURCE_DIR}/csbase/Rldb 
                  ${BASE_SOURCE_DIR}/csbase/evpp ${BASE_SOURCE_DIR}/csbase/protobuf ${BASE_SOURCE_DIR}/csbase/etcd ${BASE_SOURCE_DIR}/csbase/session 
                 ${BASE_SOURCE_DIR}/csbase/grpc/cssession ${BASE_SOURCE_DIR}/evpp ${BASE_SOURCE_DIR}/csbase/grpc ${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/include  ${BASE_SOURCE_DIR}/csbase/grpc/gens
                  ${BASE_SOURCE_DIR}/csbase/csmain ${BASE_SOURCE_DIR}/csbase/grpc/csmain ${BASE_SOURCE_DIR}/csbase/gid)

include_directories( ${BASE_LIST_INC})

ADD_DEFINITIONS( -std=gnu++11 -g -gstabs+ -W -Wall -Wno-ignored-qualifiers -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON
-DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -Wno-unused-parameter -Wno-deprecated)
   
SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
   
add_executable(csouterapi ${SRC_LIST_ROUTE}  ${SRC_LIST_BASE_PROTOBUF} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_BASE_SESSION} ${SRC_LIST_BASE_GRPC_SESSION} ${SRC_LIST_BASE_GRPC} ${SRC_LIST_BASE_JSONCPP} ${SRC_LIST_BASE_GRPC_CSMAIN} ${SRC_LIST_BASE_CSMAIN})

set_target_properties(csouterapi PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csouterapi/lib")
target_link_libraries(csouterapi  ${DEPENDENT_LIBRARIES})
