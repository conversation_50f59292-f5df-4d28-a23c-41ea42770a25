#include "http_msg_contorl.h"
#include "json/json.h"
#include "AkcsPbxHttpMsg.h"


static struct http_state_table HTTPSTATE_CHART[] = {
	{HTTP_CODE_SUC, "success"},
	{HTTP_CODE_ERROR_APP_VER, "app version error."},
	{HTTP_CODE_ERROR_ROUTE_ONT_DEFINE, "http req route is not define."},
	{HTTP_CODE_ERROR_PARSE_JSON, "parse json error."},
	{0, NULL}
};

const char *httpRespState2str(int state)
{
	int x;
	const char *str = "UNKNOWN";
    int len = sizeof(HTTPSTATE_CHART) / sizeof(struct http_state_table);
	for (x = 0; x < len - 1; x++) {
		if (HTTPSTATE_CHART[x].state == state) {
			str = HTTPSTATE_CHART[x].message;
			break;
		}
	}

	return str;
}


std::string buildCommHttpMsg(int code,  const HttpRespKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[HTTP_RET_PARMA_RESULT_CODE] = code;
    item[HTTP_RET_PARMA_MESSAGE] = httpRespState2str(code);
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    item[HTTP_RET_PARMA_DATAS] = itemData;

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string buildErrorHttpMsg(int code)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[HTTP_RET_PARMA_RESULT_CODE] = code;
    item[HTTP_RET_PARMA_MESSAGE] = httpRespState2str(code);

    std::string msg_json = w.write(item);

    return msg_json;
}

