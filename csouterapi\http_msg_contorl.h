#ifndef __HTTP_MSG_CONTORL_H__
#define __HTTP_MSG_CONTORL_H__
#include <map>
#include <string>



typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespKV;

enum HTTP_CODE
{
    HTTP_CODE_SUC = 0,
    HTTP_CODE_ERROR_START = 100,
    HTTP_CODE_ERROR_APP_VER = 101,
    HTTP_CODE_ERROR_ROUTE_ONT_DEFINE = 102,
    HTTP_CODE_ERROR_PARSE_JSON = 103,
};


struct http_state_table {
    int state;
	const char *message;
};

std::string buildCommHttpMsg(int code, const HttpRespKV& kv);
std::string buildErrorHttpMsg(int code);







#endif //__HTTP_MSG_CONTORL_H__
