#include <iostream>
#include <functional>
#include "http_resp.h"
#include "outerapi_conf.h"
#include "AkLogging.h"
#include "json/json.h"
#include "pbx_msg_contorl.h"
#include "SnowFlakeGid.h"
#include "http_msg_contorl.h"
#include <map>
#include "AkcsPbxHttpMsg.h"


extern CSOUTERAPI_CONF gstCSOUTERAPIConf; //全局配置信息

namespace csouterapi
{


csouterapi::HTTPRespCallback ReqAppStatusHandlerV6000 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string uid = ctx->GetQuery(PBX_HTTP_PARMA_UID);
    std::string str_traceid = ctx->GetQuery(PBX_HTTP_PARMA_TRACEID);
    AK_LOG_INFO << "data:uid:" << uid << " traceid:"  << str_traceid;
    
    uint64_t traceid = atoll(str_traceid.c_str());
    //char* 沿用了pbxmod
    ret = QueryUidStatus(uid.c_str(), traceid);

    AK_LOG_INFO << "return:status:" << ret << " traceid:"  << str_traceid;

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_STATUS, std::to_string(ret)));
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

csouterapi::HTTPRespCallback ReqWakeupAppHandlerV6000 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    Json::Reader reader;
    Json::Value root;  

    std::string http_body = ctx->body().ToString();
    AK_LOG_INFO << "data:" << http_body;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_PARSE_JSON));
        return;
    }

    
    AKCS_WAKEUP_APP info;
    memset(&info, 0, sizeof(info));
    snprintf(info.callee_sip, sizeof(info.callee_sip), "%s", root[PBX_HTTP_PARMA_CALLEE].asCString());
    snprintf(info.caller_sip, sizeof(info.caller_sip), "%s", root[PBX_HTTP_PARMA_CALLER].asCString());
    snprintf(info.nick_name_location, sizeof(info.nick_name_location), "%s", root[PBX_HTTP_PARMA_CALLER_NAME].asCString());
    info.app_type = root[PBX_HTTP_PARMA_APPTYPE].asInt();
    uint64_t traceid = atoll(root[PBX_HTTP_PARMA_TRACEID].asCString());
    
    ret = WakeupApp(&info, traceid);

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_STATUS, std::to_string(ret)));
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));

    return;
};

csouterapi::HTTPRespCallback ReqLandlineStatusHandlerV6000 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int ret = 0;
    std::string caller = ctx->GetQuery(PBX_HTTP_PARMA_CALLER);
    std::string phone = ctx->GetQuery(PBX_HTTP_PARMA_PHONE);
    std::string str_traceid = ctx->GetQuery(PBX_HTTP_PARMA_TRACEID);

    uint64_t traceid = atoll(str_traceid.c_str());
    AKCS_LANDLINE_STATUS info;
    memset(&info, 0, sizeof(info));
    snprintf(info.caller_sip, sizeof(info.caller_sip), "%s", caller.c_str());
    snprintf(info.phone_number, sizeof(info.phone_number), "%s", phone.c_str());

    AK_LOG_INFO << "data:caller:" <<info.caller_sip  << " phone:" << info.phone_number << " traceid:" << traceid;
    ret = QueryLandlineStatus(&info, traceid);
    AK_LOG_INFO << "return:status:" << ret << " traceid:"  << traceid;
    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_STATUS, std::to_string(ret)));
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
};

csouterapi::HTTPRespCallback ReqWriteHistoryHandlerV6000 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{    
    Json::Reader reader;
    Json::Value root;  

    std::string http_body = ctx->body().ToString();
    AK_LOG_INFO << "data:" << http_body;
    if (!reader.parse(http_body, root))
    {
        AK_LOG_WARN << "parse json error.data=" << http_body;
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_PARSE_JSON));
        return;
    }

    AKCS_CALL_HISTORY history;
    memset(&history, 0, sizeof(history));
    snprintf(history.callee_sip, sizeof(history.callee_sip), "%s", root[PBX_HTTP_PARMA_CALLEE].asCString());
    snprintf(history.caller_sip, sizeof(history.caller_sip), "%s", root[PBX_HTTP_PARMA_CALLER].asCString());
    snprintf(history.called_sip, sizeof(history.called_sip), "%s", root[PBX_HTTP_PARMA_CALLED].asCString());
    snprintf(history.answer_time, sizeof(history.answer_time), "%s", root[PBX_HTTP_PARMA_ANSWER_TIME].asCString());
    snprintf(history.start_time, sizeof(history.start_time), "%s", root[PBX_HTTP_PARMA_START_TIME].asCString());
    history.bill_second = root[PBX_HTTP_PARMA_BILL_SECOND].asInt();
    
    uint64_t traceid = atoll(root[PBX_HTTP_PARMA_TRACEID].asCString());
    WriteCallHistory(&history, traceid);

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_STATUS, std::to_string(1)));
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));

    return;
};


csouterapi::HTTPRespVerCallbackMap HTTPReqAppStatusMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V6000] = ReqAppStatusHandlerV6000;
    return OMap;
}

csouterapi::HTTPRespVerCallbackMap HTTPReqWakeupAPPMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V6000] = ReqWakeupAppHandlerV6000;
    return OMap;
}

csouterapi::HTTPRespVerCallbackMap HTTPReqWriteHistoryMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V6000] = ReqWriteHistoryHandlerV6000;
    return OMap;
}

csouterapi::HTTPRespVerCallbackMap HTTPReqLandlineStatusMap()
{
    HTTPRespVerCallbackMap OMap;
    OMap[V6000] = ReqLandlineStatusHandlerV6000;
    return OMap;
}


csouterapi::HTTPAllRespCallbackMap HTTPAllRespMapInit()
{
    csouterapi::HTTPAllRespCallbackMap OMap;
    OMap[csouterapi::PBX_REQ_STATUS] = HTTPReqAppStatusMap();
    OMap[csouterapi::PBX_REQ_WAKEUP] = HTTPReqWakeupAPPMap();
    OMap[csouterapi::PBX_REQ_WRITE_CALL_HISTORY] = HTTPReqWriteHistoryMap();
    OMap[csouterapi::PBX_REQ_LANDLINE_STATUS] = HTTPReqLandlineStatusMap();
    return OMap;
}

}

