#ifndef __CSOUTERAPI_HTTP_RESP_H__
#define __CSOUTERAPI_HTTP_RESP_H__
#include <functional>
#include <evpp/http/context.h>


#define API_VERSION              "api-version"


namespace csouterapi
{

const std::string V6000 = "6000";

//http路由
enum HTTP_ROUTE
{
    PBX_REQ_STATUS = 0,
    PBX_REQ_WAKEUP,
    PBX_REQ_WRITE_CALL_HISTORY,
    PBX_REQ_LANDLINE_STATUS
};

typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
typedef std::map<int, HTTPRespVerCallbackMap> HTTPAllRespCallbackMap;
HTTPAllRespCallbackMap HTTPAllRespMapInit();



}
#endif //__CSOUTERAPI_HTTP_RESP_H__