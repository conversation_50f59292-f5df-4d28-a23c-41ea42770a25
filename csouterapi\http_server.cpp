#include <stdio.h>
#include <stdlib.h>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"

#include "http_msg_contorl.h"
#include "http_server.h"
#include "http_resp.h"
#include "outerapi_conf.h"
#include "AkcsMonitor.h"
#include "AkcsPbxHttpMsg.h"


//全局变量
static csouterapi::HTTPAllRespCallbackMap httpRespCbs;


//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();

    //chenyc 2019-11-25,记录出错的次数,达到一定频率的时候,通过iptables加入黑名单. eg：remote_ip is ************ remote_ip是ipv4的形式
    //目前不需要
    //AKCS::Singleton<BussinessLimit>::instance().AddBussiness(csgate::CSGATE_HTTP_BUSSINESS, ctx->remote_ip());

    cb(buildErrorHttpMsg(HTTP_CODE_ERROR_ROUTE_ONT_DEFINE));
}
void HttpReqAppStatusCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();

    const char* api_version = ctx->FindRequestHeader(API_VERSION);
    if (nullptr == api_version)
    {
        LOG_WARN << "http req head 'api-version' is null";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }
    auto httpRespEchoMap = httpRespCbs[csouterapi::PBX_REQ_STATUS];
    csouterapi::HTTPRespVerCallbackMap::iterator it = httpRespEchoMap.find(api_version);
    if (it ==  httpRespEchoMap.end())
    {
        LOG_WARN << "http req version is [ " << api_version << " ] ,which out of request";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

void HttpReqWakeAppCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();

    const char* api_version = ctx->FindRequestHeader(API_VERSION);
    if (nullptr == api_version)
    {
        LOG_WARN << "http req head 'api-version' is null";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }

    auto httpRespEchoMap = httpRespCbs[csouterapi::PBX_REQ_WAKEUP];
    csouterapi::HTTPRespVerCallbackMap::iterator it = httpRespEchoMap.find(api_version);
    if (it ==  httpRespEchoMap.end())
    {
        LOG_WARN << "http req version is [ " << api_version << " ] ,which out of request";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

void HttpReqWriteHistoryCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();

    const char* api_version = ctx->FindRequestHeader(API_VERSION);
    if (nullptr == api_version)
    {
        LOG_WARN << "http req head 'api-version' is null";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }

    auto httpRespEchoMap = httpRespCbs[csouterapi::PBX_REQ_WRITE_CALL_HISTORY];
    csouterapi::HTTPRespVerCallbackMap::iterator it = httpRespEchoMap.find(api_version);
    if (it ==  httpRespEchoMap.end())
    {
        LOG_WARN << "http req version is [ " << api_version << " ] ,which out of request";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

void HttpReqLandlineStatusCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();

    const char* api_version = ctx->FindRequestHeader(API_VERSION);
    if (nullptr == api_version)
    {
        LOG_WARN << "http req head 'api-version' is null";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }

    auto httpRespEchoMap = httpRespCbs[csouterapi::PBX_REQ_LANDLINE_STATUS];
    csouterapi::HTTPRespVerCallbackMap::iterator it = httpRespEchoMap.find(api_version);
    if (it ==  httpRespEchoMap.end())
    {
        LOG_WARN << "http req version is [ " << api_version << " ] ,which out of request";
        cb(buildErrorHttpMsg(HTTP_CODE_ERROR_APP_VER));
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}


void startHttpServer()
{
    httpRespCbs = csouterapi::HTTPAllRespMapInit();
    const int port2 = 8528;
    const int thread_num2 = 3;
    bool ipv62 = false;
    evpp::http::Server server2(thread_num2, ipv62);
    server2.RegisterDefaultHandler(&DefaultHandler);
    server2.RegisterHandler(PBX_HTTP_URL_UID_STATUS, HttpReqAppStatusCallback);
    server2.RegisterHandler(PBX_HTTP_URL_APP_WAKEUP, HttpReqWakeAppCallback);
    server2.RegisterHandler(PBX_HTTP_URL_WRITE_HISTORY, HttpReqWriteHistoryCallback);
    server2.RegisterHandler(PBX_HTTP_URL_LANDLINE_STATUS, HttpReqLandlineStatusCallback);
    server2.Init(port2);
    server2.Start();
    return ;
}

