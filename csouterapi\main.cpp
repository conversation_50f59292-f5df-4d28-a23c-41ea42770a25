#include <evpp/tcp_server.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "http_server.h"
#include "outerapi_conf.h"
#include "ConfigFileReader.h"
#include "outerapi_etcd.h"
#include "EtcdCliMng.h"
#include <evnsq/message.h>
#include <evnsq/producer.h>
#include "AkcsMonitor.h"
#include "session_rpc_client.h"
#include "SnowFlakeGid.h"


#define OUTER_API_MAX_RLDB_CONN 1
#define MIN_SOCKET_FRAME_BUFFER 10
CSOUTERAPI_CONF gstCSOUTERAPIConf; //全局配置信息
SmRpcClient* g_sm_client_ptr = nullptr;

#define PIDFILE "/var/run/csouterapi.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}
void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csouterapilog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csouterapilog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csouterapilog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csouterapilog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 10;    //单日志文件最大10M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

void ConfInit()
{
    memset(&gstCSOUTERAPIConf, 0, sizeof(CSOUTERAPI_CONF));
    CConfigFileReader config_file("/usr/local/akcs/csouterapi/conf/csouterapi.conf");

    ::strncpy(gstCSOUTERAPIConf.szEtcdServerAddr, config_file.GetConfigName("etcd_srv_net"), sizeof(gstCSOUTERAPIConf.szEtcdServerAddr));
}



int OnRouteMQAlarmMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void ProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQAlarmMessage);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}


int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        LOG_WARN << "another csgate has been running in this system.";
        return -1;
    }
    glogInit(argv[0]);
    ConfInit();

    AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setWorkerId(1);
    AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setDatacenterId(1);


    std::thread mqProduceThread = std::thread(ProduceInit);
    //起http服务线程
    std::thread httpThread(startHttpServer);

    g_sm_client_ptr = new SmRpcClient();
    
    //etcd发现服务
    std::thread etcdCliThread = std::thread(EtcdSrvInit);

    etcdCliThread.join();
    mqProduceThread.join();
    httpThread.join();
    glogClean();
    return 0;
}
