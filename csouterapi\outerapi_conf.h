#ifndef __OUTERAPI_CONF_H__
#define __OUTERAPI_CONF_H__

#define CSOUTERAPI_CONF_COMMON_LEN 64

typedef struct CSOUTERAPI_CONF_T
{
    char szEtcdServerAddr[CSOUTERAPI_CONF_COMMON_LEN];
    /* DB配置项 */
    char szDbIP[CSOUTERAPI_CONF_COMMON_LEN];
    char szDbUserName[CSOUTERAPI_CONF_COMMON_LEN];
    char szDbPassword[CSOUTERAPI_CONF_COMMON_LEN];
    char szDbDatabase[CSOUTERAPI_CONF_COMMON_LEN];
    int  nDbPort;

} CSOUTERAPI_CONF;

#endif //__OUTERAPI_CONF_H__

