#include <string.h>
#include <signal.h>
#include <etcd/Client.hpp>
#include <evnsq/producer.h>
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evnsq/producer.h>
#include "session_rpc_client.h"
#include "csmain_rpc_client.h"
#include "csmain_rpc_client_mng.h"
#include "util.h"
#include "outerapi_conf.h"
#include "outerapi_etcd.h"
#include "AkLogging.h"


extern CSOUTERAPI_CONF gstCSOUTERAPIConf; //全局配置信息
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern SmRpcClient* g_sm_client_ptr;
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);
static evpp::PipeEventWatcher*  ev_cssession = nullptr;
static evpp::PipeEventWatcher*  ev_csmain = nullptr;


void WatchSessionSrv()
{
    ev_cssession->Notify();
}

void WatchCsmainSrv()
{
    ev_csmain->Notify();
}

static void SessionSrvConnInit(const std::set<std::string>& cssession_addrs)
{
    std::vector<AddressData> addresses;
    AddressData addr_tmp;
    for (const auto& cssesson : cssession_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = cssesson.find(":");
        if (std::string::npos != pos)
        {
            ip = cssesson.substr(0, pos);
            port = cssesson.substr(pos + 1);
        }
        addresses.emplace_back(AddressData{std::atoi(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
    }
    g_sm_client_ptr->SetNextResolution(addresses);
}

void CsmainRpcSrvInit(const std::set<std::string>& csmain_inner_addrs)
{
    for (const auto& csmain : csmain_inner_addrs)
    {
        MainRpcClientPtr route_cli_ptr(new MainRpcClient(csmain));
        MainRpcClientMng::Instance()->AddCsmainRpcSrv(csmain, route_cli_ptr);
    }
}

static void UpdateSrvInfo(evpp::EventLoop* etcd_loop, enum ETCD_UPDATE_SRV_TYPE type)
{
    switch (type)
    {
        case ETCD_UPDATE_SRV_TYPE_SESSION:
        {
            std::set<std::string> cssession_addrs;
            if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) == 0)
            {
                SessionSrvConnInit(cssession_addrs);
            }
        }
        break;
        case ETCD_UPDATE_SRV_TYPE_CSMAIN:
        {
            std::set<std::string> csmain_addrs;
            if (g_etcd_cli_mng->GetAllAccRpcInnerSrvs(csmain_addrs) == 0)
            {
                MainRpcClientMng::Instance()->UpdateCsmainRpcSrv(csmain_addrs);
            }
        }
        break;
        default:
            break;
    }
}


static int WatchSrvFromEtcd(const std::string& key, WatchSrvCallback cb)
{
    return g_etcd_cli_mng->WatchSrvs(key, cb);
}

static void WatchEtcdSessionInit()
{
    WatchSrvFromEtcd("/akcs/cssession/", WatchSessionSrv);
}

void WatchCsmainEtcdInit()
{
    WatchSrvFromEtcd("/akcs/csmain/", WatchCsmainSrv);
}

static void WatchSignalInit()
{
    ev_cssession->Init();
    ev_cssession->AsyncWait();

    ev_csmain->Init();
    ev_csmain->AsyncWait();
}


void EtcdSrvInit()
{    
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSOUTERAPIConf.szEtcdServerAddr);//"http://ip:port"
    //cssession
    std::set<std::string> cssession_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    SessionSrvConnInit(cssession_addrs);

    //csmain
    std::set<std::string> csmain_inner_addrs;
    if (g_etcd_cli_mng->GetAllAccRpcInnerSrvs(csmain_inner_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    CsmainRpcSrvInit(csmain_inner_addrs);
    //csmain rpc async
    std::thread cm_async_thread = std::thread(AsyncCompleteCMRpc);

    //watch cssession/csmain
    std::thread etcdWatchThread = std::thread(WatchCsmainEtcdInit);
    ev_csmain = new evpp::PipeEventWatcher(g_etcd_loop.get(), std::bind(&UpdateSrvInfo, g_etcd_loop.get(), ETCD_UPDATE_SRV_TYPE_CSMAIN));
    std::thread etcdWatchSessionThread = std::thread(WatchEtcdSessionInit);
    ev_cssession = new evpp::PipeEventWatcher(g_etcd_loop.get(), std::bind(&UpdateSrvInfo, g_etcd_loop.get(), ETCD_UPDATE_SRV_TYPE_SESSION));

    g_etcd_loop->RunInLoop(&WatchSignalInit);
    g_etcd_loop->Run();
    CAkEtcdCliManager::destroyInstance();
}



