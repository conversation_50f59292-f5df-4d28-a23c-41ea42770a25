#include "AK.Route.pb.h"
#include <sys/stat.h>
#include <errno.h>
#include "gid/SnowFlakeGid.h"
#include <evnsq/message.h>
#include <evnsq/producer.h>
#include "AkcsMonitor.h"
#include <etcd/Client.hpp>
#include <evnsq/producer.h>
#include "outerapi_etcd.h"
#include "session_rpc_client.h"
#include "csmain_rpc_client.h"
#include "csmain_rpc_client_mng.h"
#include "util.h"
#include "pbx_msg_contorl.h"
#include "AkLogging.h"

extern SmRpcClient* g_sm_client_ptr;

int QueryUidStatus(const char* uid, uint64_t traceid)
{
    int status = 0;
    if (!uid)
    {
        return -1;
    }
    
    std::string sid = g_sm_client_ptr->QueryUid(uid);
    if (sid.length() == 0)
    {
        return -1;
    }
    
    MainRpcClientPtr csmain_cli = MainRpcClientMng::Instance()->getRpcClientInstance(sid);
    if (csmain_cli)
    {
        status = csmain_cli->QueryUidStatus(uid, traceid);
    }
    return status;
}

int WakeupApp(AKCS_WAKEUP_APP* wakeup, uint64_t traceid)
{
    if (!wakeup)
    {
        return 0;
    }
    int status = 0;
    MainRpcClientPtr csmain_cli;
    if(2 == wakeup->app_type) //SDK
    {
        csmain_cli = MainRpcClientMng::Instance()->getRpcRandomClientInstance();
    }
    else
    {
        std::string sid = g_sm_client_ptr->QueryUid(wakeup->callee_sip);
        csmain_cli = MainRpcClientMng::Instance()->getRpcClientInstance(sid);
    }
    if (csmain_cli)
    {
        status = csmain_cli->WakeupApp(wakeup->caller_sip, wakeup->callee_sip, wakeup->nick_name_location, traceid, wakeup->app_type);
    }
    return status;
}

int QueryLandlineStatus(AKCS_LANDLINE_STATUS* landline, uint64_t traceid)
{
    if (!landline)
    {
        return 0;
    }

    int status = 0;
    MainRpcClientPtr csmain_cli = MainRpcClientMng::Instance()->getRpcRandomClientInstance();
    if (csmain_cli)
    {
        status = csmain_cli->QueryLandlineStatus(landline->caller_sip, landline->phone_number, traceid);
    }
    return status;
}

void WriteCallHistory(AKCS_CALL_HISTORY* history, uint64_t traceid)
{
    if (!history)
    {
        return;
    }
    MainRpcClientPtr csmain_cli = MainRpcClientMng::Instance()->getRpcRandomClientInstance();
    if (csmain_cli)
    {
        csmain_cli->WriteCallHistory(history->caller_sip, history->callee_sip, history->called_sip,
                                     history->start_time, history->answer_time, history->bill_second, traceid);
    }
    return;
}




