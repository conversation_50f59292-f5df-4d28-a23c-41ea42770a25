#ifndef _PBX_MSG_CONTORL_H_
#define _PBX_MSG_CONTORL_H_

#include "AK.Route.pb.h"


#define AKCS_SIP_SIZE   64
typedef struct AKCS_CALL_HISTORY_T
{
#define AKCS_CALL_HISTORY_DATETIME_SIZE     32
#define AKCS_CALL_HISTORY_HANGUP_CAUSE_SIZE 32

    int bill_second;
    char caller_sip[AKCS_SIP_SIZE];/*主叫*/
    char callee_sip[AKCS_SIP_SIZE];/*被叫*/
    char called_sip[AKCS_SIP_SIZE];/*接听*/
    char start_time[AKCS_CALL_HISTORY_DATETIME_SIZE];
    char answer_time[AKCS_CALL_HISTORY_DATETIME_SIZE];
    char hangup_cause[AKCS_CALL_HISTORY_HANGUP_CAUSE_SIZE];
} AKCS_CALL_HISTORY;



typedef struct AKCS_WAKEUP_APP_T
{
    char caller_sip[AKCS_SIP_SIZE];/*主叫*/
    char callee_sip[AKCS_SIP_SIZE];/*被叫*/
    char nick_name_location[128];/*主叫名称*/
    int  app_type;/*app类型 smartplus还是sdk*/
} AKCS_WAKEUP_APP;


typedef struct AKCS_LANDLINE_STATUS_T
{
    char caller_sip[AKCS_SIP_SIZE];
    char phone_number[32];
} AKCS_LANDLINE_STATUS;




int QueryUidStatus(const char* uid, uint64_t traceid);
int WakeupApp(AKCS_WAKEUP_APP* wakeup, uint64_t traceid);
int QueryLandlineStatus(AKCS_LANDLINE_STATUS* landline, uint64_t traceid);
void WriteCallHistory(AKCS_CALL_HISTORY* history, uint64_t traceid);


#endif

