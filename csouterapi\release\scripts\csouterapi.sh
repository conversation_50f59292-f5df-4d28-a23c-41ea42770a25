#!/bin/bash
ACMD="$1"
CSGATE_BIN='/usr/local/akcs/csouterapi/bin/csouterapi'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csgate()
{
    nohup $CSGATE_BIN >/dev/null 2>&1 &
    echo "Start csouterapi successful"
    if [ -z "`ps -fe|grep "csouterapirun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csouterapi/scripts/csouterapirun.sh >/dev/null 2>&1 &
    fi
}
stop_csgate()
{
    echo "Begin to stop csouterapirun.sh"
    csouterapirunid=`ps aux | grep -w csouterapirun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csouterapirunid}" ];then
	    echo "csouterapirun.sh is running at ${csouterapirunid}, will kill it first."
	    kill -9 ${csouterapirunid}
    fi
    echo "Begin to stop csouterapi"
    kill -9 `pidof csouterapi`
    sleep 2
    echo "Stop csouterapi successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 8528 | grep csouterapi | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csgate
    else
        echo "csouterapi is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 8528 | grep csouterapi | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csouterapi is already stopping"
    else
        stop_csgate
    fi
    ;;
  restart)
    stop_csgate
    sleep 1
    start_csgate
    ;;
  status)
    cnt=`ss -alnp | grep 8528 | grep csouterapi | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csouterapi is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csouterapi is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

