CMAKE_MINIMUM_REQUIRED(VERSION 2.8)
 
project (cscall C CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../src ${CMAKE_CURRENT_SOURCE_DIR}/../include ${CMAKE_CURRENT_SOURCE_DIR}/../../csbase/doorlog")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libcsbase.a pthread libevent.so libhiredis.a libglog.so libmysqlclient.so libgpr.so libgrpc.so libgrpc++.so libprotobuf.so libevpp.so -lssl -lcrypto -lcpprest -letcd-cpp-api  -levpp -levent -lboost_system -lcurl)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/redis/hiredis /usr/local/lib)

AUX_SOURCE_DIRECTORY(../src SRC_LIST)
AUX_SOURCE_DIRECTORY(../src/app SRC_LIST_APP)
AUX_SOURCE_DIRECTORY(../src/push SRC_LIST_PUSH)
AUX_SOURCE_DIRECTORY(../src/main SRC_LIST_MAIN)
AUX_SOURCE_DIRECTORY(../src/control SRC_LIST_CONTROL)
AUX_SOURCE_DIRECTORY(../src/maintenance SRC_LIST_STORAGE_MAINTENANCE)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/encrypt SRC_LIST_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Character SRC_LIST_CHAR)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Character/cstring SRC_LIST_CSTR)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/protobuf SRC_LIST_PROTBUF)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/cspush SRC_LIST_PUSH)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/cspbxrpc SRC_LIST_BASE_CSPBXRPC)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/session SRC_LIST_BASE_SESSION)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/csmain SRC_LIST_BASE_CSMAIN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc SRC_LIST_BASE_GRPC)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc/csmain SRC_LIST_BASE_GRPC_MAIN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc/cssession SRC_LIST_BASE_GRPC_SESSION)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc/cspbxrpc SRC_LIST_BASE_GRPC_PBXRPC)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/doorlog SRC_LIST_BASE_DOORLOG)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/http SRC_LIST_BASE_HTTP)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)

SET(BASE_LIST_INC 
     ../src/main ../src/app ../src/control ../src/push ../src/maintenance ../include 
     ${CSBASE_SOURCE_DIR}/pbxmod ${CSBASE_SOURCE_DIR}
	${CSBASE_SOURCE_DIR}/Character ${CSBASE_SOURCE_DIR}/Character/cstring ${CSBASE_SOURCE_DIR}/encrypt ${CSBASE_SOURCE_DIR}/jsoncpp0.5/include ${CSBASE_SOURCE_DIR}/gid
     ${CSBASE_SOURCE_DIR}/grpc ${CSBASE_SOURCE_DIR}/grpc/gens ${CSBASE_SOURCE_DIR}/grpc/include ${CSBASE_SOURCE_DIR}/grpc/cspbxrpc ${CSBASE_SOURCE_DIR}/grpc/csmain ${CSBASE_SOURCE_DIR}/grpc/cssession 
     ${CSBASE_SOURCE_DIR}/cspush ${CSBASE_SOURCE_DIR}/session  ${CSBASE_SOURCE_DIR}/cspbxrpc ${CSBASE_SOURCE_DIR}/csmain 
     ${CSBASE_SOURCE_DIR}/dbinterface ${CSBASE_SOURCE_DIR}/dbinterface/Log ${CSBASE_SOURCE_DIR}/dbinterface/office ${CSBASE_SOURCE_DIR}/dbinterface/new-office ${CSBASE_SOURCE_DIR}/dbinterface/resident ${CSBASE_SOURCE_DIR}/dbinterface/mapping 
     ${CSBASE_SOURCE_DIR}/protobuf ${CSBASE_SOURCE_DIR}/protobuf/include ${CSBASE_SOURCE_DIR}/redis ${CSBASE_SOURCE_DIR}/Rldb ${CSBASE_SOURCE_DIR}/evpp ${CSBASE_SOURCE_DIR}/etcd  ${CSBASE_SOURCE_DIR}/mysql/include 
)

ADD_DEFINITIONS( -std=c++11 -g  -W -Wall -Werror -Wno-unused-parameter -Wno-deprecated -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories(${BASE_LIST_INC} ${CSBASE_SOURCE_DIR}/metrics /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include)

add_executable(cspbxrpc ${SRC_LIST_PUSH} ${SRC_LIST} ${SRC_LIST_APP} ${SRC_LIST_MAIN} ${SRC_LIST_CONTROL} 
               ${SRC_LIST_PROTBUF} ${SRC_LIST_CSTR} ${SRC_LIST_CHAR} ${SRC_LIST_ENCRYPT} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_ETCD} 
               ${SRC_LIST_BASE_GRPC} ${SRC_LIST_BASE_GRPC_SESSION} ${SRC_LIST_BASE_GRPC_PBXRPC} ${SRC_LIST_BASE_GRPC_MAIN} ${SRC_LIST_STORAGE_MAINTENANCE}
               ${SRC_LIST_BASE_SESSION} ${SRC_LIST_BASE_CSPBXRPC} ${SRC_LIST_BASE_CSMAIN}
               ${SRC_LIST_BASE_DOORLOG} ${SRC_LIST_BASE_HTTP} ${SRC_LIST_BASE_METRICS} ${prefixed_file_list})

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../release/bin)
set_target_properties(cspbxrpc PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/cspbxrpc/lib")

target_link_libraries(cspbxrpc  ${DEPENDENT_LIBRARIES})
