#ifndef __IBMC_MSG_H__
#define __IBMC_MSG_H__

#include <memory>
#include <evpp/tcp_conn.h>
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"

typedef std::weak_ptr<evpp::TCPConn> WeakTCPConnPtr;

#define MSG_TYPE_MASK       0xFFFF0000
#define MSG_TIMER           0x00010000
#define MSG_CTRL            0x00040000

#define MSG_BACK_UP         0x00010001

enum
{
    MSG_CTRL_DEVICE_CONNECTED = MSG_CTRL + 1,
    MSG_CTRL_REBOOT_DEVICE,
};

#define MSG_SEQ_SIZE                    16    //消息序列号
#define COMMUNITY_SIZE                  32    //社区编码长度
#define AREA_NODE_COMMA_COUNT           4     //地址节点<,>分割符最大个数
#define AREA_NODE_SIZE                  32    //地址节点长度
#define TMP_KEY_SIZE                    24    //临时秘钥长度
#define IMEI_SIZE                       16    //手机串号长度
#ifndef DEV_ACCESS_SIZE
#define DEV_ACCESS_SIZE                 1024   //privatekey/rfidkey所有能匹配的设备
#endif

#define RELAY_DEFAULT                   15    //relay默认值1234
#define RELAY_NUM                       4     //relay数量最多4个
#define REQUEST_OPEN_DOOR_SUCCESS       1


namespace csmain
{
enum AppType
{
    APP_IOS = 0,
    APP_ANDROID_HUAWEI,
    APP_ANDROID_XIAOMI,
    APP_ANDROID_OTHERS,
    APP_ANDROID_FCM,
    APP_ANDROID_OPPO,
    APP_ANDROID_VIVO,
    APP_ANDROID_FLYME,
    APP_ANDROID_JPUSH,
};

enum CaptureType //设备端截图的时间类型
{
    kMotion = 0,
    kLogs = 1,
};

enum MotionRecvType //app端是否接受motion alert的设置项
{
    kNoRecv = 0,
    kRecv = 1,
    kNone = 2, //初始化状态，在此状态下,需要查询redis中缓存的状态
};

enum ArmingType //app端是否接受motion alert的设置项
{
    kDisarm = 0,
    kIndoor,
    kSleeping,
    kOutdoor,
};
enum ActType //室内机记录的各种动作(开门)记录
{
    kCall = 0,
    kInputPwd,
    kCard,
};

enum CaptureLogRetType //设备（室外机）上报(开门)消息的动作流
{
    kSuccess = 0,
    kFail,
};

enum EmailType
{
    EMAIL_CREATE_UID = 0,
    EMAIL_CHANGE_PWD,
    EMAIL_RESET_PWD,
    EMAIL_CHECK_CODE,
    EMAIL_DEV_APP_WILLBE_EXPIRE,
    EMAIL_FREETRIAL_WILLBE_EXPIRE,
    EMAIL_SHARE_TMPKEY,
    EMAIL_ACCOUNT_ACTIVE,
    EMAIL_CREATE_PROPERTY_WORK,
};

}

typedef struct SDMC_MESSAGE_T
{
    unsigned int id;
    unsigned long wParam;
    unsigned long lParam;
    void* lpData;
    struct SDMC_MESSAGE_T* next;
} SDMC_MESSAGE;

typedef enum DEVICE_STATUS_E
{
    DEVICE_STATUS_UNKOWN = 0,
    DEVICE_STATUS_IDLE,
    DEVICE_STATUS_TALKING,
    DEVICE_STATUS_UPDATING,
    DEVICE_STATUS_UPGRADING,
    DEVICE_STATUS_MAX,
} DEVICE_STATUS;

typedef struct DEVICE_INFO_T
{
    char ip_addr[IP_SIZE];
    uint32_t port;
} DEVICE_INFO;

typedef struct DEVICE_DATA_T
{
    unsigned int id;
    unsigned int status;
    unsigned int port;
    char device_id[DEVICE_ID_SIZE];
    char device_type[DEVICE_TYPE_SIZE];
    char ip[IP_SIZE];
    char mac[MAC_SIZE];
    char SWVer[DEVICE_SWVER_SIZE];
    char HWVer[DEVICE_HWVER_SIZE];
    struct DEVICE_DATA_T* next;
} DEVICE_DATA;

typedef enum DEVICE_TYPE_E
{
    DEVICE_TYPE_COMMUNITY = 1,   //社区设备
    DEVICE_TYPE_PERSONNAL = 2,   //个人设备
} DEVICE_TYPE;


typedef struct DEVICE_NET_T
{
    unsigned int port;
    char ip[IP_SIZE];
} DEVICE_NET;

typedef struct DEVICE_UPGRADE_T
{
#define MODEL_NAME_SIZE     64
#define MODEL_ID_SIZE       32
    uint32_t id;
    char model[DEVICE_ID_SIZE];
    char model_id[MODEL_ID_SIZE];
    char firmware_version[DEVICE_SWVER_SIZE];
    char firmware_file[URL_SIZE];
    char update_time[DATETIME_SIZE];
    struct DEVICE_UPGRADE_T* next;
} DEVICE_UPGRADE;

typedef struct KEY_SEND_T
{
    char community[COMMUNITY_SIZE];
    char device_node[DEVICE_ID_SIZE];
    uint32_t extension;
    int tz_type;    //1-xml 2-tzdata
    char mac[MAC_SIZE];
    WeakTCPConnPtr weak_conn;

    KEY_SEND_T() {
      community[0] = 0;
      device_node[0] = 0;
      extension = 0;
      tz_type = 0;
      mac[0] = 0;
    }	
} KEY_SEND;

//个人终端用户
typedef struct PERSONAL_KEY_SEND_T
{
    char node[DEVICE_ID_SIZE];
    char mac[MAC_SIZE];
    int tz_type;    //1-xml 2-tzdata

    PERSONAL_KEY_SEND_T() {
      node[0] = 0;
      mac[0] = 0;
      tz_type = 0;
    }
    WeakTCPConnPtr weak_conn;
} PERSONAL_KEY_SEND;

typedef struct GIVEN_KEY_SEND_T
{
    int type;
    char mac[MAC_SIZE];
    uint64_t traceid;
    char file_path[256];
    char file_md5[64];

    GIVEN_KEY_SEND_T() {
      file_path[0] = 0;
      file_md5[0] = 0;
      mac[0] = 0;
      type = 0;
      traceid = 0;
    }	
    WeakTCPConnPtr weak_conn;
} GIVEN_KEY_SEND;


enum
{
    TIME_ZONE_NONE = 0,
    TIME_ZONE_XML = 1,
    TIME_ZONE_DATA = 2,
};

enum
{
    AREA_NODE_TYPE_NONE = 0,
    AREA_NODE_TYPE_AREA,
    AREA_NODE_TYPE_BUILDING,
    AREA_NODE_TYPE_UNIT,
    AREA_NODE_TYPE_FLOOR,
    AREA_NODE_TYPE_ROOM,
    AREA_NODE_TYPE_MAX,
};

typedef struct CALL_HISTORY_T
{
#define CALL_HISTORY_NUMBER_SIZE        64
#define CALL_HISTORY_CALL_TIME_SIZE     32
    unsigned int id;
    unsigned int type;
    unsigned int duration;
    char number[CALL_HISTORY_NUMBER_SIZE];
    char calltime[CALL_HISTORY_CALL_TIME_SIZE];
    struct CALL_HISTORY_T* next;
} CALL_HISTORY;

typedef struct SYSTEM_SETTING_T
{
    char addr_md5[MD5_SIZE];
} SYSTEM_SETTING;

typedef struct APP_RES_INFO_T
{
#define APP_NAME_SIZE       24
#define APP_NO_SIZE         4
    char protocal[PROTOCAL_SIZE];
    char app_name[APP_NAME_SIZE];
    char device_mac[MAC_SIZE];
    char app_no[APP_NO_SIZE];
    char config_url[URL_SIZE];
    char config_md5[MD5_SIZE];
} APP_RES_INFO;

//Begin added by chenyc,2017-02-27,模块配置项结构体
typedef struct CONFIG_ITEM_T
{
#define CONFIG_ITEM_COMMON_SIZE 64
#define CONFIG_ITEM_DESC_SIZE       256
    unsigned int id;
    unsigned int module_id;
    char name[CONFIG_ITEM_COMMON_SIZE];
    char config[CONFIG_ITEM_COMMON_SIZE];
    char desc[CONFIG_ITEM_DESC_SIZE];
    char value[CONFIG_ITEM_COMMON_SIZE];
    struct CONFIG_ITEM_T* next;
} CONFIG_ITEM;

typedef struct CONFIGURATION_MODULE_T
{
#define CONFIGURATION_MODULE_COMMON_SIZE    64
#define CONFIGURATION_MODULE_DESC_SIZE      256
    unsigned int id;
    char name[CONFIGURATION_MODULE_COMMON_SIZE];
    char desc[CONFIGURATION_MODULE_DESC_SIZE];
    struct CONFIGURATION_MODULE_T* next;
} CONFIGURATION_MODULE;
//End added by chenyc,2017-02-27,模块配置项结构体

/* Begin added by chenyc,2017-05-24,云平台接入app开发 */
//临时访问秘钥校验请求结构体
typedef struct SOCKET_MSG_CHECK_TMP_KEY_T
{
    char protocal[PROTOCAL_SIZE];
    char tmpkey[TMP_KEY_SIZE];
    char community[COMMUNITY_SIZE];
    char area_node[AREA_NODE_SIZE];
    char msg_seq[MSG_SEQ_SIZE];
    char mac[MAC_SIZE]; //v3.1加密时补充
    int result;   //0:校验成功  1:校验失败
    uint32_t unit_id;
    uint32_t manager_account_id;
} SOCKET_MSG_CHECK_TMP_KEY;

//临时访问秘钥结构体
typedef struct SOCKET_MSG_GET_TMP_KEY_T
{
    uint32_t id;
    char tmpkey[TMP_KEY_SIZE];
    char community[COMMUNITY_SIZE];
    char area_node[AREA_NODE_SIZE];
    char begin_time[DATETIME_SIZE];
    char end_time[DATETIME_SIZE];
    uint32_t access_time;
    uint32_t allowed_time;
} SOCKET_MSG_GET_TMP_KEY;

//个人终端用户临时访问秘钥校验结果结构体
typedef struct SOCKET_MSG_PERSONNAL_GET_TMP_KEY_T
{
    uint32_t id;
    char tmpkey[TMP_KEY_SIZE];
    char area_node[AREA_NODE_SIZE];
    char begin_time[DATETIME_SIZE];
    char end_time[DATETIME_SIZE];
    char access[DEV_ACCESS_SIZE];
    uint32_t access_time;
    uint32_t allowed_time;
} SOCKET_MSG_PERSONNAL_GET_TMP_KEY;

//个人终端告警处理结构体
typedef struct SOCKET_MSG_PERSONNAL_ALARM_DEAL_T
{
    char protocal[PROTOCAL_SIZE];
    char area_node[AREA_NODE_SIZE];
    char alarm_id[ALARM_ID_SIZE];
    char user[USER_SIZE];
    char result[ALARM_RESULT_SIZE];
    char type[ALARM_DEAL_TYPE_SIZE];
    char time[DATETIME_SIZE];
    char device_name[ALARM_DEV_NAME_SIZE];
    char community[COMMUNITY_SIZE];
    int alarm_code;
    int alarm_zone;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    uint64_t trace_id;
} SOCKET_MSG_PERSONNAL_ALARM_DEAL;

//个人终端告警处理端外推送结构体
typedef struct SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE_T
{
    char mac[MAC_SIZE];
    char alarm_type[ALARM_TYPE_SIZE];
    char device_location[LOCATION_SIZE];
    char community[COMMUNITY_SIZE];
    int alarm_code;
    int alarm_zone;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    uint64_t trace_id;
} SOCKET_MSG_PERSONNAL_ALARM_DEAL_OFFLINE;

//社区终端告警处理端外推送结构体
typedef struct SOCKET_MSG_ALARM_DEAL_OFFLINE_T
{
    char mac[MAC_SIZE];
    char alarm_type[ALARM_TYPE_SIZE];
    char device_location[LOCATION_SIZE];
    uint32_t manager_account_id;
    int alarm_code;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    int alarm_zone;
    uint32_t unit_id;
    uint64_t trace_id;
} SOCKET_MSG_ALARM_DEAL_OFFLINE;



//绑定码结构体
typedef struct SOCKET_MSG_APP_CONF_T
{
    char protocal[PROTOCAL_SIZE];
    char bind_code[TMP_KEY_SIZE];
    char community[COMMUNITY_SIZE];
    char area_node[AREA_NODE_SIZE];
    char config_url[URL_SIZE];
    char msg_seq[MSG_SEQ_SIZE];
    uint32_t app_no;
} SOCKET_MSG_APP_CONF;

//KEY访问设备结构体
typedef struct SOCKET_MSG_KEY_ACCESS_T
{
    char community[COMMUNITY_SIZE];
    char device_access[DEV_ACCESS_SIZE];
} SOCKET_MSG_KEY_ACCESS;

//设备地址节点结构体
typedef struct DEVICE_AREA_T
{
    char community[COMMUNITY_SIZE];
    char area_node[AREA_NODE_SIZE];
} DEVICE_AREA;
/* End added by chenyc,2017-05-24,云平台接入app开发 */

//added by chenyc,2017-08-25,个人终端用户功能开发
//个人终端用户app信息结构体
typedef struct SOCKET_MSG_PERSONNAL_APP_CONF_T
{
    char protocal[PROTOCAL_SIZE];
    char node[NODE_SIZE];  //指个人用户联动系统单元
    char user[USER_EMAIL_SIZE];  //解析xml完之后,可以是uid或者email
    char password[MD5_SIZE];
    char token[TOKEN_SIZE];//端外推送的token
    char fcm_token[TOKEN_SIZE];
    char voip_token[TOKEN_SIZE];
    char app_token[TOKEN_SIZE]; // 网关登陆验证的token
    char sip[SIP_SIZE];  //2017-11-17,支持pbx对接,索引sip->uid时新增
    char username[USER_EMAIL_SIZE];
    int version;//app上传的版本号，用于兼容处理 通用的版本号ios/android一样
    int  mobile_type;   //参见 csmain::AppType
    char msg_seq[MSG_SEQ_SIZE];
    uint32_t role;//add by chenzhx ********
    int  lastread_message_id;
    int  is_expire;
    char app_version[32]; //app版本号，是各个上架时候的版本
    int  id_active;
    uint32_t manager_account_id;//add by chenzhx ********
    uint32_t unit_id;
    char language[32]; //app语言
    char oem_name[USER_SIZE];  //OEM
    char email[65];
    char mobile_number[25];
    char uuid[64];
    int is_office;//add by chenzhx ********
    int dynamics_iv;//aes 动态iv
    char user_info_uuid[64];
    char report_user[32];
    uint32_t report_user_role;
} SOCKET_MSG_PERSONNAL_APP_CONF;

//个人终端用户app信息结构体,保留在tcp客户端连接链表中
typedef struct SOCKET_MSG_PERSONNAL_APP_NODE_T
{
    char node[NODE_SIZE];  //指个人用户联动系统单元
    char user[USER_EMAIL_SIZE];  //uid或者邮箱; modify 2017-10-12,已经将可能的邮箱地址在app接入的时候改成uid了
    char password[MD5_SIZE];
    char token[TOKEN_SIZE];
    char fcm_token[TOKEN_SIZE];
    char voip_token[TOKEN_SIZE];
    char app_token[TOKEN_SIZE];
    char username[USER_EMAIL_SIZE];
    int  mobile_type;   //参见 csmain::AppType
    int  motion_recv_type;   //参见 csmain::MotionRecvType
    uint32_t role;//账户类型
    int version;//app上传的版本号，用于兼容处理
    char app_version[32]; //app版本号，是各个上架时候的版本
    int  id_active;
    uint32_t manager_account_id;//add by chenzhx ********
    uint32_t unit_id;
    char language[32]; //app语言
    char uuid[64];
    char user_info_uuid[64];
    char report_user[32];
    uint32_t report_user_role;
} SOCKET_MSG_PERSONNAL_APP_NODE;

//个人终端用户设备请求联动单元设备列表结构体
typedef struct SOCKET_MSG_PERSONNAL_DEV_LIST_T
{
    char protocal[PROTOCAL_SIZE];
    char msg_seq[MSG_SEQ_SIZE];
    int result;   //0:查询成功  -1:查询失败
} SOCKET_MSG_PERSONNAL_DEV_LIST;

//个人终端用户设备列表结构体
typedef struct PERSONNAL_DEVICE_SIP_T
{
    char name[USER_SIZE];
    char sip_account[SIP_SIZE];
    char ip[IP_SIZE];
    char mac[MAC_SIZE];
    char rtsp_password[RTSP_PWD_SIZE];
    char uuid[64];
    int  type;
} PERSONNAL_DEVICE_SIP;

//社区终端用户设备列表结构体
typedef struct COMMUNITY_DEVICE_SIP_T
{
    char name[USER_SIZE];
    char sip_account[SIP_SIZE];
    char ip[IP_SIZE];
    char mac[MAC_SIZE];
    char rtsp_password[RTSP_PWD_SIZE];
    char room_num[16];
    char uuid[64];
    int  type;
} COMMUNITY_DEVICE_SIP;

//账号信息
typedef struct ACCOUNT_MSG_T
{
    char name[USER_SIZE];
    char sip_account[SIP_SIZE];
    char rtsp_password[RTSP_PWD_SIZE];
    char room_num[16];
    char node[NODE_SIZE];
    int role;
    uint32_t manager_id;
    uint32_t unit_id;
    uint32_t type;
} ACCOUNT_MSG;


//v3.4
//设备码结构体
typedef struct SOCKET_MSG_DEVICE_CODE_T
{
#define DEVICE_CODE_SIZE 16
    char mac[MAC_SIZE];
    char device_code[DEVICE_CODE_SIZE];
    char firmware_code[8];//固件码
} SOCKET_MSG_DEVICE_CODE;


typedef struct OpenDoorPrivateKeyInfo_T
{
    uint32_t unit_id;
    uint32_t room_id;
    char account_name[128];
    char node[32];
} OpenDoorPrivateKeyInfo;



#endif
