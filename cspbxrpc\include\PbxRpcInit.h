#ifndef __CSPBXRPCSERVER_INIT_H__
#define __CSPBXRPCSERVER_INIT_H__

#include "ConfigFileReader.h"

#define MAX_RLDB_CONN 10
#define CONF_COMMON_LEN 64

enum OEM_TYPE
{
    OEM_AKUVOX = 1,
    OEM_DISCREET = 2,
};

typedef struct AKCS_CONF_T
{
    // 本机配置信息 
    char cspbxrpc_outer_ip[CONF_COMMON_LEN];
    int log_level; //日志打印级别 LOG_LEVEL_E
    char log_file[CONF_COMMON_LEN];

    // DB配置项 
    char akcs_db_ip[CONF_COMMON_LEN];
    char log_db_ip[CONF_COMMON_LEN];
    char mapping_db_ip[CONF_COMMON_LEN];
    char db_username[CONF_COMMON_LEN];
    char db_password[CONF_COMMON_LEN];
    char akcs_db_database[CONF_COMMON_LEN];
    char log_db_database[CONF_COMMON_LEN];
    char mapping_db_database[CONF_COMMON_LEN];
    int akcs_db_port;
    int log_db_port;
    int mapping_db_port;
    // 推送服务器net信息,格式=ip:port
    char push_server_addr[CONF_COMMON_LEN];
    // web服务器net信息,格式=ip
    char web_server_addr[CONF_COMMON_LEN];
    // web服务器net信息,格式=ipv6
    char web_server_ipv6_addr[CONF_COMMON_LEN];
    // Etcd服务器net信息,格式=ip1:port1;ip2:port2;...
    char etcd_server_addr[128];
    // 会话服务器net信息,格式=ip:port
    char session_server_addr[CONF_COMMON_LEN];

    // 网关的编号
    char gateway_code[16];
    // OEM名称,用于区分推送服务器
    char oem_name[32];
    short oem_num;
    // OEM push AESkey
    char push_AESkey[33];

    char nsq_topic[CONF_COMMON_LEN];

    int reg_etcd;
    char tz_md5[CONF_COMMON_LEN];
    char tz_data_md5[CONF_COMMON_LEN];
    char tz_data_md5_old[CONF_COMMON_LEN];
    int offline_notify;

    int is_aws;

    char linker_nsq_topic[CONF_COMMON_LEN];
    char voice_server_ipv4[64];
    char voice_server_ipv6[64];
    char linker_nsq_ip[CONF_COMMON_LEN];
    
    char server_tag[CONF_COMMON_LEN];
    int server_area;

    //不进行呼叫限制,线上场景可能有同个小区可以相互呼叫的场景，加个开关可以随时调整
    int call_no_limit;
    int rpc_server_num;
} AKCS_CONF;


void DbConfInit(CConfigFileReader& config_file);
int ConfInit();
int DaoInit();
int DaoReInit();
int LogDeliveryInit();

#endif
