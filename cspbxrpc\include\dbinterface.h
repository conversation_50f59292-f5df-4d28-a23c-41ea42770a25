#ifndef __AKCS_DBINTERFACE_H__
#define __AKCS_DBINTERFACE_H__

// common
#include "dbinterface/AbnormalMacStatus.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/AccountAccess.h"
#include "dbinterface/AccountMap.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/AlexaToken.h"
#include "dbinterface/AmenityReservation.h"
#include "dbinterface/AppCallDndDB.h"
#include "dbinterface/AppLoginLog.h"
#include "dbinterface/AppManualRtsp.h"
#include "dbinterface/AppPushTokenDB.h"
#include "dbinterface/AwsRedirect.h"
#include "dbinterface/CommPerPrivateKey.h"
#include "dbinterface/CommPerRfKey.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/CommunityCallRule.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/ContactBlock.h"
#include "dbinterface/ContactFavorite.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/DbCommonSt.h"
#include "dbinterface/Delivery.h"
#include "dbinterface/DeliveryAccess.h"
#include "dbinterface/DeliveryIDAccess.h"
#include "dbinterface/DevLoginLog.h"
#include "dbinterface/DevOfflineLog.h"
#include "dbinterface/DevRtspLog.h"
#include "dbinterface/DevUpdateUserLogDB.h"
#include "dbinterface/DeviceForRegister.h"
#include "dbinterface/DistributorInfo.h"
#include "dbinterface/DormakabaLock.h"
#include "dbinterface/DtPbxServer.h"
#include "dbinterface/ErrorConnectDB.h"
#include "dbinterface/FaceMngDB.h"
#include "dbinterface/FeaturePlan.h"
#include "dbinterface/InsToken.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/LadderControl.h"
#include "dbinterface/MacPool.h"
#include "dbinterface/ManageFeature.h"
#include "dbinterface/Message.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/OfflineResendLog.h"
#include "dbinterface/PbxServer.h"
#include "dbinterface/PcapCaptureControl.h"
#include "dbinterface/PendingRegUser.h"
#include "dbinterface/PerNodeDevices.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/PersonalAccountSingleInfo.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalIDAccess.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/PmAccountMap.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/PubPrivateKeyList.h"
#include "dbinterface/PubRfcardKeyList.h"
#include "dbinterface/Shadow.h"
#include "dbinterface/Sip.h"
#include "dbinterface/Staff.h"
#include "dbinterface/StaffAccess.h"
#include "dbinterface/StaffIDAccess.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/ThirdParty.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/ThirdPartyLockAccount.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/TmpLoginJpFromScloudLog.h"
#include "dbinterface/Token.h"
#include "dbinterface/UUID.h"
#include "dbinterface/UpgradeRomDevices.h"
#include "dbinterface/UpgradeRomVersion.h"
#include "dbinterface/UserAccessGroup.h"
#include "dbinterface/Verification.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/VideoLength.h"
#include "dbinterface/VideoList.h"
#include "dbinterface/VideoScheduleDB.h"
#include "dbinterface/VisitorIDAccess.h"

// resident
#include "dbinterface/resident/AmenityDevice.h"
#include "dbinterface/resident/CommunityDeviceContact.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

// office
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"

// new-office
#include "dbinterface/new-office/OfficeAccessGroup.h"
#include "dbinterface/new-office/OfficeAccessGroupDevice.h"
#include "dbinterface/new-office/OfficeAccessGroupHoliday.h"
#include "dbinterface/new-office/OfficeAccessGroupPersonnel.h"
#include "dbinterface/new-office/OfficeCompany.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeDeliveryAccessFloor.h"
#include "dbinterface/new-office/OfficeDeliveryAccessGroup.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"
#include "dbinterface/new-office/OfficeFace.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"
#include "dbinterface/new-office/OfficeGroupSequenceCall.h"
#include "dbinterface/new-office/OfficeHoliday.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"
#include "dbinterface/new-office/OfficePinCode.h"
#include "dbinterface/new-office/OfficeRfCard.h"
#include "dbinterface/new-office/OfficeTempKey.h"

// log
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/Log/PersonalMotion.h"

// mapping
#include "dbinterface/mapping/PhoneMapping.h"

#endif
