#csmain conf
cspbxrpc_outer_ip=*************

#db conf
#akcs db conf
akcs_db_ip=***********
akcs_db_port=3306
akcs_db_database=AKCS

#log db conf
log_db_ip=***********
log_db_port=3306
log_db_database=LOG

#mapping db conf
mapping_db_ip=**************
mapping_db_port=3306
mapping_db_database=AKCSMapping

#common db conf
db_username=dbuser01

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#cspush conf
cspush_net=***********:8000

#配置OEM，用于推送服务的区分,空值就是akuvox
oem_name=Akuvox

#配置和cspush推送的aes加密密钥
push_aeskey=

#AWS
is_aws=0

#log 加密
log_encrypt=0
log_trace=1

#rpc server number
rpc_server_num=6



