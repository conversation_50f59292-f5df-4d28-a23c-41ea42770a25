#!/bin/bash
ACMD="$1"
CSPBXRPC_BIN='/usr/local/akcs/cspbxrpc/bin/cspbxrpc'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_cspbxrpc()
{
    nohup $CSPBXRPC_BIN >/dev/null 2>&1 &
    echo "Start cspbxrpc successful"
    if [ -z "`ps -fe|grep "cspbxrpcrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/cspbxrpc/scripts/cspbxrpcrun.sh >/dev/null 2>&1 &
    fi
}
stop_cspbxrpc()
{
    echo "Begin to stop cspbxrpcrun.sh"
    cspbxrpcrunid=`ps aux | grep -w cspbxrpcrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${cspbxrpcrunid}" ];then
	    echo "cspbxrpcrun.sh is running at ${cspbxrpcrunid}, will kill it first."
	    kill -9 ${cspbxrpcrunid}
    fi
    echo "Begin to stop cspbxrpc"
    kill -9 `pidof cspbxrpc`
    sleep 2
    echo "Stop cspbxrpc successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 8800 | grep cspbxrpc | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_cspbxrpc
    else
        echo "cspbxrpc is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 8800 | grep cspbxrpc | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "cspbxrpc is already stopping"
    else
        stop_cspbxrpc
    fi
    ;;
  restart)
    stop_cspbxrpc
    sleep 1
    start_cspbxrpc
    ;;
  status)
    cnt=`ps -ef |grep -E "cspbxrpc$" | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m cspbxrpc is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m cspbxrpc is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

