#!/bin/bash
PROCESS_NAME=cspbxrpc
PROCESS_START_CMD="/usr/local/akcs/cspbxrpc/scripts/cspbxrpcctl.sh start"
PROCESS_PID_FILE=/var/run/cspbxrpc.pid
LOG_FILE=/var/log/cspbxrpc_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/cspbxrpc/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/cspbxrpc/scripts/log_back.sh"
cspbxrpclog_path="/var/log/cspbxrpclog"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done
