#include "AkLogging.h"
#include "HangupAppPush.h"
#include "NotifyHangupMsg.h"
#include "dbinterface.h"

void HangupAppPush::HangupApp(const HangupAppRequest& request)
{
    std::string caller_nickname = GetCallerNickName(request.caller_sip(), request.caller_sip(), request.x_name());
    
    CHangUpAppMsg hangup_app_msg(request.caller_sip(), request.callee_sip(), caller_nickname, request.msg_traceid(), request.app_type());
    
    GetAppWakeupMsgControlInstance()->AddHangUpAppMsg(std::move(hangup_app_msg));
    
    return;
}

// 查找是不是转流的,如果是室内机转流的x_caller是门口机的sip,需要改为来自门口机呼叫
// x_name为转流门口机的名称,x_caller为转流门口机的sip
std::string HangupAppPush::GetCallerNickName(const std::string& caller_sip, const std::string& x_caller, const std::string& x_name)
{
    // 如果x_name有值，直接返回x_name
    if (x_name.size() > 0)
    {
        return x_name;
    }

    // 如果x_caller有值，优先根据x_caller获取设备名称
    std::string caller_nick_name;
    if (!x_caller.empty())
    {
        dbinterface::ProjectUserManage::GetDevLocation(x_caller, caller_nick_name);
        if (!caller_nick_name.empty())
        {
            return caller_nick_name;
        }
    }
    
    // 不为转流设备,通过主叫sip查询名称
    dbinterface::ProjectUserManage::GetSipName(caller_sip, caller_nick_name);
    
    return caller_nick_name;
}


