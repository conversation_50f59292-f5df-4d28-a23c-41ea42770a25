#ifndef __CSPBXRPC_HANGUP_APP_PUSH__
#define __CSPBXRPC_HANGUP_APP_PUSH__

#include <string>
#include "AkLogging.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "AK.PBX.grpc.pb.h"

using AK::PBX::HangupAppRequest;

class HangupAppPush
{
public:
    static void HangupApp(const HangupAppRequest& request);
          
private:
    static std::string GetCallerNickName(const std::string& caller_sip, const std::string& x_caller, const std::string& x_name);
};

#endif
