#include "dbinterface.h"
#include "QueryLandLineNumber.h"

std::pair<std::string, std::string> QueryLandlineNumber::GetLandlineNumber(const QueryLandlineNumberRequest& request)
{
    std::string phone;
    std::string phone_code;
    std::string uid = request.sip();
    
    // 群呼 根据 sipgroup 找到 sip
    if (request.type() == SipAccountType::GROUP_SIP)
    {
        uid = dbinterface::Sip::GetNodeByGroupFromSip2(request.sip());
    }

    int dev_type = dbinterface::ProjectUserManage::GetUserTypeBySip(uid);
    if (dev_type == csmain::OFFICE_APP || dev_type == csmain::OFFICE_DEV)
    {
        OfficeAccount account;
        if (dbinterface::OfficePersonalAccount::GetUidAccount(uid, account) == 0)
        {
            phone = account.phone;
            phone_code = account.phone_code;
        }
    }
    else
    {
        phone = dbinterface::ResidentPersonalAccount::GetPhoneBySip(request.sip(), request.type(), phone_code);
    }
    
    AK_LOG_INFO << "QueryLandlineNumber SipAccountType = " <<  request.type()  << ", dev type = " << dev_type << ", account = " << uid << ", phone code = " << phone_code << ", phone = " << phone;
    return std::make_pair(phone, phone_code);
}
