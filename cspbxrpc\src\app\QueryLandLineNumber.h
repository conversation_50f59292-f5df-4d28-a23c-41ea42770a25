#ifndef __CSPBXRPC_QUERY_LANDLINE_NUMBER__ 
#define __CSPBXRPC_QUERY_LANDLINE_NUMBER__

#include <string>
#include "AkLogging.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "AK.PBX.grpc.pb.h"

using AK::PBX::QueryLandlineNumberRequest;

class QueryLandlineNumber 
{
public:
    static std::pair<std::string, std::string> GetLandlineNumber(const QueryLandlineNumberRequest& request);
};


#endif

