#include "dbinterface.h"
#include "QueryLandLineStatus.h"

int QueryLandlineStatus::GetLandlineStatus(QueryLandlineStatusRequest& request)
{
    int ret = 0;
    
    int sip_type = dbinterface::ProjectUserManage::GetUserTypeBySip(request.caller_sip());
    if (sip_type == csmain::OFFICE_APP || sip_type == csmain::OFFICE_DEV)
    {
        OfficeAccountList account_list;
        dbinterface::OfficePersonalAccount::GetPhoneAccountList(request.phone_number(), account_list);
        
        OfficeDevPtr dev = nullptr;
        if (0 != dbinterface::OfficeDevices::GetSipDev(request.caller_sip(), dev))
        {
            AK_LOG_INFO << "[pbx] QueryLandlineStatus, GetSipDev faied, phone = " << request.phone_number() << ", caller = " << request.caller_sip() << ", status = " << ret;
            return 0;
        }
        
        for (const auto &account : account_list)
        {
            if (account.office_id == dev->office_id && account.active == 1 && account.is_expire == 0)
            {
                // 判断办公落地开关是否开启
                OfficeInfoPtr office_info = std::make_shared<OfficeInfo>(account.office_id);
                if (office_info->EnableLandline())
                {
                    ret = 1;
                    break;
                }
            }
        }
    }
    else
    {
        ret = dbinterface::ResidentPersonalAccount::GetNodeActiveByPhone(request.phone_number(), request.caller_sip());
    }
    
    AK_LOG_INFO << "[pbx] QueryLandlineStatus, phone = " << request.phone_number() << ", caller = " << request.caller_sip() << ", status = " << ret;

    return ret;
}
