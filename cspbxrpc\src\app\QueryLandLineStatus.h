#ifndef __CSPBXRPC_QUERY_LANDLINE_STATUS__ 
#define __CSPBXRPC_QUERY_LANDLINE_STATUS__

#include <string>
#include "AkLogging.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "AK.PBX.grpc.pb.h"

using AK::PBX::QueryLandlineStatusRequest;

class QueryLandlineStatus
{
public:
    static int GetLandlineStatus(QueryLandlineStatusRequest& request);
    
private:
    QueryLandlineStatus() = delete;
    ~QueryLandlineStatus() = delete;

    static bool AccountExpire(const std::string& caller, const std::string& callee);
};


#endif
