#include "dbinterface.h"
#include "QueryMainSiteSip.h"

std::string QueryMainSiteSip::GetMainSiteSip(const QueryMainSiteSipRequest& request)
{
    std::string main_site_sip = request.sip();

    if (0 != dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(request.sip(), main_site_sip))
    {
        AK_LOG_WARN << "[pbx] QueryMainSiteSip failed, current_site = " << request.sip();
    }

    AK_LOG_INFO << "[pbx] QueryMainSiteSip, current_site = " << request.sip() << ", main_site =" << main_site_sip;

    return main_site_sip;
}
