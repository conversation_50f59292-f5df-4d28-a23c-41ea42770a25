#include "dbinterface.h"
#include "QuerySipInfo.h"

std::string QuerySipInfo::GetTranferCallNickName(const QuerySipInfoRequest& request)
{
    std::string nikename;
    
    //转流的,如果是室内机转流需要查下门口机信息，在唤醒app时候带给家居
    dbinterface::ProjectUserManage::GetDevLocation(request.sip(), nikename);

    AK_LOG_INFO << "[pbx] PbxQuerySipInfo only devices sip, x-caller sip = " << request.sip() << ", nikename = " << nikename;
    return nikename;
}
