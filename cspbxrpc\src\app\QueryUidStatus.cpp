#include "QueryUidStatus.h"
#include "AppCallStatus.h"
#include "CallBlock.h"
#include "AppPushToken.h"
#include "dbinterface.h"
#include "session_rpc_client.h"
#include "csmain_rpc_client.h"
#include "csmain_rpc_client_mng.h"

extern SmRpcClient* g_sm_client_ptr;

static const std::map<APP_STATE, std::string> KAppStatusMap = 
        {{APP_STATE_OFFLINE, "offline"}, {APP_STATE_ONLINE, "online"}, {APP_STATE_DND, "DND"}, {APP_STATE_CALLER_EXPIRE, "caller expire"}, {APP_STATE_CALLEE_EXPIRE, "callee expire"},
        {DEV_STATE_CALLEE_NOT_SUPPORT_SRTP, "callee not support srtp"}, {APP_STATE_CALL_BLOCK, "call block"}, {APP_STATE_LOGOUT, "app logout"}, {APP_STATE_ONLINE_PUSH, "online push"}};

int QueryUidStatus::GetUidStatus(QueryUidStatusRequest& request) 
{
    std::string caller = request.caller();
    std::string main_site = request.uid();
    std::string current_site = request.original_callee();

    int uid_status = AppCallStatus::GetInstance().GetAppState(current_site);

    if (uid_status == APP_STATE_DND)
    {
        return uid_status;
    }
    else if (CallBlock::GetInstance().IsCallBlock(caller, current_site))
    {
        uid_status = APP_STATE_CALL_BLOCK;
    }
    else if (request.app_type() == int(AkcsDeviceType::AKCS_DEVICE_TYPE_APP) && !GetAppPushTokenInstance()->AppPushTokenExist(main_site))
    {
        // 对讲账号根据pushtoken是否判断logout, 家居和sdk的账号不能对pushtoken进行判断
        uid_status = APP_STATE_LOGOUT;
    }
    else if (dbinterface::ResidentPersonalAccount::CheckAccountIsExpire(caller))
    {
        uid_status = APP_STATE_CALLER_EXPIRE;
    }
    else if (dbinterface::ResidentPersonalAccount::CheckAccountIsExpire(current_site))
    {
        uid_status = APP_STATE_CALLEE_EXPIRE;
    }
    else if (request.app_type() == int(AkcsDeviceType::AKCS_DEVICE_TYPE_APP_SDK) && dbinterface::AppPushToken::CheckOnlinePushSwitch(main_site))
    {
        uid_status = APP_STATE_ONLINE_PUSH;
    }
    else
    {
        if (request.app_type() == int(AkcsDeviceType::AKCS_DEVICE_TYPE_APP))
        {
            // 向csmain查询app Dclient在线状态
            std::string sid = g_sm_client_ptr->QueryUid(main_site);
            MainRpcClientPtr csmain_grpc_client = MainRpcClientMng::Instance()->getRpcClientInstance(sid);
            if (csmain_grpc_client)
            {
                uid_status = csmain_grpc_client->QueryAppDclientStatus(main_site, request.msg_traceid());
                if (uid_status == -1)
                {
                    uid_status = (int)APP_STATE_OFFLINE;
                }
            }
        }
    }
    
    AK_LOG_INFO << "QueryUidStatus caller = " << request.caller() << ", main_site = " << request.uid() 
                << ", current_callee = " << request.original_callee() << ", callee type = " << request.app_type()
                << ", status code = " << uid_status << ", status msg = " << KAppStatusMap.at((APP_STATE)uid_status) 
                << ", trace_id = " << request.msg_traceid();

    return uid_status;
}
