#ifndef __CSPBXRPC_QUERY_UID_STATUS__  
#define __CSPBXRPC_QUERY_UID_STATUS__

#include <map>
#include <string>
#include "AkLogging.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "AK.PBX.grpc.pb.h"

using AK::PBX::QueryUidStatusRequest;

class QueryUidStatus
{
public:
    static int GetUidStatus(QueryUidStatusRequest& request);

private:
    QueryUidStatus() = delete;
    ~QueryUidStatus() = delete;

};


#endif
