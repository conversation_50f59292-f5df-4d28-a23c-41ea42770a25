#include "WakeupAppPush.h"
#include "NotifyWakeupMsg.h"
#include "dbinterface.h"

void WakeupAppPush::WakeupApp(const WakeupAppRequest& request)
{
    std::string nick_name_location = GetCallerNickName(request.caller_sip(), request.x_caller(), request.x_name());

    CWakeUpAppMsg wakeup_msg(request.caller_sip(), request.callee_sip(), nick_name_location, request.msg_traceid(), request.app_type(), request.timestamp());
    
    GetAppWakeupMsgControlInstance()->AddWakeUpAppMsg(std::move(wakeup_msg));
    
    return;
}

std::string WakeupAppPush::GetCallerNickName(const std::string& caller_sip, const std::string& x_caller, const std::string& x_name)
{
    // 如果x_name有值，直接返回x_name
    if (!x_name.empty())
    {
        return x_name;
    }

    // 如果x_caller有值，优先根据x_caller获取设备名称
    std::string caller_nick_name;
    if (!x_caller.empty())
    {
        dbinterface::ProjectUserManage::GetDevLocation(x_caller, caller_nick_name);
        if (!caller_nick_name.empty())
        {
            return caller_nick_name;
        }
    }

    // 不为转流设备,通过主叫sip查询名称
    dbinterface::ProjectUserManage::GetSipName(caller_sip, caller_nick_name);

    return caller_nick_name;
}

