#include "CallHistory.h"
#include "WriteCallHistory.h"
#include "dbinterface.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

void WriteCallHistory::WriteHistory(const WriteCallHistoryRequest& request)
{
    PbxCallHistory history;
    memset(&history, 0, sizeof(history));

    history.bill_second = request.bill_second();
    Snprintf(history.caller, sizeof(history.caller), request.caller().c_str());
    Snprintf(history.caller_name, sizeof(history.caller_name), request.caller_name().c_str());
    //下面两个选项和pbx相反，后面和pbx协商改为统一的
    Snprintf(history.callee, sizeof(history.callee), request.callee().c_str());
    Snprintf(history.called, sizeof(history.called), request.called().c_str());
    Snprintf(history.start_time, sizeof(history.start_time), request.start_time().c_str());
    Snprintf(history.answer_time, sizeof(history.answer_time), request.answer_time().c_str());
    Snprintf(history.freeswitch_node, sizeof(history.freeswitch_node), request.freeswitch_node().c_str());
    Snprintf(history.caller_ops_node, sizeof(history.caller_ops_node), request.caller_ops_node().c_str());
    Snprintf(history.callee_ops_node, sizeof(history.callee_ops_node), request.callee_ops_node().c_str());
    Snprintf(history.group_call_list, sizeof(history.group_call_list), request.group_call_list().c_str());
    Snprintf(history.call_trace_id, sizeof(history.call_trace_id), request.call_trace_id().c_str());

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(history.caller, sip_info);
    AK_LOG_INFO << "[pbx] WriteCallHistory, project_type=" << sip_info.project_type
                << ", caller=" << history.caller << ", caller_name = " << history.caller_name 
                << ", callee = " << history.callee << ", pickuped callee=" << history.called
                << ", call_trace_id = " << history.call_trace_id;

    if (sip_info.project_type == project::OFFICE)
    {
        GetCallHistoryInstance()->HandlePbxPutCallHistoryForOffice(&history);
        GetCallHistoryInstance()->WriteDBCallHistory(&history, gstAKCSLogDelivery.call_history_delivery);
    }
    else if(sip_info.project_type == project::OFFICE_NEW)
    {
        GetCallHistoryInstance()->HandlePbxPutCallHistoryForNewOffice(&history);
        GetCallHistoryInstance()->WriteDBCallHistory(&history, gstAKCSLogDelivery.call_history_delivery);
    }
    else
    {
        GetCallHistoryInstance()->HandlePbxPutCallHistory(&history);
        GetCallHistoryInstance()->WriteDBCallHistory(&history, gstAKCSLogDelivery.call_history_delivery);
    }

    return;
}

