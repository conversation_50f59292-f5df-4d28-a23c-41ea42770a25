#ifndef __CSPBXRPC_WRITE_CALL_HISTORY__ 
#define __CSPBXRPC_WRITE_CALL_HISTORY__

#include <string>
#include "AkLogging.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "AK.PBX.grpc.pb.h"

using AK::PBX::WriteCallHistoryRequest;

class WriteCallHistory
{
public:
    static void WriteHistory(const WriteCallHistoryRequest& request);
    static void SetCallGroupType(PbxCallHistory& history, const std::string& call_trace_id, const std::string& callee);
};


#endif
