#include "stdafx.h"
#include "PbxRpcInit.h"
#include "AppPushToken.h"
#include "MobileToken.h"
#include "ConnectionPool.h"
#include "util.h"
#include "dbinterface/Token.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"


#define TABLE_NAME_APP_PUSH_TOKEN   "AppPushToken"

extern AKCS_CONF gstAKCSConf;

CAppPushToken* GetAppPushTokenInstance()
{
    return CAppPushToken::GetInstance();
}

CAppPushToken* CAppPushToken::instance = NULL;

CAppPushToken::CAppPushToken()
{

}

CAppPushToken::~CAppPushToken()
{

}

CAppPushToken* CAppPushToken::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAppPushToken();
    }

    return instance;
}

bool CAppPushToken::AppPushTokenExist(const std::string& uid)
{
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(uid, token_info))
    {
        return true;
    }
    return false;
}

int CAppPushToken::GetAppPushTokenByUid(const std::string& uid, CMobileToken& mobile_token)
{
    int is_multi_site = dbinterface::ProjectUserManage::IsAccountMultiSite(uid);
    
    AppPushTokenInfo token_info;
    memset(&token_info, 0, sizeof(token_info));
    if (0 == dbinterface::AppPushToken::GetAppPushTokenByUid(uid, token_info))
    {
        mobile_token.setMobileType(token_info.mobile_type);
        mobile_token.setFcmToken(token_info.fcm_token);
        mobile_token.setToken(token_info.token);
        mobile_token.setVoipToken(token_info.voip_token);
        mobile_token.setCommonVersion(token_info.common_version);
        mobile_token.setLanguage(token_info.language);
        mobile_token.setOemName(token_info.oem_name);
        mobile_token.setAppOem(token_info.app_oem);
        mobile_token.setIsDyIv(token_info.is_dy_iv);
        mobile_token.setIsMultiSite(is_multi_site);
        return 0;
    }

    return -1;
}
