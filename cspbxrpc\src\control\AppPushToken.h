#ifndef __APP_PUSH_TOKEN_H__
#define __APP_PUSH_TOKEN_H__

#include <cstring>
#include "MobileToken.h"
#include "dbinterface/AppPushTokenDB.h"
#include <boost/noncopyable.hpp>

class CAppPushToken : boost::noncopyable
{
public:
    CAppPushToken();
    ~CAppPushToken();

    static CAppPushToken* GetInstance();
    bool AppPushTokenExist(const std::string& uid);
    int GetAppPushTokenByUid(const std::string& uid, CMobileToken& cToken);
private:
    static CAppPushToken* instance;
};

CAppPushToken* GetAppPushTokenInstance();

#endif //__APP_TOKEN_H__
