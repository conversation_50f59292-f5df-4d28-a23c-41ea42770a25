#include "PbxRpcInit.h"
#include "CallBlock.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ContactBlock.h"
#include "dbinterface/Account.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/CommunityInfo.h"
#include "util_string.h"

extern AKCS_CONF gstAKCSConf;

CallBlock& CallBlock::GetInstance()
{
	static CallBlock call_block;
	return call_block;
}

int CallBlock::GetNodeInfoBySip(int user_type, const std::string& sip, SipNodeInfo& node_info)
{
    if(user_type == csmain::COMMUNITY_DEV)
    {
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetDevicesBySip(sip, dev))
        {
            return -1;
        }

        if(strlen(dev.node) == 0)
        {
            //非家庭下的设备
            return 0;
        }
        
        node_info.mng_id = dev.project_mng_id;
        ResidentPerAccount account_info;
        dbinterface::ResidentPersonalAccount::GetUidAccount(dev.node, account_info); 
        snprintf(node_info.node_uuid, sizeof(node_info.node_uuid), "%s", account_info.uuid);
        snprintf(node_info.node, sizeof(node_info.node), "%s", account_info.account);
    }
    else if(user_type == csmain::COMMUNITY_APP || user_type == csmain::PERSONNAL_APP)
    {
        ResidentPerAccount account_info;
        memset(&account_info, 0, sizeof(account_info));
        if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(sip, account_info))
        {
            return -1;
        }
        if(account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || account_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
        {
            snprintf(node_info.node_uuid, sizeof(node_info.node_uuid), "%s", account_info.uuid);
            snprintf(node_info.node, sizeof(node_info.node), "%s", account_info.account);
            node_info.mng_id = account_info.parent_id;
        }
        else if(account_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || account_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
        {
            //若是从账号
            ResidentPerAccount account_master;
            memset(&account_master, 0, sizeof(account_master));
            if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(account_info.parent_uuid, account_master))
            {
                return -1;
            }
            snprintf(node_info.node_uuid, sizeof(node_info.node_uuid), "%s", account_master.uuid);
            snprintf(node_info.node, sizeof(node_info.node), "%s", account_master.account);
            node_info.mng_id = account_master.parent_id;
        }
        else //pm app
        {
            return -1;
        }
    }

    //office dev,personal dev
    return 0;
}

std::string CallBlock::GetCommunitySipNode(const SipInfo &sipinfo)
{
    std::string node;
    if (sipinfo.sip_type == csmain::COMMUNITY_DEV)
    {
        node = sipinfo.dev_node;//呼叫限制这里 不管公共设备
    }
    else if(sipinfo.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        node = sipinfo.sip;
    }
    else //社区从
    {
        ResidentPerAccount main_account;
        memset(&main_account, 0, sizeof(main_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(sipinfo.parent_uuid, main_account))
        {
           node = main_account.account;
        }
    }
    return node;

}


//在拨号脚本的基础上再进行呼叫限制
bool CallBlock::CallLimit(const SipInfo &caller_sipinfo, const SipInfo &callee_sipinfo, const std::string &caller_node, const std::string &callee_node)
{
    if (gstAKCSConf.call_no_limit)
    {
        return false;
    }
    
    //单住户跨家庭呼叫已经在freeswitch拨号脚本拦截
    if(caller_sipinfo.project_type == project::PERSONAL)
    {
        return false;
    }

    //office放行，不同项目已经在freeswitch拨号脚本拦截
    if(caller_sipinfo.project_type == project::OFFICE || caller_sipinfo.project_type == project::OFFICE_NEW)
    {
        return false;
    }
    
    //公共设备/PM app忽略
    if(caller_sipinfo.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
        || caller_sipinfo.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT
        || caller_sipinfo.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        return false;
    }

    //公共设备/PM app忽略
    if(callee_sipinfo.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
        || callee_sipinfo.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT
        || callee_sipinfo.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        return false;
    }
    
    //不同家庭不允许呼叫
    if (callee_node != caller_node)
    {
        AK_LOG_WARN << "limit different family call. caller:"<<caller_sipinfo.sip << " callee:" << callee_sipinfo.sip 
            << " callerNode:" << caller_node << " calleeNode:" << callee_node;
        return true;
    }

    return false;
}

//判断是否进行多套房拦截，一人多套房账号需考虑室内机方案和从账号数量控制
bool CallBlock::MultiSiteLimit(const SipInfo& sip_info, const std::string& sip, const SipNodeInfo& node_info)
{
    if(sip_info.sip_type != csmain::PERSONNAL_APP && sip_info.sip_type != csmain::COMMUNITY_APP)
    {
        return false;
    }

    std::string userinfo_uuid = dbinterface::ResidentPersonalAccount::GetUserInfoUUIDByAccount(sip);
    if(0 == dbinterface::ProjectUserManage::IsMultiSiteUser(userinfo_uuid))
    {
        return false;
    }

    if(sip_info.sip_type == csmain::PERSONNAL_APP) 
    {
        //室内机方案校验
        //校验不通过 直接拦截
        if(!dbinterface::ResidentPerDevices::CheckIndoorPlan(node_info.node))
        {
            AK_LOG_INFO << "[pbx] call block, because indoor plan, sip:" << sip << ",node:" << node_info.node;
            return true;
        }
    }
    else if(sip_info.sip_type == csmain::COMMUNITY_APP)
    {
        //室内机方案校验
        //校验不通过 直接拦截
        if(!dbinterface::ResidentDevices::CheckIndoorPlan(node_info.node))
        {   
            AK_LOG_INFO << "[pbx] call block, because indoor plan, sip:" << sip << ",node:" << node_info.node;
            return true;
        }

        if(sip_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            //从账号数量控制
            CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(node_info.mng_id);
            //检查高级功能
            if (comm_info->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_FAMILYMEMBER) 
                && !comm_info->IsExpire()) 
            {
                if(!dbinterface::PersonalAccountCnf::CheckFamilyMemberControl(node_info.node, sip))
                {
                    AK_LOG_INFO << "[pbx] call block, because family member control, sip:" << sip << ",node:" << node_info.node;
                    return true;
                }
            }
        }
    }

    return false;

}


bool CallBlock::IsCallBlock(const std::string& caller, const std::string& callee)
{
    if(caller.size() == 0 || callee.size() == 0)
    {
        return false;
    }

    SipInfo sipinfo;
    SipInfo callee_sipinfo;
    dbinterface::ProjectUserManage::GetSipInfoBySip(caller, sipinfo);
    dbinterface::ProjectUserManage::GetSipInfoBySip(callee, callee_sipinfo);

    SipNodeInfo caller_node_info,callee_node_info;

    if (sipinfo.project_type == project::RESIDENCE || sipinfo.project_type == project::PERSONAL) //社区 单住户
    {
        //查询主叫的的房间主账号相关信息
        if(GetNodeInfoBySip(sipinfo.sip_type, caller, caller_node_info) != 0)
        {
            return false;
        }
        
        //查询被叫的的房间主账号相关信息
        if(GetNodeInfoBySip(callee_sipinfo.sip_type, callee, callee_node_info) != 0)
        {
            return false;
        }

        if(MultiSiteLimit(sipinfo, caller, caller_node_info) || MultiSiteLimit(callee_sipinfo, callee, callee_node_info))
        {
            //拦截
            return true;
        }

        if(sipinfo.project_type == project::RESIDENCE)
        {
            //以下为房间之间 互相呼叫的拦截, node为空的不进行
            if(strlen(caller_node_info.node) == 0 || strlen(caller_node_info.node) == 0)
            {
                return false;
            }
            
            //查询社区户户通开关是否开启
            if(!dbinterface::Account::GetCommunityContactSwitch(caller_node_info.mng_id))
            {
                //没开启户户通 进行sip呼叫限制判断
                return CallLimit(sipinfo, callee_sipinfo, caller_node_info.node, callee_node_info.node);
            }
            
            //判断被叫房间是否被主叫房间拉黑
            return dbinterface::ContactBlock::JudgeBlock(callee_node_info.node_uuid, caller_node_info.node_uuid);
        }
    }
    else if (sipinfo.project_type == project::OFFICE ) //办公 判断APP是否开启对讲功能
    {
        //主叫判断
        if(csmain::OFFICE_APP == sipinfo.sip_type)
        {
            if(!dbinterface::OfficePersonalAccount::IsEnableIntercom(sipinfo.uuid))
            {
                AK_LOG_INFO << "[pbx] call block, because caller unenable intercom, caller:" << caller;
                return true;
            }
        }
        
        if(csmain::OFFICE_APP == callee_sipinfo.sip_type)
        {
            if(!dbinterface::OfficePersonalAccount::IsEnableIntercom(callee_sipinfo.uuid))
            {
                AK_LOG_INFO << "[pbx] call block, because callee unenable intercom, callee:" << callee;
                return true;
            }
        }
    }
    else if (sipinfo.project_type == project::OFFICE_NEW)
    {
        OfficePersonnelInfo info;
        
        //主叫判断
        if(IsNewOfficeCallBlock(sipinfo))
        {
            return true;
        }
        
        //被叫判断
        if(IsNewOfficeCallBlock(callee_sipinfo))
        {
            return true;
        }
    }
    
    return false;
}

bool CallBlock::NewOfficeCheckProjectHighendDevOnline(const std::string& project_uuid)
{
    //获取高端机型的固件开头列表
    FirmwareList firmware_list;
    dbinterface::VersionModel::GetHighendDevList(firmware_list);
    std::string highend_firm_list = ListToSeparatedFormatString(firmware_list);
    //判断是否有高端设备在线过
    if(dbinterface::ResidentDevices::CheckProjectHighendDevOnline(project_uuid, highend_firm_list))
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool CallBlock::IsNewOfficeCallBlock(const SipInfo& sip_info)
{
    if(csmain::OFFICE_APP == sip_info.sip_type)
    {
        if (sip_info.role == ACCOUNT_ROLE_OFFICE_NEW_PER)
        {
            OfficePersonnelInfo info;
            if (dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(sip_info.uuid, info) != 0){
                AK_LOG_INFO << "[pbx] call block, because query call intercom failed, call:" << sip_info.sip;
                return true;
            }
            //未开启对讲功能时
            if(info.is_smart_plus_intercom == false)
            {
                AK_LOG_INFO << "[pbx] call block, because call sip unenable intercom, sip:" << sip_info.sip;
                return true;
            }
            //对讲功能未激活或过期时
            if(!info.app_intercome_active || info.app_intercome_expire)
            {
                AK_LOG_INFO << "[pbx] call block, because call sip app_intercome unactive/expire, sip:" << sip_info.sip;
                return true;
            }
            //免费app且高端机型未上线时
            if(info.is_free_app_intercome && !NewOfficeCheckProjectHighendDevOnline(info.project_uuid))
            {
                AK_LOG_INFO << "[pbx] call block, because project highend device has not been online, call sip:" << sip_info.sip;
                return true;
            }
        }
        else if (sip_info.role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
        {
            OfficeAdminInfo info;
            if (dbinterface::OfficeAdmin::GetOfficeAdminByPersonalAccountUUID(sip_info.uuid, info) != 0) {
                AK_LOG_INFO << "[pbx] call block, because query caller intercom failed, caller:" << sip_info.sip;
                return true;
            }
            
            if (info.app_status == (int)AdminAppStatus::DISABLE)
            {
                AK_LOG_INFO << "[pbx] call block, because caller unenable admin app, caller:" << sip_info.sip;
                return true;
            }
        }
    }
    return false;
}