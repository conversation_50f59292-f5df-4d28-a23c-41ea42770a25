﻿#ifndef __CSPBXRPC_CALL_BLOCK_H__
#define __CSPBXRPC_CALL_BLOCK_H__

#include <boost/noncopyable.hpp>
#include <string>
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/VersionModel.h"

typedef struct SipNodeInfo_T {
    char node[32];
    int mng_id;
    char node_uuid[64];
    
    SipNodeInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}SipNodeInfo;


class CallBlock : private boost::noncopyable
{
    enum AccountFlags
    {
        FLAGS_COMMUNITY_CONTACT = 1 << 4,   //户户通开关
    };

public:
    static CallBlock& GetInstance();

    bool IsCallBlock(const std::string& caller, const std::string& callee);

private:
    int GetNodeInfoBySip(int user_type, const std::string& sip, SipNodeInfo& node_info);
    bool CallLimit(const SipInfo &sipinfo, const SipInfo &callee_sipinfo, const std::string &caller_node, const std::string &callee_node);
    bool MultiSiteLimit(const SipInfo& sip_info, const std::string& sip, const SipNodeInfo& node_info);
    bool IsNewOfficeCallBlock(const SipInfo& sip_info);
    bool NewOfficeCheckProjectHighendDevOnline(const std::string& project_uuid);
    std::string GetCommunitySipNode(const SipInfo &sipinfo);
};

#endif

