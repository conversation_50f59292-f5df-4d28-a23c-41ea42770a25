#include "stdafx.h"
#include "CallHistory.h"
#include "InnerMsgDef.h"
#include <boost/algorithm/string.hpp>
#include "util.h"
#include "util_string.h"
#include "dbinterface.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeCompany.h"

#define TABLE_NAME_CALL_HISTORY _T("CallHistory")

CCallHistory* GetCallHistoryInstance()
{
    return CCallHistory::GetInstance();
}

CCallHistory::CCallHistory()
{

}

CCallHistory::~CCallHistory()
{

}

CCallHistory* CCallHistory::instance = NULL;

CCallHistory* CCallHistory::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CCallHistory();
    }

    return instance;
}

void CCallHistory::HandlePbxPutCallHistory(PbxCallHistory* tmp_history)
{
    if (tmp_history == NULL)
    {
        return;
    }
    if (0 == tmp_history->bill_second)
    {
        tmp_history->is_answer = 0;
        tmp_history->status = 0;
        ::snprintf(tmp_history->bill_second_time, sizeof(tmp_history->bill_second_time), "%02d:%02d:%02d", 0, 0, 0);
    }
    else
    {
        tmp_history->is_answer = 1;
        tmp_history->status = 1;
        ::snprintf(tmp_history->bill_second_time, sizeof(tmp_history->bill_second_time), "%02d:%02d:%02d",
                   tmp_history->bill_second / 3600, tmp_history->bill_second / 60, tmp_history->bill_second % 60);
    }

    //通过sip查找昵称，可能是设备也可能是个人。先查找个人，在查找设备
    //这里需要通过数据库查找，不能单单只查找在线的数据
    //TODO:其实被叫就是所在的联动。因为个人没有办法呼叫公共设备
    int caller_type = SIP_TYPE_APP;
    int callee_type = SIP_TYPE_APP;
    std::string nick_name;
    std::string node;
    std::string caller_node;
    int manager_id = 0;

    // hager转流caller_name设置在sip header中,在freeswitch通话记录模板中赋值过了
    if (strlen(tmp_history->caller_name) == 0)
    {
        // 未赋值过则通过sip号查询caller_name
        dbinterface::ResidentPersonalAccount::GetNickNameAndNodeAndMngIDByUid(tmp_history->caller, nick_name, caller_node, manager_id);
        if (nick_name.empty())
        {
            caller_type = SIP_TYPE_DEV;
            dbinterface::ResidentDevices::GetLocationAndNodeAndMngIDBySip(tmp_history->caller, nick_name, caller_node, manager_id);
            if (nick_name.empty())
            {
                caller_type = SIP_TYPE_NONE;
                AK_LOG_WARN << "HandlePbxPutCallHistory, caller sip [" << tmp_history->caller << "] does not nick name";
                nick_name = tmp_history->caller;
            }
        }
        std::snprintf(tmp_history->caller_name, sizeof(tmp_history->caller_name), "%s", nick_name.c_str());
    }

    // 查询被叫名称
    nick_name = "";
    dbinterface::ResidentPersonalAccount::GetNickNameAndNodeByUid(tmp_history->callee, nick_name, node);
    if (nick_name.empty())
    {
        callee_type = SIP_TYPE_DEV;
        dbinterface::ResidentDevices::GetLocationAndNodeBySip2(tmp_history->callee, nick_name, node);
        if (nick_name.empty())
        {
            //被叫可能是群组号
            std::string tmp_node = dbinterface::Sip::GetNodeByGroupFromSip2(tmp_history->callee);
            if (tmp_node.size() > 0)
            {
                callee_type = SIP_TYPE_GROUP;
                //todo:目前只有群呼没有接听会出现app没有办法过滤出数据，所以添加node标识用于过滤
                node = tmp_node;
                ::snprintf(tmp_history->node, sizeof(tmp_history->node), "%s", tmp_node.c_str());
                ::snprintf(tmp_history->sipgroup, sizeof(tmp_history->sipgroup), "%s", tmp_history->callee);
                nick_name = "Family-call";
                //看接听人
                if (strlen(tmp_history->called) > 0 && tmp_history->bill_second > 0) //因为群呼时候如果只有一个账号时候，pbx会把对应值赋值到接听人
                {
                    std::string called_name;
                    std::string node;
                    int nTmpMngID = 0;//不管被叫的nMngID 因为被叫可能是手机号码

                    dbinterface::ResidentPersonalAccount::GetNickNameAndNodeAndMngIDByUid(tmp_history->called, called_name, node, nTmpMngID);
                    if (called_name.empty())
                    {
                        dbinterface::ResidentDevices::GetLocationAndNodeAndMngIDBySip(tmp_history->called, called_name, node, nTmpMngID);
                        if (called_name.empty())
                        {
                            AK_LOG_WARN << "HandlePbxPutCallHistory, called sip [" << tmp_history->called << "] does not nick name";
                            called_name = tmp_history->called;
                        }
                    }
                    nick_name += " -> ";
                    nick_name += called_name;
                    Snprintf(tmp_history->called_name, sizeof(tmp_history->called_name), called_name.c_str());
                }
            }
            else
            {
                //呼叫手机时候CalleeID是手机，但是没接听时候CalledID可能没有值，看pbx
                //CAkUserManager::GetInstance()->GetNickNameAndNodeByPhone(tmp_history->callee, nick_name, node);
                PersonalPhoneInfo phone_info;
                dbinterface::ResidentPersonalAccount::GetPhoneInfoByMngID(tmp_history->callee, manager_id, phone_info);
                node = phone_info.node;
                if (node.size() > 0)
                {
                    nick_name = phone_info.name;
                    callee_type = SIP_TYPE_PHONE;
                }
                else
                {
                    callee_type = SIP_TYPE_NONE;
                    AK_LOG_WARN << "HandlePbxPutCallHistory, callee sip [" << tmp_history->callee << "] does not nick name";
                    nick_name = tmp_history->callee;
                }

            }
        }
    }
    if (node.size() <= 0)
    {
        node = caller_node;//如果被叫没有node,则可能是公共设备，这时候赋值主叫的node
    }

    ::snprintf(tmp_history->node, sizeof(tmp_history->node), "%s", node.c_str());
    std::snprintf(tmp_history->callee_name, sizeof(tmp_history->callee_name), "%s", nick_name.c_str());
    tmp_history->mng_id = manager_id;

    if (node.size() > 0)
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, per_account))
        {
            if (per_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
            {
                Snprintf(tmp_history->project_uuid2, sizeof(tmp_history->project_uuid2), per_account.uuid);
            }
            else
            {
                dbinterface::AccountInfo account;
                if (0 == dbinterface::Account::GetAccountById(manager_id, account))
                {
                    Snprintf(tmp_history->project_uuid2, sizeof(tmp_history->project_uuid2), account.uuid);
                }
            }
        }  
        else
        {
            AK_LOG_WARN << "HandlePbxPutCallHistory, node [" << node << "] does not exist";
        }
    }
    else
    {
        //公共设备呼叫公共设备，主叫，被叫都没有node
        dbinterface::AccountInfo account;
        if (0 == dbinterface::Account::GetAccountById(manager_id, account)) 
        {
            Snprintf(tmp_history->project_uuid2, sizeof(tmp_history->project_uuid2), account.uuid);
        }        
    }

    Snprintf(tmp_history->db_delivery_uuid, sizeof(tmp_history->db_delivery_uuid), dbinterface::CallHistory::GetDbLogDeliveryUUID(tmp_history->project_uuid2).c_str());
    tmp_history->calltype = GetCallType(caller_type, callee_type);
}

void CCallHistory::HandlePbxPutCallHistoryForOffice(PbxCallHistory* tmp_history)
{
    if (tmp_history == NULL)
    {
        return;
    }
    if (0 == tmp_history->bill_second)
    {
        tmp_history->is_answer = 0;
        tmp_history->status = 0;
        ::snprintf(tmp_history->bill_second_time, sizeof(tmp_history->bill_second_time), "%02d:%02d:%02d", 0, 0, 0);
    }
    else
    {
        tmp_history->is_answer = 1;
        tmp_history->status = 1;
        ::snprintf(tmp_history->bill_second_time, sizeof(tmp_history->bill_second_time), "%02d:%02d:%02d",
                   tmp_history->bill_second / 3600, tmp_history->bill_second / 60, tmp_history->bill_second % 60);
    }

    //通过sip查找昵称，可能是用户也可能是个人。先查找个人，在查找设备
    //这里需要通过数据库查找，不能单单只查找在线的数据
    //TODO:其实被叫就是所在的联动。因为个人没有办法呼叫公共设备
    int caller_type = SIP_TYPE_APP;
    int callee_type = SIP_TYPE_APP;
    std::string nick_name;
    std::string node;
    std::string caller_node;
    int office_id = 0;

    OfficeAccount caller;
    if ( dbinterface::OfficePersonalAccount::GetUidAccount(tmp_history->caller, caller) == 0)
    {
        caller_node = caller.account;
        office_id = caller.office_id;
        nick_name = caller.name;
        tmp_history->caller_role = caller.role;
        Snprintf(tmp_history->caller_uuid, sizeof(tmp_history->caller_uuid), caller.uuid);
    }
    else
    {
        caller_type = SIP_TYPE_DEV;
        OfficeDevPtr dev = nullptr;
        dbinterface::OfficeDevices::GetSipDev(tmp_history->caller, dev);
        if (dev)
        {
            caller_node = dev->node;
            office_id = dev->office_id;
            nick_name = dev->location;
            Snprintf(tmp_history->caller_uuid, sizeof(tmp_history->caller_uuid), dev->uuid);
        }
        else
        {
            caller_type = SIP_TYPE_NONE;
            nick_name = tmp_history->caller;
        }
    }

    Snprintf(tmp_history->caller_name, sizeof(tmp_history->caller_name), nick_name.c_str());

    nick_name = "";
    OfficeAccount callee;
    if (dbinterface::OfficePersonalAccount::GetUidAccount(tmp_history->callee, callee) == 0)
    {
        node = callee.account;
        nick_name = callee.name;
        tmp_history->callee_role = callee.role;
        Snprintf(tmp_history->callee_uuid, sizeof(tmp_history->callee_uuid), callee.uuid);
    }
    else
    {
        OfficeDevPtr dev = nullptr;
        dbinterface::OfficeDevices::GetSipDev(tmp_history->callee, dev);
        if (dev)
        {
            node = dev->node;
            nick_name = dev->location;  
            callee_type = SIP_TYPE_DEV;
            Snprintf(tmp_history->callee_uuid, sizeof(tmp_history->callee_uuid), dev->uuid);
        }
        else
        {
            std::string tmp_node = dbinterface::Sip::GetNodeByGroupFromSip2(tmp_history->callee);
            if (tmp_node.size() > 0)
            {
                //todo:这里不赋值callee_uuid, 不然需要在查找一遍数据库，当前这个只有新办公会用于查找公司信息，但是新办公不会有呼叫群组号的情况，
                //除非手动配置，但是意义不大，因为新办公群组号下只有主账号，不会有室内机等。
                callee_type = SIP_TYPE_GROUP;
                //todo:目前只有群呼没有接听会出现app没有办法过滤出数据，所以添加node标识用于过滤
                node = tmp_node;
                Snprintf(tmp_history->node, sizeof(tmp_history->node), tmp_node.c_str());
                Snprintf(tmp_history->sipgroup, sizeof(tmp_history->sipgroup), tmp_history->callee);

                nick_name = "Family-call";
                //看接听人
                if (strlen(tmp_history->called) > 0 && tmp_history->bill_second > 0) //因为群呼时候如果只有一个账号时候，pbx会把对应值赋值到接听人
                {
                    std::string called_name;

                    OfficeAccount tmp_account;
                    if (dbinterface::OfficePersonalAccount::GetUidAccount(tmp_history->called, tmp_account) == 0)
                    {
                        called_name = tmp_account.name;
                    }
                    else
                    {
                        OfficeDevPtr dev = nullptr;
                        dbinterface::OfficeDevices::GetSipDev(tmp_history->called, dev);
                        if (dev)
                        {
                            called_name = dev->location;  
                        }
                        else
                        {
                            AK_LOG_WARN << "HandlePbxPutCallHistory, called sip [" << tmp_history->called << "] does not nick name";
                            called_name = tmp_history->called;
                        }
                    }
                    nick_name += " -> ";
                    nick_name += called_name;
                    Snprintf(tmp_history->called_name, sizeof(tmp_history->called_name), called_name.c_str());
                }

            }
            else
            {
                //呼叫手机时候CalleeID是手机，但是没接听时候CalledID可能没有值，看pbx
                //CAkUserManager::GetInstance()->GetNickNameAndNodeByPhone(tmp_history->callee, nick_name, node);
                OfficeAccount account;
                if (dbinterface::OfficePersonalAccount::GetPhoneAccountForOfficeid(tmp_history->callee, office_id, account) == 0)
                {
                    node = account.account;
                    nick_name = account.name;
                    callee_type = SIP_TYPE_PHONE;
                    tmp_history->callee_role = account.role;
                    Snprintf(tmp_history->callee_uuid, sizeof(tmp_history->callee_uuid), account.uuid);
                }
                else
                {
                    callee_type = SIP_TYPE_NONE;
                    AK_LOG_WARN << "HandlePbxPutCallHistory, callee sip [" << tmp_history->callee << "] does not nick name";
                    nick_name = tmp_history->callee;
                }
            }
        }
    }

    if (node.size() <= 0)
    {
        node = caller_node;//如果被叫没有node,则可能是公共设备，这时候赋值主叫的node
    }

    Snprintf(tmp_history->node, sizeof(tmp_history->node), node.c_str());
    Snprintf(tmp_history->callee_name, sizeof(tmp_history->callee_name), nick_name.c_str());
    tmp_history->mng_id = office_id;

    dbinterface::AccountInfo account;
    if (0 == dbinterface::Account::GetAccountById(office_id, account))
    {
        Snprintf(tmp_history->project_uuid2, sizeof(tmp_history->project_uuid2), account.uuid);
        Snprintf(tmp_history->db_delivery_uuid, sizeof(tmp_history->db_delivery_uuid), dbinterface::CallHistory::GetDbLogDeliveryUUID(tmp_history->project_uuid2).c_str());
    }

    tmp_history->calltype = GetCallType(caller_type, callee_type);
}

void CCallHistory::HandlePbxPutCallHistoryForNewOffice(PbxCallHistory* history)
{
    HandlePbxPutCallHistoryForOffice(history);

    // 获取company uuid
    std::string company_uuid = "";
    if ((history->calltype == CALL_TYPE_APP2APP || history->calltype == CALL_TYPE_APP2PHONE || history->calltype == CALL_TYPE_APP2DEV) &&
        strlen(history->caller_uuid) > 0)
    {
        company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(history->caller_uuid, history->caller_role);
    }
    else if ((history->calltype == CALL_TYPE_DEV2PHONE || history->calltype == CALL_TYPE_DEV2APP) && strlen(history->callee_uuid))
    {
        company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(history->callee_uuid, history->callee_role);
    }
    else if (history->calltype == CALL_TYPE_DEV2DEV)
    {
        // 优先记录 被叫设备的关联人 company_uuid
        OfficeDeviceAssignInfo assign;
        if (dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(history->callee_uuid, assign) == 0)
        {
            company_uuid = assign.office_company_uuid;
        }
        else if (dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(history->caller_uuid, assign) == 0)
        {
            company_uuid = assign.office_company_uuid;
        }
    }

    std::snprintf(history->company_uuid, sizeof(history->company_uuid), "%s", company_uuid.c_str());
    AK_LOG_DEBUG << "Get company uuid: calltype=" << history->calltype
        << ", caller=" << history->caller << ", callee=" << history->callee
        << ", company_uuid=" << company_uuid;
}

void CCallHistory::WriteGroupCallHistory(PbxCallHistory* history, int delivery)
{
    //群呼记录
    history->calltype = CALL_HISTORY_CALL_TYPE::CALL_TYPE_GROUP_CALL;
    dbinterface::CallHistory::AddCallHistory(history, delivery);
}

void CCallHistory::WriteGroupEachCallHistory(PbxCallHistory *history, int delivery)
{
    //群呼中单个接听者记录
    history->calltype = CALL_HISTORY_CALL_TYPE::CALL_TYPE_GROUP_EACH_CALL;
    std::vector<std::string> group_call_list;
    SplitString(history->group_call_list, ",", group_call_list);
    for(const auto& callee_app_sip : group_call_list)
    {
        ::snprintf(history->callee, sizeof(history->callee), "%s", callee_app_sip.c_str());
        if (history->bill_second == 0 || 0 != strcmp(history->called, history->callee))
        {
            history->is_answer = 0;
        }
        else
        {
            history->is_answer = 1;
        }
        
        dbinterface::CallHistory::AddCallHistory(history, delivery);
    }
}

int CCallHistory::GetCallType(int caller_type, int callee_type)
{
    int calltype = CALL_TYPE_NONE;
    if (caller_type == SIP_TYPE_APP && callee_type == SIP_TYPE_APP)
    {
        calltype = CALL_TYPE_APP2APP;
    }
    else if (caller_type == SIP_TYPE_APP && callee_type == SIP_TYPE_DEV)
    {
        calltype = CALL_TYPE_APP2DEV;
    }
    else if (caller_type == SIP_TYPE_DEV && callee_type == SIP_TYPE_DEV)
    {
        calltype = CALL_TYPE_DEV2DEV;
    }
    else if (caller_type == SIP_TYPE_DEV && callee_type == SIP_TYPE_APP)
    {
        calltype = CALL_TYPE_DEV2APP;
    }
    else if (caller_type == SIP_TYPE_DEV && callee_type == SIP_TYPE_PHONE)
    {
        calltype = CALL_TYPE_DEV2PHONE;
    }
    else if (caller_type == SIP_TYPE_APP && callee_type == SIP_TYPE_PHONE)
    {
        calltype = CALL_TYPE_APP2PHONE;
    }
    else if (callee_type == SIP_TYPE_GROUP)
    {
        calltype = CALL_TYPE_GROUP_CALL;
    }
    else
    {
        calltype = CALL_TYPE_NONE;
    }

    return calltype;
}

void CCallHistory::WriteDBCallHistory(PbxCallHistory* history, int delivery)
{
    //群呼
    if (history->calltype == CALL_TYPE_GROUP_CALL)
    {
        WriteGroupCallHistory(history, delivery);
        WriteGroupEachCallHistory(history, delivery);
    }
    //其余直接插入
    else
    {
        dbinterface::CallHistory::AddCallHistory(history, delivery);
    }
}


