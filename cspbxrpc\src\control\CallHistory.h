#ifndef __CALL_HISTORY_H__
#define __CALL_HISTORY_H__

#pragma once
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


enum CALL_HISTORY_CALL_TYPE
{
    CALL_TYPE_NONE = 0,
    CALL_TYPE_APP2APP = 1,
    CALL_TYPE_DEV2APP = 2,
    CALL_TYPE_APP2DEV = 3,
    CALL_TYPE_DEV2DEV = 4,
    CALL_TYPE_DEV2PHONE = 5,
    CALL_TYPE_APP2PHONE = 6,
    CALL_TYPE_GROUP_CALL = 7, //群呼
    CALL_TYPE_GROUP_EACH_CALL = 8, //群呼时群组下每个账号单独记录
};

enum SipType
{
    SIP_TYPE_DEV = 1,
    SIP_TYPE_APP = 2,
    SIP_TYPE_NONE = 3,
    SIP_TYPE_PHONE = 4,
    SIP_TYPE_GROUP = 5,
};


class CCallHistory
{
public:
    CCallHistory();
    ~CCallHistory();

    static CCallHistory* GetInstance();
    void HandlePbxPutCallHistory(PbxCallHistory* history);
    void HandlePbxPutCallHistoryForOffice(PbxCallHistory* history);
    void HandlePbxPutCallHistoryForNewOffice(PbxCallHistory* history);
    void WriteDBCallHistory(PbxCallHistory* history, int delivery);

private:
    int  GetCallType(int caller_type, int callee_type);
    void WriteGroupCallHistory(PbxCallHistory* history, int delivery);
    void WriteGroupEachCallHistory(PbxCallHistory* history, int delivery);
    static CCallHistory* instance;

};

CCallHistory* GetCallHistoryInstance();

#endif
