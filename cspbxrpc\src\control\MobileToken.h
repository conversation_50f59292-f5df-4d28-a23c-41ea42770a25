#ifndef __CSPBXRPC_AKUSER_MANAGE_H__
#define __CSPBXRPC_AKUSER_MANAGE_H__

#include <set>
#include <string>


class CMobileToken
{
public:
    CMobileToken() {}
    CMobileToken(std::string uid, std::string token, int type, bool status, std::string ios_voip_token)
        : uid_(uid)
        , token_(token)
        , mobile_type_(type)
        , status_(status)
        , ios_voip_token_(ios_voip_token)
    {}
    CMobileToken(const CMobileToken& other)
    {
        uid_ = other.uid_;
        token_ = other.token_;
        mobile_type_ = other.mobile_type_;
        status_ = other.status_;
        fcm_token_ = other.fcm_token_;
        ios_voip_token_ = other.ios_voip_token_;
        app_token =  other.app_token;
        common_version_ =  other.common_version_;
        language_ =  other.language_;
        oem_name_ = other.oem_name_;
        app_oem_ = other.app_oem_;
        is_multi_site_ = other.is_multi_site_;
        ringtone_ = other.ringtone_;
    }
    CMobileToken& operator=(const CMobileToken& other)
    {
        if (this == &other)
        {
            return *this;
        }
        this->uid_ = other.uid_;
        this->token_ = other.token_;
        this->mobile_type_ = other.mobile_type_;
        this->status_ = other.status_;
        this->fcm_token_ = other.fcm_token_; //added by chenyc,2018-08-12,解决opernat = 导致fcm_token被清空的问题
        this->ios_voip_token_ =  other.ios_voip_token_;
        this->app_token =  other.app_token;
        this->common_version_ =  other.common_version_;
        this->language_ =  other.language_;
        this->oem_name_ = other.oem_name_;
        this->app_oem_ = other.app_oem_;
        is_multi_site_ = other.is_multi_site_;
        ringtone_ = other.ringtone_;
        
        return *this;
    }
    void setUid(const std::string& uid)
    {
        uid_ = uid;
    }

    const std::string Uid() const
    {
        return uid_;
    }
    void setToken(const std::string& token)//指的是最初的ios token 或者android的厂商推送
    {
        token_ = token;
    }

    const std::string Token() const
    {
        return token_;
    }

    void setVoipToken(const std::string& token)
    {
        ios_voip_token_ = token;
    }

    const std::string VoipToken() const
    {
        return ios_voip_token_;
    }

    void setAppToken(const std::string& token)
    {
        app_token = token;
    }

    const std::string AppToken() const
    {
        return app_token;
    }


    void setFcmToken(const std::string& fcm_token)  //fcm token
    {
        fcm_token_ = fcm_token;
    }

    const std::string FcmToken() const   //fcm token
    {
        //assert(mobile_type_ != csmain::APP_IOS);
        return fcm_token_;
    }
    void setMobileType(int type)
    {
        mobile_type_ = type;
    }

    int MobileType() const
    {
        return mobile_type_;
    }

    void setMobileOnline(int staus)
    {
        status_ = staus;
    }

    bool MobileOnline()
    {
        return status_ == 1;
    }

    void setCommonVersion(int type)
    {
        common_version_ = type;
    }

    int CommonVersion() const
    {
        return common_version_;
    }

    void setAppVersion(const std::string& app_version)
    {
        app_version_ = app_version;
    }

    std::string AppVersion() const
    {
        return app_version_;
    }

    void setUidNode(const std::string& uid_node)
    {
        uid_node_ = uid_node;
    }

    const std::string UidNode() const
    {
        return uid_node_;
    }

    void setLanguage(const std::string& language)
    {
        language_ = language;
    }

    std::string Language() const
    {
        return language_;
    }

    void setOemName(const std::string& oem_name)
    {
        oem_name_ = oem_name;
    }

    std::string OemName() const
    {
        return oem_name_;
    }

    void setAppOem(int type)
    {
        app_oem_ = type;
    }

    int AppOem() const
    {
        return app_oem_;
    }

    void setIsDyIv(int dy)
    {
        dy_iv_ = dy;
    }

    int IsDyIv() const
    {
        return dy_iv_;
    }

    void setIsMultiSite(int is_multi_site)
    {
        is_multi_site_ = is_multi_site;
    }
    
    int IsMultiSite() const
    {
        return is_multi_site_;
    }

    void setRingtone(const std::string& ringtone)
    {
        ringtone_ = ringtone;
    }

    std::string Ringtone() const
    {
        return ringtone_;
    }
private:
    std::string uid_;  //uid
    std::string token_;
    int mobile_type_;   //参见 csmain::AppType
    int common_version_;//版本号，目前版本号1 用于安卓区分是否要voip推送
    std::string app_version_; //app上架时候的版本号
    bool status_;  //online:1 or offline:0
    std::string ios_voip_token_;
    std::string app_token; //csgate 返回的访问token
    int app_oem_;
    int dy_iv_;//add by chenzhx 20230502暂时先这样处理
    int is_multi_site_;//是否为多套房
    
    std::string fcm_token_; //fcm token
    std::string uid_node_; //uid的联动
    std::string language_; //推送语言
    std::string oem_name_;  //oem名
    std::string ringtone_; //铃声
};

#endif