#include "EtcdCliMng.h"
#include "PbxRpcInit.h"
#include "PbxRpcClientInit.h"
#include "session_rpc_client.h"
#include "csmain_rpc_client.h"
#include "csmain_rpc_client_mng.h"

extern AKCS_CONF gstAKCSConf;
extern SmRpcClient* g_sm_client_ptr;
extern CAkEtcdCliManager* g_etcd_cli_mng;

void GrpcClientInit()
{
    CssessionGrpcClientInit();
    CsmainGrpcClientInit();
    return;
}

void CssessionGrpcClientInit()
{
    // 从etcd中获取 cssession grpc server地址
    std::set<std::string> cssession_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "CssessionGrpcClientInit connetc to etcd srv fialed";
        return;
    }

    std::string cssesson_str = "cssession list = ";

    std::vector<AddressData> addresses;
    for (const auto& cssesson : cssession_grpc_server_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = cssesson.find(":");
        if (std::string::npos != pos)
        {
            ip = cssesson.substr(0, pos);
            port = cssesson.substr(pos + 1);
        }
        addresses.emplace_back(AddressData{std::atoi(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
        cssesson_str += cssesson ;
        cssesson_str += " ";
    }

    g_sm_client_ptr = new SmRpcClient();
    g_sm_client_ptr->SetNextResolution(addresses);

    std::string cssession_client_node = "cspbxrpc_" + std::string(gstAKCSConf.cspbxrpc_outer_ip);
    g_sm_client_ptr->RegisterNode(cssession_client_node);

    AK_LOG_INFO << "CssessionGrpcClientInit " << cssesson_str;
    return;
}

void CsmainGrpcClientInit()
{
    // 从etcd中获取 csmain grpc server地址
    std::set<std::string> csmain_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllAccRpcInnerSrvs(csmain_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "CsmainGrpcClientInit connetc to etcd srv fialed";
        return;
    }
    
    for (const auto& csmain_grpc_addr : csmain_grpc_server_addrs)
    {
        MainRpcClientPtr csmain_rpc_client(new MainRpcClient(csmain_grpc_addr));
        MainRpcClientMng::Instance()->AddCsmainRpcSrv(csmain_grpc_addr, csmain_rpc_client);
    }

    return;
}
