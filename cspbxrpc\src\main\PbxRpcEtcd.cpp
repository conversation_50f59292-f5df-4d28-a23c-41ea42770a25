#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include "EtcdDns.h"
#include <vector>
#include "util.h"
#include "PbxRpcInit.h"
#include "PbxRpcEtcd.h"
#include "evpp/event_watcher.h"
#include "NotifyPushClient.h"
#include "PushClientMng.h"
#include "session_rpc_client.h"
#include "csmain_rpc_client_mng.h"

extern AKCS_CONF gstAKCSConf;
extern SmRpcClient* g_sm_client_ptr;
extern CEtcdDnsManager* g_etcd_dns_mng;
extern const char *g_ak_srv_session;
extern const char *g_ak_srv_cspush;
extern const char *g_conf_db_addr;
extern const char *g_ak_srv_main;

CAkEtcdCliManager* g_etcd_cli_mng = nullptr;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}

static void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.akcs_db_ip) != 0) || (conf_tmp.db_port != gstAKCSConf.akcs_db_port))
    {
        Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip),  conf_tmp.db_ip);
        gstAKCSConf.akcs_db_port = conf_tmp.db_port;
        DaoReInit();
    }
    return;
}

static void SessionSrvConnInit(const std::set<std::string>& cssession_addrs)
{
    std::string cssesson_str = "SessionSrvConnInit session list = ";
    std::vector<AddressData> addresses;
    AddressData addr_tmp;
    for (const auto& cssesson : cssession_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = cssesson.find(":");
        if (std::string::npos != pos)
        {
            ip = cssesson.substr(0, pos);
            port = cssesson.substr(pos + 1);
        }
        addresses.emplace_back(AddressData{ATOI(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
        cssesson_str += cssesson;
        cssesson_str += " ";
    }
    
    g_sm_client_ptr->SetNextResolution(addresses);
    
    AK_LOG_INFO << cssesson_str;
}

void UpdateCsmainSrvList()
{
    std::set<std::string> csmain_addrs;
    if (g_etcd_cli_mng->GetAllAccRpcInnerSrvs(csmain_addrs) == 0)
    {
        //更新route的连接列表
        MainRpcClientMng::Instance()->UpdateCsmainRpcSrv(csmain_addrs);
    }
}

void UpdateSessionSrvList()
{
    std::set<std::string> cssession_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) == 0)
    {
        SessionSrvConnInit(cssession_addrs);
    }

}

void EtcdSrvInit()
{
    //cspush
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    PushSrvConnInit(cspush_addrs, g_etcd_loop.get(), &CNotifyPushClient::CreateClient);

    if (strlen(gstAKCSConf.push_server_addr) > 5)
    {
        PushClientPtr push_cli_ptr = CNotifyPushClient::CreateClient(std::string(gstAKCSConf.push_server_addr), g_etcd_loop.get());
        push_cli_ptr->Start();
        CPushClientMng::Instance()->AddOuterPushSrv(push_cli_ptr);
    }
    
    EtcdRegCsPbxRpcInnerInfo();
    
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_main, UpdateCsmainSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_session, UpdateSessionSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_cspush, std::bind(&CNotifyPushClient::UpdatePushSrvList));
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
    g_etcd_loop->Run();
    return;
}

void EtcdRegCsPbxRpcInnerInfo()
{
    std::string inner_addr = GetEth0IPAddr();
    char inner_rpc_addr[256] = {0};
    ::snprintf(inner_rpc_addr, sizeof(inner_rpc_addr), "%s:%s", inner_addr.c_str(), "8800");
    char inner_reg_info[256] = {0};
    ::snprintf(inner_reg_info, sizeof(inner_reg_info), "%s%s", "/akcs/cspbxrpc/innerip/rpc/", gstAKCSConf.cspbxrpc_outer_ip);//注册内网rpc服务地址
    RegSrv2Etcd(inner_reg_info, inner_rpc_addr, 10, csbase::REG_INNER, g_etcd_loop.get());
    
    return;
}

int EtcdConnInit()
{
    g_etcd_dns_mng = new CEtcdDnsManager(gstAKCSConf.etcd_server_addr);
    std::thread dns_thread = std::thread(&CEtcdDnsManager::StartDnsResolver, g_etcd_dns_mng);
    while(!g_etcd_dns_mng->DnsIsOk())
    {
        usleep(10);
    }
    dns_thread.detach();
    
    //域名解析完才能初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_dns_mng->GetAddrs());
    g_etcd_dns_mng->SetEtcdCli(g_etcd_cli_mng); 
    return 0;
}
