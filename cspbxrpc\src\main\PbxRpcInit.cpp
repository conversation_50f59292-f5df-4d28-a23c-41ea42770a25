#include <string.h>
#include <signal.h>
#include "ConnectionPool.h"
#include "LogConnectionPool.h"
#include "MappingConnectionPool.h"
#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdDns.h"
#include "EtcdCliMng.h"
#include "PbxRpcInit.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/SystemSettingTable.h"
#include <KdcDecrypt.h>

AKCS_CONF gstAKCSConf;
LOG_DELIVERY gstAKCSLogDelivery;
extern CAkEtcdCliManager* g_etcd_cli_mng;

int LogDeliveryInit()
{
    gstAKCSLogDelivery.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    gstAKCSLogDelivery.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    gstAKCSLogDelivery.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    if(gstAKCSLogDelivery.personal_capture_delivery == 0 || gstAKCSLogDelivery.personal_motion_delivery == 0 || gstAKCSLogDelivery.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}
void DbConfInit(CConfigFileReader& config_file)
{
    // 数据库账号密码
    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username),  config_file.GetConfigName("db_username"));
    
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), decrypt_db_passwd.c_str());

    // 数据库名称
    Snprintf(gstAKCSConf.akcs_db_database, sizeof(gstAKCSConf.akcs_db_database),  config_file.GetConfigName("akcs_db_database"));
    Snprintf(gstAKCSConf.log_db_database, sizeof(gstAKCSConf.log_db_database),  config_file.GetConfigName("log_db_database"));
    Snprintf(gstAKCSConf.mapping_db_database, sizeof(gstAKCSConf.mapping_db_database), config_file.GetConfigName("mapping_db_database"));

    // 数据库ip
    Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip), config_file.GetConfigName("akcs_db_ip"));
    Snprintf(gstAKCSConf.log_db_ip, sizeof(gstAKCSConf.log_db_ip), config_file.GetConfigName("log_db_ip"));
    Snprintf(gstAKCSConf.mapping_db_ip, sizeof(gstAKCSConf.mapping_db_ip), config_file.GetConfigName("mapping_db_ip"));

    // 数据库端口
    const char* akcs_db_port = config_file.GetConfigName("akcs_db_port");
    gstAKCSConf.akcs_db_port = ATOI(akcs_db_port);
    const char* log_db_port = config_file.GetConfigName("log_db_port");
    gstAKCSConf.log_db_port = ATOI(log_db_port);
    const char* mapping_db_port = config_file.GetConfigName("mapping_db_port");
    gstAKCSConf.mapping_db_port = ATOI(mapping_db_port);

    const char* rpc_server_num = config_file.GetConfigName("rpc_server_num");
    gstAKCSConf.rpc_server_num = ATOI(rpc_server_num);
    
   
    return;
}

int ConfInit()
{
    CConfigFileReader config_file("/usr/local/akcs/cspbxrpc/conf/cspbxrpc.conf");

    DbConfInit(config_file);

    Snprintf(gstAKCSConf.cspbxrpc_outer_ip, sizeof(gstAKCSConf.cspbxrpc_outer_ip), config_file.GetConfigName("cspbxrpc_outer_ip"));
    Snprintf(gstAKCSConf.push_server_addr, sizeof(gstAKCSConf.push_server_addr), config_file.GetConfigName("cspush_net"));
    Snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));

    Snprintf(gstAKCSConf.oem_name, sizeof(gstAKCSConf.oem_name), config_file.GetConfigName("oem_name"));
    if (!strcasecmp(gstAKCSConf.oem_name, "AKUVOX"))
    {
        gstAKCSConf.oem_num = OEM_AKUVOX;
    }
    else if (!strcasecmp(gstAKCSConf.oem_name, "DISCREET"))
    {
        gstAKCSConf.oem_num = OEM_DISCREET;
    }
    Snprintf(gstAKCSConf.push_AESkey, sizeof(gstAKCSConf.push_AESkey), config_file.GetConfigName("push_aeskey"));
    
    const char* is_aws = config_file.GetConfigName("is_aws");
    gstAKCSConf.is_aws = ATOI(is_aws);

    const char* call_no_limit = config_file.GetConfigName("call_no_limit");
    gstAKCSConf.call_no_limit = ATOI(call_no_limit);

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));
    
    return 0;
}

int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip), conf_tmp.db_ip);
    gstAKCSConf.akcs_db_port = conf_tmp.db_port;
    return 0;
}

// 初始化数据库连接 
int DaoInit()
{
    LoadConfFromConfSrv();
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstAKCSConf.akcs_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.akcs_db_database, gstAKCSConf.akcs_db_port, MAX_RLDB_CONN, "cspbxrpc");

    LogConnPool* log_conn_pool = GetLogDBConnPollInstance();
    if (NULL == log_conn_pool)
    {
        AK_LOG_WARN << "LOG DaoInit failed.";
        return -1;
    }
    log_conn_pool->Init(gstAKCSConf.log_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.log_db_database, gstAKCSConf.log_db_port, MAX_RLDB_CONN, "cspbxrpc");

    MappingConnPool* mapping_conn_pool = GetMappingDBConnPollInstance();
    if (NULL == mapping_conn_pool)
    {
        AK_LOG_WARN << "Mapping DaoInit failed.";
        return -1;
    }
    mapping_conn_pool->Init(gstAKCSConf.mapping_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.mapping_db_database, gstAKCSConf.mapping_db_port, MAX_RLDB_CONN, "cspbxrpc");

    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstAKCSConf.akcs_db_ip, gstAKCSConf.akcs_db_port);
    return 0;
}
