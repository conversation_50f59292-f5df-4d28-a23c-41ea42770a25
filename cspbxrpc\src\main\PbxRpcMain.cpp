#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <vector>
#include <string>
#include <map>
#include <thread>
#include <pthread.h>
#include <errno.h>
#include "AkLogging.h"
#include "util.h"
#include "util_time.h"
#include "Rldb/RldbQuery.h"
#include "CachePool.h"
#include "ConfigFileReader.h"
#include "PbxRpcServer.h"
#include "PbxRpcEtcd.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "PbxRpcInit.h"
#include "PbxRpcClientInit.h"
#include "AkcsAppInit.h"
#include "NotifyMsgControl.h"
#include "ProjectUserManage.h"
#include "session_rpc_client.h"
#include "HttpServer.h"
#include "Metric.h"
#include "AkcsMonitor.h"
#include "PbxRpcMQProduce.h"

std::string g_logic_srv_id;
SmRpcClient* g_sm_client_ptr = nullptr;
extern CAkEtcdCliManager* g_etcd_cli_mng;
std::map<string, AKCS_DST> g_time_zone_DST;

void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void InstanceInit()
{    
    CacheManager::getInstance()->Init("/usr/local/akcs/cspbxrpc/conf/cspbxrpc_redis.conf", "csbpxrpcCacheInstances");

    GetAppWakeupMsgControlInstance()->Init();
    
    return;
}

int RpcServerInit()
{
    PbxRpcServer rpc_server("8800");
    rpc_server.Run();
    return 0;
}

int main(int argc, char** argv)
{
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2("/var/run/cspbxrpc.pid"))
    {
        printf("another cspbxrpc has been running in this sytem.");
        return -1;
    }

    GlogInit2(argv[0], "cspbxrpclog");
    
    ConfInit();

    ParseTimeZone("/usr/local/akcs/cspbxrpc/conf/TimeZone.xml", g_time_zone_DST);

    EtcdConnInit();
    
    GrpcClientInit();
    
    InstanceInit();

    if (DaoInit() != 0)
    {
        AK_LOG_WARN << "DaoInit fialed.";
        GlogClean2();
        return -1;
    }

    //获取LOG库日志表分片数
    if (LogDeliveryInit() != 0)
    {
        AK_LOG_WARN << "LogDeliveryInit fialed.";
        GlogClean2();
        return -1;
    }

    g_logic_srv_id = "cspbxrpc_" + GetEth0IPAddr();
    
    std::thread etcd_thread = std::thread(EtcdSrvInit);
    SetThreadName(etcd_thread.native_handle(), "EtcdSrvInit");
    
    std::thread conf_watch_thread = std::thread(ConfWatch);
    SetThreadName(conf_watch_thread.native_handle(), "ConfWatch");
    
    std::thread rpc_server_thread = std::thread(RpcServerInit);
    SetThreadName(rpc_server_thread.native_handle(), "RpcServerInit");

    // 起http维护通道线程, port = 9990
    std::thread httpThread(startHttpServer);
    SetThreadName(httpThread.native_handle(), "HttpServer");

    // 起nsq生产者线程, 用于告警推送
    std::thread mq_produce_thread = std::thread(MQProduceInit);
    SetThreadName(mq_produce_thread.native_handle(), "MQProduceInit");

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "cspbxrpc is running.";

    etcd_thread.join();
    conf_watch_thread.join();
    rpc_server_thread.join();
    
    GlogClean2();
    return 0;
}

