#include "util.h"
#include "PbxRpcServer.h"
#include "WakeupAppPush.h"
#include "HangupAppPush.h"
#include "QuerySipInfo.h"
#include "QueryUidStatus.h"
#include "QueryMainSiteSip.h"
#include "WriteCallHistory.h"
#include "QueryLandLineStatus.h"
#include "QueryLandLineNumber.h"
#include "MetricService.h"
#include "PbxRpcInit.h"
#include "AkLogging.h"
#include <vector>
#include <pthread.h>

extern AKCS_CONF gstAKCSConf;

void PbxRpcServer::Run()
{
    std::string listen_net = GetEth0IPAddr() + std::string(":") + rpc_port_;
    std::string server_address(listen_net);
    ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service_);
    cq_ = builder.AddCompletionQueue();//可以多个的.
    server_ = builder.BuildAndStart();
    AK_LOG_INFO << "PbxRpcServer run grpc server listening on " << server_address;

    // Proceed to the server's main loop. 在构造函数里面触发CallData::Proceed()
    //这样不会触发多个线程,只是会触发:CallData::Proceed 中的这个流程而已 if (status_ == CREATE),
    //所以每个rpc接口都需要这个
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::WAKEUP_APP);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::QUERY_UID_STATUS);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::QUERY_LANDLINE_STATUS);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::WRITE_CALL_HISTORY);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::QUERY_LANDLINE_NUMBER);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::QUERY_MAIN_SITE_SIP);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::HANGUP_APP);
    new CallData(&service_, cq_.get(), CSPBX_RPC_SERVER_TYPE::QUERY_SIP_INFO);

    //根据配置文件中的rpc_server_num参数创建RPC服务线程
    AK_LOG_INFO << "创建" << gstAKCSConf.rpc_server_num << "个RPC服务线程";
    std::vector<std::thread> rpc_threads;
    int i = 0;
    for (i = 0; i < gstAKCSConf.rpc_server_num - 1; i++) {
        rpc_threads.emplace_back(std::bind(&PbxRpcServer::HandleRpcs, this));
        // 设置线程名称
        char threadName[16];
        snprintf(threadName, sizeof(threadName), "RpcServer_%d", i);
        pthread_t threadId = rpc_threads.back().native_handle();
        SetThreadName(threadId, threadName);
        
        AK_LOG_INFO << "创建RPC服务线程ID=" << i << "，名称=" << threadName;
    }
    
    // 设置主线程名称
    char threadName[16];
    snprintf(threadName, sizeof(threadName), "RpcServer_%d", i);
    SetThreadName(pthread_self(), threadName);
    
    AK_LOG_INFO << "创建RPC服务线程ID=" << i << "，名称=" << threadName;
    HandleRpcs();
}

void PbxRpcServer::CallData::Proceed()
{
    if (status_ == CREATE)
    {
        // Make this instance progress to the PROCESS state.
        status_ = PROCESS;
        switch (s_type_)
        {
            case CSPBX_RPC_SERVER_TYPE::QUERY_UID_STATUS:
            {
                service_->RequestQueryUidStatusHandle(&ctx_, &query_uid_status_request_, &query_uid_status_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::WAKEUP_APP:
            {
                service_->RequestWakeupAppHandle(&ctx_, &wakeup_app_request_, &wakeup_app_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_LANDLINE_STATUS:
            {
                service_->RequestQueryLandlineStatusHandle(&ctx_, &query_landline_status_request_, &query_landline_status_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::WRITE_CALL_HISTORY:
            {
                service_->RequestWriteCallHistoryHandle(&ctx_, &write_callhistory_request_, &write_callhistory_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_LANDLINE_NUMBER:
            {
                service_->RequestQueryLandlineNumberHandle(&ctx_, &query_landline_number_request_, &query_landline_number_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_MAIN_SITE_SIP:
            {
                service_->RequestQueryMainSiteSipHandle(&ctx_, &query_main_site_sip_request_, &query_main_site_sip_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::HANGUP_APP:
            {
                service_->RequestHangupAppHandle(&ctx_, &hangup_app_request_, &hangup_up_responder_, cq_, cq_, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_SIP_INFO:
            {
                service_->RequestQuerySipInfoHandle(&ctx_, &query_sip_info_request_, &query_sip_info_responder_, cq_, cq_, this);
                break;
            }           
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }
    }
    else if (status_ == PROCESS)
    {
        status_ = FINISH;
        new CallData(service_, cq_, this->s_type_);
        switch (s_type_)
        {
            case CSPBX_RPC_SERVER_TYPE::QUERY_UID_STATUS:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_QueryUidStatus_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(query_uid_status_request_.msg_traceid());
                int uid_status = QueryUidStatus::GetUidStatus(query_uid_status_request_);
                
                query_uid_status_reply_.set_ret(uid_status);
                query_uid_status_responder_.Finish(query_uid_status_reply_, Status::OK, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::WAKEUP_APP:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_WakeupApp_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(wakeup_app_request_.msg_traceid());
            
                WakeupAppPush::WakeupApp(wakeup_app_request_);

                wakeup_app_reply_.set_ret(0);
                wakeup_app_responder_.Finish(wakeup_app_reply_, Status::OK, this); 
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::HANGUP_APP:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_HangupApp_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(hangup_app_request_.msg_traceid());
                
                HangupAppPush::HangupApp(hangup_app_request_);

                hangup_app_reply_.set_ret(0);
                hangup_up_responder_.Finish(hangup_app_reply_, Status::OK, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_LANDLINE_STATUS:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_QueryLandlineStatus_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(query_landline_status_request_.msg_traceid());
                
                int status = QueryLandlineStatus::GetLandlineStatus(query_landline_status_request_);
                
                query_landline_status_reply_.set_ret(status);
                query_landline_status_responder_.Finish(query_landline_status_reply_, Status::OK, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_LANDLINE_NUMBER:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_QueryLandlineNumber_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(query_landline_number_request_.msg_traceid());

                std::pair<std::string, std::string> phone = QueryLandlineNumber::GetLandlineNumber(query_landline_number_request_);
                
                query_landline_number_reply_.set_ret(phone.first);
                query_landline_number_reply_.set_phone_code(phone.second);
                query_landline_number_responder_.Finish(query_landline_number_reply_, Status::OK, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_MAIN_SITE_SIP:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_QueryMainSiteSip_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(query_main_site_sip_request_.msg_traceid());
                std::string main_site_sip = QueryMainSiteSip::GetMainSiteSip(query_main_site_sip_request_);

                query_main_site_sip_reply_.set_main_sip(main_site_sip);
                query_main_site_sip_responder_.Finish(query_main_site_sip_reply_, Status::OK, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::WRITE_CALL_HISTORY:
            {   
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_WriteCallHistory_total", 1);
                }
                
                ThreadLocalSingleton::GetInstance().SetTraceID(write_callhistory_request_.msg_traceid());
                WriteCallHistory::WriteHistory(write_callhistory_request_);
                
                write_callhistory_reply_.set_ret(0);
                write_callhistory_responder_.Finish(write_callhistory_reply_, Status::OK, this);
                break;
            }
            case CSPBX_RPC_SERVER_TYPE::QUERY_SIP_INFO:
            {
                MetricService* metric_service = MetricService::GetInstance();
                if(metric_service) {
                    metric_service->AddValue("cspbxrpc_RpcCallFrequency_QuerySipInfo_total", 1);
                }

                ThreadLocalSingleton::GetInstance().SetTraceID(query_sip_info_request_.msg_traceid());
                std::string nickname = QuerySipInfo::GetTranferCallNickName(query_sip_info_request_);
                
                if (nickname.size() > 0)
                {
                    query_sip_info_reply_.set_ret(0);
                    query_sip_info_reply_.set_name(nickname);
                }
                else
                {
                    query_sip_info_reply_.set_ret(1);
                }
                
                query_sip_info_responder_.Finish(query_sip_info_reply_, Status::OK, this);
                break;
            }            
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }

    }
    else
    {
        GPR_ASSERT(status_ == FINISH);
        // Once in the FINISH state, deallocate ourselves (CallData).
        delete this;
    }
}

// This can be run in multiple threads if needed.
void PbxRpcServer::HandleRpcs()
{
    //TODO 当开启多线程的时候,这个必须挪到业务线程之前?
    //new CallData(&service_, cq_.get());
    void* tag;
    bool ok;
    while (true)
    {
        {
            std::lock_guard<std::mutex> lock(mtx_cq_);
            //modified by chenyc,2021-10-19,原先的代码写得不严谨,在一些场景下ok可能为false,具体可参考:gRPC源码中CompletionQueue的描述
            //TODO: 每个HandleRpcs线程单独一个cq,避免加锁与消息干扰.
            if(cq_->Next(&tag, &ok) != true || !ok)
            {
                AK_LOG_WARN << "gRPC HandleRpcs cq next operation error ";
                continue;
            }
            //GPR_ASSERT(cq_->Next(&tag, &ok));
            //GPR_ASSERT(ok);//这里断言会概率失败
        }
        static_cast<CallData*>(tag)->Proceed();
    }
}

