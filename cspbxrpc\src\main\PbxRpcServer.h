#include <memory>
#include <iostream>
#include <string>
#include <thread>
#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include "AK.PBX.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonSt.h"

using grpc::Server;
using grpc::ServerAsyncResponseWriter;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::ServerCompletionQueue;
using grpc::Status;

using AK::PBX::QueryUidStatusRequest;
using AK::PBX::QueryUidStatusReply;
using AK::PBX::WakeupAppRequest;
using AK::PBX::WakeupAppReply;
using AK::PBX::QueryLandlineStatusRequest;
using AK::PBX::QueryLandlineStatusReply;
using AK::PBX::WriteCallHistoryRequest;
using AK::PBX::WriteCallHistoryReply;
using AK::PBX::QueryLandlineNumberRequest;
using AK::PBX::QueryLandlineNumberReply;
using AK::PBX::QueryMainSiteSipRequest;
using AK::PBX::QueryMainSiteSipReply;
using AK::PBX::HangupAppRequest;
using AK::PBX::HangupAppReply;
using AK::PBX::QuerySipInfoRequest;
using AK::PBX::QuerySipInfoReply;

using AK::PBX::PbxRpcSrv; //rpc服务名

class PbxRpcServer
{
public:

    PbxRpcServer(const std::string& port)
    {
        rpc_port_ = port;
    }
    ~PbxRpcServer()
    {
        server_->Shutdown();
        // Always shutdown the completion queue after the server.
        cq_->Shutdown();
    }
    // There is no shutdown handling in this code.
    void Run();
private:
    // Class encompasing the state and logic needed to serve a request.
    class CallData
    {
    public:

    public:
        CallData(PbxRpcSrv::AsyncService* service, ServerCompletionQueue* cq, CSPBX_RPC_SERVER_TYPE s_type)
            : service_(service), cq_(cq), s_type_(s_type), wakeup_app_responder_(&ctx_), query_uid_status_responder_(&ctx_),
              query_landline_status_responder_(&ctx_), write_callhistory_responder_(&ctx_), query_landline_number_responder_(&ctx_), 
              query_main_site_sip_responder_(&ctx_), hangup_up_responder_(&ctx_), query_sip_info_responder_(&ctx_), status_(CREATE)
        {
            // Invoke the serving logic right away.
            Proceed();
        }

        void Proceed();

    private:
        // The means of communication with the gRPC runtime for an asynchronous
        // server.
        PbxRpcSrv::AsyncService* service_;
        // The producer-consumer queue where for asynchronous server notifications.  客户端用的是:CompletionQueue,都是生产者消费者的模型
        ServerCompletionQueue* cq_;
        // Context for the rpc, allowing to tweak aspects of it such as the use
        // of compression, authentication, as well as to send metadata back to the
        // client.
        ServerContext ctx_;
        //多个接口服务用这个来标示
        CSPBX_RPC_SERVER_TYPE s_type_;

        QueryUidStatusRequest query_uid_status_request_;
        QueryUidStatusReply query_uid_status_reply_;

        WakeupAppRequest wakeup_app_request_;
        WakeupAppReply wakeup_app_reply_;

        QueryLandlineStatusRequest  query_landline_status_request_;
        QueryLandlineStatusReply query_landline_status_reply_;

        WriteCallHistoryRequest write_callhistory_request_;
        WriteCallHistoryReply write_callhistory_reply_;
        
        QueryLandlineNumberRequest  query_landline_number_request_;
        QueryLandlineNumberReply query_landline_number_reply_;
    
        QueryMainSiteSipRequest  query_main_site_sip_request_;
        QueryMainSiteSipReply query_main_site_sip_reply_;

        HangupAppRequest  hangup_app_request_;
        HangupAppReply hangup_app_reply_;

        QuerySipInfoRequest query_sip_info_request_;
        QuerySipInfoReply query_sip_info_reply_;

        // The means to get back to the client.
        //对于客户端则是: ClientAsyncResponseReader 读，都是针对reply而言的..
        ServerAsyncResponseWriter<WakeupAppReply> wakeup_app_responder_;
        ServerAsyncResponseWriter<QueryUidStatusReply> query_uid_status_responder_;
        ServerAsyncResponseWriter<QueryLandlineStatusReply> query_landline_status_responder_;
        ServerAsyncResponseWriter<WriteCallHistoryReply> write_callhistory_responder_;
        ServerAsyncResponseWriter<QueryLandlineNumberReply> query_landline_number_responder_;
        ServerAsyncResponseWriter<QueryMainSiteSipReply> query_main_site_sip_responder_;
        ServerAsyncResponseWriter<HangupAppReply> hangup_up_responder_;
        ServerAsyncResponseWriter<QuerySipInfoReply> query_sip_info_responder_;

        // Let's implement a tiny state machine with the following states.
        enum CallStatus { CREATE, PROCESS, FINISH };
        CallStatus status_;  // The current serving state.
    };

private:
    // This can be run in multiple threads if needed.
    void HandleRpcs();
private:
    std::unique_ptr<ServerCompletionQueue> cq_;//一个服务可以有多个CompletionQueue
    PbxRpcSrv::AsyncService service_;//指服务接口
    std::unique_ptr<Server> server_;//指服务器
    std::mutex mtx_cq_;
    std::string rpc_port_;
};


