#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <map>
#include <cstdlib>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "util.h"
#include "http/HttpMsgControl.h"
#include "AES256.h"
#include "CachePool.h"
#include "ConnectionPool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "AkcsMsgDef.h"
#include "PbxRpcInit.h"
#include "MetricService.h"

//全局变量
extern bool g_nsq_ready;
extern CAkEtcdCliManager* g_etcd_cli_mng;


//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}


void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void startHttpServer()
{
    const int port = 9990;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网

    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler); 
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    
    server.Init(port);
    server.Start();
    
    return;
}

