#include "Metric.h"
#include "AkLogging.h"
#include "ConfigFileReader.h"

#define VERSION_CONF_FILE "/usr/local/akcs/cspbxrpc/conf/version.conf"

void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_QueryUidStatus_total",
        "QueryUidStatus total",
        "cspbxrpc_RpcCallFrequency_QueryUidStatus_total",
        nullptr
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_WakeupApp_total",
        "WakeupApp total",
        "cspbxrpc_RpcCallFrequency_WakeupApp_total",
        nullptr 
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_QueryLandlineStatus_total",
        "QueryLandlineStatus total",
        "cspbxrpc_RpcCallFrequency_QueryLandlineStatus_total",
        nullptr
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_WriteCallHistory_total",
        "WriteCallHistory total",
        "cspbxrpc_RpcCallFrequency_WriteCallHistory_total",
        nullptr
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_QueryLandlineNumber_total",
        "QueryLandlineNumber total",
        "cspbxrpc_RpcCallFrequency_QueryLandlineNumber_total",
        nullptr
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_QueryMainSiteSip_total",
        "QueryMainSiteSip total",
        "cspbxrpc_RpcCallFrequency_QueryMainSiteSip_total",
        nullptr
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_HangupApp_total",
        "HangupApp total",
        "cspbxrpc_RpcCallFrequency_HangupApp_total",
        nullptr
    );
    metric_service->AddMetric(
        "cspbxrpc_RpcCallFrequency_QuerySipInfo_total",
        "QuerySipInfo total",
        "cspbxrpc_RpcCallFrequency_QuerySipInfo_total",
        nullptr
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return (long)version_metric; }
    );
}
