#include "AppPushToken.h"
#include "AkcsCommonDef.h"
#include "PushClientMng.h"
#include "NotifyHangupMsg.h"
#include "doorlog/UserInfo.h"

int CHangUpAppMsg::NotifyMsg()
{
    std::string current_site = getUid();
    std::string main_site = current_site;

    // 拦截非法的离线推送 未激活/房间删除
    CUserInfo user_info(current_site);
    if (!user_info.IsUserLegal())
    {
        AK_LOG_WARN << "pbx hangup app failed, current_site status is not legal, main_site = " << main_site << ", current_site = " << current_site;
        return -1;
    }

    // 获取主站点信息
    if (dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(current_site, main_site) != 0)
    {
        AK_LOG_INFO << "pbx hangup app GetMainAccountByAccount falied, current_site = " << current_site;
        return -1;
    }

    // 获取主站点推送token
    CMobileToken app_push_token;
    if (GetAppPushTokenInstance()->GetAppPushTokenByUid(main_site, app_push_token) != 0)
    {
        AK_LOG_WARN << "pbx hangup app GetMainSitePushToken failed, main_site = " << main_site << " token invalid, current_site = " << current_site;
        return -1;
    }

    // 获取callkit信息
    TokenInfo online_token_info;
    if (dbinterface::Token::GetTokenInfoByAccount(main_site, online_token_info) != 0)
    {
        AK_LOG_WARN << "pbx hangup app GetTokenInfoByAccount failed, main_site = " << main_site << " token invalid, current_site = " << current_site;
        return -1;
    }

    // ios或sdk才进行hangup推送
    if (app_push_token.MobileType() == csmain::AppType::APP_IOS || app_type_ == int(AkcsDeviceType::AKCS_DEVICE_TYPE_APP_SDK))
    {
        AppOfflinePushKV push_msg;
        BuildPushMsg(app_push_token, online_token_info, current_site, push_msg);
        
        CNotifyPushClientPtr push_cli_ptr = std::dynamic_pointer_cast<CNotifyPushClient>(CPushClientMng::Instance()->GetPushSrv());
        if (push_cli_ptr)
        {
            push_cli_ptr->buildPushMsgHangup(app_push_token, traceid_, push_msg);
        }
    }

    AK_LOG_INFO << "pbx hangup app, caller = " << caller_sip_ << ", caller name = " << nick_name_location_ 
                << ", main_site = " << main_site << ", current_site = " << current_site << ", is_multi_site = " << app_push_token.IsMultiSite();

    return 0;
}

void CHangUpAppMsg::BuildPushMsg(const CMobileToken& app_push_token, const TokenInfo& online_token_info, const std::string& current_site, AppOfflinePushKV& push_msg)
{
    push_msg.insert(map<std::string, std::string>::value_type("device_name", nick_name_location_));

    std::string title_prefix;
    if (app_push_token.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(current_site, title_prefix) == 0 )
    {
        push_msg.insert(map<std::string, std::string>::value_type("title_prefix", title_prefix));
    }

    int is_enable_callkit = online_token_info.enable_callkit ? 1 : 0;
    push_msg.insert(map<std::string, std::string>::value_type("is_enable_callkit", std::to_string(is_enable_callkit)));

    return;
}
