#ifndef __CSPBXRPC_NOTIFY_HANGUP_MSG_H__
#define __CSPBXRPC_NOTIFY_HANGUP_MSG_H__

#include <string>
#include <memory>
#include "AkLogging.h"
#include "NotifyMsg.h"
#include "MobileToken.h"
#include "NotifyMsgControl.h"
#include "NotifyPushClient.h"
#include "dbinterface.h"

class CHangUpAppMsg : public CNotifyMsg
{
public:
    CHangUpAppMsg() {};

    CHangUpAppMsg(std::string caller_sip, std::string callee_uid,   std::string nick_name_location,   const uint64_t traceid, const uint32_t app_type)
    {
         caller_sip_ = std::move(caller_sip);
         callee_uid_ = std::move(callee_uid);
         nick_name_location_ = std::move(nick_name_location);
         traceid_ = traceid;
         app_type_ = app_type;
    } 

    CHangUpAppMsg(const CHangUpAppMsg&& that)
    {
        caller_sip_ = std::move(that.caller_sip_);
        callee_uid_ = std::move(that.callee_uid_);
        nick_name_location_ = std::move(that.nick_name_location_);
        traceid_ = that.traceid_;
        app_type_ = that.app_type_;
     }

    ~CHangUpAppMsg() {}

    int NotifyMsg();
    const std::string& getUid()    { return callee_uid_; }
    void BuildPushMsg(const CMobileToken& app_push_token, const TokenInfo& online_token_info, const std::string& current_site, AppOfflinePushKV& push_msg);
private:
    std::string caller_sip_;
    std::string callee_uid_;
    std::string nick_name_location_; // 设备location
    uint64_t traceid_;
    uint32_t app_type_;
};


#endif

