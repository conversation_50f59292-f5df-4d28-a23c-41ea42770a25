#include "NotifyMsgControl.h"
#include "AkcsMonitor.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"
#include "PbxRpcInit.h"

extern AKCS_CONF gstAKCSConf;

CNotifyMsgControl* CNotifyMsgControl::instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::app_wakeup_instance = NULL;


CNotifyMsgControl::~CNotifyMsgControl()
{
    notify_msg_list_.clear();
}

CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}

CNotifyMsgControl* GetAppWakeupMsgControlInstance()
{
    return CNotifyMsgControl::GetAppWakeupInstance();
}

CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CNotifyMsgControl();
    }

    return instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetAppWakeupInstance()
{
    if (app_wakeup_instance == NULL)
    {
        app_wakeup_instance = new CNotifyMsgControl();
    }

    return app_wakeup_instance;
}

int CNotifyMsgControl::Init()
{
    thread_ = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success, thread_id = " << thread_.get_id();
    return 0;
}

int CNotifyMsgControl::GetNotifyMsgListSize()
{
    std::unique_lock<std::mutex> lock(mutex_);
    return notify_msg_list_.size();
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        NotifyMsgPrt msg_ptr;
        {
            std::unique_lock<std::mutex> lock(mutex_);
            while (notify_msg_list_.size() == 0)
            {
                condition_variable_.wait(lock);
            }
           
            msg_ptr = notify_msg_list_.back();
            notify_msg_list_.pop_back();
        }
        
        msg_ptr->NotifyMsg();
    }
    return 0;
}

int CNotifyMsgControl::AddWakeUpAppMsg(const CWakeUpAppMsg&& msg)
{
    {
        std::unique_lock<std::mutex> lock(mutex_);
        notify_msg_list_.emplace_back(std::make_shared<CWakeUpAppMsg>(std::move(msg)));
        condition_variable_.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddHangUpAppMsg(const CHangUpAppMsg&& msg)
{
    {
        std::unique_lock<std::mutex> lock(mutex_);
        notify_msg_list_.emplace_back(std::make_shared<CHangUpAppMsg>(std::move(msg)));
        condition_variable_.notify_all();
    }
    return 0;
}

