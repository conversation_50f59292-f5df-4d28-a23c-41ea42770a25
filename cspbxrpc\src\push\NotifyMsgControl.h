#ifndef __CSPBXRPC_NOTIFY_MSG_CONTROL_H__
#define __CSPBXRPC_NOTIFY_MSG_CONTROL_H__

#include <thread>
#include <mutex>
#include <memory>
#include <list>
#include "AkLogging.h"
#include <condition_variable>
#include "BasicDefine.h"
#include "NotifyMsg.h"
#include "NotifyHangupMsg.h"
#include "NotifyWakeupMsg.h"

class CNotifyMsg;
class CWakeUpAppMsg;
class CHangUpAppMsg;

class CNotifyMsgControl
{
public:
    using NotifyMsgPrt = std::shared_ptr<CNotifyMsg>;

public:
    CNotifyMsgControl() {};
    ~CNotifyMsgControl();

    static CNotifyMsgControl* GetInstance();
    static CNotifyMsgControl* GetAppWakeupInstance();

    //初始化
    int Init();
    int GetNotifyMsgListSize();
    int ProcessNotifyMsg();

    int AddWakeUpAppMsg(const CWakeUpAppMsg&& msg);
    int AddHangUpAppMsg(const CHangUpAppMsg&& msg);
private:
    std::list<NotifyMsgPrt> notify_msg_list_;
    std::mutex mutex_;
    std::condition_variable condition_variable_;
    std::thread thread_;
    static CNotifyMsgControl* instance;
    static CNotifyMsgControl* app_wakeup_instance;
};

CNotifyMsgControl* GetNotifyMsgControlInstance();
CNotifyMsgControl* GetAppWakeupMsgControlInstance();

#endif
