#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "InnerMsgDef.h"
#include "AES128.h"
#include "EtcdCliMng.h"
#include "AkcsWebMsgSt.h"
#include "PbxRpcInit.h"
#include "NotifyPushClient.h"
#include "PushClientMng.h"

extern AKCS_CONF gstAKCSConf;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern std::shared_ptr<evpp::EventLoop> g_etcd_loop;

CNotifyPushClient::CNotifyPushClient(evpp::EventLoop* loop, const std::string& serverAddr/*ip:port*/, const std::string& name) : CPushClient(loop, serverAddr, name)
{
    
}

PushClientPtr CNotifyPushClient::CreateClient(const std::string &addr, evpp::EventLoop* loop)
{
    //获取srv id
    std::string  logic_srv_id = "cspbxrpc_";
    logic_srv_id += GetEth0IPAddr();
    PushClientPtr push_cli_ptr = std::make_shared<CNotifyPushClient>(loop, addr, logic_srv_id);
    return push_cli_ptr;       
}

void CNotifyPushClient::UpdatePushSrvList()
{
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) == 0)
    {
        AK_LOG_INFO << "UpdatePushSrv begin";
        CPushClientMng::Instance()->UpdatePushSrv(cspush_addrs, g_etcd_loop.get(),
            std::bind(&CNotifyPushClient::CreateClient, std::placeholders::_1, std::placeholders::_2));
    }
}

void CNotifyPushClient::buildPushMsgCall(const CMobileToken &apptoken, int is_voip, const uint64_t traceid, const AppOfflinePushKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    int app_type = apptoken.MobileType();
    int dclientver = apptoken.CommonVersion();
    std::string oem = apptoken.OemName();
    std::string token = apptoken.Token();
    std::string language = apptoken.Language();
    std::string ringtone = apptoken.Ringtone();
        
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }

    if (app_type == csmain::APP_IOS)
    {
        if (is_voip)
        {
            token = apptoken.VoipToken();
        }
        else
        {
            token = apptoken.Token();
        }
    }
    else
    {
        token = apptoken.FcmToken();
    }

    item["ver"] = PUSH_SERVER_VER;
    if (app_type == csmain::APP_IOS)
    {
        item["app_type"] = "ios";
        if (is_voip)
        {
            token = apptoken.VoipToken();
        }
        else
        {
            token = apptoken.Token();
        }
    }
    else if (app_type == csmain::APP_ANDROID_FCM)
    {
        item["app_type"] = "fcm";
        token = apptoken.FcmToken();
    }
    else if (app_type == csmain::APP_ANDROID_HUAWEI)
    {
        item["app_type"] = "android_huawei";
    }
    else if (app_type == csmain::APP_ANDROID_XIAOMI)
    {
        item["app_type"] = "android_xiaomi";
    }
    else if (app_type == csmain::APP_ANDROID_OTHERS)
    {
        item["app_type"] = "fcm";   ////兼容原先上报类型
    }
    else if (app_type == csmain::APP_ANDROID_OPPO)
    {
        item["app_type"] = "android_oppo";
    }
    else if (app_type == csmain::APP_ANDROID_VIVO)
    {
        item["app_type"] = "android_vivo";
    }
    else if (app_type == csmain::APP_ANDROID_FLYME)
    {
        item["app_type"] = "android_flyme";
    }
    else if (app_type == csmain::APP_ANDROID_JPUSH)
    {
        item["app_type"] = "android_jpush";
    }

    itemData["token"] = token;
    itemData["msg_type"] = "CALL";
    itemData["is_voip"] =  is_voip;
    itemData["dclient"] =  dclientver;
    itemData["language"] = language;
    itemData["app_oem"] = apptoken.AppOem();
    itemData["ringtone"] =  ringtone;

    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    
    //生成traceid:
    char traceid_tmp[20] = {0};
    ::snprintf(traceid_tmp, 20, "%ld", traceid);
    itemData["traceid"] = traceid_tmp;

    std::stringstream logs;
    logs << "[PushMsg] CALL, app_oem:" << itemData["app_oem"] << ", app_type = " << item["app_type"]
         << ", pushtoken = " << token << ", language = " << language << ", ringtone = " << ringtone 
         << ", calltime = " << itemData["timestamp"] << ", trace_id = " << traceid;

    std::string log = logs.str();
    boost::algorithm::replace_all(log, "\n", "");
    LOG_INFO << log;
    if (token.empty())
    {
        return;
    }

    std::string data_json = wData.write(itemData);
    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }

    std::string msg_json = w.write(item);

    PushMsg(msg_json);
    return;
}

void CNotifyPushClient::buildPushMsgHangup(const CMobileToken &apptoken, const uint64_t traceid, const AppOfflinePushKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    
    std::string token;
    if(apptoken.MobileType() == csmain::AppType::APP_IOS)
    {
        token = apptoken.Token();        
        item["app_type"] = "ios";
    }
    //sdk需支持安卓的hangup
    else
    {
        token = apptoken.FcmToken();        
        item["app_type"] = "fcm";
    }
    
    int dclientver = apptoken.CommonVersion();
    std::string language = apptoken.Language();
    std::string oem = apptoken.OemName();

    if (token.empty())
    {
        LOG_WARN << "push hangup, token is empty";
        return;
    }
    
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }

    item["ver"] = PUSH_SERVER_VER;
    itemData["token"] = token;
    itemData["msg_type"] = "HANGUP";
    itemData["is_voip"] =  0;
    itemData["dclient"] =  dclientver;
    itemData["language"] = language;
    itemData["app_oem"] = apptoken.AppOem();

    //生成traceid:
    char traceid_tmp[20] = {0};
    ::snprintf(traceid_tmp, 20, "%ld", traceid);
    itemData["traceid"] = traceid_tmp;
    
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
   
    std::string data_json = wData.write(itemData);
    LOG_INFO << "[PushMsg] data_json is:" << data_json;

    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }

    std::string msg_json = w.write(item);

    PushMsg(msg_json);
    return;
}


void CNotifyPushClient::PushMsg(const std::string& msg_json)
{  
    auto c = client_.conn();
    if (c && c->IsConnected())
    {
        c->Send(msg_json);
    }
    else
    {
        AK_LOG_INFO << "[PushMsg] conn is error msg_json = " << msg_json;
    }
}

