#ifndef __CSPBXRPC_PUSH_CLIENT_H__
#define __CSPBXRPC_PUSH_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkLogging.h"
#include "PushClient.h"
#include "util.h"
#include "MobileToken.h"
#include <boost/algorithm/string.hpp>

namespace cspbxrpc
{
enum PushMsgType
{
    PUSH_MSG_TYPE_CALL = 0,
    PUSH_MSG_TYPE_ALARM,
    PUSH_MSG_TYPE_DEALALARM,
    PUSH_MSG_TYPE_MOTION,
    PUSH_MSG_TYPE_FORCE_LOGOUT,
    PUSH_MSG_TYPE_DELIVERY,
    PUSH_MSG_TYPE_TMPKEY,
    PUSH_MSG_TYPE_TEXT,
    PUSH_MSG_TYPE_DELIVERY_BOX, //用于JTS
    PUSH_MSG_TYPE_VOICE_MSG,
    PUSH_MSG_TYPE_YALE_BATTERY,
    PUSH_MSG_TYPE_HUNGUP,
    PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST,//测试push服务是否正常
    PUSH_MSG_TYPE_BOOKING, //用于booking预约的消息
};

}
#define PUSH_SERVER_VER "1"

class CPushClient;
class CNotifyPushClient;
typedef std::shared_ptr<CPushClient> PushClientPtr;
typedef std::shared_ptr<CNotifyPushClient> CNotifyPushClientPtr;
typedef std::map<std::string/*key*/, std::string/*value*/> AppOfflinePushKV;

class CNotifyPushClient : public CPushClient
{
public:
    CNotifyPushClient(evpp::EventLoop* loop, const std::string& serverAddr/*ip:port*/, const std::string& name);

    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
    {

    }
    static PushClientPtr CreateClient(const std::string &addr, evpp::EventLoop* loop);
    static void UpdatePushSrvList();
    
    void buildPushMsgCall(const CMobileToken &apptoken, int is_voip, const uint64_t traceid, const AppOfflinePushKV& kv);
    void buildPushMsgHangup(const CMobileToken &apptoken, const uint64_t traceid, const AppOfflinePushKV& kv);

private:
    void PushMsg(const std::string& msg_json);

};



#endif // __CSMAIN_PUSH_CLIENT_H__
