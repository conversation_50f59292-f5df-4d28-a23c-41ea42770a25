#include "AppPushToken.h"
#include "PushClientMng.h"
#include "NotifyWakeupMsg.h"
#include "doorlog/UserInfo.h"

int CWakeUpAppMsg::NotifyMsg()
{
    std::string current_site = getUid();
    std::string main_site = current_site;
    
    // 判断当前站点的状态,拦截非法的离线推送
    CUserInfo user_info(current_site);
    if (!user_info.IsUserLegal())
    {
        AK_LOG_WARN << "pbx wakeup app failed, current_site status is not legal, main_site = " << main_site << ", current_site = " << current_site;
        return -1;
    }
    
    // 获取主站点信息
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(current_site, main_site);
    
    // 获取主站点推送token
    CMobileToken app_push_token;
    if (GetAppPushTokenInstance()->GetAppPushTokenByUid(main_site, app_push_token) != 0)
    {
        AK_LOG_WARN << "pbx wakeup app GetMainSitePushToken failed, main_site = " << main_site << " token invalid, current_site = " << current_site;
        return -1;
    }

    // 获取铃声信息
    GetRingtone(current_site, app_push_token);
    
    // 获取callkit信息
    TokenInfo online_token_info;
    if (dbinterface::Token::GetTokenInfoByAccount(main_site, online_token_info) != 0)
    {    
        AK_LOG_WARN << "pbx wakeup app GetTokenInfoByAccount failed, main_site = " << main_site << " token invalid, current_site = " << current_site;
        return -1;
    }

    AppOfflinePushKV push_msg;
    BuildPushMsg(app_push_token, current_site, push_msg);
    
    int is_voip = IsVoip(app_push_token, online_token_info);

    CNotifyPushClientPtr push_cli_ptr = std::dynamic_pointer_cast<CNotifyPushClient>(CPushClientMng::Instance()->GetPushSrv());
    if (push_cli_ptr)
    {
        push_cli_ptr->buildPushMsgCall(app_push_token, is_voip, traceid_, push_msg);
    }

    AK_LOG_INFO << "pbx wakeup app, caller = " << caller_sip_ << ", caller name = " << nick_name_location_ 
                << ", main_site = " << main_site << ", current_site = " << current_site << ", is_multi_site = " << app_push_token.IsMultiSite();
    return 0;
}

int CWakeUpAppMsg::IsVoip(const CMobileToken& app_push_token, const TokenInfo& online_token_info)
{
    if (app_push_token.MobileType() == csmain::AppType::APP_IOS 
        && app_push_token.VoipToken().size() > 0 
        && online_token_info.enable_callkit)
    {
        return 1;
    }
    
    return 0;
}

void CWakeUpAppMsg::BuildPushMsg(const CMobileToken& app_push_token, const std::string& current_site, AppOfflinePushKV& push_msg)
{
    std::string title_prefix;
    if (app_push_token.IsMultiSite() && OfflinePush::GetMultiSiteUserTitle(current_site, title_prefix) == 0)
    {
       push_msg.insert(map<std::string, std::string>::value_type("title_prefix", title_prefix));
    }
    
    push_msg.insert(map<std::string, std::string>::value_type("user", nick_name_location_));
    push_msg.insert(map<std::string, std::string>::value_type("timestamp",  timestamp_));

    return;
}

void CWakeUpAppMsg::GetRingtone(const std::string& current_site, CMobileToken& app_push_token)
{
    if (app_push_token.MobileType() == csmain::AppType::APP_IOS)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(current_site, account))
        {
            app_push_token.setRingtone(account.ringtone);
        }
    }
    
    return;
}
