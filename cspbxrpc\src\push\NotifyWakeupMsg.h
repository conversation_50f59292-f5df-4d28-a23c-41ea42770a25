#ifndef __CSPBXRPC_NOTIFY_WAKEUP_APP_MSG_H__
#define __CSPBXRPC_NOTIFY_WAKEUP_APP_MSG_H__

#include <string>
#include <memory>
#include "InnerMsgDef.h"
#include "AkLogging.h"
#include "MobileToken.h"
#include "AkcsCommonDef.h"
#include "dbinterface.h"
#include "NotifyMsg.h"
#include "NotifyMsgControl.h"
#include "NotifyPushClient.h"

class CWakeUpAppMsg : public CNotifyMsg
{
public:    
    CWakeUpAppMsg() {};
    ~CWakeUpAppMsg() {}
    
    CWakeUpAppMsg(std::string caller_sip, std::string callee_uid, std::string nick_name_location, const uint64_t traceid, const uint32_t app_type, std::string timestamp) 
    {
        caller_sip_ = std::move(caller_sip);
        callee_uid_ = std::move(callee_uid);
        nick_name_location_ = std::move(nick_name_location);
        traceid_ = traceid;
        app_type_ = app_type;
        timestamp_ = timestamp;
    }
                       
    CWakeUpAppMsg(const CWakeUpAppMsg& that)
    {
        caller_sip_ = that.caller_sip_;
        callee_uid_ = that.callee_uid_;
        nick_name_location_ = that.nick_name_location_;
        traceid_ = that.traceid_;
        app_type_ = that.app_type_;
        timestamp_ = that.timestamp_;
    }
    
    CWakeUpAppMsg(CWakeUpAppMsg &&that)
    {
        caller_sip_ = std::move(that.caller_sip_);
        callee_uid_ = std::move(that.callee_uid_);
        nick_name_location_ = std::move(that.nick_name_location_);
        traceid_ = that.traceid_;
        app_type_ = that.app_type_;
        timestamp_ = std::move(that.timestamp_);
    }
    
    int NotifyMsg();
    const std::string& getUid() { return  callee_uid_; }
    int IsVoip(const CMobileToken& app_push_token, const TokenInfo& online_token_info);
    void GetRingtone(const std::string& current_site, CMobileToken& app_push_token);
    void BuildPushMsg(const CMobileToken& app_push_token, const std::string& current_site, AppOfflinePushKV& msg);
private:
    std::string caller_sip_;
    std::string callee_uid_;
    std::string nick_name_location_; //昵称 or 设备location
    uint64_t traceid_;
    uint32_t app_type_;
    std::string timestamp_;
};

#endif
