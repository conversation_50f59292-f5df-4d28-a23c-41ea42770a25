CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (cspdu2kafkamq  CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(CSBASE_DIR ../csbase)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libglog.so libmysqlclient.so librdkafka++.so librdkafka.so libdl.so libz.so libcrypto.so libssl.so 
libevpp.so libglog.so libprotobuf.so cppkafka)

AUX_SOURCE_DIRECTORY(./src SRC_LIST_PRODUCER)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/Rldb SRC_LIST_DB)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/metrics SRC_LIST_BASE_METRICS)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR} 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/thirdlib 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/redis/hiredis 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/evpp/lib /usr/local/lib)


SET(BASE_LIST_INC ${CSBASE_DIR} ${CSBASE_DIR}/mysql/include ${CSBASE_DIR}/Rldb  ${CSBASE_DIR}/librdkafka ${CSBASE_DIR}/cppkafka
${CSBASE_DIR}/evpp ${CSBASE_DIR}/jsoncpp0.5/include ${CSBASE_DIR}/encrypt ${CSBASE_DIR}/metrics)

ADD_DEFINITIONS( -std=c++11 -g -Werror -Wno-unused-parameter -Wno-deprecated -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories( ${BASE_LIST_INC} ./src /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

add_executable(cspdu2kafkamq ${SRC_LIST_PRODUCER} ${SRC_LIST_DB} ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_BASE_METRICS})

set_source_files_properties(
    ./src/kafka_producer_handle.cpp
    PROPERTIES
    COMPILE_FLAGS "-w"
)

set_target_properties(cspdu2kafkamq PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/cspdu2kafkamq/lib")
target_link_libraries(cspdu2kafkamq  ${DEPENDENT_LIBRARIES})
