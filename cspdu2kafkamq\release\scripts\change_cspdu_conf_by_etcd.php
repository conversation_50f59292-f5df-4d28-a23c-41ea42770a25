<?php

function cmd_usage($cmd){
	echo("usage: ".$cmd. " <etcd_addr> <key>\n");  
	exit(0);
}

if ($argc < 3){  
	cmd_usage($argv[0]);
}
$etcd_addr = $argv[1];
$key = $argv[2];

if ($key == "/akconf/db_addr"){
    $retval =  "";
    $value = "";
    exec("/bin/etcdctl --endpoints=\"http://$etcd_addr\" get $key", $output, $return_val);
    if ($return_val == 0){
       $value = $output[1];
    }
    if($value){
        echo "change_cspdu: /akconf/db_addr change ".$value;
        db_change($value);
    }
}

function db_change($value){
    $value_arr = explode(":",$value);
    $dbhost = $value_arr[0];
    $dbport = $value_arr[1];


    $app_exec[0] = "sed -i 's/^.*db_ip.*/db_ip=$dbhost/g' /usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf";
    $app_exec[1] = "sed -i 's/^.*db_port.*/db_port=$dbport/g' /usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf";
    $app_exec[2] = "kill -9 `ps -ef|grep cspdu2kafkamq|grep -v grep|grep -v run|awk '{print $2}'`";

    foreach($app_exec as $app_exec_value){
        exec($app_exec_value);
    }
}
