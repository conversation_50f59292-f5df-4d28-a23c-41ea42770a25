#!/bin/bash
ACMD="$1"
cspdu2kafkamq_BIN='/usr/local/akcs/cspdu2kafkamq/bin/cspdu2kafkamq'
PROCESS_PID_FILE=/var/run/cspdu2kafkamq.pid
if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

if [ -f $PROCESS_PID_FILE ];then
    pid=`cat $PROCESS_PID_FILE`
else
    #重启之后没有这个pid文件
    pid="xxxxxxxxxx"
fi

start_cspdu2kafkamq()
{
    nohup $cspdu2kafkamq_BIN >/dev/null 2>&1 &
    echo "Start cspdu2kafkamq successful"
    if [ -z "`ps -fe|grep "cspdu2kafkamqrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/cspdu2kafkamq/scripts/cspdu2kafkamqrun.sh >/dev/null 2>&1 &
    fi
}
stop_cspdu2kafkamq()
{
    echo "Begin to stop cspdu2kafkamqrun.sh"
    kill -9 `ps aux | grep -w cspdu2kafkamqrun.sh | grep -v grep | awk '{ print $(2) }'`
    echo "Begin to stop cspdu2kafkamq"
    kill -9 `pidof cspdu2kafkamq`
    sleep 2
    echo "Stop cspdu2kafkamq successful"
}

case $ACMD in
  start)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_cspdu2kafkamq
    else
        echo "cspdu2kafkamq is already running"
    fi
    ;;
  stop)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "cspdu2kafkamq is already stopping"
    else
        stop_cspdu2kafkamq
    fi
    ;;
  restart)
    stop_cspdu2kafkamq
    sleep 1
    start_cspdu2kafkamq
    ;;
  status)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m cspdu2kafkamq is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m cspdu2kafkamq is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

