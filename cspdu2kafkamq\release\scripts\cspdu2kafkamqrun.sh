#!/bin/bash

#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/cspdu2kafkamq/scripts/sedconf.sh

PROCESS_NAME=cspdu2kafkamq
PROCESS_START_CMD="/usr/local/akcs/cspdu2kafkamq/scripts/cspdu2kafkamqctl.sh start"
PROCESS_PID_FILE=/var/run/cspdu2kafkamq.pid
LOG_FILE=/var/log/cspdu2kafkamq_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/cspdu2kafkamq/scripts/common.sh"
CSPDU2KAFKAMQ_BIN='/usr/local/akcs/cspdu2kafkamq/bin/cspdu2kafkamq'

#容器化后直接前台运行，这样挂了之后docker也会重启，这样才能监控检测
$CSPDU2KAFKAMQ_BIN > /dev/null 2>&1