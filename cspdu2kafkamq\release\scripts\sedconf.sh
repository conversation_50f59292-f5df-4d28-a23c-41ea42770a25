#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

# cspdu2kafkamq.conf

# db 配置
if [ "$ENABLE_AKCS_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" /usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" /usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf
fi
 
# kafka配置
sed -i "
    s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g
	" /usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf

# etcd 配置
sed -i "
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
	" /usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf

