#include <stdexcept>
#include <iostream>
#include "cppkafka/utils/buffered_producer.h"
#include "cppkafka/configuration.h"
#include "ConnectionPool.h"
#include "AkLogging.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "kafka_transaction_def.h"
#include "json/json.h"
#include "ConfigFileReader.h"

using cppkafka::BufferedProducer;
using cppkafka::Configuration;
using cppkafka::Topic;
using cppkafka::MessageBuilder;
using cppkafka::Message;


extern KAFKA_PRODUCER_CONF gstProducerConf;
const int gOnecGetCount = 2000;
static const char kAkcsMonitorAlarmProduceKafkaSip[] = "alarm.kafka.produce.sip";
static const char kAlarmTopic[] = "akcs_alarm";
//移除nsq，临时方案直接通过curl进行发送
void PushMonitorAlarm(const std::string& worker_node, const std::string& description, const std::string&alarm_key)
{
    //获取当前的大致时间。
    time_t setTime;
    time(&setTime);
    tm* ptm = localtime(&setTime);
    std::string time = std::to_string(ptm->tm_year + 1900)
                       + "-"
                       + std::to_string(ptm->tm_mon + 1)
                       + "-"
                       + std::to_string(ptm->tm_mday)
                       + " "
                       + std::to_string(ptm->tm_hour) + ":"
                       + std::to_string(ptm->tm_min) + ":"
                       + std::to_string(ptm->tm_sec);
    
    //初始化内网外网ip
    CConfigFileReader ipfile("/etc/ip"); 
    std::string hostname = ipfile.GetConfigName("AKCS_HOSTNAME");
    std::string out_ip = ipfile.GetConfigName("SERVERIP");

    Json::Value item;
    Json::FastWriter w;
    item["node"] = worker_node;
    item["time"] = time;
    item["key"] = alarm_key;
    item["description"] = description;//description 就是对应的value,再iptables告警下，就是对应得black_ip
    item["hostname"] = hostname;
    item["ip"] = out_ip;
    std::string alarm_msg_json = w.write(item);
    AK_LOG_INFO << "push monitor alarm msg:" << alarm_msg_json;

    std::string url_ip = GetEth0IPAddr(); //eth0地址
    std::string url_port = "8513"; //nsqd端口
    std::string param = "topic=" + std::string(kAlarmTopic); //topic作为http参数
    std::string path = "pub"; //nsq指定path
    std::string url = "http://" + url_ip + ":" + url_port + "/" + path + "?" + param;


    char curl_cmd[1024] = {0};
    snprintf(curl_cmd, sizeof(curl_cmd), "curl -H Content-type: application/json -X POST -d '%s' %s", alarm_msg_json.c_str(), url.c_str());
    if (system(curl_cmd))
    {
        AK_LOG_WARN  << "push monitor alarm msg to nsq failed: " << alarm_msg_json;
        char mutt_cmd[1024] = {0};
        snprintf(mutt_cmd, sizeof(mutt_cmd), "echo \"%s\" | mutt -s \"SystemMonitor publish alarm msg error\"  -b <EMAIL> -c  <EMAIL> -c <EMAIL> \
                 <EMAIL> &", alarm_msg_json.c_str());
        system(mutt_cmd);
        return;
    }
    return;
}

void handle_transaction_msg()
{
    // Create a message builder for this topic
    MessageBuilder builder(gstProducerConf.kafka_consumer_topic_name);


    // Construct the configuration
    //配置看rdkafka_conf.c
    Configuration config =
    {
        { "metadata.broker.list", gstProducerConf.kafka_broker_ip },
        {"request.required.acks", "1"},//leader 接收到就算成功
        //{ "enable.auto.commit", "false" },
        //{ "auto.commit.interval.ms", "1000" },
        //{ "offset.store.method", "broker" }
    };

    // Create the producer
    //自己在断开连接时候会重新尝试，应用不需要处理
    BufferedProducer<std::string> producer(config);

    // Set a produce success callback
    //producer.set_produce_success_callback([](const Message& msg) {
    //    cout << "Successfully produced message with payload " << msg.get_payload() << endl;
    //});
    // Set a produce failure callback
    producer.set_produce_failure_callback([](const Message & msg)
    {
        AK_LOG_WARN << "Failed to produce message with payload " << msg.get_payload();
        // Return false so we stop trying to produce this message
        return false;
    });

    uint64_t last_id = 0;
    KAFKA_CONSUMER_MSG msg;
    memset(&msg, 0, sizeof(msg));
    while (1)
    {
        int get_count = 0;
        std::stringstream streamSQL;
        std::stringstream stream_update_statu_sql;
        int back_id = last_id;
        if (back_id > 500)
        {
            //回退500条，解决web插入id在并发的情况下没有办法保证有序。比如我们这里读到了id=100的数据，但是可能id=99还没有插入，导致99用户不会被处理
            back_id = back_id - 500;
        }
        streamSQL << "/*master*/SELECT ID,Sip,Message,MessageStatus FROM LocalSipTransaction WHERE id > " << back_id << " and  MessageStatus=0 order by id limit " << gOnecGetCount;

        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* pTmpConn = conn.get();
        if (NULL == pTmpConn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            continue;
        }
        conn->BeginTransAction();

        CRldbQuery query(pTmpConn);
        query.Query(streamSQL.str());
        
        while (query.MoveToNextRow())
        {
            memset(&msg, 0, sizeof(msg));
            msg.id = atoi(query.GetRowData(0));
            snprintf(msg.sip, sizeof(msg.sip), "%s", query.GetRowData(1));
            snprintf(msg.message, sizeof(msg.message), "%s", query.GetRowData(2));
            msg.message_status = atoi(query.GetRowData(3));

            //发送，如果发送失败，则这组事务里面的消息需要全部重新发送，那么消费端就会多消费，但是因为消息队列可以保证顺序
            //并且业务已经支持幂等性。所以批量处理，一次事务处理多条消息，提高性能。
            {
                //不能把char类型的message直接放入，这样会导致发送出结束符之后的所有数据
                std::string payload = msg.message;
                std::string key = msg.sip;
                builder.payload(payload);
                builder.key(key);
                producer.produce(builder);
            }

            stream_update_statu_sql << msg.id << ",";
            get_count++;
        }

        std::string ids = stream_update_statu_sql.str();
        if (ids.length() > 0)
        {
            ids = ids.substr(0, ids.length() - 1);
            std::stringstream db_cmd; 
            db_cmd << "/*master*/update LocalSipTransaction set MessageStatus=1,HandleTime=now() where id in (" << ids << ")";

            int nRet = conn->Execute(db_cmd.str());
            if (nRet == -1)
            {
                conn->TransActionRollback();

                std::string worker_node = "cspdu2kafkamq";
                std::stringstream alarm_msg;
                alarm_msg << "update LocalSipTransaction failed.SQL=" << db_cmd.str();
                PushMonitorAlarm(worker_node, alarm_msg.str(), kAkcsMonitorAlarmProduceKafkaSip);
                AK_LOG_FATAL << "update LocalSipTransaction failed.SQL=" << db_cmd.str();
            }
        }
        producer.async_flush();
        std::chrono::milliseconds mscond(1000 * 15);
        if (!producer.wait_for_acks(mscond))
        {
            conn->TransActionRollback();

            std::string worker_node = "cspdu2kafkamq";
            std::stringstream alarm_msg;
            alarm_msg << "Producer message wait akcs timeout! ids=" << ids;
            PushMonitorAlarm(worker_node, alarm_msg.str(), kAkcsMonitorAlarmProduceKafkaSip);
            AK_LOG_FATAL << "Producer message wait akcs timeout! ids=" << ids;
        };

        conn->EndTransAction();
        last_id = msg.id;
        ReleaseDBConn(conn);
        if (get_count >= gOnecGetCount)
        {
            sleep(1);
        }
		else
		{
			sleep(5);
		}
    }
}

