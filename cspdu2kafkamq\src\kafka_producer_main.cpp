#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <iostream>
#include <sstream>
#include <thread>
#include <fcntl.h>
#include "kafka_transaction_def.h"
#include "kafka_producer_handle.h"
#include "ConfigFileReader.h"
#include <unistd.h>
#include <signal.h>
#include "AkLogging.h"
#include "ConnectionPool.h"
#include "util.h"
#include <KdcDecrypt.h>

KAFKA_PRODUCER_CONF gstProducerConf;
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(CSPDUKAFKA_MQ_PID, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}



void ConfInit()
{
    memset(&gstProducerConf, 0, sizeof(KAFKA_CONSUMER_CONF));
    CConfigFileReader config_file(CSPDUKAFKA_MQ_CONF_FILEPATH);

    Snprintf(gstProducerConf.db_ip, sizeof(gstProducerConf.db_ip),  config_file.GetConfigName("db_ip"));
    Snprintf(gstProducerConf.db_usernmae, sizeof(gstProducerConf.db_usernmae),  config_file.GetConfigName("db_username"));

    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstProducerConf.db_password, sizeof(gstProducerConf.db_password), decrypt_db_passwd.c_str());

    Snprintf(gstProducerConf.db_database, sizeof(gstProducerConf.db_database),  config_file.GetConfigName("db_database"));
    const char* db_port = config_file.GetConfigName("db_port");
    gstProducerConf.db_port = ::atoi(db_port);

    Snprintf(gstProducerConf.kafka_consumer_topic_name, sizeof(gstProducerConf.kafka_consumer_topic_name),  config_file.GetConfigName("kafka_consumer_topic_name"));
    Snprintf(gstProducerConf.kafka_consumer_group, sizeof(gstProducerConf.kafka_consumer_group),  config_file.GetConfigName("kafka_consumer_group"));
    Snprintf(gstProducerConf.kafka_broker_ip, sizeof(gstProducerConf.kafka_broker_ip),  config_file.GetConfigName("kafka_broker_ip"));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));
}


void StartUDPServer()
{

}


void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/cspdu2kafkamqlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/cspdu2kafkamqlog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/cspdu2kafkamqlog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/cspdu2kafkamqlog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstProducerConf.db_ip, gstProducerConf.db_usernmae, gstProducerConf.db_password,
                    gstProducerConf.db_database, gstProducerConf.db_port, 1, "cspdu2kafkamq");
    return 0;
}


int main(int argc, char* argv[])
{

    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        printf("another cspdu2kafkamq has been running in this sytem.");
        return -1;
    }


    /* 读取配置文件 */
    ConfInit();

    glogInit(argv[0]);

    /* 初始化数据库连接 */
    int nRet = DaoInit();
    if (0 != nRet)
    {
        AK_LOG_WARN << "DaoInit fialed.";
        glogClean();
        return -1;
    }


    AK_LOG_INFO << "kafka_consumer is starting";

    handle_transaction_msg();
    
    glogClean();
    return 0;
}




