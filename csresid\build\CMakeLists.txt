CMAKE_MINIMUM_REQUIRED(VERSION 2.8)
 
project (csresid C CXX)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../src ${CMAKE_CURRENT_SOURCE_DIR}/../../csbase/doorlog")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libcsbase.a pthread libevent.so libhiredis.a libglog.so libmysqlclient.so libgpr.so libgrpc.so libgrpc++.so libprotobuf.so libevpp.so -lssl -lcrypto -lcpprest -letcd-cpp-api  -levpp -levent -lboost_system -lcurl)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/redis/hiredis ${CSBASE_SOURCE_DIR}/thirdlib/oss /usr/local/lib)

AUX_SOURCE_DIRECTORY(../src SRC_LIST_SESSION)
AUX_SOURCE_DIRECTORY(../src/main SRC_LIST_FW)
AUX_SOURCE_DIRECTORY(../src/core SRC_LIST_FWCORE)
AUX_SOURCE_DIRECTORY(../src/route SRC_LIST_FWROUTE)
AUX_SOURCE_DIRECTORY(../src/main/videorecord SRC_LIST_VIDEORECORD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/jsoncpp0.5/src SRC_LIST_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/encrypt SRC_LIST_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Character SRC_LIST_CHAR)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Character/cstring SRC_LIST_CSTR)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/protobuf SRC_LIST_PROTBUF)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/nsq SRC_LIST_NSQ)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/model SRC_LIST_MODEL)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/csroute SRC_LIST_ROUTE)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/cspush SRC_LIST_PUSH)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/backend SRC_LIST_BACKEND)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/doorlog SRC_LIST_BASE_DOORLOG)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/csvideorecord SRC_LIST_BASE_VIDEORECORD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/grpc/csvideorecord SRC_LIST_BASE_GRPC_VIDEORECORD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Tinyxml SRC_LIST_BASE_XML)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/loop SRC_LIST_BASE_LOOP)

SET(BASE_LIST_INC
     ./ 
     ../include
     ../src/main 
     ../src/main/videorecord
     ../src/core 
     ../src/route
     ${CSBASE_SOURCE_DIR} 
     ${CSBASE_SOURCE_DIR}/Character 
     ${CSBASE_SOURCE_DIR}/Character/cstring 
     ${CSBASE_SOURCE_DIR}/encrypt 
     ${CSBASE_SOURCE_DIR}/mysql/include 
     ${CSBASE_SOURCE_DIR}/Rldb 
     ${CSBASE_SOURCE_DIR}/redis 
     ${CSBASE_SOURCE_DIR}/etcd 
     ${CSBASE_SOURCE_DIR}/evpp 
     ${CSBASE_SOURCE_DIR}/dbinterface 
     ${CSBASE_SOURCE_DIR}/dbinterface/Log
     ${CSBASE_SOURCE_DIR}/dbinterface/resident 
     ${CSBASE_SOURCE_DIR}/dbinterface/doorlog
     ${CSBASE_SOURCE_DIR}/protobuf  
     ${CSBASE_SOURCE_DIR}/nsq 
     ${CSBASE_SOURCE_DIR}/jsoncpp0.5/include 
     ${CSBASE_SOURCE_DIR}/model 
     ${CSBASE_SOURCE_DIR}/csroute
     ${CSBASE_SOURCE_DIR}/csvideorecord
     ${CSBASE_SOURCE_DIR}/cspush
     ${CSBASE_SOURCE_DIR}/gid
     ${CSBASE_SOURCE_DIR}/backend
     ${CSBASE_SOURCE_DIR}/metrics
     ${CSBASE_SOURCE_DIR}/grpc 
     ${CSBASE_SOURCE_DIR}/grpc/gens 
     ${CSBASE_SOURCE_DIR}/grpc/include 
     ${CSBASE_SOURCE_DIR}/grpc/cssession 
     ${CSBASE_SOURCE_DIR}/grpc/csvideorecord
     ${CSBASE_SOURCE_DIR}/Tinyxml
)     

ADD_DEFINITIONS( -std=c++11 -g  -W -Wall -Werror -Wno-unused-parameter -Wno-deprecated -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories(${BASE_LIST_INC} /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include)

add_executable(csresid  ${SRC_LIST_ROUTE} ${SRC_LIST_PUSH} ${SRC_LIST_SESSION} ${SRC_LIST_BASE_XML} ${SRC_LIST_PROTBUF} ${SRC_LIST_NSQ} ${SRC_LIST_CSTR} ${SRC_LIST_CHAR} 
${SRC_LIST_ENCRYPT} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_JSON}  ${SRC_LIST_MODEL} ${SRC_LIST_BACKEND} ${SRC_LIST_FW} ${SRC_LIST_FWCORE} 
${SRC_LIST_FWROUTE} ${SRC_LIST_BASE_METRICS} ${prefixed_file_list} ${SRC_LIST_BASE_DOORLOG} ${SRC_LIST_BASE_VIDEORECORD} ${SRC_LIST_BASE_GRPC_VIDEORECORD}  ${SRC_LIST_VIDEORECORD}
${SRC_LIST_BASE_LOOP})

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../release/bin)
set_target_properties(csresid PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csresid/lib")

target_link_libraries(csresid  ${DEPENDENT_LIBRARIES})