﻿#ifndef _APP_CALL_STATUS_DND_H_
#define _APP_CALL_STATUS_DND_H_

#include <boost/noncopyable.hpp>
#include <string>
#include "dbinterface/AppCallDndDB.h"
#include "AkcsCommonDef.h"

enum AppCallStats
{
	APP_CALL_STATUS_DISABLED = 0, //未开启
	APP_CALL_STATUS_ENABLED = 1 //已开启
};

class AppCallStatus : private boost::noncopyable
{
public:
    static AppCallStatus& GetInstance();

	int GetAppState(const std::string& account, unsigned int app_type = (unsigned int)AkcsDeviceType::AKCS_DEVICE_TYPE_APP);

	AppDndInfo GetCacheDndInfo(std::string account);

	AppDndInfo GetDbDndInfo(std::string account);
	
	int GetElapsedMinutes(std::string current_time);
};

#endif

