#ifndef __CLIENT_CONTROL_H__
#define __CLIENT_CONTROL_H__

#include <string>
#include "AkcsCommonDef.h"

//csresid与客户端消息交互的控制类
class CClientControl
{
public:
    CClientControl();
    ~CClientControl();

    //初始化
    void Init();
    static CClientControl* GetInstance();
    int SendTransferMsg(const std::string& client, const csmain::DeviceType type, const unsigned char* data, uint32_t size);
    int SendTransferMsg(MsgStruct& msg);

private:
    static CClientControl* instance_;

};

CClientControl* GetClientControlInstance();

#endif //__CLIENT_CONTROL_H__

