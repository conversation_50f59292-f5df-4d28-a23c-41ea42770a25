#ifndef __CSRESID_ONLINE_MANAGE_H__
#define __CSRESID_ONLINE_MANAGE_H__
#include <string>
#include <mutex>
#include <deque>
#include <pthread.h>
#include <unistd.h>
#include "ResidDb.h"
typedef struct MacInfo_t
{
    char mac[16];
    int type;
    int flags;
    int firmware_number; 
    int is_personal;
    char uuid[64];
    int init_status;
    int mng_id;
    char node[64];
    int project_type;
    char project_uuid[64];
    char ins_uuid[64];
    char node_uuid[64];
    int enable_smarthome;
    short oem_id;
    char sip[16];
    char timezone[32];
    char log_delivery_uuid[64];
    int grade;
    csmain::DeviceType conn_type;//office/comm/per
    bool is_support_scan_indoor_qrcode_to_reg_enduser;
}MacInfo;
void CreateOnlineMacInfo(MacInfo &macinfo, const ResidentDev &dev_setting);


class DevOnlineMng
{
public:
    DevOnlineMng() {}
    ~DevOnlineMng();
    void InitMacInfo(const std::string &mac, ResidentDev &deviceSetting, MacInfo &macinfo);
    static DevOnlineMng* GetInstance();
    void AddPerMac(const MacInfo &msg);
    void AddCommunityMac(const MacInfo &msg);
    void AddOfficeMac(const MacInfo &msg);
    static void* DevOnlineThread(void* mng);
    int Init();
private:
    void CheckPerOnlineMsg();
    void CheckOfficeOnlineMsg();
    void CheckCommunityOnlineMsg();    
    void SendDevOnlineNotifyMsg(const MacInfo& macinfo);
    pthread_t thread_process;
    std::mutex per_online_mutex_;
    std::deque<MacInfo> per_eque_;

    std::mutex comm_online_mutex_;
    std::deque<MacInfo> comm_eque_;

    std::mutex office_online_mutex_;
    std::deque<MacInfo> office_eque_;    
};



#endif // __CSMAIN_ONLINE_MANAGE_H__