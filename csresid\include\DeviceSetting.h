#ifndef __DEVICE_SETTING_H__
#define __DEVICE_SETTING_H__
#include <string>
#include "util_cstring.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Account.h"
#include "InnerMsgDef.h"
class CDeviceSetting
{
public:
    CDeviceSetting();
    ~CDeviceSetting();

    //根据MAC获取设备设置信息
    int GetDeviceSettingByMac(const std::string& mac, ResidentDev &device_setting);

    static CDeviceSetting* GetInstance();

private:
    static CDeviceSetting* instance;

};
CDeviceSetting* GetDeviceSettingInstance();

#endif
