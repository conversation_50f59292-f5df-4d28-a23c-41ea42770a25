#ifndef __MSG_CONTROL_H__
#define __MSG_CONTROL_H__

#include "json/json.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "Resid2MainHandle.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "ResidServer.h"
#include "AkcsCommonDef.h"


typedef std::function<int(char *, void*)> ParseMacCallback;
typedef std::function<int(char *, void*, std::string&)> ParseHagerCallback;
extern ResidServer* g_resid_srv_ptr;

//class evpp::Any;
class CMsgControl
{
public:
    enum ActOpenDoorType
    {
        CALL = 0,//call开门截图——社区这里代表app 开门  个人还未改
        TMPKEY = 1,
        LOCALKEY = 2,  //私钥
        RFCARD = 3,
        FACE = 4,
        REMOTE_OPEN_DOOR = 5,//dclient开门 app首页开门
        
        PM_UNLOCK = 9,     //PM一键开门
        AUTO_UNLOCK = 10,  //设备告警自动开门
        
        CALL_CAPTURE = 103,//通话截图，在呼叫时候就截图，不管是否接听            
        TEMP_CAPTURE = 104,//测温截图
        PM_LOCK = 105,      //PM一键关门
        
        //以下三个由设备CALL=0 转为平台要求的类型
        CLOUD_CALL_UNLOCK_APP = 6,
        CLOUD_CALL_UNLOCK_INDOOR = 7,
        CLOUD_CALL_UNLOCK_GUARD_PHONE = 8,
        
        //以下三个由设备REMOTE_OPEN_DOOR=5 转为平台要求的类型
        CLOUD_REMOTE_UNLOCK_APP = 5,
        CLOUD_REMOTE_UNLOCK_INDOOR = 7,
        CLOUD_REMOTE_UNLOCK_GUARD_PHONE = 8,        
        CLOUD_NFC = 100,
        CLOUD_BLE = 101,//小于 102 全部是开门截图(web需要根据这个标识过滤所有开门)
        CLOUD_APP_MANUAL = 102,//app手动截图 放在这里是因为截图放的表在一起
    };
public:
    CMsgControl();
    ~CMsgControl();
    static CMsgControl* GetInstance();
    int OnDeviceReportStatusMsg(SOCKET_MSG_NORMAL* msg, const MsgStruct* acc_msg);
    int OnDeviceReportVoiceMsg(const MsgStruct* acc_msg);
    //int OnDeviceRequestVoiceMsgList(const MsgStruct* acc_msg);
    int OnDeviceRequestVoiceMsgUrl(const MsgStruct* acc_msg);
    int OnDeviceRequestDelVoiceMsg(const MsgStruct* acc_msg);
    int OnDeviceReportRelayStatusMsg(const MsgStruct* acc_msg, int already_check = 0);

    void AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, const PersonalVoiceMsgInfo &per_voice_msg, int type);

    // v6.7
    int GetDevRegionInfo(const ResidentDev& dev, SOCKET_MSG_DEV_WEATHER_INFO& weather_info);
    int OnDeviceRequestWeatherInfoMsg(const MsgStruct* acc_msg);
    int OnDeviceRequestPacportRegistMsg(const MsgStruct* acc_msg);
    int OnDeviceRequesetPacportUnlockMsg(const MsgStruct* acc_msg);
    // hager kit
    int OnDeviceRequestCreateRoom(const MsgStruct* acc_msg);
    
    int OnAndroidReportStatusMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg);
    int OnIOSReportStatusMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg);
    int OnConnChange(const MsgStruct* acc_msg);
    int OnAppForceLogout(const MsgStruct* acc_msg);
    int OnSendDevListChangeMsg(const std::string& uid);
    // doorcom快递柜demo
    int OnDeviceReportDoorcomDeliveryMsg(const MsgStruct* acc_msg);
    int OnAppRequestChangeRelayMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg);
    
private:
    int GetPacportDevRegInfo(const ResidentDev& dev, SOCKET_MSG_PACPORT_REG_INFO& pacport_reg_info);
private:
    static CMsgControl* instance;
};

CMsgControl* GetMsgControlInstance();

#define GET_DEV_SETTING(mac, dev) \
    memset(&dev, 0, sizeof(dev)); \
    if (g_resid_srv_ptr->GetDevSetting(mac, dev) < 0) { \
        AK_LOG_WARN << "GetDeviceSetting failed. mac is " << mac; \
        return -1; \
    }

#define GET_DEV_SETTING_RET_VOID(mac, dev) \
    memset(&dev, 0, sizeof(dev)); \
    if (g_resid_srv_ptr->GetDevSetting(mac, dev) < 0) { \
        AK_LOG_WARN << "GetDeviceSetting failed. mac is " << mac; \
        return; \
    }

#define GET_APP_SETTING(uid, app) \
    memset(&app, 0, sizeof(app)); \
    if (g_resid_srv_ptr->GetAppSetting(uid, app) < 0) { \
        AK_LOG_WARN << "GetAppSetting failed. uid is " << uid; \
        return -1; \
    }



int MsgParseByMac(const MsgStruct* acc_msg, ResidentDev &dev, ParseMacCallback cb, void *st);
int HagerKitMsgParse(const MsgStruct* acc_msg, ParseMacCallback cb, void *st);

#endif

