#ifndef __NOTIFY_ALARM_H__
#define __NOTIFY_ALARM_H__

#include <thread>
#include <mutex>
#include <memory>
#include <list>
#include "NotifyMsgControl.h"
#include "DclientMsgDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalAlarm.h"
#include "DclientMsgSt.h"
#include "AgentBase.h"
#include "dbinterface/AlarmDB.h"
#include "NotifyCommunityAlarm.h"
#include "NotifyPersonalAlarm.h"

class CNotifyMsg; //前置声明

class CAlarmNotifyMsg : public CNotifyMsg
{
public:
    CAlarmNotifyMsg() = default;
    CAlarmNotifyMsg(const SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev, uint64_t trace_id)
    : alarm_msg_(alarm_msg),conn_dev_(conn_dev), trace_id_(trace_id), has_personal_alarm_(false), has_community_alarm_(false) // 使用初始化列表
    {
    }

    ~CAlarmNotifyMsg()
    {

    }

    int NotifyMsg();
    void SetPersonalAlarmInfo(const PERSONNAL_ALARM& personal_alarm);
    void SetCommunityAlarmInfo(const ALARM& alarm);
    void PostAlexaChangeStatus(uint64_t trace_id);
private:
    SOCKET_MSG_ALARM alarm_msg_;
    ResidentDev conn_dev_;
    uint64_t trace_id_;
    PERSONNAL_ALARM personal_alarm_;  // 新增：存储个人告警信息
    ALARM community_alarm_;           // 新增：存储社区告警信息
    bool has_personal_alarm_;         // 新增：标记是否有个人告警信息
    bool has_community_alarm_;        // 新增：标记是否有社区告警信息
};
#endif //__NOTIFY_ALARM_H__