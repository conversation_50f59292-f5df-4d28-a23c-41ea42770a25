#ifndef __NOTIFY_ALARM_DEAL_H__
#define __NOTIFY_ALARM_DEAL_H__

#include <thread>
#include <mutex>
#include <memory>
#include <list>
#include "NotifyMsgControl.h"
#include "DclientMsgDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalAlarm.h"
#include "DclientMsgSt.h"
#include "AgentBase.h"
#include "dbinterface/AlarmDB.h"


class CNotifyMsg; //前置声明

class CAlarmDealNotifyMsg : public CNotifyMsg
{
public:
    CAlarmDealNotifyMsg() = default;
    CAlarmDealNotifyMsg(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info, const ResidentDev& conn_dev)
    : alarm_deal_info_(alarm_deal_info),dev_(conn_dev) // 使用初始化列表
    {
    }

    ~CAlarmDealNotifyMsg()
    {

    }

    int NotifyMsg();
    void PostAlexaChangeStatus();
    void PostAlexaChangeStatusHttpReq(const std::string& mac, uint64_t traceid);
private:
    SOCKET_MSG_ALARM_DEAL alarm_deal_info_;
    ResidentDev dev_;
};
#endif //__NOTIFY_ALARM_DEAL_H__