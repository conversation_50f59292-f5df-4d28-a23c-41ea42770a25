#ifndef __NOTIFY_COMMUNITY_ALARM_H__
#define __NOTIFY_COMMUNITY_ALARM_H__

#include <thread>
#include <mutex>
#include <memory>
#include <list>
#include "DclientMsgDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DclientMsgSt.h"
#include "dbinterface/AlarmDB.h"

class CCommunityAlarmProcessor
{
public:
    CCommunityAlarmProcessor() = default;
    ~CCommunityAlarmProcessor() = default;

    static int ProcessCommunityAlarmMsg(const SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev, const ALARM& alarm);
    static int AddAlarmToDB(uint64_t trace_id, ALARM& alarm, SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev);
};

#endif //__NOTIFY_COMMUNITY_ALARM_H__ 