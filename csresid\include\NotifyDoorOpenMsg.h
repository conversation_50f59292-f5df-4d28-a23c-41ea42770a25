#ifndef __NOTIFY_DOOR_OPEN_MSG_H__
#define __NOTIFY_DOOR_OPEN_MSG_H__

#include "json/json.h"
#include "ReportActLog.h"
#include "NotifyMsgControl.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "AkcsCommonSt.h"

class CNotifyMsg; 
class CDoorOpenMsg : public CNotifyMsg
{
private:
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg_;
    ResidentDev conn_dev_;
    MacInfo mac_info_;
public:
    ~CDoorOpenMsg(){}
    CDoorOpenMsg() = default;
    CDoorOpenMsg(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& conn_dev, const MacInfo& mac_info)
    : act_msg_(act_msg),conn_dev_(conn_dev),mac_info_(mac_info) // 使用初始化列表
    {
    }

    int NotifyMsg();

private:
    // 发送开门通知到cslinker(cslinker调用接口开三方锁并记录日志)
    int  PostThirdPartyLog(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    int  PushLinKerThirdPartyLog(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ThirdPartyLockDeviceInfo& third_dev);
    int  GetLinkerMsgFromDevice(LINKER_NORMAL_MSG &linker_msg);
    int  PushLinKerOpenDoor(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const LINKER_NORMAL_MSG &linker_msg);
    int  PushLinKerTmpKey(const PersonalTempKeyUserInfo &tmpkey_info, const LINKER_NORMAL_MSG &linker_msg);
    void OpenSaltoLockNotify(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    void OpenDormakabaLockNotify(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    void OpenItecLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);    
    void OpenTTLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    void OpenSmartLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    void FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item);
    void UpdateSL20Lock(const std::string& lock_uuid, const std::string& mac, const std::string& pic_name);
};
#endif //__NOTIFY_DOOR_OPEN_MSG_H__

