#ifndef __NOTIFY_MSG_CONTROL_H__
#define __NOTIFY_MSG_CONTROL_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AK.Base.pb.h"
#include "AkcsHttpRequest.h"
#include "BasicDefine.h"

typedef std::map<std::string/*key*/, std::string/*value*/> AkcsKv;

class CHttpReqNotifyMsg;
class CPerTextNotifyMsg;
class CPerMotionNotifyMsg;
class CDoorOpenMsg;
class CVideoRecordNotifyMsg;
class CAlarmNotifyMsg;
class CAlarmDealNotifyMsg;
class CNotifyMsg
{
public:
    CNotifyMsg()
    {

    }
    virtual ~CNotifyMsg()
    {

    }
    int virtual NotifyMsg() = 0;
};


class CNotifyMsgControl
{
public:
    typedef std::shared_ptr<CNotifyMsg> NotifyMsgPrt;

public:
    CNotifyMsgControl();
    ~CNotifyMsgControl();
    //added by chenyc,2019-03-05,这个消息处理只需要关注本机即可,
    //所有的消息都通过csroute走一遍,即使是本机所挂载的终端消息之间的投递.
    static CNotifyMsgControl* GetInstance();
    static CNotifyMsgControl* GetHttpReqInstance();
    static CNotifyMsgControl* GetMotionNotifyInstance();
    static CNotifyMsgControl* GetDoorOpenInstance();
    static CNotifyMsgControl* GetAlarmInstance();
    static CNotifyMsgControl* GetAlarmDealInstance();

    //初始化
    int Init();
    int GetNotifyMsgListSize();
    //处理消息
    int ProcessNotifyMsg();
    //不需要广播
    int AddHttpReqNotiyMsg(const CHttpReqNotifyMsg& msg);
    int AddTextNotifyMsg(const CPerTextNotifyMsg& CMsg);
    int AddMotionNotifyMsg(const CPerMotionNotifyMsg& msg);
    int AddDoorOpenMsg(const CDoorOpenMsg& msg);
    int AddVideoRecordNotifyMsg(const CVideoRecordNotifyMsg& msg);
    int AddAlarmMsg(const CAlarmNotifyMsg& msg);
    int AddAlarmDealMsg(const CAlarmDealNotifyMsg& msg);
private:
    std::list<NotifyMsgPrt> m_NotifyMsgList;
    std::mutex m_mtx;
    std::condition_variable m_cv;
    std::thread m_t;
    uint32_t m_MsgCount;  //通知消息队列中未消费的消息个数
    static CNotifyMsgControl* instance;
    static CNotifyMsgControl* http_req_instance;
    static CNotifyMsgControl* motion_notify_instance;
    static CNotifyMsgControl* door_open_log_instance;
    static CNotifyMsgControl* alarm_instance;
    static CNotifyMsgControl* alarm_deal_instance;
};

CNotifyMsgControl* GetNotifyMsgControlInstance();
CNotifyMsgControl* GetHttpReqMsgControlInstance();
CNotifyMsgControl* GetMotionNotifyMsgControlInstance();
CNotifyMsgControl* GetDoorOpenMsgProcessInstance();
CNotifyMsgControl* GetAlarmMsgProcessInstance();
CNotifyMsgControl* GetAlarmDealMsgProcessInstance();
#endif //__NOTIFY_MSG_CONTROL_H__

