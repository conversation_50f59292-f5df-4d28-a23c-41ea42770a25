#ifndef __NOTIFY_PER_MOTION_MSG_H__
#define __NOTIFY_PER_MOTION_MSG_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AK.Base.pb.h"
#include "AkcsHttpRequest.h"
#include "AK.Resid.pb.h"
#include "DclientMsgDef.h"
#include "InnerMsgDef.h"
#include "DclientMsgSt.h"
#include "AK.BackendCommon.pb.h"
#include "NotifyMsgControl.h"
#include "MsgStruct.h"
#include "Resid2AppMsg.h"
#include "AkcsCommonSt.h"
#include "util.h"

class CPerMotionNotifyMsg : public CNotifyMsg
{
public:
    CPerMotionNotifyMsg(){}

    CPerMotionNotifyMsg(const SOCKET_MSG_MOTION_ALERT_SEND& other, const std::string& account)
    {
        memset(&motion_alert_, 0, sizeof(motion_alert_));
        motion_alert_.id = other.id;
        Snprintf(motion_alert_.protocal, sizeof(motion_alert_.protocal), other.protocal);
        Snprintf(motion_alert_.mac, sizeof(motion_alert_.mac), other.mac);
        Snprintf(motion_alert_.node, sizeof(motion_alert_.node), other.node);
        Snprintf(motion_alert_.location, sizeof(motion_alert_.location), other.location);
        Snprintf(motion_alert_.capture_time, sizeof(motion_alert_.capture_time), other.capture_time);
        Snprintf(motion_alert_.sip_account, sizeof(motion_alert_.sip_account), other.sip_account);
        real_account_ = account;
        motion_alert_.detection_type = other.detection_type;
        motion_alert_.detection_info = other.detection_info;
    }
    ~CPerMotionNotifyMsg() = default;
    int NotifyMsg();
private:
    SOCKET_MSG_MOTION_ALERT_SEND motion_alert_;
    std::string real_account_;     // 实际站点

private:
    bool IsNeedNotifyApp();
    bool CheckAppDNDState(const std::string& real_site);
    bool CheckMotionRecvState(const std::string& real_site);
    void BuildOfflineMotionAlertNotifyMsg(CResid2AppMsg& msg_sender);
};

#endif