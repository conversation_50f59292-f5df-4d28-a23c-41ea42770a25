#ifndef __NOTIFY_PER_TEXT_MSG_H__
#define __NOTIFY_PER_TEXT_MSG_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include "AK.Base.pb.h"
#include "AkcsHttpRequest.h"
#include "AK.Resid.pb.h"
#include "DclientMsgDef.h"
#include "InnerMsgDef.h"
#include "DclientMsgSt.h"
#include "AK.BackendCommon.pb.h"
#include "NotifyMsgControl.h"

class CNotifyMsg; //前置声明

class CPerTextNotifyMsg : public CNotifyMsg
{
public:
    enum ClientType
    {
        DEV_SEND = 1,  //设备发送
        APP_SEND = 2,  //app发送
    };
    enum MessageType
    {
        TEXT_MSG = 0,  
        DELIVERY_MSG = 1,  
        TMPKEY_MSG = 2,
        DELIVERY_BOX_MSG = 3,  //用于JTS
        YALE_BATTERY_2WEEK = 4,
        YALE_BATTERY_1WEEK = 5,
        YALE_BATTERY_LOW = 6,
        VOICE_MSG = 7,
        BOOKING_MSG = 8, 
    };
public:
    CPerTextNotifyMsg() = default;

    CPerTextNotifyMsg(const CPerTextNotifyMsg& other)
    {
        client_type_ = other.client_type_;
        account_ = other.account_;
        text_msg_info_ = other.text_msg_info_;
        base_ = other.base_;
    }

    CPerTextNotifyMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const SOCKET_MSG_SEND_TEXT_MESSAGE& text_msg, const std::string& account)
    {
        client_type_ = text_msg.client_type;
        account_ = account;
        text_msg_info_ = text_msg.text_message;
        base_ = base;
    }
    ~CPerTextNotifyMsg()
    {

    }
    int NotifyMsg();
    std::string GetMsgId(uint32_t msg_id, int role);

private:
    AK::BackendCommon::BackendP2PBaseMessage base_;

    int client_type_;//联动系统里面设备的类型
    std::string account_;
    SOCKET_MSG_TEXT_MESSAGE text_msg_info_;
    bool IsSmartLockEventNotifyType();
};


#endif //__NOTIFY_MSG_CONTROL_H__

