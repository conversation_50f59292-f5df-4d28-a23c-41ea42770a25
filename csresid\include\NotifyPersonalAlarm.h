#ifndef __NOTIFY_PERSONAL_ALARM_H__
#define __NOTIFY_PERSONAL_ALARM_H__

#include <thread>
#include <mutex>
#include <memory>
#include <list>
#include "DclientMsgDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalAlarm.h"
#include "DclientMsgSt.h"

class CPersonalAlarmProcessor
{
public:
    CPersonalAlarmProcessor() = default;
    ~CPersonalAlarmProcessor() = default;

    static int ProcessPersonalAlarmMsg(const SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev, const PERSONNAL_ALARM& personal_alarm);
    static int AddPersonalAlarmToDB(uint64_t trace_id, PERSONNAL_ALARM& personal_alarm, SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev);
};

#endif //__NOTIFY_PERSONAL_ALARM_H__ 