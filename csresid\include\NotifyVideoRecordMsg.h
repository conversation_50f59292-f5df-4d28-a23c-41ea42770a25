#ifndef __NOTIFY_VIDEO_RECORD_MSG_H__
#define __NOTIFY_VIDEO_RECORD_MSG_H__

#include "NotifyMsgControl.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "AkcsCommonSt.h"
#include "DevOnlineMng.h"

class CNotifyMsg; 
class CVideoRecordNotifyMsg : public CNotifyMsg
{
private:
    SOCKET_MSG_DEVICE_REPORT_VIDEO_RECORD video_record_msg_;
    ResidentDev conn_dev_;
    MacInfo mac_info_;
    
public:
    ~CVideoRecordNotifyMsg(){}
    CVideoRecordNotifyMsg() = default;
    CVideoRecordNotifyMsg(const SOCKET_MSG_DEVICE_REPORT_VIDEO_RECORD& video_record_msg, const ResidentDev& conn_dev, const MacInfo& mac_info) 
                : video_record_msg_(video_record_msg), conn_dev_(conn_dev), mac_info_(mac_info) {}

    int NotifyMsg();

private:

};
#endif //__NOTIFY_VIDEO_RECORD_MSG_H__

