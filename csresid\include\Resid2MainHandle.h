#ifndef __CSRESDI_2_MAIN_HANDLE_H__
#define __CSRESDI_2_MAIN_HANDLE_H__

#include <boost/noncopyable.hpp>
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"
#include "AkLogging.h"
#include "InnerMsgDef.h"
#include "MsgStruct.h"


//用于csresid与csmain的消息通信管理
class Resid2MainHandle
{
public:
    Resid2MainHandle(int mqueue_num);
    ~Resid2MainHandle(){}
    void Start();
    void Send(const std::string& client, const csmain::DeviceType type, const char *data, size_t size);
    void Send(MsgStruct& msg);
private:
    void Init();
    void InnerSend(int msg_id, const char *daa, size_t size);
    void InnerRecvThread(int msg_id );
private:
    //原则上,只要区分消息类型 mtype,就可以在一个队列里面实现全双工，但是考虑到后面用ipc命令管理队列的方便(例如清空消息等)，还是拆成两个队列进行管理
    static const size_t kMian2ResidMsgKey; //从main发送到resid的key
    static const size_t kResid2MainMsgKey; //从resid发送到main的key
    int main2resid_msg_id_;
    int resid2main_msg_id_;

    std::vector<size_t> produce_msg_id_;
    std::vector<size_t> consumer_msg_id_;
    int mqueue_num_;
};

#endif // __CSRESDI_2_MAIN_HANDLE_H__
