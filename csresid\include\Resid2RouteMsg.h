#ifndef __RESID_2_ROUTE_H__
#define __RESID_2_ROUTE_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"
#include "DclientMsgSt.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "AK.Resid.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/Message.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "AkcsCommonDef.h"

class CResid2RouteMsg
{
public:
    CResid2RouteMsg();
    ~CResid2RouteMsg();

    static void GroupVoiceMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void PushLinKerWeather(const SOCKET_MSG_DEV_WEATHER_INFO& weather_info);
    static void PushLinkerPacportReg(const SOCKET_MSG_PACPORT_REG_INFO& pacport_reg_info);
    static void PushLInkerPacportUnReg(const std::string& mac);
    static void PushLinkerPacportUnlock(const SOCKET_MSG_PACPORT_UNLOCK_CHECK& pacport_unlock_info);
    static void GroupDeliveryMsg(const PerTextMessageSendList& text_messages);
    static void GroupIndoorRelayStatusMsg(const std::string&node, const std::string& mac, uint64_t relay_status, int relay_type, int project_type);

    static void SendSL50UnlockNotify(const std::string& lock_uuid);
    static void PushLinkerWeather(const SOCKET_MSG_DEV_WEATHER_INFO& weather_info);
    static void SendUpdateConfigByAccount(int type, const std::string &node, const std::string &account, int account_role, int manager_id, int unit_id, int project_type);
    static void PushLinkerChangeKitDev(const std::string& original_mac, const std::string& replace_mac, const std::string& msg_seq);
    static void SendP2PIndoorRelayControlMsg(const std::string& mac, int relay_id, int relay_switch, int relay_type, int project_type);
    static void PushMsg2Route(const google::protobuf::MessageLite* msg);
    static void SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key);
    static void PushLinkerCreateRoom(const std::vector<SOCKET_MSG_DEV_KIT_DEVICE>& devices, const std::string& kit_mac, const std::string& msg_seq);
    static void SendP2PEmergencyDoorControlMsg(const dbinterface::PmEmergencyDoorLogInfoList& info_list, const std::string& msd_uuid, 
                                               const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type, int project_type);
    static void SendRoute2WebCommonMsg(int msg_type, const std::string &data_json);
    static void SendEmergencyNotifyWebMsg(const std::string& alarm_uuid, const std::string& project_uuid);
    static void SendMonitorCaptureMsg(const SOCKET_MSG_REQ_CAPTURE& request_capture);
    static void SendStopVideoRecordMsg(const SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO& stop_record_video);
    static void SendUpdateConfig(int changetype, const std::string& mac, int project_type, const std::string& ip);
    static void SendP2PMainSendTmpKeyUsedMsg(const std::string& creator, const std::string& name);
    static void SendSmartLockUpdateConfigurationNotify(const std::string& lock_uuid, NotifySmartLockType notify_lock_type);
    static void SendP2PAlarmNotifyMsg(project::PROJECT_TYPE project_type, TransP2PMsgType p2p_msg_type, csmain::DeviceType dev_type, const std::string& endpoint, const SOCKET_MSG_ALARM_SEND& alarm_msg);
    static AK::BackendCommon::BackendP2PBaseMessage CreateP2PBaseMsg(int msgid, int type, const std::string &uid, csmain::DeviceType conntype, int projecttype);
    static csmain::DeviceType DevProjectTypeToDevType(int projecttype);
};

#endif // __RESID_ROUTE_CLIENT_H__
