#ifndef __RESID_PUSH_CLIENT_H__
#define __RESID_PUSH_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkLogging.h"
#include "PushClient.h"
#include "util.h"
#include "AKUserMng.h"

class CPushClient;
class CResidPushClient;
typedef std::shared_ptr<CPushClient> PushClientPtr;
typedef std::map<std::string/*key*/, std::string/*value*/> AppOfflinePushKV;
typedef std::shared_ptr<CResidPushClient> CResidPushClientPtr;

class CResidPushClient: public CPushClient
{
public:
    CResidPushClient(evpp::EventLoop* loop,
                const std::string& serverAddr/*ip:port*/,
                const std::string& name);

    
    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
    {

    }
    static PushClientPtr CreateClient(const std::string &addr, evpp::EventLoop* loop);
    static void UpdatePushSrvList();
    
    static void buildPushMsg(int MobileTyp, const std::string& token, int msgType, const AppOfflinePushKV& kv, std::string oem, std::string& msg_json);
    void buildPushMsgCall(const CMobileToken &apptoken, int is_voip, std::string& caller_name,  const uint64_t traceid);

private:
};

#endif // __CSMAIN_PUSH_CLIENT_H__
