#ifndef __CSRESID_SERVER_H__
#define __CSRESID_SERVER_H__

#include <vector>
#include <map>
#include <set>
#include <mutex>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/any.h>
#include "Resid2MainHandle.h"
#include "Client.h"
#include "AkcsCommonSt.h"
#include "DevOnlineMng.h"
#include "AKUserMng.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

typedef std::map<std::string, CClientPtr> ClientList; //客户端的信息缓存列表,在resdi服务这里,客户端指的就是设备或者APP,代码中统一用client来指代客户端
typedef ClientList::iterator ClientListIter;

class ResidServer
{
public:
    ResidServer(int mqueue_num);
    void Start();
    int OnMainMsg(const MsgStruct* acc_msg);
    void SetDevSetting(const std::string& mac, const ResidentDev& dev_client);  
    void SetAppSetting(const std::string& uid, const ResidentPerAccount& app_client, CMobileToken& info);
    void RemoteAppInfo(const std::string& uid);
    int GetDevSetting(const std::string& mac, ResidentDev& dev_client);
    int GetMacInfo(const std::string& mac, MacInfo& info);
    int GetAppToken(const std::string& uid, CMobileToken& token);
    int GetAppSetting(const std::string& uid, ResidentPerAccount& appinfo);    
    void SendMsg2Main(const std::string& client, const csmain::DeviceType type, const unsigned char *data, size_t size);
    void SendMsg2Main(MsgStruct& msg);
    void SetAppOffline(const std::string& uid);
    void SetAppOnline(const std::string& uid);
    bool IsAppOnline(const std::string& uid);    
    int GetDevClientFromMac(const std::string& mac, CClientPtr& client);
private:
    std::unique_ptr<Resid2MainHandle> resid_2_main_ptr_;
    std::mutex dev_clients_mutex_;
    ClientList dev_clients_;
    std::mutex app_clients_mutex_;
    ClientList app_clients_;

    int mqueue_num_;
};

extern ResidServer* g_resid_srv_ptr;


#endif
