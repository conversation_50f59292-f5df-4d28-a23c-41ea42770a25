#ifndef __ROUTE_2_REISD_H__
#define __ROUTE_2_REISD_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "AkcsWebMsgSt.h"
#include "AK.Server.pb.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "AK.Resid.pb.h"
#include "BasicDefine.h"
#include "AK.BackendCommon.pb.h"

class CRoute2ResidMsg
{
public:
    CRoute2ResidMsg();
    ~CRoute2ResidMsg();
    static void HandleP2PVoiceMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PSendVoiceMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const AK::Server::P2PSendVoiceMsg &msg);
    static void HandleP2PWeatherInfoMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PRefreshUserConfMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PDeliveryMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const AK::Server::P2PSendDeliveryMsg &msg);
    static void HandleP2PPacportUnlockMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    /*
    static void HandleP2PRemoteOpendoorMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PRemoteOpenSecurityRelayMsg(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2PFromDeviceOpenDoorReq(const std::unique_ptr<CAkcsPdu> &pdu);
    static void HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO* open_door, const std::string &open_door_type);
    */

private:
    //向房间下主从账号及室内机发Message
    static int SendMessageToRoom(uint32_t unit_id, const std::string& room_num, const std::string& message_title, const std::string& message_content);
};

#endif // __ROUTE_2_REISD_H__
