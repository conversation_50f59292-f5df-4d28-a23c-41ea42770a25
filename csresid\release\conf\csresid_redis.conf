#configure for motion

csresidCacheInstances=appconf,weather,appdnd,video_record,auth,sl20_lock
# uid-app是否接受motion alert的redis
appconf_host=***********
appconf_port=8504
appconf_db=1
appconf_maxconncnt=2
dev_sid_conn_master=1

# weather: 天气状态
weather_host=127.0.0.1
weather_port=8504
weather_db=21
weather_maxconncnt=2

# appdnd: app免打扰
appdnd_host=127.0.0.1
appdnd_port=8504
appdnd_db=3
appdnd_maxconncnt=2

auth_host=127.0.0.1
auth_port=8504
auth_db=34
auth_maxconncnt=2

# videorecord: 视频存储
csvideorecordCacheInstances=video_record
video_record_host=***********
video_record_port=8504
video_record_db=32
video_record_maxconncnt=2

# sl20_lock: SL20锁
sl20_lock_host=127.0.0.1
sl20_lock_port=8504
sl20_lock_db=30
sl20_lock_maxconncnt=2

#如果sentinels有值,代表启动主从，那么_host的配置就不生效，如果没有就是单机
sentinels=