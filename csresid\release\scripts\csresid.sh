#!/bin/bash
ACMD="$1"
csresid_BIN='/usr/local/akcs/csresid/bin/csresid'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csresid()
{
    nohup $csresid_BIN >/dev/null 2>&1 &
    echo "Start csresid successful"
    if [ -z "`ps -fe|grep "csresidrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csresid/scripts/csresidrun.sh >/dev/null 2>&1 &
    fi
}
stop_csresid()
{
    echo "Begin to stop csresidrun.sh"
    csresidrunid=`ps aux | grep -w csresidrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csresidrunid}" ];then
	    echo "csresidrun.sh is running at ${csresidrunid}, will kill it first."
	    kill -9 ${csresidrunid}
    fi
    echo "Begin to stop csresid"
    kill -9 `pidof csresid`
    sleep 2
    echo "Stop csresid successful"
}

case $ACMD in
  start)
    start_csresid
    ;;
  stop)
    stop_csresid
    ;;
  restart)
    stop_csresid
    sleep 1
    start_csresid
    ;;
  status)
    cnt=`ps -ef |grep -E "csresid$" | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csresid is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csresid is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

