#!/bin/bash
PROCESS_NAME=csresid
PROCESS_START_CMD="/usr/local/akcs/csresid/scripts/csresidctl.sh start"
PROCESS_PID_FILE=/var/run/csresid.pid
LOG_FILE=/var/log/csresid_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csresid/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csresid/scripts/log_back.sh"
csresidlog_path="/var/log/csresidlog"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done
