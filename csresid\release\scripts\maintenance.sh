#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`

# Define functions
cmd_usage() {
  echo "usage: $0 metrics"
  exit 0
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi


if [[ "$1" == "metrics" ]]; then
  echo "curl $SERVER_INNER_IP:9993/metrics"
  curl $SERVER_INNER_IP:9993/metrics
else
  cmd_usage
fi
