//#include "stdafx.h"
#include <string.h>
#include "DeviceSetting.h"
#include "AKCSDao.h"
#include "ResidDb.h"




int DaoGetCommunityDevListByNode(const std::string& node, std::vector<COMMUNITY_DEVICE_SIP>& device)
{
    //获取设备
    ResidentDeviceList devlist;
    if (0 == dbinterface::ResidentDevices::GetNodeDevList(node, devlist))
    {
        for (const auto dev : devlist)
        {
            if (dev.dev_type != DEVICE_TYPE_INDOOR)
            {
                continue;
            }
            COMMUNITY_DEVICE_SIP tmp_device;
            memset(&tmp_device, 0, sizeof(tmp_device));
            tmp_device.type = dev.dev_type;
            snprintf(tmp_device.uuid, sizeof(tmp_device.uuid),  "%s", dev.uuid);
            device.push_back(tmp_device);
        }
    }

    //获取用户
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    ResidentPerAccountList account_list;
    COMMUNITY_DEVICE_SIP tmp_device;
    dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_COMMUNITY_ATTENDANT, account_list);
    for (const auto account : account_list)
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), "%s", account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }

    //再查主账号
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, main_account))
    {
        memset(&tmp_device, 0, sizeof(tmp_device));
        snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), "%s", main_account.uuid);
        tmp_device.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device);
    }
    else
    {
        //主账户不存在返回-1
        return -1;
    }
    
    return 0;
}


int GetPersonalAppAndIndoorDevListByNode(const std::string& node, std::vector<PERSONNAL_DEVICE_SIP>& device)
{
    PERSONNAL_DEVICE_SIP tmp_device;
    ResidentDeviceList devlist;
    if (0 == dbinterface::ResidentPerDevices::GetNodeIndoorDevList(node, devlist))
    {
        for (const auto& dev : devlist)
        {
            memset(&tmp_device, 0, sizeof(tmp_device));
            tmp_device.type = dev.dev_type;
            snprintf(tmp_device.uuid, sizeof(tmp_device.uuid), "%s", dev.uuid);
            device.push_back(tmp_device);
        }
    }

    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    ResidentPerAccountList account_list;
    PERSONNAL_DEVICE_SIP tmp_device2;
    dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_PERSONNAL_ATTENDANT, account_list);
    for (const auto account : account_list)
    {
        memset(&tmp_device2, 0, sizeof(tmp_device2));
        snprintf(tmp_device2.uuid, sizeof(tmp_device2.uuid), "%s", account.uuid);
        tmp_device2.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device2);
    }

    //再查主账号
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, main_account))
    {
        memset(&tmp_device2, 0, sizeof(tmp_device2));
        snprintf(tmp_device2.uuid, sizeof(tmp_device2.uuid), "%s",main_account.uuid);
        tmp_device2.type = DEVICE_TYPE_APP;
        device.push_back(tmp_device2);
    }
    else
    {
        //主账户不存在返回-1
        return -1;
    }

    return 0;
}




