#include <string>
#include <string.h>
#include "CsmainAES256.h"
#include "aes.h"
#include <arpa/inet.h>
#include "AkcsCommonSt.h"
#include "AkLogging.h"
#include "ResidServer.h"
#include "ResidDb.h"
#include "util.h"

extern ResidServer* g_resid_srv_ptr;

int AesEncryptByMac(const char* in, char* out, const std::string& mac, int* pdata_size, const uint32_t max_allowed_size)
{
    int dynamics = 0;
    unsigned char iv[17] = {0};
    unsigned char ivtmp[17] = {0};

    ResidentDev dev;
    if (g_resid_srv_ptr->GetDevSetting(mac, dev) == 0)
    {
        dynamics = dev.is_dy_iv;
        if(!dynamics)
        {
            memset(iv, 0, sizeof(iv));
        }
        else
        {
            std::string s = generateRandomString(16);
            snprintf((char*)iv, sizeof(iv), "%s", s.c_str());
            snprintf((char*)ivtmp, sizeof(ivtmp), "%s", s.c_str());
        }
    }

    if (in == NULL || out == NULL || pdata_size == NULL)
    {
        return -1;
    }
    *pdata_size = strlen(in);
    *pdata_size = ((*pdata_size - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    if (max_allowed_size < (uint32_t)(*pdata_size + 1 + DYNAMICS_VI_HEAD_LEN))
    {
        return -1;
    }
    
    char* pszOutBuf = new char[*pdata_size + 1 + DYNAMICS_VI_HEAD_LEN];
    memset(pszOutBuf, 0, *pdata_size + 1 + DYNAMICS_VI_HEAD_LEN);
    
    //生成key
    std::string strAesKey = mac;
    strAesKey += AES_KEY_DEFAULT_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    char* pszAesKey = NULL;
    Snprintf(szAesKeyTmp, sizeof(szAesKeyTmp),  strAesKey.c_str());
    pszAesKey = strupr(szAesKeyTmp);

    AES_256_ENCRYPT_With_IV((unsigned char*)in, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, *pdata_size, iv);
    if (dynamics)
    {
        memcpy(out, DYNAMICS_VI_FLAGS, 8);
        memcpy(out + 8, ivtmp, 16);
        memcpy(out + DYNAMICS_VI_HEAD_LEN, pszOutBuf, *pdata_size + 1);
        *pdata_size += DYNAMICS_VI_HEAD_LEN;
    }
    else 
    {
        memcpy(out, pszOutBuf, *pdata_size + 1);
    }
    
    delete []pszOutBuf;
    return 0;
}

