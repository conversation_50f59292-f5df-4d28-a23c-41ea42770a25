#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "util.h"
#include "AkLogging.h"
#include "ResidInit.h"
#include "MsgControl.h"
#include "ResidServer.h"
#include "DevOnlineMng.h"
#include "MsgToControl.h"
#include "DeviceSetting.h"
#include "AkcsOemDefine.h"
#include "AkcsCommonDef.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


extern ResidServer* g_resid_srv_ptr;


void CreateOnlineMacInfo(MacInfo &macinfo, const ResidentDev &dev_setting)
{
    macinfo.is_personal  = 0;
    macinfo.oem_id = dev_setting.oem_id;
    macinfo.type  = dev_setting.dev_type;
    macinfo.flags  = dev_setting.flags;
    macinfo.firmware_number  = dev_setting.firmware;
    macinfo.init_status = dev_setting.init;
    macinfo.project_type = dev_setting.project_type;
    macinfo.grade = dev_setting.grade;
    macinfo.is_personal  = 0;
    if (macinfo.project_type == project::RESIDENCE)
    {
        macinfo.conn_type = csmain::DeviceType::COMMUNITY_DEV;
    }
    else if (macinfo.project_type == project::PERSONAL)
    {
        macinfo.conn_type = csmain::DeviceType::PERSONNAL_DEV;
        macinfo.is_personal  = 1;
    }
    else
    {
        macinfo.conn_type = csmain::DeviceType::OFFICE_DEV;
    }
    
    macinfo.mng_id = dev_setting.project_mng_id;
    snprintf(macinfo.node, sizeof(macinfo.node), "%s", dev_setting.node);
    snprintf(macinfo.mac, sizeof(macinfo.mac), "%s", dev_setting.mac);
    snprintf(macinfo.uuid, sizeof(macinfo.uuid), "%s", dev_setting.uuid);
}

DevOnlineMng::~DevOnlineMng()
{
    LOG_INFO << "Release DevOnlineMng.";
}

DevOnlineMng* DevOnlineMng::GetInstance()
{
    static DevOnlineMng dev_online_mng;
    return &dev_online_mng;
}

void DevOnlineMng::AddPerMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(per_online_mutex_);
    per_eque_.push_back(msg);
}

void DevOnlineMng::AddCommunityMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(comm_online_mutex_);
    comm_eque_.push_back(msg);    
}

void DevOnlineMng::AddOfficeMac(const MacInfo &msg)
{
    std::lock_guard<std::mutex> lock_(office_online_mutex_);
    office_eque_.push_back(msg);    
}

void DevOnlineMng::InitMacInfo(const std::string &mac, ResidentDev &dev, MacInfo &macinfo)
{    
    CreateOnlineMacInfo(macinfo, dev);
 
    ProjectUserInfo project_info;
    ProjectInfo project;
    if (dev.project_type == project::PERSONAL)
    {
        project.GetPersonalProjectInfo(macinfo.node, project_info);    
        Snprintf(macinfo.log_delivery_uuid, sizeof(macinfo.log_delivery_uuid), project_info.node_uuid); 
    }
    else 
    {
        project.GetCommProjectInfo(macinfo.mng_id, macinfo.node, project_info);
        macinfo.is_support_scan_indoor_qrcode_to_reg_enduser = project_info.is_support_scan_indoor_qrcode_to_reg_enduser;
        Snprintf(macinfo.log_delivery_uuid, sizeof(macinfo.log_delivery_uuid), project_info.project_uuid);
    }

    macinfo.init_status = 1;
    macinfo.enable_smarthome = project_info.enable_smarthome;
    Snprintf(macinfo.sip, sizeof(macinfo.sip), dev.sip);
    Snprintf(macinfo.timezone, sizeof(macinfo.timezone), project_info.timezone);
    Snprintf(macinfo.ins_uuid, sizeof(macinfo.ins_uuid), project_info.ins_uuid);
    Snprintf(macinfo.node_uuid, sizeof(macinfo.node_uuid),  project_info.node_uuid);
    Snprintf(macinfo.project_uuid, sizeof(macinfo.project_uuid),  project_info.project_uuid);
}

void DevOnlineMng::SendDevOnlineNotifyMsg(const MacInfo& macinfo)
{
    SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
    memset(&online_msg, 0, sizeof(online_msg));
    snprintf(online_msg.mac, sizeof(online_msg.mac),"%s", macinfo.mac);
    online_msg.unread_voice_count = 0;
    if (macinfo.type == DEVICE_TYPE_INDOOR)
    {
        int count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(macinfo.uuid);
        if (count > 0)
        {
            online_msg.unread_voice_count = count;
        }
    }
    snprintf(online_msg.uuid, sizeof(online_msg.uuid),"%s", macinfo.uuid);

    GetMsgToControlInstance()->SendOnlineNotifyMsg(macinfo.mac, online_msg);
}

void DevOnlineMng::CheckPerOnlineMsg()
{
    MacInfo macinfo;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(per_online_mutex_);
        if (per_eque_.size() == 0)
        {
            return;
        }        
        per_eque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        macinfo = tmp_deque.front();
        tmp_deque.pop_front();

        dbinterface::VersionModel::CheckIndoorPlanFlags(macinfo.mac, macinfo.firmware_number, macinfo.is_personal);
        //家庭是否为Kit方案,如果是kit方案才下发
        if (dbinterface::SwitchHandle(macinfo.flags, DeviceSwitch::INDOOR_IS_KIT))
        {
            GetMsgToControlInstance()->SendIsKit(macinfo.mac);
            AK_LOG_INFO << "CheckPerOnlineMsg, Kit Device is online, mac = " << macinfo.mac << ", firmware = " << macinfo.firmware_number 
                        << ", is_personal = " << macinfo.is_personal << ", flags = " << macinfo.flags ;
        }        

        //设备在线后返回通知消息
        SendDevOnlineNotifyMsg(macinfo);
    }
}

void DevOnlineMng::CheckCommunityOnlineMsg()
{
    MacInfo macinfo;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(comm_online_mutex_);
        if (comm_eque_.size() == 0)
        {
            return;
        }        
        comm_eque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        macinfo = tmp_deque.front();
        tmp_deque.pop_front();

        dbinterface::VersionModel::CheckIndoorPlanFlags(macinfo.mac, macinfo.firmware_number, macinfo.is_personal);

        // nice室内机或开启社区支持室内机二维码注册的都下发为kit设备
        if ((macinfo.oem_id == OEMID_NICE || macinfo.is_support_scan_indoor_qrcode_to_reg_enduser) && macinfo.type == DEVICE_TYPE_INDOOR)
        {
            GetMsgToControlInstance()->SendIsKit(macinfo.mac);
            AK_LOG_INFO << "nice indoor online, send is kit to mac = " << macinfo.mac;
        }

        SendDevOnlineNotifyMsg(macinfo);
    }
}


void DevOnlineMng::CheckOfficeOnlineMsg()
{

    MacInfo macinfo;
    std::deque<MacInfo> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(office_online_mutex_);
        if (office_eque_.size() == 0)
        {
            return;
        }
        office_eque_.swap(tmp_deque);
    }
    while (tmp_deque.size() > 0)
    {
        macinfo = tmp_deque.front();
        tmp_deque.pop_front();

        //设备在线后返回通知消息
        SendDevOnlineNotifyMsg(macinfo);     
    }
}

int DevOnlineMng::Init()
{   
    pthread_create(&thread_process, NULL, &DevOnlineThread, (void*)this);
    LOG_INFO << "Create DevOnlineMng thread.";
    return 0;
}


void* DevOnlineMng::DevOnlineThread(void* this_mng)
{
    DevOnlineMng *mng = (DevOnlineMng*)this_mng;
    while (true)
    {
        mng->CheckOfficeOnlineMsg();
        mng->CheckCommunityOnlineMsg();
        mng->CheckPerOnlineMsg();
        sleep(2);
    }

    return NULL;
}


