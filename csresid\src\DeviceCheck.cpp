#include "util.h"
#include "DeviceCheck.h"
#include "ConnectionPool.h"
#include "MsgControl.h"
#include "ResidInit.h"

extern AKCS_CONF gstAKCSConf;

CDeviceCheck* GetDeviceCheckInstance()
{
    return CDeviceCheck::GetInstance();
}

CDeviceCheck::CDeviceCheck()
{

}

CDeviceCheck::~CDeviceCheck()
{

}

CDeviceCheck* CDeviceCheck::instance = NULL;

CDeviceCheck* CDeviceCheck::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceCheck();
    }

    return instance;
}

bool CDeviceCheck::CheckAuthcodeExist(const std::string &mac, std::string& authcode)
{
    bool ret = false;
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    CRldbQuery query(tmp_conn);
    std::stringstream sql;

    sql << "/*master*/select Authcode from MacPool where MAC = '";
    sql<< mac;
    sql << "'";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        ret = true;
        authcode = query.GetRowData(0);
    }

    ReleaseDBConn(dbconn);

    return ret;
}


//add bu xuzr,比对mac和devicesSpecial表中的mac，存在则将devices表中flag第8位置为1
bool CDeviceCheck::CheckIndoorPlanFlags(const std::string &mac, int firmware_number, int is_single)
{
    int flags = 0;

    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    CRldbQuery query(tmp_conn);

    std::stringstream sql1;
    std::stringstream sql2;
    std::stringstream sql3;  
     
    sql1 << "select ID from DevicesSpecial where MAC = '";
    sql1 << mac;
    sql1 << "'";

    query.Query(sql1.str());
    if (query.MoveToNextRow())
    {
        sql2 << "select Type from VersionModel where VersionNumber =";
        sql2 << firmware_number;
        query.Query(sql2.str());
        if (query.MoveToNextRow())
        {
            if (ATOI(query.GetRowData(0)) != 2)
            {
                ReleaseDBConn(dbconn);
            	return false;
            }
        } 
        else
        {
            ReleaseDBConn(dbconn);
            return false;
        }
        if (is_single)
        {
            //按位标识 0位=home;1=away;2=sleep;3=管理机是否开启全选,默认开启; 4-7设备relay;8位室内机上线标识
            sql3 << "update PersonalDevices set Flags = Flags | (1 << 8) ";
            sql3 << "where MAC = '";
            sql3 << mac;
            sql3 << "'";
        }
        else
        {
            //按位标识 0位=home;1=away;2=sleep;3=管理机是否开启全选,默认开启; 4-7设备relay;8位室内机上线标识
            sql3 << "update Devices set Flags = Flags | (1 << 8) ";
            sql3 << "where MAC = '";
            sql3 << mac;
            sql3 << "'";
        }

        if (dbconn->Execute(sql3.str()) < 0)
        {
            AK_LOG_WARN << "update devices flags failed, flags = " << flags;
            ReleaseDBConn(dbconn);
            return false;
        }	
    }

    ReleaseDBConn(dbconn);
    return true;
	
}

//add bu xuzr,检查高级功能PersonnalAccount中TmpKeyPermission字段。
bool CDeviceCheck::CheckTmpKeyPermission(const std::string& node)
{
    int checkflag = 0;
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    CRldbQuery query(tmp_conn);
    std::stringstream sql1;
    sql1 << "select TempKeyPermission from PersonalAccount  where Account = '";
    sql1 << node;
    sql1 << "' ";

    query.Query(sql1.str());
    if (query.MoveToNextRow())
    {
        checkflag = ATOI(query.GetRowData(0));		
    }

    ReleaseDBConn(dbconn);
    return (checkflag == 1) ? true : false;	
}

int CDeviceCheck::GetDeviceType(const std::string &firm_ware)
{
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    size_t pos = firm_ware.find(".");
    if (pos == string::npos)
    {
        return -1;
    }
    std::string version_number = firm_ware.substr(0, pos);

    CRldbQuery query(tmp_conn);

    std::stringstream sql2;
    sql2 << "select Type from VersionModel where VersionNumber = " << version_number;

    int type = -1;
    query.Query(sql2.str());
    if (query.MoveToNextRow())
    {
        type = ATOI(query.GetRowData(0));
    }
    ReleaseDBConn(dbconn);

    return type;
}

/**
 * 首先检查MAC是否在distributor的MAC LIBRARY底下
 * 其次检查这个设备是否已经添加
 * 返回true，表示可以绑定这个设备
 * 返回false,表示不可用绑定这个设备
 *
 * <AUTHOR> (2021/10/27)
 *
 * @param mac
 * @param node
 *
 * @return bool
 */
bool CDeviceCheck::CheckKitDevice(const std::string &mac, const std::string &node)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    std::stringstream sql;
    CRldbQuery query(rldb_conn);
    int distributor_id = 0;

    sql << "select b.Account Distributor, b.ID DistributorID from Account a "
        << " join Account b on a.ParentID = b.ID "
        << " join PersonalAccount t on a.ID = t.ParentID "
        << " where t.Account = '" << node << "' LIMIT 1";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        distributor_id = ATOI(query.GetRowData(1));
    }
    else
    {
        //没有找到区域管理员,不能绑定该设备
        AK_LOG_WARN << "Find Distributor error,sql=" << sql.str();
        ReleaseDBConn(conn);
        return false;
    }


    sql.str("");
    sql.clear();
    sql << "select 1 from DeviceForRegister r  "
        << " WHERE r.MAC = '" << mac << "' and r.MngID = " << distributor_id << " LIMIT 1";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
    }
    else
    {
        //没有添加到MAC LIBRARY,那么不能绑定该设备
        AK_LOG_WARN << "Device not add to mac library,sql=" << sql.str();
        ReleaseDBConn(conn);
        return false;
    }

    sql.str("");
    sql.clear();
    sql << "select 1 from PersonalDevices r  "
        << " WHERE r.MAC = '" << mac << "' LIMIT 1";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        //如果已经绑定设备,那么不能再绑定
        AK_LOG_WARN << "Device is alreay be binded,sql=" << sql.str();
        ReleaseDBConn(conn);
        return false;
    }

    ReleaseDBConn(conn);
    return true;
}

bool CDeviceCheck::IsErrorFirmwareForChangeRelay(const std::string &firmware)
{
    if(strstr(gstAKCSConf.reportrelay_block_list, firmware.c_str()))
    {
        return true;
    }

    return false;
}
