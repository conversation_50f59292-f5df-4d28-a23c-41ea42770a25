#include "util.h"
#include "DeviceSetting.h"
#include "ConnectionPool.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Account.h"
#include "AkLogging.h"


#define TABLE_NAME_DEVICES  "Devices"

CDeviceSetting* GetDeviceSettingInstance()
{
    return CDeviceSetting::GetInstance();
}

CDeviceSetting::CDeviceSetting()
{

}

CDeviceSetting::~CDeviceSetting()
{

}

CDeviceSetting* CDeviceSetting::instance = NULL;

CDeviceSetting* CDeviceSetting::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceSetting();
    }

    return instance;
}

//根据MAC获取设备设置信息
int CDeviceSetting::GetDeviceSettingByMac(const std::string &mac, ResidentDev &device_setting)
{
    if (dbinterface::ResidentDevices::GetMacDev(mac, device_setting) == 0)
    {
        device_setting.conn_type = csmain::COMMUNITY_DEV;  //社区设备
        return 0;
    }
    else if (dbinterface::ResidentPerDevices::GetMacDev(mac, device_setting) == 0)
    {
        device_setting.conn_type = csmain::PERSONNAL_DEV;  //个人终端设备
        return 0;
    }

    return -1;//查询到空值
}

