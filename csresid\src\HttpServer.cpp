#include <stdio.h>
#include <stdlib.h>
#include <sstream>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "AkLogging.h"

#include "json/json.h"
#include "HttpServer.h"
#include "util.h"
#include "NotifyMsgControl.h"
#include "MsgFilter.h"
#include "EtcdCliMng.h"
#include "redis/CachePool.h"
#include "RouteClientMng.h"
#include "ResidInit.h"
#include "ConnectionPool.h"
#include "RouteMqProduce.h"
#include "MetricService.h"

//全局变量
extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;

const static int HTTP_RET_RESULT_SUCCESS_CODE = 0;
const static int HTTP_RET_RESULT_FAILE_CODE = -1;

const static char HTTP_RET_MSG_FILTER_MSG_LIST_EMPTY[] = "param: msgid_list_hex not exist";
const static char HTTP_RET_MSG_PARAM_FAILE[] = "param error";
const static char HTTP_RET_MSG_SUCCESS[] = "success";


//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}


void BuildQueueLengthMetrics(std::string& response)
{
    int queue_lenth = CNotifyMsgControl::GetInstance()->GetNotifyMsgListSize();
    int motion_queue_lenth = CNotifyMsgControl::GetMotionNotifyInstance()->GetNotifyMsgListSize();
    std::stringstream data; 
    data << "# HELP csresid_notify_queue_length The length of csresid notify queue\n"
         << "# TYPE csresid_notify_queue_length gauge\n"
         << "csresid_notify_queue_length " << queue_lenth << "\n"
         << "csresid_motion_queue_length " << motion_queue_lenth << "\n";
    response = data.str();
}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

// curl -H "Content-Type:application/json" -X POST --data '{"msgid_list_hex":"102,19f"}' http://172.18.41.220:9993/addFilterMsgID
void HttpReqAddMsgFilterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    
    Json::Reader reader;
    Json::Value root;
    if(!reader.parse(ctx->body().ToString(), root))
    {
        cb(GetHttpResponseMsg(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    std::string filter_msg_id_list;

    if(!root.isMember("msgid_list_hex"))
    {
        AK_LOG_WARN << "param: msgid_list_hex not exist";
        cb(GetHttpResponseMsg(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_FILTER_MSG_LIST_EMPTY));
        return;
    }
    filter_msg_id_list = root["msgid_list_hex"].asString();
    if(filter_msg_id_list.size() == 0)
    {
        AK_LOG_WARN << "msg id list is empty";
        cb(GetHttpResponseMsg(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    FilterMsgManager::GetInstance()->AddFilterMsgID(filter_msg_id_list);
    cb(GetHttpResponseMsg(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}
// curl -H "Content-Type:application/json" -X POST --data '{"msgid_list_hex":"102,19f"}' http://172.18.41.220:9993/removeFilterMsgID
void HttpReqRemoveMsgFilterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    
    Json::Reader reader;
    Json::Value root;
    if(!reader.parse(ctx->body().ToString(), root))
    {
        cb(GetHttpResponseMsg(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }

    std::string filter_msg_id_list;

    if(!root.isMember("msgid_list_hex"))
    {
        AK_LOG_WARN << "param: msgid_list_hex not exist";
        cb(GetHttpResponseMsg(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_FILTER_MSG_LIST_EMPTY));
        return;
    }
    filter_msg_id_list = root["msgid_list_hex"].asString();
    if(filter_msg_id_list.size() == 0)
    {
        AK_LOG_WARN << "msg id list is empty";
        cb(GetHttpResponseMsg(HTTP_RET_RESULT_FAILE_CODE, HTTP_RET_MSG_PARAM_FAILE));
        return;
    }
    FilterMsgManager::GetInstance()->RemoveFilterMsgID(filter_msg_id_list);
    cb(GetHttpResponseMsg(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS));
    return;
}
// curl http://172.18.41.220:9993/queryFilterMsgID
void HttpReqQueryMsgFilterListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    std::string filter_msg_list = FilterMsgManager::GetInstance()->GetFilterMsgID();

    cb(GetHttpResponseMsg(HTTP_RET_RESULT_SUCCESS_CODE, HTTP_RET_MSG_SUCCESS, filter_msg_list));
    return;
}

void startHttpServer()
{
    const int port = 9993;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网
    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    server.RegisterHandler("/addFilterMsgID", HttpReqAddMsgFilterCallback); //添加过滤消息ID，十六进制形式，支持多个，通过逗号分隔.如"102,12f,19f"
    server.RegisterHandler("/removeFilterMsgID", HttpReqRemoveMsgFilterCallback); //移除过滤消息ID，十六进制形式，支持多个，通过逗号分隔.如"102,12f,19f"
    server.RegisterHandler("/queryFilterMsgID", HttpReqQueryMsgFilterListCallback); //查询过滤消息ID，返回十六进制形式消息ID
    server.Init(port);
    server.Start();
    return ;
}
