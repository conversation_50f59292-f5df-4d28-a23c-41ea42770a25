#include "Metric.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "ConnectionPool.h"
#include "RouteClientMng.h"
#include "RouteMqProduce.h"
#include "ConfigFileReader.h"
#include "NotifyMsgControl.h"

extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;
#define VERSION_CONF_FILE "/usr/local/akcs/csresid/conf/version.conf"


void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csresid_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "logdb_get_conn_failed_count",
        "LOGDB GetConnection failed count",
        "csresid_logdb_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csresid_linux_msgsnd_failed_count",
        "csresid linux msgsnd failed count",
        "csresid_linux_msgsnd_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csresid_linux_msgrcv_failed_count",
        "csresid linux msgrcv failed count",
        "csresid_linux_msgrcv_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csresid_notify_queue_length",
        "The length of csresid notify queue",
        "csresid_notify_queue_length",
        []() -> long { return (long)(CNotifyMsgControl::GetInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "csresid_motion_queue_length",
        "The length of csoffice motion queue",
        "csresid_motion_queue_length",
        []() -> long { return (long)(CNotifyMsgControl::GetMotionNotifyInstance()->GetNotifyMsgListSize()); }
    );
    metric_service->AddMetric(
        "nsq_check",
        "nsq producer status",
        "csresid_nsq_check_error",
        []() -> long { return (long)(g_nsq_producer->Status() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "redis_check",
        "redis server status",
        "csresid_redis_check_error",
        []() -> long { return (long)(CacheManager::getInstance()->CheckRedisNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "db_conn_check",
        "db conn status",
        "csresid_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csroute_check",
        "route server status",
        "csresid_csroute_check_error",
        []() -> long { return (long)(CRouteClientMng::Instance()->CheckRouteNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "etcd_check",
        "etcd server status",
        "csresid_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );
}


