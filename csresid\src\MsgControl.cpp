#include <stdlib.h>
#include <stdio.h>
#include <string>
#include <tuple>
#include "CachePool.h"
#include "MsgControl.h"
#include "MsgToControl.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "AkcsPduBase.h"
#include <ctime>
#include "AkcsCommonDef.h"
#include "DevOnlineMng.h"
#include "ResidServer.h"
#include <arpa/inet.h>
#include "CsmainAES256.h"
#include "Account.h"
#include "DeviceSetting.h"
#include "ProjectUserManage.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "ClientControl.h"
#include "RouteMqProduce.h"
#include "Md5.h"
#include "AKCSDao.h"
#include "AkcsHttpRequest.h"
#include "NotifyMsgControl.h"
#include "ResidInit.h"
#include "Resid2RouteMsg.h"
#include "AKUserMng.h"
#include "AppPushToken.h"
#include "NotifyHttpReq.h"
#include "ResidDb.h"
#include "AK.Linker.pb.h"
#include "AkcsHttpRequest.h"
#include "util.h"
#include "dbinterface/PendingRegUser.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/DeviceForRegister.h"
#include "dbinterface/PendingRegUser.h"
#include "dbinterface/PersonalAccountSingleInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "NotifyPerText.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "util_relay.h"
#include "DclientMsgSt.h"
#include "dbinterface/PushButtonNotify.h"
#include "dbinterface/NotifyList.h"
#include "dbinterface/ProjectUserManage.h"


extern ResidServer* g_resid_srv_ptr;
extern RouteMQProduce* g_nsq_producer;
extern AKCS_CONF gstAKCSConf;

CMsgControl* GetMsgControlInstance()
{
    return CMsgControl::GetInstance();
}

CMsgControl::CMsgControl()
{

}

CMsgControl::~CMsgControl()
{

}

CMsgControl* CMsgControl::instance = NULL;

CMsgControl* CMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgControl();
    }

    return instance;
}

int MsgParseByMac(const MsgStruct* acc_msg, ResidentDev &dev, ParseMacCallback cb, void *st)
{
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    std::string mac = acc_msg->client;
    
    memset(&dev, 0, sizeof(dev));
    if (g_resid_srv_ptr->GetDevSetting(mac, dev) < 0)
    {
        AK_LOG_WARN << "GetDeviceSetting failed. mac is " << mac;
        return -1;
    }

    uint32_t size = ntohs(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, dev.mac, size); 
    
    if (cb(payload, st) < 0)
    {
        AK_LOG_WARN << "MsgParseByMac failed.";
        return -1;
    }
    return 0;
}

int HagerKitMsgParse(const MsgStruct* acc_msg, ParseHagerCallback cb, void *st, std::string& msg_seq)
{
    std::string mac = acc_msg->client;
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    
    uint32_t size = ntohs(msg->data_size);
    char* payload = (char*)msg->data;    
    AesDecryptByMac(payload, payload, mac, size); 
    
    if (cb(payload, st, msg_seq) < 0)
    {
        AK_LOG_WARN << "HagerKitMsgParse failed.";
        return -1;
    }
    return 0;
}

static bool IsMsgLimit(const MsgStruct* acc_msg, InternalBussinessLimit::BussinessType limit_type)
{
    CClientPtr client;
    if (g_resid_srv_ptr->GetDevClientFromMac(acc_msg->client, client) == 0)
    {
        InternalBussinessLimitPtr limitptr;
        client->GetBussinessLimit(limit_type, limitptr);
        if(limitptr != nullptr)
        {
            //新请求入队列，并查看是否限流
            int limit_status = limitptr->AddBussiness(acc_msg);
            if(limit_status == InternalBussinessLimit::LimitStatus::LIMITED)
            {                
                AK_LOG_INFO << acc_msg->client << " enter current limiting, type: " << limit_type;
                return true;
            }
        }
    }

    return false;
}

int CMsgControl::OnDeviceReportStatusMsg(SOCKET_MSG_NORMAL* msg, const MsgStruct* acc_msg)
{
    SOCKET_MSG_REPORT_STATUS reportStatusMsg;
    memset(&reportStatusMsg, 0, sizeof(SOCKET_MSG_REPORT_STATUS));
    char* payload = (char*)msg->data;
    uint32_t msg_version = (msg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET;
    uint32_t data_size = ::ntohs(msg->data_size);    
    if (GetMsgParseHandleInstance()->ParseReportStatusMsg(payload, &reportStatusMsg, data_size, msg_version) < 0)
    {
        AK_LOG_WARN << "ParseReportStatusMsg failed.";
        return -1;
    }

    //2023,chenyc,拆分csmain之后,在resid这边只需要处理一部分新的业务,后续的上报状态后的新业务也需要在这里处理，不能再在csmain处理了
    //每次设备重新上报状态信息,都需要从数据库获取最新的设备状态信息
    std::string mac = acc_msg->client;
    ResidentDev dev_setting;
    memset(&dev_setting, 0, sizeof(dev_setting));
    if (GetDeviceSettingInstance()->GetDeviceSettingByMac(mac, dev_setting) != 0 )
    {
        AK_LOG_WARN << "ParseReportStatusMsg get Mac Fail. mac=" << mac;
        return -1;
    }
    g_resid_srv_ptr->SetDevSetting(mac, dev_setting);

    MacInfo mac_info;
    DevOnlineMng::GetInstance()->InitMacInfo(mac, dev_setting, mac_info);
    if (dev_setting.conn_type == csmain::DeviceType::COMMUNITY_DEV)
    {
        DevOnlineMng::GetInstance()->AddCommunityMac(mac_info);
    }
    else
    {
        DevOnlineMng::GetInstance()->AddPerMac(mac_info);
    }
    return 0;
}

//只做在社区，语音留言上报
int CMsgControl::OnDeviceReportVoiceMsg(const MsgStruct* acc_msg)
{
    PersonalVoiceMsgInfo per_voice_msg;
    memset(&per_voice_msg, 0, sizeof(per_voice_msg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseReportVoiceMsg, (void *)&per_voice_msg) < 0)
    {
        AK_LOG_WARN << "ParseReportVoiceMsg failed.";
        return -1;
    }

    MacInfo info;
    memset(&info, 0, sizeof(info));
    if (g_resid_srv_ptr->GetMacInfo(dev.mac, info) < 0)
    {
        AK_LOG_WARN << "GetMacInfo failed. mac is " << dev.mac;
        return -1;
    }
    snprintf(per_voice_msg.project_uuid, sizeof(per_voice_msg.project_uuid), "%s", info.project_uuid);


    std::string msg_uuid;
    std::string prefix = dbinterface::ProjectUserManage::GetServerTag() + '-';
    dbinterface::ProjectUserManage::GetUUID(prefix, msg_uuid);
    snprintf(per_voice_msg.uuid, sizeof(per_voice_msg.uuid), "%s", msg_uuid.c_str());
    snprintf(per_voice_msg.mac, sizeof(per_voice_msg.mac), "%s", dev.mac);
    snprintf(per_voice_msg.dev_uuid, sizeof(per_voice_msg.dev_uuid), "%s", dev.uuid);
    snprintf(per_voice_msg.location, sizeof(per_voice_msg.location), "%s", dev.location);
    if (0 != dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsg(per_voice_msg))
    {
        AK_LOG_WARN << "InsertPersonalVoiceMsg failed.";
        return -1;
    }
    
    //插入插入PersonalVoiceMsgList
    if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_FAMILY)
    {
        //查找家庭所有用户和室内机的uuid
        std::vector<COMMUNITY_DEVICE_SIP> apps;
        std::vector<PERSONNAL_DEVICE_SIP> per_apps;
        if (dev.project_type == project::PERSONAL)
        {
            GetPersonalAppAndIndoorDevListByNode(per_voice_msg.uid, per_apps);
            for (const auto& app : per_apps)
            {
                AddPersonalVoiceMsgNode(prefix, app.uuid, per_voice_msg, app.type);
            }
        }
        else
        {
            DaoGetCommunityDevListByNode(per_voice_msg.uid, apps);
            for (const auto& app : apps)
            {
                AddPersonalVoiceMsgNode(prefix, app.uuid, per_voice_msg, app.type);
            }
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_INDOOR)
    {
        //下发给室内机
        ResidentDev dev;
        ResidentDev per_dev;
        if (0 == dbinterface::ResidentDevices::GetSipDev(per_voice_msg.uid, dev))
        {
            AddPersonalVoiceMsgNode(prefix, dev.uuid, per_voice_msg, DEVICE_TYPE_INDOOR);
        }
        else if (0 == dbinterface::ResidentPerDevices::GetSipDev(per_voice_msg.uid, per_dev))
        {
            AddPersonalVoiceMsgNode(prefix, per_dev.uuid, per_voice_msg, DEVICE_TYPE_INDOOR);
        }
    }
    else if (per_voice_msg.msg_type == VoiceMsgSendType::SEND_TO_APP)
    {
        //下发给app
        ResidentPerAccount account_ptr;
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(per_voice_msg.uid, account_ptr))
        {
            AddPersonalVoiceMsgNode(prefix, account_ptr.uuid, per_voice_msg, DEVICE_TYPE_APP);
        }
    }
    return 0;
}

void CMsgControl::AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, const PersonalVoiceMsgInfo &per_voice_msg, int type)
{
    PersonalVoiceMsgNode node;
    memset(&node, 0, sizeof(node));
    std::string tmp_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, tmp_uuid);

    snprintf(node.receiver_uuid, sizeof(node.receiver_uuid), "%s", receiver_uuid.c_str());
    snprintf(node.uuid, sizeof(node.uuid), "%s", tmp_uuid.c_str());
    snprintf(node.msg_uuid, sizeof(node.msg_uuid), "%s", per_voice_msg.uuid);
    node.type = type;

    dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsgList(node);
}


/*
int CMsgControl::OnDeviceRequestVoiceMsgList(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_VOICE_MSG_LIST msg_list;
    memset(&msg_list, 0, sizeof(msg_list));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestVoiceMsgList, (void *)&msg_list) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgList failed.";
        return -1;
    }
    
    int page_size = msg_list.page_size;
    int page_index = msg_list.page_index;

    PersonalVoiceMsgSendList send_list;
    msg_list.msg_count = dbinterface::PersonalVoiceMsg::GetVoicePageListByIndoorUUID(dev.uuid, page_size, page_index, send_list);
    if (msg_list.msg_count > 0)
    {
        GetMsgToControlInstance()->SendVoiceMsgListMsg(dev.mac, send_list, msg_list);
    }
    return 0;
}
*/

//只做在社区，语音留言上报
int CMsgControl::OnDeviceRequestVoiceMsgUrl(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestVoiceMsgUrl, (void *)&url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgUrl failed.";
        return -1;
    }

    PersonalVoiceMsgInfo per_voice_msg;
    std::string file_url;
    //TODO msg uuid直接获取语音文件会存在越权问题
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByUUID(url_msg.uuid, per_voice_msg))
    {
        file_url = per_voice_msg.file_url;
        std::size_t pos2 =  file_url.find("/group");
        if (pos2 == std::string::npos)
        {
            //存oss的流程

            model::HttpRespuestKV parma_kv;
            parma_kv.insert(map<std::string, std::string>::value_type("Node", "SuperManage"));
            parma_kv.insert(map<std::string, std::string>::value_type("Path", file_url));
            
            char url[1024];
            snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/common/capture/getLink", gstAKCSConf.web_backend_domain);

            AkcsKv kv;
            kv.insert(map<std::string, std::string>::value_type("mac", dev.mac));
            kv.insert(map<std::string, std::string>::value_type("mac_uuid", dev.uuid));
            kv.insert(map<std::string, std::string>::value_type("voice_uuid", url_msg.uuid));
            CHttpReqNotifyMsg notify_msg(url, parma_kv, kv, CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL);
            GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);            
            return 0;
        }
        
        //以下是存fdfs的流程
        size_t pos = file_url.find("/M");
        if (std::string::npos != pos)
        {
            //获取到 /M00/05/CB/rBIp3GMpNwqADMrHAAEqVhPHnOw417.wav
            std::string file_remote = file_url.substr(pos + 1);
            time_t timer = time(nullptr);
            char time_sec[16] = {0};
            snprintf(time_sec, 16, "%ld", timer);
            file_remote += "ak_fdfs";
            file_remote += time_sec;
            std::string token = akuvox_encrypt::MD5(file_remote).toStr();
            if (!dev.is_ipv6)
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv4, file_url.c_str(), token.c_str(), time_sec);
            }
            else
            {
                snprintf(url_msg.url, sizeof(url_msg.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv6, file_url.c_str(), token.c_str(), time_sec);
            }
        }
        GetMsgToControlInstance()->SendVoiceMsgUrl(dev.mac, dev.uuid, url_msg.uuid, url_msg.url);
    }
    return 0;
}

int CMsgControl::OnDeviceRequestDelVoiceMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestDelVoiceMsg, (void *)&url_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestVoiceMsgUrl failed.";
        return -1;
    }

    if (0 != dbinterface::PersonalVoiceMsg::DelVoiceMsgInfoByIndoorUUID(url_msg.uuid, dev.uuid))
    {
        AK_LOG_WARN << "DelVoiceMsgInfoByUUID failed.";
        return -1;
    }

    return 0;
}

//个人终端用户App-android上报身份识别给接入服务器
int CMsgControl::OnAndroidReportStatusMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL msg;
    memcpy(&msg, socket_msg, sizeof(SOCKET_MSG_NORMAL));
    
    SOCKET_MSG_PERSONNAL_APP_CONF app_config; //
    memset(&app_config, 0, sizeof(app_config));
    if (CMsgParseHandle::ProcessAppReportStatusMsg(socket_msg, app_config) < 0)
    {
        AK_LOG_WARN << "ProcessAppReportStatusMsg failed.";
        return -1;
    } 

    if (strlen(app_config.user) == 0)
    {
        AK_LOG_WARN << "parameter error report user is null!";
        return -1;
    }


    std::string main_user_account = app_config.user;
    std::string  report_user_account = app_config.user;
    if (strchr(app_config.user, '@') == nullptr)
    {
        report_user_account = app_config.user;
        dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(app_config.user, main_user_account);
    }
    else
    {
        PerAccountUserInfo user_info;
        //旧版本没有多套房，故两个account相同
        if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(app_config.user, user_info))
        {
            main_user_account = user_info.main_user_account;
            report_user_account = user_info.main_user_account;
        }
    }
    
    ResidentPerAccount account;
    if (0 != dbinterface::ResidentPersonalAccount::GetUserAccount(main_user_account, account))
    {
        AK_LOG_WARN << "Can not found main_user_account:" << main_user_account;
        return -1;
    }
    

    CMobileToken token;
    GetAppPushTokenInstance()->getAppPushTokenByUid(account.account, token);
    
    g_resid_srv_ptr->SetAppSetting(account.account, account, token);
    AK_LOG_INFO << "App reportstatus uid:" << account.account << " username:" << account.name << " aes dy iv: " << token.IsDyIv() ;
    return 0;
}


int CMsgControl::OnIOSReportStatusMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg)
{
    OnAndroidReportStatusMsg(socket_msg, acc_msg);
    return 0;
}

int CMsgControl::OnConnChange(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    InnerConnInfo* conninfo = (InnerConnInfo*)msg->data;
    if (conninfo->type == CONN_INFO_TYPE::DISCONNECT)
    {
        if (conninfo->conn_type == csmain::COMMUNITY_APP || conninfo->conn_type == csmain::PERSONNAL_APP)
        {
            g_resid_srv_ptr->SetAppOffline(conninfo->uid);
            AK_LOG_INFO << "App is offline  uid:" << conninfo->uid << " username:" << conninfo->username;
        }
    }
    else if (conninfo->type == CONN_INFO_TYPE::HEARTBEAT)
    {
        if (conninfo->conn_type == csmain::COMMUNITY_APP || conninfo->conn_type == csmain::PERSONNAL_APP)
        {
            g_resid_srv_ptr->SetAppOnline(conninfo->uid);
            AK_LOG_INFO << "App hearbeat  uid:" << conninfo->uid << " username:" << conninfo->username;
        }
        else
        {
            CClientPtr dev;
            if (g_resid_srv_ptr->GetDevClientFromMac(conninfo->uid, dev) != 0)
            {
                return 0;
            }

            //上报relay业务限流的延时处理
            //放到心跳处理，这样不用在用单独的线程处理，也可以减少锁的使用
            InternalBussinessLimitPtr limitptr;
            dev->GetBussinessLimit(InternalBussinessLimit::BussinessType::REPORT_RELAY, limitptr);
            if(limitptr != nullptr)
            {
                int limit_status = limitptr->CheckBussinessLimit();
                if(limit_status == InternalBussinessLimit::LimitStatus::LIMITED)
                {
                    AK_LOG_INFO << "Heartbeat handel bussiness limited";
                    MsgStruct msg_buf;
                    memset(&msg_buf, 0, sizeof(MsgStruct));
                    limitptr->GetMsgBuffer(msg_buf);
                    GetMsgControlInstance()->OnDeviceReportRelayStatusMsg(&msg_buf, 1);
                }
            }
        }
    }
    else if (conninfo->type == CONN_INFO_TYPE::LOGOUT)
    {
        if (conninfo->conn_type == csmain::COMMUNITY_APP || conninfo->conn_type == csmain::PERSONNAL_APP)
        {
            g_resid_srv_ptr->RemoteAppInfo(conninfo->uid);
            AK_LOG_INFO << "App is logout  uid:" << conninfo->uid << " username:" << conninfo->username;
        }
    }    
    
    
    return 0;
}

int CMsgControl::OnAppForceLogout(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    InnerConnInfoApp* appinfo = (InnerConnInfoApp*)msg->data;
    std::string node = appinfo->node;
    std::string user_account = appinfo->account;
    std::string main_site = appinfo->main_site;
    std::string temp_nfc;
    std::string temp_ble;
    ResidentPerAccount account_info;
    //app强制登出刷新nfc和ble
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(user_account,account_info))
    {
        temp_nfc = account_info.nfc_code;
        temp_ble = account_info.ble_code;
        bool is_need_update = false;
        if (0 != temp_nfc.length())
        {
            std::string nfccode = GenerateNFCCode();
            AK_LOG_INFO << "updateNFCCode, Code: " << nfccode << "Node :" << node << "User Account :" << user_account;
            if(0 == dbinterface::ProjectUserManage::UpdateNfcCodeByAccount(user_account,nfccode))
            {
                is_need_update = true;
            }
        }
        
        if (0 != temp_ble.length())
        {
            std::string blecode = GenerateBLECode();
            AK_LOG_INFO << "updateBLECode, Code: " << blecode << "Node :" << node << "User Account :" << user_account;
            if(0 == dbinterface::ProjectUserManage::UpdateBleCodeByAccount(user_account,blecode))
            {
                is_need_update = true;
            }
        }

        if (is_need_update)
        {
            CResid2RouteMsg::SendUpdateConfigByAccount(CSMAIN_UPDATE_CONFIG_RF_CHANGE,node,appinfo->account,appinfo->real_site_role,appinfo->manager_id,appinfo->unit_id,project::RESIDENCE);
        }
    }

    return 0;
}
//6.7新社区、单租户接收设备relay状态上报并展示
int CMsgControl::OnDeviceReportRelayStatusMsg(const MsgStruct* acc_msg, int already_check)
{
    //限流判断
    if(already_check == 0)
    {
        if(IsMsgLimit(acc_msg, InternalBussinessLimit::BussinessType::REPORT_RELAY))
        {
            return 0;
        }
    }
    
    int ret = 0;
    SOCKET_MSG_RELAY_STATUS relay_status_msg;
    memset(&relay_status_msg, 0, sizeof(SOCKET_MSG_RELAY_STATUS));
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseReportRelayStatusMsg, (void *)&relay_status_msg) < 0)
    {
        AK_LOG_WARN << "ParseRelayStatusMsg failed.";
        return -1;
    }
    MacInfo info;
    memset(&info, 0, sizeof(info));
    if (g_resid_srv_ptr->GetMacInfo(dev.mac, info) < 0)
    {
        AK_LOG_WARN << "GetMacInfo failed. mac is " << dev.mac;
        return -1;
    }
    //根据解析的TraceID向设备回ACK
    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac),  dev.mac);
    Snprintf(common_ack.msg_type, sizeof(common_ack.msg_type),  relay_status_msg.msg_type);
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id),  relay_status_msg.trace_id);
    common_ack.result = 1;

    //回ack
    if (GetMsgToControlInstance()->SendCommonAckMsg(MSG_FROM_DEVICE_REPORT_INPUT_STATUS, common_ack) != 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed";
        return -1;
    }
    //对door relay status字段为空的情况进行兼容
    if(strlen(relay_status_msg.door_relay_status) == 0)
    {
        snprintf(relay_status_msg.door_relay_status, sizeof(relay_status_msg.door_relay_status), "%s", "2222");
    }
    //对door security relay status字段为空的情况进行兼容
    if(strlen(relay_status_msg.door_se_relay_status) == 0)
    {
        snprintf(relay_status_msg.door_se_relay_status, sizeof(relay_status_msg.door_se_relay_status), "%s", "22");
    }
    //将relay status更新到数据库 
    if(acc_msg->client_type == csmain::DeviceType::COMMUNITY_DEV)
    {
        ret = dbinterface::ResidentDevices::UpdateDoorRelayStatus(dev.mac,relay_status_msg.door_relay_status, relay_status_msg.door_se_relay_status);
    } else if(acc_msg->client_type == csmain::DeviceType::PERSONNAL_DEV)
    {
        ret = dbinterface::ResidentPerDevices::UpdateDoorRelayStatus(dev.mac,relay_status_msg.door_relay_status, relay_status_msg.door_se_relay_status);
    }
    if(ret == -1)
    {
        AK_LOG_WARN << "Update Relay Status failed. mac is " << dev.mac; 
    }
    
    return 0;
    
}


int CMsgControl::GetDevRegionInfo(const ResidentDev& dev, SOCKET_MSG_DEV_WEATHER_INFO& weather_info)
{
    memset(&weather_info, 0, sizeof(weather_info));
    ::snprintf(weather_info.mac, sizeof(weather_info.mac), "%s", dev.mac);
    
    if (dev.project_type == project::RESIDENCE)
    {
        CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(dev.project_mng_id);
        if (comm_info == nullptr) 
        {
            return -1;
        }
        ::snprintf(weather_info.city, sizeof(weather_info.city), "%s", comm_info->City().c_str());
        ::snprintf(weather_info.states, sizeof(weather_info.states), "%s", comm_info->States().c_str());
        ::snprintf(weather_info.country, sizeof(weather_info.country), "%s", comm_info->Country().c_str());
    }
    else if (dev.project_type ==  project::PERSONAL)
    {
        MacInfo mac_info;
        memset(&mac_info, 0, sizeof(mac_info));
        if (0 != g_resid_srv_ptr->GetMacInfo(dev.mac, mac_info))
        {
            return -1;
        }
        
        dbinterface::SingleInfo single_info;
        memset(&single_info, 0, sizeof(single_info));
        if (0 != dbinterface::PersonalAccountSingleInfo::GetSingleInfoByUUID(mac_info.node_uuid, single_info)) 
        {
            return -1;
        }
        ::snprintf(weather_info.city, sizeof(weather_info.city), "%s", single_info.city);
        ::snprintf(weather_info.states, sizeof(weather_info.states), "%s", single_info.states);
        ::snprintf(weather_info.country, sizeof(weather_info.country), "%s", single_info.country);
    }
    return 0;
}

int CMsgControl::OnDeviceRequestWeatherInfoMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_REQ_WEATHER_WAY req_weather_way;
    memset(&req_weather_way, 0, sizeof(req_weather_way));
    
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestWeatherMsg, (void *)&req_weather_way) < 0)
    {
        AK_LOG_WARN << "OnDeviceRequestWeatherInfoMsg failed.";
        return -1;
    }
    
    AK_LOG_INFO << dev.mac << " request weather, manual_update : " << req_weather_way.manual_update;
    
    // 获取设备的国家-城市-地区
    SOCKET_MSG_DEV_WEATHER_INFO weather_info;
    if (0 != GetDevRegionInfo(dev, weather_info))
    {
        AK_LOG_WARN << "dev :" << dev.mac << ", GetDevRegionInfo error";
        return -1;
    }
    
    if (req_weather_way.manual_update)
    {
        // 手动刷新 : 直接请求家居的天气接口获取
        CResid2RouteMsg::PushLinkerWeather(weather_info);
    }
    else
    {
        // 定时刷新 :     缓存存在直接返回
        CacheManager* cache_manager = CacheManager::getInstance();
        CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_weather); //获取与redis实例的tcp连接
        if (cache_conn)
        {
            //  redis key 格式 : 国家-城市-地区
            char weather_redis_key[256];
            ::snprintf(weather_redis_key, sizeof(weather_redis_key), "%s-%s-%s", weather_info.country, weather_info.states, weather_info.city);
            
            if (cache_conn->isExists(weather_redis_key))
            {
                std::vector<std::string> weather_vec;
                std::string weather_value = cache_conn->get(weather_redis_key);
                SplitString(weather_value, "!", weather_vec);

                // weather!temperature!humidity,判断weather_vec长度为3,防止取下标出现段错误
                if (weather_vec.size() == 3)
                {
                    ::snprintf(weather_info.weather, sizeof(weather_info.weather), "%s", weather_vec[0].c_str());
                    ::snprintf(weather_info.temperature, sizeof(weather_info.temperature), "%s", weather_vec[1].c_str());
                    ::snprintf(weather_info.humidity, sizeof(weather_info.humidity), "%s", weather_vec[2].c_str());

                    // 发送天气信息给设备
                    GetMsgToControlInstance()->SendDevWeatherInfoMsg(MSG_TO_DEVICE_REPORT_WEATHER_MSG, weather_info);

                    // redis key存在,释放redis连接后return
                    cache_manager->RelCacheConn(cache_conn);
                    return 0;
                }
            }
            // key不存在,释放redis连接,请求家居的天气接口
            cache_manager->RelCacheConn(cache_conn);
        }
        
        // 定时刷新 :     未查到缓存, 请求家居的天气接口
        CResid2RouteMsg::PushLinkerWeather(weather_info);
    }
    
    return 0;
}

// kit设备上报设备列表,创建房间
int CMsgControl::OnDeviceRequestCreateRoom(const MsgStruct* acc_msg)
{
    std::string mac = acc_msg->client;
    AK_LOG_INFO << "OnDeviceRequestCreateRoom, kit device mac: " << mac;

    // 解析上报设备列表
    std::string msg_seq; 
    std::vector<SOCKET_MSG_DEV_KIT_DEVICE> report_devices;
    if (0 != HagerKitMsgParse(acc_msg, CMsgParseHandle::ParseRequestCreateRoomMsg, (void *)&report_devices, msg_seq))
    {
        AK_LOG_WARN << "OnDeviceRequestCreateRoom ParseRequestCreateRoomMsg failed.";
        return -1;
    }
    
    // 是否已导入到mac库中
    DeviceRegisterInfo register_info;
    if (0 != dbinterface::DeviceForRegister::GetDeviceRegisterInfo(mac, register_info))
    {
        AK_LOG_WARN << "OnDeviceRequestCreateRoom, mac not add to macLibrary, mac: " << mac;
        return -1;
    }
    
    ResidentDev dev_info;
    if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev_info))
    {
        // 已在设备表中 : 查找同个家庭下的kit信息,进行kit设备更换
        RegEndUserInfo pending_user_info;
        if (0 != dbinterface::PendingRegUser::GetPendingRegUserInfoByNode(dev_info.node, pending_user_info))
        {
            AK_LOG_WARN << "dev node is not kit user, cannot resquest this interface. mac: " << mac;
            return -1;
        }

        // 当前mac和kit mac不相等,进行kit设备更换
        if (strncmp(dev_info.mac, pending_user_info.mac, strlen(dev_info.mac)) != 0)
        {        
            // 更换转流设备,等更换转流设备成功之后再回复ack
            CResid2RouteMsg::PushLinkerChangeKitDev(pending_user_info.mac, mac, msg_seq);
            AK_LOG_INFO << "OnDeviceRequestCreateRoom, change kit dev from : " << pending_user_info.mac << " to " << mac;
        }
        else
        {
            AK_LOG_WARN << "mac already is kit dev, cannot change kit dev, mac: " << mac;
        }
    }
    else
    {
        // 未插入到设备表中, 进行创建房间
        CResid2RouteMsg::PushLinkerCreateRoom(report_devices, mac, msg_seq);
    }

    return 0;
}

int CMsgControl::OnSendDevListChangeMsg(const std::string& uid)
{
    GetMsgToControlInstance()->SendDevListChangeMsg(MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE, uid);
    return 0;
}

int CMsgControl::OnDeviceReportDoorcomDeliveryMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG delivery_info;
    memset(&delivery_info, 0, sizeof(delivery_info));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if(MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParseRequestDeliveryMsg, (void*)&delivery_info) < 0)
    {
        AK_LOG_WARN << "OnDeviceReportDoorcomDeliveryMsg failed.";
        return -1;
    }

    AK_LOG_INFO << dev.mac << " report doorcom delivery message, status:" << delivery_info.status;

    uint32_t room_id = 0;
    if(0 != dbinterface::CommunityRoom::GetRoomIDByUnitIDAndRoomNum(dev.unit_id, delivery_info.apt_num, room_id))
    {
        AK_LOG_WARN << "get room id failed, unit id: " << dev.unit_id << ", room number: " << delivery_info.apt_num;
        return -1;
    }

    std::string node;
    if(0 != dbinterface::CommPersonalAccount::GetNodeByRoomID(room_id, node))
    {
        AK_LOG_WARN << "get node failed, room id: " << room_id;
        return -1;
    }
    ResidentPerAccount master_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(node, master_account))
    {
        AK_LOG_WARN << "get main account failed, node: " << node;
        return -1;
    }

    //插入从账号
    ResidentPerAccountList sub_account_list;
    if(0 != dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_COMMUNITY_ATTENDANT, sub_account_list))
    {
        AK_LOG_WARN << "get sub account list failed, node: " << node;
        return -1;
    }
    ResidentDeviceList dev_list;
    //房间室内机列表获取
    if(0 != dbinterface::ResidentDevices::GetNodeIndoorDevList(node, dev_list))
    {
        //不返回 因为有可能是没有设备 仍要发app
        AK_LOG_WARN << "get node indoor dev list empty or failed, node: " << node;
    }
    //通用消息构造
    std::string title;
    std::string content;
    //当前只有日本市场上该功能，Message先写死日语
    if(delivery_info.status == 0) //存入快递
    {
        title = "宅配のお荷物があります";
        content = "宅配ボックス  " + std::string(delivery_info.box_num) + " に新しい荷物が届きました, お早めに受け取ってください.";
    }
    else if(delivery_info.status == 1) //取出快递
    {
        title = "宅配のお荷物が受領されました";
        content = "宅配ボックス " + std::string(delivery_info.box_num) + " の中のお荷物が受領されました.";
    }

    CommPerTextMessage comm_text_msg;//消息通用部分
    memset(&comm_text_msg, 0, sizeof(comm_text_msg));
    Snprintf(comm_text_msg.title, sizeof(comm_text_msg.title), title.c_str());
    Snprintf(comm_text_msg.content, sizeof(comm_text_msg.content), content.c_str());
    comm_text_msg.project_type = project::RESIDENCE;
    //发送消息列表
    PerTextMessageSendList text_messages;

    //插入相关Message表
    if (0 != dbinterface::Message::AddGroupTextMsg(CPerTextNotifyMsg::MessageType::TEXT_MSG, comm_text_msg, dev_list, master_account, sub_account_list ,text_messages))
    {
        AK_LOG_WARN << "add doorcom delivery dev text msg failed. ";
        return -1;
    }
    //消息推送给家庭下室内机/App
    CResid2RouteMsg::GroupDeliveryMsg(text_messages);

    return 0;
}

int CMsgControl::GetPacportDevRegInfo(const ResidentDev& dev, SOCKET_MSG_PACPORT_REG_INFO& pacport_reg_info)
{
    memset(&pacport_reg_info, 0, sizeof(pacport_reg_info));
    Snprintf(pacport_reg_info.mac, sizeof(pacport_reg_info.mac), dev.mac);

    if (dev.project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << "device project type wrong. type:" << dev.project_type;
        return -1;
    }
    //根据社区获取相关地址信息
    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(dev.project_mng_id);
    if (comm_info == nullptr)
    {
        AK_LOG_WARN << "cannot find device community, mngid:" << dev.project_mng_id;
        return -1;
    }

    Snprintf(pacport_reg_info.prefecture_name, sizeof(pacport_reg_info.prefecture_name), comm_info->States().c_str());
    Snprintf(pacport_reg_info.city_name, sizeof(pacport_reg_info.city_name), comm_info->City().c_str());
    Snprintf(pacport_reg_info.postal_code, sizeof(pacport_reg_info.postal_code), comm_info->PostalCode().c_str());
    std::string district_name;
    std::string street_num; //街道号
    //TODO:确认下pacport那边确保用户按照空格分隔输入banchi
    SplitStringFromLastFilter(comm_info->Street(), " ", district_name, street_num);
    Snprintf(pacport_reg_info.district_name, sizeof(pacport_reg_info.district_name), district_name.c_str());
    Snprintf(pacport_reg_info.street_num, sizeof(pacport_reg_info.street_num), street_num.c_str());   
    return 0;
}

int CMsgControl::OnDeviceRequestPacportRegistMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_REQ_PACPORT_REG req_pacport_reg;
    memset(&req_pacport_reg, 0, sizeof(req_pacport_reg));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParsePacportRegMsg, (void *)&req_pacport_reg) < 0)
    {
        AK_LOG_WARN << "OnDeviceRequestPacportRegistMsg failed.";
        return -1;
    }
    //非梯口机直接拦截
    if(dev.grade != csmain::CommunityDeviceGrade::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        AK_LOG_WARN << "only support public unit device, device grade:" << dev.grade;
        return -1;
    }

    AK_LOG_INFO <<  dev.mac << " request pacport register, status: " << req_pacport_reg.status;

    if (req_pacport_reg.status == 0)
    {
        //注销只需设备mac即可
        CResid2RouteMsg::PushLInkerPacportUnReg(dev.mac);
    }
    //status = 1 注册逻辑
    else
    {
        //获取设备相关地址信息
        SOCKET_MSG_PACPORT_REG_INFO reg_info;
        memset(&reg_info, 0, sizeof(reg_info));
        if (0 != GetPacportDevRegInfo(dev, reg_info))
        {
            AK_LOG_WARN << "dev :" << dev.mac << ", GetDevRegionInfo error";
            return -1;
        }

        //推送到linker进行注册/注销动作
        CResid2RouteMsg::PushLinkerPacportReg(reg_info);
    }
    return 0;
}

int CMsgControl::OnDeviceRequesetPacportUnlockMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_REQ_PACPORT_UNLOCK req_pacport_unlock;
    memset(&req_pacport_unlock, 0, sizeof(req_pacport_unlock));

    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (MsgParseByMac(acc_msg, dev, CMsgParseHandle::ParsePacportUnlockMsg, (void *)&req_pacport_unlock) < 0)
    {
        AK_LOG_WARN << "OnDeviceRequesetPacportUnlockMsg failed.";
        return -1;
    }

    //非梯口机直接拦截
    if(dev.grade != csmain::CommunityDeviceGrade::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        AK_LOG_WARN << "only support public unit device, device grade:" << dev.grade;
        return -1;
    }

    AK_LOG_INFO <<  dev.mac << " request pacport unlock, traceid: " << req_pacport_unlock.trace_id;

    //快递校验所需信息
    SOCKET_MSG_PACPORT_UNLOCK_CHECK unlock_check_info;
    memset(&unlock_check_info, 0, sizeof(unlock_check_info));
    Snprintf(unlock_check_info.mac, sizeof(unlock_check_info.mac), dev.mac);
    Snprintf(unlock_check_info.room_num, sizeof(unlock_check_info.room_num), req_pacport_unlock.room_num);
    Snprintf(unlock_check_info.courier_name, sizeof(unlock_check_info.courier_name), req_pacport_unlock.courier_name);
    Snprintf(unlock_check_info.trace_id, sizeof(unlock_check_info.trace_id), req_pacport_unlock.trace_id);
    Snprintf(unlock_check_info.tracking_num, sizeof(unlock_check_info.tracking_num), req_pacport_unlock.tracking_num);

    //推送到linker
    CResid2RouteMsg::PushLinkerPacportUnlock(unlock_check_info);

    return 0;
}

int CMsgControl::OnAppRequestChangeRelayMsg(SOCKET_MSG_NORMAL* socket_msg, const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL msg;
    memcpy(&msg, socket_msg, sizeof(SOCKET_MSG_NORMAL));

    SOCKET_MSG_APP_REQUEST_CHANGE_RELAY change_relay_msg;

    if (CMsgParseHandle::ProcessAppRequestChangeRelayMsg(socket_msg, change_relay_msg) < 0)
    {
        AK_LOG_WARN << "ProcessAppRequestChangeRelayMsg failed.";
        return -1;
    }

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(change_relay_msg.mac, dev))
    {
        if (0 != dbinterface::ResidentPerDevices::GetMacDev(change_relay_msg.mac, dev))
        {
            AK_LOG_WARN << "could not find device, mac is:" << change_relay_msg.mac;
            return -1;
        }
    }

    //给对应设备发消息
    CResid2RouteMsg::SendP2PIndoorRelayControlMsg(change_relay_msg.mac, change_relay_msg.relay_id, change_relay_msg.relay_switch, change_relay_msg.relay_type, dev.project_type);
    return 0;
}


