#include "NotifyCommunityAlarm.h"
#include "NotifyPersonalAlarm.h"
#include "NotifyAlarm.h"
#include "dbinterface/AlexaToken.h"
#include "SnowFlakeGid.h"
#include "AkcsHttpRequest.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "ResidInit.h"
#include "json/json.h"
#include "NotifyHttpReq.h"
#include "dbinterface/ProjectUserManage.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/PubDevMngList.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

int CAlarmNotifyMsg::NotifyMsg()
{
    /*
    //兼容门口机防拆/阿联酋门未关告警/SOS没有告警位置和防区 这部分兼容直接在推送服务器处理
    if (alarm_msg_.alarm_code == 8 || alarm_msg_.alarm_code == 1 || alarm_msg_.alarm_code == 7)
    {
        alarm_msg_.alarm_zone = 1;//防区1
        alarm_msg_.alarm_location = 2;//告警类型DOOR
    }*/
    if (conn_dev_.conn_type == csmain::COMMUNITY_NONE)
    {
        AK_LOG_ERROR << "The device has never report status msg, we can not make sure which type of it.";
        return -1;
    }
    // 推送alarm状态给 Alexa
    PostAlexaChangeStatus(trace_id_);
    
    // 直接使用已设置的alarm信息，不需要再插入数据库
    if (conn_dev_.conn_type == csmain::PERSONNAL_DEV && has_personal_alarm_)
    {
        if (CPersonalAlarmProcessor::ProcessPersonalAlarmMsg(alarm_msg_, conn_dev_, personal_alarm_) != 0)
        {
            AK_LOG_ERROR << "processPersonnalAlarmMsg Failed";
            return -1;
        }
    }
    else if (conn_dev_.conn_type == csmain::COMMUNITY_DEV && has_community_alarm_)
    {
        if (CCommunityAlarmProcessor::ProcessCommunityAlarmMsg(alarm_msg_, conn_dev_, community_alarm_) != 0)
        {
            AK_LOG_ERROR << "ProcessCommunityAlarmMsg Failed";
            return -1;
        }
    }
    return 0;
}


void CAlarmNotifyMsg::PostAlexaChangeStatus(uint64_t trace_id)
{
    dbinterface::AlexaTokenInfo alexa_token_info;
    //只推家庭下alarm
    if (strlen(conn_dev_.node) == 0)
    {
        return;
    }
    if (0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(conn_dev_.node_uuid, alexa_token_info))
    {
        std::string data;
        Json::Value item;
        Json::FastWriter fast_writer;

        item["MAC"] = conn_dev_.mac;
        item["TraceId"] = std::to_string(trace_id);
        data = fast_writer.write(item);

        char url[128];
        snprintf(url, sizeof(url), "http://%s/alexaInner/v1/device/changeStatus", gstAKCSConf.smg_alexa_addr);
        AK_LOG_INFO << "alexa device alarm notify web , mac :" << conn_dev_.mac << ", trace_id : " << trace_id;
        
        CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
        GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
    }
    return;
}

void CAlarmNotifyMsg::SetPersonalAlarmInfo(const PERSONNAL_ALARM& personal_alarm)
{
    personal_alarm_ = personal_alarm;
    has_personal_alarm_ = true;
}

void CAlarmNotifyMsg::SetCommunityAlarmInfo(const ALARM& alarm)
{
    community_alarm_ = alarm;
    has_community_alarm_ = true;
}