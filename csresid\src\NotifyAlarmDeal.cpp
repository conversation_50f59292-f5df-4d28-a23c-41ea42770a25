#include "NotifyCommunityAlarmDeal.h"
#include "NotifyPersonalAlarmDeal.h"
#include "NotifyAlarmDeal.h"
#include "dbinterface/AlexaToken.h"
#include "SnowFlakeGid.h"
#include "AkcsHttpRequest.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "ResidInit.h"
#include "json/json.h"
#include "NotifyHttpReq.h"
#include "dbinterface/ProjectUserManage.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/resident/ResidentDevices.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

int CAlarmDealNotifyMsg::NotifyMsg()
{
    AK_LOG_INFO << "NotifyMsg: alarm_id=" << alarm_deal_info_.alarm_id << ", dev=" << dev_.mac;
   // Alexa推送
    PostAlexaChangeStatus();

    // 告警处理
    if (dev_.conn_type == csmain::PERSONNAL_DEV) {
        AK_LOG_INFO << "NotifyMsg: personal alarm deal";
        CPersonalAlarmDealProcessor::ProcessPersonalAlarmDealMsg(alarm_deal_info_, dev_);
    } else {
        AK_LOG_INFO << "NotifyMsg: community alarm deal";
        CCommunityAlarmDealProcessor::ProcessCommunityAlarmDealMsg(alarm_deal_info_, dev_);
    }
    return 0;
}


void CAlarmDealNotifyMsg::PostAlexaChangeStatus()
{
    if (strlen(dev_.node) == 0)
    {
        return;
    }

    // 推送alarm状态给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(dev_.node_uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatusHttpReq(dev_.mac, traceid);
        AK_LOG_INFO << "alexa device alarm notify web , mac :" << dev_.mac << ", traceid : " << traceid;
    }
}

void CAlarmDealNotifyMsg::PostAlexaChangeStatusHttpReq(const std::string& mac, uint64_t traceid)
{
    std::string data;
    Json::Value item;
    Json::FastWriter fast_writer;

    item["MAC"] = mac;
    item["TraceId"] = std::to_string(traceid);
    data = fast_writer.write(item);

    char url[128];
    snprintf(url, sizeof(url), "http://%s/alexaInner/v1/device/changeStatus", gstAKCSConf.smg_alexa_addr);

    CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}




