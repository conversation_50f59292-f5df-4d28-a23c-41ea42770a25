#include "NotifyCommunityAlarm.h"
#include "SnowFlakeGid.h"
#include "AkcsCommonDef.h"
#include "ResidInit.h"
#include "json/json.h"
#include "dbinterface/ProjectUserManage.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

int CCommunityAlarmProcessor::AddAlarmToDB(uint64_t trace_id, ALARM& alarm, SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev)
{
    //社区设备
    Snprintf(alarm.device_node, sizeof(alarm.device_node) / sizeof(TCHAR), conn_dev.node);
    Snprintf(alarm.alarm_type, sizeof(alarm.alarm_type) / sizeof(TCHAR), alarm_msg.type);
    alarm.alarm_code = alarm_msg.alarm_code;
    alarm.alarm_location = alarm_msg.alarm_location;
    alarm.alarm_zone = alarm_msg.alarm_zone;
    alarm.alarm_customize = alarm_msg.alarm_customize;

    alarm.status = AlarmStatus::ALARM_STATUS_UNDEALED;
    Snprintf(alarm.mac, sizeof(alarm.mac) / sizeof(TCHAR), conn_dev.mac);
    alarm.unit_id = conn_dev.unit_id;
    alarm.manager_account_id = conn_dev.project_mng_id;
    std::string node_time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(conn_dev.node, g_time_zone_DST);
    Snprintf(alarm.alarm_time, sizeof(alarm.alarm_time), node_time.c_str());
    alarm.trace_id = trace_id;

    if (0 != dbinterface::Alarm::AddAlarm(&alarm, dbinterface::ProjectUserManage::GetServerTag()))
    {
        return -1;
    }
    alarm_msg.alarm_id = alarm.id;
    Snprintf(alarm_msg.alarm_uuid, sizeof(alarm_msg.alarm_uuid), alarm.uuid);

    return 0;
}

int CCommunityAlarmProcessor::ProcessCommunityAlarmMsg(const SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev, const ALARM& alarm)
{
    SOCKET_MSG_ALARM_SEND recvAlarmMsg;
    memset(&recvAlarmMsg, 0, sizeof(SOCKET_MSG_ALARM_SEND));

    Snprintf(recvAlarmMsg.type, sizeof(recvAlarmMsg.type) / sizeof(TCHAR), alarm_msg.type);
    Snprintf(recvAlarmMsg.from_local, sizeof(recvAlarmMsg.from_local) / sizeof(TCHAR), conn_dev.location);
    Snprintf(recvAlarmMsg.mac, sizeof(recvAlarmMsg.mac) / sizeof(TCHAR), conn_dev.mac);
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address) / sizeof(TCHAR), conn_dev.node);
    Snprintf(recvAlarmMsg.msg_seq, sizeof(recvAlarmMsg.msg_seq) / sizeof(TCHAR), alarm_msg.msg_seq);
    recvAlarmMsg.device_type = conn_dev.dev_type;
    recvAlarmMsg.manager_account_id = conn_dev.project_mng_id;
    recvAlarmMsg.unit_id = conn_dev.unit_id;
    recvAlarmMsg.grade = conn_dev.grade;
    recvAlarmMsg.id = alarm.id;
    recvAlarmMsg.alarm_code = alarm.alarm_code;
    recvAlarmMsg.alarm_zone = alarm.alarm_zone;
    recvAlarmMsg.alarm_location = alarm.alarm_location;
    recvAlarmMsg.alarm_customize = alarm.alarm_customize;
    Snprintf(recvAlarmMsg.time, sizeof(recvAlarmMsg.time), alarm.alarm_time);
    recvAlarmMsg.trace_id = alarm.trace_id;
    
    if (conn_dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        AK_LOG_INFO << "CommunityAlarmNotify, not apt device, ignore this alarm, mac = " << conn_dev.mac;
        return 0;
    }

    // 通知pm app
    //获取pm_app和主站点列表
    ResidentPerAccountList pm_app_list;
    //pm所有站点列表<主站点site, 该社区对应实际pm站点的site>
    std::map<std::string, std::string> pm_all_sites;
    dbinterface::ResidentPersonalAccount::GetCommPmApplistByMngID(recvAlarmMsg.manager_account_id, pm_app_list);
    if (pm_app_list.size() == 0)
    {
        AK_LOG_INFO << "pm app list is empty.";
    }
    dbinterface::PersonalAccountUserInfo::GetPmAllSitesByAppList(pm_app_list, pm_all_sites);
    auto it = pm_all_sites.begin();
    for (; it != pm_all_sites.end(); ++it)
    {
        std::string main_site = it->first;
        std::string pm_site = it->second;
        //校验实际站点账号是否异常
        if (dbinterface::ProjectUserManage::MultiSiteLimit(pm_site)) 
        {
            continue;
        }
        CResid2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::RESIDENCE, TransP2PMsgType::TO_APP_UID, csmain::DeviceType::COMMUNITY_APP, main_site, recvAlarmMsg);
    }

    
    // 通知管理机
    ResidentDeviceList mng_dev_list;
    dbinterface::ResidentDevices::GetAllMngDevList(conn_dev.project_mng_id, mng_dev_list);

    for (const auto& mng_dev : mng_dev_list)
    {
        if (mng_dev.status == 0)
        {
            AK_LOG_INFO << "CommunityNotifyMngDev continue, dev offline, mac = " << mng_dev.mac;
            continue;
        }

        // 发给有相关的管理中心机: 同一栋 或者 最外层的
        if (!(mng_dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || (mng_dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && mng_dev.unit_id == conn_dev.unit_id)))
        {
            AK_LOG_INFO << "CommunityNotifyMngDev continue, not correct grade, mac = " << mng_dev.mac;
            continue;
        }

        // 最外围的设备需要管理室内机所在的楼栋
        if (mng_dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC && !dbinterface::PubDevMngList::IsManageBuildingByMac(mng_dev.mac, conn_dev.unit_id))
        {
            AK_LOG_INFO << "CommunityNotifyMngDev continue, public dev not manage this building, mac = " << mng_dev.mac;
            continue;
        }
        CResid2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::RESIDENCE, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::COMMUNITY_DEV, mng_dev.mac, recvAlarmMsg);
    }

    // 通知APT及其设备
    ResidentDeviceList dev_list;
    dbinterface::ResidentPerDevices::GetNodeDevList(conn_dev.node, dev_list);

    for (const auto& dev : dev_list)
    {
        if (dev.status == 0)
        {
            AK_LOG_INFO << "Community AlarmNotify Device, dev is offline, mac = " << dev.mac;
            continue;
        }

        if (dev.dev_type == DEVICE_TYPE_MANAGEMENT || dev.dev_type == DEVICE_TYPE_INDOOR)
        {
            CResid2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::RESIDENCE, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::COMMUNITY_DEV, dev.mac, recvAlarmMsg);
            AK_LOG_INFO << "Community AlarmNotify Device, mac = " << dev.mac;
        }
    }
    // 通知主从app
    std::set<std::string> app_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetAttendantListByUid(conn_dev.node, app_list))
    {
        AK_LOG_ERROR << "Get room app list failed. node=" << conn_dev.node;
    }
    for (const auto& account : app_list)
    {
        // 校验实际站点账号是否为多套房账户且状态异常
        if (dbinterface::ProjectUserManage::MultiSiteLimit(account))
        {
            AK_LOG_INFO << "Community AlarmNotify App, MultiSiteLimit stop send mag to app, account = " << account;
            continue;
        }

        CResid2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::RESIDENCE, TransP2PMsgType::TO_APP_UID, csmain::DeviceType::PERSONNAL_APP, account, recvAlarmMsg);
        AK_LOG_INFO << "Community AlarmNotify App, account = " << account;
    }
    return 0;
} 