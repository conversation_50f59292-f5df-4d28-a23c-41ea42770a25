#include "NotifyCommunityAlarmDeal.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "Resid2RouteMsg.h"
#include <string>
#include <cstring>
#include "ResidInit.h"
#include "control/AlarmDealNotifyMsg.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

int CCommunityAlarmDealProcessor::ProcessCommunityAlarmDealMsg(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info, const ResidentDev& dev)
{
    int alarm_id = atoi(alarm_deal_info.alarm_id);
    if (alarm_id <= 0)
    {
        AK_LOG_ERROR << "Get alarm info failed: alarm_id=" << alarm_id;
        return -1;
    }

    // 处理告警状态
    ALARM_DEAL_INFO deal_info;
    Snprintf(deal_info.result, sizeof(deal_info.result), alarm_deal_info.result);
    Snprintf(deal_info.user, sizeof(deal_info.user), alarm_deal_info.user);
    Snprintf(deal_info.alarm_id, sizeof(deal_info.alarm_id), alarm_deal_info.alarm_id);
    if (dbinterface::Alarm::DealAlarmStatus(deal_info) != 0)
    {
        AK_LOG_ERROR << "DealAlarmStatus failed: alarm_id=" << alarm_id;
        return -1;
    }

    std::string node = dev.node;
    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountByUUID(dev.project_uuid, project_info))
    {
        AK_LOG_WARN << "get project info failed. project uuid=" << dev.project_uuid;
        return -1;
    }
    std::string nodeTime = GetNodeNowDateTimeByTimeZoneStr(project_info.timezone, g_time_zone_DST);
    // Snprintf(alarm_deal_info_.time, sizeof(alarm_deal_info_.time), nodeTime.c_str());

    // 构造告警状态
    AK::Server::P2PAlarmDealNotifyMsg alarm_deal_notify_msg;
    alarm_deal_notify_msg.set_area_node(node.c_str());
    alarm_deal_notify_msg.set_user(alarm_deal_info.user);
    alarm_deal_notify_msg.set_alarm_id(alarm_deal_info.alarm_id);
    alarm_deal_notify_msg.set_result(alarm_deal_info.result);
    alarm_deal_notify_msg.set_alarm_time(nodeTime);
    alarm_deal_notify_msg.set_deal_type(alarm_deal_info.type);
    alarm_deal_notify_msg.set_target_type(0);
    alarm_deal_notify_msg.set_target("");
    community::ProcessAlarmDealNotify(dev.project_mng_id, alarm_deal_notify_msg);
    return 0;
} 