#include <cstring>
#include "util.h"
#include "doorlog/RecordActLog.h"
#include "MsgControl.h"
#include "dbinterface/SaltoLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AkcsCommonDef.h"
#include "dbinterface/DormakabaLock.h"
#include "NotifyDoorOpenMsg.h"
#include "AkcsCommonSt.h"
#include "MsgControl.h"
#include "CommunityInfo.h"
#include "dbinterface/Log/ParkingLog.h"
#include "doorlog/RecordParkingLog.h"
#include "dbinterface/ITecLock.h"
#include "dbinterface/TtLock.h"
#include "dbinterface/SmartLock.h"
#include "Resid2RouteMsg.h"
#include "redis/SafeCacheConn.h"
#include "ResidInit.h"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern AKCS_CONF gstAKCSConf;

int CDoorOpenMsg::PostThirdPartyLog(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    ThirdPartyLockDevList third_devlist;
    int default_relay = DoornumToRelayStatus(act_msg.relay);
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(conn_dev_.node, account))
    {
        return -1;
    }

    if (0 == dbinterface::ThirdPartyLockDevice::GetThirdPartyLockDevlistByMac(act_msg.mac, third_devlist))
    {
        int qrio_relay = 0;
        int yale_relay = 0;

        for (const auto& third_dev : third_devlist)
        {
            if (third_dev.lock_type == ThirdPartyLockType::QRIO && 
                strcmp(account.uuid, third_dev.personal_uuid) == 0)
            {
                qrio_relay = third_dev.relay;
                AK_LOG_INFO << "Qrio_relay_value: " << qrio_relay << ", DefaultRelay: " << default_relay 
                    << ", personal_account_uuid: " << third_dev.personal_uuid;

                if (qrio_relay & default_relay)
                {
                    //设备绑定第三方锁且本次开门有开第三方锁绑定的relay
                    PushLinKerThirdPartyLog(act_msg, third_dev);
                }
            }
            else if (third_dev.lock_type == ThirdPartyLockType::YALE &&
                strcmp(account.uuid, third_dev.personal_uuid) == 0)
            {
                yale_relay = third_dev.relay;
                AK_LOG_INFO << "Yale_relay_value: " << yale_relay << ", DefaultRelay: " << default_relay 
                    << ", personal_account_uuid: " << third_dev.personal_uuid;

                if (yale_relay & default_relay)
                {
                    //设备绑定第三方锁且本次开门有开第三方锁绑定的relay
                    PushLinKerThirdPartyLog(act_msg, third_dev);
                }
            }
        } 
    }

    return 0;
}

int CDoorOpenMsg::PushLinKerThirdPartyLog(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ThirdPartyLockDeviceInfo& third_dev)
{
    Json::Value item;
    Json::FastWriter w;
    
    item["uuid"] = third_dev.dev_uuid;
    item["lock_type"] = third_dev.lock_type;
    item["capture_type"] = act_msg.act_type;
    item["initiator"] = act_msg.initiator_sql;
    item["pic_name"] = act_msg.pic_name;
    item["personal_account_uuid"] = third_dev.personal_uuid;
    std::string data_json = w.write(item);
    int msg_type = 0;
    if (third_dev.lock_type == ThirdPartyLockType::QRIO)
    {
        msg_type = LinkerPushMsgType::LINKER_MSG_TYPE_QRIO_OPEN_DOOR;
    }
    else if (third_dev.lock_type == ThirdPartyLockType::YALE)
    {
        msg_type = LinkerPushMsgType::LINKER_MSG_TYPE_YALE_OPEN_DOOR;
    }
    else
    {
        return 0;
    }

    CResid2RouteMsg::SendLinKerCommonMsg(msg_type, data_json, third_dev.dev_uuid);
    return 0;
}

int CDoorOpenMsg::GetLinkerMsgFromDevice(LINKER_NORMAL_MSG &linker_msg)
{
    Snprintf(linker_msg.dev_uuid, sizeof(linker_msg.dev_uuid), conn_dev_.uuid);
    Snprintf(linker_msg.dev_name, sizeof(linker_msg.dev_name), conn_dev_.location);
    linker_msg.dev_grade = conn_dev_.grade;
    linker_msg.dev_type = conn_dev_.dev_type;
    linker_msg.project_type = conn_dev_.project_type;
    
    linker_msg.enable_smarthome = mac_info_.enable_smarthome;
    Snprintf(linker_msg.project_uuid, sizeof(linker_msg.project_uuid), mac_info_.project_uuid);
    Snprintf(linker_msg.ins_uuid, sizeof(linker_msg.ins_uuid), mac_info_.ins_uuid);
    return 0;
}

void CDoorOpenMsg::FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item)
{
    item["dev_uuid"] = linker_msg.dev_uuid;
    item["dev_name"] = linker_msg.dev_name;
    item["dev_type"] = linker_msg.dev_type;
    item["dev_grade"] = linker_msg.dev_grade;
    item["account_uuid"] = linker_msg.account_uuid;
    item["node_uuid"] = linker_msg.node_uuid;
    item["account_name"] = linker_msg.account_name;
    item["language"] = linker_msg.language;
    item["project_type"] = linker_msg.project_type;
    item["project_uuid"] = linker_msg.project_uuid;
    item["timestamp"] = GetCurrentMilliTimeStamp();
    item["ins_uuid"] = linker_msg.ins_uuid;
    item["enable_smarthome"] = linker_msg.enable_smarthome;
}

int CDoorOpenMsg::PushLinKerOpenDoor(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    // 室内机开门
    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_INDOOR) {
        item["indoor_sip"] = act_msg.initiator;
    }
    item["account_uuid"] = act_msg.account_uuid;
    item["open_type"] = act_msg.act_type;
    item["key"] = act_msg.key;
    item["response"] = act_msg.resp;
    item["mac"] = act_msg.mac;
    //act_msg.account是主账户
    if (strlen(act_msg.account))
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if(0 == dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, account))
        {
            item["node_uuid"] = account.uuid;
        }
    }
    item["account_name"] = act_msg.initiator_sql;
    std::string data_json = w.write(item);
    CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_OPENDOOR, data_json, linker_msg.dev_uuid);
    return 0;
}

void CDoorOpenMsg::OpenSaltoLockNotify(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return;
    }

    SaltoLockInfoList salto_lock_list;
    dbinterface::SaltoLock::GetSaltoLockListByDeviceUUID(conn_dev_.uuid, salto_lock_list);
    if (salto_lock_list.size() == 0)
    {
        return;
    }

    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // salto lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& salto_lock : salto_lock_list)
    {
        if (salto_lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;
            item["role"] = initiator_account.role;
            item["account"] = initiator_account.account;
            item["link_mac"] = conn_dev_.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = salto_lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = salto_lock.third_uuid;
            item["initiator"] = act_msg.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::SALTO;
            item["personal_account_uuid"] = initiator_account.uuid;
            AK_LOG_INFO << "open saltolock success, link mac=" << conn_dev_.mac
                        << ", saltolock bind relay=" << salto_lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << salto_lock.name << ", lock uuid=" << salto_lock.third_uuid
                        << ", personal_account_uuid=" << initiator_account.uuid
                        << ", account = " << initiator_account.account
                        << ", role = " << initiator_account.role;
            
            std::string data_json = fast_writer.write(item);
            CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_SALTO_OPEN_DOOR, data_json, salto_lock.third_uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open saltolock failed, maybe bind other lock, saltolock bind relay=" << salto_lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }
}

void CDoorOpenMsg::OpenDormakabaLockNotify(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    // dormakaba lock link设备后,权限同设备绑定,设备开启对应relay成功,才通知dormakaba lock开门
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE) 
    {
        return;
    }

    DormakabaLockInfoList dormakaba_lock_list;
    dbinterface::DormakabaLock::GetDormakabaLockListByDeviceUUID(conn_dev_.uuid, dormakaba_lock_list);
    if (dormakaba_lock_list.size() == 0)
    {
        return;
    }
    
    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // dormakaba lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& dormakaba_lock : dormakaba_lock_list)
    {
        if (dormakaba_lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;

            item["link_mac"] = conn_dev_.mac;
            item["pic_name"] = act_msg.pic_name;
            item["role"] = initiator_account.role;
            item["lock_name"] = dormakaba_lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = dormakaba_lock.third_uuid;
            item["initiator"] = act_msg.initiator_sql;
            item["account"] = initiator_account.account;
            item["lock_type"] = ThirdPartyLockType::DORMAKABA;
            item["personal_account_uuid"] = initiator_account.uuid;

            AK_LOG_INFO << "open dormakabalock success, link mac = " << conn_dev_.mac 
                        << ", dormakabalock bind relay = " << dormakaba_lock.relay << ", report_open_relay = " << report_open_relay 
                        << ", lock name = " << dormakaba_lock.name << ", lock uuid = " << dormakaba_lock.third_uuid
                        << ", personal_account_uuid = " << initiator_account.uuid;
            std::string data_json = fast_writer.write(item);
            CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_DORMAKABA_OPEN_DOOR, data_json, dormakaba_lock.third_uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open dormakabalock failed, maybe bind other lock, dormakabalock bind relay = " << dormakaba_lock.relay << ", report_open_relay = " << report_open_relay << ", personal_account_uuid = " << initiator_account.uuid;
        }
    }
}

void CDoorOpenMsg::OpenItecLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return;
    }

    ITecLockInfoList lock_list;
    dbinterface::ITecLock::GetItecLockListByDeviceUUID(conn_dev_.uuid, lock_list);
    if (lock_list.size() == 0)
    {
        return;
    }

    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // 绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& lock : lock_list)
    {
        if (lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;
            item["link_mac"] = conn_dev_.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = lock.uuid;            
            item["lock_id"] = lock.lock_id;
            item["initiator"] = act_msg.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::ITEC;
            item["personal_account_uuid"] = initiator_account.uuid;
            item["role"] = initiator_account.role;
            item["account"] = initiator_account.account;

            AK_LOG_INFO << "open itec lock success, link mac=" << conn_dev_.mac
                        << ", itec lock bind relay=" << lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << lock.name << ", lock uuid=" << lock.uuid << ", lock_id=" << lock.lock_id
                        << ", personal_account_uuid=" << initiator_account.uuid
                        << ", account = " << initiator_account.account << ", role = " << initiator_account.role;

            std::string data_json = fast_writer.write(item);
            CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_ITEC_OPEN_DOOR, data_json, lock.uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open itec failed, maybe bind other lock, itec lock bind relay=" << lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }
}

void CDoorOpenMsg::OpenTTLockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE)
    {
        return;
    }

    TtLockInfoList lock_list;
    dbinterface::TtLock::GetTtLockListByDeviceUUID(conn_dev_.uuid, lock_list);
    if (lock_list.size() == 0)
    {
        return;
    }

    ResidentPerAccount initiator_account;
    if (strlen(act_msg.account))
    {
        dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.account, initiator_account);
    }

    // 绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const auto& lock : lock_list)
    {
        if (lock.relay & report_open_relay)
        {
            Json::Value item;
            Json::FastWriter fast_writer;
            item["link_mac"] = conn_dev_.mac;
            item["pic_name"] = act_msg.pic_name;
            item["lock_name"] = lock.name;
            item["capture_type"] = act_msg.act_type;
            item["uuid"] = lock.uuid;            
            item["lock_id"] = lock.lock_id;
            item["initiator"] = act_msg.initiator_sql;
            item["lock_type"] = ThirdPartyLockType::TT;
            item["personal_account_uuid"] = initiator_account.uuid;
            item["role"] = initiator_account.role;
            item["account"] = initiator_account.account;    

            AK_LOG_INFO << "open tt lock success, link mac=" << conn_dev_.mac
                        << ", tt lock bind relay=" << lock.relay << ", report_open_relay=" << report_open_relay
                        << ", lock name=" << lock.name << ", lock uuid=" << lock.uuid << ", lock_id=" << lock.lock_id
                        << ", personal_account_uuid=" << initiator_account.uuid
                        << ", account = " << initiator_account.account << ", role = " << initiator_account.role;

            std::string data_json = fast_writer.write(item);
            CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_TT_OPEN_DOOR, data_json, lock.uuid);
            return;
        }
        else
        {
            AK_LOG_INFO << "open tt failed, maybe bind other lock, tt lock bind relay=" << lock.relay
                        << ", report_open_relay=" << report_open_relay << ", personal_account_uuid=" << initiator_account.uuid;
        }
    }
}

void CDoorOpenMsg::OpenSL20LockNotify(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{   
    // SL20 lock link设备后,权限同设备绑定,设备开启对应relay成功,才通知SL20 lock开门
    if (act_msg.resp == CAPTURE_LOG_RET_TYPE::FAILURE) 
    {
        return;
    }
    SmartLockInfoList sl20_lock_list;
    dbinterface::SmartLock::GetSmartLockListByDeviceUUID(conn_dev_.uuid, sl20_lock_list);
    if (sl20_lock_list.size() == 0)
    {
        return;
    }       
    // sl20 lock绑定了设备, 判断是否开的为link的relay
    int report_open_relay = DoornumToRelayStatus(act_msg.relay);
    for (const SmartLockInfo& sl20_lock : sl20_lock_list)
    {  
        if (sl20_lock.relay & report_open_relay)
        {
            UpdateSL20Lock(sl20_lock.uuid, sl20_lock.mac, act_msg.pic_name);
            // 若开启了保活，则需要推送给SL20锁
            if (sl20_lock.keep_alive == 1)
            {
                CResid2RouteMsg::SendSmartLockUpdateConfigurationNotify(sl20_lock.uuid, NotifySmartLockType::NOTIFY_SMARTLOCK_TYPE_SL20);
            }
        }
        else
        {
            AK_LOG_INFO << "open sl20lock failed, maybe bind other lock, sl20lock bind relay = " << sl20_lock.relay << ", report_open_relay = " << report_open_relay ;
        }
    }
}

int CDoorOpenMsg::PushLinKerTmpKey(const PersonalTempKeyUserInfo &tmpkey_info, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    item["tmpkey_name"] = tmpkey_info.name;
    //act_msg_.account是主账户
    if (strlen(act_msg_.account))
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg_.account, account))
        {
            item["node_uuid"] = account.uuid;
            item["language"] = account.language;
        }
    }
    ResidentPerAccount creator_account;
    memset(&creator_account, 0, sizeof(creator_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(tmpkey_info.creator, creator_account))
    {
        item["account_uuid"] = creator_account.uuid;
        // 后续通知也用到
        Snprintf(act_msg_.account_uuid, sizeof(act_msg_.account_uuid), creator_account.uuid);
    }
    
    item["account_name"] = act_msg_.initiator_sql;
    std::string data_json = w.write(item);
    CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_TMPKEY, data_json, linker_msg.dev_uuid);
    return 0;
}

int CDoorOpenMsg::NotifyMsg()
{
    LINKER_NORMAL_MSG linker_msg;
    memset(&linker_msg, 0, sizeof(linker_msg));
    GetLinkerMsgFromDevice(linker_msg);


    int handle_mode = RecordActLog::GetInstance().HandleMode(act_msg_, conn_dev_);
    AK_LOG_INFO << conn_dev_.mac << " device report activity logs. type:" << act_msg_.act_type << " initiator:" << act_msg_.initiator << " handle_mode:" << handle_mode;

    if (handle_mode == NEW_COMMUNITY_NEW_DEVICE)
    {
        if (strlen(act_msg_.per_id) < 2)
        {
            //自动开门及快递开门类型
            if(RecordActLog::GetInstance().EmergencyType(act_msg_) || act_msg_.act_type == ACT_OPEN_DOOR_TYPE::DELIVERY_UNLOCK)
            {
                Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), act_msg_.initiator);
                Snprintf(act_msg_.key, sizeof(act_msg_.key), "--");
            }
            else
            {
                Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), "visitor");
            }
            RecordActLog::GetInstance().SetCaptureAction(act_msg_);
        }
        else
        {
            RecordActLog::GetInstance().NewModeHandle(act_msg_);
        }
        //新社区个人设备记录三方锁日志
        if (act_msg_.is_public == ACT_LOG_PERSONAL_DEV)
        {
            PostThirdPartyLog(act_msg_);
        }

        //家居开门通知
        PushLinKerOpenDoor(act_msg_, linker_msg);
        if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)  //内开门
        {
            AK_LOG_INFO << conn_dev_.mac << " inward unlock";
            // 直接返回，后续有需要在扩展
            return 0;
        }

        // dormakaba开门通知
        OpenDormakabaLockNotify(act_msg_);

        // SL20锁开门通知
        OpenSL20LockNotify(act_msg_);
        // salto开门通知
        OpenSaltoLockNotify(act_msg_);
        
        //ITec
        OpenItecLockNotify(act_msg_);
        //TT
        OpenTTLockNotify(act_msg_);

        if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery) < 0)
        {
            AK_LOG_WARN << "Add personnal motion capture failed.";
            return -1;
        }
        if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK && act_msg_.mng_type == 0 && act_msg_.resp == CAPTURE_LOG_RET_TYPE::SUCCESS) {
            PARKING_LOG parking_log;
            RecordParkingLog::GetInstance().NewParkingHandle(act_msg_, parking_log, conn_dev_);
            RecordParkingLog::GetInstance().RecordParkingVehicleLog(parking_log);
        }
        return 0;
    }
    else if (handle_mode == NEW_COMMUNITY_OLD_DEVICE)
    {
        RecordActLog::GetInstance().NewCommunityOldDeviceHandle(act_msg_, conn_dev_);
        
        //家居开门通知
        PushLinKerOpenDoor(act_msg_, linker_msg);
        
        // dormakaba开门通知
        OpenDormakabaLockNotify(act_msg_);

        // SL20锁开门通知
        OpenSL20LockNotify(act_msg_);

        // salto开门通知
        OpenSaltoLockNotify(act_msg_);

        
        //ITec
        OpenItecLockNotify(act_msg_);
        //TT
        OpenTTLockNotify(act_msg_);

        
        if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery) < 0)
        {
            AK_LOG_WARN << "Add personnal motion capture failed.";
            return -1;
        }
        return 0;
    }
    else
    {
        //OLD_MODE
    }

    if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::CALL)
    {
        RecordActLog::GetInstance().RecordCallLog(act_msg_, conn_dev_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::TMPKEY)
    {
        //写死'visitor'
        Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), "visitor");
        PersonalTempKeyUserInfo tempkey_user_info;

        RecordActLog::GetInstance().RecordTmpKeyLog(act_msg_, conn_dev_, tempkey_user_info);
        if ((!act_msg_.resp) && (tempkey_user_info.creator.size()  > 0))
        {
            //Tmpkey使用通知创建者&家居
            CResid2RouteMsg::SendP2PMainSendTmpKeyUsedMsg(tempkey_user_info.creator, tempkey_user_info.name);
            PushLinKerTmpKey(tempkey_user_info, linker_msg);
        }
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY)
    {
        //写死'visitor'
        Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), "visitor");
        RecordActLog::GetInstance().RecordLocalKeyLog(act_msg_, conn_dev_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::RFCARD)
    {
        RecordActLog::GetInstance().RecordRfCardLog(act_msg_, conn_dev_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::FACE)
    {
        RecordActLog::GetInstance().RecordFaceLog(act_msg_, conn_dev_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR)
    {
        RecordActLog::GetInstance().RecordRemoteLog(act_msg_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::TEMP_CAPTURE)  //测温上报
    {
        Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), act_msg_.initiator);
        if (dbinterface::PersonalCapture::AddTemperatureCapture(act_msg_) < 0)
        {
            AK_LOG_WARN << "Add  temperature capture failed.";
            return -1;
        }
        return 0;
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::INWARD_UNLOCK)  //内开门
    {
        AK_LOG_INFO << conn_dev_.mac << " inward unlock";
        // 直接通知家居开门通知，后续
        PushLinKerOpenDoor(act_msg_, linker_msg);
        return 0;
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::HANDSET_UNLOCK)
    {
        RecordActLog::GetInstance().RecordHandsetLog(act_msg_);
    }
    else if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK)
    {
        RecordActLog::GetInstance().SetCaptureAction(act_msg_);
    }
    else
    {
        //add by chenzhx 20230104
        //门禁E16旧版本会上报一个type=7CALL_UNLOCK_INDOOR,但是这个其实是他们本地input开门方式。
        //这里进行兼容也是为了防止设备上报了个区分不了的类型，然后因为没有记录日志，csstorage根据picname更新不到导致告警。
        //去掉，不然网页记录会有问题。csstorage先不进行这部分的告警
        //Snprintf(act_msg_.initiator_sql, sizeof(act_msg_.initiator_sql), act_msg_.initiator);
        //Snprintf(act_msg_.key, sizeof(act_msg_.key), "--");
        //Snprintf(act_msg_.room_num, sizeof(act_msg_.room_num), "--");        
        AK_LOG_WARN << "invalid open door active type: " << act_msg_.act_type;
        return -1;
    }

    CommunityInfo community_info(conn_dev_.project_mng_id);
    int is_new = community_info.GetIsNew();
    //单住户及新社区个人设备记录三方锁日志
    if (act_msg_.is_public == ACT_LOG_PERSONAL_DEV && (act_msg_.mng_type || is_new))
    {
        PostThirdPartyLog(act_msg_);
    }

    // 家居开门通知
    PushLinKerOpenDoor(act_msg_, linker_msg);

    // dormakaba开门通知
    OpenDormakabaLockNotify(act_msg_);

    // SL20锁开门通知
    OpenSL20LockNotify(act_msg_);

    // salto开门通知
    OpenSaltoLockNotify(act_msg_);

    //ITec
    OpenItecLockNotify(act_msg_);
    //TT
    OpenTTLockNotify(act_msg_);

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg_, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }
    if (act_msg_.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK && act_msg_.mng_type == 0) {
        PARKING_LOG parking_log;
        RecordParkingLog::GetInstance().NewParkingHandle(act_msg_, parking_log, conn_dev_);
        RecordParkingLog::GetInstance().RecordParkingVehicleLog(parking_log);
    }
    return 0;
}

void CDoorOpenMsg::UpdateSL20Lock(const std::string& lock_uuid, const std::string& mac, const std::string& pic_name)
{
    SafeCacheConn cache_conn(g_redis_db_smart_lock);
    if (!cache_conn.isConnect())
    {
        return;
    }
    if (!cache_conn.isExists(lock_uuid))
    {
        std::string pic = "picname_"+ pic_name;
        cache_conn.hset(lock_uuid, mac, pic);
        AK_LOG_INFO <<"UpdateSL20Lock [lock_uuid] "<<lock_uuid;
        cache_conn.expire(lock_uuid, gstAKCSConf.sl20_opendoor_expire);
    }
    return;
}