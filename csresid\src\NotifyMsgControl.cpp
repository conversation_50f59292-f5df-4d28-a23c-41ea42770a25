#include "NotifyMsgControl.h"
#include "AkcsMonitor.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "ResidInit.h"
#include "AkcsHttpRequest.h"
#include "NotifyHttpReq.h"
#include "NotifyPerText.h"
#include "NotifyVideoRecordMsg.h"
#include "AkLogging.h"
#include "NotifyPerMotion.h"
#include "NotifyDoorOpenMsg.h"
#include "NotifyAlarm.h"
#include "NotifyAlarmDeal.h"
CNotifyMsgControl::CNotifyMsgControl(): m_MsgCount(0)
{

}

CNotifyMsgControl::~CNotifyMsgControl()
{
    //curl_easy_cleanup(m_pCurl);
    m_NotifyMsgList.clear();
}

CNotifyMsgControl* CNotifyMsgControl::instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::http_req_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::motion_notify_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::door_open_log_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::alarm_instance = NULL;
CNotifyMsgControl* CNotifyMsgControl::alarm_deal_instance = NULL;

CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CNotifyMsgControl();
    }

    return instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetHttpReqInstance()
{
    if (http_req_instance == NULL)
    {
        http_req_instance = new CNotifyMsgControl();
    }

    return http_req_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetMotionNotifyInstance()
{
    if (motion_notify_instance == NULL)
    {
        motion_notify_instance = new CNotifyMsgControl();
    }

    return motion_notify_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetDoorOpenInstance()
{
    if (door_open_log_instance == NULL)
    {
        door_open_log_instance = new CNotifyMsgControl();
    }

    return door_open_log_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetAlarmInstance()
{
    if (alarm_instance == NULL)
    {
        alarm_instance = new CNotifyMsgControl();
    }
    return alarm_instance;
}

CNotifyMsgControl* CNotifyMsgControl::GetAlarmDealInstance()
{
    if (alarm_deal_instance == NULL)
    {
        alarm_deal_instance = new CNotifyMsgControl();
    }   
    return alarm_deal_instance;
}


CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}

CNotifyMsgControl* GetHttpReqMsgControlInstance()
{
    return CNotifyMsgControl::GetHttpReqInstance();
}

CNotifyMsgControl* GetMotionNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetMotionNotifyInstance();
}

CNotifyMsgControl* GetDoorOpenMsgProcessInstance()
{
    return CNotifyMsgControl::GetDoorOpenInstance();
}

CNotifyMsgControl* GetAlarmMsgProcessInstance()
{
    return CNotifyMsgControl::GetAlarmInstance();
}

CNotifyMsgControl* GetAlarmDealMsgProcessInstance()
{
    return CNotifyMsgControl::GetAlarmDealInstance();
}


//在主线程初始化,注意m_pCurl不是线程安全的.
int CNotifyMsgControl::Init()
{
    m_t = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success,thread_id=" << m_t.get_id();
    return 0;
}

int CNotifyMsgControl::GetNotifyMsgListSize()
{
    std::unique_lock<std::mutex> lock(m_mtx);
    return m_NotifyMsgList.size();
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        int is_next_wait = 0;
        NotifyMsgPrt TmpPtr;
        {
            std::unique_lock<std::mutex> lock(m_mtx);
            while (m_NotifyMsgList.size() == 0)
            {
                m_cv.wait(lock);
                is_next_wait = 1;
            }
            //检测队列长度
            //modified by czw,2021-09-09,当社区群发message时队列会达到大几百,故调整为1000告警
            if (is_next_wait == 1 && m_NotifyMsgList.size() > 1000) //added by chenyc,2019-08-13,500只是估算值,具体的数值需要测试:消费能力TPS*允许的延迟时间来决定.
            {
                std::string msg = "notify msg queue overflow, the length now is:";
                msg += std::to_string(m_NotifyMsgList.size());
                std::string worker_node = "csresident_";
                worker_node += gstAKCSConf.csmain_outer_ip;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, msg, AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSMAIN_NOTIFY);
                AK_LOG_WARN << msg;
            }
            TmpPtr = m_NotifyMsgList.back();
            m_NotifyMsgList.pop_back();
        }
        TmpPtr->NotifyMsg();
    }
    return 0;
}

int CNotifyMsgControl::AddHttpReqNotiyMsg(const CHttpReqNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CHttpReqNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}
int CNotifyMsgControl::AddTextNotifyMsg(const CPerTextNotifyMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CPerTextNotifyMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddMotionNotifyMsg(const CPerMotionNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CPerMotionNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddDoorOpenMsg(const CDoorOpenMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CDoorOpenMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddVideoRecordNotifyMsg(const CVideoRecordNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CVideoRecordNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddAlarmMsg(const CAlarmNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CAlarmNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddAlarmDealMsg(const CAlarmDealNotifyMsg& msg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt tmp_ptr(new CAlarmDealNotifyMsg(msg));
        m_NotifyMsgList.push_front(tmp_ptr);
        m_cv.notify_all();
    }
    return 0;
}

