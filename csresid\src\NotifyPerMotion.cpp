#include "MsgControl.h"
#include "NotifyMsgControl.h"
#include "NotifyPerMotion.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "ResidInit.h"
#include "MsgToControl.h"
#include "AKUserMng.h"
#include "PushClientMng.h"
#include "ResidPushClient.h"
#include "ResidServer.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "dbinterface/Message.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "MsgBuild.h"
#include "AkcsCommonSt.h"
#include "cspush/PushClient.h"
#include "AppCallStatus.h"
#include "CachePool.h"
#include "SafeCacheConn.h"
#include "dbinterface/ProjectUserManage.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

int CPerMotionNotifyMsg::NotifyMsg()
{    
    if (!IsNeedNotifyApp())
    {
        return 0;
    }

    CResid2AppMsg msg_sender;

    //在线发送 消息构造
    std::string dclient_msg;
    GetMsgBuildHandleInstance()->BuildMotionAlertMsg(motion_alert_, dclient_msg);

    //离线推送消息构造
    BuildOfflineMotionAlertNotifyMsg(msg_sender);
    
    //消息发送给csmain
    msg_sender.SetOnlineMsgData(dclient_msg);
    msg_sender.SetClient(real_account_);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetMsgId(MSG_TO_APP_NOTIFY_MOTION_OCCURED);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SendMsg(csmain::PushMsgType::PUSH_MSG_TYPE_MOTION);
    return 0;
}


void CPerMotionNotifyMsg::BuildOfflineMotionAlertNotifyMsg(CResid2AppMsg& msg_sender)
{
    //离线推送消息构造
    std::string title;
    int is_multi_site = dbinterface::ProjectUserManage::IsAccountMultiSite(real_account_);
    if (is_multi_site && OfflinePush::GetMultiSiteUserTitle(motion_alert_.node, title) == 0 )
    {
        //6.6多套房场景下，标题增加前缀
        msg_sender.InsertOfflineMsgKV("title_prefix", title);
        msg_sender.InsertOfflineMsgKV("site", motion_alert_.node);
    }
    
    msg_sender.InsertOfflineMsgKV("mac_sip", motion_alert_.mac);
    msg_sender.InsertOfflineMsgKV("device_name", motion_alert_.location);
    msg_sender.InsertOfflineMsgKV("detection_type", std::to_string(motion_alert_.detection_type));
    msg_sender.InsertOfflineMsgKV("detection_info", std::to_string(motion_alert_.detection_info));
}

bool CPerMotionNotifyMsg::IsNeedNotifyApp()
{
    //接收Motion状态校验
    if (!CheckMotionRecvState(real_account_))
    {
        return false;
    }

    //校验实际站点账号是否为多套房账户且状态异常
    if(dbinterface::ProjectUserManage::MultiSiteLimit(real_account_))
    {
        return false;
    }

    //DND校验
    if (!CheckAppDNDState(real_account_))
    {
        return false;
    }

    return true;
}

bool CPerMotionNotifyMsg::CheckAppDNDState(const std::string& real_site)
{
    int app_state = AppCallStatus::GetInstance().GetAppState(real_site);
    if (APP_STATE_DND == app_state)
    {
        AK_LOG_INFO << "real site=" << real_site << " set dnd, ignore motion alert message.";
        return false;
    }
    
    return true;
}

bool CPerMotionNotifyMsg::CheckMotionRecvState(const std::string& real_site)
{
    //先确定redis中是否有设置过了
    SafeCacheConn redis_conn(g_redis_db_appconf);

    std::string key = "motion";
    std::string type;

    type = redis_conn.hget(key, real_site);
    if (type.empty()) //如果该uid从来没有设置过,设置为默认接收motion
    {
        redis_conn.hset(key, real_site, Int2String(csmain::MotionRecvType::kRecv));
        AK_LOG_INFO << "real_site=" << real_site << " need receive motion alert message.";
        return true;
    }
    else //redis里面有记录
    {
        uint32_t type2 = String2Int(type);
        if (type2 == csmain::MotionRecvType::kRecv)
        {
            AK_LOG_INFO << "real_site=" << real_site << " need receive motion alert message.";
            return true;
        }
        AK_LOG_INFO << "real_site=" << real_site << " motion recv type= " << type2 << ". no need receive motion alert message.";
        return false;
    }

    return false;
}
