#include "MsgControl.h"
#include "NotifyMsgControl.h"
#include "NotifyPerText.h"
#include "AkcsHttpRequest.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "ResidInit.h"
#include "MsgToControl.h"
#include "AKUserMng.h"
#include "PushClientMng.h"
#include "ResidPushClient.h"
#include "ResidServer.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "Resid2AppMsg.h"
#include "dbinterface/Message.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/OfflinePushInfo.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/SL20Lock.h"

extern LOG_DELIVERY gstAKCSLogDelivery;


int CPerTextNotifyMsg::NotifyMsg()
{    
    if (client_type_ != CPerTextNotifyMsg::APP_SEND)
    {
        return -1;
    }

    //账号异常 通知拦截
    if(dbinterface::ProjectUserManage::MultiSiteLimit(account_))
    {
        return -1;
    }

    int role;
    if (0 != dbinterface::ProjectUserManage::GetRoleByAccount(account_, role))
    {
        AK_LOG_WARN << "GetRoleByAccount failed, account = " << account_;
        return -1;
    }

    int dclient_msg_id = MSG_TO_DEVICE_SEND_TEXT_MESSAGE;
    MsgEncryptType dclient_encrypt_type = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;

    //在线dclient消息构造
    char xml_msg_buf[4096];
    memset(xml_msg_buf, 0, sizeof(xml_msg_buf));
    // 家居锁事件类型推送，需要构建对应的dclient消息
    if (IsSmartLockEventNotifyType())
    {
        SL20LockInfo lock_info;
        std::string lock_name;
        if (0 == dbinterface::SL20Lock::GetSL20LockInfoByUUID(text_msg_info_.extension_field, lock_info))
        {
            lock_name = lock_info.name;
        }

        std::string str_msg;
        GetMsgBuildHandleInstance()->BuildSmartLockMessageNotifyEventXmlMsg(text_msg_info_, account_, lock_name, str_msg);
        Snprintf(xml_msg_buf, sizeof(xml_msg_buf), str_msg.c_str());
        dclient_msg_id = MSG_TO_APP_SMARTLOCK_EVENT_NOTIFY;
        dclient_encrypt_type = MsgEncryptType::TYEP_DEFAULT_ENCRYPT;
    }
    else if (GetMsgBuildHandleInstance()->BuildTextMessageXmlMsg(xml_msg_buf, sizeof(xml_msg_buf), &text_msg_info_) != 0)
    {
        AK_LOG_WARN << "BuildTextMessageXmlMsg failed";
        return -1;
    }
    
    AK_LOG_INFO << "build xml_msg_buf = " << xml_msg_buf;

    //离线推送消息 构造   
    CResid2AppMsg msg_sender;
    msg_sender.InsertOfflineMsgKV("msg_id", GetMsgId(text_msg_info_.id, role));

    std::string title;
    int msg_type = csmain::PUSH_MSG_TYPE_ONLY_ONLINE;
    if (OfflinePush::GetMultiSiteUserTitle(account_, title) == 0 )
    {
        msg_sender.InsertOfflineMsgKV("title_prefix", title);
    }    
    if(text_msg_info_.type == CPerTextNotifyMsg::TEXT_MSG || text_msg_info_.type == CPerTextNotifyMsg::BOOKING_MSG)
    {
        msg_type = csmain::PUSH_MSG_TYPE_TEXT;
        msg_sender.InsertOfflineMsgKV("title", text_msg_info_.title);
        msg_sender.InsertOfflineMsgKV("content", text_msg_info_.content);
    }
    else if(text_msg_info_.type == CPerTextNotifyMsg::VOICE_MSG)
    {
        msg_type = csmain::PUSH_MSG_TYPE_VOICE_MSG;
        msg_sender.InsertOfflineMsgKV("title", "Voice_Message");
        msg_sender.InsertOfflineMsgKV("content", text_msg_info_.content);
	} 
    else if (text_msg_info_.type == (int)MessageType2::AKUBELA_LOCK_BATTERY_NOTICE)
    {
        msg_type = csmain::PUSH_MSG_TYPE_AKUBELA_LOCK_BATTERY;
        msg_sender.InsertOfflineMsgKV("lock_name", text_msg_info_.content);
        msg_sender.InsertOfflineMsgKV("extension_field", text_msg_info_.extension_field);
    } 
    else if (text_msg_info_.type == (int)MessageType2::TRAILERROR_NOTICE)
    {
        msg_type = csmain::PUSH_MSG_TYPE_LOCK_TRAILERROR_NOTICE;
        msg_sender.InsertOfflineMsgKV("lock_name", text_msg_info_.content);
        msg_sender.InsertOfflineMsgKV("extension_field", text_msg_info_.extension_field);
    }
    else if (text_msg_info_.type == (int)MessageType2::MAILBOX_ARRIVAL_MSG)
    {
        msg_type = csmain::PUSH_MSG_TYPE_MAILBOX_ARRIVAL_NOTICE;
        msg_sender.InsertOfflineMsgKV("title", text_msg_info_.title);
        msg_sender.InsertOfflineMsgKV("content", text_msg_info_.content);
    }
    else if (text_msg_info_.type == (int)MessageType2::SMARTLOCK_DOORBELL_EVENT)
    {
        msg_type = csmain::PUSH_MSG_TYPE_SMARTLOCK_DOORBELL_EVENT;
        msg_sender.InsertOfflineMsgKV("lock_name", text_msg_info_.content);
        msg_sender.InsertOfflineMsgKV("extension_field", text_msg_info_.extension_field);
    }

/*
    // 未读消息总数 : notice_msg + voice_msg + call_history . 目前只有推给ios才能生效 (等app方案成熟再推)
    if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
    {
        PerAccountUserInfo user_info;
        dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByAccount(account_, user_info);
        int unread_messages_count = dbinterface::Message::getUnReadMessageCount(user_info.uuid, user_info.create_time);
        int unread_callhistory_count = dbinterface::CallHistory::GetUnReadCallHistoryCount(account_ ,gstAKCSLogDelivery.call_history_delivery);
        int unread_all_count = unread_messages_count + unread_callhistory_count;
        kv.insert(map<std::string, std::string>::value_type("unread_msg", std::to_string(unread_all_count)));
    }
*/

    msg_sender.SetClient(base_.uid());
    msg_sender.SetSendType((TransP2PMsgType)base_.type());
    msg_sender.SetEncType(dclient_encrypt_type);
    msg_sender.SetMsgId(dclient_msg_id);
    msg_sender.SetOnlineMsgData(xml_msg_buf);
    msg_sender.SendMsg(msg_type);
    return 0;
}

std::string CPerTextNotifyMsg::GetMsgId(uint32_t msg_id, int role)
{
    return std::to_string(msg_id) + "_" + std::to_string(role);
}

bool CPerTextNotifyMsg::IsSmartLockEventNotifyType()
{
    return text_msg_info_.type == (int)MessageType2::SMARTLOCK_DOORBELL_EVENT;
}