#include "NotifyPersonalAlarm.h"
#include "SnowFlakeGid.h"
#include "AkcsCommonDef.h"
#include "ResidInit.h"
#include "json/json.h"
#include "dbinterface/ProjectUserManage.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

int CPersonalAlarmProcessor::AddPersonalAlarmToDB(uint64_t trace_id, PERSONNAL_ALARM& personal_alarm, SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev)
{
    Snprintf(personal_alarm.community, sizeof(personal_alarm.community), conn_dev.community);
    Snprintf(personal_alarm.device_node, sizeof(personal_alarm.device_node), conn_dev.node);
    Snprintf(personal_alarm.alarm_type, sizeof(personal_alarm.alarm_type), alarm_msg.type);
    Snprintf(personal_alarm.mac, sizeof(personal_alarm.mac), conn_dev.mac);  //2017-11-02,增加告警设备的mac地址
    personal_alarm.extension = conn_dev.extension;
    personal_alarm.status = AlarmStatus::ALARM_STATUS_UNDEALED;
    personal_alarm.alarm_code = alarm_msg.alarm_code;
    personal_alarm.alarm_zone = alarm_msg.alarm_zone;
    personal_alarm.alarm_location = alarm_msg.alarm_location;
    personal_alarm.alarm_customize = alarm_msg.alarm_customize;
    std::string node_time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(conn_dev.node, g_time_zone_DST);
    Snprintf(personal_alarm.alarm_time, sizeof(personal_alarm.alarm_time), node_time.c_str());
    personal_alarm.trace_id = trace_id;
    if (0 != dbinterface::PersonalAlarm::AddAlarm(personal_alarm, dbinterface::ProjectUserManage::GetServerTag()))
    {
        return -1;
    }
    alarm_msg.alarm_id = personal_alarm.id;
    Snprintf(alarm_msg.alarm_uuid, sizeof(alarm_msg.alarm_uuid), personal_alarm.uuid);
    return 0;
}

int CPersonalAlarmProcessor::ProcessPersonalAlarmMsg(const SOCKET_MSG_ALARM& alarm_msg, const ResidentDev& conn_dev, const PERSONNAL_ALARM& personal_alarm)
{
    SOCKET_MSG_ALARM_SEND recvAlarmMsg;
    memset(&recvAlarmMsg, 0, sizeof(SOCKET_MSG_ALARM_SEND));

    Snprintf(recvAlarmMsg.type, sizeof(recvAlarmMsg.type), alarm_msg.type);
    Snprintf(recvAlarmMsg.msg_seq, sizeof(recvAlarmMsg.msg_seq), alarm_msg.msg_seq);
    recvAlarmMsg.alarm_code = alarm_msg.alarm_code;
    recvAlarmMsg.alarm_zone = alarm_msg.alarm_zone;
    recvAlarmMsg.alarm_location = alarm_msg.alarm_location;
    recvAlarmMsg.alarm_customize = alarm_msg.alarm_customize;
    recvAlarmMsg.id = personal_alarm.id;
    Snprintf(recvAlarmMsg.community, sizeof(recvAlarmMsg.community), conn_dev.community);
    //联动单元
    Snprintf(recvAlarmMsg.address, sizeof(recvAlarmMsg.address), conn_dev.node);
    Snprintf(recvAlarmMsg.time, sizeof(recvAlarmMsg.time), personal_alarm.alarm_time);
    Snprintf(recvAlarmMsg.from_local, sizeof(recvAlarmMsg.from_local) / sizeof(TCHAR), conn_dev.location);
    Snprintf(recvAlarmMsg.mac, sizeof(recvAlarmMsg.mac) / sizeof(TCHAR), conn_dev.mac);
    recvAlarmMsg.trace_id = personal_alarm.trace_id;

    // 通知Apt下设备
    ResidentDeviceList dev_list;
    dbinterface::ResidentPerDevices::GetNodeDevList(conn_dev.node, dev_list);
    for (const auto& dev : dev_list)
    {
        if (dev.status == 0)
        {
            AK_LOG_INFO << "dev is offline, mac = " << dev.mac;
            continue;
        }

        if (dev.dev_type == DEVICE_TYPE_MANAGEMENT || dev.dev_type == DEVICE_TYPE_INDOOR)
        {
            CResid2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::PERSONAL, TransP2PMsgType::TO_DEV_MAC, csmain::DeviceType::PERSONNAL_DEV, dev.mac, recvAlarmMsg);
            AK_LOG_INFO << "Personal AlarmNotify Device, mac = " << dev.mac;
        }
    }

    // 通知主从app
    std::set<std::string> app_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetAttendantListByUid(conn_dev.node, app_list))
    {
        AK_LOG_ERROR << "Get room app list failed. node=" << conn_dev.node;
    }
    for (const auto& account : app_list)
    {
        // 校验实际站点账号是否为多套房账户且状态异常
        if (dbinterface::ProjectUserManage::MultiSiteLimit(account))
        {
            AK_LOG_INFO << "Personal AlarmNotify App, MultiSiteLimit stop send mag to app, account = " << account;
            continue;
        }

        CResid2RouteMsg::SendP2PAlarmNotifyMsg(project::PROJECT_TYPE::PERSONAL, TransP2PMsgType::TO_APP_UID, csmain::DeviceType::PERSONNAL_APP, account, recvAlarmMsg);
        AK_LOG_INFO << "Personal AlarmNotify App, account = " << account;
    }

    return 0;
} 