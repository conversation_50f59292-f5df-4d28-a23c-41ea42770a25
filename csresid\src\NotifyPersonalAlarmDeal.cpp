#include "NotifyPersonalAlarmDeal.h"
#include "dbinterface/PersonalAlarm.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "Resid2RouteMsg.h"
#include <string>
#include <cstring>
#include "ResidInit.h"
#include "control/AlarmDealPerNotifyMsg.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

int CPersonalAlarmDealProcessor::ProcessPersonalAlarmDealMsg(const SOCKET_MSG_ALARM_DEAL& alarm_deal_info, const ResidentDev& dev)
{
    int alarm_id = atoi(alarm_deal_info.alarm_id);
    if (alarm_id <= 0)
    {
        AK_LOG_ERROR << "Get personal alarm info failed: alarm_id=" << alarm_id;
        return -1;
    }

    PERSONAL_ALARM_DEAL_INFO deal_info;
    Snprintf(deal_info.result, sizeof(deal_info.result), alarm_deal_info.result);
    Snprintf(deal_info.user, sizeof(deal_info.user), alarm_deal_info.user);
    Snprintf(deal_info.alarm_id, sizeof(deal_info.alarm_id), alarm_deal_info.alarm_id);
    if (dbinterface::PersonalAlarm::DealAlarmStatus(deal_info) != 0)
    {
        AK_LOG_ERROR << "DealPersonalAlarmStatus failed: alarm_id=" << alarm_id;
        return -1;
    }

    std::string node = dev.node;
    std::string timezone = dbinterface::ResidentPersonalAccount::GetNodeTimeZoneStr(node);
    std::string nodeTime = GetNodeNowDateTimeByTimeZoneStr(timezone, g_time_zone_DST);
    // 这里假设alarm_deal_info_是可修改的，如果不是，需要调整
    // Snprintf(alarm_deal_info_.time, sizeof(alarm_deal_info_.time), nodeTime.c_str());

    // 构造告警状态
    AK::Server::P2PAlarmDealNotifyMsg alarm_deal_notify_msg;
    alarm_deal_notify_msg.set_area_node(node.c_str());
    alarm_deal_notify_msg.set_user(alarm_deal_info.user);
    alarm_deal_notify_msg.set_alarm_id(alarm_deal_info.alarm_id);
    alarm_deal_notify_msg.set_result(alarm_deal_info.result);
    alarm_deal_notify_msg.set_alarm_time(nodeTime);
    alarm_deal_notify_msg.set_deal_type(alarm_deal_info.type);
    alarm_deal_notify_msg.set_target_type(0);
    alarm_deal_notify_msg.set_target("");
    personal::ProcessAlarmDealNotify(dev.community, alarm_deal_notify_msg);
    return 0;
} 