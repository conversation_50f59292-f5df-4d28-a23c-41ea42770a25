#include <cstring>
#include "util.h"
#include "AkcsCommonDef.h"
#include "NotifyVideoRecordMsg.h"
#include "AkcsCommonSt.h"
#include "CommunityInfo.h"

extern LOG_DELIVERY gstAKCSLogDelivery;


int CVideoRecordNotifyMsg::NotifyMsg()
{
    // 更新Log库的VideoRecordName字段
    dbinterface::PersonalCapture::UpdateVideoRecordName(conn_dev_.mac, video_record_msg_.pic_name, video_record_msg_.video_record_name, 
                                            gstAKCSLogDelivery.personal_capture_delivery, mac_info_.log_delivery_uuid);

    AK_LOG_INFO << "UpdateVideoRecordName mac = " << conn_dev_.mac << ", pic_name = " << video_record_msg_.pic_name << ", video_record_name = " << video_record_msg_.video_record_name;
    return 0;
}

