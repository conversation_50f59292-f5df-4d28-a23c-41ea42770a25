#include "Resid2AppMsg.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "ResidServer.h"
#include "ClientControl.h"
#include "PushClientMng.h"
#include "ProjectUserManage.h"
#include "PersonalAccountUserInfo.h"

extern ResidServer* g_resid_srv_ptr;

std::string CResid2AppMsg::AccountToMainSiteAccount(const std::string& account)
{
    if (account.empty())
    {
        return account;
    }

    std::string main_account;
    if (msg_.send_type == TransP2PMsgType::TO_APP_UID || msg_.send_type == TransP2PMsgType::TO_APP_UID_ONLINE)
    {
        if (dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(account, main_account) != 0)
        {
            return account;
        }
    }
    else if (msg_.send_type == TransP2PMsgType::TO_APP_UUID)
    {
        if (dbinterface::PersonalAccountUserInfo::GetMainAccountByAccountUUID(account, main_account) != 0)
        {
            return account;
        }
    }

    return main_account;
}

int CResid2AppMsg::SendMsg(int msg_type)
{
    // 1.校验实际站点账号是否异常
    if (dbinterface::ProjectUserManage::MultiSiteLimit(msg_.client))
    {
        AK_LOG_WARN << "SendMsg failed: multi site limit, account=" << msg_.client;
        return -1;
    }

    // 2.多套房转换成主站点
    std::string main_account = AccountToMainSiteAccount(msg_.client);
    
    if (main_account.empty())
    {
        AK_LOG_WARN << "SendMsg failed: main_account is empty, account=" << msg_.client;
        return -1;
    }

    // 3.构造离线推送消息
    CMobileToken mobile_token;
    g_resid_srv_ptr->GetAppToken(main_account, mobile_token);
    
    //6.6多套房场景下，标题增加前缀,不是多套房不需要前缀
    if (!mobile_token.IsMultiSite())
    {
        offline_kv_["title_prefix"] = "";
    }   
    BuildOffilePushMsg(msg_type, mobile_token);

    // 4.特殊消息再加工
    if (msg_.msg_id == MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY)      // 紧急消息
    {
        int alarm_reminder_status = 0;
        dbinterface::ProjectUserManage::GetCurrentLoginSiteAlarmReminderStatusByMainSite(
            main_account, alarm_reminder_status
        );

        InsertOfflineMsgKV("enable_strong_reminder", to_string(alarm_reminder_status));
    }
    else if (msg_.msg_id == MSG_TO_DEVICE_SEND_TEXT_MESSAGE)
    {
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS || msg_type == csmain::PUSH_MSG_TYPE_YALE_BATTERY)
        {
            if (msg_type != csmain::PUSH_MSG_TYPE_VOICE_MSG)
            {
                SetForcePush(1);
            }
        }
    }

    AK_LOG_INFO << "Send message to main site app: account=" << msg_.client
        << ", main_site=" << main_account << ", msg_id=" << msg_.msg_id
        << ", msg_type=" << msg_type;

    // 5.推送消息
    SetClient(main_account);
    msg_.msg_len = strlen(msg_.msg_data);
    msg_.push_msg_len = strlen(msg_.push_msg_data);
    GetClientControlInstance()->SendTransferMsg(msg_);
    return 0;
}

int CResid2AppMsg::BuildOffilePushMsg(int msg_type, const CMobileToken& mobile_token)
{
    std::string push_msg;
    TransP2PMsgType send_type = msg_.send_type;
    if (send_type == TransP2PMsgType::TO_ALL_APP || send_type == TransP2PMsgType::TO_APP_UID || send_type == TransP2PMsgType::TO_APP_UUID)
    {
        InsertOfflineMsgKV("language", mobile_token.Language());
        InsertOfflineMsgKV("app_oem", std::to_string(mobile_token.AppOem()));
        InsertOfflineMsgKV("dclient", std::to_string(mobile_token.CommonVersion()));

        std::string token = mobile_token.FcmToken();
        if (mobile_token.MobileType() == csmain::AppType::APP_IOS)
        {
            token = mobile_token.Token();
        }

        CResidPushClient::buildPushMsg(mobile_token.MobileType(), token, msg_type, offline_kv_, mobile_token.OemName(), push_msg);
    }

    SetPushMsgData(push_msg);
    return 0;
}


