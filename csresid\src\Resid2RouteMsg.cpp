#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "util.h"
#include <boost/algorithm/string.hpp>
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "Resid2RouteMsg.h"
#include "ResidInit.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectInfo.h"
#include "AkcsCommonSt.h"
#include "ProjectUserManage.h"
#include "ResidServer.h"
#include "MsgBuild.h"
#include "RouteMqProduce.h"
#include "AK.Linker.pb.h"
#include "AK.Resid.pb.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "ResidInit.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "RouteMqProduce.h"

extern AKCS_CONF gstAKCSConf;
extern LOG_DELIVERY gstAKCSLogDelivery;
extern RouteMQProduce* g_nsq_producer;
CResid2RouteMsg::CResid2RouteMsg()
{

}
             

CResid2RouteMsg::~CResid2RouteMsg()
{

}

AK::BackendCommon::BackendP2PBaseMessage CResid2RouteMsg::CreateP2PBaseMsg(int msgid, int type, 
   const std::string &uid, csmain::DeviceType conntype, int projecttype)
{
    //设备的话 client_uid=uid
    std::string client_uid = uid;
    
    /*
    if(type == TO_APP_UID)
    {
        //多套房转换
        if(dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(uid, client_uid) != 0)
        {
            AK_LOG_WARN << "change to main site error, uid:" << uid;
        }
    } 
    else if (type == TO_APP_UUID)
    {
        //多套房转换
        if(dbinterface::PersonalAccountUserInfo::GetMainAccountUUIDByAccountUUID(uid, client_uid) != 0)
        {
            AK_LOG_WARN << "change to main site error, uuid:" << uid;
        }
    }
    */
    
    AK::BackendCommon::BackendP2PBaseMessage msg;
    msg.set_type(type);
    msg.set_uid(client_uid);
    msg.set_msgid(msgid);
    msg.set_conn_type(conntype);
    msg.set_project_type(projecttype);    
    return msg;
}

csmain::DeviceType CResid2RouteMsg::DevProjectTypeToDevType(int projecttype)
{
    if(projecttype == project::PERSONAL)
    {
        return csmain::DeviceType::PERSONNAL_DEV;
    }
    else if (projecttype == project::RESIDENCE)
    {
        return csmain::DeviceType::COMMUNITY_DEV;
    }
    else if (projecttype == project::OFFICE)
    {
        return csmain::DeviceType::OFFICE_DEV;
    }
    return csmain::DeviceType::COMMUNITY_NONE;
}

void CResid2RouteMsg::PushMsg2Route(const google::protobuf::MessageLite* msg)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project::RESIDENCE);//TODO:这里没有区分单住户和社区
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);    
}

void CResid2RouteMsg::PushLinkerWeather(const SOCKET_MSG_DEV_WEATHER_INFO& weather_info)
{
    Json::Value item;
    Json::FastWriter w;
    item["mac"] = weather_info.mac;
    item["city"] = weather_info.city;
    item["state_province"] = weather_info.states;
    item["country"] = weather_info.country;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LINKER_MSG_TYPE_WEATHER, data_json, weather_info.mac);
}


void CResid2RouteMsg::PushLinkerPacportReg(const SOCKET_MSG_PACPORT_REG_INFO& pacport_reg_info)
{
    Json::Value item;
    Json::FastWriter w;
    item["mac"] = pacport_reg_info.mac;
    item["prefecture_name"] = pacport_reg_info.prefecture_name;
    item["city_name"] = pacport_reg_info.city_name;
    item["district_name"] = pacport_reg_info.district_name;
    item["street_num"] = pacport_reg_info.street_num;
    item["postal_code"] = pacport_reg_info.postal_code;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LINKER_MSG_TYPE_PACPORT_REGIST, data_json, pacport_reg_info.mac);
}

void CResid2RouteMsg::PushLInkerPacportUnReg(const std::string& mac)
{
    Json::Value item;
    Json::FastWriter w;
    item["mac"] = mac;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LINKER_MSG_TYPE_PACPORT_UNREGIST, data_json, mac);
}

void CResid2RouteMsg::PushLinkerPacportUnlock(const SOCKET_MSG_PACPORT_UNLOCK_CHECK& pacport_unlock_info)
{
    Json::Value item;
    Json::FastWriter w;
    item["mac"] = pacport_unlock_info.mac;
    item["courier_name"] = pacport_unlock_info.courier_name;
    item["tracking_num"] = pacport_unlock_info.tracking_num;
    item["room_num"] = pacport_unlock_info.room_num;
    item["trace_id"] = pacport_unlock_info.trace_id;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LINKER_MSG_TYPE_PACPORT_UNLOCK_CHECK, data_json, pacport_unlock_info.mac);
}

// kit设备创建房间
void CResid2RouteMsg::PushLinkerCreateRoom(const std::vector<SOCKET_MSG_DEV_KIT_DEVICE>& devices, const std::string& kit_mac, const std::string& msg_seq)
{
    Json::Value devices_array;
    for (const auto& dev : devices)
    {
        Json::Value device_item;
        device_item["mac"] = dev.mac;
        device_item["type"] = dev.type;
        device_item["location"] = dev.location;
        device_item["relay"] = model::DEFAULT_INDOOR_REALY;
        devices_array.append(device_item);
    }

    Json::Value root;
    root["devices"] = devices_array;
    root["msg_seq"] = msg_seq;
    root["msg_id"] = MSG_FROM_DEVICE_REQUEST_CREATE_ROOM;
    std::string data_json = root.toStyledString();  

    AK_LOG_INFO << "PushLinkerCreateRoom : " << data_json;
    SendLinKerCommonMsg(LINKER_MSG_TYPE_KIT_CREATE_ROOM, data_json, kit_mac);
}

// 更换kit设备
void CResid2RouteMsg::PushLinkerChangeKitDev(const std::string& original_mac, const std::string& replace_mac, const std::string& msg_seq)
{
    Json::Value item;
    Json::FastWriter writer;
    item["msg_seq"] = msg_seq;
    item["replace_mac"] = replace_mac;
    item["original_mac"] = original_mac;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "PushLinkerChangeKitDev : " << data_json;
    SendLinKerCommonMsg(LINKER_MSG_TYPE_KIT_REPLACE_MAC, data_json, original_mac);
}

void CResid2RouteMsg::SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key, int project_type)
{
    AK::Linker::P2PRouteLinker msg;
    msg.set_key(key);
    msg.set_msg_json(data_json);
    msg.set_message_type(msg_type);
    msg.set_project_type(project_type);
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void CResid2RouteMsg::GroupVoiceMsg(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    //获取留言信息
    PersonalVoiceMsgInfo voice_msg_info;
    PersonalVoiceMsgSendList send_list;
    std::string location;
    std::string msg_uuid;
    memset(&voice_msg_info, 0, sizeof(voice_msg_info));
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByMacAndFileName(msg.mac(), msg.filename(), voice_msg_info))
    {
        location = voice_msg_info.location;
        msg_uuid = voice_msg_info.uuid;
    }

    //获取发送列表
    dbinterface::PersonalVoiceMsg::GetVoiceMsgListInfoByMsgUUID(msg_uuid, send_list);

    for (const auto & send_node: send_list)
    {
        int type;
        int count;
        int msg_id;
        std::string receiver_uuid;
        AK::BackendCommon::BackendP2PBaseMessage base;
        if (strlen(send_node.indoor_uuid) > 0)
        {
            receiver_uuid = send_node.indoor_uuid;
            count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(send_node.indoor_uuid);
            type = DEVICE_TYPE_INDOOR;
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_VOICE_MSG, TransP2PMsgType::TO_DEV_UUID, receiver_uuid,
                DevProjectTypeToDevType(msg.project_type()), msg.project_type());
        }
        else if (strlen(send_node.personal_uuid) > 0)
        {
            receiver_uuid = send_node.personal_uuid;
            msg_id = send_node.id;
            type = DEVICE_TYPE_APP;
            std::string uid;
            dbinterface::ResidentPersonalAccount::GetAccountByUUID(receiver_uuid, uid);
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_VOICE_MSG, TransP2PMsgType::TO_APP_UID, uid, 
               DevProjectTypeToDevType(msg.project_type()), msg.project_type());
        }
        
        AK::Server::P2PSendVoiceMsg msg2;
        msg2.set_msg_id(msg_id);
        msg2.set_count(count);
        msg2.set_location(location);
        msg2.set_receiver_uuid(receiver_uuid);
        msg2.set_receiver_type(type);
        //通过project_type区分需要处理的消息的业务类型，通过base里面的conntype区别主站点的业务类型，用于发送
        msg2.set_project_type(msg.project_type());
        base.mutable_p2psendvoicemsg2()->CopyFrom(msg2);
        PushMsg2Route(&base);
    }
}

void CResid2RouteMsg::SendUpdateConfigByAccount(int type, const std::string &node, const std::string &account,
    int account_role, int manager_id, int unit_id, int project_type)
{
    AK::Server::P2PMainAccountConfigRewriteMsg msg;
    msg.set_node(node);
    msg.set_account(account);
    msg.set_account_role(account_role);
    msg.set_type(type);
    msg.set_manager_id(manager_id);
    msg.set_unit_id(unit_id);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_S2C_ACCOUNT_CONFIG_REWRITE);
    pdu2.SetSeqNum(0);
    pdu2.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.route_topic);
}

void CResid2RouteMsg::GroupDeliveryMsg(const PerTextMessageSendList& text_messages)
{
    for(const auto &text_message : text_messages)
    {
        AK::BackendCommon::BackendP2PBaseMessage base;
        int type = 0; //接收方类型：室内机或App
        if(text_message.client_type == PersoanlMessageSend::DEV_SEND)
        {
            type = DEVICE_TYPE_INDOOR;
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_DELIVERY_MSG, TransP2PMsgType::TO_DEV_UUID, text_message.uuid,
                DevProjectTypeToDevType(text_message.comm_message.project_type), text_message.comm_message.project_type);
        }
        else if (text_message.client_type == PersoanlMessageSend::APP_SEND)
        {
            type = DEVICE_TYPE_APP;
            std::string uid;
            dbinterface::ResidentPersonalAccount::GetAccountByUUID(text_message.uuid, uid);
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_DELIVERY_MSG, TransP2PMsgType::TO_APP_UID, uid,
                DevProjectTypeToDevType(text_message.comm_message.project_type), text_message.comm_message.project_type);
        }
        AK::Server::P2PSendDeliveryMsg msg2;
        msg2.set_title(text_message.comm_message.title);
        msg2.set_content(text_message.comm_message.content);
        msg2.set_receiver_type(type);
        msg2.set_receiver_uuid(text_message.uuid);
        msg2.set_project_type(text_message.comm_message.project_type);
        msg2.set_message_id(text_message.id);
        base.mutable_p2psenddeliverymsg2()->CopyFrom(msg2);
        PushMsg2Route(&base);
    }
}

void CResid2RouteMsg::GroupIndoorRelayStatusMsg(const std::string&node, const std::string& mac, uint64_t relay_status, int relay_type, int project_type)
{
    std::vector<std::string> node_uid_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeUidListByNode(node, node_uid_list))
    {
        AK_LOG_WARN << "GroupIndoorRelayStatusMsg. Get Node Uid List failed. node:" << node;
        return;
    }

    for(const auto& uid : node_uid_list)
    {
        AK::BackendCommon::BackendP2PBaseMessage base;
        base = CreateP2PBaseMsg(AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG, TransP2PMsgType::TO_APP_UID, uid,
                DevProjectTypeToDevType(project_type), project_type);

        AK::Server::GroupMainReportRelayStatus msg;
        msg.set_relay_status(relay_status);
        msg.set_mac(mac);
        msg.set_account(uid);
        msg.set_relay_type(relay_type);
        base.mutable_groupindoorrelaystatusmsg2()->CopyFrom(msg);
        PushMsg2Route(&base);
    }

    return;
}

void CResid2RouteMsg::SendP2PIndoorRelayControlMsg(const std::string& mac, int relay_id, int relay_switch, int relay_type, int project_type)
{
    AK::Server::P2PMainChangeRelay msg;
    msg.set_relay_switch(relay_switch);
    msg.set_relay_id(relay_id);
    msg.set_mac(mac);
    msg.set_relay_type(relay_type);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_CHANGE_RELAY_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void CResid2RouteMsg::SendP2PEmergencyDoorControlMsg(const dbinterface::PmEmergencyDoorLogInfoList& info_list, const std::string& msg_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type, int project_type)
{
    for(auto const& info : info_list)
    {	
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(info.device_uuid, dev))
        {
            AK_LOG_WARN << "Get device Info failed. device uuid=" << info.device_uuid;
            continue;
        }
        //离线记录异常doorlog
        if (dev.status == DeviceStatus::DEVICE_STATUS_OFFLINE)
        {
            AK_LOG_WARN << "device is offline ,device_uuid =" << info.device_uuid;
            dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(msg_uuid, info.device_uuid, dbinterface::RelayStatus::OFFLINE);
            dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(info.device_uuid, initiator, act_type, dbinterface::RelayStatus::OFFLINE, gstAKCSLogDelivery.personal_capture_delivery);
            continue;
        }

        AK::BackendCommon::BackendP2PBaseMessage base_msg;
        base_msg = CreateP2PBaseMsg(AKCS_M2R_EMERGENCY_DOOR_CONTROL, TransP2PMsgType::TO_DEV_UUID, info.device_uuid,
                                                            DevProjectTypeToDevType(project_type), project_type);

        //设备在线消息推送
        AK::Server::P2PPmEmergencyDoorControlMsg control_msg;
        control_msg.set_msg_uuid(msg_uuid);
        control_msg.set_device_uuid(info.device_uuid);
        control_msg.set_initiator(initiator);
        control_msg.set_auto_manual(OPERATE_TYPE::AUTO);
        control_msg.set_operation_type(CONTROL_TYPE::OPEN_DOOR);
        control_msg.set_relay(info.relay);
        control_msg.set_security_relay(info.security_relay);

        base_msg.mutable_p2ppmemergencydoorcontrolmsg2()->CopyFrom(control_msg);
        PushMsg2Route(&base_msg);
	}
    return;
}

void CResid2RouteMsg::SendRoute2WebCommonMsg(int msg_type, const std::string &data_json)
{
    AK::Server::P2PRouteToWebMsg msg;
    msg.set_message_type(msg_type);
    msg.set_msg_json(data_json);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_PUSH_WEB_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void CResid2RouteMsg::SendEmergencyNotifyWebMsg(const std::string& alarm_uuid, const std::string& project_uuid)
{
    Json::Value item;
    Json::FastWriter writer;
    item["alarm_uuid"] = alarm_uuid;
    item["project_uuid"] = project_uuid;
    std::string data_json = writer.write(item);

    AK_LOG_INFO << "SendEmergencyNotifyWebMsg : " << data_json;
    SendRoute2WebCommonMsg(PushWebMsgType::PUSH_WEB_MSG_EMERGENCY, data_json);
}

void CResid2RouteMsg::SendStopVideoRecordMsg(const SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO& stop_record_video)
{
    AK::Route::P2PRouteStopVideoRecordReq msg;    
    msg.set_mac(stop_record_video.mac);
    msg.set_site(stop_record_video.site);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_STOP_VIDEO_RECORD_REQ);
    pdu.SetProjectType(project::RESIDENCE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
    return;
}

void CResid2RouteMsg::SendMonitorCaptureMsg(const SOCKET_MSG_REQ_CAPTURE& request_capture)
{
    AK::Route::RtspCaptureReq msg;
    msg.set_mac(request_capture.mac);
    msg.set_site(request_capture.site);
    msg.set_node(request_capture.node);
    msg.set_pic_name(request_capture.pic_name);
    msg.set_user_name(request_capture.username);
    msg.set_record_video(request_capture.record_video);
    msg.set_flow_uuid(request_capture.flow_uuid);
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_RTSP_CAPTURE_REQ);
    pdu.SetProjectType(project::RESIDENCE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void CResid2RouteMsg::SendUpdateConfig(int changetype, const std::string& mac, int project_type, const std::string& ip)
{
    AK::Server::P2PMainDevConfigRewriteMsg msg;
    msg.set_mac(mac);
    msg.set_ip(ip);
    msg.set_type(changetype);
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_S2C_DEV_CONFIG_REWRITE);
    pdu2.SetSeqNum(0);
    pdu2.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu2, gstAKCSConf.route_topic);
    return ;
}

void CResid2RouteMsg::SendP2PMainSendTmpKeyUsedMsg(const std::string& creator, const std::string& name)
{
    //Tmpkey使用通知创建者
    AK::Server::P2PMainSendTmpkeyUsed msg;
    msg.set_account(creator);
    msg.set_name(name);
    //6.6一人多套房修改，新增主站点字段
    std::string main_site;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(creator, main_site);
    msg.set_main_site(main_site);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
}

void CResid2RouteMsg::SendSmartLockUpdateConfigurationNotify(const std::string& lock_uuid, NotifySmartLockType notify_lock_type)
{
    AK::Server::SmartLockUpdateConfigurationNotifyMsg msg;
    msg.set_lock_uuid(lock_uuid);
    msg.set_lock_type((int)notify_lock_type);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);

}