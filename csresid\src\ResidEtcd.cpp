#include <string.h>
#include <signal.h>
#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evnsq/producer.h>
#include "RouteClient.h"
#include "RouteClientMng.h"
#include "AkcsMonitor.h"
#include "ResidInit.h"
#include "ResidRoute.h"
#include "PushClientMng.h"
#include "ResidPushClient.h"
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include "loop/RouteLoopManager.h"

#define MAX_RLDB_CONN 10
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;

extern const char *g_conf_db_addr;
extern const char *g_ak_srv_route;
extern const char *g_ak_srv_cspush;
extern const char *g_ak_srv_nsqlookupd;
extern const char *g_ak_srv_smg_alexa;
extern const char *g_ak_srv_video_record;
std::vector<std::string> g_csvideorecord_http_addrs;
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);


static void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.akcs_db_ip) != 0) || (conf_tmp.db_port != gstAKCSConf.akcs_db_port))
    {
        Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip),  conf_tmp.db_ip);
        gstAKCSConf.akcs_db_port = conf_tmp.db_port;
        DaoReInit();
    }
}

// alexa
void UpdateSmgAlexaConfSrv()
{
    std::string smg_alexa_addr;
    g_etcd_cli_mng->LoadSrvSmgAlexaConf(smg_alexa_addr);
    Snprintf(gstAKCSConf.smg_alexa_addr, sizeof(gstAKCSConf.smg_alexa_addr), smg_alexa_addr.c_str());
}

//含csroute cspush的连接
void EtcdSrvInit()
{    
    //csroute
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }

    RouteSrvConnInit(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(),
        std::bind(&CResidRouteClient::CreateClient, std::placeholders::_1, std::placeholders::_2));

    GetRouteLoopManagerInstance()->StartLoop();

    //cspush
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    PushSrvConnInit(cspush_addrs, g_etcd_loop.get(), &CResidPushClient::CreateClient);

    if (strlen(gstAKCSConf.push_server_addr) > 5)
    {
        PushClientPtr push_cli_ptr = CResidPushClient::CreateClient(std::string(gstAKCSConf.push_server_addr), g_etcd_loop.get());
        push_cli_ptr->Start();
        CPushClientMng::Instance()->AddOuterPushSrv(push_cli_ptr);
    }

    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }

    // csvideorecord
    if (g_etcd_cli_mng->GetAllVideoRecordSrvs(g_csvideorecord_http_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    
    //smg alexa
    UpdateSmgAlexaConfSrv();
    //等待接入服务启动完成,再启动csroute的loop,让csmain去建立与csroute的长连接
   // while (!g_access_srv_ready)
   // {
   //     usleep(100 * 1000);
   // }

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_route, std::bind(&CResidRouteClient::UpdateClient));
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_cspush, std::bind(&CResidPushClient::UpdatePushSrvList));
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_smg_alexa, UpdateSmgAlexaConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_video_record, UpdateSmgAlexaConfSrv);
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get());

    //etcd_loop 目前只有route的连接在用  
    g_etcd_loop->Run();

}



