#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <signal.h>
#include <unistd.h>
#include <sys/stat.h>
#include <errno.h>
#include <signal.h>
#include "ConfigFileReader.h"
#include "DevOnlineMng.h"
#include "Resid2MainHandle.h"
#include "ResidServer.h"
#include "CachePool.h"
#include "ClientControl.h"
#include "ProjectUserManage.h"
#include "RouteMqProduce.h"
#include "ResidInit.h"
#include "ResidEtcd.h"
#include "EtcdDns.h"
#include "NotifyMsgControl.h"
#include "EtcdCliMng.h"
#include "AkcsAppInit.h"
#include "DevOnlineMng.h"
#include "HttpServer.h"
#include "MsgFilter.h"
#include "tinyxml.h"
#include "util_time.h"
#include "NotifyMsgControl.h"
#include "CharChans.h"
#include "ResidRpcClientInit.h"
#include "EmergencyMsgControl.h"
#include "dbinterface/Log/LogSlice.h"
#include "Metric.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;
const size_t kMqueue_number = 2;
std::map<std::string, AKCS_DST> g_time_zone_DST;

void InstanceInit()
{
    CacheManager::getInstance()->Init("/usr/local/akcs/csresid/conf/csresid_redis.conf", "csresidCacheInstances");
    CClientControl::GetInstance();

    GetNotifyMsgControlInstance()->Init();
    GetHttpReqMsgControlInstance()->Init();
    GetMotionNotifyMsgControlInstance()->Init();
    GetDoorOpenMsgProcessInstance()->Init();
    DevOnlineMng::GetInstance()->Init();
}

int EtcdConnInit()
{
    g_etcd_dns_mng = new CEtcdDnsManager(gstAKCSConf.etcd_server_addr);
    std::thread dnsThread = std::thread(&CEtcdDnsManager::StartDnsResolver, g_etcd_dns_mng);
    while(!g_etcd_dns_mng->DnsIsOk())
    {
        usleep(10);
    }
    dnsThread.detach();
    //域名解析完才能初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_dns_mng->GetAddrs());
    g_etcd_dns_mng->SetEtcdCli(g_etcd_cli_mng);   
    return 0;
}

void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void onOneSecondTimer()
{
    GetEmergencyControlInstance()->AddBucketMsg();
}

void TimeTaskInit()
{
    evpp::EventLoop loop;

    //一键开关门设备超时检测时间轮
    GetEmergencyControlInstance()->InitBucketSize(30); //超时时间：30s

    loop.RunEvery(evpp::Duration(1.0), onOneSecondTimer);
    loop.Run();
}

int main(int argc, char** argv)
{
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2("/var/run/csresid.pid"))
    {
        printf("There is another csresid running in this system.");
        return -1;
    }

    g_srv_id = GetEth0IPAddr();

    GlogInit2(argv[0], "csresidlog");
    
    ConfInit();

    EtcdConnInit();

    //起http服务线程
    std::thread httpthread(startHttpServer);

    GrpcClientInit();
        
    InstanceInit();
    if(DaoInit() != 0)
    {
        AK_LOG_FATAL << "DaoInit fialed.";
        return -1;
    }
    
    if (LogDeliveryInit() != 0)
    {
        AK_LOG_FATAL << "LogDeliveryInit fialed.";
        return -1;
    }
    
    dbinterface::ProjectUserManage::GetServerTag();

    std::thread nsqthread2 = std::thread(MQProduceInit);
    
    std::thread etcdthread = std::thread(EtcdSrvInit);//里面启动消费路由的消息

    std::thread conf_watch_thread = std::thread(ConfWatch);

    ParseTimeZone("/usr/local/akcs/csmain/conf/TimeZone.xml", g_time_zone_DST);

    //要万事俱备了,才能启动这个服务,否则有些资源没有准备好,就启动核心业务逻辑,会有风险
    g_resid_srv_ptr  = new ResidServer(kMqueue_number); 
    g_resid_srv_ptr->Start();

    std::thread timer_thread = std::thread(TimeTaskInit);

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csresid is starting";


    httpthread.join();
    etcdthread.join();
    nsqthread2.join();
    conf_watch_thread.join();
    timer_thread.join();

    GlogClean2();
    return 0;
}

