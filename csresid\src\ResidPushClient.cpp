#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "ResidPushClient.h"
#include <boost/algorithm/string.hpp>
#include "AES128.h"
#include "AKUserMng.h"
#include "ResidInit.h"
#include "ResidEtcd.h"
#include "EtcdCliMng.h"
#include "PushClientMng.h"
#include "InnerMsgDef.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;

CResidPushClient::CResidPushClient(evpp::EventLoop* loop,
                         const std::string& serverAddr/*ip:port*/,
                         const std::string& name):CPushClient(loop, serverAddr, name)
{

}

PushClientPtr CResidPushClient::CreateClient(const std::string &addr, evpp::EventLoop* loop)
{
    //获取srv id
    std::string  logic_srv_id = "csresid_";
    logic_srv_id += GetEth0IPAddr();
    PushClientPtr route_cli_ptr = std::make_shared<CResidPushClient>(loop, addr, logic_srv_id);
    return route_cli_ptr;       
}
void CResidPushClient::UpdatePushSrvList()
{
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) == 0)
    {
        AK_LOG_INFO << "UpdatePushSrv begin";
        CPushClientMng::Instance()->UpdatePushSrv(cspush_addrs, g_etcd_loop.get(),
            std::bind(&CResidPushClient::CreateClient, std::placeholders::_1, std::placeholders::_2));
    }
}

//只构造消息，发送到csmain统一发
void CResidPushClient::buildPushMsg(int MobileTyp, const std::string& token, int msgType, const AppOfflinePushKV& kv, std::string oem, std::string& msg_json)
{
    if (token.empty())
    {
        AK_LOG_INFO << "The token is empty, no message needs to be constructed";
        return;
    }

    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }
    item["ver"] = PUSH_SERVER_VER;
    if (MobileTyp == csmain::APP_IOS)
    {
        item["app_type"] = "ios";
    }
    else if (MobileTyp == csmain::APP_ANDROID_FCM)
    {
        item["app_type"] = "fcm";
    }
    else if (MobileTyp == csmain::APP_ANDROID_HUAWEI)
    {
        item["app_type"] = "android_huawei";
    }
    else if (MobileTyp == csmain::APP_ANDROID_XIAOMI)
    {
        item["app_type"] = "android_xiaomi";
    }
    else if (MobileTyp == csmain::APP_ANDROID_OTHERS)
    {
        item["app_type"] = "fcm";   //兼容原先上报fcm用的此枚举
    }
    else if (MobileTyp == csmain::APP_ANDROID_OPPO)
    {
        item["app_type"] = "android_oppo";
    }
    else if (MobileTyp == csmain::APP_ANDROID_VIVO)
    {
        item["app_type"] = "android_vivo";
    }
    else if (MobileTyp == csmain::APP_ANDROID_FLYME)
    {
        item["app_type"] = "android_flyme";
    }
    else if (MobileTyp == csmain::APP_ANDROID_JPUSH)
    {
        item["app_type"] = "android_jpush";
    }

    itemData["token"] = token;

    if (msgType == csmain::PUSH_MSG_TYPE_CALL)
    {
        itemData["msg_type"] = "CALL";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_ALARM)
    {
        itemData["msg_type"] = "ALARM";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_DEALALARM)
    {
        itemData["msg_type"] = "DEALALARM";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_MOTION)
    {
        itemData["msg_type"] = "MOTION";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_FORCE_LOGOUT)
    {
        itemData["msg_type"] = "FORCELOGOUT";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_DELIVERY || msgType == csmain::PUSH_MSG_TYPE_DELIVERY_BOX)
    {
        itemData["msg_type"] = "DELIVERY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_TMPKEY)
    {
        itemData["msg_type"] = "TMPKEY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_TEXT)
    {
        itemData["msg_type"] = "TEXT_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_VOICE_MSG)
    {
        itemData["msg_type"] = "VOICE_MSG";
	}
    else if (msgType == csmain::PUSH_MSG_TYPE_YALE_BATTERY)
    {
        itemData["msg_type"] = "YALE_BATTERY_MSG";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST)
    {
        itemData["msg_type"] = "TRIGGER_CSPUSH_TEST";
    }    
    else if (msgType == csmain::PUSH_MSG_TYPE_EMERGENCY_NOTIFY)
    {
        itemData["msg_type"] = "EMERGENCY_CONTROL_NOTIFY";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_AKUBELA_LOCK_BATTERY)
    {
        itemData["msg_type"] = "AKUBELA_LOCK_BATTERY_LEVEL";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_LOCK_TRAILERROR_NOTICE)
    {
        itemData["msg_type"] = "LOCK_TRAILERROR_NOTICE";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_MAILBOX_ARRIVAL_NOTICE)
    {
        itemData["msg_type"] = "MAILBOX_ARRIVAL_NOTICE"; 
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_SMARTLOCK_DOORBELL_EVENT)
    {
        itemData["msg_type"] = "SMARTLOCK_DOORBELL_NOTIFY";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_SMARTLOCK_DWELL_EVENT)
    {
        itemData["msg_type"] = "SMARTLOCK_DWELL_NOTIFY";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_SMARTLOCK_TAMPER_EVENT)
    {
        itemData["msg_type"] = "SMARTLOCK_TAMPER_NOTIFY";
    }
    else if (msgType == csmain::PUSH_MSG_TYPE_SMARTLOCK_HIJACK_EVENT)
    {
        itemData["msg_type"] = "SMARTLOCK_HIJACK_NOTIFY";
    }
    else
    {
        AK_LOG_WARN << "[DEBUG] 未匹配到任何消息类型, msgType=" << msgType;
    }

    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }

    std::stringstream logs;
    logs << "[PushMsg] type:" << item["app_type"] << " msgtype:" << itemData["msg_type"] << " token:" << token;
    std::string log = logs.str();
    boost::algorithm::replace_all(log, "\n", "");
    AK_LOG_INFO << log;

    std::string data_json = wData.write(itemData);
    AK_LOG_INFO << "[PushMsg] data_json is:" << data_json;
    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }


    msg_json = w.write(item);
}


void CResidPushClient::buildPushMsgCall(const CMobileToken &apptoken, int is_voip, std::string& caller_name,  const uint64_t traceid)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    int MobileTyp = apptoken.MobileType();
    std::string token = apptoken.Token();
    int dclientver = apptoken.CommonVersion();
    std::string language = apptoken.Language();
    std::string oem = apptoken.OemName();
    
    if (oem.size() == 0)
    {
        item["OEM"] = gstAKCSConf.oem_name;
    }
    else
    {
        item["OEM"] = oem;
    }

    if (MobileTyp == csmain::APP_IOS)
    {
        if (is_voip)
        {
            token = apptoken.VoipToken();
        }
        else
        {
            token = apptoken.Token();
        }
    }
    else
    {
        token = apptoken.FcmToken();
    }

    
    item["ver"] = PUSH_SERVER_VER;
    if (MobileTyp == csmain::APP_IOS)
    {
        item["app_type"] = "ios";
        if (is_voip)
        {
            token = apptoken.VoipToken();
        }
        else
        {
            token = apptoken.Token();
        }
        
    }
    else if (MobileTyp == csmain::APP_ANDROID_FCM)
    {
        item["app_type"] = "fcm";
        token = apptoken.FcmToken();
    }
    else if (MobileTyp == csmain::APP_ANDROID_HUAWEI)
    {
        item["app_type"] = "android_huawei";
    }
    else if (MobileTyp == csmain::APP_ANDROID_XIAOMI)
    {
        item["app_type"] = "android_xiaomi";
    }
    else if (MobileTyp == csmain::APP_ANDROID_OTHERS)
    {
        item["app_type"] = "fcm";   ////兼容原先上报类型
    }
    else if (MobileTyp == csmain::APP_ANDROID_OPPO)
    {
        item["app_type"] = "android_oppo";
    }
    else if (MobileTyp == csmain::APP_ANDROID_VIVO)
    {
        item["app_type"] = "android_vivo";
    }
    else if (MobileTyp == csmain::APP_ANDROID_FLYME)
    {
        item["app_type"] = "android_flyme";
    }
    else if (MobileTyp == csmain::APP_ANDROID_JPUSH)
    {
        item["app_type"] = "android_jpush";
    }

    itemData["token"] = token;
    itemData["msg_type"] = "CALL";
    itemData["user"] =  caller_name;
    itemData["is_voip"] =  is_voip;
    itemData["dclient"] =  dclientver;
    itemData["language"] = language;
    itemData["app_oem"] = apptoken.AppOem();
    
    //生成traceid:
    char traceid_tmp[20] = {0};
    ::snprintf(traceid_tmp, 20, "%ld", traceid);
    itemData["traceid"] = traceid_tmp;

    std::stringstream logs;
    logs << "[PushMsg] voip call type:" << item["app_type"] << " msgtype: CALL" << " token:" << token << " language:" << language << " app_oem:" << itemData["app_oem"];
    std::string log = logs.str();
    boost::algorithm::replace_all(log, "\n", "");
    LOG_INFO << log;
    if (token.empty())
    {
        return;
    }

    std::string data_json = wData.write(itemData);
    char* pszEncData = NULL;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_AESkey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);

    if (pszEncData)
    {
        item["data"] = pszEncData;
        free(pszEncData);
    }


    std::string msg_json = w.write(item);

    auto c = client_.conn();
    if (c && c->IsConnected())
    {
        c->Send(msg_json);
    }
    else
    {
        AK_LOG_INFO << "[PushMsg] conn is error";
    }
    return;
}

