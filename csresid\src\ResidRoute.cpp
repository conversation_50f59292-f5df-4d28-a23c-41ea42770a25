#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "util.h"
#include <boost/algorithm/string.hpp>
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "ResidRoute.h"
#include "ResidEtcd.h"
#include "Route2ResidMsg.h"
#include "AK.Resid.pb.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "ThreadLocalSingleton.h"
#include "MsgIdToMsgName.h"
#include "loop/RouteLoopManager.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;


CResidRouteClient::CResidRouteClient(evpp::EventLoop* loop,
             const std::string& serverAddr/*ip:port*/,
             const std::string& name):CRouteClient(loop, serverAddr, name)
{

}
             

CResidRouteClient::~CResidRouteClient()
{

}


RouteClientPtr CResidRouteClient::CreateClient(const std::string &addr, evpp::EventLoop* loop)
{
    //获取srv id
    std::string  logic_srv_id = "csmain_";//这个同csmain一致,这样route找设备挂的csmain再找resid才能正确
    logic_srv_id += GetEth0IPAddr();

    RouteClientPtr route_cli_ptr = std::make_shared<CResidRouteClient>(loop, addr, "csresid client");
    route_cli_ptr->SetServerInfo(logic_srv_id, AK::Base::LOGIC_CLIENT_TYPE_RESID);
    return route_cli_ptr;       
}


void CResidRouteClient::UpdateClient()
{
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) == 0)
    {
        AK_LOG_INFO << "UpdateRouteSrv begin";
        CRouteClientMng::Instance()->UpdateRouteSrv(csroute_addrs,  GetRouteLoopManagerInstance()->GetRouteLoop(), 
            std::bind(&CResidRouteClient::CreateClient, std::placeholders::_1, std::placeholders::_2));
    }
}


void CResidRouteClient::SetServerInfo(const std::string& id, AK::Base::LogicClientType type)
{
    logic_srv_id_ = id;
    logic_type_ = type;
} 

//csmain与csroute的tcp长连接消息,都是csroute的消息发往csmain
void CResidRouteClient::OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu)
{
    uint32_t msg_id = pdu->GetCommandId();
    uint16_t project = pdu->GetProjectType();
    
    ThreadLocalSingleton::GetInstance().SetTraceID(pdu->GetTraceId());
    AK_LOG_INFO << "receive csroute srv msg, pdumsg id:[" << std::hex << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(msg_id) << ", project type:[" <<  project << "]";

    if (AKCS_BUSSNESS_P2P_MSG == msg_id)
    {
        AK::BackendCommon::BackendP2PBaseMessage base_msg;
        CHECK_PB_PARSE_MSG(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
        
        if (RouteFactory::GetInstance()->DispatchMsg(base_msg.msgid(), pdu) == 0)
        {
            return;
        }
        
    }
    //通用
    if (RouteFactory::GetInstance()->DispatchMsg(msg_id, pdu) == 0)
    {
        return;
    }


    //住宅
    switch (msg_id)
    {
        //csroute主动发往各个逻辑服务器的消息
        case AKCS_MSG_R2L_PING_REQ:
        {
            OnRoutePing();
            return;
        }
        case AKCS_R2S_P2P_VOICE_MSG_ACK_REQ:
        {
            //CRoute2ResidMsg::HandleP2PVoiceMsgAckReq(pdu);
            break;
        }
        case AKCS_R2B_P2P_WEATHER_INFO_RESP:
        {
            //CRoute2ResidMsg::HandleP2PWeatherInfoMsg(pdu);
            break;
        }
        case AKCS_R2B_P2P_REFRESH_APP_USERCONF:
        {
            CRoute2ResidMsg::HandleP2PRefreshUserConfMsg(pdu);
            break;
        }
        case AKCS_R2B_P2P_PACPORT_UNLOCK_RESP:
        {
            CRoute2ResidMsg::HandleP2PPacportUnlockMsg(pdu);
            break;
        }
        /*
        case AKCS_R2B_P2P_REMOTE_OPENDOOR:
        {
            CRoute2ResidMsg::HandleP2PRemoteOpendoorMsg(pdu);
            break;
        }
        case AKCS_R2B_P2P_REMOTE_OPEN_SECURITY_RELAY:
        {
            CRoute2ResidMsg::HandleP2PRemoteOpenSecurityRelayMsg(pdu);
            break;
        }
        case AKCS_R2B_P2P_FROM_DEVICE_OPENDOOR_REQ:
        {
            CRoute2ResidMsg::HandleP2PFromDeviceOpenDoorReq(pdu);
            break;
        }
        */
        case AKCS_BUSSNESS_P2P_MSG:
        {            
            //OnP2PMessage(pdu);
            break;
        }
        default:
        {
            AK_LOG_WARN << "csroute srv msg,invalid pdumsg id " << msg_id << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(msg_id);
        }
    }
}

void CResidRouteClient::OnP2PMessage(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    switch (base_msg.msgid())
    {
        case AKCS_M2R_P2P_SEND_VOICE_MSG:
        {
            CRoute2ResidMsg::HandleP2PSendVoiceMsg(base_msg ,base_msg.p2psendvoicemsg2());
            break;
        }
        default:
        {
            AK_LOG_WARN << "p2p msg,invalid pdumsg id " << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());
        }
    }
 
}


