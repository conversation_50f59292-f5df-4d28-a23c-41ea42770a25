#include "EtcdCliMng.h"
#include "ResidRpcClientInit.h"
#include "VideoRecordClient.h"
#include "VideoRecordClientMng.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;

void GrpcClientInit()
{
    VideoRecordGrpcClientInit();
    return;
}

void VideoRecordGrpcClientInit()
{
    std::set<std::string> csvideo_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllVideoRecordRpcSrvs(csvideo_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "VideorRecord GrpcClientInit connetc to etcd srv fialed";
        return;
    }
    
    for (const auto& csvido_record_grpc_addr : csvideo_grpc_server_addrs)
    {
        auto csvideorecord_rpc_client = std::make_shared<VideoRecordRpcClient>(csvido_record_grpc_addr);
        VideoRecordClientMng::Instance()->AddVideoRecordRpcSrv(csvido_record_grpc_addr, csvideorecord_rpc_client);
    }

    return;
}