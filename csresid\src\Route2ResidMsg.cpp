#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "CachePool.h"
#include "util.h"
#include <boost/algorithm/string.hpp>
#include "AK.Linker.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "Route2ResidMsg.h"
#include "ResidInit.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/ProjectInfo.h"
#include "AkcsCommonSt.h"
#include "ProjectUserManage.h"
#include "ResidServer.h"
#include "MsgBuild.h"
#include "RouteMqProduce.h"
#include "MsgToControl.h"
#include "NotifyMsgControl.h"
#include "ResidDb.h"
#include "Resid2RouteMsg.h"
#include "AK.Resid.pb.h"
#include "NotifyPerText.h"
#include "ClientControl.h"
#include "MsgControl.h"
#include "Message.h"
#include "AK.Adapt.pb.h"
#include "AK.Linker.pb.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

CRoute2ResidMsg::CRoute2ResidMsg()
{

}
             

CRoute2ResidMsg::~CRoute2ResidMsg()
{

}

void CRoute2ResidMsg::HandleP2PVoiceMsgAckReq(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] p2p handle voice msg:" << msg.DebugString();

    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac),  msg.mac().c_str());
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id),  msg.filename().c_str());
    common_ack.result = msg.result();

    //回ack
    if (GetMsgToControlInstance()->SendCommonAckMsg(MSG_FROM_DEVICE_REPORT_VOICE_MSG, common_ack) != 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed";
        return;
    }

    //通知接收人
    CResid2RouteMsg::GroupVoiceMsg(pdu);
}

void CRoute2ResidMsg::HandleP2PSendVoiceMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const AK::Server::P2PSendVoiceMsg &msg)
{
    if (msg.receiver_type() == DEVICE_TYPE_APP)
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(msg.receiver_uuid(), per_account))
        {
            SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
            memset(&text_send.text_message, 0, sizeof(text_send.text_message));

            text_send.client_type = CPerTextNotifyMsg::APP_SEND;
            Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title),  "VOICE_MSG");
            text_send.text_message.id = msg.msg_id();
            ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "You have a voice message from %s", msg.location().c_str());
            text_send.text_message.type = CPerTextNotifyMsg::VOICE_MSG;
            CPerTextNotifyMsg cNotifyMsg(base, text_send, per_account.account);
            GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
        }
    }
    else if (msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
        memset(&online_msg, 0, sizeof(online_msg));
        online_msg.unread_voice_count = msg.count();
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(msg.receiver_uuid(), dev)
            && 0 != dbinterface::ResidentPerDevices::GetUUIDDev(msg.receiver_uuid(), dev))
        {
            AK_LOG_WARN << "HandleP2PSendVoiceMsg failed uuid not found. uuid:" << msg.receiver_uuid();
            return;
        }
        snprintf(online_msg.uuid, sizeof(online_msg.uuid), "%s", dev.uuid);
        snprintf(online_msg.mac, sizeof(online_msg.mac), "%s", dev.mac);        


        std::string xml_msg;
        GetMsgBuildHandleInstance()->BuildOnlineNotifyXmlMsg(online_msg, xml_msg);
        MsgStruct send_msg;
        ::memset(&send_msg, 0 , sizeof(send_msg));
        send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
        send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
        send_msg.msg_id = MSG_TO_DEVICE_ONLINE_NOTIFY_MSG;
        snprintf(send_msg.client, sizeof(send_msg.client), "%s", dev.mac);        
        snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), "%s", xml_msg.c_str());
        send_msg.msg_len = strlen(send_msg.msg_data);       
        GetClientControlInstance()->SendTransferMsg(send_msg);
    }
}

void CRoute2ResidMsg::HandleP2PWeatherInfoMsg(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Linker::LinkerWeatherNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] HandleP2PWeatherInfoMsg:" << msg.DebugString();
    
    SOCKET_MSG_DEV_WEATHER_INFO weather_msg;
    memset(&weather_msg, 0, sizeof(weather_msg));
    ::snprintf(weather_msg.mac, sizeof(weather_msg.mac), "%s", msg.mac().c_str());
    ::snprintf(weather_msg.city, sizeof(weather_msg.city), "%s", msg.city().c_str());
    ::snprintf(weather_msg.states, sizeof(weather_msg.states), "%s", msg.states().c_str());
    ::snprintf(weather_msg.country, sizeof(weather_msg.country), "%s", msg.country().c_str());
    ::snprintf(weather_msg.weather, sizeof(weather_msg.weather), "%s", msg.weather().c_str());    
    ::snprintf(weather_msg.humidity, sizeof(weather_msg.humidity), "%s", msg.humidity().c_str());
    ::snprintf(weather_msg.temperature, sizeof(weather_msg.temperature), "%s", msg.temperature().c_str());

    if (GetMsgToControlInstance()->SendDevWeatherInfoMsg(MSG_TO_DEVICE_REPORT_WEATHER_MSG, weather_msg) != 0)
    {
        AK_LOG_WARN << "SendDevWeatherInfoMsg failed";
        return;
    } 

    // 缓存到redis
    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_weather);
    if (cache_conn)
    {
        char weather_redis_key[256];
        char weather_redis_value[256];
        ::snprintf(weather_redis_key, sizeof(weather_redis_key), "%s-%s-%s", weather_msg.country, weather_msg.states, weather_msg.city);
        ::snprintf(weather_redis_value, sizeof(weather_redis_value), "%s!%s!%s", weather_msg.weather, weather_msg.temperature, weather_msg.humidity);

        cache_conn->set(weather_redis_key, weather_redis_value);
        cache_conn->expire(weather_redis_key, WEATHER_EXPIRE_SECOND);
        cache_manager->RelCacheConn(cache_conn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csresid " << g_redis_db_weather;
    }
}

void CRoute2ResidMsg::HandleP2PRefreshUserConfMsg(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PAdaptNotifyAppRefreshConfigMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[csroute] request refresh app conf msg:" << msg.DebugString();
    
    if (GetMsgControlInstance()->OnSendDevListChangeMsg(msg.account()) != 0)
    {
        AK_LOG_WARN << "OnSendDevListChangeMsg failed";
        return;
    }
}

void CRoute2ResidMsg::HandleP2PPacportUnlockMsg(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Linker::LinkerPacportCheckNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "HandleP2PPacportUnlockMsg:" << msg.DebugString();   

    SOCKET_MSG_PACPORT_UNLOCK_RES unlock_res;
    memset(&unlock_res, 0, sizeof(unlock_res));

    unlock_res.result = msg.result();
    Snprintf(unlock_res.mac, sizeof(unlock_res.mac), msg.mac().c_str());
    Snprintf(unlock_res.trace_id, sizeof(unlock_res.trace_id), msg.traceid().c_str());
    std::string room_num = msg.room_num();

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(unlock_res.mac, dev))
    {
        AK_LOG_WARN << "Get device info failed. mac:" << unlock_res.mac;
        return;
    }

    uint32_t room_id = 0;
    if (0 != dbinterface::CommunityRoom::GetRoomIDByUnitIDAndRoomNum(dev.unit_id, room_num, room_id))
    {
        AK_LOG_WARN << "Get room id failed. unit id:" << dev.unit_id << " room number:" << room_num;
        return;
    }

    std::string master_account;
    if (0 != dbinterface::CommPersonalAccount::GetNodeByRoomID(room_id, master_account))
    {
        AK_LOG_WARN << "Get master account failed. room id:" << room_id;
        return;
    }

    Snprintf(unlock_res.master_account, sizeof(unlock_res.master_account), master_account.c_str());

    //回复设备校验结果
    if (GetMsgToControlInstance()->SendDevPacportUnlockResMsg(MSG_TO_DEVICE_SEND_PACPORT_CHECK_RESULT, unlock_res))
    {
        AK_LOG_WARN << "SendDevPacportUnlockResMsg failed";
        return;
    }
    //校验失败 无需后续处理
    if (unlock_res.result == 0)
    {
        AK_LOG_INFO << "Pacport unlock check failed. no need to send message";
        return;
    }
    //校验成功 则向对应的房间发Message
    std::string message_title = "お宅配荷物が届きました";
    std::string message_content = "宅配荷物が部屋の前に置きました。お早めに受け取ってください";
    if (0 != SendMessageToRoom(dev.unit_id, room_num, message_title, message_content))
    {
        AK_LOG_WARN << "send Pacport unlock check message failed. mac is:" << unlock_res.mac;
        return;
    }
}

int CRoute2ResidMsg::SendMessageToRoom(uint32_t unit_id, const std::string& room_num, const std::string& message_title, const std::string& message_content)
{
    uint32_t room_id = 0;
    if(0 != dbinterface::CommunityRoom::GetRoomIDByUnitIDAndRoomNum(unit_id, room_num, room_id))
    {
        AK_LOG_WARN << "get room id failed, unit id: " << unit_id << ", room number: " << room_num;
        return -1;
    }

    std::string node;
    if(0 != dbinterface::CommPersonalAccount::GetNodeByRoomID(room_id, node))
   {
        AK_LOG_WARN << "get node failed, room id: " << room_id;
        return -1;
   }
   //主账号
    ResidentPerAccount master_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(node, master_account))
    {
        AK_LOG_WARN << "get main account failed, node: " << node;
        return -1;
    }

    //从账号列表获取
    ResidentPerAccountList sub_account_list;
    if(0 != dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_COMMUNITY_ATTENDANT, sub_account_list))
    {
        AK_LOG_WARN << "get sub account list failed, node: " << node;
        return -1;
    }
    //室内机
    ResidentDeviceList dev_list;
    dbinterface::ResidentDevices::GetNodeIndoorDevList(node, dev_list);

    CommPerTextMessage comm_text_msg;//消息通用部分
    memset(&comm_text_msg, 0, sizeof(comm_text_msg));
    Snprintf(comm_text_msg.title, sizeof(comm_text_msg.title), message_title.c_str());
    Snprintf(comm_text_msg.content, sizeof(comm_text_msg.content), message_content.c_str());
    comm_text_msg.project_type = project::RESIDENCE;
    //发送消息列表
    PerTextMessageSendList text_messages;

    //插入相关Message表并构造发送对象
    if (0 != dbinterface::Message::AddGroupTextMsg(CPerTextNotifyMsg::MessageType::TEXT_MSG, comm_text_msg, dev_list, master_account, sub_account_list ,text_messages))
    {
        AK_LOG_WARN << "add doorcom delivery dev text msg failed. ";
        return -1;
    }
    //消息推送给家庭下室内机/App
    CResid2RouteMsg::GroupDeliveryMsg(text_messages);

    return 0;
}

void CRoute2ResidMsg::HandleP2PDeliveryMsg(const AK::BackendCommon::BackendP2PBaseMessage &base, const AK::Server::P2PSendDeliveryMsg &msg)
{
    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), msg.title().c_str());
    Snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), msg.content().c_str());
    text_send.text_message.type = CPerTextNotifyMsg::MessageType::TEXT_MSG;
    text_send.text_message.id = msg.message_id();
    if(msg.receiver_type() == DEVICE_TYPE_APP)
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if(0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(msg.receiver_uuid(), per_account))
        {
            text_send.client_type = CPerTextNotifyMsg::APP_SEND;
            CPerTextNotifyMsg cNotifyMsg(base, text_send, per_account.account);
            GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
        }
    }
    else if(msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        if(0 != dbinterface::ResidentDevices::GetUUIDDev(msg.receiver_uuid(), dev)) //目前只做在社区
        {
            AK_LOG_WARN << "HandleP2PDoorcomDeliveryMsg failed, device uuid not found. uuid:" << msg.receiver_uuid();
            return;
        }
        char dclient_msg[4096];
        memset(dclient_msg, 0, sizeof(dclient_msg));
        if(0 != GetMsgBuildHandleInstance()->BuildTextMessageXmlMsg(dclient_msg, sizeof(dclient_msg), &text_send.text_message))
        {
            AK_LOG_WARN << "BuildTextMessageXmlMsg failed";
            return;
        }
        MsgStruct send_msg;
        memset(&send_msg, 0, sizeof(send_msg));
        send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
        send_msg.enc_type = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;
        send_msg.msg_id = MSG_TO_DEVICE_SEND_TEXT_MESSAGE;
        Snprintf(send_msg.client, sizeof(send_msg.client), dev.mac);
        Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), dclient_msg);
        send_msg.msg_len = strlen(send_msg.msg_data);
        GetClientControlInstance()->SendTransferMsg(send_msg);
    }
    return;
}

/*
void CRoute2ResidMsg::HandleP2PRemoteOpendoorMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "csresid receive open door msg from router:" << msg.DebugString();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), msg.msg_traceid().c_str());
    Snprintf(open_door.repost_mac, sizeof(open_door.repost_mac), msg.repost_mac().c_str());
    open_door.relay = static_cast<int>(msg.relay());

    HandleP2POpenDoorReq(&open_door, SOCKET_MSG_TYPE_NAME_OPENDOOR);
}

void CRoute2ResidMsg::HandleP2PRemoteOpenSecurityRelayMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::OpenSecurityRelayNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "csresid receive open SecurityRelay msg from router:" << msg.DebugString();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), msg.msg_traceid().c_str());
    Snprintf(open_door.repost_mac, sizeof(open_door.repost_mac), msg.repost_mac().c_str());
    open_door.relay = static_cast<int>(msg.security_relay());

    HandleP2POpenDoorReq(&open_door, SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY);
}

void CRoute2ResidMsg::HandleP2PFromDeviceOpenDoorReq(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainRequestOpenDoor msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "csresid receive open door msg from router:" << msg.DebugString();

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.mac, sizeof(open_door.mac), msg.mac().c_str());
    Snprintf(open_door.uid, sizeof(open_door.uid), msg.uid().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), msg.msg_traceid().c_str());
    open_door.relay = static_cast<int>(msg.relay());

    HandleP2POpenDoorReq(&open_door, msg.open_door_type());
}

void CRoute2ResidMsg::HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO* open_door, const std::string& open_door_type)
{
    if (open_door == NULL)
    {
        AK_LOG_WARN << "open_door is NULL";
        return;
    }

    int relay = open_door->relay;
    std::string uid = open_door->uid;
    std::string mac = open_door->mac;
    std::string accessible_floor = "0";              // 分号分割楼层, eg: "1;2;3;6;7;8;9;"
    std::string msg_traceid = open_door->trace_id;   // 
    std::string receiver_mac = strlen(open_door->repost_mac) > 0 ? (open_door->repost_mac) : (open_door->mac);

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(uid, sip_info);

    if (sip_info.sip_type == csmain::COMMUNITY_DEV)
    {
        ResidentDev dev;
        if (dbinterface::ResidentDevices::GetSipDev(uid, dev) == 0)
        {
            CommunityRoomInfo room;
            if (dbinterface::CommunityRoom::GetCommunityRoomByNode(dev.node, room) == 0)
            {
                accessible_floor = room.floor;
            }
        }
    }
    else if (sip_info.sip_type == csmain::COMMUNITY_APP)
    {
        std::string node;
        if (dbinterface::CommPersonalAccount::GetUidNode(uid, node) == 0)
        {
            // 社区用户多楼层
            accessible_floor = dbinterface::PersonalAccountCommunityInfo::GetFloorByAccount(uid);

            // 社区用户apt_floor
            CommunityRoomInfo room;
            if (dbinterface::CommunityRoom::GetCommunityRoomByNode(node, room) == 0)
            {
                accessible_floor = GetAccessibleFloor(room.floor, accessible_floor);
            }
        }
    }

    if (accessible_floor.length() == 0)
    {
        accessible_floor = "0";
    }

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remote_control_msg;
    memset(&remote_control_msg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remote_control_msg.protocal, sizeof(remote_control_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remote_control_msg.type, sizeof(remote_control_msg.type), open_door_type.c_str());
    Snprintf(remote_control_msg.item[0], sizeof(remote_control_msg.item[0]), uid.c_str());
    ::snprintf(remote_control_msg.item[1], sizeof(remote_control_msg.item[1]), "%d", relay);
    Snprintf(remote_control_msg.item[2], sizeof(remote_control_msg.item[2]), msg_traceid.c_str());
    ::snprintf(remote_control_msg.item[3], sizeof(remote_control_msg.item[3]), "%s", accessible_floor.c_str());
    ::snprintf(remote_control_msg.item[4], sizeof(remote_control_msg.item[4]), "%s", mac.c_str());

    AK_LOG_INFO << "Request open door uid=" << uid
        << ", mac=" << mac << ", repost_mac=" << open_door->repost_mac
        << ", traceid=" << msg_traceid << ", open_door_type=" << open_door_type
        << ", accessible_floor=" << accessible_floor;

    char xml_msg[4096];
    memset(xml_msg, 0, sizeof(xml_msg));
    GetMsgBuildHandleInstance()->BuildRemoteControlMsg(xml_msg, sizeof(xml_msg), &remote_control_msg);

    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.msg_id = MSG_TO_DEVICE_REMOTE_CONTROL;
    send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
    send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    Snprintf(send_msg.client, sizeof(send_msg.client), receiver_mac.c_str());
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), xml_msg);
    send_msg.msg_len = strlen(send_msg.msg_data);

    GetClientControlInstance()->SendTransferMsg(send_msg);
    return;
}
*/
