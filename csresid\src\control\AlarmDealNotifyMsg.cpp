#include "AlarmDealNotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "RouteMsg.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "SnowFlakeGid.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/ProjectUserManage.h"

namespace community
{
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target,
        AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // target type 转换成 TransP2PMsgType
        TransP2PMsgType type = TransP2PMsgType::TO_APP_UID;
        if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT ||
            target_type == AlarmNotifyTargetType::DEV_INDOOR ||
            target_type == AlarmNotifyTargetType::DEV_OUTDOOR)
        {
            type = TransP2PMsgType::TO_DEV_MAC;
        }

        // 消息转发
        AK::BackendCommon::BackendP2PBaseMessage base = CResid2RouteMsg::CreateP2PBaseMsg(
            AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG,
            type,
            target,
            CResid2RouteMsg::DevProjectTypeToDevType(project::RESIDENCE),
            project::RESIDENCE
        );

        msg.set_target(target);
        msg.set_target_type((int)target_type);
        base.mutable_p2palarmdealnotifymsg2()->CopyFrom(msg);
        IP2PToRouteMsg(&base);
    }

    void ProcessAlarmDealNotify(uint32_t mng_id, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        NotifyToIndoorDevByNode(msg.area_node(), msg);
        NotifyToPubMngDev(mng_id, msg);

        NotifyToPMApp(mng_id, msg);
        NotifyToUserAppByAccount(msg.area_node(), msg);
    }

    void NotifyToIndoorDevByNode(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历node下的室内机
        ResidentDeviceList dev_list;
        if (dbinterface::ResidentDevices::GetNodeIndoorDevList(node, dev_list) != 0)
        {
            AK_LOG_ERROR << "GetNodeIndoorDevList failed: node=" << node;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_INDOOR, dev.mac, msg);
        }
    }

    void NotifyToPubMngDev(uint32_t mng_id, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历项目下的所有管理机
        ResidentDeviceList dev_list;
        {
            if (dbinterface::ResidentDevices::GetAllMngDevList(mng_id, dev_list) != 0)
                AK_LOG_ERROR << "GetAllMngDevList failed: mng_account_id=" << mng_id;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_MANAGEMENT, dev.mac, msg);
        }
    }

    void NotifyToPMApp(uint32_t mng_id, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历项目下的所有PM APP
        ResidentPerAccountList pm_app_list;
        if (dbinterface::ResidentPersonalAccount::GetCommPmApplistByMngID(mng_id, pm_app_list) != 0)
        {
            AK_LOG_ERROR << "GetCommPmApplistByMngID failed: mng_account_id=" << mng_id;
            return;
        }
        if (pm_app_list.size() == 0)
        {
            AK_LOG_INFO << "pm app list is empty.";
            return;
        }

        // 转发告警处理通知
        std::map<std::string, std::string> pm_all_sites;
        dbinterface::PersonalAccountUserInfo::GetPmAllSitesByAppList(pm_app_list, pm_all_sites);
        auto it = pm_all_sites.begin();
        for (; it != pm_all_sites.end(); ++it)
        {
            std::string main_site = it->first;
            std::string pm_site = it->second;
            //校验实际站点账号是否异常
            if (dbinterface::ProjectUserManage::MultiSiteLimit(pm_site)) 
            {
                continue;
            }
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_PM, main_site, msg);
        }
    }

    void NotifyToUserAppByAccount(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 通知主从app
        std::set<std::string> app_list;
        if (0 != dbinterface::ResidentPersonalAccount::GetAttendantListByUid(node, app_list))
        {
            AK_LOG_ERROR << "Get room app list failed. node=" << node;
        }
        for (const auto& account : app_list)
        {
            // 校验实际站点账号是否为多套房账户且状态异常
            if (dbinterface::ProjectUserManage::MultiSiteLimit(account))
            {
                AK_LOG_INFO << "Community AlarmDealNotify App, MultiSiteLimit stop send mag to app, account = " << account;
                continue;
            }

            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_USER, account, msg);
            AK_LOG_INFO << "Community AlarmNotify Deal App, account = " << account;
        }
    }

}




