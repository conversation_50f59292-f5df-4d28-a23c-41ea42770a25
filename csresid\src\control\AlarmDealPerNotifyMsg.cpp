#include "AlarmDealPerNotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "RouteMsg.h"
#include "ClientControl.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "SnowFlakeGid.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/ProjectUserManage.h"

namespace personal
{
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target,
        AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // target type 转换成 TransP2PMsgType
        TransP2PMsgType type = TransP2PMsgType::TO_APP_UID;
        if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT ||
            target_type == AlarmNotifyTargetType::DEV_INDOOR ||
            target_type == AlarmNotifyTargetType::DEV_OUTDOOR)
        {
            type = TransP2PMsgType::TO_DEV_MAC;
        }

        // 消息转发
        AK::BackendCommon::BackendP2PBaseMessage base = CResid2RouteMsg::CreateP2PBaseMsg(
            AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG,
            type,
            target,
            CResid2RouteMsg::DevProjectTypeToDevType(project::PERSONAL),
            project::PERSONAL
        );

        msg.set_target(target);
        msg.set_target_type((int)target_type);
        base.mutable_p2palarmdealnotifymsg2()->CopyFrom(msg);
        IP2PToRouteMsg(&base);
    }

    void ProcessAlarmDealNotify(const std::string& mng_account, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        NotifyToPerIndoorDevByNode(msg.area_node(), msg);
        NotifyToUserAppByAccount(msg.area_node(), msg);
    }

    void NotifyToPerIndoorDevByNode(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 遍历node下的室内机设备
        ResidentDeviceList dev_list;
        if (dbinterface::ResidentPerDevices::GetNodeIndoorDevList(node, dev_list) != 0)
        {
            AK_LOG_ERROR << "GetNodeDevList failed: node=" << node;
            return;
        }

        // 转发告警处理通知
        for (const auto& dev : dev_list)
        {
            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::DEV_INDOOR, dev.mac, msg);
        }
    }

    void NotifyToUserAppByAccount(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg)
    {
        // 通知主从app
        std::set<std::string> app_list;
        if (0 != dbinterface::ResidentPersonalAccount::GetAttendantListByUid(node, app_list))
        {
            AK_LOG_ERROR << "Get room app list failed. node=" << node;
        }
        for (const auto& account : app_list)
        {
            // 校验实际站点账号是否为多套房账户且状态异常
            if (dbinterface::ProjectUserManage::MultiSiteLimit(account))
            {
                AK_LOG_INFO << "Personal AlarmDealNotify App, MultiSiteLimit stop send mag to app, account = " << account;
                continue;
            }

            ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType::APP_USER, account, msg);
            AK_LOG_INFO << "Personal AlarmNotify Deal App, account = " << account;
        }
    }

}




