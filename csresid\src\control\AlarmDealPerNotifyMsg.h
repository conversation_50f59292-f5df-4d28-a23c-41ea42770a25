#ifndef _CONTROL_ALARM_DEAL_PER_NOTIFY_MSG_H_
#define _CONTROL_ALARM_DEAL_PER_NOTIFY_MSG_H_
#include <string>
#include "RouteBase.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Resid2AppMsg.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/PersonalAlarm.h"

namespace personal
{
    // 消息转发到Router
    void ToRouteP2PAlarmDealNotify(AlarmNotifyTargetType target_type, const std::string& target,
        AK::Server::P2PAlarmDealNotifyMsg& msg);

    void ProcessAlarmDealNotify(const std::string& mng_account, AK::Server::P2PAlarmDealNotifyMsg& msg);
    // 通知Node下的所有 室内机
    void NotifyToPerIndoorDevByNode(const std::string& node, AK::Server::P2PAlarmDealNotifyMsg& msg);
    // 通知 UserAPP
    void NotifyToUserAppByAccount(const std::string& account, AK::Server::P2PAlarmDealNotifyMsg& msg);
}

#endif
