#ifndef __AK_EXCEPT_H__
#define __AK_EXCEPT_H__

#include <iostream>
#include <exception>
#include <string>

class MyException : public std::exception 
{
public:
    MyException() : message("Error."){}
    MyException(std::string str) : message("Error : " + str) {}
    ~MyException() throw ()
    {
    }

    virtual const char* what() const throw ()
    {
        return message.c_str();
    }

private:
    std::string message;
};

#endif