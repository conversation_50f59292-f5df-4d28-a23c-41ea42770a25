#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "BackendFactory.h"
#include "ResidServer.h"
#include "CsmainAES256.h"
#include "ClientControl.h"
#include "MsgBuild.h"
#include "Resid2RouteMsg.h"
#include "CoreUtil.h"
#include "MsgIdToMsgName.h"


extern ResidServer* g_resid_srv_ptr;

BackendFactory* BackendFactory::GetInstance()
{
    static BackendFactory handle;
    return &handle;
}

void BackendFactory::AddFunc(IBasePtr &ptr, FUNC_TYPE type, uint16_t msgid)
{
    if (type == FUNC_TYPE::DEV)
    {
        dev_funcs_[msgid] = std::move(ptr);
    }
    else if (type == FUNC_TYPE::APP)
    {
        app_funcs_[msgid] = std::move(ptr);
    }
    
    AK_LOG_INFO <<  "AddFunc type = " << type << ", msgid = " << msgid <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msgid);
}

int BackendFactory::DispatchMsg(const MsgStruct* acc_msg)
{
    SOCKET_MSG_NORMAL* normal_msg = (SOCKET_MSG_NORMAL*)(&acc_msg->msg_data);
    int message_id = normal_msg->message_id & SOCKET_MSG_ID_MASK;
    AK_LOG_INFO << "OnMainMsg recv msg. msgid:0x" << std::hex << message_id <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id);

    auto it = dev_funcs_.find(message_id);
    if (it != dev_funcs_.end())
    {
        AK_LOG_INFO << "found msgid:0x" << std::hex << message_id <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(message_id);

        ResidentDev dev;
        char *msg = nullptr;
        if (ParseDevMsgInfo(acc_msg, dev, &msg, it->second->EncType()) != 0)
        {
            AK_LOG_INFO << "ParseDevMsgInfo error";
            return 0;
        }

        try
        {
            IBasePtr p = std::move(it->second->NewInstance());     
            p->SetContext(dev);
            if (p->IParseXml(msg) != 0)
            {
                return -1;
            }
            
            if (p->IControl() != 0)
            {
                return -1;
            }
            
            std::string msg;
            uint16_t msg_id = 0;
            if (p->IReplyMsg(msg, msg_id) != 0)
            {
                return -1;
            }
            if (msg.size() > 0 && msg_id > 0)
            {            
                FactoryReplyDevMsg(dev, msg, msg_id, p->EncType());             
            }

            if (p->IPushNotify() != 0)
            {
                return -1;
            }

            std::string linkmsg;
            std::string key;
            uint32_t  link_id = 0;
            if (p->IPushThirdNotify(linkmsg, link_id, key) != 0)
            {
                return -1;
            }
            if (linkmsg.size() > 0 && link_id)
            {
                CResid2RouteMsg::SendLinKerCommonMsg((int)link_id, linkmsg, key);
            }            
            
            if (p->IToRouteMsg() != 0)
            {
                return -1;
            }

        }
        catch(MyException& e)
        {
            AK_LOG_WARN << "MyException " << e.what();
            return -1;
        }
        catch(std::exception& e)
        {
            //其他的错误
            AK_LOG_WARN << "MyException1 " <<e.what();
            return -1;
        }

        return 0;
    }

    auto it2 = app_funcs_.find(message_id);
    if (it2 != app_funcs_.end())
    {
        char *msg = nullptr;
        ResidentPerAccount account;
        if (ParseAppMsgInfo(acc_msg, account, &msg) != 0)
        {
            AK_LOG_INFO << "ParseAppMsgInfo error";
            return 0;
        }
        
        try
        {
            IBasePtr p = std::move(it2->second->NewInstance());     
            p->SetContext(account);
            if (p->IParseXml(msg) != 0)
            {
                return -1;
            }
            
            if (p->IControl() != 0)
            {
                return -1;
            }
            
            std::string msg;
            uint16_t msg_id = 0;
            if (p->IReplyMsg(msg, msg_id) != 0)
            {
                return -1;
            }
            
            if (p->IPushNotify() != 0)
                {
            return -1;
            }

            std::string linkmsg;
            std::string key;
            uint32_t  link_id = 0;
            if (p->IPushThirdNotify(linkmsg, link_id, key) != 0)
            {
                return -1;
            }
            if (linkmsg.size() > 0 && link_id)
            {
                CResid2RouteMsg::SendLinKerCommonMsg((int)link_id, linkmsg, key);
            }            

            if (p->IToRouteMsg() != 0)
            {
                return -1;
            }
        }
        catch(MyException& e)
        {
            AK_LOG_WARN << "MyException " << e.what();
            return -1;
        }
        catch(std::exception& e)
        {
            //其他的错误
            AK_LOG_WARN << "MyException1 " <<e.what();
            return -1;
        }
        return 0;
    }    
    return 1;
}

/*
程序启动时候自动注册到这里的类，是属于工具类，后续会为每个消息都重新new一个对象来处理
*/
void RegFunc(IBasePtr &f, BackendFactory::FUNC_TYPE type, uint16_t msgid)
{
    BackendFactory::GetInstance()->AddFunc(f, type, msgid);
    AK_LOG_INFO << "RegFunc msgid = " << msgid <<". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msgid);;
}






