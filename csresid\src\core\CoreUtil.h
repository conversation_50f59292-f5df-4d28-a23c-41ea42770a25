#ifndef _CORE_UTIL_H_
#define _CORE_UTIL_H_
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <cstdint>
#include "AkcsCommonDef.h"
#include "MsgStruct.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

int ParseDevMsgInfo(const MsgStruct* acc_msg, ResidentDev &dev, char **msg, MsgEncryptType enc_type);

int ParseAppMsgInfo(const MsgStruct* acc_msg, ResidentPerAccount& account, char **msg);

int BuildDclientMacEncMsg(const ResidentDev &dev, const std::string&msg,  uint16_t msgid, SOCKET_MSG &socket_message, MsgEncryptType enc_type);

int FactoryReplyDevMsg(const ResidentDev &dev, const std::string&msg, uint16_t msgid, MsgEncryptType enc_type);

#endif
