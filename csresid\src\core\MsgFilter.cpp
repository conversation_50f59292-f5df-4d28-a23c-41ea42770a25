#include "MsgFilter.h"
#include "util_string.h"
#include "AkLogging.h"
#include "ConfigFileReader.h"
#include "ResidInit.h"

std::string Int2HexString(int dec_num)
{
    std::stringstream hex_ss;
    hex_ss << std::hex << dec_num;
    return hex_ss.str();
}

FilterMsgManager::FilterMsgManager()
{
    ReadFilterMsgConf();//读取过滤消息配置
}

FilterMsgManager* FilterMsgManager::GetInstance()
{
    static FilterMsgManager filter_msg_manager_;
    return &filter_msg_manager_;
}

void FilterMsgManager::AddFilterMsgID(const std::string& msg_id_list)
{
    if(msg_id_list.size() == 0)
    {
        AK_LOG_WARN << "filter msg id list is empty";
        return;
    }

    std::set<std::string> filter_id_list;
    SplitString(msg_id_list, ",", filter_id_list);
    {
        std::lock_guard<std::mutex> lock(mutex_);
        for (const auto& filter_id : filter_id_list)
        {
            filter_msg_set_.insert(std::stoi(filter_id, 0, 16)); //十六进制字符串转存为十进制整数
        }
    }
    return;
}

void FilterMsgManager::RemoveFilterMsgID(const std::string& msg_id_list)
{
    if(msg_id_list.size() == 0)
    {
        AK_LOG_WARN << "filter msg id list is empty";
        return;
    }

    std::set<std::string> filter_id_list;
    SplitString(msg_id_list, ",", filter_id_list);
    {
        std::lock_guard<std::mutex> lock(mutex_);
        for (const auto& filter_id : filter_id_list)
        {
            filter_msg_set_.erase(std::stoi(filter_id, 0, 16));
        }
    }
    
    return;
}

std::string FilterMsgManager::GetFilterMsgID()
{
    std::string res;
    if(filter_msg_set_.size() == 0)
    {
        return res;
    }
    for(const auto& filter_msg : filter_msg_set_)
    {
        res += Int2HexString(filter_msg) + ",";
    }
    return res;
}

bool FilterMsgManager::CheckMsgIDInFilterList(int id)
{
    bool exist = false;

    const auto &it = filter_msg_set_.find(id);
    if(it != filter_msg_set_.end())
    {
        exist = true;
    }

    return exist;
}

void FilterMsgManager::ReadFilterMsgConf()
{
    std::set<std::string> filter_id_list;
    SplitString(gstAKCSConf.filter_msg_list, ",", filter_id_list);
    for (const auto& filter_id : filter_id_list)
    {
        filter_msg_set_.insert(std::stoi(filter_id, 0, 16)); //十六进制字符串转存为十进制整数
    }
}