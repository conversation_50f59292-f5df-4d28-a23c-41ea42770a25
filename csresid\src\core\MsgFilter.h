#ifndef __MSG_FILTER_H__
#define __MSG_FILTER_H__
#include <set>
#include <string>
#include <mutex>

//单例 管理过滤消息ID
class FilterMsgManager
{
public:
    FilterMsgManager();
    static FilterMsgManager* GetInstance();
    void AddFilterMsgID(const std::string& msg_id_list);
    void RemoveFilterMsgID(const std::string& msg_id_list);
    std::string GetFilterMsgID();
    bool CheckMsgIDInFilterList(int id);

private:
    void ReadFilterMsgConf();
	std::mutex mutex_;
    std::set<int> filter_msg_set_;
};

#endif