#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "msgparse/ParseDevRequestOpen.hpp"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "DeviceRequestOpenDoor.h"
#include "Resid2RouteMsg.h"
#include "DclientMsgDef.h"
#include "ClientControl.h"
#include "DeviceSetting.h"
#include "SnowFlakeGid.h"
#include "AkLogging.h"
#include "util_judge.h"
#include "MsgBuild.h"

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<DeviceRequestOpenDoor>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_OPENDOOR);
};

int DeviceRequestOpenDoor::IParseXml(char* msg)
{
    memset(&dev_request_open_, 0, sizeof(dev_request_open_));
    if (0 != akcs_msgparse::ParseDevRequestOpen(msg, dev_request_open_))
    {
        AK_LOG_WARN << "parse remote control ack msg failed.";
        return -1;
    }

    return 0;
}

int DeviceRequestOpenDoor::IControl()
{
    // 判断开门类型
    OpenDoorRelayType relay_type = OpenDoorRelayType::RELAY;
    if (strcmp(dev_request_open_.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_DOOR) == 0)
    {
        relay_type = OpenDoorRelayType::RELAY;
    }
    else if (strcmp(dev_request_open_.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_SECURITY_RELAY) == 0)
    {
        relay_type = OpenDoorRelayType::SECURITY_RELAY;
    }
    else
    {
        AK_LOG_WARN << "Open Door Type:" << dev_request_open_.type;
        return -1;
    }

    //权限校验
    memset(&conn_dev_, 0, sizeof(conn_dev_));
    memset(&target_dev_, 0, sizeof(target_dev_));
    if (GetDeviceSettingInstance()->GetDeviceSettingByMac(dev_request_open_.mac, target_dev_) < 0)
    {
        AK_LOG_WARN << "The device requested to open does not exist";
        return -1;
    }

    //基本开门校验
    conn_dev_ = GetDevicesClient();
    if (!IfCanDevOpenDoor(conn_dev_, target_dev_))
    {
        AK_LOG_WARN << "The device does not have permission. Indoor MAC:" << conn_dev_.mac
            << " request open MAC:" << dev_request_open_.mac;
        return -1;
    }

    // 构造traceid
    std::string trace_id = dev_request_open_.trace_id;
    if (trace_id.size() <= 0)
    {
        trace_id = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    }
    
    // 转发消息到路由
    AK::Server::P2POpenDoorNotifyMsg post_msg;
    post_msg.set_uid(conn_dev_.sip);
    post_msg.set_mac(dev_request_open_.mac);
    post_msg.set_relay(dev_request_open_.relay);
    post_msg.set_relay_type((int)relay_type);
    post_msg.set_project_type(target_dev_.project_type);
    post_msg.set_repost_mac("");            // 室内机开门没有转流
    post_msg.set_msg_traceid(trace_id);

    AK::BackendCommon::BackendP2PBaseMessage base = CResid2RouteMsg::CreateP2PBaseMsg(
        AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG,
        TransP2PMsgType::TO_DEV_MAC,
        dev_request_open_.mac,
        CResid2RouteMsg::DevProjectTypeToDevType(target_dev_.project_type),
        target_dev_.project_type
    );
    
    base.mutable_p2popendoornotifymsg2()->CopyFrom(post_msg);
    CResid2RouteMsg::PushMsg2Route(&base);
    return 0;
}

// 检查开门权限
bool DeviceRequestOpenDoor::IfCanDevOpenDoor(const ResidentDev& src_dev, const ResidentDev& target_dev_)
{
    //目标设备为项目设备
    if (target_dev_.conn_type == csmain::COMMUNITY_DEV)
    {
        //跨项目无法开门
        if (src_dev.project_mng_id != target_dev_.project_mng_id)
        {
            return false;
        }
        return true;
    }
    //目标设备为个人设备
    if (target_dev_.conn_type == csmain::PERSONNAL_DEV)
    {
        if (0 != strncmp(target_dev_.node, src_dev.node, sizeof(target_dev_.node)))
        {
            //不为同一单住户下的设备，不允许开门
            return false;
        }
        return true;
    }
    //类型错误 无法开门
    return false;
}

int DeviceRequestOpenDoor::IReplyMsg(std::string& msg, uint16_t& msg_id)
{
    //室内机和门口机Dclient版有小于6.5的直接回成功
    if (conn_dev_.dclient_ver < D_CLIENT_VERSION_6520 || !akjudge::DevSupportRemoteOpenDoorAck(conn_dev_.fun_bit))
    {
        msg_id = MSG_TO_DEVICE_OPENDOOR_ACK;

        // 构造ACK消息
        SOCKET_MSG_OPEN_DOOR_ACK_T open_door_ack;
        memset(&open_door_ack, 0, sizeof(open_door_ack));
        open_door_ack.result = 1;
        Snprintf(open_door_ack.protocal, sizeof(open_door_ack.protocal), "1.0");
        GetMsgBuildHandleInstance()->BuildOpenDoorAckMsg(open_door_ack, msg);
        AK_LOG_INFO << "Reply Open Door Ack:msgid=" << msg_id << ", msg=" << msg;
    }

    return 0;
}
