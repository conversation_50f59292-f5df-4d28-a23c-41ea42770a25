#include "IndoorReportUpdateDoorIP.h"
#include "DclientMsgDef.h"
#include "MsgParse.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "Resid2RouteMsg.h"
#include "AkcsMsgDef.h"
#include "msgparse/ParseIndoorReportUpdateDoorIP.hpp"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<IndoorReportUpdateDoorIP>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_UPDATE_DOOR_IP);
};

int IndoorReportUpdateDoorIP::IParseXml(char *msg)
{
    memset(&ip_info_, 0, sizeof(ip_info_));
    if (0 != akcs_msgparse::ParseIndoorReportUpdateDoorIP(msg, ip_info_))
    {
        AK_LOG_WARN << "parse indoor report update door IP msg failed.";
        return -1;
    }
    AK_LOG_INFO << "indoor report update door IP, report MAC : " << ip_info_.mac << " report IP: " << ip_info_.ip;
    return 0;
}

int IndoorReportUpdateDoorIP::IControl()
{
    ResidentDev indoor_dev = GetDevicesClient();
    //更新ip门口机的限定条件：门口机开启转流功能（IsRepost=1）且室内机和门口机在同一个小区
    dbinterface::ResidentDevices::GetMacDev(ip_info_.mac, door_dev_);
    if(door_dev_.repost && strcmp(door_dev_.project_uuid, indoor_dev.project_uuid) == 0)
    {
        //数据库状态字段更新
        if (0 != dbinterface::ResidentDevices::UpdateDevIPByMac(ip_info_.mac, ip_info_.ip))
        {
            AK_LOG_WARN << "update door IP failed. indoor_dev mac:" << indoor_dev.mac <<"door_dev mac" << door_dev_.mac;
            return -1;
        }
        AK_LOG_INFO << "update door IP success. " <<" door_dev mac: " << door_dev_.mac <<" door_dev ip: " << ip_info_.ip;
        return 0;
    }
    AK_LOG_WARN << "update door IP failed. dev isrepost: " << door_dev_.repost << " door_dev.project_uuid: " 
                << door_dev_.project_uuid << " indoor_dev.project_uuid: " << indoor_dev.project_uuid;
    return -1;
}

//设备的ip有变化时候，通知联动重新获取联系人。
int IndoorReportUpdateDoorIP::IToRouteMsg()
{
    CResid2RouteMsg::SendUpdateConfig(CSMAIN_UPDATE_CONFIG_IP_CHANGE, door_dev_.mac, project::RESIDENCE, ip_info_.ip);
    return 0;
}
