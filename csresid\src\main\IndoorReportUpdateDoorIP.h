#ifndef _REPORT_INDOOR_RELAY_STATUS_H_
#define _REPORT_INDOOR_RELAY_STATUS_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentDevices.h"

class IndoorReportUpdateDoorIP:public IBase
{
public:
    IndoorReportUpdateDoorIP(){}
    ~IndoorReportUpdateDoorIP() = default;

    int IParseXml(char *msg);
    int IControl();
    int IToRouteMsg();

    IBasePtr NewInstance() {return std::make_shared<IndoorReportUpdateDoorIP>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "IndoorReportUpdateDoorIP";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEV_REPORT_UPDATE_DOOR_IP ip_info_;
    ResidentDev door_dev_;

    int UpdateDoorIP(const SOCKET_MSG_DEV_REPORT_UPDATE_DOOR_IP& ip_info);
};

#endif //_REPORT_INDOOR_RELAY_STATUS_H_