#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/InterfaceComm.h"
#include "BackendFactory.h"
#include "AgentBase.h"
#include "KitRequestAccountLogout.h"
#include "ResidInit.h"
#include "RouteMqProduce.h"

extern AKCS_CONF gstAKCSConf;
extern RouteMQProduce* g_nsq_producer;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<KitRequestAccountLogoutMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_ACCOUNT_LOGOUT);
};


// 请求web注销账号
int KitRequestAccountLogoutMsg::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    ResidentDev dev = GetDevicesClient();
    AK_LOG_INFO << "OnKitRequestAccountLogout mac = " << dev.mac;
    char data[64];
    
    snprintf(data, sizeof(data), "Node=%s", dev.node);
    

    Json::Value item;
    Json::FastWriter fast_writer;
    item["project_type"] = dev.project_type;
    item["data"] = data;
    msg = fast_writer.write(item);
    msg_id = LinkerPushMsgType::LINKER_MSG_TYPE_KIT_REQUEST_ACCOUNT_LOGOUT;
    key = dev.node;
    return 0;
}

//删除apt内设备日志
int KitRequestAccountLogoutMsg::IToRouteMsg()
{
    ResidentDev dev_setting = GetDevicesClient();
    ResidentDeviceList dev_list;
    if (dev_setting.project_type == project::PERSONAL)
    {
        dbinterface::ResidentPerDevices::GetNodeDevList(dev_setting.node, dev_list);
    }
    else if (dev_setting.project_type == project::RESIDENCE)
    {
        dbinterface::ResidentDevices::GetNodeDevList(dev_setting.node, dev_list);
    }

    // 通知node下的设备删除日志 要分别走route
    for (const auto& dev : dev_list)
    {
        AK::Server::P2PSendRequestDevDelLog msg;
        msg.set_mac(dev.mac);
        
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_C2S_REQUEST_DEV_DEL_LOG);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic); 
    }
    return 0;
}



