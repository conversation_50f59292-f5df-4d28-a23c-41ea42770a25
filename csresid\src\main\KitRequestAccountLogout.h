#ifndef __DEVICE_ADD_KIT_DEVICES_MSG_H__
#define __DEVICE_ADD_KIT_DEVICES_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"


class KitRequestAccountLogoutMsg: public IBase
{
public:
    KitRequestAccountLogoutMsg(){};
    ~KitRequestAccountLogoutMsg() = default;

    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);
    int IToRouteMsg();
    IBasePtr NewInstance() {return std::make_shared<KitRequestAccountLogoutMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:    
    std::string func_name_ = "KitRequestAccountLogoutMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
};

#endif

