#pragma once
#include "IControl.h"
#include "json/json.h"

/**
 * 处理远程解锁通知的处理器
 * 负责接收来自 csroute 的远程解锁通知，并发送给 app
 */
class RemoteUnlockNotifyHandler : public IControl
{
public:
    RemoteUnlockNotifyHandler();
    virtual ~RemoteUnlockNotifyHandler();

    /**
     * 处理远程解锁通知消息
     */
    void IControl(const std::string& msg_data) override;

private:
    /**
     * 解析消息
     */
    bool ParseMessage(const std::string& msg_data, Json::Value& json_msg);
    
    /**
     * 发送通知给 app
     */
    void NotifyApp(const Json::Value& json_msg);
    
    /**
     * 构建发送给 app 的消息
     */
    void BuildAppMessage(const Json::Value& json_msg, std::string& app_msg);
    
    /**
     * 获取用户信息
     */
    bool GetUserInfo(const std::string& device_id, std::string& uid);
};
