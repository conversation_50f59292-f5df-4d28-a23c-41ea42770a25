#include "ReportAppArming.h"
#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include <string>
#include "DclientMsgSt.h"
#include "Resid2RouteMsg.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportAppArming>();
    RegFunc(p, BackendFactory::FUNC_TYPE::APP, MSG_FROM_APP_HANDLE_DEV_ARMING);
};

int ReportAppArming::IParseXml(char *msg)
{
    conn_account_ = GetResidentPerAccount();
    if (strlen(conn_account_.account) == 0)
    {
        AK_LOG_ERROR <<  "parse arming msg get account is null";
        return -1;
    }
    if (0 != CMsgParseHandle::ParseReqArmingMsg(msg, &arming_msg_))
    {
        AK_LOG_ERROR <<  "parse arming msg failed";
        return -1;
    }
    AK_LOG_INFO <<  " handle parse arming msg";
    return 0;
}

int ReportAppArming::IControl()
{
    AK::Server::P2PMainAppHandleArmingMsg msg;
    msg.set_mac(arming_msg_.mac);
    msg.set_action(arming_msg_.szAction);
    msg.set_uid(conn_account_.account);
    msg.set_mode(arming_msg_.mode);
    int project_type = conn_account_.conn_type == csmain::COMMUNITY_APP ? project::PROJECT_TYPE::RESIDENCE : project::PROJECT_TYPE::PERSONAL;

    AK::BackendCommon::BackendP2PBaseMessage base;
    base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ, TransP2PMsgType::TO_DEV_MAC, arming_msg_.mac,
                                                    CResid2RouteMsg::DevProjectTypeToDevType(project_type), project_type);
    base.mutable_p2pmainapphandlearmingmsg2()->CopyFrom(msg);
    CResid2RouteMsg::PushMsg2Route(&base);

    return 0;
}

int ReportAppArming::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    return 0;
}