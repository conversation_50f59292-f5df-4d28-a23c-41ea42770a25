#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "MsgBuild.h"
#include "MsgParse.h"
#include "MsgControl.h"
#include "ReportCallCapture.h"
#include "AkcsCommonDef.h"
#include "doorlog/UserInfo.h"
#include "dbinterface/Sip.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "doorlog/RecordActLog.h"
#include "util_judge.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportCallCapture>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_CALL_CAPTURE);
};

int ReportCallCapture::IParseXml(char* msg)
{
    memset(&call_capture_msg_, 0, sizeof(call_capture_msg_));
    return CMsgParseHandle::ParseReportCallCaptureMsg(msg, (void*)&call_capture_msg_);
}

bool ReportCallCapture::NeedRecord()
{
    // 被叫不记录,门口机不会上报
    // 门口机视频存储会上报，要记录
    // hager室内机上报的要记录
    ResidentDev dev = GetDevicesClient();
    if (call_capture_msg_.dialog_out == 0 
        && strlen(call_capture_msg_.video_record_name) == 0 
        && !akjudge::DevIndoorType(dev.dev_type))
    {
        AK_LOG_INFO << "device report call capture, not need record, mac = " << dev.mac;
        return false;
    }
    return true;
}

int ReportCallCapture::IControl()
{
    if (!NeedRecord())
    {
        return 0;
    }

    ResidentDev dev = GetDevicesClient();
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;
    memset(&act_msg, 0, sizeof(act_msg));

    if (RecordActLog::GetInstance().RewriteProjectInfo(act_msg, dev) != 0 ) 
    {
        AK_LOG_WARN << "RewriteProjectInfo error mac:" << dev.mac;
        return -1;
    }
    
    act_msg.mng_type = dev.is_personal;
    act_msg.unit_id = dev.unit_id;
    act_msg.is_public = dev.is_public;
    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CALL_CAPTURE;

    Snprintf(act_msg.key, sizeof(act_msg.key), "--");
    Snprintf(act_msg.mac, sizeof(act_msg.mac), dev.mac);
    Snprintf(act_msg.dev_uuid, sizeof(act_msg.dev_uuid), dev.uuid);
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");
    Snprintf(act_msg.account, sizeof(act_msg.account), dev.node);               //node
    Snprintf(act_msg.location, sizeof(act_msg.location), dev.location);
    Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account), dev.sip);
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), "Call");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");  //可能含有特殊字符
    Snprintf(act_msg.pic_name, sizeof(act_msg.pic_name), call_capture_msg_.picture_name);
    Snprintf(act_msg.call_trace_id, sizeof(act_msg.call_trace_id), call_capture_msg_.call_trace_id);
    Snprintf(act_msg.indoor_mac_list, sizeof(act_msg.indoor_mac_list), call_capture_msg_.indoor_mac_list);
    Snprintf(act_msg.video_record_name, sizeof(act_msg.video_record_name), call_capture_msg_.video_record_name);
    
    // call_trace_id 的前缀必须为Caller或者Callee的sip
    if (strlen(act_msg.call_trace_id) > 0 && (!strstr(act_msg.call_trace_id, call_capture_msg_.caller) && !strstr(act_msg.call_trace_id, call_capture_msg_.callee)))
    {
        AK_LOG_WARN << "device report call logs, call trace id error, mac = " << dev.mac << ", call_trace_id = " << act_msg.call_trace_id 
                    << ", caller = " << call_capture_msg_.caller << ", callee = " << call_capture_msg_.callee;
        Snprintf(act_msg.call_trace_id, sizeof(act_msg.call_trace_id), "");
    }

    AK_LOG_INFO << "mac = " << dev.mac << " report call capture. dialog_out = "<< call_capture_msg_.dialog_out
                << ", caller = " << call_capture_msg_.caller << ", callee = " << call_capture_msg_.callee
                << ", call_trace_id = " << call_capture_msg_.call_trace_id << ", video_record_name = " << call_capture_msg_.video_record_name;

    // 插入ip呼叫的室内机列表,用于室内机点播校验
    InsertIndoorIpCallVideoStorage();

    // dialog_out 呼出=1  呼入=0 呼出取被叫数据，呼入取主叫数据; target是被叫sip
    std::string node;
    std::string st_name;
    const char* target = call_capture_msg_.dialog_out ? call_capture_msg_.callee : call_capture_msg_.caller;
    if (strlen(target) > 0)
    {
        GetCallNodeInfo(target, node, st_name);
        if (!node.empty())
        {
            if (call_capture_msg_.dialog_out == 0 && !st_name.empty())
            {
                Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), st_name.c_str());//可能含有特殊字符
            }
            CNodeInfo cNodeCfg(node);
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num), cNodeCfg.getRoomNumber().c_str());
            Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
        }
    }

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personal capture failed.";
        return 0;
    }

    return 0;
}

int ReportCallCapture::GetCallNodeInfo(const std::string& sip, std::string& node, std::string& st_name)
{
    // 呼叫sip号
    if (sip.length() > 0)
    {
        //先检查群组
        node = dbinterface::Sip::GetNodeByGroupFromSip2(sip);

        // 查找人
        if (node.empty())
        {
            int manager_id = 0;
            dbinterface::ResidentPersonalAccount::GetNickNameAndNodeAndMngIDByUid(sip, st_name, node, manager_id);
        }

        // 查找设备
        if (node.empty())
        {
            ResidentDev resident_dev;
            if (dbinterface::ResidentDevices::GetDevicesBySip(sip, resident_dev) == 0)
            {
                node = resident_dev.node;
                st_name = resident_dev.location;
            } 
            else if (dbinterface::ResidentPerDevices::GetDevicesBySip(sip, resident_dev) == 0)
            {
                node = resident_dev.node;
                st_name = resident_dev.location;
            } 
        }
        
        if (node.empty())
        {
            AK_LOG_WARN << "there is illegal callee " << ", sip=" << sip;
            return -1;
        }
    }

    return 0;
}

void ReportCallCapture::InsertIndoorIpCallVideoStorage()
{
    if (strlen(call_capture_msg_.indoor_mac_list) > 0 && strlen(call_capture_msg_.call_trace_id) > 0)
    {    
        ResidentDev dev = GetDevicesClient();
        std::vector<std::string> indoor_mac_list;
        SplitString(call_capture_msg_.indoor_mac_list, ";", indoor_mac_list);

        for (const auto& indoor_mac : indoor_mac_list)    
        {
            ResidentDev indoor_info;
            if (0 == dbinterface::ResidentDevices::GetMacDev(indoor_mac, indoor_info) || 0 == dbinterface::ResidentPerDevices::GetMacDev(indoor_mac, indoor_info))
            {
                IndoorIpCallVideoStorageInfo indoor_ip_call_video_storage_info;
                Snprintf(indoor_ip_call_video_storage_info.indoor_devices_uuid, sizeof(indoor_ip_call_video_storage_info.indoor_devices_uuid), indoor_info.uuid);
                Snprintf(indoor_ip_call_video_storage_info.door_devices_uuid, sizeof(indoor_ip_call_video_storage_info.door_devices_uuid), dev.uuid);
                Snprintf(indoor_ip_call_video_storage_info.call_trace_id, sizeof(indoor_ip_call_video_storage_info.call_trace_id), call_capture_msg_.call_trace_id);
                
                dbinterface::IndoorIpCallVideoStorage::InsertIndoorIpCallVideoStorageInfo(indoor_ip_call_video_storage_info);
                AK_LOG_INFO << "IndoorIpCallVideoStorage call_trace_id = " << indoor_ip_call_video_storage_info.call_trace_id 
                            << ", door_devices_uuid = " << indoor_ip_call_video_storage_info.door_devices_uuid
                            << ", indoor_devices_uuid = " << indoor_ip_call_video_storage_info.door_devices_uuid;
            }
        }
    }

    return;
}

int ReportCallCapture::IReplyMsg(std::string& msg, uint16_t& msg_id)
{
    if (strlen(call_capture_msg_.call_trace_id) > 0)
    {
        msg_id = MSG_TO_DEVICE_ACK;
        GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_CALL_CAPTURE, call_capture_msg_.call_trace_id, msg);
    }
    return 0;
}

