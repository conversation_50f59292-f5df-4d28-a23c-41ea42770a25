#ifndef REPORT_CALL_CAPTURE_H_
#define REPORT_CALL_CAPTURE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "dbinterface/IndoorIpCallVideoStorage.h"

class ReportCallCapture: public IBase
{
private:
    SOCKET_MSG_CALL_CAPTURE call_capture_msg_;

    std::string func_name_ = "ReportCallCapture";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    int GetCallNodeInfo(const std::string& sip, std::string& node, std::string& st_name);
    void InsertIndoorIpCallVideoStorage();
    bool NeedRecord();
    
public:
    ReportCallCapture(){}
    ~ReportCallCapture() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string& msg, uint16_t& msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportCallCapture>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
};

#endif
