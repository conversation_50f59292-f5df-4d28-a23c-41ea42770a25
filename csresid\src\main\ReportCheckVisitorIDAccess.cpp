#include "ReportCheckVisitorIDAccess.h"
#include "DclientMsgDef.h"
#include "MsgParse.h"
#include "util_relay.h"
#include "DeviceCheck.h"
#include "MsgBuild.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "dbinterface/VisitorIDAccess.h"
#include "CommunityInfo.h" 
#include "util_time.h"

extern std::map<string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportCheckVisitorIDAccess>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_CHECK_VISITOR_IDACCESS);
};

int ReportCheckVisitorIDAccess::IParseXml(char *msg)
{
    memset(&report_check_id_access_, 0, sizeof(report_check_id_access_));
    if (0 != CMsgParseHandle::ParseCheckIDAccessMsg(msg, &report_check_id_access_))
    {
        AK_LOG_WARN << "parse check id access msg failed.";
        return -1;
    }
    AK_LOG_INFO << "report check id access. run=" << report_check_id_access_.id_access_run << " serial=" << report_check_id_access_.id_access_serial;
    return 0;
}

int ReportCheckVisitorIDAccess::IControl()
{
    //ID Access访客校验
    GetVisitorIDAccessCheckInfo();
    //获取校验结果
    GenerateCheckRes();
    //更新可用次数
    UpdateIDAccessAllowedTimes();

    return 0;
}

int ReportCheckVisitorIDAccess::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    msg_id = MSG_TO_DEVICE_CHECK_VISITOR_IDACCESS_ACK;

    GetMsgBuildHandleInstance()->BuildCheckVisitorIDAccessReplyMsg(check_id_access_res_, msg);

    return 0;
}

void ReportCheckVisitorIDAccess::GetVisitorIDAccessCheckInfo()
{
    check_visitor_id_access_info_.check_res = IDAccessCheck::CheckRes::CHECK_RES_SUCCESS;

    if (strlen(report_check_id_access_.id_access_run) == 0)
    {
        check_visitor_id_access_info_.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
        return;
    }
    
    //构造校验访客id access所需信息
    GenerateCheckAccessInfo();

    //Run Serial Mac校验
    if (0 != dbinterface::VisitorIDAccess::CheckVisitorIDAccess(check_visitor_id_access_info_))
    {
        AK_LOG_WARN << "check id access run serial failed";
        check_visitor_id_access_info_.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
        return;
    }

    //有效性校验
    CheckIDAccessValid();
}

void ReportCheckVisitorIDAccess::CheckIDAccessValid()
{
    //ScheType为Never时校验可用次数
    if (check_visitor_id_access_info_.sche_type == IDAccessCheck::SchedType::NEVER)
    {
        if (check_visitor_id_access_info_.access_times <= check_visitor_id_access_info_.allowed_times)
        {
            AK_LOG_WARN << "check id access allowed times failed.";
            check_visitor_id_access_info_.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
            return;
        }
    }


    //时间计划校验
    if (0 != dbinterface::VisitorIDAccess::CheckIDAccessPlanTime(check_visitor_id_access_info_))
    {
        AK_LOG_WARN << "check id access time failed.";
        check_visitor_id_access_info_.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
    }

    return;
}

void ReportCheckVisitorIDAccess::UpdateIDAccessAllowedTimes()
{
    //校验结果为失败或不是Never类型则无需更新
    if (check_visitor_id_access_info_.sche_type != IDAccessCheck::SchedType::NEVER
        || check_id_access_res_.check_res == IDAccessCheck::CheckRes::CHECK_RES_FAILED)
    {
        return;
    }

    dbinterface::VisitorIDAccess::UpdateIDAccessAllowedTimesByUUID(check_id_access_res_.visitor_uuid);
}

void ReportCheckVisitorIDAccess::GenerateCheckAccessInfo()
{
    //获取对应时区的当前时间
    ResidentDev dev = GetDevicesClient();
    CommunityInfo comm_info(dev.project_mng_id);
    std::string now_time_with_ymd = GetNodeNowDateTimeByTimeZoneStr(comm_info.TimeZone(), g_time_zone_DST);
    std::string now_time;
    int day_of_week = GetWeekDayAndTimeByTimeZoneStr(comm_info.TimeZone(), now_time, g_time_zone_DST);
    //针对周日做转换
    if (day_of_week == 0)
    {
        day_of_week = 7;
    }

    Snprintf(check_visitor_id_access_info_.run, sizeof(check_visitor_id_access_info_.run), report_check_id_access_.id_access_run);
    Snprintf(check_visitor_id_access_info_.serial, sizeof(check_visitor_id_access_info_.serial), report_check_id_access_.id_access_serial);
    Snprintf(check_visitor_id_access_info_.now_time_with_ymd, sizeof(check_visitor_id_access_info_.now_time_with_ymd), now_time_with_ymd.c_str());
    Snprintf(check_visitor_id_access_info_.now_time, sizeof(check_visitor_id_access_info_.now_time), now_time.c_str());
    check_visitor_id_access_info_.day_of_week = day_of_week;
    Snprintf(check_visitor_id_access_info_.mac, sizeof(check_visitor_id_access_info_.mac), dev.mac);
}

void ReportCheckVisitorIDAccess::GenerateCheckRes()
{
    //获取设备relay信息
    ResidentDev conn_dev = GetDevicesClient();
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(conn_dev.mac, dev))
    {
        AK_LOG_WARN << "mac not found in db. return";
        return;
    }
    int dev_relay_val = 0;
    int dev_se_relay_val = 0;
    GetValueByRelay(dev.relay, dev_relay_val);
    GetValueByRelay(dev.security_relay, dev_se_relay_val);

    //获取校验结果relay
    int res_relay_val = check_visitor_id_access_info_.relay_val & dev_relay_val;
    int res_se_relay_val = check_visitor_id_access_info_.se_relay_val & dev_se_relay_val;

    AK_LOG_INFO << "device relay:" << dev_relay_val << " id access relay:" << check_visitor_id_access_info_.relay_val;

    //relay赋值1234的形式
    Snprintf(check_id_access_res_.relay, sizeof(check_id_access_res_.relay), RelayToString(res_relay_val).c_str());
    Snprintf(check_id_access_res_.security_relay, sizeof(check_id_access_res_.security_relay), RelayToString(res_se_relay_val).c_str());
    Snprintf(check_id_access_res_.msg_seq, sizeof(check_id_access_res_.msg_seq), report_check_id_access_.msg_seq);
    Snprintf(check_id_access_res_.protocal, sizeof(check_id_access_res_.protocal), report_check_id_access_.protocal);
    Snprintf(check_id_access_res_.visitor_uuid, sizeof(check_id_access_res_.visitor_uuid), check_visitor_id_access_info_.visitor_uuid);
    check_id_access_res_.check_res = check_visitor_id_access_info_.check_res;
    Snprintf(check_id_access_res_.visitor_name, sizeof(check_id_access_res_.visitor_name), check_visitor_id_access_info_.name);
    GetInitiator();

    //兼容relay和security relay都为空
    if (strlen(check_id_access_res_.relay) == 0 && strlen(check_id_access_res_.security_relay) == 0)
    {
        check_id_access_res_.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
    }
}

void ReportCheckVisitorIDAccess::GetInitiator()
{
    //失败返回设备上报的Run Serial, 成功则根据实际校验的mode返回
    if (check_id_access_res_.check_res == IDAccessCheck::CheckRes::CHECK_RES_FAILED)
    {
        ::snprintf(check_id_access_res_.initiator, sizeof(check_id_access_res_.initiator), "%s %s", report_check_id_access_.id_access_run, report_check_id_access_.id_access_serial);
    }
    else if (check_id_access_res_.check_res == IDAccessCheck::CheckRes::CHECK_RES_SUCCESS)
    {
        if (check_visitor_id_access_info_.mode == IDAccessCheck::Mode::RUN)
        {
            ::snprintf(check_id_access_res_.initiator, sizeof(check_id_access_res_.initiator), "%s", report_check_id_access_.id_access_run);
        }
        else if (check_visitor_id_access_info_.mode == IDAccessCheck::Mode::RUN_SERIAL)
        {
            ::snprintf(check_id_access_res_.initiator, sizeof(check_id_access_res_.initiator), "%s %s", report_check_id_access_.id_access_run, report_check_id_access_.id_access_serial);
        }
    }
}