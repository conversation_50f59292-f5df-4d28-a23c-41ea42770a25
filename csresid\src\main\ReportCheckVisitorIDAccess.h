#ifndef _REPORT_CHECK_VISITOR_ID_ACCESS_H_
#define _REPORT_CHECK_VISITOR_ID_ACCESS_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"

class ReportCheckVisitorIDAccess: public IBase
{
public:
    ReportCheckVisitorIDAccess(){
    }
    ~ReportCheckVisitorIDAccess() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportCheckVisitorIDAccess>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportCheckVisitorIDAccess";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    void GetVisitorIDAccessCheckInfo();
    void GenerateCheckAccessInfo();
    void GenerateCheckRes();
    void GetInitiator();
    void UpdateIDAccessAllowedTimes();
    void CheckIDAccessValid();

    SOCKET_MSG_REPORT_CHECK_ID_ACCESS       report_check_id_access_;
    SOCKET_MSG_TO_DEVICE_CHECK_ID_ACCESS  check_id_access_res_;
    CheckVisitorIDAccessInfo                                check_visitor_id_access_info_;
};

#endif //_REPORT_CHECK_VISITOR_ID_ACCESS_H_