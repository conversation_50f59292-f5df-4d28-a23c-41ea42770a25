#include "ReportDeviceAlarm.h"
#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "json/json.h"
#include "util.h"
#include <string>
#include "DclientMsgSt.h"
#include "dbinterface/AlexaToken.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "Singleton.h"
#include "ResidInit.h"
#include "NotifyHttpReq.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "CommunityInfo.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "Resid2RouteMsg.h"
#include "DclientMsgDef.h"
#include "MsgBuild.h"
#include "SnowFlakeGid.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "ProjectUserManage.h"
#include "dbinterface/resident/CommunityEmergencyDoorGroup.h"

extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportDeviceAlarm>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_ALARM);
};

int ReportDeviceAlarm::IParseXml(char *msg)
{
    if (0 != CMsgParseHandle::ParseAlarmMsg(msg, &alarm_msg_))
    {
        AK_LOG_WARN <<  "parse alarm msg failed";
        return -1;
    }
    AK_LOG_INFO <<  " handle parse alarm msg";
    return 0;
}

int ReportDeviceAlarm::IControl()
{
    //由于未完全迁移 因此先丢弃其他类型告警
    if (alarm_msg_.alarm_code != (int)ALARM_CODE::EMERGENCY)
    {
        AK_LOG_INFO << "alarm type not handle there. alarm code=" << alarm_msg_.alarm_code;
        return -1;
    }

    //Alexa推送
    PostAlexaChangeStatus();
    
    //不进行alarm后续处理的判断
    if (!NeedHandleAlarm())
    {
        return -1;
    }

    //兼容旧设备
    OldDeviceAlarmTypeTransfer();

    //Alarm插入数据库
    if (0 != AddAlarmToDB())
    {
        AK_LOG_WARN << "add alarm to db failed.";
        return -1;
    }

    //根据Alarm类型进行消息分发
    if (0 != HandleAlarmMsgDistribution())
    {
        AK_LOG_WARN << "msg distribution failed.";
        return -1;
    }


    return 0;
}

void ReportDeviceAlarm::OldDeviceAlarmTypeTransfer()
{
    if (alarm_msg_.alarm_code == 0)
    {
        alarm_msg_.alarm_customize = 1; //自定义类型
    }
}

void ReportDeviceAlarm::PostAlexaChangeStatus()
{
    ResidentDev conn_dev = GetDevicesClient();

    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));

    //只推家庭下alarm
    if (strlen(conn_dev.node) == 0)
    {
        return;
    }

    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(conn_dev.node, node_info))
    {
        AK_LOG_WARN << "find account by node failed. node=" << conn_dev.node;
        return;
    }

    // 推送alarm状态给 Alexa
    dbinterface::AlexaTokenInfo alexa_token_info;
    if (0 == dbinterface::AlexaToken::GetAlexaTokenInfoByNodeUUID(node_info.uuid, alexa_token_info))
    {
        uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        PostAlexaChangeStatusHttpReq(conn_dev.mac, traceid);
        AK_LOG_INFO << "alexa device alarm notify web , mac :" << conn_dev.mac << ", traceid : " << traceid;
    }
}

bool ReportDeviceAlarm::NeedHandleAlarm()
{
    ResidentDev conn_dev = GetDevicesClient();
    //对于emergency_alarm，个人设备和非新社区设备不处理
    if (alarm_msg_.alarm_code == (int)ALARM_CODE::EMERGENCY)
    {
        CommunityInfo community_info(conn_dev.project_mng_id);
        if (conn_dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL || !community_info.GetIsNew())
        {
            return false;
        }
    }

    return true;
}

void ReportDeviceAlarm::PostAlexaChangeStatusHttpReq(const std::string& mac, uint64_t traceid)
{
    std::string data;
    Json::Value item;
    Json::FastWriter fast_writer;

    item["MAC"] = mac;
    item["TraceId"] = std::to_string(traceid);
    data = fast_writer.write(item);

    char url[128];
    snprintf(url, sizeof(url), "http://%s/alexaInner/v1/device/changeStatus", gstAKCSConf.smg_alexa_addr);

    CHttpReqNotifyMsg notify_msg(url, data, CHttpReqNotifyMsg::JSON);
    GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);
}

int ReportDeviceAlarm::AddAlarmToDB()
{
    ResidentDev conn_dev = GetDevicesClient();
    //社区设备
    if (conn_dev.conn_type == csmain::COMMUNITY_DEV)
    {
        //写入数据库
        ALARM alarm;
        memset(&alarm, 0, sizeof(alarm));
        Snprintf(alarm.device_node, sizeof(alarm.device_node) / sizeof(TCHAR), conn_dev.node);
        Snprintf(alarm.alarm_type, sizeof(alarm.alarm_type) / sizeof(TCHAR), alarm_msg_.type);
        alarm.alarm_code = alarm_msg_.alarm_code;
        alarm.alarm_location = alarm_msg_.alarm_location;
        alarm.alarm_zone = alarm_msg_.alarm_zone;
        alarm.alarm_customize = alarm_msg_.alarm_customize;

        alarm.status = AlarmStatus::ALARM_STATUS_UNDEALED;
        Snprintf(alarm.mac, sizeof(alarm.mac) / sizeof(TCHAR), conn_dev.mac);
        alarm.unit_id = conn_dev.unit_id;
        alarm.manager_account_id = conn_dev.project_mng_id;
        std::string node_time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(conn_dev.node, g_time_zone_DST);
        Snprintf(alarm.alarm_time, sizeof(alarm.alarm_time), node_time.c_str());
        uint64_t trace_id = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
        alarm.trace_id = trace_id;

        if (0 != dbinterface::Alarm::AddAlarm(&alarm, dbinterface::ProjectUserManage::GetServerTag()))
        {
            return -1;
        }

        alarm_msg_.alarm_id = alarm.id;
        Snprintf(alarm_msg_.alarm_uuid, sizeof(alarm_msg_.alarm_uuid), alarm.uuid);
    }

    //个人设备
    //TODO:紧急告警暂无单住户场景,插入数据库语句后续完全移到新框架后补充
    if (conn_dev.conn_type == csmain::PERSONNAL_DEV)
    {

    }

    return 0;
}

int ReportDeviceAlarm::HandleAlarmMsgDistribution()
{
    if (alarm_msg_.alarm_code == (int)ALARM_CODE::EMERGENCY)
    {
        EmergencyNotifyMsgDistribution();//一键开关门告警通知分发
        return EmergencyDoorControlMsgDistribution();
    }
    else
    {
        return AlarmNotifyMsgDistribution();
    }
}

void ReportDeviceAlarm::EmergencyNotifyMsgDistribution()
{
    //获取社区信息
    ResidentDev dev = GetDevicesClient();
    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountByUUID(dev.project_uuid, project_info))
    {
        AK_LOG_WARN << "get project info failed. project uuid=" << dev.project_uuid;
        return;
    }

    if (CheckEmergencyNeedNotify(project_info.id))
    {
        //一键开关门告警通知流程
        SendEmergencyNotify(project_info, PmEmergencyDoorControlType::CONTROL_TYPE_OPEN);
        InsertEmegencyNotifyAlarmLog(project_info, PmEmergencyDoorControlType::CONTROL_TYPE_OPEN);
    }
}

bool ReportDeviceAlarm::CheckEmergencyNeedNotify(int project_id)
{
    CommunityInfo comm_info(project_id);
    if (comm_info.InitSuccess() && comm_info.EmergencyNeedNotify() && comm_info.EnableAutoEmergency())
    {
        return true;
    }
    return false;
}

void ReportDeviceAlarm::SendEmergencyNotify(const dbinterface::AccountInfo& project_info, int control_type)
{
    AK::Server::P2PSendEmergencyNotifyMsg p2p_msg;
    p2p_msg.set_control_type(control_type);
    AK::BackendCommon::BackendP2PBaseMessage base;

    int project_type = project::PROJECT_TYPE::RESIDENCE;

    //获取要发送的App账号列表
    std::set<std::string> app_list;
    GetCommunityAllAppList(project_info.uuid, app_list);

    //App相关P2P消息发送
    for(const auto& account : app_list)
    {
        p2p_msg.set_receiver_uid(account);
        base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, account,
                                                    CResid2RouteMsg::DevProjectTypeToDevType(project_type), project_type);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
        CResid2RouteMsg::PushMsg2Route(&base);
    }

    //获取要发送的室内机列表
    std::set<std::string> indoor_dev_list;
    if (project_type == project::PROJECT_TYPE::RESIDENCE)
    {
        if(0 != dbinterface::ResidentDevices::GetAllAptIndoorDevices(project_info.id, indoor_dev_list))
        {
            return;
        }
    }

    //室内机相关P2P消息发送
    for(const auto& mac : indoor_dev_list)
    {
        p2p_msg.set_receiver_uid(mac);
        base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_DEV_MAC, mac,
                                                            CResid2RouteMsg::DevProjectTypeToDevType(project_type), project_type);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
        CResid2RouteMsg::PushMsg2Route(&base);
    }

    return;
}

int ReportDeviceAlarm::EmergencyDoorControlMsgDistribution()
{
    ResidentDev conn_dev = GetDevicesClient();
    uint32_t mng_id = conn_dev.project_mng_id;
    //判断是否开启auto_emergency
    CommunityInfo community_info(mng_id);
    if (!community_info.EnableAutoEmergency())
    {
        return 0;
    }
    
    std::string pm_uuid;
    std::string msg_uuid;
    std::string project_uuid;
    std::string initiator = "--";

    ResidentDeviceList pub_list;
    dbinterface::PmEmergencyDoorLogInfoList info_list;
    dbinterface::Account::GetUUIDByMngAccountId(mng_id, project_uuid);
    
    // 发送紧急告警推送web消息
    CResid2RouteMsg::SendEmergencyNotifyWebMsg(alarm_msg_.alarm_uuid, project_uuid);
    
    //插入PmEmergencyDoorLog表
    dbinterface::PmEmergencyDoorLog::InsertPmEmergencyDoorLog(dbinterface::ProjectUserManage::GetServerTag(), project_uuid, msg_uuid);

    if (0 != dbinterface::PmEmergencyDoorLog::InsertPmEmergencyDoorLogList(msg_uuid, project_uuid, project::PROJECT_TYPE::RESIDENCE, community_info.IsAllEmergencyDoor()))
    {
        AK_LOG_WARN << "InsertPmEmergencyDoorLogList failed. msg_uuid=" << msg_uuid << ", project_uuid=" << project_uuid;
        return -1;
    }

    //获取一键开门的设备信息
    dbinterface::PmEmergencyDoorLog::GetPmEmergencyDoorLogByUUID(msg_uuid, pm_uuid, info_list);

    if(info_list.empty())
    {
        AK_LOG_INFO << "PmEmergencyDoorControl UUID = " << msg_uuid << " PmEmergencyDoorLogList is null";
        return 0;
    }

    CResid2RouteMsg::SendP2PEmergencyDoorControlMsg(info_list, msg_uuid, initiator, ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK, project::PROJECT_TYPE::RESIDENCE);
   
    return 0;
}

int ReportDeviceAlarm::AlarmNotifyMsgDistribution()
{
    return 0;
}

int ReportDeviceAlarm::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    msg_id = MSG_TO_DEVICE_NOTIFY_ALARM_ACK;
    GetMsgBuildHandleInstance()->BuildAlarmAckMsg(alarm_msg_, msg);
    return 0;
}

void ReportDeviceAlarm::GetCommunityAllAppList(const std::string& project_uuid, std::set<std::string>& app_list)
{
    //获取社区下主账号列表
    std::set<std::string> node_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeListByProjectUUID(project_uuid, node_list))
    {
        AK_LOG_WARN << "Get node list failed. project_uuid=" << project_uuid;
        return;
    }
    
    for (const auto& node : node_list)
    {
        //获取社区下账号列表
        if (0 != dbinterface::ResidentPersonalAccount::GetAttendantListByUid(node, app_list))
        {
            AK_LOG_WARN << "Get room app list failed. node=" << node;
        }
    }
    return;
}

void ReportDeviceAlarm::InsertEmegencyNotifyAlarmLog(const dbinterface::AccountInfo& project_info, int control_type)
{
    //获取社区下主账号列表
    std::set<std::string> node_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeListByProjectUUID(project_info.uuid, node_list))
    {
        AK_LOG_WARN << "Get node list failed. project_uuid=" << project_info.uuid;
        return;
    }

    int alarm_code = 0;
    if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_OPEN)
    {
        alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_OPEN;
    }
    else if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_CLOSE)
    {
        alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_CLOSED;
    }

    ALARM alarm;
    memset(&alarm, 0, sizeof(alarm));
    alarm.alarm_code = alarm_code;
    alarm.manager_account_id = project_info.id;
    alarm.status = (uint32_t)AlarmStatus::ALARM_STATUS_UNDEALED;

    for (const auto& node : node_list)
    {
        Snprintf(alarm.device_node, sizeof(alarm.device_node), node.c_str());
        dbinterface::Alarm::AddAlarm(&alarm, dbinterface::ProjectUserManage::GetServerTag());
    }

    return;
}