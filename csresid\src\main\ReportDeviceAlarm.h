#ifndef _REPORT_DEVICE_ALARM_H_
#define _REPORT_DEVICE_ALARM_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/Account.h"
#include "dbinterface/AlarmDB.h"
class ReportDeviceAlarm: public IBase
{
public:
    ReportDeviceAlarm(){
    }
    ~ReportDeviceAlarm() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportDeviceAlarm>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportDeviceAlarm";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;//默认MAC加密
    SOCKET_MSG_ALARM alarm_msg_;
    ResidentDev conn_dev_;
    void OldDeviceAlarmTypeTransfer();
    bool NeedHandleEmergencyAlarm();
    int HandleAlarmMsgDistribution();
    int EmergencyDoorControlMsgDistribution();
    void EmergencyNotifyMsgDistribution();
    bool CheckEmergencyNeedNotify(int project_id);
    void SendEmergencyNotify(const dbinterface::AccountInfo& project_info, int control_type);
    void GetCommunityAllAppList(const std::string& project_uuid, std::set<std::string>& app_list);
    //TODO：完全迁移后实现
    int AlarmNotifyMsgDistribution();
    void InsertEmegencyNotifyAlarmLog(const dbinterface::AccountInfo& project_info, int control_type); 
};

#endif //_RESPONSE_EMERGENCY_CLOSE_DOOR_H_