#include <string>
#include "ReportDeviceAlarmDeal.h"

#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "json/json.h"
#include "util.h"
#include "DclientMsgSt.h"
#include "Singleton.h"
#include "NotifyHttpReq.h"
#include "CommunityInfo.h"
#include "DclientMsgDef.h"
#include "MsgBuild.h"
#include "SnowFlakeGid.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "ResidInit.h"
#include "Resid2RouteMsg.h"
#include "RouteFactory.h"
#include "NotifyAlarmDeal.h"


extern AKCS_CONF gstAKCSConf;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportDeviceAlarmDeal>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_PUT_ALARM_DEAL);
};

int ReportDeviceAlarmDeal::IParseXml(char* msg)
{
    if (msg == nullptr)
    {
        AK_LOG_ERROR << "msg is null.";
        return -1;
    }

    if (CMsgParseHandle::ParseAlarmDealMsg(msg, &alarm_deal_info_) < 0)
    {
        AK_LOG_ERROR << "Parse AlarmDealMsg failed.";
        return -1;
    }

    AK_LOG_INFO << "ReportDeviceAlarmDeal handle parse alarm msg: alarm_id=" << alarm_deal_info_.alarm_id
        << ", alarm_code=" << alarm_deal_info_.alarm_code
        << ", user=" << alarm_deal_info_.user
        << ", type=" << alarm_deal_info_.type
        << ", alarm_location=" << alarm_deal_info_.alarm_location;

    return 0;
}

int ReportDeviceAlarmDeal::IControl()
{
    dev_ = GetDevicesClient();
    CAlarmDealNotifyMsg async_msg(alarm_deal_info_, dev_);
    GetAlarmDealMsgProcessInstance()->AddAlarmDealMsg(async_msg);
    return 0;
}