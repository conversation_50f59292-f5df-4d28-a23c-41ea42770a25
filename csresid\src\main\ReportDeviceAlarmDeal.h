#ifndef _REPORT_DEVICE_ALARM_DEAL_H_
#define _REPORT_DEVICE_ALARM_DEAL_H_

#include <string>
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/Account.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "AK.Server.pb.h"

class ReportDeviceAlarmDeal : public IBase
{
public:
    ReportDeviceAlarmDeal() {}
    ~ReportDeviceAlarmDeal() = default;

    int IControl();
    int IParseXml(char* msg);
    int IReplyMsg(std::string& msg, uint16_t& msg_id) { return 0; }

    MsgEncryptType EncType() { return enc_type_; }
    std::string FuncName() { return "ReportDeviceAlarmDeal"; }
    IBasePtr NewInstance() { return std::make_shared<ReportDeviceAlarmDeal>(); }

private:
    SOCKET_MSG_ALARM_DEAL   alarm_deal_info_;       // 从设备上报的告警处理信息
    const MsgEncryptType    enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;//默认MAC加密
    ResidentDev dev_;
};

#endif //_REPORT_DEVICE_ALARM_DEAL_H_
