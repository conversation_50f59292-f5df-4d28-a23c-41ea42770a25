#ifndef _REPORT_DEVICE_ARMING_STATUS_H_
#define _REPORT_DEVICE_ARMING_STATUS_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonDef.h"
class ReportDeviceArmingStatus: public IBase
{
public:
    ReportDeviceArmingStatus(){
    }
    ~ReportDeviceArmingStatus() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportDeviceArmingStatus>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportDeviceArmingStatus";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;//默认加密方式
    SOCKET_MSG_DEV_ARMING arming_msg_;
    ResidentDev conn_dev_;
    void PostAlexaChangeStatus();
    void PostAlexaChangeStatusHttpReq(const std::string& mac, uint64_t traceid);
};

#endif //_REPORT_DEVICE_ARMING_STATUS_H_