#ifndef _REPORT_DEVICE_HANDLE_ARMING_H_
#define _REPORT_DEVICE_HANDLE_ARMING_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonDef.h"
class ReportDeviceHandleArming: public IBase
{
public:
    ReportDeviceHandleArming(){
    }
    ~ReportDeviceHandleArming() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReportDeviceHandleArming>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportDeviceHandleArming";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;//默认加密方式
    SOCKET_MSG_DEV_ARMING arming_msg_;
    ResidentDev conn_dev_;
};

#endif //_REPORT_DEVICE_HANDLE_ARMING_H_