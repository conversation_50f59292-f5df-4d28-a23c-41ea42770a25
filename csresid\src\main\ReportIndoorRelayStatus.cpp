#include "ReportIndoorRelayStatus.h"
#include "DclientMsgDef.h"
#include "MsgParse.h"
#include "util_relay.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"
#include "Resid2RouteMsg.h"
#include "DeviceCheck.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportIndoorRelayStatus>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_RELAY_STATUS);
};

int ReportIndoorRelayStatus::IParseXml(char *msg)
{
    memset(&indoor_relay_status_, 0, sizeof(indoor_relay_status_));
    if (0 != CMsgParseHandle::ParseReportIndoorRelayStatusMsg(msg, &indoor_relay_status_))
    {
        AK_LOG_WARN << "parse indoor relay status msg failed.";
        return -1;
    }
    AK_LOG_INFO << "report indoor relay status, report id list:" << indoor_relay_status_.relay_ids << " indoor relay type:" << indoor_relay_status_.relay_type;
    return 0;
}

int ReportIndoorRelayStatus::IControl()
{
    ResidentDev dev = GetDevicesClient();
    if (GetDeviceCheckInstance()->IsErrorFirmwareForChangeRelay(dev.sw_ver))
    {
		AK_LOG_WARN << "OnDevReportRelayStatus Firmware=" << dev.sw_ver << " is abnormal version. do noting";
        return -1;
    }
    //数据库状态字段更新
    if (0 != UpdateIndoorRelayStatus(indoor_relay_status_, dev.mac, dev.project_type))
    {
        AK_LOG_WARN << "indoor relay status update failed. dev mac:" << dev.mac;
        return -1;
    }
    return 0;
}

int ReportIndoorRelayStatus::IToRouteMsg()
{
    ResidentDev dev = GetDevicesClient();
    
    uint64_t relay_status = ExternDoornumToRelayStatus(indoor_relay_status_.relay_ids);
    CResid2RouteMsg::GroupIndoorRelayStatusMsg(dev.node, dev.mac, relay_status, indoor_relay_status_.relay_type, project::RESIDENCE);
    return 0;
}

int ReportIndoorRelayStatus::UpdateIndoorRelayStatus(const SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS& indoor_relay_status, const std::string& mac, int project_type)
{

    uint64_t relay_status = ExternDoornumToRelayStatus(indoor_relay_status.relay_ids);
    
    switch(indoor_relay_status.relay_type)
    {
        //本地relay
        case IndoorRelayType::TYPE_LOCAL:
        {
            return UpdateIndoorLocalRelayStatus(mac, project_type, relay_status);
        }    
        //外接relay
        case IndoorRelayType::TYPE_EXTERN:
        {
            return UpdateIndoorExternRelayStatus(mac, project_type, relay_status);
        }
    }
    AK_LOG_WARN <<  "report relay status type not support. type=" << indoor_relay_status.relay_type;
    return -1;
}

int ReportIndoorRelayStatus::UpdateIndoorLocalRelayStatus(const std::string& mac, int project_type, uint64_t relay_status)
{
    int ret = 0;
    if (project_type == project::RESIDENCE)
    {
        ret = dbinterface::ResidentDevices::SetDeviceRelayStatus(mac, relay_status);
    }
    else if (project_type == project::PERSONAL)
    {
        ret = dbinterface::ResidentPerDevices::SetDeviceRelayStatus(mac, relay_status);
    }
    else
    {
        AK_LOG_WARN << "project type no support, project type: " << project_type;
        ret = -1;
    }
    return ret;
}

int ReportIndoorRelayStatus::UpdateIndoorExternRelayStatus(const std::string& mac, int project_type, uint64_t relay_status)
{
    ResidentDev dev;
    int ret = 0;
    if (project_type == project::RESIDENCE)
    {
        ret = dbinterface::ResidentDevices::GetMacDev(mac, dev);
        if (ret != 0)
        {
            AK_LOG_WARN << "could not find the dev, mac is:" << mac;
            return -1;
        }
    }
    else if (project_type == project::PERSONAL)
    {
        ret = dbinterface::ResidentPerDevices::GetMacDev(mac, dev);
        if (ret != 0)
        {
            AK_LOG_WARN << "could not find the dev, mac is:" << mac;
            return -1;
        }
    }
    else
    {
        AK_LOG_WARN << "project type no support, project type: " << project_type;
        ret = -1;
    }

    std::string indoor_config_uuid;
    if (0 != dbinterface::IndoorMonitorConfig::GetIndoorMonitorConfigUUIDByDevUUID(dev.uuid, indoor_config_uuid))
    {
        AK_LOG_WARN << "could not find indoor config uuid, device uuid is:" << dev.uuid;
        return -1;
    }

    if (0 != dbinterface::ExtraDeviceRelayAction::SetDevicesExRelayStatus(indoor_config_uuid, relay_status))
    {
        AK_LOG_WARN << "update exrelay status failed. indoor config uuid:" << indoor_config_uuid << " relay_status:" << relay_status;
        return -1;
    }

    return ret;

}