#ifndef _REPORT_INDOOR_RELAY_STATUS_H_
#define _REPORT_INDOOR_RELAY_STATUS_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"

class ReportIndoorRelayStatus : public IBase
{
public:
    ReportIndoorRelayStatus(){}
    ~ReportIndoorRelayStatus() = default;

    int IParseXml(char *msg);
    int IControl();
    int IToRouteMsg();

    IBasePtr NewInstance() {return std::make_shared<ReportIndoorRelayStatus>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportIndoorRelayStatus";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS indoor_relay_status_;

    int UpdateIndoorRelayStatus(const SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS& indoor_relay_status, const std::string& mac, int project_type);
    int UpdateIndoorLocalRelayStatus(const std::string& mac, int project_type, uint64_t relay_status);
    int UpdateIndoorExternRelayStatus(const std::string& mac, int project_type, uint64_t relay_status);
};

#endif //_REPORT_INDOOR_RELAY_STATUS_H_