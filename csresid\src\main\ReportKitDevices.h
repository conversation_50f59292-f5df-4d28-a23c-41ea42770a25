#ifndef __REPORT_KIT_DEVICES_MSG_H__
#define __REPORT_KIT_DEVICES_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"


class ReportKitDevicesMsg: public IBase
{
public:
    ReportKitDevicesMsg(){};
    ~ReportKitDevicesMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg() {return 0;};
    void PushThirdNotify(const SOCKET_MSG_DEV_KIT_DEVICE &kit_device, const char *node, int command_id);
    bool AddKitDeviceDefaultValue(SOCKET_MSG_DEV_KIT_DEVICE &kit_device);

    IBasePtr NewInstance() {return std::make_shared<ReportKitDevicesMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
public:    
    std::string func_name_ = "ReportKitDevicesMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    std::vector<SOCKET_MSG_DEV_KIT_DEVICE> kit_devices_;
};

#endif

