#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "ReportMailboxArrivalNotice.h"
#include "dbinterface/InterfaceComm.h"
#include "AkcsPduBase.h"
#include "ResidInit.h"
#include "RouteMqProduce.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "msgparse/ParseReportMailboxArrivalNotice.hpp"

extern AKCS_CONF gstAKCSConf;
extern RouteMQProduce* g_nsq_producer;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportMailboxArrivalNotice>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_MAILBOX_ARRIVAL_NOTICE);
};

int ReportMailboxArrivalNotice::IParseXml(char *msg)
{
    if (0 != akcs_msgparse::ParseReportMailboxArrivalNotice(msg, mailbox_arrival_msg_))
    {
        AK_LOG_WARN << "Parse Report Mailbox Arrival Notice Msg failed";
        return -1;
    }
    AK_LOG_INFO << " Parse Report Mailbox Arrival Notice Msg success";
    return 0;
}

int ReportMailboxArrivalNotice::IControl()
{

    ResidentDev dev = GetDevicesClient();
    
    //获取主从账号
    std::vector<ResidentPerAccount> accounts;
    dbinterface::ResidentPersonalAccount::GetAttendantListUUIDByUid(mailbox_arrival_msg_.account, accounts);

    //获取室内机设备
    ResidentDeviceList devlist;
    if (dev.project_type == project::PROJECT_TYPE::RESIDENCE)
    {
        if (0 != dbinterface::ResidentDevices::GetNodeIndoorDevList(mailbox_arrival_msg_.account, devlist))
        {
            AK_LOG_WARN << "ReportMailboxArrivalNotice: indoor_devs not found.";
        }
    }
    else if(dev.project_type == project::PROJECT_TYPE::PERSONAL)
    {
        if (0 != dbinterface::ResidentPerDevices::GetNodeIndoorDevList(mailbox_arrival_msg_.account, devlist))
        {
            AK_LOG_WARN << "ReportMailboxArrivalNotice: indoor_devs not found.";
        }
    }
    else
    {
        AK_LOG_WARN << "ReportMailboxArrivalNotice: indoor_devs not found.";
    }
    if(dbinterface::Message::InsertMailBoxMessage(mailbox_arrival_msg_,text_messages_, accounts, devlist, dev.project_type) < 0)
    {
        AK_LOG_WARN << "Add Mailbox Arrival Msg Failed.";
        return -1;
    }
    AK_LOG_INFO << "Add Mailbox Arrival Msg success.";
    return 0;
}

int ReportMailboxArrivalNotice::IToRouteMsg()
{   
    AK::BackendCommon::BackendP2PBaseMessage base_msg; 
    base_msg.set_msgid(AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG);
    for (auto& text_send : text_messages_)
    {
        AK::Server::P2PCommonTxtMsgNotifyMsg msg;
        msg.set_client_type(text_send.client_type);
        msg.set_title(text_send.text_message.title);
        msg.set_uuid(text_send.uuid);
        msg.set_content(text_send.text_message.content);
        msg.set_msg_type(static_cast<int>(MessageType2::MAILBOX_ARRIVAL_MSG));
        base_msg.mutable_p2pcommontxtmsgnotifymsg2()->CopyFrom(msg);
        base_msg.set_uid(text_send.uid);
        base_msg.set_project_type(text_send.project_type);
        base_msg.set_type(text_send.type);

        CAkcsPdu pdu;
        pdu.SetMsgBody(&base_msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
        pdu.SetSeqNum(0);
 
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.route_topic);
    }     
    return 0;
}

