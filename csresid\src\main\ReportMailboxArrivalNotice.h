#ifndef _REQ_RESET_ROOM_H_
#define _REQ_RESET_ROOM_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "Message.h"

class ReportMailboxArrivalNotice: public IBase
{
public:
    ReportMailboxArrivalNotice(){};
    ~ReportMailboxArrivalNotice() = default;

    int IParseXml(char *msg);
    int IControl();
    int IToRouteMsg();
    IBasePtr NewInstance() {return std::make_shared<ReportMailboxArrivalNotice>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:
    std::string func_name_ = "ReportMailboxArrivalNotice";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    
    SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE mailbox_arrival_msg_;
    MailBoxMsgSendList text_messages_;
private:
    
};

#endif
