#include "ReportMotionAlert.h"
#include "DclientMsgDef.h"
#include "dbinterface/Log/PersonalMotion.h"
#include "util_time.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "Resid2RouteMsg.h"
#include "MsgParse.h"
#include "doorlog/RecordActLog.h"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportMotionAlert>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_MOTION_ALERT);
};

int ReportMotionAlert::IParseXml(char *msg)
{
    memset(&motion_report_info_, 0, sizeof(motion_report_info_));
    if (0 != CMsgParseHandle::ParseMotionAlertMsg(msg, &motion_report_info_))
    {
        AK_LOG_WARN << "parse motion alert msg failed.";
        return -1;
    }
    return 0;
}

int ReportMotionAlert::IControl()
{
    ResidentDev conn_dev = GetDevicesClient();

    AK_LOG_INFO << "device report motion alert msg. mac=" << conn_dev.mac;

    //将Motion信息写入数据库PersonalMotion表
    if (0 != WriteMotionInfoToDB())
    {
        AK_LOG_WARN << "Add personnal motion capture failed.";
        return -1;
    }

    //公共设备无需推送motion提醒
    bool is_public_dev = strlen(conn_dev.node) == 0;
    if (is_public_dev)
    {
        AK_LOG_INFO << "public device, no need to notify app";
        return 0;
    }

    SendMotionNotifyToNodeApp();

    return 0;
}

int ReportMotionAlert::WriteMotionInfoToDB()
{
    ResidentDev conn_dev = GetDevicesClient();

    bool is_comm_dev = conn_dev.conn_type == csmain::COMMUNITY_DEV;

    memset(&personal_capture_, 0, sizeof(personal_capture_));
    Snprintf(personal_capture_.mac, sizeof(personal_capture_.mac), conn_dev.mac);
    Snprintf(personal_capture_.dev_uuid, sizeof(personal_capture_.dev_uuid), conn_dev.uuid);
    Snprintf(personal_capture_.picture_name, sizeof(personal_capture_.picture_name), motion_report_info_.picture_name);
    Snprintf(personal_capture_.capture_time, sizeof(personal_capture_.capture_time), BaseGetCurTime().GetBuffer());
    Snprintf(personal_capture_.account, sizeof(personal_capture_.account), conn_dev.node);
    Snprintf(personal_capture_.location, sizeof(personal_capture_.location), conn_dev.location);
    Snprintf(personal_capture_.sip_account, sizeof(personal_capture_.sip_account), conn_dev.sip);
    Snprintf(personal_capture_.video_record_name, sizeof(personal_capture_.video_record_name), motion_report_info_.video_record_name);
    personal_capture_.manager_type = is_comm_dev ? 0 : 1; // 0=社区 1=单住户
    personal_capture_.manager_id = conn_dev.project_mng_id;
    personal_capture_.device_type = (strlen(conn_dev.node) > 0 ? 0 : 1); //0=个人设备 1=公共设备
    
    personal_capture_.detection_type = motion_report_info_.detection_type; // 0:移动侦测1:包裹检测2声音检测
    personal_capture_.detection_info = motion_report_info_.detection_info;
    if (RecordActLog::GetInstance().RewriteMotionProjectInfo(personal_capture_, conn_dev) != 0 ) 
    {
        AK_LOG_WARN << "RewriteProjectInfo error mac:" << conn_dev.mac;
        return -1;
    }

    return dbinterface::PersonalMotion::AddPersonalMotion(personal_capture_, gstAKCSLogDelivery.personal_motion_delivery);
}


void ReportMotionAlert::SendMotionNotifyToNodeApp()
{
    ResidentDev conn_dev = GetDevicesClient();
    std::vector<std::string> app_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeUidListByNode(conn_dev.node, app_list))
    {
        AK_LOG_WARN << "get node uid list by node failed. node=" << conn_dev.node;
        return;
    }

    bool is_comm_dev = conn_dev.conn_type == csmain::COMMUNITY_DEV;
    int project_type = is_comm_dev ? project::PROJECT_TYPE::RESIDENCE : project::PROJECT_TYPE::PERSONAL;
    std::string motion_time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(conn_dev.node, g_time_zone_DST);

    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    AK::Server::P2PSendMotionNotifyMsg p2p_msg;

    p2p_msg.set_mac(conn_dev.mac);
    p2p_msg.set_node(conn_dev.node);
    p2p_msg.set_dev_location(conn_dev.location);
    p2p_msg.set_motion_time(motion_time);
    p2p_msg.set_sip_account(conn_dev.sip);
    p2p_msg.set_id(personal_capture_.id);
    p2p_msg.set_detection_type(personal_capture_.detection_type);
    p2p_msg.set_detection_info(personal_capture_.detection_info);

    for(const auto& account : app_list)
    {
        p2p_msg.set_receiver_account(account);
        base_msg = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_MOTION_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, account,
                                        CResid2RouteMsg::DevProjectTypeToDevType(project_type), project_type);
        base_msg.mutable_p2psendmotionnotifymsg2()->CopyFrom(p2p_msg);
        CResid2RouteMsg::PushMsg2Route(&base_msg);
    }

    return;
}
