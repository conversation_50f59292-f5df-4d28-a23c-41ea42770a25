#ifndef _REPORT_MOTION_ALERT_H_
#define _REPORT_MOTION_ALERT_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"

class ReportMotionAlert : public IBase
{
public:
    ReportMotionAlert(){}
    ~ReportMotionAlert() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ReportMotionAlert>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ReportMotionAlert";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_MOTION_ALERT motion_report_info_;
    PERSONNAL_CAPTURE personal_capture_;

    int WriteMotionInfoToDB();
    void SendMotionNotifyToNodeApp();
};

#endif //_REPORT_INDOOR_RELAY_STATUS_H_