/*#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "ReportRelayStatus.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "ResidDb.h"
#include "MsgBuild.h"
#include "CachePool.h"
#include "json/json.h"

#include "ProjectUserManage.h"
#include "MsgToControl.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/PersonalAccountSingleInfo.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<RepRleayStatus>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_INPUT_STATUS);
};


int RepRleayStatus::IParseXml(char *msg)
{
    memset(&relay_status_msg_, 0, sizeof(SOCKET_MSG_RELAY_STATUS));
    if (CMsgParseHandle::ParseReportRelayStatusMsg(msg, (void *)&relay_status_msg_ )< 0)
    {
        AK_LOG_WARN << "ParseRelayStatusMsg failed.";
        return -1;
    }

    return 0;
}

int RepRleayStatus::IControl()
{
    //限流判断
    //if(already_check == 0)
    //{
    //    if(IsMsgLimit(acc_msg, InternalBussinessLimit::BussinessType::REPORT_RELAY))
    //    {
    //        return 0;
    //    }
    //}
    

    //对door relay status字段为空的情况进行兼容
    if(strlen(relay_status_msg_.door_relay_status) == 0)
    {
        snprintf(relay_status_msg_.door_relay_status, sizeof(relay_status_msg_.door_relay_status), "%s", "2222");
    }
    //对door security relay status字段为空的情况进行兼容
    if(strlen(relay_status_msg_.door_se_relay_status) == 0)
    {
        snprintf(relay_status_msg_.door_se_relay_status, sizeof(relay_status_msg_.door_se_relay_status), "%s", "22");
    }

    int ret = -1;
    ResidentDev conn_dev = GetDevicesClient();
    //将relay status更新到数据库 
    if(conn_dev.conn_type == csmain::DeviceType::COMMUNITY_DEV)
    {
        ret = dbinterface::ResidentDevices::UpdateDoorRelayStatus(conn_dev.mac,relay_status_msg_.door_relay_status, relay_status_msg_.door_se_relay_status);
    } else if(conn_dev.conn_type == csmain::DeviceType::PERSONNAL_DEV)
    {
        ret = dbinterface::ResidentPerDevices::UpdateDoorRelayStatus(conn_dev.mac,relay_status_msg_.door_relay_status, relay_status_msg_.door_se_relay_status);
    }
    if(ret == -1)
    {
        AK_LOG_WARN << "Update Relay Status failed. mac is " << conn_dev.mac; 
    }

    return 0;
}

int RepRleayStatus::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    ResidentDev conn_dev = GetDevicesClient();
    //根据解析的TraceID向设备回ACK
    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac),  conn_dev.mac);
    Snprintf(common_ack.msg_type, sizeof(common_ack.msg_type),  relay_status_msg_.msg_type);
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id),  relay_status_msg_.trace_id);
    common_ack.result = 1;


    msg_id = MSG_TO_DEVICE_ACK;
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_INPUT_STATUS, common_ack, msg);
    return 0;
}


int RepRleayStatus::IPushNotify()
{
    return 0;
}

int RepRleayStatus::IToRouteMsg()
{   
    return 0;
}


int RepRleayStatus::IPushThirdNotify()
{
    return 0;
}
*/

