
/* 这个不移动了，因为onconnChange也会用到，业务逻辑比较简单就不处理了
#ifndef _REP_RELAY_STATUS_INFO_H_
#define _REP_RELAY_STATUS_INFO_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class RepRleayStatus: public IBase
{
public:
    RepRleayStatus(){
    }
    ~RepRleayStatus() = default;


    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushNotify();
    int IToRouteMsg();
    int IPushThirdNotify();

    IBasePtr NewInstance() {return std::make_shared<RepRleayStatus>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:
    std::string func_name_ = "RepRleayStatus";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_RELAY_STATUS relay_status_msg_;

};

#endif
*/
