#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "MsgParse.h"
#include "MsgBuild.h"
#include "AkcsOemDefine.h"
#include "ReportTimeZone.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportTimeZone>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_TIMEZONE);
};

int ReportTimeZone::IParseXml(char *msg)
{
    return CMsgParseHandle::ParseReportTimeZoneMsg(msg, timezone_);
}

int ReportTimeZone::IControl()
{
    MacInfo mac_info;
    GetMacInfo(mac_info);
    
    // 不能从缓存中取设备信息 flags 可能被改了 但是缓存没改 (切换kit设备 flags变化了)
    ResidentDev request_dev;
    if (0 != dbinterface::ResidentPerDevices::GetMacDev(mac_info.mac, request_dev)) 
    {
        AK_LOG_WARN << "get device " << mac_info.mac << " from database error";
        return -1;
    }
    
    // 只有hager的设备能上报时区
    if (request_dev.oem_id != OEMID_HAGER)
    {
        AK_LOG_WARN << "device is not hager oem, oem_id = " << request_dev.oem_id << ", mac = " << mac_info.mac;
        return -1;
    }
    
    // 是否为kit设备
    if (!dbinterface::SwitchHandle(request_dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "device " << request_dev.mac << " report timezone error, dev not kit dev, flags = " << request_dev.flags;
        return -1;
    }

    // 查询主账号信息
    ResidentPerAccount node_info;
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(request_dev.node, node_info) || strlen(node_info.uuid) == 0)
    {
        AK_LOG_WARN << "device " << request_dev.mac << " report timezone error, get node info error, node = " << request_dev.node;
        return -1;
    }
    
    if (TransferTimezone())
    {
        dbinterface::ResidentPersonalAccount::UpdateNodeTimeZoneByUUID(node_info.uuid, timezone_);
    }
    
    return 0;
}

bool ReportTimeZone::TransferTimezone()
{
    if (!timezone_.empty() && timezone_.substr(0, 3) == "GMT")
    {
        timezone_ = timezone_.substr(3);
        return true;
    }
    return false;
}
