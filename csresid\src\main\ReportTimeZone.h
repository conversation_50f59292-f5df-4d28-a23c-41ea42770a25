#ifndef __REPORT_TIMEZONE_H__
#define __REPORT_TIMEZONE_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReportTimeZone: public IBase
{
public:
    ReportTimeZone(){}
    ~ReportTimeZone() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ReportTimeZone>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    bool TransferTimezone();

public:
    std::string func_name_ = "ReportTimeZone";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    std::string timezone_;
};

#endif
