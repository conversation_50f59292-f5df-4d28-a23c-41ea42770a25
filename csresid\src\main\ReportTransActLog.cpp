#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "MsgParse.h"
#include "MsgBuild.h"
#include "ReportTransActLog.h"
#include "ProjectUserManage.h"
#include "doorlog/RecordActLog.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/PersonalCapture.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportTransActLog>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_TRANS_ACTIVITY_LOGS);
};

int ReportTransActLog::IParseXml(char *msg)
{
    memset(&act_log_, 0, sizeof(act_log_));
    CMsgParseHandle::ParseReportTransActLogMsg(msg, &act_log_);
    AK_LOG_INFO <<  "report trans act log, act_type: " << act_log_.act_type << ",initiator:" << act_log_.initiator << ",location:" << act_log_.location;
    return 0;
}

int ReportTransActLog::IControl()
{
    MacInfo mac_info;
    GetMacInfo(mac_info);
    ResidentDev conn_dev = GetDevicesClient();  

    ResidentPerAccount per_account;
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(act_log_.initiator, per_account))
    {
        Snprintf(act_log_.room_num, sizeof(act_log_.room_num), per_account.room_number);
        Snprintf(act_log_.initiator_sql, sizeof(act_log_.initiator_sql), per_account.name);
    }
    
    act_log_.mng_id   = conn_dev.project_mng_id;
    act_log_.unit_id  = conn_dev.unit_id;
    act_log_.is_public = conn_dev.dev_type; 
    act_log_.mng_type = conn_dev.project_type;
    Snprintf(act_log_.key, sizeof(act_log_.key), "--");
    Snprintf(act_log_.mac, sizeof(act_log_.mac), conn_dev.mac);
    Snprintf(act_log_.account, sizeof(act_log_.account), conn_dev.node);
    Snprintf(act_log_.sip_account, sizeof(act_log_.sip_account), conn_dev.sip); 
    Snprintf(act_log_.capture_action, sizeof(act_log_.capture_action), open_door_on_app); // hager只有这种开门方式
    Snprintf(act_log_.db_delivery_uuid, sizeof(act_log_.db_delivery_uuid), mac_info.node_uuid); // log库里单住户的project_uuid是主账号的uuid
    
    // 插入数据库
    dbinterface::PersonalCapture::AddPersonalCapture(act_log_, gstAKCSLogDelivery.personal_capture_delivery);
    return 0;
}

int ReportTransActLog::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    msg_id = MSG_TO_DEVICE_ACK;
    
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_TRANS_ACTIVITY_LOGS, act_log_.msg_seq, msg);

    AK_LOG_INFO << "reply trans act log success, msg:" << msg;
    return 0;
}

int ReportTransActLog::IPushNotify()
{
    return 0;
}

int ReportTransActLog::IToRouteMsg()
{   
    return 0;
}


int ReportTransActLog::IPushThirdNotify()
{
    return 0;
}
