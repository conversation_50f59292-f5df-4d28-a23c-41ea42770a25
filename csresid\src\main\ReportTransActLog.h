#ifndef _REPORT_TRANS_ACT_LOG_H_
#define _REPORT_TRANS_ACT_LOG_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReportTransActLog: public IBase
{
public:
    ReportTransActLog(){}
    ~ReportTransActLog() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify();
    int IToRouteMsg();
    int IPushThirdNotify();

    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    IBasePtr NewInstance() {return std::make_shared<ReportTransActLog>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:
    std::string func_name_ = "ReportTransActLog";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEV_REPORT_ACTIVITY act_log_;
};

#endif
