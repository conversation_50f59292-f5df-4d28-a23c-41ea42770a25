#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "ReportVoiceMsg.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "ProjectUserManage.h"


__attribute__((constructor))  static void init(){
    IBasePtr p = std::make_shared<ReportVoiceMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_VOICE_MSG);
};


int ReportVoiceMsg::IParseXml(char *msg)
{
    memset(&voice_msg_, 0, sizeof(voice_msg_));
    CMsgParseHandle::ParseReportVoiceMsg(msg, &voice_msg_);

    return 0;
}

int ReportVoiceMsg::IControl()
{
    ResidentDev conn_dev = GetDevicesClient();  
    MacInfo info;
    GetMacInfo(info);
    
    snprintf(voice_msg_.project_uuid, sizeof(voice_msg_.project_uuid), "%s", info.project_uuid);
    
    std::string prefix = dbinterface::ProjectUserManage::GetServerTag() + "-";
    std::string msg_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, msg_uuid);
    snprintf(voice_msg_.uuid, sizeof(voice_msg_.uuid), "%s", msg_uuid.c_str());
    snprintf(voice_msg_.mac, sizeof(voice_msg_.mac), "%s", conn_dev.mac);
    snprintf(voice_msg_.dev_uuid, sizeof(voice_msg_.dev_uuid), "%s", conn_dev.uuid);
    snprintf(voice_msg_.location, sizeof(voice_msg_.location), "%s", conn_dev.location);
    if (0 != dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsg(voice_msg_))
    {
        AK_LOG_WARN << "InsertPersonalVoiceMsg failed.";
        return -1;
    }
    
    //插入插入PersonalVoiceMsgList
    if (voice_msg_.msg_type == VoiceMsgSendType::SEND_TO_FAMILY)
    {
        //查找家庭所有用户和室内机的uuid
        std::vector<COMMUNITY_DEVICE_SIP> apps;
        std::vector<PERSONNAL_DEVICE_SIP> per_apps;
        if (conn_dev.project_type == project::PERSONAL)
        {
            GetPersonalAppAndIndoorDevListByNode(voice_msg_.uid, per_apps);
            for (const auto& app : per_apps)
            {
                AddPersonalVoiceMsgNode(prefix, app.uuid, app.type);
            }
        }
        else
        {
            DaoGetCommunityDevListByNode(voice_msg_.uid, apps);
            for (const auto& app : apps)
            {
                AddPersonalVoiceMsgNode(prefix, app.uuid, app.type);
            }
        }
    }
    else if (voice_msg_.msg_type == VoiceMsgSendType::SEND_TO_INDOOR)
    {
        //下发给室内机
        ResidentDev dev;
        ResidentDev per_dev;
        if (0 == dbinterface::ResidentDevices::GetSipDev(voice_msg_.uid, dev))
        {
        AddPersonalVoiceMsgNode(prefix, dev.uuid, DEVICE_TYPE_INDOOR);
        }
        else if (0 == dbinterface::ResidentPerDevices::GetSipDev(voice_msg_.uid, per_dev))
        {
            AddPersonalVoiceMsgNode(prefix, per_dev.uuid, DEVICE_TYPE_INDOOR);
        }
    }
    else if (voice_msg_.msg_type == VoiceMsgSendType::SEND_TO_APP)
    {
        //下发给app
        ResidentPerAccount account_ptr;
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(voice_msg_.uid, account_ptr))
        {
            AddPersonalVoiceMsgNode(prefix, account_ptr.uuid, DEVICE_TYPE_APP);
        }
    }
    return 0; 
}

int ReportVoiceMsg::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    return 0;
}

int ReportVoiceMsg::IPushNotify()
{
    return 0;
}

int ReportVoiceMsg::IToRouteMsg()
{
    return 0;
}


void ReportVoiceMsg::AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, int type)
{
    PersonalVoiceMsgNode node;
    memset(&node, 0, sizeof(node));
    std::string tmp_uuid;
    dbinterface::ProjectUserManage::GetUUID(prefix, tmp_uuid);

    snprintf(node.receiver_uuid, sizeof(node.receiver_uuid), "%s", receiver_uuid.c_str());
    snprintf(node.uuid, sizeof(node.uuid), "%s", tmp_uuid.c_str());
    snprintf(node.msg_uuid, sizeof(node.msg_uuid), "%s", voice_msg_.uuid);
    node.type = type;

    dbinterface::PersonalVoiceMsg::InsertPersonalVoiceMsgList(node);
}


