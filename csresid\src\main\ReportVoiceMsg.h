#ifndef _REQORT_VOICE_H_
#define _REQORT_VOICE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReportVoiceMsg: public IBase
{
public:
    ReportVoiceMsg(){}
    ~ReportVoiceMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushNotify();
    int IToRouteMsg();
    
    IBasePtr NewInstance() {return std::make_shared<ReportVoiceMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    void AddPersonalVoiceMsgNode(const std::string& prefix, const std::string& receiver_uuid, int type);

    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    std::string func_name_ = "ReportVoiceMsg";
    PersonalVoiceMsgInfo voice_msg_;
};


#endif
