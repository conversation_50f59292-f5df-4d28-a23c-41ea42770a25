#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "SafeCacheConn.h"
#include "Resid2RouteMsg.h"
#include "BackendFactory.h"
#include "AgentBase.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/VersionModel.h"
#include "InterfaceComm.h"
#include "ResidInit.h"
#include "RequestAddKitDevices.h"
#include "msgparse/ParseReportKitDevicesMsg.hpp"
#include "AkcsHttpRequest.h"

extern AKCS_CONF gstAKCSConf;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<RequestAddKitDevicesMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_ADD_KIT_DEVICES);
};

int RequestAddKitDevicesMsg::IParseXml(char *msg)
{
    akcs_msgparse::ParseReportKitDevices(msg, kit_devices_);
    return 0;
}

int RequestAddKitDevicesMsg::IControl()
{
    ResidentDev device_setting = GetDevicesClient();
    //家庭是否为Kit方案
    if (!dbinterface::SwitchHandle(device_setting.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "Indoor is Not Kit device, did not resquest this interface. mac: " << device_setting.mac;
        return -1;
    }

    AK_LOG_INFO << "Begin Munual Add Kit Devices,size=" << kit_devices_.size();
    for (auto kit_device : kit_devices_)
    {
        if (0 == strlen(kit_device.mac))
        {
            continue;
        }

        if (!dbinterface::ResidentPerDevices::CheckKitDevice(kit_device.mac, device_setting.node))
        {
            continue;
        }

        int device_type = kit_device.type;
        if (device_type == DEVICE_TYPE_STAIR || device_type == DEVICE_TYPE_DOOR || device_type == DEVICE_TYPE_ACCESS)
        {
            Snprintf(kit_device.relay, sizeof(kit_device.relay), model::DEFAULT_DOOR_REALY.c_str());
        }
        else if (device_type == DEVICE_TYPE_INDOOR)
        {
            Snprintf(kit_device.relay, sizeof(kit_device.relay), model::DEFAULT_INDOOR_REALY.c_str());
        }
        else
        {
            AK_LOG_INFO << "Kit Device MAC=" << kit_device.mac << " device_type=" << kit_device.type << " is error";
            continue;
        }
        AK_LOG_INFO << kit_device.Print();
        PushThirdNotify(kit_device, device_setting.node, MSG_FROM_DEVICE_ADD_KIT_DEVICES);
    }

    AK_LOG_INFO << "Munual Add Kit Devices Success.";
    return 0;
}

void RequestAddKitDevicesMsg::PushThirdNotify(const SOCKET_MSG_DEV_KIT_DEVICE &kit_device, const char *node, int command_id)
{
    std::string data;
    char key_value[3000];

    snprintf(key_value, sizeof(key_value), "MAC=%s", kit_device.mac);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Node=%s", node);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Location=%s", URLEncode(kit_device.location).c_str());
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Type=%d", kit_device.type);
    data += key_value;
    snprintf(key_value, sizeof(key_value), "&Relay=%s", kit_device.relay);
    data += key_value;

    Json::Value item;
    Json::FastWriter w;
    item["data"] = data;
    std::string data_json = w.write(item);

    CResid2RouteMsg::SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_KIT_ADD_KIT_DEVICE, data_json, node);
}



