#ifndef __DEVICE_ADD_KIT_DEVICES_MSG_H__
#define __DEVICE_ADD_KIT_DEVICES_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"


class RequestAddKitDevicesMsg: public IBase
{
public:
    RequestAddKitDevicesMsg(){};
    ~RequestAddKitDevicesMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg() {return 0;};
    void PushThirdNotify(const SOCKET_MSG_DEV_KIT_DEVICE &kit_device, const char *node, int command_id);
   
    IBasePtr NewInstance() {return std::make_shared<RequestAddKitDevicesMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
public:    
    std::string func_name_ = "RequestAddKitDevicesMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    std::vector<SOCKET_MSG_DEV_KIT_DEVICE> kit_devices_;
};

#endif

