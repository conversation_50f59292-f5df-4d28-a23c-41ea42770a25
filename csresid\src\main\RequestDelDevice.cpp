#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "RequestDelDevice.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqDelDeviceMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_DELETE_MAC);
};

int ReqDelDeviceMsg::IParseXml(char *msg)
{
    CMsgParseHandle::ParseRequestDelDeviceMsg(msg, &del_devive_);
    AK_LOG_INFO <<  "on request del kit device, mac will be delete : " << del_devive_.mac << ", msg_seq : " << del_devive_.msg_seq;
    return 0;
}

int ReqDelDeviceMsg::IControl()
{
    return 0;
}

int ReqDelDeviceMsg::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    msg_id = MSG_TO_DEVICE_ACK;

    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REQUEST_DELETE_MAC, del_devive_.msg_seq, msg);
    
    AK_LOG_INFO << "reply request delete device success, msg:" << msg;
    return 0;
}

int ReqDelDeviceMsg::IPushNotify()
{
    return 0;
}

int ReqDelDeviceMsg::IToRouteMsg()
{   
    return 0;
}

int ReqDelDeviceMsg::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    MacInfo mac_info;
    GetMacInfo(mac_info);

    // 不能从缓存中取设备信息 flags 可能被改了 但是缓存没改 (切换kit设备 flags变化了)
    ResidentDev request_dev;
    if (0 != dbinterface::ResidentPerDevices::GetMacDev(mac_info.mac, request_dev)) 
    {
        AK_LOG_WARN << "get device " << mac_info.mac << " from database error";
        return -1;
    }
    
    // 请求设备是否为kit设备
    if (0 == dbinterface::SwitchHandle(request_dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {        
        AK_LOG_WARN << "device " << request_dev.mac << " request del device error, request dev not kit dev, flags = " << request_dev.flags 
                     << ", delete mac:" << del_devive_.mac;
        return -1;
    }

    // 获取被删除设备的信息
    ResidentDev del_dev_info;
    if (0 != dbinterface::ResidentPerDevices::GetMacDev(del_devive_.mac, del_dev_info))
    {
        AK_LOG_WARN << "device " << request_dev.mac << " request del device error, get delete device info error, delete mac:" << del_devive_.mac;
        return -1;     
    }

    // 被删除的设备不能是kit设备
    if (dbinterface::SwitchHandle(del_dev_info.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN <<  "device " << request_dev.mac << " request del device error, delete dev is kit dev, delete mac:" << del_devive_.mac;
        return -1; 
    }
    
    // 判断被删除的设备是否和kit设备在同个家庭下
    if (strncmp(request_dev.node, del_dev_info.node, sizeof(request_dev.node)) != 0)
    {
        AK_LOG_WARN << "kit mac and del mac not in the same room, request mac : " << request_dev.mac << ", del mac:" << del_devive_.mac;
        return -1;    
    }
    
    // 设置key
    key = request_dev.mac;
    
    // 设置msg_id
    msg_id = LinkerPushMsgType::LINKER_MSG_TYPE_KIT_DELETE_MAC;

    // 构造msg
    Json::Value item;
    Json::FastWriter writer;
    item["MAC"] = del_dev_info.mac;
    item["UUID"] = del_dev_info.uuid;
    item["ProjectUUID"] = mac_info.ins_uuid;  // hager设备删除后要重新加回ins的mac库
    msg = writer.write(item);
    
    return 0;
}
