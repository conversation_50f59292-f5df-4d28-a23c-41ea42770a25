#ifndef _REQ_DEL_DEVICE_H_
#define _REQ_DEL_DEVICE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

class ReqDelDeviceMsg: public IBase
{
public:
    ReqDelDeviceMsg(){}
    ~ReqDelDeviceMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify();
    int IToRouteMsg();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);
    
    IBasePtr NewInstance() {return std::make_shared<ReqDelDeviceMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:
    std::string func_name_ = "ReqDelDeviceMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEL_KIT_DEVICE del_devive_;
};

#endif
