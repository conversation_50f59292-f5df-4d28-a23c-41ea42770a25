#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "RequestDelRoom.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqDelRoomMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_DELETE_ROOM);
};

int ReqDelRoomMsg::IParseXml(char *msg)
{
    CMsgParseHandle::ParseRequestDelRoomMsg(msg, (void *)&del_room_msg_);
    AK_LOG_INFO <<  "on request del room msg, msg_seq:" << del_room_msg_.msg_seq;
    return 0;
}

int ReqDelRoomMsg::IControl()
{
    return 0;
}

int ReqDelRoomMsg::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    msg_id = MSG_TO_DEVICE_ACK;
    
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REQUEST_DELETE_ROOM, del_room_msg_.msg_seq, msg);

    AK_LOG_INFO << "reply request delete room success, msg:" << msg;

    return 0;
}

int ReqDelRoomMsg::IPushNotify()
{
    return 0;
}

int ReqDelRoomMsg::IToRouteMsg()
{   
    return 0;
}

int ReqDelRoomMsg::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    MacInfo mac_info;
    GetMacInfo(mac_info);
    
    // 不能从缓存中取设备信息 flags 可能被改了 但是缓存没改 (切换kit设备 flags变化了)
    ResidentDev request_dev;
    if (0 != dbinterface::ResidentPerDevices::GetMacDev(mac_info.mac, request_dev)) 
    {
        AK_LOG_WARN << "get device " << mac_info.mac << " from database error";
        return -1;
    }
    
    // 是否为kit设备
    if (!dbinterface::SwitchHandle(request_dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "device " << request_dev.mac << " request del room error, request dev not kit dev, flags = " << request_dev.flags;
        return -1;
    }
    
    // 查询主账号信息
    ResidentPerAccount node_info;
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(request_dev.node, node_info))
    {
        AK_LOG_WARN << "device " << request_dev.mac << " request del room error, get node info error, node = " << request_dev.node;
        return -1;
    }
    
    // 设置key
    key = request_dev.mac;

    // 设置msg_id
    msg_id = LinkerPushMsgType::LINKER_MSG_TYPE_KIT_DELETE_ROOM;

    // 构造msg
    Json::Value item;
    Json::FastWriter writer;
    item["UUID"] = node_info.uuid;
    msg = writer.write(item);
    
    return 0;
}
