#ifndef _REQ_DEL_ROOM_H_
#define _REQ_DEL_ROOM_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

class ReqDelRoomMsg: public IBase
{
public:
    ReqDelRoomMsg(){}
    ~ReqDelRoomMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify();
    int IToRouteMsg();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);

    IBasePtr NewInstance() {return std::make_shared<ReqDelRoomMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:
    std::string func_name_ = "ReqDelRoomMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    
    SOCKET_MSG_KIT_DEL_ROOM del_room_msg_;
private:
    
};

#endif
