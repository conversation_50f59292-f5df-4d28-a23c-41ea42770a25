#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/InterfaceComm.h"
#include "BackendFactory.h"
#include "AgentBase.h"
#include "RequestDeviceModifyLocation.h"
#include "ResidInit.h"
#include "msgparse/ParseReqModifyLocation.hpp"

extern AKCS_CONF gstAKCSConf;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReqDeviceModifyLocationMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_MODIFY_LOCATION);
};

int ReqDeviceModifyLocationMsg::IParseXml(char *msg)
{
    akcs_msgparse::ParseReqModifyLocation(msg, &kit_device_);
    return 0;
}

int ReqDeviceModifyLocationMsg::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    AK_LOG_INFO << "OnDeviceModifyLocation " << kit_device_.Print(); 
    ResidentDev dev = GetDevicesClient();
    
    std::string data = "MAC=" + std::string(kit_device_.mac) + 
                       "&Node=" + std::string(dev.node) + 
                       "&Location=" + URLEncode(kit_device_.location);

    Json::Value item;
    Json::FastWriter fast_writer;
    item["data"] = data;
    item["project_type"] = dev.project_type;
    msg = fast_writer.write(item);
    msg_id = LinkerPushMsgType::LINKER_MSG_TYPE_DEVICE_MODIFY_LOCATION;
    key = dev.node;
    return 0;
}



