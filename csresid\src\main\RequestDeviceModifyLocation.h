#ifndef __DEVICE_ADD_KIT_DEVICES_MSG_H__
#define __DEVICE_ADD_KIT_DEVICES_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"


class ReqDeviceModifyLocationMsg: public IBase
{
public:
    ReqDeviceModifyLocationMsg(){};
    ~ReqDeviceModifyLocationMsg() = default;

    int IParseXml(char *msg);
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);
   
    IBasePtr NewInstance() {return std::make_shared<ReqDeviceModifyLocationMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:    
    std::string func_name_ = "ReqDeviceModifyLocationMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_KIT_DEVICE kit_device_;
};

#endif

