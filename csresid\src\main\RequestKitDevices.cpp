#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/InterfaceComm.h"
#include "BackendFactory.h"
#include "AgentBase.h"
#include "RequestKitDevices.h"
#include "AkcsOemDefine.h"
#include "ResidInit.h"


extern AKCS_CONF gstAKCSConf;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<RequestKitDevicesMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_KIT_DEVICES);
};

int RequestKitDevicesMsg::IControl()
{
    ResidentDev dev = GetDevicesClient();

    if (dev.project_type == project::PERSONAL)
    {
        HandlePersonalKitRequestDevices(dev);
    }
    else if (dev.project_type == project::RESIDENCE)
    {
        HandleCommunityKitRequestDevices(dev);
    }
    
    AK_LOG_INFO << "OnDeviceRequestKitDevices success, node = " << dev.node << ", mac = " << dev.mac;
    return 0;
}

void RequestKitDevicesMsg::HandlePersonalKitRequestDevices(const ResidentDev& dev)
{    
    //家庭是否为Kit方案
    if (!dbinterface::SwitchHandle(dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "HandlePersonalKitRequestDevices, Not Kit device, can not resquest this interface. mac: " << dev.mac;
        return;
    }
    
    if (0 != dbinterface::ResidentPerDevices::GetNodeDevList(dev.node, kit_devices_))
    {
        AK_LOG_WARN << "HandlePersonalKitRequestDevices GetNodeDevList failed,node=" << dev.node;
        return;
    }
    
    AK_LOG_INFO << "HandlePersonalKitRequestDevices Node=" << dev.node << " has [" << kit_devices_.size() << "] kit devices.";
    return;
}

void RequestKitDevicesMsg::HandleCommunityKitRequestDevices(const ResidentDev& dev)
{
    if (dev.dev_type != DEVICE_TYPE_INDOOR || dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        AK_LOG_WARN << "HandleCommunityKitRequestDevices failed, dev type or grade error, mac = " << dev.mac << ", type = " << dev.dev_type << ", grade = " << dev.grade;
        return;
    }

    if (0 != dbinterface::ResidentDevices::GetNodeDevList(dev.node, kit_devices_))
    {
        AK_LOG_WARN << "HandleCommunityKitRequestDevices GetNodeDevList failed, mac = " << dev.mac << ", node = " << dev.node;
        return;
    }
     
    AK_LOG_INFO << "HandleCommunityKitRequestDevices Node=" << dev.node << " has [" << kit_devices_.size() << "] kit devices.";
    return;    
}

int RequestKitDevicesMsg::IReplyMsg(std::string &msg, uint16_t &msg_id)
{   
    msg_id = MSG_TO_DEVICE_REPORT_KIT_DEVICES;
    char payload[4096] = "";
    GetMsgBuildHandleInstance()->BuildReqKitDevices(payload, sizeof(payload), kit_devices_);
    msg = payload;
    return 0 ;
}

