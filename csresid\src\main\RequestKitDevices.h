#ifndef __DEVICE_ADD_KIT_DEVICES_MSG_H__
#define __DEVICE_ADD_KIT_DEVICES_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"


class RequestKitDevicesMsg: public IBase
{
public:
    RequestKitDevicesMsg(){};
    ~RequestKitDevicesMsg() = default;
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg() {return 0;};
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
   
    IBasePtr NewInstance() {return std::make_shared<RequestKitDevicesMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    void HandlePersonalKitRequestDevices(const ResidentDev& dev);
    void HandleCommunityKitRequestDevices(const ResidentDev& dev);
public:    
    std::string func_name_ = "RequestKitDevicesMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    std::vector<ResidentDev> kit_devices_;
};

#endif

