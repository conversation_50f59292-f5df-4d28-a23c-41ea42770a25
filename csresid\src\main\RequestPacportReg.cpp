#include "RequestPacportReg.h"
#include "MsgParse.h"
#include "AkcsCommonDef.h"
#include "dbinterface/CommunityInfo.h"
#include "util_string.h"
#include "json/json.h"
#include "util.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqPacportReg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_PACPORT_REGISTER);
};

int ReqPacportReg::IParseXml(char *msg)
{
    if (0 != CMsgParseHandle::ParsePacportRegMsg(msg, &report_reg_info_))
    {
        AK_LOG_WARN << "handle parse pacport reg request failed";
        return -1;
    }
    AK_LOG_INFO << " handle parse pacport reg request";
    return 0;
}

int ReqPacportReg::IControl()
{
    if (report_reg_info_.status != 0 && report_reg_info_.status != 1)
    {
        AK_LOG_WARN << "pacport req request status wrong. status:" << report_reg_info_.status;
        return -1;
    }
    ResidentDev conn_dev = GetDevicesClient();
    //非梯口机直接拦截
    if(conn_dev.grade != csmain::CommunityDeviceGrade::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        AK_LOG_WARN << "only support public unit device, device grade:" << conn_dev.grade;
        return -1;
    }
    AK_LOG_INFO << conn_dev.mac << " request pacport register, status: " << report_reg_info_.status;
    //注册需要获取设备地址信息
    if (report_reg_info_.status == 1)
    {
        if (0 != GetPacportDevRegInfo(conn_dev, reg_info_))
        {
            AK_LOG_WARN << "dev :" << conn_dev.mac << ", GetDevRegionInfo error";
            return -1;
        }
    }
    return 0;
}

int ReqPacportReg::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    Json::Value item;
    Json::FastWriter w;
    ResidentDev conn_dev = GetDevicesClient();
    key = std::string(conn_dev.mac);
    //注销
    if (report_reg_info_.status == 0)
    {
        item["mac"] = conn_dev.mac;
        msg_id = LINKER_MSG_TYPE_PACPORT_UNREGIST;
    }
    //注册
    else if (report_reg_info_.status == 1)
    {
        item["mac"] = conn_dev.mac;
        item["prefecture_name"] = reg_info_.prefecture_name;
        item["city_name"] = reg_info_.city_name;
        item["district_name"] = reg_info_.district_name;
        item["street_num"] = reg_info_.street_num;
        item["postal_code"] = reg_info_.postal_code;
        msg_id = LINKER_MSG_TYPE_PACPORT_REGIST;
    }
    msg = w.write(item);

    return 0;
}

int ReqPacportReg::GetPacportDevRegInfo(const ResidentDev& dev, SOCKET_MSG_PACPORT_REG_INFO& pacport_reg_info)
{
    memset(&pacport_reg_info, 0, sizeof(pacport_reg_info));
    Snprintf(pacport_reg_info.mac, sizeof(pacport_reg_info.mac), dev.mac);

    if (dev.project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << "device project type wrong. type:" << dev.project_type;
        return -1;
    }
    //根据社区获取相关地址信息
    CommunityInfo comm_info(dev.project_mng_id);
    if (!comm_info.InitSuccess())
    {
        AK_LOG_WARN << "cannot find device community, mngid:" << dev.project_mng_id;
        return -1;
    }

    Snprintf(pacport_reg_info.prefecture_name, sizeof(pacport_reg_info.prefecture_name), comm_info.States().c_str());
    Snprintf(pacport_reg_info.city_name, sizeof(pacport_reg_info.city_name), comm_info.City().c_str());
    Snprintf(pacport_reg_info.postal_code, sizeof(pacport_reg_info.postal_code), comm_info.PostalCode().c_str());
    std::string district_name;
    std::string street_num; //街道号
    //客户约定云上通过社区street填street_num 通过空格分隔
    SplitStringFromLastFilter(comm_info.Street(), " ", district_name, street_num);
    Snprintf(pacport_reg_info.district_name, sizeof(pacport_reg_info.district_name), district_name.c_str());
    Snprintf(pacport_reg_info.street_num, sizeof(pacport_reg_info.street_num), street_num.c_str());   
    return 0;
}