#ifndef _REQ_PACPORT_REG_H_
#define _REQ_PACPORT_REG_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"

class ReqPacportReg: public IBase
{
public:
    ReqPacportReg(){
    }
    ~ReqPacportReg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);

    IBasePtr NewInstance() {return std::make_shared<ReqPacportReg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    int GetPacportDevRegInfo(const ResidentDev& dev, SOCKET_MSG_PACPORT_REG_INFO& pacport_reg_info);

    std::string func_name_ = "ReqPacportReg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_PACPORT_REG_INFO reg_info_;
    SOCKET_MSG_REQ_PACPORT_REG report_reg_info_;
};

#endif //_REQ_PACPORT_REG_H_