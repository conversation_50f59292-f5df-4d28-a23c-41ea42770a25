#include "RequestPacportUnlock.h"
#include "MsgParse.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AkcsCommonDef.h"
#include "json/json.h"
#include "util.h"
#include <string>

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqPacportUnlock>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_PACPORT_CHECK_INFO);
};


int ReqPacportUnlock::IParseXml(char *msg)
{
    if (0 != CMsgParseHandle::ParsePacportUnlockMsg(msg, &report_unlock_check_info_))
    {
        AK_LOG_WARN <<  "parse pacport unlock msg failed";
        return -1;
    }
    AK_LOG_INFO <<  " handle parse pacport reg request";
    return 0;
}

int ReqPacportUnlock::IControl()
{
    ResidentDev conn_dev = GetDevicesClient();

    //非梯口机直接拦截
    if(conn_dev.grade != csmain::CommunityDeviceGrade::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        AK_LOG_WARN << "only support public unit device, device grade:" << conn_dev.grade;
        return -1;
    }
    GenerateUnlockCheckInfo();
    return 0;
}

void ReqPacportUnlock::GenerateUnlockCheckInfo()
{
    ResidentDev conn_dev = GetDevicesClient();
    //快递校验所需信息
    Snprintf(unlock_check_info_.mac, sizeof(unlock_check_info_.mac), conn_dev.mac);
    Snprintf(unlock_check_info_.room_num, sizeof(unlock_check_info_.room_num), report_unlock_check_info_.room_num);
    Snprintf(unlock_check_info_.courier_name, sizeof(unlock_check_info_.courier_name), report_unlock_check_info_.courier_name);
    Snprintf(unlock_check_info_.trace_id, sizeof(unlock_check_info_.trace_id), report_unlock_check_info_.trace_id);
    Snprintf(unlock_check_info_.tracking_num, sizeof(unlock_check_info_.tracking_num), report_unlock_check_info_.tracking_num);
}

int ReqPacportUnlock::IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key)
{
    Json::Value item;
    Json::FastWriter w;
    msg_id = LINKER_MSG_TYPE_PACPORT_UNLOCK_CHECK;
    ResidentDev conn_dev = GetDevicesClient();
    key = std::string(conn_dev.mac);
    item["mac"] = unlock_check_info_.mac;
    item["courier_name"] = unlock_check_info_.courier_name;
    item["tracking_num"] = unlock_check_info_.tracking_num;
    item["room_num"] = unlock_check_info_.room_num;
    item["trace_id"] = unlock_check_info_.trace_id;
    msg = w.write(item);
    return 0;
}