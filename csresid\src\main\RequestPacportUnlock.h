#ifndef _REQ_PACPORT_UNLOCK_H_
#define _REQ_PACPORT_UNLOCK_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"

class ReqPacportUnlock: public IBase
{
public:
    ReqPacportUnlock(){
    }
    ~ReqPacportUnlock() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);

    IBasePtr NewInstance() {return std::make_shared<ReqPacportUnlock>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    void GenerateUnlockCheckInfo();
    std::string func_name_ = "ReqPacportUnlock";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_REQ_PACPORT_UNLOCK      report_unlock_check_info_;
    SOCKET_MSG_PACPORT_UNLOCK_CHECK unlock_check_info_;
};

#endif //_REQ_PACPORT_UNLOCK_H_