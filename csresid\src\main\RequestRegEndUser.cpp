#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "SafeCacheConn.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/PendingRegUser.h"
#include "BackendFactory.h"
#include "AgentBase.h"
#include "RequestRegEndUser.h"
#include "AkcsOemDefine.h"
#include "dbinterface/CommunityPendingRegUser.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/CommunityRoom.h"
#include "ResidInit.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityInfo.h"


extern AKCS_CONF gstAKCSConf;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<RequestRegEndUserMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_END_USER_REG);
};


int RequestRegEndUserMsg::IControl()
{
    ResidentDev dev = GetDevicesClient();

    if (dev.project_type == project::PERSONAL)
    {
        HandlePersonalKitRegister(dev);
    }
    else if (dev.project_type == project::RESIDENCE)
    {
        HandleCommunityKitRegister(dev);
    }
    return 0;
}

void RequestRegEndUserMsg::HandlePersonalKitRegister(const ResidentDev& dev)
{
    // 判断是否为kit设备
    if (!dbinterface::SwitchHandle(dev.flags, DeviceSwitch::INDOOR_IS_KIT))
    {
        AK_LOG_WARN << "HandlePersonalKitRegister, Indoor is Not Kit device, cannot resquest this interface, mac = " << dev.mac;
        return;
    }

    // 获取kit注册信息
    reg_user_info_.user_projcet_type = dev.project_type;
    if (0 != dbinterface::PendingRegUser::GetPendingRegUserInfoByMac(dev.mac, reg_user_info_))
    {
        AK_LOG_WARN << "HandlePersonalKitRegister GetPendingRegUserInfoByMac failed, mac = " << dev.mac;
        return;
    }

    // 更新token
    std::string token = GetNbitRandomString(18);
    Snprintf(reg_user_info_.token, sizeof(reg_user_info_.token), token.c_str());
    if (0 != dbinterface::PendingRegUser::UpdatePendingRegUserToken(dev.mac, token))
    {
        AK_LOG_WARN << "HandlePersonalKitRegister UpdatePendingRegUserToken failed, mac = " << dev.mac;
        return;
    }
    
    AK_LOG_INFO << "personal indoor request kit register success, mac = " << dev.mac << ", account = " << reg_user_info_.account << ", token = " << reg_user_info_.token;
    return;
}

void RequestRegEndUserMsg::HandleCommunityKitRegister(const ResidentDev& dev)
{
    Snprintf(reg_user_info_.mac, sizeof(reg_user_info_.mac), dev.mac);
    Snprintf(reg_user_info_.account, sizeof(reg_user_info_.account), dev.node);

    //兼容网页上填写邮箱/手机号注册的场景
    ResidentPerAccount per_account;
    reg_user_info_.user_projcet_type = dev.project_type;
    dbinterface::ResidentPersonalAccount::GetUidAccount(reg_user_info_.account, per_account);
    if (strlen(per_account.getEmail()) > 0 || strlen(per_account.getMobileNumber()) > 0)
    {
        //用户填写了邮箱或手机号都下发用户信息
        reg_user_info_.status = (int)KitEndUserDisplayStatus::USER_REG_INFO;
    }
    else 
    {
        //都未填写的,根据社区"Scan indoor monitor QR code to register app account"开关判断是否下发
        CommunityInfo comm_info(dev.project_mng_id);
        if (!comm_info.IsSupportScanIndoorQRCodeToReg())
        {
            reg_user_info_.status = (int)KitEndUserDisplayStatus::CLEAR_USER_INFO; //通过下发空url清空原有的二维码
            AK_LOG_WARN << "not support scan indoor qr code to register app account. mac = " << dev.mac;
        }
    }

    //需要用户通过室内机二维码注册时，插入信息到PendingRegUser表中,相当于导入kit用户
    if ((int)KitEndUserDisplayStatus::USER_REG_CODE == reg_user_info_.status 
        && 0 != dbinterface::CommunityPendingRegUser::InsertCommunityPendingRegUser(reg_user_info_))
    {
        AK_LOG_WARN << "HandleCommunityKitRegister InserPendingRegUser fail, mac = " << dev.mac << ", account = " << reg_user_info_.account;
        return;
    }

    AK_LOG_INFO << "community indoor request kit register success, mac = " << dev.mac << ", account = " << reg_user_info_.account << ", token = " << reg_user_info_.token;
    return;
}

int RequestRegEndUserMsg::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    ResidentDev dev = GetDevicesClient();
    if (reg_user_info_.status == (int)KitEndUserDisplayStatus::USER_REG_CODE
     || reg_user_info_.status == (int)KitEndUserDisplayStatus::USER_REG_INFO)
    {
        // 通过设备固件号区分oem,下发不同的url
        GetRegisterUrl(dev.oem_id, reg_user_info_);
    }
    else if (reg_user_info_.status == (int)KitEndUserDisplayStatus::CLEAR_USER_INFO)
    {
        //status为该类型且url为空，实现清空用户信息
        reg_user_info_.status = (int)KitEndUserDisplayStatus::USER_REG_CODE;
    }
    
    ResidentPerAccount account;
    if (0 != dbinterface::ResidentPersonalAccount::GetUserAccount(reg_user_info_.account, account))
    {
        AK_LOG_WARN << "BuildRegEndUserUrlMsg failed";
        return -1;
    }
    
    Snprintf(reg_user_info_.email, sizeof(reg_user_info_.email), account.getEmail());
    Snprintf(reg_user_info_.account_name, sizeof(reg_user_info_.account_name), account.name);
    Snprintf(reg_user_info_.mobile_number, sizeof(reg_user_info_.mobile_number), account.getMobileNumber());
    
    msg_id = MSG_TO_DEVICE_REG_END_USER;
    if (GetMsgBuildHandleInstance()->BuildRegEndUserUrlMsg(reg_user_info_, dev.mac, msg) != 0)
    {
        AK_LOG_WARN << "BuildRegEndUserUrlMsg failed";
        return -1;
    }
    
    AK_LOG_INFO << "BuildRegEndUserUrlMsg success, mac = " << dev.mac << ", account = " << account.account;
    return 0 ;
}

void RequestRegEndUserMsg::GetRegisterUrl(const short oem_id, RegEndUserInfo& user_info)
{
    if (oem_id == OEMID_HAGER)
        {
            snprintf(user_info.reg_url, sizeof(user_info.reg_url), "https://intercom.hager-iot.com/?account=%s&token=%s", user_info.account, user_info.token);
        }
        else if (oem_id == OEMID_NICE)
        {
            ResidentPerAccount per_account;
            if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(user_info.account, per_account))
            {
                AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, account = " << user_info.account;
                return;
            }
    
            if (per_account.role != ACCOUNT_ROLE_COMMUNITY_MAIN)
            {
                AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, node role not match, role = " << per_account.role;
                return;
            }
    
            dbinterface::AccountInfo project_info;
            if (0 != dbinterface::Account::GetAccountByUUID(per_account.parent_uuid, project_info))
            {
                AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, Get Project Info Failed, account = " << per_account.account;
                return;
            }
    
            CommunityUnitInfo unit_info;
            if (0 != dbinterface::CommunityUnit::GetCommunityUnitByID(per_account.unit_id, unit_info))
            {
                AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, Get Unit Info Failed, account = " << per_account.account;
                return;
            }
            
            CommunityRoomInfo room_info;
            if (0 != dbinterface::CommunityRoom::GetCommunityRoomByID(per_account.room_id, room_info))
            {
                AK_LOG_INFO << "OEM Nice GetRegisterUrl Failed, Get Unit Info Failed, account = " << per_account.account;
                return;
          }
            snprintf(user_info.reg_url, sizeof(user_info.reg_url), "https://mybell.yubii.com/kit?account=%s&token=%s&community=%s&building=%s&apt=%s", 
                                            user_info.account, user_info.token, project_info.location, unit_info.unit_name, room_info.room_number);
        }
        else
        {
            if (user_info.user_projcet_type == project::PERSONAL)
            {
                snprintf(user_info.reg_url, sizeof(user_info.reg_url), "http://%s/smartplus/Kit.html?account=%s&token=%s", gstAKCSConf.web_domain, user_info.account, user_info.token);
            }
            else if (user_info.user_projcet_type == project::RESIDENCE)
            {
                snprintf(user_info.reg_url, sizeof(user_info.reg_url), "http://%s/smartplus/CommunityKit.html?account=%s&token=%s", gstAKCSConf.web_domain, user_info.account, user_info.token);
            }
        }
    
        std::string reg_url_encode = URLEncode(user_info.reg_url);
        Snprintf(user_info.reg_url, sizeof(user_info.reg_url), reg_url_encode.c_str());
        return;

}

