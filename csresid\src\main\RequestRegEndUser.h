#ifndef __REQ_REG_ENDUSER_MSG_H__
#define __REQ_REG_ENDUSER_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "PendingRegUser.h"

class RequestRegEndUserMsg: public IBase
{
public:
    RequestRegEndUserMsg(){}
    ~RequestRegEndUserMsg() = default;

    int IParseXml(char *msg){return 0;};
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg() {return 0;};
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key) {return 0;};
    void HandlePersonalKitRegister(const ResidentDev& dev);
    void HandleCommunityKitRegister(const ResidentDev& dev);
    void GetRegisterUrl(const short oem_id, RegEndUserInfo& user_info);

    IBasePtr NewInstance() {return std::make_shared<RequestRegEndUserMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    RegEndUserInfo reg_user_info_;
public:    
    std::string func_name_ = "RequestRegEndUserMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
};

#endif

