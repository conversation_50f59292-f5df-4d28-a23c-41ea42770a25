#ifndef _REQ_RESET_ROOM_H_
#define _REQ_RESET_ROOM_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

class ReqResetRoomMsg: public IBase
{
public:
    ReqResetRoomMsg(){}
    ~ReqResetRoomMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify();
    int IToRouteMsg();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key);

    IBasePtr NewInstance() {return std::make_shared<ReqResetRoomMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

public:
    std::string func_name_ = "ReqResetRoomMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    
    SOCKET_MSG_KIT_RESET_ROOM reset_room_msg_;
private:
    
};

#endif
