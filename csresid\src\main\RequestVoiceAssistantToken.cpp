#include "MsgParse.h"
#include "MsgBuild.h"
#include "MsgControl.h"
#include "RequestVoiceAssistantToken.h"
#include "Resid2RouteMsg.h"
#include "SafeCacheConn.h"
#include "util_string.h"


__attribute__((constructor))  static void Init()
{
    //设备(室内机)请求语音助手会话token
    IBasePtr p = std::make_shared<RequestVoiceAssistantToken>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_VOICE_ASSISTANT_TOKEN);
};

int RequestVoiceAssistantToken::IParseXml(char* msg)
{
    conn_dev_ = GetDevicesClient();

    AK_LOG_INFO << conn_dev_.mac <<" request voice assistant token";
    return 0;
}

int RequestVoiceAssistantToken::IControl()
{  
    SafeCacheConn redis(g_redis_db_auth);
    if (redis.isConnect())
    {
        std::string key = "voice_";
        key += conn_dev_.mac;
        token_ = GetNbitRandomString(32);
        redis.setex(key, VOICE_ASSISTANT_EXPIRE_SECOND, token_);
        AK_LOG_INFO << "set voice assistant token, key = " << key << ", token = " << token_;
    }
    return 0;
}

int RequestVoiceAssistantToken::IReplyMsg(std::string& msg, uint16_t& msg_id)
{
    msg_id = MSG_TO_DEVICE_VOICE_ASSISTANT_TOKEN;
    GetMsgBuildHandleInstance()->BuildVoiceAssistantTokenMsg(token_, msg);
    return 0;
}
