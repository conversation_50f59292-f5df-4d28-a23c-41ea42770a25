#ifndef __REQUEST_VOICE_TOKEN_H__
#define __REQUEST_VOICE_TOKEN_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "Resid2RouteMsg.h"

#define VOICE_ASSISTANT_EXPIRE_SECOND 3600

class RequestVoiceAssistantToken : public IBase
{
public:
    RequestVoiceAssistantToken() {}
    ~RequestVoiceAssistantToken() = default;

    int IParseXml(char* msg);
    int IControl();
    int IReplyMsg(std::string& msg, uint16_t& msg_id);

    IBasePtr NewInstance() { return std::make_shared<RequestVoiceAssistantToken>(); }
    std::string FuncName() { return func_name_; }
    MsgEncryptType EncType() { return enc_type_; }

public:
    std::string func_name_ = "RequestVoiceAssistantToken";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    ResidentDev conn_dev_;
    std::string token_;
};

#endif
