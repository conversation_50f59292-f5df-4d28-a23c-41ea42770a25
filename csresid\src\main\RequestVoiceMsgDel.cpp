#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RequestVoiceMsgDel.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "ResidDb.h"
#include "MsgBuild.h"

#include "ProjectUserManage.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqVoiceDel>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG);
};


int ReqVoiceDel::IParseXml(char *msg)
{
    memset(&del_msg_, 0, sizeof(del_msg_));
    CMsgParseHandle::ParseRequestDelVoiceMsg(msg, (void *)&del_msg_);
    return 0;
}


int ReqVoiceDel::IControl()
{
    ResidentDev dev = GetDevicesClient();
    AK_LOG_INFO << "DelVoiceMsgInfoByUUID mac:" << dev.mac<< "uuid:" <<del_msg_.uuid;
    if (0 != dbinterface::PersonalVoiceMsg::DelVoiceMsgInfoByIndoorUUID(del_msg_.uuid, dev.uuid))
    {
        AK_LOG_WARN << "DelVoiceMsgInfoByUUID failed.";
        return -1;
    }
    return 0;
}


int ReqVoiceDel::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    return 0;
}

int ReqVoiceDel::IPushNotify()
{
    return 0;
}

int ReqVoiceDel::IToRouteMsg()
{
    return 0;
}


