#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RequestVoiceMsgList.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "ResidDb.h"
#include "MsgBuild.h"
#include "ProjectUserManage.h"
#include "MsgToControl.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqVoiceList>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST);
};


int ReqVoiceList::IParseXml(char *msg)
{
    memset(&voice_msg_list_, 0, sizeof(voice_msg_list_));
    CMsgParseHandle::ParseRequestVoiceMsgList(msg, &voice_msg_list_);
    return 0;
}

int ReqVoiceList::IControl()
{
    return 0;
}


int ReqVoiceList::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    ResidentDev conn_dev = GetDevicesClient(); 
    int page_size = voice_msg_list_.page_size;
    int page_index = voice_msg_list_.page_index;
    AK_LOG_INFO << "ReqVoiceList mac " << conn_dev.mac << " pagesize:" << page_size << " page_index" << page_index;   


    PersonalVoiceMsgSendList send_list;
    voice_msg_list_.msg_count = dbinterface::PersonalVoiceMsg::GetVoicePageListByIndoorUUID(conn_dev.uuid, page_size, page_index, send_list);
    if (voice_msg_list_.msg_count > 0)
    {
        char payload[4096] = "";
        if (GetMsgBuildHandleInstance()->BuildVoiceMsgListNotifyMsg(payload, sizeof(payload), send_list, voice_msg_list_) < 0)
        {
            return -1;
        }
        msg = payload;
    }
    msg_id = MSG_TO_DEVICE_REPORT_VOICE_MSG_LIST;
    return 0;
}

int ReqVoiceList::IPushNotify()
{
    return 0;
}

int ReqVoiceList::IToRouteMsg()
{
    return 0;
}


