#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RequestVoiceMsgUrl.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "ResidDb.h"
#include "MsgBuild.h"
#include "ProjectUserManage.h"
#include "MsgToControl.h"
#include "AkcsHttpRequest.h"
#include "NotifyMsgControl.h"
#include "NotifyHttpReq.h"
#include "ResidInit.h"
#include "Md5.h"


extern AKCS_CONF gstAKCSConf;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqVoiceUrl>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL);
};


int ReqVoiceUrl::IParseXml(char *msg)
{
    memset(&url_msg_, 0, sizeof(url_msg_));
    CMsgParseHandle::ParseRequestVoiceMsgUrl(msg, (void *)&url_msg_);
    return 0;
}


int ReqVoiceUrl::IControl()
{
    PersonalVoiceMsgInfo per_voice_msg;
    std::string file_url;
    //TODO msg uuid直接获取语音文件会存在越权问题
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByUUID(url_msg_.uuid, per_voice_msg))
    {
        ResidentDev dev = GetDevicesClient();
        file_url = per_voice_msg.file_url;
        std::size_t pos2 =  file_url.find("/group");
        if (pos2 == std::string::npos)
        {
            //存oss的流程

            model::HttpRespuestKV parma_kv;
            parma_kv.insert(map<std::string, std::string>::value_type("Node", "SuperManage"));
            parma_kv.insert(map<std::string, std::string>::value_type("Path", file_url));
            
            char url[1024];
            snprintf(url, sizeof(url), "https://%s/web-server/v3/basic/common/capture/getLink", gstAKCSConf.web_backend_domain);

            AkcsKv kv;
            kv.insert(map<std::string, std::string>::value_type("mac", dev.mac));
            kv.insert(map<std::string, std::string>::value_type("mac_uuid", dev.uuid));
            kv.insert(map<std::string, std::string>::value_type("voice_uuid", url_msg_.uuid));
            CHttpReqNotifyMsg notify_msg(url, parma_kv, kv, CHttpReqNotifyMsg::NOTIFY_HTTP_REQ_TYPE::GET_S3_URL);
            GetHttpReqMsgControlInstance()->AddHttpReqNotiyMsg(notify_msg);            
            return 0;
        }
        
        //以下是存fdfs的流程
        size_t pos = file_url.find("/M");
        if (std::string::npos != pos)
        {
            //获取到 /M00/05/CB/rBIp3GMpNwqADMrHAAEqVhPHnOw417.wav
            std::string file_remote = file_url.substr(pos + 1);
            time_t timer = time(nullptr);
            char time_sec[16] = {0};
            snprintf(time_sec, 16, "%ld", timer);
            file_remote += "ak_fdfs";
            file_remote += time_sec;
            std::string token = akuvox_encrypt::MD5(file_remote).toStr();
            if (!dev.is_ipv6)
            {
                snprintf(url_msg_.url, sizeof(url_msg_.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv4, file_url.c_str(), token.c_str(), time_sec);
            }
            else
            {
                snprintf(url_msg_.url, sizeof(url_msg_.url), "https://%s:8091%s?token=%s&ts=%s", gstAKCSConf.voice_server_ipv6, file_url.c_str(), token.c_str(), time_sec);
            }
        }
        ReplyMsg();
    }

    return 0;
}


int ReqVoiceUrl::ReplyMsg()
{
    ResidentDev dev = GetDevicesClient();

    std::string msg;
    uint32_t msg_id = MSG_TO_DEVICE_REPORT_VOICE_MSG_URL;
    if (GetMsgBuildHandleInstance()->BuildVoiceMsgUrlNotifyMsg(url_msg_, dev.mac, msg) != 0)
    {
        AK_LOG_WARN << "BuildVoiceMsgListNotifyMsg failed";
        return -1;
    }

    //更新已读未读状态
    if (0 != dbinterface::PersonalVoiceMsg::UpdateVoiceMsgStatus(url_msg_.uuid, dev.uuid))
    {
        AK_LOG_WARN << "UpdateVoiceMsgStatus failed";
        return -1;
    }
    
    ReplyDevMsg(msg, msg_id);
    return 0;
}

int ReqVoiceUrl::IPushNotify()
{
    return 0;
}

int ReqVoiceUrl::IToRouteMsg()
{
    return 0;
}


