#ifndef _REQ_WEATHER_INFO_H_
#define _REQ_WEATHER_INFO_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"

class ReqWeatherInfo: public IBase
{
public:
    ReqWeatherInfo(){
    }
    ~ReqWeatherInfo() = default;


    int IParseXml(char *msg);
    int IControl();
    int IPushNotify();
    int IToRouteMsg();
    int IPushThirdNotify();

    int ReplyMsg();
    IBasePtr NewInstance() {return std::make_shared<ReqWeatherInfo>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}


    int GetDevRegionInfo(const ResidentDev& dev, SOCKET_MSG_DEV_WEATHER_INFO& weather_info);
public:
    std::string func_name_ = "ReqWeatherInfo";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEV_REQ_WEATHER_WAY req_weather_;
    SOCKET_MSG_DEV_WEATHER_INFO weather_info_;
};

#endif
