#ifndef _RESPONSE_EMERGENCY_CLOSE_DOOR_H_
#define _RESPONSE_EMERGENCY_CLOSE_DOOR_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"

class ResponseEmergencyCloseDoor: public IBase
{
public:
    ResponseEmergencyCloseDoor(){
    }
    ~ResponseEmergencyCloseDoor() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ResponseEmergencyCloseDoor>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ResponseEmergencyCloseDoor";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_EMERGENCY_CONTROL control_msg_;
};

#endif //_RESPONSE_EMERGENCY_CLOSE_DOOR_H_