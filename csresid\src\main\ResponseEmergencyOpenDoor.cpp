#include "ResponseEmergencyOpenDoor.h"
#include "MsgParse.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AkcsCommonDef.h"
#include "json/json.h"
#include "util.h"
#include <string>
#include "DclientMsgSt.h"
#include "EmergencyMsgControl.h"
#include "dbinterface/PmEmergencyDoorLog.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ResponseEmergencyOpenDoor>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_RESPONSE_EMERGENCY_KEEP_OPEN_DOOR);
};

int ResponseEmergencyOpenDoor::IParseXml(char *msg)
{
    if (0 != CMsgParseHandle::ParseResponseEmergencyControlMsg(msg, &control_msg_))
    {
        AK_LOG_WARN <<  "parse response emergency control msg failed";
        return -1;
    }
    AK_LOG_INFO <<  " handle parse pacport reg request";
    return 0;
}

int ResponseEmergencyOpenDoor::IControl()
{
    ResidentDev conn_dev = GetDevicesClient();

    //时间轮超时检测移出
    std::string key = GetEmergencyControlInstance()->GenerateKey(conn_dev.mac, control_msg_.msg_uuid);
    GetEmergencyControlInstance()->RemoveEmergencyControlMsg(key);

    //数据库更新
    if(dbinterface::PmEmergencyDoorLog::UpdateDeviceRelayStatus(conn_dev.uuid,control_msg_.msg_uuid,control_msg_.relay,control_msg_.security_relay) < 0)
    {
        AK_LOG_WARN << "UpdateDeviceRelayStatus failed.";
        return -1;
    }
    return 0;
}
