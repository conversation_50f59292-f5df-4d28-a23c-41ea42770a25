#ifndef _RESPONSE_EMERGENCY_OPEN_DOOR_H_
#define _RESPONSE_EMERGENCY_OPEN_DOOR_H_

#include "AgentBase.h"
#include <string>
#include "DclientMsgSt.h"

class ResponseEmergencyOpenDoor: public IBase
{
public:
    ResponseEmergencyOpenDoor(){
    }
    ~ResponseEmergencyOpenDoor() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ResponseEmergencyOpenDoor>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::string func_name_ = "ResponseEmergencyOpenDoor";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_EMERGENCY_CONTROL control_msg_;
};

#endif //_RESPONSE_EMERGENCY_OPEN_DOOR_H_