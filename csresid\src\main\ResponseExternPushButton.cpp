#include "ResponseExternPushButton.h"
#include "DclientMsgDef.h"
#include "MsgParse.h"
#include "dbinterface/ExternPushButton.h"
#include "dbinterface/NotifyList.h"
#include "dbinterface/PushButtonNotify.h"
#include "msgparse/ParseResponseExternPushButton.hpp"


__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ResponseExternPushButton>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_RESPONSE_EXTERN_PUSH_BUTTON);
};

int ResponseExternPushButton::IParseXml(char *msg)
{
    if (0 != akcs_msgparse::ParseResponseExternPushButtonMsg(msg, module_list_))
    {
        AK_LOG_WARN << "parse response extern pushbutton msg failed.";
        return -1;
    }
    AK_LOG_INFO << "parse response extern pushbutton msg success";
    return 0;
}

int ResponseExternPushButton::IControl()
{   
    ResidentDev dev = GetDevicesClient();
    AK_LOG_INFO <<  dev.mac << " Report Extern Push Button Msg ";
    //将extern push button 更新到数据库 
    int ret = dbinterface::ExternPushButton::UpdateExternPushButton(dev, module_list_);
    if(ret == -1)
    {
        AK_LOG_WARN << "Update Extern Push Button failed. mac is " << dev.mac; 
    }
    
    //系统通知
    std::string notify_uuid;
    dbinterface::PushButtonNotify::InsertPushButtonNotify(dev, notify_uuid);
    dbinterface::PushButtonNotifyList::InsertPushButtonNotifyList(dev, notify_uuid);
    return 0;
}


