#ifndef _RESPONSE_EXTERN_PUSH_BUTTON_H_
#define _RESPONSE_EXTERN_PUSH_BUTTON_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"

class ResponseExternPushButton : public IBase
{
public:
    ResponseExternPushButton(){}
    ~ResponseExternPushButton() = default;

    int IParseXml(char *msg);
    int IControl();

    IBasePtr NewInstance() {return std::make_shared<ResponseExternPushButton>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    std::map<int,int> module_list_;
    std::string func_name_ = "ResponseExternPushButton";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    
};

#endif //_RESPONSE_EXTERN_PUSH_BUTTON__H_