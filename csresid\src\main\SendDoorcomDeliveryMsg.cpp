#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "SendDoorcomDeliveryMsg.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "CachePool.h"
#include "json/json.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/Message.h"
#include "NotifyPerText.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "Resid2RouteMsg.h"

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<DoorcomDeliveryMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_SEND_DOORCOM_DELIVERY_MSG);
};

int DoorcomDeliveryMsg::IParseXml(char *msg)
{
    if (0 != CMsgParseHandle::ParseRequestDeliveryMsg(msg, &doorcom_msg_))
    {
        AK_LOG_WARN << " parse doorcom delivery send message failed";
        return -1;
    }
    AK_LOG_INFO << " handle doorcom delivery send message, status:" << doorcom_msg_.status;
    return 0;
}

int DoorcomDeliveryMsg::IControl()
{
    ResidentDev conn_dev = GetDevicesClient();

    std::string node;

    if(0 != GetNodeByUnitIDAndRoomNum(conn_dev.unit_id, doorcom_msg_.apt_num, node))
    {
        return -1;
    }
   
    CommPerTextMessage comm_text_msg;
    DoorcomDeliveryMessageConstruct(doorcom_msg_.status, doorcom_msg_.box_num, comm_text_msg);

    //构建发送Message列表并插入数据库
    if (0 != SendMessageToRoom(node, comm_text_msg))
    {
        AK_LOG_WARN <<  "send doorcom delivery dev text msg failed. ";
        return -1;
    }

    return 0;
}

int DoorcomDeliveryMsg::IToRouteMsg()
{
    CResid2RouteMsg::GroupDeliveryMsg(text_messages_);
    return 0;
}

void DoorcomDeliveryMsg::DoorcomDeliveryMessageConstruct(int delivery_status, const std::string& box_num, CommPerTextMessage& comm_text_msg)
{
    //通用消息构造
    std::string title;
    std::string content;
    //当前只有日本市场上该功能，Message先写死日语
    if(delivery_status == DELIVERY_STATUS::DELIVERY_STATUS_IN)
    {
        title = "宅配のお荷物があります";
        content = "宅配ボックス  " + box_num + " に新しい荷物が届きました, お早めに受け取ってください.";
    }
    else if(delivery_status == DELIVERY_STATUS::DELIVERY_STATUS_OUT) //取出快递
    {
        title = "宅配のお荷物が受領されました";
        content = "宅配ボックス " + box_num + " の中のお荷物が受領されました.";
    }

    memset(&comm_text_msg, 0, sizeof(comm_text_msg));
    Snprintf(comm_text_msg.title, sizeof(comm_text_msg.title), title.c_str());
    Snprintf(comm_text_msg.content, sizeof(comm_text_msg.content), content.c_str());
    comm_text_msg.project_type = project::RESIDENCE;
}

int DoorcomDeliveryMsg::SendMessageToRoom(const std::string&node, const CommPerTextMessage& comm_text_msg)
{
    //主账号
    ResidentPerAccount master_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(node, master_account))
    {
        AK_LOG_WARN << "get main account failed, node: " << node;
        return -1;
    }

    //从账号列表获取
    ResidentPerAccountList sub_account_list;
    if(0 != dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_COMMUNITY_ATTENDANT, sub_account_list))
    {
        AK_LOG_WARN << "get sub account list failed, node: " << node;
        return -1;
    }
    //室内机
    ResidentDeviceList dev_list;
    dbinterface::ResidentDevices::GetNodeIndoorDevList(node, dev_list);

    //发送消息列表
    PerTextMessageSendList text_messages;
    //插入相关Message表并构造发送对象
    if (0 != dbinterface::Message::AddGroupTextMsg(CPerTextNotifyMsg::MessageType::TEXT_MSG, comm_text_msg, dev_list, master_account, sub_account_list ,text_messages))
    {
        AK_LOG_WARN <<  "add doorcom delivery dev text msg failed. ";
        return -1;
    }
    CResid2RouteMsg::GroupDeliveryMsg(text_messages);
    return 0;
}


int DoorcomDeliveryMsg::GetNodeByUnitIDAndRoomNum(uint32_t unit_id, const std::string& room_num, std::string& node)
{
    uint32_t room_id = 0;
    if (0 != dbinterface::CommunityRoom::GetRoomIDByUnitIDAndRoomNum(unit_id, room_num, room_id))
    {
        AK_LOG_WARN << "Get room id failed. unit id:" << unit_id << " room number:" << room_num;
        return -1;
    }

    if (0 != dbinterface::CommPersonalAccount::GetNodeByRoomID(room_id, node))
    {
        AK_LOG_WARN << "Get master account failed. room id:" << room_id;
        return -1;
    }
    return 0;
}