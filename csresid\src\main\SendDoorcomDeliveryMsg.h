#ifndef _SEND_DOORCOM_DELIVERY_MSG_H_
#define _SEND_DOORCOM_DELIVERY_MSG_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgDef.h"
#include "dbinterface/Message.h"

class DoorcomDeliveryMsg : public IBase
{
enum DELIVERY_STATUS
{
    //存入快递
    DELIVERY_STATUS_IN,
    //取出快递
    DELIVERY_STATUS_OUT,
};
public:
    DoorcomDeliveryMsg(){}
    ~DoorcomDeliveryMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IToRouteMsg();

    IBasePtr NewInstance() {return std::make_shared<DoorcomDeliveryMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:
    //给一个房间下所有App和室内机发消息
    int SendMessageToRoom(const std::string&node, const CommPerTextMessage& comm_text_msg);
    int GetNodeByUnitIDAndRoomNum(uint32_t unit_id, const std::string& room_num, std::string& node);
    std::string func_name_ = "DoorcomDeliveryMsg";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

    SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG doorcom_msg_;
    //发送消息列表
    PerTextMessageSendList text_messages_;
    void DoorcomDeliveryMessageConstruct(int delivery_status, const std::string& box_num, CommPerTextMessage& comm_text_msg);
};

#endif // _SEND_DOORCOM_DELIVERY_MSG_H_