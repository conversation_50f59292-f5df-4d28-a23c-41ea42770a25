#include "MsgBuild.h"
#include "MsgControl.h"
#include "ReportIpCallRecord.h"
#include "dbinterface/Account.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "msgparse/ParseReportIpCallRecordMsg.hpp"
#include "PbxMsgDef.h"
#include "cspbx_rpc_client_mng.h"
#include "dbinterface/ProjectUserManage.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportIpCallRecordHandler>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_IP_CALLLOG);
};

int ReportIpCallRecordHandler::IParseXml(char* msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != akcs_msgparse::ParseReportIpCallRecordMsg(msg, ip_call_record_msg_))
    {
        AK_LOG_WARN << "ParseReportIpCallRecordMsg fail, mac = " << conn_dev_.mac;
        return -1;
    }
    AK_LOG_INFO << "ReportIpCallRecordMsg mac = " << conn_dev_.mac << ", caller = " << ip_call_record_msg_.caller << ", callee = " << ip_call_record_msg_.callee 
    << ", call_trace_id = " << ip_call_record_msg_.call_trace_id << ", is_group_call = " << ip_call_record_msg_.is_group_call << ", is_answer = " << ip_call_record_msg_.is_answer
    << ", start_time_stamp = " << ip_call_record_msg_.start_time_stamp << ", answer_time_stamp = " << ip_call_record_msg_.answer_time_stamp << ", duration = " << ip_call_record_msg_.duration << ", trace_id = " << ip_call_record_msg_.trace_id;
    return 0;
}

int ReportIpCallRecordHandler::IControl()
{
    AKCS_CALL_HISTORY history;
    memset(&history, 0, sizeof(history));
    
    std::vector<std::string> call_trace_id_vec;
    Snprintf(history.call_trace_id, sizeof(history.call_trace_id), ip_call_record_msg_.call_trace_id);
    SplitString(history.call_trace_id, "-", call_trace_id_vec);
    if(call_trace_id_vec.size() != 3 && call_trace_id_vec.size() != 4)
    {
        AK_LOG_ERROR << "ReportIpCallRecordHandler::IControl() call_trace_id = " << history.call_trace_id << " is invalid";
        return -1;
    }

    history.is_ip_call = 1;
    if (conn_dev_.is_personal == 1)
    {
        Snprintf(history.db_delivery_uuid, sizeof(history.db_delivery_uuid), dbinterface::ProjectUserManage::GetLogDeliveryUUIDByAccount(conn_dev_.node).c_str());
    }
    else
    {
        Snprintf(history.db_delivery_uuid, sizeof(history.db_delivery_uuid), conn_dev_.project_uuid);
    }
    history.bill_second = ip_call_record_msg_.duration;
    Snprintf(history.start_time, sizeof(history.start_time), StampToStandardTime(ip_call_record_msg_.start_time_stamp).c_str());
    Snprintf(history.caller_sip, sizeof(history.caller_sip), ip_call_record_msg_.caller);
    Snprintf(history.caller_name, sizeof(history.caller_name), ip_call_record_msg_.caller_name);
    Snprintf(history.callee_sip, sizeof(history.callee_sip), ip_call_record_msg_.callee);
    Snprintf(history.callee_name, sizeof(history.callee_name), ip_call_record_msg_.callee_name);
    if (ip_call_record_msg_.is_answer)
    {
        Snprintf(history.called_sip, sizeof(history.called_sip), ip_call_record_msg_.callee);
        Snprintf(history.answer_time, sizeof(history.answer_time), StampToStandardTime(ip_call_record_msg_.answer_time_stamp).c_str());
    }

    PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcClientInstanceByCallTraceID(history.call_trace_id);
    if (rpc_client)
    {
        rpc_client->WriteCallHistory(&history, history.call_trace_id);
    }
    return 0;
}

int ReportIpCallRecordHandler::IReplyMsg(std::string& msg, uint16_t& msg_id)
{    
    msg_id = MSG_TO_DEVICE_ACK;
    //根据解析的TraceID向设备回ACK
    SOCKET_MSG_COMMON_ACK common_ack;
    memset(&common_ack, 0, sizeof(common_ack));
    Snprintf(common_ack.mac, sizeof(common_ack.mac),  conn_dev_.mac);
    Snprintf(common_ack.trace_id, sizeof(common_ack.trace_id), ip_call_record_msg_.trace_id);
    common_ack.result = 1;
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_IP_CALLLOG, common_ack, msg);
    return 0;
}
