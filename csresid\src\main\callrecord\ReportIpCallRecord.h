#ifndef __REPORT_IP_CALL_RECORD_MSG_H__
#define __REPORT_IP_CALL_RECORD_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

class ReportIpCallRecordHandler: public IBase
{
public:
    ReportIpCallRecordHandler(){}
    ~ReportIpCallRecordHandler() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg() {return 0;};
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key) {return 0;};

    IBasePtr NewInstance() {return std::make_shared<ReportIpCallRecordHandler>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

private:    
    ResidentDev conn_dev_;
    std::string func_name_ = "ReportIpCallRecordHandler";
    SOCKET_MSG_DEVICE_REPORT_IP_CALL_RECORD ip_call_record_msg_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;

};

#endif
