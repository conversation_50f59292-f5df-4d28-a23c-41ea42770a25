#include "MsgBuild.h"
#include "MsgControl.h"
#include "ReportVideoRecord.h"
#include "NotifyVideoRecordMsg.h"
#include "dbinterface/Account.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "msgparse/ParseReportVideoRecordMsg.hpp"

extern LOG_DELIVERY gstAKCSLogDelivery;

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReportVideoRecordHandler>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REPORT_VIDEO_RECORD);
};

int ReportVideoRecordHandler::IParseXml(char* msg)
{
    conn_dev_ = GetDevicesClient();
    if (0 != akcs_msgparse::ParseReportVideoRecordMsg(msg, video_record_msg_))
    {
        AK_LOG_WARN << "ParseReportVideoRecordMsg fail, mac = " << conn_dev_.mac;
        return -1;
    }
    AK_LOG_INFO << "ReportVideoRecordMsg mac = " << conn_dev_.mac << ", pic_name = " << video_record_msg_.pic_name << ", video_record_name = " << video_record_msg_.video_record_name;
    return 0;
}

int ReportVideoRecordHandler::IControl()
{
    MacInfo mac_info;
    GetMacInfo(mac_info);

    CVideoRecordNotifyMsg async_msg(video_record_msg_, conn_dev_, mac_info);
    GetDoorOpenMsgProcessInstance()->AddVideoRecordNotifyMsg(async_msg);
    return 0;
}

int ReportVideoRecordHandler::IReplyMsg(std::string& msg, uint16_t& msg_id)
{    
    msg_id = MSG_TO_DEVICE_ACK;
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_VIDEO_RECORD, video_record_msg_.video_record_name, msg);
    return 0;
}
