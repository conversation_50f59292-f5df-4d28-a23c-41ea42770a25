#ifndef __REQ_START_CAPTURE_MSG_H__
#define __REQ_START_CAPTURE_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

class ReqStartCaptureMsg: public IBase
{
public:
    ReqStartCaptureMsg(){}
    ~ReqStartCaptureMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg();
    int IReplyMsg(std::string &msg, uint16_t &msg_id) {return 0;};
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key) {return 0;};

    IBasePtr NewInstance() {return std::make_shared<ReqStartCaptureMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}

    void SendStartRecord();
    int InsertCaptureLog();
    void SetRecordCache(const std::string& server_id);
    std::string CapturePicName(const std::string& mac);
private:    
    std::string func_name_ = "ReqStartCaptureMsg";
    std::string pic_name_;
    SOCKET_MSG_REQ_CAPTURE request_capture_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_ENCRYPT;
    void GenerateFlowInfo();
    void GetCaptureSite();
};

#endif
