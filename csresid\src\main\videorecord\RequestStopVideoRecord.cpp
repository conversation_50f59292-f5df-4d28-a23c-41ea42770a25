#include "MsgParse.h"
#include "MsgBuild.h"
#include "json/json.h"
#include "SafeCacheConn.h"
#include "Resid2RouteMsg.h"
#include "VideoRecordClient.h"
#include "VideoRecordClientMng.h"
#include "RequestStopVideoRecord.h"
#include "RequestVideoRecordUtil.hpp"
#include "dbinterface/InterfaceComm.h"
#include "msgparse/ParseStopVideoRecordMsg.hpp"

__attribute__((constructor))  static void Init()
{
    IBasePtr p = std::make_shared<ReqStopVideoRecordMsg>();
    RegFunc(p, BackendFactory::FUNC_TYPE::APP, MSG_FROM_APP_REQUEST_STOP_CAPTURE);
};

int ReqStopVideoRecordMsg::IParseXml(char *msg)
{
    return akcs_msgparse::ParseRequestStopVideoRecordMsg(msg, stop_record_video_);
}

int ReqStopVideoRecordMsg::IControl()
{
    std::string logic_srv_cache_key = ReqVideoRecordUtil::LogicServerCacheKey(stop_record_video_.site, stop_record_video_.mac);

    SafeCacheConn redis(g_redis_db_video_record);
    std::string videorecord_srv_id = redis.get(logic_srv_cache_key);
    if (videorecord_srv_id.empty())
    {
        AK_LOG_WARN << "stop record failed, videorecord_srv_id is empty, logic_srv_cache_key = " << logic_srv_cache_key;
        return -1;
    }

    VideoRecordRpcClientPtr grpc_client = VideoRecordClientMng::Instance()->GetRpcClientInstance(videorecord_srv_id);
    if (grpc_client == nullptr)
    {
        AK_LOG_WARN << "stop record failed, grpc_client is null, logic_srv_cache_key = " << logic_srv_cache_key << ", videorecord_srv_id = " << videorecord_srv_id;
        return -1;
    }

    // 停止录制
    grpc_client->StopVideoRecord(stop_record_video_.site, stop_record_video_.mac);
    
    AK_LOG_INFO << "StopVideoRecord success site = " << stop_record_video_.site << ", mac = " << stop_record_video_.mac << ", server_id = " << videorecord_srv_id;

    return 0;
}