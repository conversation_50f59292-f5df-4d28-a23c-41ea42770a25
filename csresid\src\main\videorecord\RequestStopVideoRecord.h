#ifndef __REQ_STOP_VIDEO_RECORD_MSG_H__
#define __REQ_STOP_VIDEO_RECORD_MSG_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "AK.Route.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

class ReqStopVideoRecordMsg: public IBase
{
public:
    ReqStopVideoRecordMsg(){}
    ~ReqStopVideoRecordMsg() = default;

    int IParseXml(char *msg);
    int IControl();
    int IPushNotify() {return 0;};
    int IToRouteMsg() {return 0;};
    int IReplyMsg(std::string &msg, uint16_t &msg_id) {return 0;};
    int IPushThirdNotify(std::string &msg, uint32_t &msg_id, std::string &key) {return 0;};

    IBasePtr NewInstance() {return std::make_shared<ReqStopVideoRecordMsg>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
public:    
    std::string func_name_ = "ReqStopVideoRecordMsg";
    SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO stop_record_video_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_ENCRYPT;
};

#endif
