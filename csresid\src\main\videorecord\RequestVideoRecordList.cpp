#include "util_time.h"
#include "MsgBuild.h"
#include "ResidServer.h"
#include "MsgToControl.h"
#include "RequestVideoRecordList.h"
#include "RequestVideoRecordUtil.hpp"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/IndoorIpCallVideoStorage.h"
#include "msgparse/ParseRequestRecordVideoMsgList.hpp"
#include "dbinterface/Log/CallHistoryDB.h"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqVideoRecordList>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_RECORD_VIDEO_MSG_LIST);
};

int ReqVideoRecordList::IParseXml(char *msg)
{
    return akcs_msgparse::ParseRequestRecordVideoMsgList(msg, request_record_msg_list_);
}

int ReqVideoRecordList::IControl()
{
    MacInfo dev;
    GetMacInfo(dev);

    for (auto& record_msg : request_record_msg_list_)
    {
        // 判断traceID是否存在
        if (!ReqVideoRecordUtil::TraceIDExist(dev, record_msg)) 
        {
            record_msg.status = VideoRecordPlayStatus::NOT_FOUND_TRACE_ID;
            continue;
        }
        
        // 判断VideoRecordUrl是否存在
        if (!ReqVideoRecordUtil::VideoRecordUrlExist(dev, record_msg))
        {
            record_msg.status = VideoRecordPlayStatus::NOT_FOUND_VIDEO;
            continue;
        }
        
        // 判断视频存储方案是否过期
        if (ReqVideoRecordUtil::VideoRecordExpire(dev))
        {
            record_msg.status = VideoRecordPlayStatus::NO_PERMISSION;
            continue;
        }

        record_msg.status = VideoRecordPlayStatus::OK;
        AK_LOG_INFO << "ReqVideoRecordList type = " << (int)record_msg.type << ", call_trace_id = " << record_msg.call_trace_id << ", status is ok";
    }
    return 0;
}

int ReqVideoRecordList::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    GetMsgBuildHandleInstance()->BuildRecordVideoInfoMsg(request_record_msg_list_, msg);
    msg_id = MSG_TO_DEVICE_RESPONSE_RECORD_VIDEO_MSG_LIST;
    return 0;
}
