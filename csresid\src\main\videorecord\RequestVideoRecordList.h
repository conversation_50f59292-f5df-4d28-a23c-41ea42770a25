#ifndef __REQ_RECORD_VIDEO_MSG_LIST_H__
#define __REQ_RECORD_VIDEO_MSG_LIST_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"

class ReqVideoRecordList: public IBase
{
public:
    ReqVideoRecordList(){}
    ~ReqVideoRecordList() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);

    IBasePtr NewInstance() {return std::make_shared<ReqVideoRecordList>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    
public:
    std::string func_name_ = "ReqVideoRecordList";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    std::vector<SOCKET_MSG_REQUEST_RECORD_VIDEO> request_record_msg_list_;
};

#endif
