#include "json/json.h"
#include "MsgBuild.h"
#include "util_string.h"
#include "ResidServer.h"
#include "MsgToControl.h"
#include "AkcsHttpRequest.h"
#include "VideoRecordClient.h"
#include "VideoRecordClientMng.h"
#include "RequestVideoRecordUrl.h"
#include "RequestVideoRecordUtil.hpp"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/IndoorIpCallVideoStorage.h"
#include "msgparse/ParseRequestRecordVideoUrl.hpp"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern std::vector<std::string> g_csvideorecord_http_addrs;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReqVideoRecordUrl>();
    RegFunc(p, BackendFactory::FUNC_TYPE::DEV, MSG_FROM_DEVICE_REQUEST_PLAY_VIDEO);
};

int ReqVideoRecordUrl::IParseXml(char *msg)
{
    return akcs_msgparse::ParseRequestPlayRecordVideoUrl(msg, record_msg_);
}

int ReqVideoRecordUrl::IControl()
{
    MacInfo dev;
    GetMacInfo(dev);
    
    if (!ReqVideoRecordUtil::RequestVideoRecordUrlValid(dev, record_msg_))
    {
        AK_LOG_WARN << "VideoRecordUrl InValid, mac = " << dev.mac << ", call_trace_id = " << record_msg_.call_trace_id;
        return -1;
    }

    std::string video_record_url;
    std::string video_record_name;
    if (DatabaseExistenceStatus::EXIST != dbinterface::PersonalCapture::GetVideoRecordInfo(record_msg_.call_trace_id, dev.log_delivery_uuid, 
                gstAKCSLogDelivery.personal_capture_delivery, video_record_name, video_record_url))
    {
        AK_LOG_WARN << "GetVideoRecordInfo Failed, mac = " << dev.mac << ", call_trace_id = " << record_msg_.call_trace_id;
        return -1;
    }

    std::string csvideo_record_addr = g_csvideorecord_http_addrs[crc32_hash(dev.mac) % g_csvideorecord_http_addrs.size()];

    std::stringstream request_url;
    request_url << csvideo_record_addr << "/video/url?url=" << video_record_url << "&filename=" << video_record_name;

    std::string response;
    model::HttpRequest::GetInstance().Post(request_url.str(), "", response, 0);

    GetResponsePlayUrl(response);
    
    return 0;
}

void ReqVideoRecordUrl::GetResponsePlayUrl(const std::string& response)
{
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(response, root)) 
    {
         AK_LOG_WARN << "get response video url failed to parse json message";
         return;
    }
    
    if (root.isMember("data") && root["data"].isMember("url")) {
        video_play_url_ = root["data"]["url"].asString();
    }    
    return;
}

int ReqVideoRecordUrl::IReplyMsg(std::string &msg, uint16_t &msg_id)
{
    GetMsgBuildHandleInstance()->BuildRecordVideoPlayUrlMsg(record_msg_.call_trace_id, video_play_url_, msg);
    msg_id = MSG_TO_DEVICE_RECORD_VIDEO_URL;
    return 0;
}
