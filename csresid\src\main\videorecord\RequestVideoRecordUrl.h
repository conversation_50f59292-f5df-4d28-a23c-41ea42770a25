#ifndef __REQ_RECORD_VIDEO_URL_H__
#define __REQ_RECORD_VIDEO_URL_H__

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AgentBase.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"

class ReqVideoRecordUrl: public IBase
{
public:
    ReqVideoRecordUrl(){}
    ~ReqVideoRecordUrl() = default;

    int IParseXml(char *msg);
    int IControl();
    int IReplyMsg(std::string &msg, uint16_t &msg_id);
    void GetResponsePlayUrl(const std::string& response);

    IBasePtr NewInstance() {return std::make_shared<ReqVideoRecordUrl>();}
    std::string FuncName() {return func_name_;}
    MsgEncryptType EncType() {return enc_type_;}
    
public:
    std::string func_name_ = "ReqVideoRecordUrl";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_REQUEST_RECORD_VIDEO record_msg_;
    std::string video_play_url_;
};

#endif
