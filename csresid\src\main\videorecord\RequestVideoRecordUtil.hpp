#ifndef __RESID_RECORD_VIDEO_UTIL_H__
#define __RESID_RECORD_VIDEO_UTIL_H__

#include "util_time.h"
#include "AkLogging.h"
#include "DclientMsgSt.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "dbinterface/VideoStorage.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/IndoorIpCallVideoStorage.h"

extern LOG_DELIVERY gstAKCSLogDelivery;
extern std::map<std::string, AKCS_DST> g_time_zone_DST;

class ReqVideoRecordUtil
{
public:
    static bool TraceIDExist(MacInfo& dev, SOCKET_MSG_REQUEST_RECORD_VIDEO& record_msg)
    {
        if (strlen(record_msg.call_trace_id) == 0)
        {
            AK_LOG_WARN << "call_trace_id is null, mac = " << dev.mac;
            return false;
        }

        AK_LOG_INFO << "mac = " << dev.mac << ", trace_id = " << record_msg.call_trace_id << ", type = " << (int)record_msg.type;
        
        if (record_msg.type == VideoRecordCallType::IP)
        {
            IndoorIpCallVideoStorageInfo indoor_ip_call_video_storage_info;
            if (0 != dbinterface::IndoorIpCallVideoStorage::GetIndoorIpCallVideoStorageInfo(record_msg.call_trace_id, dev.uuid, indoor_ip_call_video_storage_info))
            {
                AK_LOG_WARN << "get indoor ipcall video storage failed, mac = " << dev.mac << ", call_trace_id = " << record_msg.call_trace_id;
                return false;
            }            
        }
        else if (record_msg.type == VideoRecordCallType::SIP) 
        {
            if (DatabaseExistenceStatus::EXIST != dbinterface::CallHistory::CallHistoryExist(dev.log_delivery_uuid, gstAKCSLogDelivery.call_history_delivery, dev.node, record_msg.call_trace_id))
            {
                AK_LOG_WARN << "get call history failed, mac = " << dev.mac << ", call_trace_id = " << record_msg.call_trace_id;
                return false;
            }
        }  
        // 根据traceID获取呼叫信息
        std::vector<CallHistoryRecord> call_records = dbinterface::CallHistory::GetAllCallHistoryByTraceID(dev.log_delivery_uuid, gstAKCSLogDelivery.call_history_delivery, record_msg.call_trace_id);  
        for (auto& call_record : call_records)
        {
            if (call_record.call_type == (int)CallType::GROUP_CALL)
            {
                Snprintf(record_msg.answer_name, sizeof(record_msg.answer_name), call_record.answer_name);
                record_msg.is_answer = call_record.is_answer;
                record_msg.answer_duration = call_record.answer_duration;
                record_msg.is_group_call = 1;
            }
            else if (call_record.call_type != (int)CallType::GROUP_EACH_CALL)
            {
                // 单呼
                Snprintf(record_msg.answer_name, sizeof(record_msg.answer_name), call_record.answer_name);
                record_msg.is_answer = call_record.is_answer;
                record_msg.answer_duration = call_record.answer_duration;
                record_msg.is_group_call = 0;
            }
        }
        
        return true;
    }

    static bool VideoRecordUrlExist(MacInfo& dev, SOCKET_MSG_REQUEST_RECORD_VIDEO& record_msg)
    {
        std::string video_record_url;
        std::string video_record_name;
        if (DatabaseExistenceStatus::EXIST != dbinterface::PersonalCapture::GetVideoRecordInfo(record_msg.call_trace_id, dev.log_delivery_uuid,
                gstAKCSLogDelivery.personal_capture_delivery, video_record_name, video_record_url))
        {
            AK_LOG_INFO << "GetVideoRecordInfo Failed, CallTraceID " << record_msg.call_trace_id << " not related any Doorlog";
            return false;
        }

        // 上传了doorlog, 但是未上传视频文件
        if (video_record_url.empty()) 
        {
            AK_LOG_INFO << "VideoRecordUrl Not Exist, Door not upload VideoFile, mac = " << dev.mac 
                        << ", call_trace_id = " << record_msg.call_trace_id << ", video_record_name = " << video_record_name;
            return false;
        }
        return true;
    }

    static bool VideoRecordExpire(MacInfo& dev)
    {
        VideoStorageInfo video_storage_info;
        if (dev.project_type == project::PERSONAL) 
        {
            if (0 != dbinterface::VideoStorage::GetVideoStorageByPersonalAccountUUID(dev.node_uuid, video_storage_info))
            {
                AK_LOG_WARN << "Personal VideoRecord Switch off or Expire, mac = " << dev.mac;
                return true;
            }
        }
        else if (dev.project_type == project::RESIDENCE)
        {
            if (0 != dbinterface::VideoStorage::GetVideoStorageByAccountUUID(dev.project_uuid, video_storage_info))
            {
                AK_LOG_WARN << "Community VideoRecord Switch off or Expire, mac = " << dev.mac;
                return true;
            }
        }

        return false;
    }

    static bool RequestVideoRecordUrlValid(MacInfo& dev, SOCKET_MSG_REQUEST_RECORD_VIDEO& record_msg)
    {
        return TraceIDExist(dev, record_msg) && VideoRecordUrlExist(dev, record_msg) && !VideoRecordExpire(dev);
    }   

    static std::string LogicServerCacheKey(const std::string& site, const std::string& mac)
    {
        return site + ":" + mac;
    }

    static std::string VideoRecodStreamKey(const std::string& site, const std::string& mac)
    {
        return "__defaultVhost__/" + site + "/" + mac;
    }
public:
    ReqVideoRecordUtil()= delete;
    ~ReqVideoRecordUtil() = delete;
};

#endif


