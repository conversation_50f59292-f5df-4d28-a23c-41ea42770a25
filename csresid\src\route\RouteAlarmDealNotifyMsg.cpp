#include "RouteAlarmDealNotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "Resid2RouteMsg.h"
#include "../control/AlarmDealNotifyMsg.h"
#include "dbinterface/Account.h"

extern std::map<std::string, AKCS_DST> g_time_zone_DST;
__attribute__((constructor)) static void init()
{
    IRouteBasePtr p = std::make_shared<RouteAlarmDealNotifyMsg>();
    RegRouteFunc(p, MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL);
};

int RouteAlarmDealNotifyMsg::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "RouteAlarmDealNotifyMsg BackendP2PBaseMessage details: type=" << base_msg.type()
        << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
        << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    AK::Server::P2PAlarmDealNotifyMsg alarm_deal_msg = base_msg.p2palarmdealnotifymsg2();
    if (alarm_deal_msg.alarm_id().empty())
    {
        AK_LOG_WARN << "alarm_id is empty";
        return -1;
    }

    // 获取告警信息
    ALARM alarm_info;
    (void)memset(&alarm_info, 0, sizeof(alarm_info));
    int alarm_id = atoi(alarm_deal_msg.alarm_id().c_str());
    if (alarm_id <= 0 || dbinterface::Alarm::GetAlarm(alarm_id, &alarm_info) != 0)
    {
        AK_LOG_WARN << "Get alarm info failed: alarm_id=" << alarm_id;
        return -1;
    }

    // 获取项目信息
    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountById(alarm_info.manager_account_id, project_info))
    {
        AK_LOG_WARN << "get project info failed. project id=" << alarm_info.manager_account_id;
        return -1;
    }
    std::string nodeTime = GetNodeNowDateTimeByTimeZoneStr(project_info.timezone, g_time_zone_DST);
    alarm_deal_msg.set_alarm_time(nodeTime);

    // 处理告警状态
    alarm_info.status = 1;
    Snprintf(alarm_info.deal_user, sizeof(alarm_info.deal_user), alarm_deal_msg.user().c_str());
    Snprintf(alarm_info.deal_result, sizeof(alarm_info.deal_result), alarm_deal_msg.result().c_str());
    if (dbinterface::Alarm::DealAlarm(&alarm_info) != 0)
    {
        AK_LOG_WARN << "DealAlarmStatus failed: alarm_id=" << alarm_id;
        return -1;
    }

    community::ProcessAlarmDealNotify(alarm_info.manager_account_id, alarm_deal_msg);
    return 0;
}
