#ifndef _ROUTE_ALARM_DEAL_NOTIFY_MSG_H_
#define _ROUTE_ALARM_DEAL_NOTIFY_MSG_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Resid2AppMsg.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/AlarmDB.h"


class RouteAlarmDealNotifyMsg : public IRouteBase
{
    AK::Server::P2PAlarmDealNotifyMsg   alarm_deal_msg_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;

public:
    RouteAlarmDealNotifyMsg() {}
    ~RouteAlarmDealNotifyMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu>& pdu);
    std::string FuncName() { return "RouteAlarmDealNotifyMsg"; }
    IRouteBasePtr NewInstance() { return std::make_shared<RouteAlarmDealNotifyMsg>(); }
};


#endif //_ROUTE_ALARM_DEAL_NOTIFY_MSG_H_
