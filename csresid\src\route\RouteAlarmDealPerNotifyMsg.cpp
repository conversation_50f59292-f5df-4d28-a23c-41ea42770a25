#include "RouteAlarmDealPerNotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "Resid2RouteMsg.h"
#include "../control/AlarmDealPerNotifyMsg.h"

extern std::map<std::string, AKCS_DST> g_time_zone_DST;
__attribute__((constructor)) static void init()
{
    IRouteBasePtr p = std::make_shared<RouteAlarmDealPerNotifyMsg>();
    RegRouteFunc(p, MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL);
};

int RouteAlarmDealPerNotifyMsg::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "RouteAlarmDealPerNotifyMsg BackendP2PBaseMessage details: type=" << base_msg.type()
        << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
        << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    AK::Server::P2PAlarmDealNotifyMsg alarm_deal_msg = base_msg.p2palarmdealnotifymsg2();
    if (alarm_deal_msg.alarm_id().empty())
    {
        AK_LOG_WARN << "alarm_id is empty";
        return -1;
    }

    // 获取告警信息
    PERSONAL_ALARM_DEAL_OFFLINE_INFO alarm_info;
    (void)memset(&alarm_info, 0, sizeof(alarm_info));
    int alarm_id = atoi(alarm_deal_msg.alarm_id().c_str());
    if (alarm_id <= 0 || dbinterface::PersonalAlarm::GetAlarmInfo(alarm_deal_msg.alarm_id(), alarm_info) != 0)
    {
        AK_LOG_WARN << "Get alarm info failed: alarm_id=" << alarm_id;
        return -1;
    }

    std::string timezone = dbinterface::ResidentPersonalAccount::GetNodeTimeZoneStr(alarm_deal_msg.area_node());
    std::string nodeTime = GetNodeNowDateTimeByTimeZoneStr(timezone, g_time_zone_DST);
    alarm_deal_msg.set_alarm_time(nodeTime);

    // 处理告警状态
    PERSONAL_ALARM_DEAL_INFO deal_info;
    Snprintf(deal_info.result, sizeof(deal_info.result), alarm_deal_msg.result().c_str());
    Snprintf(deal_info.user, sizeof(deal_info.user), alarm_deal_msg.user().c_str());
    Snprintf(deal_info.alarm_id, sizeof(deal_info.alarm_id), alarm_deal_msg.alarm_id().c_str());
    if (dbinterface::PersonalAlarm::DealAlarmStatus(deal_info) != 0)
    {
        AK_LOG_WARN << "DealAlarmStatus failed: alarm_id=" << alarm_id;
        return -1;
    }

    personal::ProcessAlarmDealNotify(alarm_info.community, alarm_deal_msg);
    return 0;
}
