#include "RouteAlarmDealReplyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "ResidPushClient.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "Resid2RouteMsg.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/PersonalAlarm.h"

__attribute__((constructor)) static void init()
{
    IRouteBasePtr p = std::make_shared<RouteAlarmDealReplyMsg>();
    RegRouteFunc(p, AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG);
};

int RouteAlarmDealReplyMsg::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "RouteAlarmDealReplyMsg BackendP2PBaseMessage details: type=" << base_msg.type()
        << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
        << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    alarm_notify_msg_ = base_msg.p2palarmdealnotifymsg2();
    if (alarm_notify_msg_.target().empty())
    {
        AK_LOG_ERROR << "target is empty";
        return -1;
    }
    // 获取告警信息
    int alarm_id = atoi(alarm_notify_msg_.alarm_id().c_str());
    if (base_msg.project_type() == project::PERSONAL) {
        PERSONAL_ALARM_DEAL_OFFLINE_INFO personal_alarm_info;
        (void)memset(&personal_alarm_info, 0, sizeof(personal_alarm_info));
        if (dbinterface::PersonalAlarm::GetAlarmInfo(alarm_notify_msg_.alarm_id(), personal_alarm_info) != 0)
        {
            AK_LOG_ERROR << "Get personal alarm info failed: alarm_id=" << alarm_id;
            return -1;
        }
        alarm_info_.alarm_code = personal_alarm_info.alarm_code;
        alarm_info_.alarm_zone = personal_alarm_info.alarm_zone;
        alarm_info_.alarm_location = personal_alarm_info.alarm_location;
        alarm_info_.alarm_customize = personal_alarm_info.alarm_customize;
        alarm_info_.trace_id = personal_alarm_info.trace_id;
        Snprintf(alarm_info_.mac, sizeof(alarm_info_.mac), personal_alarm_info.mac);
        // 获取告警设备信息
        if (dbinterface::ResidentPerDevices::GetMacDev(alarm_info_.mac, resident_dev_) != 0)
        {
            AK_LOG_ERROR << "GetMacPerDev failed: mac=" << alarm_info_.mac;
        }
    } else {
        if (dbinterface::Alarm::GetAlarm(alarm_id, &alarm_info_) != 0)
        {
            AK_LOG_ERROR << "Get alarm info failed: alarm_id=" << alarm_id;
            return -1;
        }
        // 获取告警设备信息
        if (dbinterface::ResidentDevices::GetMacDev(alarm_info_.mac, resident_dev_) != 0)
        {
            AK_LOG_ERROR << "GetMacDev failed: mac=" << alarm_info_.mac;
        }
    }
    ProcessAlarmReply();
    return 0;
}

void RouteAlarmDealReplyMsg::ProcessAlarmReply()
{
    // 初始化告警信息
    std::string DeviceName = resident_dev_.location;
    Snprintf(alarm_deal_info_.type, sizeof(alarm_deal_info_.type), "0");
    Snprintf(alarm_deal_info_.protocal, sizeof(alarm_deal_info_.protocal), "1.0");
    Snprintf(alarm_deal_info_.user, sizeof(alarm_deal_info_.user), alarm_notify_msg_.user().c_str());
    Snprintf(alarm_deal_info_.result, sizeof(alarm_deal_info_.result), alarm_notify_msg_.result().c_str());
    Snprintf(alarm_deal_info_.alarm_id, sizeof(alarm_deal_info_.alarm_id), alarm_notify_msg_.alarm_id().c_str());
    Snprintf(alarm_deal_info_.time, sizeof(alarm_deal_info_.time), alarm_notify_msg_.alarm_time().c_str());
    Snprintf(alarm_deal_info_.device_name, sizeof(alarm_deal_info_.device_name), DeviceName.c_str());
    Snprintf(alarm_deal_info_.area_node, sizeof(alarm_deal_info_.area_node), alarm_notify_msg_.area_node().c_str());

    alarm_deal_info_.alarm_code = alarm_info_.alarm_code;
    alarm_deal_info_.alarm_zone = alarm_info_.alarm_zone;
    alarm_deal_info_.alarm_location = alarm_info_.alarm_location;
    alarm_deal_info_.alarm_customize = alarm_info_.alarm_customize;
    alarm_deal_info_.manager_account_id = alarm_info_.manager_account_id;
    alarm_deal_info_.unit_id = alarm_info_.unit_id;
    alarm_deal_info_.trace_id = alarm_info_.trace_id;

    SendAlarmDealReply();
}

void RouteAlarmDealReplyMsg::SendAlarmDealReply()
{
    AlarmNotifyTargetType target_type = (AlarmNotifyTargetType)alarm_notify_msg_.target_type();
    if (target_type == AlarmNotifyTargetType::DEV_MANAGEMENT ||
        target_type == AlarmNotifyTargetType::DEV_INDOOR)
    {
        SendAlarmDealReplyToDev(alarm_notify_msg_.target());
    }
    else if (target_type == AlarmNotifyTargetType::APP_USER)
    {
        SendAlarmDealReplyToApp(alarm_notify_msg_.target());
    }
    else if (target_type == AlarmNotifyTargetType::APP_PM)
    {
        SendAlarmDealReplyToPMApp();
    }
    else
    {
        AK_LOG_ERROR << "Unknown target type: " << (int)target_type;
    }
}

void RouteAlarmDealReplyMsg::SendAlarmDealReplyToPMApp()
{
    std::string pm_site = alarm_notify_msg_.target(); // pm实际站点

    // 获取PM APP在线推送信息
    OfflinePushUserInfo offline_user;
    OfflinePush::GetPmAlarmPushInfoByNode(alarm_notify_msg_.area_node(), offline_user);
    Snprintf(alarm_deal_info_.title, sizeof(alarm_deal_info_.title), offline_user.pm_online_title);
    Snprintf(alarm_deal_info_.site, sizeof(alarm_deal_info_.site), pm_site.c_str());

    // 发送消息
    SendAlarmDealReplyToApp(pm_site);
}

void RouteAlarmDealReplyMsg::BuildOfflinePushNotifyMsg(CResid2AppMsg& msg_sender)
{
    std::string target = alarm_notify_msg_.target();    // 实际站点
    msg_sender.InsertOfflineMsgKV("mac_sip", alarm_info_.mac);
    msg_sender.InsertOfflineMsgKV("device_name", resident_dev_.location);
    msg_sender.InsertOfflineMsgKV("alarm_msg", alarm_info_.alarm_type);
    msg_sender.InsertOfflineMsgKV("alarm_id", std::to_string(alarm_info_.id));
    msg_sender.InsertOfflineMsgKV("alarm_code", std::to_string(alarm_info_.alarm_code));
    msg_sender.InsertOfflineMsgKV("alarm_zone", std::to_string(alarm_info_.alarm_zone));
    msg_sender.InsertOfflineMsgKV("alarm_location", std::to_string(alarm_info_.alarm_location));
    msg_sender.InsertOfflineMsgKV("alarm_customize", std::to_string(alarm_info_.alarm_customize));
    msg_sender.InsertOfflineMsgKV("traceid", std::to_string(alarm_info_.trace_id));

    // 查询当前站点
    std::string main_site;
    ResidentPerAccount main_account;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(target, main_site);
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(main_site, main_account))
    {
        AK_LOG_INFO << "BuildOfflinePushNotifyMsg GetUidAccount failed, main_site = " << main_site;
        return;
    }

    // 一人多套房增加 title_prefix 和 site字段
    if (dbinterface::ProjectUserManage::IsMultiSiteUser(main_account.user_info_uuid))
    {
        // 标题使用当前站点查询
        std::string title_prefix;
        OfflinePush::GetMultiSiteUserTitle(main_site, title_prefix);
        msg_sender.InsertOfflineMsgKV("title_prefix", title_prefix);
        msg_sender.InsertOfflineMsgKV("site", target);
    }

    return;
}

void RouteAlarmDealReplyMsg::SendAlarmDealReplyToDev(const std::string target_mac)
{
    // 获取目标设备信息
    ResidentDev dev;
    if (g_resid_srv_ptr->GetDevSetting(target_mac, dev) < 0)
    {
        AK_LOG_ERROR << "SendAlarmNotifyToDev GetDevSetting failed, mac = " << target_mac;
        return;
    }

    // 构造通知消息
    std::string notify_msg;
    GetMsgBuildHandleInstance()->BuildAlarmDealReplyMsg(alarm_deal_info_, notify_msg);

    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, notify_msg, MSG_ID, socket_message, MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT) != 0)
    {
        AK_LOG_ERROR << "SendAlarmNotifyToDev BuildDclientMacEncMsg failed, mac=" << target_mac;
        return;
    }

    // 发送消息
    GetClientControlInstance()->SendTransferMsg(target_mac, dev.conn_type, socket_message.data, socket_message.size);
    AK_LOG_INFO << "SendAlarmDealReplyToDev mac = " << target_mac << ", conn_type = " << dev.conn_type;
}

void RouteAlarmDealReplyMsg::SendAlarmDealReplyToApp(const std::string target_user)
{
    CResid2AppMsg msg_sender;

    // 构造在线消息
    std::string notify_msg;
    GetMsgBuildHandleInstance()->BuildAlarmDealReplyMsg(alarm_deal_info_, notify_msg);

    // 构造离线消息
    BuildOfflinePushNotifyMsg(msg_sender);
    AK_LOG_INFO << "SendAlarmDealReplyToApp, account = " << target_user;

    //消息发送给csmain
    msg_sender.SetMsgId(MSG_ID);
    msg_sender.SetClient(target_user);
    msg_sender.SetOnlineMsgData(notify_msg);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_DEALALARM);
    return;
}
