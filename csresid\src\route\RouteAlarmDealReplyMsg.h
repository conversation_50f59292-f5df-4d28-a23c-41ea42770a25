#ifndef _ROUTE_ALARM_DEAL_NOTIFY_MSG_H_
#define _ROUTE_ALARM_DEAL_NOTIFY_MSG_H_

#include "RouteBase.h"
#include <string>
#include "MsgBuild.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Resid2AppMsg.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/AlarmDB.h"

class RouteAlarmDealReplyMsg : public IRouteBase
{
public:
    RouteAlarmDealReplyMsg() = default;
    ~RouteAlarmDealReplyMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu>& pdu);

    std::string FuncName() { return "RouteAlarmDealReplyMsg"; }
    IRouteBasePtr NewInstance() { return std::make_shared<RouteAlarmDealReplyMsg>(); }

private:
    void ProcessAlarmReply();
    void SendAlarmDealReply();

    // 通知app
    void SendAlarmDealReplyToPMApp();
    // 转发消息
    void SendAlarmDealReplyToApp(const std::string target_user);
    void SendAlarmDealReplyToDev(const std::string target_mac);
    void BuildOfflinePushNotifyMsg(CResid2AppMsg& msg_sender);

private:
    ALARM                               alarm_info_;
    ResidentDev                         resident_dev_;
    SOCKET_MSG_ALARM_DEAL_INFO          alarm_deal_info_;
    AK::Server::P2PAlarmDealNotifyMsg   alarm_notify_msg_;
    const int MSG_ID                    = MSG_TO_DEVICE_NOTIFY_ALARM_DEAL;
};

#endif //_ROUTE_ALARM_DEAL_NOTIFY_MSG_H_
