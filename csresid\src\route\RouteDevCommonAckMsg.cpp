#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "AK.Linker.pb.h"
#include "CachePool.h"
#include "MsgToControl.h"
#include "Resid2RouteMsg.h"
#include "ProjectUserManage.h"
#include "RouteDevCommonAckMsg.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteDevCommonAckMsg>();
    RegRouteFunc(p, AKCS_R2B_P2P_DEV_COMMON_ACK);
};

int RouteDevCommonAckMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Linker::LinkerDevCommonAck msg;
    if (msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false) 
    {
        AK_LOG_WARN << "parse pb msg failed.";
        return -1;
    }

    std::string mac = msg.mac();
    uint16_t msg_id = msg.msg_id();
    std::string msg_seq = msg.msg_seq();
    
    if (msg_id == MSG_FROM_DEVICE_REQUEST_CREATE_ROOM)
    {
        GetMsgToControlInstance()->SendHagerCreateRoomAck(msg_id, mac, msg_seq);

        // web回复ack,才能下发iskit
        GetMsgToControlInstance()->SendHagerDevIsKitPlanMsg(MSG_TO_DEVICE_REQUEST_IS_KIT, mac);
    }

    return 0; 
}

int RouteDevCommonAckMsg::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    return 0;
}
