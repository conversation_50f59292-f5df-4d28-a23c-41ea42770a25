#ifndef _ROUTE_DEV_COMMON_ACK_H_
#define _ROUTE_DEV_COMMON_ACK_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "DclientMsgDef.h"

class RouteDevCommonAckMsg: public IRouteBase
{
public:
    RouteDevCommonAckMsg(){}
    ~RouteDevCommonAckMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    int IPushNotify();
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteDevCommonAckMsg>();}
    std::string FuncName() {return func_name_;}

private:

    std::string func_name_ = "RouteDevCommonAckMsg";
};


#endif



