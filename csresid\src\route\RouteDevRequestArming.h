#ifndef _ROUTE_DEV_REQUEST_ARMING_H_
#define _ROUTE_DEV_REQUEST_ARMING_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AK.Server.pb.h"


class RouteDevRequestArming : public IRouteBase
{
public:
    RouteDevRequestArming() {}
    ~RouteDevRequestArming() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    IRouteBasePtr NewInstance() { return std::make_shared<RouteDevRequestArming>(); }

    std::string FuncName() { return func_name_; }
    MsgEncryptType EncType() { return enc_type_; }

private:

    std::string func_name_ = "P2PRouteDevRequestArming";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_ARMING arming_msg_;
    void GetArmingInfo(const AK::Server::P2PMainAppHandleArmingMsg& msg);
};

#endif //_ROUTE_DEV_REQUEST_ARMING_H_
