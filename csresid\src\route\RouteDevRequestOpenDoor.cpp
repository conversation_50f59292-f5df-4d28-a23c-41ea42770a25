/*
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "msgparse/ParseDevRequestOpen.hpp"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "RouteDevRequestOpenDoor.h"
#include "DclientMsgDef.h"
#include "Resid2RouteMsg.h"
#include "ClientControl.h"
#include "DeviceSetting.h"
#include "RouteFactory.h"
#include "AkLogging.h"
#include "util_judge.h"
#include "MsgBuild.h"


__attribute__((constructor))  static void Init()
{
    IRouteBasePtr p = std::make_shared<RouteDevRequestOpenDoor>();
    RegRouteFunc(p, AKCS_R2B_P2P_FROM_DEVICE_OPENDOOR_REQ);
};

int RouteDevRequestOpenDoor::IParseXml(char* msg)
{
    memset(&dev_request_open_, 0, sizeof(dev_request_open_));
    if (0 != akcs_msgparse::ParseDevRequestOpen(msg, dev_request_open_))
    {
        AK_LOG_WARN << "parse remote control ack msg failed.";
        return -1;
    }

    return 0;
}

int RouteDevRequestOpenDoor::IControl()
{
    // 转换开门类型
    std::string open_door_type;
    if (strcmp(dev_request_open_.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_DOOR) == 0)
    {
        open_door_type = SOCKET_MSG_TYPE_NAME_OPENDOOR;
    }
    else if (strcmp(dev_request_open_.type, SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_SECURITY_RELAY) == 0)
    {
        open_door_type = SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY;
    }
    else
    {
        AK_LOG_WARN << "Open Door Type:" << dev_request_open_.type;
        return -1;
    }

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    open_door.relay = dev_request_open_.relay;
    Snprintf(open_door.uid, sizeof(open_door.uid), conn_dev_.sip);
    Snprintf(open_door.mac, sizeof(open_door.mac), dev_request_open_.mac);
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), dev_request_open_.trace_id);
    HandleP2POpenDoorReq(open_door, open_door_type);
    return 0;
}

void RouteDevRequestOpenDoor::HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO& open_door, const std::string& open_door_type)
{
    int relay = open_door.relay;
    std::string uid = open_door.uid;
    std::string mac = open_door.mac;
    std::string accessible_floor = "0";             // 分号分割楼层, eg: "1;2;3;6;7;8;9;"
    std::string msg_traceid = open_door.trace_id;   // 
    std::string receiver_mac = strlen(open_door.repost_mac) > 0 ? (open_door.repost_mac) : (open_door.mac);

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(uid, sip_info);

    if (sip_info.sip_type == csmain::COMMUNITY_DEV)
    {
        ResidentDev dev;
        if (dbinterface::ResidentDevices::GetSipDev(uid, dev) == 0)
        {
            CommunityRoomInfo room;
            if (dbinterface::CommunityRoom::GetCommunityRoomByNode(dev.node, room) == 0)
            {
                accessible_floor = room.floor;
            }
        }
    }
    else if (sip_info.sip_type == csmain::COMMUNITY_APP)
    {
        std::string node;
        if (dbinterface::CommPersonalAccount::GetUidNode(uid, node) == 0)
        {
            // 社区用户多楼层
            accessible_floor = dbinterface::PersonalAccountCommunityInfo::GetFloorByAccount(uid);

            // 社区用户apt_floor
            CommunityRoomInfo room;
            if (dbinterface::CommunityRoom::GetCommunityRoomByNode(node, room) == 0)
            {
                accessible_floor = GetAccessibleFloor(room.floor, accessible_floor);
            }
        }
    }

    if (accessible_floor.length() == 0)
    {
        accessible_floor = "0";
    }

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remote_control_msg;
    memset(&remote_control_msg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remote_control_msg.protocal, sizeof(remote_control_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remote_control_msg.type, sizeof(remote_control_msg.type), open_door_type.c_str());
    Snprintf(remote_control_msg.item[0], sizeof(remote_control_msg.item[0]), uid.c_str());
    ::snprintf(remote_control_msg.item[1], sizeof(remote_control_msg.item[1]), "%d", relay);
    Snprintf(remote_control_msg.item[2], sizeof(remote_control_msg.item[2]), msg_traceid.c_str());
    ::snprintf(remote_control_msg.item[3], sizeof(remote_control_msg.item[3]), "%s", accessible_floor.c_str());
    ::snprintf(remote_control_msg.item[4], sizeof(remote_control_msg.item[4]), "%s", mac.c_str());

    AK_LOG_INFO << "Request open door uid=" << uid
        << ", mac=" << mac << ", repost_mac=" << open_door.repost_mac
        << ", traceid=" << msg_traceid << ", open_door_type=" << open_door_type
        << ", accessible_floor=" << accessible_floor;

    char xml_msg[4096];
    memset(xml_msg, 0, sizeof(xml_msg));
    GetMsgBuildHandleInstance()->BuildRemoteControlMsg(xml_msg, sizeof(xml_msg), &remote_control_msg);

    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.msg_id = MSG_TO_DEVICE_REMOTE_CONTROL;
    send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
    send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    Snprintf(send_msg.client, sizeof(send_msg.client), receiver_mac.c_str());
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), xml_msg);
    send_msg.msg_len = strlen(send_msg.msg_data);

    GetClientControlInstance()->SendTransferMsg(send_msg);
    return;
}
*/
