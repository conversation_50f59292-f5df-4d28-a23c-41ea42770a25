#ifndef 0
#define _DEVICE_REQUEST_OPENDOOR_H_

#include "AgentBase.h"
#include "AkLogging.h"
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "RouteBase.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"
#include "AkcsWebMsgSt.h"
#include "dbinterface/resident/ResidentDevices.h"

class RouteDevRequestOpenDoor : public IRouteBase
{
public:
    RouteDevRequestOpenDoor() {}
    ~RouteDevRequestOpenDoor() = default;

    int IControl();
    int IParseXml(char* msg);
    int IReplyMsg(std::string& msg, uint16_t& msg_id) { return 0; }
    IRouteBasePtr NewInstance() { return std::make_shared<RouteDevRequestOpenDoor>(); }

    std::string FuncName() { return func_name_; }
    MsgEncryptType EncType() { return enc_type_; }

private:
    // 处理P2P开门请求
    void HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO& open_door, const std::string& open_door_type);

    ResidentDev conn_dev_;
    ResidentDev target_dev_;
    std::string func_name_ = "P2PRouteDevRequestOpenDoor";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG_DEV_REQUEST_OPEN dev_request_open_;
};

#endif //_DEVICE_REQUEST_OPENDOOR_H_
