#include "RouteGroupIndoorRelayStatus.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "MsgControl.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "string.h"
#include "MsgStruct.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "Resid2AppMsg.h"
#include "cspush/PushClient.h"
#include "DclientMsgDef.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteGroupIndoorRelayStatus>();
    RegRouteFunc(p, AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG);
};

int RouteGroupIndoorRelayStatus::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    AK::Server::GroupMainReportRelayStatus relay_status_msg = base_msg.groupindoorrelaystatusmsg2();

    std::string mac = relay_status_msg.mac();
    uint64_t relay_status = relay_status_msg.relay_status();
    int relay_type = relay_status_msg.relay_type();
    std::string account = relay_status_msg.account();

    //通知App改变relay状态
    SendReqChangeAppRelayMsg(relay_status, relay_type, mac, account);

    return 0;
}

void RouteGroupIndoorRelayStatus::SendReqChangeAppRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, const std::string& account)
{
    // 如果是本地继电器类型，只需要发送一次；如果是外接继电器，拆分成3次发送，每次发送16个继电器状态，解决旧版本app消息截断问题
    if (relay_type == IndoorRelayType::TYPE_EXTERN)
    {
        // 外接继电器类型，拆分成3次发送
        for (int board_index = 0; board_index < TOTAL_EXTRA_DEVICES_NUM; board_index++)
        {
            // 计算当前板的起始继电器ID
            int start_relay_id = board_index * RELAYS_PER_EXTRA_DEVICE;
            SendRelayMsgToApp(relay_status, relay_type, mac, account, start_relay_id);
        }
    }
    else
    {
        SendRelayMsgToApp(relay_status, relay_type, mac, account, 0);
    }
}

void RouteGroupIndoorRelayStatus::SendRelayMsgToApp(uint64_t relay_status, int relay_type, const std::string& mac, const std::string& account, int start_relay_id)
{
    std::string msg;
    uint16_t msg_id = 0;
    
    // 使用带偏移量的消息构建函数
    GetMsgBuildHandleInstance()->BuildReqChangeAppRelayMsg(relay_status, relay_type, mac, msg, msg_id, start_relay_id);

    CResid2AppMsg msg_sender;
    msg_sender.SetOnlineMsgData(msg);
    msg_sender.SetMsgId(msg_id);
    msg_sender.SetClient(account);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID_ONLINE);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_ONLY_ONLINE);
}
