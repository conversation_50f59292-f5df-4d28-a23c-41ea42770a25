#ifndef _ROUTE_GROUP_INDOOR_RELAY_STATUS_H_
#define _ROUTE_GROUP_INDOOR_RELAY_STATUS_H_

#include "RouteBase.h"
#include <string>

class RouteGroupIndoorRelayStatus : public IRouteBase
{
public:
    RouteGroupIndoorRelayStatus(){}
    ~RouteGroupIndoorRelayStatus() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteGroupIndoorRelayStatus>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteGroupIndoorRelayStatus";
    void SendReqChangeAppRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, const std::string& account);
    void SendRelayMsgToApp(uint64_t relay_status, int relay_type, const std::string& mac, const std::string& account, int start_relay_id);
};

#endif // _ROUTE_GROUP_INDOOR_RELAY_STATUS_H_