#include "RouteGroupSL20EventNotifyMsg.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "MsgBuild.h"
#include "DclientMsgDef.h"
#include "cspush/PushClient.h"
#include "Resid2AppMsg.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteGroupSL20EventNotifyMsg>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_SL20_LOCK_EVENT_NOTIFY);
};

int RouteGroupSL20EventNotifyMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    AK::Server::P2PSendSL20LockEventNotify lock_event_notify_msg = base_msg.p2psendsl20lockeventnotify2();

    std::string lock_uuid = lock_event_notify_msg.lock_uuid();
    int event_type = lock_event_notify_msg.event_type();
    std::string site = lock_event_notify_msg.site();

    SendSL20LockEventNotifyMsg(event_type, lock_uuid, site);

    return 0;
}

void RouteGroupSL20EventNotifyMsg::SendSL20LockEventNotifyMsg(int event_type, const std::string& lock_uuid, const std::string& site)
{
    std::string msg;
    uint16_t msg_id = MSG_TO_APP_SMARTLOCK_EVENT_NOTIFY;
    GetMsgBuildHandleInstance()->BuildSL20LockEventNotifyMsg(event_type, lock_uuid, site, msg);

    CResid2AppMsg msg_sender;
    msg_sender.SetOnlineMsgData(msg);
    msg_sender.SetMsgId(msg_id);
    msg_sender.SetClient(site);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID_ONLINE);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_ONLY_ONLINE);
}
