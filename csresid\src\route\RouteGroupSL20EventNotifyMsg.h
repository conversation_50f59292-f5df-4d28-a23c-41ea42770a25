#ifndef _ROUTE_GROUP_SL20_EVENT_NOTIFY_MSG_H_
#define _ROUTE_GROUP_SL20_EVENT_NOTIFY_MSG_H_

#include "RouteBase.h"
#include <string>

class RouteGroupSL20EventNotifyMsg : public IRouteBase
{
public:
    RouteGroupSL20EventNotifyMsg(){}
    ~RouteGroupSL20EventNotifyMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteGroupSL20EventNotifyMsg>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteGroupSL20EventNotifyMsg";
    void SendSL20LockEventNotifyMsg(int event_type, const std::string& lock_uuid, const std::string& site);
};

#endif // _ROUTE_GROUP_SL20_EVENT_NOTIFY_MSG_H_