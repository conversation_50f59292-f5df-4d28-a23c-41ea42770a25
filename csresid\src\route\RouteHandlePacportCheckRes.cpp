#include "RouteHandlePacportCheckRes.h"
#include "AK.Linker.pb.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "MsgBuild.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/CommPersonalAccount.h"
#include "util.h"
#include "NotifyPerText.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "Resid2RouteMsg.h"
#include "RouteFactory.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteHandlePacportCheckRes>();
    RegRouteFunc(p, AKCS_R2B_P2P_PACPORT_UNLOCK_RESP);
};

int RouteHandlePacportCheckRes::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Linker::LinkerPacportCheckNotify msg;
    //解析失败返回-1
    CHECK_PB_PARSE_MSG_ERR_RET(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    //校验结果信息
    unlock_check_res_.result = msg.result();
    Snprintf(unlock_check_res_.mac, sizeof(unlock_check_res_.mac), msg.mac().c_str());
    Snprintf(unlock_check_res_.trace_id, sizeof(unlock_check_res_.trace_id), msg.traceid().c_str());
    std::string room_num = msg.room_num();

    //校验失败 无需后续处理
    if (unlock_check_res_.result == 0)
    {
        AK_LOG_INFO << "Pacport unlock check failed. no need to send message";
        return 0;
    }

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(unlock_check_res_.mac, dev))
    {
        AK_LOG_WARN << "Get device info failed. mac:" << unlock_check_res_.mac;
        return -1;
    }
    //获取房间主账号account,用于下发校验结果
    std::string master_account;
    if (0 != GetNodeByUnitIDAndRoomNum(dev.unit_id, room_num, master_account))
    {
        return -1;
    }

    Snprintf(unlock_check_res_.master_account, sizeof(unlock_check_res_.master_account), master_account.c_str());
    //向对应的房间所有App和室内机发Message
    std::string message_title = "お宅配荷物が届きました";
    std::string message_content = "宅配荷物が部屋の前に置きました。お早めに受け取ってください";
    if (0 != SendMessageToRoom(master_account, message_title, message_content))
    {
        AK_LOG_WARN << "send Pacport unlock check message failed. mac is:" << unlock_check_res_.mac;
        return -1;
    }
    return 0;
}

int RouteHandlePacportCheckRes::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    msg_id = MSG_TO_DEVICE_SEND_PACPORT_CHECK_RESULT;
    to_mac = unlock_check_res_.mac;
    enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    GetMsgBuildHandleInstance()->BuildPacportUnlockCheckResMsg(unlock_check_res_, msg);
    return 0;
}

int RouteHandlePacportCheckRes::SendMessageToRoom(const std::string&node, const std::string& message_title, const std::string& message_content)
{
    //主账号
    ResidentPerAccount master_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(node, master_account))
    {
        AK_LOG_WARN << "get main account failed, node: " << node;
        return -1;
    }

    //从账号列表获取
    ResidentPerAccountList sub_account_list;
    if(0 != dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(node, ACCOUNT_ROLE_COMMUNITY_ATTENDANT, sub_account_list))
    {
        AK_LOG_WARN << "get sub account list failed, node: " << node;
        return -1;
    }
    //室内机
    ResidentDeviceList dev_list;
    dbinterface::ResidentDevices::GetNodeIndoorDevList(node, dev_list);

    CommPerTextMessage comm_text_msg;//消息通用部分
    memset(&comm_text_msg, 0, sizeof(comm_text_msg));
    Snprintf(comm_text_msg.title, sizeof(comm_text_msg.title), message_title.c_str());
    Snprintf(comm_text_msg.content, sizeof(comm_text_msg.content), message_content.c_str());
    comm_text_msg.project_type = project::RESIDENCE;
    //发送消息列表
    PerTextMessageSendList text_messages;
    //插入相关Message表并构造发送对象
    if (0 != dbinterface::Message::AddGroupTextMsg(CPerTextNotifyMsg::MessageType::TEXT_MSG, comm_text_msg, dev_list, master_account, sub_account_list ,text_messages))
    {
        AK_LOG_WARN << "add pacport delivery dev text msg failed. ";
        return -1;
    }
    GroupDeliveryMsg(text_messages);
    return 0;
}

void RouteHandlePacportCheckRes::GroupDeliveryMsg(const PerTextMessageSendList& text_messages)
{
    for(const auto &text_message : text_messages)
    {
        AK::BackendCommon::BackendP2PBaseMessage base;
        int type = 0; //接收方类型：室内机或App
        if(text_message.client_type == PersoanlMessageSend::DEV_SEND)
        {
            type = DEVICE_TYPE_INDOOR;
            base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_DELIVERY_MSG, TransP2PMsgType::TO_DEV_UUID, text_message.uuid,
                CResid2RouteMsg::DevProjectTypeToDevType(text_message.comm_message.project_type), text_message.comm_message.project_type);
        }
        else if (text_message.client_type == PersoanlMessageSend::APP_SEND)
        {
            type = DEVICE_TYPE_APP;
            std::string uid;
            dbinterface::ResidentPersonalAccount::GetAccountByUUID(text_message.uuid, uid);
            base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_DELIVERY_MSG, TransP2PMsgType::TO_APP_UID, uid,
                CResid2RouteMsg::DevProjectTypeToDevType(text_message.comm_message.project_type), text_message.comm_message.project_type);
        }
        AK::Server::P2PSendDeliveryMsg msg2;
        msg2.set_title(text_message.comm_message.title);
        msg2.set_content(text_message.comm_message.content);
        msg2.set_receiver_type(type);
        msg2.set_receiver_uuid(text_message.uuid);
        msg2.set_project_type(text_message.comm_message.project_type);
        msg2.set_message_id(text_message.id);
        base.mutable_p2psenddeliverymsg2()->CopyFrom(msg2);
        IP2PToRouteMsg(&base);
    }
	return;
}

int RouteHandlePacportCheckRes::GetNodeByUnitIDAndRoomNum(uint32_t unit_id, const std::string& room_num, std::string& node)
{
    uint32_t room_id = 0;
    if (0 != dbinterface::CommunityRoom::GetRoomIDByUnitIDAndRoomNum(unit_id, room_num, room_id))
    {
        AK_LOG_WARN <<  "Get room id failed. unit id:" << unit_id << " room number:" << room_num;
        return -1;
    }

    if (0 != dbinterface::CommPersonalAccount::GetNodeByRoomID(room_id, node))
    {
        AK_LOG_WARN << "Get master account failed. room id:" << room_id;
        return -1;
    }
    return 0;
}