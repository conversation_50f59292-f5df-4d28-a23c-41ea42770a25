#ifndef _ROUTE_HANDLE_PACPORT_CHECK_H_
#define _ROUTE_HANDLE_PACPORT_CHECK_H_

#include "RouteBase.h"
#include "DclientMsgSt.h"
#include "dbinterface/Message.h"

class RouteHandlePacportCheckRes : public IRouteBase
{
public:
    RouteHandlePacportCheckRes(){}
    ~RouteHandlePacportCheckRes() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteHandlePacportCheckRes>();}
    std::string FuncName() {return func_name_;}

private:
    //给一个房间下所有App和室内机发消息
    int SendMessageToRoom(const std::string&node, const std::string& message_title, const std::string& message_content);
    int GetNodeByUnitIDAndRoomNum(uint32_t unit_id, const std::string& room_num, std::string& node);
    //快递消息广播
    void GroupDeliveryMsg(const PerTextMessageSendList& text_messages);
    std::string func_name_ = "RouteHandlePacportCheckRes";
    SOCKET_MSG_PACPORT_UNLOCK_RES unlock_check_res_;
};
#endif // _ROUTE_HANDLE_PACPORT_CHECK_H_