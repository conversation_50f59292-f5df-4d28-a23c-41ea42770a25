#include "RouteP2PAlarmNotify.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "ResidPushClient.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "doorlog/UserInfo.h"
#include "Resid2AppMsg.h"

__attribute__((constructor)) static void init()
{
    IRouteBasePtr p = std::make_shared<RouteP2PAlarmNotify>();
    RegRouteFunc(p, AKCS_M2R_GROUP_COMM_ALARM_REQ);
};

int RouteP2PAlarmNotify::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    AK::Server::P2PSendAlarmNotifyMsg msg = base_msg.p2psendalarmnotifymsg2();
    receiver_endpoint_ = msg.receive_endpoint();

    // 获取告警消息
    GetAlarmSendMsg(msg);
    if (base_msg.type() == TransP2PMsgType::TO_APP_UID)
    {
        SendAlarmNotifyToApp(msg);
    }
    else if (base_msg.type() == TransP2PMsgType::TO_DEV_MAC)
    {
        SendAlarmNotifyToDev(msg);
    }

    return 0;
}

void RouteP2PAlarmNotify::GetAlarmSendMsg(const AK::Server::P2PSendAlarmNotifyMsg& msg)
{
    send_alarm_msg_.id = msg.id();
    send_alarm_msg_.grade = msg.grade();
    send_alarm_msg_.unit_id = msg.unit_id();
    send_alarm_msg_.trace_id = msg.trace_id();
    send_alarm_msg_.alarm_code = msg.alarm_code();
    send_alarm_msg_.alarm_zone = msg.alarm_zone();
    send_alarm_msg_.device_type = msg.device_type();
    send_alarm_msg_.alarm_location = msg.alarm_location();
    send_alarm_msg_.alarm_customize = msg.alarm_customize();
    send_alarm_msg_.manager_account_id = msg.mng_account_id();
    Snprintf(send_alarm_msg_.mac, sizeof(send_alarm_msg_.mac), msg.mac().c_str());
    Snprintf(send_alarm_msg_.time, sizeof(send_alarm_msg_.time), msg.time().c_str());
    Snprintf(send_alarm_msg_.type, sizeof(send_alarm_msg_.type), msg.alarm_type().c_str());
    Snprintf(send_alarm_msg_.address, sizeof(send_alarm_msg_.address), msg.address().c_str());
    Snprintf(send_alarm_msg_.msg_seq, sizeof(send_alarm_msg_.msg_seq), msg.msg_seq().c_str());
    Snprintf(send_alarm_msg_.from_local, sizeof(send_alarm_msg_.from_local), msg.from_local().c_str());
    if (msg.address().size() > 0)
    {
        CNodeInfo cNodeCfg(msg.address());
        Snprintf(send_alarm_msg_.APT, sizeof(send_alarm_msg_.APT), cNodeCfg.getRoomNumber().c_str());
    }

    return;
}

void RouteP2PAlarmNotify::SendAlarmNotifyToDev(const AK::Server::P2PSendAlarmNotifyMsg& msg)
{
    ResidentDev dev;
    if (g_resid_srv_ptr->GetDevSetting(receiver_endpoint_, dev) < 0)
    {
        AK_LOG_ERROR << "SendAlarmNotifyToDev GetDevSetting failed, mac = " << receiver_endpoint_;
        return;
    }

    uint16_t msg_id = 0;
    std::string notify_msg;

    // 注意: 管理机和室内机用的不是同一条信令
    if (dev.dev_type == DEVICE_TYPE_MANAGEMENT)
    {
        msg_id = MSG_TO_DEVICE_MANAGE_ALARM;
        GetMsgBuildHandleInstance()->BuildAlarmNotifyManageMsg(send_alarm_msg_, notify_msg);
    }
    else if (dev.dev_type == DEVICE_TYPE_INDOOR)
    {
        msg_id = MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED;
        GetMsgBuildHandleInstance()->BuildAlarmOccuredNotifyMsg(send_alarm_msg_, notify_msg);
    }

    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, notify_msg, msg_id, socket_message, MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT) != 0)
    {
        AK_LOG_ERROR << "SendAlarmNotifyToDev BuildDclientMacEncMsg failed, mac=" << receiver_endpoint_;
        return;
    }

    GetClientControlInstance()->SendTransferMsg(receiver_endpoint_, dev.conn_type, socket_message.data, socket_message.size);
    AK_LOG_INFO << "SendAlarmNotifyToDev mac = " << receiver_endpoint_ << ", conn_type = " << dev.conn_type;
    return;
}

void RouteP2PAlarmNotify::SendAlarmNotifyToApp(const AK::Server::P2PSendAlarmNotifyMsg& msg)
{
    CResid2AppMsg msg_sender;

    //在线消息构造
    std::string notify_msg;
    Snprintf(send_alarm_msg_.site, sizeof(send_alarm_msg_.site), receiver_endpoint_.c_str());
    GetMsgBuildHandleInstance()->BuildAlarmOccuredNotifyMsg(send_alarm_msg_, notify_msg);

    // 构造离线消息
    BuildOfflinePushNotifyMsg(msg, msg_sender);
    AK_LOG_INFO << "SendAlarmNotifyToApp, account = " << receiver_endpoint_;

    //消息发送给csmain
    msg_sender.SetOnlineMsgData(notify_msg);
    msg_sender.SetClient(receiver_endpoint_);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetMsgId(MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_ALARM);
    return;
}

void RouteP2PAlarmNotify::BuildOfflinePushNotifyMsg(const AK::Server::P2PSendAlarmNotifyMsg& msg, CResid2AppMsg& msg_sender)
{
    std::string dev_location = msg.from_local().size() > 0 ? msg.from_local() : msg.mac();

    msg_sender.InsertOfflineMsgKV("mac_sip", msg.mac());
    msg_sender.InsertOfflineMsgKV("device_name", dev_location);
    msg_sender.InsertOfflineMsgKV("alarm_msg", msg.alarm_type());
    msg_sender.InsertOfflineMsgKV("alarm_id", std::to_string(send_alarm_msg_.id));
    msg_sender.InsertOfflineMsgKV("alarm_code", std::to_string(send_alarm_msg_.alarm_code));
    msg_sender.InsertOfflineMsgKV("alarm_zone", std::to_string(send_alarm_msg_.alarm_zone));
    msg_sender.InsertOfflineMsgKV("alarm_location", std::to_string(send_alarm_msg_.alarm_location));
    msg_sender.InsertOfflineMsgKV("alarm_customize", std::to_string(send_alarm_msg_.alarm_customize));

    // 查询当前站点
    ResidentPerAccount current_site;
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(receiver_endpoint_, current_site))
    {
        AK_LOG_INFO << "BuildOfflinePushNotifyMsg GetUidAccount failed, receiver_endpoint = " << receiver_endpoint_;
        return;
    }

    // 强提醒状态使用当前站点的
    msg_sender.InsertOfflineMsgKV("enable_strong_reminder", std::to_string(current_site.strong_alarm));

    // 一人多套房增加 title_prefix 和 site字段
    if (dbinterface::ProjectUserManage::IsMultiSiteUser(current_site.user_info_uuid))
    {
        // 标题使用当前站点查询
        std::string title;
        OfflinePush::GetMultiSiteUserTitle(receiver_endpoint_, title);

        msg_sender.InsertOfflineMsgKV("title_prefix", title);
        msg_sender.InsertOfflineMsgKV("site", receiver_endpoint_);
    }

    msg_sender.InsertOfflineMsgKV("traceid", std::to_string(send_alarm_msg_.trace_id));
    return;
}
