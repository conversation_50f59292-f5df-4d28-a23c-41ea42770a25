#ifndef _ROUTE_P2P_ALARM_NOTIFY_H_
#define _ROUTE_P2P_ALARM_NOTIFY_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "Resid2AppMsg.h"
#include "AK.BackendCommon.pb.h"

class RouteP2PAlarmNotify : public IRouteBase
{
public:
    RouteP2PAlarmNotify() = default;
    ~RouteP2PAlarmNotify() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PAlarmNotify>();}
    std::string FuncName() {return func_name_;}
    
private:
    void SendAlarmNotifyToApp(const AK::Server::P2PSendAlarmNotifyMsg& msg);
    void SendAlarmNotifyToDev(const AK::Server::P2PSendAlarmNotifyMsg& msg);
    
    void GetAlarmSendMsg(const AK::Server::P2PSendAlarmNotifyMsg& msg);
    void BuildOfflinePushNotifyMsg(const AK::Server::P2PSendAlarmNotifyMsg& msg, CResid2AppMsg& offline_push_info);
private:
    std::string receiver_endpoint_;
    std::string func_name_ = "RouteP2PAlarmNotify";
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;
    
    SOCKET_MSG_ALARM_SEND send_alarm_msg_;
};

#endif //_ROUTE_P2P_ALARM_NOTIFY_H_
