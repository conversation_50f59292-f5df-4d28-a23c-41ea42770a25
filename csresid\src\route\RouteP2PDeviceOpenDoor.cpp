/*
#include "RouteP2PDeviceOpenDoor.h"
#include "AkcsCommonDef.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "DclientMsgDef.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "RouteFactory.h"
#include "AkcsMsgDef.h"

__attribute__((constructor))  static void init()
{
    IRouteBasePtr p = std::make_shared<RouteP2PDeviceOpenDoor>();
    RegRouteFunc(p, AKCS_M2R_P2P_DEVICE_OPEN_DOOR_MSG);
};

int RouteP2PDeviceOpenDoor::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    */
   /*
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    const AK::Server::P2PDeviceOpenDoorNotifyMsg& opendoor_msg = base_msg.p2pdeviceopendoornotifymsg2();

    ResidentDev dev;
    GetDevClientByMac(opendoor_msg.mac(), dev);

    std::string dclient_msg;
    SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR device_open_door_msg;
    ::memset(&device_open_door_msg, 0, sizeof(device_open_door_msg));
    Snprintf(device_open_door_msg.protocal, sizeof(device_open_door_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(device_open_door_msg.mac, sizeof(device_open_door_msg.mac), msg.mac().c_str());
    Snprintf(device_open_door_msg.relay, sizeof(device_open_door_msg.relay), std::to_string(msg.relay()).c_str());
    Snprintf(device_open_door_msg.trace_id, sizeof(device_open_door_msg.trace_id), msg.msg_traceid().c_str());

    if (msg.type() == (int)OpenDoorRelayType::RELAY) {
        GetMsgBuildHandleInstance()->BuildRequestOpenDoorMsg(device_open_door_msg, dclient_msg);
    } else if (msg.type() == (int)OpenDoorRelayType::SECURITY_RELAY) {
        GetMsgBuildHandleInstance()->BuildRequestOpenSecurityRelayMsg(device_open_door_msg, dclient_msg);
    }

    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.send_type = TransP2PMsgType::TRAN_TYPE_NONE;
    send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    send_msg.msg_id = MSG_FROM_DEVICE_REQUEST_OPENDOOR;
    send_msg.client_type = dev.conn_type;
    Snprintf(send_msg.client, sizeof(send_msg.client), msg.from_mac().c_str());
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), dclient_msg.c_str());
    send_msg.msg_len = strlen(send_msg.msg_data);
    GetClientControlInstance()->SendTransferMsg(send_msg);

    CSP2A_REMOTE_OPENDDOR_INFO open_door;
    ::memset(&open_door, 0, sizeof(open_door));
    Snprintf(open_door.uid, sizeof(open_door.uid), dev.node);
    Snprintf(open_door.mac, sizeof(open_door.mac), opendoor_msg.mac().c_str());
    Snprintf(open_door.trace_id, sizeof(open_door.trace_id), opendoor_msg.msg_traceid().c_str());
    Snprintf(open_door.repost_mac, sizeof(open_door.repost_mac), opendoor_msg.from_mac().c_str());
    open_door.relay = static_cast<int>(opendoor_msg.relay());

    std::string open_door_type = "";
    if (opendoor_msg.type() == (int)OpenDoorRelayType::RELAY)
    {
        open_door_type = SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY;
    }
    else if (opendoor_msg.type() == (int)OpenDoorRelayType::SECURITY_RELAY)
    {
        open_door_type = SOCKET_MSG_TYPE_NAME_OPENDOOR;
    }

    AK_LOG_INFO << "csresid receive device open door msg from router:type=" << open_door_type
                << ", uid=" << open_door.uid << ", mac=" << open_door.mac << ", relay=" << open_door.relay
                << ", repost_mac=" << open_door.repost_mac << ", traceid" << open_door.trace_id;

    HandleP2POpenDoorReq(open_door, open_door_type);
    return 0;
*/

/*
    return 0;
}

void RouteP2PDeviceOpenDoor::HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO& open_door, const std::string& open_door_type)
{
    int relay = open_door.relay;
    std::string uid = open_door.uid;
    std::string mac = open_door.mac;
    std::string accessible_floor = "0";             // 分号分割楼层, eg: "1;2;3;6;7;8;9;"
    std::string msg_traceid = open_door.trace_id;   // 
    std::string receiver_mac = strlen(open_door.repost_mac) > 0 ? (open_door.repost_mac) : (open_door.mac);

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(uid, sip_info);

    if (sip_info.sip_type == csmain::COMMUNITY_DEV)
    {
        ResidentDev dev;
        if (dbinterface::ResidentDevices::GetSipDev(uid, dev) == 0)
        {
            CommunityRoomInfo room;
            if (dbinterface::CommunityRoom::GetCommunityRoomByNode(dev.node, room) == 0)
            {
                accessible_floor = room.floor;
            }
        }
    }
    else if (sip_info.sip_type == csmain::COMMUNITY_APP)
    {
        std::string node;
        if (dbinterface::CommPersonalAccount::GetUidNode(uid, node) == 0)
        {
            // 社区用户多楼层
            accessible_floor = dbinterface::PersonalAccountCommunityInfo::GetFloorByAccount(uid);

            // 社区用户apt_floor
            CommunityRoomInfo room;
            if (dbinterface::CommunityRoom::GetCommunityRoomByNode(node, room) == 0)
            {
                accessible_floor = GetAccessibleFloor(room.floor, accessible_floor);
            }
        }
    }

    if (accessible_floor.length() == 0)
    {
        accessible_floor = "0";
    }

    //发送REMOTE_CONTROL消息
    SOCKET_MSG_REMOTE_CONTROL remote_control_msg;
    memset(&remote_control_msg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));
    Snprintf(remote_control_msg.protocal, sizeof(remote_control_msg.protocal), PROTOCAL_NAME_DEFAULT);
    Snprintf(remote_control_msg.type, sizeof(remote_control_msg.type), open_door_type.c_str());
    Snprintf(remote_control_msg.item[0], sizeof(remote_control_msg.item[0]), uid.c_str());
    ::snprintf(remote_control_msg.item[1], sizeof(remote_control_msg.item[1]), "%d", relay);
    Snprintf(remote_control_msg.item[2], sizeof(remote_control_msg.item[2]), msg_traceid.c_str());
    ::snprintf(remote_control_msg.item[3], sizeof(remote_control_msg.item[3]), "%s", accessible_floor.c_str());
    ::snprintf(remote_control_msg.item[4], sizeof(remote_control_msg.item[4]), "%s", mac.c_str());

    AK_LOG_INFO << "Request open door uid=" << uid
        << ", mac=" << mac << ", repost_mac=" << open_door.repost_mac
        << ", traceid=" << msg_traceid << ", open_door_type=" << open_door_type
        << ", accessible_floor=" << accessible_floor;

    char xml_msg[4096];
    memset(xml_msg, 0, sizeof(xml_msg));
    GetMsgBuildHandleInstance()->BuildRemoteControlMsg(xml_msg, sizeof(xml_msg), &remote_control_msg);

    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.msg_id = MSG_TO_DEVICE_REMOTE_CONTROL;
    send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
    send_msg.enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    Snprintf(send_msg.client, sizeof(send_msg.client), receiver_mac.c_str());
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), xml_msg);
    send_msg.msg_len = strlen(send_msg.msg_data);

    GetClientControlInstance()->SendTransferMsg(send_msg);
    return;
}

*/