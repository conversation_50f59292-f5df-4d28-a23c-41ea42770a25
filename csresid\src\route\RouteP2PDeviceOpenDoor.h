#ifndef 0
#define _ROUTE_P2P_DEVICE_OPEN_DOOR_H_

#include "RouteBase.h"
#include <string>
#include "AkcsWebMsgSt.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "AK.BackendCommon.pb.h"

class RouteP2PDeviceOpenDoor : public IRouteBase
{
public:
    RouteP2PDeviceOpenDoor(){}
    ~RouteP2PDeviceOpenDoor() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PDeviceOpenDoor>();}
    std::string FuncName() {return func_name_;}

private:
    int client_type_;
    std::string func_name_ = "RouteP2PDeviceOpenDoor";
    
    void HandleP2POpenDoorReq(const CSP2A_REMOTE_OPENDDOR_INFO& open_door, const std::string& open_door_type);
};

#endif //_ROUTE_P2P_DEVICE_OPEN_DOOR_H_