#include "RouteP2PEmergencyControlNotify.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "util_time.h"
#include "MsgStruct.h"
#include "MsgControl.h"
#include "ResidPushClient.h"
#include "PushClientMng.h"
#include "dbinterface/OfflinePushInfo.h"
#include "SnowFlakeGid.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "Resid2AppMsg.h"

extern std::map<string, AKCS_DST> g_time_zone_DST; 

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PEmergencyControlNotify>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG);
};

int RouteP2PEmergencyControlNotify::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    const AK::Server::P2PSendEmergencyNotifyMsg& msg = base_msg.p2psendemergencynotifymsg2();
    
    int receiver_type = base_msg.type();
    int control_type = msg.control_type();
    std::string receiver_uid = msg.receiver_uid();

    if (receiver_type == TransP2PMsgType::TO_APP_UID)
    {
        if (0 != SendEmergencyNotifyToApp(control_type, receiver_uid, receiver_uid, base_msg))
        {
            AK_LOG_WARN << "send emergency notify to app failed. app account=" << receiver_uid;
            return -1;
        }
    }
    else if (receiver_type == TransP2PMsgType::TO_DEV_MAC)
    {
        if (0 != SendEmergencyNotifyToDev(control_type, receiver_uid, base_msg))
        {
            AK_LOG_WARN << "send emergency notify to dev failed. device mac=" << msg.receiver_uid();
            return -1;
        }
    }
    else
    {
        AK_LOG_WARN << "send emergency control msg failed. receiver type not support.";
        return -1;
    }
    

    return 0;
}

int RouteP2PEmergencyControlNotify::SendEmergencyNotifyToApp(int control_type, const std::string& account, const std::string& receiver_account, const AK::BackendCommon::BackendP2PBaseMessage& base_msg)
{
    if (dbinterface::ProjectUserManage::MultiSiteLimit(receiver_account))
    {
        AK_LOG_INFO << "multi site account abnormal. no need to notify. site=" << receiver_account;
        return -1;
    }
    
    CResid2AppMsg msg_sender;

    //在线消息构造
    std::string dclient_msg;
    std::string time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(receiver_account, g_time_zone_DST);
    GetMsgBuildHandleInstance()->BuildEmergencyControlNotifyMsg(control_type, time, receiver_account, dclient_msg);

    //离线消息构造
    std::string title;
    msg_sender.InsertOfflineMsgKV("control_type", std::to_string(control_type));
    if (OfflinePush::GetMultiSiteUserTitle(receiver_account, title) == 0)
    {
        msg_sender.InsertOfflineMsgKV("title_prefix", title);
        msg_sender.InsertOfflineMsgKV("site", receiver_account);
    }

    //消息发送给csmain
    msg_sender.SetClient(account);
    msg_sender.SetOnlineMsgData(dclient_msg);
    msg_sender.SetSendType(TransP2PMsgType::TO_APP_UID);
    msg_sender.SetEncType(MsgEncryptType::TYEP_DEFAULT_ENCRYPT);
    msg_sender.SetMsgId(MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY);
    msg_sender.SendMsg(csmain::PUSH_MSG_TYPE_EMERGENCY_NOTIFY);
    return 0;
}

int RouteP2PEmergencyControlNotify::SendEmergencyNotifyToDev(int control_type, const std::string& receiver_mac, const AK::BackendCommon::BackendP2PBaseMessage& base_msg)
{
    ResidentDev dev;
    GetDevClientByMac(receiver_mac, dev);

    //获取当前时间，以y-m-d h:m:s的格式
    std::string time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(dev.node, g_time_zone_DST);
    std::string msg;

    GetMsgBuildHandleInstance()->BuildEmergencyControlNotifyMsg(control_type, time, receiver_mac, msg);

    uint16_t msg_id = MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY;
    MsgEncryptType enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (BuildDclientMacEncMsg(dev, msg, msg_id, socket_message, enc_type) != 0)
    {
        AK_LOG_WARN << "BuildDclientMacEncMsg failed. mac=" << receiver_mac;
        return -1;
    }
    if (GetClientControlInstance()->SendTransferMsg(receiver_mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendTransferMsg failed. mac=" << receiver_mac;
        return -1;
    }

    return 0;
}