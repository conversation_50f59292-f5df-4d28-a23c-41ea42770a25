#ifndef _ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_
#define _ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "AK.BackendCommon.pb.h"

class RouteP2PEmergencyControlNotify : public IRouteBase
{
public:
    RouteP2PEmergencyControlNotify(){}
    ~RouteP2PEmergencyControlNotify() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PEmergencyControlNotify>();}
    std::string FuncName() {return func_name_;}

private:
    int client_type_;
    std::string func_name_ = "RouteP2PEmergencyControlNotify";
    
    int SendEmergencyNotifyToApp(int control_type, const std::string& account, const std::string& receiver_account, const AK::BackendCommon::BackendP2PBaseMessage& base_msg);
    int SendEmergencyNotifyToDev(int control_type, const std::string& receiver_mac, const AK::BackendCommon::BackendP2PBaseMessage& base_msg);
};

#endif //_ROUTE_P2P_EMERGENCY_CONTROL_NOTIFY_H_