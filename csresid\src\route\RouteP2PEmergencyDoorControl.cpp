#include "RouteP2PEmergencyDoorControl.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "MsgBuild.h"
#include "util.h"
#include "NotifyPerText.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "Resid2RouteMsg.h"
#include "RouteFactory.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "BasicDefine.h"
#include "EmergencyMsgControl.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PEmergencyDoorControl>();
    RegRouteFunc(p, AKCS_M2R_EMERGENCY_DOOR_CONTROL);
};

int RouteP2PEmergencyDoorControl::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    auto msg = base_msg.p2ppmemergencydoorcontrolmsg2();
    
    GetEmergencyControlInfo(msg);

    AK_LOG_INFO << "handle emergency door control ,msg_uuid= " << control_msg_.msg_uuid << " device_uuid=" << control_msg_.device_uuid;

    if (0 != CheckAndRecordAbnormalControl())
    {
        return -1;
    }

    //插入时间轮
    GetEmergencyControlInstance()->InsertIntoTimingWheel(mac_, control_msg_.msg_uuid, control_msg_.device_uuid, control_msg_.initiator, act_type_);

    return 0;
}

void RouteP2PEmergencyDoorControl::GetEmergencyControlInfo(const AK::Server::P2PPmEmergencyDoorControlMsg& msg)
{
    //control msg
    memset(&control_msg_, 0, sizeof(control_msg_));
    Snprintf(control_msg_.msg_uuid, sizeof(control_msg_.msg_uuid), msg.msg_uuid().c_str());
    Snprintf(control_msg_.device_uuid, sizeof(control_msg_.device_uuid), msg.device_uuid().c_str());
    Snprintf(control_msg_.initiator, sizeof(control_msg_.initiator), msg.initiator().c_str());
    Snprintf(control_msg_.relay, sizeof(control_msg_.relay), msg.relay().c_str());
    Snprintf(control_msg_.security_relay, sizeof(control_msg_.security_relay), msg.security_relay().c_str());
    control_msg_.auto_manual = msg.auto_manual();
    control_msg_.operation_type = msg.operation_type();

    //act type
    if(control_msg_.auto_manual == OPERATE_TYPE::MANUAL)
    {
        if(control_msg_.operation_type == CONTROL_TYPE::OPEN_DOOR)
        {
            act_type_ = ACT_OPEN_DOOR_TYPE::PM_UNLOCK;
        }
        else
        {
            act_type_ = ACT_OPEN_DOOR_TYPE::PM_LOCK;
        }
    }
    else
    {
        act_type_ = ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK;
    }

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(control_msg_.device_uuid, dev))
    {
        AK_LOG_WARN << "get device info failed. device uuid=" << control_msg_.device_uuid;
        return;
    }

    mac_ = dev.mac;
}

int RouteP2PEmergencyDoorControl::CheckAndRecordAbnormalControl()
{
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(control_msg_.device_uuid, dev))
    {
        AK_LOG_WARN << "get device info failed. device uuid=" << control_msg_.device_uuid;
        return -1;
    }
    if (dev.status == DeviceStatus::DEVICE_STATUS_OFFLINE)
    {
        AK_LOG_WARN << "remote control failed. device is offline.";
        dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(control_msg_.msg_uuid, control_msg_.device_uuid, dbinterface::RelayStatus::OFFLINE);
        dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(control_msg_.device_uuid, control_msg_.initiator, act_type_, dbinterface::RelayStatus::OFFLINE, gstAKCSLogDelivery.personal_capture_delivery);
        return -1; 
    }
    if (dev.dclient_ver < D_CLIENT_VERSION_6520)
    {
        AK_LOG_WARN << "remote control failed. device dclient version is too low. dclient ver=" << dev.dclient_ver;
        dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(control_msg_.msg_uuid, control_msg_.device_uuid, dbinterface::RelayStatus::VERSION_MISMATCH);
        dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(control_msg_.device_uuid, control_msg_.initiator, act_type_, dbinterface::RelayStatus::VERSION_MISMATCH, gstAKCSLogDelivery.personal_capture_delivery);
        return -1;
    }

    return 0;
}

int RouteP2PEmergencyDoorControl::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    msg_id = 0;
    if(control_msg_.operation_type == CONTROL_TYPE::OPEN_DOOR)
    {
        msg_id = MSG_TO_DEVICE_REQUEST_EMERGENCY_KEEP_OPEN_DOOR;
    }
    else
    {
        msg_id = MSG_TO_DEVICE_REQUEST_EMERGENCY_CLOSE_DOOR;
    }
    to_mac = mac_;
    enc_type = MsgEncryptType::TYEP_MAC_ENCRYPT;
    GetMsgBuildHandleInstance()->BuildEmergencyDoorControlMsg(control_msg_, mac_, msg);
    return 0;
}