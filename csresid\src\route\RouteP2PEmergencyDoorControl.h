#ifndef _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_
#define _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AK.Server.pb.h"

class RouteP2PEmergencyDoorControl : public IRouteBase
{
public:
    RouteP2PEmergencyDoorControl(){}
    ~RouteP2PEmergencyDoorControl() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PEmergencyDoorControl>();}
    std::string FuncName() {return func_name_;}

private:
    void GetEmergencyControlInfo(const AK::Server::P2PPmEmergencyDoorControlMsg& msg);
    int CheckAndRecordAbnormalControl();
    std::string func_name_ = "RouteP2PEmergencyDoorControl";
    SOCKET_MSG_EMERGENCY_CONTROL control_msg_;//远程控制相关消息
    ACT_OPEN_DOOR_TYPE act_type_; //动作类型
    std::string mac_; //下发的设备mac
};

#endif // _ROUTE_P2P_EMERGENCY_DOOR_CONTROL_H_