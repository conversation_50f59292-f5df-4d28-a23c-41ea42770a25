#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteP2PMsg.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "Resid2RouteMsg.h"

#include "ProjectUserManage.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PMsg>();
    RegRouteFunc(p, AKCS_BUSSNESS_P2P_MSG);
};


int RouteP2PMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{

    return 0; 
}

int RouteP2PMsg::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{

    return 0;
}

