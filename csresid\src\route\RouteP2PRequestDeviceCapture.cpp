#include "RouteP2PRequestDeviceCapture.h"
#include "AkcsCommonDef.h"
#include "MsgBuild.h"
#include "RouteFactory.h"
#include "AkcsMsgDef.h"
#include "MsgControl.h"
#include "DeviceSetting.h"
#include "SafeCacheConn.h"
#include "Resid2RouteMsg.h"
#include "doorlog/UserInfo.h"
#include "VideoRecordClient.h"
#include "VideoRecordClientMng.h"
#include "RequestMonitorCapture.h"
#include "RequestVideoRecordUtil.hpp"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "Md5.h"
#include "doorlog/RecordActLog.h"


__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PRequestDeviceCapture>();
    RegRouteFunc(p, AKCS_M2R_P2P_REQUEST_DEVICE_CAPTURE_MSG);
};

int RouteP2PRequestDeviceCapture::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid();

    auto msg = base_msg.p2prequestdevicecapturenotifymsg2();

    Snprintf(request_capture_.mac, sizeof(request_capture_.mac), msg.mac().c_str());
    Snprintf(request_capture_.site, sizeof(request_capture_.site), msg.site().c_str());
    Snprintf(request_capture_.uuid, sizeof(request_capture_.uuid), msg.uuid().c_str());
    Snprintf(request_capture_.camera, sizeof(request_capture_.camera), msg.camera().c_str());

    // 获取截图的site
    GetCaptureSite();

    // 获取截图的flow
    GenerateFlowInfo();

    // 插入log记录
    if (0 != InsertCaptureLog())
    {
        AK_LOG_WARN << "Add app request capture failed, mac = " << request_capture_.mac << ", site = " << request_capture_.site;
        return -1;
    }

    // 发送录制请求
    //SendStartRecord();
    //发送截图
    SendStartCapture();


    return 0;
}

void RouteP2PRequestDeviceCapture::GetCaptureSite()
{
    return;
}

void RouteP2PRequestDeviceCapture::GenerateFlowInfo()
{
    std::string flow_uuid = GetFlowUUID(request_capture_.mac, request_capture_.camera, request_capture_.stream_id);
    Snprintf(request_capture_.flow_uuid, sizeof(request_capture_.flow_uuid), flow_uuid.c_str());
}

int RouteP2PRequestDeviceCapture::InsertCaptureLog()
{
    UIPC_MSG_CAPTURE_RTSP monitor_capture;
    monitor_capture.capture_type = CMsgControl::ActOpenDoorType::CLOUD_APP_MANUAL;
    Snprintf(monitor_capture.mac, sizeof(monitor_capture.mac), request_capture_.mac);

    // 获取操作人名称
    ResidentPerAccount account;
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(request_capture_.site, account))
    {
        Snprintf(monitor_capture.username, sizeof(monitor_capture.username), account.name);
        Snprintf(request_capture_.username, sizeof(request_capture_.username), account.name);
    }

    // 获取node
    std::string node;
    dbinterface::ResidentPersonalAccount::GetNodeByAccount(request_capture_.site, node);
    Snprintf(monitor_capture.node, sizeof(monitor_capture.node), node.c_str());
    Snprintf(request_capture_.node, sizeof(request_capture_.node), node.c_str());

    // 获取room_num
    CNodeInfo node_config(node);
    Snprintf(monitor_capture.room_num, sizeof(monitor_capture.room_num), node_config.getRoomNumber().c_str());

    ResidentDev dev;
    if (0 == GetDeviceSettingInstance()->GetDeviceSettingByMac(request_capture_.mac, dev))
    {
        monitor_capture.manager_id = dev.project_mng_id;
        monitor_capture.device_type = dev.is_public;
        monitor_capture.manager_type = dev.is_personal;
        Snprintf(monitor_capture.dev_uuid, sizeof(monitor_capture.dev_uuid), dev.uuid);
        Snprintf(monitor_capture.location, sizeof(monitor_capture.location), dev.location);
        Snprintf(monitor_capture.sip_account, sizeof(monitor_capture.sip_account), dev.sip);
    }

    // 获取截图名称
    pic_name_ = CapturePicName(request_capture_.mac);
    Snprintf(monitor_capture.picture_name, sizeof(monitor_capture.picture_name), pic_name_.c_str());
    Snprintf(request_capture_.pic_name, sizeof(request_capture_.pic_name), pic_name_.c_str());

    //设置CaptureProjectInfo
    if (RecordActLog::GetInstance().RewriteRtspCaptureProjectInfo(monitor_capture, dev) != 0 ) 
    {
        AK_LOG_WARN << "Rewrite CaptureProjectInfo error mac:" << dev.mac;
        return -1;
    }

    // 插入log记录
    if (dbinterface::PersonalCapture::AddUidReqCapture(monitor_capture, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        return -1;
    }
    return 0;
}

void RouteP2PRequestDeviceCapture::SendStartRecord()
{
    if (request_capture_.record_video == 0)
    {
        AK_LOG_INFO << "Not Need Record Video, site = " << request_capture_.site << ", mac = " << request_capture_.mac;
        return;
    }

    // 随机获取一个VideoRecordRPC服务
    auto client_instance = VideoRecordClientMng::Instance()->GetRpcRandomClientInstance();
    VideoRecordRpcClientPtr grpc_client = client_instance.second;
    if (grpc_client)
    {
        // 开始录制
        grpc_client->StartVideoRecord(request_capture_.site, request_capture_.mac);

        // 缓存本次录制信息
        SetRecordCache(client_instance.first);
        AK_LOG_INFO << "Grpc SendStartRecord Success, site = " << request_capture_.site << ", mac = " << request_capture_.mac;
        return;
    }

    AK_LOG_INFO << "Grpc SendStartRecord Failed, grpc_client is nullptr, site = " << request_capture_.site << ", mac = " << request_capture_.mac;
    return;
}

void RouteP2PRequestDeviceCapture::SetRecordCache(const std::string& server_id)
{
    std::string stream_key = ReqVideoRecordUtil::VideoRecodStreamKey(request_capture_.site, request_capture_.mac);
    std::string logic_srv_cache_key = ReqVideoRecordUtil::LogicServerCacheKey(request_capture_.site, request_capture_.mac);

    SafeCacheConn redis(g_redis_db_video_record);
    if (redis.isConnect())
    {
        // 记录本次录制的pic_name, 用于录制完成更新数据库
        redis.setex(stream_key, 60, pic_name_);

        // 缓存使用那台服务器录制
        redis.setex(logic_srv_cache_key, 60, server_id);
        AK_LOG_INFO << "SetRecordCache, stream_key = " << stream_key << ", server_id = " << server_id << ", pic_name = " << pic_name_;
    }
    return;
}

void RouteP2PRequestDeviceCapture::SendStartCapture()
{
    CResid2RouteMsg::SendMonitorCaptureMsg(request_capture_);
    return;
}

std::string RouteP2PRequestDeviceCapture::CapturePicName(const std::string& mac)
{
    time_t timer = time(nullptr);
    char time_sec[16] = {0};
    ::snprintf(time_sec, 16, "%ld", timer);
    uint32_t sequence = SecSeqCreate();
    char seq_sec[16] = {0};
    ::snprintf(seq_sec, 16, "%d", sequence);

    std::string temp_string = "ak_ftp:" + mac + ":" + time_sec;
    std::string picture_code = akuvox_encrypt::MD5(temp_string).toStr();
    std::string pic_name = mac + "-" + time_sec + "_" + seq_sec + "_APP_" + picture_code +  ".jpg";
    return pic_name;
}
