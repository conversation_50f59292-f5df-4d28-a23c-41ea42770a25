#ifndef _ROUTE_P2P_REQUEST_DEVICE_CAPTURE_H_
#define _ROUTE_P2P_REQUEST_DEVICE_CAPTURE_H_

#include "RouteBase.h"
#include <string>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "AK.BackendCommon.pb.h"

class RouteP2PRequestDeviceCapture : public IRouteBase
{
public:
    RouteP2PRequestDeviceCapture(){}
    ~RouteP2PRequestDeviceCapture() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);

    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PRequestDeviceCapture>();}
    std::string FuncName() {return func_name_;}

    void SendStartCapture();
    void SendStartRecord();
    int InsertCaptureLog();
    void SetRecordCache(const std::string& server_id);
    std::string CapturePicName(const std::string& mac);

private:
    int client_type_;
    std::string func_name_ = "RouteP2PRequestDeviceCapture";

    std::string pic_name_;
    SOCKET_MSG_REQ_CAPTURE request_capture_;
    const MsgEncryptType enc_type_ = MsgEncryptType::TYEP_DEFAULT_ENCRYPT;
    void GenerateFlowInfo();
    void GetCaptureSite();
};

#endif //_ROUTE_P2P_REQUEST_DEVICE_CAPTURE_H_