#ifndef _ROUTE_P2P_DELIVERY_MSG_H_
#define _ROUTE_P2P_DELIVERY_MSG_H_
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "DclientMsgSt.h"
#include "RouteBase.h"

class RouteP2PSendDeliveryMsg : public IRouteBase
{
public:
    RouteP2PSendDeliveryMsg(){}
    ~RouteP2PSendDeliveryMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PSendDeliveryMsg>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteP2PSendDeliveryMsg";
    int SendMessageToApp(SOCKET_MSG_SEND_TEXT_MESSAGE& text_send, const AK::BackendCommon::BackendP2PBaseMessage& base_msg,
                                           const AK::Server::P2PSendDeliveryMsg& server_msg);
    int SendMessageToDev(SOCKET_MSG_SEND_TEXT_MESSAGE& text_send, const AK::BackendCommon::BackendP2PBaseMessage& base_msg,
                                           const AK::Server::P2PSendDeliveryMsg& server_msg);
};

#endif // _ROUTE_P2P_DELIVERY_MSG_H_