#include "RouteP2PSendDeliveryMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PSendDeliveryMsg>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_DELIVERY_MSG);
};

int RouteP2PSendDeliveryMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    const AK::Server::P2PSendDeliveryMsg& msg = base_msg.p2psenddeliverymsg2();

    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), msg.title().c_str());
    Snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), msg.content().c_str());
    text_send.text_message.type = CPerTextNotifyMsg::MessageType::TEXT_MSG;
    text_send.text_message.id = msg.message_id();
    if(msg.receiver_type() == DEVICE_TYPE_APP)
    {
        return SendMessageToApp(text_send, base_msg, msg);        
    }
    else if(msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        return SendMessageToDev(text_send, base_msg, msg);
    }
    return 0;
}

int RouteP2PSendDeliveryMsg::SendMessageToApp(SOCKET_MSG_SEND_TEXT_MESSAGE& text_send, const AK::BackendCommon::BackendP2PBaseMessage& base_msg,
                                           const AK::Server::P2PSendDeliveryMsg& server_msg)
{
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if(0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(server_msg.receiver_uuid(), per_account))
    {
        text_send.client_type = CPerTextNotifyMsg::APP_SEND;
        CPerTextNotifyMsg cNotifyMsg(base_msg, text_send, per_account.account);
        GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
        return 0;
    }
    AK_LOG_WARN << "send message to app failed. receiver uuid:" << server_msg.receiver_uuid();
    return -1;
}

int RouteP2PSendDeliveryMsg::SendMessageToDev(SOCKET_MSG_SEND_TEXT_MESSAGE& text_send, const AK::BackendCommon::BackendP2PBaseMessage& base_msg,
                                           const AK::Server::P2PSendDeliveryMsg& server_msg)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if(0 != dbinterface::ResidentDevices::GetUUIDDev(server_msg.receiver_uuid(), dev)) //目前只做在社区
    {
        AK_LOG_WARN << "HandleP2PDoorcomDeliveryMsg failed, device uuid not found. uuid:" << server_msg.receiver_uuid();
        return -1;
    }
    char dclient_msg[4096];
    memset(dclient_msg, 0, sizeof(dclient_msg));
    if(0 != GetMsgBuildHandleInstance()->BuildTextMessageXmlMsg(dclient_msg, sizeof(dclient_msg), &text_send.text_message))
    {
        AK_LOG_WARN << "BuildTextMessageXmlMsg failed";
        return -1;
    }
    MsgStruct send_msg;
    memset(&send_msg, 0, sizeof(send_msg));
    send_msg.send_type = TransP2PMsgType::TO_DEV_MAC;
    send_msg.enc_type = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;
    send_msg.msg_id = MSG_TO_DEVICE_SEND_TEXT_MESSAGE;
    Snprintf(send_msg.client, sizeof(send_msg.client), dev.mac);
    Snprintf(send_msg.msg_data, sizeof(send_msg.msg_data), dclient_msg);
    send_msg.msg_len = strlen(send_msg.msg_data);
    GetClientControlInstance()->SendTransferMsg(send_msg);
    return 0;
}