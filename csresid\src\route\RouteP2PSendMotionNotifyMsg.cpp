#include "RouteP2PSendMotionNotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyPerText.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DclientMsgDef.h"
#include "AkcsMsgDef.h"
#include "RouteFactory.h"
#include "util.h"
#include "MsgBuild.h"
#include "ClientControl.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "NotifyPerMotion.h"
#include "NotifyMsgControl.h"

__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PSendMotionNotifyMsg>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_MOTION_NOTIFY_MSG);
};

int RouteP2PSendMotionNotifyMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    CHECK_PB_PARSE_MSG_ERR_RET(base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    const AK::Server::P2PSendMotionNotifyMsg& p2p_msg = base_msg.p2psendmotionnotifymsg2();
    
    SOCKET_MSG_MOTION_ALERT_SEND recv_motion;
    memset(&recv_motion, 0, sizeof(recv_motion));
    Snprintf(recv_motion.mac, sizeof(recv_motion.mac), p2p_msg.mac().c_str());
    Snprintf(recv_motion.node, sizeof(recv_motion.node), p2p_msg.node().c_str());
    Snprintf(recv_motion.location, sizeof(recv_motion.location), p2p_msg.dev_location().c_str());
    Snprintf(recv_motion.sip_account, sizeof(recv_motion.sip_account), p2p_msg.sip_account().c_str());
    Snprintf(recv_motion.capture_time, sizeof(recv_motion.capture_time), p2p_msg.motion_time().c_str());
    recv_motion.id = p2p_msg.id();
    recv_motion.detection_type = p2p_msg.detection_type();
    recv_motion.detection_info = p2p_msg.detection_info();

    CPerMotionNotifyMsg notify_msg(recv_motion, p2p_msg.receiver_account());
    GetMotionNotifyMsgControlInstance()->AddMotionNotifyMsg(notify_msg);

    return 0;
}