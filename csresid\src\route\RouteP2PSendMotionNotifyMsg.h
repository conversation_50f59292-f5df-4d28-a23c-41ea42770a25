#ifndef _ROUTE_P2P_SEND_MOTION_NOTIFY_MSG_H_
#define _ROUTE_P2P_SEND_MOTION_NOTIFY_MSG_H_
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "DclientMsgSt.h"
#include "RouteBase.h"

class RouteP2PSendMotionNotifyMsg : public IRouteBase
{
public:
    RouteP2PSendMotionNotifyMsg(){}
    ~RouteP2PSendMotionNotifyMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PSendMotionNotifyMsg>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteP2PSendMotionNotifyMsg";
};

#endif // _ROUTE_P2P_SEND_MOTION_NOTIFY_MSG_H_