#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteP2PSendTextNotifyMsg.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "NotifyPerText.h"
#include "NotifyMsgControl.h"
#include "ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "ClientControl.h"

extern std::map<string, AKCS_DST> g_time_zone_DST;

__attribute__((constructor))  static void init()
{
    IRouteBasePtr p = std::make_shared<RouteP2PSendTextNotifyMsg>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG);
};


int RouteP2PSendTextNotifyMsg::IControl(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage receive_base_msg;

    if (receive_base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false)
    {
        AK_LOG_WARN << "RouteP2PSendTextNotifyMsg parse pb msg failed.";
        return -1;
    }
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << receive_base_msg.type()
                << ", uid=" << receive_base_msg.uid() << ", project_type=" << receive_base_msg.project_type()
                << ", conn_type=" << receive_base_msg.conn_type() << ", msgid=" << receive_base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(receive_base_msg.msgid());

    const AK::Server::P2PCommonTxtMsgNotifyMsg msg = receive_base_msg.p2pcommontxtmsgnotifymsg2();
    if (msg.client_type() == MessageClientType::APP_SEND)
    {
        if (0 != SendMessageToApp(receive_base_msg))
        {
            AK_LOG_WARN << "Send Message To App failed. Account uuid =" << msg.uuid();
            return -1;
        }
    }
    else if (msg.client_type() == MessageClientType::DEV_SEND)
    {
        if (0 != SendMessageToDev(receive_base_msg))
        {
            AK_LOG_WARN << "Send Message To Dev failed. Dev uuid =" << msg.uuid();
            return -1;
        }
    }

    return 0;
}

int RouteP2PSendTextNotifyMsg::SendMessageToApp(const AK::BackendCommon::BackendP2PBaseMessage& recive_base_msg)
{
    const AK::Server::P2PCommonTxtMsgNotifyMsg msg = recive_base_msg.p2pcommontxtmsgnotifymsg2();

    std::string uuid = msg.uuid();
    std::string title = msg.title();
    std::string content = msg.content();
    std::string extension_field = msg.extension_field();
    std::string account;
    if (0 != dbinterface::ResidentPersonalAccount::GetAccountByUUID(uuid, account))
    {
        AK_LOG_WARN << "user account not found. uuid=" << uuid;
        return -1;
    }
    AK_LOG_INFO << "RouteP2PSendTextNotifyMsg: type=" << msg.client_type() << ", uuid=" << uuid;;

    SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
    memset(&text_send.text_message, 0, sizeof(text_send.text_message));
    Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title), title.c_str());
    Snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), content.c_str());
    Snprintf(text_send.text_message.extension_field, sizeof(text_send.text_message.extension_field), extension_field.c_str());
    text_send.text_message.type = msg.msg_type();
    text_send.text_message.id = msg.recv_msg_id();
    text_send.client_type = msg.client_type();
    //没传的，默认取当前时间
    std::string trigger_time;
    if (msg.time().empty())
    {
        trigger_time = dbinterface::ResidentPersonalAccount::GetAccountCurrentTimeString(account, g_time_zone_DST);
    }
    else
    {
        trigger_time = msg.time();
    }
    Snprintf(text_send.text_message.time, sizeof(text_send.text_message.time), trigger_time.c_str());

    CPerTextNotifyMsg notify_msg(recive_base_msg, text_send, account);
    GetNotifyMsgControlInstance()->AddTextNotifyMsg(notify_msg);
    return 0;
}

int RouteP2PSendTextNotifyMsg::SendMessageToDev(const AK::BackendCommon::BackendP2PBaseMessage& recive_base_msg)
{
    const AK::Server::P2PCommonTxtMsgNotifyMsg msg = recive_base_msg.p2pcommontxtmsgnotifymsg2();

    std::string uuid = msg.uuid();

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(uuid, dev))
    {
        AK_LOG_WARN << "device not found. uuid=" << uuid;
        return -1;
    }

    std::string receiver_mac = dev.mac;

    //在线dclient消息构造
    std::string xml_txt_msg;
    GetMsgBuildHandleInstance()->BuildMessageTxtInfoMsg(msg, xml_txt_msg);

    int msg_id = MSG_TO_DEVICE_SEND_TEXT_MESSAGE;
    MsgEncryptType enc_type = MsgEncryptType::TYEP_DEFAULT_MAC_ENCRYPT;

    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));

    if (BuildDclientMacEncMsg(dev, xml_txt_msg, msg_id, socket_message, enc_type) != 0)
    {
        AK_LOG_WARN << "BuildDclientMacEncMsg failed. mac=" << receiver_mac;
        return -1;
    }

    if (GetClientControlInstance()->SendTransferMsg(receiver_mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendTransferMsg failed. mac=" << receiver_mac;
        return -1;
    }

    return 0;
}

int RouteP2PSendTextNotifyMsg::IReplyToDevMsg(std::string& to_mac, std::string& msg, uint32_t& msg_id, MsgEncryptType& enc_type)
{
    return 0;
}