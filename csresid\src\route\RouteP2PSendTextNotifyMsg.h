#ifndef _ROUTE_P2P_MSG_H_
#define _ROUTE_P2P_MSG_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "DclientMsgDef.h"
#include "AK.BackendCommon.pb.h"


class RouteP2PSendTextNotifyMsg: public IRouteBase
{
public:
    RouteP2PSendTextNotifyMsg(){}
    ~RouteP2PSendTextNotifyMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    int IPushNotify();
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PSendTextNotifyMsg>();}
    std::string FuncName() {return func_name_;}

private:
    int SendMessageToApp(const AK::BackendCommon::BackendP2PBaseMessage& recive_base_msg);
    int SendMessageToDev(const AK::BackendCommon::BackendP2PBaseMessage& recive_base_msg);
    std::string func_name_ = "RouteP2PSendTextNotifyMsg";
    SOCKET_MSG_COMMON_ACK common_ack_;
};


#endif



