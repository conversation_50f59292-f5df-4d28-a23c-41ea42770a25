#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteP2PSendVoiceMsg.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "Resid2RouteMsg.h"
#include "NotifyMsgControl.h"
#include "NotifyPerText.h"
#include "ProjectUserManage.h"


__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteP2PSendVoiceMsg>();
    RegRouteFunc(p, AKCS_M2R_P2P_SEND_VOICE_MSG);
};


int RouteP2PSendVoiceMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    if (base_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false) 
    {
        AK_LOG_WARN << "parse pb msg failed.";
        return -1;
    }
    AK_LOG_INFO << "BackendP2PBaseMessage details: type=" << base_msg.type()
                << ", uid=" << base_msg.uid() << ", project_type=" << base_msg.project_type()
                << ", conn_type=" << base_msg.conn_type() << ", msgid=" << base_msg.msgid() << ". msgname = " << MsgIdToMsgName::GetAkcsMessageName(base_msg.msgid());

    const AK::Server::P2PSendVoiceMsg msg = base_msg.p2psendvoicemsg2();

    
    AK_LOG_INFO << "RouteP2PSendVoiceMsg uuid:" << msg.receiver_uuid();
    if (msg.receiver_type() == DEVICE_TYPE_APP)
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(msg.receiver_uuid(), per_account))
        {
            SOCKET_MSG_SEND_TEXT_MESSAGE text_send;
            memset(&text_send.text_message, 0, sizeof(text_send.text_message));

            text_send.client_type = CPerTextNotifyMsg::APP_SEND;
            Snprintf(text_send.text_message.title, sizeof(text_send.text_message.title),  "VOICE_MSG");
            text_send.text_message.id = msg.msg_id();
            ::snprintf(text_send.text_message.content, sizeof(text_send.text_message.content), "You have a voice message from %s", msg.location().c_str());
            text_send.text_message.type = CPerTextNotifyMsg::VOICE_MSG;
            CPerTextNotifyMsg cNotifyMsg(base_msg, text_send, per_account.account);
            GetNotifyMsgControlInstance()->AddTextNotifyMsg(cNotifyMsg);
        }
    }
    else if (msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg;
        memset(&online_msg, 0, sizeof(online_msg));
        online_msg.unread_voice_count = msg.count();
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(msg.receiver_uuid(), dev)
            && 0 != dbinterface::ResidentPerDevices::GetUUIDDev(msg.receiver_uuid(), dev))
        {
            AK_LOG_WARN << "HandleP2PSendVoiceMsg failed uuid not found. uuid:" << msg.receiver_uuid();
            return -1;
        }
        snprintf(online_msg.uuid, sizeof(online_msg.uuid), "%s", dev.uuid);
        snprintf(online_msg.mac, sizeof(online_msg.mac), "%s", dev.mac);

        std::string xml_msg;
        GetMsgBuildHandleInstance()->BuildOnlineNotifyXmlMsg(online_msg, xml_msg);
        ReplyToDevMsg(online_msg.mac, xml_msg, MSG_TO_DEVICE_ONLINE_NOTIFY_MSG, MsgEncryptType::TYEP_MAC_ENCRYPT);
    }    

    return 0; 
}

int RouteP2PSendVoiceMsg::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    return 0;
}
