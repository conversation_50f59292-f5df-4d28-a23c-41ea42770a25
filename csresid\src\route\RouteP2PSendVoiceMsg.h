#ifndef _ROUTE_P2P_MSG_H_
#define _ROUTE_P2P_MSG_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "DclientMsgDef.h"


class RouteP2PSendVoiceMsg: public IRouteBase
{
public:
    RouteP2PSendVoiceMsg(){}
    ~RouteP2PSendVoiceMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    int IPushNotify();
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteP2PSendVoiceMsg>();}
    std::string FuncName() {return func_name_;}

private:

    std::string func_name_ = "RouteP2PSendVoiceMsg";
    SOCKET_MSG_DEV_ONLINE_NOTIFY online_msg_;
};


#endif



