#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteVoiceMsgAck.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "Resid2RouteMsg.h"

#include "ProjectUserManage.h"


__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteVoiceAckMsg>();
    RegRouteFunc(p, AKCS_R2S_P2P_VOICE_MSG_ACK_REQ);
};


int RouteVoiceAckMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    if (msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false) 
    {
        AK_LOG_WARN << "parse pb msg failed.";
        return -1;
    }
    AK_LOG_INFO << "[csroute] p2p handle voice msg:" << msg.DebugString();

    memset(&common_ack_, 0, sizeof(common_ack_));
    Snprintf(common_ack_.mac, sizeof(common_ack_.mac),  msg.mac().c_str());
    Snprintf(common_ack_.trace_id, sizeof(common_ack_.trace_id),  msg.filename().c_str());
    common_ack_.result = msg.result();

    //通知接收人
    CResid2RouteMsg::GroupVoiceMsg(pdu);

    return 0; 
}

int RouteVoiceAckMsg::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    msg_id = MSG_TO_DEVICE_ACK;
    to_mac = common_ack_.mac;
    enc_type = TYEP_MAC_ENCRYPT;
    GetMsgBuildHandleInstance()->BuildCommonAckMsg(MSG_FROM_DEVICE_REPORT_VOICE_MSG, common_ack_, msg);
    return 0;
}

void RouteVoiceAckMsg::GroupVoiceMsg(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    if (msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false) 
    {
        AK_LOG_WARN << "parse pb msg failed.";
        return;
    }

    //获取留言信息
    PersonalVoiceMsgInfo voice_msg_info;
    PersonalVoiceMsgSendList send_list;
    std::string location;
    std::string msg_uuid;
    memset(&voice_msg_info, 0, sizeof(voice_msg_info));
    if (0 == dbinterface::PersonalVoiceMsg::GetVoiceMsgInfoByMacAndFileName(msg.mac(), msg.filename(), voice_msg_info))
    {
        location = voice_msg_info.location;
        msg_uuid = voice_msg_info.uuid;
    }

    //获取发送列表
    dbinterface::PersonalVoiceMsg::GetVoiceMsgListInfoByMsgUUID(msg_uuid, send_list);

    for (const auto & send_node: send_list)
    {
        int type;
        int count;
        int msg_id;
        std::string receiver_uuid;
        AK::BackendCommon::BackendP2PBaseMessage base;
        if (strlen(send_node.indoor_uuid) > 0)
        {
            receiver_uuid = send_node.indoor_uuid;
            count = dbinterface::PersonalVoiceMsg::GetUnreadCountByIndoorUUID(send_node.indoor_uuid);
            type = DEVICE_TYPE_INDOOR;
            base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_VOICE_MSG, TransP2PMsgType::TO_DEV_UUID, receiver_uuid,
                CResid2RouteMsg::DevProjectTypeToDevType(msg.project_type()), msg.project_type());
        }
        else if (strlen(send_node.personal_uuid) > 0)
        {
            receiver_uuid = send_node.personal_uuid;
            msg_id = send_node.id;
            type = DEVICE_TYPE_APP;
            std::string uid;
            dbinterface::ResidentPersonalAccount::GetAccountByUUID(receiver_uuid, uid);
            base = CResid2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_VOICE_MSG, TransP2PMsgType::TO_APP_UID, uid, 
               CResid2RouteMsg::DevProjectTypeToDevType(msg.project_type()), msg.project_type());
        }
        
        AK::Server::P2PSendVoiceMsg msg2route;
        msg2route.set_msg_id(msg_id);
        msg2route.set_count(count);
        msg2route.set_location(location);
        msg2route.set_receiver_uuid(receiver_uuid);
        msg2route.set_receiver_type(type);
        //通过project_type区分需要处理的消息的业务类型，通过base里面的conntype区别主站点的业务类型，用于发送
        msg2route.set_project_type(msg.project_type());
        
        base.mutable_p2psendvoicemsg2()->CopyFrom(msg2route);
        IP2PToRouteMsg(&base);
    }
}


