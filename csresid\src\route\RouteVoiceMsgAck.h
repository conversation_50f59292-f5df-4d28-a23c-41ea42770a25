#ifndef _ROUTE_VOICE_ACK_H_
#define _ROUTE_VOICE_ACK_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "DclientMsgDef.h"


class RouteVoiceAckMsg: public IRouteBase
{
public:
    RouteVoiceAckMsg(){}
    ~RouteVoiceAckMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    int IPushNotify();
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteVoiceAckMsg>();}
    std::string FuncName() {return func_name_;}

private:
    void GroupVoiceMsg(const std::unique_ptr<CAkcsPdu> &pdu);

    std::string func_name_ = "RouteVoiceAckMsg";
    SOCKET_MSG_COMMON_ACK common_ack_;
};


#endif



