#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkcsMsgDef.h"
#include "RouteWeatherInfoMsg.h"
#include "RouteFactory.h"
#include "MsgParse.h"
#include "ResidServer.h"
#include "AKCSDao.h"
#include "MsgBuild.h"
#include "AK.Server.pb.h"
#include "AK.Resid.pb.h"
#include "AK.Linker.pb.h"
#include "CachePool.h"

#include "Resid2RouteMsg.h"

#include "ProjectUserManage.h"


__attribute__((constructor))  static void init(){
    IRouteBasePtr p = std::make_shared<RouteWeatherInfoMsg>();
    RegRouteFunc(p, AKCS_R2B_P2P_WEATHER_INFO_RESP);
};


int RouteWeatherInfoMsg::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Linker::LinkerWeatherNotify msg;
    if (msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()) == false) 
    {
        AK_LOG_WARN << "parse pb msg failed.";
        return -1;
    }

    AK_LOG_INFO << "[csroute] HandleP2PWeatherInfoMsg:" << msg.DebugString();
    

    memset(&weather_msg_, 0, sizeof(weather_msg_));
    ::snprintf(weather_msg_.mac, sizeof(weather_msg_.mac), "%s", msg.mac().c_str());
    ::snprintf(weather_msg_.city, sizeof(weather_msg_.city), "%s", msg.city().c_str());
    ::snprintf(weather_msg_.states, sizeof(weather_msg_.states), "%s", msg.states().c_str());
    ::snprintf(weather_msg_.country, sizeof(weather_msg_.country), "%s", msg.country().c_str());
    ::snprintf(weather_msg_.weather, sizeof(weather_msg_.weather), "%s", msg.weather().c_str());    
    ::snprintf(weather_msg_.humidity, sizeof(weather_msg_.humidity), "%s", msg.humidity().c_str());
    ::snprintf(weather_msg_.temperature, sizeof(weather_msg_.temperature), "%s", msg.temperature().c_str());

    // 缓存到redis
    CacheManager* cache_manager = CacheManager::getInstance();
    CacheConn* cache_conn = cache_manager->GetCacheConn(g_redis_db_weather);
    if (cache_conn)
    {
        char weather_redis_key[256];
        char weather_redis_value[256];
        ::snprintf(weather_redis_key, sizeof(weather_redis_key), "%s-%s-%s", weather_msg_.country, weather_msg_.states, weather_msg_.city);
        ::snprintf(weather_redis_value, sizeof(weather_redis_value), "%s!%s!%s", weather_msg_.weather, weather_msg_.temperature, weather_msg_.humidity);

        cache_conn->set(weather_redis_key, weather_redis_value);
        cache_conn->expire(weather_redis_key, WEATHER_EXPIRE_SECOND);
        cache_manager->RelCacheConn(cache_conn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csresid " << g_redis_db_weather;
    }

    return 0; 
}

int RouteWeatherInfoMsg::IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type)
{
    msg_id = MSG_TO_DEVICE_REPORT_WEATHER_MSG;
    to_mac = weather_msg_.mac;
    enc_type = TYEP_MAC_ENCRYPT;
    GetMsgBuildHandleInstance()->BuildWeatherInfoMsg(weather_msg_, msg);
    return 0;
}




