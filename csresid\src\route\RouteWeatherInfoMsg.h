#ifndef _ROUTE_WEATHER_INFO_H_
#define _ROUTE_WEATHER_INFO_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "DclientMsgDef.h"


class RouteWeatherInfoMsg: public IRouteBase
{
public:
    RouteWeatherInfoMsg(){}
    ~RouteWeatherInfoMsg() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    int IReplyToDevMsg(std::string &to_mac, std::string &msg, uint32_t &msg_id, MsgEncryptType &enc_type);
    int IPushNotify();
    
    IRouteBasePtr NewInstance() {return std::make_shared<RouteWeatherInfoMsg>();}
    std::string FuncName() {return func_name_;}

private:

    std::string func_name_ = "RouteWeatherInfoMsg";
    SOCKET_MSG_DEV_WEATHER_INFO weather_msg_;
};


#endif



