CMAKE_MINIMUM_REQUIRED(VERSION 3.6.3)

project (route C CXX)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/../csbase/doorlog")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libcsbase.a pthread libhiredis.a libevent.so libglog.so libmysqlclient.so libevpp.so libprotobuf.so libetcd-cpp-api.so libcpprest.so libboost_system.so libssl.so libcrypto.so libgpr.so libgrpc.so libgrpc++.so libcppkafka.so
 librdkafka.so librdkafka++.so libz.so libdl.so)
SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../)
LINK_DIRECTORIES(${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/thirdlib ${BASE_SOURCE_DIR}/csbase/redis/hiredis ${BASE_SOURCE_DIR}/csbase/evpp/lib /usr/local/lib /usr/local/cpprest/lib)

AUX_SOURCE_DIRECTORY(./ SRC_LIST_ROUTE)
AUX_SOURCE_DIRECTORY(./dao SRC_LIST_ROUTE_DAO)
AUX_SOURCE_DIRECTORY(${CMAKE_CURRENT_SOURCE_DIR}/office SRC_LIST_ROUTE_OFFICE)
AUX_SOURCE_DIRECTORY(${CMAKE_CURRENT_SOURCE_DIR}/test/integration_test SRC_LIST_INTEGRATION_TEST)
AUX_SOURCE_DIRECTORY(../csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(../csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(../csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(../csbase/protobuf SRC_LIST_BASE_PROTOBUF)
AUX_SOURCE_DIRECTORY(../csbase/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(../csbase/grpc SRC_LIST_BASE_GRPC)
AUX_SOURCE_DIRECTORY(../csbase/grpc/cssession SRC_LIST_BASE_GRPC_SESSION)
AUX_SOURCE_DIRECTORY(../csbase/session SRC_LIST_BASE_SESSION)
AUX_SOURCE_DIRECTORY(../csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(../csbase/jsoncpp0.5/src/json SRC_LIST_BASE_JSONCPP)
AUX_SOURCE_DIRECTORY(../csbase/zipkin SRC_LIST_BASE_ZIPKIN)
AUX_SOURCE_DIRECTORY(../csbase/kafka SRC_LIST_BASE_KAFKA)
AUX_SOURCE_DIRECTORY(../csbase/metrics SRC_LIST_BASE_METRICS)

SET(BASE_LIST_INC ${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/mysql/include ${BASE_SOURCE_DIR}/csbase/Rldb 
                  ${BASE_SOURCE_DIR}/csbase/evpp ${BASE_SOURCE_DIR}/csbase/protobuf ${BASE_SOURCE_DIR}/csbase/etcd ${BASE_SOURCE_DIR}/csbase/session 
				   ${BASE_SOURCE_DIR}/csbase/grpc/cssession ${BASE_SOURCE_DIR}/csbase/redis ${BASE_SOURCE_DIR}/csbase/grpc 
                   ${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/include ${BASE_SOURCE_DIR}/csbase/encrypt ${BASE_SOURCE_DIR}/csbase/grpc/gens
                   ${BASE_SOURCE_DIR}/csbase/gid )

ADD_DEFINITIONS( -std=c++11 -g -W -Wall -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON
-DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -Werror -Wno-unused-parameter -Wno-deprecated)

#added by chenyc,2024-02-19,该编译选项只能用于压测rtspd对设备端(门口机)视频预览并发能力的集成压测使用,只能开发内部环境与代码分支开启,一定不能在线上版本开启该选项
#add_definitions(-DRTSP_RTP_INTERCEPT)

include_directories( ./ ${BASE_LIST_INC} ./dao ./office ./test/integration_test 
    /usr/local/boost/include /usr/local/grpc/include /usr/local/protobuf/include
    ${BASE_SOURCE_DIR}/csbase/metrics)

add_executable(csroute ${SRC_LIST_ROUTE} ${SRC_LIST_ROUTE_DAO} ${SRC_LIST_ROUTE_OFFICE} ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_PROTOBUF} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_BASE_SESSION} ${SRC_LIST_BASE_GRPC_SESSION} ${SRC_LIST_BASE_REDIS} 
${SRC_LIST_BASE_GRPC} ${SRC_LIST_BASE_JSONCPP}  ${SRC_LIST_BASE_ZIPKIN} ${SRC_LIST_BASE_KAFKA} ${SRC_LIST_INTEGRATION_TEST} ${prefixed_file_list} ${SRC_LIST_BASE_METRICS})

set_source_files_properties(
    ./kafka_consumer.cpp ./kafka_producer.cpp ./push_client.cpp ./push_kafka.cpp ./route_main.cpp ./route_mq.cpp ${SRC_LIST_BASE_ZIPKIN} ${SRC_LIST_BASE_KAFKA}
    PROPERTIES
    COMPILE_FLAGS "-Wno-ignored-qualifiers"
)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
set_target_properties(csroute PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csroute/lib")

target_link_libraries(csroute  ${DEPENDENT_LIBRARIES})
