#include "Metric.h"
#include "route_mq.h"
#include "AkLogging.h"
#include "ConfigFileReader.h"

#define VERSION_CONF_FILE "/usr/local/akcs/csroute/conf/version.conf"

void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "csroute_pdu_queue_length",
        "csroute pdu queue length",
        "csroute_pdu_queue_length",
        []() -> long { return (long)RouteMQCust::GetInstance()->GetRoutePduSize(); }
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );

    std::vector<uint32_t> csroute_mq_msg_all_handle_latencies = {1000,2000,3000,4000,5000,6000,7000,8000,9000};
    MetricLatencyPtr csroute_mq_msg_all_handle_latency = std::make_shared<MetricLatency>(csroute_mq_msg_all_handle_latencies, "queue_handle_latency");
    metric_service->AddLatencyMetric("queue_handle_latency", csroute_mq_msg_all_handle_latency);
}