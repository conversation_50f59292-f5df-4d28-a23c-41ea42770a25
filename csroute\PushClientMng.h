#ifndef __CSROUTE_PUSH_CLIENT_MNG_H__
#define __CSROUTE_PUSH_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "push_client.h"

typedef std::map<std::string, PushClientPtr>PushClientMap;

class CPushClientMng : public boost::noncopyable
{
public:
    CPushClientMng()
    {}
    ~CPushClientMng()
    {}
    static CPushClientMng* Instance();
    void AddPushSrv(const std::string& push_addr, const PushClientPtr& push_cli);
    void UpdatePushSrv(const std::set<std::string>& push_addrs, evpp::EventLoop* etcd_loop);
    const PushClientPtr GetPushSrv();

private:
    void RemoveDisconnectCli();
    static CPushClientMng* pInstance_;
    
    std::mutex push_clis_mutex_;
    PushClientMap push_clis_;

    std::mutex push_clis_remove_mutex_;
    std::vector<PushClientPtr> push_remove_clis_;

    std::mutex push_clis_iter_mutex_;

};

#endif //__CSROUTE_PUSH_CLIENT_MNG_H__


