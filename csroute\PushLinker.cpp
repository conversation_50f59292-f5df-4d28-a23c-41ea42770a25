#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <evnsq/producer.h>
#include "json/json.h"
#include "PushLinker.h"
#include "SnowFlakeGid.h"
#include "RouteMqProduce.h"
#include "route_server.h"
#include "AkcsMonitor.h"


RouteMQProduce* g_cslinker_nsq_producer;
extern AKCS_ROUTE_CONF gstAKCSConf;

/*
{
	"message_type": 1,
	"trace_id": 1234,
	"timestamp": 12345678,
	"key": "123",
	"datas": ""
}
*/

void CPushLinKer::PushLinkerInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetConnectErrorCallback(&OnLinkerConnectError);
    client.SetMessageCallback(&OnLinkerRouteMQMessage);
    client.SetReadyCallback(&OnNSQReady);
    std::string inner_addr = gstAKCSConf.linker_nsq_ip;
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    g_cslinker_nsq_producer = new RouteMQProduce(&client);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}

void CPushLinKer::PushMsg(int msg_type, const std::string &msg_json, const std::string &key)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    std::time_t t = std::time(0);
    
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    item["message_type"] = msg_type;
    item["trace_id"] = (long long)traceid;
    item["timestamp"] = (long long)t;
    item["key"] = key;
    //item["datas"] = msg_json;

    Json::Reader reader;
    Json::Value msg;
    if (!reader.parse(msg_json, msg))
    {
        AK_LOG_WARN <<  "CPushLinKer::PushMsg Parse json error.data=" << msg_json << " error msg=" << reader.getFormatedErrorMessages();
        return;
    }
    item["datas"] = msg;

    std::string data_json = wData.write(item);
    LOG_INFO << "[PushLinker] message type:" << msg_type << " trace_id:" << traceid << " datas:" << data_json;
    if (g_cslinker_nsq_producer)
    {
        g_cslinker_nsq_producer->OnPublishLinker(data_json, gstAKCSConf.linker_nsq_topic);
    }
    return;
}



