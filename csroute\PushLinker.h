#ifndef __CSMAIN_PUSH_LINKER_H__
#define __CSMAIN_PUSH_LINKER_H__

#include <evpp/tcp_client.h>
#include "AkLogging.h"

typedef std::map<std::string/*key*/, std::string/*value*/> LinkerParamKV;

class CPushLinKer
{
public:
    CPushLinKer(){};
    static void PushLinkerInit();
    static void PushMsg(int msg_type, const std::string &msg_json, const std::string &key);

private:
};

#endif // __CSMAIN_PUSH_LINKER_H__