#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include <evnsq/consumer.h>
#include "RouteMqProduce.h"
#include "AkcsMonitor.h"
#include "AkLogging.h"


const static uint32_t kAkMsgHoldLen = sizeof(int32_t);
bool g_nsq_ready = false;

int OnLinkerRouteMQMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void OnNSQReady()
{
    g_nsq_ready = true;
}

void OnLinkerConnectError(const std::string& addr)
{   
    AK_LOG_WARN << "Connect nsqd-" << addr << "error";
    ConnectNsqErrorMutt(addr, "csroute");
}

bool RouteMQProduce::OnPublish(CAkcsPdu& pdu, const std::string& topic)
{
    std::string msg(pdu.GetBuffer(), pdu.GetLength());
    if (!client_->Publish(topic, msg))
    {
        //added by chenyc,2020-03-31,当发布失败的时候,触发告警系统
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csroute", "NSQD publish msg error", AKCS_MONITOR_ALARM_NSQD_PUBLISH_CSROUTE);
        return false;
    }
    return true;
}

bool RouteMQProduce::OnPublishLinker(const std::string &msg, const std::string& topic)
{
    if (!client_->Publish(topic, msg))
    {
        //added by chenyc,2020-03-31,当发布失败的时候,触发告警系统
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csroute", "NSQD publish linker msg error", AKCS_MONITOR_ALARM_NSQD_PUBLISH_CSROUTE);
        return false;
    }
    return true;
}


