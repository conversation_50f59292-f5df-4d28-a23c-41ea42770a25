#ifndef __CSROUTE_MQ_PRODUCE_H__
#define __CSROUTE_MQ_PRODUCE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <evnsq/producer.h>

int OnLinkerRouteMQMessage(const evnsq::Message* msg);
void OnNSQReady();
void OnLinkerConnectError(const std::string& addr);

class RouteMQProduce
{
public:
    RouteMQProduce(evnsq::Producer* producer)
        : client_(producer)
    {}
    ~RouteMQProduce() {}

public:
    bool OnPublish(CAkcsPdu& pdu, const std::string& topic);
    bool OnPublishLinker(const std::string &msg, const std::string& topic);
private:
    evnsq::Producer* client_;
};

#endif //__CSROUTE_MQ_PRODUCE_H__

