#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "ak_dev_mng.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"

CAkDevManager::~CAkDevManager()
{
}

CAkDevManager* CAkDevManager::GetInstance()
{
    static CAkDevManager s_manager;
    return &s_manager;
}

int CAkDevManager::GetLocationAndNodeBySip2(const std::string& sip, std::string& location, std::string& node)
{
    ResidentDev per_dev;
    memset(&per_dev, 0, sizeof(per_dev));
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
    }
    
    return ret == 0 ? 1 : 0;
}

void CAkDevManager::GetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& nMngID)
{
    ResidentDev per_dev;
    memset(&per_dev, 0, sizeof(per_dev));
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
            nMngID = dev.project_mng_id;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
        nMngID = per_dev.project_mng_id;
    }
}

