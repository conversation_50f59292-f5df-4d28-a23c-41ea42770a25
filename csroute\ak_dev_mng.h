#ifndef __ROUTE_AKDEV_MANAGE_H__
#define __ROUTE_AKDEV_MANAGE_H__

#include <evpp/tcp_client.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include <set>

enum DevType
{
    PER_NULL = 0,
    PER_DEV,
    PUB_DEV,
};

class CAkDevManager
{
public:
    CAkDevManager() {}
    ~CAkDevManager();
    static CAkDevManager* GetInstance();
    //更新sip-mac列表
    int GetLocationAndNodeBySip2(const std::string& sip, std::string& location, std::string& node);
    void GetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& nMngID);

};

#endif // __ROUTE_AKDEV_MANAGE_H__