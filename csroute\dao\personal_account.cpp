//#include "stdafx.h"
#include <sstream>
#include "personal_account.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"

#define COMMUNITY_AREANODE      "Community,AreaNode"
extern CRldb rldb;

CPersonalAccount* GetPersonalAccountInstance()
{
    return CPersonalAccount::GetInstance();
}

CPersonalAccount* CPersonalAccount::instance = NULL;

CPersonalAccount* CPersonalAccount::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonalAccount();
    }

    return instance;
}

int CPersonalAccount::DaoGetFamilyMemberByMaster(const std::string& account, std::vector<std::string>& account_list)
{
    std::stringstream sql;
    sql << "SELECT A.Account FROM PersonalAccount A JOIN PersonalAccount B ON A.ParentID = B.ID WHERE A.Role IN ("
        << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << "," << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ") AND B.Account = '"
        << account
        << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {        
        std::string member = query.GetRowData(0);
        account_list.push_back(member);
    }
    ReleaseDBConn(conn);
    return 0;
}