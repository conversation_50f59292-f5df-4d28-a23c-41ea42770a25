#ifndef __ROUTE_PERSONNAL_ACCOUNT_H__
#define __ROUTE_PERSONNAL_ACCOUNT_H__
#include <vector>
#include <boost/noncopyable.hpp>

//账号名长度
#ifndef USER_SIZE
#define USER_SIZE                       32
#endif

#ifndef SIP_SIZE
#define SIP_SIZE                        32
#endif

#ifndef RTSP_PWD_SIZE
#define RTSP_PWD_SIZE                   36
#endif

#ifndef MAC_SIZE
#define MAC_SIZE                 20
#endif

#ifndef IP_SIZE
#define IP_SIZE                  16
#endif

//个人终端用户设备列表结构体
typedef struct PERSONNAL_DEVICE_SIP_T
{
    char name[USER_SIZE];
    char sip_account[SIP_SIZE];
    char ip[IP_SIZE];
    char mac[MAC_SIZE];
    char rtsp_pwd[RTSP_PWD_SIZE];
    int  type;
} PERSONNAL_DEVICE_SIP;

typedef struct PerAccountStatu_t
{
    int phone_status;
    int enable_ip_direct;
    std::string phone;
} PerAccountStatu;

class CPersonalAccount : public boost::noncopyable
{
public:
    CPersonalAccount()
    {
    }
    ~CPersonalAccount()
    {
    }
    int DaoGetNickNameByUid(const std::string& uid, std::string& name, std::string& community);   
    int DaoGetNickNameRoleByUid(const std::string& uid, std::string& name, std::string& community, int &role);   
    int DaoGetNickNameByEmail(const std::string& email, std::string& community);  
    int DaoGetCommunityByID(int mng_id, std::string& community);
    int DaoGetLanguageByEmail(const std::string& email, std::string& language);  
    //std::string DaoGetLanguageByPhone(const std::string& phone);
    int DaoGetMobileNumberAndNameByUid(const std::string& uid , std::string& phone, std::string& area_code, std::string& name);
    int DaoGetFamilyMemberByMaster(const std::string& account, std::vector<std::string>& account_list);
    static CPersonalAccount* GetInstance();
private:

    static CPersonalAccount* instance;

};

CPersonalAccount* GetPersonalAccountInstance();
int DaoGetAccountLanguageByEmail(const std::string& email, std::string& language);

#endif //__ADAPT_PERSONNAL_ACCOUNT_H__
