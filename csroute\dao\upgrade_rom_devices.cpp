#include <sstream>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "ConnectionPool.h"
#include "upgrade_rom_devices.h"

CUpgradeRomDevices* CUpgradeRomDevices::instance = NULL;

CUpgradeRomDevices* CUpgradeRomDevices::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CUpgradeRomDevices();
    }
    return instance;
}


int CUpgradeRomDevices::DaoGetWaitUpgradeList(int rom_ver_id, std::vector<std::string>& macs)
{
    int maxid = 0;
    int num = dbinterface::UpgradeRomDevices::GetWaitUpgradeList(rom_ver_id, maxid, macs);
    
    if (num >= 100)  //还有设备待升级
    {
        SetNeedUpgrade();
    }
    //将小于maxid的行的状态置为已升级
    return dbinterface::UpgradeRomDevices::UpdateUpgradeStatus(maxid, rom_ver_id);
}


