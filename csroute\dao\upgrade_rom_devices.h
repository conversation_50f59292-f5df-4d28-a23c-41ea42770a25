#ifndef __ROUTE_UPGRADE_ROM_DEV_H__
#define __ROUTE_UPGRADE_ROM_DEV_H__

#include <vector>
#include <string>
#include <atomic>
#include <map>
#include "dbinterface/UpgradeRomDevices.h"


class CUpgradeRomDevices
{
public:
    CUpgradeRomDevices()
    {
    }
    ~CUpgradeRomDevices()
    {
    }
    int DaoGetWaitUpgradeList(int rom_ver_id, std::vector<std::string>& macs);
    
    void SetNeedUpgrade()
    {
        upgrade_.store(true);

    }
    void SetUpgradeDone()
    {
        upgrade_.store(false);

    }
    bool IsNeedUpgrade()
    {
        return upgrade_.load() == true;
    }
    static CUpgradeRomDevices* GetInstance();
private:
    std::atomic<bool> upgrade_ = { false };
    static CUpgradeRomDevices* instance;

};

#endif //__ROUTE_UPGRADE_ROM_DEV_H__
