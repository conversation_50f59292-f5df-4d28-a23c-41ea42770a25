#include <stdexcept>
#include <iostream>
#include <csignal>
#include <unistd.h>
#include <signal.h>
#include "CachePool.h"
#include "AkLogging.h"  
#include "AkcsMonitor.h"
#include "AkcsPduBase.h"
#include "cppkafka/consumer.h"
#include "cppkafka/configuration.h"
#include "cppkafka/topic_partition_list.h"
#include "util.h"
#include "route_mq.h"
#include "route_server.h"
#include "kafka_consumer.h"

using cppkafka::Consumer;
using cppkafka::Configuration;
using cppkafka::Message;
using cppkafka::TopicPartitionList;

const static int32_t kAkMsgHoldLen = 4;
extern AKCS_ROUTE_CONF gstAKCSConf;

void KafkaConsumerInit()
{
    Configuration config =
    {
        { "metadata.broker.list", gstAKCSConf.kafka_broker_ip },
        { "group.id", gstAKCSConf.kafka_csroute_group },
        { "enable.auto.commit", false },
        { "auto.offset.reset", "earliest"}
    };

    AK_LOG_INFO << "kafka_broker_ip: " << gstAKCSConf.kafka_broker_ip;
    AK_LOG_INFO << "kafka_consumer_group: " << gstAKCSConf.kafka_csroute_group;
    Consumer consumer(config);

    consumer.set_assignment_callback([](const TopicPartitionList& partitions)
    {
        AK_LOG_INFO << "Got assigned: " << partitions;
    });

    consumer.set_revocation_callback([](const TopicPartitionList& partitions)
    {
        AK_LOG_WARN << "Got revoked: " << partitions;
    });

    consumer.subscribe({gstAKCSConf.kafka_csroute_topic});
    
    while (1)
    {
        Message msg = consumer.poll();
        if (msg)
        {
            if (msg.get_error())
            {
                if (!msg.is_eof())
                {
                    AK_LOG_INFO << "[+] Received error notification: " << msg.get_error();
                }
            }
            else
            {
                consumer.commit(msg);

                const cppkafka::Buffer& payload = msg.get_payload();
                const char* data = reinterpret_cast<const char*>(payload.get_data());
                const uint32_t size = payload.get_size();

                if (size >= kAkMsgHoldLen)
                {
                    const int32_t pb_msg_len = PeekInt32(data, size);
                    if (size != (uint32_t)pb_msg_len)
                    {
                        AK_LOG_WARN << "Invalid evnsq length " << size;
                    }
                    else
                    {
                        std::shared_ptr<CAkcsPdu> akcs_pdu(new CAkcsPdu());
                        akcs_pdu->Write(data, size); //包整体长度全部整进去
                        char tmp_buf[sizeof(PduHeader_t)] = {0};
                        memcpy(tmp_buf, data, sizeof(tmp_buf));
                        if (akcs_pdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0)
                        {
                            AK_LOG_WARN << "Pdu packet header len is invalid";
                        }
                        else
                        {
                            RouteMQCust::GetInstance()->AddMessage(akcs_pdu);
                        }
                    }
                }
                else
                {
                    AK_LOG_WARN << "Invalid kafka length " << size;
                }
            }
        }
    }
}

