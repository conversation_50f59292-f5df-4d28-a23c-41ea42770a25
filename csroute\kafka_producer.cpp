#include "kafka_producer.h"
#include "AkcsMonitor.h"
#include "AkLogging.h"
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "push_client.h"
#include "push_kafka.h"

#define KAFKA_PUSH_EMAIL_PARTITION_NUM  5
#define KAFKA_AK_LINKER_PARTITION_NUM  2
static const std::string trigger_email = "<EMAIL>";

CKafakProducer::CKafakProducer(const std::string& topic, const std::string& kafka_broker_ip_list)
    : email_topic_(topic), broker_ip_list_(kafka_broker_ip_list)
{
    email_builder_ = std::make_shared<MessageBuilder>(email_topic_);
    Configuration config =
    {
        { "metadata.broker.list", broker_ip_list_ },
        {"request.required.acks", "1"},//leader 接收到就算成功
        //{ "enable.auto.commit", "false" },
        //{ "auto.commit.interval.ms", "1000" },
        //{ "offset.store.method", "broker" }
    };
    producer_ = std::make_shared<BufferedProducer<std::string>>(config);
    Init();
}

void CKafakProducer::CreatePmExportLogTopic(const std::string& topic)
{
    pm_export_log_topic_ = topic;
    pm_export_log_builder_ = std::make_shared<MessageBuilder>(pm_export_log_topic_);
}

void CKafakProducer::CreatePmExportLogExcelTopic(const std::string& topic)
{
    pm_export_log_excel_topic_ = topic;
    pm_export_log_excel_builder_ = std::make_shared<MessageBuilder>(pm_export_log_excel_topic_);
}

void CKafakProducer::CreateNotifyWebTopic(const std::string& topic)
{
    notify_web_topic_ = topic;
    notify_web_builder_ = std::make_shared<MessageBuilder>(notify_web_topic_);
}

void CKafakProducer::CreateNotifyLinkerTopic(const std::string& topic)
{
    notify_linker_topic_ = topic;
    notify_linker_builder_ = std::make_shared<MessageBuilder>(notify_linker_topic_);
}

void CKafakProducer::CreateNotifyWebMessageTopic(const std::string& topic)
{
    notify_web_message_topic_ = topic;
    notify_web_message_builder_ = std::make_shared<MessageBuilder>(notify_web_message_topic_);
}

void CKafakProducer::CreateNotifyWebAttendanceTopic(const std::string& topic)
{
    notify_web_attendance_topic_ = topic;
    notify_web_attendance_builder_ = std::make_shared<MessageBuilder>(notify_web_attendance_topic_);
}

void CKafakProducer::CreateNotifyWebAccessDoorTopic(const std::string& topic)
{
    notify_web_access_door_topic_ = topic;
    notify_web_access_door_builder_ = std::make_shared<MessageBuilder>(notify_web_access_door_topic_);
}


int CKafakProducer::Init()
{
    msg_handle_thread_t_ = std::thread(&CKafakProducer::ProcessMsgThread, this);
    return 0;

}

int CKafakProducer::ProduceMsg(KafkaProduceMsgPtr msg)
{
    std::unique_lock<std::mutex> lock(msg_mutex_);
    msg_list_.push_back(msg);
    condition_var_.notify_all();
	return 0;
}

int CKafakProducer::ProcessMsgThread()
{
    int produce_index = 0;
    int cslinker_index = 0;
    while (1)
    {
        std::unique_lock<std::mutex> lock(msg_mutex_);
        while (msg_list_.size() == 0)
        {
            condition_var_.wait(lock);
        }

        if (msg_list_.size() > 500)
        {
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csroute", "push email msg queue overflow", AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
            AK_LOG_WARN << "push email msg queue overflow, the length now is " << msg_list_.size();
        }
        KafkaProduceMsgPtr tmp_msg_ptr = msg_list_.back();
        msg_list_.pop_back();

        std::string msg = tmp_msg_ptr->get_payload();
        std::string key = tmp_msg_ptr->get_key();
        int  msg_type = tmp_msg_ptr->get_msg_type();
        if (msg_type == KAFKA_MSG_EMAIL)
        {
            email_builder_->key(key);
            email_builder_->payload(msg);
            
            // 监控往所有kafka分区发送
            if (strncmp(key.c_str(), trigger_email.c_str(), trigger_email.length()) == 0)
            {
                for (int partition = 0; partition < KAFKA_PUSH_EMAIL_PARTITION_NUM; partition++)
                {
                    email_builder_->partition(partition);
                    producer_->produce(*email_builder_);
                    producer_->async_flush();
                }
            }
            else
            {
                email_builder_->partition(produce_index);
                producer_->produce(*email_builder_);
                producer_->async_flush();
                std::chrono::milliseconds mscond(1000 * 15);
                if (!producer_->wait_for_acks(mscond))
                {
                    std::string worker_node = "csroute";
                    std::stringstream alarm_msg;
                    alarm_msg << "Producer Email message wait akcs timeout! key=" << key;
                    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                    AK_LOG_WARN << "Producer message wait akcs timeout! key=" << key;
                };
                ++produce_index;
                produce_index = produce_index % KAFKA_PUSH_EMAIL_PARTITION_NUM;   
            }
        }
        else if (msg_type == KAFKA_MSG_PM_EXPORT_LOG)
        {
            pm_export_log_builder_->payload(msg);
            pm_export_log_builder_->key(key);
            pm_export_log_builder_->partition(0);
            producer_->produce(*pm_export_log_builder_);
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream alarm_msg;
                alarm_msg << "Producer Export Log message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                AK_LOG_WARN << "Producer message wait akcs timeout! key=" << key;
            };

        }
        else if (msg_type == KAFKA_MSG_PM_EXPORT_LOG_EXCEL)
        {
            pm_export_log_excel_builder_->payload(msg);
            pm_export_log_excel_builder_->key(key);
            pm_export_log_excel_builder_->partition(0);
            producer_->produce(*pm_export_log_excel_builder_);
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream alarm_msg;
                alarm_msg << "Producer Export Log Excel message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                AK_LOG_WARN << "Producer message wait akcs timeout! key=" << key;
            };

        }
        else if (msg_type == KAFKA_MSG_NOTIFY_WEB)
        {
            notify_web_builder_->payload(msg);
            notify_web_builder_->key(key);
            notify_web_builder_->partition(0);
            producer_->produce(*notify_web_builder_);
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream alarm_msg;
                alarm_msg << "Producer Nofiy Web message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                AK_LOG_WARN << "Producer message wait akcs timeout! key=" << key;
            };
        }
        else if (msg_type == KAFKA_MSG_NOTIFY_LINKER)
        {
            notify_linker_builder_->payload(msg);
            notify_linker_builder_->key(key);
            notify_linker_builder_->partition(cslinker_index);
            producer_->produce(*notify_linker_builder_);
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream alarm_msg;
                alarm_msg << "Producer Nofiy cslinker message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_CSLINKER);
                AK_LOG_WARN << "Producer message wait akcs timeout! key=" << key;
            };
            ++cslinker_index;
            cslinker_index = cslinker_index % KAFKA_AK_LINKER_PARTITION_NUM;            
        }
        else if (msg_type == KAFKA_MSG_NOTIFY_WEB_MESSAGE)
        {
            notify_web_message_builder_->payload(msg);
            notify_web_message_builder_->key(key);
            producer_->produce(*notify_web_message_builder_);
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream message;
                message << "Producer Nofiy Web Message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, message.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                AK_LOG_WARN << "Producer Nofiy Web Message wait akcs timeout! key=" << key;
            };
        }
        else if (msg_type == KAFKA_MSG_NOTIFY_WEB_ATTENDANCE)
        {
            notify_web_attendance_builder_->payload(msg);
            notify_web_attendance_builder_->key(key);
            producer_->produce(*notify_web_attendance_builder_);
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream alarm_msg;
                alarm_msg << "Producer Nofiy Web attendance message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                AK_LOG_WARN << "Producer attendance message wait akcs timeout! key=" << key;
            };
        }
        else if (msg_type == KAFKA_MSG_NOTIFY_WEB_ACCESS_DOOR)
        {
            notify_web_access_door_builder_->payload(msg);
            notify_web_access_door_builder_->key(key);
            producer_->produce(*notify_web_access_door_builder_);
            AK_LOG_INFO << "notify web access door msg. topic=" << notify_web_access_door_builder_->topic() << "\n msg=" << msg;
            producer_->async_flush();
            std::chrono::milliseconds mscond(1000 * 15);
            if (!producer_->wait_for_acks(mscond))
            {
                std::string worker_node = "csroute";
                std::stringstream alarm_msg;
                alarm_msg << "Producer Nofiy Web access door message wait akcs timeout! key=" << key;
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
                AK_LOG_WARN << "Producer access door message wait akcs timeout! key=" << key;
            };
        }
    }
    return 0;
}




