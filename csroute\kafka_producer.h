#ifndef KAFAK_PRODUCER_
#define KAFAK_PRODUCER_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <thread>
#include <boost/algorithm/string.hpp>
#include <stdexcept>
#include <iostream>
#include "cppkafka/utils/buffered_producer.h"
#include "cppkafka/configuration.h"
#include "AkLogging.h"
#include "AkcsMonitor.h"


using cppkafka::BufferedProducer;
using cppkafka::Configuration;
using cppkafka::Topic;
using cppkafka::MessageBuilder;
using cppkafka::Message;

class  KAFKA_PRODUCE_MSG;
typedef std::shared_ptr<KAFKA_PRODUCE_MSG> KafkaProduceMsgPtr;

class  KAFKA_PRODUCE_MSG
{
public:
    KAFKA_PRODUCE_MSG(int msg_type, const std::string& key, const std::string& payload)
        : key_(key), payload_(payload), msg_type_(msg_type)
    {

    }
    std::string get_key()
    {
        return key_;
    }
    std::string get_payload()
    {
        return payload_;
    }
    int get_msg_type()
    {
        return msg_type_;
    }    
private:
    std::string key_;
    std::string payload_;
    int  msg_type_;
};


class CKafakProducer
{

public:
    CKafakProducer(const std::string& topic, const std::string& broker_ip_list);
    ~CKafakProducer() {};

    void CreatePmExportLogTopic(const std::string& topic);
    void CreatePmExportLogExcelTopic(const std::string& topic);
    void CreateNotifyWebTopic(const std::string& topic);
    void CreateNotifyLinkerTopic(const std::string& topic);
    void CreateNotifyWebMessageTopic(const std::string& topic);
    void CreateNotifyWebAttendanceTopic(const std::string& topic);
    void CreateNotifyWebAccessDoorTopic(const std::string& topic);
    
    int Init();
    int ProduceMsg(KafkaProduceMsgPtr msg);
    int ProcessMsgThread();
private:
    std::string email_topic_;
    std::string broker_ip_list_;
    std::list<KafkaProduceMsgPtr> msg_list_;
    std::shared_ptr<BufferedProducer<std::string>> producer_;
    std::shared_ptr<MessageBuilder> email_builder_;

    std::string pm_export_log_topic_;
    std::shared_ptr<MessageBuilder> pm_export_log_builder_;
    
    std::string pm_export_log_excel_topic_;
    std::shared_ptr<MessageBuilder> pm_export_log_excel_builder_;

    std::string notify_web_topic_;
    std::shared_ptr<MessageBuilder>notify_web_builder_;

    std::string notify_linker_topic_;
    std::shared_ptr<MessageBuilder>notify_linker_builder_;
    
    std::string notify_web_message_topic_;
    std::shared_ptr<MessageBuilder>notify_web_message_builder_;

    std::string notify_web_attendance_topic_;
    std::shared_ptr<MessageBuilder>notify_web_attendance_builder_;

    std::string notify_web_access_door_topic_;
    std::shared_ptr<MessageBuilder>notify_web_access_door_builder_;

    std::mutex msg_mutex_;
    std::condition_variable condition_var_;
    std::thread msg_handle_thread_t_;
};

#endif
