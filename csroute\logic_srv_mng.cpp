#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AkLogging.h"
#include <sstream>
#include "util.h"
#include "ConsistentHashMap.hpp"
#include "logic_srv_mng.h"


CLogicSrvMng* CLogicSrvMng::instance_ = nullptr;
//const int32_t CLogicSrvMng::kVnodeNum = 50; 直接初始化
CLogicSrvMng* CLogicSrvMng::Instance()
{
    if (!instance_)
    {
        instance_ = new CLogicSrvMng();
    }
    return instance_;
}

uint32_t CLogicSrvMng::crc32_hash(const std::string key)
{
    boost::crc_32_type ret;
    ret.process_bytes(key.c_str(), key.size());
    return ret.checksum();
}


void CLogicSrvMng::UpdateConfigOfficeSrvList(const std::set<std::string>& csconfig_office_list)
{
    std::vector<vnode_t> vnode_list;
    for (const auto& addr : csconfig_office_list)
    {
        vnode_list.push_back(vnode_t(addr, 0));
    }
    std::lock_guard<std::mutex> lock(config_office_consistent_mutex_);
    config_office_consistent_hash_.clear();
    for (const auto &vnode: vnode_list)
    {
        for (int j = 0; j < kVnodeNum; j++)
        {
            config_office_consistent_hash_.insert(vnode_t(vnode.ipv4_, j));
        }
    }
}

std::string CLogicSrvMng::GetConfigOfficeSrv(const std::string& key)
{
    uint32_t key_hash = crc32_hash(key);
    consistent_hash_t::iterator it;
    {
        std::lock_guard<std::mutex> lock(config_office_consistent_mutex_);
        it = config_office_consistent_hash_.find(key_hash);
        if (it == config_office_consistent_hash_.end())
        {
            AK_LOG_WARN << "GetConfigOfficeSrv, key is " << key;
            return "";
        }
        
        return it->second.ipv4_;
    }
    return "";
}

