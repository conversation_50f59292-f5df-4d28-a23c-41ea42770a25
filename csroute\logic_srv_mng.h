#ifndef __CSROUTE_LOGIC_SRV_MANAGE_H__
#define __CSROUTE_LOGIC_SRV_MANAGE_H__

#include <boost/noncopyable.hpp>
#include <map>
#include <string>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "ConsistentHashMap.hpp"
#include <mutex>

struct vnode_t
{
    vnode_t() {}
    vnode_t(std::string ipv4, std::size_t v)
        : ipv4_(ipv4), vnode_id_(v) {}

    std::string to_str() const
    {
        return boost::str(boost::format("%1%-%2%") % ipv4_ % vnode_id_);
    }
    std::string ipv4_;
    std::size_t vnode_id_;
};

//只对ipv4做hash
struct crc32_hasher
{
    uint32_t operator()(const vnode_t& node)   //这样就可以这样了: size_type hash = hasher_(node);
    {
        boost::crc_32_type ret;
        std::string vnode = node.to_str();
        ret.process_bytes(vnode.c_str(), vnode.size()); //对vnode的str进行哈希,得到虚拟节点的key
        return ret.checksum();
    }
    typedef uint32_t result_type;
};

//通过一致性哈希算法进行负载均衡
class CLogicSrvMng : boost::noncopyable
{
public:
    typedef ConsistentHash<vnode_t, crc32_hasher> consistent_hash_t;
    static const int kVnodeNum = 5000;
public:
    CLogicSrvMng() {}
    ~CLogicSrvMng() {}
    static CLogicSrvMng* Instance();
    uint32_t crc32_hash(const std::string key);
   
    void UpdateConfigOfficeSrvList(const std::set<std::string>& csconfig_office_list);
    std::string GetConfigOfficeSrv(const std::string& key);

private:
    static CLogicSrvMng* instance_;
    std::mutex config_office_consistent_mutex_;
    consistent_hash_t config_office_consistent_hash_;
};

#endif // __CSGATE_LOGIC_SRV_MANAGE_H__

