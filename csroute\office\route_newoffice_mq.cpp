#include "stdlib.h"
#include <functional>
#include <evpp/tcp_conn.h>
#include <evnsq/consumer.h>
#include <evnsq/producer.h>
#include <json/json.h>
#include "util.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "AkcsOemDefine.h"
#include "AkcsMonitor.h"
#include "push_client.h"
#include "push_kafka.h"
#include "route_server.h"
#include "session_rpc_client.h"
#include "AK.Linker.pb.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AK.Adapt.pb.h"
#include "SafeCacheConn.h"
#include "video_rpc_client.h"
#include "route_mq.h"
#include "route_newoffice_mq.h"
#include "AK.ServerOffice.pb.h"
#include "PushClientMng.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"


const static int32_t kAkMsgHoldLen = 4;
extern RouteServer* g_route_ser;
extern SmRpcClient* g_sm_client_ptr;
extern VideoStorageClient* g_vs_client_ptr;
extern CPushKafkaClient* g_push_kafka;

RouteNewOfficeMQCust* RouteNewOfficeMQCust::instance_ = NULL;

RouteNewOfficeMQCust* RouteNewOfficeMQCust::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new RouteNewOfficeMQCust();
    }
    return instance_;
}

int RouteNewOfficeMQCust::OnMessage(const std::shared_ptr<CAkcsPdu>& pdu)
{
    int already_handle = 1;
    uint32_t msg_id = pdu->GetCommandId();
    switch (msg_id)
    {
        case MSG_S2C_DEV_CONFIG_REWRITE:
        {            
            HandleP2PReportToConfigMsg(pdu);
            break;
        }
        case MSG_S2C_ACCOUNT_CONFIG_REWRITE:
        {
            HandleP2PReportToConfigNodeMsg(pdu);
            break;
        }
        case MSG_S2C_DEV_REQ_USER_INFO:
        {
            HandleP2PReqUserInfoMsg(pdu);
            break;
        }
        case MSG_C2S_NEW_OFFICE_EXPORT_LOG:
        {
            HandleNewOfficeExportLogMsg(pdu);
            break;
        }
        case AKCS_M2R_P2P_TEMPKEY_USED_NOTIFY_MSG:
        {
            HandleP2PTempKeyUsedNotifyMsg(pdu);
            break;
        }
        case AKCS_M2R_P2P_SEND_LOCKDOWN_NOTIFY_MSG:
        {
            HandleP2PSendLockDownNotifyMsg(pdu);
            break;
        }
        default:
        {
            already_handle = 0;
        }
    }    
    return already_handle;

}

void RouteNewOfficeMQCust::HandleP2PReportToConfigMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csmain->csroute->csconfig-office HandleP2PReportToConfigMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCsconfigOfficeConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csconfig route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

//只有这个需要做投递到指定config-office，其他到config-office会在投递到kafka
void RouteNewOfficeMQCust::HandleP2PReqUserInfoMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainRequestWriteUserinfo msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    
    std::string key = msg.project_uuid();
    
    AK_LOG_INFO << "csmain->csroute->csconfig-office HandleP2PReportToConfigMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCsconfigOfficeConn(key);
    if (!conn)
    {
        AK_LOG_WARN << "get csconfig route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}


void RouteNewOfficeMQCust::HandleP2PReportToConfigNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csmain->csroute->csconfig-office HandleP2PReportToConfigNodeMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCsconfigOfficeConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csconfig route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteNewOfficeMQCust::HandleNewOfficeExportLogMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    Json::Value         root;
    Json::Reader        reader;
    Json::FastWriter    writer;
    std::string         trace_id = "";

    int kafka_msg_type = KAFKA_MSG_PM_EXPORT_LOG;
    std::string msg_json = std::string(pdu->GetBodyData(), pdu->GetBodyLength());
    if (reader.parse(msg_json, root) == false)
    {
        AK_LOG_WARN << "HandleNewOfficeExportLogMsg parse json error. message=" << msg_json;
        return;
    }

    if (root.isMember("data") == false)
    {
        AK_LOG_WARN << "HandleNewOfficeExportLogMsg no data member. message=" << msg_json;
        return;
    }

    Json::Value& data = root["data"];
    data["project_type"] = project::OFFICE_NEW;
    if (data.isMember("export_type") && data["export_type"].asInt() == (int)ExportLogType::EXPORT_CSV)
    {
        kafka_msg_type = KAFKA_MSG_PM_EXPORT_LOG_EXCEL;
    }

    if(data.isMember("trace_id") && data["trace_id"].isString()){
        trace_id = data["trace_id"].asString();
    }

    std::string push_msg = writer.write(root);
    g_push_kafka->pushMsg(kafka_msg_type, trace_id, push_msg);
}

void RouteNewOfficeMQCust::HandleP2PTempKeyUsedNotifyMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    g_push_kafka->pushMsg(
        KAFKA_MSG_NOTIFY_WEB_MESSAGE,
        std::to_string(pdu->GetTraceId()),
        std::string(pdu->GetBodyData(), pdu->GetBodyLength())
    );
}

void RouteNewOfficeMQCust::HandleP2PSendLockDownNotifyMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    g_push_kafka->pushMsg(
        KAFKA_MSG_NOTIFY_WEB_MESSAGE,
        std::to_string(pdu->GetTraceId()),
        std::string(pdu->GetBodyData(), pdu->GetBodyLength())
    );
}
