#ifndef __CSROUTE_NEW_OFFICE_MQ_H__
#define __CSROUTE_NEW_OFFICE_MQ_H__

#include <memory>
#include <thread>
#include <mutex>
#include <list>
#include <condition_variable>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <evpp/evnsq/message.h>
#include "AK.Adapt.pb.h"


class RouteNewOfficeMQCust
{
public:
    RouteNewOfficeMQCust() {}
    ~RouteNewOfficeMQCust() {}

public:
    static RouteNewOfficeMQCust* GetInstance();
    //message已经是一条完整的消息了
    int OnMessage(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PReportToConfigMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PReqUserInfoMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PReportToConfigNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandleNewOfficeExportLogMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PTempKeyUsedNotifyMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PSendLockDownNotifyMsg(const std::shared_ptr<CAkcsPdu>& pdu);

private:
    static RouteNewOfficeMQCust* instance_;
};

#endif //__CSROUTE_MQ_H__

