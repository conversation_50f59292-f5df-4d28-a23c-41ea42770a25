#include "stdlib.h"
#include <functional>
#include <evpp/tcp_conn.h>
#include <evnsq/consumer.h>
#include <evnsq/producer.h>
#include "util.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "AkcsOemDefine.h"
#include "AkcsMonitor.h"
#include "push_client.h"
#include "push_kafka.h"
#include "route_server.h"
#include "session_rpc_client.h"
#include "AK.Linker.pb.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AK.Adapt.pb.h"
#include "SafeCacheConn.h"
#include "video_rpc_client.h"
#include "route_mq.h"
#include "route_office_mq.h"
#include "AK.ServerOffice.pb.h"
#include "PushClientMng.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"


const static int32_t kAkMsgHoldLen = 4;
extern RouteServer* g_route_ser;
extern SmRpcClient* g_sm_client_ptr;
extern VideoStorageClient* g_vs_client_ptr;
extern CPushKafkaClient* g_push_kafka;

RouteOfficeMQCust* RouteOfficeMQCust::instance_ = NULL;

RouteOfficeMQCust* RouteOfficeMQCust::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new RouteOfficeMQCust();
    }
    return instance_;
}

int RouteOfficeMQCust::OnMessage(const std::shared_ptr<CAkcsPdu>& pdu)
{
    int already_handle = 1;
    uint32_t msg_id = pdu->GetCommandId();
    switch (msg_id)
    {
        //以下是route对各个逻辑服务器请求的转发
        case AKCS_M2R_GROUP_OFFICE_ALARM_REQ:
        case AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_REQ:
        case AKCS_M2R_GROUP_OFFICE_PER_MOTION_REQ:
        case AKCS_M2R_GROUP_OFFICE_MNG_TEXT_MSG_REQ:
        {
            HandleGroupMsg2Csmain(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ:
        {
            HandleP2PAppGetArmingMsg(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP:
        {
            HandleP2PAppGetArmingRespMsg(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_VISITOR_AUTHORIZE_REQ:
        {
            HandleP2PVisitorAuthorize(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_RTSP_CAPTURE_REQ:
        {
            HandleP2PRtspCaputreMsg(pdu);
            break;
        }
        /*
        case AKCS_M2R_P2P_OFFICE_OPEN_DOOR_REQ:
        {
            HandleP2PDevOpenDoor(pdu);
            break;
        }
        */
        case AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ:
        {
            HandleP2PDevSendDelivery(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_CHANGE_RELAY_REQ:
        {
            HandleP2PChangeRelay(pdu);
            break;
        }
        case AKCS_M2R_P2P_OFFICE_SEND_REMIND_FLOW_OUT_OF_LIMIT:
        {
            HandleSendRemindOutOfFlow(pdu);
            break;
        }
        //csadapt 过来的消息
        //广播到各个csmain
        case MSG_C2S_OFFICE_NOTIFY_CONFIG_FILE_CHANGE:
        {
            HandleGroupMsg2Csmain(pdu);
            break;
        }        
        case MSG_C2S_OFFICE_SEND_CREATE_UID_MAIL:
        {
            HandleCreateUidMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_ACCOUNT_RENEW_MAIL:
        {
            HandleAccountRenewMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_PM_ACCOUNT_WILL_EXPIRE_MAIL:
        {
            HandlePMAccountWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_PM_ACCOUNT_EXPIRE_MAIL:
        {
            HandlePMAccountExpireMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_RESET_PWD_MAIL:
        {
            HandleResetPwdMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_CHANGE_PWD_MAIL:
        {
            HandleChangePwdMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_PM_FEATURE_WILL_EXPIRE_MAIL:
        {
            HandlePMFeatureWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_WILL_EXPIRE_MAIL:
        {
            HandleInstallerFeatureWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_PM_FEATURE_EXPIRE_MAIL:
        {
            HandlePMFeatureExpireMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_EXPIRE_MAIL:
        {
            HandleInstallerFeatureExpireMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_SEND_USER_ADD_NEWSITE:
        {
            HandleOfficeUserAddNewSite(pdu);
            break;
        }
        case AKCS_S2R_P2P_VOICE_MSG_ACK_REQ:
        {
            HandleOfficeStorageVoiceAckMsg(pdu);
            break;
        }
        case AKCS_L2R_WEATHER_INFO_RESP:
        {
            HandleOfficeP2PDevWeatherMsg(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG:
        {
            HandleOfficeLinkerCommonMsg(pdu);
            break;
        } 
        case MSG_C2S_REFRESH_APP_CONF:
        {
            HandleOfficeNotifyAppChangeConfMsg(pdu);
            break;
        }
        default:
        {
            already_handle = 0;
        }
    }    
    return already_handle;

}

void RouteOfficeMQCust::P2PRouteMsg(const evpp::TCPConnPtr &conn, const google::protobuf::MessageLite &msg, uint32_t command_id)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(command_id);
    pdu.SetSeqNum(0);
    conn->Send(pdu.GetBuffer(), pdu.GetLength());
}

void RouteOfficeMQCust::HandleCreateUidMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_CREATE_UID, &msg);
}

void RouteOfficeMQCust::HandleAccountRenewMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::ServerOffice::P2PAdaptPMOfficeRenewMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_ACCOUNT_RENEW, &msg);
}

void RouteOfficeMQCust::HandlePMAccountExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "office receive csadapt pm mail msg";
    AK::ServerOffice::PMAppExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_PM_ACCOUNT_EXPIRE, &msg);
}

void RouteOfficeMQCust::HandlePMAccountWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "office receive csadapt pm mail msg";
    AK::Server::P2PAdaptPMAccountWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_PM_ACCOUNT_WILL_EXPIRE, &msg);
}

void RouteOfficeMQCust::HandlePMFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "office receive csadapt pm feature mail msg";
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_PM_FEATURE_WILL_EXPIRE, &msg);
}

void RouteOfficeMQCust::HandleInstallerFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "office receive csadapt ins feature mail msg";
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_INSTALLER_FEATURE_WILL_EXPIRE, &msg);
}

void RouteOfficeMQCust::HandlePMFeatureExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "office receive csadapt pm feature mail msg";
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_PM_FEATURE_EXPIRE, &msg);
}

void RouteOfficeMQCust::HandleInstallerFeatureExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "office receive csadapt ins feature mail msg";
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_INSTALLER_FEATURE_EXPIRE, &msg);
}

void RouteOfficeMQCust::HandleResetPwdMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptResetPwdMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_RESET_PWD, &msg);
}

void RouteOfficeMQCust::HandleChangePwdMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_CHANGE_PWD, &msg);
}

//群发
void RouteOfficeMQCust::HandleGroupMsg2Csmain(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "group message to csmain msg id:" << pdu->GetCommandId();
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu->GetBuffer(), pdu->GetLength());
    }
}

void RouteOfficeMQCust::HandleP2PAppGetArmingMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainAppHandleArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain app get dev arming req:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteOfficeMQCust::HandleP2PVisitorAuthorize(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainHandleVisitorAuth msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain visitor authorize msg:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteOfficeMQCust::HandleP2PRtspCaputreMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Route::RtspCaptureReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    std::string mac = msg.mac();
    std::string flow_uuid = msg.flow_uuid();
    AK_LOG_INFO << "receive rtsp capture req from csmain:" << msg.DebugString();

    SafeCacheConn redis(g_redis_db_mac_vrtspsid);
    std::string logic_vrtsp_srv_id_cache = redis.get(flow_uuid);

    // mac还没有在其他的监控服务器上传输流,直接退出,本次截图信令失败
    if (logic_vrtsp_srv_id_cache.size() == 0) 
    {
        AK_LOG_WARN << "the flow: " << flow_uuid << " is not be monitored now, failed to capture";
    }
    else //该设备正在被监控中
    {
        evpp::TCPConnPtr conn = g_route_ser->GetVrtspConnBySid(logic_vrtsp_srv_id_cache);
        if (!conn)
        {
            AK_LOG_WARN << "get csvrtspd route client conn failed,sid is " << logic_vrtsp_srv_id_cache;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2V_RTSP_CAPTURE_REQ); //route往设备视频流的源站rtsp服务发送分流信令
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

/*
void RouteOfficeMQCust::HandleP2PDevOpenDoor(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainRequestOpenDoor msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain open door msg:" << msg.DebugString();

    //查询mac在那一台服务器上面，转发到csoffice
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetOfficeConnBySid(sid);

    if (conn)
    {
        // P2PRouteMsg(conn, msg, AKCS_R2B_P2P_OFFICE_FROM_DEVICE_OPEN_DOOR_REQ);
    }
    else
    {
        AK_LOG_WARN << "get office route client conn failed, sid is " << sid;
        return;
    }
}
*/

void RouteOfficeMQCust::HandleP2PDevSendDelivery(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainSendDelivery msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain send delivery msg:" << msg.DebugString();
    std::vector<std::string> account_list;
    account_list.push_back(msg.account());
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, SmsType::SMS_DELIVERY);
    }     

    //查询account在那一台服务器上面
    for(const auto& account : account_list)
    {
        std::string sid = g_sm_client_ptr->QueryUid(account);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            return;
        }

        msg.set_account(account);
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ);
        pdu2.SetProjectType(project::OFFICE);
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());//消息id不变
    }
}

void RouteOfficeMQCust::HandleP2PChangeRelay(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainChangeRelay msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain change " << msg.mac() << " relay";
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());
}

void RouteOfficeMQCust::HandleSendRemindOutOfFlow(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Server::SendSmsRemindFlowOutofLimit msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, msg.type());
    }
}

void RouteOfficeMQCust::HandleP2PAppGetArmingRespMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainAppHandleArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain app get/or indoor set dev arming resp,uid is " << msg.uid() << ",main_site is " << msg.main_site() << ";oem is " << msg.oem() << ";resp_action is " << msg.resp_action();
    int oem = msg.oem();
    if (oem == OEMID_ROBERT)
    {
        int resp_action  = msg.resp_action();
        if (resp_action == REPORT_ARMING_ACTION_TYPE_GET)
        {
            std::string sid = g_sm_client_ptr->QueryUid(msg.main_site());
            evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
                return;
            }
            conn->Send(pdu->GetBuffer(), pdu->GetLength());
        }
        else //别的情况广播出去
        {
            LogicSerConnList csmain_ser_conns;
            {
                std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
                csmain_ser_conns = g_route_ser->csmain_ser_conns_;
            }
            for (auto& conn : csmain_ser_conns)
            {
                conn.second->Send(pdu->GetBuffer(), pdu->GetLength());
            }
        }
        return;
    }

    std::string sid = g_sm_client_ptr->QueryUid(msg.main_site());//设备自己设置arming时候 uid为空
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (conn)
    {
        conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
        return;
    }

}

void RouteOfficeMQCust::HandleOfficeUserAddNewSite(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendUserAddNewSite msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildOfficeMailPushMsg(EMAIL_OFFICE_ADD_NEW_SITE, &msg);
}

void RouteOfficeMQCust::HandleOfficeStorageVoiceAckMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive storage dev voice resend msg handle ok, msg:" << msg.DebugString();

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetOfficeConnBySid(sid);
    if (conn)
    {   
        P2PRouteMsg(conn, msg, AKCS_R2S_P2P_VOICE_MSG_ACK_REQ);
    }
}

void RouteOfficeMQCust::HandleOfficeP2PDevWeatherMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::LinkerWeatherNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetOfficeConnBySid(sid);
    if (conn)
    {   
        P2PRouteMsg(conn, msg, AKCS_R2B_P2P_WEATHER_INFO_RESP);
    }
}

void RouteOfficeMQCust::HandleOfficeLinkerCommonMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::P2PRouteLinker msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildLinkerCommonMsg(msg.message_type(), msg.project_type(), msg.msg_json(), msg.key());
}

void RouteOfficeMQCust::HandleOfficeNotifyAppChangeConfMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptNotifyAppRefreshConfigMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[HandleOfficeNotifyAppChangeConfMsg] receive msg:" << msg.DebugString();
    std::string main_site;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(msg.account(), main_site);

    std::string sid = g_sm_client_ptr->QueryUid(main_site);
    evpp::TCPConnPtr conn = g_route_ser->GetOfficeConnBySid(sid);
    if (conn)
    {   
        P2PRouteMsg(conn, msg, AKCS_R2B_P2P_REFRESH_APP_USERCONF);
    }
}

