#ifndef __CSROUTE_OFFICE_MQ_H__
#define __CSROUTE_OFFICE_MQ_H__

#include <memory>
#include <thread>
#include <mutex>
#include <list>
#include <condition_variable>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <evpp/evnsq/message.h>
#include "AK.Adapt.pb.h"


class RouteOfficeMQCust
{
public:
    RouteOfficeMQCust() {}
    ~RouteOfficeMQCust() {}

public:
    static RouteOfficeMQCust* GetInstance();
    //message已经是一条完整的消息了
    int OnMessage(const std::shared_ptr<CAkcsPdu>& pdu);
    //广播消息到所有csmain
    void HandleGroupMsg2Csmain(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PAppGetArmingMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PVisitorAuthorize(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PRtspCaputreMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    // void HandleP2PDevOpenDoor(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PDevSendDelivery(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PChangeRelay(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendRemindOutOfFlow(const std::shared_ptr<CAkcsPdu> &pdu);
    void HandleCreateUidMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleAccountRenewMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMAccountWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleResetPwdMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleChangePwdMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMAccountExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PAppGetArmingRespMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleInstallerFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMFeatureExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleInstallerFeatureExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleOfficeUserAddNewSite(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleOfficeStorageVoiceAckMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleOfficeP2PDevWeatherMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleOfficeLinkerCommonMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleOfficeNotifyAppChangeConfMsg(const std::shared_ptr<CAkcsPdu>& pdu);
private:
    void P2PRouteMsg(const evpp::TCPConnPtr &conn, const google::protobuf::MessageLite &msg, uint32_t command_id);
    static RouteOfficeMQCust* instance_;
};

#endif //__CSROUTE_MQ_H__

