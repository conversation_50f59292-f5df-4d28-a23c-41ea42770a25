#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "push_client.h"
#include "encrypt/AES128.h"
#include "AK.Server.pb.h"
#include "AK.Adapt.pb.h"
#include <boost/algorithm/string.hpp>
#include "route_server.h"
#include "kafka_producer.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/Account.h"
#include "AkcsCommonDef.h"
#include "AK.ServerOffice.pb.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "AkcsMsgDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include "Singleton.h"
#include "SnowFlakeGid.h"


extern AKCS_ROUTE_CONF gstAKCSConf;
extern CKafakProducer* g_kafka_producer;

CPushClient::CPushClient(evpp::EventLoop* loop,
                         const std::string& serverAddr/*ip:port*/,
                         const std::string& name)
    : client_(loop, serverAddr, name)
    , addr_(serverAddr)
{
    client_.SetConnectionCallback(
        std::bind(&CPushClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&CPushClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(5.0));
    client_.set_auto_reconnect(true);
}

bool CPushClient::IsConnStatus()
{
    return connect_status_ == true;
}

std::string CPushClient::GetAddr()
{
    return addr_;
}


void CPushClient::buildSmsPushMsg(const ::google::protobuf::Message* msg, int sms_type)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    item["app_type"] = "sms";
    item["OEM"] = gstAKCSConf.oem_name;
    item["ver"] = PUSH_SERVER_VER;
    itemData["type"] = sms_type;    
    if(sms_type == SMS_LOGIN || sms_type == SMS_RESET)
    {       
        const AK::Adapt::SendSmsCode* sms_msg = static_cast<const AK::Adapt::SendSmsCode*>(msg);
        itemData["code"] = sms_msg->code();
        itemData["area_code"] = sms_msg->area_code();
        itemData["phone"] = sms_msg->phone();
        PhoneUserType user_type = static_cast<PhoneUserType>(sms_msg->user_type());

        ResidentPerAccount  account;
        memset(&account, 0, sizeof(account));
        if (0 != dbinterface::ResidentPersonalAccount::GetPhoneAccount(sms_msg->phone(), account, user_type))
        {
           AK_LOG_INFO << "[PushMsg] SMS_CHECKCODE phone:" <<  sms_msg->phone() << " code:" << sms_msg->code() << " userType:" << sms_msg->user_type() << " error, phone not found!";        
           return;
        }
        itemData["language"] = account.language;

        if (account.conn_type == csmain::DeviceType::COMMUNITY_APP)
        {
            CommunityInfo communitinfo(account.account);
            if (communitinfo.EnableSmartHome())
            {
                itemData["enable_smarthome"] = "1";
            }
            else
            {
                itemData["enable_smarthome"] = "0";
            }            
        }
        
        AK_LOG_INFO << "[PushMsg] SMS_CHECKCODE phone:" << sms_msg->phone() << " code:" << sms_msg->code();
    }
    else if(sms_type == SMS_DELIVERY)
    {
        const AK::Server::P2PMainSendDelivery* sms_msg = static_cast<const AK::Server::P2PMainSendDelivery*>(msg);        
        ResidentPerAccount  account;
        memset(&account, 0, sizeof(account));
        if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(sms_msg->account(), account))
        {
            AK_LOG_WARN << "account:" << sms_msg->account() << " don't have mobile number";
            return;
        }
        std::string mobile_number = account.getMobileNumber();
        if(mobile_number.empty())   //未绑定手机号的用户
        {
            return;
        }
        itemData["phone"] = mobile_number;
        itemData["area_code"] = account.phone_code;
        itemData["name"] = account.name;
        itemData["language"] = account.language;
        
        itemData["amount"] = sms_msg->amount();
        AK_LOG_INFO << "[PushMsg] SMS_DELIVERY name:" << account.name << " phone:" << account.phone_code << mobile_number << " amount:" << sms_msg->amount();
    }
    else if(sms_type == SMS_DELIVERY_BOX)
    {
        const AK::Server::P2PMainSendDelivery* sms_msg = static_cast<const AK::Server::P2PMainSendDelivery*>(msg);        
        ResidentPerAccount  account;
        memset(&account, 0, sizeof(account));
        if(0 != dbinterface::ResidentPersonalAccount::GetUidAccount(sms_msg->account(), account))
        {
            AK_LOG_WARN << "account:" << sms_msg->account() << " don't have mobile number";
            return;
        }
        std::string mobile_number = account.getMobileNumber();
        if(mobile_number.empty())   //未绑定手机号的用户
        {
            return;
        }
        itemData["phone"] = mobile_number;
        itemData["area_code"] = account.phone_code;
        itemData["content"] = sms_msg->content();
        AK_LOG_INFO << "[PushMsg] SMS_DELIVERY_BOX name:" << account.name << " phone:" << account.phone_code << mobile_number;
    }
    else if (sms_type == SMS_FLOW_OUT_OF_LIMIT)
    {
        const AK::Server::SendSmsRemindFlowOutofLimit *sms_msg = static_cast<const AK::Server::SendSmsRemindFlowOutofLimit *>(msg);
        itemData["phone"] = sms_msg->phone();
        itemData["area_code"] = sms_msg->area_code();
        itemData["flow"] = sms_msg->flow();
        itemData["unit_name"] = sms_msg->unit_name();
        itemData["location"] = sms_msg->location();
        itemData["grade"] = sms_msg->grade();
        itemData["language"] = sms_msg->language();
        itemData["out_of_flow"] = sms_msg->out_of_flow();
        itemData["community_name"] = sms_msg->community_name();
        AK_LOG_INFO << "[PushMsg] SMS_FLOW_OUT_OF_LIMIT" << " phone:" << sms_msg->phone();
    }
    else if (sms_type == SMS_DEL_APP_ACCOUNT || sms_type == SMS_FAMILY_DEL_APP_ACCOUNT)
    {
        const AK::Server::SendSmsCodeDelAppAccount *sms_msg = static_cast<const AK::Server::SendSmsCodeDelAppAccount *>(msg);
        itemData["phone"] = sms_msg->phone();
        itemData["area_code"] = sms_msg->area_code();
        itemData["language"] = sms_msg->language();
        itemData["code"] = sms_msg->code();
        AK_LOG_INFO << "[PushMsg] SMS_DEL_APP_ACCOUNT" << " phone:" << sms_msg->phone();
    } 
    else if (sms_type == SMS_FAMILY_CREATE_UID)
    {
        const AK::Server::SendSmsCreateUid *sms_msg = static_cast<const AK::Server::SendSmsCreateUid *>(msg);
        itemData["phone"] = sms_msg->phone();
        itemData["area_code"] = sms_msg->area_code();
        itemData["user"] = sms_msg->user();
        itemData["pwd"] = sms_msg->pwd();
        itemData["gw_code"] = gstAKCSConf.gw_code;
        itemData["language"] = sms_msg->language();
        AK_LOG_INFO << "[PushMsg] SMS_CREATE_UID MSG" << " phone:" << sms_msg->phone();
    } 
    else if (sms_type == SMS_COMMON_SEND_CODE)
    {
        const AK::Server::P2PSendCodeToMobile *sms_msg = static_cast<const AK::Server::P2PSendCodeToMobile *>(msg);
        itemData["phone"] = sms_msg->phone();
        itemData["area_code"] = sms_msg->area_code();
        itemData["language"] = sms_msg->language();
        itemData["code"] = sms_msg->code();
        itemData["code_type"] = sms_msg->type();
        AK_LOG_INFO << "[PushMsg] SMS_COMMON_SEND_CODE" << " phone:" << sms_msg->phone();
    } 
    else
    {
        AK_LOG_WARN << "Invalid Sms Type=" << sms_type;
        return;
    }


    std::string data_json = wData.write(itemData);
    char *pszEncData = nullptr;
    char szIv[17];
    GetCspushAES128IV(szIv);
    AES128Base64Encrypt(gstAKCSConf.push_aeskey, szIv, data_json.c_str(), strlen(data_json.c_str()), &pszEncData);
    if (pszEncData)
    {
        item["data"] = pszEncData;
        ::free(pszEncData);
    }
    std::string msg_json = w.write(item);
    auto c = client_.conn();
    if (c && c->IsConnected())
    {
        c->Send(msg_json);   
    }
    else
    {
       AK_LOG_INFO << "[PushMsg] conn is error";
    }    
}


