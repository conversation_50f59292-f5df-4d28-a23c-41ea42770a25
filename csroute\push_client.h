#ifndef __CSROUTE_PUSH_CLIENT_H__
#define __CSROUTE_PUSH_CLIENT_H__

#include <evpp/tcp_client.h>
#include <evpp/tcp_conn.h>

#include "AkLogging.h"

namespace google
{
namespace protobuf
{
class Message;
}
}


#define PUSH_SERVER_VER "1"

class CPushClient;
typedef std::shared_ptr<CPushClient> PushClientPtr;

class CPushClient
{
public:
    CPushClient(evpp::EventLoop* loop,
                const std::string& serverAddr/*ip:port*/,
                const std::string& name);

    void Start()
    {
        client_.Connect();
        client_.set_auto_reconnect(true);
    }

    void Stop()
    {
        client_.set_auto_reconnect(false);    
        if (connect_status_ == true)
        {
            client_.Disconnect();
            connect_status_ = false;
        }
    }

    void ReConnectByNewSeverAddr(const std::string& serverAddr)
    {
        addr_ = serverAddr;
        client_.ReconnectByNewServerAddr(serverAddr);
    }

    bool IsConnStatus();
    std::string GetAddr();
    
    void buildSmsPushMsg(const ::google::protobuf::Message* msg, int sms_type);
    //static CPushClient *Instance();
private:
    void OnConnection(const evpp::TCPConnPtr& conn)
    {
        if (conn->IsConnected())
        {
            connect_status_ = true;
            AK_LOG_INFO << "connect to push server " << addr_ << " successful.";
        }
        else
        {
            connect_status_ = false;
            AK_LOG_WARN << "disconnect to push server ";
        }
    }
    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
    {

    }
private:
    evpp::TCPClient client_;
    std::string addr_;
    std::atomic<bool> connect_status_;
};

#endif // __CSROUTE_PUSH_CLIENT_H__
