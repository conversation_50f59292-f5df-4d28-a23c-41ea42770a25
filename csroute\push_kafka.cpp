#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "push_kafka.h"
#include "encrypt/AES128.h"
#include "AK.Server.pb.h"
#include "AK.Adapt.pb.h"
#include <boost/algorithm/string.hpp>
#include "route_server.h"
#include "kafka_producer.h"
#include "AkcsCommonDef.h"
#include "AK.ServerOffice.pb.h"
#include "AkcsMsgDef.h"
#include "Singleton.h"
#include "SnowFlakeGid.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "ThreadLocalSingleton.h"

extern AKCS_ROUTE_CONF gstAKCSConf;
extern CKafakProducer* g_kafka_producer;

CPushKafkaClient::CPushKafkaClient()
{

}

void CPushKafkaClient::pushMsg(int msg_type, const std::string& key, const std::string& payload)
{
    KafkaProduceMsgPtr message = std::make_shared<KAFKA_PRODUCE_MSG>(msg_type, key, payload);
    if (g_kafka_producer != nullptr && message != nullptr)
    {
        g_kafka_producer->ProduceMsg(message);
    }
}

std::string CPushKafkaClient::getEmailLanguage(const EmailType email_type, const std::string& email)
{
    std::string language = "en";

    if (email_type == EMAIL_CREATE_UID
            || email_type == EMAIL_CHANGE_PWD
            || email_type == EMAIL_RESET_PWD
            || email_type == EMAIL_ACCOUNT_ACTIVE
            || email_type == EMAIL_APP_EXPIRE
            || email_type == EMAIL_PHONE_WILL_EXPIRE
            || email_type == EMAIL_PHONE_EXPIRE
            || email_type == EMAIL_FREETRIAL_WILLBE_EXPIRE
            || email_type == EMAIL_RENEW_SERVER
            || email_type == EMAIL_ADD_NEW_SITE
            || email_type == EMAIL_DEV_APP_WILLBE_EXPIRE
       )
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUserAccountFromMaster(email, account))
        {

            language = account.language;
        }
    }
    else if (email_type == EMAIL_CREATE_PROPERTY_WORK
             || email_type == EMAIL_PM_ACCOUNT_WILLBE_EXPIRE
             || email_type == EMAIL_INSTALLER_APP_WILL_EXPIRE
             || email_type == EMAIL_INSTALLER_PHONE_WILL_EXPIRE
             || email_type == EMAIL_PM_FEATURE_WILL_EXPIRE
             || email_type == EMAIL_PM_APP_ACCOUNT_WILLBE_EXPIRE
             || email_type == EMAIL_PM_APP_ACCOUNT_EXPIRE
             || email_type == EMAIL_PM_APP_CREATE_UID
             || email_type == EMAIL_PM_APP_ACCOUNT_ACTIVE
             || email_type == EMAIL_PM_APP_RENEW_SERVER
             || email_type == EMAIL_PM_APP_RESET_PWD
             || email_type == EMAIL_PM_LINK_NEW_SITES
             || email_type == EMAIL_PM_WEB_CREATE_UID
             || email_type == EMAIL_PM_WEB_CHANGE_PWD
             || email_type == EMAIL_PM_ADD_NEW_SITE
            )
    {
        dbinterface::AccountInfo account;
        if (0 == dbinterface::Account::GetAccountFromMasterByEmail(email, account))
        {
            language = account.language;
        }
    }
    else if (email_type == EMAIL_SHARE_TMPKEY || email_type == EMAIL_CHECK_CODE)
    {
        //有不是注册在服务器账号的邮箱 所以要一级级传递
        //还为注册的验证码
    }
    return language;
}

std::string CPushKafkaClient::getEmailLanguageByAccount(const std::string& account)
{
    std::string language = "en";
    dbinterface::AccountInfo account_info;
    if (0 == dbinterface::Account::GetAccountInfoByAccount(account, account_info))
    {
        language = account_info.language;
    }

    return language;
}

std::string CPushKafkaClient::getOfficeEmailLanguage(const OfficeEmailType email_type, const std::string email)
{
    std::string language = "en";

    if (email_type == EMAIL_OFFICE_CREATE_UID
               || email_type == EMAIL_OFFICE_RESET_PWD
               || email_type == EMAIL_OFFICE_CHANGE_PWD
               || email_type == EMAIL_OFFICE_ADD_NEW_SITE)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetEmailAccount(email, account))
        {
            language = account.language;
        }
    }
    else if (email_type == EMAIL_OFFICE_PM_ACCOUNT_WILL_EXPIRE
                       || email_type == EMAIL_OFFICE_ACCOUNT_RENEW
                       || email_type == EMAIL_OFFICE_PM_ACCOUNT_EXPIRE
                       || email_type == EMAIL_OFFICE_PM_FEATURE_WILL_EXPIRE
                       || email_type == EMAIL_OFFICE_PM_FEATURE_EXPIRE
                       || email_type == EMAIL_OFFICE_INSTALLER_FEATURE_EXPIRE)
    {
        dbinterface::AccountInfo account;
        if (0 == dbinterface::Account::GetAccountByEmail(email, account))
        {
            language = account.language;
        }
    }
    return language;
}

void CPushKafkaClient::getEmailInfoByAccount(EmailInfo &email_info, const std::string &user)
{
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    
    if (0 == dbinterface::ResidentPersonalAccount::GetUserAccountFromMaster(user, personal_account))
    {
        Snprintf(email_info.username, sizeof(email_info.username),  personal_account.name);
        email_info.role = personal_account.role;

        dbinterface::AccountInfo account;
        if(ACCOUNT_ROLE_COMMUNITY_MAIN == email_info.role || ACCOUNT_ROLE_COMMUNITY_PM == email_info.role)
        {
            dbinterface::Account::GetAccountById(personal_account.parent_id, account);
        }
        else if (ACCOUNT_ROLE_COMMUNITY_ATTENDANT == email_info.role)
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(personal_account.parent_id, main_account))
            {
                dbinterface::Account::GetAccountById(main_account.parent_id, account);
            }
        }
        
        Snprintf(email_info.community, sizeof(email_info.community),  account.location);

        // 获取dis所属的oem
        DistributorInfoSt dis_info;
        if (0 == getDistributorInfo(personal_account, dis_info))
        {
            Snprintf(email_info.oem_name, sizeof(email_info.oem_name), dis_info.oem_name);
        }
        else
        {
            Snprintf(email_info.oem_name, sizeof(email_info.oem_name), gstAKCSConf.oem_name);
        }
    }
}

// 获取dis所属的oem
int CPushKafkaClient::getDistributorInfo(const ResidentPerAccount& personal_account, DistributorInfoSt& dis_info)
{
    dbinterface::AccountInfo dis_account;
    if (0 == dbinterface::Account::GetDisAccount(personal_account, dis_account))
    {
        if (0 == dbinterface::DistributorInfo::GetDistributorInfo(dis_account.account, dis_info))
        {
            AK_LOG_INFO << "uid = " << personal_account.account << ",dis account = " << dis_account.account << ",dis oem = " << dis_info.oem_name;
            return 0;
        }
    }
    return -1;
}


void CPushKafkaClient::buildMailPushMsg(const EmailType email_type, const ::google::protobuf::Message* msg)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    item["app_type"] = "email";

    item["OEM"] = gstAKCSConf.oem_name;
    item["ver"] = PUSH_SERVER_VER;
    
    if (email_type == EMAIL_CREATE_UID)
    {
        //静态转换即可,正确性由调用者保证
        const AK::Server::P2PAdaptCreateUidMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptCreateUidMailMsg*>(msg);
        itemData["uid"] = mail_msg->user();
        itemData["email"] = mail_msg->email();
        itemData["pwd"] =  mail_msg->pwd();
        itemData["qrcode_body"] = mail_msg->qrcode_body();
        itemData["qrcode_url"] = mail_msg->qrcode_url();
        itemData["gw_code"] = gstAKCSConf.gw_code;
        itemData["url"] =  mail_msg->srv_web_url();
        itemData["is_fake"] =  mail_msg->is_fake();

        EmailInfo email_info;
        getEmailInfoByAccount(email_info, mail_msg->user());

        // 数据库中标识dis所属的oem
        item["OEM"] = email_info.oem_name;
        
        itemData["role"] = email_info.role;
        itemData["community"] = email_info.community;
        if (email_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            itemData["email_type"] = "create_pm_uid";
            itemData["user"] = mail_msg->user(); //pm 邮件模板的user的是sip 账号
            itemData["language"] = getEmailLanguage(EMAIL_PM_APP_CREATE_UID, mail_msg->email());
        }
        else if (mail_msg->to_master() == SEND_TO_MASTER)
        {
            itemData["email_type"] = "family_create_uid";
            itemData["user"] = email_info.username;
            itemData["uid"] = mail_msg->user_uid();
            itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        }
        else
        {
            itemData["email_type"] = "create_uid";
            itemData["user"] = email_info.username;
            itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
            
            //PM不会有智能家居APP
            CommunityInfo communitinfo(mail_msg->user());
            if (communitinfo.EnableSmartHome())
            {
                itemData["enable_smarthome"] = "1";
            }
            else
            {
                itemData["enable_smarthome"] = "0";
            }
        }
        
        AK_LOG_INFO << "[PushMsg] create uid email. user = " << itemData["user"] << ", uid = " << itemData["uid"] 
                    << " , role = " << email_info.role << ", email = " << mail_msg->email() << ", to_master = " << mail_msg->to_master() << ", oem = " << item["OEM"];
    }
    else if (email_type == EMAIL_CHANGE_PWD)
    {
        const AK::Server::P2PAdaptPerChangePwdMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptPerChangePwdMailMsg*>(msg);

        itemData["email_type"] = "change_pwd";
        itemData["uid"] = mail_msg->user();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->user());
        itemData["pwd"] =  mail_msg->pwd();
        itemData["qrcode_body"] = mail_msg->qrcode_body();
        itemData["qrcode_url"] = mail_msg->qrcode_url();
        itemData["gw_code"] = gstAKCSConf.gw_code;

        EmailInfo email_info;
        memset(&email_info, 0, sizeof(email_info));
        getEmailInfoByAccount(email_info, mail_msg->user()); 

        if (email_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            itemData["email_type"] = "change_pm_pwd";
            itemData["user"] = mail_msg->user(); //pm 邮件模板的user的是sip 账号
            itemData["language"] = getEmailLanguage(EMAIL_PM_APP_CREATE_UID, mail_msg->email());
        }
        else if (mail_msg->to_master() == SEND_TO_MASTER)
        {
            itemData["email_type"] = "family_change_pwd";
            itemData["user"] = email_info.username;
            itemData["uid"] = mail_msg->user_uid();
        }
        else
        {
            itemData["user"] = email_info.username;
        }
        itemData["community"] = email_info.community;
        AK_LOG_INFO << "[PushMsg] change pwd send email. user:" << itemData["user"] << " uid:" << mail_msg->user() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_RESET_PWD)
    {
        const AK::Server::P2PAdaptResetPwdMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptResetPwdMailMsg*>(msg);
        if (mail_msg->to_master() == SEND_TO_MASTER)
        {
            itemData["email_type"] = "family_reset_pwd";
        }
        else
        {
            itemData["email_type"] = "reset_pwd";
        }
        itemData["web_ip"] = mail_msg->web_ip();
        itemData["email"] = mail_msg->email();
        itemData["token"] =  mail_msg->token();
        itemData["role_type"] =  mail_msg->role_type();
        
        EmailInfo email_info;
        memset(&email_info, 0, sizeof(email_info));
        getEmailInfoByAccount(email_info, mail_msg->user());
        
        itemData["role"] = email_info.role;
        itemData["user"] = email_info.username;
        itemData["community"] = email_info.community;
        
        if (email_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            itemData["language"] = getEmailLanguage(EMAIL_PM_APP_RESET_PWD, mail_msg->email());
        }
        else
        {
            itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        }
        AK_LOG_INFO << "language :" <<  itemData["language"];
        AK_LOG_INFO << "[PushMsg] reset pwd email. user:" << mail_msg->user() << " email:" << mail_msg->email() << " role:" << email_info.role << " role_type: " << mail_msg->role_type() << " community:" << email_info.community;
    }
    else if (email_type == EMAIL_CHECK_CODE)    //ak版本已经没有用户自注册功能了
    {
        itemData["email_type"] = "check_code";
        const AK::Server::P2PAdaptPerCheckCodeMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptPerCheckCodeMailMsg*>(msg);
        itemData["code"] = mail_msg->check_code();
        itemData["email"] = mail_msg->email();
        itemData["language"] = mail_msg->language();
        AK_LOG_INFO << "[PushMsg] check code email. code:" << mail_msg->check_code() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_DEV_APP_WILLBE_EXPIRE)
    {
        itemData["email_type"] = "will_expire";
        const AK::Server::P2PAdaptDevAppWillBeExpireMsg* mail_msg = static_cast<const AK::Server::P2PAdaptDevAppWillBeExpireMsg*>(msg);
        itemData["user"] = mail_msg->user_name();
        itemData["email"] = mail_msg->email();
        itemData["community"] = mail_msg->community();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        AK_LOG_INFO << "[PushMsg] app will be expire email. user:" << mail_msg->user_name() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_SHARE_TMPKEY)
    {
        itemData["email_type"] = "share_tmpkey";
        const AK::Server::P2PAdaptTmpKeyInfoMsg* mail_msg = static_cast<const AK::Server::P2PAdaptTmpKeyInfoMsg*>(msg);
        itemData["key"] = mail_msg->tmp_key();
        itemData["msg"] = mail_msg->msg();
        itemData["email"] = mail_msg->email();
        itemData["language"] = mail_msg->language();
        itemData["counts_every"] = mail_msg->count_every();
        itemData["from_time"] = mail_msg->start_time();
        itemData["until_time"] = mail_msg->stop_time();
        itemData["web_url"] = mail_msg->web_url();
        itemData["qrcode_body"] = mail_msg->qrcode_body();

        std::string community;
        dbinterface::AccountInfo account;
        dbinterface::Account::GetAccountById(mail_msg->mng_id(), account);
        if (account.grade == AccountGrade::COMMUNITY_MANEGER_GRADE)
        {
            community = account.location;
        }
        itemData["community"] = community;
        AK_LOG_INFO << "[PushMsg] share tmpkey. email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_ACCOUNT_ACTIVE)
    {
        itemData["email_type"] = "account_active";
        const AK::Server::P2PAdaptAccountActInfoMsg* mail_msg = static_cast<const AK::Server::P2PAdaptAccountActInfoMsg*>(msg);
        itemData["user"] = mail_msg->user_name();
        itemData["email"] = mail_msg->email();
        itemData["enable"] =  mail_msg->active();
        itemData["time"] = mail_msg->time();
        itemData["url"] =  mail_msg->web_url();
        itemData["subscription"] = mail_msg->subscription();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        
        EmailInfo email_info;
        memset(&email_info, 0, sizeof(email_info));
        getEmailInfoByAccount(email_info, mail_msg->email());

        itemData["community"] = email_info.community;
        AK_LOG_INFO << "[PushMsg] Account Active enable:" << mail_msg->active() << " subscription: " << mail_msg->subscription() << " email:" << mail_msg->email() << " username:" << mail_msg->user_name();
    }
    else if(email_type == EMAIL_PM_APP_ACCOUNT_ACTIVE)
    {
        itemData["email_type"] = "pm_app_active";
        const AK::Server::P2PAdaptPmAccountActInfoMsg* mail_msg = static_cast<const AK::Server::P2PAdaptPmAccountActInfoMsg*>(msg);
        itemData["user"] = mail_msg->user_name();
        itemData["email"] = mail_msg->email();
        itemData["enable"] =  mail_msg->active();
        itemData["time"] = mail_msg->time();
        itemData["url"] =  mail_msg->web_url();
        itemData["subscription"] = mail_msg->subscription();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());

        EmailInfo email_info;
        memset(&email_info, 0, sizeof(email_info));
        getEmailInfoByAccount(email_info, mail_msg->account());

        itemData["community"] = email_info.community;
        AK_LOG_INFO << "[PushMsg] Pm Account Active enable:" << mail_msg->active() << " subscription: " << mail_msg->subscription() << " email:" << mail_msg->email() << " username:" << mail_msg->user_name();
    }
    else if (email_type == EMAIL_CREATE_PROPERTY_WORK)
    {
        itemData["email_type"] = "create_property";
        const AK::Server::P2PAdaptCreatePropertyWorkMsg* mail_msg = static_cast<const AK::Server::P2PAdaptCreatePropertyWorkMsg*>(msg);
        itemData["user"] = mail_msg->user_name();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        itemData["password"] =  mail_msg->pwd();
        itemData["url"] =  mail_msg->srv_web_url();
        AK_LOG_INFO << "[PushMsg] Create Property work username:" << mail_msg->user_name() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_RENEW_SERVER)
    {
        itemData["email_type"] = "account_renew";
        const AK::Server::P2PAdaptRenewSrvMsg::P2PAdaptRenewSrvInnerMsg* mail_msg = static_cast<const AK::Server::P2PAdaptRenewSrvMsg::P2PAdaptRenewSrvInnerMsg*>(msg);
        itemData["user"] = mail_msg->user_name();//昵称
        itemData["email"] = mail_msg->email();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        itemData["time"] = mail_msg->time();
        itemData["type"] = mail_msg->type();
        
        EmailInfo email_info;
        memset(&email_info, 0, sizeof(email_info));
        getEmailInfoByAccount(email_info, mail_msg->email());
        
        itemData["community"] = email_info.community;
        AK_LOG_INFO << "[PushMsg] renew server, username:" << mail_msg->user_name() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_PM_APP_RENEW_SERVER)
    {
        itemData["email_type"] = "pm_account_renew";
        const AK::Server::P2PAdaptPmRenewSrvMsg* mail_msg = static_cast<const AK::Server::P2PAdaptPmRenewSrvMsg*>(msg);
        itemData["user"] = mail_msg->user_name();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        itemData["time"] = mail_msg->time();
        itemData["type"] = mail_msg->type();
        
        EmailInfo email_info;
        memset(&email_info, 0, sizeof(email_info));
        getEmailInfoByAccount(email_info, mail_msg->uid());
        
        itemData["community"] = email_info.community;
        AK_LOG_INFO << "[PushMsg] renew server, username:" << mail_msg->user_name() << " email:" << mail_msg->email() << " community:" << email_info.community << " time:" << mail_msg->time();
    }
    else if (email_type == EMAIL_DELETE_APP_ACCOUNT)
    {
        const AK::Server::P2PAdaptDelAppAccountMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptDelAppAccountMailMsg*>(msg);
        if (mail_msg->to_master() == SEND_TO_MASTER)
        {
            itemData["email_type"] = "family_delete_app_account";
        }
        else
        {
            itemData["email_type"] = "delete_app_account";
        }
        itemData["name"] = mail_msg->name();
        itemData["email"] = mail_msg->email();
        itemData["language"] = mail_msg->language();
        itemData["code"] = mail_msg->code();
        AK_LOG_INFO << "[PushMsg] Delete App Account email. email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_ADD_NEW_SITE )
    {
        itemData["email_type"] = "user_add_new_site";
        const AK::Server::P2PSendUserAddNewSite* mail_msg = static_cast<const AK::Server::P2PSendUserAddNewSite*>(msg);
        itemData["send_type"] = mail_msg->send_type();
        itemData["name"] = mail_msg->name();
        itemData["project_name"] = mail_msg->project_name();
        itemData["email"] = mail_msg->email();
        itemData["apt_num"] = mail_msg->apt_num();
        int role = mail_msg->role();
        EmailType email_type_new = EMAIL_ADD_NEW_SITE;
        if(role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            email_type_new = EMAIL_PM_ADD_NEW_SITE;
        }
        itemData["language"] = getEmailLanguage(email_type_new, mail_msg->email());
        AK_LOG_INFO << "[PushMsg] notify user_add_new_site, project_name:" << mail_msg->project_name() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_PM_LINK_NEW_SITES)
    {
        itemData["email_type"] = "pm_link_new_sites";
        const AK::Server::P2PSendPmWebLinkNewSites* mail_msg = static_cast<const AK::Server::P2PSendPmWebLinkNewSites*>(msg);
        itemData["comm_name_list"] = mail_msg->comm_name_list();
        itemData["office_name_list"] = mail_msg->office_name_list();
        itemData["name"] = mail_msg->name();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getEmailLanguage(email_type, mail_msg->email());
        AK_LOG_INFO << "[PushMsg] notify pm_link_new_sites, comm_name_list:" << mail_msg->comm_name_list() << " office_name_list:" << mail_msg->office_name_list() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_PM_WEB_CREATE_UID)
    {
        itemData["email_type"] = "pm_web_create_uid";
        const AK::Server::P2PAdaptCreateUidMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptCreateUidMailMsg*>(msg);
        itemData["uid"] = mail_msg->user();
        itemData["email"] = mail_msg->email();
        itemData["pwd"] =  mail_msg->pwd();
        itemData["qrcode_body"] = mail_msg->qrcode_body();
        itemData["qrcode_url"] = mail_msg->qrcode_url();
        itemData["gw_code"] = gstAKCSConf.gw_code;
        itemData["url"] =  mail_msg->srv_web_url();
        itemData["user"] = mail_msg->email(); //邮件模板的user的是邮箱
        itemData["language"] = getEmailLanguage(EMAIL_PM_WEB_CREATE_UID, mail_msg->email());
        AK_LOG_INFO << "[PushMsg] notify pm_web_create_uid, email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_PM_WEB_CHANGE_PWD)
    {
        itemData["email_type"] = "pm_web_change_pwd";
        const AK::Server::P2PAdaptPerChangePwdMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptPerChangePwdMailMsg*>(msg);
        itemData["uid"] = mail_msg->user();
        itemData["email"] = mail_msg->email();
        itemData["pwd"] =  mail_msg->pwd();
        itemData["qrcode_body"] = mail_msg->qrcode_body();
        itemData["qrcode_url"] = mail_msg->qrcode_url();
        itemData["gw_code"] = gstAKCSConf.gw_code;
        itemData["user"] = mail_msg->email(); //邮件模板的user的是邮箱
        itemData["language"] = getEmailLanguage(EMAIL_PM_WEB_CHANGE_PWD, mail_msg->email());
        AK_LOG_INFO << "[PushMsg] notify pm_web_change_pwd, email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_COMMON_SEND_CODE)
    {
        const AK::Server::P2PSendCodeToEmail* mail_msg = static_cast<const AK::Server::P2PSendCodeToEmail*>(msg);
        itemData["email_type"] = mail_msg->type();
        itemData["code"] = mail_msg->code();
        itemData["name"] = mail_msg->name();
        itemData["email"] = mail_msg->email();
        itemData["language"] = mail_msg->language();
        AK_LOG_INFO << "[PushMsg] notify send_email_code, code:" << mail_msg->code() << " email:" << mail_msg->email();
    }

    item["data"] = wData.write(itemData);
    std::string msg_json = w.write(item);
    KafkaProduceMsgPtr email_msg = std::make_shared<KAFKA_PRODUCE_MSG>(KAFKA_MSG_EMAIL, itemData["email"].asCString(), msg_json);
    g_kafka_producer->ProduceMsg(email_msg);
}

void CPushKafkaClient::buildOfficeMailPushMsg(const OfficeEmailType email_type, const ::google::protobuf::Message* msg)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    item["app_type"] = "email";

    item["OEM"] = gstAKCSConf.oem_name;
    item["ver"] = PUSH_SERVER_VER;

    if (email_type == EMAIL_OFFICE_CREATE_UID)
    {
        itemData["email_type"] = "office_create_uid";
        //静态转换即可,正确性由调用者保证
        const AK::Server::P2PAdaptCreateUidMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptCreateUidMailMsg*>(msg);
        itemData["uid"] = mail_msg->user();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getOfficeEmailLanguage(email_type, mail_msg->email());
        itemData["pwd"] =  mail_msg->pwd();
        itemData["qrcode_body"] = mail_msg->qrcode_body();
        itemData["qrcode_url"] = mail_msg->qrcode_url();
        itemData["gw_code"] = gstAKCSConf.gw_code;
        itemData["url"] =  mail_msg->srv_web_url();
        itemData["is_fake"] =  mail_msg->is_fake();

        std::string username;
        std::string community;
        uint32_t office_id = 0;
        dbinterface::OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(mail_msg->user(), username, office_id);
        community =  dbinterface::Account::GetOfficeNameById(office_id);
        
        itemData["user"] = username;
        itemData["community"] = community;
        itemData["project_type"] = project::OFFICE;
        AK_LOG_INFO << "[OfficePushMsg] create uid email. user:" << mail_msg->user() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_OFFICE_ACCOUNT_RENEW)
    {
        itemData["email_type"] = "office_account_renew";
        const AK::ServerOffice::P2PAdaptPMOfficeRenewMsg* mail_msg = static_cast<const AK::ServerOffice::P2PAdaptPMOfficeRenewMsg*>(msg);
        itemData["account_num"] = mail_msg->account_num();
        itemData["community"] = mail_msg->community();
        itemData["email"] = mail_msg->email();
        itemData["user"] = mail_msg->pm_name();
        itemData["list"] = mail_msg->list();
        itemData["language"] = getOfficeEmailLanguage(email_type, mail_msg->email());
        itemData["project_type"] = project::OFFICE;
        AK_LOG_INFO << "[OfficePushMsg] account renew, pm username:" << mail_msg->pm_name() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_OFFICE_RESET_PWD)
    {
        itemData["email_type"] = "office_reset_pwd";
        const AK::Server::P2PAdaptResetPwdMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptResetPwdMailMsg*>(msg);
        itemData["web_ip"] = mail_msg->web_ip();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getOfficeEmailLanguage(email_type, mail_msg->email());
        itemData["token"] =  mail_msg->token();
        itemData["role_type"] =  mail_msg->role_type();
        std::string username;
        std::string community;
        uint32_t office_id = 0;
        dbinterface::OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(mail_msg->user(), username, office_id);
        community =  dbinterface::Account::GetOfficeNameById(office_id);
        
        itemData["user"] = username;
        itemData["community"] = community;
        itemData["project_type"] = project::OFFICE;
        AK_LOG_INFO << "[OfficePushMsg] reset pwd email. user:" << mail_msg->user() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_OFFICE_CHANGE_PWD)
    {
        itemData["email_type"] = "office_change_pwd";
        const AK::Server::P2PAdaptPerChangePwdMailMsg* mail_msg = static_cast<const AK::Server::P2PAdaptPerChangePwdMailMsg*>(msg);
        itemData["uid"] = mail_msg->user();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getOfficeEmailLanguage(email_type, mail_msg->email());
        itemData["pwd"] =  mail_msg->pwd();
        itemData["qrcode_body"] = mail_msg->qrcode_body();
        itemData["qrcode_url"] = mail_msg->qrcode_url();
        itemData["gw_code"] = gstAKCSConf.gw_code;

        std::string username;
        std::string community;
        uint32_t office_id = 0;
        dbinterface::OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(mail_msg->user(), username, office_id);
        community =  dbinterface::Account::GetOfficeNameById(office_id);

        itemData["user"] = username;
        itemData["community"] = community;
        itemData["project_type"] = project::OFFICE;
        AK_LOG_INFO << "[OfficePushMsg] change pwd send email. user:" << username << " uid:" << mail_msg->user() << " email:" << mail_msg->email();
    }
    else if (email_type == EMAIL_OFFICE_ADD_NEW_SITE)
    {
        itemData["email_type"] = "office_add_new_site";
        const AK::Server::P2PSendUserAddNewSite* mail_msg = static_cast<const AK::Server::P2PSendUserAddNewSite*>(msg);
        itemData["name"] = mail_msg->name();
        itemData["project_name"] = mail_msg->project_name();
        itemData["email"] = mail_msg->email();
        itemData["language"] = getOfficeEmailLanguage(email_type, mail_msg->email());
        itemData["project_type"] = project::OFFICE;
        AK_LOG_INFO << "[PushMsg] notify office_add_new_site, project_name:" << mail_msg->project_name() << " email:" << mail_msg->email();
    }

    item["data"] = wData.write(itemData);
    std::string msg_json = w.write(item);
    KafkaProduceMsgPtr email_msg = std::make_shared<KAFKA_PRODUCE_MSG>(KAFKA_MSG_EMAIL, itemData["email"].asCString(), msg_json);
    g_kafka_producer->ProduceMsg(email_msg);    
}

void CPushKafkaClient::buildKafkaPushMsg(const KafkaMsgType msg_type, const ::google::protobuf::Message* msg)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    switch(msg_type)
    {
        case KAFKA_MSG_PM_EXPORT_LOG:
        {
            const AK::Adapt::PmExportLog* exportlog_msg = static_cast<const AK::Adapt::PmExportLog*>(msg);
            
            item["trace_id"] = exportlog_msg->trace_id();
            item["communit_id"] = exportlog_msg->communit_id();
            item["datas"] = exportlog_msg->datas();

            int grade;
            grade =  dbinterface::Account::GetAccountGradeById(exportlog_msg->communit_id());
            if (grade == AccountGrade::OFFICE_MANEGER_GRADE)
            {
                item["project_type"] = project::OFFICE;
            }
            else
            {
                item["project_type"] = project::RESIDENCE;
            }

            Json::Reader reader;
            Json::Value root;
            if (!reader.parse(exportlog_msg->datas(), root))
            {
                AK_LOG_WARN << "parse json error.data=" << exportlog_msg->datas();
                return;
            }
            std::string exporttype = root["export_type"].asString();

            AK_LOG_INFO << "pm export log, communityid:" << exportlog_msg->communit_id() << " trace_id:" << exportlog_msg->trace_id();
            
            std::string msg_json = w.write(item);
            if (exporttype == "1") // 0=all 1=only_excel
            {
                KafkaProduceMsgPtr exportlog_msg2 = std::make_shared<KAFKA_PRODUCE_MSG>(KAFKA_MSG_PM_EXPORT_LOG_EXCEL, itemData["trace_id"].asString(), msg_json);
                g_kafka_producer->ProduceMsg(exportlog_msg2);

            }
            else
            {
                KafkaProduceMsgPtr exportlog_msg2 = std::make_shared<KAFKA_PRODUCE_MSG>(KAFKA_MSG_PM_EXPORT_LOG, itemData["trace_id"].asString(), msg_json);
                g_kafka_producer->ProduceMsg(exportlog_msg2);  
            }               
            break;
        }
        default:
        {
            AK_LOG_INFO << "[Kafka] unkown msg type=" << msg_type;
            return;
        }
    }
}

void CPushKafkaClient::buildLinkerCommonMsg(int msg_type, int project_type, const std::string &msg_json, const std::string &key)
{
    uint64_t traceid = ThreadLocalSingleton::GetInstance().GetTraceID();
    std::time_t t = std::time(0);
    
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;
    item["message_type"] = msg_type;
    item["project_type"] = project_type;
    item["trace_id"] = (long long)traceid;
    item["timestamp"] = (long long)t;
    item["key"] = key;

    Json::Reader reader;
    Json::Value msg;
    if (!reader.parse(msg_json, msg))
    {
        AK_LOG_WARN <<  "CPushLinKer::PushMsg Parse json error.data=" << msg_json << " error msg=" << reader.getFormatedErrorMessages();
        return;
    }
    item["datas"] = msg;
    std::string data_json = wData.write(item);
    
    AK_LOG_INFO << "[PushLinker] message type:" << msg_type << " trace_id:" << traceid << " datas:" << data_json;
    KafkaProduceMsgPtr linker_msg = std::make_shared<KAFKA_PRODUCE_MSG>(KAFKA_MSG_NOTIFY_LINKER, key, data_json);
    g_kafka_producer->ProduceMsg(linker_msg);
    return;
}

void CPushKafkaClient::buildNotifyWebAccessDoorItemData(int message_id, const Json::Value& input_json, Json::Value& item_data, Json::Value& item, std::string& key)
{
    switch (message_id)
    {
        case (int)NotifyWebAccessDoorMessageID::MUSTER_READER:
        {
            AK_LOG_INFO << "CPushKafkaClient::buildNotifyWebAccessDoorItemData MUSTER_READER";
            item_data["user_uuid"] = input_json["user_uuid"];
            item_data["account_type"] = input_json["account_type"];
            item_data["office_uuid"] = input_json["office_uuid"];
            key = input_json["user_uuid"].asString();
            break;
        }
        case (int)NotifyWebAccessDoorMessageID::ENTRY_OR_EXIT_RECORD:
        {
            AK_LOG_INFO << "CPushKafkaClient::buildNotifyWebAccessDoorItemData ENTRY_OR_EXIT_RECORD";
            item_data["user_uuid"] = input_json["user_uuid"];
            item_data["account_type"] = input_json["account_type"];
            item_data["mode"] = input_json["mode"];
            item_data["door_uuid"] = input_json["door_uuid"];
            item["timestamp"] = input_json["timestamp"];
            key = input_json["user_uuid"].asString();
            break;
        }
        default:
        {
            AK_LOG_WARN << "CPushKafkaClient::buildNotifyWebAccessDoorItemData unkown message id=" << message_id;
            return;
        }
    }
}

void CPushKafkaClient::buildNotifyWebCommonMsg(int msg_type, const std::string& msg_json)
{
    Json::Value item;
    Json::Value item_data;
    Json::FastWriter write_data;
    Json::FastWriter write_data2;
    std::time_t t = std::time(0);
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    //生成traceid:
    char traceid_tmp[32] = {0};
    ::snprintf(traceid_tmp, 32, "%ld", traceid);
    
    item["trace_id"] = traceid_tmp;
    item["timestamp"] = (Json::Value::UInt)t;
    Json::Reader reader;
    Json::Value msg;
    if (!reader.parse(msg_json, msg))
    {
        AK_LOG_WARN <<  "CPushKafkaClient::buildNotifyWebCommonMsg Parse json error.data=" << msg_json << " error msg=" << reader.getFormatedErrorMessages();
        return;
    }

    std::string key;
    int kafka_msg_type;
    switch (msg_type)
    {
        case PushWebMsgType::PUSH_WEB_MSG_EMERGENCY:
        {
            item["message_id"] = NotifyWebMsgType::DevEmergency;
            item_data["alarm_uuid"] = msg["alarm_uuid"];
            item_data["project_uuid"] = msg["project_uuid"];
            key = item_data["alarm_uuid"].asString();
            kafka_msg_type = KAFKA_MSG_NOTIFY_WEB;
            break;
        }
        case PushWebMsgType::PUSH_WEB_MSG_ATTENDANCE:
        {
            item["message_id"] = NotifyWebMsgType::DevAttendance;
            item_data["device_uuid"] = msg["device_uuid"];
            item_data["clock_time"] = msg["clock_time"];
            item_data["relay"] = msg["relay"];
            item_data["security_relay"] = msg["security_relay"];
            item_data["account"] = msg["account"];
            key = item_data["device_uuid"].asString();
            kafka_msg_type = KAFKA_MSG_NOTIFY_WEB_ATTENDANCE; 
            break;
        }
        case PushWebMsgType::PUSH_WEB_MSG_ACCESS_DOOR:
        {
            AK_LOG_INFO << "CPushKafkaClient::buildNotifyWebCommonMsg PUSH_WEB_MSG_ACCESS_DOOR";
            int message_id = msg["message_id"].asInt();
            item["message_id"] = message_id;
            buildNotifyWebAccessDoorItemData(message_id, msg, item_data, item, key);
            kafka_msg_type = KAFKA_MSG_NOTIFY_WEB_ACCESS_DOOR;
            break;
        }
        default:
        {
            return;
        }
    }
    item["data"] = item_data;
    
    std::string output_msg_json = write_data2.write(item);
    KafkaProduceMsgPtr notify_web_msg = std::make_shared<KAFKA_PRODUCE_MSG>(kafka_msg_type, key, output_msg_json );
    g_kafka_producer->ProduceMsg(notify_web_msg);
    return;         
}

