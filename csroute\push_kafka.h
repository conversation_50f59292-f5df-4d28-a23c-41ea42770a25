#ifndef __PUSH_KAFKA_CLIENT_H__
#define __PUSH_KAFKA_CLIENT_H__

#include <evpp/tcp_client.h>
#include <evpp/tcp_conn.h>

#include "AkLogging.h"
#include "dbinterface/DistributorInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include "json/json.h"

namespace google
{
namespace protobuf
{
class Message;
}
}

enum EmailType
{
    EMAIL_CREATE_UID = 0,
    EMAIL_CHANGE_PWD = 1,
    EMAIL_RESET_PWD = 2,
    EMAIL_CHECK_CODE = 3,
    EMAIL_DEV_APP_WILLBE_EXPIRE = 4,
    EMAIL_FREETRIAL_WILLBE_EXPIRE = 5,
    EMAIL_SHARE_TMPKEY = 6,
    EMAIL_ACCOUNT_ACTIVE = 7,
    EMAIL_CREATE_PROPERTY_WORK = 8,
    EMAIL_RENEW_SERVER = 9,
    EMAIL_APP_EXPIRE = 10,
    EMAIL_PM_ACCOUNT_WILLBE_EXPIRE = 11,
    EMAIL_PHONE_EXPIRE = 12,
    EMAIL_PHONE_WILL_EXPIRE = 13,
    EMAIL_INSTALLER_APP_WILL_EXPIRE = 14,
    EMAIL_INSTALLER_PHONE_WILL_EXPIRE = 15,
    EMAIL_PM_DEV_OFFLINE_NOTIFY = 16,
    EMAIL_PM_FEATURE_WILL_EXPIRE = 17,
    EMAIL_INSTALLER_FEATURE_WILL_EXPIRE = 18,
    EMAIL_DELETE_APP_ACCOUNT = 19,
    EMAIL_PM_APP_CREATE_UID = 20,
    EMAIL_PM_APP_ACCOUNT_WILLBE_EXPIRE = 21,
    EMAIL_PM_APP_ACCOUNT_EXPIRE = 22,
    EMAIL_PM_APP_ACCOUNT_ACTIVE = 23,
    EMAIL_PM_APP_RENEW_SERVER = 24,
    EMAIL_PM_APP_RESET_PWD = 25,
    EMAIL_ADD_NEW_SITE = 26,
    EMAIL_PM_LINK_NEW_SITES = 27,
    EMAIL_PM_WEB_CREATE_UID = 28,
    EMAIL_PM_WEB_CHANGE_PWD = 29,
    EMAIL_COMMON_SEND_CODE = 30,
    EMAIL_PM_ADD_NEW_SITE = 31,
};

enum OfficeEmailType
{
    EMAIL_OFFICE_CREATE_UID = 0,
    EMAIL_OFFICE_ACCOUNT_RENEW,
    EMAIL_OFFICE_PM_ACCOUNT_WILL_EXPIRE,
    EMAIL_OFFICE_RESET_PWD,
    EMAIL_OFFICE_CHANGE_PWD,
    EMAIL_OFFICE_PM_ACCOUNT_EXPIRE,
    EMAIL_OFFICE_PM_DEV_OFFLINE_NOTIFY,
    EMAIL_OFFICE_PM_FEATURE_WILL_EXPIRE,
    EMAIL_OFFICE_INSTALLER_FEATURE_WILL_EXPIRE,
    EMAIL_OFFICE_PM_FEATURE_EXPIRE,
    EMAIL_OFFICE_INSTALLER_FEATURE_EXPIRE,
    EMAIL_OFFICE_ADD_NEW_SITE,
};

enum KafkaMsgType
{
    KAFKA_MSG_PM_EXPORT_LOG = 0,
    KAFKA_MSG_PM_EXPORT_LOG_EXCEL = 1,
    KAFKA_MSG_EMAIL = 2,
    KAFKA_MSG_NOTIFY_WEB = 3,
    KAFKA_MSG_NOTIFY_LINKER = 4,
    KAFKA_MSG_NOTIFY_WEB_MESSAGE = 5,
    KAFKA_MSG_NOTIFY_WEB_ATTENDANCE = 6,
    KAFKA_MSG_NOTIFY_WEB_ACCESS_DOOR = 7,
};

enum NotifyWebMsgType
{
    DevEmergency = 0,
    DevAttendance = 1,
};

enum class ExportLogType
{
    EXPORT_ALL = 0,
    EXPORT_CSV = 1,
};

typedef struct Email_T
{
    int role;
    char username[128];// 数据库字段长度
    char community[128];
    char oem_name[64];

    Email_T() {
        memset(this, 0, sizeof(*this));
    }
}EmailInfo;


#define PUSH_SERVER_VER "1"

class CPushKafkaClient
{
public:
    CPushKafkaClient();

    void pushMsg(int msg_type, const std::string& key, const std::string& payload);

    void buildMailPushMsg(const EmailType email_type, const ::google::protobuf::Message* msg);

    void buildKafkaPushMsg(const KafkaMsgType email_type, const ::google::protobuf::Message* msg);

    void buildOfficeMailPushMsg(const OfficeEmailType email_type, const ::google::protobuf::Message* msg);

    void buildNotifyWebCommonMsg(int msg_type, const std::string& msg_json);
    
    void buildLinkerCommonMsg(int msg_type,  int project_type, const std::string &msg_json, const std::string &key);

    void buildNotifyWebAccessDoorItemData(int message_id, const Json::Value& input_json, Json::Value& item_data, Json::Value& item, std::string& key);

private:
    std::string getEmailLanguage(const EmailType email_type, const std::string& email);
    std::string getEmailLanguageByAccount(const std::string& account);
    std::string getOfficeEmailLanguage(const OfficeEmailType email_type, const std::string email);
    void getEmailInfoByAccount(EmailInfo &email_info, const std::string &user);
    int getDistributorInfo(const ResidentPerAccount& personal_account, DistributorInfoSt& dis_info);
};

#endif // __CSROUTE_PUSH_CLIENT_H__
