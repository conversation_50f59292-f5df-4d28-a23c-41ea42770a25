#db conf
db_username=dbuser01

akcs_db_ip=127.0.0.1
akcs_db_database=AKCS
akcs_db_port=3306

#log db conf
log_db_ip=***********
log_db_port=3306
log_db_database=LOG

nsq_route_topic=ak_route
nsq_route_channel=route_msg

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;


#视频存储服务信息,ip:port
video_server_net=**************:9001

#配置OEM，用于推送服务的区分,空值就是akuvox
oem_name=Akuvox
#cspush的配置信息
cspush_net=***********:8000
#配置和cspush推送的aes加密密钥
push_aeskey=
#网关编号
gateway_code=50

#email kafka
kafka_push_email_topic=push_email
kafka_broker_ip=
kafka_pm_export_log_topic=exportlog
kafka_pm_export_log_excel_topic=exportlog_excel
kafka_notify_web_topic=notify_web
kafka_notify_linker_topic=ak_cslinker
kafka_csroute_topic=ak_csroute
kafka_csroute_group=ak_csroute_group
kafka_notify_web_message_topic=notify_web_message
kafka_notify_web_attendance_topic=notify_web_attendance
kafka_notify_web_access_door_topic=notify_web_access_door

#nsq_linker
nsq_linker_topic=ak_linker
nsq_linker_ip=

log_encrypt=0
log_trace=1

#route handle thread, 需要注意数据库线程是否能匹配上
route_msg_thread_num=5