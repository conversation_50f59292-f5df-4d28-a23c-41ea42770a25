csrouteCacheInstances=mac_vrtspsid,seq_pbxsid,userdetail
# mac_vrtspsid: mac跟vrtspd_logic_id的关系
mac_vrtspsid_host=127.0.0.1
mac_vrtspsid_port=8504
mac_vrtspsid_db=9
mac_vrtspsid_maxconncnt=4

# seq_pbxsid: pbx查询app的状态
seq_pbxsid_host=***********
seq_pbxsid_port=8504
seq_pbxsid_db=10
seq_pbxsid_maxconncnt=1

# userdetail: 设备请求用户详细数据去重
userdetail_host=127.0.0.1
userdetail_port=8504
userdetail_db=14
userdetail_maxconncnt=1

#如果sentinels有值,代表启动主从，那么_host的配置就不生效，如果没有就是单机
sentinels=



