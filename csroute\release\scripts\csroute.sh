#!/bin/bash
ACMD="$1"
CSROUTE_BIN='/usr/local/akcs/csroute/bin/csroute'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csroute()
{
    nohup $CSROUTE_BIN >/dev/null 2>&1 &
    echo "Start csroute successful"
    if [ -z "`ps -fe|grep "csrouterun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csroute/scripts/csrouterun.sh >/dev/null 2>&1 &
    fi
}
stop_csroute()
{
    echo "Begin to stop csrouterun.sh"
    csrouterunid=`ps aux | grep -w csrouterun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csrouterunid}" ];then
	    echo "csrouterun.sh is running at ${csrouterunid}, will kill it first."
	    kill -9 ${csrouterunid}
    fi
    echo "Begin to stop csroute"
    kill -9 `pidof csroute`
    sleep 2
    echo "Stop csroute successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 8500 | grep csroute | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csroute
    else
        echo "csroute is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 8500 | grep csroute | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csroute is already stopping"
    else
        stop_csroute
    fi
    ;;
  restart)
    stop_csroute
    sleep 1
    start_csroute
    ;;
  status)
    cnt=`ss -alnp | grep 8500 | grep csroute | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csroute is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csroute is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

