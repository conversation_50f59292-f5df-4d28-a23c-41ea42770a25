<?php
ini_set('date.timezone','Asia/Shanghai');

// 消费组信息 /usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server *************:8520 --group ak_csroute_group --describe
// 消息指定消息 /usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server *************:8520  --topic ak_csroute_group --offset 10  --partition 0 --max-messages 1
// 主题信息  /usr/local/kafka/bin/kafka-topics.sh --zookeeper *************:8521 --describe --topic ak_csroute
// 修改分区数量 /usr/local/kafka/bin/kafka-topics.sh --alter  --zookeeper *************:8521  --topic ak_csroute  --partitions 5

function cmd_usage($cmd)
{
    echo("Usage: \n");
    echo("  Kafka Commands:\n");
    echo("    ".$cmd. " get <partition> <offset>   # 获取指定分区和偏移量的消息\n");  
    echo("    ".$cmd. " list                        # 列出消费者组的描述信息\n");
    
    echo("\n  Metrics Command:\n");
    echo("    ".$cmd. " metrics                    # 获取csroute的指标数据\n");  
    exit(0);
}

function parse_etc_ip()
{
	$fileContent = file('/etc/ip');
	
	$configs = array();
	foreach ($fileContent as $line) {
		$line = trim($line); // 去掉开头和结尾的空格和换行符
        if (!empty($line)) { // 跳过空行
            parse_str($line, $config);
            $configs = array_merge($configs, $config);
        }
	}
	return $configs;
}

function parse_conf()
{
	$fileContent = file('/usr/local/akcs/csroute/conf/csroute.conf');
	
	$configs = array();
	foreach ($fileContent as $line) {
		$line = trim($line); // 去掉开头和结尾的空格和换行符
        if (!empty($line)) { // 跳过空行
            parse_str($line, $config);
            $configs = array_merge($configs, $config);
        }
	}
	return $configs;
}

if ($argc < 2){  
	cmd_usage($argv[0]);
}

if ($argv[1] == "get") 
{
	$partition = $argv[2];
	$offset = $argv[3];

	$consumer_config = parse_conf();
	$kafka_ip = $consumer_config['kafka_broker_ip'];
	echo "Excute the following command on running kafka server machine\n";
	echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $kafka_ip --topic ak_csroute --offset $offset  --partition $partition --max-messages 1\n";
}
elseif ($argv[1] == "list")
{
	$consumer_config = parse_conf();
	$kafka_ip = $consumer_config['kafka_broker_ip'];
	echo "Excute the following command on running kafka server machine\n";
	echo "/usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server $kafka_ip --group ak_csroute_group --describe\n";	
}
elseif ($argv[1] == "metrics") 
{
	$etc_ip = parse_etc_ip();
	$SERVER_INNER_IP = $etc_ip['SERVER_INNER_IP'];
	echo "curl $SERVER_INNER_IP:9994/metrics\n"; 
	$output = shell_exec("curl $SERVER_INNER_IP:9994/metrics");
	echo $output;

}
else
{
	cmd_usage($argv[0]);
}

?>