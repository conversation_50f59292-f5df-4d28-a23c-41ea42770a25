#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evpp/event_loop.h>
#include "route_server.h"
#include "util.h"
#include "push_client.h"
#include "push_kafka.h"
#include "session_rpc_client.h"
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include "PushClientMng.h"


extern AKCS_ROUTE_CONF gstAKCSConf;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern SmRpcClient* g_sm_client_ptr;
extern evnsq::Consumer* g_nsq_consumer_client_ptr;
extern const char *g_ak_srv_session;
extern const char *g_ak_srv_nsqlookupd;
extern const char *g_ak_srv_cspush;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

static void SessionSrvConnInit(const std::set<std::string>& cssession_addrs)
{
    std::string cssesson_str = "session list:";
    std::vector<AddressData> addresses;
    AddressData addr_tmp;
    for (const auto& cssesson : cssession_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = cssesson.find(":");
        if (std::string::npos != pos)
        {
            ip = cssesson.substr(0, pos);
            port = cssesson.substr(pos + 1);
        }
        addresses.emplace_back(AddressData{ATOI(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
        cssesson_str += cssesson;
        cssesson_str += " ";        
    }
    g_sm_client_ptr->SetNextResolution(addresses);
    AK_LOG_INFO << cssesson_str;
}

static void NsqLookupdSrvInit(const std::set<std::string>& nsqLookupd_addrs)
{
    
    std::vector<std::string> urls;
    for (const auto& nsqLookupd : nsqLookupd_addrs) //ip:port的形式
    {
        std::stringstream lookupd_http_url;
        lookupd_http_url << "http://" << nsqLookupd << "/lookup?topic=" << gstAKCSConf.nsq_topic;
        urls.push_back(lookupd_http_url.str());
        
    }
    g_nsq_consumer_client_ptr->ConnectToLookupds(urls);
}

void UpdateSessionSrvList()
{
    std::set<std::string> cssession_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) == 0)
    {
        SessionSrvConnInit(cssession_addrs);
    }

}

void UpdateLookupdSrvList()
{
    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) == 0)
    {
        g_nsq_consumer_client_ptr->reset_nsqlookupd();
        NsqLookupdSrvInit(nsqlookupd_addrs);
    }
}

void UpdatePushSrvList()
{
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) == 0)
    {
        AK_LOG_INFO << "UpdatePushSrv begin";
        //更新route的连接列表
        CPushClientMng::Instance()->UpdatePushSrv(cspush_addrs, g_etcd_loop.get());
        AK_LOG_INFO << "UpdatePushSrv end";
    }
}

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}


void PushSrvConnInit(const std::set<std::string>& cspush_addrs, evpp::EventLoop* loop)
{
    for (const auto& cspush : cspush_addrs) //ip:port的形式
    {
        PushClientPtr push_cli_ptr(new CPushClient(loop, cspush, "csroute push client"));
        push_cli_ptr->Start();
        CPushClientMng::Instance()->AddPushSrv(cspush, push_cli_ptr);
    }
}

void EtcdSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.etcd_server_addr);//"ip:port;ip:port;..."
    std::set<std::string> cspush_addrs;
    if (g_etcd_cli_mng->GetAllPushSrvs(cspush_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    PushSrvConnInit(cspush_addrs, g_etcd_loop.get());

    //cssession
    std::set<std::string> cssession_addrs;
    if (g_etcd_cli_mng->GetAllSessionSrvs(cssession_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    SessionSrvConnInit(cssession_addrs);

    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    NsqLookupdSrvInit(nsqlookupd_addrs);

    std::string inner_addr = GetEth0IPAddr();
    inner_addr += ":8500";//csroute tcp 监听端口
    char inner_reg_info[64] = {0};
    ::snprintf(inner_reg_info, 64, "%s%s", "/akcs/csroute/innerip/", inner_addr.c_str());
    RegSrv2Etcd(inner_reg_info, inner_addr, 10, csbase::REG_INNER, g_etcd_loop.get());


    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_session, UpdateSessionSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_nsqlookupd, UpdateLookupdSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_cspush, UpdatePushSrvList);
    g_etcd_loop->Run();
}

