// csroute server
// Created on: 2018-12-4
// Author: chenyc

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <vector>
#include <string>
#include <map>
#include <thread>
#include <errno.h>
#include "glog/logging.h"
#include "AkLogging.h"
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include "ConfigFileReader.h"
#include "route_mq.h"
#include "route_server.h"
#include "route_etcd.h"
#include "session_rpc_client.h"
#include "SafeCacheConn.h"
#include "video_rpc_client.h"
#include "ConnectionPool.h"
#include "AkcsMonitor.h"
#include "kafka_consumer.h"
#include "kafka_producer.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "AkcsCommonDef.h"
#include "dbinterface/SystemSettingTable.h"
#include "PushLinker.h"
#include "push_kafka.h"
#include "zipkin/ZipkinConf.h"
#include "zipkin/ZipkinAsyn.h"
#include "upgrade_dev_mng.h"
#include "HttpServer.h"
#include "LogConnectionPool.h"
#include "dbinterface/Log/LogSlice.h"
#include "AkcsAppInit.h"
#include <KdcDecrypt.h>
#include "Metric.h"

static const char csroute_conf_file[] = "/usr/local/akcs/csroute/conf/csroute.conf";
AKCS_ROUTE_CONF gstAKCSConf;
SmRpcClient* g_sm_client_ptr = nullptr;
VideoStorageClient* g_vs_client_ptr = nullptr;
CKafakProducer* g_kafka_producer = nullptr;
CPushKafkaClient* g_push_kafka = nullptr;

extern CAkEtcdCliManager* g_etcd_cli_mng;
extern CAkEtcdCliManager* g_etcd_zipkinconf_cli;
extern const char *g_conf_db_addr;
int g_etcd_dns_res = 0;
extern const char* g_redis_db_userdetail;
extern const char *g_ak_srv_zipkin_kafka;
LOG_DELIVERY gstAKCSLogDelivery;

#define PIDFILE "/var/run/csroute.pid"


int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstAKCSConf.db_ip, gstAKCSConf.db_port);
    return 0;
}
void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(SrvDbConf));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.db_ip) != 0) || (conf_tmp.db_port != gstAKCSConf.db_port))
    {
        Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip),  conf_tmp.db_ip);
        gstAKCSConf.db_port = conf_tmp.db_port;
        DaoReInit();
    }
}

int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(SrvDbConf));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip),  conf_tmp.db_ip);
    gstAKCSConf.db_port = conf_tmp.db_port;
    return 0;
}
void ConfSrvInit()
{

    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.etcd_server_addr);

    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);

    g_etcd_zipkinconf_cli = g_etcd_cli_mng;
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_zipkin_kafka, UpdateZipkinConfFromConfSrv);
    //从配置中心获取 初始化zipkin配置
    UpdateZipkinConfFromConfSrv();
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

int ConfInit()
{

    CConfigFileReader config_file(csroute_conf_file);

    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username),  config_file.GetConfigName("db_username"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), decrypt_db_passwd.c_str());
    
    Snprintf(gstAKCSConf.db_database, sizeof(gstAKCSConf.db_database),  config_file.GetConfigName("akcs_db_database"));
    
    Snprintf(gstAKCSConf.log_db_ip, sizeof(gstAKCSConf.log_db_ip),  config_file.GetConfigName("log_db_ip"));
    const char* log_db_port = config_file.GetConfigName("log_db_port");
    gstAKCSConf.log_db_port = ATOI(log_db_port);
    Snprintf(gstAKCSConf.log_db_database, sizeof(gstAKCSConf.log_db_database), config_file.GetConfigName("log_db_database"));

    Snprintf(gstAKCSConf.nsq_topic, sizeof(gstAKCSConf.nsq_topic),  config_file.GetConfigName("nsq_route_topic"));
    Snprintf(gstAKCSConf.nsq_channel, sizeof(gstAKCSConf.nsq_channel),  config_file.GetConfigName("nsq_route_channel"));

    Snprintf(gstAKCSConf.video_server_addr, sizeof(gstAKCSConf.video_server_addr),  config_file.GetConfigName("video_server_net"));    Snprintf(gstAKCSConf.gw_code, sizeof(gstAKCSConf.gw_code),  config_file.GetConfigName("gateway_code"));
    Snprintf(gstAKCSConf.gw_code, sizeof(gstAKCSConf.gw_code),  config_file.GetConfigName("gateway_code"));

    if(LoadConfFromConfSrv() != 0)
    {
        Snprintf(gstAKCSConf.db_ip, sizeof(gstAKCSConf.db_ip),  config_file.GetConfigName("akcs_db_ip"));
        const char* db_port = config_file.GetConfigName("akcs_db_port");
        gstAKCSConf.db_port = ATOI(db_port);
    }

    Snprintf(gstAKCSConf.oem_name, sizeof(gstAKCSConf.oem_name),  config_file.GetConfigName("oem_name"));
    if (!strcasecmp(gstAKCSConf.oem_name, "AKUVOX"))
    {
        gstAKCSConf.oem_num = OEM_AKUVOX;
    }
    else if (!strcasecmp(gstAKCSConf.oem_name, "DISCREET"))
    {
        gstAKCSConf.oem_num = OEM_DISCREET;
    }
    Snprintf(gstAKCSConf.push_server_addr, sizeof(gstAKCSConf.push_server_addr),  config_file.GetConfigName("cspush_net"));
    Snprintf(gstAKCSConf.push_aeskey, sizeof(gstAKCSConf.push_aeskey),  config_file.GetConfigName("push_aeskey"));

    Snprintf(gstAKCSConf.kafka_push_email_topic, sizeof(gstAKCSConf.kafka_push_email_topic),  config_file.GetConfigName("kafka_push_email_topic"));
    Snprintf(gstAKCSConf.kafka_broker_ip, sizeof(gstAKCSConf.kafka_broker_ip),  config_file.GetConfigName("kafka_broker_ip"));
    Snprintf(gstAKCSConf.kafka_pm_export_log_topic, sizeof(gstAKCSConf.kafka_pm_export_log_topic),  config_file.GetConfigName("kafka_pm_export_log_topic"));
    Snprintf(gstAKCSConf.kafka_pm_export_log_excel_topic, sizeof(gstAKCSConf.kafka_pm_export_log_excel_topic),  config_file.GetConfigName("kafka_pm_export_log_excel_topic"));
    Snprintf(gstAKCSConf.kafka_notify_web_topic, sizeof(gstAKCSConf.kafka_notify_web_topic),  config_file.GetConfigName("kafka_notify_web_topic"));
    Snprintf(gstAKCSConf.kafka_notify_linker_topic, sizeof(gstAKCSConf.kafka_notify_linker_topic),  config_file.GetConfigName("kafka_notify_linker_topic"));
    Snprintf(gstAKCSConf.kafka_notify_web_message_topic, sizeof(gstAKCSConf.kafka_notify_web_message_topic),  config_file.GetConfigName("kafka_notify_web_message_topic"));
    Snprintf(gstAKCSConf.kafka_notify_web_attendance_topic, sizeof(gstAKCSConf.kafka_notify_web_attendance_topic),  config_file.GetConfigName("kafka_notify_web_attendance_topic"));
    Snprintf(gstAKCSConf.kafka_notify_web_access_door_topic, sizeof(gstAKCSConf.kafka_notify_web_access_door_topic), config_file.GetConfigName("kafka_notify_web_access_door_topic"));

    Snprintf(gstAKCSConf.kafka_csroute_topic, sizeof(gstAKCSConf.kafka_csroute_topic),  config_file.GetConfigName("kafka_csroute_topic"));
    Snprintf(gstAKCSConf.kafka_csroute_group, sizeof(gstAKCSConf.kafka_csroute_group),  config_file.GetConfigName("kafka_csroute_group"));
    Snprintf(gstAKCSConf.linker_nsq_topic, sizeof(gstAKCSConf.linker_nsq_topic),  config_file.GetConfigName("nsq_linker_topic"));
    Snprintf(gstAKCSConf.linker_nsq_ip, sizeof(gstAKCSConf.linker_nsq_ip),  config_file.GetConfigName("nsq_linker_ip"));
    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));


    const char* route_msg_thread_num = config_file.GetConfigName("route_msg_thread_num");
    gstAKCSConf.route_msg_thread_num = ATOI(route_msg_thread_num);        

    CConfigFileReader server_config_file("/etc/ip");
    Snprintf(gstAKCSConf.route_outer_ip, sizeof(gstAKCSConf.route_outer_ip),  server_config_file.GetConfigName("SERVERIP"));
    return 0;
}

bool InstanceInit()
{
    CacheManager::getInstance()->Init("/usr/local/akcs/csroute/conf/csroute_redis.conf", "csrouteCacheInstances");
    //清空userdetail，因为csmaint投递的userinfo消息
    //route和adapt不保证一定能处理到的，比如csmain写成功了，但是路由或config崩溃了    
    SafeCacheConn redis(g_redis_db_userdetail);
    return redis.flushdb();
}

int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstAKCSConf.db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.db_database, gstAKCSConf.db_port, 8, "csroute");

    LogConnPool* log_conn_pool = GetLogDBConnPollInstance();
    if (NULL == log_conn_pool)
    {
        AK_LOG_WARN << "LOG DaoInit failed.";
        return -1;
    }
    log_conn_pool->Init(gstAKCSConf.log_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.log_db_database, gstAKCSConf.log_db_port, 8, "csroute");

    return 0;
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstAKCSConf.etcd_server_addr;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    CConfigFileReader config_file(csroute_conf_file);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr),  config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstAKCSConf.etcd_server_addr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstAKCSConf.etcd_server_addr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}

void UpgradeDevPlan()
{
    while(1)
    {
        CUpgradeDevMng::GetInstance()->GetUpgradeDevList();
        sleep(10);
    }
}

int LogDeliveryInit()
{
    gstAKCSLogDelivery.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    gstAKCSLogDelivery.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    gstAKCSLogDelivery.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    if (gstAKCSLogDelivery.personal_capture_delivery == 0 || gstAKCSLogDelivery.personal_motion_delivery == 0 || gstAKCSLogDelivery.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}

int main(int argc, char** argv)
{
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2(PIDFILE))
    {
        printf("another csroute has been running in this system.");
        return -1;
    }
    GlogInit2(argv[0], "csroutelog");

    //一定要另起线程，不能用别的loop，因为dns解析有可能会卡住，会影响别的执行
	memset(&gstAKCSConf, 0, sizeof(AKCS_ROUTE_CONF));
    std::thread dns_thread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }  
    
    //配置中心初始化
    ConfSrvInit();
    /* 读取配置文件 */
    if (ConfInit() != 0)
    {
        AK_LOG_FATAL << "init conf failed";
        return -1;
    }
    if (!InstanceInit())
    {
        AK_LOG_FATAL << "init instance failed";
        return -1;
    }
    if (DaoInit() != 0)
    {
        AK_LOG_FATAL << "init dao failed";
        return -1;
    }

    //kafka客户端初始化
    g_kafka_producer = new CKafakProducer(gstAKCSConf.kafka_push_email_topic, gstAKCSConf.kafka_broker_ip);
    g_kafka_producer->CreatePmExportLogTopic(gstAKCSConf.kafka_pm_export_log_topic);
    g_kafka_producer->CreatePmExportLogExcelTopic(gstAKCSConf.kafka_pm_export_log_excel_topic);
    g_kafka_producer->CreateNotifyWebTopic(gstAKCSConf.kafka_notify_web_topic);
    g_kafka_producer->CreateNotifyLinkerTopic(gstAKCSConf.kafka_notify_linker_topic);
    g_kafka_producer->CreateNotifyWebMessageTopic(gstAKCSConf.kafka_notify_web_message_topic);
    g_kafka_producer->CreateNotifyWebAttendanceTopic(gstAKCSConf.kafka_notify_web_attendance_topic);
    g_kafka_producer->CreateNotifyWebAccessDoorTopic(gstAKCSConf.kafka_notify_web_access_door_topic);
    g_push_kafka = new CPushKafkaClient();

    // route新支持kafka的mq, 处理cslinker过来的消息
    std::thread kafka_consumer_thread = std::thread(KafkaConsumerInit);
    
    //cssession grpc客户端 要先于etcd
    g_sm_client_ptr = new SmRpcClient();
    std::string cssession_client_node = "csroute_" + std::string(gstAKCSConf.route_outer_ip);
    g_sm_client_ptr->RegisterNode(cssession_client_node);

    //nsq消息 消费处理,需要等etcd那边拉取到nsqlookupd地址后才会去连接nsqd
    RouteMQCust::GetInstance()->Init();
    //等待g_nsq_consumer_client_ptr初始化
    usleep(500*1000);

    //注册服务，并监测其他服务地址变化
    std::thread etcd_cli_thread = std::thread(EtcdSrvInit);

    //起http服务线程
    std::thread httpThread(startHttpServer);

    //cslinker nsq指定
    std::thread linker_mq_thread = std::thread(CPushLinKer::PushLinkerInit);

    //起消息路由服务,支持其他组件通过tcp来连接
    std::thread route_server_thread(startRouteServer);

    //设备升级计划处理线程
    std::thread upgrade_dev_thread(UpgradeDevPlan);

    //csvs grpc客户端
    std::string vs_srv_net = gstAKCSConf.video_server_addr;
    g_vs_client_ptr = new VideoStorageClient(grpc::CreateChannel(
                vs_srv_net, grpc::InsecureChannelCredentials()));
                
    //nsq消息发布客户端, 用于发送运维监控告警消息
    std::thread mqProduceThread = std::thread(MQProduceInit);
    //配置中心配置监测
    std::thread conf_watch_thread = std::thread(ConfWatch);
    //zipkin异步消息处理
    GetZipkinAsynInstance()->Init();

    //获取LOG库日志表分片数
    if (LogDeliveryInit() != 0)
    {
        AK_LOG_WARN << "LogDeliveryInit fialed.";
        google::ShutdownGoogleLogging();
        return -1;
    }

    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csgate is starting";

    conf_watch_thread.join();
    route_server_thread.join();
    dns_thread.join();
    etcd_cli_thread.join();
    httpThread.join();
    linker_mq_thread.join();
    kafka_consumer_thread.join();
    upgrade_dev_thread.join();
    return 0;
}

