#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include <evpp/tcp_conn.h>
#include <evnsq/consumer.h>
#include "AkcsMsgDef.h"
#include "push_client.h"
#include "push_kafka.h"
#include "route_server.h"
#include "session_rpc_client.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AK.Adapt.pb.h"
#include "SafeCacheConn.h"
#include "video_rpc_client.h"
#include "AkcsMonitor.h"
#include <evnsq/producer.h>
#include "kafka_producer.h"
#include "route_mq.h"
#include "route_office_mq.h"
#include "route_newoffice_mq.h"
#include "AkcsOemDefine.h"
#include "AkcsCommonDef.h"
#include "personal_account.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "PushLinker.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "PushClientMng.h"
#include "AK.Resid.pb.h"
#include "AK.Linker.pb.h"
#include "google/protobuf/any.pb.h"
#include "zipkin/AkcsZipkin.h"
#include "zipkin/ZipkinConf.h"
#include "rtsp_rtp_intercept.h"
#include "AK.BackendCommon.pb.h"
#include "SnowFlakeGid.h"
#include "MetricService.h"

const static int32_t kAkMsgHoldLen = 4;
extern RouteServer* g_route_ser;
extern SmRpcClient* g_sm_client_ptr;
extern CPushKafkaClient* g_push_kafka;
extern VideoStorageClient* g_vs_client_ptr;
extern AKCS_ROUTE_CONF gstAKCSConf;
extern CKafakProducer* g_kafka_producer;
extern AKCS_ZIPKIN_CONF g_zipkin_conf;
evnsq::Consumer* g_nsq_consumer_client_ptr = nullptr;
extern LOG_DELIVERY gstAKCSLogDelivery;

RouteMQCust* RouteMQCust::instance_ = NULL;

RouteMQCust* RouteMQCust::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new RouteMQCust();
    }
    return instance_;
}

void RouteMQCust::Init()
{
    std::thread thread = std::thread(&RouteMQCust::InitNsqConsumer, this);
    thread.detach();

    //起处理消息线程，用于处理上述InitNsqConsumer->OnRouteMQMessage时投递的消息        
    for (int i = 0; i < gstAKCSConf.route_msg_thread_num; i++) {
        AK_LOG_INFO << "Start ProcessPduMsg thread " << i;
        auto threadPtr = std::unique_ptr<std::thread>(new std::thread(&RouteMQCust::ProcessPduMsg, this));
        threadPtr->detach();  // 分离线程
        process_thread_list.push_back(std::move(threadPtr));
    }
    
}

void RouteMQCust::InitNsqConsumer()
{
    evpp::EventLoop nsq_loop;
    g_nsq_consumer_client_ptr = new evnsq::Consumer(&nsq_loop, gstAKCSConf.nsq_topic, gstAKCSConf.nsq_channel, evnsq::Option());
    g_nsq_consumer_client_ptr->SetConnectErrorCallback(&OnConnectError);
    g_nsq_consumer_client_ptr->SetMessageCallback(&OnRouteMQMessage);
    nsq_loop.Run();
}

//msg不需要再判断消息
//参考: NSQConn::OnMessage的代码片段,如果OnRouteMQMessage返回非0值,则消息会被重新投递进队列中,重新被消费
//if (msg_fn_(&msg) == 0) {
//    Finish(msg.id);
//} else {
//    Requeue(msg.id);
//}
int OnRouteMQMessage(const evnsq::Message* msg) //
{
    const char* data = msg->body.data();
    const uint32_t size = msg->body.size();
    if (size >= kAkMsgHoldLen)
    {
        const int32_t pb_msg_len = PeekInt32(data, size);
        if (size != (uint32_t)pb_msg_len)
        {
            AK_LOG_WARN << "Invalid evnsq length " << size;
            return 0;
        }
        else
        {
            std::shared_ptr<CAkcsPdu> pPdu(new CAkcsPdu());
            pPdu->Write(data, size); //包整体长度全部整进去
            char tmp_buf[sizeof(PduHeader_t)] = {0};
            memcpy(tmp_buf, data, sizeof(tmp_buf));
            if (pPdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0)
            {
                AK_LOG_WARN << "Pdu packet header len is invalid";
                return 0;
            }
            else
            {
                RouteMQCust::GetInstance()->AddMessage(pPdu);
            }
        }
    }
    else
    {
        AK_LOG_WARN << "Invalid evnsq length " << size;
        return 0;
    }
    return 0;
}

void OnConnectError(const std::string& addr)
{   
    AK_LOG_WARN << "Connect nsqd-" << addr << " error.";
    ConnectNsqErrorMutt(addr, "csroute");
}

int RouteMQCust::GetRoutePduSize()
{
    std::unique_lock<std::mutex> lock(msg_pdus_mtx_);
    return msg_pdus_.size();
}

void RouteMQCust::AddMessage(const std::shared_ptr<CAkcsPdu>& pdu)
{
    std::unique_lock<std::mutex> lock(msg_pdus_mtx_);
    msg_pdus_.push_front(pdu);
    msg_pdus_cv_.notify_all();
}

void RouteMQCust::AddLatencyMetric(const std::shared_ptr<CAkcsPdu>& pdu)
{
    std::chrono::steady_clock::time_point msg_handle_end = std::chrono::steady_clock::now();
    auto duration_all_handle = std::chrono::duration_cast<std::chrono::milliseconds>(msg_handle_end - pdu->GetPduCreateTime());

    MetricService* metric_service = MetricService::GetInstance();
    if(metric_service) 
    {
        metric_service->AddLatencyLatencyValue("queue_handle_latency", duration_all_handle.count(), "RouteTransfer");
    } 
}

void RouteMQCust::ProcessPduMsg()
{
    while (1)
    {
        std::shared_ptr<CAkcsPdu> pdu;
        {
            std::unique_lock<std::mutex> lock(msg_pdus_mtx_);
            while (msg_pdus_.size() == 0)
            {
                msg_pdus_cv_.wait(lock);
            }
            pdu = msg_pdus_.back();
            msg_pdus_.pop_back();
        }

        // 处理消息
        OnMessage(pdu);

        // 添加延迟指标
        AddLatencyMetric(pdu);
    }
}

void RouteMQCust::OnMessage(const std::shared_ptr<CAkcsPdu>& pdu)
{
    uint64_t traceid = pdu->GetTraceId();
    if (traceid == 0)
    {
        // 设置glog线程traceid
        traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();        
    }
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    uint32_t msg_id = pdu->GetCommandId();
    uint16_t project = pdu->GetProjectType();
    AK_LOG_INFO << "OnMessage msgid:0x" << std::hex << msg_id << " project:" << project << std::dec << " traceid:" << traceid;
    
    //通用
    switch (msg_id)
    {
        case AKCS_R2M_UPGRADE_DEV_REQ:
        {
            return;
        }
        case AKCS_BUSSNESS_P2P_MSG:
        {
            HandleP2PBackendMsg(pdu);
            return;            
        }
        case MSG_C2S_PCAP_CAPTURE_CONTROL:
        {
            HandleGroupPcapCaptureMsg(pdu);
            return;
        }
        case MSG_C2S_SIP_PCAP_CAPTURE_CONTROL:
        {
            HandleGroupSipPcapCaptureMsg(pdu);
            return;
        }
    }

    //add by chenzhx 20240730
    //如果上游给设备或者用户统一标识了新办公，但是因为下游的代码没有变动，
    //这时候就按旧办公的逻辑走.但是下游的代码可能拿这个标识进行判断去走办公/住宅的分支就会有问题，这个风险不好把控，
    //所以当前我们新办公要求，在明确需要区分新旧办公的时候，我们通过打project::OFFICE_NEW来区分，不需要区分的就打project::OFFICE标识
    //然后在缓存的地方不能直接给设备或者用户打project::OFFICE_NEW标识，因为代码可能直接拿缓存的类型进行传递，
    //这时候就没有办法控制消息对新旧办公的区分
    
    //办公
    if (project == project::OFFICE)
    {
        //办公有处理过就直接退出
        if (RouteOfficeMQCust::GetInstance()->OnMessage(pdu))
        {
            return;
        }
    }
    //新办公
    if (project == project::OFFICE_NEW)
    {
        //办公有处理过就直接退出
        if (RouteNewOfficeMQCust::GetInstance()->OnMessage(pdu))
        {
            return;
        }
    }    
    //AK_LOG_INFO << "csroute mq msg, pdumsg id " <<  msg_id;
    switch (msg_id)
    {
        //csmain部分
        case AKCS_M2R_GROUP_COMM_ALARM_REQ:
        {
            HandleGroupCommAlarmMsg(pdu);
            break;
        }
        case AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ:
        {
            HandleGroupCommAlarmDealMsg(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PER_ALARM_REQ:
        {
            HandleGroupPerAlarmMsg(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ:
        {
            HandleGroupPerAlarmDealMsg(pdu);
            break;
        }
        case AKCS_M2R_GROUP_PER_MOTION_REQ:
        {
            HandleGroupPerMotionMsg(pdu);
            break;
        }
        case AKCS_M2R_P2P_RTSP_CAPTURE_REQ:
        {
            HandleP2PRtspCaputreMsg(pdu);
            break;
        }
        case AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ:
        {
            HandleGroupMngTextMsg(pdu);
            break;
        }
        // case AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ:
        // {
        //     HandleP2PAppGetArmingMsg(pdu);
        //     break;
        // }
        case AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP:
        {
            HandleP2PAppGetArmingRespMsg(pdu);
            break;
        }
        case AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ:
        {
            HandleP2PVisitorAuthorize(pdu);
            break;
        }
        case AKCS_M2R_P2P_FACE_DATA_FORWARD_REQ:
        {
            HandleP2PForwardFaceData(pdu);
            break;
        }
        /*
        case AKCS_M2R_P2P_OPEN_DOOR_REQ:
        {
            HandleP2PDevOpenDoor(pdu);
            break;
        }
        */
        case AKCS_M2R_P2P_SEND_DELIVERY_REQ:
        {
            HandleP2PDevSendDelivery(pdu);
            break;            
        }
        case AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ:
        {
            HandleP2PDevSendTmpkeyUsed(pdu);
            break;            
        }
        case AKCS_M2R_P2P_CHANGE_RELAY_REQ:
        {
            HandleP2PChangeRelay(pdu);
            break;            
        }
        case AKCS_M2R_P2P_SEND_REMIND_FLOW_OUT_OF_LIMIT:
        {
            HandleSendRemindOutOfFlow(pdu);
            break;
        }
        case AKCS_M2R_P2P_SEND_VOICE_MSG:
        {
            HandleSendVoiceMsg(pdu);
            break;
        }
        //csmain发送给csadapt的部分，透传即可
        case MSG_S2C_DEV_REPORT_VISITOR:
        {
            HandleP2PReportToAdaptMsg(pdu);
            break;
        }
        case MSG_S2C_DEV_CONFIG_REWRITE:
        {            
            //HandleP2PReportToAdaptMsg(pdu);
            HandleP2PReportToConfigMsg(pdu);
            break;
        }
        case MSG_S2C_ACCOUNT_CONFIG_REWRITE:
        {
            HandleP2PReportToConfigNodeMsg(pdu);
            break;
        }
        case MSG_S2C_DEV_REQ_USER_INFO:
        {
            HandleP2PReportToConfigMsg(pdu);
            break;
        }  
        //csmain-->csroute-->cslinker
        //csadapt-->csroute-->cslinker 
        case AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG:
        {
            HandleLinkerCommonMsg(pdu);
            break;
        }  
                
        //csvrtsp部分
        case AKCS_V2R_START_RTSP_REQ:
        case AKCS_MSG_L2R_START_RTSP_REQ:
        {
            HandleP2PStartRtspMsg(pdu);
            break;
        }
        case AKCS_V2R_STOP_RTSP_REQ:
        case AKCS_MSG_L2R_STOP_RTSP_REQ:
        {
            HandleP2PStopRtspMsg(pdu);
            break;
        }
        case AKCS_MSG_L2R_KEEPALIVE_RTSP_REQ:
        case AKCS_V2R_KEEPALIVE_RTSP_REQ:
        {
            HandleP2PRtspKeepAliveMsg(pdu);
            break;
        }      
        //pbx部分,全部走长连接同道

        //csstorage部分
        case AKCS_S2R_P2P_OFFLINE_MSG_ACK_REQ://离线重传成功
        {
            HandleStorageOfflineAckMsg(pdu);
            break;
        }
        case AKCS_S2R_P2P_VOICE_MSG_ACK_REQ://离线重传成功
        {
            HandleStorageVoiceAckMsg(pdu);
            break;
        }
        //csadapt部分
        case MSG_C2S_REBOOT_DEVICE:
        {
            HandleRebootDevMsg(pdu);
            break;
        }
        case MSG_C2S_RESET_DEVICE:
        {
            HandleResetDevMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_UPDATE_NODE:
        {
            HandlePerUpdateNodeMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE:
        {
            HandleCommUpdateNodeMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL:
        {
            HandleGroupPerAlarmDealMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL:
        {
            HandleGroupCommAlarmDealMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_REPORT_STATUS://对应 csadapt:MSG_P2A_PERSONNAL_ADD_DEV
        {
            HandleDevReportStatusMsg(pdu);
            break;
        }
        case MSG_C2S_DEV_CHANGE:
        {
            HandleDevChangeMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_DEL_DEV://对应 csadapt:MSG_P2A_PERSONNAL_ADD_DEV,因为从批量删除改成单个删除,所以信令需要修改
        {
            HandlePerDevLogOutSipMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_DEL_UID://对应 csadapt:MSG_P2A_PERSONNAL_DEL_UID,因为从批量删除改成单个删除,所以信令需要修改
        {
            HandlePerUidLogOutSipMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_TEXT_MSG:
        {
            HandlePerTextMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE:
        {
            HandleConfFileChangeMsg(pdu);
            break;
        }
        case MSG_C2S_DEV_APP_EXPIRE:
        {
            HandleDevAppExpireMsg(pdu);
            break;
        }
        case MSG_C2S_DEV_APP_WILL_BE_EXPIRE:
        {
            HandleDevAppWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_FREETRIAL_WILL_BE_EXPIRE:
        {
            HandleFreeTrialWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_DEV_NOT_EXPIRE:
        {
            HandleDevNotExpireMsg(pdu);
            break;
        }
        case MSG_C2S_DEV_CLEAN_DEV_CODE:
        {
            HandleDevCleanDevCodeMsg(pdu);
            break;
        }
        /*
        case MSG_C2S_REMOTE_OPENDOOR:
        {
            HandleRemoteOpenDoorMsg(pdu);
            break;
        }
        case MSG_C2S_REMOTE_OPEN_SECURITY_RELAY:
        {
            HandleOpenSecurityRelayMsg(pdu);
            break;
        }
        */
        case MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD:
        {
            HandleNotifyFaceServerPicDownloadMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY:
        {
            HandleNotifyFaceServerPicModifyMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE:
        {
            HandleNotifyFaceServerPicDeleteMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD:
        {
            HandleNotifyFaceServerPicBatchDownloadMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_MODIFY:
        {
            HandleNotifyFaceServerPicBatchModifyMsg(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DELETE:
        {
            HandleNotifyFaceServerPicBatchDeleteMsg(pdu);
            break;
        }

        //以下为邮件相关,直接从csroute往cspush发送出去
        case MSG_C2S_SEND_EMAIL_NOTIFY:
        {
            HandleSendEmailNotifyMsg((pdu));
            break;
        }
        case MSG_C2S_PER_SEND_CREATE_UID_MAIL:
        {
            HandleLocalCreateUidMailMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_RESET_PWD_MAIL:
        {
            HandleLocalResetPwdMailMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_CHANGE_PWD_MAIL:
        {
            HandleLocalChangePwdMailMsg(pdu);
            break;
        }
        case MSG_C2S_PER_SEND_CHECK_CODE_MAIL:
        {
            HandleLocalCheckCodeMailMsg(pdu);
            break;
        }
        case MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT:
        {
            HandleDelAppAccountMailMsg(pdu);
            break;
        }
        case MSG_C2S_ACCOUNT_ACTIVE:
        {
            HandleLocalAccountActMailMsg(pdu);
            break;
        }
        case MSG_C2S_PM_ACCOUNT_ACTIVE:
        {
            HandlePmAccountActMailMsg(pdu);
            break;
        }
        case MSG_C2S_SHARE_TMPKEY:
        {
            HandleLocalShareTmpKeyMailMsg(pdu);
            break;
        }
        case MSG_C2S_CREATE_PROPERTY_WORK:
        {
            HandleLocalCreatePropertyWorkMailMsg(pdu);
            break;
        }
        case MSG_C2S_RENEW_SERVER:
        {
            HandleRenewSrvMailMsg(pdu);
            break;
        }
        case MSG_C2S_PM_RENEW_SERVER:
        {
            HandlePmRenewSrvMailMsg(pdu);
            break;
        }
        case MSG_C2S_PM_WILL_EXPIRE:
        {
            HandlePMMailMsg(pdu);
            break;
        }
        case MSG_C2S_PHONE_EXPIRE:
        {
            HandlePhoneExpireMsg(pdu);
            break;
        }
        case MSG_C2S_PHONE_WILL_EXPIRE:
        {
            HandlePhoneWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE:
        {
            HandleInstallerPhoneWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_INSTALLER_APP_WILL_EXPIRE:
        {
            HandleInstallerAppWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_PM_FEATURE_WILL_EXPIRE:
        {
            HandlePMFeatureWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE:
        {
            HandleInstallerFeatureWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_SEND_SMS_CODE:
        {
            HandleSendSmsCodeMsg(pdu);
            break;
        }
        case MSG_C2S_PM_EXPORT_LOG:
        {
            HandlePmExportLogMsg(pdu);
            break;
        }        
        case MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT:
        {
            HandleSendDelAppAccountSmsCodeMsg(pdu);
            break;
        }
        //6.4新增
        case MSG_C2S_SEND_SMS_CREATE_UID:
        {
            HandleSendSmsCreateUidMsg(pdu);
            break;
        }
        case MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE:
        {
            HandlePMAppAccountWillExpireMsg(pdu);
            break;
        }
        case MSG_C2S_PM_APP_ACCOUNT_EXPIRE:
        {
            HandlePMAppAccountExpireMsg(pdu);
            break;
        }
        //alex
        case MSG_C2S_ALEXA_LOGIN_MSG:
        {
            HandleGroupAlexaLoginReqMsg(pdu);
            break;
        }
        case MSG_C2S_ALEXA_SET_ARMING_MSG:
        {
            HandleP2PAlexaSetArmingReqMsg(pdu);
            break;
        }

        //以下为视频录制相关
        case MSG_C2S_ADD_VIDEO_STORAGE_SCHED:
        {
            HandleGroupAddVideoSchedMsg(pdu);
            break;
        }
        case MSG_C2S_DEL_VIDEO_STORAGE_SCHED:
        {
            HandleGroupDelVideoSchedMsg(pdu);
            break;
        }
        case MSG_C2S_DEL_VIDEO_STORAGE:
        {
            HandleLocalDelVideoMsg(pdu);//直接从csroute往csvs发送出去
            break;
        }
        //远程设备网页控制
        case MSG_C2S_CREATE_REMOTE_DEV_CONTORL:
        {
            HandleRemoteDevContorl(pdu);
            break;
        }
        case MSG_C2S_REFRESH_CONN_CACHE:
        {
            HandleNotifyRefreshConnCache(pdu);
            break;
        }
        case MSG_C2S_UPDATE_TO_DEVICE:
        {
            HandleNotifyConfigUpdate(pdu);
            break;
        }
        case MSG_C2S_KEEP_OPEN_RELAY:
        {
            HandleNotifyDoorControl(pdu);
            break;
        }
        case MSG_C2S_KEEP_CLOSE_RELAY:
        {
            HandleNotifyDoorControl(pdu);
            break;
        }
        case MSG_C2S_NOTIFY_FILE_CHANGE:
        {
            HandleNotifyDevFileChange(pdu);
            break;
        }
        case MSG_C2S_PM_EMERGENCY_DOOR_CONTROL:
        {	
            HandlePmEmergencyDoorControlMsg(pdu);
            break;
        }
        case MSG_C2S_SEND_USER_ADD_NEWSITE:
        {	
            HandleSendUserAddNewSiteMsg(pdu);
            break;
        }
        case MSG_C2S_SEND_PM_WEB_LINK_NEWSITES:
        {	
            HandleSendPmWebLinkNewSitesMsg(pdu);
            break;
        }
        case MSG_C2S_PM_WEB_CREATE_UID_MAIL:
        {	
            HandleSendPmWebCreateUidMailMsg(pdu);
            break;
        }
        case MSG_C2S_PM_WEB_CHANGE_UID_MAIL:
        {	
            HandleSendPmWebChangeUidMailMsg(pdu);
            break;
        }
        case MSG_C2S_SEND_CODE_TO_EMAIL:
        {	
            HandleSendCodeToEmailMsg(pdu);
            break;
        }
        case MSG_C2S_SEND_CODE_TO_MOBILE:
        {	
            HandleSendCodeToMobileMsg(pdu);
            break;
        }
        case MSG_C2S_CHANGE_MAIN_SITE:
        {
            HandleNotifyChangeMainSite(pdu);
            break;
        }
        case AKCS_L2R_WEATHER_INFO_RESP:
        {
            HandleP2PDevWeatherMsg(pdu);
            return;
        }
        case MSG_C2S_REQUEST_DEV_DEL_LOG:
        {
            HandleDevDelLogMsg(pdu);
            break;
        }
        case MSG_C2S_REFRESH_APP_CONF:
        {
            HandleNotifyAppChangeConfMsg(pdu);
            break;
        }
        //linker->route->resid
        case AKCS_L2R_PACPORT_UNLOCK_RESP:
        {
            HandleP2PPacportUnlockMsg(pdu);
            break;
        }
        case AKCS_L2R_DEV_COMMON_ACK:
        {
            HandleP2PDevCommonAckMsg(pdu);
            break;
        }
        //后端业务->route->web
        case AKCS_M2R_PUSH_WEB_COMMON_MSG:
        {
            HandlePushWebCommonMsg(pdu);
            break;
        }
        case MSG_C2S_OFFICE_DEVICE_SEND_IS_ATTENDANCE:
        {
            HandleNotifyDeviceIsAttendanceMsg(pdu);
            break;
        }
        case AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ:
        {
            HandlePushSmartlockConfigurationMsg(pdu);
            break;
        }
        case AKCS_R2S_P2P_SMARTLOCK_MSG:
        case AKCS_R2S_P2P_ACK_SMARTLOCK_MSG:
        case AKCS_R2S_P2P_SMARTLOCK_HTTP_UP_MSG:
        {
            HandleSmartlockP2PMsg(pdu);
            break;
        }      
        default:
        {
            AK_LOG_WARN << "csroute mq msg,invalid pdumsg id:" << msg_id;
        }
    }
}
//业务监控埋点.
void RouteMQCust::HandleGroupCommAlarmMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupCommAlarmMsg group_comm_alarm_msg;
    CHECK_PB_PARSE_MSG(group_comm_alarm_msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain comm alarm msg:" << group_comm_alarm_msg.DebugString();    
    if(group_comm_alarm_msg.alarm_code() == (int)ALARM_CODE::EMERGENCY)
    {
        AK_LOG_INFO << "EMERGENCY impossible";
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&group_comm_alarm_msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_COMM_ALARM_REQ);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        AK_LOG_INFO << " notify to csmain:" << conn.second->AddrToString() << " is " << (conn.second->IsConnected() ? "Connecting" : "disconnect");
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}
void RouteMQCust::HandleGroupCommAlarmDealMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupCommAlarmDealMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive comm alarm deal msg:" << msg.DebugString();
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        AK_LOG_INFO << " notify to csmain:" << conn.second->AddrToString() << " is " << (conn.second->IsConnected() ? "Connecting" : "disconnect");
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleGroupPerAlarmMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupPerAlarmMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain personal alarm msg:" << msg.DebugString();
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_PER_ALARM_REQ);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        AK_LOG_INFO << " notify to csmain:" << conn.second->AddrToString() << " is " << (conn.second->IsConnected() ? "Connecting" : "disconnect");
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleGroupPerAlarmDealMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupPerAlarmDealMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive personal alarm deal msg:" << msg.DebugString();
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        AK_LOG_INFO << " notify to csmain:" << conn.second->AddrToString() << " is " << (conn.second->IsConnected() ? "Connecting" : "disconnect");
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleGroupPerMotionMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupPerMotionMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    //AK_LOG_INFO << "receive motion alert from csmain:" << msg.DebugString(); //太多了屏蔽掉
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_PER_MOTION_REQ);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

//csmain发送过来的app截图信令
void RouteMQCust::HandleP2PRtspCaputreMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Route::RtspCaptureReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive rtsp capture req from csmain:" << msg.DebugString();
    
    std::string mac = msg.mac();
    std::string flow_uuid = msg.flow_uuid();
    SafeCacheConn redis(g_redis_db_mac_vrtspsid);
    std::string logic_vrtsp_srv_id_cache = redis.get(flow_uuid);

    // mac还没有在其他的监控服务器上传输流,直接退出,本次截图信令失败
    if (logic_vrtsp_srv_id_cache.size() == 0) 
    {
        AK_LOG_WARN << "the mac " << mac << " is not be monitored now, failed to capture";
    }
    else //该设备正在被监控中
    {
        evpp::TCPConnPtr conn = g_route_ser->GetVrtspConnBySid(logic_vrtsp_srv_id_cache);
        if (!conn)
        {
            AK_LOG_WARN << "get csvrtspd route client conn failed,sid is " << logic_vrtsp_srv_id_cache;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2V_RTSP_CAPTURE_REQ); //route往设备视频流的源站rtsp服务发送分流信令
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
    
    return;
}

//csmain通过csroute通知csadapt 仅透传而已
void RouteMQCust::HandleP2PReportToAdaptMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csmain->csroute->csadapt HandleP2PReportToAdaptMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetAdaptConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csadapt route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}
void RouteMQCust::HandleP2PReportToConfigMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csmain->csroute->csconfig HandleP2PReportToConfigMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCsconfigConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csconfig route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}
void RouteMQCust::HandleP2PReportToConfigNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csmain->csroute->csconfig HandleP2PReportToConfigNodeMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCsconfigConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csconfig route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}


//app发过来的,经路由服务器通知csmain,给相应的设备发送保活指令.
// 2019-05-09,源站跟边缘的keepalive处理方式需要不一样
void RouteMQCust::HandleP2PRtspKeepAliveMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Route::RtspKeepAliveReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    uint64_t traceid = pdu->GetTraceId();
    AK_LOG_INFO << "HandleP2PRtspKeepAliveMsg recv rtsp keep alive msg : " << msg.DebugString();

    // mac : app请求监控设备的对象,正常监控时=门口机的mac, 三方摄像头监控时        =三方摄像头的uuid, 转流监控时=转流门口机的mac
    // dev_mac : 正常监控时门口机时  =   门口机的mac, 三方摄像头监控时=三方摄像头绑定的设备mac, 转流监控时=转流室内机的mac
    std::string mac = msg.mac();
    std::string indoor_mac = msg.transfer_indoor_mac();
    std::string dev_mac = indoor_mac.size() > 0 ? indoor_mac : msg.dev_mac();
    std::string flow_uuid = msg.flow_uuid();

    if (dev_mac.length() == 0)
    {
        AK_LOG_WARN << "get third camera binded mac failed, third-uuid:" << mac;
        return;
    }
    
    SafeCacheConn redis(g_redis_db_mac_vrtspsid);
    std::string logic_vrtsp_srv_id_cache = redis.get(flow_uuid);
    
    if (logic_vrtsp_srv_id_cache.empty())
    {
        AK_LOG_WARN << "get csvrtspd rtsp monitor logic_srv_id_cache failed";
        return;
    }

    std::string vrtspd_logic_id_keepalive = msg.vrtspd_logic_id();
    if (vrtspd_logic_id_keepalive == logic_vrtsp_srv_id_cache) //源站要求keepalive，则发往csmain
    {
        //重置rtsps srtp_key的生命周期
        if (!msg.srtp_key().empty()) {
            redis.expire(flow_uuid + "_srtp", 33);
        }

        //重置rtsp源的生命周期
        redis.setex(flow_uuid, 33, logic_vrtsp_srv_id_cache);

        //查询mac在那一台服务器上面
        std::string sid = g_sm_client_ptr->QueryDev(dev_mac);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_KEEPALIVE_RTSP_REQ);
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
    else//边缘要求keepalive，则发往源站csvrtspd
    {
        // 重置rtsps srtp_key的生命周期
        if (!msg.srtp_key().empty()) {
            redis.expire(flow_uuid + "_srtp", 33);
        }
        
        //重置rtsp源的生命周期
        redis.setex(flow_uuid, 33, logic_vrtsp_srv_id_cache);
        
        evpp::TCPConnPtr conn = g_route_ser->GetVrtspConnBySid(logic_vrtsp_srv_id_cache);
        if (!conn)
        {
            AK_LOG_WARN << "get csvrtspd route client conn failed,sid is " << logic_vrtsp_srv_id_cache;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2V_KEEPALIVE_RTSP_REQ);
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

//vrtsp部分
// 将被监控的设备mac地址写入缓存中.几种情况:
// 如果没有在缓存中,那么发送nsq给csroute->csmain
// 如果设备在缓存中，缓存在服务器csvrtspd-A,那么发送nsq给csroute->csvrtspd-A
//TODO,需要分布式锁保证原子性.
//TODO,rtsp相关的需要增加响应,防止相关信令发送不到位。导致占用无谓的监控资源
static const char KRtspLogicCache[] = "local flow_uuid = KEYS[1];local vrtspd_logic_id = ARGV[1];local ttl = tonumber(ARGV[2]);local logic_vrtsp_srv_id_cache = redis.call('GET', flow_uuid);if not logic_vrtsp_srv_id_cache then redis.call('SETEX', flow_uuid, ttl, vrtspd_logic_id) return '0' else return logic_vrtsp_srv_id_cache end";
void RouteMQCust::HandleP2PStartRtspMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Route::StartRtspReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    uint64_t traceid = pdu->GetTraceId();
    AK_LOG_INFO << "receive start rtsp msg: " << msg.DebugString();

    // mac : app请求监控设备的对象,正常监控时=门口机的mac, 三方摄像头监控时        =三方摄像头的uuid, 转流监控时=转流门口机的mac
    // dev_mac : 正常监控时门口机时  =   门口机的mac, 三方摄像头监控时=三方摄像头绑定的设备mac, 转流监控时=转流室内机的mac
    // 查询redis时使用mac,查询sid时使用dev_mac
    std::string mac = msg.mac();
    std::string indoor_mac = msg.transfer_indoor_mac();
    std::string dev_mac = indoor_mac.size() > 0 ? indoor_mac : msg.dev_mac();
    std::string flow_uuid = msg.flow_uuid();

    if (dev_mac.length() == 0)
    {
        AK_LOG_WARN << "get third camera binded mac failed, third-uuid:" << mac;
        return;
    }
    
    // 使用redis script eval命令执行lua脚本保证getset的原子性,防止出现对一个key同时get都为空,再set错误value的情况
    std::string vrtspd_logic_id = msg.vrtspd_logic_id();
    std::vector<std::string> keys = {flow_uuid};
    std::vector<std::string> args = {vrtspd_logic_id, "33"};
    
    SafeCacheConn redis(g_redis_db_mac_vrtspsid);
    std::string logic_vrtsp_srv_id_cache = redis.eval(KRtspLogicCache, keys, args); 
    
    AK_LOG_INFO << "request vrtspd_logic_id = " << vrtspd_logic_id << ", cache vrtspd_logic_id = " << logic_vrtsp_srv_id_cache;
    
    if (logic_vrtsp_srv_id_cache == "0") //mac还没有在其他的监控服务器上传输流
    {
        //added by chenyc,2024-02-19,通过cmake编译选项完成对rtsp相关业务消息的拦截以实现云端支撑rtsp设备性能的压测，生产环境下不开启拦截的编译选项对性能不影响
#ifdef RTSP_RTP_INTERCEPT
        RtspRtpInterceptTest(msg);
#else   //原则上不需要#else直接#end即可,这里特意加上,防止后续主线分支把集成测试的编译选项打开,导致即通知集成测试又发给正常的设备，导致严重bug,测试又无法发现的问题

        //查询mac在哪一台接入服务器上面
        std::string sid;
        sid = g_sm_client_ptr->QueryDev(dev_mac);
        AK_LOG_INFO << "there is not app monitoring dev, mac is " << dev_mac << ", get csmain sid is " << sid;

        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_START_RTSP_REQ);
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
#endif
    }
    else if (logic_vrtsp_srv_id_cache == vrtspd_logic_id) //同一台vrtspd服务器,不需要动作，延长生命周期即可
    {
        AK_LOG_INFO << "the dev is being monitored now at same csvrtspd, mac is " << mac << ", csvrtspd sid is " << logic_vrtsp_srv_id_cache;
        //如果有转流的情况,则增加生命周期的时间,因为如果也是30秒,那么当边缘的rtsp srv发送stop时,可能已经过30秒了,此时logic_vrtsp_srv_id_cache
        //已经被清空,那么csroute就没办法通知源rtsp srv停止监控了,清理相关资源了.
        //进而导致当同一个uid再次监控同一台设备时,出现对应的vrtspd提示:there has been one app monitor dev,也就是永远不会再让设备上传流了.
        redis.setex(flow_uuid, 33, logic_vrtsp_srv_id_cache);

        // 重置rtsps srtp_key的生命周期
        if (!msg.srtp_key().empty()) 
        {
            redis.expire(flow_uuid + "_srtp", 33);
        }
        
        //当第一次发送给csmain失败的时候,如果在30s内继续发送的话,那么还是会失败.容错,再通知csmain一次,防止出现上述讨论的情况.
        std::string sid;
        sid = g_sm_client_ptr->QueryDev(dev_mac);
        AK_LOG_INFO << "there is not app monitoring dev, mac is " << dev_mac << ", get csmain sid is " << sid;

        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed, sid is " << sid;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_START_RTSP_REQ);
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
    else //在不同的vrtspd服务器上面,走vrtspd集群的内部分流协议
    {
        AK_LOG_INFO << "dev is being monitored now at different csvrtspd:" << logic_vrtsp_srv_id_cache;
        // 重置rtsp源的生命周期
        redis.setex(flow_uuid, 33, logic_vrtsp_srv_id_cache);

        // 重置rtsps srtp_key的生命周期
        if (!msg.srtp_key().empty()) 
        {
            redis.expire(flow_uuid + "_srtp", 33);
        }
        
        evpp::TCPConnPtr conn = g_route_ser->GetVrtspConnBySid(logic_vrtsp_srv_id_cache);
        if (!conn)
        {
            AK_LOG_WARN << "get csvrtspd route client conn failed,sid is " << logic_vrtsp_srv_id_cache;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2V_START_RTSP_REQ); //route往设备视频流的源站rtsp服务发送分流信令
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleP2PStopRtspMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Route::StopRtspReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    uint64_t traceid = pdu->GetTraceId();
    AK_LOG_INFO << "HandleP2PStopRtspMsg: " << msg.DebugString();

    // mac : app请求监控设备的对象,正常监控时=门口机的mac, 三方摄像头监控时        =三方摄像头的uuid, 转流监控时=转流门口机的mac
    // dev_mac : 正常监控时门口机时  =   门口机的mac, 三方摄像头监控时=三方摄像头绑定的设备mac, 转流监控时=转流室内机的mac
    // 查询redis时使用mac,查询sid时使用dev_mac
    std::string mac = msg.mac();
    std::string indoor_mac = msg.transfer_indoor_mac();
    std::string dev_mac = indoor_mac.size() > 0 ? indoor_mac : msg.dev_mac();
    std::string flow_uuid = msg.flow_uuid();
    
    if (dev_mac.length() == 0)
    {
        AK_LOG_WARN << "get third camera binded mac failed, third-uuid:" << mac;
        return;
    }
    
    std::string vrtspd_logic_id = msg.vrtspd_logic_id();
    AK_LOG_INFO << "stop rtsp monitor, flow uuid is " << flow_uuid << ", vrtspd logicid is " << vrtspd_logic_id;
    
    SafeCacheConn redis(g_redis_db_mac_vrtspsid);
    std::string logic_vrtsp_srv_id_cache = redis.get(flow_uuid);  

    if (logic_vrtsp_srv_id_cache.size() == 0) //没有客户端在监控设备
    {
        AK_LOG_INFO << "stop rtsp, there is not rtsp client monitoring dev";
        //通知csmain,避免由于缓存出错,导致停止设备监控的信令丢失
        std::string sid = g_sm_client_ptr->QueryDev(dev_mac);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_STOP_RTSP_REQ);
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
    else if (logic_vrtsp_srv_id_cache == vrtspd_logic_id) //同一台vrtspd服务器,清空生命周期即可
    {
        redis.del(flow_uuid);
        if (!msg.srtp_key().empty()) 
        {
            redis.del(flow_uuid + "_srtp");
        }
        
        //查询mac在哪一台接入服务器上面, 通知设备停止发送视频流
        std::string sid = g_sm_client_ptr->QueryDev(dev_mac);
        AK_LOG_INFO << "stop rtsp, notify csmain: " << sid;
        
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            return;
        }
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_STOP_RTSP_REQ);
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
    else 
    {
        //在不同的vrtspd服务器上面,走vrtspd集群的内部分流协议
        AK_LOG_INFO << "stop rtsp, notify csvrtspd: " << logic_vrtsp_srv_id_cache;
        evpp::TCPConnPtr conn = g_route_ser->GetVrtspConnBySid(logic_vrtsp_srv_id_cache);
        if (!conn)
        {
            AK_LOG_WARN << "get csvrtspd route client conn failed,sid is " << logic_vrtsp_srv_id_cache;
            return;
        }
        
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2V_STOP_RTSP_REQ);//route往设备视频流的源站rtsp服务发送停止分流信令
        pdu2.SetSeqNum(0);
        pdu2.SetTraceId(traceid);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

// 对设备进行抓包
void RouteMQCust::HandleGroupPcapCaptureMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::WebPcapCaptureNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt pcap capture msg:" << msg.DebugString();

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_R2V_PCAP_CAPTURE_REQ);
    pdu2.SetSeqNum(0);
    
    LogicSerConnList csvrtspd_ser_conns = g_route_ser->GetAllVrtspConn();
    for (const auto& conn : csvrtspd_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());//消息透传
    } 
}

// 对sip进行抓包
void RouteMQCust::HandleGroupSipPcapCaptureMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::WebSipPcapCaptureNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt sip pcap capture msg:" << msg.DebugString();

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_R2S_P2P_SIP_PCAP_REQ);
    pdu2.SetSeqNum(0);
    
    LogicSerConnList siphub_ser_conns = g_route_ser->GetAllSiphubConn();
    for (const auto& conn : siphub_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());//消息透传
    } 
}


//csadapt部分
void RouteMQCust::HandleRebootDevMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptRebootDevMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt reboot device msg:" << msg.DebugString();
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_REBOOT_DEVICE);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

void RouteMQCust::HandleResetDevMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptResetDevMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt reset device msg:" << msg.DebugString();
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_RESET_DEVICE);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

void RouteMQCust::HandlePerUpdateNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptPerUpdateNodeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt personal update node msg:" << msg.DebugString();
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_NOTIFY_UPDATE_NODE);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleCommUpdateNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptCommUpdateNodeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt comm update node msg,node is " << msg.node() << ", dev type is " << msg.update_dev_type();
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}
void RouteMQCust::HandlePerDevLogOutSipMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDevLogOutMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt dev log out msg:" << msg.DebugString();
    std::vector<std::string> macs;
    SplitString(msg.macs(), ",", macs);//csadapt是批量删除的,csroute只能拆开一个个转发给csmain
    for (const auto& one_mac : macs)
    {
        std::string sid = g_sm_client_ptr->QueryDev(one_mac);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid << " mac:" << one_mac;
            continue;
        }
        AK::Route::P2PRouteOneDevLogOutMsg one_dev_msg; //route去通知csmain,设备在web上被删除了
        one_dev_msg.set_mac(one_mac);
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&one_dev_msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_DEL_DEV_REQ);//devs->dev
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandlePerUidLogOutSipMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptUidLogOutMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt uid log out msg:" << msg.DebugString();
    std::vector<std::string> uids;
    SplitString(msg.uids(), ",", uids);
    for (const auto& one_uid : uids)
    {
        std::string sid = g_sm_client_ptr->QueryUid(one_uid);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            continue;
        }
        AK::Route::P2PRouteOneUidLogOutMsg one_uid_msg;
        one_uid_msg.set_uid(one_uid);
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&one_uid_msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_DEL_UID_REQ);
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}
//每台csmain都要处理，因为目前是整个node发送的...
void RouteMQCust::HandlePerTextMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptTextMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "HandlePerTextMsg receive csadapt text msg: trace_id=" << pdu->GetTraceId();
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_PER_SEND_TEXT_MSG);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

//csadapt->csroute->csmain
void RouteMQCust::HandleConfFileChangeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleDevAppExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptDevAppExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_DEV_APP_EXPIRE);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
    //发送邮件通知
    AK::Server::GroupAdaptDevAppExpireMsg::GroupAdaptAppExpireInnerMsg inner_msg;
    uint32_t uid_cnt = msg.expire_uid_list_size();
    for (uint32_t i = 0; i < uid_cnt; ++i)
    {
        inner_msg = msg.expire_uid_list(i);
        g_push_kafka->buildMailPushMsg(EMAIL_APP_EXPIRE, &inner_msg);
    }

}

void RouteMQCust::HandleDevAppWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDevAppWillBeExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_DEV_APP_WILLBE_EXPIRE, &msg);
}

void RouteMQCust::HandlePhoneExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPhoneExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PHONE_EXPIRE, &msg);
}

void RouteMQCust::HandlePhoneWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPhoneWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PHONE_WILL_EXPIRE, &msg);
}

void RouteMQCust::HandleInstallerAppWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptInstallerAppWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_INSTALLER_APP_WILL_EXPIRE, &msg);
}

void RouteMQCust::HandleInstallerPhoneWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptInstallerPhoneWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_INSTALLER_PHONE_WILL_EXPIRE, &msg);
}

void RouteMQCust::HandleFreeTrialWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptFreeTrialWillBeExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_FREETRIAL_WILLBE_EXPIRE, &msg);
}

void RouteMQCust::HandlePMFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_FEATURE_WILL_EXPIRE, &msg);
}

void RouteMQCust::HandleInstallerFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_INSTALLER_FEATURE_WILL_EXPIRE, &msg);
}

void RouteMQCust::HandlePMAppAccountWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPMAppAccountWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_APP_ACCOUNT_WILLBE_EXPIRE, &msg);
}

void RouteMQCust::HandlePMAppAccountExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPMAppAccountExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_APP_ACCOUNT_EXPIRE, &msg);
}

void RouteMQCust::HandleDevNotExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptDevNotExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_DEV_NOT_EXPIRE);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleDevCleanDevCodeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDevCleanDeviceCodeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    std::vector<std::string> macs;
    SplitString(msg.macs(), ";", macs);
    for (const auto& one_mac : macs)
    {
        std::string sid = g_sm_client_ptr->QueryUid(one_mac);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            continue;
        }
        AK::Route::P2PAdaptOneDevCleanDeviceCodeMsg one_mac_msg;
        one_mac_msg.set_mac(one_mac);
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&one_mac_msg);//req跟resp内容完全一样
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_R2M_CLEAN_DEV_CODE_REQ);
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }

}
/*
void RouteMQCust::HandleRemoteOpenDoorMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt opendoor msg:" << msg.DebugString();

    // repost_mac不为空,则需要转发消息发送给室内机
    std::string sid = (msg.repost_mac().size() > 0) ? g_sm_client_ptr->QueryDev(msg.repost_mac()) : g_sm_client_ptr->QueryDev(msg.mac());

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(msg.uid(), sip_info);

    evpp::TCPConnPtr conn(nullptr);
    if (sip_info.sip_type == csmain::COMMUNITY_DEV || sip_info.sip_type == csmain::COMMUNITY_APP ||
        sip_info.sip_type == csmain::PERSONNAL_DEV || sip_info.sip_type == csmain::PERSONNAL_APP)
    {
        conn = g_route_ser->GetResidConnBySid(sid);
    }
    else
    {
        conn = g_route_ser->GetOfficeConnBySid(sid);
    }

    if (conn)
    {
        P2PRouteMsg(conn, msg, AKCS_R2B_P2P_REMOTE_OPENDOOR);
    }
    else
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
}

void RouteMQCust::HandleOpenSecurityRelayMsg(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Adapt::OpenSecurityRelayNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt open security relay msg:" << msg.DebugString();

    // repost_mac不为空,则需要转发消息发送给室内机
    std::string sid = (msg.repost_mac().size() > 0) ? g_sm_client_ptr->QueryDev(msg.repost_mac()) : g_sm_client_ptr->QueryDev(msg.mac());

    SipInfo sip_info;
    dbinterface::ProjectUserManage::GetSipInfoBySip(msg.uid(), sip_info);

    evpp::TCPConnPtr conn(nullptr);
    if (sip_info.sip_type == csmain::COMMUNITY_DEV || sip_info.sip_type == csmain::COMMUNITY_APP)
    {
        conn = g_route_ser->GetResidConnBySid(sid);
    }
    else
    {
        conn = g_route_ser->GetOfficeConnBySid(sid);
    }

    if (conn)
    {
        P2PRouteMsg(conn, msg, AKCS_R2B_P2P_REMOTE_OPEN_SECURITY_RELAY);
    }
    else
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
}
*/
void RouteMQCust::HandlePmEmergencyDoorControlMsg(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Adapt::PmEmergencyDoorControlNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt pm emergency door control msg:" << msg.DebugString();

    ACT_OPEN_DOOR_TYPE act_type;
    std::string pm_uuid;
    std::string initiator;
    dbinterface::PmEmergencyDoorLogInfoList info_list;
    dbinterface::PmEmergencyDoorLog::GetPmEmergencyDoorLogByUUID(msg.uuid(), pm_uuid, info_list);
    dbinterface::PropertyInfo::GetFullNameByUUID(pm_uuid, initiator);
  
    if(info_list.empty())
    {
        AK_LOG_INFO << "PmEmergencyDoorControl UUID = " << msg.uuid() << " PmEmergencyDoorLogList is null";
        return;
    }
    
	if(msg.type())
    {
        act_type = ACT_OPEN_DOOR_TYPE::PM_UNLOCK;
    }
    else
    {
        act_type = ACT_OPEN_DOOR_TYPE::PM_LOCK;
    }
    
    for(auto const& info : info_list)
    {	
        std::string sid = g_sm_client_ptr->QueryDevByUUID(info.device_uuid);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            //记录异常doorlog
            AK_LOG_WARN << "get csmain route client conn failed, device is offline ,device_uuid is " << info.device_uuid;
            dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(msg.uuid(), info.device_uuid, dbinterface::RelayStatus::OFFLINE);
            dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(info.device_uuid, initiator, act_type, dbinterface::RelayStatus::OFFLINE, gstAKCSLogDelivery.personal_capture_delivery);
            continue;
        }
        AK::Server::P2PPmEmergencyDoorControlMsg control_msg;
        control_msg.set_msg_uuid(msg.uuid());
        control_msg.set_device_uuid(info.device_uuid);
        control_msg.set_initiator(initiator);
        control_msg.set_auto_manual(OPERATE_TYPE::MANUAL);
        control_msg.set_operation_type(msg.type());
        control_msg.set_relay(info.relay);
        control_msg.set_security_relay(info.security_relay);

        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&control_msg);
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(MSG_C2S_PM_EMERGENCY_DOOR_CONTROL);
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
	}
}

//added by chenyc, 2019-03-06,这条消息的作用就是通知对应的设备再次上报状态,以融入云.
//场景是:installer添加完设备之后,设备可以尽快被平台识别
//installer添加完设备后,web-php会调用:MSG_P2A_PERSONNAL_UPDATE_NODE_DEV + MSG_P2A_PERSONNAL_ADD_DEV
void RouteMQCust::HandleDevReportStatusMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptReportStatusMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt request dev report status msg:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_PER_SEND_REPORT_STATUS);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

//added by chenyc, 2019-03-06,这条消息的作用就是通知对应的csmain修改在内存中的设备配置信息
//场景是:installer修改完设备之后,设备配置信息可以尽快被平台同步修改
//installer添加完设备后,web-php会调用:MSG_P2A_PERSONNAL_UPDATE_NODE_DEV + MSG_P2A_PERSONNAL_MODIFY_DEV

void RouteMQCust::HandleDevChangeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDevChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt dev change msg:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_DEV_CHANGE);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

void RouteMQCust::HandleLinkerCommonMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::P2PRouteLinker msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildLinkerCommonMsg(msg.message_type(), msg.project_type(), msg.msg_json(), msg.key());
}

void RouteMQCust::HandleSendEmailNotifyMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::SendEmailNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "HandleSendEmailNotifyMsg msg";
    
    KafkaProduceMsgPtr email_msg = std::make_shared<KAFKA_PRODUCE_MSG>(KAFKA_MSG_EMAIL, msg.key(), msg.payload());
    g_kafka_producer->ProduceMsg(email_msg);
}

void RouteMQCust::HandleLocalCreateUidMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_CREATE_UID, &msg);
}
void RouteMQCust::HandleLocalResetPwdMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptResetPwdMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_RESET_PWD, &msg);
}
void RouteMQCust::HandleLocalChangePwdMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_CHANGE_PWD, &msg);
}

void RouteMQCust::HandleLocalCheckCodeMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPerCheckCodeMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_CHECK_CODE, &msg);
}

void RouteMQCust::HandleDelAppAccountMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDelAppAccountMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_DELETE_APP_ACCOUNT, &msg);
}

void RouteMQCust::HandleLocalAccountActMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptAccountActInfoMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_ACCOUNT_ACTIVE, &msg);
}

void RouteMQCust:: HandlePmAccountActMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPmAccountActInfoMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_APP_ACCOUNT_ACTIVE, &msg);
}

void RouteMQCust::HandleLocalShareTmpKeyMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptTmpKeyInfoMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_SHARE_TMPKEY, &msg);
}

void RouteMQCust::HandleLocalCreatePropertyWorkMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptCreatePropertyWorkMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_CREATE_PROPERTY_WORK, &msg);
}

void RouteMQCust::HandleSendSmsCreateUidMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::SendSmsCreateUid msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, msg.type());
    }
}

void RouteMQCust::HandleRenewSrvMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptRenewSrvMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    AK::Server::P2PAdaptRenewSrvMsg::P2PAdaptRenewSrvInnerMsg inner_msg;
    uint32_t uid_cnt = msg.renew_srv_uid_list_size();
    for (uint32_t i = 0; i < uid_cnt; ++i)
    {
        inner_msg = msg.renew_srv_uid_list(i);
        g_push_kafka->buildMailPushMsg(EMAIL_RENEW_SERVER, &inner_msg);
    }
}

void RouteMQCust::HandlePmRenewSrvMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPmRenewSrvMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_APP_RENEW_SERVER, &msg);
}

void RouteMQCust::HandlePMMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "receive csadapt pm mail msg";
    AK::Server::P2PAdaptPMAccountWillExpireMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_ACCOUNT_WILLBE_EXPIRE, &msg);
}

void RouteMQCust::HandleGroupAddVideoSchedMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptAddVsSchedMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_ADD_VIDEO_STORAGE_SCHED);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}
void RouteMQCust::HandleGroupDelVideoSchedMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptDelVsSchedMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_DEL_VIDEO_STORAGE_SCHED);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}
void RouteMQCust::HandleLocalDelVideoMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptDelVsMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_vs_client_ptr->DelVideoStorage(msg.video_id());
}

void RouteMQCust::HandleGroupMngTextMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupMngTextMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt text msg";
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ);
    pdu2.SetSeqNum(0);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }
}

void RouteMQCust::HandleP2PAppGetArmingMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainAppHandleArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain app get dev arming req:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteMQCust::HandleP2PAppGetArmingRespMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    /* 这个函数应该用不到了 chenzhx 20250626
    AK::Server::P2PMainAppHandleArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain app get/or indoor set dev arming resp:" << msg.DebugString();
    int oem = msg.oem();
    if (oem == OEMID_ROBERT 
        || oem == OEMID_ROBERT2 
        || msg.home_sync() == ARMING_HOME_SYNC_TYPE_ON 
        || msg.home_sync() == ARMING_HOME_SYNC_TYPE_NOTIFY_OFF_CONFIG 
        || msg.home_sync() == ARMING_HOME_SYNC_TYPE_NOTIFY_ON_CONFIG)
    {
        int resp_action  = msg.resp_action();
        if (resp_action != REPORT_ARMING_ACTION_TYPE_GET)//别的情况广播出去
        {
            LogicSerConnList csmain_ser_conns;
            {
                std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
                csmain_ser_conns = g_route_ser->csmain_ser_conns_;
            }
            for (auto& conn : csmain_ser_conns)
            {
                conn.second->Send(pdu->GetBuffer(), pdu->GetLength());
            }
            return;
        }
    }

    std::string sid = g_sm_client_ptr->QueryUid(msg.main_site());//设备自己设置arming时候 uid为空
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (conn)
    {
        conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
        return;
    }*/

}

void RouteMQCust::HandleP2PAlexaSetArmingReqMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptAlexaSetArmingMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt dev change msg:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_ALEXA_SET_ARMING_MSG);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}


void RouteMQCust::HandleGroupAlexaLoginReqMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptAlexaLoginMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt alexa login msg:" << msg.DebugString();

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_ALEXA_LOGIN_MSG);
    pdu2.SetSeqNum(0);

    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu2.GetBuffer(), pdu2.GetLength());
    }

}

//告警消息通道
int OnRouteMQAlarmMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void MQProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQAlarmMessage);//基本不需要关心
    //client.SetReadyCallback(&OnNSQReady);//ready(与其中一个nsqd服务tcp连接上之后)之后才能开始发布消息.
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}

void RouteMQCust::HandleP2PVisitorAuthorize(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainHandleVisitorAuth msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain visitor authorize msg:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteMQCust::HandleP2PForwardFaceData(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainHandleForwardFaceData msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain forward face data:" << msg.DebugString();

    std::vector<std::string> oMac;
    LogicSerConnList csmain_ser_conns;
    SplitString(msg.mac_list(), "_", oMac);
    for (const auto& mac : oMac)
    {
        std::string sid = g_sm_client_ptr->QueryDev(mac);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            continue;
        }
        csmain_ser_conns[sid] = conn;   //相同可覆盖
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu->GetBuffer(), pdu->GetLength());    //转发人脸数据
    }
}

//远程设备访问
void RouteMQCust::HandleRemoteDevContorl(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptCreateRemoteDevContorlMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt create remote device web contorl msg:" << msg.DebugString();
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_CREATE_REMOTE_DEV_CONTORL);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

/*
void RouteMQCust::HandleP2PDevOpenDoor(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainRequestOpenDoor msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain open door msg:" << msg.DebugString();

    //查询mac在那一台服务器上面, 转发到csresid
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetResidConnBySid(sid);
    
    if (conn)
    {
        // P2PRouteMsg(conn, msg, AKCS_R2B_P2P_FROM_DEVICE_OPENDOOR_REQ);
    }
    else
    {
        AK_LOG_WARN << "get csresid route client conn failed, sid is " << sid;
        return;
    }
}
*/

void RouteMQCust::HandleNotifyFaceServerPicDownloadSingle(const AK::Adapt::FaceServerPicDownloadNotify& msg)
{
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (sid.empty())
    {
        AK_LOG_WARN << "QueryDev failed,mac is " << msg.mac();
        return;
    }
    AK_LOG_INFO << "Mac:[" << msg.mac() << "] Get sid:" << sid;

    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());

    return;

}

void RouteMQCust::HandleNotifyFaceServerPicDownloadMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicDownloadNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt HandleNotifyFaceServerPicDownloadMsg msg:" << msg.DebugString();

    HandleNotifyFaceServerPicDownloadSingle(msg);
    return;
}

void RouteMQCust::HandleNotifyFaceServerPicBatchDownloadMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicDownloadBatchNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt HandleNotifyFaceServerPicBatchDownloadMsg msg:" << msg.DebugString();

    int size = msg.pic_download_list_size();
    for (int i = 0; i < size; i++)
    {
        const AK::Adapt::FaceServerPicDownloadNotify single_notify = msg.pic_download_list(i);
        HandleNotifyFaceServerPicDownloadSingle(single_notify);
    }

    return;
}

void RouteMQCust::HandleNotifyFaceServerPicBatchModifyMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicModifyBatchNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt HandleNotifyFaceServerPicBatchModifyMsg msg:" << msg.DebugString();

    int size = msg.pic_modify_list_size();
    for (int i = 0; i < size; i++)
    {
        const AK::Adapt::FaceServerPicModifyNotify& single_notify = msg.pic_modify_list(i);
        HandleNotifyFaceServerPicModifySingle(single_notify);
    }
    return;
}

void RouteMQCust::HandleNotifyFaceServerPicModifySingle(const AK::Adapt::FaceServerPicModifyNotify& msg)
{
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (sid.empty())
    {
        AK_LOG_WARN << "QueryDev failed,mac is " << msg.mac();
        return;
    }
    AK_LOG_INFO << "Mac:[" << msg.mac() << "] Get sid:" << sid;

    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());

    return;
}

void RouteMQCust::HandleNotifyFaceServerPicModifyMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt HandleNotifyFaceServerPicModifyMsg msg:" << msg.DebugString();

    HandleNotifyFaceServerPicModifySingle(msg);
    return;
}

void RouteMQCust::HandleNotifyFaceServerPicBatchDeleteMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicDeleteBatchNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt HandleNotifyFaceServerPicBatchDeleteMsg msg:" << msg.DebugString();

    int size = msg.pic_delete_list_size();
    for (int i = 0; i < size; i++)
    {
        const AK::Adapt::FaceServerPicDeleteNotify& single_notify = msg.pic_delete_list(i);
        HandleNotifyFaceServerPicDeleteSingle(single_notify);
    }
    return;
}

void RouteMQCust::HandleNotifyFaceServerPicDeleteSingle(const AK::Adapt::FaceServerPicDeleteNotify& msg)
{
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (sid.empty())
    {
        AK_LOG_WARN << "QueryDev failed,mac is " << msg.mac();
        return;
    }
    AK_LOG_INFO << "Mac:[" << msg.mac() << "] Get sid:" << sid;

    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());

    return;

}

void RouteMQCust::HandleNotifyFaceServerPicDeleteMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::FaceServerPicDeleteNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt HandleNotifyFaceServerPicDeleteMsg msg:" << msg.DebugString();

    HandleNotifyFaceServerPicDeleteSingle(msg);
    return;
}

void RouteMQCust::HandleSendSmsCodeMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::SendSmsCode msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, msg.type());
    }

}

void RouteMQCust::HandleSendDelAppAccountSmsCodeMsg(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Server::SendSmsCodeDelAppAccount msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, msg.type());
    }

}

void RouteMQCust::HandleSendRemindOutOfFlow(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Server::SendSmsRemindFlowOutofLimit msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, msg.type());
    }
}

void RouteMQCust::HandleSendVoiceMsg(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PSendVoiceMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain send voice msg:" << msg.DebugString();
    std::string sid;
    //只有办公在用该接口
    if (msg.receiver_type() == DEVICE_TYPE_INDOOR)
    {
        //查询mac在那一台服务器上面
        sid = g_sm_client_ptr->QueryDevByUUID(msg.receiver_uuid());
    }
    else
    {
        sid = g_sm_client_ptr->QueryUid(msg.main_site());
    }


    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    P2PRouteMsg(conn, msg, AKCS_M2R_P2P_SEND_VOICE_MSG);
}

void RouteMQCust::HandleP2PDevSendDelivery(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainSendDelivery msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain send delivery msg:" << msg.DebugString();   
    std::vector<std::string> account_list;
    account_list.push_back(msg.account());
    if(DELIVERY_BOX_MSG == msg.type())
    {
        PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
        if (push_cli_ptr)
        {
            push_cli_ptr->buildSmsPushMsg(&msg, SmsType::SMS_DELIVERY_BOX);
        }            
        GetPersonalAccountInstance()->DaoGetFamilyMemberByMaster(msg.account(), account_list);
    }
    else
    {
        PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
        if (push_cli_ptr)
        {
            push_cli_ptr->buildSmsPushMsg(&msg, SmsType::SMS_DELIVERY);
        }        
    }
    
    //查询account在那一台服务器上面   
    for(const auto& account : account_list)
    {
        //6.6一人多套房修改，新增主站点字段
        std::string main_site;
        dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(account, main_site);
        std::string sid = g_sm_client_ptr->QueryUid(main_site);
        evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
        if (!conn)
        {
            AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
            return;
        }

        msg.set_account(account);
        CAkcsPdu pdu2;
        pdu2.SetMsgBody(&msg);
        pdu2.SetHeadLen(sizeof(PduHeader_t));
        pdu2.SetVersion(50);
        pdu2.SetCommandId(AKCS_M2R_P2P_SEND_DELIVERY_REQ);
        pdu2.SetSeqNum(0);
        conn->Send(pdu2.GetBuffer(), pdu2.GetLength());//消息id不变
    }
}

void RouteMQCust::HandleP2PDevSendTmpkeyUsed(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainSendTmpkeyUsed msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain send tmpkey msg:" << msg.DebugString();   
    
    //查询主站点在那一台服务器上面   
    std::string sid = g_sm_client_ptr->QueryUid(msg.main_site());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteMQCust::HandleP2PChangeRelay(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PMainChangeRelay msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csmain change " << msg.mac() << " relay";
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());
}

void RouteMQCust::HandlePmExportLogMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::PmExportLog msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildKafkaPushMsg(KAFKA_MSG_PM_EXPORT_LOG, &msg);
}

void RouteMQCust::HandleNotifyRefreshConnCache(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptNotifyRefreshConnCache msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt notify refresh conn cache";

    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
        csmain_ser_conns = g_route_ser->csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        conn.second->Send(pdu->GetBuffer(), pdu->GetLength());
    }
}

void RouteMQCust::HandleNotifyChangeMainSite(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptNotifyChangeMainSite msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt notify change main site";

    std::string sid = g_sm_client_ptr->QueryUid(msg.before_main_site());
    g_sm_client_ptr->RemoveUid(msg.before_main_site(), sid);
    //更新站点注册
    std::string uuid;
    dbinterface::ResidentPersonalAccount::GetUUIDByAccount(msg.before_main_site(), uuid);
    g_sm_client_ptr->RegUid(msg.after_main_site(), sid, uuid);
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (conn)
    {
        conn->Send(pdu->GetBuffer(), pdu->GetLength());
        return;
    }
}


void RouteMQCust::HandleNotifyConfigUpdate(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Adapt::DevConfigUpdateNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt notify config update, msg:" << msg.DebugString();

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (sid.empty())
    {
        AK_LOG_WARN << "QueryDev failed,mac is " << msg.mac();
        return;
    }

    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());
}

void RouteMQCust::HandleNotifyDoorControl(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt notify door control, msg:" << msg.DebugString();

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (sid.empty())
    {
        AK_LOG_WARN << "QueryDev failed,mac is " << msg.mac();
        return;
    }

    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());
}

void RouteMQCust::HandleNotifyDevFileChange(const std::shared_ptr<CAkcsPdu> &pdu)
{
    AK::Server::P2PAdaptNotifyFileChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt notify file change, msg:" << msg.DebugString();

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (sid.empty())
    {
        AK_LOG_WARN << "QueryDev failed,mac is " << msg.mac();
        return;
    }

    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());
}

void RouteMQCust::P2PRouteMsg(const evpp::TCPConnPtr &conn, const google::protobuf::MessageLite &msg, uint32_t command_id)
{
    if (!conn)
    {
        AK_LOG_WARN << "P2PRouteMsg get csmain route client conn failed";
        return;
    }

    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(command_id);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

//离线压缩包处理完成
void RouteMQCust::HandleStorageOfflineAckMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PStorageHandleOfflineAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive storage dev offline resend msg handle ok, msg:" << msg.DebugString();
    //查询mac在那一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }
    CAkcsPdu pdu2;
    pdu2.SetMsgBody(&msg);//req跟resp内容完全一样
    pdu2.SetHeadLen(sizeof(PduHeader_t));
    pdu2.SetVersion(50);
    pdu2.SetCommandId(AKCS_R2S_P2P_OFFLINE_MSG_ACK_REQ);
    pdu2.SetSeqNum(0);
    conn->Send(pdu2.GetBuffer(), pdu2.GetLength());
}

void RouteMQCust::HandleStorageVoiceAckMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive storage dev voice resend msg handle ok, msg:" << msg.DebugString();
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    if (msg.project_type() == project::RESIDENCE || msg.project_type() == project::PERSONAL)
    {
        evpp::TCPConnPtr conn = g_route_ser->GetResidConnBySid(sid);
        if (conn)
        {   
            P2PRouteMsg(conn, msg, AKCS_R2S_P2P_VOICE_MSG_ACK_REQ);
        }
    }
    else
    {
        evpp::TCPConnPtr conn = g_route_ser->GetOfficeConnBySid(sid);
        if (conn)
        {   
            P2PRouteMsg(conn, msg, AKCS_R2S_P2P_VOICE_MSG_ACK_REQ);
        }
    }
}

void RouteMQCust::HandleP2PDevWeatherMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::LinkerWeatherNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    
    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr resid_conn = g_route_ser->GetResidConnBySid(sid);
    if (resid_conn)
    {   
        P2PRouteMsg(resid_conn, msg, AKCS_R2B_P2P_WEATHER_INFO_RESP);
    }
}

void RouteMQCust::HandleP2PPacportUnlockMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::LinkerPacportCheckNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr resid_conn = g_route_ser->GetResidConnBySid(sid);
    if (resid_conn)
    {   
        P2PRouteMsg(resid_conn, msg, AKCS_R2B_P2P_PACPORT_UNLOCK_RESP);
    }
}

void RouteMQCust::HandleP2PDevCommonAckMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Linker::LinkerDevCommonAck msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "HandleP2PDevCommonAckMsg, msg = " << msg.DebugString();

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr resid_conn = g_route_ser->GetResidConnBySid(sid);
    if (resid_conn)
    {   
        P2PRouteMsg(resid_conn, msg, AKCS_R2B_P2P_DEV_COMMON_ACK);
    }
}

void RouteMQCust::HandleSendUserAddNewSiteMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendUserAddNewSite msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_ADD_NEW_SITE, &msg);
}

void RouteMQCust::HandleSendPmWebLinkNewSitesMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendPmWebLinkNewSites msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_LINK_NEW_SITES, &msg);
}

void RouteMQCust::HandleSendPmWebCreateUidMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_WEB_CREATE_UID, &msg);
}

void RouteMQCust::HandleSendPmWebChangeUidMailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_PM_WEB_CHANGE_PWD, &msg);
}

void RouteMQCust::HandleSendCodeToEmailMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendCodeToEmail msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    g_push_kafka->buildMailPushMsg(EMAIL_COMMON_SEND_CODE, &msg);
}

void RouteMQCust::HandleSendCodeToMobileMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendCodeToMobile msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    PushClientPtr push_cli_ptr = CPushClientMng::Instance()->GetPushSrv();
    if (push_cli_ptr)
    {
        push_cli_ptr->buildSmsPushMsg(&msg, SMS_COMMON_SEND_CODE);
    }
}

void RouteMQCust::HandleP2PBackendMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::BackendCommon::BackendP2PBaseMessage msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive request trans msg:" << msg.DebugString();

    std::string sid;
    evpp::TCPConnPtr conn = nullptr;
    if (msg.project_type() == project::RESIDENCE || msg.project_type() == project::PERSONAL)
    {
        if (msg.type() == TransP2PMsgType::TO_DEV_UUID)
        {
            sid = g_sm_client_ptr->QueryDevByUUID(msg.uid());
            conn = g_route_ser->GetResidConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryDevByUUID: sid is " << sid;
                return;
            }
        }
        else if (msg.type() == TransP2PMsgType::TO_DEV_MAC)
        {
            sid = g_sm_client_ptr->QueryDev(msg.uid());
            conn = g_route_ser->GetResidConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryDev: sid is " << sid;
                return;
            }
        }
        else if (msg.type() == TransP2PMsgType::TO_APP_UUID)
        {
            // 一人多套房转换
            std::string main_account_uuid;
            if(dbinterface::PersonalAccountUserInfo::GetMainAccountByAccountUUID(msg.uid(), main_account_uuid) != 0)
            {
                main_account_uuid = msg.uid();
            }

            sid = g_sm_client_ptr->QueryAccountUUID(main_account_uuid);
            conn = g_route_ser->GetResidConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryAccountUUID: sid is " << sid;
                return;
            }
        }
        else if (msg.type() == TransP2PMsgType::TO_APP_UID || msg.type() == TransP2PMsgType::TO_APP_UID_ONLINE)
        {
            // 一人多套房转换
            std::string main_account;
            if(dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(msg.uid(), main_account) != 0)
            {
                main_account = msg.uid();
            }
            sid = g_sm_client_ptr->QueryUid(main_account);
            conn = g_route_ser->GetResidConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryUid:sid is " << sid;
                return;
            }
        }
        //广播
        else if(msg.type() == TransP2PMsgType::TO_ALL_APP)
        {            
            LogicSerConnList resid_ser_conns;
            {
                std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
                resid_ser_conns = g_route_ser->csresid_ser_conns_;
            }
            for (auto& conn : resid_ser_conns)
            {
                conn.second->Send(pdu->GetBuffer(), pdu->GetLength());
            }
            return;
        }

        if (conn)
        {   
            conn->Send(pdu->GetBuffer(), pdu->GetLength());
        }

    }
    else if  (msg.project_type() == project::OFFICE || msg.project_type() == project::OFFICE_NEW)
    {
        if (msg.type() == TransP2PMsgType::TO_DEV_UUID)
        {
            sid = g_sm_client_ptr->QueryDevByUUID(msg.uid());
            conn = g_route_ser->GetOfficeConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryDevByUUID: sid is " << sid;
                return;
            }        
        }
        else if (msg.type() == TransP2PMsgType::TO_DEV_MAC)
        {
            sid = g_sm_client_ptr->QueryDev(msg.uid());
            conn = g_route_ser->GetOfficeConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryDev: sid is " << sid;
                return;
            }            
        }
        else if (msg.type() == TransP2PMsgType::TO_APP_UUID)
        {

            // 一人多套房转换
            std::string main_account_uuid;
            if(dbinterface::PersonalAccountUserInfo::GetMainAccountByAccountUUID(msg.uid(), main_account_uuid) != 0)
            {
                main_account_uuid = msg.uid();
            }
        
            sid = g_sm_client_ptr->QueryAccountUUID(main_account_uuid);
            conn = g_route_ser->GetOfficeConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryAccountUUID: sid is " << sid;
                return;
            }
        }
        else if (msg.type() == TransP2PMsgType::TO_APP_UID || msg.type() == TransP2PMsgType::TO_APP_UID_ONLINE)
        {
            // 一人多套房转换
            std::string main_account;
            if(dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(msg.uid(), main_account) != 0)
            {
                main_account = msg.uid();
            }        
            sid = g_sm_client_ptr->QueryUid(main_account);
            conn = g_route_ser->GetOfficeConnBySid(sid);
            if (!conn)
            {
                AK_LOG_WARN << "get csmain route client conn failed,QueryUid:sid is " << sid;
                return;
            }
        
        }        
        //广播
        else if(msg.type() == TransP2PMsgType::TO_ALL_APP)
        {            
            LogicSerConnList office_ser_conns;
            {
                std::lock_guard<std::mutex> lock(g_route_ser->csmain_mutex_);
                office_ser_conns = g_route_ser->csoffice_ser_conns_;
            }
            for (auto& conn : office_ser_conns)
            {
                conn.second->Send(pdu->GetBuffer(), pdu->GetLength());
            }
            return;
        }
        
        if (conn)
        {   
            conn->Send(pdu->GetBuffer(), pdu->GetLength());
        }
    }
    else 
    {
        AK_LOG_WARN << "project_type is unknow is: " << msg.project_type();
        return;
    }
}


void RouteMQCust::HandleDevDelLogMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PSendRequestDevDelLog msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[HandleDevDelLogMsg] receive msg:" << msg.DebugString();

    std::string sid = g_sm_client_ptr->QueryDev(msg.mac());
    evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
    if (!conn)
    {
        AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
        return;
    }

    conn->Send(pdu->GetBuffer(), pdu->GetLength());
}

//通知app刷新userconf
void RouteMQCust::HandleNotifyAppChangeConfMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptNotifyAppRefreshConfigMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[HandleNotifyAppChangeConfMsg] receive msg:" << msg.DebugString();   

    std::string sid;
    evpp::TCPConnPtr conn = nullptr;
    std::string main_site;
    dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(msg.account(), main_site);

    if (msg.project_type() == project::RESIDENCE || msg.project_type() == project::PERSONAL)
    {
        sid = g_sm_client_ptr->QueryUid(main_site);
        conn = g_route_ser->GetResidConnBySid(sid);
        P2PRouteMsg(conn, msg, AKCS_R2B_P2P_REFRESH_APP_USERCONF);
    }
}



void RouteMQCust::HandlePushWebCommonMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PRouteToWebMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    g_push_kafka->buildNotifyWebCommonMsg(msg.message_type(), msg.msg_json());
}

void RtspRtpInterceptTest(const AK::Route::StartRtspReq &msg)
{
    AK_LOG_WARN << "Now we are in RtspRtpInterceptTest environment... ...";
    CAkcsPdu pdu_tmp;
    pdu_tmp.SetMsgBody(&msg);
    RtspRtpIntercept(pdu_tmp.GetBodyData(), pdu_tmp.GetBodyLength());
}

//通知刷新设备IsAttendance
void RouteMQCust::HandleNotifyDeviceIsAttendanceMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::P2PAdaptNotifyDeviceIsAttendanceMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "[HandleNotifyDeviceIsAttendanceMsg] receive msg:" << msg.DebugString();   

    //查询mac在哪一台服务器上面
    std::string sid = g_sm_client_ptr->QueryDevByUUID(msg.device_uuid());
    evpp::TCPConnPtr conn = g_route_ser->GetOfficeConnBySid(sid);
    P2PRouteMsg(conn, msg, AKCS_R2B_P2P_REFRESH_DEVICE_IS_ATTENDANCE);
}

void RouteMQCust::HandlePushSmartlockConfigurationMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csroute->cssmartlock HandlePushSmartlockConfigurationMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCssmartlockConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csadapt route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}

void RouteMQCust::HandleSmartlockP2PMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK_LOG_INFO << "csroute->cssmartlock HandleSmartlockP2PMsg MsgID=" << pdu->GetCommandId();
    evpp::TCPConnPtr conn = g_route_ser->GetCssmartlockConn();
    if (!conn)
    {
        AK_LOG_WARN << "get csadapt route client conn failed";
        return;
    }
    conn->Send(pdu->GetBuffer(), pdu->GetLength());//消息id不变
}