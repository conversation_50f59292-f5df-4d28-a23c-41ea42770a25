#ifndef __CSROUTE_MQ_H__
#define __CSROUTE_MQ_H__

#include <memory>
#include <thread>
#include <mutex>
#include <list>
#include <condition_variable>
#include <evpp/tcp_conn.h>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <evpp/evnsq/message.h>
#include "AK.Adapt.pb.h"
#include "AK.Route.pb.h"

//const evnsq::Message* msg的body承载的是protobuf消息,且客户端已经保证了是一条完整的消息了
//| package length   | true  | int32 bigendian | 包长度   |
//| header Length    | true  | int16 bigendian | 包头长度 |
//| ver              | true  | int16 bigendian | 协议版本 |
//| id               | true  | int32 bigendian | 协议指令 |
//| seq              | true  | int32 bigendian | 序列号      |
//| body             | false | binary          | 具体消息 |
int OnRouteMQMessage(const evnsq::Message* msg);
void OnConnectError(const std::string& addr);

//typedef std::function<void(const evpp::TCPConnPtr&, const std::string&)> StringMessageCallback;

static const uint32_t kSipLen = 32;
struct SipCall
{
    char calling_sip[kSipLen];
    char called_sip[kSipLen];
};
enum AppStatus
{
    kOffline = 0,
    kOnline = 1,
};

class RouteMQCust
{
public:
    RouteMQCust() {}
    ~RouteMQCust() {}

public:
    static RouteMQCust* GetInstance();
    void Init();
    void InitNsqConsumer();
    void ProcessPduMsg();
    int GetRoutePduSize();
    void AddMessage(const std::shared_ptr<CAkcsPdu>& pdu);
    //message已经是一条完整的消息了
    void OnMessage(const std::shared_ptr<CAkcsPdu>& pdu);
    // 添加延迟指标
    void AddLatencyMetric(const std::shared_ptr<CAkcsPdu>& pdu);
private:
    //csmain部分
    void HandleGroupCommAlarmMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupCommAlarmDealMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupPerAlarmMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupPerAlarmDealMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupPerMotionMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PRtspCaputreMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PReportToAdaptMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PReportToConfigMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PReportToConfigNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PAppGetArmingMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PAppGetArmingRespMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupMngTextMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PVisitorAuthorize(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PForwardFaceData(const std::shared_ptr<CAkcsPdu>& pdu);
    //vrtsp部分
    void HandleP2PStartRtspMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PStopRtspMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PRtspKeepAliveMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //pbx部分,在tcp连接处交互,后续需要全部改到这里来
    //void HandleP2PWakeUpAppMsg(const std::unique_ptr<CAkcsPdu>& pdu);
    //csadapt部分
    void HandleRebootDevMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePerUpdateNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleCommUpdateNodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePerDevLogOutSipMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePerUidLogOutSipMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePerTextMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleConfFileChangeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevAppExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevAppWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleFreeTrialWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevNotExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevCleanDevCodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevReportStatusMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevChangeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalCreateUidMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalResetPwdMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalChangePwdMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalCheckCodeMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDelAppAccountMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupAddVideoSchedMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleGroupDelVideoSchedMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalDelVideoMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    // void HandleRemoteOpenDoorMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    // void HandleOpenSecurityRelayMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePmEmergencyDoorControlMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalAccountActMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalShareTmpKeyMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleLocalCreatePropertyWorkMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleRenewSrvMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePmRenewSrvMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePmAccountActMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandleGroupAlexaLoginReqMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PAlexaSetArmingReqMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandlePhoneExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePhoneWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleInstallerAppWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleInstallerPhoneWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //v5.2
    void HandleRemoteDevContorl(const std::shared_ptr<CAkcsPdu>& pdu);
    // void HandleP2PDevOpenDoor(const std::shared_ptr<CAkcsPdu>& pdu);

    //v6.2
    void HandlePMFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleInstallerFeatureWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    // 发消息通知rtsp抓包
    void HandleGroupPcapCaptureMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    
    //发送短信验证码
    void HandleSendSmsCodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandleSendDelAppAccountSmsCodeMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendRemindOutOfFlow(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandlePmExportLogMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    //faceserver部分
    //发消息通知AKCS让设备去下载图片
    void HandleNotifyFaceServerPicDownloadMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyFaceServerPicDownloadSingle(const AK::Adapt::FaceServerPicDownloadNotify& msg);
    //发消息通知AKCS让多台设备去下载图片
    void HandleNotifyFaceServerPicBatchDownloadMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //发消息通知AKCS让设备修改人脸数据
    void HandleNotifyFaceServerPicModifyMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyFaceServerPicModifySingle(const AK::Adapt::FaceServerPicModifyNotify& msg);
    //发消息通知AKCS让多台设备修改人脸数据
    void HandleNotifyFaceServerPicBatchModifyMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //发消息通知AKCS让设备删除人脸数据
    void HandleNotifyFaceServerPicBatchDeleteMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //发消息通知AKCS让设备删除人脸数据
    void HandleNotifyFaceServerPicDeleteMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyFaceServerPicDeleteSingle(const AK::Adapt::FaceServerPicDeleteNotify& msg);
    void HandleP2PDevSendDelivery(const std::shared_ptr<CAkcsPdu>& pdu);    
    void HandleP2PDevSendTmpkeyUsed(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyRefreshConnCache(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyConfigUpdate(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyDoorControl(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PChangeRelay(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyDevFileChange(const std::shared_ptr<CAkcsPdu> &pdu);
    //v6.4
    void HandleSendSmsCreateUidMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMAppAccountWillExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePMAppAccountExpireMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //csstorage
    void HandleStorageOfflineAckMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    //6.5
    void HandleSendVoiceMsg(const std::shared_ptr<CAkcsPdu> &pdu);
    //cslinker
    void HandleLinkerCommonMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PDevWeatherMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleStorageVoiceAckMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PBackendMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleResetDevMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendUserAddNewSiteMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendPmWebLinkNewSitesMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendPmWebCreateUidMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendPmWebChangeUidMailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendCodeToEmailMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendCodeToMobileMsg(const std::shared_ptr<CAkcsPdu>& pdu);    
    void HandleNotifyChangeMainSite(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleSendEmailNotifyMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleDevDelLogMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PPacportUnlockMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    // sip抓包
    void HandleGroupSipPcapCaptureMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyAppChangeConfMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleP2PDevCommonAckMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePushWebCommonMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNewNotifyMessage(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandleNotifyDeviceIsAttendanceMsg(const std::shared_ptr<CAkcsPdu>& pdu);
    void HandlePushSmartlockConfigurationMsg(const std::shared_ptr<CAkcsPdu>& pdu);

private:
    void P2PRouteMsg(const evpp::TCPConnPtr &conn, const google::protobuf::MessageLite &msg, uint32_t command_id);
    std::list<std::shared_ptr<CAkcsPdu> > msg_pdus_;
    std::mutex msg_pdus_mtx_;
    std::condition_variable msg_pdus_cv_;
    static RouteMQCust* instance_;
    std::list<std::unique_ptr<std::thread>> process_thread_list;

};

//告警消息通道
void MQProduceInit();
//added by chenyc,2024-02-19,集成测试框架的处理函数
void RtspRtpInterceptTest(const AK::Route::StartRtspReq &msg);

#endif //__CSROUTE_MQ_H__

