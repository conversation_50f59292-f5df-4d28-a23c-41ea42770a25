#include "stdlib.h"
#include <functional>
#include <memory>
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "route_mq.h"
#include "route_server.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "session_rpc_client.h"
#include "SafeCacheConn.h"
#include "ak_user_mng.h"
#include "ak_dev_mng.h"

RouteServer* g_route_ser = nullptr;
extern SmRpcClient* g_sm_client_ptr;

const char RouteServer::app_status_seq[] = "app_status_seq";

void startRouteServer()
{
    evpp::EventLoop route_loop;
    //modified by chenyc,2019-05-17,监听在内网
    std::string inner_addr = GetEth0IPAddr();
    inner_addr += ":8500";//csroute tcp 监听端口
    g_route_ser = new RouteServer(&route_loop, inner_addr, "RouteServer", 1);
    g_route_ser->Start();
    route_loop.Run();
}

RouteServer::RouteServer(evpp::EventLoop* loop, const std::string& addr, const std::string& name, uint32_t thread_num)
    : server_(loop, addr, name, thread_num)
    , codec_(std::bind(&RouteServer::OnStringMessage, this, std::placeholders::_1, std::placeholders::_2))
    , loop_(loop)
{
    server_.SetConnectionCallback(
        std::bind(&RouteServer::OnConnection, this, std::placeholders::_1));

    //tcp应用层有消息的时候,就调用该函数
    server_.SetMessageCallback(
        std::bind(&AkcsIpcMsgCodec2::OnMessage, &codec_, std::placeholders::_1, std::placeholders::_2));
}

void RouteServer::Start()
{
    server_.Init();
    server_.Start();
    //5分钟后启动ping
    loop_->RunEvery(evpp::Duration(60.0), std::bind(&RouteServer::PingLogicSrv, this));
}

void RouteServer::OnConnection(const evpp::TCPConnPtr& conn)
{
    AK_LOG_INFO << conn->AddrToString() << " is " << (conn->IsConnected() ? "UP" : "DOWN");
    if (conn->IsConnected())
    {
        AK::Route::P2PRoutePingPong msg;
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_MSG_R2L_PING_REQ);
        pdu.SetSeqNum(0);
        conn->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    else//断开的时候,清除掉逻辑服务器的连接信息
    {
        if (conn->context(0).IsEmpty() || conn->context(1).IsEmpty()) //证明对应的逻辑服务器还没有上报过状态
        {
            return;
        }
        evpp::Any sid_any = conn->context(0);
        std::string sid = evpp::any_cast<std::string>(sid_any);
        evpp::Any srv_type_any = conn->context(1);
        AK::Base::LogicClientType srv_type = evpp::any_cast<AK::Base::LogicClientType>(srv_type_any);
        if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_MAIN)
        {
            std::lock_guard<std::mutex> lock(csmain_mutex_);
            //还需要比较conn,因为同一时刻，可能会存在同一个sid，但是又多个conn的情况,也就是断开、连接的时刻在网络层回调时发生交叉
            if (conn == csmain_ser_conns_[sid])
            {
                csmain_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_VRTSP)
        {
            std::lock_guard<std::mutex> lock(csvrtspd_mutex_);
            if (conn == csvrtspd_ser_conns_[sid])
            {
                csvrtspd_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_ADAPT)
        {
            std::lock_guard<std::mutex> lock(csadapt_mutex_);
            if (conn == csadapt_ser_conns_[sid])
            {
                csadapt_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_CONFIG)
        {
            std::lock_guard<std::mutex> lock(csconfig_mutex_);
            if (conn == csconfig_ser_conns_[sid])
            {
                csconfig_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_RESID)
        {
            std::lock_guard<std::mutex> lock(csresid_mutex_);
            if (conn == csresid_ser_conns_[sid])
            {
                csresid_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_OFFICE)
        {
            std::lock_guard<std::mutex> lock(csoffice_mutex_);
            if (conn == csoffice_ser_conns_[sid])
            {
                csoffice_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_SIPHUB)
        {
            std::lock_guard<std::mutex> lock(siphub_mutex_);
            if (conn == siphub_ser_conns_[sid])
            {
                siphub_ser_conns_.erase(sid);
            }
        }
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_CONFIG_OFFICE)
        {
            {
                std::lock_guard<std::mutex> lock(csconfig_office_mutex_);
                if (conn == csconfig_office_ser_conns_[sid])
                {
                    csconfig_office_ser_conns_.erase(sid);
                    csconfig_office_srv_set_.erase(sid);
                }
            }
            CLogicSrvMng::Instance()->UpdateConfigOfficeSrvList(csconfig_office_srv_set_);
        }    
        else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_SMARTLOCK)
        {
            std::lock_guard<std::mutex> lock(cssmartlock_mutex_);
            if (conn == cssmartlock_ser_conns_[sid])
            {
                cssmartlock_ser_conns_.erase(sid);
            }
        }
    }
}

const evpp::TCPConnPtr RouteServer::GetMainConnBySid(const std::string& sid)
{
    evpp::TCPConnPtr conn;
    std::lock_guard<std::mutex> lock(csmain_mutex_);
    LogicSerConnIter iter = csmain_ser_conns_.find(sid);
    if (iter != csmain_ser_conns_.end())
    {
        conn = iter->second;
    }
    return conn;
}

const evpp::TCPConnPtr RouteServer::GetVrtspConnBySid(const std::string& sid)
{
    evpp::TCPConnPtr conn;
    std::lock_guard<std::mutex> lock(csvrtspd_mutex_);
    LogicSerConnIter iter = csvrtspd_ser_conns_.find(sid);
    if (iter != csvrtspd_ser_conns_.end())
    {
        conn = iter->second;
    }
    return conn;
}

const LogicSerConnList RouteServer::GetAllVrtspConn()
{
    std::lock_guard<std::mutex> lock(csvrtspd_mutex_);
    return csvrtspd_ser_conns_;
}

const LogicSerConnList RouteServer::GetAllSiphubConn()
{
    std::lock_guard<std::mutex> lock(siphub_mutex_);
    return siphub_ser_conns_;
}

const evpp::TCPConnPtr RouteServer::GetAdaptConn()
{
    evpp::TCPConnPtr conn;
    if (csadapt_ser_conns_.size() > 0)
    {
        conn = csadapt_ser_conns_.begin()->second;
    }
    return conn;
}

const evpp::TCPConnPtr RouteServer::GetCsconfigConn()
{
    evpp::TCPConnPtr conn;
    if (csconfig_ser_conns_.size() > 0)
    {
        conn = csconfig_ser_conns_.begin()->second;
    }
    return conn;
}

const evpp::TCPConnPtr RouteServer::GetCsconfigOfficeConn(const std::string &key_hash)
{
    std::string sid = CLogicSrvMng::Instance()->GetConfigOfficeSrv(key_hash);
    evpp::TCPConnPtr conn;
    std::lock_guard<std::mutex> lock(csconfig_office_mutex_);
    LogicSerConnIter iter = csconfig_office_ser_conns_.find(sid);
    if (iter != csconfig_office_ser_conns_.end())
    {
        conn = iter->second;
    }
    return conn;
}

const evpp::TCPConnPtr RouteServer::GetCsconfigOfficeConn()
{
    std::string key_hash = "ABCDEF1234567890";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::shuffle(key_hash.begin(),key_hash.end(),gen);    

    std::string sid = CLogicSrvMng::Instance()->GetConfigOfficeSrv(key_hash);
    evpp::TCPConnPtr conn;
    std::lock_guard<std::mutex> lock(csconfig_office_mutex_);
    LogicSerConnIter iter = csconfig_office_ser_conns_.find(sid);
    if (iter != csconfig_office_ser_conns_.end())
    {
        conn = iter->second;
    }
    return conn;
}

const evpp::TCPConnPtr RouteServer::GetCssmartlockConn()
{
    evpp::TCPConnPtr conn;
    if (cssmartlock_ser_conns_.size() > 0)
    {
        conn = cssmartlock_ser_conns_.begin()->second;
    }
    return conn;
}


const evpp::TCPConnPtr RouteServer::GetResidConnBySid(const std::string& sid)
{
    evpp::TCPConnPtr conn;
    std::lock_guard<std::mutex> lock(csresid_mutex_);
    LogicSerConnIter iter = csresid_ser_conns_.find(sid);
    if (iter != csresid_ser_conns_.end())
    {
        conn = iter->second;
    }
    return conn;
}

const evpp::TCPConnPtr RouteServer::GetOfficeConnBySid(const std::string& sid)
{
    evpp::TCPConnPtr conn;
    std::lock_guard<std::mutex> lock(csoffice_mutex_);
    LogicSerConnIter iter = csoffice_ser_conns_.find(sid);
    if (iter != csoffice_ser_conns_.end())
    {
        conn = iter->second;
    }
    return conn;
}

void RouteServer::PingLogicSrv()
{
    std::string inner_addr = GetEth0IPAddr();
    std::string logic_srv_id = std::string("route_") + inner_addr;
    AK::Route::P2PRoutePingPong msg;
    msg.set_logic_srv_id(logic_srv_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_MSG_R2L_PING_REQ);
    pdu.SetSeqNum(0);
    AK_LOG_INFO << "Ping logicsrv";
    //依次发送ping
    //evpp::Any is_received_pong(false);
    LogicSerConnList csmain_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csmain_mutex_);
        csmain_ser_conns = csmain_ser_conns_;
    }
    for (auto& conn : csmain_ser_conns)
    {
        //conn->set_context(2, is_received_pong);
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    LogicSerConnList csvrtspd_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csvrtspd_mutex_);
        csvrtspd_ser_conns = csvrtspd_ser_conns_;
    }
    for (auto& conn : csvrtspd_ser_conns)
    {
        //conn->set_context(2, is_received_pong);
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    LogicSerConnList csadapt_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csadapt_mutex_);
        csadapt_ser_conns = csadapt_ser_conns_;
    }
    for (auto& conn : csadapt_ser_conns)
    {
        //conn->set_context(2, is_received_pong);
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    LogicSerConnList csconfig_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csconfig_mutex_);
        csconfig_ser_conns = csconfig_ser_conns_;
    }
    for (auto& conn : csconfig_ser_conns)
    {
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }

    LogicSerConnList csresid_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csresid_mutex_);
        csresid_ser_conns = csresid_ser_conns_;
    }
    for (auto& conn : csresid_ser_conns)
    {
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }

    LogicSerConnList csoffice_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csoffice_mutex_);
        csoffice_ser_conns = csoffice_ser_conns_;
    }
    for (auto& conn : csoffice_ser_conns)
    {
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }

    LogicSerConnList siphub_ser_conns;
    {
        std::lock_guard<std::mutex> lock(siphub_mutex_);
        siphub_ser_conns = siphub_ser_conns_;
    }
    for (auto& conn : siphub_ser_conns)
    {
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    
    LogicSerConnList csconfig_office_ser_conns;
    {
        std::lock_guard<std::mutex> lock(csconfig_office_mutex_);
        csconfig_office_ser_conns = csconfig_office_ser_conns_;
    }    
    for (auto& conn : csconfig_office_ser_conns)
    {
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }

    LogicSerConnList cssmartlock_ser_conns;
    {
        std::lock_guard<std::mutex> lock(cssmartlock_mutex_);
        cssmartlock_ser_conns = cssmartlock_ser_conns_;
    }    
    for (auto& conn : cssmartlock_ser_conns)
    {
        conn.second->Send(pdu.GetBuffer(), pdu.GetLength());
    }

    
}

//message已经是一条完整的消息了

//| package length   | true  | int32 bigendian | 包长度   |
//| header Length    | true  | int16 bigendian | 包头长度 |
//| ver              | true  | int16 bigendian | 协议版本 |
//| id               | true  | int32 bigendian | 协议指令 |
//| seq              | true  | int32 bigendian | 序列号   |
//| body             | false | binary          | 具体消息 |

//先这样传递数据,后续需要用Impdu的形式来承载消息,参考:RouteConn.cpp
void RouteServer::OnStringMessage(const evpp::TCPConnPtr& conn, std::unique_ptr<CAkcsPdu>& pdu)
{
    uint32_t msg_id = pdu->GetCommandId();
    AK_LOG_INFO << "csroute ser msg, pdumsg id " <<  msg_id << " traceid:" << pdu->GetTraceId();

    switch (msg_id)
    {
        case AKCS_MSG_L2R_REG_UID_REQ:
        {
            HandleSrvRegUid(conn, pdu);
            break;
        }
        case AKCS_MSG_L2R_START_RTSP_REQ:
        {
            std::shared_ptr<CAkcsPdu> pdu2 = std::move(pdu);
            RouteMQCust::GetInstance()->AddMessage(pdu2);
            break;
        }
        case AKCS_MSG_L2R_STOP_RTSP_REQ:
        {
            std::shared_ptr<CAkcsPdu> pdu2= std::move(pdu);
            RouteMQCust::GetInstance()->AddMessage(pdu2);
            break;
        }    
        case AKCS_MSG_L2R_KEEPALIVE_RTSP_REQ:
        {
            std::shared_ptr<CAkcsPdu> pdu2= std::move(pdu);
            RouteMQCust::GetInstance()->AddMessage(pdu2);
            break;
        }

        default:
        {
            AK_LOG_WARN << "csroute ser msg,invalid pdumsg id " << msg_id;
        }
    }
}

//logic srv注册srv-uid,所有逻辑服务器重新与route建立连接的时候,都需要重新上报
void RouteServer::HandleSrvRegUid(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Server::LogicSrvReg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    std::string srv_uid = msg.logic_srv_uid();
    AK::Base::LogicClientType srv_type = msg.srv_type();
    evpp::Any uid_tmp(srv_uid);
    conn->set_context(0, uid_tmp);
    evpp::Any srv_type_tmp(srv_type);
    conn->set_context(1, srv_type_tmp);
    if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_MAIN)
    {
        //csmain_ser_conns_.insert(std::pair<std::string, evpp::TCPConnPtr>(srv_uid,conn)); 不能用这种形式的,因为会刷新conn失败
        std::lock_guard<std::mutex> lock(csmain_mutex_);
        csmain_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_VRTSP)
    {
        std::lock_guard<std::mutex> lock(csvrtspd_mutex_);
        csvrtspd_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_ADAPT)
    {
        std::lock_guard<std::mutex> lock(csadapt_mutex_);
        csadapt_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_PBX)
    {
        std::lock_guard<std::mutex> lock(cspbx_mutex_);
        cspbx_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_CONFIG)
    {
        std::lock_guard<std::mutex> lock(csconfig_mutex_);
        csconfig_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_RESID)
    {
        std::lock_guard<std::mutex> lock(csresid_mutex_);
        csresid_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_OFFICE)
    {
        std::lock_guard<std::mutex> lock(csoffice_mutex_);
        csoffice_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_SIPHUB)
    {
        std::lock_guard<std::mutex> lock(siphub_mutex_);
        siphub_ser_conns_[srv_uid] = conn;
    }
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_CONFIG_OFFICE)
    {
        {
            std::lock_guard<std::mutex> lock(csconfig_office_mutex_);
            csconfig_office_ser_conns_[srv_uid] = conn;
            csconfig_office_srv_set_.insert(srv_uid);
        }
        CLogicSrvMng::Instance()->UpdateConfigOfficeSrvList(csconfig_office_srv_set_);
    }  
    else if (srv_type == AK::Base::LOGIC_CLIENT_TYPE_SMARTLOCK)
    {
        std::lock_guard<std::mutex> lock(cssmartlock_mutex_);
        cssmartlock_ser_conns_[srv_uid] = conn;
    }
    else
    {
        AK_LOG_WARN << "invalid logic server type:" << srv_type << "conn net info is: ";
        conn->Close();
    }
}

int64_t RouteServer::GetIncrSeq()
{
    int64_t seq = 0;
    SafeCacheConn redis(g_redis_db_seq_pbxsid);
    seq = redis.incr(app_status_seq);
    return seq;
}
