#ifndef __ROUTE_SERVER_H__
#define __ROUTE_SERVER_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include <evpp/tcp_server.h>
#include <evpp/any.h>
#include "AkcsIpcMsgCodec.h"
#include "logic_srv_mng.h"

#define CSROUTE_CONF_COMMON_LEN 64
enum OEM_TYPE
{
    OEM_AKUVOX = 1,
    OEM_DISCREET = 2,
};

typedef struct AKCS_ROUTE_CONF_T
{
    char route_outer_ip[CSROUTE_CONF_COMMON_LEN];
    /* DB配置项 */
    char db_ip[CSROUTE_CONF_COMMON_LEN];
    char db_username[CSROUTE_CONF_COMMON_LEN];
    char db_password[CSROUTE_CONF_COMMON_LEN];
    char db_database[CSROUTE_CONF_COMMON_LEN];
    char nsq_topic[CSROUTE_CONF_COMMON_LEN];
    char nsq_channel[CSROUTE_CONF_COMMON_LEN];

    char etcd_server_addr[CSROUTE_CONF_COMMON_LEN];
    char session_server_addr[CSROUTE_CONF_COMMON_LEN];
    char video_server_addr[CSROUTE_CONF_COMMON_LEN];
    char gw_code[CSROUTE_CONF_COMMON_LEN];
    char push_server_addr[CSROUTE_CONF_COMMON_LEN];
    char push_aeskey[CSROUTE_CONF_COMMON_LEN];
    int  db_port;
    char server_tag[CSROUTE_CONF_COMMON_LEN];
    
    //OEM名称,用于区分推送服务器
    char oem_name[32];
    short oem_num;
    int is_china;

    char kafka_push_email_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_broker_ip[CSROUTE_CONF_COMMON_LEN];
    char kafka_pm_export_log_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_pm_export_log_excel_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_notify_web_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_notify_linker_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_csroute_topic[CSROUTE_CONF_COMMON_LEN]; // csroute作为kafka的消费者
    char kafka_csroute_group[CSROUTE_CONF_COMMON_LEN]; 
    char kafka_notify_web_message_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_notify_web_attendance_topic[CSROUTE_CONF_COMMON_LEN];
    char kafka_notify_web_access_door_topic[CSROUTE_CONF_COMMON_LEN];
    
    char linker_nsq_topic[CSROUTE_CONF_COMMON_LEN];
    char linker_nsq_ip[CSROUTE_CONF_COMMON_LEN];

    char log_db_ip[CSROUTE_CONF_COMMON_LEN];
    int log_db_port;
    char log_db_database[CSROUTE_CONF_COMMON_LEN];

    int route_msg_thread_num;
} AKCS_ROUTE_CONF;

void startRouteServer();

typedef std::map<std::string/*sid*/, evpp::TCPConnPtr> LogicSerConnList;//逻辑服务器
typedef LogicSerConnList::iterator LogicSerConnIter;
typedef std::map<int64_t/*seq*/, evpp::TCPConnPtr/*pbx_conn*/> SeqPbxConnList;//pbx查询app在线状态seq跟其与csroute之间长连接conn的映射
//与所有的逻辑服务器之间通过ping-ping来保持连接
class RouteServer
{
public:
    enum SipType
    {
        SIP_TYPE_DEV = 1,
        SIP_TYPE_APP = 2,
        SIP_TYPE_NONE = 3,
        SIP_TYPE_PHONE = 4,
    };
    enum CallType
    {
        CALL_TYPE_NONE = 0,
        CALL_TYPE_APP2APP = 1,
        CALL_TYPE_DEV2APP = 2,
        CALL_TYPE_APP2DEV = 3,
        CALL_TYPE_DEV2DEV = 4,
        CALL_TYPE_DEV2PHONE = 5,
        CALL_TYPE_APP2PHONE = 6
    };
    RouteServer(evpp::EventLoop* loop, const std::string& addr, const std::string& name, uint32_t thread_num);

    void Start();
public:
    friend class RouteMQCust;
    friend class RouteOfficeMQCust;
public:
    //message已经是一条完整的消息了
    void OnStringMessage(const evpp::TCPConnPtr& conn, std::unique_ptr<CAkcsPdu>& pdu);

    //对于akcs来说,就是在这里讲请求设备上报状态的消息下发了,同时保存设备的相关状态信息(含地址信息\联动系统等)
    void OnConnection(const evpp::TCPConnPtr& conn);
    //根据sid查找对应裸机服务器的conn
    const evpp::TCPConnPtr GetMainConnBySid(const std::string& sid);
    const evpp::TCPConnPtr GetVrtspConnBySid(const std::string& sid);
    const LogicSerConnList GetAllVrtspConn();
    const evpp::TCPConnPtr GetAdaptConn();
    const evpp::TCPConnPtr GetCsconfigConn();
    const evpp::TCPConnPtr GetResidConnBySid(const std::string& sid);   
    const evpp::TCPConnPtr GetOfficeConnBySid(const std::string& sid);
    const LogicSerConnList GetAllSiphubConn();
    const evpp::TCPConnPtr GetCsconfigOfficeConn(const std::string &key_hash);
    const evpp::TCPConnPtr GetCsconfigOfficeConn();
    const evpp::TCPConnPtr GetCssmartlockConn();
private:
    void HandleSrvRegUid(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);
    void HandlePbxQueryCallLandline(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);
    int64_t GetIncrSeq();
    void PingLogicSrv();
private:
    evpp::TCPServer server_;
    AkcsIpcMsgCodec2 codec_;
    evpp::EventLoop* loop_;
    std::mutex csmain_mutex_;
    LogicSerConnList csmain_ser_conns_;
    std::mutex csvrtspd_mutex_;
    LogicSerConnList csvrtspd_ser_conns_;
    std::mutex csadapt_mutex_;
    LogicSerConnList csadapt_ser_conns_;
    std::mutex csconfig_mutex_;
    LogicSerConnList csconfig_ser_conns_;
    //pbx的消息全部用长连接,不用mq
    std::mutex cspbx_mutex_;
    LogicSerConnList cspbx_ser_conns_;
    std::mutex csresid_mutex_;
    LogicSerConnList csresid_ser_conns_;

    std::mutex csoffice_mutex_;
    LogicSerConnList csoffice_ser_conns_;

    std::mutex siphub_mutex_;
    LogicSerConnList siphub_ser_conns_;

    std::mutex cspbx_seq_mutex_;
    SeqPbxConnList seq_pbx_conns_;
    static const char app_status_seq[];

    std::mutex csconfig_office_mutex_;
    LogicSerConnList csconfig_office_ser_conns_;
    std::set<std::string> csconfig_office_srv_set_;
    
    std::mutex cssmartlock_mutex_;
    LogicSerConnList cssmartlock_ser_conns_;
};

#endif //__CSMAIN_SERVER_H__

