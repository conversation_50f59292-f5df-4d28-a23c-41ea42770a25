#include "RouteRemoteUnlockNotify.h"
#include "AkLogging.h"
#include "Route2ResidMsg.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

RouteRemoteUnlockNotify::RouteRemoteUnlockNotify()
{
    AK_LOG_INFO << "RouteRemoteUnlockNotify 构造函数";
}

RouteRemoteUnlockNotify::~RouteRemoteUnlockNotify()
{
    AK_LOG_INFO << "RouteRemoteUnlockNotify 析构函数";
}

void RouteRemoteUnlockNotify::ProcessMessage(const AK::Route::P2PSendAlarmNotifyMsg& alarm_msg)
{
    AK_LOG_INFO << "RouteRemoteUnlockNotify::ProcessMessage - 开始处理远程解锁通知";
    AK_LOG_INFO << "设备ID: " << alarm_msg.receive_endpoint();
    AK_LOG_INFO << "告警类型: " << alarm_msg.alarm_type();
    AK_LOG_INFO << "时间: " << alarm_msg.time();

    // 转发到 csresid
    ForwardToResid(alarm_msg);

    AK_LOG_INFO << "RouteRemoteUnlockNotify::ProcessMessage - 处理完成";
}

void RouteRemoteUnlockNotify::ForwardToResid(const AK::Route::P2PSendAlarmNotifyMsg& alarm_msg)
{
    AK_LOG_INFO << "RouteRemoteUnlockNotify::ForwardToResid - 开始转发到 csresid";

    // 获取设备信息
    std::string mac, uid;
    if (!GetDeviceInfo(alarm_msg.receive_endpoint(), mac, uid)) {
        AK_LOG_ERROR << "RouteRemoteUnlockNotify::ForwardToResid - 获取设备信息失败: " << alarm_msg.receive_endpoint();
        return;
    }

    // 构建发送给 csresid 的消息
    std::string resid_msg;
    BuildResidMessage(alarm_msg, resid_msg);

    // 发送到 csresid
    Route2ResidMsg::SendRemoteUnlockNotifyMsg(mac, uid, resid_msg);

    AK_LOG_INFO << "RouteRemoteUnlockNotify::ForwardToResid - 转发完成, MAC: " << mac << ", UID: " << uid;
}

void RouteRemoteUnlockNotify::BuildResidMessage(const AK::Route::P2PSendAlarmNotifyMsg& alarm_msg, std::string& resid_msg)
{
    // 构建 JSON 格式的消息
    Json::Value json_msg;
    json_msg["msg_type"] = "remote_unlock_notify";
    json_msg["device_id"] = alarm_msg.receive_endpoint();
    json_msg["alarm_type"] = alarm_msg.alarm_type();
    json_msg["time"] = alarm_msg.time();
    json_msg["from_local"] = alarm_msg.from_local();
    json_msg["alarm_code"] = alarm_msg.alarm_code();
    json_msg["trace_id"] = std::to_string(alarm_msg.trace_id());
    
    // 解析标题中的解锁信息
    std::string title = alarm_msg.alarm_type();  // 这里包含了解锁详细信息
    json_msg["unlock_info"] = title;

    Json::FastWriter writer;
    resid_msg = writer.write(json_msg);

    AK_LOG_INFO << "RouteRemoteUnlockNotify::BuildResidMessage - 构建消息: " << resid_msg;
}

bool RouteRemoteUnlockNotify::GetDeviceInfo(const std::string& device_id, std::string& mac, std::string& uid)
{
    // 从数据库获取智能锁信息
    SmartLockInfo lock_info;
    if (dbinterface::SmartLock::GetSmartLockInfoByUUID(device_id, lock_info) != 0) {
        AK_LOG_ERROR << "RouteRemoteUnlockNotify::GetDeviceInfo - 获取智能锁信息失败: " << device_id;
        return false;
    }

    mac = lock_info.mac;

    // 获取个人账户信息
    ResidentPersonalAccountInfo account_info;
    if (dbinterface::ResidentPersonalAccount::GetResidentPersonalAccountInfoByUUID(
            lock_info.personal_account_uuid, account_info) != 0) {
        AK_LOG_ERROR << "RouteRemoteUnlockNotify::GetDeviceInfo - 获取个人账户信息失败: " << lock_info.personal_account_uuid;
        return false;
    }

    uid = account_info.account;

    AK_LOG_INFO << "RouteRemoteUnlockNotify::GetDeviceInfo - 设备信息: " 
                << "device_id=" << device_id << ", mac=" << mac << ", uid=" << uid;

    return true;
}
