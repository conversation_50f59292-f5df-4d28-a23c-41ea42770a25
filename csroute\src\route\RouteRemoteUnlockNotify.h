#pragma once
#include "RouteBase.h"
#include "DclientMsgSt.h"

/**
 * 处理远程解锁通知的路由类
 * 负责将来自 cssmartlock 的远程解锁通知转发给 csresid，最终通知到 app
 */
class RouteRemoteUnlockNotify : public RouteBase
{
public:
    RouteRemoteUnlockNotify();
    virtual ~RouteRemoteUnlockNotify();

    /**
     * 处理远程解锁通知消息
     */
    void ProcessMessage(const AK::Route::P2PSendAlarmNotifyMsg& alarm_msg) override;

private:
    /**
     * 转发通知到 csresid
     */
    void ForwardToResid(const AK::Route::P2PSendAlarmNotifyMsg& alarm_msg);
    
    /**
     * 构建发送给 csresid 的消息
     */
    void BuildResidMessage(const AK::Route::P2PSendAlarmNotifyMsg& alarm_msg, std::string& resid_msg);
    
    /**
     * 获取设备相关信息
     */
    bool GetDeviceInfo(const std::string& device_id, std::string& mac, std::string& uid);
};
