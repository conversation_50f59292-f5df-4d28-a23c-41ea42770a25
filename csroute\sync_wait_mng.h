#ifndef __ROUTE_SYNC_WAIT_MNG_H__
#define __ROUTE_SYNC_WAIT_MNG_H__

#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>

class CSyncWaitMng : public boost::noncopyable
{
public:
    CSyncWaitMng()
    {}
    ~CSyncWaitMng()
    {}
    static CSyncWaitMng* Instance();
public:
    int BeingQueryUidStatus(std::string uid, uint32_t seq, const evpp::TCPConnPtr& conn);
private:
    static CSyncWaitMng*    instance_;
    std::map<uint32_t, int> seq_status;
};

#endif //__ROUTE_SYNC_WAIT_MNG_H__
