syntax = "proto3";

package AK.Route; 

//vrtspd通过mq发送监控请求给csroute,csroute通过tcp连接转发给csmain,信令内容相同,信令id不同
message StartRtspReq{
	//cmd id:   AKCS_V2R_START_RTSP_MSG_REQ / AKCS_R2M_START_RTSP_REQ 共用
    //RTSP_DATA
    string mac = 1; // app请求监控设备的mac (有三方摄像头时为摄像头的uuid)
    string ip = 2;
    int32 port = 3;
    string vrtspd_logic_id = 4;
    string ssrc = 5;//十六进制的
    string ipv6 = 6;//流媒体的ipv6
    int32 is_third = 7;//三方摄像头监控转发标识
    string dev_mac = 8;//三方摄像头绑定的设备mac
    string mac_sip = 9;    // app请求监控设备的sip
    string indoor_mac = 10; // 转流室内机的mac
    int32 video_pt = 11;
    string video_type = 12;
    string video_fmtp = 13;
}
