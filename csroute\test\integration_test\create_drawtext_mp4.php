<?php
// 该文件的主要功能是：根据提供的设备列表,将同一路径下的MP4视频加上设备具体的MAC地址水印
// 这样就可以确定是否有串流，在大规模模拟门口机rtsp服务的压测中使用

// 步骤1：解析mac_list.csv文件
$csvFile = 'mac_list.csv';
$csvContent = file_get_contents($csvFile);
$macList = explode("\n", $csvContent);

// 删除最后一行如果为空行
if (empty(end($macList))) {
    array_pop($macList);
}

// 步骤2：给rtsp.mp4文件加上水印
$inputFile = 'rtsp_test.mp4';
$outputDirectory = dirname($inputFile);

foreach ($macList as $mac) {
    $outputFile = $outputDirectory . '/' . $mac . '.mp4';
    $inoutFileTmp = 'tmp.mp4';
    // 拷贝原始文件
    copy($inputFile, $inoutFileTmp);

    // 使用ffmpeg给MP4文件加水印
    $command = 'ffmpeg -i ' . $inoutFileTmp . ' -vf "drawtext=text=' . $mac . ':x=20:y=20:fontsize=24:fontcolor=black" -c:a copy ' . $outputFile . ' -y';
    #echo $command;
    exec($command);
}

echo '水印添加完成';
?>

