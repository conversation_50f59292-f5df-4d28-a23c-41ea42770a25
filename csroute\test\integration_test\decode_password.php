<?php

require_once(dirname(__FILE__) . '/funcs_password_confuse.php');
const STATIS_FILE = "./mac_rtsppwd.csv";
$pre_mac = 'FFFFFF%';
$rtsp_ip = '';

if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 

chmod(STATIS_FILE, 0777);
function STATIS_WRITE($content)
{
    file_put_contents(STATIS_FILE, $content, FILE_APPEND);
    file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();
//前缀
$sth_dis = $db->prepare("select MAC,RtspPwd from PersonalDevices where MAC like '$pre_mac'");
$sth_dis->execute();
$mac_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($mac_list as $row => $mac_pwd)
{
    $mac = $mac_pwd['MAC'];
    $pwd = $mac_pwd['RtspPwd'];
    //解密rtsp pwd
    $de_pwd = PasswdDecode($pwd);

    $static_str = $mac. ',' . $de_pwd;
    STATIS_WRITE($static_str);
    //$i = 2;
    //$cmd = "/home/<USER>/bench-rtsp/ffmpeg -y -rtsp_transport udp -i p://user:$de_pwd@$rtsp_ip:554/$mac -c:v copy -t 30 $i.mp4";
    //echo $cmd;
}

