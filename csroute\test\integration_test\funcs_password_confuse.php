<?php

//用于凯撒的偏移列表,找到字符然后加上偏移得到最终的字符，这个要和C++的对应
const CONFUSE_OFFSET_STR = 'CADaHgKxWec5f2otYIiRlmNLqP0z3hU7ZnFbES8QTOs9dG6yMvu4Jp1VjkrBwX';
const CONFUSE_RANDOM_HEAD_LEN = 4;
const CONFUSE_PASSWORD_INDEX = 5;
const CONFUSE_PASSWORD_LEN = 2;
const CONFUSE_SETP_LEN = 1;//凯撒的偏移setp 用1 位表示
const CONFUSE_OUT_LEN = 32;
const CONFUSE_PREFIX_INDEX = 7;
const CONFUSE_PASSWD_MAX_LEN = 25;//32-7
const CONFUSE_PASSWD_MIN_LEN = 6;
const CONFUSE_OFFSET_STR_MAX_INDEX = 61;
const CONFUSE_OFFSET_STR_LEN = 62;

function ConfuseRandomkeys($length)
{
    $pattern = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $key = "";
    for ($i=0; $i < $length; $i++) {
        $key .= $pattern[mt_rand(0, 61)];    //生成php随机数
    }
    return $key;
}

function GetEncodeChar($char, $step, $offset_arr)
{
    $new_code ='';
    $index = array_search($char, $offset_arr);
    if ($index !== false) {
        $new_index = $index + $step;
        if ($new_index > CONFUSE_OFFSET_STR_MAX_INDEX) {
            $new_index = $new_index - CONFUSE_OFFSET_STR_LEN;
        }
        $new_code = CONFUSE_OFFSET_STR[$new_index];
    } else {
        $new_code = $char;
    }
    return $new_code;
}

//密码混淆接口
//$code:sip/rtsp密码
//混淆成功返回$offset_code，失败返回false
function PasswdEncode($code)
{
    //计算密码长度并补齐前缀
    $code_length = strlen($code);
    if ($code_length < CONFUSE_PASSWD_MIN_LEN || $code_length > CONFUSE_PASSWD_MAX_LEN) {
        return $code;
    }
    //生成混淆前缀
    $prefix = ConfuseRandomkeys(CONFUSE_RANDOM_HEAD_LEN);
    //随机生成偏移位
    $step = mt_rand(1, 9);
    $prefix .=$step;

    $offset_arr = str_split(CONFUSE_OFFSET_STR, 1);
    if ($code_length < 10) {
        $prefix .= GetEncodeChar('0', $step, $offset_arr);
        $prefix .= GetEncodeChar(strval($code_length), $step, $offset_arr);
    } else {
        $tmp = strval($code_length);
        $prefix .= GetEncodeChar($tmp[0], $step, $offset_arr);
        $prefix .= GetEncodeChar($tmp[1], $step, $offset_arr);
    }

    //生成混淆后缀,密码最大长度 = 32-7 = 25
    $suffix_length = CONFUSE_OUT_LEN - CONFUSE_PREFIX_INDEX - $code_length;
    $suffix = ConfuseRandomkeys($suffix_length);

    //对密码进行混淆，特殊字符不处理
    $new_code = '';
    for ($i=0; $i<$code_length; $i++) {
        $new_code .= GetEncodeChar($code[$i], $step, $offset_arr);
    }
    $offset_code = $prefix.$new_code.$suffix;

    return $offset_code;
}

function GetDecodeChar($char, $step, $offset_arr)
{
    $real_code = '';
    $index = array_search($char, $offset_arr);
    if ($index !== false) {
        $new_index = $index - $step;
        if ($new_index < 0) {
            $new_index = $new_index + CONFUSE_OFFSET_STR_LEN;
        }
        $real_code = CONFUSE_OFFSET_STR[$new_index];
    } else {
        $real_code = $char;
    }
    return $real_code;
}

//密码去混淆接口
//$code:混淆后的密码
//去混淆成功返回原密码，失败返回false
function PasswdDecode($code)
{
    $length = strlen($code);
    if ($length != CONFUSE_OUT_LEN) {
        return $code;
    }

    $offset_arr = str_split(CONFUSE_OFFSET_STR, 1);
    //偏移位数
    $step = intval(substr($code, CONFUSE_RANDOM_HEAD_LEN, CONFUSE_SETP_LEN));

    //密码长度
    $tmp_code_len = substr($code, CONFUSE_PASSWORD_INDEX, CONFUSE_PASSWORD_LEN);
    $code_len = GetDecodeChar($tmp_code_len[0], $step, $offset_arr);
    $code_len .= GetDecodeChar($tmp_code_len[1], $step, $offset_arr);
    $code_len = intval($code_len);
    //偏移后的密码
    $confused_code = substr($code, CONFUSE_PREFIX_INDEX, $code_len);

    $real_code = '';
    //对密码进行去混淆，特殊字符不处理
    for ($i=0; $i<$code_len; $i++) {
        $real_code  .= GetDecodeChar($confused_code[$i], $step, $offset_arr);
    }
    return $real_code;
}
