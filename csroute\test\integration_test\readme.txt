使用说明：
1、先了解同路径下的整体架构图,方便出现问题时，可以排查具体原因；

2、准备模拟门口机的拦截服务压测环境(压测环境最好用云实例，公司内部的网络一压测就封禁udp端口)：
   2.1、从代码库主线获取最新的AK.Route.proto文件，在压测环境中的{RTSP_TEST_DIR}路径中执行命令protoc --php_out=./  AK.Route.proto 在同路径下生成php相关的protobuf代码,
         protoc可执行bin文件可以在编译环境拷贝，也可以到192.168.10.101 上/usr/local/bin/protoc获取;{RTSP_TEST_DIR}是你规划执行下一步拦截服务的路径,没有特殊要求;
		 
   2.2、在压测环境中部署ffmpeg可执行文件并放置到{RTSP_TEST_DIR}路径下，确保可执行权限;ffmpeg bin文件可以自行去官网下载也可以到192.168.10.101上/home/<USER>/bench-rtsp/ffmpeg-5.4获取；
   
   2.3、php需要开启protobuf扩展,操作方法：/usr/local/php/etc/php.ini： 打开注释 extension=/usr/local/php/lib/php/extensions/no-debug-zts-20131226/protobuf.so, 
          akcs的php安全包是有自带protobuf扩展只是默认没打开，打开即可。如果自行下载的php环境则根据网上教程添加一下扩展也是很快；
		  
   2.4、拷贝本路径下的rtsp_rtp_intercept_srv.php以及rtsp_test.264到压测环境中的{RTSP_TEST_DIR}路径,执行命令php rtsp_rtp_intercept_srv.php拉起udp服务,接受来自csroute的rtp视频流消息；具体原理可见架构图说明；
   
3、打开csroute的rtsp集成测试编译选项：cmake文件将add_definitions(-DRTSP_RTP_INTERCEPT)的注释#删除即可,同时修改csroute配置文件中指示拦截服务rtsp_rtp_intercept_srv部署的udp地址(注:该配置项没有上CMDB),
    以上两个修改点修改后重新编译csroute,这样就实现了原先csroute通知csmain的消息拦截到rtsp_rtp_intercept_srv的udp服务中
    后续rtsp_rtp_intercept_srv解析csroute发送过来的rtp包相关的信息，就调用ffmpeg直接往相关的csvrtspd接受rtp包的端口发送rtp数据，实现模拟门口机发送rtp包的功能；

4、被压测的云上给用户添加虚拟门口机或者真实门口机,用VLC或者app都可以看到rtsp_rtp_intercept_srv用ffmpeg伪造的视频；注意当前小睿app的解码没做好,画面不流畅，但是用VLC就很流畅;

5、如果想要模拟批量的rtsp客户端，则可以使用批量拉起N个ffmpeg来模拟，在同路径下的rtsp_client.php脚本就是采用多进程拉起多个ffmpeg的脚本.

特殊说明：
1、能添加水印的ffmpeg版本在本地主机192.168.10.101的/usr/sbin/ffmpeg，后面发现ffmpeg添加水印后发现再通过ffmpeg读取带水印264文件往rtp服务地址打流的时候，VLC也无法解码，遂放弃不同的MAC的视频有不同的水印的需求；
    后面有时间可以花时间调试下ffmpeg的命令行把水印的功能加上，这样在压测模拟多台门口机的rtsp视频时，不同mac的视频画面有不同水印会更加理想；
	
2、create_drawtext_mp4.php文件就是用于给MP4文件加水印的脚本，当前暂时没用;

3、decode_password.php文件是批量从数据库读取MAC以及对应rtsppwd并解密，然后将MAC=pwd用csv的格式保存起来，如文件mac_rtsppwd.csv所示，这样上述使用说明中的第5步用ffmpeg命令行的时候就可以直接用csv
   文件来构造rtsp客户端.
	