<?php
$csvFileName = 'mac_rtsppwd.csv';
$index = 0;
$rtsp_ip = '*************';//要压测的rtsp服务节点的IP
$children = [];

$handle = fopen($csvFileName, 'r');
// 逐行读取CSV文件
while (($data = fgetcsv($handle)) !== false) {
    $index++;
    $mac = $data[0];
    $pwd = $data[1];

    $cmd = "/home/<USER>/bench-rtsp/ffmpeg -y -rtsp_transport udp -i rtsp://user:$pwd@$rtsp_ip:554/$mac  -timeout 3000000 -c:v copy -t 30 $index.mp4";
    echo $cmd. PHP_EOL;

    $pid = pcntl_fork();
    if ($pid == -1) {
        die('Failed to fork a new process');
    } elseif ($pid) {
        // 父进程，继续业务
        $children[] = $pid;
        continue;
    } else {
        // 子进程，执行Shell命令
        $output = exec($cmd); 
        exit(); 
    }
}

// 关闭文件句柄
fclose($handle);

// 父进程等待所有子进程结束
foreach ($children as $pid) {
    pcntl_waitpid($pid, $status);
    echo "Child process $pid has exited.\n";
}
// 父进程退出
echo "All child processes have exited. Parent process exits.\n";

?>
