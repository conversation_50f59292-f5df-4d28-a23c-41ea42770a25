#include <iostream>
#include <cstring>
#include <arpa/inet.h>
#include <unistd.h>
#include "ConfigFileReader.h"
#include "AkLogging.h"
#include "rtsp_rtp_intercept.h"

//rtsp拦截服务的实际部署地址
static char rtsp_rtp_intercept_srv_addr[20]; 
static uint16_t rtsp_rtp_intercept_srv_port;
static bool is_init = false;

#define CSROUTE_CONF_FILE "/usr/local/akcs/csroute/conf/csroute.conf"

bool InitRtspRtpInterceptAddr()
{
    if(!is_init)
    {
        is_init = true;
        CConfigFileReader config_file(CSROUTE_CONF_FILE);
        Snprintf(rtsp_rtp_intercept_srv_addr, sizeof(rtsp_rtp_intercept_srv_addr), config_file.GetConfigName("rtsp_rtp_intercept_srv_addr"));
        const char* tmp_port = config_file.GetConfigName("rtsp_rtp_intercept_srv_port");
        rtsp_rtp_intercept_srv_port = ATOI(tmp_port);
        AK_LOG_WARN << "RtspRtpIntercept udp srv add is " << rtsp_rtp_intercept_srv_addr << ":" << rtsp_rtp_intercept_srv_port;
    }
    return true;
}

int RtspRtpIntercept(const char* msg, uint32_t msg_len)
{
    InitRtspRtpInterceptAddr();
    
    // 创建UDP socket
    int client_socket = socket(AF_INET, SOCK_DGRAM, 0);
    if (client_socket == -1) 
    {
        AK_LOG_WARN << "Error creating socket";
        return -1;
    }

    // 服务器地址和端口
    sockaddr_in serverAddress;
    std::memset(&serverAddress, 0, sizeof(serverAddress));
    serverAddress.sin_family = AF_INET;
    serverAddress.sin_port = htons(rtsp_rtp_intercept_srv_port);
    inet_pton(AF_INET, rtsp_rtp_intercept_srv_addr, &(serverAddress.sin_addr));

    // 发送数据
    ssize_t sentBytes = sendto(client_socket, msg, msg_len, 0,reinterpret_cast<sockaddr*>(&serverAddress), sizeof(serverAddress));
    if (sentBytes == -1)
    {
        AK_LOG_WARN << "Error sending data to addr:" << rtsp_rtp_intercept_srv_addr << ":" << rtsp_rtp_intercept_srv_port;
        close(client_socket);
        return -1;
    }

    // 关闭socket
    close(client_socket);
    return 0;
}

