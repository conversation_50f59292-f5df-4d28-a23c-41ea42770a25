<?php
date_default_timezone_set('UTC');
include 'GPBMetadata/AKRoute.php';

include 'AK/Route/P2PAdaptOneDevCleanDeviceCodeMsg.php';
include 'AK/Route/P2PRouteOneDevLogOutMsg.php';
include 'AK/Route/P2PRouteOneUidLogOutMsg.php';
include 'AK/Route/P2PRoutePingPong.php';
include 'AK/Route/P2PRouteQueryUidStatusReq.php';
include 'AK/Route/P2PRouteQueryUidStatusResp.php';
include 'AK/Route/P2PRoutWakeUpAppReq.php';
include 'AK/Route/P2PUpgradeDevMsg.php';
include 'AK/Route/RtspCaptureReq.php';
include 'AK/Route/RtspKeepAliveReq.php';
include 'AK/Route/StartRtspReq.php';
include 'AK/Route/StopRtspReq.php';

// 检查是否支持pcntl扩展
if (!function_exists('pcntl_fork')) {
    die('PCNTL extension is not available on this system');
}

//启动UDP服务
$startAddress = '0.0.0.0'; // 或者使用 INADDR_ANY
$startPort = 12345;

function parseProtoMsg($udp_msg_body) {
    $proto_msg = new \AK\Route\StartRtspReq();
    $proto_msg->mergeFromString($udp_msg_body);
    // 构建关联数组表示结构体
    $parsedMsg = [];
    $parsedMsg['mac'] = $proto_msg->getMac();
    $parsedMsg['ip'] = $proto_msg->getIp();
    $parsedMsg['port'] = $proto_msg->getPort();
    $parsedMsg['ssrc'] = $proto_msg->getSsrc();
    return $parsedMsg;
}

function startUDPServer($address, $port) {
    // 创建UDP socket
    $socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
    if (!$socket) {
        die('Unable to create socket: ' . socket_strerror(socket_last_error()));
    }

    // 绑定地址和端口
    if (!socket_bind($socket, $address, $port)) {
        die('Unable to bind socket: ' . socket_strerror(socket_last_error()));
    }
    echo "UDP Server listening on $address:$port\n";

    while (true) {
        // 接收数据
        $data = '';
        $clientAddress = '';
        $clientPort = 0;
        socket_recvfrom($socket, $data, 2048, 0, $clientAddress, $clientPort);

        // 处理接收到的数据
        echo "Received udp data from $clientAddress:$clientPort\n";
        // 提取消息体（偏移上述的消息头的字节数,注意有字节对齐的问题，不能简单按结构体各个字段字节相加）
        $offset = 40;
        $messageBody = substr($data, $offset);
        $parsedMsg = parseProtoMsg($messageBody);
        //启动多进程
        //$parsedMsg['ssrc'] = '01010203';
        $parsedMsg['mac'] = 'rtsp_test';//所有mac都用同一个264文件
        
        $ssrc = '';//因为这样的语句ffmpeg -re -i rtsp_test.264  -vcodec copy -ssrc 0x -f rtp  rtp://************:60058 会出错：Unable to parse option value "0x"
        if($parsedMsg['ssrc'] != ''){
            $ssrc = '-ssrc 0x'. $parsedMsg['ssrc'];
        }
        
        $cmd = './ffmpeg -re -i ' . $parsedMsg['mac'] . '.264  -vcodec copy '. $ssrc. ' -f rtp ' . ' rtp://' . $parsedMsg['ip'] . ':' . $parsedMsg['port'] . ' > /dev/null 2>&1 &';
        echo $cmd;	

        $pid = pcntl_fork();
        if ($pid == -1) {
            die('Failed to fork a new process');
        } elseif ($pid) {
            // 父进程，继续业务
            continue;
        } else {
            // 子进程，执行Shell命令
            $output = exec($cmd); 
            exit(); // 子进程执行完毕后退出
        }
    }

    // 关闭socket
    socket_close($socket);
}

//启动udp服务,接受来自csroute的拦截消息
startUDPServer($startAddress, $startPort);
?>
