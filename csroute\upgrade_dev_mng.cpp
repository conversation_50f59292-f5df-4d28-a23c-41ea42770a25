#include <sstream>
#include <stdio.h>
#include <map>
#include "AK.Route.pb.h"
#include "session_rpc_client.h"
#include "route_server.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "upgrade_rom_devices.h"
#include "upgrade_dev_mng.h"
#include "dbinterface/UpgradeRomVersion.h"
#include "dbinterface/SystemSettingTable.h"


extern SmRpcClient* g_sm_client_ptr;
extern RouteServer* g_route_ser;

CUpgradeDevMng* CUpgradeDevMng::instance = NULL;

CUpgradeDevMng* CUpgradeDevMng::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CUpgradeDevMng();
    }
    return instance;
}

int CUpgradeDevMng::GetUpgradeDevList()
{
    //add by chenzhx csroute集群 添加一个简单的分布式锁
    if (!dbinterface::SystemSetting::SystemExecUpgradeLock(10))
    {
        return 1;
    }
    RomIDFileMap rom_id_file_map;
    std::vector<int> rom_id;
    dbinterface::UpgradeRomVersion::GetWaitUpgradeRomID(rom_id_file_map);
    RomIDFileMapIter iter = rom_id_file_map.begin();
    for (; iter != rom_id_file_map.end(); iter++)
    {
        int is_need_reset = iter->first.second;
        SetUpgradeRomIDStatus(iter->first.first, Upgrading); //设置该romID升级命令升级中。TODO,可能会导致多个csroute同时对一个升级指令进行操作,设备会多次升级
        std::string rom_file_url = iter->second;
        std::string rom_file_path = iter->second;
        //added by chenyc, 2017-12-04,当发现有格式错误的包名时(http需要转移的字符),不允许,并删除掉
        std::size_t pos1 = rom_file_path.find_last_of('/');  // "versionfile/80.20.11.21.rom"
        if (pos1 == std::string::npos)
        {
            AK_LOG_WARN << "verRom " << iter->second.c_str() << " file format is wrong1";
            rom_id.push_back(iter->first.first);//记录升级文件格式错误的id
            continue;
        }
        rom_file_path = rom_file_path.substr(pos1 + 1); // "80.20.11.21.rom"

        std::size_t pos2 = rom_file_path.find_last_of('.');
        if (pos2 == std::string::npos)
        {
            AK_LOG_WARN << "verRom " << iter->second.c_str() << " file format is wrong2";
            rom_id.push_back(iter->first.first);
            continue;
        }
        std::string rom_file_ver = rom_file_path.substr(0, pos2);

        std::map<evpp::TCPConnPtr, std::vector<std::string>>conn_macs;
        std::vector<std::string> macs;
        do
        {
            CUpgradeRomDevices::GetInstance()->SetUpgradeDone();
            CUpgradeRomDevices::GetInstance()->DaoGetWaitUpgradeList(iter->first.first, macs);
            //逐个查询设备所在的csmain升级
            for (const auto& mac : macs)
            {
                //查询mac在哪一台接入服务器上面
                std::string sid = g_sm_client_ptr->QueryDev(mac);
                AK_LOG_INFO << "start notify csmain to upgrade dev, mac is " << mac << ", get csmain sid is " << sid;
                evpp::TCPConnPtr conn = g_route_ser->GetMainConnBySid(sid);
                if (!conn)
                {
                    AK_LOG_WARN << "get csmain route client conn failed,sid is " << sid;
                    continue;
                }
                conn_macs[conn].push_back(mac);
            }
            macs.clear();
        }
        while (CUpgradeRomDevices::GetInstance()->IsNeedUpgrade());
        //逐个通知设备所在的csmain升级
        for (const auto& conn_mac : conn_macs)
        {
            AK::Route::P2PUpgradeDevMsg msg;
            for (const auto& mac : conn_mac.second)
            {
                msg.add_mac_list(mac);
            }
            msg.set_firmware_url(rom_file_url);
            msg.set_firmware_ver(rom_file_ver);
            msg.set_is_need_reset(is_need_reset);
            CAkcsPdu pdu;
            pdu.SetMsgBody(&msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(AKCS_R2M_UPGRADE_DEV_REQ);
            pdu.SetSeqNum(0);
            conn_mac.first->Send(pdu.GetBuffer(), pdu.GetLength());
        }
        SetUpgradeRomIDStatus(iter->first.first, Upgraded); //设置该romID升级命令已经升级完成
    }

    //删除掉升级文件格式错误的id
    if (!rom_id.empty())
    {
        dbinterface::UpgradeRomVersion::DelUpgradeRomID(rom_id);
    }

    dbinterface::SystemSetting::SystemExecUpgradeUnLock();
    return 0;
}

void CUpgradeDevMng::SetUpgradeRomIDStatus(int romid, UpgradeStatus s)
{
    dbinterface::UpgradeRomVersion::SetUpgradeRomIDStatus(romid, s);
}
