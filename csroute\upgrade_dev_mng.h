/*================================================================
 *   文件名称：UpgradeDevMng.h
 *   创 建 者：chenyicong
 *   创建日期：2017-10-26
 *   描    述：设备升级的管理器
 *
 ================================================================*/
#ifndef __ROUTE_UPGRADE_DEV_MNG_H__
#define __ROUTE_UPGRADE_DEV_MNG_H__

#include <vector>
#include <string>
#include <atomic>

class CUpgradeDevMng
{
public:
    enum UpgradeStatus
    {
        UnUpgrade = 0,
        Upgrading,
        Upgraded,
    };
public:
    CUpgradeDevMng()
    {
    }
    ~CUpgradeDevMng()
    {
    }
    static CUpgradeDevMng* GetInstance();
    int GetUpgradeDevList();
private:
    void SetUpgradeRomIDStatus(int romid, UpgradeStatus s);
    static CUpgradeDevMng* instance;
};

#endif //__ROUTE_UPGRADE_DEV_MNG_H__
