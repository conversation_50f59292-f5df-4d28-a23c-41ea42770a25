/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#include <iostream>
#include <memory>
#include <string>
#include <vector>
#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include "csvs.grpc.pb.h"
#include "AkLogging.h"
#include "video_rpc_client.h"

using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using VideoStorage::VsDelReply;
using VideoStorage::VsDelRequest;

using VideoStorage::VideoStorageMsg; //rpc服务名
void VideoStorageClient::DelVideoStorage(uint32_t vid)
{
    // Data we are sending to the server.
    VsDelRequest del_request;
    del_request.set_global_video_id(vid);
    // Call object to store rpc data
    AsyncClientCall* call = new AsyncClientCall;
    call->request_type = 1;
    // stub_->PrepareAsyncSayHello() creates an RPC object, returning
    // an instance to store in "call" but does not actually start the RPC
    // Because we are using the asynchronous API, we need to hold on to
    // the "call" instance in order to get updates on the ongoing RPC.
    call->del_response_reader =
        stub_->PrepareAsyncDelVideoStorageHandle(&call->context, del_request, &cq_);

    // StartCall initiates the RPC call
    call->del_response_reader->StartCall();

    // Request that, upon completion of the RPC, "reply" be updated with the
    // server's response; "status" with the indication of whether the operation
    // was successful. Tag the request with the memory address of the call object.
    call->del_response_reader->Finish(&call->del_reply, &call->status, (void*)call);

}

// Loop while listening for completed responses.
// Prints out the response from the server.
void VideoStorageClient::AsyncCompleteRpc()
{
    void* got_tag;
    bool ok = false;

    // Block until the next result is available in the completion queue "cq".
    while (cq_.Next(&got_tag, &ok))
    {
        // The tag in this example is the memory location of the call object
        AsyncClientCall* call = static_cast<AsyncClientCall*>(got_tag);

        // Verify that the request was completed successfully. Note that "ok"
        // corresponds solely to the request for updates introduced by Finish().
        GPR_ASSERT(ok);
        if (call->status.ok())
        {
            if (call->request_type == 1)
            {
                //TODO,视频删除的响应,暂时不需要处理，后续需要考虑rpc客户端重试机制,同时rpc服务端需要保证幂等性
            }

        }
        else
        {
            AK_LOG_WARN << "RPC failed, please check rpc server";
        }

        delete call; //记得要析构掉自己
    }
}
