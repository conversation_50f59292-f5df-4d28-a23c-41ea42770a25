/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#include <iostream>
#include <memory>
#include <string>
#include <vector>
#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include "csvs.grpc.pb.h"
#include "AkLogging.h"

using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using VideoStorage::VsDelReply;
using VideoStorage::VsDelRequest;
using VideoStorage::VideoStorageMsg; //rpc服务名

//对rpc内部接口的封装
class VideoStorageClient
{
public:
    explicit VideoStorageClient(std::shared_ptr<Channel> channel)
        : stub_(VideoStorageMsg::NewStub(channel)) {}
    void DelVideoStorage(uint32_t vid);

    // Loop while listening for completed responses.
    // Prints out the response from the server.
    void AsyncCompleteRpc();

private:
    // struct for keeping state and data information
    // TODO,通过多态来处理AsyncClientCall的逻辑
    struct AsyncClientCall
    {
        int request_type; //0:视频存储; 1:视频片段删除
        //暂时使用多个reply来承载,会有内存占用过多的问题，后续处理
        VsDelReply del_reply;
        // Context for the client. It could be used to convey extra information to
        // the server and/or tweak certain RPC behaviors.
        ClientContext context;
        // Storage for the status of the RPC upon completion.
        Status status;
        std::unique_ptr<ClientAsyncResponseReader<VsDelReply>> del_response_reader;
        int32_t msg_id;
    };

    // Out of the passed in Channel comes the stub, stored here, our view of the
    // server's exposed services.
    std::unique_ptr<VideoStorageMsg::Stub> stub_;

    // The producer-consumer queue we use to communicate asynchronously with the
    // gRPC runtime.
    CompletionQueue cq_;
};

