#include <map>
#include <set>
#include <mutex>
#include "video_storage_mng.h"
#include "video_rpc_client.h"

extern VideoStorageClient* g_vs_client_ptr;

CVideoSchedMng* CVideoSchedMng::instance_ = NULL;

CVideoSchedMng* CVideoSchedMng::Instance()
{
    if (!instance_)
    {
        instance_ = new CVideoSchedMng();
    }
    return instance_;
}

int CVideoSchedMng::DelVs(const uint32_t video_id)
{
    //启动rpc客户端进行删除动作.
    g_vs_client_ptr->DelVideoStorage(video_id);
	return 0;
}

