#ifndef __ROUTE_VIDEO_STORAGE_MNG_H__
#define __ROUTE_VIDEO_STORAGE_MNG_H__

#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>

class CVideoSchedMng : public boost::noncopyable
{
public:
    CVideoSchedMng()
    {}
    ~CVideoSchedMng()
    {}
    static CVideoSchedMng* Instance();
    int DelVs(const uint32_t video_id);
private:
    static CVideoSchedMng*  instance_;
};

#endif //__ROUTE_VIDEO_STORAGE_MNG_H__
