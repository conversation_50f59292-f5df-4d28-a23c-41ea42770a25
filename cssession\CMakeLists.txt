CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (cssession C CXX)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}" )
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libcsbase.a pthread libevent.so libhiredis.a libglog.so libmysqlclient.so libgpr.so libgrpc.so libgrpc++.so libprotobuf.so libevpp.so -lssl -lcrypto -lcpprest -letcd-cpp-api  -levpp -levent -lboost_system)
SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../)
LINK_DIRECTORIES(${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/thirdlib ${BASE_SOURCE_DIR}/csbase/redis/hiredis ${BASE_SOURCE_DIR}/csbase/evpp/lib /usr/local/lib)

AUX_SOURCE_DIRECTORY(./ SRC_LIST_SESSION)
AUX_SOURCE_DIRECTORY(./dao SRC_LIST_SESSION_DAO)
AUX_SOURCE_DIRECTORY(../csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(../csbase/grpc/cssession SRC_LIST_BASE_GRPC)
AUX_SOURCE_DIRECTORY(../csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(../csbase/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(../csbase/jsoncpp0.5/src SRC_LIST_JSON)
AUX_SOURCE_DIRECTORY(../csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)

SET(BASE_LIST_INC ./dao ${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/mysql/include ${BASE_SOURCE_DIR}/csbase/Rldb ${BASE_SOURCE_DIR}/csbase/grpc/cssession 
	${BASE_SOURCE_DIR}/csbase/redis ${BASE_SOURCE_DIR}/csbase/grpc ${BASE_SOURCE_DIR}/csbase/grpc/gens ${BASE_SOURCE_DIR}/csbase/grpc/include  
	${BASE_SOURCE_DIR}/csbase/etcd ${BASE_SOURCE_DIR}/csbase/evpp ${BASE_SOURCE_DIR}/csbase/dbinterface ${BASE_SOURCE_DIR}/csbase/dbinterface/resident  
	${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/include ${BASE_SOURCE_DIR}/csbase/encrypt)

ADD_DEFINITIONS( -std=c++11 -g  -W -Wall -Werror -Wno-unused-parameter -Wno-deprecated -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories(${BASE_LIST_INC} ${CSBASE_SOURCE_DIR}/metrics /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include)

add_executable(cssession ${SRC_LIST_SESSION} ${SRC_LIST_SESSION_DAO} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_GRPC} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_ETCD} ${prefixed_file_list}
${SRC_LIST_JSON} ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_BASE_METRICS})

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
set_target_properties(cssession PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/cssession/lib")

target_link_libraries(cssession  ${DEPENDENT_LIBRARIES})
