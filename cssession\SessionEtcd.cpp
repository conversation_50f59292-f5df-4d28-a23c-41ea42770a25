#include "SessionEtcd.h"
#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <vector>
#include "util.h"
#include "session_server.h"

#include "evpp/event_watcher.h"

extern AKCS_SESSION_CONF gstAKCSConf;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}


void EtcdSrvInit()
{
    std::shared_ptr<evpp::EventLoop> etcd_loop(new evpp::EventLoop);
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.etcd_server_addr);//"ip:port;ip:port;..."


    std::string inner_addr = GetEth0IPAddr();
    inner_addr += ":9002";
    char inner_reg_info[64] = {0};
    ::snprintf(inner_reg_info, 64, "%s%s", "/akcs/cssession/innerip/", inner_addr.c_str());
    RegSrv2Etcd(inner_reg_info, inner_addr, 10, csbase::REG_INNER, etcd_loop.get());
    etcd_loop->Run();
}

