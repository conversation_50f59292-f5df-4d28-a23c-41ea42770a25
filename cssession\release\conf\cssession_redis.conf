cssessionCacheInstances=dev_sid,uid_sid,sid_node_uids
# dev_sid: 设备dev与csmain id的映射关系
dev_sid_host=127.0.0.1
dev_sid_port=8504
dev_sid_db=5
dev_sid_maxconncnt=2

# uid_sid: app uid与csmain id的映射关系
uid_sid_host=127.0.0.1
uid_sid_port=8504
uid_sid_db=6
uid_sid_maxconncnt=2

# sid_node_uids: sid的uids集合
sid_node_uids_host=127.0.0.1
sid_node_uids_port=8504
sid_node_uids_db=8
sid_node_uids_maxconncnt=2

#如果sentinels有值,代表启动主从，那么_host的配置就不生效，如果没有就是单机
sentinels=

