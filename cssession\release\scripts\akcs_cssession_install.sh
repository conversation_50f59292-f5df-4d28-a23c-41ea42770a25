#!/bin/bash

#csroute安装脚本,含csroute等两个组件
AKCS_INSTALL_PATH=/usr/local/akcs/cssession
AKCS_RUN_SCRIPT_NAME=cssessionrun.sh
AKCS_RUN_SCRIPT=${AKCS_INSTALL_PATH}/scripts/${AKCS_RUN_SCRIPT_NAME}
INSTALL_CONF=/etc/cssession_install.conf
HOST_IP=/etc/ip
WORK_DIR=`pwd`
PAKCAGES_ROOT=${WORK_DIR}/..
chmod 777 -R ${PAKCAGES_ROOT}/*

if [ ! -d /usr/local/akcs ];then
    mkdir /usr/local/akcs
fi

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null;
    #IP地址必须为全数字
    if [ $? -ne 0 ]
    then
        return 1
    fi
    ipaddr=$1
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值
    b=`echo $ipaddr|awk -F . '{print $2}'`
    c=`echo $ipaddr|awk -F . '{print $3}'`
    d=`echo $ipaddr|awk -F . '{print $4}'`
    for num in $a $b $c $d
    do
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间
        then
            return 1
        fi
    done
    return 0
}

EnterBasicSrvIPAddr()
{
    #输入redis内网IP
    yellow "Enter your redis server inner IPV4: \c"
    read REDIS_INNER_IP;

    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;

    #输入etcd内网IP
    yellow "Enter your etcd cluster servers inner IPV4,(eg:************:5204;************:15204;...): \c"
    read ETCD_INNER_IP;

    for ip in $REDIS_INNER_IP $MYSQL_INNER_IP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "REDIS_INNER_IP=$REDIS_INNER_IP" >>$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
    echo "ETCD_INNER_IP=$ETCD_INNER_IP" >>$INSTALL_CONF
}


 EchoHostIPAddr()
{
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
}
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    for ip in $SERVER_INNER_IP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
}
function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0

	fi
}
#added by chenyc,2019-03-29,分布式脚本，先确定本机的内外网地址,所有的ip信息放在:/etc/ip里面
if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the host ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi

EchoBasicSrvIPAddr()
{
    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip

    redis_inner_ip_str="REDIS_INNER_IP="
    redis_inner_ip_cat=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    redis_inner_ip=$redis_inner_ip_str$redis_inner_ip_cat
    echo $redis_inner_ip

    etcd_inner_ip_str="ETCD_INNER_IP="
    etcd_inner_ip_cat=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    etcd_inner_ip=$etcd_inner_ip_str$etcd_inner_ip_cat
    echo $etcd_inner_ip
}
#再确定redis、mysql、etcd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    REDIS_INNER_IP=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    ETCD_INNER_IP=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/cssession_install.conf>, please enter all information below:"
    EnterBasicSrvIPAddr
fi


#replace serverip,注意ip后面的等号不能有空格
sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" ${PAKCAGES_ROOT}/cssession/conf/cssession.conf
sed -i "s/^.*dev_sid_host=.*/dev_sid_host=${REDIS_INNER_IP}/g" ${PAKCAGES_ROOT}/cssession/conf/cssession_redis.conf
sed -i "s/^.*uid_sid_host=.*/uid_sid_host=${REDIS_INNER_IP}/g" ${PAKCAGES_ROOT}/cssession/conf/cssession_redis.conf
sed -i "s/^.*sid_node_uids_host=.*/sid_node_uids_host=${REDIS_INNER_IP}/g" ${PAKCAGES_ROOT}/cssession/conf/cssession_redis.conf
sed -i "s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g" ${PAKCAGES_ROOT}/cssession/conf/cssession.conf

#后于之前替换的代码,保证db redis配置不被覆盖
bash $WORK_DIR/redis-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/cssession/conf/cssession_redis.conf
bash $WORK_DIR/dbproxy-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/cssession/conf/cssession.conf


scriptpid=`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${AKCS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi

echo "stopping cssession services..."
${PAKCAGES_ROOT}/cssession_scripts/cssessionctl.sh stop
sleep 1
echo "making logs directories..."
if [ ! -d /var/log/cssessionlog ]; then
    mkdir -p /var/log/cssessionlog
fi

echo "copying akcs cssession files..."
if [ -d /usr/local/akcs/cssession ]; then
    rm -rf  /usr/local/akcs/cssession
fi

if [ -d /usr/local/akcs/cssession_scripts ]; then
    rm -rf -p /usr/local/akcs/cssession_scripts
fi

chmod 777 -R /usr/local/akcs/

cp -rf ${PAKCAGES_ROOT}/cssession/ /usr/local/akcs
#4.6新增md5sum校验，避免拷贝不完全
Md5sumCheck ${PAKCAGES_ROOT}/cssession/bin/cssession /usr/local/akcs/cssession/bin/cssession

mkdir -p /usr/local/akcs/cssession/scripts
chmod -R 777 /usr/local/akcs/cssession/scripts/
cp -rf ${PAKCAGES_ROOT}/cssession_scripts/* /usr/local/akcs/cssession/scripts/


#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "bash ${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi

echo "starting services..."
chmod 777 /usr/local/akcs/cssession/scripts/cssessionctl.sh
/usr/local/akcs/cssession/scripts/cssessionctl.sh start

sleep 1
chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup bash ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
fi
echo "cssession install completed ..."

#core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi
if [ -z "`grep "kernel.core_pattern" /etc/sysctl.conf`" ];then
	echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
fi

if [ -z "`grep "ulimit -c unlimited" /etc/profile`" ];then
	echo 'ulimit -c unlimited' >> /etc/profile
fi

sysctl -p >/dev/null
. /etc/profile
#echo status
/usr/local/akcs/cssession/scripts/cssessionctl.sh status
