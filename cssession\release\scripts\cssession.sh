#!/bin/bash
ACMD="$1"
CSSESSION_BIN='/usr/local/akcs/cssession/bin/cssession'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_cssession()
{
    nohup $CSSESSION_BIN >/dev/null 2>&1 &
    echo "Start cssession successful"
    if [ -z "`ps -fe|grep "cssessionrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/cssession/scripts/cssessionrun.sh >/dev/null 2>&1 &
    fi
}
stop_cssession()
{
    echo "Begin to stop cssessionrun.sh"
    cssessionrunid=`ps aux | grep -w cssessionrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${cssessionrunid}" ];then
	    echo "cssessionrun.sh is running at ${cssessionrunid}, will kill it first."
	    kill -9 ${cssessionrunid}
    fi
    echo "Begin to stop cssession"
    kill -9 `pidof cssession`
    sleep 2
    echo "Stop cssession successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 9002 | grep cssession | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_cssession
    else
        echo "cssession is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 9002 | grep cssession | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "cssession is already stopping"
    else
        stop_cssession
    fi
    ;;
  restart)
    stop_cssession
    sleep 1
    start_cssession
    ;;
  status)
    cnt=`ss -alnp | grep 9002 | grep cssession | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m cssession is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m cssession is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

