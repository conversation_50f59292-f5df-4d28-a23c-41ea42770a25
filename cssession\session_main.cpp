// cssession server
// Created on: 2019-04-12
// Author: chenyc

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <vector>
#include <string>
#include <map>
#include <thread>
#include <errno.h>
#include "glog/logging.h"
#include "AkLogging.h"
#include "util.h"
#include "Rldb/RldbQuery.h"
#include "CachePool.h"
#include "ConfigFileReader.h"
#include "session_server.h"
#include "session_rpc_server.h"
#include "session_mq_pub.h"
#include "SessionEtcd.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "HttpServer.h"
#include <KdcDecrypt.h>
#include "Metric.h"


AKCS_SESSION_CONF gstAKCSConf;
static const char cssession_conf_file[] = "/usr/local/akcs/cssession/conf/cssession.conf";
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_conf_db_addr;
int g_etcd_dns_res = 0;

#define PIDFILE "/var/run/cssession.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstAKCSConf.db_IP, gstAKCSConf.db_port);
    return 0;
}
void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.db_IP) != 0) || (conf_tmp.db_port != gstAKCSConf.db_port))
    {
        Snprintf(gstAKCSConf.db_IP, sizeof(gstAKCSConf.db_IP),  conf_tmp.db_ip);
        gstAKCSConf.db_port = conf_tmp.db_port;
        DaoReInit();
    }
}
int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstAKCSConf.db_IP, sizeof(gstAKCSConf.db_IP),  conf_tmp.db_ip);
    gstAKCSConf.db_port = conf_tmp.db_port;
    return 0;
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.etcd_server_addr);
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}


int ConfInit()
{
    CConfigFileReader config_file(cssession_conf_file);

    Snprintf(gstAKCSConf.session_outer_ip, sizeof(gstAKCSConf.session_outer_ip),  config_file.GetConfigName("cssession_outerip"));
    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username),  config_file.GetConfigName("db_username"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), decrypt_db_passwd.c_str());
    Snprintf(gstAKCSConf.db_database, sizeof(gstAKCSConf.db_database),  config_file.GetConfigName("db_database"));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    if(LoadConfFromConfSrv() != 0)
    {
        Snprintf(gstAKCSConf.db_IP, sizeof(gstAKCSConf.db_IP),  config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        gstAKCSConf.db_port = ATOI(db_port);
    }
    return 0;

}

/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    //数据库连接数和处理消息的线程数一致，避免并发造成等待
    conn_pool->Init(gstAKCSConf.db_IP, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.db_database, gstAKCSConf.db_port, 2, "cssession");
    return 0;
}

int InstanceInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/cssession/conf/cssession_redis.conf", "cssessionCacheInstances");
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstAKCSConf.etcd_server_addr;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    CConfigFileReader config_file(cssession_conf_file);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstAKCSConf.etcd_server_addr, sizeof(gstAKCSConf.etcd_server_addr),  config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstAKCSConf.etcd_server_addr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstAKCSConf.etcd_server_addr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}


int main()
{

    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        printf("another cssession has been running in this sytem.");
        return -1;
    }

    //glog初始化
    google::InitGoogleLogging("cssession");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/cssessionlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/cssessionlog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/cssessionlog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/cssessionlog/FATAL");
    FLAGS_logbufsecs = 0;
    FLAGS_max_log_size = 50;    //单日志文件最大50M

    //配置中心初始化
    //一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
	memset(&gstAKCSConf, 0, sizeof(AKCS_SESSION_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }   
    ConfSrvInit();
    /* 读取配置文件 */
    if (ConfInit() != 0)
    {
        AK_LOG_FATAL << "init conf failed";
        return -1;
    }
    if (DaoInit() != 0)
    {
        AK_LOG_FATAL << "init dao failed";
        return -1;
    }

    if (InstanceInit() != 0)
    {
        AK_LOG_FATAL << "init instance failed";
        return -1;
    }
    std::thread mqProduceThread = std::thread(MQProduceInit);
    
    std::thread etcdCliThread = std::thread(EtcdSrvInit);
    
    std::thread conf_watch_thread = std::thread(ConfWatch);
    //起http服务线程
    std::thread httpThread(startHttpServer);

    // 初始化metric单例
    InitMetricInstance();

    //rpc服务
    SmRpcServer rpc_server("9002");
    rpc_server.Run();

    AK_LOG_INFO << "cssession is running.";

    dnsThread.join();
    etcdCliThread.join();
    conf_watch_thread.join();

    return 0;
}

