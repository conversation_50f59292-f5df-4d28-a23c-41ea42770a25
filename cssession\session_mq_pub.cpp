#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include <evpp/evnsq/message.h>
#include "session_mq_pub.h"
#include "evpp/event_loop.h"
#include "AkcsMonitor.h"
#include "AkLogging.h"

const static uint32_t kAkMsgHoldLen = sizeof(int32_t);
bool g_nsq_ready = false;
RouteMQProduce* g_nsq_producer = nullptr;

void MQProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQMessage);//基本不需要关心
    client.SetReadyCallback(&OnNSQReady);//ready(与其中一个nsqd服务tcp连接上之后)之后才能开始发布消息.
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    g_nsq_producer = new RouteMQProduce(&client);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}


//msg不需要再判断nsq的响应消息，见: NSQConn::OnMessage
int OnRouteMQMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void OnNSQReady()
{
    g_nsq_ready = true;
}

bool RouteMQProduce::OnPublish(CAkcsPdu& pdu, const std::string& topic)
{
    const std::string msg(pdu.GetBuffer(), pdu.GetLength());
    if (!client_->Publish(topic, msg))
    {
        return false;
    }
    return true;
}

