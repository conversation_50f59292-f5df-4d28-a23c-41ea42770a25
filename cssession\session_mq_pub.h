#ifndef __CSSESSION_MQ_PRODUCE_H__
#define __CSSESSION_MQ_PRODUCE_H__

#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <string>
#include <evnsq/message.h>
#include <evnsq/producer.h>

void MQProduceInit();
void OnNSQReady();
int OnRouteMQMessage(const evnsq::Message* msg);

class RouteMQProduce
{
public:
    RouteMQProduce(evnsq::Producer* producer)
        : client_(producer)
    {}
    ~RouteMQProduce() {}

public:
    bool OnPublish(CAkcsPdu& pdu, const std::string& topic);
private:
    evnsq::Producer* client_;
};

#endif //__CSSESSION_MQ_PRODUCE_H__

