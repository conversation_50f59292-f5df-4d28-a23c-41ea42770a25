#include <memory>
#include <iostream>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include "AkLogging.h"
#include "CachePool.h"
#include "AkcsMsgDef.h"
#include "session_rpc_server.h"
#include "util.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


void SmRpcServer::Run()
{
    std::string listen_net = GetEth0IPAddr() + std::string(":");
    listen_net += rpc_port_;
    std::string server_address(listen_net);
    ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service_);
    cq_ = builder.AddCompletionQueue();//可以多个的.
    server_ = builder.BuildAndStart();
    AK_LOG_INFO << "Server listening on " << server_address;

    // Proceed to the server's main loop. 在构造函数里面触发CallData::Proceed()
    //这样不会触发多个线程,只是会触发:CallData::Proceed 中的这个流程而已 if (status_ == CREATE),
    //所以每个rpc接口都需要这个
    new CallData(&service_, cq_.get(), REG_DEV_SID);
    new CallData(&service_, cq_.get(), QUERY_DEV_SID);
    new CallData(&service_, cq_.get(), REG_UID_SID);
    new CallData(&service_, cq_.get(), QUERY_UID_SID);
    new CallData(&service_, cq_.get(), REM_UID_SID);//SmRpcServer::CallData::REM_UID_SID
    new CallData(&service_, cq_.get(), QUERY_UIDS_BY_SID_NODE);
    new CallData(&service_, cq_.get(), QUERY_DEV_UUID_SID);
    new CallData(&service_, cq_.get(), QUERY_ACCOUNT_UUID_SID);
    std::thread HandleRpcsThread(std::bind(&SmRpcServer::HandleRpcs, this));
    HandleRpcs();
}

void SmRpcServer::CallData::Proceed()
{
    if (status_ == CREATE)
    {
        // Make this instance progress to the PROCESS state.
        status_ = PROCESS;
        switch (s_type_)
        {
            case REG_DEV_SID:
            {
                service_->RequestRegDevSidHandle(&ctx_, &reg_dev_request_, &reg_dev_responder_, cq_, cq_, this);
                break;
            }
            case QUERY_DEV_SID:
            {
                service_->RequestQueryDevSidHandle(&ctx_, &query_dev_request_, &query_dev_responder_, cq_, cq_, this);
                break;
            }
            case QUERY_DEV_UUID_SID:
            {
                service_->RequestQueryDevUUIDSidHandle(&ctx_, &query_dev_uuid_request_, &query_dev_responder_, cq_, cq_, this);
                break;
            }            
            case REG_UID_SID:
            {
                service_->RequestRegUidSidHandle(&ctx_, &reg_uid_request_, &reg_uid_responder_, cq_, cq_, this);
                break;
            }
            case QUERY_UID_SID:
            {
                service_->RequestQueryUidSidHandle(&ctx_, &query_uid_request_, &query_uid_responder_, cq_, cq_, this);
                break;
            }
            case QUERY_ACCOUNT_UUID_SID:
            {
                service_->RequestQueryUUIDSidHandle(&ctx_, &query_uuid_request_, &query_uid_responder_, cq_, cq_, this);
                break;
            }
            case REM_UID_SID:
            {
                service_->RequestRemoveUidSidHandle(&ctx_, &remove_uid_request_, &remove_uid_responder_, cq_, cq_, this);
                break;
            }
            case QUERY_UIDS_BY_SID_NODE:
            {
                service_->RequestQueryUidsBySidNodeHandle(&ctx_, &query_uids_by_sid_node_request_, &query_uids_sid_node_responder_, cq_, cq_, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << s_type_;
                break;
            }
        }
    }
    else if (status_ == PROCESS)
    {
        status_ = FINISH;
        new CallData(service_, cq_, this->s_type_);
        switch (s_type_)
        {
            case REG_DEV_SID:
            {
                std::string mac = reg_dev_request_.dev_mac();
                std::string uuid = reg_dev_request_.dev_uuid();
                std::string srv_id = reg_dev_request_.srv_id();
                AK_LOG_INFO <<  "reg dev_sid, mac is:" << mac << ",uuid is:" << uuid <<", sid is:" << srv_id;
                //暂时只缓存在redis接口，还是需要考虑mysql持久化,否则一旦redis拒绝服务,那么监控业务无法进行,退化到发送给所有csmain，造成惊群效应
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("dev_sid"); //获取与redis实例的tcp连接
                if (cache_conn)
                {
                    cache_conn->set(mac, srv_id);//正常情况下,不需要校验是否set成功,因为这个只是当缓存而已
                    if (uuid.size() > 0)
                    {
                        cache_conn->set(uuid, srv_id);
                    }
                    cache_manager->RelCacheConn(cache_conn);
                }
                else
                {
                    AK_LOG_WARN <<  "no redis cache connection for dev_sid ";
                }

                reg_dev_reply_.set_ret(0);
                reg_dev_responder_.Finish(reg_dev_reply_, Status::OK, this); //再一次加入队列中
                break;
            }
            //dev_sid的缓存重建时间会很长,redis的持久化数据丢失,对业务影响很大,所以一旦发现缓存失效了,需要重建缓存
            case QUERY_DEV_SID:
            {
                std::string query_mac = query_dev_request_.dev_mac();
                std::string csmain_id;
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("dev_sid");
                if (cache_conn)
                {
                    csmain_id = cache_conn->get(query_mac);
                    if (csmain_id.size() == 0)
                    {
                        AK_LOG_WARN <<  "no dev_sid redis cache for key: " << query_mac << ", we will query info from mysql db";
                        ResidentDev per_dev;
                        memset(&per_dev, 0, sizeof(per_dev));
                        ResidentDev dev;
                        memset(&dev, 0, sizeof(dev));
                        std::string csmain_db;
                        if (0 == dbinterface::ResidentDevices::GetMacDev(query_mac, dev))
                        {
                            csmain_db = dev.acc_srv_id;
                        }
                        else if (0 == dbinterface::ResidentPerDevices::GetMacDev(query_mac, per_dev))
                        {
                            csmain_db = per_dev.acc_srv_id;
                        }
                        //redis格式:"csmain_10.80.242.143", db的存储格式: "10.80.242.143"
                        if (csmain_db.size() > 0)
                        {
                            csmain_id = std::string("csmain_") + csmain_db;
                            cache_conn->set(query_mac, csmain_id);//缓存重建
                        }
                        else
                        {
                            AK_LOG_WARN <<  "query db for key: " << query_mac << ", failed";
                        }
                    }
                    cache_manager->RelCacheConn(cache_conn);
                }
                query_dev_reply_.set_srv_id(csmain_id);
                query_dev_responder_.Finish(query_dev_reply_, Status::OK, this); //再一次加入队列中
                break;
            }
            case QUERY_DEV_UUID_SID:
            {
                std::string query_uuid = query_dev_uuid_request_.dev_uuid();
                std::string csmain_id;
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("dev_sid");
                if (cache_conn)
                {
                    csmain_id = cache_conn->get(query_uuid);
                    if (csmain_id.size() == 0)
                    {
                        AK_LOG_WARN <<  "no dev_sid redis cache for key: " << query_uuid << ", we will query info from mysql db";
                        ResidentDev per_dev;
                        memset(&per_dev, 0, sizeof(per_dev));
                        ResidentDev dev;
                        memset(&dev, 0, sizeof(dev));
                        std::string csmain_db;
                        if (0 == dbinterface::ResidentDevices::GetUUIDDev(query_uuid, dev))
                        {
                            csmain_db = dev.acc_srv_id;
                        }
                        else if (0 == dbinterface::ResidentPerDevices::GetUUIDDev(query_uuid, per_dev))
                        {
                            csmain_db = per_dev.acc_srv_id;
                        }
                        //redis格式:"csmain_10.80.242.143", db的存储格式: "10.80.242.143"
                        if (csmain_db.size() > 0)
                        {
                            csmain_id = std::string("csmain_") + csmain_db;
                            cache_conn->set(query_uuid, csmain_id);//缓存重建
                        }
                        else
                        {
                            AK_LOG_WARN <<  "query db for key: " << query_uuid << ", failed";
                        }
                    }
                    cache_manager->RelCacheConn(cache_conn);
                }
                query_dev_reply_.set_srv_id(csmain_id);
                query_dev_responder_.Finish(query_dev_reply_, Status::OK, this); //再一次加入队列中
                break;
            }            
            case REG_UID_SID:
            {
                std::string uid = reg_uid_request_.uid();
                std::string uuid = reg_uid_request_.uuid();
                std::string srv_id = reg_uid_request_.srv_id();
                AK_LOG_INFO <<  "reg uid_sid, uid is:" << uid << ", sid is:" << srv_id;
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("uid_sid"); //获取与redis实例的tcp连接
                if (cache_conn)
                {
                    cache_conn->set(uid, srv_id);//对于同一个key,每次set会value都会刷新.
                    if (uuid.size() > 0)
                    {
                        cache_conn->set(uuid, srv_id);
                    }
                    cache_manager->RelCacheConn(cache_conn);
                }
                //缓存csmain : uids
                CacheManager* cache_manager2 = CacheManager::getInstance();
                CacheConn* cache_conn2 = cache_manager2->GetCacheConn("sid_node_uids");
                if (cache_conn2)
                {
                    cache_conn2->sadd(srv_id, uid);
                    cache_manager2->RelCacheConn(cache_conn2);
                }
                reg_uid_reply_.set_ret(0);
                reg_uid_responder_.Finish(reg_uid_reply_, Status::OK, this);
                break;

            }
            //uid_sid的缓存重建时间比较短,所以即使redis的持久化数据丢失,对业务影响也不会很大
            case QUERY_UID_SID:
            {
                std::string query_uid = query_uid_request_.uid();
                std::string csmain_id;
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("uid_sid"); //获取与redis实例的tcp连接
                if (cache_conn)
                {
                    csmain_id = cache_conn->get(query_uid);
                    if (csmain_id.size() == 0) //已经过期或者还没有设置
                    {
                        AK_LOG_WARN <<  "no uid_sid redis cache for key " << query_uid;
                    }
                    cache_manager->RelCacheConn(cache_conn);
                }
                query_uid_reply_.set_srv_id(csmain_id);
                query_uid_responder_.Finish(query_uid_reply_, Status::OK, this); //再一次加入队列中
                break;
            }
            case QUERY_ACCOUNT_UUID_SID:
            {
                std::string query_uuid = query_uuid_request_.uuid();
                std::string csmain_id;
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("uid_sid");
                if (cache_conn)
                {
                    csmain_id = cache_conn->get(query_uuid);
                    if (csmain_id.size() == 0)
                    {
                        AK_LOG_WARN <<  "no dev_sid redis cache for key: " << query_uuid;
                    }
                    cache_manager->RelCacheConn(cache_conn);
                }
                query_uid_reply_.set_srv_id(csmain_id);
                query_uid_responder_.Finish(query_uid_reply_, Status::OK, this); //再一次加入队列中
                break;
            }            
            case REM_UID_SID:
            {
                std::string query_uid = remove_uid_request_.uid();
                std::string csmain_id = remove_uid_request_.srv_id();
                AK_LOG_INFO <<  "remove uid_sid, uid is:" << query_uid << ", sid is:" << csmain_id;
                //清理缓存csmain : uids, 缓存uid_sid不需要处理
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("sid_node_uids");
                if (cache_conn)
                {
                    cache_conn->srem(csmain_id, query_uid);
                    AK_LOG_WARN <<  "remove sid_uids, sid:" << csmain_id << "uid:" << query_uid;
                    cache_manager->RelCacheConn(cache_conn);
                }
                remove_uid_reply_.set_ret(0);
                remove_uid_responder_.Finish(remove_uid_reply_, Status::OK, this);
                break;
            }
            case QUERY_UIDS_BY_SID_NODE: //只有csmain进程拉起的时候,才会查询这个接口
            {
                std::string csmain_id = query_uids_by_sid_node_request_.sid();
                std::string node = query_uids_by_sid_node_request_.node();
                std::set<std::string> sid_uids;
                CacheManager* cache_manager = CacheManager::getInstance();
                CacheConn* cache_conn = cache_manager->GetCacheConn("sid_node_uids");
                if (cache_conn)
                {
                    cache_conn->smembers(csmain_id, sid_uids);//先获取这个csmain负责管理的uid列表
                    cache_manager->RelCacheConn(cache_conn);
                }
                //再获取node下面的数据库uid列表
                std::set<std::string> node_uids;
                if (dbinterface::ResidentPersonalAccount::GetAttendantListByUid(node, node_uids) != 0)
                {
                    AK_LOG_INFO <<  "GetAttendantListByUid failed";
                }
                
                uint32_t size = node_uids.size();
                std::vector<std::string> inter_uids(size);//两者取交集,就获取到想要的数据
                std::vector<std::string>::iterator it = std::set_intersection(sid_uids.begin(), sid_uids.end(),
                                                        node_uids.begin(), node_uids.end(), inter_uids.begin());
                inter_uids.resize(it - inter_uids.begin());
                for (const auto& uid : inter_uids)
                {
                    query_uids_by_sid_node_reply_.add_uid_list(uid);
                }
                query_uids_sid_node_responder_.Finish(query_uids_by_sid_node_reply_, Status::OK, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << s_type_;
                break;
            }
        }

    }
    else
    {
        GPR_ASSERT(status_ == FINISH);
        // Once in the FINISH state, deallocate ourselves (CallData).
        delete this;
    }
}

// This can be run in multiple threads if needed.
void SmRpcServer::HandleRpcs()
{
    //TODO 当开启多线程的时候,这个必须挪到业务线程之前?
    //new CallData(&service_, cq_.get());
    void* tag;  // uniquely identifies a request.
    bool ok;
    while (true)
    {
        {
            std::lock_guard<std::mutex> lock(mtx_cq_);
            //GPR_ASSERT(cq_->Next(&tag, &ok));
            //GRPC_QUEUE_SHUTDOWN TIMEOUT时assert失败；
            //GPR_ASSERT(ok);
            bool status = cq_->Next(&tag, &ok);
            if ((status != true) || (ok != true))
            {
                AK_LOG_WARN << "cq_->Next return status : " << status << ", ok :" << ok;
                continue;
            }
        }
        static_cast<CallData*>(tag)->Proceed();
    }
}

