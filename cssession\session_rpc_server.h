#include <memory>
#include <iostream>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>

#include "AK.Session.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"

using grpc::Server;
using grpc::ServerAsyncResponseWriter;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::ServerCompletionQueue;
using grpc::Status;

using AK::Session::RegDevRequest;
using AK::Session::RegDevReply;
using AK::Session::QueryDevRequest;
using AK::Session::QueryDevUUIDRequest;
using AK::Session::QueryDevReply;

using AK::Session::RegUidRequest;
using AK::Session::RegUidReply;
using AK::Session::QueryUidRequest;
using AK::Session::QueryUUIDRequest;
using AK::Session::QueryUidReply;
using AK::Session::RemoveUidRequest;
using AK::Session::RemoveUidReply;
using AK::Session::QueryUidsBySidNodeRequest;
using AK::Session::QueryUidsBySidNodeReply;

using AK::Session::SmRpcSrv; //rpc服务名

class SmRpcServer
{
public:

    SmRpcServer(const std::string& port)
    {
        rpc_port_ = port;
    }
    ~SmRpcServer()
    {
        server_->Shutdown();
        // Always shutdown the completion queue after the server.
        cq_->Shutdown();
    }
    // There is no shutdown handling in this code.
    void Run();
private:
    // Class encompasing the state and logic needed to serve a request.
    class CallData
    {
    public:

    public:
        CallData(SmRpcSrv::AsyncService* service, ServerCompletionQueue* cq, SessionSrvType s_type)
            : service_(service), cq_(cq), s_type_(s_type), reg_dev_responder_(&ctx_), query_dev_responder_(&ctx_),
              reg_uid_responder_(&ctx_), query_uid_responder_(&ctx_), remove_uid_responder_(&ctx_),
              query_uids_sid_node_responder_(&ctx_), status_(CREATE)
        {
            // Invoke the serving logic right away.
            Proceed();
        }

        void Proceed();

    private:
        // The means of communication with the gRPC runtime for an asynchronous
        // server.
        SmRpcSrv::AsyncService* service_;
        // The producer-consumer queue where for asynchronous server notifications.  客户端用的是:CompletionQueue,都是生产者消费者的模型
        ServerCompletionQueue* cq_;
        // Context for the rpc, allowing to tweak aspects of it such as the use
        // of compression, authentication, as well as to send metadata back to the
        // client.
        ServerContext ctx_;
        //多个接口服务用这个来标示
        SessionSrvType s_type_;

        RegDevRequest reg_dev_request_;
        RegDevReply reg_dev_reply_;

        QueryDevRequest query_dev_request_; //mac
        QueryDevUUIDRequest query_dev_uuid_request_; //uuid
        QueryDevReply query_dev_reply_;

        RegUidRequest reg_uid_request_;
        RegUidReply reg_uid_reply_;

        QueryUidRequest query_uid_request_;    //uid
        QueryUUIDRequest query_uuid_request_;  //uuid
        QueryUidReply query_uid_reply_;

        RemoveUidRequest remove_uid_request_;
        RemoveUidReply remove_uid_reply_;

        QueryUidsBySidNodeRequest query_uids_by_sid_node_request_;
        QueryUidsBySidNodeReply query_uids_by_sid_node_reply_;

        // The means to get back to the client.
        //对于客户端则是: ClientAsyncResponseReader 读，都是针对reply而言的..
        ServerAsyncResponseWriter<RegDevReply> reg_dev_responder_;
        ServerAsyncResponseWriter<QueryDevReply> query_dev_responder_;
        ServerAsyncResponseWriter<RegUidReply> reg_uid_responder_;
        ServerAsyncResponseWriter<QueryUidReply> query_uid_responder_;
        ServerAsyncResponseWriter<RemoveUidReply> remove_uid_responder_;
        ServerAsyncResponseWriter<QueryUidsBySidNodeReply> query_uids_sid_node_responder_;

        // Let's implement a tiny state machine with the following states.
        enum CallStatus { CREATE, PROCESS, FINISH };
        CallStatus status_;  // The current serving state.
    };

private:
    // This can be run in multiple threads if needed.
    void HandleRpcs();
private:
    std::unique_ptr<ServerCompletionQueue> cq_;//一个服务可以有多个CompletionQueue
    SmRpcSrv::AsyncService service_;//指服务接口
    std::unique_ptr<Server> server_;//指服务器
    std::mutex mtx_cq_;
    std::string rpc_port_;
};


