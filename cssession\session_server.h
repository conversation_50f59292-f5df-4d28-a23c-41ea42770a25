#ifndef __CSSESSION_STORAGE_SER_H__
#define __CSSESSION_STORAGE_SER_H__

#define CSSTORAGE_CONF_COMMON_LEN 64

typedef struct AKCS_SESSION_CONF_T
{
    /* csstorage本机配置信息 */
    char session_outer_ip[CSSTORAGE_CONF_COMMON_LEN];

    /* DB配置项 */
    char db_IP[CSSTORAGE_CONF_COMMON_LEN];
    char db_username[CSSTORAGE_CONF_COMMON_LEN];
    char db_password[CSSTORAGE_CONF_COMMON_LEN];
    char db_database[CSSTORAGE_CONF_COMMON_LEN];
    int  db_port;
    char etcd_server_addr[CSSTORAGE_CONF_COMMON_LEN];

} AKCS_SESSION_CONF;


#endif  //__CSSESSION_STORAGE_SER_H__
