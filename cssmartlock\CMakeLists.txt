CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (cssmartlock  CXX)
SET(CSBASE_DIR ../csbase)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libhiredis.a libevent.so libglog.so libmysqlclient.so libgpr.so libgrpc.so libgrpc++.so libevpp.so -lssl -lcrypto -lcpprest -letcd-cpp-api  -levpp -levent -lboost_system -lpaho-mqtt3as -lpaho-mqtt3cs protobuf)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

AUX_SOURCE_DIRECTORY(./src SRC_LIST)
AUX_SOURCE_DIRECTORY(./src/core SRC_LIST_CORE)
AUX_SOURCE_DIRECTORY(./src/main SRC_LIST_MAIN)
AUX_SOURCE_DIRECTORY(./src/route SRC_LIST_ROUTE)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/encrypt SRC_LIST_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/jsoncpp0.5/src SRC_LIST_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/protobuf SRC_LIST_PROTOBUF)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/nsq SRC_LIST_BASE_NSQ)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/loop SRC_LIST_BASE_LOOP)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR} 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/thirdlib 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/evpp/lib 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/mqtt/lib 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/redis/hiredis
/usr/local/lib)


SET(BASE_LIST_INC ${CSBASE_DIR} ${CSBASE_DIR}/mysql/include ${CSBASE_DIR}/Rldb ${CSBASE_DIR}/evpp ${CSBASE_DIR}/etcd ${CSBASE_DIR}/redis ${CSBASE_DIR}/redis/hiredis
     ${CSBASE_DIR}/grpc 
     ${CSBASE_DIR}/grpc/gens 
     ${CSBASE_DIR}/grpc/include
	 ${CSBASE_DIR}/encrypt
     ${CSBASE_DIR}/mqtt/include
     ${CSBASE_DIR}/jsoncpp0.5/include
     ${CSBASE_DIR}/protobuf
     ${CSBASE_DIR}/redis
     ${CSBASE_DIR}/nsq
     ${CSBASE_DIR}/redis/hiredis
	${CSBASE_DIR}/metrics 
     ./src/core
     ./src/main)

ADD_DEFINITIONS(-std=c++11 -g -W -Wall -Werror -Wno-unused-parameter -Wno-deprecated)
                           
include_directories(${BASE_LIST_INC} ./src)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

add_executable(cssmartlock ${SRC_LIST} ${SRC_LIST_CORE} ${SRC_LIST_MAIN} ${SRC_LIST_ROUTE} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_ENCRYPT} ${SRC_LIST_JSON}
${SRC_LIST_PROTOBUF} ${SRC_LIST_BASE_REDIS}  ${prefixed_file_list} ${SRC_LIST_BASE_NSQ} ${SRC_LIST_BASE_METRICS} ${SRC_LIST_BASE_LOOP})

set_target_properties(cssmartlock PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/cssmartlock/lib")
target_link_libraries(cssmartlock  ${DEPENDENT_LIBRARIES})
