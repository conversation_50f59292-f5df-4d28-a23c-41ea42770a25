#!/bin/bash
ACMD="$1"
cssmartlock_BIN='/usr/local/akcs/cssmartlock/bin/cssmartlock'
PROCESS_PID_FILE=/var/run/cssmartlock.pid
if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

if [ -f $PROCESS_PID_FILE ];then
    pid=`cat $PROCESS_PID_FILE`
else
    #重启之后没有这个pid文件
    pid="xxxxxxxxxx"
fi

start_cssmartlock()
{
    nohup $cssmartlock_BIN >/dev/null 2>&1 &
    echo "Start cssmartlock successful"
    if [ -z "`ps -fe|grep "cssmartlockrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/cssmartlock/scripts/cssmartlockrun.sh >/dev/null 2>&1 &
    fi
}
stop_cssmartlock()
{
    echo "Begin to stop cssmartlockrun.sh"
    kill -9 `ps aux | grep -w cssmartlockrun.sh | grep -v grep | awk '{ print $(2) }'`
    echo "Begin to stop cssmartlock"
    kill -9 `pidof cssmartlock`
    sleep 2
    echo "Stop cssmartlock successful"
}

case $ACMD in
  start)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_cssmartlock
    else
        echo "cssmartlock is already running"
    fi
    ;;
  stop)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "cssmartlock is already stopping"
    else
        stop_cssmartlock
    fi
    ;;
  restart)
    stop_cssmartlock
    sleep 1
    start_cssmartlock
    ;;
  status)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m cssmartlock is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m cssmartlock is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

