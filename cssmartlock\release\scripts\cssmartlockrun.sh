#!/bin/bash
PROCESS_NAME=cssmartlock
PROCESS_START_CMD="/usr/local/akcs/cssmartlock/scripts/cssmartlockctl.sh start"
PROCESS_PID_FILE=/var/run/cssmartlock.pid
LOG_FILE=/var/log/cssmartlock_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/cssmartlock/scripts/common.sh"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done
