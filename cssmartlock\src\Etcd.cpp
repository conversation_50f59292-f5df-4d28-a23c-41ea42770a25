#include "catch.hpp"
#include <etcd/Client.hpp>
#include <vector>
#include "EtcdCliMng.h"
#include "ServiceConf.h"
#include "util.h"
#include "RouteClient.h"
#include "RouteClientMng.h"
#include "evpp/event_watcher.h"
#include "loop/RouteLoopManager.h"

extern SERVICE_CONF g_service_conf;
extern CAkEtcdCliManager* g_etcd_cli_mng;
static evpp::SignalEventWatcher* ev = nullptr;
extern const char *g_ak_srv_route;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

//与所有的csroute建立tcp连接
void RouteSrvConnInit(const std::set<std::string>& csroute_addrs, evpp::EventLoop* loop,
                      const std::string& logic_srv_id)
{
    for (const auto& csroute : csroute_addrs) //ip:port的形式
    {
        RouteClientPtr route_cli_ptr(new CRouteClient(loop, csroute, "cssmartlock client", logic_srv_id));
        route_cli_ptr->Start();
        CRouteClientMng::Instance()->AddRouteSrv(csroute, route_cli_ptr);
    }
}

//监控的回调函数,不能阻塞
void UpdateRouteSrvList()
{
    std::string logic_srv = "cssmartlock";
    logic_srv += GetEth0IPAddr();

    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) == 0)
    {
        //更新route的连接列表
        CRouteClientMng::Instance()->UpdateRouteSrv(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), logic_srv);
    }
}


void EtcdSrvInit()
{
    std::string logic_srv = "cssmartlock";
    logic_srv += GetEth0IPAddr();
    
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_service_conf.etcd_server_addr);//"ip:port;ip:port;..."
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    RouteSrvConnInit(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), logic_srv);

    GetRouteLoopManagerInstance()->StartLoop();

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_route, UpdateRouteSrvList);
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get());
    
    g_etcd_loop->Run();//etcd_loop 目前只有route的连接在用

}

