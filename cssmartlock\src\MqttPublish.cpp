#include "MqttPublish.h"
#include "AkLogging.h"
#include "ServiceConf.h"
#include <vector>


MqttPublish* g_mqtt_publish = nullptr; 
extern SERVICE_CONF g_service_conf; 
bool MqttPublish::status_ = true;

void MqttPublishInit()
{
    std::string client_id = MQTT_PUB_CLIENTID;
    client_id += g_service_conf.server_inner_ip;
    g_mqtt_publish = new MqttPublish(g_service_conf.mqtt_addr, client_id);
}

void MqttPublishReInit()
{
    if(g_mqtt_publish)
    {
        delete g_mqtt_publish;
    }
    
    MqttPublishInit();
}

MqttPublish::MqttPublish(const std::string& mqtt_address, const std::string& client_id)
{
    MQTTAsync_connectOptions conn_opts = MQTTAsync_connectOptions_initializer;

    if (MQTTAsync_create(&client_, mqtt_address.c_str(), client_id.c_str(), MQTTCLIENT_PERSISTENCE_NONE, nullptr) != MQTTASYNC_SUCCESS)
    {
        return;
    }

    if (MQTTAsync_setCallbacks(client_, client_, Connlost, MsgArrvd, nullptr) != MQTTASYNC_SUCCESS)
    {
        return;
    }

    if (MQTTAsync_setConnected(client_, client_, OnConnect) != MQTTASYNC_SUCCESS)
    {
        return;
    }

    conn_opts.keepAliveInterval = 20;
    conn_opts.cleansession = 1;
    conn_opts.onFailure = OnConnectFailure;
    conn_opts.context = client_;    
    conn_opts.username = MQTT_PUB_USERNAME;
    conn_opts.password = MQTT_PUB_PASSWORD;
    conn_opts.automaticReconnect = 1;//设置非零，断开自动重连
    conn_opts.minRetryInterval = 3; //单位秒，重连间隔次数，每次重新连接失败时，重试间隔都会加倍，直到最大间隔
    conn_opts.maxRetryInterval = 60;//单位秒，最大重连尝试间隔
    if (MQTTAsync_connect(client_, &conn_opts) != MQTTASYNC_SUCCESS)
    {
        return;
    }
}

MqttPublish::~MqttPublish()
{
	MQTTAsync_disconnectOptions opts = MQTTAsync_disconnectOptions_initializer;
    MQTTAsync_disconnect(client_, &opts);
    MQTTAsync_destroy(&client_);
}


int MqttPublish::Publish(const std::string& topic, const std::string& message, int qos)
{
    if(!client_)
    {
    	AK_LOG_WARN << "client not init";
        return -1;
    }
    
	MQTTAsync_responseOptions opts = MQTTAsync_responseOptions_initializer;
	MQTTAsync_message pubmsg = MQTTAsync_message_initializer;

	opts.onFailure = OnSendFailure;
	opts.context = client_;
    // 创建一个临时数组来存储消息数据
    char payload[4096] = {0};
    snprintf(payload, sizeof(payload), "%s", message.c_str());
	pubmsg.payload =  payload;
	pubmsg.payloadlen = (int)message.size();
	pubmsg.qos = qos;
	pubmsg.retained = 0;
	if (MQTTAsync_sendMessage(client_, topic.c_str(), &pubmsg, &opts) != MQTTASYNC_SUCCESS)
	{
		return -1;
	}
    AK_LOG_INFO << "Message publish success, topic:" << topic << " message:" << message;
    return 0;
}

int MqttPublish::MsgArrvd(void* context, char* topicName, int topicLen, MQTTAsync_message* m)
{
	/* not expecting any messages */
    //成功必须return 1
	return 1;
}

void MqttPublish::OnSendFailure(void* context, MQTTAsync_failureData* response)
{
	AK_LOG_WARN << "Message send failed token: " << response->token << ",error code " << response->code;
}

void MqttPublish::OnConnectFailure(void* context, MQTTAsync_failureData* response)
{
    AK_LOG_WARN << "Failed to connect mqtt server";
    status_ = false;
}

void MqttPublish::OnConnect(void *context, char *cause)
{
	AK_LOG_INFO << "Success connect mqtt";
    status_ = true;
}

void MqttPublish::Connlost(void *context, char *cause)
{
	AK_LOG_WARN << "mqtt connection lost";
	if (cause)
    {   
		AK_LOG_WARN << "Connlost cause: " << cause;
    }
}


