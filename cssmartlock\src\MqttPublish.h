#ifndef __MQTT_ASYNC_PUBLISH_H__
#define __MQTT_ASYNC_PUBLISH_H__
#include "MQTTAsync.h"
#include <string>

#define MQTT_PUB_USERNAME    "akcs"
#define MQTT_PUB_PASSWORD    "mqttAk20#24!ypt"
#define MQTT_PUB_ADDRESS     "tcp://dev-hz.akuvox.com:8581"
#define MQTT_PUB_CLIENTID    "akcs_pub_"
#define MQTT_PUB_TOPIC          "$sys/down/v1/edge/ack/"

#define MQTT_SUB_LOCK_DOWN_TOPIC  "$sys/down/v1/edge/" 
#define MQTT_SUB_LOCK_DOWN_ACK_TOPIC  "$sys/down/v1/edge/ack/" 

class MqttPublish {
public:
    MqttPublish(const std::string& mqtt_address, const std::string& client_id);
    ~MqttPublish();

    int Publish(const std::string& topic, const std::string& message, int qos=1);
    bool Status() {
        return status_;
    }

private:     
    static int MsgArrvd(void* context, char* topicName, int topicLen, MQTTAsync_message* m);    
    static void OnSendFailure(void* context, MQTTAsync_failureData* response);
    static void OnConnectFailure(void* context, MQTTAsync_failureData* response);
    static void OnConnect(void *context, char *cause);
    static void Connlost(void *context, char *cause);
        
private:
    MQTTAsync client_;
    static bool status_;
};


void MqttPublishInit();
void MqttPublishReInit();



#endif// __MQTT_ASYNC_PUBLISH_H__

