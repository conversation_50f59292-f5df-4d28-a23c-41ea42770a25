#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "MqttSubscribe.h"
#include "AkLogging.h"
#include "MessageFactory.h"
#include "UpMessageSL50Factory.h"
#include "ServiceConf.h"
#include <thread>
#include <sstream>
#include "CommandQueueManager.h"

extern SERVICE_CONF g_service_conf; 
bool MqttSubscribe::status_ = true;

void MqttSubscribe::Connlost(void *context, char *cause)
{
	AK_LOG_WARN << "mqtt connection lost";
	if (cause)
    {   
		AK_LOG_WARN << "Connlost cause: " << cause;
    }
}


int MqttSubscribe::MsgArrvd(void *context, char *topic_name, int topicLen, MQTTAsync_message *message)
{
    AK_LOG_INFO << "Message arrived, topic:" << topic_name << " message:" << (char*)message->payload;
    std::string receive_msg = (char*)message->payload;

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(receive_msg, root))
    {
        AK_LOG_WARN << "parse json error.msg=" << receive_msg;
		MQTTAsync_freeMessage(&message);
		MQTTAsync_free(topic_name);
		return 1;
    }
    std::string command;
    if (root.isMember("command"))       
    {
        command = root["command"].asString();
        CommandQueueManager::GetInstance()->EnqueueMessage(command, receive_msg, topic_name);		
    }           
    else
    {
        AK_LOG_WARN << "receive msg no command";
    }
	MQTTAsync_freeMessage(&message);
	MQTTAsync_free(topic_name);
    return 1;
}

void MqttSubscribe::OnSubscribe(void* context, MQTTAsync_successData* response)
{
	AK_LOG_INFO << "Subscribe succeeded";
}

void MqttSubscribe::OnSubscribeFailure(void* context, MQTTAsync_failureData* response)
{
	AK_LOG_ERROR << "Subscribe failed, rc " << response->code;
}


void MqttSubscribe::OnConnectFailure(void* context, MQTTAsync_failureData* response)
{
    AK_LOG_ERROR << "Failed to connect mqtt server";
    status_ = false;
}

void MqttSubscribe::OnConnect(void *context, char *cause)
{
	MQTTAsync client = (MQTTAsync)context;
	MQTTAsync_responseOptions opts = MQTTAsync_responseOptions_initializer;
	int rc = 0;

	AK_LOG_INFO << "Success connect mqtt";

	opts.onSuccess = OnSubscribe;
	opts.onFailure = OnSubscribeFailure;
	opts.context = client;

    // 定义要订阅的多个主题
    char* topics[] = {(char*)MQTT_SUB_TOPIC, (char*)MQTT_SUB_LOCK_UP_ACK_TOPIC};
    int qoss[] = {MQTT_SUB_QOS, MQTT_SUB_QOS};
    int count = 2;

	if ((rc = MQTTAsync_subscribeMany(client, count, topics, qoss, &opts)) != MQTTASYNC_SUCCESS)
	{
		AK_LOG_ERROR << "Failed to start subscribe, return code " << rc;
	}

    status_ = true;
}

int MqttSubscribe::Init()
{
	MQTTAsync client;
	MQTTAsync_connectOptions conn_opts = MQTTAsync_connectOptions_initializer;
    std::string client_id = MQTT_SUB_CLIENTID;
    client_id += g_service_conf.server_inner_ip;
    
    // 添加线程ID
    std::stringstream ss;
    ss << std::this_thread::get_id();
    client_id += "_" + ss.str();

	if (MQTTAsync_create(&client, g_service_conf.mqtt_addr, client_id.c_str(), MQTTCLIENT_PERSISTENCE_NONE, nullptr) != MQTTASYNC_SUCCESS)
	{
        return -1;
	}

	if (MQTTAsync_setCallbacks(client, client, Connlost, MsgArrvd, nullptr) != MQTTASYNC_SUCCESS)
	{
        return -1;
	}

    if (MQTTAsync_setConnected(client, client, OnConnect) != MQTTASYNC_SUCCESS)
    {
        return -1;
    }


	conn_opts.keepAliveInterval = 20;
	conn_opts.cleansession = 1; //设为0可以收到重启时间窗口内的数据，但维护这个session可能会出现有些客户端配置还缓存在emqx的情况（需手动清除），所以先不开
	conn_opts.onFailure = OnConnectFailure;
	conn_opts.context = client;    
    conn_opts.username = MQTT_SUB_USERNAME;
    conn_opts.password = MQTT_SUB_PASSWORD;
    conn_opts.automaticReconnect = 1;//设置非零，断开自动重连
    conn_opts.minRetryInterval = 3; //单位秒，重连间隔次数，每次重新连接失败时，重试间隔都会加倍，直到最大间隔
    conn_opts.maxRetryInterval = 60;//单位秒，最大重连尝试间隔
	if (MQTTAsync_connect(client, &conn_opts) != MQTTASYNC_SUCCESS)
	{
        return -1;
	}


    return 0;

}
