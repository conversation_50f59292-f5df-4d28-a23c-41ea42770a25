#ifndef __MQTT_ASYNC_SUBSCRIBE_H__
#define __MQTT_ASYNC_SUBSCRIBE_H__
#include "MQTTAsync.h"

#define MQTT_SUB_ADDRESS     "tcp://dev-hz.akuvox.com:8581"
#define MQTT_SUB_CLIENTID    "akcs_sub_"
#define MQTT_SUB_TOPIC       "$queue/$sys/up/#"
#define MQTT_SUB_LOCK_TOPIC  "$sys/up/v1/edge/"
#define MQTT_SUB_QOS         1
#define MQTT_SUB_USERNAME    "akcs"
#define MQTT_SUB_PASSWORD    "mqttAk20#24!ypt"

class MqttSubscribe
{

public:
    static int Init();
    static bool Status() {
        return status_;
    }

private:
    MqttSubscribe() = delete;
    ~MqttSubscribe() = delete;

    static void Connlost(void *context, char *cause);    
    static int MsgArrvd(void *context, char *topicName, int topicLen, MQTTAsync_message *message);
    static void OnSubscribe(void* context, MQTTAsync_successData* response);    
    static void OnSubscribeFailure(void* context, MQTTAsync_failureData* response);    
    static void OnConnectFailure(void* context, MQTTAsync_failureData* response);    
    static void OnConnect(void *context, char *cause);

    static bool status_;
};

#endif// __MQTT_ASYNC_SUBSCRIBE_H__

