#ifndef __ROUTE_CLIENT_H__
#define __ROUTE_CLIENT_H__

#include <evpp/tcp_client.h>
#include <memory>
#include <string>
#include <event_loop.h>
#include "AkcsIpcMsgCodec.h"

class CRouteClient;
typedef std::shared_ptr<CRouteClient> RouteClientPtr;

class CRouteClient
{
public:
    CRouteClient(evpp::EventLoop* loop,
                 const std::string& server_addr/*ip:port*/,
                 const std::string& name,
                 const std::string& logic_srv_id);

    void Start()
    {
        client_.Connect();
        client_.set_auto_reconnect(true);
    }
    void Stop();
    void ReConnectByNewSeverAddr(const std::string& server_addr)
    {
        addr_ = server_addr;
        client_.ReconnectByNewServerAddr(server_addr);
    }
    bool IsConnStatus();
    void OnRoutePing();
    void onRoutePingCheckTimer();
    std::string GetAddr();
private:
    void OnConnection(const evpp::TCPConnPtr& conn);
    void OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);

private:
    evpp::TCPClient client_;
    std::string addr_;
    AkcsIpcMsgCodec route_codec_;
    std::string logic_srv_id_;
    std::atomic<bool> connect_status_;//与tcp服务器是否连接的状态标示符
    std::atomic<bool> ping_status_;//ping的状态标记

};

#endif 