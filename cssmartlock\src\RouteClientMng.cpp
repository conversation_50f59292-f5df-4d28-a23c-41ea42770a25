#include <map>
#include <set>
#include <mutex>
#include "RouteClientMng.h"
#include "AkLogging.h"

CRouteClientMng* CRouteClientMng::instance_ = NULL;

CRouteClientMng* CRouteClientMng::Instance()
{
    if (!instance_)
    {
        instance_ = new CRouteClientMng();
    }
    return instance_;
}

void CRouteClientMng::AddRouteSrv(const std::string& route_addr, const RouteClientPtr& route_cli)
{
    std::lock_guard<std::mutex> lock(route_clis_mutex_);
    route_clis_.insert(std::pair<std::string, RouteClientPtr>(route_addr, route_cli));
}

void CRouteClientMng::UpdateRouteSrv(const std::set<std::string>& route_addrs, evpp::EventLoop* etcd_loop,
                                     const std::string& logic_srv_id)
{
    //TODO,2019-02-28,后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(route_clis_mutex_);
    for (const auto& route_addr : route_addrs) //先检查新上线的route srv
    {
        auto it = route_clis_.find(route_addr);
        if (it == route_clis_.end())
        {
            AK_LOG_INFO << "add  route_cli_ptr " << route_addr;
            RouteClientPtr route_cli_ptr(new CRouteClient(etcd_loop, route_addr, "csroute client", logic_srv_id));
            route_cli_ptr->Start();
            route_clis_.insert(std::pair<std::string, RouteClientPtr>(route_addr, route_cli_ptr));
        }
        else
        {
            if (!it->second->IsConnStatus())
            {
                AK_LOG_INFO << "update route_cli_ptr, route_addr is:" << route_addr;
            }
        }
    }
    //再检查下线的route srv
    if (route_clis_.size() == route_addrs.size())
    {
        return;
    }
    for (auto it = route_clis_.begin(); it != route_clis_.end();)
    {
        AK_LOG_INFO << "route_clis_ is " << route_clis_.size();
        auto it2 = route_addrs.find(it->first);
        if (it2 == route_addrs.end())
        {
            it->second->Stop();
            
            std::lock_guard<std::mutex> lock(route_clis_remove_mutex_);
            route_remove_clis_.push_back(it->second);
            etcd_loop->RunAfter(evpp::Duration(3.0), std::bind(&CRouteClientMng::RemoveDisconnectCli, this));
            
            route_clis_.erase(it++);
        }
        else
        {
            it++;
        }
    }
}

void CRouteClientMng::RemoveDisconnectCli()
{
    std::lock_guard<std::mutex> lock(route_clis_remove_mutex_);
    for(auto it = route_remove_clis_.begin(); it != route_remove_clis_.end(); it++)
    {
        AK_LOG_INFO << "Remote route route_cli_ptr:" << it->get()->GetAddr();
        it = route_remove_clis_.erase(it);
        if(it == route_remove_clis_.end())
        {
            break;
        }
    }
}

bool CRouteClientMng::CheckRouteNormal()
{
    std::lock_guard<std::mutex> lock(route_clis_mutex_);
    for(const auto& route_cli : route_clis_)
    {
        if (!route_cli.second->IsConnStatus())
        {
            return false;
        }
    }
    return true;
}
