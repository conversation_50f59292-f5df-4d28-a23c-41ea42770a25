#include "SL50LockControl.h"
#include "AkcsCommonDef.h"
#include "SafeCacheConn.h"
#include "AkLogging.h"
#include "MqttPublish.h"
#include "AkLogging.h"
#include "MqttSubscribe.h"
extern  MqttPublish* g_mqtt_publish; 



int SL50LockControl::PushMessage(const std::string& client_id, const std::string& msg)
{
    std::string permium_smartlock_client_id = PREMIUM_SMARTLOCK_CLEINT_FLAG + client_id;
    std::string topic = std::string(MQTT_SUB_LOCK_DOWN_TOPIC) + permium_smartlock_client_id;
    if (g_mqtt_publish)
    {
        return g_mqtt_publish->Publish(topic, msg);
    }
    else
    {
        AK_LOG_WARN << "g_mqtt_publish is null";
        return -1;
    }

    AK_LOG_INFO << "Send PushMessage msg:" << msg;
    return 0;
}

int SL50LockControl::PushAckMessage(const std::string& client_id, const std::string& msg)
{
    std::string permium_smartlock_client_id = PREMIUM_SMARTLOCK_CLEINT_FLAG + client_id;
    std::string topic = std::string(MQTT_SUB_LOCK_DOWN_ACK_TOPIC) + permium_smartlock_client_id;
    if (g_mqtt_publish)
    {
        return g_mqtt_publish->Publish(topic, msg);
    }
    else
    {
        AK_LOG_WARN << "g_mqtt_publish is null";
        return -1;
    }

    AK_LOG_INFO << "Send PushAckMessage msg:" << msg;
    return 0;
}