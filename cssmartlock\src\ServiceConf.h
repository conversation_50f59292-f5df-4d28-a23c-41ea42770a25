#ifndef __SERVICE_CONF_H__
#define __SERVICE_CONF_H__


#define SERVICE_CONF_COMMON_LEN 64

typedef struct SERVICE_CONF_T
{
    /* 本机IP配置信息 */
    char server_outer_ip[SERVICE_CONF_COMMON_LEN];
    char server_inner_ip[SERVICE_CONF_COMMON_LEN];
    char server_hostname[SERVICE_CONF_COMMON_LEN];

    /* DB配置项 */
    char db_ip[SERVICE_CONF_COMMON_LEN];
    char db_username[SERVICE_CONF_COMMON_LEN];
    char db_password[SERVICE_CONF_COMMON_LEN];
    char db_database[SERVICE_CONF_COMMON_LEN];
    int  db_port;

    /* etcd */
    char etcd_server_addr[SERVICE_CONF_COMMON_LEN];

    char mqtt_addr[SERVICE_CONF_COMMON_LEN];

    /* nsq */
    char route_topic[SERVICE_CONF_COMMON_LEN];

    int mqtt_sub_thread_num;

} SERVICE_CONF;


#endif// __SERVICE_CONF_H__

