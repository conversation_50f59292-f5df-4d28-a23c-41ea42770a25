#include "SmartLock2RouteMsg.h"
#include "AkcsMsgDef.h"
#include "ServiceConf.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

extern RouteMQProduce* g_nsq_producer;
extern SERVICE_CONF g_service_conf; 

CSmartLock2RouteMsg::CSmartLock2RouteMsg()
{

}
             
CSmartLock2RouteMsg::~CSmartLock2RouteMsg()
{

}

AK::BackendCommon::BackendP2PBaseMessage CSmartLock2RouteMsg::CreateP2PBaseMsg(int msgid, int type, 
   const std::string &uid, csmain::DeviceType conntype, int projecttype)
{
    AK::BackendCommon::BackendP2PBaseMessage msg;
    msg.set_type(type);
    msg.set_uid(uid);
    msg.set_msgid(msgid);
    msg.set_conn_type(conntype);
    msg.set_project_type(projecttype);    
    return msg;
}

csmain::DeviceType CSmartLock2RouteMsg::DevProjectTypeToDevType(int project_type)
{
    if(project_type == project::PERSONAL)
    {
        return csmain::DeviceType::PERSONNAL_DEV;
    }
    else if (project_type == project::RESIDENCE)
    {
        return csmain::DeviceType::COMMUNITY_DEV;
    }
    else if (project_type == project::OFFICE)
    {
        return csmain::DeviceType::OFFICE_DEV;
    }
    return csmain::DeviceType::COMMUNITY_NONE;
}

void CSmartLock2RouteMsg::SendGroupTextMessage(const PerTextMessageSendList& text_messages, const std::string& trigger_time)
{
    for(const auto &text_message : text_messages)
    {
        std::string receiver_uuid(text_message.uuid);
        AK::BackendCommon::BackendP2PBaseMessage base;
        int client_type = 0; //接收方类型：室内机或App
        if(text_message.client_type == PersoanlMessageSend::DEV_SEND)
        {
            client_type = MessageClientType::DEV_SEND;
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG, TransP2PMsgType::TO_DEV_UUID, receiver_uuid,
                DevProjectTypeToDevType(text_message.comm_message.project_type), text_message.comm_message.project_type);
        }
        else if (text_message.client_type == PersoanlMessageSend::APP_SEND)
        {
            client_type = MessageClientType::APP_SEND;
            std::string account;
            dbinterface::ResidentPersonalAccount::GetAccountByUUID(receiver_uuid, account);
            base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, account,
                DevProjectTypeToDevType(text_message.comm_message.project_type), text_message.comm_message.project_type);
        }

        AK::Server::P2PCommonTxtMsgNotifyMsg p2p_msg;
        p2p_msg.set_msg_type((int)text_message.comm_message.msg_type);
        p2p_msg.set_title(text_message.comm_message.title);
        p2p_msg.set_content(text_message.comm_message.content);
        p2p_msg.set_extension_field(text_message.comm_message.extension_field);
        p2p_msg.set_client_type(client_type);
        p2p_msg.set_uuid(receiver_uuid);
        p2p_msg.set_recv_msg_id(text_message.id);
        p2p_msg.set_time(trigger_time);

        base.mutable_p2pcommontxtmsgnotifymsg2()->CopyFrom(p2p_msg);
        PushMsg2Route(&base, text_message.comm_message.project_type);
    }
}

void CSmartLock2RouteMsg::SendGroupSL20LockDoorOpenEvent(const std::set<std::string>& opener_list, const std::string& lock_uuid, int project_type)
{
    AK::BackendCommon::BackendP2PBaseMessage base;
    csmain::DeviceType dev_type;
    int notify_project_type = 0;

    if (project_type == (int)SL20LockProjectType::PERSONAL)
    {
        dev_type = csmain::DeviceType::PERSONNAL_DEV;
        notify_project_type = project::PERSONAL;
    }
    else if (project_type == (int)SL20LockProjectType::COMMUNITY)
    {
        dev_type = csmain::DeviceType::COMMUNITY_DEV;
        notify_project_type = project::RESIDENCE;
    }

    for(const auto &opener : opener_list)
    {
        base = CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_SL20_LOCK_EVENT_NOTIFY, TransP2PMsgType::TO_APP_UID_ONLINE,
            opener, dev_type, notify_project_type);

        AK::Server::P2PSendSL20LockEventNotify p2p_msg;
        p2p_msg.set_lock_uuid(lock_uuid);
        p2p_msg.set_event_type((int)SmartLockEventType::DOOR_OPEN_EVENT);
        p2p_msg.set_site(opener);

        base.mutable_p2psendsl20lockeventnotify2()->CopyFrom(p2p_msg);
        PushMsg2Route(&base, notify_project_type);
    }
}

void CSmartLock2RouteMsg::PushMsg2Route(const google::protobuf::MessageLite* msg, int project_type)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu, g_service_conf.route_topic);    
}

