#ifndef __SMARTLOCK_2_ROUTE_MSG_H__
#define __SMARTLOCK_2_ROUTE_MSG_H__

#include "RouteMqProduce.h"
#include "AkcsCommonDef.h"
#include "AK.BackendCommon.pb.h"
#include "dbinterface/Message.h"

class CSmartLock2RouteMsg
{
public:
    CSmartLock2RouteMsg();
    ~CSmartLock2RouteMsg();

    static void SendGroupTextMessage(const PerTextMessageSendList& text_messages, const std::string& trigger_time);
    static void SendGroupSL20LockDoorOpenEvent(const std::set<std::string>& opener_list, const std::string& lock_uuid, int project_type);
    static void PushMsg2Route(const google::protobuf::MessageLite* msg, int project_type);
    static AK::BackendCommon::BackendP2PBaseMessage CreateP2PBaseMsg(int msgid, int type, const std::string &uid, csmain::DeviceType conntype, int projecttype);
    static csmain::DeviceType DevProjectTypeToDevType(int project_type);
};


#endif //__SMARTLOCK_2_ROUTE_MSG_H__