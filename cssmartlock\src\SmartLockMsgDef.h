#ifndef __SMART_LOCK_COMMAND_DEF_H__
#define __SMART_LOCK_COMMAND_DEF_H__

/*SL20锁相关*/
//command
#define SL20_LOCK_GET_CONFIGURATION_V1_0        "v1.0_u_get_lock_configuration"
#define SL20_LOCK_REPORT_LOCK_EVENT                  "v1.0_report_lock_event"

//event type
#define SL20_LOCK_EVENT_TYPE_BATTERY_LEVEL     "battery_level"
#define SL20_LOCK_EVENT_TYPE_DOOR_OPEN         "door_open"
#define SL20_LOCK_EVENT_TYPE_TRAIL_ERROR        "trial_and_error"
#define SL20_LOCK_EVENT_TYPE_DOORBELL          "doorbell"

//开门方式 1. temp_password 2. credential（在线密码开门） 3.offline_password（离线密码开门） 4.rfcard（卡开门） 5. remote_control（远程开门（通过下发配置））
//unlock mode
#define SL20_LOCK_UNLOCK_MODE_TEMP_PASSWORD     "temp_password"
#define SL20_LOCK_UNLOCK_MODE_CREDENTIAL        "credential"
#define SL20_LOCK_UNLOCK_MODE_OFFLINE_PASSWORD  "offline_password"
#define SL20_LOCK_UNLOCK_MODE_RF_CARD           "rfcard"
#define SL20_LOCK_UNLOCK_MODE_REMOTE_CONTROL    "remote_control"

/*SL50锁相关*/
#define SL50_LOCK_REPORT_ENTITY_STATE               "v1.0_u_report_entity_state"
#define SL50_LOCK_REPORT_LOCK_ENTITY_STATE               "v1.0_u_lock_report_entity_state"
#define SL50_LOCK_REPORT_LOCK_LOG                   "v1.0_u_report_lock_log"
#define SL50_LOCK_GET_NEW_VERSION                   "v1.0_u_get_new_version"
#define SL50_LOCK_REPORT_VERSION                    "v1.0_u_report_version"
#define SL50_LOCK_GET_TIMEZONE_AND_TIMETAMP        "v1.0_u_get_timezone_and_timetamp"
#define SL50_LOCK_GET_SIP_GROUP                    "v1.0_u_get_sip_group"
#define SL50_LOCK_GET_SIP_INFO                     "v1.0_u_get_sip_info"



#endif