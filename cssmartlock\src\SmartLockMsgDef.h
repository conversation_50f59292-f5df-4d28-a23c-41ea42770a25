#ifndef __SMART_LOCK_COMMAND_DEF_H__
#define __SMART_LOCK_COMMAND_DEF_H__

/*SL20锁相关*/
//command
#define SL20_LOCK_GET_CONFIGURATION_V1_0        "v1.0_u_get_lock_configuration"
#define SL20_LOCK_REPORT_LOCK_EVENT                  "v1.0_report_lock_event"

//event type
#define SL20_LOCK_EVENT_TYPE_BATTERY_LEVEL     "battery_level"
#define SL20_LOCK_EVENT_TYPE_DOOR_OPEN         "door_open"
#define SL20_LOCK_EVENT_TYPE_TRAIL_ERROR        "trial_and_error"
#define SL20_LOCK_EVENT_TYPE_DOORBELL          "doorbell"

//开门方式 1. temp_password 2. credential（在线密码开门） 3.offline_password（离线密码开门） 4.rfcard（卡开门） 5. remote_control（远程开门（通过下发配置））
//unlock mode
#define SL20_LOCK_UNLOCK_MODE_TEMP_PASSWORD     "temp_password"
#define SL20_LOCK_UNLOCK_MODE_CREDENTIAL        "credential"
#define SL20_LOCK_UNLOCK_MODE_OFFLINE_PASSWORD  "offline_password"
#define SL20_LOCK_UNLOCK_MODE_RF_CARD           "rfcard"
#define SL20_LOCK_UNLOCK_MODE_REMOTE_CONTROL    "remote_control"


#endif