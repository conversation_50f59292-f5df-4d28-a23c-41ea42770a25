#include "SmartLockReqCommon.h"
#include "util_time.h"
#include <exception>
#include "MqttPublish.h"
#include "AkLogging.h"

extern  MqttPublish* g_mqtt_publish; 

void GenerateSL20ReplyCommonInfo(Json::Value& data, const std::string& id, const std::string& command, bool is_success, const std::string& trace_id)
{
    data["success"] = is_success;
    data["id"] = id;
    data["command"] = command;
    data["timestamp"] = GetCurrentTimeStamp();
    data["trace_id"] = trace_id;
}

int SendSL20ReplyMsg(Json::Value& data, const std::string& id, const std::string& command, bool is_success, const std::string& trace_id)
{
    GenerateSL20ReplyCommonInfo(data, id, command, is_success, trace_id);

    Json::FastWriter writer;
    std::string msg;

    try 
    {
        msg = writer.write(data);
    } 
    catch (const std::exception& e) 
    {
        AK_LOG_WARN << "get json string failed.";
        return -1;
    }

    std::string topic = std::string(MQTT_PUB_TOPIC) + id;

    if (g_mqtt_publish)
    {
        return g_mqtt_publish->Publish(topic, msg);
    }

    return -1;
}