#ifndef _AGENT_BASE_H_
#define _AGENT_BASE_H_
#include <string>
#include <memory>
#include "json/json.h"
#include <boost/any.hpp>

class IBase;
typedef std::shared_ptr<IBase> IBasePtr;

class IBase
{
public:
    IBase() : req_success_(true) {};
    virtual ~IBase() = default;
    virtual  int IControl(const Json::Value& param, const std::string& client_id) = 0; 
    virtual  void IReplyParamConstruct() = 0;
    virtual int IReply(const std::string& id, const std::string& command, const std::string& trace_id);
    
    virtual  IBasePtr NewInstance() = 0;

protected:
    bool req_success_;
    Json::Value reply_data_;
};

#endif
