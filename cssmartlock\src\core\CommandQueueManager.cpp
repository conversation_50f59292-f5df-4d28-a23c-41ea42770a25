#include "CommandQueueManager.h"
#include "MessageFactory.h"
#include "UpMessageSL50Factory.h"
#include "AkLogging.h"
#include "ServiceConf.h"
#include "MqttSubscribe.h"
#include "json/json.h"

CommandQueueManager* CommandQueueManager::GetInstance() {
    static CommandQueueManager instance;
    return &instance;
}

void CommandQueueManager::Start(size_t num_queues) {
    queues_.resize(num_queues);
    // 使用unique_ptr来管理互斥锁和条件变量
    for (size_t i = 0; i < num_queues; ++i) {
        mutexes_.push_back(std::unique_ptr<std::mutex>(new std::mutex()));
        conditions_.push_back(std::unique_ptr<std::condition_variable>(new std::condition_variable()));
    }
    stops_.resize(num_queues, false);

    // 为每个队列创建一个工作线程
    for (size_t i = 0; i < num_queues; ++i) {
        workers_.emplace_back([this, i] {
            AK_LOG_INFO << "CommandQueueManager started with thread >>>" << i;
            ProcessQueue(i);
        });
    }
    
    AK_LOG_INFO << "CommandQueueManager started with " << num_queues << " queues";
}

void CommandQueueManager::Stop() {
    // 通知所有队列停止
    for (size_t i = 0; i < queues_.size(); ++i) {
        {
            std::unique_lock<std::mutex> lock(*mutexes_[i]);
            stops_[i] = true;
        }
        conditions_[i]->notify_one();
    }
    
    // 等待所有线程结束
    for (auto& worker : workers_) {
        if (worker.joinable()) worker.join();
    }
    
    AK_LOG_INFO << "CommandQueueManager stopped";
}

void CommandQueueManager::EnqueueMessage(const std::string& command, const std::string& message, const std::string& topic) {
    if (queues_.empty()) {
        AK_LOG_WARN << "CommandQueueManager not started";
        return;
    }

    size_t index = GetQueueIndex(command);
    {
        std::unique_lock<std::mutex> lock(*mutexes_[index]);
        CommandMessage cmd_msg;
        cmd_msg.message = message;
        cmd_msg.topic = topic;
        cmd_msg.command = command;
        queues_[index].push(cmd_msg);
        
        if (queues_[index].size() > 500) {
            AK_LOG_WARN << "Command queue " << index << " size: " << queues_[index].size();
        }
    }
    conditions_[index]->notify_one();
}

size_t CommandQueueManager::GetQueueSize(size_t index) {
    if (index >= queues_.size()) {
        return 0;
    }
    
    std::unique_lock<std::mutex> lock(*mutexes_[index]);
    return queues_[index].size();
}

size_t CommandQueueManager::GetQueueCount() const {
    return queues_.size();
}

void CommandQueueManager::ProcessQueue(size_t index) {
    AK_LOG_INFO << "CommandQueueManager started with thread index " << index;
    while (true) {
        CommandMessage msg;
        bool has_msg = false;
        
        {
            std::unique_lock<std::mutex> lock(*mutexes_[index]);
            conditions_[index]->wait(lock, [this, index] {
                return stops_[index] || !queues_[index].empty();
            });
            
            if (stops_[index] && queues_[index].empty()) {
                break;  // 使用 break 而不是 return
            }
            
            if (!queues_[index].empty()) {
                msg = queues_[index].front();
                queues_[index].pop();
                has_msg = true;
            }
        }
        
        if (has_msg) {
            // 处理消息
            ProcessMessage(msg);
        }
    }
    AK_LOG_INFO << "CommandQueueManager stop thread index " << index;  // 现在这行代码可以执行了
}

void CommandQueueManager::ProcessMessage(const CommandMessage& msg) {
    std::string topic = msg.topic;
    std::string client_id;
    std::string topic_name;
    size_t last_slash = topic.find_last_of('/');
    if (last_slash != std::string::npos) {
        client_id = topic.substr(last_slash + 1);
        topic_name = topic.substr(0, last_slash + 1);
    }
    AK_LOG_INFO << "Message parse, client_id:" << client_id << " topic:" << topic_name;
    if (client_id.find(PREMIUM_SMARTLOCK_CLEINT_FLAG) != std::string::npos) 
    {
        UpMessageSL50Factory::GetInstance()->DispatchMsg(msg.message, topic);
        return;	
    }

    //没有特定开头的是SL20
    MessageFactory::GetInstance()->DispatchMsg(msg.message, topic_name);
}

size_t CommandQueueManager::GetQueueIndex(const std::string& command) {
    // 使用简单的哈希函数将command映射到队列索引
    return std::hash<std::string>{}(command) % queues_.size();
}
// 如果需要额外的实现，可以放在这里