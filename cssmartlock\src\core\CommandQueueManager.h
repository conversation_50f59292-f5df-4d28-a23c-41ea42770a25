#ifndef _COMMAND_QUEUE_MANAGER_H_
#define _COMMAND_QUEUE_MANAGER_H_

#include <vector>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <string>
#include <unordered_map>
#include <memory> // 用于std::unique_ptr

// 前向声明
class MessageFactory;
class UpMessageSL50Factory;

struct CommandMessage {
    std::string message;
    std::string topic;
    std::string command;
};

class CommandQueueManager {
public:
    static CommandQueueManager* GetInstance();
    void Start(size_t num_queues);
    void Stop();
    void EnqueueMessage(const std::string& command, const std::string& message, const std::string& topic);
    size_t GetQueueSize(size_t index);
    size_t GetQueueCount() const;

private:
    CommandQueueManager() = default; // 私有构造函数
    ~CommandQueueManager() = default; // 私有析构函数
    
    void ProcessQueue(size_t index);
    void ProcessMessage(const CommandMessage& msg);
    size_t GetQueueIndex(const std::string& command);

    std::vector<std::queue<CommandMessage>> queues_;
    std::vector<std::unique_ptr<std::mutex>> mutexes_; // 使用unique_ptr存储互斥锁
    std::vector<std::unique_ptr<std::condition_variable>> conditions_; // 使用unique_ptr存储条件变量
    std::vector<bool> stops_;
    std::vector<std::thread> workers_;
};

#endif