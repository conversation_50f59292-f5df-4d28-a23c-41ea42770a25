#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "MessageFactory.h"
#include "json/json.h"
#include "AkLogging.h"
#include "MqttSubscribe.h"
#include "SmartLockReqCommon.h"

MessageFactory* MessageFactory::GetInstance()
{
    static MessageFactory handle;
    return &handle;
}

void MessageFactory::AddFunc(IBasePtr &ptr, const std::string& command)
{
    funcs_[command] = std::move(ptr);
}

void MessageFactory::DispatchMsg(const std::string& msg, const std::string& topic)
{
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(msg, root))
    {
        AK_LOG_WARN << "parse json error.msg=" << msg;
        return;
    }
    
    std::string id = root["id"].asString();
    std::string command = root["command"].asString();
    std::string trace_id = root["trace_id"].asString();

    std::string client_id;
    std::string topic_prefix = MQTT_SUB_LOCK_TOPIC;
    size_t prefix_pos = topic.find(topic_prefix);
    if (prefix_pos != std::string::npos) {
        client_id = topic.substr(prefix_pos + topic_prefix.length());
    } 
    
    if(root.isMemberCheckType("param", Akcs::Json::objectValue))
    {
        auto func_iter = funcs_.find(command);
        if(func_iter != funcs_.end())
        {
            IBasePtr msg_handler = func_iter->second->NewInstance();

            if(0 != msg_handler->IControl(root["param"], client_id))
            {
                AK_LOG_WARN << command << " msg_handler control error";
                return;
            }

            msg_handler->IReplyParamConstruct();
            
            if(0 != msg_handler->IReply(id, command, trace_id))
            {
                AK_LOG_WARN << command << " msg_handler reply error";
                return;
            }

            
        }
    }
}

/*
程序启动时候自动注册到这里的类
*/
void RegFunc(IBasePtr &f, const std::string& command)
{
    MessageFactory::GetInstance()->AddFunc(f, command);
}
