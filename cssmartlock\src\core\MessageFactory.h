#ifndef _MSG_FACTORY_H_
#define _MSG_FACTORY_H_
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <cstdint>
#include "AgentBase.h"

typedef std::map<std::string, IBasePtr> FuncList;

class MessageFactory
{
public:
    MessageFactory() = default;
    void AddFunc(IBasePtr &ptr, const std::string& command);
    void DispatchMsg(const std::string& msg, const std::string& topic);
    static MessageFactory* GetInstance();

private:
    FuncList funcs_;

};

void RegFunc(IBasePtr &f, const std::string& command);

#endif
