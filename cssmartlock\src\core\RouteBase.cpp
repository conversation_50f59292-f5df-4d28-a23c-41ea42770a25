#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RouteBase.h"
#include <random>
#include "SmartLockReqCommon.h"

IRouteBase::IRouteBase()
{

}

int IRouteBase::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    return 0;
}

int IRouteBase::IReplyToSmartLock(const std::string& trace_id)
{
    //主动推送给锁的，都发送成功
    return SendSL20ReplyMsg(reply_data_, id_, command_, true, trace_id);
}

