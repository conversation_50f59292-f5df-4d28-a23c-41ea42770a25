#ifndef _ROUTE_BASE_H_
#define _ROUTE_BASE_H_
#include <string>
#include <memory>
#include <boost/any.hpp>
#include "AkcsPduBase.h"
#include "MsgIdToMsgName.h"
#include "json/json.h"

class IRouteBase;

typedef std::shared_ptr<IRouteBase> IRouteBasePtr;

class IRouteBase
{
public:
    IRouteBase();

    virtual int IControl(const std::unique_ptr<CAkcsPdu> &pdu); 
    
    virtual void IReplyParamConstruct() = 0;
    virtual int IReplyToSmartLock(const std::string& trace_id);

    virtual IRouteBasePtr NewInstance() = 0;
    virtual std::string FuncName() = 0;
    
protected:
    std::string id_;
    std::string command_;
    Json::Value reply_data_;
};


#endif