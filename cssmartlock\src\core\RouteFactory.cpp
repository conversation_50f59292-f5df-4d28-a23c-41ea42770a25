#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteFactory.h"
#include "AkLogging.h"
#include "MsgIdToMsgName.h"
#include "gid/SnowFlakeGid.h"

RouteFactory* RouteFactory::GetInstance()
{
    static RouteFactory handle;
    return &handle;
}

void RouteFactory::AddRouteFunc(IRouteBasePtr &ptr, uint32_t msgid)
{
    funcs_[msgid] = std::move(ptr);
}

int RouteFactory::DispatchMsg(uint32_t message_id, const std::unique_ptr<CAkcsPdu>& pdu)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    AK_LOG_INFO << "OnRouteMsg recv msg. msgid:" << message_id <<". msgname = " << MsgIdToMsgName::GetAkcsMessageName(message_id);
   
    auto it = funcs_.find(message_id);
    if (it != funcs_.end())
    {
        IRouteBasePtr p = std::move(it->second->NewInstance());

        if (p->IControl(pdu) != 0)
        {
            AK_LOG_WARN << "exec IControl error";
            return 0;
        }
            
        p->IReplyParamConstruct();

        if (p->IReplyToSmartLock(std::to_string(traceid)) != 0)
        {
            AK_LOG_WARN << "exec IReplyToSmartLock error";
            return 0;
        }


        return 0;
    }
 
    return -1;
}

/*
程序启动时候自动注册到这里的类，是属于工具类，后续会为每个消息都重新new一个对象来处理
*/
void RegRouteFunc(IRouteBasePtr &f, uint32_t msgid)
{
    RouteFactory::GetInstance()->AddRouteFunc(f, msgid);
}