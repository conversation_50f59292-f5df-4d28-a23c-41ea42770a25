#ifndef _ROUTE_FACTORY_H_
#define _ROUTE_FACTORY_H_
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <cstdint>
#include "RouteBase.h"


typedef std::map<uint32_t, IRouteBasePtr> RouteFuncList;

class RouteFactory
{
public:
    RouteFactory() = default;
    void AddRouteFunc(IRouteBasePtr &ptr, uint32_t msgid);
    int DispatchMsg(uint32_t message_id, const std::unique_ptr<CAkcsPdu>& pdu);
    static RouteFactory* GetInstance();
private:
    RouteFuncList funcs_;
};

void RegRouteFunc(IRouteBasePtr &f, uint32_t msgid);

#endif
