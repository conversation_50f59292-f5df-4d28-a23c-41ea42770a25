#include "SL50MessageBase.h"
#include "AkLogging.h"
#include "util_time.h"
#include "AkLogging.h"
#include "SL50/DownAckMessage/AckMessageOnly.h"
#include "SL50LockControl.h"

int ILS50Base::SetMessageInfo(const Json::Value& root, const std::string& id, 
    const std::string& client_id, bool success, uint64_t timestamp, const std::string& command)
{
    id_ = id;
    client_id_ = client_id;
    success_ = success;
    timestamp_ = timestamp;
    command_ = command;
    return 0;
}
int ILS50Base::BuildMessagAck()
{
    AckMessageOnly ack(id_, command_);
    reply_data_ = ack.to_json();
    return 0;
}

int ILS50Base::ReplyToSmartLock()
{
    if (!reply_data_.empty())
    {
        return SL50LockControl::PushAckMessage(client_id_, reply_data_);
    }
    return 0;
}



