#ifndef _SL50_AGENT_BASE_H_
#define _SL50_AGENT_BASE_H_
#include <string>
#include <memory>
#include "json/json.h"
#include <boost/any.hpp>

class ILS50Base;
typedef std::shared_ptr<ILS50Base> ILS50BasePtr;

class ILS50Base
{
public:
    ILS50Base(){};
    virtual ~ILS50Base() = default;
    virtual int SetMessageInfo(const Json::Value& root, const std::string& id, 
        const std::string& client_id, bool success, uint64_t timestamp, const std::string& command);
    virtual int BuildMessagAck();
    virtual int ReplyToSmartLock();
    //接口
    virtual int IParseData(const Json::Value& param) = 0;
    virtual  int IControl() = 0; 
    virtual  void IReplyParamConstruct() = 0;

    virtual  ILS50BasePtr NewInstance() = 0;

    std::string id_;
    std::string client_id_;
    bool success_ = true;
    uint64_t timestamp_ = 0;

    std::string reply_data_;
    std::string command_;
};

#endif
