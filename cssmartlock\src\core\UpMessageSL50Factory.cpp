#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "UpMessageSL50Factory.h"
#include "json/json.h"
#include "AkLogging.h"
#include "MqttSubscribe.h"
#include "SmartLockReqCommon.h"
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <sstream>
#include <json/json.h>
#include <json/json.h>
#include "CommandQueueManager.h"

UpMessageSL50Factory* UpMessageSL50Factory::GetInstance()
{
    static UpMessageSL50Factory handle;
    return &handle;
}

void UpMessageSL50Factory::AddFunc(ILS50BasePtr &ptr, const std::string& command)
{
    funcs_[command] = std::move(ptr);
}


void UpMessageSL50Factory::DispatchMsg(const std::string& msg, const std::string& topic)
{
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(msg, root))
    {
        AK_LOG_WARN << "parse json error.msg=" << msg;
        return;
    }
    std::string id;
    std::string command;
    uint64_t timestamp = 0;
    bool success = true;
    if (root.isMember("success"))
    {
        success = root["success"].asBool();
    }
    if (root.isMember("id"))
    {
        id = root["id"].asString();
    }
    if (root.isMember("command"))       
    {
        command = root["command"].asString();
    }           
    if (root.isMember("timestamp"))  
    {
        timestamp = root["timestamp"].asDulLong();
    }

    std::string client_id;
    if (topic.find(MQTT_SUB_LOCK_UP_TOPIC) != std::string::npos) {
        client_id = topic.substr(strlen(MQTT_SUB_LOCK_UP_TOPIC));
    }
    else if (topic.find(MQTT_SUB_LOCK_UP_ACK_TOPIC) != std::string::npos) {
        client_id = topic.substr(strlen(MQTT_SUB_LOCK_UP_ACK_TOPIC));
    }
    else
    {
        AK_LOG_WARN << "topic is not lock up topic, topic:" << topic;
        return;
    }
    
    if(root.isMemberCheckType("param", Akcs::Json::objectValue) || root.isMemberCheckType("param", Akcs::Json::arrayValue))
    {
        auto func_iter = funcs_.find(command);
        if(func_iter != funcs_.end())
        {
            ILS50BasePtr msg_handler = func_iter->second->NewInstance();
            msg_handler->SetMessageInfo(root, id, client_id, success, timestamp, command);

            if(0 != msg_handler->IParseData(root["param"]))
            {
                AK_LOG_WARN << command << " msg_handler IParseData error";
                return;
            }            
            if(0 != msg_handler->IControl())
            {
                AK_LOG_WARN << command << " msg_handler control error";
                return;
            }
            
            msg_handler->IReplyParamConstruct();

            if (msg_handler->ReplyToSmartLock() != 0)
            {
                AK_LOG_WARN << "exec ReplyToSmartLock error";
                return;
            }

        }
        else
        {
            AK_LOG_WARN << "not find msg handler, command:" << command;
        }
    }
    else
    {
        AK_LOG_WARN << "msg param is not obj or array, msg:" << msg;
    }
}

/*
程序启动时候自动注册到这里的类
*/
void RegSL50UpFunc(ILS50BasePtr &f, const std::string& command)
{
    UpMessageSL50Factory::GetInstance()->AddFunc(f, command);
}