#ifndef _UP_MSG_SL50_FACTORY_H_
#define _UP_MSG_SL50_FACTORY_H_
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <cstdint>
#include "SL50MessageBase.h"

typedef std::map<std::string, ILS50BasePtr> SL50FuncList;

class UpMessageSL50Factory
{
public:
    UpMessageSL50Factory() = default;
    void AddFunc(ILS50BasePtr &ptr, const std::string& command);
    void DispatchMsg(const std::string& msg, const std::string& topic);
    static UpMessageSL50Factory* GetInstance();

private:
    SL50FuncList funcs_;
};

void RegSL50UpFunc(ILS50BasePtr &f, const std::string& command);

#endif