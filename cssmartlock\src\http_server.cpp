#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <cstdlib>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "util.h"
#include "AkLogging.h"
#include "MetricService.h"

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void startHttpServer()
{
    const int port = 9279;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网

    evpp::http::Server server(addr, thread_num, false);
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);

    server.Init(port);
    server.Start();
    
    return;
}

