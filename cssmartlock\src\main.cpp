#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <thread>
#include <fcntl.h>
#include "AkLogging.h"
#include "ConnectionPool.h"
#include "ServiceConf.h"
#include "EtcdCliMng.h"
#include "util.h"
#include "ConfigFileReader.h"
#include "AkcsDnsResolver.h"
#include "MqttPublish.h"
#include "MqttSubscribe.h"
#include "CachePool.h"
#include "Etcd.h"
#include "RouteMqProduce.h"
#include "CachePool.h"
#include <KdcDecrypt.h>
#include "util_time.h"
#include "http_server.h"
#include "metric.h"


SERVICE_CONF g_service_conf;
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)
#define PIDFILE "/var/run/cssmartlock.pid"
#define SERVICE_CONF_FILE "/usr/local/akcs/cssmartlock/conf/cssmartlock.conf"
#define SERVICE_RLDB_CONN 5

int g_etcd_dns_res = 0;
CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_conf_db_addr;
extern MqttPublish* g_mqtt_publish;
std::map<std::string, AKCS_DST> g_time_zone_DST;

int RedisInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/cssmartlock/conf/cssmartlock_redis.conf", "cssmartlockCacheInstances");
}

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/cssmartlocklog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/cssmartlocklog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/cssmartlocklog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/cssmartlocklog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

int LoadConfFromConfSrv()
{
    if(g_etcd_cli_mng == nullptr)
    {
        return -1;
    }
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(g_service_conf.db_ip, sizeof(g_service_conf.db_ip), conf_tmp.db_ip);
    g_service_conf.db_port = conf_tmp.db_port;

    std::string mqtt_addr;
    g_etcd_cli_mng->LoadSrvMqttConf(mqtt_addr);
    Snprintf(g_service_conf.mqtt_addr, sizeof(g_service_conf.mqtt_addr), mqtt_addr.c_str());
    
    return 0;
}

void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void ConfInit()
{
    memset(&g_service_conf, 0, sizeof(SERVICE_CONF));
    CConfigFileReader config_file(SERVICE_CONF_FILE);

    Snprintf(g_service_conf.db_username, sizeof(g_service_conf.db_username),  config_file.GetConfigName("db_username"));

    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(g_service_conf.db_password, sizeof(g_service_conf.db_password), decrypt_db_passwd.c_str());
    
    Snprintf(g_service_conf.db_database, sizeof(g_service_conf.db_database),  config_file.GetConfigName("db_database"));
    
    if(LoadConfFromConfSrv() != 0)
    {
        Snprintf(g_service_conf.db_ip, sizeof(g_service_conf.db_ip), config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        g_service_conf.db_port = ATOI(db_port);
    }

    Snprintf(g_service_conf.route_topic, sizeof(g_service_conf.route_topic),  config_file.GetConfigName("nsq_route_topic"));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));
}


int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(g_service_conf.db_ip, g_service_conf.db_username, g_service_conf.db_password,
                    g_service_conf.db_database, g_service_conf.db_port, SERVICE_RLDB_CONN, "cssmartlock");
    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(g_service_conf.db_ip, g_service_conf.db_port);
    return 0;
}

void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, g_service_conf.db_ip) != 0) || (conf_tmp.db_port != g_service_conf.db_port))
    {
        Snprintf(g_service_conf.db_ip, sizeof(g_service_conf.db_ip), conf_tmp.db_ip);
        g_service_conf.db_port = conf_tmp.db_port;
        DaoReInit();
    }
}

void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_service_conf.etcd_server_addr);
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(g_service_conf.etcd_server_addr, sizeof(g_service_conf.etcd_server_addr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << g_service_conf.etcd_server_addr;

    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }

    return 0;
    
}

void DnsResolver()
{
    CConfigFileReader config_file(SERVICE_CONF_FILE);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(g_service_conf.etcd_server_addr, sizeof(g_service_conf.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = g_service_conf.etcd_server_addr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(g_service_conf.etcd_server_addr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}


int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        printf("another cssmartlock has been running in this sytem.");
        return -1;
    }

    //glog初始化    
    glogInit(argv[0]);

    //配置中心初始化
	memset(&g_service_conf, 0, sizeof(SERVICE_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }   
    ConfSrvInit();
    
    //读取配置文件
    ConfInit();

    ParseTimeZone("/usr/local/akcs/cssmartlock/conf/TimeZone.xml", g_time_zone_DST);

    if (RedisInit() != 0)
    {
        AK_LOG_FATAL << "redis init failed";
        return -1;
    }   
    //初始化数据库连接
    if (0 != DaoInit())
    {
        AK_LOG_WARN << "DaoInit fialed.";
        glogClean();
        return -1;
    }

    MqttPublishInit();

    //工作线程
    std::thread conf_watch_thread = std::thread(ConfWatch);
    std::thread mqtt_sub_thread = std::thread(MqttSubscribe::Init);
    std::thread etcd_cli_thread = std::thread(EtcdSrvInit);
    std::thread nsq_thread = std::thread(MQProduceInit);


    // start http server.
    LOG_INFO << "start sever!";
    std::thread httpThread(startHttpServer);//port = 9298
    // 初始化metric单例
    InitMetricInstance();

    
    AK_LOG_INFO << "cssmartlock is starting";

    conf_watch_thread.join();
    dnsThread.join();
    mqtt_sub_thread.join();
    etcd_cli_thread.join();
    nsq_thread.join();

    glogClean();
    return 0;
}




