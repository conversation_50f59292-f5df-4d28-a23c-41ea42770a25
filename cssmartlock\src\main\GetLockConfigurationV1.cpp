#include "GetLockConfigurationV1.h"
#include "MessageFactory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "util_time.h"
#include "json/json.h"
#include "SL20LockControl.h"
#include "SmartLockReqCommon.h"
#include "ServiceConf.h"
#include "SmartLock2RouteMsg.h"

extern SERVICE_CONF g_service_conf;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<GetLockConfigurationV1>();
    RegFunc(p, SL20_LOCK_GET_CONFIGURATION_V1_0);
};

//id对应锁的lock uuid
int GetLockConfigurationV1::IControl(const Json::Value& param, const std::string& id)
{
    if (0 != GetLockRelateInfo(id, param))
    {
        AK_LOG_WARN << "get lock relate info failed. request fail";
        req_success_ = false; //获取锁信息失败，此次请求失败
        return 0;
    }

    UpdateSL20LockInfoDB();

    NotifyDoorOpenEventIfNotKeepAlive();

    return 0;
}

int GetLockConfigurationV1::GetLockRelateInfo(const std::string& lock_uuid, const Json::Value& param)
{
    if (0 != dbinterface::SL20Lock::GetSL20LockInfoByUUID(lock_uuid, sl20_lock_info_))
    {
        AK_LOG_WARN << "get lock info failed. lock_uuid = " << lock_uuid;
        return -1;
    }

    UpdateLockVersionInfo(param, SL20LockVersionType::SL20_LOCK_MODULE_VERSION);
    UpdateLockVersionInfo(param, SL20LockVersionType::SL20_LOCK_BODY_VERSION);
    UpdateLockVersionInfo(param, SL20LockVersionType::SL20_LOCK_COMBINED_VERSION);

    if (0 != dbinterface::SL20Shadow::GetSL20ShadowBySL20LockUUID(lock_uuid, sl20_lock_shadow_info_))
    {
        AK_LOG_WARN << "get lock shadow info failed. lock_uuid = " << lock_uuid;
        return -1;
    }
    if (param.isMember("hash"))
    {
        std::string hash = param["hash"].asString();
        if (strcmp(hash.c_str(), sl20_lock_shadow_info_.configuration_hash) != 0)
        {
            need_upgrade_configuration_ = true;
        }
    }

    //没查到可能是因为锁不需要更新，不是异常
    if (0 == dbinterface::SL20Upgrade::GetSL20UpgradeBySL20LockUUID(lock_uuid, sl20_upgrade_info_))
    {
        //有查到且更新状态为1时，表明当前不是最新版本，需要下发新版本
        if (sl20_upgrade_info_.upgrade_status == 1)
        {
            need_upgrade_version_ = true;
        }
    }

    SL20LockControl::GetSL20LockOpenDoorInfo(sl20_lock_info_.uuid, door_state_, open_door_relate_, opener_list_);

    return 0;
}

void GetLockConfigurationV1::UpdateLockVersionInfo(const Json::Value& param, const SL20LockVersionType& version_type)
{
    switch (version_type)
    {
        case SL20LockVersionType::SL20_LOCK_MODULE_VERSION:
            if (param.isMember("module_version"))
            {
                std::string module_version = param["module_version"].asString();
                if (module_version.size() > 0)
                {
                    Snprintf(sl20_lock_info_.module_version, sizeof(sl20_lock_info_.module_version), module_version.c_str());
                }
            }
            break;

        case SL20LockVersionType::SL20_LOCK_BODY_VERSION:
            if (param.isMember("lock_body_version"))
            {
                std::string lock_body_version = param["lock_body_version"].asString();
                if (lock_body_version.size() > 0)
                {
                    Snprintf(sl20_lock_info_.lock_body_version, sizeof(sl20_lock_info_.lock_body_version), lock_body_version.c_str());
                }
            }
            break;

        case SL20LockVersionType::SL20_LOCK_COMBINED_VERSION:
            if (param.isMember("combined_version"))
            {
                std::string combined_version = param["combined_version"].asString();
                if (combined_version.size() > 0)
                {
                    Snprintf(sl20_lock_info_.combined_version, sizeof(sl20_lock_info_.combined_version), combined_version.c_str());
                }
            }
            break;
        default:
            break;
    }
}

void GetLockConfigurationV1::UpdateSL20LockInfoDB()
{
    // 锁来拉取配置，云就认为pin已经同步
    bool pin_code_already_sync = true;

    //更新LastConnectedTime, LockBodyVersion, CombinedVersion, ModuleVersion, IsPinCodeSynchronizing
    if (0 != dbinterface::SL20Lock::UpdateSL20LockRelatedInfo(sl20_lock_info_, pin_code_already_sync))
    {
        AK_LOG_WARN << "update sl20 lock related info failed. lock uuid=" << sl20_lock_info_.uuid;
    }

    //上报的combined_version与upgrade_combined_version相同，且之前有升级的动作，更新UpgradeStatus=0表示升级完成
    if (strcmp(sl20_lock_info_.combined_version, sl20_upgrade_info_.upgrade_combined_version) == 0 && need_upgrade_version_)
    {
        AK_LOG_INFO << "update sl20 lock upgrade status. lock uuid=" << sl20_lock_info_.uuid;
        int upgrade_status = 0; //0代表升级完成
        if (0 != dbinterface::SL20Upgrade::UpdateSL20UpgradeStatus(sl20_lock_info_.uuid, upgrade_status))
        {
            AK_LOG_WARN << "update sl20 lock upgrade status failed. lock uuid=" << sl20_lock_info_.uuid;
        }
    }

    return;
}

void GetLockConfigurationV1::GenerateSL20ReplyParamInfo(Json::Value& data)
{
    data["param"]["hash"] = sl20_lock_shadow_info_.configuration_hash;
    if (need_upgrade_configuration_)
    {
        GenerateSL20ConfigurationJson(data);
    }

    data["param"]["lock_info"]["state"] = door_state_;
    data["param"]["lock_info"]["open_door_relate"] = open_door_relate_;
    data["param"]["lock_info"]["upgrade_version"] = sl20_upgrade_info_.upgrade_combined_version;
    data["param"]["lock_info"]["firmware_download_url"] = sl20_upgrade_info_.firmware_download_url;

}
/*
"configuration": {
            "updated_time": 1653278488265,
            "credential_pwds": [
                {
                    "id": 1,
                    "note": "admin的密码",
                    "pwd": "112233",
                    "credential_type": 1
                }
            ],
            "basic": {
                "auto_lock": true,
                "auto_lock_delay": 10
            }
        },
*/

void GetLockConfigurationV1::GenerateSL20ConfigurationJson(Json::Value& data)
{
    std::string configuration(sl20_lock_shadow_info_.configuration);

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(configuration, root))
    {
        return;
    }

    if (!root.isMember("configuration"))
    {
        return;
    }

    Json::Value conf_json = root["configuration"];

    data["param"]["configuration"] = conf_json;
}

void GetLockConfigurationV1::IReplyParamConstruct()
{
    if (req_success_)
    {
        GenerateSL20ReplyParamInfo(reply_data_);
    }
    return;
}

void GetLockConfigurationV1::NotifyDoorOpenEventIfNotKeepAlive()
{
    // 保活和无用户开门时无需推送
    if (sl20_lock_info_.keep_alive || opener_list_.empty())
    {
        return;
    }

    CSmartLock2RouteMsg::SendGroupSL20LockDoorOpenEvent(opener_list_, sl20_lock_info_.uuid, sl20_lock_info_.project_type);
}
