#ifndef _GET_LOCK_CONFIG_V1_
#define _GET_LOCK_CONFIG_V1_

#include "AgentBase.h"
#include <string>
#include "dbinterface/SmartLockShadow.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/SmartLockUpgrade.h"
#include "AkcsCommonDef.h"

class GetLockConfigurationV1: public IBase
{
public:
    GetLockConfigurationV1() : need_upgrade_version_(false), need_upgrade_configuration_(false){}
    ~GetLockConfigurationV1() = default;


    int IControl(const Json::Value& param, const std::string& client_id);

    void IReplyParamConstruct();

    IBasePtr NewInstance() {return std::make_shared<GetLockConfigurationV1>();}

private:
    int GetLockRelateInfo(const std::string& lock_uuid, const Json::Value& param);

    void UpdateSL20LockInfoDB();
    void UpdateLockVersionInfo(const Json::Value& param, const SL20LockVersionType& version_type);

    void GenerateSL20ReplyParamInfo(Json::Value& data);
    void GenerateSL20ConfigurationJson(Json::Value& data);

    void NotifyDoorOpenEventIfNotKeepAlive();

private:
    bool need_upgrade_version_;
    bool need_upgrade_configuration_;

    SmartLockInfo sl20_lock_info_;
    SmartLockShadowInfo sl20_lock_shadow_info_;
    SmartLockUpgradeInfo sl20_upgrade_info_;

    std::string door_state_;
    std::string open_door_relate_;
    std::set<std::string> opener_list_;
};

#endif 
