#include "ReportLockEventV1.h"
#include <string>
#include "dbinterface/ThirdPartyLockCapture.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/SL20Lock.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/Message.h"
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "MessageFactory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"
#include "SmartLock2RouteMsg.h"

extern std::map<string, AKCS_DST> g_time_zone_DST;

using namespace Akcs;

__attribute__((constructor))  static void Init(){
    IBasePtr p = std::make_shared<ReportLockEventV1>();
    RegFunc(p, SL20_LOCK_REPORT_LOCK_EVENT);
};

int ReportLockEventV1::IControl(const Json::Value& param, const std::string& lock_uuid)
{   
    if (!param.isMember("event_type"))
    {
        AK_LOG_WARN << "not report event type. req failed.";
        req_success_ = false;
        return 0;
    }
    event_type_ = param["event_type"].asString();
    client_id_ = lock_uuid;

    if (0 != GetRelatedInfo())
    {
        AK_LOG_WARN << "get related info failed.";
        req_success_ = false;
        return 0;
    }

    if (event_type_ == SL20_LOCK_EVENT_TYPE_DOOR_OPEN)
    {
        RecordSL20OpenDoorLog(param);
    }
    else if (event_type_ == SL20_LOCK_EVENT_TYPE_BATTERY_LEVEL)
    {
        //数据库更新成功再通知，防止消息通知的电量跟App首页展示的电量不一致
        if (0 == UpdateSL20BatteryLevel(param))
        {
            return HandleMessageNotifyRelatedEvent(param);
        }
        AK_LOG_WARN << "update sl20 battery level failed.";
        req_success_ = false;
    }
    else if (event_type_ == SL20_LOCK_EVENT_TYPE_DOORBELL)
    {
        return HandleMessageNotifyRelatedEvent(param);
    }
    else if (event_type_ == SL20_LOCK_EVENT_TYPE_TRAIL_ERROR)
    {
        return HandleMessageNotifyRelatedEvent(param);
    }   
    else
    {
        req_success_ = false;
        AK_LOG_WARN << "event type not support. type=" << event_type_;
    }
    return 0;
}

int ReportLockEventV1::GetRelatedInfo()
{
    // 锁信息获取
    if (0 != dbinterface::SL20Lock::GetSL20LockInfoByUUID(client_id_, lock_info_))
    {
        AK_LOG_WARN << "lock not exist. get info failed.";
        return -1;
    }
    // 锁对应账号信息获取
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(lock_info_.personal_account_uuid, per_account_))
    {
        AK_LOG_WARN << "lock not added yet. get info failed.";
        return -1;
    }
    return 0;
}

int ReportLockEventV1::UpdateSL20BatteryLevel(const Json::Value& param)
{
    if (!param.isMember("event_data"))
    {
        AK_LOG_WARN << "miss param: event_data";
        return -1;
    }

    Json::Value event_data = param["event_data"];
    if (event_data.isMember("battery_level"))
    {
        auto last_battery = event_data["battery_level"].asInt();
        //合法性校验
        if (last_battery >= 0 && last_battery <= 100)
        {
            return dbinterface::SL20Lock::UpdateBatteryLevel(client_id_, last_battery);
        }
        AK_LOG_WARN << "illegal battery level:" << last_battery;
    }

    return -1;
}

int ReportLockEventV1::HandleMessageNotifyRelatedEvent(const Json::Value& param)
{
    //通知消息构建
    CommPerTextMessage text_msg;
    ConstructPerTextMessage(param, text_msg);

    //请求正常并且需要通知
    if (req_success_ && need_notify_)
    {
        //获取发送列表并插入数据库
        PerTextMessageSendList text_messages;    //发送消息列表
        if (0 != GetMessageSendListAndSave(lock_info_.personal_account_uuid, text_msg, text_messages))
        {
            AK_LOG_WARN << "get message send list and save failed.";
            req_success_ = false;
            return 0;
        }

        //消息发送
        CSmartLock2RouteMsg::SendGroupTextMessage(text_messages, text_msg.trigger_time);
    }
    return 0;
}

void ReportLockEventV1::ConstructPerTextMessage(const Json::Value& param, CommPerTextMessage& text_msg)
{
    if (0 != ConstructCommonMessage(text_msg))
    {
        return;
    }

    if (event_type_ == SL20_LOCK_EVENT_TYPE_BATTERY_LEVEL)
    {
        ConstructBatteryLevelMessage(param, text_msg);
    }
    else if (event_type_ == SL20_LOCK_EVENT_TYPE_TRAIL_ERROR)
    {
        ConstructTrailMessage(param, text_msg);
    }
    else if (event_type_ == SL20_LOCK_EVENT_TYPE_DOORBELL)
    {
        ConstructDoorBellMessage(param, text_msg);
    }
    else
    {
        AK_LOG_WARN << "event type not support. type=" << event_type_;
    }   
    return;
}

int ReportLockEventV1::ConstructCommonMessage(CommPerTextMessage& text_msg)
{
    // 项目类型获取
    int project_type = 0;
    if (akjudge::IsCommunityEndUserRole(per_account_.role))
    {
        project_type = project::PROJECT_TYPE::RESIDENCE;
    }
    else
    {
        project_type = project::PROJECT_TYPE::PERSONAL;
    }

    //通用部分
    text_msg.project_type = project_type;
    Snprintf(text_msg.content, sizeof(text_msg.content), lock_info_.name);
    return 0;
}

void ReportLockEventV1::ConstructBatteryLevelMessage(const Json::Value& param, CommPerTextMessage& text_msg)
{
    std::string title = "Akubela Battery Warning";  //这里写死英语，实际展示由web根据type进行多语言处理

    // 电池电量低于15%时，才通知,默认不通知
    need_notify_ = false;

    int last_battery = 0;
    if (param.isMember("event_data"))
    {
        Json::Value event_data = param["event_data"];
        if (event_data.isMember("battery_level"))
        {
            last_battery = event_data["battery_level"].asInt();
            //合法性校验
            if (last_battery >= 0 && last_battery <= 15)
            {
                need_notify_ = true;
            }
        }
    }
    if (!need_notify_)
    {
        AK_LOG_INFO << "report battery level higher than 15, no need notify.";
        return;
    }

    std::map <std::string, int> json_int_datas;
    json_int_datas.emplace(MESSAGE_EXTENSION_FIELD_LAST_BATTERY, last_battery);
    std::map <std::string, std::string> json_str_datas;
    std::map <std::string, bool> json_bool_datas;

    text_msg.msg_type =  MessageType2::AKUBELA_LOCK_BATTERY_NOTICE;
    Snprintf(text_msg.extension_field, sizeof(text_msg.extension_field), GetJsonString(json_str_datas, json_int_datas, json_bool_datas).c_str());
    Snprintf(text_msg.title, sizeof(text_msg.title), title.c_str());
    return;
}

void ReportLockEventV1::ConstructTrailMessage(const Json::Value& param, CommPerTextMessage& text_msg)
{
    std::string title = "Incorrect Password Attempts Detected";

    std::string report_state;
    bool report_normal = false;
    if (param.isMember("event_data"))
    {
        Json::Value event_data = param["event_data"];
        if (event_data.isMember("state"))
        {
            if(event_data["state"].asString() == "on")
            {
                report_normal = true;
            }
        }
    }
    if (!report_normal)
    {
        AK_LOG_WARN << "report trail error content wrong, request failed.";
        req_success_ = false;
        return;
    }

    text_msg.msg_type = MessageType2::TRAILERROR_NOTICE;
    Snprintf(text_msg.title, sizeof(text_msg.title), title.c_str());
    return;
}

void ReportLockEventV1::ConstructDoorBellMessage(const Json::Value& param, CommPerTextMessage& text_msg)
{
    int64_t trigger_timestamp = 0;
    if (param.isMember("event_data"))
    {
        Json::Value event_data = param["event_data"];
        if (event_data.isMember("trigger_timestamp"))
        {
            trigger_timestamp = event_data["trigger_timestamp"].asInt();
            //合法性校验
            if (trigger_timestamp <= 0)
            {
                AK_LOG_WARN << "report doorbell event content wrong, request failed.";
                req_success_ = false;
                return;
            }
            Snprintf(text_msg.trigger_time, sizeof(text_msg.trigger_time), dbinterface::ResidentPersonalAccount::GetAccountTimeStringByTimestamp(per_account_.account, trigger_timestamp, g_time_zone_DST).c_str());
        }
    }

    std::string title = "Doorbell Event Notification";

    text_msg.msg_type = MessageType2::SMARTLOCK_DOORBELL_EVENT;
    Snprintf(text_msg.title, sizeof(text_msg.title), title.c_str());
    Snprintf(text_msg.extension_field, sizeof(text_msg.extension_field), lock_info_.uuid);

    return;
}

int ReportLockEventV1::GetMessageSendListAndSave(const std::string& node_uuid, const CommPerTextMessage& comm_text_msg, PerTextMessageSendList& text_messages)
{
    //主账号
    ResidentPerAccount master_account;
    if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(node_uuid, master_account))
    {
        AK_LOG_WARN << "get main account failed, node uuid: " << node_uuid;
        return -1;
    }

    int sub_account_role = akjudge::IsCommunityEndUserRole(master_account.role) ? ACCOUNT_ROLE_COMMUNITY_ATTENDANT : ACCOUNT_ROLE_PERSONNAL_ATTENDANT;
    //从账号列表获取
    ResidentPerAccountList sub_account_list;
    if(0 != dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(master_account.account, sub_account_role, sub_account_list))
    {
        AK_LOG_WARN << "get sub account list failed, node uuid: " << node_uuid;
        return -1;
    }

    //不需要通知设备,设备列表为空
    ResidentDeviceList dev_list;
    bool message_need_notify_dev = false;

    //Message存到数据库并获取发送列表结构
    if (0 != dbinterface::Message::AddGroupTextMsg((int)comm_text_msg.msg_type, comm_text_msg, dev_list, master_account, sub_account_list, text_messages, message_need_notify_dev))
    {
        AK_LOG_WARN << "add group text msg to db failed.";
        return -1;
    }

    return 0;    
}

void ReportLockEventV1::RecordSL20OpenDoorLog(const Json::Value& param)
{     
    ThirdPartyLockCaptureInfo third_party_lock_capture_info;
    Snprintf(third_party_lock_capture_info.lock_name, sizeof(third_party_lock_capture_info.lock_name), lock_info_.name);
    Snprintf(third_party_lock_capture_info.personal_account_uuid, sizeof(third_party_lock_capture_info.personal_account_uuid), lock_info_.personal_account_uuid);
    third_party_lock_capture_info.lock_type = ThirdPartyLockType::SL20;

    if (param.isMember("event_data"))
    {
        Json::Value event_data = param["event_data"];
        if (event_data.isMember("trigger_timestamp"))
        {
            int time_stamp = event_data["trigger_timestamp"].asInt();
            Snprintf(third_party_lock_capture_info.capture_time, sizeof(third_party_lock_capture_info.capture_time), StampToStandardTime(time_stamp).c_str());
        }
        if (event_data.isMember("unlock_mode"))
        {
            std::string unlock_mode = event_data["unlock_mode"].asString();
            third_party_lock_capture_info.capture_type = GetCaptureTypeByUnlockMode(unlock_mode);

            // 离线密码开门，写死开门用户为主账号（目前离线密码只能在主账号创建）
            if (third_party_lock_capture_info.capture_type == ACT_OPEN_DOOR_TYPE::OFFLINE_CODE_UNLOCK)
            {
                ResidentPerAccount initiator_account;
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(lock_info_.personal_account_uuid, initiator_account))
                {
                    std::string name = initiator_account.name;
                    Snprintf(third_party_lock_capture_info.initiator, sizeof(third_party_lock_capture_info.initiator), name.c_str());
                }

                if (event_data.isMember("note"))
                {
                    std::string note = event_data["note"].asString();
                    dbinterface::SL20Lock::UpdateOfflineCodeUsed(lock_info_.uuid, note);
                }
            }
        }
        if (event_data.isMember("open_door_relate"))
        {
            std::string open_door_relate = event_data["open_door_relate"].asString();
            std::string prefix = "picname_";
            auto pos = open_door_relate.find(prefix);
            //如果open_door_relate是picname,说明为设备联动开门
            if (std::string::npos != pos)
            {   
                std::string pic_name = open_door_relate.substr(pos + prefix.length());
                Snprintf(third_party_lock_capture_info.pic_name, sizeof(third_party_lock_capture_info.pic_name), pic_name.c_str());

                ResidentDev dev;
                if(0 == dbinterface::ResidentDevices::GetUUIDDev(lock_info_.device_uuid,dev))
                {   
                    Snprintf(third_party_lock_capture_info.mac, sizeof(third_party_lock_capture_info.mac), dev.mac);
                }
                else
                {
                    dbinterface::ResidentPerDevices::GetUUIDDev(lock_info_.device_uuid,dev);
                    Snprintf(third_party_lock_capture_info.mac, sizeof(third_party_lock_capture_info.mac), dev.mac);
                }
            }
            //如果open_door_relate是initiator_开头,说明为未link的锁开门
            prefix = "initiator_";
            pos = open_door_relate.find(prefix);
            if (std::string::npos != pos)
            {
                std::string account = open_door_relate.substr(pos + prefix.length());
                ResidentPerAccount initiator_account;
                if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(account, initiator_account))
                {
                    std::string name = initiator_account.name;
                    Snprintf(third_party_lock_capture_info.initiator, sizeof(third_party_lock_capture_info.initiator), name.c_str());
                }
            }
            dbinterface::ThirdPartyLockCapture::InsertThirdPartyLockCapture(third_party_lock_capture_info);
        }
    }

    
}

void ReportLockEventV1::IReplyParamConstruct()
{
    if (req_success_)
    {
        reply_data_["param"] = Json::objectValue; //空对象
    }
    return;
}

int ReportLockEventV1::GetCaptureTypeByUnlockMode(const std::string& unlock_mode)
{
    if (unlock_mode == SL20_LOCK_UNLOCK_MODE_CREDENTIAL)
    {
        return ACT_OPEN_DOOR_TYPE::LOCALKEY;
    }
    else if (unlock_mode == SL20_LOCK_UNLOCK_MODE_OFFLINE_PASSWORD)
    {
        return ACT_OPEN_DOOR_TYPE::OFFLINE_CODE_UNLOCK;
    }
    else if (unlock_mode == SL20_LOCK_UNLOCK_MODE_RF_CARD)
    {
        return ACT_OPEN_DOOR_TYPE::RFCARD;
    }
    else if (unlock_mode == SL20_LOCK_UNLOCK_MODE_REMOTE_CONTROL)
    {
        return ACT_OPEN_DOOR_TYPE::REMOTE_OPEN_DOOR;
    }
         
            
    return 0;       
}
