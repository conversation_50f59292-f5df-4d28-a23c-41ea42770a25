#ifndef _REPORT_LOCK_EVENT_V1_
#define _REPORT_LOCK_EVENT_V1_

#include "AgentBase.h"
#include <string>
#include "dbinterface/Message.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/SL20Lock.h"
class ReportLockEventV1: public IBase
{
public:
    ReportLockEventV1() : need_notify_(true){}
    ~ReportLockEventV1() = default;

    int IControl(const Json::Value& param, const std::string& lock_uuid);
    void IReplyParamConstruct();
    IBasePtr NewInstance() {return std::make_shared<ReportLockEventV1>();}

private:
    void RecordSL20OpenDoorLog(const Json::Value& param);
    int HandleMessageNotifyRelatedEvent(const Json::Value& param);
    void ConstructPerTextMessage(const Json::Value& param, CommPerTextMessage& text_msg);
    int ConstructCommonMessage(CommPerTextMessage& text_msg);
    void ConstructBatteryLevelMessage(const Json::Value& param, CommPerTextMessage& text_msg);
    void ConstructTrailMessage(const Json::Value& param, CommPerTextMessage& text_msg);
    void ConstructDoorBellMessage(const Json::Value& param, CommPerTextMessage& text_msg);
    int GetMessageSendListAndSave(const std::string& node_uuid, const CommPerTextMessage& comm_text_msg, PerTextMessageSendList& text_messages);
    int UpdateSL20BatteryLevel(const Json::Value& param);
    int GetRelatedInfo();
    int GetCaptureTypeByUnlockMode(const std::string& unlock_mode);

    std::string event_type_;
    std::string client_id_;
    ResidentPerAccount per_account_;
    SL20LockInfo lock_info_;
    bool need_notify_;
};

#endif 

