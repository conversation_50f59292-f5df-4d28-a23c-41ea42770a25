#include "SL20LockControl.h"
#include "AkcsCommonDef.h"
#include "SafeCacheConn.h"
#include "AkLogging.h"

void SL20LockControl::GetSL20LockOpenDoorInfo(const std::string& lock_uuid, std::string& state, std::string& open_door_relate, std::set<std::string>& opener_list)
{
    SafeCacheConn cache_conn(g_redis_db_sl20_lock);
    if (cache_conn.isConnect() && cache_conn.isExists(lock_uuid))
    {   
        state = "unlock";
        map<std::string, std::string> open_door_relate_map;
        cache_conn.hgetAll(lock_uuid, open_door_relate_map);
        //取获取到的第一个开门者的account
        open_door_relate = open_door_relate_map.begin()->second;
        //获取开门者account列表
        for (const auto& door_opener : open_door_relate_map)
        {
            opener_list.insert(door_opener.first); //account
        }
        cache_conn.del(lock_uuid);
    }
    else
    {
        state = "lock";
        open_door_relate = "";
    }
    return;

}
