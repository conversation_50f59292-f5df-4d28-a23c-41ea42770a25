#include "GetNewVersion.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"

using namespace Akcs;

/*
{
    "id":"cffffffd8ff404fff8fb271deffffffb0",
    "command":"v1.0_u_get_new_version",
    "param":{}
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<GetNewVersion>();
    RegSL50UpFunc(p, SL50_LOCK_GET_NEW_VERSION);
};

int GetNewVersion::IParseData(const Json::Value& param)
{   
    // param是空对象，无需解析
    AK_LOG_INFO << "Received get_new_version request";
    return 0;
}

int GetNewVersion::IControl()
{   
    // 处理获取新版本的逻辑
    AK_LOG_INFO << "Processing get_new_version request";
    
    // 这里可以添加获取新版本的具体实现逻辑
    // 例如：查询数据库获取最新版本信息，或者调用其他服务获取版本信息
    
    return 0;
}

void GetNewVersion::IReplyParamConstruct()
{
    BuildMessagAck();
}