#include "GetNewVersion.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "SL50/DownAckMessage/AckGetNewVersion.h"

using namespace Akcs;

/*
请求示例:
{
    "id":"cffffffd8ff404fff8fb271deffffffb0",
    "command":"v1.0_u_get_new_version",
    "param":{}
}

返回示例:
{
    "success": true,
    "id":"cffffffd8ff404fff8fb271deffffffb0",
    "timestamp": 131664646665465,
    "command": "v1.0_u_get_new_version",
    "param":{
        "version_no": "********",
        "device_version": "********",
        "upgrade_id": "sdfgwagaswgfwgwgwa",
        "immediately": false,
        "is_forced": false
    }
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<GetNewVersion>();
    RegSL50UpFunc(p, SL50_LOCK_GET_NEW_VERSION);
};

int GetNewVersion::IParseData(const Json::Value& param)
{   
    return 0;
}

int GetNewVersion::IControl()
{   
    AK_LOG_INFO << "Processing get_new_version request, client_id: " << client_id_;
    
    if (0 != dbinterface::SmartLockUpgrade::GetSmartLockUpgradeBySmartLockUUID(client_id_, smartlock_upgrade_info_)) 
    {   
        AK_LOG_INFO << "GetSmartLockUpgradeBySmartLockUUID failed, client_id: " << client_id_;
        return -1;
    }

    if (smartlock_upgrade_info_.upgrade_status == SMARTLOCK_UPGRADE_STATUS::DONE)
    {
        AK_LOG_INFO << "upgrade_status is already done, client_id: " << client_id_;
        return -1;
    }
    return 0;
}

void GetNewVersion::IReplyParamConstruct()
{
    AckGetNewVersion ack(smartlock_upgrade_info_.upgrade_module_version, smartlock_upgrade_info_.upgrade_module_version, smartlock_upgrade_info_.uuid);
    ack.SetAckID(id_);
    reply_data_ = ack.to_json();
}