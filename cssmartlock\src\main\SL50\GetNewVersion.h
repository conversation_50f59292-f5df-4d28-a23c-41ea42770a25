#ifndef _GET_NEW_VERSION_H_
#define _GET_NEW_VERSION_H_

#include "SL50MessageBase.h"
#include <string>

class GetNewVersion: public ILS50Base
{
public:
    GetNewVersion(){}
    ~GetNewVersion() = default;

    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<GetNewVersion>();}

private:
    // 由于param是空对象，这里不需要额外的成员变量
};

#endif