#ifndef _GET_NEW_VERSION_H_
#define _GET_NEW_VERSION_H_

#include <string>
#include "SL50MessageBase.h"
#include "dbinterface/SmartLockUpgrade.h"

class GetNewVersion: public ILS50Base
{
public:
    GetNewVersion(){}
    ~GetNewVersion() = default;

    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<GetNewVersion>();}

private:
    SmartLockUpgradeInfo smartlock_upgrade_info_;
};

#endif