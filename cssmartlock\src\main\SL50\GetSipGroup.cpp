#include "GetSipGroup.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"
#include "SL50/DownAckMessage/AckGetSipGroup.h"

using namespace Akcs;

/*
{
	"id": "c45e846ca23ab42c9ae469d988ae32a96",
	"command": "v1.0_u_get_sip_group",
	"param": {}
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<GetSipGroup>();
    RegSL50UpFunc(p, SL50_LOCK_GET_SIP_GROUP);
};

int GetSipGroup::IParseData(const Json::Value& param)
{   
    // param为空对象，无需解析
    AK_LOG_INFO << "GetSipGroup - 收到获取SIP组请求";
    return 0;
}

int GetSipGroup::IControl()
{   
    AK_LOG_INFO << "处理获取SIP组请求";
    // 在这里处理获取SIP组的逻辑
    // 可以从数据库或配置中获取SIP组信息
    return 0;
}

void GetSipGroup::IReplyParamConstruct()
{
    // 获取SIP组信息
    std::string sip_group = "12134566666"; // 默认SIP组，实际应从系统获取
    
    // 创建回复消息
    AckGetSipGroup ack(sip_group);
    ack.SetAckID(id_);
    reply_data_ = ack.to_json();
}