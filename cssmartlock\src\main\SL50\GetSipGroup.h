#ifndef _GET_SIP_GROUP_H_
#define _GET_SIP_GROUP_H_

#include "SL50MessageBase.h"
#include <string>
#include "AkLogging.h"

class GetSipGroup: public ILS50Base
{
public:
    GetSipGroup(){}
    ~GetSipGroup() = default;
    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<GetSipGroup>();}

private:
    // 由于param为空对象，这里不需要额外的成员变量
};

#endif