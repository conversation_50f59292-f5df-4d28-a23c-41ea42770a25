#include "GetSipInfo.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"
#include "SL50/DownAckMessage/AckGetSipInfo.h"
using namespace Akcs;

/*
{
	"id": "c45e846ca23ab42c9ae469d988ae32a96",
	"command": "v1.0_u_get_sip_info",
	"param": {}
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<GetSipInfo>();
    RegSL50UpFunc(p, SL50_LOCK_GET_SIP_INFO);
};

int GetSipInfo::IParseData(const Json::Value& param)
{   
    // param为空对象，无需解析
    AK_LOG_INFO << "GetSipInfo - 收到获取SIP信息请求";
    return 0;
}

int GetSipInfo::IControl()
{   
    AK_LOG_INFO << "处理获取SIP信息请求";
    // 在这里处理获取SIP信息的逻辑
    // 可以从数据库或配置中获取SIP服务器信息
    return 0;
}

void GetSipInfo::IReplyParamConstruct()
{
    // 获取SIP信息
    std::string server_url = "sip.example.com"; // 默认SIP服务器URL，实际应从配置获取
    std::string sip = "sip_account"; // 默认SIP账号，实际应从配置获取
    std::string password = "sip_password"; // 默认SIP密码，实际应从配置获取
    int sip_type = 0; // 默认SIP类型，实际应从配置获取
    int confusion = 0; // 默认混淆值，实际应从配置获取
    
    // 创建回复消息
    AckGetSipInfo ack(server_url, sip, password, sip_type, confusion);
    ack.SetAckID(id_);
    reply_data_ = ack.to_json();
}