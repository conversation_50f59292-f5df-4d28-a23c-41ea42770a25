#ifndef _GET_SIP_INFO_H_
#define _GET_SIP_INFO_H_

#include "SL50MessageBase.h"
#include <string>
#include "AkLogging.h"

class GetSipInfo: public ILS50Base
{
public:
    GetSipInfo(){}
    ~GetSipInfo() = default;
    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<GetSipInfo>();}

private:
    // 由于param为空对象，这里不需要额外的成员变量
};

#endif