#include "GetTimezoneAndTimetamp.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"
#include "SL50/DownAckMessage/AckTimeInfo.h"
using namespace Akcs;

/*
{
	"id": "c45e846ca23ab42c9ae469d988ae32a96",
	"command": "v1.0_u_get_timezone_and_timetamp",
	"param": { }
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<GetTimezoneAndTimetamp>();
    RegSL50UpFunc(p, SL50_LOCK_GET_TIMEZONE_AND_TIMETAMP);
};

int GetTimezoneAndTimetamp::IParseData(const Json::Value& param)
{   
    // param为空对象，无需解析
    AK_LOG_INFO << "GetTimezoneAndTimetamp - 收到获取时区和时间戳请求";
    return 0;
}

int GetTimezoneAndTimetamp::IControl()
{   
    AK_LOG_INFO << "处理获取时区和时间戳请求";
    // 在这里处理获取时区和时间戳的逻辑
    // 可以从系统获取当前时区信息和时间戳
    return 0;
}

void GetTimezoneAndTimetamp::IReplyParamConstruct()
{
    // 获取系统时区信息
    std::string timezone = "GMT-10:00"; // 默认时区，实际应从系统获取
    std::string daylight_timezone = "GMT-9:00"; // 默认夏令时时区，实际应从系统获取
    
    // 创建回复消息
    AckTimeInfo ack(timezone, daylight_timezone);
    ack.SetAckID(id_);
    reply_data_ = ack.to_json();
}