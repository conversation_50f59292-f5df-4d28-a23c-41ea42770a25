#ifndef _GET_TIMEZONE_AND_TIMETAMP_H_
#define _GET_TIMEZONE_AND_TIMETAMP_H_

#include "SL50MessageBase.h"
#include <string>
#include "AkLogging.h"

class GetTimezoneAndTimetamp: public ILS50Base
{
public:
    GetTimezoneAndTimetamp(){}
    ~GetTimezoneAndTimetamp() = default;
    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<GetTimezoneAndTimetamp>();}

private:
    // 由于param为空对象，这里不需要额外的成员变量
};

#endif