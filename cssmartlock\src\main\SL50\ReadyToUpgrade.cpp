#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"
#include "ReadyToUpgrade.h"
#include "SL50/DownAckMessage/AckReadyToUpgrade.h"


using namespace Akcs;

/*
请求示例:
{
    "id":"cffffffd8ff404fff8fb271deffffffb0",
    "command":"v1.0_u_ready_to_upgrade",
    "param":{
        "upgrade_id":"u7028aa298e654c2aa033952223e615fe"
    }
}

返回示例:
{
    "success": true,
    "timestamp": 1646119529324,
    "id":"c45e846ca23ab42c9ae469d988ae32a96",
    "command":"v1.0_u_ready_to_upgrade
    "param": {
        "upgrade_id":"u7028aa298e654c2aa033952223e615fe",
        "version_path":"https://tempfile.akubela.com/aa/bb/cc",
        "product_path":"https://tempfile.akubela.com/aa/bb/csf",
        "dependency_path":"modify",
        "record": "modify",
        "record_path": "https://tempfile.akubela.com/aa/bb/11
    }
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<ReadyToUpgrade>();
    RegSL50UpFunc(p, SL50_LOCK_READY_TO_UPGRADE);
};

int ReadyToUpgrade::IParseData(const Json::Value& param)
{   
    upgrade_id_ = param.get("upgrade_id", "").asString();
    AK_LOG_INFO << "IParseData ReadyToUpgrade - upgrade_id: " << upgrade_id_;
    return 0;
}

int ReadyToUpgrade::IControl()
{   
    AK_LOG_INFO << "Processing ready_to_upgrade request, client_id_" << client_id_;
    if (0 != dbinterface::SmartLockUpgrade::GetSmartLockUpgradeBySmartLockUUID(client_id_, smartlock_upgrade_info_)) 
    {   
        AK_LOG_INFO << "GetSmartLockUpgradeBySmartLockUUID failed, client_id_" << client_id_;
        return -1;
    }

    if (smartlock_upgrade_info_.upgrade_status == SMARTLOCK_UPGRADE_STATUS::NONE)
    {
        AK_LOG_INFO << "upgrade_status is DONE, no need to upgrade, client_id_" << client_id_ << ", upgrade_id_" << upgrade_id_;
        return -1;
    }
    return 0;
}

void ReadyToUpgrade::IReplyParamConstruct()
{
    std::string record = "modify";
    std::string dependency_path = "modify";
    
    AckReadyToUpgrade ack(upgrade_id_, smartlock_upgrade_info_.firmware_download_url, smartlock_upgrade_info_.firmware_download_url, 
                    smartlock_upgrade_info_.firmware_download_url, record, dependency_path);

    ack.SetAckID(id_);
    reply_data_ = ack.to_json();
}