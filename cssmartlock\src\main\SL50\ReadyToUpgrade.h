#ifndef _READY_TO_H_
#define _GET_NEW_VERSION_H_

#include <string>
#include "SL50MessageBase.h"
#include "dbinterface/SmartLockUpgrade.h"

class ReadyToUpgrade: public ILS50Base
{
public:
    ReadyToUpgrade(){}
    ~ReadyToUpgrade() = default;

    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<ReadyToUpgrade>();}

private:
    std::string upgrade_id_;
    SmartLockUpgradeInfo smartlock_upgrade_info_;
};

#endif