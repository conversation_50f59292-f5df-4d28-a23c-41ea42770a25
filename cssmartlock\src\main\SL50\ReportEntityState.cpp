#include "ReportEntityState.h"
#include "events/SmartLockEventService.h"
#include "AkLogging.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include <regex>
#include <chrono>

namespace SmartLock {

/*
自动注册消息处理器
支持两种命令格式：
1. v1.0_u_report_entity_state (通用实体状态上报)
2. v1.0_u_lock_report_entity_state (锁实体状态上报)
*/
__attribute__((constructor)) static void Init(){
    // 注册通用实体状态上报处理器
    ILS50BasePtr p1 = std::make_shared<ReportEntityState>();
    RegSL50UpFunc(p1, SL50_LOCK_REPORT_ENTITY_STATE);

    // 注册锁实体状态上报处理器
    ILS50BasePtr p2 = std::make_shared<ReportEntityState>();
    RegSL50UpFunc(p2, SL50_LOCK_REPORT_LOCK_ENTITY_STATE);
};

int ReportEntityState::IParseData(const Json::Value& json) 
{
    if (!json.isMember("entities") || !json["entities"].isArray()) {
        AK_LOG_ERROR << "No entities array found in param JSON";
        return -1;
    }

    const Json::Value& entities_array = json["entities"];
    for (const auto& entity_json : entities_array) {
        Entity entity;
        entity.from_json(entity_json);
        // 设置 device_time（如果存在）
        if (json.isMember("device_time")) {
            entity.device_time = json["device_time"].asString();
        }
        entities_.push_back(std::move(entity));
    }
    
    return 0;
}

int ReportEntityState::IControl() 
{
    AK_LOG_INFO << "ReportEntityState::IControl - 开始处理消息";

    if (entities_.empty()) {
        AK_LOG_WARN << "No entities to process";
        return 0;
    }

    // 使用事件服务的统一消息处理入口
    SmartLock::Events::SmartLockEventService& event_service = SmartLock::Events::SmartLockEventService::GetInstance();
    event_service.ProcessEntityStateMessage(entities_);
    
    AK_LOG_INFO << "ReportEntityState::IControl - 处理完成";
    return 0;
}

void ReportEntityState::IReplyParamConstruct() 
{
    BuildMessagAck();
    AK_LOG_INFO << "Reply message constructed successfully";
}

// SafeJsonParser 实现
bool SafeJsonParser::parseJson(const std::string& json_str, Json::Value& result) {
    if (!checkJsonSize(json_str)) {
        char size_str[32];
        snprintf(size_str, sizeof(size_str), "%zu", json_str.size());
        AK_LOG_WARN << "JSON string too large: " << size_str << " bytes";
        return false;
    }

    try {
        Json::Reader reader;

        if (!reader.parse(json_str, result)) {
            AK_LOG_WARN << "JSON parse failed: " << reader.getFormatedErrorMessages();
            return false;
        }

        if (!checkJsonDepth(result)) {
            AK_LOG_WARN << "JSON depth too deep";
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        AK_LOG_ERROR << "Exception during JSON parsing: " << e.what();
        return false;
    }
}

bool SafeJsonParser::jsonToString(const Json::Value& json, std::string& result) {
    try {
        Json::FastWriter writer;
        result = writer.write(json);

        // 移除 FastWriter 添加的末尾换行符
        if (!result.empty() && result.back() == '\n') {
            result.pop_back();
        }

        if (!checkJsonSize(result)) {
            char size_str[32];
            snprintf(size_str, sizeof(size_str), "%zu", result.size());
            AK_LOG_WARN << "Generated JSON string too large: " << size_str << " bytes";
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        AK_LOG_ERROR << "Exception during JSON to string conversion: " << e.what();
        return false;
    }
}

bool SafeJsonParser::validateJsonStructure(const Json::Value& json) {
    return !json.isNull() && checkJsonDepth(json);
}

bool SafeJsonParser::checkJsonDepth(const Json::Value& json, int max_depth) {
    if (max_depth <= 0) {
        return false;
    }

    if (json.isObject()) {
        Json::Value::Members members = json.getMemberNames();
        for (Json::Value::Members::iterator it = members.begin(); it != members.end(); ++it) {
            if (!checkJsonDepth(json[*it], max_depth - 1)) {
                return false;
            }
        }
    } else if (json.isArray()) {
        for (Json::Value::ArrayIndex i = 0; i < json.size(); ++i) {
            if (!checkJsonDepth(json[i], max_depth - 1)) {
                return false;
            }
        }
    }

    return true;
}

bool SafeJsonParser::checkJsonSize(const std::string& json_str, size_t max_size) {
    return json_str.size() <= max_size;
}

} // namespace SmartLock




/*
{
        "id": "p45e846ca23ab42c9ae469d988ae32a96",
        "command": "v1.0_u_report_entity_state",
        "param": {
            "device_time": "2023-07-25 01:01:01",
            "entities": [
                {
                    "entity_id": "climate.62b4df83b2606a6912efa657f0a3e7ee",
                    "device_id": "dxxxxxxxxxxxxx",
                    "value": {
                        "state": "on",
                        "attributes": {
                            "device_class": "motion",
                            "supported_features": 2,
                            "dwell": "on",
                            "unit_of_measurement": "C",
                            "image": "climate_img.jpg"
                        },
                        "timestamp": 1718611119,
                        "cloud_timestamp": 1718611121
                    },
                    "previous_value": {
                        "state": "off",
                        "attributes": {
                            "device_class": "motion",
                            "supported_features": 2,
                            "dwell": "off",
                            "unit_of_measurement": "C",
                            "image": "climate_img_prev.jpg"
                        },
                        "timestamp": 1718611000,
                        "cloud_timestamp": 1718611001
                    }
                },
                {
                    "entity_id": "lock.1234567890abcdef",
                    "device_id": "dyyyyyyyyyyyyyy",
                    "value": {
                        "state": "locked",
                        "attributes": {
                            "lock_status": "engaged",
                            "battery_level": "80%"
                        },
                        "timestamp": 1718612222,
                        "cloud_timestamp": 1718612223
                    },
                    "previous_value": {
                        "state": "unlocked",
                        "attributes": {
                            "lock_status": "disengaged",
                            "battery_level": "85%"
                        },
                        "timestamp": 1718612000,
                        "cloud_timestamp": 1718612001
                    }
                }
            ]
        }
    }
*/