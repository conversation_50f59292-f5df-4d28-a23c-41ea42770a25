#pragma once
#include "SL50MessageBase.h"
#include "entities/Entity.h"
#include <vector>
#include <string>
#include <memory>

namespace SmartLock {

/**
 * 处理结果枚举
 */
enum ProcessingResult {
    PROCESSING_SUCCESS = 0,
    PROCESSING_PARSE_ERROR,
    PROCESSING_VALIDATION_ERROR,
    PROCESSING_ERROR,
    PROCESSING_DOMAIN_SERVICE_ERROR
};

/**
 * 实体状态上报处理器
 * 改进点：
 * 1. 统一错误处理
 * 2. 内存安全
 * 3. 输入验证
 * 4. 异常安全
 */
class ReportEntityState : public ILS50Base {
public:
    ReportEntityState() {}
    virtual ~ReportEntityState() {}

    // 禁用拷贝构造和赋值，确保对象唯一性
    ReportEntityState(const ReportEntityState&); // 不实现
    ReportEntityState& operator=(const ReportEntityState&); // 不实现

    virtual int IParseData(const Json::Value& json);
    virtual int IControl();
    virtual void IReplyParamConstruct();
    
    ILS50BasePtr NewInstance() { 
        return std::make_shared<ReportEntityState>(); 
    }

private:
    std::vector<Entity> entities_;
    std::string json_message_;
};


/**
 * JSON 解析器 - 安全的 JSON 处理
 */
class SafeJsonParser {
public:
    /**
     * 安全解析 JSON 字符串
     */
    static bool parseJson(const std::string& json_str, Json::Value& result);

    /**
     * 安全转换 JSON 为字符串
     */
    static bool jsonToString(const Json::Value& json, std::string& result);

    /**
     * 验证 JSON 结构
     */
    static bool validateJsonStructure(const Json::Value& json);

private:
    /**
     * 检查 JSON 深度（防止过深嵌套导致栈溢出）
     */
    static bool checkJsonDepth(const Json::Value& json, int max_depth = 10);

    /**
     * 检查 JSON 大小（防止过大 JSON 导致内存问题）
     */
    static bool checkJsonSize(const std::string& json_str, size_t max_size = 1024 * 1024); // 1MB
};


} // namespace SmartLock