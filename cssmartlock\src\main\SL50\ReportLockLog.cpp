#include "ReportLockLog.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"

using namespace Akcs;

/*
{
	"id": "pabc4676ccc0438e91c72609ada858e",
	"command": "v1.0_u_report_lock_log",
	"param": [{ 
		"entity_id": "lock.d13ca332eaf4e4aa4932e41fac463585a",
		"note": "user 0",
		"image": "94f11531e4472e316f59d07bc5c69f92.jpg",
		"log_type": 1,
		"state": 10031,
		"timestamp": 1725526955,
        "key_id": 6333
	}]
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<ReportLockLog>();
    RegSL50UpFunc(p, SL50_LOCK_REPORT_LOCK_LOG);
};

int ReportLockLog::IParseData(const Json::Value& param)
{   
    if (param.isArray()) {
        for (const auto& entry : param) {
            LockLogEntry log_entry;
            log_entry.from_json(entry);
            log_entries_.push_back(std::move(log_entry));
        }
    }
    return 0;
}

int ReportLockLog::IControl()
{   
    for (const auto& entry : log_entries_) {
        entry.process();
    }
    return 0;
}

void ReportLockLog::IReplyParamConstruct()
{
    BuildMessagAck();
}