#ifndef _REPORT_LOCK_LOG_H_
#define _REPORT_LOCK_LOG_H_

#include "SL50MessageBase.h"
#include <string>
#include "AkLogging.h"

struct LockLogEntry {
    std::string entity_id;
    std::string note;
    std::string image;
    int log_type;
    int state;
    long long timestamp;
    int key_id;

    void from_json(const Json::Value& j) {
        entity_id = j.get("entity_id", "").asString();
        note = j.get("note", "").asString();
        image = j.get("image", "").asString();
        log_type = j.get("log_type", 0).asInt();
        state = j.get("state", 0).asInt();
        timestamp = j.get("timestamp", 0).asDulLong();
        key_id = j.get("key_id", 0).asInt();
    }
    void process() const {
        AK_LOG_INFO << "LockLogEntry - entity_id: " << entity_id << ", note: " << note << ", image: " << image 
                  << ", log_type: " << log_type << ", state: " << state << ", timestamp: " << timestamp << ", key_id: " << key_id;
    }
    
        
};

class ReportLockLog: public ILS50Base
{
public:
    ReportLockLog(){}
    ~ReportLockLog() = default;
    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<ReportLockLog>();}

private:
    std::vector<LockLogEntry> log_entries_;
};

#endif