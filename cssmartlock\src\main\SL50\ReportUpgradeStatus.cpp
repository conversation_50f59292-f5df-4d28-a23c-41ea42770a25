#include "ReportUpgradeStatus.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "util_string.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "AkLogging.h"
#include "SmartLockMsgDef.h"
#include "SmartLockReqCommon.h"
#include "UpMessageSL50Factory.h"

using namespace Akcs;

/*
请求示例:
{
	"id":"c113dff4fff3346ff85f0ffffe2a7ff3c",
	"command":"v1.0_u_report_upgrade_status",
	"param":{
		"upgrade_id": "u7028aa298e654c2aa033952223e615fe",
        "status": 2,
        "message": "failed"
	}
}

返回示例:
{
    "success": true,
    "id": "c113dff4fff3346ff85f0ffffe2a7ff3c",
    "timestamp": 1646119529324,
    "command": "v1.0_u_report_upgrade_status",
    "param": {}
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<ReportUpgradeStatus>();
    RegSL50UpFunc(p, SL50_LOCK_REPORT_UPGRADE_STATUS);
};

int ReportUpgradeStatus::IParseData(const Json::Value& param)
{   
    device_version_ = param.get("device_version", "").asString();
    AK_LOG_INFO << "ReportUpgradeStatus - device_version: " << device_version_;
    return 0;
}

int ReportUpgradeStatus::IControl()
{
    AK_LOG_INFO << "Processing device version report: " << device_version_;
    // 在这里处理设备版本上报的逻辑
    // 可以将版本信息存储到数据库或进行其他操作



    return 0;
}

void ReportUpgradeStatus::IReplyParamConstruct()
{
    BuildMessagAck();
}
