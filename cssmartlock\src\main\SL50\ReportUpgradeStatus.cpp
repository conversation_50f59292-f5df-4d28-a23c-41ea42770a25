#include "ReportUpgradeStatus.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "util_string.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "AkLogging.h"
#include "SmartLockMsgDef.h"
#include "SmartLockReqCommon.h"
#include "UpMessageSL50Factory.h"
#include "dbinterface/SmartLockUpgrade.h"

/*
请求示例:
{
	"id":"c113dff4fff3346ff85f0ffffe2a7ff3c",
	"command":"v1.0_u_report_upgrade_status",
	"param":{
		"upgrade_id": "u7028aa298e654c2aa033952223e615fe",
        "status": 2,
        "message": "failed"
	}
}

返回示例:
{
    "success": true,
    "id": "c113dff4fff3346ff85f0ffffe2a7ff3c",
    "timestamp": 1646119529324,
    "command": "v1.0_u_report_upgrade_status",
    "param": {}
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<ReportUpgradeStatus>();
    RegSL50UpFunc(p, SL50_LOCK_REPORT_UPGRADE_STATUS);
};

int ReportUpgradeStatus::IParseData(const Json::Value& param)
{
    message_ = param.get("message", "").asString();
    upgrade_id_ = param.get("upgrade_id", "").asString();
    status_ = static_cast<SL50_UPGRADE_STATUS>(param.get("status", 0).asInt());

    AK_LOG_INFO << "ReportUpgradeStatus - upgrade_id: " << upgrade_id_
                << ", status: " << GetStatusString(status_)
                << ", message: " << message_;
    return 0;
}

int ReportUpgradeStatus::IControl()
{
    if (status_ == SL50_UPGRADE_STATUS::SUCCESS) 
    {
        dbinterface::SmartLockUpgrade::UpdateSmartLockUpgradeStatus(client_id_, SMARTLOCK_UPGRADE_STATUS::DONE);
    }
    return 0;
}

void ReportUpgradeStatus::IReplyParamConstruct()
{
    BuildMessagAck();
}

std::string ReportUpgradeStatus::GetStatusString(SL50_UPGRADE_STATUS status)
{
    switch (status) {
        case SL50_UPGRADE_STATUS::NOT_RECEIVED:
            return "设备未收到通知";
        case SL50_UPGRADE_STATUS::FAILED:
            return "未成功";
        case SL50_UPGRADE_STATUS::SUCCESS:
            return "升级成功";
        case SL50_UPGRADE_STATUS::PENDING_USER_CONFIRM:
            return "设备收到通知，未开启自动更新，用户还未确认";
        case SL50_UPGRADE_STATUS::USER_REJECTED:
            return "设备收到通知，并且用户拒绝";
        case SL50_UPGRADE_STATUS::WAITING_IDLE_TIME:
            return "设备收到通知，开启自动更新等待空闲更新，或者未开启自动更新，但用户已经确认再空闲时间更新";
        case SL50_UPGRADE_STATUS::OTHER_ERROR:
            return "其他原因的错误（后端错误）";
        default:
            return "未知状态";
    }
}