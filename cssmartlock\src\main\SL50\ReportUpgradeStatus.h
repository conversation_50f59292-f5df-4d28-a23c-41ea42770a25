#ifndef _REPORT_UPGRADE_STATUS_H_
#define _REPORT_UPGRADE_STATUS_H_

#include "SL50MessageBase.h"
#include <string>
#include "AkLogging.h"


enum class UPGRADE_STATUS {
    SUCCESS = 0,
    FAILED = 1
};

class ReportUpgradeStatus: public ILS50Base
{
public:
    ReportUpgradeStatus(){}
    ~ReportUpgradeStatus() = default;
    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<ReportUpgradeStatus>();}

private:
    int status_;
    std::string message_;
    std::string upgrade_id_;
};

#endif