#include "ReportVersion.h"
#include <string>
#include "util.h"
#include "util_time.h"
#include "util_judge.h"
#include "MqttPublish.h"
#include "json/json.h"
#include "UpMessageSL50Factory.h"
#include "SmartLockMsgDef.h"
#include "AkLogging.h"
#include "SmartLockReqCommon.h"
#include "util_string.h"

using namespace Akcs;

/*
{
	"id":"c113dff4fff3346ff85f0ffffe2a7ff3c",
	"command":"v1.0_u_report_version",
	"param":{
		"device_version":"***********"
	}
}
*/

__attribute__((constructor)) static void Init(){
    ILS50BasePtr p = std::make_shared<ReportVersion>();
    RegSL50UpFunc(p, SL50_LOCK_REPORT_VERSION);
};

int ReportVersion::IParseData(const Json::Value& param)
{   
    device_version_ = param.get("device_version", "").asString();
    AK_LOG_INFO << "ReportVersion - device_version: " << device_version_;
    return 0;
}

int ReportVersion::IControl()
{   
    AK_LOG_INFO << "Processing device version report: " << device_version_;
    // 在这里处理设备版本上报的逻辑
    // 可以将版本信息存储到数据库或进行其他操作
    return 0;
}

void ReportVersion::IReplyParamConstruct()
{
    BuildMessagAck();
}
