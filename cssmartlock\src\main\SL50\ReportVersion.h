#ifndef _REPORT_VERSION_H_
#define _REPORT_VERSION_H_

#include "SL50MessageBase.h"
#include <string>
#include "AkLogging.h"

class ReportVersion: public ILS50Base
{
public:
    ReportVersion(){}
    ~ReportVersion() = default;
    int IParseData(const Json::Value& param);
    int IControl();
    void IReplyParamConstruct();
    ILS50BasePtr NewInstance() {return std::make_shared<ReportVersion>();}

private:
    std::string device_version_;
};

#endif