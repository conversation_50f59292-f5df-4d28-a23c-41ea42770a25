#pragma once
#include <string>
#include <iostream>
#include <json/json.h>
#include "AkLogging.h"

namespace SmartLock {

class AttributeBase {
public:
    AttributeBase() = default;
    virtual ~AttributeBase() = default;

    virtual void fromJson(const Json::Value& json) = 0;
    virtual void toJson(Json::Value& json) const = 0;

protected:
    int64_t timestamp = 0;
    int64_t cloud_timestamp = 0;
};

} // namespace SmartLock
