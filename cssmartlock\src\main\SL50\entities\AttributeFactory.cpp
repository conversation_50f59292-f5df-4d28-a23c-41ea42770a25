#include "AttributeFactory.h"
#include "LockAttributes.h"
#include "ClimateAttributes.h"
#include "SensorAttributes.h"
#include "BinarySensorAttributes.h"
#include <memory>
#include <string>
#include <json/json.h>
#include "AttributeBase.h"

namespace SmartLock {

std::unique_ptr<AttributeBase> CreateAttributes(const std::string& entity_id, const Json::Value& j) {
    std::unique_ptr<AttributeBase> ptr;
    
    if (entity_id.find("climate.") == 0) {
        ptr = std::unique_ptr<AttributeBase>(new ClimateAttributes());
    } else if (entity_id.find("lock.") == 0) {
        ptr = std::unique_ptr<AttributeBase>(new LockAttributes());
    } else if (entity_id.find("sensor.") == 0) {
        ptr = std::unique_ptr<AttributeBase>(new SensorAttributes());
    } else if (entity_id.find("binary_sensor.") == 0) {
        ptr = std::unique_ptr<AttributeBase>(new BinarySensorAttributes());
    }
    
    if (ptr) {
        ptr->fromJson(j);
    }
    return ptr;
}

} // namespace SmartLock
