#pragma once
#include "AttributeBase.h"
#include <string>

namespace SmartLock {

struct BinarySensorAttributes : public AttributeBase {
    std::string state;
    std::string device_class;
    std::string dwell;        // 逗留告警属性
    std::string image;        // 图片属性
    std::string tamper;       // 防拆告警属性

    void fromJson(const Json::Value& j) override {
        state = j.get("state", "").asString();
        device_class = j.get("device_class", "").asString();
        dwell = j.get("dwell", "").asString();
        image = j.get("image", "").asString();
        tamper = j.get("tamper", "").asString();
    }

    void toJson(Json::Value& json) const override {
        json["state"] = state;
        json["device_class"] = device_class;
        if (!dwell.empty()) json["dwell"] = dwell;
        if (!image.empty()) json["image"] = image;
        if (!tamper.empty()) json["tamper"] = tamper;
    }
};

} // namespace SmartLock
