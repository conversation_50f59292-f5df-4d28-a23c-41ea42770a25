#pragma once
#include "AttributeBase.h"
#include <string>

namespace SmartLock {

struct ClimateAttributes : public AttributeBase {
    std::string device_class;
    int supported_features = 0;
    std::string dwell;
    std::string unit_of_measurement;
    std::string image;

    void fromJson(const Json::Value& j) override {
        device_class = j.get("device_class", "").asString();
        supported_features = j.get("supported_features", 0).asInt();
        dwell = j.get("dwell", "").asString();
        unit_of_measurement = j.get("unit_of_measurement", "").asString();
        image = j.get("image", "").asString();
    }
    
    void toJson(Json::Value& json) const override {
        json["device_class"] = device_class;
        json["supported_features"] = supported_features;
        json["dwell"] = dwell;
        json["unit_of_measurement"] = unit_of_measurement;
        json["image"] = image;
    }
};

} // namespace SmartLock
