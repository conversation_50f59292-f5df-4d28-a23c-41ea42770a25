#pragma once
#include <string>
#include <memory>
#include <iostream>
#include <cstdint>
#include <stdexcept>
#include <json/json.h>
#include "AttributeFactory.h"
#include "AttributeBase.h"
#include "LockAttributes.h"
#include "SensorAttributes.h"
#include "BinarySensorAttributes.h"
#include "ClimateAttributes.h"
#include "AkLogging.h"

namespace SmartLock {

enum class EntityDomain {
    UNKNOWN,
    LOCK,
    SENSOR,
    BINARY_SENSOR,
    CLIMATE
};

enum class EntityEventType {
    UNKNOWN,
    DOOR_BELL,
    TRIAL_AND_ERROR,
    DWELL,
    TAMPERED,
    BATTERY_LOW,
    LOCK_CONFIGURATION,
    REMOTE_UNLOCK,
};

struct EntityStateValue 
{
    std::string state;
    std::unique_ptr<AttributeBase> attributes;
    int64_t timestamp = 0;
    int64_t cloud_timestamp = 0;
    
    // 默认构造函数
    EntityStateValue() = default;
    
    // 拷贝构造函数
    EntityStateValue(const EntityStateValue& other) 
        : state(other.state), timestamp(other.timestamp), cloud_timestamp(other.cloud_timestamp) {
        if (other.attributes) {
            // 根据类型克隆属性对象
            attributes = CloneAttributes(other.attributes.get());
        }
    }
    
    // 赋值运算符
    EntityStateValue& operator=(const EntityStateValue& other) {
        if (this != &other) {
            state = other.state;
            timestamp = other.timestamp;
            cloud_timestamp = other.cloud_timestamp;
            if (other.attributes) {
                attributes = CloneAttributes(other.attributes.get());
            } else {
                attributes.reset();
            }
        }
        return *this;
    }
    
    // 移动构造函数
    EntityStateValue(EntityStateValue&& other) = default;
    
    // 移动赋值运算符
    EntityStateValue& operator=(EntityStateValue&& other) = default;

    void from_json(const Json::Value& j, EntityDomain domain, const std::string& entity_id) {
        state = j.get("state", "").asString();
        timestamp = j.get("timestamp", 0).asInt();
        cloud_timestamp = j.get("cloud_timestamp", 0).asInt();
        
        // 使用AttributeFactory创建相应的属性对象
        Json::Value attr_json = j.get("attributes", Json::Value(Json::objectValue));
        attributes = CreateAttributes(entity_id, attr_json);
    }

    void to_json(Json::Value& j) const {
        j["state"] = state;
        j["timestamp"] = static_cast<int>(timestamp);
        j["cloud_timestamp"] = static_cast<int>(cloud_timestamp);
        
        // 将属性对象转换为JSON
        Json::Value attr_json(Json::objectValue);
        if (attributes) {
            attributes->toJson(attr_json);
        }
        j["attributes"] = attr_json;
    }
    
    // 获取特定类型的属性
    template<typename T>
    T* GetAttributes() const {
        return dynamic_cast<T*>(attributes.get());
    }
    
    // 检查是否有属性且属性有特定成员
    bool HasAttribute(const std::string& attr_name) const {
        if (!attributes) return false;

        Json::Value json;
        attributes->toJson(json);

        // 检查字段是否存在且有值（不是null或空对象）
        if (!json.isMember(attr_name)) {
            return false;
        }

        const Json::Value& value = json[attr_name];
        // 如果是null值，返回false
        if (value.isNull()) {
            return false;
        }

        // 如果是对象类型，检查是否为空对象
        if (value.isObject() && value.empty()) {
            return false;
        }

        // 如果是字符串类型，检查是否为空字符串
        if (value.isString() && value.asString().empty()) {
            return false;
        }

        return true;
    }
    
    // 获取属性值的字符串表示
    std::string GetAttributeAsString(const std::string& attr_name) const {
        if (!attributes) return "";
        
        Json::Value json;
        attributes->toJson(json);
        
        if (json.isMember(attr_name)) {
            return json[attr_name].asString();
        }
        return "";
    }

private:
    std::unique_ptr<AttributeBase> CloneAttributes(const AttributeBase* source) const {
        if (!source) return nullptr;
        
        // 将源属性转换为JSON然后创建新对象
        Json::Value json;
        source->toJson(json);
        
        // 根据类型创建新的属性对象
        if (dynamic_cast<const LockAttributes*>(source)) {
            std::unique_ptr<AttributeBase> attr(new LockAttributes());
            attr->fromJson(json);
            return attr;
        } else if (dynamic_cast<const SensorAttributes*>(source)) {
            std::unique_ptr<AttributeBase> attr(new SensorAttributes());
            attr->fromJson(json);
            return attr;
        } else if (dynamic_cast<const BinarySensorAttributes*>(source)) {
            std::unique_ptr<AttributeBase> attr(new BinarySensorAttributes());
            attr->fromJson(json);
            return attr;
        } else if (dynamic_cast<const ClimateAttributes*>(source)) {
            std::unique_ptr<AttributeBase> attr(new ClimateAttributes());
            attr->fromJson(json);
            return attr;
        }
        
        return nullptr;
    }
};

struct Entity 
{
    std::string entity_id;
    std::string device_id;
    EntityDomain domain;
    EntityStateValue current_value;
    EntityStateValue previous_value;
    std::string device_time;
    
    // 默认构造函数
    Entity() = default;
    
    // 拷贝构造函数
    Entity(const Entity& other) 
        : entity_id(other.entity_id), device_id(other.device_id), 
          domain(other.domain), current_value(other.current_value), 
          previous_value(other.previous_value), device_time(other.device_time) {
    }
    
    // 移动构造函数
    Entity(Entity&& other) = default;
    
    // 拷贝赋值操作符
    Entity& operator=(const Entity& other) {
        if (this != &other) {
            entity_id = other.entity_id;
            device_id = other.device_id;
            domain = other.domain;
            current_value = other.current_value;
            previous_value = other.previous_value;
            device_time = other.device_time;
        }
        return *this;
    }
    
    // 移动赋值操作符
    Entity& operator=(Entity&& other) = default;
    
    static EntityDomain GetDomainFromEntityId(const std::string& entity_id) {
        if (entity_id.find("lock.") == 0) return EntityDomain::LOCK;
        if (entity_id.find("sensor.") == 0) return EntityDomain::SENSOR;
        if (entity_id.find("binary_sensor.") == 0) return EntityDomain::BINARY_SENSOR;
        if (entity_id.find("climate.") == 0) return EntityDomain::CLIMATE;
        return EntityDomain::UNKNOWN;
    }
    
    static std::string DomainToString(EntityDomain domain) {
        switch (domain) {
            case EntityDomain::LOCK: return "lock";
            case EntityDomain::SENSOR: return "sensor";
            case EntityDomain::BINARY_SENSOR: return "binary_sensor";
            case EntityDomain::CLIMATE: return "climate";
            default: return "unknown";
        }
    }

    void from_json(const Json::Value& j) {
        device_id = j.get("device_id", "").asString();
        entity_id = j.get("entity_id", "").asString();
        domain = GetDomainFromEntityId(entity_id);
        
        previous_value.from_json(j.get("previous_value", Json::Value(Json::objectValue)), domain, entity_id);
        current_value.from_json(j.get("value", Json::Value(Json::objectValue)), domain, entity_id);
    }

    void to_json(Json::Value& j) const {
        j["entity_id"] = entity_id;
        j["device_id"] = device_id; 
        j["domain"] = DomainToString(domain);
        j["device_time"] = device_time;
        
        Json::Value current_json, previous_json;
        current_value.to_json(current_json);
        previous_value.to_json(previous_json);
        
        j["value"] = current_json;
        j["previous_value"] = previous_json;
    }

    // 字符串转域枚举
    static EntityDomain StringToDomain(const std::string& s) {
        if (s == "lock") return EntityDomain::LOCK;
        if (s == "sensor") return EntityDomain::SENSOR;
        if (s == "binary_sensor") return EntityDomain::BINARY_SENSOR;
        if (s == "climate") return EntityDomain::CLIMATE;
        return EntityDomain::UNKNOWN;
    }

    static std::string EventTypeToString(EntityEventType event_type) {
        switch (event_type) {
            case EntityEventType::DOOR_BELL: return "door_bell";
            case EntityEventType::TRIAL_AND_ERROR: return "trial_and_error";
            case EntityEventType::DWELL: return "dwell";
            case EntityEventType::TAMPERED: return "tampered";
            case EntityEventType::BATTERY_LOW: return "battery_low";
            case EntityEventType::LOCK_CONFIGURATION: return "lock_configuration";
            case EntityEventType::REMOTE_UNLOCK: return "remote_unlock";
            default: return "unknown";
        }
    }

    // 便捷方法：获取当前值的特定类型属性
    template<typename T>
    T* GetCurrentAttributes() const {
        return current_value.GetAttributes<T>();
    }
    
    // 便捷方法：获取之前值的特定类型属性
    template<typename T>
    T* GetPreviousAttributes() const {
        return previous_value.GetAttributes<T>();
    }
};

} // namespace SmartLock
