#pragma once
#include "AttributeBase.h"
#include "AkLogging.h"
#include <string>

namespace SmartLock {

struct LockAttributes : public AttributeBase {
    std::string lock_status;
    std::string battery_level;
    std::string doorbell;
    std::string trial_and_error;
    Json::Value basic_configuration;

    void fromJson(const Json::Value& j) override {
        lock_status = j.get("lock_status", "").asString();
        battery_level = j.get("battery_level", "").asString();
        doorbell = j.get("doorbell", "").asString();
        trial_and_error = j.get("trial_and_error", "").asString();
        basic_configuration = j.get("basic_configuration", Json::Value(Json::objectValue));
    }

    void toJson(Json::Value& json) const override {
        json["lock_status"] = lock_status;
        json["battery_level"] = battery_level;
        json["doorbell"] = doorbell;
        json["trial_and_error"] = trial_and_error;
        json["basic_configuration"] = basic_configuration;
    }
};

} // namespace SmartLock
