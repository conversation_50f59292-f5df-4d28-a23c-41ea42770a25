#pragma once
#include "AttributeBase.h"
#include <string>

namespace SmartLock {

struct SensorAttributes : public AttributeBase {
    std::string state;
    std::string unit_of_measurement;
    double value = 0.0;

    void fromJson(const Json::Value& j) override {
        state = j.get("state", "").asString();
        unit_of_measurement = j.get("unit_of_measurement", "").asString();
        value = j.get("value", 0.0).asDouble();
    }

    void toJson(Json::Value& json) const override {
        json["state"] = state;
        json["unit_of_measurement"] = unit_of_measurement;
        json["value"] = value;
    }
};

} // namespace SmartLock
