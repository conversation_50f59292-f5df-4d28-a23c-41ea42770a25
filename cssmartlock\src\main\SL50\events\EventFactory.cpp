#include "EventFactory.h"
#include "lock/DoorbellEvent.h"
#include "lock/TrialErrorEvent.h"
#include "lock/ConfigurationEvent.h"
#include "sensor/BatteryLowEvent.h"
#include "binary_sensor/DwellEvent.h"
#include "binary_sensor/TamperEvent.h"

namespace SmartLock {
namespace Events {

std::unique_ptr<BaseEvent> EventFactory::CreateEvent(const Entity& entity, EntityEventType event_type) 
{
    switch (event_type) {
        case EntityEventType::DOOR_BELL:
            return std::unique_ptr<BaseEvent>(new SmartLock::Events::Lock::DoorbellEvent(entity));

        case EntityEventType::TRIAL_AND_ERROR:
            return std::unique_ptr<BaseEvent>(new SmartLock::Events::Lock::TrialErrorEvent(entity));

        case EntityEventType::BATTERY_LOW:
            return std::unique_ptr<BaseEvent>(new SmartLock::Events::Sensor::BatteryLowEvent(entity));

        case EntityEventType::DWELL:
            return std::unique_ptr<BaseEvent>(new SmartLock::Events::BinarySensor::DwellEvent(entity));

        case EntityEventType::TAMPERED:
            return std::unique_ptr<BaseEvent>(new SmartLock::Events::BinarySensor::TamperEvent(entity));

        case EntityEventType::LOCK_CONFIGURATION:
            return std::unique_ptr<BaseEvent>(new SmartLock::Events::Lock::ConfigurationEvent(entity));

        default:
            return std::unique_ptr<BaseEvent>();
    }
}

EntityEventType EventFactory::DetectEventType(const Entity& entity) 
{
    switch (entity.domain) {
        case EntityDomain::LOCK:
            return DetectLockEvents(entity);
        case EntityDomain::SENSOR:
            return DetectSensorEvents(entity);
        case EntityDomain::BINARY_SENSOR:
            return DetectBinarySensorEvents(entity);
        case EntityDomain::CLIMATE:
            return DetectClimateEvents(entity);
        default:
            return EntityEventType::UNKNOWN;
    }
}

EntityEventType EventFactory::DetectLockEvents(const Entity& entity) 
{
    // 检查是否包含basic_configuration属性（锁配置更新）
    if (SmartLock::Events::Lock::ConfigurationEvent::IsEventDetected(entity)) {
        return EntityEventType::LOCK_CONFIGURATION;
    }

    // 检查是否包含trial_and_error属性
    if (SmartLock::Events::Lock::TrialErrorEvent::IsEventDetected(entity)) {
        return EntityEventType::TRIAL_AND_ERROR;
    }

    // 检查是否包含doorbell属性
    if (SmartLock::Events::Lock::DoorbellEvent::IsEventDetected(entity)) {
        return EntityEventType::DOOR_BELL;
    }

    return EntityEventType::UNKNOWN;
}

EntityEventType EventFactory::DetectSensorEvents(const Entity& entity) 
{
    if (SmartLock::Events::Sensor::BatteryLowEvent::IsEventDetected(entity)) {
        return EntityEventType::BATTERY_LOW;
    }
    return EntityEventType::UNKNOWN;
}

EntityEventType EventFactory::DetectBinarySensorEvents(const Entity& entity) 
{
    if (SmartLock::Events::BinarySensor::DwellEvent::IsEventDetected(entity)) {
        return EntityEventType::DWELL;
    }

    if (SmartLock::Events::BinarySensor::TamperEvent::IsEventDetected(entity)) {
        return EntityEventType::TAMPERED;
    }
    return EntityEventType::UNKNOWN;
}

EntityEventType EventFactory::DetectClimateEvents(const Entity& entity) 
{
    return EntityEventType::UNKNOWN;
}

} // namespace Events
} // namespace SmartLock
