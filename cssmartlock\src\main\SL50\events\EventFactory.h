#pragma once
#include "base/BaseEvent.h"
#include <memory>

namespace SmartLock {
namespace Events {

/**
 * 事件工厂
 * 负责创建各种具体的事件实例
 */
class EventFactory {
public:
    /**
     * 根据实体和事件类型创建对应的事件实例
     * @param entity 实体对象
     * @param event_type 事件类型
     * @return 事件实例，如果不支持则返回nullptr
     */
    static std::unique_ptr<BaseEvent> CreateEvent(const Entity& entity, EntityEventType event_type);
    
    /**
     * 检测实体的事件类型
     * @param entity 实体对象
     * @return 检测到的事件类型
     */
    static EntityEventType DetectEventType(const Entity& entity);
    
private:
    // 各个Domain的事件检测方法
    static EntityEventType DetectLockEvents(const Entity& entity);
    static EntityEventType DetectSensorEvents(const Entity& entity);
    static EntityEventType DetectBinarySensorEvents(const Entity& entity);
    static EntityEventType DetectClimateEvents(const Entity& entity);
};

} // namespace Events
} // namespace SmartLock
