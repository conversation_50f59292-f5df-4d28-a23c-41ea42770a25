#include "SmartLockEventService.h"
#include "../notify/NotificationService.h"

namespace SmartLock {
namespace Events {

SmartLockEventService& SmartLockEventService::GetInstance() {
    static SmartLockEventService instance;
    // 初始化服务
    static bool initialized = false;
    if (!initialized) {
        // 初始化通知服务
        Notify::NotificationService::GetInstance().Initialize();
        
        // 启动事件处理器
        SmartLock::Events::EventProcessor::GetInstance().Start(4); // 4个工作线程
        
        initialized = true;
    }
    return instance;
}

bool SmartLockEventService::ProcessEntityStateMessage(const std::vector<Entity>& entities) {
    AK_LOG_INFO << "SmartLockEventService::ProcessEntityStateMessage - Processing message";

    for (const auto& entity : entities) {
        ProcessEntityEvent(entity);
    }

    AK_LOG_INFO << "Processed " << entities.size() << " entities";
    return true;
}

bool SmartLockEventService::ProcessEntityEvent(const Entity& entity) {
    AK_LOG_INFO << "=== 开始处理实体事件 ===";
    AK_LOG_INFO << "Entity ID: " << entity.entity_id;
    AK_LOG_INFO << "Domain: " << Entity::DomainToString(entity.domain);

    // 检测事件类型
    EntityEventType event_type = SmartLock::Events::EventFactory::DetectEventType(entity);;
    AK_LOG_INFO << "检测到事件类型: " << Entity::EventTypeToString(event_type);

    // 处理事件
    if (event_type != EntityEventType::UNKNOWN) {
        return ProcessEvent(entity, event_type);
    }
    return true;
}

bool SmartLockEventService::ProcessEvent(const Entity& entity, EntityEventType event_type) {
    AK_LOG_INFO << "开始处理事件: " << Entity::EventTypeToString(event_type);

    // 记录事件详情
    LogEventDetails(entity, event_type);

    // 使用事件工厂创建事件实例
    auto event = SmartLock::Events::EventFactory::CreateEvent(entity, event_type);

    // 提交到事件处理器进行异步处理
    auto& processor = SmartLock::Events::EventProcessor::GetInstance();
    bool submitted = processor.SubmitEvent(std::move(event));

    if (submitted) {
        AK_LOG_INFO << "事件已提交到事件处理器: " << Entity::EventTypeToString(event_type);
    } else {
        AK_LOG_ERROR << "提交事件到事件处理器失败: " << Entity::EventTypeToString(event_type);
    }

    return submitted;
}

void SmartLockEventService::LogEventDetails(const Entity& entity, EntityEventType event_type) {
    AK_LOG_INFO << "=== 事件详情 ===";
    AK_LOG_INFO << "Entity ID: " << entity.entity_id;
    AK_LOG_INFO << "Device ID: " << entity.device_id;
    AK_LOG_INFO << "Domain: " << Entity::DomainToString(entity.domain);
    AK_LOG_INFO << "Event Type: " << Entity::EventTypeToString(event_type);
    AK_LOG_INFO << "Device Time: " << entity.device_time;
    AK_LOG_INFO << "Previous State: " << entity.previous_value.state;
    AK_LOG_INFO << "Current State: " << entity.current_value.state;
    AK_LOG_INFO << "================";
}


} // namespace Events
} // namespace SmartLock