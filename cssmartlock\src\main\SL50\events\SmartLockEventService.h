#pragma once
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include "AkLogging.h"
#include "../entities/Entity.h"
#include "EventFactory.h"
#include "base/EventProcessor.h"

namespace SmartLock {
namespace Events {

/**
 * SmartLock Event Service - 事件驱动架构
 * 新架构: JSON → Parse → Domain识别 → Event创建 → Event处理
 */
class SmartLockEventService {
public:
    static SmartLockEventService& GetInstance();

    // 主要处理入口
    bool ProcessEntityStateMessage(const std::vector<Entity>& entities);

    // 处理单个实体事件
    bool ProcessEntityEvent(const Entity& entity);

private:
    SmartLockEventService() = default;

    bool ProcessEvent(const Entity& entity, EntityEventType event_type);

    void LogEventDetails(const Entity& entity, EntityEventType event_type);

};

} // namespace Events
} // namespace SmartLock