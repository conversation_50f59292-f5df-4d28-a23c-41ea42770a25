#pragma once
#include "../../entities/Entity.h"
#include "AkLogging.h"

namespace SmartLock {
namespace Events {

/**
 * 事件抽象基类
 * 所有具体的事件类都应该继承此类
 */
class BaseEvent {
public:
    // 修复：使用值拷贝而不是引用，避免悬挂引用问题
    // 这确保了Entity对象在事件的整个生命周期内都是有效的
    BaseEvent(const Entity& entity) : entity_(entity) {}
    virtual ~BaseEvent() = default;
    
    /**
     * 处理事件的具体逻辑
     * 每个具体的事件类都必须实现此方法
     */
    virtual void Process() = 0;
    
    /**
     * 获取事件类型
     */
    virtual EntityEventType GetEventType() const = 0;
    
    /**
     * 获取关联的实体
     */
    const Entity& GetEntity() const { return entity_; }
    
protected:
    /**
     * 检查属性是否发生变化
     */
    bool HasAttributeChanged(const std::string& attr_name,  const std::string& from_val, const std::string& to_val) const {
        if (!entity_.previous_value.HasAttribute(attr_name) || !entity_.current_value.HasAttribute(attr_name)) {
            return false;
        }
        return entity_.previous_value.GetAttributeAsString(attr_name) == from_val && entity_.current_value.GetAttributeAsString(attr_name) == to_val;
    }
    
    /**
     * 检查状态是否发生变化
     */
    bool HasStateChanged(const std::string& from_state, const std::string& to_state) const {
        return entity_.previous_value.state == from_state && entity_.current_value.state == to_state;
    }
    
private:
    const Entity entity_;
};

} // namespace Events
} // namespace SmartLock
