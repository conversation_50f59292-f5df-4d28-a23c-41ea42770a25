#pragma once
#include <queue>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <chrono>
#include <vector>
#include "BaseEvent.h"
#include "../../entities/Entity.h"
#include "AkLogging.h"

namespace SmartLock {
namespace Events {

} // namespace Events
} // namespace SmartLock

namespace SmartLock {
namespace Events {

/**
 * 事件包装器 - 简化版本，只包含基本信息
 */
struct EventWrapper {
    std::unique_ptr<BaseEvent> event;
    std::chrono::steady_clock::time_point created_time;
    int retry_count = 0;

    EventWrapper(std::unique_ptr<BaseEvent> evt)
        : event(std::move(evt)) {
        created_time = std::chrono::steady_clock::now();
    }

    // 显式移动构造函数
    EventWrapper(EventWrapper&& other) noexcept
        : event(std::move(other.event))
        , created_time(other.created_time)
        , retry_count(other.retry_count) {
        other.retry_count = 0;
    }

    // 显式移动赋值操作符
    EventWrapper& operator=(EventWrapper&& other) noexcept {
        if (this != &other) {
            event = std::move(other.event);
            created_time = other.created_time;
            retry_count = other.retry_count;
            other.retry_count = 0;
        }
        return *this;
    }

    // 删除拷贝构造函数和拷贝赋值操作符
    EventWrapper(const EventWrapper&) = delete;
    EventWrapper& operator=(const EventWrapper&) = delete;

    // 检查事件是否有效
    bool IsValid() const {
        return event != nullptr;
    }

    // 安全地获取事件描述，避免空指针访问
    std::string GetEventDescriptionSafe() const {
        if (!IsValid()) {
            return "无效事件(空指针)";
        }
        return Entity::EventTypeToString(event->GetEventType());
    }
};

/**
 * 简单事件处理器 - 使用FIFO队列
 */
class EventProcessor {
private:
    // 简单的FIFO队列
    std::queue<EventWrapper> event_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;

    // 处理线程
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> running_{false};

    // 配置
    size_t worker_count_ = 4;

    // 统计信息
    std::atomic<size_t> processed_events_{0};
    std::atomic<size_t> failed_events_{0};

    // 重试队列
    std::queue<EventWrapper> retry_queue_;
    mutable std::mutex retry_mutex_;
    
public:
    static EventProcessor& GetInstance() {
        static EventProcessor instance;
        return instance;
    }
    
    /**
     * 启动事件处理器
     */
    void Start(size_t worker_count = 4) {
        if (running_.load()) {
            return;
        }
        
        worker_count_ = worker_count;
        running_.store(true);
        
        // 启动工作线程
        for (size_t i = 0; i < worker_count_; ++i) {
            worker_threads_.emplace_back([this, i]() {
                WorkerLoop(i);
            });
        }
        
        // 启动重试线程
        worker_threads_.emplace_back([this]() {
            RetryLoop();
        });
        
        AK_LOG_INFO << "事件处理器已启动，工作线程数: " << worker_count_;
    }
    
    /**
     * 停止事件处理器
     */
    void Stop() {
        if (!running_.load()) {
            return;
        }
        
        running_.store(false);
        queue_cv_.notify_all();
        
        for (auto& thread : worker_threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        worker_threads_.clear();
        
        AK_LOG_INFO << "事件处理器已停止";
    }
    
    /**
     * 提交事件进行处理
     */
    bool SubmitEvent(std::unique_ptr<BaseEvent> event) {
        if (!event) {
            AK_LOG_ERROR << "提交事件失败: event指针为空";
            return false;
        }

        // 记录事件提交信息
        std::string event_desc = Entity::EventTypeToString(event->GetEventType());
        AK_LOG_DEBUG << "提交事件: " << event_desc;

        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            // 直接加入FIFO队列
            event_queue_.emplace(std::move(event));

            // 验证刚创建的EventWrapper是否有效
            if (!event_queue_.back().IsValid()) {
                AK_LOG_ERROR << "严重错误: 刚创建的EventWrapper无效! 事件: " << event_desc;
            }
        }

        queue_cv_.notify_one();
        return true;
    }
    

    
    /**
     * 获取队列大小
     */
    size_t GetQueueSize() const {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        return event_queue_.size();
    }
private:
    EventProcessor() = default;

    ~EventProcessor() {
        Stop();
    }
    
    /**
     * 工作线程循环
     */
    void WorkerLoop(size_t worker_id) {
        AK_LOG_INFO << "事件处理工作线程 " << worker_id << " 已启动";
        
        while (running_.load()) {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            // 等待事件或停止信号
            queue_cv_.wait(lock, [this]() {
                return !event_queue_.empty() || !running_.load();
            });
            
            if (!running_.load()) {
                break;
            }
            
            if (event_queue_.empty()) {
                continue;
            }
            
            // 取出队列前端的事件 - FIFO方式
            EventWrapper wrapper = std::move(event_queue_.front());
            event_queue_.pop();
            lock.unlock();
            
            // 检查事件是否有效（防止空指针崩溃）
            if (!wrapper.IsValid()) {
                AK_LOG_ERROR << "工作线程 " << worker_id << " 发现无效事件: event指针为空";
                failed_events_.fetch_add(1);
                continue;
            }
            
            // 简化版本不检查过期，直接处理
            
            // 处理事件
            bool success = ProcessEvent(wrapper);
            
            if (success) {
                processed_events_.fetch_add(1);
                AK_LOG_DEBUG << "工作线程 " << worker_id << " 成功处理事件: " 
                            << wrapper.GetEventDescriptionSafe();
            } else {
                failed_events_.fetch_add(1);
                
                // 重试逻辑 - 只有有效事件才重试
                if (wrapper.IsValid() && wrapper.retry_count < GetMaxRetries()) {
                    wrapper.retry_count++;
                    
                    std::lock_guard<std::mutex> retry_lock(retry_mutex_);
                    retry_queue_.push(std::move(wrapper));
                    
                    AK_LOG_WARN << "工作线程 " << worker_id << " 事件处理失败，加入重试队列: " 
                               << wrapper.GetEventDescriptionSafe() 
                               << ", 重试次数: " << wrapper.retry_count;
                } else {
                    const std::string event_desc = wrapper.GetEventDescriptionSafe();
                    AK_LOG_ERROR << "工作线程 " << worker_id << " 事件处理最终失败: " << event_desc;
                }
            }
        }
        
        AK_LOG_INFO << "事件处理工作线程 " << worker_id << " 已退出";
    }
    
    /**
     * 重试线程循环
     */
    void RetryLoop() {
        AK_LOG_INFO << "事件重试线程已启动";
        
        while (running_.load()) {
            std::this_thread::sleep_for(std::chrono::seconds(5)); // 5秒检查一次
            
            std::queue<EventWrapper> pending_retries;
            
            {
                std::lock_guard<std::mutex> lock(retry_mutex_);
                pending_retries = std::move(retry_queue_);
                retry_queue_ = std::queue<EventWrapper>();
            }
            
            while (!pending_retries.empty() && running_.load()) {
                EventWrapper wrapper = std::move(pending_retries.front());
                pending_retries.pop();
                
                // 重新提交到主队列
                {
                    std::lock_guard<std::mutex> lock(queue_mutex_);
                    event_queue_.push(std::move(wrapper));
                }
                queue_cv_.notify_one();
            }
        }
        
        AK_LOG_INFO << "事件重试线程已退出";
    }
    
    /**
     * 处理单个事件
     */
    bool ProcessEvent(const EventWrapper& wrapper) {
        // 防止空指针崩溃：检查事件是否有效
        if (!wrapper.IsValid()) {
            AK_LOG_ERROR << "事件处理失败: 事件指针为空 (EventWrapper.event is nullptr)";
            return false;
        }
    
        auto start_time = std::chrono::steady_clock::now();
        
        wrapper.event->Process();
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        AK_LOG_DEBUG << "事件处理完成: " << wrapper.GetEventDescriptionSafe() 
                    << ", 耗时: " << duration.count() << "ms";
        
        return true;
    }
    
    /**
     * 获取最大重试次数 - 简化为固定值
     */
    int GetMaxRetries() {
        return 3;  // 统一的重试次数
    }
};

} // namespace Events
} // namespace SmartLock