#pragma once
#include "BaseEvent.h"

namespace SmartLock {
namespace Events {

/**
 * 状态变化事件基类
 * 为常见的状态变化检测提供通用方法
 * 大部分智能锁事件都是基于状态变化触发的
 */
class StateChangeEventBase : public BaseEvent {
public:
    StateChangeEventBase(const Entity& entity) : BaseEvent(entity) {}
    
protected:
    /**
     * 检查Domain类型是否匹配
     * @param domain 期望的Domain类型
     * @return 是否匹配
     */
    bool CheckDomain(EntityDomain domain) const {
        return GetEntity().domain == domain;
    }
    
    /**
     * 检查属性是否从off变为on
     * 这是最常见的状态变化模式
     * @param attr_name 属性名称
     * @return 是否发生off→on变化
     */
    bool CheckOffToOnChange(const std::string& attr_name) const {
        return HasAttributeChanged(attr_name, "off", "on");
    }
    
    /**
     * 检查属性是否从on变为off  
     * @param attr_name 属性名称
     * @return 是否发生on→off变化
     */
    bool CheckOnToOffChange(const std::string& attr_name) const {
        return HasAttributeChanged(attr_name, "on", "off");
    }
    
    /**
     * 检查实体状态是否从某个值变为另一个值
     * @param from_state 原状态
     * @param to_state 目标状态
     * @return 是否发生变化
     */
    bool CheckEntityStateChange(const std::string& from_state, const std::string& to_state) const {
        return HasStateChanged(from_state, to_state);
    }
    
    /**
     * 检查EntityID是否包含特定关键词
     * 用于防拆等特殊事件的检测
     * @param keyword 关键词
     * @return 是否包含关键词
     */
    bool CheckEntityIdContains(const std::string& keyword) const {
        const std::string& entity_id = GetEntity().entity_id;
        return entity_id.find(keyword) != std::string::npos;
    }
    
    /**
     * 检查属性是否存在
     * @param attr_name 属性名称
     * @return 当前值和之前值都存在该属性
     */
    bool CheckAttributeExists(const std::string& attr_name) const {
        return GetEntity().previous_value.HasAttribute(attr_name) && 
               GetEntity().current_value.HasAttribute(attr_name);
    }
    
    /**
     * 通用的Domain+属性检查
     * 最常见的检测模式：检查Domain类型 + 属性存在性
     * @param domain 期望的Domain类型
     * @param attr_name 属性名称
     * @return 是否满足基础检查条件
     */
    bool CheckBasicConditions(EntityDomain domain, const std::string& attr_name) const {
        return CheckDomain(domain) && CheckAttributeExists(attr_name);
    }
    
    /**
     * 检测状态变化 (off→on)
     * Domain检查 + 属性存在性检查 + off→on变化检查
     * @param domain 期望的Domain类型
     * @param attr_name 属性名称
     * @return 是否检测到状态变化
     */
    bool DetectStateChange(EntityDomain domain, const std::string& attr_name) const {
        return CheckBasicConditions(domain, attr_name) && CheckOffToOnChange(attr_name);
    }
    
    /**
     * 记录事件日志
     */
    void LogEvent(const std::string& message) const {
        AK_LOG_INFO << "[" << GetEntity().entity_id << "] " << message;
    }

    /**
     * 记录错误日志
     */
    void LogError(const std::string& message) const {
        AK_LOG_ERROR << "[" << GetEntity().entity_id << "] " << message;
    }
};

} // namespace Events
} // namespace SmartLock
