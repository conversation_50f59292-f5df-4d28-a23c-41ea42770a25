#include "DwellEvent.h"
#include "AkLogging.h"
#include "../../notify/NotificationService.h"

namespace SmartLock {
namespace Events {
namespace BinarySensor {

bool DwellEvent::IsEventDetected(const Entity& entity) {
    AK_LOG_INFO << "DwellEvent::isEventDetected - 开始检查实体: " << entity.entity_id;

    // 使用基类的状态变化检测方法：Domain检查 + 属性存在性检查 + off→on变化检查
    DwellEvent dwell_event(entity);
    bool result = dwell_event.DetectStateChange(EntityDomain::BINARY_SENSOR, "dwell");

    AK_LOG_INFO << "DwellEvent::isEventDetected - 结果: " << (result ? "true" : "false");

    // 如果检测成功，记录详细信息
    if (result) {
        std::string prev_dwell = entity.previous_value.GetAttributeAsString("dwell");
        std::string curr_dwell = entity.current_value.GetAttributeAsString("dwell");
        AK_LOG_INFO << "DwellEvent::isEventDetected - 检测到逗留告警: " << prev_dwell << " → " << curr_dwell;
    }
    return result;
}

void DwellEvent::Process()
{
    AK_LOG_INFO << "DwellEvent::process - 开始处理驻留告警事件";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "DwellEvent::process - 实体ID: " << entity.entity_id << ", 设备ID: " << entity.device_id;

    // 使用通知服务发送驻留告警通知
    Notify::NotificationService& notificationService = Notify::NotificationService::GetInstance();
    notificationService.SendDwellNotification(entity);

    AK_LOG_INFO << "DwellEvent::process - 驻留告警事件处理完成";
    return;
}

} // namespace BinarySensor
} // namespace Events
} // namespace SmartLock