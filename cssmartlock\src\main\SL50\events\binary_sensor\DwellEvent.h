#pragma once
#include "../base/StateChangeEventBase.h"
#include "../../notify/NotificationService.h"

namespace SmartLock {
namespace Events {
namespace BinarySensor {

/**
 * 驻留事件
 * 当检测到有人在门前长时间停留时触发
 */
class DwellEvent : public SmartLock::Events::StateChangeEventBase {
public:
    DwellEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::DWELL; }
    
    /**
     * 检测是否为驻留事件
     */
    static bool IsEventDetected(const Entity& entity);

};

} // namespace BinarySensor
} // namespace Events
} // namespace SmartLock