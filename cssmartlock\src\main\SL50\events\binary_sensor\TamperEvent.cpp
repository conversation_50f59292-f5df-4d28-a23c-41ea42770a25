#include "TamperEvent.h"
#include "AkLogging.h"

namespace SmartLock {
namespace Events {
namespace BinarySensor {

bool TamperEvent::IsEventDetected(const Entity& entity) {
    AK_LOG_INFO << "TamperEvent::isEventDetected - 开始检查实体: " << entity.entity_id;

    // 检查是否为防拆传感器 (entity_id 以 "binary_sensor.t" 开头)
    if (entity.entity_id.find("binary_sensor.t") != 0) {
        AK_LOG_INFO << "TamperEvent::isEventDetected - 不是防拆传感器, entity_id: " << entity.entity_id;
        return false;
    }

    // 使用基类的方法检查Domain和状态变化：Domain检查 + off→on状态变化检查
    TamperEvent tamper_event(entity);
    bool result = tamper_event.CheckEntityStateChange("off", "on");
    AK_LOG_INFO << "TamperEvent::isEventDetected - 结果: " << (result ? "true" : "false");

    // 如果检测成功，记录详细信息
    if (result) {
        std::string prev_state = entity.previous_value.state;
        std::string curr_state = entity.current_value.state;
        AK_LOG_INFO << "TamperEvent::isEventDetected - 检测到防拆告警: " << prev_state << " → " << curr_state;
    }
    return result;
}

void TamperEvent::Process()
{
    AK_LOG_INFO << "TamperEvent::process - 开始处理防拆告警事件";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "TamperEvent::process - 实体ID: " << entity.entity_id << ", 设备ID: " << entity.device_id;

    // 使用通知服务发送防拆告警通知
    Notify::NotificationService& notificationService = Notify::NotificationService::GetInstance();
    notificationService.SendTamperNotification(entity);

    AK_LOG_INFO << "TamperEvent::process - 防拆告警事件处理完成";
    return;
}

} // namespace BinarySensor
} // namespace Events
} // namespace SmartLock