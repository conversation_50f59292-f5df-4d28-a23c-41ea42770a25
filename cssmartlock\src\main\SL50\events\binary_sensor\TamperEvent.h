#pragma once
#include "../base/StateChangeEventBase.h"
#include "../../notify/NotificationService.h"

namespace SmartLock {
namespace Events {
namespace BinarySensor {

/**
 * 防拆事件
 * 当检测到设备被恶意拆除或破坏时触发
 */
class TamperEvent : public SmartLock::Events::StateChangeEventBase {
public:
    TamperEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::TAMPERED; }
    
    /**
     * 检测是否为防拆事件
     */
    static bool IsEventDetected(const Entity& entity);

};

} // namespace BinarySensor
} // namespace Events
} // namespace SmartLock