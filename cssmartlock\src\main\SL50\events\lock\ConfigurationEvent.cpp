#include "ConfigurationEvent.h"
#include "AkLogging.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/SL50Lock.h"
#include <json/json.h>
#include <cstring>
#include "util.h"
#include "util_string.h"

namespace SmartLock {
namespace Events {
namespace Lock {

bool ConfigurationEvent::IsEventDetected(const Entity& entity) 
{
    AK_LOG_INFO << "LockConfigurationEvent::IsEventDetected - 开始检查实体: " << entity.entity_id;

    ConfigurationEvent configuration_event(entity);
    bool result = configuration_event.CheckAttributeExists("basic_configuration");
    AK_LOG_INFO << "LockConfigurationEvent::IsEventDetected - 结果: " << (result ? "true" : "false");

    return result;
}

void ConfigurationEvent::Process() 
{
    AK_LOG_INFO << "LockConfigurationEvent::Process - 开始处理锁配置更新事件";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "LockConfigurationEvent::Process - 实体信息: " << entity.entity_id << ", 设备: " << entity.device_id;

    LockConfiguration lock_config;
    if (!ParseConfiguration(entity, lock_config)) {
        AK_LOG_ERROR << "LockConfigurationEvent::UpdateSmartLockTable - 解析配置失败";
        return;
    }

    // 更新SmartLock表
    UpdateSmartLockTable(lock_config);
  
    // 更新SL50Lock表
    UpdateSL50LockTable(lock_config);

    AK_LOG_INFO << "LockConfigurationEvent::Process - 锁配置更新成功";
    return;
}

void ConfigurationEvent::UpdateSmartLockTable(const LockConfiguration& lock_config)
{
    const Entity& entity = GetEntity();
    AK_LOG_INFO << "ConfigurationEvent::UpdateSmartLockTable - 开始更新SmartLock表";

    SmartLockInfo smartlock_info;
    smartlock_info.wifi_status = !lock_config.wifi_ssid.empty() ? 1 : 0;
    Snprintf(smartlock_info.lock_body_version, sizeof(smartlock_info.lock_body_version), lock_config.lock_body_version.c_str());
    Snprintf(smartlock_info.last_connected_time, sizeof(smartlock_info.last_connected_time), entity.device_time.c_str());

    if (0 != dbinterface::SmartLock::UpdateSmartLockInfoByUUID(entity.device_id, smartlock_info)) {
        AK_LOG_ERROR << "ConfigurationEvent::UpdateSmartLockTable - 更新SmartLock信息失败";
        return;
    }
    AK_LOG_INFO << "ConfigurationEvent::UpdateSmartLockTable - SmartLock表更新成功";
}

// WifiName, LastConnectedTime, Language, Volume, StayAlarm, StayAlarmTime, MonitoringScope, EnableTwoFactoryAuth
void ConfigurationEvent::UpdateSL50LockTable(const LockConfiguration& lock_config)
{    
    const Entity& entity = GetEntity();
    AK_LOG_INFO << "ConfigurationEvent::UpdateSL50LockTable - 开始更新SL50Lock表";

    SL50LockInfo sl50_lock_info;
    sl50_lock_info.stay_alarm_time = lock_config.dwell_time;
    sl50_lock_info.stay_alarm = lock_config.dwell_alarm ? 1 : 0;
    sl50_lock_info.enable_two_factory_auth = lock_config.double_verification ? 1 : 0;
    Snprintf(sl50_lock_info.volume, sizeof(sl50_lock_info.volume), lock_config.volume.c_str());
    Snprintf(sl50_lock_info.language, sizeof(sl50_lock_info.language), lock_config.language.c_str());
    Snprintf(sl50_lock_info.wifi_name, sizeof(sl50_lock_info.wifi_name), lock_config.wifi_ssid.c_str());
    Snprintf(sl50_lock_info.monitoring_scope, sizeof(sl50_lock_info.monitoring_scope), lock_config.monitoring_scope.c_str());
    Snprintf(sl50_lock_info.last_connected_time, sizeof(sl50_lock_info.last_connected_time), entity.device_time.c_str());

    if (0 != dbinterface::SL50Lock::UpdateSL50LockInfoBySmartLockUUID(entity.device_id, sl50_lock_info)) {
        AK_LOG_ERROR << "ConfigurationEvent::UpdateSL50LockTable - 更新SL50Lock信息失败";
        return;
    }
    AK_LOG_INFO << "ConfigurationEvent::UpdateSL50LockTable - SL50Lock表更新成功";
}

bool ConfigurationEvent::ParseConfiguration(const Entity& entity, LockConfiguration& config)
{
    AK_LOG_INFO << "LockConfigurationEvent::ParseConfiguration - 开始解析配置";

    // 获取basic_configuration属性
    Json::Value basic_config;
    if (!entity.current_value.attributes) {
        AK_LOG_ERROR << "ConfigurationEvent::ParseConfiguration - 没有属性信息";
        return false;
    }

    Json::Value attr_json;
    entity.current_value.attributes->toJson(attr_json);

    if (!attr_json.isMember("basic_configuration")) {
        AK_LOG_ERROR << "ConfigurationEvent::ParseConfiguration - 没有basic_configuration属性";
        return false;
    }

    basic_config = attr_json["basic_configuration"];

    // 解析各个字段
    config.last_connected_time = entity.device_time;
    config.language = basic_config.get("language", "en").asString();
    config.volume = basic_config.get("volume", "high").asString();
    config.dwell_alarm = basic_config.get("dwell_alarm", true).asBool();
    config.dwell_time = basic_config.get("dwell_time", 20).asInt();
    config.monitoring_scope = basic_config.get("monitoring_scope", "large").asString();
    config.double_verification = basic_config.get("double_verification", false).asBool();
    config.wifi_ssid = basic_config.get("wifi_ssid", "").asString();
    config.wifi_ip = basic_config.get("wifi_ip", "").asString();
    config.wifi_gateway = basic_config.get("wifi_gateway", "").asString();
    config.wifi_rssi = basic_config.get("wifi_rssi", 0).asInt();
    config.lock_body_version = basic_config.get("lock_body_version", "").asString();

    AK_LOG_INFO << "LockConfigurationEvent::ParseConfiguration - 配置解析成功";
    AK_LOG_INFO << "Language: " << config.language << ", Volume: " << config.volume
                << ", DwellAlarm: " << config.dwell_alarm << ", DwellTime: " << config.dwell_time;
    AK_LOG_INFO << "WiFi SSID: " << config.wifi_ssid << ", LockBodyVersion: " << config.lock_body_version << ", LastConnectedTime: " << config.last_connected_time;

    return true;
}

} // namespace Lock
} // namespace Events
} // namespace SmartLock
