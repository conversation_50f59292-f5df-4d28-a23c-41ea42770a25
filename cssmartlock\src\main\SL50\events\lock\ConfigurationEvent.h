#pragma once
#include "../base/StateChangeEventBase.h"

namespace SmartLock {
namespace Events {
namespace Lock {

struct LockConfiguration {
    std::string language;
    std::string volume;
    bool dwell_alarm;
    int dwell_time;
    std::string monitoring_scope;
    bool double_verification;
    std::string wifi_ssid;
    std::string wifi_ip;
    std::string wifi_gateway;
    int wifi_rssi;
    std::string lock_body_version;
    std::string last_connected_time;
};

/**
 * 锁配置更新事件
 * 处理锁的基础配置信息上报，更新数据库中的相关字段
 */
class ConfigurationEvent : public SmartLock::Events::StateChangeEventBase {
public:
    ConfigurationEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::LOCK_CONFIGURATION; }
    
    /**
     * 检测是否为锁配置更新事件
     */
    static bool IsEventDetected(const Entity& entity);

private:
    /**
     * 更新SmartLock表的字段
     */
    void UpdateSmartLockTable(const LockConfiguration& lock_config);
    
    /**
     * 更新SL50Lock表的字段
     */
    void UpdateSL50LockTable(const LockConfiguration& lock_config);
    
    /**
     * 解析配置信息
     */
    bool ParseConfiguration(const Entity& entity, LockConfiguration& config);
};

} // namespace Lock
} // namespace Events
} // namespace SmartLock
