#include "DoorbellEvent.h"

namespace SmartLock {
namespace Events {
namespace Lock {

bool DoorbellEvent::IsEventDetected(const Entity& entity) 
{
    // 使用基类的状态变化检测方法：Domain检查 + 属性存在性检查 + off→on变化检查
    DoorbellEvent temp_event(entity);
    return temp_event.DetectStateChange(EntityDomain::LOC<PERSON>, "doorbell");
}

void DoorbellEvent::Process() 
{
    LogEvent("开始处理门铃事件");

     const Entity& entity = GetEntity();
        
    // 使用通知服务发送门铃通知
    Notify::NotificationService& notificationService = Notify::NotificationService::GetInstance();
    notificationService.SendDoorbellNotification(entity);
    
    LogEvent("门铃事件处理完成");
}

} // namespace Lock
} // namespace Events
} // namespace SmartLock