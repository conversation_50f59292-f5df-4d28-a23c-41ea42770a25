#pragma once
#include "../base/StateChangeEventBase.h"
#include "../../notify/NotificationService.h"

namespace SmartLock {
namespace Events {
namespace Lock {

/**
 * 门铃事件
 * 当门铃从"off"状态变为"on"状态时触发
 */
class DoorbellEvent : public SmartLock::Events::StateChangeEventBase {
public:
    DoorbellEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::DOOR_BELL; }
    
    /**
     * 检测是否为门铃事件
     */
    static bool IsEventDetected(const Entity& entity);
    
private:
    void SendDoorbellNotification();
};

} // namespace Lock
} // namespace Events
} // namespace SmartLock