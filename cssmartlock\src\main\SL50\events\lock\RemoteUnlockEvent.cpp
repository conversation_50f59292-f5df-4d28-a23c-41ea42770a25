#include "AkLogging.h"
#include "SafeCacheConn.h"
#include "RemoteUnlockEvent.h"

#include "../../entities/LockAttributes.h"

namespace SmartLock {
namespace Events {
namespace Lock {


bool RemoteUnlockEvent::IsEventDetected(const Entity& entity)
{
    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 开始检查实体: " << entity.entity_id;

    RemoteUnlockEvent remote_unlock_event(entity);

    // 检查是否为开门事件
    if (!remote_unlock_event.CheckAttributeExists("unlock_mode")) {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 没有unlock_mode属性";
        return false;
    }

    // 检查是否开门了
    if (!remote_unlock_event.CheckEntityStateChange("locked", "unlocked")) {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 状态变化不匹配: "
                    << entity.previous_value.state << " -> " << entity.current_value.state;
        return false;
    }

    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 检测到远程解锁事件";
    return true;
}

void RemoteUnlockEvent::Process() 
{
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 开始处理远程解锁事件";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 实体信息: " << entity.entity_id << ", 设备: " << entity.device_id;

    SafeCacheConn cache_conn(g_redis_db_smart_lock);
    if (!cache_conn.isConnect()) {
        AK_LOG_ERROR << "RemoteUnlockEvent::Process - 连接Redis失败";
        return;
    }

    std::string unlock_key = GetRemoteUnlockKey(entity.entity_id);
    std::string initiator = cache_conn.get(unlock_key);

    AK_LOG_INFO << "RemoteUnlockEvent::Process - 解锁者: " << initiator << ", 设备: " << entity.device_id;

    AK_LOG_INFO << "RemoteUnlockEvent::Process - 远程解锁事件处理完成";
}

std::string RemoteUnlockEvent::GetRemoteUnlockKey(const std::string& entity_id)
{
    return std::string("remote_") + entity_id;
}


} // namespace Lock
} // namespace Events
} // namespace SmartLock
