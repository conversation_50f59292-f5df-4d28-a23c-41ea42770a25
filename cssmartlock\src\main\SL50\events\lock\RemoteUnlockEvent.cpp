#include "AkLogging.h"
#include "AK.Server.pb.h"
#include "AK.BackendCommon.pb.h"
#include "SafeCacheConn.h"
#include "RemoteUnlockEvent.h"
#include "entities/LockAttributes.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

namespace SmartLock {
namespace Events {
namespace Lock {


bool RemoteUnlockEvent::IsEventDetected(const Entity& entity)
{
    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 开始检查实体: " << entity.entity_id;

    RemoteUnlockEvent remote_unlock_event(entity);

    // 检查是否为开门事件
    if (!remote_unlock_event.CheckAttributeExists("unlock_mode")) {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 没有unlock_mode属性";
        return false;
    }

    // 获取远程开锁结果
    GetRemoteUnlockResult(remote_unlock_event, entity);
    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 检测到远程解锁事件";
    return true;
}

void RemoteUnlockEvent::Process() 
{
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 开始处理远程解锁事件";

    // 异步通知app开门结果
    SendRemoteUnlockResponse();

    AK_LOG_INFO << "RemoteUnlockEvent::Process - 远程解锁事件处理完成";
}

std::string RemoteUnlockEvent::GetRemoteUnlockKey(const std::string& entity_id)
{
    return std::string("remote_") + entity_id;
}

void RemoteUnlockEvent::GetRemoteUnlockResult(const RemoteUnlockEvent& remote_unlock_event, const Entity& entity) 
{
    if (!remote_unlock_event.CheckEntityStateChange("locked", "unlocked")) {
        AK_LOG_INFO << "RemoteUnlockEvent::GetRemoteUnlockResult - 开锁失败, 状态变化: "
                    << entity.previous_value.state << " -> " << entity.current_value.state;
        unlock_success_ = 0;
    }
    unlock_success_ = 1;
    return;
}

void RemoteUnlockEvent::SendRemoteUnlockResponse()
{
    AK_LOG_INFO << "RemoteUnlockEvent::SendRemoteUnlockResponse - 开始开锁异步通知回复发送app";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 实体信息: " << entity.entity_id << ", 设备: " << entity.device_id;

    SafeCacheConn cache_conn(g_redis_db_smart_lock);
    if (!cache_conn.isConnect()) {
        AK_LOG_INFO << "RemoteUnlockEvent::Process - 连接Redis失败";
        return;
    }

    std::map<string, string> remote_unlock_info;
    std::string unlock_key = GetRemoteUnlockKey(entity.device_id);
    if (!cache_conn.hgetAll(unlock_key, remote_unlock_info)) {
        AK_LOG_INFO << "RemoteUnlockEvent::Process - 获取解锁信息失败, unlock_key: " << unlock_key;
        return;
    }

    // 判断initiator和trace id的值
    if (strlen(remote_unlock_info["initiator"]) == 0 || strlen(remote_unlock_info["trace_id"]) == 0) {
        AK_LOG_INFO << "RemoteUnlockEvent::Process - 解锁者或TraceId为空, 不发送开锁异步通知回复, initiator: " 
                    << remote_unlock_info["initiator"] << ", TraceId: " << remote_unlock_info["trace_id"];
        return;
    }

    AK::Server::P2PMainResponseOpenDoorMsg p2p_msg;
    p2p_msg.set_msg_traceid(remote_unlock_info["trace_id"]);
    p2p_msg.set_mac_or_uid(remote_unlock_info["initiator"]);
    p2p_msg.set_result(unlock_success_);
    p2p_msg.set_response_type((int)RemoteControlAckResponseType::RESPONSE_TYPE_APP);

    int project_type = project::RESIDENCE;
    AK::BackendCommon::BackendP2PBaseMessage base_msg;
    base_msg = CSmartLock2RouteMsg::CreateP2PBaseMsg(AKCS_M2R_P2P_OPEN_DOOR_ACK, TransP2PMsgType::TO_APP_UID_ONLINE, remote_unlock_info["initiator"],
                        CSmartLock2RouteMsg::DevProjectTypeToDevType(project_type), project_type);

    base_msg.mutable_p2pmainresponseopendoormsg2()->CopyFrom(p2p_msg);
    CSmartLock2RouteMsg::PushMsg2Route(&base_msg);

    AK_LOG_INFO << "RemoteUnlockEvent::SendRemoteUnlockResponse - 开锁异步通知回复发送完成, 解锁者: " << remote_unlock_info["initiator"] << ", TraceId: " << remote_unlock_info["trace_id"];
    return;
}


} // namespace Lock
} // namespace Events
} // namespace SmartLock
