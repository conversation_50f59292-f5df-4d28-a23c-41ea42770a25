#include "RemoteUnlockEvent.h"
#include "AkLogging.h"
#include "../../entities/LockAttributes.h"
#include "Resid2RouteMsg.h"

namespace SmartLock {
namespace Events {
namespace Lock {

    /*
     AK_LOG_INFO << "TamperEvent::isEventDetected - 开始检查实体: " << entity.entity_id;

    // 检查是否为防拆传感器 (entity_id 以 "binary_sensor.t" 开头)
    if (entity.entity_id.find("binary_sensor.t") != 0) {
        AK_LOG_INFO << "TamperEvent::isEventDetected - 不是防拆传感器, entity_id: " << entity.entity_id;
        return false;
    }

    // 使用基类的方法检查Domain和状态变化：Domain检查 + off→on状态变化检查
    TamperEvent tamper_event(entity);
    bool result = tamper_event.CheckEntityStateChange("off", "on");
    AK_LOG_INFO << "TamperEvent::isEventDetected - 结果: " << (result ? "true" : "false");

    // 如果检测成功，记录详细信息
    if (result) {
        std::string prev_state = entity.previous_value.state;
        std::string curr_state = entity.current_value.state;
        AK_LOG_INFO << "TamperEvent::isEventDetected - 检测到防拆告警: " << prev_state << " → " << curr_state;
    }
    return result;
    */
bool RemoteUnlockEvent::IsEventDetected(const Entity& entity) 
{
    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 开始检查实体: " << entity.entity_id;

    RemoteUnlockEvent remote_unlock_event(entity);

    bool is_remote_unlock = remote_unlock_event.CheckAttributeExists("unlock_mode") && 
                            remote_unlock_event.CheckAttributeExists("unlock_mode") == "remote";


    bool state_change = remote_unlock_event.CheckEntityStateChange("locked", "unlocked");


    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 结果: " << (result ? "true" : "false");

    return result;


    // 检查状态是否从locked变为unlocked
    if (entity.previous_value.state != "locked" || entity.current_value.state != "unlocked") {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 状态变化不匹配: " 
                    << entity.previous_value.state << " -> " << entity.current_value.state;
        return false;
    }

    // 检查unlock_mode是否为remote
    if (!entity.current_value.HasAttribute("unlock_mode")) {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 没有unlock_mode属性";
        return false;
    }

    std::string unlock_mode = entity.current_value.GetAttributeAsString("unlock_mode");
    if (unlock_mode != "remote") {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - unlock_mode不是remote: " << unlock_mode;
        return false;
    }

    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 检测到远程解锁事件";
    return true;
}

void RemoteUnlockEvent::Process() 
{
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 开始处理远程解锁事件";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 实体信息: " << entity.entity_id << ", 设备: " << entity.device_id;

    // 发送远程解锁通知
    SendRemoteUnlockNotification();

    AK_LOG_INFO << "RemoteUnlockEvent::Process - 远程解锁事件处理完成";
}

void RemoteUnlockEvent::SendRemoteUnlockNotification()
{
    const Entity& entity = GetEntity();
    
    AK_LOG_INFO << "RemoteUnlockEvent::SendRemoteUnlockNotification - 发送远程解锁通知";
    AK_LOG_INFO << "设备ID: " << entity.device_id;
    AK_LOG_INFO << "解锁时间: " << entity.device_time;
    
    // 获取credential信息
    std::string credential_type = entity.current_value.GetAttributeAsString("credential_type");
    std::string credential_id = entity.current_value.GetAttributeAsString("credential_id");
    
    AK_LOG_INFO << "凭证类型: " << credential_type << ", 凭证ID: " << credential_id;

    // 这里可以调用相应的通知接口
    // 例如：发送到路由消息、记录日志、更新数据库等
    // Resid2RouteMsg::SendRemoteUnlockNotification(entity.device_id, entity.device_time, credential_type, credential_id);
    
    AK_LOG_INFO << "RemoteUnlockEvent::SendRemoteUnlockNotification - 通知发送完成";
}

} // namespace Lock
} // namespace Events
} // namespace SmartLock
