#include "AkLogging.h"
#include "SafeCacheConn.h"
#include "RemoteUnlockEvent.h"
#include "Resid2RouteMsg.h"

#include "../../entities/LockAttributes.h"

namespace SmartLock {
namespace Events {
namespace Lock {


bool RemoteUnlockEvent::IsEventDetected(const Entity& entity)
{
    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 开始检查实体: " << entity.entity_id;

    RemoteUnlockEvent remote_unlock_event(entity);

    // 检查是否为开门事件
    if (!remote_unlock_event.CheckAttributeExists("unlock_mode")) {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 没有unlock_mode属性";
        return false;
    }

    // 检查是否开门了
    if (!remote_unlock_event.CheckEntityStateChange("locked", "unlocked")) {
        AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 状态变化不匹配: "
                    << entity.previous_value.state << " -> " << entity.current_value.state;
        return false;
    }

    AK_LOG_INFO << "RemoteUnlockEvent::IsEventDetected - 检测到远程解锁事件";
    return true;
}

void RemoteUnlockEvent::Process() 
{
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 开始处理远程解锁事件";

    const Entity& entity = GetEntity();
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 实体信息: " << entity.entity_id << ", 设备: " << entity.device_id;

    SafeCacheConn cache_conn(g_redis_db_smart_lock);
    if (!cache_conn.isConnect()) {
        AK_LOG_ERROR << "RemoteUnlockEvent::Process - 连接Redis失败";
        return;
    }

    std::string unlock_key = GetRemoteUnlockKey(entity.device_id);
    std::string initiator = cache_conn.get(unlock_key);

    // 发送远程解锁通知到 csroute
    SendRemoteUnlockNotification(entity, initiator);

    AK_LOG_INFO << "RemoteUnlockEvent::Process - 解锁者: " << initiator << ", unlock_key: " << unlock_key;
    AK_LOG_INFO << "RemoteUnlockEvent::Process - 远程解锁事件处理完成";
}

std::string RemoteUnlockEvent::GetRemoteUnlockKey(const std::string& entity_id)
{
    return std::string("remote_") + entity_id;
}

void RemoteUnlockEvent::SendRemoteUnlockNotification(const Entity& entity, const std::string& initiator)
{
    AK_LOG_INFO << "RemoteUnlockEvent::SendRemoteUnlockNotification - 开始发送通知";

    // 构建通知消息
    SOCKET_MSG_ALARM_SEND alarm_msg;
    memset(&alarm_msg, 0, sizeof(alarm_msg));

    // 设置基本信息
    strncpy(alarm_msg.mac, entity.device_id.c_str(), sizeof(alarm_msg.mac) - 1);
    strncpy(alarm_msg.type, "remote_unlock", sizeof(alarm_msg.type) - 1);
    alarm_msg.alarm_code = static_cast<int>(ALARM_CODE::DOOR_UNLOCK);  // 使用门解锁告警类型
    alarm_msg.trace_id = 0;  // 可以设置为当前时间戳或其他唯一ID

    // 设置解锁相关信息
    std::string credential_type = entity.current_value.GetAttributeAsString("credential_type");
    std::string credential_id = entity.current_value.GetAttributeAsString("credential_id");

    // 设置社区和地址信息
    strncpy(alarm_msg.community, "SmartLock", sizeof(alarm_msg.community) - 1);
    strncpy(alarm_msg.address, entity.device_id.c_str(), sizeof(alarm_msg.address) - 1);
    strncpy(alarm_msg.from_local, "Remote", sizeof(alarm_msg.from_local) - 1);

    // 设置时间
    time_t now = time(nullptr);
    struct tm* tm_info = localtime(&now);
    strftime(alarm_msg.time, sizeof(alarm_msg.time), "%Y-%m-%d %H:%M:%S", tm_info);

    // 设置标题，包含解锁信息
    snprintf(alarm_msg.title, sizeof(alarm_msg.title),
             "Remote unlock: initiator=%s, credential_type=%s, credential_id=%s",
             initiator.c_str(), credential_type.c_str(), credential_id.c_str());

    // 发送到 csroute
    Resid2RouteMsg::SendP2PAlarmNotifyMsg(
        project::RESIDENCE,
        TransP2PMsgType::TO_APP_UID,  // 发送给APP用户
        csmain::DeviceType::DEVICE_TYPE_SMART_LOCK,
        entity.device_id,
        alarm_msg
    );

    AK_LOG_INFO << "RemoteUnlockEvent::SendRemoteUnlockNotification - 通知发送完成, 设备: "
                << entity.device_id << ", 解锁者: " << initiator;
}

} // namespace Lock
} // namespace Events
} // namespace SmartLock
