#pragma once
#include "../base/StateChangeEventBase.h"

namespace SmartLock {
namespace Events {
namespace Lock {

/**
 * 远程解锁事件
 * 处理通过远程方式解锁的事件，发送通知消息
 */
class RemoteUnlockEvent : public SmartLock::Events::StateChangeEventBase {
public:
    RemoteUnlockEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::REMOTE_UNLOCK; }
    
    /**
     * 检测是否为远程解锁事件
     * 条件：
     * 1. Domain为lock
     * 2. 状态从locked变为unlocked
     * 3. unlock_mode为remote
     */
    static bool IsEventDetected(const Entity& entity);

private:
    /**
     * 发送远程解锁通知消息
     */
    void SendRemoteUnlockNotification();
};

} // namespace Lock
} // namespace Events
} // namespace SmartLock
