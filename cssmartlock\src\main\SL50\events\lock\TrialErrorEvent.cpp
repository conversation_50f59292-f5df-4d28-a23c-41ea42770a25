#include "TrialErrorEvent.h"
#include "../../notify/NotificationService.h"

namespace SmartLock {
namespace Events {
namespace Lock {

bool TrialErrorEvent::IsEventDetected(const Entity& entity) {
    // 使用基类的状态变化检测方法：Domain检查 + 属性存在性检查 + off→on变化检查
    TrialErrorEvent temp_event(entity);
    return temp_event.DetectStateChange(EntityDomain::LOCK, "trial_and_error");
}

void TrialErrorEvent::Process() 
{
    LogEvent("开始处理试错告警事件");
    
    const Entity& entity = GetEntity();
    
    // 使用通知服务发送试错告警通知
    auto& notification_service = Notify::NotificationService::GetInstance();
    
    if (!notification_service.SendTrialErrorNotification(entity)) {
        LogEvent("试错告警通知发送失败 - 设备: " + entity.device_id);
        return;
    }
    
    LogEvent("试错告警事件处理完成 - 通知已发送");
}

} // namespace Lock
} // namespace Events
} // namespace SmartLock