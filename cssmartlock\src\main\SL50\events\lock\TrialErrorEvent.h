#pragma once
#include "../base/StateChangeEventBase.h"

namespace SmartLock {
namespace Events {
namespace Lock {

/**
 * 试错攻击事件
 * 当检测到多次错误密码输入等试错攻击行为时触发
 */
class TrialErrorEvent : public SmartLock::Events::StateChangeEventBase {
public:
    TrialErrorEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::TRIAL_AND_ERROR; }
    
    /**
     * 检测是否为试错告警事件
     */
    static bool IsEventDetected(const Entity& entity);
};

} // namespace Lock
} // namespace Events
} // namespace SmartLock