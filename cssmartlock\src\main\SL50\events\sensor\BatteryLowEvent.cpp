#include "BatteryLowEvent.h"
#include "AkLogging.h"
#include <stdexcept>
#include <vector>

namespace SmartLock {
namespace Events {
namespace Sensor {

bool BatteryLowEvent::IsEventDetected(const Entity& entity) {
    AK_LOG_INFO << "BatteryLowEvent::isEventDetected - 开始检查实体: " << entity.entity_id;

    // 检查是否为传感器Domain
    if (entity.domain != EntityDomain::SENSOR) {
        AK_LOG_INFO << "BatteryLowEvent::isEventDetected - 不是 sensor domain: " << static_cast<int>(entity.domain);
        return false;
    }
    AK_LOG_INFO << "BatteryLowEvent::isEventDetected - 确认是 sensor domain";

    int prev_battery = std::stoi(entity.previous_value.state);
    int curr_battery = std::stoi(entity.current_value.state);
    AK_LOG_INFO << "BatteryLowEvent::isEventDetected - 电量变化: " << prev_battery << "% → " << curr_battery << "%";

    // 检查是否跨越了关键电量阈值
    std::string trigger_reason = "";
    bool should_trigger = CheckBatteryThresholdCrossed(prev_battery, curr_battery, trigger_reason);

    if (should_trigger) {
        AK_LOG_INFO << "BatteryLowEvent::isEventDetected - 触发低电量告警: " << trigger_reason;
    } else {
        AK_LOG_INFO << "BatteryLowEvent::isEventDetected - 未触发低电量告警";
    }
    return should_trigger;
}

void BatteryLowEvent::Process()
{
    LogEvent("开始处理低电量告警事件");
    AK_LOG_INFO << "BatteryLowEvent::process - 开始处理低电量告警事件";

    // 发送低电量告警通知
    const Entity& entity = GetEntity();
    AK_LOG_INFO << "BatteryLowEvent::process - 实体信息: " << entity.entity_id << ", 设备: " << entity.device_id;

    Notify::NotificationService& notificationService = Notify::NotificationService::GetInstance();
    AK_LOG_INFO << "BatteryLowEvent::process - 准备调用 sendBatteryLowNotification";

    bool success = notificationService.SendBatteryLowNotification(entity);

    if (success) {
        LogEvent("低电量告警通知发送成功");
        AK_LOG_INFO << "BatteryLowEvent::process - 低电量告警通知发送成功";
    } else {
        LogEvent("低电量告警通知发送失败");
        AK_LOG_ERROR << "BatteryLowEvent::process - 低电量告警通知发送失败";
    }
}

bool BatteryLowEvent::CheckBatteryThresholdCrossed(int prev_battery, int curr_battery, std::string& trigger_reason) 
{
    // 定义关键电量阈值（从高到低）
    static const std::vector<int> thresholds = {15, 10, 5};

    for (int threshold : thresholds) {
        if (prev_battery > threshold && curr_battery <= threshold) {
            trigger_reason = "电量降至" + std::to_string(threshold) + "%以下";
            return true;
        }
    }

    return false;
}

} // namespace Sensor
} // namespace Events
} // namespace SmartLock