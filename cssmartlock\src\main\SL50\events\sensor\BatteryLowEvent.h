#pragma once
#include "../base/StateChangeEventBase.h"
#include "../../notify/NotificationService.h"

namespace SmartLock {
namespace Events {
namespace Sensor {

/**
 * 电池低电量事件
 * 当传感器电池电量过低时触发
 */
class BatteryLowEvent : public SmartLock::Events::StateChangeEventBase {
public:
    BatteryLowEvent(const Entity& entity) : SmartLock::Events::StateChangeEventBase(entity) {}
    
    void Process() override;
    EntityEventType GetEventType() const override { return EntityEventType::BATTERY_LOW; }
    
    /**
     * 检测是否为电池低电量事件
     */
    static bool IsEventDetected(const Entity& entity);

private:
    /**
     * 检查是否跨越了关键电量阈值
     */
    static bool CheckBatteryThresholdCrossed(int prev_battery, int curr_battery, std::string& trigger_reason);
};

} // namespace Sensor
} // namespace Events
} // namespace SmartLock