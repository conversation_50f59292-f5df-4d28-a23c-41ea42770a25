#pragma once
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include "AkLogging.h"
#include "../entities/Entity.h"

namespace SmartLock {
namespace Notify {

/**
 * 通知类型枚举
 */
enum class NotificationType {
    TRIAL_ERROR,        // 试错告警
    DOORBELL,          // 门铃
    DWELL,             // 逗留告警
    TAMPER,            // 防拆告警
    BATTERY_LOW        // 低电量告警
};

/**
 * 通知消息结构
 */
struct NotificationMessage {
    NotificationType type;
    std::string device_id;
    std::string entity_id;
    std::string title;
    std::string content;
    std::string trigger_time;
    std::string extension_field;
    int project_type;
    int msg_type;
    
    NotificationMessage() : project_type(0), msg_type(0) {}
};

/**
 * 通知队列接口
 */
class INotificationQueue {
public:
    virtual ~INotificationQueue() = default;
    
    /**
     * 添加通知到队列
     */
    virtual bool Enqueue(const NotificationMessage& notification) = 0;
    
    /**
     * 处理队列中的通知
     */
    virtual bool ProcessQueue() = 0;
    
    /**
     * 获取队列大小
     */
    virtual size_t GetQueueSize() const = 0;
};

/**
 * 通知构建器接口
 */
class INotificationBuilder {
public:
    virtual ~INotificationBuilder() = default;
    
    /**
     * 从实体构建通知消息
     */
    virtual NotificationMessage BuildNotification(const Entity& entity, NotificationType type) = 0;
};

/**
 * 通知发送器接口
 */
class INotificationSender {
public:
    virtual ~INotificationSender() = default;
    
    /**
     * 发送通知
     */
    virtual bool SendNotification(const NotificationMessage& notification) = 0;
};

} // namespace Notify
} // namespace SmartLock 