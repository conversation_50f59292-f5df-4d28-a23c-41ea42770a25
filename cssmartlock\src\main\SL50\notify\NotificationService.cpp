#include "NotificationService.h"
#include "AkLogging.h"

namespace SmartLock {
namespace Notify {

NotificationService& NotificationService::GetInstance() {
    static NotificationService Instance;
    return Instance;
}

void NotificationService::Initialize() {
    // 创建通知发送器
    notification_sender_ = std::shared_ptr<INotificationSender>(new SmartLockNotificationSender());
    
    // 创建试错告警构建器
    trial_error_builder_ = std::shared_ptr<TrialErrorBuilder>(new TrialErrorBuilder());

    // 创建逗留告警构建器
    dwell_builder_ = std::shared_ptr<DwellBuilder>(new DwellBuilder());

    // 创建低电量告警构建器
    battery_low_builder_ = std::shared_ptr<BatteryLowBuilder>(new BatteryLowBuilder());

    // 创建防拆告警构建器
    tamper_builder_ = std::shared_ptr<TamperBuilder>(new TamperBuilder());

    // 创建门铃告警构建器
    doorbell_builder_ = std::shared_ptr<DoorbellBuilder>(new DoorbellBuilder());

    // 设置通知队列的发送器
    SmartLockNotificationQueue::GetInstance().SetNotificationSender(notification_sender_);
    
    AK_LOG_INFO << "通知服务初始化完成";
}

bool NotificationService::SendTrialErrorNotification(const Entity& entity) {
    return SendNotification(entity, NotificationType::TRIAL_ERROR);
}

bool NotificationService::SendDoorbellNotification(const Entity& entity) {
    return SendNotification(entity, NotificationType::DOORBELL);
}

bool NotificationService::SendDwellNotification(const Entity& entity) {
    return SendNotification(entity, NotificationType::DWELL);
}

bool NotificationService::SendBatteryLowNotification(const Entity& entity) {
    return SendNotification(entity, NotificationType::BATTERY_LOW);
}

bool NotificationService::SendTamperNotification(const Entity& entity) {
    return SendNotification(entity, NotificationType::TAMPER);
}

bool NotificationService::ProcessAllNotifications() {
    return SmartLockNotificationQueue::GetInstance().ProcessQueue();
}

size_t NotificationService::GetQueueSize() const {
    return SmartLockNotificationQueue::GetInstance().GetQueueSize();
}

bool NotificationService::SendNotification(const Entity& entity, NotificationType type) 
{
    NotificationMessage notification;
    
    // 根据通知类型使用相应的构建器
    switch (type) {
        case NotificationType::TRIAL_ERROR:
            notification = trial_error_builder_->BuildNotification(entity, type);
            break;
            
        case NotificationType::DWELL:
            notification = dwell_builder_->BuildNotification(entity, type);
            break;

        case NotificationType::BATTERY_LOW:
            notification = battery_low_builder_->BuildNotification(entity, type);
            break;

        case NotificationType::TAMPER:
            notification = tamper_builder_->BuildNotification(entity, type);
            break;

        case NotificationType::DOORBELL:
            notification = doorbell_builder_->BuildNotification(entity, type);
            break;
            
        default:
            AK_LOG_ERROR << "不支持的通知类型: " << static_cast<int>(type);
            return false;
    }
    
    // 将通知加入队列
    bool enqueue_result = SmartLockNotificationQueue::GetInstance().Enqueue(notification);
    if (!enqueue_result) {
        AK_LOG_ERROR << "通知加入队列失败 - 设备: " << entity.device_id  << ", 类型: " << static_cast<int>(type);
        return false;
    }
    
    AK_LOG_INFO << "通知已加入队列 - 设备: " << entity.device_id  << ", 类型: " << static_cast<int>(type);
    
    // 立即处理队列
    return ProcessAllNotifications();
}

} // namespace Notify
} // namespace SmartLock 