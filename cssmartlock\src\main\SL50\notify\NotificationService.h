#pragma once
#include "NotificationQueue.h"
#include "SmartLockNotificationQueue.h"
#include "SmartLockNotificationSender.h"
#include "builder/TrialErrorBuilder.h"
#include "builder/DwellBuilder.h"
#include "builder/BatteryLowBuilder.h"
#include "builder/TamperBuilder.h"
#include "builder/DoorbellBuilder.h"
#include <memory>

namespace SmartLock {
namespace Notify {

/**
 * 通知服务 - 提供统一的通知处理接口
 */
class NotificationService {
public:
    static NotificationService& GetInstance();
    
    /**
     * 初始化通知服务
     */
    void Initialize();
    
    /**
     * 发送试错告警通知
     */
    bool SendTrialErrorNotification(const Entity& entity);
    
    /**
     * 发送门铃通知
     */
    bool SendDoorbellNotification(const Entity& entity);
    
    /**
     * 发送逗留告警通知
     */
    bool SendDwellNotification(const Entity& entity);

    /**
     * 发送低电量告警通知
     */
    bool SendBatteryLowNotification(const Entity& entity);
    
    /**
     * 发送防拆告警通知
     */
    bool SendTamperNotification(const Entity& entity);

    /**
     * 处理所有待处理的通知
     */
    bool ProcessAllNotifications();
    
    /**
     * 获取队列中的通知数量
     */
    size_t GetQueueSize() const;

private:
    NotificationService() = default;
    
    std::shared_ptr<INotificationSender> notification_sender_;
    std::shared_ptr<TrialErrorBuilder> trial_error_builder_;
    std::shared_ptr<DwellBuilder> dwell_builder_;
    std::shared_ptr<BatteryLowBuilder> battery_low_builder_;
    std::shared_ptr<TamperBuilder> tamper_builder_;
    std::shared_ptr<DoorbellBuilder> doorbell_builder_;
    
    /**
     * 通用通知发送方法
     */
    bool SendNotification(const Entity& entity, NotificationType type);
};

} // namespace Notify
} // namespace SmartLock 