#include "SmartLockNotificationQueue.h"
#include <mutex>

namespace SmartLock {
namespace Notify {

SmartLockNotificationQueue& SmartLockNotificationQueue::GetInstance() {
    static SmartLockNotificationQueue instance;
    return instance;
}

bool SmartLockNotificationQueue::Enqueue(const NotificationMessage& notification) {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    try {
        notification_queue_.push(notification);
        AK_LOG_INFO << "通知已加入队列 - 类型: " << static_cast<int>(notification.type) 
                   << ", 设备: " << notification.device_id 
                   << ", 队列大小: " << notification_queue_.size();
        return true;
    } catch (const std::exception& e) {
        AK_LOG_ERROR << "加入通知队列失败: " << e.what();
        return false;
    }
}

bool SmartLockNotificationQueue::ProcessQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    if (notification_queue_.empty()) {
        return true;
    }
    
    AK_LOG_INFO << "开始处理通知队列，当前队列大小: " << notification_queue_.size();
    
    int processed_count = 0;
    int failed_count = 0;
    
    while (!notification_queue_.empty()) {
        NotificationMessage notification = notification_queue_.front();
        notification_queue_.pop();
        
        if (ProcessSingleNotification(notification)) {
            processed_count++;
        } else {
            failed_count++;
        }
    }
    
    AK_LOG_INFO << "通知队列处理完成 - 成功: " << processed_count << ", 失败: " << failed_count;
    return failed_count == 0;
}

size_t SmartLockNotificationQueue::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return notification_queue_.size();
}

void SmartLockNotificationQueue::SetNotificationSender(std::shared_ptr<INotificationSender> sender) {
    notification_sender_ = sender;
    AK_LOG_INFO << "通知发送器已设置";
}

bool SmartLockNotificationQueue::ProcessSingleNotification(const NotificationMessage& notification) {
    if (!notification_sender_) {
        AK_LOG_ERROR << "通知发送器未设置，无法处理通知";
        return false;
    }

    AK_LOG_INFO << "处理通知 - 类型: " << static_cast<int>(notification.type) 
                << ", 设备: " << notification.device_id 
                << ", 标题: " << notification.title;
    
    bool result = notification_sender_->SendNotification(notification);
    
    if (result) {
        AK_LOG_INFO << "通知发送成功 - 设备: " << notification.device_id;
    } else {
        AK_LOG_ERROR << "通知发送失败 - 设备: " << notification.device_id;
    }
    
    return result;
}

} // namespace Notify
} // namespace SmartLock 