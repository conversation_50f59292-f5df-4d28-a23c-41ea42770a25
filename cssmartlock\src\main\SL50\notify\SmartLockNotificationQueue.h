#pragma once
#include "NotificationQueue.h"
#include <queue>
#include <mutex>
#include <memory>

namespace SmartLock {
namespace Notify {

/**
 * 智能锁通知队列实现
 */
class SmartLockNotificationQueue : public INotificationQueue {
public:
    static SmartLockNotificationQueue& GetInstance();
    
    // INotificationQueue 接口实现
    bool Enqueue(const NotificationMessage& notification) override;
    bool ProcessQueue() override;
    size_t GetQueueSize() const override;
    
    /**
     * 设置通知发送器
     */
    void SetNotificationSender(std::shared_ptr<INotificationSender> sender);

private:
    SmartLockNotificationQueue() = default;
    
    mutable std::mutex queue_mutex_;
    std::queue<NotificationMessage> notification_queue_;
    std::shared_ptr<INotificationSender> notification_sender_;
    
    /**
     * 处理单个通知
     */
    bool ProcessSingleNotification(const NotificationMessage& notification);
};

} // namespace Notify
} // namespace SmartLock 