#include "SmartLockNotificationSender.h"
#include "AkLogging.h"
#include "util.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

namespace SmartLock {
namespace Notify {

bool SmartLockNotificationSender::SendNotification(const NotificationMessage& notification) {
    std::string log_prefix = GetNotificationLogPrefix(notification.type);
    if (log_prefix.empty()) {
        AK_LOG_ERROR << "未知的通知类型: " << static_cast<int>(notification.type);
        return false;
    }
    
    return SendNotificationCommon(notification, log_prefix);
}

bool SmartLockNotificationSender::SendNotificationCommon(const NotificationMessage& notification, const std::string& log_prefix) {
    AK_LOG_INFO << "开始发送" << log_prefix << "通知 - 设备: " << notification.device_id;

    // 直接使用数据库接口获取智能锁信息
    SmartLockInfo smartlock_info;
    if (0 != dbinterface::SmartLock::GetSmartLockInfoByUUID(notification.device_id, smartlock_info)) {
        AK_LOG_ERROR << "获取智能锁信息失败: " << notification.device_id;
        return false;
    }

    // 转换消息格式
    CommPerTextMessage text_msg = ConvertToCommPerTextMessage(notification);
    
    // 获取发送列表并插入数据库
    PerTextMessageSendList text_messages;
    if (0 != GetMessageSendListAndSave(smartlock_info.personal_account_uuid, notification, text_messages)) {
        AK_LOG_ERROR << "获取消息发送列表并保存失败";
        return false;
    }
    
    // 发送消息
    CSmartLock2RouteMsg::SendGroupTextMessage(text_messages, text_msg.trigger_time);
    AK_LOG_INFO << log_prefix << "通知发送成功 - 设备: " << notification.device_id;
    return true;
}


std::string SmartLockNotificationSender::GetNotificationLogPrefix(NotificationType type) const {
    switch (type) {
        case NotificationType::TRIAL_ERROR:
            return "试错告警";
        case NotificationType::DWELL:
            return "驻留告警";
        case NotificationType::TAMPER:
            return "防拆告警";
        case NotificationType::DOORBELL:
            return "门铃";
        case NotificationType::BATTERY_LOW:
            return "低电量告警";
        default:
            return ""; // 返回空字符串表示未知类型
    }
}

int SmartLockNotificationSender::GetMessageSendListAndSave(const std::string& node_uuid, 
                                                           const NotificationMessage& notification, 
                                                           PerTextMessageSendList& text_messages) {
    // 主账号
    ResidentPerAccount master_account;
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(node_uuid, master_account)) {
        AK_LOG_WARN << "获取主账号失败, node uuid: " << node_uuid;
        return -1;
    }

    int sub_account_role = akjudge::IsCommunityEndUserRole(master_account.role) ? 
                          ACCOUNT_ROLE_COMMUNITY_ATTENDANT : ACCOUNT_ROLE_PERSONNAL_ATTENDANT;
    
    // 从账号列表获取
    ResidentPerAccountList sub_account_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetPersoanlAttendantListByUid(
            master_account.account, sub_account_role, sub_account_list)) {
        AK_LOG_WARN << "获取子账号列表失败, node uuid: " << node_uuid;
        return -1;
    }

    // 不需要通知设备，设备列表为空
    ResidentDeviceList dev_list;
    bool message_need_notify_dev = false;

    // 转换消息格式
    CommPerTextMessage comm_text_msg = ConvertToCommPerTextMessage(notification);

    // Message存到数据库并获取发送列表结构
    if (0 != dbinterface::Message::AddGroupTextMsg(
            static_cast<int>(comm_text_msg.msg_type), comm_text_msg, dev_list, 
            master_account, sub_account_list, text_messages, message_need_notify_dev)) {
        AK_LOG_WARN << "添加群组文本消息到数据库失败";
        return -1;
    }

    return 0;
}

CommPerTextMessage SmartLockNotificationSender::ConvertToCommPerTextMessage(const NotificationMessage& notification) {
    CommPerTextMessage text_msg;
    text_msg.project_type = notification.project_type;
    text_msg.msg_type = static_cast<MessageType2>(notification.msg_type);
    
    // 使用 Snprintf 安全地复制字符串
    Snprintf(text_msg.title, sizeof(text_msg.title), notification.title.c_str());
    Snprintf(text_msg.content, sizeof(text_msg.content), notification.content.c_str());
    Snprintf(text_msg.extension_field, sizeof(text_msg.extension_field), notification.extension_field.c_str());
    
    // 设置触发时间
    if (!notification.trigger_time.empty()) {
        Snprintf(text_msg.trigger_time, sizeof(text_msg.trigger_time), notification.trigger_time.c_str());
    }
    
    return text_msg;
}

} // namespace Notify
} // namespace SmartLock 