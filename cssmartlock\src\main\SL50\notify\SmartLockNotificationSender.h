#pragma once
#include "NotificationQueue.h"
#include "SmartLock2RouteMsg.h"
#include "util_judge.h"
#include "dbinterface/Message.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

namespace SmartLock {
namespace Notify {

/**
 * 智能锁通知发送器
 */
class SmartLockNotificationSender : public INotificationSender {
public:
    bool SendNotification(const NotificationMessage& notification) override;

private:
    /**
     * 通用通知发送实现
     * @param notification 通知消息
     * @param log_prefix 日志前缀，用于区分不同类型的通知
     * @return 发送是否成功
     */
    bool SendNotificationCommon(const NotificationMessage& notification, const std::string& log_prefix);
    
    /**
     * 获取通知类型对应的日志前缀
     */
    std::string GetNotificationLogPrefix(NotificationType type) const;
    
    /**
     * 获取消息发送列表并保存
     */
    int GetMessageSendListAndSave(const std::string& node_uuid, 
                                 const NotificationMessage& notification, 
                                 PerTextMessageSendList& text_messages);
    
    /**
     * 转换为CommPerTextMessage格式
     */
    CommPerTextMessage ConvertToCommPerTextMessage(const NotificationMessage& notification);
};

} // namespace Notify
} // namespace SmartLock 