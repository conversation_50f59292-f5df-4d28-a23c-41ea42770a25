#include "BatteryLowBuilder.h"
#include "AkLogging.h"
#include "util.h"
#include "util_string.h"
#include "dbinterface/Message.h"

namespace SmartLock {
namespace Notify {

NotificationMessage BatteryLowBuilder::BuildNotification(const Entity& entity, NotificationType type) 
{
    NotificationMessage notification;
    notification.type = type;
    notification.device_id = entity.device_id;
    notification.entity_id = entity.entity_id;
    notification.trigger_time = entity.device_time;

    // 获取电量等级
    int battery_level = std::stoi(entity.current_value.state);

    // 获取锁信息
    SmartLockInfo smartlock_info;
    if (!GetSmartLockInfo(entity.device_id, smartlock_info)) {
        AK_LOG_ERROR << "BatteryLowNotificationBuilder::buildNotification - 获取智能锁信息失败: " << entity.device_id;
        return notification;
    }

    // 获取账户信息
    ResidentPerAccount per_account;
    if (!GetAccountInfo(smartlock_info.personal_account_uuid, per_account)) {
        AK_LOG_ERROR << "BatteryLowNotificationBuilder::buildNotification - 获取账户信息失败: " << smartlock_info.personal_account_uuid;
        return notification;
    }

    // 构造通知消息
    ConstructPersonalTextMessage(per_account, smartlock_info, notification, battery_level);
    AK_LOG_INFO << "BatteryLowNotificationBuilder::buildNotification - 设备: " << entity.device_id 
               << ", 标题: " << notification.title << ", 内容: " << notification.content;

    return notification;
}

void BatteryLowBuilder::ConstructPersonalTextMessage(const ResidentPerAccount& per_account,
                                                               const SmartLockInfo& smartlock_info, 
                                                               NotificationMessage& notification,
                                                               int battery_level) {
    int project_type = 0;
    if (akjudge::IsCommunityEndUserRole(per_account.role)) {
        project_type = project::PROJECT_TYPE::RESIDENCE;
    } else {
        project_type = project::PROJECT_TYPE::PERSONAL;
    }

    notification.project_type = project_type;
    notification.msg_type = static_cast<int>(MessageType2::AKUBELA_LOCK_BATTERY_NOTICE);

    notification.title = "akubela Battery Warning";
    notification.content = smartlock_info.name;

    // 设置 extension_field，模仿 ReportLockEventV1.cpp 的做法
    std::map<std::string, int> json_int_datas;
    json_int_datas.emplace(MESSAGE_EXTENSION_FIELD_LAST_BATTERY, battery_level);
    std::map<std::string, std::string> json_str_datas;
    std::map<std::string, bool> json_bool_datas;

    notification.extension_field = GetJsonString(json_str_datas, json_int_datas, json_bool_datas);

    AK_LOG_INFO << "BatteryLowNotificationBuilder - extension_field: " << notification.extension_field;
}

} // namespace Notify
} // namespace SmartLock
