#pragma once
#include "BuilderBase.h"
#include "util_judge.h"

namespace SmartLock {
namespace Notify {

/**
 * 低电量告警通知构建器
 */
class BatteryLowBuilder : public BuilderBase {
public:
    /**
     * 构建低电量告警通知
     */
    NotificationMessage BuildNotification(const Entity& entity, NotificationType type) override;

private:
    void ConstructPersonalTextMessage(const ResidentPerAccount& per_account, const SmartLockInfo& smartlock_info, NotificationMessage& notification, int battery_level);
};

} // namespace Notify
} // namespace SmartLock
