#include "BuilderBase.h"

namespace SmartLock {
namespace Notify {

bool BuilderBase::GetSmartLockInfo(const std::string& device_id, SmartLockInfo& smartlock_info) {
    if (0 != dbinterface::SmartLock::GetSmartLockInfoByUUID(device_id, smartlock_info)) {
        AK_LOG_WARN << "智能锁不存在，获取信息失败: " << device_id;
        return false;
    }
    return true;
}

bool BuilderBase::GetAccountInfo(const std::string& account_uuid, ResidentPerAccount& per_account) {
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(account_uuid, per_account)) {
        AK_LOG_WARN << "锁尚未添加，获取账户信息失败: " << account_uuid;
        return false;
    }
    return true;
}

} // namespace Notify
} // namespace SmartLock
