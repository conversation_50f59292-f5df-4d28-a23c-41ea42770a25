#pragma once
#include "../NotificationQueue.h"
#include "../../entities/Entity.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AkLogging.h"

namespace SmartLock {
namespace Notify {

/**
 * 通知构建器基类
 * 提供公共的数据获取方法
 */
class BuilderBase : public INotificationBuilder {
public:
    virtual ~BuilderBase() = default;

protected:
    /**
     * 获取智能锁信息
     */
    bool GetSmartLockInfo(const std::string& device_id, SmartLockInfo& smartlock_info);
    
    /**
     * 获取账户信息
     */
    bool GetAccountInfo(const std::string& account_uuid, ResidentPerAccount& per_account);
};

} // namespace Notify
} // namespace SmartLock
