#pragma once
#include "BuilderBase.h"
#include "util_judge.h"

namespace SmartLock {
namespace Notify {

/**
 * 逗留告警通知构建器
 */
class DwellBuilder : public BuilderBase {
public:
    /**
     * 构建逗留告警通知
     */
    NotificationMessage BuildNotification(const Entity& entity, NotificationType type) override;

private:
    void ConstructPersonalTextMessage(const ResidentPerAccount& per_account, const SmartLockInfo& smartlock_info, NotificationMessage& notification);
};

} // namespace Notify
} // namespace SmartLock
