#include "TamperBuilder.h"
#include "AkLogging.h"
#include "util.h"

namespace SmartLock {
namespace Notify {

NotificationMessage TamperBuilder::BuildNotification(const Entity& entity, NotificationType type) 
{
    NotificationMessage notification;
    notification.type = type;
    notification.device_id = entity.device_id;
    notification.entity_id = entity.entity_id;
    notification.trigger_time = entity.device_time;

    // 获取锁信息
    SmartLockInfo smartlock_info;
    if (!GetSmartLockInfo(entity.device_id, smartlock_info)) {
        AK_LOG_ERROR << "TamperNotificationBuilder::BuildNotification - 获取智能锁信息失败: " << entity.device_id;
        return notification;
    }

    // 获取账户信息
    ResidentPerAccount per_account;
    if (!GetAccountInfo(smartlock_info.personal_account_uuid, per_account)) {
        AK_LOG_ERROR << "TamperNotificationBuilder::BuildNotification - 获取账户信息失败: " << smartlock_info.personal_account_uuid;
        return notification;
    }

    // 构造通知消息
    ConstructPersonalTextMessage(per_account, smartlock_info, notification);
    
    AK_LOG_INFO << "TamperNotificationBuilder::BuildNotification - 设备: " << entity.device_id << ", 标题: " << notification.title << ", 内容: " << notification.content;

    return notification;
}

void TamperBuilder::ConstructPersonalTextMessage(const ResidentPerAccount& per_account,
                                                           const SmartLockInfo& smartlock_info, 
                                                           NotificationMessage& notification) {
    int project_type = 0;
    if (akjudge::IsCommunityEndUserRole(per_account.role)) {
        project_type = project::PROJECT_TYPE::RESIDENCE;
    } else {
        project_type = project::PROJECT_TYPE::PERSONAL;
    }

    notification.project_type = project_type;
    notification.msg_type = static_cast<int>(MessageType2::SMARTLOCK_TAMPER_EVENT); // 使用通用文本消息类型

    notification.title = "Tamper Alert";
    notification.content = smartlock_info.name;;
}



} // namespace Notify
} // namespace SmartLock
