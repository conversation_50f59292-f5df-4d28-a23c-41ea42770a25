#pragma once
#include "BuilderBase.h"
#include "util_judge.h"

namespace SmartLock {
namespace Notify {

/**
 * 防拆告警通知构建器
 */
class TamperBuilder : public BuilderBase {
public:
    /**
     * 构建防拆告警通知
     */
    NotificationMessage BuildNotification(const Entity& entity, NotificationType type) override;

private:
    void ConstructPersonalTextMessage(const ResidentPerAccount& per_account, const SmartLockInfo& smartlock_info, NotificationMessage& notification);
};

} // namespace Notify
} // namespace SmartLock
