#pragma once
#include "BuilderBase.h"
#include "util_judge.h"
#include "dbinterface/Message.h"

namespace SmartLock {
namespace Notify {

/**
 * 试错告警通知构建器
 */
class TrialErrorBuilder : public BuilderBase {
public:
    NotificationMessage BuildNotification(const Entity& entity, NotificationType type) override;
    
private:
    void ConstructPersonalTextMessage(const ResidentPerAccount& per_account, const SmartLockInfo& smartlock_info, NotificationMessage& notification);
};

} // namespace Notify
} // namespace SmartLock 