#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "AkLogging.h"
#include "SmartLockMsgDef.h"
#include "AK.Adapt.pb.h"
#include "AK.Route.pb.h"
#include "AkcsMsgDef.h"
#include "RouteLockP2PMessage.h"
#include "RouteHttpUpP2PMessage.h"
#include "MqttPublish.h"
#include "AkLogging.h"
#include "SL50LockControl.h"
#include "SL50/DownMessage/LockControl.h"
#include "SL50/DownMessage/UnLockControl.h"
#include "UpMessageSL50Factory.h"
#include "MqttSubscribe.h"
#include "CommandQueueManager.h"

int SmartLockHttpUpMessage(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Adapt::SmartLockHttpUpP2PRouteMessage msg;
    CHECK_PB_PARSE_MSG_ERR_RET(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    std::string client_id = msg.client_id();
    std::string receive_msg = msg.json_message();

    AK_LOG_INFO << "SmartLockHttpUpMessage call mqtt DispatchMsg cleintID:" << client_id << ", message:" << receive_msg;
    std::string topic_name = std::string(MQTT_SUB_LOCK_UP_TOPIC) + std::string(PREMIUM_SMARTLOCK_CLEINT_FLAG) + client_id;

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(receive_msg, root))
    {
        AK_LOG_WARN << "parse json error.msg=" << receive_msg;
		return 0;
    }
    std::string command;
    if (root.isMember("command"))       
    {
        command = root["command"].asString();
		CommandQueueManager::GetInstance()->EnqueueMessage(command, receive_msg, topic_name);		
    }
    return 0;
}
