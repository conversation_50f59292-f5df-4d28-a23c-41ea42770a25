#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RouteFactory.h"
#include "RouteBase.h"
#include "AkLogging.h"
#include "SmartLockMsgDef.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AkcsMsgDef.h"
#include "RouteLockP2PAckMessage.h"
#include "MqttPublish.h"
#include "AkLogging.h"
#include "SL50LockControl.h"
#include "SL50/DownMessage/LockControl.h"
#include "SL50/DownMessage/UnLockControl.h"

__attribute__((constructor))  static void init()
{
    IRouteBasePtr p = std::make_shared<RouteLockP2PAckMessage>();
    RegRouteFunc(p, AKCS_R2S_P2P_ACK_SMARTLOCK_MSG);
};

int RouteLockP2PAckMessage::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Route::P2PRoutLockAckMessage msg;
    CHECK_PB_PARSE_MSG_ERR_RET(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);
    std::string client_id = msg.client_id();
    std::string json_message = msg.json_message();

    SL50LockControl::PushAckMessage(client_id, json_message);

    //二次处理示例
    if (std::string(UnLockControl::AKCS_COMMAND) == msg.akcs_command())
    {
        LockControl lock_control;
        lock_control.from_json(json_message);
        AK_LOG_INFO << "<<<<<<LockControl::AKCS_COMMAND" << lock_control.entity_id_;
    }
    return 0;
}

void RouteLockP2PAckMessage::IReplyParamConstruct()
{

    return;
}
