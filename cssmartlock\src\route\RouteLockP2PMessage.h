#ifndef _ROUTE_LOCK_P2P_MESSAGE_H_
#define _ROUTE_LOCK_P2P_MESSAGE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "AkcsMsgDef.h"
#include "json/json.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/SmartLockShadow.h"
#include "dbinterface/SmartLockUpgrade.h"
#include "AkcsCommonDef.h"

class RouteLockP2PMessage: public IRouteBase
{
public: 
    RouteLockP2PMessage(){}
    ~RouteLockP2PMessage() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    void IReplyParamConstruct();

    IRouteBasePtr NewInstance() {return std::make_shared<RouteLockP2PMessage>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteLockP2PMessage";

};


#endif



