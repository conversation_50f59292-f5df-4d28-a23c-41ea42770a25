#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>
#include "RouteFactory.h"
#include "RouteBase.h"
#include "AkLogging.h"
#include "SmartLockMsgDef.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AkcsMsgDef.h"
#include "SL20LockControl.h"
#include "RouteNotifySmartlockUpdate.h"

__attribute__((constructor))  static void init()
{
    IRouteBasePtr p = std::make_shared<RouteNotifySmartlockUpdate>();
    RegRouteFunc(p, AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ);
};

int RouteNotifySmartlockUpdate::IControl(const std::unique_ptr<CAkcsPdu> &pdu)
{
    AK::Server::SmartLockUpdateConfigurationNotifyMsg msg;
    CHECK_PB_PARSE_MSG_ERR_RET(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()), -1);

    lock_uuid_ = msg.lock_uuid();
    notify_smartlock_type_ = (NotifySmartLockType)msg.lock_type();
    AK_LOG_INFO << "Receive upgrade configuration request, lock_uuid=" << lock_uuid_;

    if (GetLockRelatedInfo() != 0)
    {
        AK_LOG_WARN << "Get lock related info failed, lock_uuid=" << lock_uuid_;
        return -1;
    }

    return 0;
}

int RouteNotifySmartlockUpdate::GetLockRelatedInfo()
{
    if (notify_smartlock_type_ == NotifySmartLockType::NOTIFY_SMARTLOCK_TYPE_SL20)
    {
        return GetSL20LockRelatedInfo();
    }

    return 0;
}

void RouteNotifySmartlockUpdate::GenerateSL20ConfigurationJson(Json::Value& data)
{
    std::string configuration(sl20_lock_shadow_info_.configuration);

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(configuration, root))
    {
        return;
    }

    if (!root.isMember("configuration"))
    {
        return;
    }

    Json::Value conf_json = root["configuration"];

    data["param"]["configuration"] = conf_json;
}

void RouteNotifySmartlockUpdate::IReplyParamConstruct()
{
    GenerateMsgJson(reply_data_);

    return;
}

void RouteNotifySmartlockUpdate::GenerateMsgJson(Json::Value& data)
{
    if (notify_smartlock_type_ == NotifySmartLockType::NOTIFY_SMARTLOCK_TYPE_SL20)
    {
        id_ = lock_uuid_;
        command_ = SL20_LOCK_GET_CONFIGURATION_V1_0;

        reply_data_["param"]["hash"] = sl20_lock_shadow_info_.configuration_hash;
        GenerateSL20ConfigurationJson(reply_data_);

        reply_data_["param"]["lock_info"]["state"] = door_state_;
        reply_data_["param"]["lock_info"]["open_door_relate"] = open_door_relate_;
        reply_data_["param"]["lock_info"]["upgrade_version"] = sl20_upgrade_info_.upgrade_module_version;
        reply_data_["param"]["lock_info"]["firmware_download_url"] = sl20_upgrade_info_.firmware_download_url;
    }
}

int RouteNotifySmartlockUpdate::GetSL20LockRelatedInfo()
{
    if (dbinterface::SmartLock::GetSmartLockInfoByUUID(lock_uuid_, sl20_lock_info_) != 0)
    {
        AK_LOG_WARN << "Get lock info failed, lock_uuid=" << lock_uuid_;
        return -1;
    }

    if (dbinterface::SmartLockShadow::GetSmartLockShadowBySmartLockUUID(lock_uuid_, sl20_lock_shadow_info_) != 0)
    {
        AK_LOG_WARN << "Get lock shadow info failed, lock_uuid=" << lock_uuid_;
        return -1;
    }
    
    // 锁未升级过就不会有数据，没查到是正常的
    dbinterface::SmartLockUpgrade::GetSmartLockUpgradeBySmartLockUUID(lock_uuid_, sl20_upgrade_info_);

    SL20LockControl::GetSL20LockOpenDoorInfo(lock_uuid_, door_state_, open_door_relate_, opener_list_);

    return 0;
}

