#ifndef _ROUTE_UPGRADE_CONFIGURATION_H_
#define _ROUTE_UPGRADE_CONFIGURATION_H_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "RouteBase.h"
#include "AkcsMsgDef.h"
#include "json/json.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/SmartLockShadow.h"
#include "dbinterface/SmartLockUpgrade.h"
#include "AkcsCommonDef.h"

class RouteNotifySmartlockUpdate: public IRouteBase
{
public: 
    RouteNotifySmartlockUpdate(){}
    ~RouteNotifySmartlockUpdate() = default;

    int IControl(const std::unique_ptr<CAkcsPdu> &pdu);
    void IReplyParamConstruct();

    IRouteBasePtr NewInstance() {return std::make_shared<RouteNotifySmartlockUpdate>();}
    std::string FuncName() {return func_name_;}

private:
    std::string func_name_ = "RouteNotifySmartlockUpdate";

    std::string lock_uuid_;
    NotifySmartLockType notify_smartlock_type_;

    SmartLockInfo sl20_lock_info_;
    SmartLockShadowInfo sl20_lock_shadow_info_;
    SmartLockUpgradeInfo sl20_upgrade_info_;

    std::string door_state_;
    std::string open_door_relate_;
    std::set<std::string> opener_list_;

    // 获取锁相关配置信息
    int GetLockRelatedInfo();
    int GetSL20LockRelatedInfo();
    void GenerateMsgJson(Json::Value& data);
    // 构建SL20锁configuration信息
    void GenerateSL20ConfigurationJson(Json::Value& data);
};


#endif



