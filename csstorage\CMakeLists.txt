CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (storage C CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/../csbase/doorlog")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libaws-cpp-sdk-core.so libaws-cpp-sdk-s3.so fastcommon libfdfsclient.a 
libcsbase.a libjpeg.so pthread libhiredis.a libevent.so libglog.so libmysqlclient.so libgpr.so 
libgrpc.so libgrpc++.so libevpp.so libssl.so libcrypto.so libprotobuf.so libetcd-cpp-api.so 
libcpprest.so libboost_system.so libboost_filesystem.so libalibabacloud-oss-cpp-sdk.a 
-lcurl -lssl -lcrypto libufilecppsdk.a libjson-c.a)

SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../)
LINK_DIRECTORIES(${BASE_SOURCE_DIR}/csbase/fdfs_client/fdfsclient ${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/thirdlib ${BASE_SOURCE_DIR}/csbase/redis/hiredis ${BASE_SOURCE_DIR}/csbase/thirdlib/oss ${BASE_SOURCE_DIR}/csbase/json-c /usr/local/lib ${BASE_SOURCE_DIR}/csbase/thirdlib/turbojpeg/lib)

AUX_SOURCE_DIRECTORY(./ SRC_LIST_STORAGE)
AUX_SOURCE_DIRECTORY(./model SRC_LIST_STORAGE_MODE)
AUX_SOURCE_DIRECTORY(./model/Maintenance SRC_LIST_STORAGE_MAINTENANCE)
AUX_SOURCE_DIRECTORY(./common SRC_LIST_STORAGE_COMMON)
AUX_SOURCE_DIRECTORY(./control SRC_LIST_STORAGE_CONTROL)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/protobuf SRC_LIST_BASE_PROTOBUF)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/src/json SRC_LIST_BASE_JSON)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/Character/cstring SRC_LIST_BASE_CSTRING)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/doorlog SRC_LIST_BASE_DOORLOG)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/uploader SRC_LIST_BASE_UPLOADER)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/cspbxrpc SRC_LIST_BASE_CSPBXRPC)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/grpc/cspbxrpc SRC_LIST_BASE_GRPC_CSPBXRPC)

SET(STORAGE_MODE_LIST_INC ./model)
SET(STORAGE_COMMON_LIST_INC ./common)
SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
SET(BASE_LIST_INC ${BASE_SOURCE_DIR}/csbase/etcd ${BASE_SOURCE_DIR}/csbase ${BASE_SOURCE_DIR}/csbase/mysql/include 
    ${BASE_SOURCE_DIR}/csbase/Rldb ${BASE_SOURCE_DIR}/csbase/evpp ${BASE_SOURCE_DIR}/csbase/encrypt 
    ${BASE_SOURCE_DIR}/csbase/protobuf ${BASE_SOURCE_DIR}/csbase/grpc ${BASE_SOURCE_DIR}/csbase/grpc/gens 
    ${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/include ${BASE_SOURCE_DIR}/csbase/Character/cstring 
    ${BASE_SOURCE_DIR}/csbase/redis ${BASE_SOURCE_DIR}/csbase/oss/include/alibabacloud 
    ${BASE_SOURCE_DIR}/csbase/http ${BASE_SOURCE_DIR}/csbase/oss/include 
    ${BASE_SOURCE_DIR}/csbase/thirdlib/turbojpeg/include ${CSBASE_SOURCE_DIR}/cspbxrpc
    ${CSBASE_SOURCE_DIR}/grpc/cspbxrpc ${CSBASE_SOURCE_DIR}/pbxmod)

# -WALL
ADD_DEFINITIONS( -std=gnu++11 -g2 -fno-stack-protector -Werror 
-D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO -DAC_HAS_WARNING 
-DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON
-DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING 
-Wno-unused-parameter -Wno-deprecated -Wno-class-memaccess -Wno-deprecated-copy -Wno-implicit-fallthrough)
                           
include_directories(${PROJECT_SOURCE_DIR} ${BASE_SOURCE_DIR}/csbase/fdfs_client/fdfsclient 
                    ${BASE_SOURCE_DIR}/csbase/fdfs_client/libfdfscomm ${BASE_LIST_INC} ${BASE_SOURCE_DIR}/csbase/metrics 
                    ${BASE_SOURCE_DIR}/csbase/uploader ${STORAGE_COMMON_LIST_INC} 
                    ${STORAGE_MODE_LIST_INC} ${STORAGE_MODE_LIST_INC}/Maintenance 
                    /usr/local/grpc/include /usr/local/protobuf/include /usr/local/boost/include)

add_executable(csstorage ${SRC_LIST_STORAGE} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_ETCD} 
            ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_PROTOBUF} ${SRC_LIST_BASE_JSON} ${SRC_LIST_BASE_CHARACTER} 
            ${SRC_LIST_BASE_CSTRING} ${SRC_LIST_STORAGE_MODE} ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_STORAGE_COMMON}  
            ${SRC_LIST_BASE_METRICS} ${SRC_LIST_BASE_DOORLOG} ${SRC_LIST_BASE_UPLOADER} ${prefixed_file_list} 
            ${SRC_LIST_STORAGE_MAINTENANCE} ${SRC_LIST_STORAGE_CONTROL} ${STORAGE_MODE_LIST_INC}/Maintenance 
            ${SRC_LIST_BASE_CSPBXRPC} ${SRC_LIST_BASE_GRPC_CSPBXRPC})

set_target_properties(csstorage PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csstorage/lib")

target_link_libraries(csstorage  ${DEPENDENT_LIBRARIES})
