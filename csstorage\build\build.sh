#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../
AKCS_SRC_BIN_DIR=${AKCS_SRC_ROOT}/csstorage
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=akcs_csstorage_packeg
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/${AKCS_PACKAGE_NAME}
AKCS_PACKAGE_ROOT_DEBUG=${AKCS_SRC_ROOT}/${AKCS_PACKAGE_NAME}_debug

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    if [ -d "$AKCS_PACKAGE_ROOT_DEBUG" ]
	then
		rm -rf "$AKCS_PACKAGE_ROOT_DEBUG"
	fi

	mkdir -p $AKCS_PACKAGE_ROOT/csstorage
	mkdir -p $AKCS_PACKAGE_ROOT_DEBUG/csstorage

    #create protobuf
    cd $AKCS_SRC_ROOT/csbp/proto || exit 1
    bash create_proto.sh 
	
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit 1;
    fi

	cd $AKCS_SRC_BIN_DIR || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make successed";
    else
        echo "make failed";
        exit 1;
    fi
	
    cp -rf $AKCS_SRC_BIN_DIR/release/bin/csstorage $AKCS_SRC_BIN_DIR/release/bin/csstorage_offline
	cp -rf $AKCS_SRC_BIN_DIR/release/* $AKCS_PACKAGE_ROOT/csstorage
	cp -rf $AKCS_SRC_CSBASE/common_scripts/* $AKCS_PACKAGE_ROOT/csstorage/scripts
    cp -rf $AKCS_SRC_BIN_DIR/shell/Dockerfile $AKCS_PACKAGE_ROOT
    cp -rf $AKCS_SRC_BIN_DIR/shell/Dockerfile_debug $AKCS_PACKAGE_ROOT_DEBUG/Dockerfile
    cp -rf $AKCS_SRC_BIN_DIR/release/bin $AKCS_PACKAGE_ROOT_DEBUG/csstorage

    cp -rf $AKCS_SRC_CSBASE/thirdlib/oss/libaws-cpp-sdk-core.so  $AKCS_PACKAGE_ROOT/csstorage/lib
    cp -rf $AKCS_SRC_CSBASE/thirdlib/oss/libaws-cpp-sdk-s3.so  $AKCS_PACKAGE_ROOT/csstorage/lib
    cp -rf $AKCS_SRC_CSBASE/thirdlib/libboost_filesystem.so.1.82.0  $AKCS_PACKAGE_ROOT/csstorage/lib
    cp -rf $AKCS_SRC_CSBASE/thirdlib/turbojpeg/lib/libjpeg.so.62  $AKCS_PACKAGE_ROOT/csstorage/lib

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/version ${AKCS_PACKAGE_ROOT}

    strip $AKCS_PACKAGE_ROOT/csstorage/bin/*

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_SRC_ROOT} || exit 1
    rm -rf ${AKCS_PACKAGE_NAME}.tar.gz
    chmod -R 755 ${AKCS_PACKAGE_ROOT}
    tar zcvf ${AKCS_PACKAGE_NAME}.tar.gz ${AKCS_PACKAGE_NAME}
    echo "${AKCS_PACKAGE_ROOT}.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSBASE || exit 1
	cmake ./
    make clean
    
	cd $AKCS_SRC_BIN_DIR || exit 1
	cmake ./
	make clean
}
print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean application, eg : $0 clean "
    echo "  $0 build ---  build application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac

