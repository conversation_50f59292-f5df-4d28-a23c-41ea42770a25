#include <stdio.h>
#include "util.h"
#include "storage_util.h"
#include "AkcsBussiness.h"
#include "AkcsMonitor.h"
#include "AkLogging.h"
#include "encrypt/Md5.h"
#include "encrypt/AES256.h"
#include "MetricService.h"

static const char kVideoRecordEncryptKey[] = "Akuvox55069013Akuvox";
static const char KFtpDataDir[] = "/usr/local/akcs/csstorage/ftp/data";

namespace csstorage
{
namespace common
{

static const char* abnormal_file_prefixes[] = {"aws_sdk", "ftp_test"}; 

void TruncFtpFileIPInfo(const std::string &file_name, std::string &ftp_client_ip, std::string &original_file_name)
{
    std::size_t pos = file_name.find("-IP-");
    if(pos != std::string::npos)
    {
        std::string tmp_file_name = file_name.substr(0, pos);
        ftp_client_ip = file_name.substr(pos + 4);//跳过 "-IP-",获取IP
        std::size_t pos_ip = ftp_client_ip.find("::ffff");
        if (pos_ip != std::string::npos)
        {
            ftp_client_ip = ftp_client_ip.substr(pos_ip + 7);//跳过 "::ffff:"
        }
        original_file_name = tmp_file_name;
    }
    else
    {
        original_file_name = file_name;
    }
}

void AddBussiness(const std::string &bussiness, const std::string &key)
{
    AK_LOG_WARN << "csstorage ftp file name is invalid, bussiness is " << bussiness << ", key is " << key;
    AKCS::Singleton<BussinessLimit>::instance().AddBussiness(bussiness, key);
    MetricService* metric_service = MetricService::GetInstance();
    if(metric_service) {
        metric_service->AddValue("csstorage_ftp_filename_invalid_count", 1);
    }

}

void AttackedCallback(const std::string& bussiness, const std::string& key)
{
    AK_LOG_WARN << "there is one attack happens, iptables input drop,bussiness is " << bussiness <<", ip is " << key;
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("csstorage", key);
}

int CheckOneFileMd5(const std::string& file, const std::string& mac_or_uuid, const std::string& time_stamp, const std::string& file_name_md5, FILE_TYPE file_type)
{
    int i = 0, sleep_time = 1, try_times = file_type;

    do
    {
        std::string file_md5 = akuvox_encrypt::MD5::GetFileMD5(file);
        std::string temp_string = "ak_ftp:";
        temp_string += mac_or_uuid;
        temp_string += ":";
        temp_string += time_stamp;
        temp_string += ":";
        temp_string += file_md5;
        std::string true_token_md5 = akuvox_encrypt::MD5(temp_string).toStr();
        if (file_name_md5 == true_token_md5)
        {
            return 0;
        }
        ++i;
        if (i == try_times)
        {
            AK_LOG_WARN << "True file_md5 is " << true_token_md5 << ",but device give token md5 is " << file_name_md5 
                << " mismatch error,file md5 is " << file_md5 << " Skip this file.";
            
            return -1;
        }
        sleep_time = 2 * sleep_time;
        sleep(sleep_time);
    } while (1);
    
    return -1;
}

std::string GetFileNameMd5(const std::string &file_name)
{
    size_t found =  file_name.find_first_of('.');
    if (found == std::string::npos)
    {
        return "";
    }

    return file_name.substr(0, found);
}

bool CheckIsMotionPic(const std::string &filename)
{
    if (filename.find("MD") != std::string::npos || filename.find("MotionDev") != std::string::npos)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool IsPicFile(const std::string &file_name)
{
    size_t found =  file_name.find_last_of('.');
    if (found == std::string::npos)
    {
        return false;
    }

    std::string suffix = file_name.substr(found + 1);
    if (suffix == "jpg" || suffix == "png" || suffix == "jpeg")
    {
        return true;
    }

    return false;
}

bool IsTarFile(const std::string &file_name)
{
    if (strstr(file_name.c_str(), ".tar") != NULL)
    {
        return true;
    }
    return false;
}

bool IsWavFile(const std::string &file_name)
{
    size_t found =  file_name.find_last_of('.');
    if (found == std::string::npos)
    {
        return false;
    }

    std::string suffix = file_name.substr(found+1);
    if (suffix == "wav")
    {
        return true;
    }

    return false;
}

bool IsVideoFile(const std::string &file_name)
{
    size_t found =  file_name.find_last_of('.');
    if (found == std::string::npos)
    {
        return false;
    }

    std::string suffix = file_name.substr(found+1);
    if (suffix == "mp4")
    {
        return true;
    }
    return false;
}

bool IsAbnormalFile(const std::string& file_name) 
{
    for (const auto& prefix : abnormal_file_prefixes) 
    {
        std::string prefix_str(prefix);
        //特定前缀的判断为异常文件
        if (file_name.compare(0, prefix_str.size(), prefix_str) == 0) 
        {
            return true;
        }
    }
    return false;
}

int GetFileSize(const std::string& file_name)
{
    int file_size = 0;
    FILE* fp = fopen(file_name.c_str(), "r");
    if (fp == nullptr)
    {
        return -1;
    }
    fseek(fp, 0L, SEEK_END);
    file_size = ftell(fp);
    fclose(fp);
    return file_size;
}

bool IsMotionFile(const std::string& file_name)
{
    if (std::string::npos != file_name.find("_MD") || std::string::npos != file_name.find("_MotionDev") || std::string::npos != file_name.find("-MRC"))
    {
        return true;
    }
    return false;
}

bool IsTemperatureFile(const std::string& file_name)
{
    if (std::string::npos != file_name.find("TD") || std::string::npos != file_name.find("TempDev"))
    {
        return true;
    }
    return false;
}

int EncryptVideoFile(const std::string& uuid, const std::string& filename)
{
    char aes_encrypt_key_v1[64] = {0};
    std::string aes_encrypt_key = uuid + std::string(kVideoRecordEncryptKey);
    Snprintf(aes_encrypt_key_v1, sizeof(aes_encrypt_key_v1), aes_encrypt_key.c_str());

    std::string filepath = std::string(KFtpDataDir) + "/" + filename;
    AK_LOG_INFO << "Encrypt filepath = " << filepath << ", aes_encrypt_key_v1 = " << aes_encrypt_key;
    return FileAESEncrypt(filepath.c_str(), aes_encrypt_key_v1, filepath.c_str());
}

} //common
} //csstorage

