#ifndef __STORAGE_UTIL_INCLUDED__
#define __STORAGE_UTIL_INCLUDED__

#include <string>
#include "AkcsCommonDef.h"
#include <stdio.h>

namespace csstorage
{
namespace common
{

//截取文件上传后,vsftpd在文件名后面携带的ftp客户端IP地址
void TruncFtpFileIPInfo(const std::string &file_name, std::string &ftp_client_ip, std::string &original_file_name);
void AddBussiness(const std::string &bussiness, const std::string &key);
void AttackedCallback(const std::string& bussiness, const std::string& key);
int CheckOneFileMd5(const std::string& file, const std::string& mac_or_uuid, const std::string& time_stamp, const std::string& file_name_md5, FILE_TYPE file_type);
std::string GetFileNameMd5(const std::string &file_name);
bool CheckIsMotionPic(const std::string &file_name);
bool IsPicFile(const std::string &file_name);
bool IsTarFile(const std::string &file_name);
bool IsWavFile(const std::string &file_name);
bool IsMotionFile(const std::string &file_name);
bool IsVideoFile(const std::string &file_name);
bool IsTemperatureFile(const std::string& file_name);
int GetFileSize(const std::string& file_name);
int EncryptVideoFile(const std::string& uuid, const std::string& filename);
bool IsAbnormalFile(const std::string& file_name);

} //common
} //csstorage

#endif//__STORAGE_UTIL_INCLUDED__

