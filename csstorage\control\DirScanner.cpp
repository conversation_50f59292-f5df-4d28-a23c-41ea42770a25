#include "FileProcessor.h"
#include <dirent.h>
#include <sys/stat.h>
#include "AkLogging.h"
#include <unistd.h>
#include "common/storage_util.h"
#include "FileProcessControl.h"
#include "DirScanner.h"
#include "storage_ser.h"
#include "redis/SafeCacheConn.h"
#include "AkcsCommonDef.h"
#include "FileCacheManager.h"
#include "util.h"

extern const char *g_redis_db_backend_limiting;

void CheckDirAbnormalFile(const char* dir)
{
    AK_LOG_INFO << "entry->CheckDirEmptyFile,dir: " << dir;

    DIR* dp;
    struct dirent* entry;
    struct stat statbuf;
    if ((dp = ::opendir(dir)) == nullptr)
    {
        AK_LOG_FATAL << "access to " << dir << " failed";
        return;
    }

    while ((entry = ::readdir(dp)) != nullptr)
    {
        //绝对路径
        std::string file_path = std::string(dir) + "/" + entry->d_name;

        lstat(file_path.c_str(), &statbuf);
        if (S_ISDIR(statbuf.st_mode))
        {
             continue;
        }
        else
        {
            int file_size = 0;
            FILE* fp = fopen(file_path.c_str(), "r");
            if (!fp)
            {
              continue;
            }
            fseek(fp, 0L, SEEK_END);
            file_size = ftell(fp);
            fclose(fp);

            time_t time_now = ::time(nullptr);
            time_t time_file_modiofy = statbuf.st_mtime;

            // 文件距上次修改时间超过十分钟
            if ((time_now > time_file_modiofy) && (time_now - time_file_modiofy > 10 * 60))
            {
                if (file_size == 0)
                {
                    // 空文件直接删除
                    AK_LOG_WARN << "there is one dev's file [" << entry->d_name << "] need to be deleted due to no data long time";
                    ::remove(file_path.c_str());
                }
                else
                {
                    // 非空文件移动到相应目录下进行上传
                    std::string dst_path;
                    if (strcmp(vsftpd_upload_dir, dir) == 0)
                    {
                        dst_path = std::string(csstorage_data_dir) + "/" + entry->d_name;
                        ::rename(file_path.c_str(), dst_path.c_str());
                    }
                    else if (strcmp(vsftpd_upload_offlinelog_dir, dir) == 0)
                    {
                        dst_path = std::string(csstorage_offline_dir) + "/" + entry->d_name;
                        ::rename(file_path.c_str(), dst_path.c_str());
                    }
                    AK_LOG_WARN << "there is one dev's file [" << entry->d_name << "] long time no deal, move to handle path";
                }
           }
        }
    }
    ::closedir(dp);
}

void FtpDirScanner::ScanStorageFtpDataDir()
{
    bool is_sleep = false;
    while (!is_sleep)
    {
        DIR* dp;
        struct dirent* entry;
        struct stat statbuf;
        if ((dp = ::opendir(csstorage_data_dir)) == nullptr)
        {
            AK_LOG_FATAL << "access to csstorage data dir failed.";
            return;
        }
        ::chdir(csstorage_data_dir); //一定要转移到这里来,否则文件会读取失败,注意进程coredump时,core文件会产生在这个路径下

        bool has_pic_file = false;
        
        while ((entry = ::readdir(dp)) != nullptr)
        {
            //10分钟检测一次异常文件
            time_t check_time_now = ::time(nullptr);
            if (check_time_now - check_file_time_ > 10 * 60)
            {
                check_file_time_ = check_time_now;
                CheckDirAbnormalFile(vsftpd_upload_dir);
            }

            lstat(entry->d_name, &statbuf);
            if (S_ISDIR(statbuf.st_mode))
            {
                //文件类型错误
                continue;
            }
            else if (csstorage::common::IsAbnormalFile(entry->d_name))
            {
                AK_LOG_WARN << "delete abnoral file:" << entry->d_name;
                ::remove(entry->d_name);
            }
            else
            {
                int file_size = csstorage::common::GetFileSize(entry->d_name);
                if (file_size > 0) //确保文件上传完
                {
                    std::string ftp_client_ip;
                    std::string original_file_name;//去掉-IP-XXX,获取原始文件名
                    csstorage::common::TruncFtpFileIPInfo(entry->d_name, ftp_client_ip, original_file_name);
                    //消息投递 当前先用相同的进行处理
                    if (csstorage::common::IsPicFile(original_file_name))
                    {
                        //避免将小图重复进行上传
                        if (IsSmallCutFile(original_file_name))
                        {
                            continue;
                        }
                        //限流检查，用于过滤motion限流的图片
                        if (IsLimitingMotionPic(original_file_name))
                        {
                            ::remove(entry->d_name); 
                            AK_LOG_INFO << "motion limiting pic. need to drop it. file name=" << original_file_name;
                            continue;
                        }
                        //用original_file_name进行缓存，保证rename后在缓存中能检测到
                        if (GetFileCacheManagerInstace()->PicCacheCheckAndAdd(original_file_name))
                        {
                            //防止因时间窗口导致处理了已经被删除的文件
                            if (fileExist(entry->d_name))
                            {
                                AK_LOG_INFO << "prepare to process pic file : " << entry->d_name;
                                GetFileProcessControlInstace()->AddFileProcessTask(entry->d_name);
                                has_pic_file = true;
                            }
                            else
                            {
                                GetFileCacheManagerInstace()->RemovePicCache(original_file_name);
                            }
                        }
                    }
                    else if (csstorage::common::IsWavFile(original_file_name))
                    {
                        if (GetFileCacheManagerInstace()->WavCacheCheckAndAdd(original_file_name))
                        {
                            //防止因时间窗口导致处理了已经被删除的文件
                            if (fileExist(entry->d_name))
                            {
                                AK_LOG_INFO << "prepare to process wav file : " << entry->d_name;
                                GetFileProcessControlInstace()->AddFileProcessTask(entry->d_name);
                            }
                            else
                            {
                                GetFileCacheManagerInstace()->RemoveWavCache(original_file_name);
                            }
                        }
                    }
                    else if (csstorage::common::IsVideoFile(original_file_name))
                    {
                        if (GetFileCacheManagerInstace()->VideoCacheCheckAndAdd(original_file_name))
                        {
                            //防止因时间窗口导致处理了已经被删除的文件
                            if (fileExist(entry->d_name))
                            {
                                AK_LOG_INFO << "prepare to process video file : " << entry->d_name;
                                GetFileProcessControlInstace()->AddFileProcessTask(entry->d_name);
                            }
                            else
                            {
                                GetFileCacheManagerInstace()->RemoveVideoCache(original_file_name);
                            }
                        }
                    }
                }
                else //file_size = 0代表上传文件动作未完成 最多等待两分钟 超时的将文件删除
                {
                    time_t time_file_modiofy = statbuf.st_mtime;
                    time_t time_now;
                    time_now = ::time(nullptr);
                    //平台最多等待两分钟，如果两分钟内，设备始终没有上传图片数据,就删除临时图片文件
                    if ((time_now > time_file_modiofy) && (time_now - time_file_modiofy > 2 * 60))
                    {

                        AK_LOG_WARN << "there is one dev's capture pic [" << entry->d_name << "] need to be deleted due to no data long time";
                        ::remove(entry->d_name);
                    }
                    else
                    {
                        AK_LOG_INFO << "there is one dev's capture pic [" << entry->d_name << "] need to be uploaded to storage srv later";
                    }
                }
            }
        }
        
        ::closedir(dp);
        if (!has_pic_file)
        {
            AK_LOG_INFO << "dir file is empty. sleep 3.";
            sleep(3);
        }
    }
}

bool FtpDirScanner::IsSmallCutFile(const std::string& file_name)
{
    if (file_name.find("_cut.jpg") == std::string::npos)
    {
        return false;
    }
    return true;
}

bool FtpDirScanner::IsLimitingMotionPic(const std::string& file_name)
{
    SafeCacheConn cache_conn(g_redis_db_backend_limiting);
    if (cache_conn.isConnect() && cache_conn.isExists(file_name))
    {
        cache_conn.del(file_name);
        return true;
    }
    return false;
}
