#ifndef _STORAGE_DIR_SCANNER_H_
#define _STORAGE_DIR_SCANNER_H_

#include "lru_cache/LRUCache.hpp"
#include <string>

static const char csstorage_offline_dir[] = "/usr/local/akcs/csstorage/ftp/data/offlinelog"; //临时压缩文件存放路径
static const char vsftpd_upload_dir[] = "/usr/local/akcs/csstorage/ftp"; //异常检测目录
static const char vsftpd_upload_offlinelog_dir[] = "/usr/local/akcs/csstorage/ftp/offlinelog";

class FtpDirScanner
{
public:
    FtpDirScanner()
    {
        check_file_time_ = ::time(nullptr);
    }
    void ScanStorageFtpDataDir();

private:
    bool IsSmallCutFile(const std::string& file_name);
    bool IsLimitingMotionPic(const std::string& file_name);
    time_t check_file_time_;
};

void CheckDirAbnormalFile(const char* dir);

#endif //_STORAGE_DIR_SCANNER_H_
