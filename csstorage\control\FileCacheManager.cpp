#include "FileCacheManager.h"
#include "AkLogging.h"

CFileCacheManager* CFileCacheManager::instance_ = nullptr;

void CFileCacheManager::InitPicCache(size_t pic_cache_size)
{
    pic_cache_.InitSize(pic_cache_size);
}

void CFileCacheManager::InitWavCache(size_t wav_cache_size)
{
    wav_cache_.InitSize(wav_cache_size);
}

void CFileCacheManager::InitVideoCache(size_t video_cache_size)
{
    video_cache_.InitSize(video_cache_size);
}

bool CFileCacheManager::PicCacheCheckAndAdd(const std::string& key)
{
    return pic_cache_.CacheCheckAndAdd(key, true);
}

bool CFileCacheManager::WavCacheCheckAndAdd(const std::string& key)
{
    return wav_cache_.CacheCheckAndAdd(key, true);
}

bool CFileCacheManager::VideoCacheCheckAndAdd(const std::string& key)
{
    bool ret = video_cache_.CacheCheckAndAdd(key, true);
    AK_LOG_INFO << "VideoCacheCheckAndAdd key = " << key << ", ret = " << ret;

    return ret;
}

void CFileCacheManager::RemovePicCache(const std::string& key)
{
    return pic_cache_.RemoveCache(key);
}

void CFileCacheManager::RemoveWavCache(const std::string& key)
{
    return wav_cache_.RemoveCache(key);
}

void CFileCacheManager::RemoveVideoCache(const std::string& key)
{
    return video_cache_.RemoveCache(key);
}

int CFileCacheManager::GetPicCacheSize()
{
    return pic_cache_.GetCacheSize();
}

int CFileCacheManager::GetWavCacheSize()
{
    return wav_cache_.GetCacheSize();
}

int CFileCacheManager::GetVideoCacheSize()
{
    return video_cache_.GetCacheSize();
}

CFileCacheManager* CFileCacheManager::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new CFileCacheManager();
    }

    return instance_;
}

CFileCacheManager* GetFileCacheManagerInstace()
{
    return CFileCacheManager::GetInstance();
}

std::string CFileCacheManager::GetPicCacheList()
{
    return pic_cache_.GetCacheList();
}
