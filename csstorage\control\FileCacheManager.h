#ifndef _STORAGE_FILE_CACHE_MANAGER_H_
#define _STORAGE_FILE_CACHE_MANAGER_H_

#include <string>
#include "lru_cache/LRUCache.hpp"

class CFileCacheManager
{
public:
    CFileCacheManager() {};
    void InitPicCache(size_t pic_cache_size); //缓存大小
    void InitWavCache(size_t wav_cache_size); //缓存大小    
    void InitVideoCache(size_t video_cache_size); //缓存大小
    bool PicCacheCheckAndAdd(const std::string& key);
    bool WavCacheCheckAndAdd(const std::string& key);
    bool VideoCacheCheckAndAdd(const std::string& key);
    void RemovePicCache(const std::string& key);
    void RemoveWavCache(const std::string& key);
    void RemoveVideoCache(const std::string& key);
    int GetPicCacheSize();
    int GetWavCacheSize();
    int GetVideoCacheSize();
    std::string GetPicCacheList();
    static CFileCacheManager* GetInstance();

private:
    akcs_base_lru_cache::LRUCache<std::string, bool> pic_cache_;
    akcs_base_lru_cache::LRUCache<std::string, bool> wav_cache_;
    akcs_base_lru_cache::LRUCache<std::string, bool> video_cache_;
    static CFileCacheManager* instance_;
};

CFileCacheManager* GetFileCacheManagerInstace();

#endif
