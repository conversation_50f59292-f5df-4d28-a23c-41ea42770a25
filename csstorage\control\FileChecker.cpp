#include "FileChecker.h"
#include "AkLogging.h"
#include "common/storage_util.h"
#include "AkcsCommonDef.h"
#include "personal_capture.h"
#include "storage_mq.h"
#include "storage_ser.h"
#include "FileCacheManager.h"

int FileChecker::CheckFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info)
{
    switch (file_type_)
    {
        case FileType::PIC_FILE :
            return CheckPicFile(full_file_name, ftp_client_ip, file_name_related_info);
        
        case FileType::WAV_FILE :
            return CheckWavFile(full_file_name, ftp_client_ip, file_name_related_info);
        
        case FileType::VIDEO_FILE :
            return CheckVideoRecordFile(full_file_name, ftp_client_ip, file_name_related_info);
            
        default :
            AK_LOG_WARN << "file type not support. type=" << (int) file_name_related_info.file_type;
            return -1;
    }
}

int FileChecker::CheckPicFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info)
{
    std::string origin_file_name(file_name_related_info.origin_file_name);
    //从图片中取出设备mac,eg:0C1100000001-1513232303_0.jpg
    std::vector<std::string> contents;
    SplitString(origin_file_name, "-", contents);
    size_t jpg_pos = origin_file_name.find(".jpg");

    std::string cut_mac_info;
    std::string mac_or_uuid;
    int is_voice_pic = 0;
    if (contents.size() > 2) //用uuid新版本 uuid里面会包含-号
    {
        AK_LOG_INFO << "have uuid.";

        cut_mac_info = contents[contents.size() - 1];
        size_t pos = origin_file_name.find(cut_mac_info);
        if(pos != std::string::npos)
        {
            mac_or_uuid = origin_file_name.substr(0, pos - 1);
        }
        else
        {
            mac_or_uuid = contents[0];
        }
    }
    else if (contents.size() == 2)
    {
        AK_LOG_INFO << "not have uuid";
        cut_mac_info = contents[1];
        mac_or_uuid = contents[0];
    }
    else if (contents.size() != 2 || std::string::npos == jpg_pos)
    {
        AK_LOG_WARN << "The name of dev's capture pic is invalid or Md5 check error, [" << origin_file_name << "], full file is " << full_file_name << " ip is " << ftp_client_ip;
        //删除掉本地文件
        ::remove(origin_file_name.c_str());
        GetFileCacheManagerInstace()->RemovePicCache(origin_file_name);
        //进入攻击的防护流程中
        csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
        return -1;
    }
    
    if (mac_or_uuid.size() > 20)//安全整改校验
    {
        AK_LOG_INFO << "safe version. mac_or_uuid:" << mac_or_uuid;
        int project;
        if (!Md5ValueCheckForNewVersion(cut_mac_info, mac_or_uuid, is_voice_pic, origin_file_name)
            || GetPersonalCaptureInstance()->GetMacByUUID(mac_or_uuid, mac_or_uuid, project) != 0)
        {
            AK_LOG_WARN << "The name of dev's capture pic is invalid or Md5 check error, [" << origin_file_name << "], full file is " << full_file_name << " ip is " << ftp_client_ip;
            //删除掉本地文件
            ::remove(origin_file_name.c_str());
            GetFileCacheManagerInstace()->RemovePicCache(origin_file_name);
            //进入攻击的防护流程中
            csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
            return -1;
        }
    }
    else if (true != Md5ValueCheck(cut_mac_info, mac_or_uuid, is_voice_pic))
    {
        AK_LOG_WARN << "The name of dev's capture pic is invalid or Md5 check error, [" << origin_file_name << "], full file is " << full_file_name << " ip is " << ftp_client_ip;
        //删除掉本地文件
        ::remove(origin_file_name.c_str());
        GetFileCacheManagerInstace()->RemovePicCache(origin_file_name);
        //进入攻击的防护流程中
        csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
        return -1;                
    }
    file_name_related_info.is_voice_pic = (is_voice_pic ? true : false);
    Snprintf(file_name_related_info.mac_or_uuid, sizeof(file_name_related_info.mac_or_uuid), mac_or_uuid.c_str());
    Snprintf(file_name_related_info.origin_file_name, sizeof(file_name_related_info.origin_file_name), origin_file_name.c_str());
    return 0;
}

int FileChecker::CheckWavFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info)
{
    std::string origin_file_name(file_name_related_info.origin_file_name);
    size_t wav_pos = origin_file_name.find(".wav");

    std::vector<std::string> contents;
    SplitString(origin_file_name, "-", contents);
    
    //mac_wav = "A62333230313-1678784633-VoiceDev-515e73f90e03fd43a1c7e428e5b326a3.wav";
    //uuid_wav = "na-04a181e5399f11eda88f00163e047e78-1678784633-VD-515e73f90e03fd43a1c7e428e5b326a3.wav"; 新版本用uuid, uuid里面会包含一个-号
    if (contents.size() != 4 && contents.size() != 5) 
    {
        AK_LOG_WARN << "the name of dev's voice msg is wrong, [" << origin_file_name << "]";
        ::remove(origin_file_name.c_str());
        GetFileCacheManagerInstace()->RemoveWavCache(origin_file_name);
        return -1;
    }

    std::string mac;
    std::string uuid;
    std::string time_stamp;
    std::string file_name_md5;
    if (contents.size() == 4)
    {
        mac = contents[0];
        time_stamp = contents[1];
        file_name_md5 = csstorage::common::GetFileNameMd5(contents[3]);
    }
    else if (contents.size() == 5)
    {
        uuid = contents[0] + "-" + contents[1];
        time_stamp = contents[2];
        file_name_md5 = csstorage::common::GetFileNameMd5(contents[4]);
    }
    int project_type = project::NONE;
    if (std::string::npos != wav_pos)
    {
        if (contents.size() == 5)//安全整改校验uuid版本
        {
            if (0 != csstorage::common::CheckOneFileMd5(origin_file_name, uuid, time_stamp, file_name_md5, FILE_TYPE_WAV) 
                || 0 != GetPersonalCaptureInstance()->GetMacByUUID(uuid, mac, project_type))
            {
                AK_LOG_WARN << "The name of dev's voice msg Md5 check wrong, [" << full_file_name << "], origi file is " << origin_file_name << " ip is " << ftp_client_ip;
                //删除掉本地文件
                ::remove(origin_file_name.c_str());
                GetFileCacheManagerInstace()->RemoveWavCache(origin_file_name);
                //进入攻击的防护流程中
                csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
                SendVoiceFileAckMsg(mac, origin_file_name, (int)VoiceMsgAckResult::ACK_RESULT_FAILED, project_type);
                return -1;
            }
        }
        else if (contents.size() == 4)
        {
            if(0 != csstorage::common::CheckOneFileMd5(origin_file_name, mac, time_stamp, file_name_md5, FILE_TYPE_WAV))
            {
                SendVoiceFileAckMsg(mac, origin_file_name, (int)VoiceMsgAckResult::ACK_RESULT_FAILED, project_type);
                AK_LOG_WARN << "The name of dev's voice msg Md5 check wrong, [" << full_file_name << "], origi file is " << origin_file_name << " ip is " << ftp_client_ip;
                ::remove(origin_file_name.c_str());
                GetFileCacheManagerInstace()->RemoveWavCache(origin_file_name);
                return -1;
            }
            GetPersonalCaptureInstance()->GetMacProject(mac, project_type);
        }
    }
    Snprintf(file_name_related_info.mac_or_uuid, sizeof(file_name_related_info.mac_or_uuid), mac.c_str());
    file_name_related_info.project_type = project_type;
    return 0;
}

int FileChecker::CheckVideoRecordFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info)
{
    std::string origin_file_name(file_name_related_info.origin_file_name);
        
    std::vector<std::string> contents;
    SplitString(origin_file_name, "-", contents);
    
    // filename : "na-04a181e5399f11eda88f00163e047e78-1616054761-DRC-4a2c83e2af7e5d4ea8e469328b2db0d9.mp4";
    if (contents.size() != 5) 
    {
        AK_LOG_WARN << "the name of dev's video file is wrong, [" << origin_file_name << "]";
        ::remove(origin_file_name.c_str());
        GetFileCacheManagerInstace()->RemoveVideoCache(origin_file_name);
        return -1;
    }
    
    std::string mac;
    std::string timestamp = contents[2];
    std::string uuid = contents[0] + "-" + contents[1];
    std::string file_name_md5 = csstorage::common::GetFileNameMd5(contents[4]);

    int project_type = project::NONE;
    if (0 != csstorage::common::CheckOneFileMd5(origin_file_name, uuid, timestamp, file_name_md5, FILE_TYPE_VIDEO) 
     || 0 != GetPersonalCaptureInstance()->GetMacByUUID(uuid, mac, project_type))
    {
        AK_LOG_WARN << "The name of dev's video Md5 check wrong, [" << full_file_name << "], origin file = " << origin_file_name << ", ip = " << ftp_client_ip;
        
        ::remove(origin_file_name.c_str());
        GetFileCacheManagerInstace()->RemoveVideoCache(origin_file_name);

        csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
        
        SendVideoFileAckMsg(mac, origin_file_name, (int)VoiceMsgAckResult::ACK_RESULT_FAILED, project_type);
        return -1;
    }
    
    Snprintf(file_name_related_info.mac_or_uuid, sizeof(file_name_related_info.mac_or_uuid), mac.c_str());
    file_name_related_info.project_type = project_type;

    // 对文件进行加密
    csstorage::common::EncryptVideoFile(uuid, origin_file_name);
    return 0;
}

