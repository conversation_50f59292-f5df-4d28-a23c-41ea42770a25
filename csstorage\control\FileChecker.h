#ifndef _STORAGE_FILE_CHECKER_H_
#define _STORAGE_FILE_CHECKER_H_

#include <string>
#include <memory>
#include "util.h"

static const char FTP_FILE_INVALID[] = "csstorage_ftp_file_name_invalid";

enum class FileType
{
    NONE = 0,
    WAV_FILE = 1,
    PIC_FILE = 2,
    VIDEO_FILE = 3
};

//文件名相关信息
typedef struct FileNameRelatedInfo_T
{
    char dev_upload_file_name[1024];
    char origin_file_name[1024];
    char mac_or_uuid[256];
    bool is_voice_pic;
    int project_type;
    FileType file_type;
    char project_uuid[64];
    FileNameRelatedInfo_T()
    {
        ::memset(this, 0, sizeof(*this));
    }
    FileNameRelatedInfo_T(const std::string& upload_file_name, const std::string& file_name)
    {
        ::memset(this, 0, sizeof(*this));
        Snprintf(this->dev_upload_file_name, sizeof(this->dev_upload_file_name), upload_file_name.c_str());
        Snprintf(this->origin_file_name, sizeof(this->origin_file_name), file_name.c_str());
    }
} FileNameRelatedInfo;

class FileChecker
{
public:
    FileChecker(FileType file_type) : file_type_(file_type) {}
    int CheckFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info);

private:
    FileType file_type_;
    int CheckPicFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info);
    int CheckWavFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info);
    int CheckVideoRecordFile(const std::string& full_file_name, const std::string& ftp_client_ip, FileNameRelatedInfo& file_name_related_info);
};


#endif
