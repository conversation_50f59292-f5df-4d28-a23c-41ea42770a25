#include "FileProcessControl.h"
#include "FileProcessor.h"
#include "AkLogging.h"
#include "storage_mng.h"
#include "storage_s3.h"
#include "ThreadLocalSingleton.h"
#include "gid/SnowFlakeGid.h"

static const char csstorage_fdfs_conf_file[] = "/usr/local/akcs/csstorage/conf/csstorage_fdfs.conf"; //fdfs客户端配置文件
static const char csstorage_data_dir[] = "/usr/local/akcs/csstorage/ftp/data"; //临时图片存放路径
static const char csstorage_retry_data_dir[] = "/usr/local/akcs/csstorage/ftp/data/retry"; //重传图片存放路径

CFileProcessControl* CFileProcessControl::instance_ = nullptr;

CFileProcessControl::~CFileProcessControl()
{
    stop_ = true;
    for (auto& queue : queues_)
    {
        queue->cv_.notify_all();
    }
    for (auto& thread : threads_)
    {
        if (thread.joinable())
        {
            thread.join();
        }
    }
}

void CFileProcessControl::Init(int thread_num)
{
    thread_num_ = thread_num;
    queues_.resize(thread_num);
    for (auto& queue : queues_)
    {
        queue.reset(new FileQueue());
    }        
    for (size_t i = 0; i < queues_.size(); ++i)
    {
        threads_.emplace_back(&CFileProcessControl::ProcessFile, this, i);
    }
    AK_LOG_INFO << "NormalFileProcess Thread Start Success,thread_num=" << thread_num;
    return;
}

//多线程消费者
int CFileProcessControl::ProcessFile(int queue_index) noexcept
{
    // 设置glog线程traceid
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);
    StorageS3Mng* storage_s3mng_ptr  = new StorageS3Mng(csstorage_data_dir, csstorage_retry_data_dir);
    CStorageMng* storage_fdfs_mng_ptr  = new CStorageMng(csstorage_fdfs_conf_file);

    std::deque<std::string> tmp_deque;
    while (!stop_)
    {
        {
            std::unique_lock<std::mutex> lock(queues_[queue_index]->mutex_);
            while (queues_[queue_index]->data_queue_.empty())
            {
                queues_[queue_index]->cv_.wait(lock, [&] { return !queues_[queue_index]->data_queue_.empty() || stop_; });
            }
            queues_[queue_index]->data_queue_.swap(tmp_deque);
        }         
        while (tmp_deque.size() > 0)
        {
            std::string file = tmp_deque.front();
            tmp_deque.pop_front();
            FileProcessor::HandleFile(file, storage_fdfs_mng_ptr, storage_s3mng_ptr);
        }
    }
    delete storage_s3mng_ptr;
    delete storage_fdfs_mng_ptr;
    return 0;
}

//单线程生产者
int CFileProcessControl::AddFileProcessTask(const std::string& file)
{
    //通过取模进行轮询
    uint32_t queue_index = current_queue_ % queues_.size();
    current_queue_ += 1; //类型设置为uint32_t，溢出是预期行为，无需清空
    {
        std::lock_guard<std::mutex> lock(queues_[queue_index]->mutex_);
        queues_[queue_index]->data_queue_.push_back(file);
    }
    queues_[queue_index]->cv_.notify_one(); //条件变量无需持有锁
    return 0;
}

CFileProcessControl* CFileProcessControl::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new CFileProcessControl();
    }

    return instance_;
}

CFileProcessControl* GetFileProcessControlInstace()
{
    return CFileProcessControl::GetInstance();
}
