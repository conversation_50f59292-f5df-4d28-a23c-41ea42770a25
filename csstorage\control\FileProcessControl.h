#ifndef _STORAGE_NORMAL_FILE_PROCESS_CONTROL_H
#define _STORAGE_NORMAL_FILE_PROCESS_CONTROL_H

#include "FileProcessControl.h"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
#include <vector>
#include <deque>
#include <thread>
#include <atomic>

struct FileQueue {
    std::deque<std::string> data_queue_;
    std::mutex mutex_;
    std::condition_variable cv_;
};

class CFileProcessControl
{
public:
    CFileProcessControl() : stop_(false), current_queue_(0) {}
    ~CFileProcessControl();
    void Init(int thread_num);
    int AddFileProcessTask(const std::string& file);
    int ProcessFile(int queue_index) noexcept;
    static CFileProcessControl* GetInstance();

private:
    std::vector<std::unique_ptr<FileQueue>> queues_;
    bool stop_;
    uint32_t current_queue_;
    std::vector<std::thread> threads_;
    int thread_num_;
    static CFileProcessControl* instance_;
};

CFileProcessControl* GetFileProcessControlInstace();

#endif