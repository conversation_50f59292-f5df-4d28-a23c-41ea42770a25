#include "FileProcessor.h"
#include "common/storage_util.h"
#include "AkLogging.h"
#include "dbinterface/ProjectInfo.h"
#include "storage_ser.h"
#include "upload_retry_control.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "storage_dao.h"
#include "storage_s3.h"
#include "FileChecker.h"
#include "storage_mq.h"
#include "AkcsCommonDef.h"
#include "UpdateUrlFailControl.h"
#include "model/personal_capture.h"
#include "FileCacheManager.h"

extern AKCS_CONF gstAKCSConf;
extern CStorageMng* g_storage_mng_ptr;

static int GetFileType(const std::string& file)
{
    std::string original_file_name;
    std::string ftp_client_ip;
    csstorage::common::TruncFtpFileIPInfo(file, ftp_client_ip, original_file_name);
    if (csstorage::common::IsPicFile(original_file_name))
    {
        return (int)FileType::PIC_FILE;
    }
    else if (csstorage::common::IsWavFile(original_file_name))
    {
        return (int)FileType::WAV_FILE;
    }
    else if (csstorage::common::IsVideoFile(original_file_name))
    {
        return (int)FileType::VIDEO_FILE;
    }
    return (int)FileType::NONE;
}


void FileProcessor::HandleFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr)
{
    int file_type = GetFileType(file);
    switch (file_type)
    {
        case (int)FileType::PIC_FILE:
            HandlePicFile(file, fdfs_mng_ptr, storage_s3mng_ptr);
        break;
        
        case (int)FileType::WAV_FILE:
            HandleWavFile(file, fdfs_mng_ptr, storage_s3mng_ptr);
        break;

        case (int)FileType::VIDEO_FILE:
            HandleVideoFile(file, fdfs_mng_ptr, storage_s3mng_ptr);
        break;
        
        default:
            AK_LOG_WARN << "csstorage file type not support. type=" << file_type;
        break;
    }
    return;
}

void FileProcessor::HandleWavFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr)
{
    AK_LOG_INFO << "begin to upload dev's voice msg [" << file << "] to storage srv";

    std::string ftp_client_ip;
    std::string original_file_name;//去掉-IP-XXX,获取原始文件名
    csstorage::common::TruncFtpFileIPInfo(file, ftp_client_ip, original_file_name);
    ::rename(file.c_str(), original_file_name.c_str());

    //解析校验文件名相关信息
    FileNameRelatedInfo file_name_related_info(file, original_file_name);
    FileChecker file_checker(FileType::WAV_FILE);
    if (0 != file_checker.CheckFile(file, ftp_client_ip, file_name_related_info))
    {
        //校验异常 不进行后续处理
        AK_LOG_WARN << "file name check failed. origin file name:" << file;
        GetFileCacheManagerInstace()->RemoveWavCache(original_file_name);
        return;
    }

    int file_size = 0;
    if ((file_size = GetFileSize(original_file_name)) <= 0)
    {
        AK_LOG_WARN << "file size is 0. no need to handle. file name:" << file;
        GetFileCacheManagerInstace()->RemoveWavCache(original_file_name);
        return;
    }

    std::string file_url;
    std::string file_path;
    if (0 != UploadWavFile(file_name_related_info, fdfs_mng_ptr, storage_s3mng_ptr, file_url))
    {
        //上传失败 不进行后续处理
        GetFileCacheManagerInstace()->RemoveWavCache(original_file_name); //从缓存中移除，以便后续处理
        return;
    }
    file_path = file_url;

    AK_LOG_INFO << "succeed to upload file,file name is [" << original_file_name << "], remote file url is [" << file_url << "], file size is " << file_size;

    if (0 != dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgFileUrl(file_path, original_file_name))
    {
        //存储文件与设备的对应关系,后面5s之后重试
        time_t timer;
        timer = ::time(nullptr);
        std::pair<std::string, std::string> voice_file_pair(original_file_name, file_path);
        GetUpdateUrlFailControlInstance()->AddVoiceUrlFail(std::make_pair(timer, voice_file_pair));
    }
    //删除掉本地文件
    ::remove(original_file_name.c_str());
    GetFileCacheManagerInstace()->RemoveWavCache(original_file_name);
    SendVoiceFileAckMsg(file_name_related_info.mac_or_uuid, original_file_name, (int)VoiceMsgAckResult::ACK_RESULT_SUCCESS, file_name_related_info.project_type);
}

void FileProcessor::HandlePicFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr)
{
    AK_LOG_INFO << "begin to upload dev's capture pic [" << file << "] to storage srv";

    std::string ftp_client_ip;
    std::string original_file_name;//去掉-IP-XXX,获取原始文件名
    csstorage::common::TruncFtpFileIPInfo(file, ftp_client_ip, original_file_name);
    ::rename(file.c_str(), original_file_name.c_str());
    
    //解析校验文件名相关信息
    FileNameRelatedInfo file_name_related_info(file, original_file_name);
    FileChecker file_checker(FileType::PIC_FILE);
    if (0 != file_checker.CheckFile(file, ftp_client_ip, file_name_related_info))
    {
        //校验异常 不进行后续处理
        AK_LOG_WARN << "file name check failed. origin file name:" << file_name_related_info.dev_upload_file_name;
        GetFileCacheManagerInstace()->RemovePicCache(original_file_name);
        return;
    }

    std::string file_name = original_file_name;
    if (GetFileSize(file_name) <= 0)
    {
        AK_LOG_WARN << "file size is 0. no need to handle. file name:" << original_file_name;
        GetFileCacheManagerInstace()->RemovePicCache(file_name);
        return;
    }

    //日志分片获取
    ProjectInfo log_project;
    std::string log_project_uuid;
    if (!file_name_related_info.is_voice_pic)
    {
        log_project.GetLogCaptureProjectUUID(file_name_related_info.mac_or_uuid, log_project_uuid);
    }

    std::string big_url;
    std::string small_url;
    int ret = UploadImageFile(fdfs_mng_ptr, storage_s3mng_ptr, file_name, big_url, small_url);

    //只要上传大图成功，就删除。防止有设备重复上传大图导致处理不了
    if (ret == UPLOAD_IMAGE_ERROR_CODE::UPLOAD_IMAGE_SUCCESS || ret == UPLOAD_IMAGE_ERROR_CODE::UPLOAD_SMALL_IMAGE_TO_S3_ERROR)
    {
        GetFileCacheManagerInstace()->RemovePicCache(original_file_name);
    }

    //上传s3失败的大图或小图进行重传
    if (!gstAKCSConf.store_fdfs && ret != UPLOAD_IMAGE_ERROR_CODE::UPLOAD_IMAGE_SUCCESS)
    {
        ReUploadS3BigImage(ret, file_name_related_info, log_project_uuid);
        return;
    }
    //更新图片url，失败则投到定时器线程进行处理
    if (0 != UpdatePicUrl(big_url, small_url, file_name_related_info))
    {
        AK_LOG_WARN << "Update pic url failed. handle later. file_name=" << file_name_related_info.origin_file_name;
        if (file_name_related_info.is_voice_pic)
        {                
            //存储文件与设备的对应关系,后面1s之后重试
            time_t timer;
            timer = ::time(nullptr);
            std::pair<std::string, std::string> voice_file_pair(original_file_name, big_url);
            GetUpdateUrlFailControlInstance()->AddVoicePicUrlFail(std::make_pair(timer, voice_file_pair));
        }
        else
        {
            time_t timer;
            timer = ::time(nullptr);
            PicUrlFailMap url_update_map;
            auto file_infos = std::make_tuple(big_url, small_url, ftp_client_ip, file_name_related_info.mac_or_uuid);
            std::pair<std::string, std::tuple<std::string, std::string, std::string, std::string>> file_pair(original_file_name, file_infos);
            GetUpdateUrlFailControlInstance()->AddPicUrlFail(std::make_pair(timer, file_pair));
        }
    }
    return;
}

void FileProcessor::HandleVideoFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr)
{
    AK_LOG_INFO << "begin to upload dev's video record [" << file << "] to storage srv";

    std::string ftp_client_ip;
    std::string original_file_name;
    csstorage::common::TruncFtpFileIPInfo(file, ftp_client_ip, original_file_name);
    ::rename(file.c_str(), original_file_name.c_str());
    
    FileNameRelatedInfo file_name_related_info(file, original_file_name);
    FileChecker file_checker(FileType::VIDEO_FILE);
    if (0 != file_checker.CheckFile(file, ftp_client_ip, file_name_related_info))
    {
        return;
    }

    //日志分片获取
    std::string project_uuid;
    ProjectInfo project_info;
    project_info.GetLogCaptureProjectUUID(file_name_related_info.mac_or_uuid, project_uuid);
    Snprintf(file_name_related_info.project_uuid, sizeof(file_name_related_info.project_uuid), project_uuid.c_str());
    
    std::string file_url;
    if (0 != UploadVideoFile(file_name_related_info, fdfs_mng_ptr, storage_s3mng_ptr, file_url))
    {
        return;
    }
    
    AK_LOG_INFO << "succeed to upload video file, file name is [" << original_file_name << "], remote file url is [" << file_url << "]";

    if (0 != DaoUpdateVideoUrl(file_name_related_info.mac_or_uuid, original_file_name, file_url, project_uuid))
    {
        //存储文件与设备的对应关系,后面5s之后重试
        std::time_t timestamp = std::time(nullptr);
        auto file_infos = std::make_tuple(file_name_related_info.mac_or_uuid, original_file_name, file_url, project_uuid);
        GetUpdateUrlFailControlInstance()->AddVideoUrlFail(std::make_pair(timestamp, file_infos));
    }

    // 删除本地文件
    ::remove(original_file_name.c_str());
    return;
}

void FileProcessor::ReUploadS3BigImage(int err_code, const FileNameRelatedInfo& file_name_related_info, const std::string& log_project_uuid)
{
    UPLOAD_RETRY_FILE_INFO file_info;
    memset(&file_info, 0, sizeof(file_info));

    file_info.error_code = err_code;
    file_info.is_voice_pic = file_name_related_info.is_voice_pic ? 1 : 0;

    Snprintf(file_info.mac, sizeof(file_info.mac), file_name_related_info.mac_or_uuid);
    Snprintf(file_info.filename, sizeof(file_info.filename), file_name_related_info.origin_file_name);
    Snprintf(file_info.project_uuid, sizeof(file_info.project_uuid), log_project_uuid.c_str());

    // 加入到重传队列中
    GetUploadRetryHandlerInstance()->AddReUploadFile(file_info);
    
    AK_LOG_WARN << "upload image filed, add to retry deque, error code: " << err_code << ",filename: " << file_name_related_info.origin_file_name;
}

int FileProcessor::UploadWavFile(const FileNameRelatedInfo& file_name_related_info, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, std::string& file_url)
{
    std::string file(file_name_related_info.origin_file_name);
    std::string mac(file_name_related_info.mac_or_uuid);
    int project_type = file_name_related_info.project_type;

    if (gstAKCSConf.store_fdfs)
    {
        if (fdfs_mng_ptr->UploadFile(file.c_str(), file_url) != 0)
        {
            AK_LOG_WARN << "failed to upload file, file name is [" << file << "]";
            ::remove(file.c_str());
            SendVoiceFileAckMsg(mac, file, (int)VoiceMsgAckResult::ACK_RESULT_FAILED, project_type);
            return -1;
        }
    }
    else
    {
        if (storage_s3mng_ptr->UploadVoiceFile(file, file_url) != 0)
        {
            UPLOAD_RETRY_FILE_INFO fileinfo;
            memset(&fileinfo, 0, sizeof(fileinfo));

            fileinfo.error_code = UPLOAD_VOICE_FILE_TO_S3_ERROR;
            Snprintf(fileinfo.filename, sizeof(fileinfo.filename), file_name_related_info.origin_file_name);
            Snprintf(fileinfo.project_uuid, sizeof(fileinfo.project_uuid), file_name_related_info.project_uuid);

            // 加入到重传队列中
            GetUploadRetryHandlerInstance()->AddReUploadFile(fileinfo);
            
            AK_LOG_WARN << "failed to upload voice file, file name is [" << file << "]";
            ::remove(file.c_str());
            SendVoiceFileAckMsg(mac, file, (int)VoiceMsgAckResult::ACK_RESULT_SUCCESS, project_type);
            return -1;
        }
    }
    return 0;
}

int FileProcessor::UpdatePicUrl(const std::string& big_url, const std::string& small_url, const FileNameRelatedInfo& file_name_related_info)
{
    if (file_name_related_info.is_voice_pic)
    {
        if (0 != dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgPicUrl(big_url, file_name_related_info.origin_file_name))
        {
            return -1;
        }
        return 0;
    }
    else
    {
        ProjectInfo log_project;
        std::string log_project_uuid;
        log_project.GetLogCaptureProjectUUID(file_name_related_info.mac_or_uuid, log_project_uuid); 

        if (DaoUpatePicUrl(file_name_related_info.mac_or_uuid, file_name_related_info.origin_file_name, big_url, small_url, log_project_uuid) != 0)
        {
            return -1;
        }
        return 0;
    }
    return 0;
}

int FileProcessor::UploadVideoFile(const FileNameRelatedInfo& file_name_related_info, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, std::string& file_url)
{
    std::string file(file_name_related_info.origin_file_name);

    if (gstAKCSConf.store_fdfs)
    {
        if (fdfs_mng_ptr->UploadFile(file.c_str(), file_url) != 0)
        {
            AK_LOG_WARN << "failed to upload file, file name is [" << file << "]";
            ::remove(file.c_str());
            return -1;
        }
    }
    else
    {
        if (storage_s3mng_ptr->UploadVideoFile(file, file_url) != 0)
        {
            UPLOAD_RETRY_FILE_INFO fileinfo;
            memset(&fileinfo, 0, sizeof(fileinfo));

            fileinfo.error_code = UPLOAD_VIDEO_FILE_TO_S3_ERROR;
            Snprintf(fileinfo.mac, sizeof(fileinfo.mac), file_name_related_info.mac_or_uuid);
            Snprintf(fileinfo.filename, sizeof(fileinfo.filename), file_name_related_info.origin_file_name);
            Snprintf(fileinfo.project_uuid, sizeof(fileinfo.project_uuid), file_name_related_info.project_uuid);

            // 加入到重传队列中
            GetUploadRetryHandlerInstance()->AddReUploadFile(fileinfo);
            
            AK_LOG_WARN << "failed to upload voice file, file name is [" << file << "]";
            ::remove(file.c_str());
            return -1;
        }
    }
    return 0;
}

int FileProcessor::GetFileSize(const std::string& file_name)
{
    FILE* fp = fopen(file_name.c_str(), "r");
    if (!fp)
    {
        return 0;
    }
    fseek(fp, 0L, SEEK_END);
    int file_size = ftell(fp);
    fclose(fp);
    return file_size;
}
