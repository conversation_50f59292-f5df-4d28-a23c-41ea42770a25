#ifndef _STORAGE_FILE_PROCESSOR_H_
#define _STORAGE_FILE_PROCESSOR_H_

#include <string>
#include "storage_mng.h"
#include "storage_s3.h"
#include "FileChecker.h"

class FileProcessor
{
public:
    static void HandleFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr);
    static void HandleWavFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr);
    static void HandlePicFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr);
    static void HandleVideoFile(const std::string& file, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr);
private:
    static void ReUploadS3BigImage(int err_code, const FileNameRelatedInfo& file_name_related_info, const std::string& log_project_uuid);
    static int GetFileSize(const std::string& file_name);
    static int UpdatePicUrl(const std::string& big_url, const std::string& small_url, const FileNameRelatedInfo& file_name_related_info);
    static int UploadWavFile(const FileNameRelatedInfo& file_name_related_info, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, std::string& file_url);
    static int UploadVideoFile(const FileNameRelatedInfo& file_name_related_info, CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, std::string& file_url);
};

#endif //_STORAGE_FILE_PROCESSOR_H_
