#include "UpdateUrlFailControl.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/ProjectInfo.h"
#include "AkLogging.h"
#include "storage_dao.h"

UpdateUrlFailControl* UpdateUrlFailControl::instance_ = nullptr;

UpdateUrlFailControl* UpdateUrlFailControl::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new UpdateUrlFailControl();
    }
    return instance_;
}

UpdateUrlFailControl::~UpdateUrlFailControl()
{
    voice_url_map_list_.clear();
    voice_pic_url_map_list_.clear();
    pic_url_fail_map_list_.clear();
}

UpdateUrlFailControl* GetUpdateUrlFailControlInstance()
{
    return UpdateUrlFailControl::GetInstance();
}

void UpdateUrlFailControl::AddVoiceUrlFail(VoiceUrlUpdateMap voice_url_fail_map)
{
    std::lock_guard<std::mutex> lock(voice_url_mutex_);
    voice_url_map_list_.push_back(voice_url_fail_map);
}

void UpdateUrlFailControl::AddVoicePicUrlFail(VoicePicUrlFailMap voice_pic_url_fail_map)
{
    std::lock_guard<std::mutex> lock(voice_pic_url_mutex_);
    voice_pic_url_map_list_.push_back(voice_pic_url_fail_map);
}

void UpdateUrlFailControl::AddPicUrlFail(PicUrlFailMap pic_url_fail_map)
{
    std::lock_guard<std::mutex> lock(pic_url_mutex_);
    pic_url_fail_map_list_.push_back(pic_url_fail_map);
}

void UpdateUrlFailControl::AddVideoUrlFail(VideoUrlFailMap video_url_fail_map)
{
    std::lock_guard<std::mutex> lock(video_url_mutex_);
    video_url_fail_map_list_.push_back(video_url_fail_map);
}

//当前每5s处理一次
void UpdateUrlFailControl::HandleUpdateUrlFail()
{
    UpdateVoiceUrl();
    UpdateVoicePicUrl();
    UpdatePicUrl();
    UpdateVideoUrl();
}

void UpdateUrlFailControl::UpdateVoiceUrl()
{
    time_t time_now;
    time_now = ::time(nullptr);
    {
        std::lock_guard<std::mutex> lock(voice_url_mutex_);
        for (auto it = voice_url_map_list_.begin(); it != voice_url_map_list_.end();)
        {
            time_t time_tmp = it->first;
            if ((time_now < time_tmp) || (time_now - time_tmp > 20))  //大于20s
            {
                voice_url_map_list_.erase(it++);
            }
            else if (time_now - time_tmp > 5) //5 < time_diff < 20s
            {
                std::string filename = it->second.first;
                std::string fileurl =  it->second.second;
                dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgFileUrl(fileurl, filename); //失败不再重试
                voice_url_map_list_.erase(it++);
            } 
            else
            {
                ++it;
            }
        }
    }
}

void UpdateUrlFailControl::UpdateVoicePicUrl()
{
    time_t time_now;
    time_now = ::time(nullptr);
    {
        std::lock_guard<std::mutex> lock(voice_pic_url_mutex_);
        for (auto it = voice_pic_url_map_list_.begin(); it != voice_pic_url_map_list_.end();)
        {
            time_t time_tmp = it->first;
            if ((time_now < time_tmp) || (time_now - time_tmp > 20))  //大于20s
            {
                voice_pic_url_map_list_.erase(it++);
            }
            else if (time_now - time_tmp > 5) //5 < time_diff < 20s
            {
                std::string filename = it->second.first;
                std::string fileurl =  it->second.second;
                dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgPicUrl(fileurl, filename); //失败不再重试
                voice_pic_url_map_list_.erase(it++);
            } 
            else
            {
                it++;
            }
        }
    }
}

void UpdateUrlFailControl::UpdatePicUrl()
{
    time_t time_now;
    time_now = ::time(nullptr);
    {
        std::lock_guard<std::mutex> lock(pic_url_mutex_);
        for (auto it = pic_url_fail_map_list_.begin(); it != pic_url_fail_map_list_.end();)  //从时间最小的刷
        {
            time_t time_tmp = it->first;
            if ((time_now < time_tmp) || (time_now - time_tmp > 20))  //大于20s
            {
                //此时证明改图片 对应的csmain同名文件一直没有上报,可能是恶意的文件上传,当前先记录文件名称,后续运维可以监控起来,
                std::string file = it->second.first;
                auto file_infos = it->second.second;
                std::string ftp_client_ip = std::get<2>(file_infos);
                AK_LOG_WARN << "Failed to DaoUpatePicUrl,devices not upload action log or action type error,file is " << file << ", ftp client ip is " << ftp_client_ip;
                pic_url_fail_map_list_.erase(it++);
            }
            else if (time_now - time_tmp > 5) //5 < time_diff < 20s
            {
                std::string file_tmp = it->second.first;
                auto file_infos = it->second.second;
                std::string file_path = std::get<0>(file_infos);//大图
                std::string file_path_tmp = std::get<1>(file_infos);   //小图
                std::string file_path_tmp1 = std::get<2>(file_infos);   //小图
                std::string mac = std::get<3>(file_infos);   //mac
                ProjectInfo log_project;
                std::string log_project_uuid;
                log_project.GetLogCaptureProjectUUID(mac, log_project_uuid); 
                DaoUpatePicUrl(mac, file_tmp, file_path, file_path_tmp, log_project_uuid); //失败不再重试
                pic_url_fail_map_list_.erase(it++);
            }
            else
            {
                it++;
            }
        }
    }
}

void UpdateUrlFailControl::UpdateVideoUrl()
{
    time_t time_now;
    time_now = ::time(nullptr);
    {
        std::lock_guard<std::mutex> lock(video_url_mutex_);
        for (auto it = video_url_fail_map_list_.begin(); it != video_url_fail_map_list_.end();)
        {
            time_t time_tmp = it->first;
            if ((time_now < time_tmp) || (time_now - time_tmp > 20))
            {
                video_url_fail_map_list_.erase(it++);
            }
            else if (time_now - time_tmp > 5)
            {
                auto file_infos = it->second;
                std::string mac = std::get<0>(file_infos);                   
                std::string video_name = std::get<1>(file_infos);
                std::string video_url = std::get<2>(file_infos); 
                std::string project_uuid = std::get<3>(file_infos); 

                DaoUpdateVideoUrl(mac, video_name, video_url, project_uuid);
                video_url_fail_map_list_.erase(it++);
            } 
            else
            {
                ++it;
            }
        }
    }

}

