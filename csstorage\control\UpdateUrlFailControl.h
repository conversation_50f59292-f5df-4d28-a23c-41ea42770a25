#ifndef _STORAGE_UPDATE_URL_FAIL_CONTROL_H_
#define _STORAGE_UPDATE_URL_FAIL_CONTROL_H_

#include <map>
#include <string>
#include <list>
#include <mutex>
#include <unistd.h>

typedef std::pair<time_t, std::pair<std::string, std::string>> VoiceUrlUpdateMap; //时间 本地文件 url
typedef std::pair<time_t, std::pair<std::string, std::string>> VoicePicUrlFailMap; //时间 本地文件 url
typedef std::pair<time_t, std::tuple<std::string, std::string, std::string, std::string>> VideoUrlFailMap; //时间 mac video_name video_url project_uuid
typedef std::pair<time_t, std::pair<std::string, std::tuple<std::string, std::string, std::string, std::string>>> PicUrlFailMap; // 时间 文件 大图url 小图url ftpclient mac

class UpdateUrlFailControl
{
public:
    UpdateUrlFailControl() = default;
    ~UpdateUrlFailControl();
    void HandleUpdateUrlFail();
    void UpdateVoiceUrl();
    void UpdateVoicePicUrl();
    void UpdatePicUrl();
    void UpdateVideoUrl();
    void AddVoiceUrlFail(VoiceUrlUpdateMap voice_url_fail_map);
    void AddVoicePicUrlFail(VoicePicUrlFailMap voice_pic_url_fail_map);
    void AddPicUrlFail(PicUrlFailMap pic_url_fail_map);
    void AddVideoUrlFail(VideoUrlFailMap video_url_fail_map);
    static UpdateUrlFailControl* GetInstance();

private:
    std::list<VoiceUrlUpdateMap> voice_url_map_list_;
    std::list<VoicePicUrlFailMap> voice_pic_url_map_list_;
    std::list<PicUrlFailMap> pic_url_fail_map_list_;
    std::list<VideoUrlFailMap> video_url_fail_map_list_;
    std::mutex voice_url_mutex_;
    std::mutex voice_pic_url_mutex_;
    std::mutex pic_url_mutex_;
    std::mutex video_url_mutex_;
    static UpdateUrlFailControl* instance_;

};

UpdateUrlFailControl* GetUpdateUrlFailControlInstance();

#endif
