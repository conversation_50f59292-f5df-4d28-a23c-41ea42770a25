﻿#include "handle_capture_log.h"
#include <sstream>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "doorlog/UserInfo.h"
#include "PersonalAccount.h"
#include "personal_capture.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/Sip.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "DeviceSetting.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeGroup.h"

#include "doorlog/RecordActLog.h"

extern LOG_DELIVERY gstAKCSLogDelivery;


//根据MAC获取设备设置信息
static int GetDeviceSettingByMac(const std::string &mac, ResidentDev &dev)
{
    if (dbinterface::ResidentDevices::GetMacDev(mac, dev) == 0)
    {
        return 0;
    }
    else if (dbinterface::ResidentPerDevices::GetMacDev(mac, dev) == 0)
    {
        return 0;
    }

    return -1;//查询到空值
}

CHandleCaptureLog& CHandleCaptureLog::GetInstance()
{
    static CHandleCaptureLog handle_capture_log;
    return handle_capture_log;
}

int CHandleCaptureLog::InsertCapture(const std::string &mac, const SOCKET_MSG_CALL_CAPTURE &call_capture_msg)
{
    // 被叫不记录,门口机不会上报
    if(call_capture_msg.dialog_out == 0){
        return 0;
    }

    ResidentDev dev;
    int ret = GetDeviceSettingByMac(mac, dev);
    if (ret != 0)
    {
        AK_LOG_WARN << "MAC=" << mac << " Not Found Devices Info.";
        return -1;
    }

    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;
    memset(&act_msg, 0, sizeof(act_msg));

    if (RecordActLog::GetInstance().RewriteProjectInfo(act_msg, dev) != 0 ) 
    {
        AK_LOG_WARN << "RewriteProjectInfo error mac:" << mac;
        return -1;
    }
    
    act_msg.mng_type = dev.is_personal;
    act_msg.is_public = dev.is_public;
    Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account),  dev.sip);
    Snprintf(act_msg.location, sizeof(act_msg.location),  dev.location);
    Snprintf(act_msg.account, sizeof(act_msg.account),  dev.node);//node
    Snprintf(act_msg.mac, sizeof(act_msg.mac),  dev.mac);
    Snprintf(act_msg.dev_uuid, sizeof(act_msg.dev_uuid),  dev.uuid);
    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CALL_CAPTURE;
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  "Call");
    Snprintf(act_msg.pic_name, sizeof(act_msg.pic_name),  call_capture_msg.picture_name);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  "visitor");//可能含有特殊字符
    Snprintf(act_msg.key, sizeof(act_msg.key),  "--");
    Snprintf(act_msg.pic_url, sizeof(act_msg.pic_url),  call_capture_msg.pic_url);
    Snprintf(act_msg.spic_url, sizeof(act_msg.spic_url),  call_capture_msg.spic_url);
    Snprintf(act_msg.video_url, sizeof(act_msg.video_url),  call_capture_msg.video_url);
    act_msg.capture_time = call_capture_msg.capture_time;
    act_msg.unit_id = dev.unit_id;
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");

    std::string node;
    std::string st_name;
    std::string company_uuid;
    std::string personal_account_uuid;
    std::string device_uuid;
    std::string callee = call_capture_msg.callee;
    std::string department_uuid = call_capture_msg.department_uuid;

    if (dev.conn_type == csmain::DeviceType::OFFICE_DEV)
    {   
        OfficeInfo office_info(act_msg.project_uuid2);
        if (office_info.IsNew())
        {
            GetNewOfficeCallNodeInfo(callee, act_msg.mng_id, node, st_name, company_uuid);
            if (company_uuid.length() == 0 && department_uuid.length() > 0)
            {
                // 呼叫OfficeGroup 
                OfficeGroupInfo office_group_info;
                if (dbinterface::OfficeGroup::GetOfficeGroupByUUID(department_uuid, office_group_info) == 0)
                {
                    company_uuid = office_group_info.office_company_uuid;
                }
            }
            Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), company_uuid.c_str());
        }
        GetOfficeCallNodeInfo(callee, act_msg.mng_id, node, st_name, personal_account_uuid);
    } else {
        Snprintf(act_msg.call_trace_id, sizeof(act_msg.call_trace_id), call_capture_msg.call_trace_id);
        // call_trace_id 的前缀必须为 当前设备的sip
        if (strlen(act_msg.call_trace_id) > 0 && !strstr(act_msg.call_trace_id, dev.sip))
        {
            AK_LOG_WARN << "device report call logs, call trace id error, mac = " << dev.mac << ", call_trace_id = " << act_msg.call_trace_id << ", sip = " << dev.sip;
            Snprintf(act_msg.call_trace_id, sizeof(act_msg.call_trace_id), "");
        }
        GetResidentCallNodeInfo(callee, node, st_name);
        CNodeInfo cNodeCfg(node);
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num), cNodeCfg.getRoomNumber().c_str());
    }
    Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());

    AK_LOG_INFO << "mac = " << dev.mac << " report call capture. dialog_out = "<< call_capture_msg.dialog_out
                << ", caller = " << call_capture_msg.caller << ", callee = " << call_capture_msg.callee
                << ", call_trace_id = " << call_capture_msg.call_trace_id << ", video_record_name = " << call_capture_msg.video_record_name;

    if (dbinterface::PersonalCapture::AddPersonalCapture(act_msg, gstAKCSLogDelivery.personal_capture_delivery) < 0)
    {
        AK_LOG_WARN << "Add personnal call capture failed.";
        return -1;
    }

    return 0;
}

int CHandleCaptureLog::GetResidentCallNodeInfo(const std::string& sip, std::string& node, std::string& st_name)
{
    // 呼叫sip号
    if (sip.length() > 0)
    {
        //先检查群组
        node = dbinterface::Sip::GetNodeByGroupFromSip2(sip);

        // 查找人
        if (node.empty())
        {
            int manager_id = 0;
            dbinterface::ResidentPersonalAccount::GetNickNameAndNodeAndMngIDByUid(sip, st_name, node, manager_id);
        }

        // 查找设备
        if (node.empty())
        {
            ResidentDev resident_dev;
            if (dbinterface::ResidentDevices::GetDevicesBySip(sip, resident_dev) == 0)
            {
                node = resident_dev.node;
                st_name = resident_dev.location;
            } 
            else if (dbinterface::ResidentPerDevices::GetDevicesBySip(sip, resident_dev) == 0)
            {
                node = resident_dev.node;
                st_name = resident_dev.location;
            } 
        }
        if (node.empty())
        {
            AK_LOG_WARN << "there is illegal callee " << ", sip=" << sip;
            return -1;
        }
    }

    return 0;
}


int CHandleCaptureLog::GetOfficeCallNodeInfo(const std::string& sip, const int& mng_id, std::string& node, std::string& st_name, std::string& personal_account_uuid)
{
    // 呼叫sip号
    if (sip.length() > 0)
    {
        std::string device_uuid;
        //先检查群组
        node = dbinterface::Sip::GetNodeByGroupFromSip2(sip);

        OfficeAccount office_account;

        // 查找人
        if (node.empty())
        {
            if (dbinterface::OfficePersonalAccount::GetUidAccount(sip, office_account) == 0)
            {
                st_name = office_account.name;
                node = office_account.account;
                personal_account_uuid = office_account.uuid;
            }
        }

        // 查找设备
        if (node.empty())
        {
            ResidentDev office_dev;
            if (dbinterface::ResidentDevices::GetDevicesBySip(sip, office_dev) == 0)
            {
                node = office_dev.node;
                st_name = office_dev.location;
                if (dbinterface::OfficePersonalAccount::GetUidAccount(node, office_account) == 0)
                {
                    personal_account_uuid = office_account.uuid;
                }
            }
        }

        //找手机号码
        if (node.empty())
        {
            OfficeAccount office_account;
            if (dbinterface::OfficePersonalAccount::GetPhoneAccountForOfficeid(sip, mng_id, office_account) == 0)
            {
                st_name = office_account.name;
                node = office_account.account;
                personal_account_uuid = office_account.uuid;
            }
        }
    
        if (node.empty())
        {
            AK_LOG_WARN << "there is illegal callee " << ", sip=" << sip;
            return -1;
        }
    }

    return 0;
}

int CHandleCaptureLog::GetNewOfficeCallNodeInfo(const std::string& sip, const int& mng_id, std::string& node, std::string& st_name, std::string& company_uuid)
{
    std::string personal_account_uuid;
    GetOfficeCallNodeInfo(sip, mng_id, node, st_name, personal_account_uuid);
    if (personal_account_uuid.length() > 0)
    {
        OfficePersonnelInfo office_personnel_info;
        if (dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(personal_account_uuid, office_personnel_info) != 0)
        {
            AK_LOG_WARN << "Get OfficePersonnel failed: PersonalAccountUUID=" << personal_account_uuid;
        }
        company_uuid = office_personnel_info.office_company_uuid;
    }
    return 0;
}