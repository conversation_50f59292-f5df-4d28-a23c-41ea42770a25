﻿#ifndef _HANDLE_CAPTURE_LOG_H_
#define _HANDLE_CAPTURE_LOG_H_

#include <memory>
#include <vector>
#include <string>
#include <set>
#include <boost/noncopyable.hpp>
#include "json/json.h"
#include "model/CommonModel.h"

class CHandleCaptureLog: private boost::noncopyable
{
public:
    static CHandleCaptureLog& GetInstance();

    int InsertCapture(const std::string &mac, const SOCKET_MSG_CALL_CAPTURE &stCallCaptureMsg);
private:
    // void GetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& nMngID);
    int GetResidentCallNodeInfo(const std::string& sip, std::string& node, std::string& st_name);
    int GetOfficeCallNodeInfo(const std::string& sip, const int& mng_id, std::string& node, std::string& st_name, std::string& personal_account_uuid);
    int GetNewOfficeCallNodeInfo(const std::string& sip, const int& mng_id, std::string& node, std::string& st_name, std::string& company_uuid);
};

#endif

