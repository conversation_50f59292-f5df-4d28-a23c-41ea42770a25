﻿#ifndef _HANDLE_DOOR_LOG_H_
#define _HANDLE_DOOR_LOG_H_

#include <memory>
#include <vector>
#include <string>
#include <set>
#include <boost/noncopyable.hpp>
#include "json/json.h"
#include "model/CommonModel.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"

class CHandleDoorLog: private boost::noncopyable
{
public:
    static CHandleDoorLog& GetInstance();

    int InsertCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &stActMsg);
private:
    void NotifyWebAccessDoorMsg(const SOCKET_MSG_DEV_REPORT_ACTIVITY& activity);
    int GenerateAccessDoorMsg(AccessDoorNotifyMsg& access_door_notify_msg, const SOCKET_MSG_DEV_REPORT_ACTIVITY& activity);
    std::string GetEntryExitModeByAccessMode(const SOCKET_MSG_DEV_REPORT_ACTIVITY& activity);
    void SendAccessDoorNotifyWebMsg(const AccessDoorNotifyMsg& access_door_notify_msg);
    int InsertOfficeCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev &dev);
    int InsertNewOfficeCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev &dev);
    int InsertResidentCapture(const std::string &mac, SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev &dev);
};

#endif

