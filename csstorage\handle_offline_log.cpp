﻿#include "handle_offline_log.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkLogging.h"
#include "storage_mng.h"
#include "personal_capture.h"
#include "common/storage_util.h"
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <fstream>
#include <mutex>
#include "model/CommonModel.h"
#include "storage_mng.h"
#include "handle_door_log.h"
#include "thumbnail.h"
#include "storage_ser.h"
#include "storage_mq.h"
#include "storage_dao.h"
#include "handle_capture_log.h"
#include "storage_util.h"
#include "encrypt/Md5.h"
#include "upload_retry_control.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "storage_s3.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "AkcsCommonDef.h"
#include "doorlog/LogLabelDefine.h"
#include "cspbx_rpc_client_mng.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

extern AKCS_CONF gstAKCSConf;
extern char FTP_FILE_INVALID[];
extern StorageS3Mng* g_storage_s3mng_ptr;

#define OFFLINE_EXTRACT_PATH "/usr/local/akcs/csstorage/offline_extract"

//MAC-Timestamp-Offline-MD5.tar.gz
bool CHandleOfflineLog::IsTagGzFile(const std::string &file_name)
{
    size_t found =  file_name.find_last_of('.');
    if (found == std::string::npos)
    {
        return false;
    }

    std::string suffix = file_name.substr(found+1);
    if (suffix == "gz")
    {
        return true;
    }

    return false;
}

bool CHandleOfflineLog::IsJsonFile(const std::string &file_name)
{
    size_t found =  file_name.find_last_of('.');
    if (found == std::string::npos)
    {
        return false;
    }

    std::string suffix = file_name.substr(found+1);
    if (suffix == "json")
    {
        return true;
    }

    return false;
}

void CHandleOfflineLog::HandleTarGzFiles(std::vector<std::string> vec_file, const std::string &csstorage_data_dir)
{
    if (vec_file.empty())
    {
        return;
    }
            
    for (auto& file : vec_file)
    {
        //先将从vsftpd传过来的IP截取出来,还原实际文件名称
        std::string ftp_client_ip;
        std::string original_file_name;
        csstorage::common::TruncFtpFileIPInfo(file, ftp_client_ip, original_file_name);//提取原始文件名称,即:去掉vsftpd添加的-IP-xxx后缀

        std::string file_full_path = csstorage_data_dir;
        file_full_path +=  "/";
        file_full_path +=  file;
        
        std::string original_file_full_path = csstorage_data_dir;
        original_file_full_path +=  "/";
        original_file_full_path +=  original_file_name;
        ::rename(file_full_path.c_str(), original_file_full_path.c_str());
        file = original_file_name;

        if (IsTagGzFile(file) != true || HandleOneTarGzFile(file, csstorage_data_dir) != 0)//判断为攻击
        {
            csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
            ::remove(original_file_full_path.c_str());
            return;
        }
        if (gstAKCSConf.bak_offline_file)
        {
            std::string bak_file_path = "/usr/local/akcs/csstorage/bak_offline/";
            bak_file_path += file;
            ::rename(original_file_full_path.c_str(), bak_file_path.c_str());
            AK_LOG_INFO << "move file:" << original_file_full_path << " to path:" << bak_file_path;
        }
        else
        {
            ::remove(original_file_full_path.c_str());
            AK_LOG_INFO << "remove file:" << original_file_full_path;
        }
    }
}

int CHandleOfflineLog::HandleOneTarGzFile(const std::string &file_name, const std::string &csstorage_data_dir)
{
    //added by chenyc,2022.08.18, 修改离线日志重传的文件命名方案,提升安全
    //6.4方案:MAC-Timestamp-Offline-MD5.tar.gz,其中MD5是压缩文件的MD5值
    //6.5方案:MAC-Timestamp-Offline-Token.tar.gz,其中 Token = MD5(ak_ftp:mac:timestamp:file_md5)
    //6.5安全整改方案:UUID-Timestamp-Offline-Token,其中 Token = MD5(ak_ftp:UUID:timestamp:file_md5),各字段说明如下:
    //ak_ftp:直接以字符串的形式；
    //mac:设备的具体mac地址，eg:A62000210525;
    //uuid:设备的uuid，eg:na-ce8bf2b1c78a11ed873400163e047e78;
    //timestamp:事件戳，跟文件名称中第二个字段Timestamp 取值一致，eg:1627884850；
    //file_md5:压缩文件的MD5值
    
    std::vector<std::string> contents;
    SplitString(file_name, "-", contents);
    if (contents.size() != 4 && contents.size() != 5) //新版本用uuid, uuid里面会包含-号
    {
        AK_LOG_WARN << "FileName:" << file_name << " error,Skip this file.";
        return -1;
    }

    std::string mac;
    std::string uuid;
    std::string time_stamp;
    std::string file_name_md5;
    std::string offline_log_type;    
    if (contents.size() == 4)
    {
        mac = contents[0];
        time_stamp = contents[1];
        file_name_md5 = csstorage::common::GetFileNameMd5(contents[3]);
        offline_log_type = contents[2];
    }
    else if (contents.size() == 5)
    {
        uuid = contents[0] + '-' + contents[1];
        time_stamp = contents[2];
        file_name_md5 = csstorage::common::GetFileNameMd5(contents[4]);
        offline_log_type = contents[3];
        int project;
        GetPersonalCaptureInstance()->GetMacByUUID(uuid, mac, project);
    }
    
    std::string file_full_name = csstorage_data_dir;
    file_full_name += "/";
    file_full_name += file_name;
    AK_LOG_INFO << "Handle TarGz File:" << file_name << " begin.MAC=" << mac << ";MD5=" << file_name_md5;
    	
    int dclient_ver = DaoSelectDclientVer(mac);
    if (-1 == dclient_ver)
    {
        return false;   //数据库找不到这个mac
    }
    else if (dclient_ver < 6500)//6500之后，检验方案变更为更安全的方式,具体查看上述函数开头部分备注
    {
        int i = 0, sleep_time = 1;
        do
        {
            std::string file_md5 = akuvox_encrypt::MD5::GetFileMD5(file_full_name);
            if (file_md5 == file_name_md5)
            {
                break;
            }
                ++i;
            if (i == 5)//modified by chenyc,2022.08.18,退避改成5次,共 2+4+8+16+32=66s,按平均每张图片30k,最大允许1k张,没有压缩的话在30M左右,压缩后多少? 文件上传的带宽多少? 66s是否足够?
            {
                AK_LOG_WARN << "file_md5 is " << file_md5 << ",but device give md5 is " << file_name_md5 << " mismatch error,Skip this file.";
                return -1;
            }
            sleep_time = 2 * sleep_time;
            //TODO,chenyc,改成异步的方式进行等待，当前sleep的阻塞方式不适用于生产环境
            sleep(sleep_time);
        } while (1);
    }
    else//6.5之后采用新的token校验方式,具体见上述 
    {
        std::string mac_uuid = contents.size() == 4 ? mac : uuid;    
        if (0 != csstorage::common::CheckOneFileMd5(file_full_name, mac_uuid, time_stamp, file_name_md5, FILE_TYPE::FILE_TYPE_TAR))
        {
            return -1;
        }
    }

    std::string extract_dir = ExtractFile(file_name, csstorage_data_dir, mac);

    if (offline_log_type == "OfflineCall")
    {
        GetAllOfflineCallFile(extract_dir.c_str(), mac);
    }
    else
    {
        GetAllFile(extract_dir.c_str(), mac);
    }
    RemoveDir(extract_dir);
    SendOfflineHandleAckMsg(mac, file_name);//TODO:直接返回成功

    AK_LOG_INFO << "Handle TarGz File:" << file_name << " end.";
	return 0;
}

std::string CHandleOfflineLog::ExtractFile(const std::string &file_name, const std::string &csstorage_data_dir, const std::string &mac)
{
    time_t now = time(NULL);
    char extract_dir[128];
    snprintf(extract_dir, sizeof(extract_dir), "%s_%ld", mac.c_str(), now);

    std::string full_path = OFFLINE_EXTRACT_PATH;
    full_path += "/";
    full_path += extract_dir;

    char cmd[512];
    snprintf(cmd, sizeof(cmd), "mkdir -p %s;tar zxf %s/%s -C %s", full_path.c_str(), csstorage_data_dir.c_str(), file_name.c_str(), full_path.c_str());
    system(cmd);

    AK_LOG_INFO << "Exec cmd:" << cmd << ";Extract path:" << full_path;
    return full_path;
}


void CHandleOfflineLog::GetAllFile(const char *data_dir, const std::string &mac)
{
    DIR* dp;
    struct dirent* entry;
    struct stat statbuf;
    if ((dp = ::opendir(data_dir)) == nullptr)
    {
        AK_LOG_FATAL << "access to data_dir:" << data_dir << " failed";
        return ;
    }

    ::chdir(data_dir);
    std::set<std::string> pic_files;
    std::set<std::string> video_files;
    std::string json_file;

    while ((entry = ::readdir(dp)) != nullptr)
    {
        lstat(entry->d_name, &statbuf);
        if (S_ISDIR(statbuf.st_mode))
        {
            continue;
        }

        int file_size = 0;
        FILE* fp = fopen(entry->d_name, "r");
        if (!fp)
        {
            continue;
        }
        fseek(fp, 0L, SEEK_END);
        file_size = ftell(fp);
        fclose(fp);

        if (0 == file_size)
        {
            AK_LOG_WARN << "File=" << entry->d_name << " Is Empty.Skip This File.";
            continue;
        }

        if (IsJsonFile(entry->d_name))
        {
            json_file = data_dir;
            json_file += "/";
            json_file += entry->d_name;          
            AK_LOG_INFO << "Get Json File:" << json_file;
        }
        else if (csstorage::common::IsPicFile(entry->d_name))
        {
            pic_files.insert(entry->d_name);
        }
        else if (csstorage::common::IsVideoFile(entry->d_name))
        {
            video_files.insert(entry->d_name);
        }
        else
        {
            AK_LOG_WARN << "File=" << entry->d_name << " has invalid suffix.Skip This File.";
            continue;
        }
    }
    ::closedir(dp);

    if (json_file.empty())
    {
        AK_LOG_WARN << "JsonFile is not exists.";
        return;
    }

    if (ParseJsonFile(json_file.c_str(), pic_files, video_files, mac, data_dir) != 0)
    {
        return;
    }
}

void CHandleOfflineLog::TransferLockdownResp(SOCKET_MSG_DEV_REPORT_ACTIVITY& activity)
{
    // 设备lockdown时上报的是2, 记录下capture_action,把resp转换成失败
    if (activity.resp == (int)CAPTURE_LOG_RET_TYPE::LOCKDOWN) 
    {
        activity.resp = (int)CAPTURE_LOG_RET_TYPE::FAILURE;
        Snprintf(activity.capture_action, sizeof(activity.capture_action), open_door_lockdown_on);
    }
}

void CHandleOfflineLog::ParseDoorLog(const Json::Value &root, const std::set<std::string> &pic_files, const std::set<std::string> &video_files, const std::string &mac, const char *data_dir)
{
    int door_log_cnt = 0;
    if (root.isMember("count"))
    {
        door_log_cnt = root["count"].asInt();
    }

    if (door_log_cnt == 0)
    {
        return;
    }

    if (!root.isMember("Records"))
    {
        return;
    }

    Json::Value door_logs = root["Records"];
    if (door_logs.empty())
    {
        return;
    }

    Json::UInt door_log_size = door_logs.size();
    AK_LOG_INFO << "Mac=" << mac << ";DoorLogCnt=" << door_log_cnt << ";DoorLogSize=" << door_log_size;

    for (Json::UInt i = 0; i < door_log_size; i++)
    {
        Json::Value door_log = door_logs[i];
        SOCKET_MSG_DEV_REPORT_ACTIVITY activity;
        memset(&activity, 0, sizeof(activity));
        if (door_log.isMember("Type"))
        {
            activity.act_type = door_log["Type"].asInt();
        }
        if (door_log.isMember("PicName"))
        {
            Snprintf(activity.pic_name, sizeof(activity.pic_name),  door_log["PicName"].asCString());
        }
        if (door_log.isMember("Initiator"))
        {
            Snprintf(activity.initiator, sizeof(activity.initiator),  door_log["Initiator"].asCString());
        }
        if (door_log.isMember("Response"))
        {
            activity.resp = door_log["Response"].asInt();
        }
        if (door_log.isMember("PerId"))
        {
            Snprintf(activity.per_id, sizeof(activity.per_id),  door_log["PerId"].asCString());
        }
        if (door_log.isMember("PerID"))
        {
            Snprintf(activity.per_id, sizeof(activity.per_id),  door_log["PerID"].asCString());
        }
        if (door_log.isMember("DepartMode"))
        {
            activity.depart_mode = door_log["DepartMode"].asInt();
        }
        if (door_log.isMember("Time"))
        {
            activity.capture_time = door_log["Time"].asInt();
        }
        if (door_log.isMember("Relay"))
        {
            Snprintf(activity.relay, sizeof(activity.relay),  door_log["Relay"].asCString());
        }
        if (door_log.isMember("SecurityRelay"))
        {
            Snprintf(activity.srelay, sizeof(activity.srelay),  door_log["SecurityRelay"].asCString());
        }
        if (door_log.isMember("CallTraceID"))
        {
            Snprintf(activity.call_trace_id, sizeof(activity.call_trace_id),  door_log["CallTraceID"].asCString());
        }
        if (door_log.isMember("RecordName"))
        {
            Snprintf(activity.video_record_name, sizeof(activity.video_record_name),  door_log["RecordName"].asCString());
        }
        if (door_log.isMember("EntryMode"))
        {
            Snprintf(activity.relay_entry_mode, sizeof(activity.relay_entry_mode),  door_log["EntryMode"].asCString());
        }
        if (door_log.isMember("SEntryMode"))
        {
            Snprintf(activity.security_relay_entry_mode, sizeof(activity.security_relay_entry_mode),  door_log["SEntryMode"].asCString());
        }

        GetAccessMode(activity);
        GetDoorNameList(activity, mac);
        TransferLockdownResp(activity);

        AK_LOG_INFO << "Type=" << activity.act_type << ";PicName=" << activity.pic_name 
                    << ";Initiator=" << activity.initiator << ";Response=" << activity.resp << ";PerId=" << activity.per_id 
                    << ";DepartMode=" << activity.depart_mode << ";CallTraceID=" << activity.call_trace_id 
                    << ";RecordName=" << activity.video_record_name;

        // 上传图片
        UploadDoorlogImageFile(mac, pic_files, activity, data_dir);

        // 上传视频
        UploadDoorlogVideoFile(mac, video_files, activity, data_dir);

        // 插入数据库记录
        CHandleDoorLog::GetInstance().InsertCapture(mac, activity);        
    }
}

void CHandleOfflineLog::GetAccessMode(SOCKET_MSG_DEV_REPORT_ACTIVITY& activity)
{
    // 不是返潜回类型的开门, 把entryMode转换成accessMode
    if (activity.access_mode == (int)AntiPassbackDoorType::NORMAL)
    {
        if (strlen(activity.relay_entry_mode) > 0)
        {
            // 先取第一个relay的entry mode, 当已知问题
            int relay_entry_mode = ATOI(std::string(1, *activity.relay_entry_mode).c_str());
            activity.access_mode = relay_entry_mode == (int)ActLogRelayEntryMode::EXIT ? (int)AntiPassbackDoorType::EXIT : (int)AntiPassbackDoorType::ENTRY;
        }
        
        else if (strlen(activity.security_relay_entry_mode) > 0)
        {
            // 先取第一个security relay的entry mode, 当已知问题
            int security_relay_entry_mode = ATOI(std::string(1, *activity.security_relay_entry_mode).c_str());
            activity.access_mode = security_relay_entry_mode == (int)ActLogRelayEntryMode::EXIT ? (int)AntiPassbackDoorType::EXIT : (int)AntiPassbackDoorType::ENTRY;
        }
    }
}

void CHandleOfflineLog::GetDoorNameList(SOCKET_MSG_DEV_REPORT_ACTIVITY& activity, const std::string& mac)
{
    // 通过设备mac获取设备uuid
    std::string dev_uuid;
    if (dbinterface::ResidentDevices::GetDevUUIDByMac(mac, dev_uuid) != 0) 
    {
        AK_LOG_WARN << "Get Dev UUID by mac failed, mac = " << mac;
        return;
    }

    std::string door_name_list = dbinterface::DevicesDoorList::GetReportActLogDoorNameList(dev_uuid, activity.relay, activity.srelay);
    Snprintf(activity.door_name_list, sizeof(activity.door_name_list), door_name_list.c_str());
}

void CHandleOfflineLog::ParseCaptureLog(const Json::Value &root, const std::set<std::string> &pic_files, const std::set<std::string> &video_files, const std::string &mac, const char *data_dir)
{
    int capture_log_cnt = 0;
    if (root.isMember("CaptureCnt"))
    {
        capture_log_cnt = root["CaptureCnt"].asInt();
    }

    if (capture_log_cnt == 0)
    {
        return;
    }

    if (!root.isMember("CaptureLog"))
    {
        return;
    }

    Json::Value capture_logs = root["CaptureLog"];
    if (capture_logs.empty())
    {
        return;
    }

    Json::UInt capture_log_size = capture_logs.size();
    AK_LOG_INFO << "CaptureLogCnt=" << capture_log_cnt << ";CaptureLogSize=" << capture_log_size;
    for (Json::UInt i = 0; i < capture_log_size; i++)
    {
        Json::Value capture_log = capture_logs[i];
        SOCKET_MSG_CALL_CAPTURE capture;
        memset(&capture, 0, sizeof(capture));
        if (capture_log.isMember("PicName"))
        {
            Snprintf(capture.picture_name, sizeof(capture.picture_name),  capture_log["PicName"].asCString());
        }
        if (capture_log.isMember("Caller"))
        {
            Snprintf(capture.caller, sizeof(capture.caller),  capture_log["Caller"].asCString());
        }
        if (capture_log.isMember("Callee"))
        {
            Snprintf(capture.callee, sizeof(capture.callee),  capture_log["Callee"].asCString());
        }
        if (capture_log.isMember("DailOut"))
        {
            capture.dialog_out = capture_log["DailOut"].asInt();
        }
        if (capture_log.isMember("CaptureTime"))
        {
            capture.capture_time = capture_log["CaptureTime"].asInt();
        }
        if (capture_log.isMember("CallTraceID"))
        {
            Snprintf(capture.call_trace_id, sizeof(capture.call_trace_id),  capture_log["CallTraceID"].asCString());
        }
        if (capture_log.isMember("RecordName"))
        {
            Snprintf(capture.video_record_name, sizeof(capture.video_record_name),  capture_log["RecordName"].asCString());
        }
        if (capture_log.isMember("DepartmentUUID"))
        {
            Snprintf(capture.department_uuid, sizeof(capture.department_uuid),  capture_log["DepartmentUUID"].asCString());
        }

        AK_LOG_INFO << "PicName=" << capture.picture_name << ";Caller=" << capture.caller << ";Callee=" << capture.callee 
                    << ";Dialout=" << capture.dialog_out << ";CaptureTime=" << capture.capture_time
                    << ";CallTraceID=" << capture.call_trace_id << ";VideoRecordName=" << capture.video_record_name
                    << ";DepartmentUUID=" << capture.department_uuid;
        
        // 上传图片
        UploadCaptureImageFile(mac, pic_files, capture, data_dir);

        // 上传视频
        UploadCaptureImageFile(mac, video_files, capture, data_dir);

        // 插入数据库记录
        CHandleCaptureLog::GetInstance().InsertCapture(mac, capture);
    }
}

int CHandleOfflineLog::ParseJsonFile(const char *file_full_name, const std::set<std::string> &pic_files, const std::set<std::string> &video_files, const std::string &mac, const char *data_dir)
{
    Json::Reader reader;
    Json::Value root;

    ifstream in(file_full_name, ios::binary);
    if (!in.is_open())
    {
        AK_LOG_WARN << "Error opening file:" << file_full_name;
        return -1;
    }

    if (!reader.parse(in, root))
    {
        AK_LOG_WARN << "Error Parse Json file:" << file_full_name;
        in.close();
        return -1;
    }
    in.close();

    ParseDoorLog(root, pic_files, video_files, mac, data_dir);
    ParseCaptureLog(root, pic_files, video_files, mac, data_dir);

    return 0;
}

void CHandleOfflineLog::RemoveDir(const std::string &dir)
{
    if (dir.empty() || dir == "/")
    {
        return;
    }

    char cmd[256];
    snprintf(cmd, sizeof(cmd), "rm -rf %s", dir.c_str());
    system(cmd);
}

/**
 * A81801210414-1635930301_0_DoorDev_e60b2be102aa8c230c548f11c4752dca.jpg
 *
 * <AUTHOR> (2021/11/5)
 *
 * @param pic_name
 *
 * @return long
 */
long CHandleOfflineLog::GetTimeFromPicName(const std::string &pic_name)
{
    std::vector<std::string> contents;
    SplitString(pic_name, "-", contents);
    if (contents.size() != 2)
    {
        return time(0);
    }

    std::string content = contents[1];
    std::string time_stamp;
    size_t time_pos = content.find_first_of("_");
    if (time_pos != std::string::npos)
    {
        time_stamp = content.substr(0, time_pos);
        return stol(time_stamp);
    }

    return time(0);
}

void CHandleOfflineLog::HandleRetryFile(const std::string& mac, const std::string& filename)
{
    // 用于hash分表
    ProjectInfo log_project;
    std::string log_project_uuid;
    log_project.GetLogCaptureProjectUUID(mac, log_project_uuid); 

    UPLOAD_RETRY_FILE_INFO fileinfo;
    memset(&fileinfo, 0, sizeof(fileinfo));
    
    fileinfo.error_code = UPLOAD_BIG_IMAGE_TO_S3_ERROR;
    fileinfo.is_voice_pic = 0;

    Snprintf(fileinfo.mac, sizeof(fileinfo.mac), mac.c_str());
    Snprintf(fileinfo.filename, sizeof(fileinfo.filename), filename.c_str());
    Snprintf(fileinfo.project_uuid, sizeof(fileinfo.project_uuid), log_project_uuid.c_str());

    // 加入到重传队列中
    GetUploadRetryHandlerInstance()->AddReUploadFile(fileinfo);
        
}

int CHandleOfflineLog::UploadDoorlogImageFile(const std::string& mac, const std::set<std::string>& pic_files, SOCKET_MSG_DEV_REPORT_ACTIVITY& activity, const char *data_dir)
{
    if (pic_files.find(activity.pic_name) == pic_files.end())
    {
        AK_LOG_WARN << "No Pic File Found, record this log with no pic.";
        return -1;
    }

    std::string pic_file = std::string(data_dir) + "/" + std::string(activity.pic_name);
    
    std::string big_file_path;
    std::string small_file_path;
    int upload_image_ret = UploadImageFile(storage_mng_ptr_, g_storage_s3mng_ptr, pic_file, big_file_path, small_file_path);
    
    // 上传s3失败的大图进行重传
    if (!gstAKCSConf.store_fdfs && upload_image_ret == UPLOAD_BIG_IMAGE_TO_S3_ERROR)
    {
        // 加入重传队列
        HandleRetryFile(mac, activity.pic_name);
    
        AK_LOG_WARN << "upload offline image filed, add to retry deque, error code: " << upload_image_ret << ", filename: " << activity.pic_name;
        return -1;
    }
    
    Snprintf(activity.pic_url, sizeof(activity.pic_url),  big_file_path.c_str());
    Snprintf(activity.spic_url, sizeof(activity.spic_url),  small_file_path.c_str());
    return 0;
}

int CHandleOfflineLog::UploadDoorlogVideoFile(const std::string& mac, const std::set<std::string>& video_files, SOCKET_MSG_DEV_REPORT_ACTIVITY& activity, const char *data_dir)
{
    if (video_files.find(activity.video_record_name) == video_files.end())
    {
        AK_LOG_WARN << "No Video File Found, not need upload video file";
        return -1;
    }
    
    std::string remote_url;
    std::string video_filepath = std::string(data_dir) + "/" + std::string(activity.video_record_name);
    
    int upload_video_ret = UploadVideoHandler(storage_mng_ptr_, g_storage_s3mng_ptr, video_filepath, remote_url);
    
    if (!gstAKCSConf.store_fdfs && upload_video_ret == UPLOAD_VIDEO_FILE_TO_S3_ERROR)
    {
        UPLOAD_RETRY_FILE_INFO fileinfo;
        memset(&fileinfo, 0, sizeof(fileinfo));
        
        fileinfo.error_code = UPLOAD_VIDEO_FILE_TO_S3_ERROR;
        Snprintf(fileinfo.filename, sizeof(fileinfo.filename), activity.video_record_name);
        
        GetUploadRetryHandlerInstance()->AddReUploadFile(fileinfo);
        
        AK_LOG_WARN << "upload offline video filed, add to retry deque, filename: " << activity.video_record_name;
        return -1;
    }
            
    Snprintf(activity.video_url, sizeof(activity.video_url),  remote_url.c_str());
    return 0;
}

int CHandleOfflineLog::UploadCaptureImageFile(const std::string& mac, const std::set<std::string>& pic_files, SOCKET_MSG_CALL_CAPTURE& capture, const char *data_dir)
{
    if (pic_files.find(capture.picture_name) == pic_files.end())
    {
        AK_LOG_WARN << "No Pic File Found, record this log with no pic.";
        return -1;
    }

    std::string pic_file = std::string(data_dir) + "/" + std::string();
    
    std::string big_file_path;
    std::string small_file_path;
    int upload_image_ret = UploadImageFile(storage_mng_ptr_, g_storage_s3mng_ptr, pic_file, big_file_path, small_file_path);
    
    // 上传s3失败的大图进行重传
    if (!gstAKCSConf.store_fdfs && upload_image_ret == UPLOAD_BIG_IMAGE_TO_S3_ERROR)
    {
        // 加入重传队列
        HandleRetryFile(mac, capture.picture_name);
    
        AK_LOG_WARN << "upload offline image filed, add to retry deque, error code: " << upload_image_ret << ", filename: " << capture.picture_name;
        return -1;
    }
    
    Snprintf(capture.pic_url, sizeof(capture.pic_url),  big_file_path.c_str());
    Snprintf(capture.spic_url, sizeof(capture.spic_url),  small_file_path.c_str());
    return 0;
}

int CHandleOfflineLog::UploadCaptureVideoFile(const std::string& mac, const std::set<std::string>& video_files, SOCKET_MSG_CALL_CAPTURE& capture, const char *data_dir)
{
    if (video_files.find(capture.video_record_name) == video_files.end())
    {
        AK_LOG_WARN << "No Video File Found, not need upload video file";
        return -1;
    }
    
    std::string remote_url;
    std::string video_filepath = std::string(data_dir) + "/" + std::string(capture.video_record_name);
    
    int upload_video_ret = UploadVideoHandler(storage_mng_ptr_, g_storage_s3mng_ptr, video_filepath, remote_url);
    
    if (!gstAKCSConf.store_fdfs && upload_video_ret == UPLOAD_VIDEO_FILE_TO_S3_ERROR)
    {
        UPLOAD_RETRY_FILE_INFO fileinfo;
        memset(&fileinfo, 0, sizeof(fileinfo));
        
        fileinfo.error_code = UPLOAD_VIDEO_FILE_TO_S3_ERROR;
        Snprintf(fileinfo.filename, sizeof(fileinfo.filename), capture.video_record_name);
        
        GetUploadRetryHandlerInstance()->AddReUploadFile(fileinfo);
        
        AK_LOG_WARN << "upload offline video filed, add to retry deque, filename: " << capture.video_record_name;
        return -1;
    }

    Snprintf(capture.video_url, sizeof(capture.video_url),  remote_url.c_str());
    return 0;
}


void CHandleOfflineLog::GetAllOfflineCallFile(const char *data_dir, const std::string &mac)
{
    DIR* dp;
    struct dirent* entry;
    struct stat statbuf;
    if ((dp = ::opendir(data_dir)) == nullptr)
    {
        AK_LOG_FATAL << "GetAllOfflineCallFile access to data_dir:" << data_dir << " failed";
        return ;
    }

    ::chdir(data_dir);
    std::set<std::string> pic_files;
    std::set<std::string> video_files;
    std::string json_file;

    while ((entry = ::readdir(dp)) != nullptr)
    {
        lstat(entry->d_name, &statbuf);
        if (S_ISDIR(statbuf.st_mode))
        {
            continue;
        }

        int file_size = 0;
        FILE* fp = fopen(entry->d_name, "r");
        if (!fp)
        {
            continue;
        }
        fseek(fp, 0L, SEEK_END);
        file_size = ftell(fp);
        fclose(fp);

        if (0 == file_size)
        {
            AK_LOG_WARN << "GetAllOfflineCallFile File=" << entry->d_name << " Is Empty.Skip This File.";
            continue;
        }

        if (IsJsonFile(entry->d_name))
        {
            json_file = data_dir;
            json_file += "/";
            json_file += entry->d_name;          
            AK_LOG_INFO << "GetAllOfflineCallFile Get Json File:" << json_file;
        }
        else
        {
            AK_LOG_WARN << "GetAllOfflineCallFile File=" << entry->d_name << " has invalid suffix.Skip This File.";
            continue;
        }
    }
    ::closedir(dp);

    if (json_file.empty())
    {
        AK_LOG_WARN << "GetAllOfflineCallFile JsonFile is not exists.";
        return;
    }

    if (ParseCallJsonFile(json_file.c_str(), mac, data_dir) != 0)
    {
        return;
    }
}

int CHandleOfflineLog::ParseCallJsonFile(const char *file_full_name, const std::string &mac, const char *data_dir)
{
    Json::Reader reader;
    Json::Value root;

    ifstream in(file_full_name, ios::binary);
    if (!in.is_open())
    {
        AK_LOG_WARN << "Error opening file:" << file_full_name;
        return -1;
    }

    if (!reader.parse(in, root))
    {
        AK_LOG_WARN << "Error Parse Json file:" << file_full_name;
        in.close();
        return -1;
    }
    in.close();

    ParseCallLog(root, mac, data_dir);

    return 0;
}

void CHandleOfflineLog::ParseCallLog(const Json::Value &root, const std::string &mac, const char *data_dir)
{
    int call_log_cnt = 0;
    if (root.isMember("count"))
    {
        call_log_cnt = root["count"].asInt();
    }

    if (call_log_cnt == 0)
    {
        AK_LOG_INFO << "CallLogCnt is 0, skip parse call log";
        return;
    }

    if (!root.isMember("Records"))
    {
        AK_LOG_INFO << "CallLog Records is empty, skip parse call log";
        return;
    }

    Json::Value call_logs = root["Records"];
    if (call_logs.empty())
    {
        AK_LOG_INFO << "CallLog json Records is empty, skip parse call log";
        return;
    }

    Json::UInt call_log_size = call_logs.size();
    AK_LOG_INFO << "Mac=" << mac << ";CallLogCnt=" << call_log_cnt << ";CallLogSize=" << call_log_size;

    ResidentDev dev;
    int ret = 0;
    if (dbinterface::ResidentDevices::GetMacDev(mac, dev) != 0)
    {
        if (dbinterface::ResidentPerDevices::GetMacDev(mac, dev) != 0)
        {
            AK_LOG_WARN << "MAC=" << mac << " Not Found Devices Info.";
            return;
        }
    }
    std::string db_delivery_uuid;
    if (dev.is_personal == 1)
    {
        db_delivery_uuid = dbinterface::ProjectUserManage::GetLogDeliveryUUIDByAccount(dev.node);
    }
    else
    {
        db_delivery_uuid = dev.project_uuid;
    }

    for (Json::UInt i = 0; i < call_log_size; i++)
    {
        Json::Value call_log = call_logs[i];
        AKCS_CALL_HISTORY call_history;
        memset(&call_history, 0, sizeof(call_history));

        call_history.is_ip_call = 1;
        if (call_log.isMember("Caller"))
        {
            Snprintf(call_history.caller_sip, sizeof(call_history.caller_sip), call_log["Caller"].asCString());
        }
        if (call_log.isMember("Callee"))
        {
            Snprintf(call_history.callee_sip, sizeof(call_history.callee_sip), call_log["Callee"].asCString());
        }
        if (call_log.isMember("CallTraceID"))
        {
            Snprintf(call_history.call_trace_id, sizeof(call_history.call_trace_id), call_log["CallTraceID"].asCString());
        }
        if (call_log.isMember("CallerName"))
        {
            Snprintf(call_history.caller_name, sizeof(call_history.caller_name), call_log["CallerName"].asCString());
        }
        if (call_log.isMember("CalleeName"))
        {
            Snprintf(call_history.callee_name, sizeof(call_history.callee_name), call_log["CalleeName"].asCString());
        }
        // if (call_log.isMember("IsGroupCall"))
        // {
        //     call_history.is_group_call = call_log["IsGroupCall"].asInt();
        // }
        if (call_log.isMember("IsAnswer"))
        {
            if (call_log["IsAnswer"].asInt())
            {
                Snprintf(call_history.called_sip, sizeof(call_history.called_sip), call_log["Callee"].asCString());
                if (call_log.isMember("AnswerTimeStamp"))
                {
                    Snprintf(call_history.answer_time, sizeof(call_history.answer_time), StampToStandardTime(call_log["AnswerTimeStamp"].asInt()).c_str());
                }
            }
        }
        if (call_log.isMember("Duration"))
        {
            call_history.bill_second = call_log["Duration"].asInt();
        }
        if (call_log.isMember("StartTimeStamp"))
        {
            Snprintf(call_history.start_time, sizeof(call_history.start_time), StampToStandardTime(call_log["StartTimeStamp"].asInt()).c_str());
        }
        // if (call_log.isMember("TraceID"))
        // {
        //     Snprintf(call_history.trace_id, sizeof(call_history.trace_id), call_log["TraceID"].asCString());
        // }
        Snprintf(call_history.db_delivery_uuid, sizeof(call_history.db_delivery_uuid), db_delivery_uuid.c_str());
        AK_LOG_INFO << "Caller=" << call_history.caller_sip << ";Callee=" << call_history.callee_sip 
                    << ";CallTraceID=" << call_history.call_trace_id << ";CallerName=" << call_history.caller_name 
                    << ";CalleeName=" << call_history.callee_name
                    << ";Duration=" << call_history.bill_second << ";StartTime=" << call_history.start_time 
                    << ";AnswerTime=" << call_history.answer_time 
                    << ";DBDeliveryUUID=" << call_history.db_delivery_uuid
                    // << ";TraceID=" << call_history.trace_id
                    ;

        PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcClientInstanceByCallTraceID(call_history.call_trace_id);
        if (rpc_client)
        {
            rpc_client->WriteCallHistory(&call_history, call_history.call_trace_id);
        }
    }
}
