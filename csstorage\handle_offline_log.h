﻿#ifndef _HANDLE_OFFLINE_LOG_H_
#define _HANDLE_OFFLINE_LOG_H_

#include <memory>
#include <vector>
#include <string>
#include <set>
#include <boost/noncopyable.hpp>
#include "json/json.h"
#include "storage_mng.h"
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"

class CHandleOfflineLog
{
public:
    CHandleOfflineLog(CStorageMng* storage_mng_ptr)
    {
        storage_mng_ptr_ = storage_mng_ptr;
    }

    static bool IsTagGzFile(const std::string &file_name);

    void HandleTarGzFiles(std::vector<std::string> vec_file, const std::string &csstorage_data_dir);

private:
    int HandleOneTarGzFile(const std::string &file_name, const std::string &csstorage_data_dir);

    void TransferLockdownResp(SOCKET_MSG_DEV_REPORT_ACTIVITY& activity);

    std::string ExtractFile(const std::string &file_name, const std::string &csstorage_data_dir, const std::string &mac);

    bool IsJsonFile(const std::string &file_name);

    void GetAllFile(const char *data_dir, const std::string &mac);

    void RemoveDir(const std::string &dir);

    int ParseJsonFile(const char *file_full_name, const std::set<std::string> &pic_files, const std::set<std::string> &video_files, const std::string &mac, const char *data_dir);

    void ParseCaptureLog(const Json::Value &root, const std::set<std::string> &pic_files, const std::set<std::string> &video_files, const std::string &mac, const char *data_dir);

    void ParseDoorLog(const Json::Value &root, const std::set<std::string> &pic_files, const std::set<std::string> &video_files, const std::string &mac, const char *data_dir);

    long GetTimeFromPicName(const std::string &pic_name);

    void HandleRetryFile(const std::string& mac, const std::string& filename);

    void GetAccessMode(SOCKET_MSG_DEV_REPORT_ACTIVITY& activity);

    void GetDoorNameList(SOCKET_MSG_DEV_REPORT_ACTIVITY& activity, const std::string& mac);

    int UploadDoorlogImageFile(const std::string& mac, const std::set<std::string>& pic_files, SOCKET_MSG_DEV_REPORT_ACTIVITY& activity, const char *data_dir);
    
    int UploadDoorlogVideoFile(const std::string& mac, const std::set<std::string>& video_files, SOCKET_MSG_DEV_REPORT_ACTIVITY& activity, const char *data_dir);

    int UploadCaptureImageFile(const std::string& mac, const std::set<std::string>& pic_files, SOCKET_MSG_CALL_CAPTURE& capture, const char *data_dir);

    int UploadCaptureVideoFile(const std::string& mac, const std::set<std::string>& video_files, SOCKET_MSG_CALL_CAPTURE& capture, const char *data_dir);

    void GetAllOfflineCallFile(const char *data_dir, const std::string &mac);

    int ParseCallJsonFile(const char *file_full_name, const std::string &mac, const char *data_dir);

    void ParseCallLog(const Json::Value &root, const std::string &mac, const char *data_dir);

    CStorageMng* storage_mng_ptr_;
};

#endif

