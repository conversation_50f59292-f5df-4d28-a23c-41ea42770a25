﻿#include "handle_pdu.h"
#include "AkcsMsgDef.h"
#include "AkcsPduBase.h"
#include "AK.Adapt.pb.h"
#include "AK.Server.pb.h"
#include "util.h"
#include "AkLogging.h"
#include "storage_mng.h"
#include "personal_capture.h"
#include "handle_voice_msg.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/PersonalCapture.h"



//defined at csadapt\src\Extern\include\csp2a_common.h
#define MSG_P2A_STORAGE             0x00400000
enum
{
    MSG_P2A_STORAGE_PERSONNAL_DEL_DEV = MSG_P2A_STORAGE + 10,
    MSG_P2A_STORAGE_PERSONNAL_DEL_PIC = MSG_P2A_STORAGE + 12,
    MSG_P2A_STORAGE_NOTIFY_COMMUNITY_MESSAGE = MSG_P2A_STORAGE + 1001,
    MSG_P2A_STORAGE_TIMER_MESSAGE = MSG_P2A_STORAGE + 9999
};

extern CStorageMng* g_storage_mng_ptr;

CHandlePdu& CHandlePdu::GetInstance()
{
    static CHandlePdu handle_pdu;
    return handle_pdu;
}

void CHandlePdu::Init()
{
    notify_t_ = std::thread(&CHandlePdu::ProcessNotify, this);
    AK_LOG_INFO << "ProcessNotify Thread Start Success,thread_id=" << notify_t_.get_id();
    return;
}

void CHandlePdu::AddNotify()
{
    std::unique_lock<std::mutex> lock(notify_pdus_mtx_);
    notify_pdus_cv_.notify_all();
}

void CHandlePdu::ProcessNotify()
{
    while (1)
    {
        {
            std::unique_lock<std::mutex> lock(notify_pdus_mtx_);
            notify_pdus_cv_.wait(lock);
        }
        HandleTimerMsg();
    }
}

void CHandlePdu::ConsumePduMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    uint32_t msg_id = pdu->GetCommandId();
    switch (msg_id)
    {
        case MSG_P2A_STORAGE_PERSONNAL_DEL_PIC:
            // HandlePersonalDelPicMsg(pdu);
            break;
        case MSG_P2A_STORAGE_NOTIFY_COMMUNITY_MESSAGE:
            // HandleCommunityDelPicMsg(pdu);
            break;
        case MSG_P2A_STORAGE_PERSONNAL_DEL_DEV:
            // HandlePerDelDevPicsMsg(pdu);
            break;
        default:
            AK_LOG_WARN << "csstorage mq msg,invalid pdumsg id=" << msg_id;
            break;
    }
}


void CHandlePdu::HandlePersonalDelPicMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::PerDelPic msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt perosnal del pic msg:" << msg.DebugString();

    std::string str_urls(msg.pic_url());
    std::vector<std::string> vec_urls;
    SplitString(str_urls, ";", vec_urls);
    for (const auto& url : vec_urls)
    {
        if (url.length() > 0)
        {
            g_storage_mng_ptr->DeleteFile(url);
        }
    }
}

void CHandlePdu::HandleCommunityDelPicMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt community del pic msg:" << msg.DebugString();

    std::set<std::string> del_urls;
    dbinterface::PersonalCapture::DelCapturePicByMngid(msg.mng_id(), del_urls);
    dbinterface::PersonalCapture::DelMotionPicByMngid(msg.mng_id(), del_urls);
    for (const auto& url : del_urls)
    {
        if (url.length() > 0)
        {
            g_storage_mng_ptr->DeleteFile(url);
        }
    }
}

void CHandlePdu::HandlePerDelDevPicsMsg(const std::shared_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::PerDelDev msg;

    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    AK_LOG_INFO << "receive csadapt per del dev pic msg:" << msg.DebugString();

    std::set<std::string> del_urls;
    dbinterface::PersonalCapture::DelCapturePic(msg.mac(), del_urls);
    dbinterface::PersonalCapture::DelMotionPic(msg.mac(), del_urls);
    for (const auto& url : del_urls)
    {
        if (url.length() > 0)
        {
            g_storage_mng_ptr->DeleteFile(url);
        }
    }
}

void CHandlePdu::HandleTimerMsg()
{
    PersonalCapture::GetInstance()->DelCapturePicExpired();
    PersonalCapture::GetInstance()->DelMotionPicExpired();
    //语音留言过期处理
    CHandleVoiceMsg handle = CHandleVoiceMsg(g_storage_mng_ptr);
    handle.DelVoiceMsgExpired();
}

