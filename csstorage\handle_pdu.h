﻿#ifndef _HANDLE_PDU_H_
#define _HANDLE_PDU_H_

#include <memory>
#include <boost/noncopyable.hpp>
#include <thread>
#include <mutex>
#include <condition_variable>


class CAkcsPdu;
class CHandlePdu: private boost::noncopyable
{
public:
    static CHandlePdu& GetInstance();

    void ConsumePduMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandleTimerMsg();

    void Init();
    void ProcessNotify();
    void AddNotify();
private:
    void HandlePersonalDelPicMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandleCommunityDelPicMsg(const std::shared_ptr<CAkcsPdu>& pdu);

    void HandlePerDelDevPicsMsg(const std::shared_ptr<CAkcsPdu>& pdu);

private:
    std::mutex notify_pdus_mtx_;
    std::condition_variable notify_pdus_cv_;
    std::thread notify_t_;
};

#endif

