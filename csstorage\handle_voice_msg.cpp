﻿#include "handle_voice_msg.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkLogging.h"
#include "storage_mng.h"
#include "personal_capture.h"
#include "common/storage_util.h"
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <fstream>
#include <mutex>
#include "model/CommonModel.h"
#include "storage_mng.h"
#include "thumbnail.h"
#include "storage_ser.h"
#include "storage_mq.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/InterfaceComm.h"
#include "storage_s3.h"
#include "encrypt/Md5.h"
#include "upload_retry_control.h"



extern AKCS_CONF gstAKCSConf;
extern StorageS3Mng* g_storage_s3mng_ptr;
extern char FTP_FILE_INVALID[];


void CHandleVoiceMsg::HandleMp3Files(std::vector<std::string> vec_file, const std::string &csstorage_data_dir, 
    VoiceUpdateFailMap& time2voice_media_files)
{
    
    for (const auto& file_tmp : vec_file)
    {
        int ret = 0;
        AK_LOG_INFO << "begin to upload dev's voice msg [" << file_tmp << "] to storage srv";
        std::string ftp_client_ip;
        std::string original_file_name;//去掉-IP-XXX,获取原始文件名
        csstorage::common::TruncFtpFileIPInfo(file_tmp, ftp_client_ip, original_file_name);
        ::rename(file_tmp.c_str(), original_file_name.c_str());
        std::string file = original_file_name;

        size_t wav_pos = file.find(".wav");

        std::vector<std::string> contents;
        SplitString(file, "-", contents);
        
        //mac_wav = "A62333230313-1678784633-VoiceDev-515e73f90e03fd43a1c7e428e5b326a3.wav";
        //uuid_wav = "na-04a181e5399f11eda88f00163e047e78-1678784633-VD-515e73f90e03fd43a1c7e428e5b326a3.wav"; 新版本用uuid, uuid里面会包含一个-号
        if (contents.size() != 4 && contents.size() != 5) 
        {
            AK_LOG_WARN << "the name of dev's voice msg is wrong, [" << file << "]";
            ::remove(file.c_str());
            continue;
        }

        std::string mac;
        std::string uuid;
        std::string content;
        std::string time_stamp;
        std::string file_name_md5;
        if (contents.size() == 4)
        {
            mac = contents[0];
            time_stamp = contents[1];
            file_name_md5 = csstorage::common::GetFileNameMd5(contents[3]);
        }
        else if (contents.size() == 5)
        {
            uuid = contents[0] + "-" + contents[1];
            time_stamp = contents[2];
            file_name_md5 = csstorage::common::GetFileNameMd5(contents[4]);
        }
        int project_type = project::NONE;
        if (std::string::npos != wav_pos)
        {
           if (contents.size() == 5)//安全整改校验uuid版本
           {
               if (0 != csstorage::common::CheckOneFileMd5(file, uuid, time_stamp, file_name_md5, FILE_TYPE_WAV) 
                   || 0 != GetPersonalCaptureInstance()->GetMacByUUID(uuid, mac, project_type))
               {
                   AK_LOG_WARN << "The name of dev's voice msg Md5 check wrong, [" << file << "], origi file is " <<original_file_name << " ip is " << ftp_client_ip;
                   //删除掉本地文件
                   ::remove(file.c_str());
                   //进入攻击的防护流程中
                   csstorage::common::AddBussiness(FTP_FILE_INVALID, ftp_client_ip);
                   SendVoiceFileAckMsg(mac, file, ret, project_type);
                   continue;
               }
           }
           else if (contents.size() == 4)
           {
                if(0 != csstorage::common::CheckOneFileMd5(file, mac, time_stamp, file_name_md5, FILE_TYPE_WAV))
                {
                    SendVoiceFileAckMsg(mac, file, ret, project_type);
                    AK_LOG_WARN << "The name of dev's voice msg Md5 check wrong, [" << file << "], origi file is " <<original_file_name << " ip is " << ftp_client_ip;
                    ::remove(file.c_str());
                    continue;
                }
                GetPersonalCaptureInstance()->GetMacProject(mac, project_type);
           }
        }
        
        long size = 0;
        {
            FILE* fp = fopen(file.c_str(), "r");
            if (!fp)
            {
                continue;
            }                
            fseek(fp, 0L, SEEK_END);
            size = ftell(fp);
            fclose(fp);
        }

        std::string file_url;
        std::string file_path;
        if (gstAKCSConf.store_fdfs)
        {
            if (storage_mng_ptr_->UploadFile(file.c_str(), file_url) != 0)
            {
                AK_LOG_WARN << "failed to upload file, file name is [" << file << "]";
                ::remove(file.c_str());
                SendVoiceFileAckMsg(mac, file, ret, project_type);
                continue;
            }
            file_path = file_url;
        }
        else
        {
            if (g_storage_s3mng_ptr->UploadVoiceFile(file, file_url) != 0)
            {
                UPLOAD_RETRY_FILE_INFO fileinfo;
                memset(&fileinfo, 0, sizeof(fileinfo));

                fileinfo.error_code = UPLOAD_VOICE_FILE_TO_S3_ERROR;
                Snprintf(fileinfo.filename, sizeof(fileinfo.filename), original_file_name.c_str());

                // 加入到重传队列中
                GetUploadRetryHandlerInstance()->AddReUploadFile(fileinfo);
                
                AK_LOG_WARN << "failed to upload voice file, file name is [" << file << "]";
                ::remove(file.c_str());
                ret = 1;
                SendVoiceFileAckMsg(mac, file, ret, project_type);
                continue;
            }
            file_path = file_url;
        }

        AK_LOG_INFO << "succeed to upload file,file name is [" << file << "], remote file url is [" << file_url << "], file size is " << size;

        if (0 != dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgFileUrl(file_path, file))
        {
            //存储文件与设备的对应关系,后面5s之后重试
            time_t timer;
            timer = ::time(nullptr);
            std::pair<std::string, std::string> voice_file_pair(file, file_path);
            time2voice_media_files.insert(std::pair<time_t, std::pair<std::string, std::string>>(timer, voice_file_pair));
        }
        //删除掉本地文件
        ::remove(file.c_str());
        ret = 1;
        SendVoiceFileAckMsg(mac, file, ret, project_type);
    }

}

void CHandleVoiceMsg::HandleUpdateUrlFail(VoiceUpdateFailMap& time2voice_media_files, VoiceUpdateFailMap& time2voice_pic_files)
{
    time_t time_now;
    time_now = ::time(nullptr);
    //处理语音留言文件上传到服务器，但设备还未上报消息的问题
    std::map<time_t, std::pair<std::string, std::string>>::iterator it;
    for (it = time2voice_media_files.begin(); it != time2voice_media_files.end();)  //从时间最小的刷
    {
        time_t time_tmp = it->first;
        if ((time_now < time_tmp) || (time_now - time_tmp > 20))  //大于20s
        {
            time2voice_media_files.erase(it++);
        }
        else if (time_now - time_tmp > 5) //5 < time_diff < 20s
        {
            std::string filename = it->second.first;
            std::string fileurl =  it->second.second;
            dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgFileUrl(fileurl, filename); //失败不再重试
            time2voice_media_files.erase(it++);
        }
        else
        {
            it++;
        }
    }

    //处理语音留言截图上传到服务器，但设备还未上报消息的问题
    std::map<time_t, std::pair<std::string, std::string>>::iterator index;
    for (index = time2voice_pic_files.begin(); index != time2voice_pic_files.end();)  //从时间最小的刷
    {
        time_t time_tmp = index->first;
        if ((time_now < time_tmp) || (time_now - time_tmp > 20))  //大于20s
        {
            time2voice_pic_files.erase(index++);
        }
        else if (time_now - time_tmp > 5) //5 < time_diff < 20s
        {
            std::string filename = index->second.first;
            std::string fileurl =  index->second.second;
            dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgPicUrl(fileurl, filename); //失败不再重试
            time2voice_pic_files.erase(index++);
        }
        else
        {
            index++;
        }
    }
}

bool CHandleVoiceMsg::DelVoiceMsgExpired()
{
    int id = 0;
    int count = 0;
    do
    {   
        std::vector<std::string> del_urls;
        count = dbinterface::PersonalVoiceMsg::GetExpiredVoiceMsgUrls(del_urls, id);
        for (auto &url : del_urls)
        {
            if (url.length() > 0)
            {
                storage_mng_ptr_->DeleteFile(url);
            }
        }
        sleep(10);
    }while (count > 0);

    return 0;
}





