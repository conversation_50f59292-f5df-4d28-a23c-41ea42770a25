﻿#ifndef _HANDLE_VOICE_MSG_H_
#define _HANDLE_VOICE_MSG_H_

#include <memory>
#include <vector>
#include <string>
#include <set>
#include <boost/noncopyable.hpp>
#include "json/json.h"
#include "storage_mng.h"
#include "storage_util.h"
#include "AkcsCommonDef.h"

typedef std::map<time_t, std::pair<std::string, std::string>>VoiceUpdateFailMap;

class CHandleVoiceMsg
{
public:
    CHandleVoiceMsg(CStorageMng* storage_mng_ptr)
    {
        storage_mng_ptr_ = storage_mng_ptr;
    }
    static bool IsWavFile(const std::string &file_name);
    void HandleMp3Files(std::vector<std::string> vec_file, const std::string &csstorage_data_dir, 
        VoiceUpdateFailMap& time2voice_media_files);
    void HandleUpdateUrlFail(VoiceUpdateFailMap& time2voice_media_files, VoiceUpdateFailMap& time2voice_pic_files);
    bool DelVoiceMsgExpired();
private:

    CStorageMng* storage_mng_ptr_;
};

#endif

