#include "stdafx.h"
#include "util.h"
#include "DeviceSetting.h"
#include "AkcsMysqlSegFlag.h"
#include "ConnectionPool.h"
#include "Utility.h"
#include "AkcsCommonDef.h"
#include "AkLogging.h"


#define TABLE_NAME_DEVICES  "Devices"

CDeviceSetting* GetDeviceSettingInstance()
{
    return CDeviceSetting::GetInstance();
}

CDeviceSetting::CDeviceSetting()
{

}

CDeviceSetting::~CDeviceSetting()
{

}

CDeviceSetting* CDeviceSetting::instance = NULL;

CDeviceSetting* CDeviceSetting::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceSetting();
    }

    return instance;
}

int CDeviceSetting::GetDeviceSettingFromDev(const ResidentDev& dev, DEVICE_SETTING* device_setting)
{
    if (strlen(dev.mac))
    {
        device_setting->id = dev.id;
        device_setting->type = dev.dev_type;
        device_setting->manager_account_id = dev.project_mng_id;
        device_setting->unit_id = dev.unit_id;
        Snprintf(device_setting->device_node, sizeof(device_setting->device_node), dev.node);
        Snprintf(device_setting->mac, sizeof(device_setting->mac), dev.mac);
        device_setting->grade = dev.grade;
        if (device_setting->grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            device_setting->is_public = 1;
        }
        device_setting->is_personal = 0;

        Snprintf(device_setting->ip_addr, sizeof(device_setting->ip_addr), dev.ipaddr);
        Snprintf(device_setting->gateway, sizeof(device_setting->gateway), dev.gateway);
        Snprintf(device_setting->subnet_mask, sizeof(device_setting->subnet_mask), dev.subnet_mask);
        Snprintf(device_setting->primary_dns, sizeof(device_setting->primary_dns), dev.primary_dns);
        Snprintf(device_setting->secondary_dns, sizeof(device_setting->secondary_dns), dev.secondary_dns);
        Snprintf(device_setting->SWVer, sizeof(device_setting->SWVer), dev.sw_ver);
        Snprintf(device_setting->HWVer, sizeof(device_setting->HWVer), dev.hw_ver);
        device_setting->status = dev.status;
        Snprintf(device_setting->outer_ip, sizeof(device_setting->outer_ip), dev.outer_ip);
        device_setting->port = dev.port;
        Snprintf(device_setting->last_connection, sizeof(device_setting->last_connection), dev.last_connection);
        Snprintf(device_setting->private_key_md5, sizeof(device_setting->private_key_md5), dev.private_key_md5);
        Snprintf(device_setting->rf_id_md5, sizeof(device_setting->rf_id_md5), dev.rf_id_md5);
        Snprintf(device_setting->config_md5, sizeof(device_setting->config_md5), dev.config_md5);
        Snprintf(device_setting->sip_account, sizeof(device_setting->sip_account), dev.sip);
        Snprintf(device_setting->contact_md5, sizeof(device_setting->contact_md5), dev.contact_md5);
        device_setting->dclient_version = dev.dclient_ver;
        Snprintf(device_setting->location, sizeof(device_setting->location), dev.location);
        Snprintf(device_setting->auth_code, sizeof(device_setting->auth_code), dev.auth_code);
        Snprintf(device_setting->face_md5, sizeof(device_setting->face_md5), dev.face_md5);
        Snprintf(device_setting->schedule_md5, sizeof(device_setting->schedule_md5), dev.schedule_md5);
        Snprintf(device_setting->user_meta_md5, sizeof(device_setting->user_meta_md5), dev.user_mate_md5);        
        device_setting->flags = dev.flags;
        device_setting->project_type = dev.project_type;
        Snprintf(device_setting->uuid, sizeof(device_setting->uuid), dev.uuid);
        device_setting->brand = dev.brand;
        device_setting->is_ipv6 = dev.is_ipv6;
        device_setting->dynamics_iv = dev.is_dy_iv;
        device_setting->repost = dev.repost; 
        device_setting->firmware = dev.firmware;
        device_setting->fun_bit = dev.fun_bit;
    
        return 0;
    }

    return -1;
}

int CDeviceSetting::GetPerDeviceSettingFromDev(const ResidentDev& dev, DEVICE_SETTING* device_setting)
{
    if (strlen(dev.mac))
    {
        device_setting->id = dev.id;
        device_setting->type = dev.dev_type;
        //插入社区
        Snprintf(device_setting->community, sizeof(device_setting->community), dev.community);
        //modify bu chenyc,2017-07-07,不再补齐地址节点
        //Snprintf(device_setting->device_node, sizeof(device_setting->device_node)/sizeof(TCHAR), device_node.GetBuffer());
        Snprintf(device_setting->device_node, sizeof(device_setting->device_node), dev.node);
        device_setting->extension = dev.extension;
        Snprintf(device_setting->ip_addr, sizeof(device_setting->ip_addr), dev.ipaddr);
        Snprintf(device_setting->gateway, sizeof(device_setting->gateway), dev.gateway);
        Snprintf(device_setting->subnet_mask, sizeof(device_setting->subnet_mask), dev.subnet_mask);
        Snprintf(device_setting->primary_dns, sizeof(device_setting->primary_dns), dev.primary_dns);
        Snprintf(device_setting->secondary_dns, sizeof(device_setting->secondary_dns), dev.secondary_dns);
        Snprintf(device_setting->mac, sizeof(device_setting->mac), dev.mac);
        Snprintf(device_setting->SWVer, sizeof(device_setting->SWVer), dev.sw_ver);
        Snprintf(device_setting->HWVer, sizeof(device_setting->HWVer), dev.hw_ver);
        device_setting->status = dev.status;
        Snprintf(device_setting->outer_ip, sizeof(device_setting->outer_ip), dev.outer_ip);
        device_setting->port = dev.port;
        Snprintf(device_setting->last_connection, sizeof(device_setting->last_connection), dev.last_connection);
        Snprintf(device_setting->private_key_md5, sizeof(device_setting->private_key_md5), dev.private_key_md5);
        Snprintf(device_setting->rf_id_md5, sizeof(device_setting->rf_id_md5), dev.rf_id_md5);
        Snprintf(device_setting->config_md5, sizeof(device_setting->config_md5), dev.config_md5);
        //把sip账号也查出来,方便后续组装个人终端用户的联动单元
        Snprintf(device_setting->sip_account, sizeof(device_setting->sip_account), dev.sip);
        Snprintf(device_setting->sip_password, sizeof(device_setting->sip_password), dev.sippwd);
        Snprintf(device_setting->rtsp_password, sizeof(device_setting->rtsp_password), dev.rtsppwd);
        Snprintf(device_setting->location, sizeof(device_setting->location), dev.location);
        Snprintf(device_setting->contact_md5, sizeof(device_setting->contact_md5), dev.contact_md5);
        device_setting->dclient_version = dev.dclient_ver;
        device_setting->flag = dev.flag;
        if (device_setting->flag == 1)
        {
            device_setting->is_public = 1;
        }
        Snprintf(device_setting->auth_code, sizeof(device_setting->auth_code), dev.auth_code);
        Snprintf(device_setting->face_md5, sizeof(device_setting->face_md5), dev.face_md5);
        Snprintf(device_setting->schedule_md5, sizeof(device_setting->schedule_md5), dev.schedule_md5);
        Snprintf(device_setting->user_meta_md5, sizeof(device_setting->user_meta_md5), dev.user_mate_md5); 
        device_setting->flags = dev.flags;
        Snprintf(device_setting->uuid, sizeof(device_setting->uuid), dev.uuid);
        device_setting->brand = dev.brand;
        device_setting->is_personal = 1;
        device_setting->is_ipv6 = dev.is_ipv6;
        device_setting->dynamics_iv = dev.is_dy_iv;
        device_setting->repost = dev.repost;
        device_setting->firmware = dev.firmware;
        device_setting->fun_bit = dev.fun_bit;

        //查询管理员id
        dbinterface::AccountInfo account;
        if (dbinterface::Account::GetAccountInfoByAccount(dev.community, account) == 0)
        {
            device_setting->manager_account_id = account.id;
            Snprintf(device_setting->project_uuid, sizeof(device_setting->project_uuid), account.uuid);
        }

        return 0;
    }

    return -1;
}

//根据MAC获取设备设置信息
int CDeviceSetting::GetDeviceSettingByMac(const std::string& mac, DEVICE_SETTING* device_setting)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (dbinterface::ResidentDevices::GetMacDev(mac, dev) == 0)
    {
        GetDeviceSettingFromDev(dev, device_setting);
        if (device_setting->project_type == project::OFFICE)
        {
            device_setting->device_type = csmain::OFFICE_DEV;  //住宅设备
        }
        else 
        {
            device_setting->device_type = csmain::COMMUNITY_DEV;  //社区设备
        }
        return 0;
    }
    else if (dbinterface::ResidentPerDevices::GetMacDev(mac, dev) == 0)
    {
        GetPerDeviceSettingFromDev(dev, device_setting);
        device_setting->project_type = project::PERSONAL;
        device_setting->device_type = csmain::PERSONNAL_DEV;  //个人终端设备
        return 0;
    }

    return -1;//查询到空值
}




