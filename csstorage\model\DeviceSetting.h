#ifndef __DEVICE_SETTING_H__
#define __DEVICE_SETTING_H__
#include <string>
#include "util_cstring.h"
#include "model/CommonModel.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/Account.h"


class CDeviceSetting
{
public:
    CDeviceSetting();
    ~CDeviceSetting();
    static CDeviceSetting* GetInstance();
    int GetDeviceSettingFromDev(const ResidentDev& dev, DEVICE_SETTING* device_setting);
    int GetPerDeviceSettingFromDev(const ResidentDev& dev, DEVICE_SETTING* device_setting);
    int GetDeviceSettingByMac(const std::string& mac, DEVICE_SETTING* device_setting);
private:
    static CDeviceSetting* instance;

};
CDeviceSetting* GetDeviceSettingInstance();

#endif
