#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <cstdlib>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "util.h"
#include "AES256.h"
#include "CachePool.h"
#include "ConnectionPool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "route_mq_produce.h"
#include "control/FileCacheManager.h"
#include "storage_ser.h"
#include "MetricService.h"


//全局变量
extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}

void BuildPicCacheListMetrics(std::string& response)
{
    std::string pic_cache_list = GetFileCacheManagerInstace()->GetPicCacheList();
    std::stringstream data;
    data  << pic_cache_list << "\n";
    response += data.str();
}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void HttpReqPicCacheListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string response;
    BuildPicCacheListMetrics(response);
    cb(response);
    return;
}

void startHttpServer()
{
    const int port = 9991;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网

    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler); 
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    server.RegisterHandler("/pic_cache_list", HttpReqPicCacheListCallback);
    
    server.Init(port);
    server.Start();
    
    return;
}
