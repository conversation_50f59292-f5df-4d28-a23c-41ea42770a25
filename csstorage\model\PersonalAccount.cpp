#include "stdafx.h"
#include <sstream>
#include "PersonalAccount.h"
#include "RedisForPhone.h"
#include "util.h"
#include "ConnectionPool.h"
#include "dbinterface/CommunityInfo.h"
#include "Utility.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


#define TABLE_NAME_BIND_CODE    "BindCode"
#define BIND_CODE_LIST          "Code,BindTime,IMEI,Status"
#define COMMUNITY_AREANODE      "Community,AreaNode"

CPersonalAccount* GetPersonalAccountInstance()
{
    return CPersonalAccount::GetInstance();
}

CPersonalAccount* CPersonalAccount::instance = NULL;

CPersonalAccount* CPersonalAccount::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonalAccount();
    }

    return instance;
}

//社区/单住户
int CPersonalAccount::DaoGetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& name, std::string& node, int& manager_id)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    ResidentPerAccount main_account;
    memset(&main_account, 0, sizeof(main_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            node = uid;
            name = account.name;
            manager_id = account.parent_id;
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
        {
            name = account.name;
            node = main_account.account;
            manager_id = main_account.parent_id;
        }
    }
    else
    {
        return -1;
    }

    return 0;
}
