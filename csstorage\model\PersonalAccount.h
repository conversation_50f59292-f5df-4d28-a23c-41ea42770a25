#ifndef __PERSONNAL_ACCOUNT_H__
#define __PERSONNAL_ACCOUNT_H__

#include <boost/noncopyable.hpp>
//#include "SDMCMsg.h"
#include <set>
#include <vector>
#include <string>
#include "model/CommonModel.h"

namespace csmain
{
enum SipAccountType
{
    PERSONAL_SIP,
    GROUP_SIP,    
};

/*
新：
SmartPlus and indoor monitors：直接呼室内机+App
Phone and indoor monitors：直接呼室内机+Phone
SmartPlus and indoor monitors, with phone as backup：先呼室内机+App，无应答再呼Phone
Indoor monitors with SmartPlus as backup：先呼室内机，再呼App
Indoor monitors with phone as backup：先呼室内机，再呼Phone
Indoor monitors with SmartPlus as backup, finally the phone：先呼室内机，再呼App，无应答再呼Phone

*/
enum NODE_CALL_TYPE
{
    //callloop=0
    NODE_CALL_TYPE_APP_INDOOR = 0,//旧：呼叫该家庭下的SmartPlus+室内机(同时呼叫)app主账号不移出群组响铃
    //V4.3 落地号码+室内机+APP
    //V4.4 改为室内机+phone 用dclient版本判断
    //callloop=2
    NODE_CALL_TYPE_INDOOR_PHONE = 1,//旧：呼叫该家庭（主账户）的落地号码+室内机(同时呼叫)app主账号移出群组响铃(相当之前的开启落地)
    //callloop=1
    NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE = 2,//先呼叫SmartPlus+室内机(同时呼叫)，若未接听，再呼叫落地号码。app主账号不移出群组响铃
    //callloop=2以下
    NODE_CALL_TYPE_INDOOR_BACK_APP = 3,
    NODE_CALL_TYPE_INDOOR_BACK_PHONE = 4,
    NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE = 5,


    NODE_CALL_TYPE_INDOOR_PHONE_OLD = 100,//设备是V4.4时候，代码NODE_CALL_TYPE_INDOOR_PHONE=2 替换为这个
};

}

typedef struct PhoneInfo_T{
    char name[256];
    char node[16];
    char account[16];
    unsigned int mng_id;//社区id或者个人管理员id
    int role;
}PhoneInfo;


class CPersonalAccount : public boost::noncopyable
{
public:
    CPersonalAccount()
    {
    }
    ~CPersonalAccount()
    {
    }

    int DaoGetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& name, std::string& node, int& manager_id);

    static CPersonalAccount* GetInstance();
private:

    static CPersonalAccount* instance;

};

CPersonalAccount* GetPersonalAccountInstance();

#endif //__PERSONNAL_ACCOUNT_H__
