#include "stdafx.h"
#include <sstream>
#include "RedisForPhone.h"
#include "CachePool.h"
#include "util.h"
/*
存储phone和acount的关系，用hash存储
key是phone的后7位，不足的就按实际位数
   field是实际的phone value是node:account
*/
const static char* PHONE_REDIS_CONN_NAME = "phone_account";



/*
account sip账号
node  主账号sip账号
*/
int CacheSetPhoneInfo(const std::string& phone, const std::string& account, const std::string& node)
{
    std::string key;
    key = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);
    if (!(key.length() > 0))
    {
        AK_LOG_WARN << "phone is blank!";
        return -1;
    }

    CacheManager* pCacheManager = CacheManager::getInstance();
    CacheConn* pCacheConn = pCacheManager->GetCacheConn(PHONE_REDIS_CONN_NAME); //获取与redis实例的tcp连接
    if (pCacheConn)
    {
        std::string value = node;
        value += ":";
        value += account;
        AK_LOG_INFO << "[redisForPhone] hset phone value, key is " << key << ", phone is " << phone << ", value is " <<  value;
        long ret = pCacheConn->hset(key, phone, value);
        if (ret == -1)
        {
            AK_LOG_WARN << "hset phone failed, phone is " << phone << ", node is " <<  node;
        }
        pCacheManager->RelCacheConn(pCacheConn);
    }
    else
    {
        AK_LOG_WARN << "no cache connection for csmain " << PHONE_REDIS_CONN_NAME;
    }
    return 0;
}


bool CacheGetPhoneNodeAndAccount(const std::string& phone, std::string& node, std::string& account)
{
    std::string key;
    key = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);
    if (!(key.length() > 0))
    {
        AK_LOG_WARN << "phone is blank!";
        return false;
    }

    CacheManager* pCacheManager = CacheManager::getInstance();
    CacheConn* pCacheConn = pCacheManager->GetCacheConn(PHONE_REDIS_CONN_NAME); //获取与redis实例的tcp连接
    if (pCacheConn)
    {
        map<string, string> ret_value;
        AK_LOG_INFO << "[redisForPhone] hgetAll phone, key " << key;
        if (pCacheConn->hgetAll(key, ret_value) && ret_value.size() > 0)
        {
            int size = ret_value.size();
            if (size == 2)//key只有一对
            {
                for (auto& v : ret_value)
                {
                    size_t pos = v.second.find(":");
                    node = v.second.substr(0, pos - 1);
                    account = v.second.substr(pos);
                }
            }
            else if (size > 2) //取最长匹配值
            {
                int matchnum = 0;
                for (auto& v : ret_value)
                {
                    int num = GetStrMatchNumFromBehind(phone, v.first);
                    if (num > matchnum)
                    {
                        matchnum = num;
                        size_t pos = v.second.find(":");
                        node = v.second.substr(0, pos - 1);
                        account = v.second.substr(pos);
                    }
                }
            }
            pCacheManager->RelCacheConn(pCacheConn);
            return true;

        }

        pCacheManager->RelCacheConn(pCacheConn);
        return false;
    }
    return false;

}



bool CachePhoneIsExist(const std::string& phone)
{
    std::string key;
    key = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);
    if (!(key.length() > 0))
    {
        AK_LOG_WARN << "phone is blank!";
        return -1;
    }

    CacheManager* pCacheManager = CacheManager::getInstance();
    CacheConn* pCacheConn = pCacheManager->GetCacheConn(PHONE_REDIS_CONN_NAME); //获取与redis实例的tcp连接
    if (pCacheConn)
    {
        return pCacheConn->isExists(key);
    }
    else
    {
        return false;
    }
}


bool CachePhoneDelete(const std::string& phone)
{
    std::string key;
    key = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);
    if (!(key.length() > 0))
    {
        AK_LOG_WARN << "phone is blank!";
        return -1;
    }

    CacheManager* pCacheManager = CacheManager::getInstance();
    CacheConn* pCacheConn = pCacheManager->GetCacheConn(PHONE_REDIS_CONN_NAME); //获取与redis实例的tcp连接
    if (pCacheConn)
    {
        AK_LOG_INFO << "[redisForPhone] hdel phone, key is " << key << ", value is " <<  phone;
        return pCacheConn->hdel(key, phone);
    }
    else
    {
        return false;
    }
}








