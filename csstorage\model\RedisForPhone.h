#ifndef __REDIS_FOR_PHONE_H__
#define __REDIS_FOR_PHONE_H__

#include <boost/noncopyable.hpp>
//#include "SDMCMsg.h"
#include <string>
#define PHONE_DETECT_NUM    (7)


int CacheSetPhoneInfo(const std::string& phone, const std::string& account, const std::string& node);
bool CacheGetPhoneNodeAndAccount(const std::string& phone, std::string& node, std::string& account);
bool CachePhoneIsExist(const std::string& phone);
bool CachePhoneDelete(const std::string& phone);

int GetStrMatchNumFromBehind(const std::string& src, const std::string& dect);





#endif //__REDIS_FOR_PHONE_H__
