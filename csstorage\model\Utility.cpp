#include "stdafx.h"
#include <iomanip>
#include <sstream>
#include <random>
#include <sys/time.h>
#include "Utility.h"
#include <boost/algorithm/string/replace.hpp>
#include "AkcsCommonDef.h"
#include "util.h"

void GetCurTime(TIME_DATA* pTimeData)
{
    if (pTimeData == NULL)
    {
        return;
    }

    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    pTimeData->nYear = 1900 + p->tm_year;
    pTimeData->nMonth = p->tm_mon + 1;
    pTimeData->nDay = p->tm_mday;
    pTimeData->nDayOfWeek = p->tm_wday;
    pTimeData->nHour = p->tm_hour;
    pTimeData->nMin = p->tm_min;
    pTimeData->nSec = p->tm_sec;

    return;
}

VOID GetCurTime(INOUT char* pszDate, IN int nSize)
{
    if (pszDate == NULL)
    {
        return;
    }
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);
    snprintf(pszDate, nSize, "%d-%02d-%02d %02d:%02d:%02d", (1900 + p->tm_year), (p->tm_mon + 1), p->tm_mday,
             p->tm_hour, p->tm_min, p->tm_sec);
    return;
}


int TransUtf8ToTchar(const char *pszSrc, TCHAR *pszDst, int nDstSize)
{
    if (pszSrc == NULL
        || pszDst == NULL
        || nDstSize <= 0)
    {
        return -1;
    }
#if 0
    //utf-8 to unicode
    MultiByteToWideChar(CP_UTF8,0,(LPCSTR)pszSrc,-1,pszDst,nDstSize);
#endif
    Snprintf(pszDst, nDstSize,  pszSrc);
    return 0;
}

//获取当前时间
CString GetCurTime()
{
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    CString strCurTime;
    //strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), curTime.wYear, curTime.wMonth, curTime.wDay, curTime.wHour, curTime.wMinute, curTime.wSecond);
    strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), (1900 + p->tm_year), (p->tm_mon + 1), p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    return strCurTime;
}
CString GetStringFromUtf8(const char* pszUtf8)
{
    CString str = _T("");
    if (pszUtf8 == NULL)
    {
        return str;
    }
    int len = strlen(pszUtf8) + 1;

    TCHAR* pwszString = new TCHAR[len];
    TransUtf8ToTchar(pszUtf8, pwszString, len);
    str = pwszString;

    delete []pwszString;

    return str;
}


void ParseConfigItem(const std::string &msg, SOCKET_MSG_CONFIG &socket_msg_config)
{
    socket_msg_config.config_count = 0;
    std::stringstream stream(msg);

    while (1) {
        std::string line;
        std::getline(stream, line);
        if (socket_msg_config.config_count >= CONFIG_MODULE_ITEM_NUM)
        {
            break;
        }

        if (line.find("=") != std::string::npos)
        {
            Snprintf(socket_msg_config.module.item[socket_msg_config.config_count], CONFIG_MODULE_ITEM_SIZE,  line.c_str());
            ++socket_msg_config.config_count;
        }

        if (!stream.good())
        {
            break;
        }
    }
    return;
}

