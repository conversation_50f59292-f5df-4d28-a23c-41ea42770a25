#include <sstream>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "personal_capture.h"
#include "storage_mng.h"
#include <set>
#include <boost/algorithm/string.hpp>
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"

#define TABLE_NAME_BIND_CODE    "BindCode"
#define BIND_CODE_LIST          "Code,BindTime,IMEI,Status"
#define COMMUNITY_AREANODE      "Community,AreaNode"
static const char per_capture[] = "PersonalCapture";
static const char per_motion[] = "PersonalMotion";
static const char temperature[] = "Temperature";

extern CStorageMng* g_storage_mng_ptr;
extern LOG_SLICE_INFO gCaptureSliceInfo;
extern LOG_SLICE_INFO gMotionSliceInfo;

PersonalCapture* GetPersonalCaptureInstance()
{
    return PersonalCapture::GetInstance();
}

PersonalCapture* PersonalCapture::instance = NULL;

PersonalCapture* PersonalCapture::GetInstance()
{
    if (instance == NULL)
    {
        instance = new PersonalCapture();
    }

    return instance;
}

/*通过mac地址查询设备的Dclient版本号，Add by czw*/
int PersonalCapture::GetDclientVerByMac(const std::string& mac)
{
    int version = 0;
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    ResidentDev per_dev;
    memset(&per_dev, 0, sizeof(per_dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        version = dev.dclient_ver;
        return version;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        version = per_dev.dclient_ver;
        return version;
    }

    return -1;
}

int PersonalCapture::DelCapturePicExpired()
{
    std::vector<std::string> akcs_tables;
    std::vector<std::string> log_tables;
    dbinterface::PersonalCapture::GetPicExpiredTables(per_capture, gCaptureSliceInfo, akcs_tables, log_tables);
    //不存在akcs表了，因此要不再处理删除akcs的capture表
    /*
    for (auto& table : akcs_tables)
    {
        DelOneAkcsPicExpired(table);
    }
    */

    for (auto& table : log_tables)
    {
        DelOneLogPicExpired(table);
    }

    return 0;
}

int PersonalCapture::DelMotionPicExpired()
{
    std::vector<std::string> akcs_tables;
    std::vector<std::string> log_tables;
    dbinterface::PersonalCapture::GetPicExpiredTables(per_motion, gCaptureSliceInfo, akcs_tables, log_tables);
    //不存在akcs表了，因此要不再处理删除akcs的capture表
    /*
    for (auto& table : akcs_tables)
    {
        DelOneAkcsPicExpired(table);
    }
    */

    for (auto& table : log_tables)
    {
        DelOneLogPicExpired(table);
    }

    return 0;
}

int PersonalCapture::DelOneAkcsPicExpired(const std::string &akcs_table_name)
{
    std::string id = "0";
    while (DelOneAkcsTablePicExpired(akcs_table_name, id) > 0)
    {
        sleep(10);
    }

    return 0;
}

int PersonalCapture::DelOneLogPicExpired(const std::string &log_table_name)
{
    std::string id = "0";
    while (DelOneLogTablePicExpired(log_table_name, id) > 0)
    {
        sleep(10);
    }

    return 0;
}

int PersonalCapture::DelOneAkcsTablePicExpired(const std::string &akcs_table_name, std::string& id)
{
    int count = 0;
    std::set<std::string> del_urls;
    count = dbinterface::PersonalCapture::delAkcsPicExpired(akcs_table_name, id, del_urls);
    for (const auto &url : del_urls)
    {
        if (url.length() > 0)
        {
            g_storage_mng_ptr->DeleteFile(url);
        }
    }
    return count;
}

int PersonalCapture::DelOneLogTablePicExpired(const std::string &log_table_name, std::string& id)
{
    int count = 0;
    std::set<std::string> del_urls;
    count = dbinterface::PersonalCapture::delLOGPicExpired(log_table_name, gCaptureSliceInfo, id, del_urls);
    for (const auto &url : del_urls)
    {
        if (url.length() > 0)
        {
            g_storage_mng_ptr->DeleteFile(url);
        }
    }
    return count;
}

int PersonalCapture::GetMacByUUID(const std::string& uuid, std::string&mac, int &project_type)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    ResidentDev per_dev;
    memset(&per_dev, 0, sizeof(per_dev));
    if (0 == dbinterface::ResidentDevices::GetUUIDDev(uuid, dev))
    {
        mac = dev.mac;
        project_type = dev.project_type;
        return 0;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetUUIDDev(uuid, per_dev))
    {
        mac = per_dev.mac;
        project_type = per_dev.project_type;
        return 0;
    }

    return -1;
}

int PersonalCapture::GetMacProject(const std::string&mac, int &project_type)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    ResidentDev per_dev;
    memset(&per_dev, 0, sizeof(per_dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        project_type = dev.project_type;
        return 0;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        project_type = per_dev.project_type;
        return 0;
    }

    return -1;

}


