#ifndef __CSSTORAGE_PERSONNAL_CAPTURE_H__
#define __CSSTORAGE_PERSONNAL_CAPTURE_H__

#include <string>
#include <vector>
#include <boost/noncopyable.hpp>
#include "model/CommonModel.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/PersonalCapture.h"



enum CaptureType
{
    MOTION = 0,
    TEMPERATURE = 1,
};

class CRldb;
class PersonalCapture : public boost::noncopyable
{
public:
    PersonalCapture()
    {
    }
    ~PersonalCapture()
    {
    }
    int GetDclientVerByMac(const std::string& mac);

    int DelCapturePicExpired();
    int DelMotionPicExpired();

    int GetMacByUUID(const std::string& uuid, std::string&mac, int &project_typ);
    int GetMacProject(const std::string&mac, int &project_type);
    static PersonalCapture* GetInstance();
private:

    static PersonalCapture* instance;
    int DelOneAkcsPicExpired(const std::string &akcs_table_name);
    int DelOneLogPicExpired(const std::string &log_table_name);
    int DelOneAkcsTablePicExpired(const std::string &akcs_table_name, std::string& id);
    int DelOneLogTablePicExpired(const std::string &log_table_name, std::string& id);

};

PersonalCapture* GetPersonalCaptureInstance();

#endif //__CSSTORAGE_PERSONNAL_CAPTURE_H__
