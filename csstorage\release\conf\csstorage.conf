#csstorage conf
#akcs db conf
akcs_db_ip=***********
akcs_db_port=3306
akcs_db_database=AKCS

#log db conf
log_db_ip=***********
log_db_port=3306
log_db_database=LOG

#common db conf
db_username=dbuser01
nsq_storage_channel=ch0
nsq_storage_topic=delpic

group_name=group1

capture_path_num=5
capture_path=/usr/local/akcs/csstorage/data

#etcd相关配置信息
etcd_srv_net=http://***********:8507

nsq_route_topic=ak_route

#存储到fdfs 还是oss
store_fdfs=0

#截图是否存储到s3/oss
store_s3=0

log_encrypt=0
log_trace=1

file_consumer_thread_num=8

pic_cache_size=10000
wav_cache_size=10000
video_cache_size=10000

log_db_pool_size=8
akcs_db_pool_size=8
