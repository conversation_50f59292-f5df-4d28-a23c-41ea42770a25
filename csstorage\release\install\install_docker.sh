#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME_CSSTORAGE="csstorage" 
CONTAINER_NAME_CSSTORAGE_OFFLINE="csstorage_offline"

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csstorage   # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csstorage
LOG_PATH=/var/log/csstoragelog
CTRL_SCRIPT=csstoragectl.sh
RUN_SCRIPT=csstoragerun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME."

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

ENV_CONF_PARAM="
-e ENABLE_AKCS_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
-e ENABLE_LOG_DBPROXY=$(grep_conf 'ENABLE_LOG_DBPROXY' $INSTALL_CONF)
-e AKCS_DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
-e LOG_DBPROXY_INNER_IP=$(grep_conf 'LOG_DBPROXY_INNER_IP' $INSTALL_CONF)
-e AKCS_MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
-e LOG_MYSQL_INNER_IP=$(grep_conf 'LOG_MYSQL_INNER_IP' $INSTALL_CONF)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
-e FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
-e GROUP_NAME=$(grep_conf 'GROUP_NAME' $INSTALL_CONF)
-e STORAGE_PIC_SAVE_FDFS=$(grep_conf 'STORAGE_PIC_SAVE_FDFS' $INSTALL_CONF)
-e STORAGE_PIC_SAVE_S3=$(grep_conf 'STORAGE_PIC_SAVE_S3' $INSTALL_CONF)
-e REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
-e ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
-e SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
"

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "创建存放ftp的文件夹 $APP_HOME/ftp/data"
mkdir -p $APP_HOME/ftp/data
mkdir -p $APP_HOME/ftp/offlinelog
mkdir -p $APP_HOME/ftp/data/offlinelog
mkdir -p $APP_HOME/ftp/data/retry

ENV_LOAD_PARAM="
-v /usr/share/zoneinfo:/usr/share/zoneinfo
-v /var/log/csstoragelog:/var/log/csstoragelog
-v /var/core:/var/core
-v /etc/ip:/etc/ip
-v /etc/kdc.conf:/etc/kdc.conf 
-v /bin/crypto:/bin/crypto
-v /usr/local/akcs/csstorage/ftp:/usr/local/akcs/csstorage/ftp
-v /etc/oss_install.conf:/etc/oss_install.conf
"

echo ${ENV_CONF_PARAM};

# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------

if [ `docker ps -a | grep -w $CONTAINER_NAME_CSSTORAGE | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME_CSSTORAGE)
    docker stop $CONTAINER_NAME_CSSTORAGE;
    docker rm -f $CONTAINER_NAME_CSSTORAGE;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 csstorage"
    app_pids=$(pidof csstorage || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi

    sed -i '/csstoragerun.sh/d' /etc/init.d/rc.local
fi


if [ `docker ps -a | grep -w $CONTAINER_NAME_CSSTORAGE_OFFLINE | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME_CSSTORAGE_OFFLINE)
    docker stop $CONTAINER_NAME_CSSTORAGE_OFFLINE;
    docker rm -f $CONTAINER_NAME_CSSTORAGE_OFFLINE;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi

    echo "停止服务 csstorage_offline"
    app_pids=$(pidof csstorage_offline || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi
    
    sed -i '/csstoragerun.sh/d' /etc/init.d/rc.local
fi

docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} ${ENV_LOAD_PARAM} --restart=always --net=host --name ${CONTAINER_NAME_CSSTORAGE} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csstorage/scripts/csstoragerun.sh ${CONTAINER_NAME_CSSTORAGE}
docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} ${ENV_LOAD_PARAM} --restart=always --net=host --name ${CONTAINER_NAME_CSSTORAGE_OFFLINE} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csstorage/scripts/csstoragerun.sh ${CONTAINER_NAME_CSSTORAGE_OFFLINE}

#守护进程中会进行环境变量替换配置文件中的内容
#具体看csstorage/scripts/sedconf.sh
