#!/bin/sh

CSSTORAGE_INSTALL_PATH=/usr/local/akcs/csstorage
CSSTORAGE_RUN_SCRIPT_NAME=csstoragerun.sh
CSSTORAGE_RUN_SCRIPT=${CSSTORAGE_INSTALL_PATH}/scripts/${CSSTORAGE_RUN_SCRIPT_NAME}


INSTALL_CONF=/etc/csstorage_install.conf
HOST_IP=/etc/ip
PWD=`pwd`
PAKCAGES_ROOT=${PWD}/..
chmod 777 -R ${PAKCAGES_ROOT}/*

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null; 
    #IP地址必须为全数字 
    if [ $? -ne 0 ] 
    then 
        return 1 
    fi 
    ipaddr=$1 
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值 
    b=`echo $ipaddr|awk -F . '{print $2}'` 
    c=`echo $ipaddr|awk -F . '{print $3}'` 
    d=`echo $ipaddr|awk -F . '{print $4}'` 
    for num in $a $b $c $d 
    do 
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间 
        then 
            return 1 
        fi 
    done 
    return 0 
}
EchoHostIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
    
    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4
        
    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
} 
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6;    

    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}
EnterBasicSrvIPAddr()
{
    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;
    
    #输入etcd内网IP
    yellow "Enter your etcd cluster servers inner IPV4,(eg:************:5204;************:15204;...): \c"
    read ETCD_INNER_IP_PORT;

    #输入FDFS内网IP
    yellow "Enter your FDFS server inner IPV4: \c"
    read FDFS_INNER_IP;
	
	yellow "Enter your fdfs storage GROUP_NAME: \c"
    read GROUP_NAME;
	    
    for ip in $REDIS_INNER_IP $MYSQL_INNER_IP; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
    echo "ETCD_INNER_IP_PORT=$ETCD_INNER_IP_PORT" >>$INSTALL_CONF
    echo "FDFS_INNER_IP=$FDFS_INNER_IP" >>$INSTALL_CONF
	echo "GROUP_NAME=$GROUP_NAME" >>$INSTALL_CONF
}


function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0
	
	fi
}
#added by chenyc,2019-03-29,分布式脚本，先确定本机的内外网地址,所有的ip信息放在:/etc/ip里面
if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    
    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi
EchoBasicSrvIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip
    
    name_str="ETCD_INNER_IP_PORT="
    name_cat=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP_PORT | awk -F'=' '{ print $2 }'`
    name=$name_str$name_cat
    echo $name
    
    fdfs_inner_ip_str="FDFS_INNER_IP="
    fdfs_inner_ip_cat=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    fdfs_inner_ip=$fdfs_inner_ip_str$fdfs_inner_ip_cat
    echo $fdfs_inner_ip
	
	name_str="GROUP_NAME="
    name_cat=`cat $INSTALL_CONF | grep -w GROUP_NAME | awk -F'=' '{ print $2 }'`
    name=$name_str$name_cat
    echo $name	
		
}
#再确定redis、mysql、etcd、nsqlookupd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    ETCD_INNER_IP_PORT=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP_PORT | awk -F'=' '{ print $2 }'`
    FDFS_INNER_IP=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    GROUP_NAME=`cat $INSTALL_CONF | grep -w GROUP_NAME | awk -F'=' '{ print $2 }'`
	yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/csstorage_install.conf>, please enter all information below:" 
    EnterBasicSrvIPAddr
fi

sed -i "s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP_PORT}/g" ${PAKCAGES_ROOT}/csstorage/conf/csstorage.conf
sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" ${PAKCAGES_ROOT}/csstorage/conf/csstorage.conf
#注意:http.tracker_server_port=8090不能被替换了,tracker用内网ip即可.
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" ${PAKCAGES_ROOT}/csstorage/conf/csstorage_fdfs.conf
if [ -n "${GROUP_NAME}" ];then
	sed -i "s/^.*group_name=.*/group_name=${GROUP_NAME}/g" ${PAKCAGES_ROOT}/csstorage/conf/csstorage.conf
fi
bash $PWD/dbproxy-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csstorage/conf/csstorage.conf

scriptpid=`ps -fe|grep "${CSSTORAGE_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${CSSTORAGE_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi

echo "stopping csstorage services..."
${PAKCAGES_ROOT}/csstorage_scripts/csstoragectl.sh stop
sleep 1

if [ ! -d /var/log/csstoragelog ]; then
    mkdir -p /var/log/csstoragelog
fi

echo "copying akcs csstorage files..."

if [ -d /usr/local/akcs/csstorage ]; then
    #rm -rf /usr/local/akcs/csstorage //不能删除 因为更新时候还是有可能上传图片的
	:
fi

chmod 777 -R /usr/local/akcs/

mkdir -p /usr/local/akcs/csstorage/scripts
chmod -R 777 /usr/local/akcs/csstorage/scripts/

cp -rfp ${PAKCAGES_ROOT}/csstorage/ /usr/local/akcs
cp -rf ${PAKCAGES_ROOT}/csstorage_scripts/* /usr/local/akcs/csstorage/scripts/
ln -sf /usr/local/akcs/csstorage/lib/libevpp.so /usr/local/akcs/csstorage/lib/libevpp.so.0.7

#csstorage
chmod 777 ${CSSTORAGE_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${CSSTORAGE_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup bash ${CSSTORAGE_RUN_SCRIPT} >/dev/null 2>&1 &
fi

echo "starting services..."
chmod 777 /usr/local/akcs/csstorage/scripts/csstoragectl.sh
/usr/local/akcs/csstorage/scripts/csstoragectl.sh start

ln -s /var/log/csstoragelog/fdfs_client.log /var/log/csstoragelog/fdfs_client.INFO 

echo "csstorage install completed ..."

#echo status
/usr/local/akcs/csstorage/scripts/csstoragectl.sh status






