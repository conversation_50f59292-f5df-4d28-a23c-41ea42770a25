#!/bin/bash
ACMD="$1"
CSSTORAGE_BIN='/usr/local/akcs/csstorage/bin/csstorage'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csstorage()
{
    nohup $CSSTORAGE_BIN >/dev/null 2>&1 &
    echo "Start csstorage successful"
    if [ -z "`ps -fe|grep "csstoragerun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csstorage/scripts/csstoragerun.sh >/dev/null 2>&1 &
    fi
}
stop_csstorage()
{
    echo "Begin to stop csstoragerun.sh"
    csstoragerunid=`ps aux | grep -w csstoragerun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csstoragerunid}" ];then
	    echo "csstoragerun.sh is running at ${csstoragerunid}, will kill it first."
	    kill -9 ${csstoragerunid}
    fi
    echo "Begin to stop csstorage"
    kill -9 `pidof csstorage`
    sleep 2
    echo "Stop csstorage successful"
}

pid=`cat /var/run/csstorage.pid || echo '0'`
case $ACMD in
  start)
    if [ ! -d /proc/$pid ];
    then
        start_csstorage
    else
        echo "csstorage is already running"
    fi
    ;;
  stop)
    if [ ! -d /proc/$pid ];
    then
        echo "csstorage is already stopping"
    else
        stop_csstorage
    fi
    ;;
  restart)
    stop_csstorage
    sleep 1
    start_csstorage
    ;;
  status)
    if [ ! -d /proc/$pid ];
    then
        echo "\033[1;31;05m csstorage is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csstorage is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

