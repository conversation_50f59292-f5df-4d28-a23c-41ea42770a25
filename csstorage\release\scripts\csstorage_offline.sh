#!/bin/bash
ACMD="$1"
CSSTORAGE_BIN='/usr/local/akcs/csstorage/bin/csstorage_offline'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csstorage()
{
    nohup $CSSTORAGE_BIN >/dev/null 2>&1 &
    echo "Start csstorage_offline successful"
    if [ -z "`ps -fe|grep "csstoragerun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csstorage/scripts/csstoragerun.sh >/dev/null 2>&1 &
    fi
}
stop_csstorage()
{
    echo "Begin to stop csstoragerun.sh"
    csstoragerunid=`ps aux | grep -w csstoragerun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csstoragerunid}" ];then
	    echo "csstoragerun.sh is running at ${csstoragerunid}, will kill it first."
	    kill -9 ${csstoragerunid}
    fi
    echo "Begin to stop csstorage_offline"
    kill -9 `pidof csstorage_offline`
    sleep 2
    echo "Stop csstorage_offline successful"
}

pid=`cat /var/run/csstorage_offline.pid || echo '0'`
case $ACMD in
  start)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csstorage
    else
        echo "csstorage_offline is already running"
    fi
    ;;
  stop)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csstorage_offline is already stopping"
    else
        stop_csstorage
    fi
    ;;
  restart)
    stop_csstorage
    sleep 1
    start_csstorage
    ;;
  status)
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csstorage_offline is stop!!!\033[0m"
    else
        echo "\033[0;32m csstorage_offline is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

