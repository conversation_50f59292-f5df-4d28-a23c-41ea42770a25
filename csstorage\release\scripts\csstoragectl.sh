#!/bin/bash
ACMD="$1"
WORK_DIR="/usr/local/akcs/csstorage/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_akcs()
{
    sh -x ${WORK_DIR}/csstorage.sh start
}
stop_akcs()
{
    sh -x ${WORK_DIR}/csstorage.sh stop
}

status_akcs()
{
    sh -x ${WORK_DIR}/csstorage.sh status
}

uninstall_akcs()
{
    sh ${WORK_DIR}/csstorage.sh stop
    kill -9 `ps aux | grep csstoragerun.sh |grep -v grep | awk '{print $2}'`
    rm -rf /usr/local/akcs/csstorage/
}

case $ACMD in
  start)
        start_akcs
    ;;
  stop)
        stop_akcs
    ;;
  uninstall)
        uninstall_akcs
    ;;
  restart)
    stop_akcs
    sleep 1
    start_akcs
    ;;
  status)
    status_akcs
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status|uninstall"
    ;;
esac
exit

