#!/bin/bash

CONTAINER_NAME=$1

#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/csstorage/scripts/sedconf.sh

# PROCESS_NAME=csstorage
# PROCESS_NAME_2=csstorage_offline
# PROCESS_START_CMD="/usr/local/akcs/csstorage/scripts/csstoragectl.sh start"
# PROCESS_START_CMD_2="/usr/local/akcs/csstorage/scripts/csstorage_offline_ctl.sh start"
# PROCESS_PID_FILE=/var/run/csstorage.pid
# PROCESS_PID_FILE_2=/var/run/csstorage_offline.pid
# LOG_FILE=/var/log/csstorage_run_daemon.log
# PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csstorage/scripts/common.sh"
# LOG_BACK_SCRIPTS="/usr/local/akcs/csstorage/scripts/log_back.sh"
# csstoragelog_path="/var/log/csstoragelog"
CSSTORAGE_BIN='/usr/local/akcs/csstorage/bin/csstorage'
CSSTORAGE_OFFLINE_BIN='/usr/local/akcs/csstorage/bin/csstorage_offline'

# #一定要是绝对路径，不然就变成要指定目录执行这个run
# source $PROCESS_COMMON_SCRIPTS
# source $LOG_BACK_SCRIPTS

if ! command -v curl &> /dev/null; then
    echo "$(date +'%Y-%m-%d %H:%M:%S') curl未安装，正在尝试安装..." >> $LOG_FILE
    apt-get update && apt-get install -y curl > /dev/null 2>&1
fi

#容器化后直接前台运行，这样挂了之后docker也会重启，这样才能监控检测
if [ "$CONTAINER_NAME" == "csstorage_offline" ]; then
    $CSSTORAGE_OFFLINE_BIN >/dev/null 2>&1
elif [ "$CONTAINER_NAME" == "csstorage" ]; then
    $CSSTORAGE_BIN >/dev/null 2>&1
fi

# while [ 1 ]
# do
#     common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
#     sleep 1
# 	common_run_pid_detect $PROCESS_NAME_2 $PROCESS_PID_FILE_2 "$PROCESS_START_CMD_2" $LOG_FILE
#     COMMON_FIRST_RUN=0
# 	sleep 5
# done
