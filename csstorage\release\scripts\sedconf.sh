#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

# csstorage.conf
sed -i "
    s/^.*akcs_db_ip=.*/akcs_db_ip=${AKCS_MYSQL_INNER_IP}/g
    s/^.*log_db_ip=.*/log_db_ip=${LOG_MYSQL_INNER_IP}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    " /usr/local/akcs/csstorage/conf/csstorage.conf

# redis 配置 csstorage_redis.conf
sed -i "
    s/^.*backend_limiting_host=.*/backend_limiting_host=${REDIS_INNER_IP}/g" /usr/local/akcs/csstorage/conf/csstorage_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" /usr/local/akcs/csstorage/conf/csstorage_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" /usr/local/akcs/csstorage/conf/csstorage_redis.conf
fi

# csstorage_fdfs.conf
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" /usr/local/akcs/csstorage/conf/csstorage_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" /usr/local/akcs/csstorage/conf/csstorage_fdfs.conf
fi

if [ -n "${GROUP_NAME}" ];then
	sed -i "s/^.*group_name=.*/group_name=${GROUP_NAME}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
fi

# dbproxy 配置
if [ "$ENABLE_AKCS_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*akcs_db_port=.*/akcs_db_port=3308/g
        s/^.*akcs_db_ip=.*/akcs_db_ip=${AKCS_DBPROXY_INNER_IP}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
else
    sed -i "
        s/^.*akcs_db_port=.*/akcs_db_port=3306/g
        s/^.*akcs_db_ip=.*/akcs_db_ip=${AKCS_MYSQL_INNER_IP}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
fi

if [ "$ENABLE_LOG_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*log_db_port=.*/log_db_port=3308/g
        s/^.*log_db_ip=.*/log_db_ip=${LOG_DBPROXY_INNER_IP}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
else
    sed -i "
        s/^.*log_db_port=.*/log_db_port=3306/g
        s/^.*log_db_ip=.*/log_db_ip=${LOG_MYSQL_INNER_IP}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
fi

sed -i "s/^.*store_fdfs=.*/store_fdfs=${STORAGE_PIC_SAVE_FDFS}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
sed -i "s/^.*store_s3=.*/store_s3=${STORAGE_PIC_SAVE_S3}/g" /usr/local/akcs/csstorage/conf/csstorage.conf
