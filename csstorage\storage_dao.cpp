#include <string.h>
#include "AkLogging.h"
#include "storage_dao.h"
#include "storage_util.h"
#include "personal_capture.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/ParkingLotDoor.h"
#include "dbinterface/Log/ParkingLog.h"
#include "dbinterface/Log/ParkingVehicle.h"

extern LOG_DELIVERY gstAKCSLogDelivery;

//刷新设备截图与图片url的映射关系
int DaoUpatePicUrl(const std::string& mac, const std::string& pic_name, const std::string& pic_url, 
                        const std::string& spic_url, const std::string& project_uuid)
{
    AK_LOG_INFO << "DaoUpatePicUrl mac = " << mac << ", pic_name = " << pic_name << ", pic_url = " <<pic_url <<", spic_url = " << spic_url << ", project_uuid = " << project_uuid;
    if (csstorage::common::IsMotionFile(pic_name))
    {
        return dbinterface::PersonalCapture::UpdateLogMotionPicUrl(mac, pic_name, pic_url, spic_url, gstAKCSLogDelivery.personal_motion_delivery, project_uuid);
    }
    else if (csstorage::common::IsTemperatureFile(pic_name))
    {
        return dbinterface::PersonalCapture::UpdateTempPicUrl(mac, pic_name, pic_url, spic_url);
    }
    else if (std::string::npos != pic_name.find("TC"))
    {
        return (dbinterface::PersonalCapture::UpdateLogCaptureThirdCameraPicUrl(mac, pic_name, pic_url, spic_url, 
            gstAKCSLogDelivery.personal_capture_delivery, project_uuid));
    }
    else
    {
        int res = dbinterface::PersonalCapture::UpdateLogCapturePicUrl(mac, pic_name, pic_url, spic_url, gstAKCSLogDelivery.personal_capture_delivery, project_uuid);
        if (res != 0) {
            return res;
        }
        // 在停车场内更新进入图片，不在停车场内更新出去图片
        if (dbinterface::ParkingLotDoor::ParkingLotDeviceExistByMac(mac) == DatabaseExistenceStatus::EXIST) {
            res = dbinterface::ParkingVehicle::UpdateParkingVehiclePicUrl(mac, pic_name, pic_url, spic_url);
            if (res > 0) {
                return 0;
            }
            res = dbinterface::ParkingLog::UpdateParkingLogPicUrl(mac, pic_name, pic_url, spic_url);
            return res;
        }
        return 0;
    }

    return 0;
}

int DaoUpdateVideoUrl(const std::string& mac, const std::string& video_name, const std::string& video_url, const std::string& project_uuid)
{
    // motion录制
    if (csstorage::common::IsMotionFile(video_name))
    {
        return dbinterface::PersonalCapture::UpdateLogMotionVideoUrl(mac, video_name, video_url, gstAKCSLogDelivery.personal_motion_delivery, project_uuid);
    }
    else
    {
        return dbinterface::PersonalCapture::UpdateLogCaptureVideoUrl(mac, video_name, video_url, gstAKCSLogDelivery.personal_capture_delivery, project_uuid);
    }
    
    return 0;
}

//根据mac查Dclient版本，Add by czw
int DaoSelectDclientVer(const std::string& mac)
{
    return (GetPersonalCaptureInstance()->GetDclientVerByMac(mac));
}


