#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <evpp/event_loop.h>
#include "util.h"
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include "storage_ser.h"


extern AKCS_CONF gstAKCSConf;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern evnsq::Consumer* g_nsq_consumer_client_ptr;
extern const char *g_ak_srv_nsqlookupd;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

static void NsqLookupdSrvInit(const std::set<std::string>& nsqLookupd_addrs)
{
    
    std::vector<std::string> urls;
    for (const auto& nsqLookupd : nsqLookupd_addrs) //ip:port的形式
    {
        std::stringstream lookupd_http_url;
        lookupd_http_url << "http://" << nsqLookupd << "/lookup?topic=" << gstAKCSConf.szNSQTopic;
        urls.push_back(lookupd_http_url.str());
        
    }
    g_nsq_consumer_client_ptr->ConnectToLookupds(urls);
}

void UpdateLookupdSrvList()
{
    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) == 0)
    {
        g_nsq_consumer_client_ptr->reset_nsqlookupd();
        NsqLookupdSrvInit(nsqlookupd_addrs);
    }
}


void EtcdSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.szEtcdServerAddr);//"ip:port;ip:port;..."

    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    NsqLookupdSrvInit(nsqlookupd_addrs);
    
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_nsqlookupd, UpdateLookupdSrvList);
    //开始启动watch loop
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void EtcdHealthCheckInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.szEtcdServerAddr);//"ip:port;ip:port;..."
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get());
    
    g_etcd_loop->Run();
}


