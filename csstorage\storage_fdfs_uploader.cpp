#include "storage_fdfs_uploader.h"
#include "fdfs_client.h"
#include "AkLogging.h"

void StorageFdfsUploader::SetLogFileName(const char* log_filepath)
{
    log_set_filename(log_filepath);
}

int StorageFdfsUploader::DeleteFile(const std::string& remote_filename)
{
    if(remote_filename.size() == 0 || tracker_server_ == nullptr)
    {
        return -1;
    }
    AK_LOG_INFO << "delete file url is [" << remote_filename << "]";
    int result = 0;

    //从 http://120.77.246.187:8090/group1/M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    //分离出group跟file_name
    size_t n = remote_filename.find("group");
    if (std::string::npos == n)
    {
        //add by chenzhx 20221224 上传到oss/s3不删除图片,过期oss/s3自动清理
        return 0;
    }
    //获取到 group1/M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    std::string file = remote_filename.substr(n);
    n = file.find_first_of("/M");
    //获取到 M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    std::string file_remote = file.substr(n + 1);
    std::string group_remote = file.substr(0, n);
    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        //控制锁范围，避免和下面的RefreshTrackerConn产生死锁
        result = storage_delete_file(tracker_server_, NULL, group_remote.c_str(), file_remote.c_str());
    }
    if (result== 0)
    {
        AK_LOG_INFO << "delete file " << remote_filename << " succeed";
    }
    else
    {
        AK_LOG_WARN << "delete file " << remote_filename << " fail, error no:" << result << " error info:" << STRERROR(result);
        if (result == 32) //Broken pipe
        {
            if ((result = RefreshTrackerConn()) != 0)
            {
                AK_LOG_WARN << "Refresh, error no:" << result << " error info:" << STRERROR(result);
                return result;
            }
            else
            {     
                //因为连接断开而删除失败的话，重连成功后再删一次
                std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
                result = storage_delete_file(tracker_server_, NULL, group_remote.c_str(), file_remote.c_str());
            }
        }
    }
    return result;
}
