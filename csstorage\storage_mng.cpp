#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include "base64.h"
#include "sockopt.h"
#include <assert.h>
#include "AkLogging.h"
//#include "AkcsMonitor.h"
#include "storage_mng.h"
#include "storage_ser.h"
#include "util.h"

#define STORAGE_FDFS_LOG_PATH "/var/log/csstoragelog/fdfs_client.log"

extern AKCS_CONF gstAKCSConf;

CStorageMng::CStorageMng(const char* file_name)
{
    uploader_ = std::unique_ptr<StorageFdfsUploader>(new StorageFdfsUploader());
    uploader_->Init(file_name);
    uploader_->SetUploadGroupName(gstAKCSConf.group_name);
}

CStorageMng::~CStorageMng()
{
}

int CStorageMng::Init()
{
    uploader_->SetLogFileName(STORAGE_FDFS_LOG_PATH);
    return 0;
}

int CStorageMng::UploadFile(const char* local_filename, std::string& remote_file)
{
    assert(local_filename);
    int retry_time = 0; //只针对上传这个动作失败的情况不做重试

    if(uploader_->UploadFile(local_filename, remote_file, retry_time) != 0)
    {
        // 针对tracker_server一段时间不用后链接挂了的情况，重试
        return uploader_->UploadFile(local_filename, remote_file, retry_time);
    }
    return 0; //上传成功

}

int CStorageMng::DeleteFile(const std::string& remote_filename)
{
    return uploader_->DeleteFile(remote_filename);
}

