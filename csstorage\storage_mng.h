//author :chenyc
//storage_mng.h

#ifndef __CSSTORAGE_STORAGE_MNG_H__
#define __CSSTORAGE_STORAGE_MNG_H__

#include <string>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "storage_fdfs_uploader.h"
#include <memory>

class CStorageMng : public boost::noncopyable
{
public:
    CStorageMng(const char* file_name);
    ~CStorageMng();
    int Init();
    int UploadFile(const char* local_filename, std::string& remote_file);
    int DeleteFile(const std::string& remote_filename);
private:
    std::unique_ptr<StorageFdfsUploader> uploader_;
};

#endif //__CSSTORAGE_STORAGE_MNG_H__

