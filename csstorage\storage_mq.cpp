#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "AkcsMonitor.h"
#include "route_mq_produce.h"
#include "storage_mq.h"
#include "AK.Server.pb.h"
#include "storage_ser.h"
#include "AkcsMsgDef.h"
#include <evpp/event_loop.h>
#include "util.h"

extern AKCS_CONF gstAKCSConf;
extern RouteMQProduce* g_nsq_producer;

void MQProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    //op.auth_secret = auth_secret;
    evnsq::Producer client(&nsq_loop, op);
    g_nsq_producer = new RouteMQProduce(&client);
    
    client.SetMessageCallback(&OnRouteMQMessage);
    client.SetReadyCallback(std::bind(&RouteMQProduce::OnNSQReady, g_nsq_producer));
    client.SetConnectErrorCallback(std::bind(&RouteMQProduce::OnConnectError, g_nsq_producer, std::placeholders::_1));
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();

}

void SendOfflineHandleAckMsg(const std::string &mac, const std::string &file)
{
    AK_LOG_INFO << "Mac=" << mac << ";file=" <<file << " handle ok";
    //通过nsq,通知csroute进行精准投递
    AK::Server::P2PStorageHandleOfflineAckMsg msg;
    msg.set_mac(mac);
    msg.set_tar_filename(file);
    msg.set_status(0);
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_S2R_P2P_OFFLINE_MSG_ACK_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.szRouteTopic);
    return;
}

void SendVoiceFileAckMsg(const std::string &mac, const std::string &file, int ret, int project_type)
{
    AK_LOG_INFO << "Mac=" << mac << ";file=" <<file << " handle ok";
    //通过nsq,通知csroute进行精准投递
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    msg.set_mac(mac);
    msg.set_filename(file);
    msg.set_result(ret);
    msg.set_project_type(project_type);
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_S2R_P2P_VOICE_MSG_ACK_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.szRouteTopic);
    return;
}

void SendVideoFileAckMsg(const std::string &mac, const std::string &file, int ret, int project_type)
{
    AK_LOG_INFO << "SendVideoFileAckMsg Mac=" << mac << ";file=" <<file << " handle ok";
    //通过nsq,通知csroute进行精准投递
    AK::Server::P2PStorageHandleVoiceAckMsg msg;
    msg.set_mac(mac);
    msg.set_filename(file);
    msg.set_result(ret);
    msg.set_project_type(project_type);
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_S2R_P2P_VOICE_MSG_ACK_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.szRouteTopic);
    return;
}


void SendWebCommonMsg(int msg_type, const std::string& data_json)
{
    AK::Server::P2PRouteToWebMsg msg;
    msg.set_message_type(msg_type);
    msg.set_msg_json(data_json);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_PUSH_WEB_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstAKCSConf.szRouteTopic);
}