//author :chenyc
//storage_mng.h

#ifndef __CSSTORAGE_STORAGE_MQ_H__
#define __CSSTORAGE_STORAGE_MQ_H__

#include <string>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "connection_pool.h"

enum class VoiceMsgAckResult
{
    ACK_RESULT_FAILED = 0,
    ACK_RESULT_SUCCESS = 1,
};

void MQProduceInit();
void SendOfflineHandleAckMsg(const std::string &mac, const std::string &file);
void SendVoiceFileAckMsg(const std::string &mac, const std::string &file, int ret, int project_type);
void SendVideoFileAckMsg(const std::string &mac, const std::string &file, int ret, int project_type);
void SendWebCommonMsg(int msg_type, const std::string& data_json);

#endif //__CSSTORAGE_STORAGE_MNG_H__

