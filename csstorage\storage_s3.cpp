#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include "AkLogging.h"
#include "storage_ser.h"
#include "storage_s3.h"
#include <time.h>
#include "upload_retry_control.h"

static const char kPicRemoteDir[] = "IMAGE";
static const char kVoiceRemoteDir[] = "VOICE";
static const char kVideoRemoteDir[] = "VIDEO";
static const char kUploaderConfigFilePath[] = "/etc/oss_install.conf";

StorageS3Mng::StorageS3Mng(const std::string &local_dir, const std::string &retry_dir):
 local_dir_(local_dir), retry_dir_(retry_dir)
{
    upload_cnt_ = 0;
    reinit_time_count_ = 250;
    InitTimeStr();
    uploader_ = std::unique_ptr<S3Uploader>(new S3Uploader());
    uploader_->Init(kUploaderConfigFilePath);
}

StorageS3Mng::~StorageS3Mng()
{
}

void StorageS3Mng::InitTimeStr()
{
    time_t timep;
    time (&timep);
    char tmp[64];
    char tmp2[64];
    strftime(tmp, sizeof(tmp), "%Y%m%d",localtime(&timep));
    strftime(tmp2, sizeof(tmp2), "%H",localtime(&timep));
    date_ = tmp;
    hour_ = tmp2;
}

int StorageS3Mng::UploadFile(const std::string& filename, const std::string& remote_dir, std::string& remote_file)
{
    std::string local_file_path;
    std::string retry_file_path;
    std::string remote_path;

    // 离线日志filename为绝对路径,正常上传filename为文件名
    size_t index = filename.rfind('/');
    if (index != std::string::npos) {
       std::string filename2 = filename.substr(index+1);
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename2;
       local_file_path = filename; 
       retry_file_path = retry_dir_ + "/" + filename2;
    }
    else
    {
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename;
       local_file_path = local_dir_ + "/" + filename;
       retry_file_path = retry_dir_ + "/" + filename;
    }

    if(uploader_->UploadFile(local_file_path, remote_path, 1) != 0)
    {
        // 重试一次仍然失败,移动文件到retry目录下重传
        ::rename(local_file_path.c_str(), retry_file_path.c_str());
        return -1;
    }
    remote_file = remote_path;
    
    if(upload_cnt_++ > reinit_time_count_)
    {
        upload_cnt_ = 0;
        InitTimeStr();
    }
    return 0;
}

int StorageS3Mng::UploadFileRetry(const std::string& filename, const std::string& remote_dir, std::string& remote_file)
{
    // local file path
    std::string local_file_path;

    // oss file path
    std::string remote_path;
    
    // 离线日志filename为绝对路径,正常上传filename为文件名
    size_t index = filename.rfind('/');
    if (index != std::string::npos) {
       std::string filename2 = filename.substr(index + 1);
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename2;
       local_file_path = retry_dir_ + filename2;
    }
    else
    {
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename;
       local_file_path = retry_dir_ + "/" + filename;
    }
    
    remote_file = remote_path;

    return uploader_->UploadFile(local_file_path, remote_path, 0);//重传则不再进行重试
}

int StorageS3Mng::UploadVideoFile(const std::string& filename, const std::string& remote_dir, std::string& remote_file_path)
{
    std::string local_file_path;
    std::string retry_file_path;
    std::string remote_path;

    // 离线日志filename为绝对路径,正常上传filename为文件名
    size_t index = filename.rfind('/');
    if (index != std::string::npos) 
    {
       std::string filename2 = filename.substr(index+1);
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename2;
       local_file_path = filename; 
       retry_file_path = retry_dir_ + "/" + filename2;
    }
    else
    {
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename;
       local_file_path = local_dir_ + "/" + filename;
       retry_file_path = retry_dir_ + "/" + filename;
    }

    if(uploader_->UploadVideoFile(local_file_path, remote_path, 1) != 0)
    {
        // 重试一次仍然失败,移动文件到retry目录下重传
        ::rename(local_file_path.c_str(), retry_file_path.c_str());
        return -1;
    }
    remote_file_path = remote_path;
    
    if(upload_cnt_++ > reinit_time_count_)
    {
        upload_cnt_ = 0;
        InitTimeStr();
    }
    return 0;
}

int StorageS3Mng::UploadVideoFileRetry(const std::string& filename, const std::string& remote_dir, std::string& remote_file_path)
{
    // local file path
    std::string local_file_path;

    // oss file path
    std::string remote_path;
    
    // 离线日志filename为绝对路径,正常上传filename为文件名
    size_t index = filename.rfind('/');
    if (index != std::string::npos) {
       std::string filename2 = filename.substr(index + 1);
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename2;
       local_file_path = retry_dir_ + filename2;
    }
    else
    {
       remote_path = remote_dir +"/"  + date_ + "/"+ hour_ + "/" + filename;
       local_file_path = retry_dir_ + "/" + filename;
    }
    remote_file_path = remote_path;

    return uploader_->UploadVideoFile(local_file_path, remote_path, 0);
}

int StorageS3Mng::UploadImageFile(const std::string& filename,  std::string& remote_file_path)
{
    return UploadFile(filename, kPicRemoteDir, remote_file_path);
}

int StorageS3Mng::UploadVoiceFile(const std::string& filename,  std::string& remote_file_path)
{
    return UploadFile(filename, kVoiceRemoteDir, remote_file_path);
}

int StorageS3Mng::UploadVideoFile(const std::string& filename,  std::string& remote_file_path)
{
    return UploadVideoFile(filename, kVideoRemoteDir, remote_file_path);
}

int StorageS3Mng::UploadImageFileRetry(const std::string& filename, std::string& remote_file_path)
{
    return UploadFileRetry(filename, kPicRemoteDir, remote_file_path);
}

int StorageS3Mng::UploadVoiceFileRetry(const std::string& filename, std::string& remote_file_path)
{
    return UploadFileRetry(filename, kVoiceRemoteDir, remote_file_path);
}

int StorageS3Mng::UploadVideoFileRetry(const std::string& filename, std::string& remote_file_path)
{
    return UploadVideoFileRetry(filename, kVideoRemoteDir, remote_file_path);
}
