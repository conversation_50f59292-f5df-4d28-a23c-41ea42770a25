//author :chenzhx
//storage_s3.h

#ifndef __CSSTORAGE_S3_H__
#define __CSSTORAGE_S3_H__

//aws
#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>
#include <aws/s3/model/PutObjectRequest.h>
#include <iostream>
#include <fstream>
#include <sys/stat.h>
#include <aws/s3/model/GetObjectRequest.h>
#include <aws/core/http/HttpTypes.h>
//aliyun
#include <alibabacloud/oss/OssClient.h>

#include "s3_uploader.h"
#include <string>

class StorageS3Mng
{
public:
    StorageS3Mng(const std::string &local_dir, const std::string &retry_dir);
    ~StorageS3Mng();
    int UploadImageFile(const std::string& filename,  std::string& remote_file_path);
    int UploadVoiceFile(const std::string& filename,  std::string& remote_file_path);
    int UploadVideoFile(const std::string& filename,  std::string& remote_file_path);
    int UploadImageFileRetry(const std::string& filename,  std::string& remote_file_path);
    int UploadVoiceFileRetry(const std::string& filename,  std::string& remote_file_path);
    int UploadVideoFileRetry(const std::string& filename, std::string& remote_file_path);
private:
    std::unique_ptr<S3Uploader> uploader_;
    int UploadFile(const std::string& filename, const std::string& remote_dir, std::string& remote_file_path);
    int UploadVideoFile(const std::string& filename, const std::string& remote_dir, std::string& remote_file_path);
    int UploadFileRetry(const std::string& filename, const std::string& remote_dir, std::string& remote_file_path);
    int UploadVideoFileRetry(const std::string& filename, const std::string& remote_dir, std::string& remote_file_path);
    void InitTimeStr();
    std::string local_dir_;
    std::string retry_dir_;
    std::string date_;
    std::string hour_;
    int reinit_time_count_;
    int upload_cnt_;//上传个数统计 用于重新计算时间
};

#endif //__CSSTORAGE_S3_H__

