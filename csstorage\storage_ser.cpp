// cs_storage server
// Created on: 2017-12-12
// Author: chenyc

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <vector>
#include <string>
#include <map>
#include <thread>
#include <tuple>
#include <errno.h>
#include "glog/logging.h"
#include "storage_mng.h"
#include "AkLogging.h"
#include "storage_dao.h"
#include "util.h"
#include "redis/PubSubManager.h"
#include "Rldb/RldbQuery.h"
#include "ConfigFileReader.h"
#include "storage_ser.h"
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include "encrypt/Md5.h"
#include "thumbnail.h"
#include "AkcsPduBase.h"
#include "handle_pdu.h"
#include "storage_etcd.h"
#include "handle_offline_log.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "storage_mq.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "handle_voice_msg.h"
#include "AkcsMonitor.h"
#include "AkcsBussiness.h"
#include "storage_util.h"
#include "storage_s3.h"
#include "personal_capture.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/ProjectInfo.h"
#include "LogConnectionPool.h"
#include "dbinterface/InterfaceComm.h"
#include "upload_retry_control.h"
#include "redis/CachePool.h"

#include "HttpServer.h"
#include "Maintenance/Metric.h"
#include "control/UpdateUrlFailControl.h"
#include "control/DirScanner.h"
#include "control/FileProcessControl.h"
#include "control/FileCacheManager.h"
#include <KdcDecrypt.h>
#include "EtcdCliMng.h"
#include "cspbx_rpc_client.h"
#include "cspbx_rpc_client_mng.h"

StorageS3Mng* g_storage_s3mng_ptr = nullptr;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_conf_db_addr;
int g_etcd_dns_res = 0;
CStorageMng* g_storage_mng_ptr = nullptr;

static const char csstorage_conf_file[] = "/usr/local/akcs/csstorage/conf/csstorage.conf";
static const char csstorage_fdfs_conf_file[] = "/usr/local/akcs/csstorage/conf/csstorage_fdfs.conf"; //fdfs客户端配置文件
static const char channel_pic_del[] = "ak_redis_pic_del"; //redis删除图片的发布订阅数据通道


char FTP_FILE_INVALID[] = "csstorage_ftp_file_name_invalid";
const uint32_t BUSSINESS_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t BUSSINESS_NUM = 5;//一段时间内,判断为错误的次数达到5次，即认为是黑客攻击
const uint32_t BUSSINESS_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s

const char FTP_FILE_UPDATE_FAILED[] = "csstorage_ftp_file_update_failed";
const uint32_t BUSSINESS_PERIOD_FILE_UPDATE = 3600;//一个小时,60 * 60s
const uint32_t BUSSINESS_NUM_FILE_UPDATE = 20;//一段时间内,判断为错误的次数达到N次，即认为是黑客攻击
const uint32_t BUSSINESS_KEY_EXPIRE_FILE_UPDATE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s

AKCS_CONF gstAKCSConf;
LOG_DELIVERY gstAKCSLogDelivery;
LOG_SLICE_INFO gCaptureSliceInfo;
LOG_SLICE_INFO gMotionSliceInfo;


evnsq::Consumer* g_nsq_consumer_client_ptr = nullptr;

#define PIDFILE "/var/run/csstorage.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

/*校验图片中带的验证码，Md5(ak_ftp:mac:timestamp),Add by czw*/
bool Md5ValueCheck(std::string& content, std::string& mac, int& is_voice)
{
    size_t pos;
    std::string time_stamp;
    std::string picture_code;
    size_t time_pos = content.find_first_of("_");
    time_stamp = content.substr(0, time_pos);

    AK_LOG_INFO << "unsafe version. mac_or_uuid:" << mac;

    if (std::string::npos == content.find("APP_"))  //0C110503B92D-1564588066_0_APP_校验码.jpg,APP截图带此字段,是csmain命名的
    {
        int dclient_ver = DaoSelectDclientVer(mac);
        if (-1 == dclient_ver)
        {
            return false;   //数据库找不到这个mac
        }
        else if (dclient_ver < 4600)
        {
            return true;    //设备截图低于4.6版本不需要校验
        }

        //6.5兼容语音留言上传
        pos = content.find("VoiceDev_");
        if (std::string::npos == pos)
        {
            pos = content.find("Dev_");
            picture_code = content.substr(pos + 4, content.size() - pos - 8); //要去掉.jpg后缀,Dev_和.jpg共8字节
        }
        else
        {
            picture_code = content.substr(pos + 9, content.size() - pos - 13); //要去掉.jpg后缀,VoiceDev_和.jpg/.mp3共13字节
            is_voice = 1;
        }
        
    }
    else
    {
        pos = content.find("APP_");
        picture_code = content.substr(pos + 4, content.size() - pos - 8);
    }

    std::string temp_string = "ak_ftp:";
    temp_string += mac;
    temp_string += ":";
    temp_string += time_stamp;

    if (picture_code == akuvox_encrypt::MD5(temp_string).toStr())
    {
        return true;    //校验通过
    }

    return false;
}


//检测新版本设备的文件是否上传完整
bool Md5ValueCheckForNewVersion(std::string& content, std::string& uuid, int& is_voice, const std::string& file)
{
    std::string time_stamp;
    size_t time_pos = content.find_first_of("_");
    time_stamp = content.substr(0, time_pos);

    if (std::string::npos != content.find("VD_"))
    {
        //6.5兼容语音留言上传
        is_voice = 1;
    }
    size_t md5_pos = content.find_last_of("_");
    std::string dev_file_md5 = content.substr(md5_pos + 1, 32);

    //AK_LOG_INFO << "content:" << content << ",time_stamp:" << time_stamp << ",uuid:" << uuid << ",dev_file_md5:" << dev_file_md5;
    if (0 == csstorage::common::CheckOneFileMd5(file, uuid, time_stamp, dev_file_md5, FILE_TYPE_JPG))
    {
        return true;
    }
    
    return false;
}

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton(const char* pid_path)
{
    int fd, val;
    char buf[10];
    if ((fd = open(pid_path, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstAKCSConf.akcs_db_ip, gstAKCSConf.akcs_db_port);
    return 0;
}
void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(SrvDbConf));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstAKCSConf.akcs_db_ip) != 0) || (conf_tmp.db_port != gstAKCSConf.akcs_db_port))
    {
        Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip),  conf_tmp.db_ip);
        gstAKCSConf.akcs_db_port = conf_tmp.db_port;
        DaoReInit();
    }
}
int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(SrvDbConf));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip),  conf_tmp.db_ip);
    gstAKCSConf.akcs_db_port = conf_tmp.db_port;
    return 0;
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstAKCSConf.szEtcdServerAddr);

    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

int LogDeliveryInit()
{
    gstAKCSLogDelivery.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    gstAKCSLogDelivery.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    gstAKCSLogDelivery.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    if(gstAKCSLogDelivery.personal_capture_delivery == 0 || gstAKCSLogDelivery.personal_motion_delivery == 0 || gstAKCSLogDelivery.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}

int ConfInit()
{
    CConfigFileReader config_file(csstorage_conf_file);
    //csstorage不需要外网IP
    //Snprintf(gstAKCSConf.szStorageOuterIP, sizeof(gstAKCSConf.szStorageOuterIP),  config_file.GetConfigName("csstorage_outerip"));
    Snprintf(gstAKCSConf.db_username, sizeof(gstAKCSConf.db_username),  config_file.GetConfigName("db_username"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstAKCSConf.db_password, sizeof(gstAKCSConf.db_password), decrypt_db_passwd.c_str());

    Snprintf(gstAKCSConf.akcs_db_database, sizeof(gstAKCSConf.akcs_db_database),  config_file.GetConfigName("akcs_db_database"));
    Snprintf(gstAKCSConf.log_db_database, sizeof(gstAKCSConf.log_db_database),  config_file.GetConfigName("log_db_database"));

    Snprintf(gstAKCSConf.szNSQChannel, sizeof(gstAKCSConf.szNSQChannel),  config_file.GetConfigName("nsq_storage_channel"));
    Snprintf(gstAKCSConf.szNSQTopic, sizeof(gstAKCSConf.szNSQTopic),  config_file.GetConfigName("nsq_storage_topic"));
    Snprintf(gstAKCSConf.szRouteTopic, sizeof(gstAKCSConf.szRouteTopic),  config_file.GetConfigName("nsq_route_topic"));

    Snprintf(gstAKCSConf.group_name, sizeof(gstAKCSConf.group_name),  config_file.GetConfigName("group_name"));

    if(LoadConfFromConfSrv() != 0)
    {
        Snprintf(gstAKCSConf.akcs_db_ip, sizeof(gstAKCSConf.akcs_db_ip),  config_file.GetConfigName("akcs_db_ip"));
        const char* akcs_db_port = config_file.GetConfigName("akcs_db_port");
        gstAKCSConf.akcs_db_port = ATOI(akcs_db_port);
    }

    Snprintf(gstAKCSConf.log_db_ip, sizeof(gstAKCSConf.log_db_ip),  config_file.GetConfigName("log_db_ip"));
    const char* log_db_port = config_file.GetConfigName("log_db_port");
    gstAKCSConf.log_db_port = ATOI(log_db_port);
    const char *bak_offline_file = config_file.GetConfigName("bak_offline_file");
    if (bak_offline_file != NULL && strlen(bak_offline_file) > 0)
    {
        gstAKCSConf.bak_offline_file = ATOI(bak_offline_file);
    }
    else
    {
        gstAKCSConf.bak_offline_file = 0; //默认不备份
    }

    const char *store_fdfs = config_file.GetConfigName("store_fdfs");
    gstAKCSConf.store_fdfs = ATOI(store_fdfs);
    const char *store_s3 = config_file.GetConfigName("store_s3");
    gstAKCSConf.store_s3 = ATOI(store_s3);
    gstAKCSConf.file_consumer_thread_num = ATOI(config_file.GetConfigName("file_consumer_thread_num"));

    CConfigFileReader config_file_s3("/etc/oss_install.conf");
    Snprintf(gstAKCSConf.bucket_name, sizeof(gstAKCSConf.bucket_name),  config_file_s3.GetConfigName("BucketForPic"));
    Snprintf(gstAKCSConf.endpoint, sizeof(gstAKCSConf.endpoint),  config_file_s3.GetConfigName("Endpoint"));
    Snprintf(gstAKCSConf.region_id, sizeof(gstAKCSConf.region_id),  config_file_s3.GetConfigName("RegionID"));
    Snprintf(gstAKCSConf.user, sizeof(gstAKCSConf.user),  config_file_s3.GetConfigName("User"));
    Snprintf(gstAKCSConf.password, sizeof(gstAKCSConf.password),  config_file_s3.GetConfigName("Password"));
    Snprintf(gstAKCSConf.s3_tag, sizeof(gstAKCSConf.s3_tag),  config_file_s3.GetConfigName("OSS_TAG"));
    const char* is_aws = config_file_s3.GetConfigName("IS_AWS");
    gstAKCSConf.is_aws = ATOI(is_aws);
    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    gstAKCSConf.pic_cache_size = ATOI(config_file.GetConfigName("pic_cache_size"));
    gstAKCSConf.wav_cache_size = ATOI(config_file.GetConfigName("wav_cache_size"));
    gstAKCSConf.video_cache_size = ATOI(config_file.GetConfigName("video_cache_size"));

    gstAKCSConf.log_db_pool_size = ATOI(config_file.GetConfigName("log_db_pool_size"));
    gstAKCSConf.akcs_db_pool_size = ATOI(config_file.GetConfigName("akcs_db_pool_size"));
    
    return 0;
}

/* 初始化数据库连接 */
int DaoInit()
{
    LogConnPool* log_conn_pool = GetLogDBConnPollInstance();
    if (NULL == log_conn_pool)
    {
        AK_LOG_WARN << "Aws DaoInit failed.";
        return -1;
    }
    log_conn_pool->Init(gstAKCSConf.log_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.log_db_database, gstAKCSConf.log_db_port, gstAKCSConf.log_db_pool_size, "csstorage");

    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstAKCSConf.akcs_db_ip, gstAKCSConf.db_username, gstAKCSConf.db_password, gstAKCSConf.akcs_db_database, gstAKCSConf.akcs_db_port, gstAKCSConf.akcs_db_pool_size, "csstorage");

    return 0;
}
//
void HandlePicReq(const std::string msg)
{
    //msg就是图片的url
    g_storage_mng_ptr->DeleteFile(msg);
}

//订阅redis 删除文件频道的回调函数
void onSubMessage(redisAsyncContext* c, void* reply, void* privdata)
{
    if ((reply == nullptr) || (c == nullptr))
    {
        return;
    }
    (void)privdata; //warning: unused parameter
    redisReply* r = static_cast<redisReply*>(reply);
    //进程刚拉起首次订阅时,redis的响应是这样子的:
    //1) "subscribe"
    //2) "ak_redis_pic_del"
    //3) (integer) 1

    //进程拉起后订阅频道有消息时,redis的响应是这样子的:
    //1) "message"
    //2) "ak_redis_pic_del"
    //3) "http://**************:8090/group1/M00/00/7D/rBI9elpMRueAMgNwAAEAADm9YUo911.jpg"

    if (r->type == REDIS_REPLY_ARRAY)
    {
        redisReply* file_url = r->element[2]; //获取到url,即订阅频道的消息内容
        if (file_url->type == REDIS_REPLY_INTEGER)
        {
            return;
        }
        else if (file_url->type == REDIS_REPLY_STRING)
        {
            AK_LOG_INFO << " file_url->type ==" << file_url->type;
            AK_LOG_INFO << "file_url->str ==" << file_url->str;
            HandlePicReq(file_url->str);//处理图片删除
        }
    }
}

void SaveOfflineLog()
{
    CStorageMng* storage_mng_ptr = new CStorageMng(csstorage_fdfs_conf_file);
    if (storage_mng_ptr->Init() != 0)
    {
        AK_LOG_FATAL << "init StorageMng failed";
        return;
    }
    CHandleOfflineLog handlog = CHandleOfflineLog(storage_mng_ptr);
    bool is_sleep = false;
    time_t check_file_time = ::time(nullptr); // 检查空文件/缺失文件
    while (!is_sleep)
    {
        DIR* dp;
        struct dirent* entry;
        struct stat statbuf;
        if ((dp = ::opendir(csstorage_offline_dir)) == nullptr)
        {
            AK_LOG_FATAL << "access to csstorage_offline_dir:" << csstorage_offline_dir << " failed";
            return;
        }

        ::chdir(csstorage_offline_dir);
        std::vector<std::string> vec_file;

        while ((entry = ::readdir(dp)) != nullptr)
        {
            // 10分钟检测一次异常文件
            time_t check_time_now = ::time(nullptr);
            if (check_time_now - check_file_time > 10 * 60)
            {
                check_file_time = check_time_now;
                CheckDirAbnormalFile(vsftpd_upload_offlinelog_dir);
            }

            lstat(entry->d_name, &statbuf);
            if (S_ISDIR(statbuf.st_mode))
            {
                continue;
            }

            int file_size = 0;
            FILE* fp = fopen(entry->d_name, "r");
            if (!fp)
            {
                continue;
            }
            fseek(fp, 0L, SEEK_END);
            file_size = ftell(fp);
            fclose(fp);

            if (file_size > 0)
            {
                vec_file.push_back(entry->d_name);
            }
            else
            {
                AK_LOG_WARN << "there is unknow file [" << entry->d_name << "] need to be deleted due to no data long time";
                ::remove(entry->d_name); 
				//added by chenyc,补充说明: 因为在后面的文件名称校验流程会校验文件MD5,指数退避等待文件传输完成
/*                
                if (handlog.IsPicFile(entry->d_name))
                {
                    continue;
                }               

                time_t time_file_modiofy = statbuf.st_mtime;
                time_t time_now;
                time_now = ::time(nullptr);

                //平台最多等待5分钟，如果5分钟内，设备始终没有上传json数据,就删除临时json文件
                if ((time_now > time_file_modiofy) && (time_now - time_file_modiofy > 2 * 60))
                {

                    AK_LOG_WARN << "there is json file [" << entry->d_name << "] need to be deleted due to no data long time";
                    ::remove(entry->d_name);
                }
                else
                {
                    AK_LOG_INFO << "there is json file [" << entry->d_name << "] need to be uploaded to storage srv later";
                }
*/                
            }
        }
        ::closedir(dp);

        if (vec_file.empty())
        {
            sleep(3);
            continue;
        }

        handlog.HandleTarGzFiles(vec_file, csstorage_offline_dir);
    }

}

// store_fdfs为总开关, store_s3控制capture+mp3,       motion一定存s3/oss
int UploadImageFile(CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, const std::string &filename, std::string &big_url, std::string &small_url)
{
    // 上传大图
    int upload_big_ret = UploadImageFileHandler(fdfs_mng_ptr, storage_s3mng_ptr, filename, big_url);
    if (upload_big_ret != 0)
    {
        // 上传大图失败return
        return upload_big_ret;
    }
    
    AK_LOG_INFO << "succeed to upload big file,filename is [" << filename << "], remote file url is [" << big_url;

    // 上传大图成功,获取小图名称
    std::string image_thumbnail = filename.substr(0, filename.size() - 4) + "_cut.jpg";

    // 开始生成小图
    if (true == generate_image_thumbnail(filename.c_str(), image_thumbnail.c_str()))
    {
        // 上传小图
        int upload_small_ret = UploadImageFileHandler(fdfs_mng_ptr, storage_s3mng_ptr, image_thumbnail, small_url);
        if (upload_small_ret != 0)
        {
            // 上传大图成功,删除本地大图
            ::remove(filename.c_str());
            return UPLOAD_SMALL_IMAGE_TO_S3_ERROR;
        }

        // 上传小图成功, 删除本地小图
        ::remove(image_thumbnail.c_str());
        AK_LOG_INFO << "succeed to upload small file,file name is [" << image_thumbnail << "], remote file url is [" << small_url << "]";
    }

    // 上传大图成功,删除本地大图
    ::remove(filename.c_str());
    return 0;
}

int UploadImageFileHandler(CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, const std::string& filename, std::string& file_url)
{
    std::string remote_file_url;

    // store_fdfs = 0: motion一定存储到s3, store_s3 = 1时其他文件也存储到s3
    if (!gstAKCSConf.store_fdfs && (csstorage::common::CheckIsMotionPic(filename) || gstAKCSConf.store_s3))
    {
        if (storage_s3mng_ptr->UploadImageFile(filename, remote_file_url) != 0)
        {
            AK_LOG_WARN << "failed upload s3 file, file name is [" << filename << "]";
            return UPLOAD_BIG_IMAGE_TO_S3_ERROR;
        }
        file_url = remote_file_url;
    }
    else
    {
        // 其他情况都存fdfs
        if (fdfs_mng_ptr && fdfs_mng_ptr->UploadFile(filename.c_str(), remote_file_url) != 0)
        {
            AK_LOG_WARN << "failed upload fdfs file, file name is [" << filename << "]";
            ::remove(filename.c_str());
            return UPLOAD_BIG_IMAGE_TO_FDFS_ERROR;
        }
        //added by chenyc,去掉url前面的ip:port,最后存入数据库的文件格式为:/group1/M00/00/00/eE791FsPrWmAN-KiAAAfdNDetM8875.jpg
        std::size_t pos =  remote_file_url.find("/group");
        if (pos == std::string::npos)
        {
           AK_LOG_WARN << "invalid file url is [" << file_url << "]";
           ::remove(filename.c_str());
           return UPLOAD_BIG_IMAGE_INVALID_URL;
        }
        file_url  = remote_file_url.substr(pos);
    }

    return UPLOAD_IMAGE_SUCCESS;
}

int UploadVideoHandler(CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, const std::string& filepath, std::string& file_url)
{
    if (gstAKCSConf.store_fdfs)
    {
        if (fdfs_mng_ptr->UploadFile(filepath.c_str(), file_url) != 0)
        {
            AK_LOG_WARN << "failed to upload video file, filepath is [" << filepath << "]";
            return -1;
        }
    }
    else
    {
        if (storage_s3mng_ptr->UploadVideoFile(filepath, file_url) != 0)
        {
            AK_LOG_WARN << "failed to upload video file, filepath is [" << filepath << "]";
            return UPLOAD_VIDEO_FILE_TO_S3_ERROR;
        }
    }

    return UPLOAD_IMAGE_SUCCESS;
}

int OnNsqSubMessage(const evnsq::Message* msg)
{
    const static int32_t kAkMsgHoldLen = 4;
    const char* data = msg->body.data();
    const uint32_t size = msg->body.size();
    if (size >= kAkMsgHoldLen)
    {
        std::string body = msg->body.ToString();
        if (body == std::string("crontab"))
        {
            AK_LOG_INFO << "Crontab triggerd";
            
            CHandlePdu::GetInstance().AddNotify();
            return 0;
        }

        const int32_t pb_msg_len = PeekInt32(data, size);
        if (size != (uint32_t)pb_msg_len)
        {
            AK_LOG_WARN << "Invalid evnsq length:" << size;
            return 0;
        }
        else
        {
            std::shared_ptr<CAkcsPdu> pdu(new CAkcsPdu());
            pdu->Write(data, size); //包整体长度全部整进去
            char tmp_buf[sizeof(PduHeader_t)] = {0};
            memcpy(tmp_buf, data, sizeof(tmp_buf));
            if (pdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0)
            {
                AK_LOG_WARN << "Pdu packet header len is invalid";
                return 0;
            }
            CHandlePdu::GetInstance().ConsumePduMsg(pdu);
        }
    }
    else
    {
        AK_LOG_WARN << "Invalid evnsq length " << size;
        return 0;
    }
    return 0;
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstAKCSConf.szEtcdServerAddr, sizeof(gstAKCSConf.szEtcdServerAddr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstAKCSConf.szEtcdServerAddr;
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    CConfigFileReader config_file(csstorage_conf_file);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstAKCSConf.szEtcdServerAddr, sizeof(gstAKCSConf.szEtcdServerAddr),  config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstAKCSConf.szEtcdServerAddr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstAKCSConf.szEtcdServerAddr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}

void onFiveSecondsTimer()
{
    GetUpdateUrlFailControlInstance()->HandleUpdateUrlFail();
}

void TimeTaskInit()
{
    evpp::EventLoop loop;

    loop.RunEvery(evpp::Duration(5.0), onFiveSecondsTimer);
    loop.Run();
}

int RedisInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/csstorage/conf/csstorage_redis.conf", "csstorageCacheInstances");
}



void InitFileProcessThread()
{
    GetFileProcessControlInstace()->Init(gstAKCSConf.file_consumer_thread_num);
}

void PbxRpcClientInit()
{
    std::set<std::string> pbx_grpc_server_addrs;
    if (g_etcd_cli_mng->GetAllPbxRpcInnerSrvs(pbx_grpc_server_addrs) != 0)
    {
        AK_LOG_FATAL << "PbxRpcClientInit connetc to etcd srv fialed";
        return;
    }
    
    for (const auto& pbx_grpc_addr : pbx_grpc_server_addrs)
    {
        auto pbx_rpc_client = std::make_shared<PbxRpcClient>(pbx_grpc_addr);
        PbxRpcClientMng::Instance()->AddPbxRpcSrv(pbx_grpc_addr, pbx_rpc_client);
    }

    return;
}


int main(int argc, char* argv[])
{
    int offline_store = 0;
    char pid_path[64];
    snprintf(pid_path, sizeof(pid_path), "/var/run/csstorage.pid");
    if (strstr(argv[0], "csstorage_offline"))
    {
        offline_store = 1;
        snprintf(pid_path, sizeof(pid_path), "/var/run/csstorage_offline.pid");
    }

    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton(pid_path))
    {
        printf("another csstorage has been running in this sytem.");
        return -1;
    }
    
    if(offline_store)
    {
        google::InitGoogleLogging("csstorage_offline");
    }
    else
    {
        google::InitGoogleLogging("csstorage");
    }
    PbxRpcClientInit();
    
    //glog初始化
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csstoragelog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csstoragelog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csstoragelog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csstoragelog/FATAL");
    FLAGS_logbufsecs = 0;
    FLAGS_max_log_size = 50;    //单日志文件最大50M

    //配置中心初始化
    //一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
	memset(&gstAKCSConf, 0, sizeof(AKCS_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }   
    ConfSrvInit();
    /* 读取配置文件 */
    if (ConfInit() != 0)
    {
        AK_LOG_FATAL << "init conf failed";
        return -1;
    }
    if (DaoInit() != 0)
    {
        AK_LOG_FATAL << "init dao failed";
        return -1;
    }

    //获取LOG库日志表分片数
    if(LogDeliveryInit() != 0)
    {
        AK_LOG_FATAL << "LogDeliveryInit failed";
        return -1;
    }
    dbinterface::LogSlice::GetSliceInfoByTableName("PersonalCapture", gCaptureSliceInfo);
    dbinterface::LogSlice::GetSliceInfoByTableName("PersonalMotion", gMotionSliceInfo);

    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(FTP_FILE_INVALID, BUSSINESS_PERIOD,
                                     BUSSINESS_NUM, BUSSINESS_KEY_EXPIRE, std::bind(csstorage::common::AttackedCallback, std::placeholders::_1, std::placeholders::_2));
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(FTP_FILE_UPDATE_FAILED, BUSSINESS_PERIOD_FILE_UPDATE,
                                     BUSSINESS_NUM_FILE_UPDATE, BUSSINESS_KEY_EXPIRE_FILE_UPDATE, std::bind(csstorage::common::AttackedCallback, std::placeholders::_1, std::placeholders::_2));

    std::thread mq_produce_thread = std::thread(MQProduceInit);
    std::thread conf_watch_thread = std::thread(ConfWatch);

    if(offline_store)
    {
        g_etcd_cli_mng->UpdateEtcdServerStatus();
        if(!g_etcd_cli_mng->CheckEtcdCliStatus())
        {
            //etcd状态异常，直接抛出异常重启；有时初始化etcd时已经有问题（如etcd还没启动），导致后面无法恢复
            AK_LOG_FATAL << "etcd status is abnormal, restart";
            return -1;
        }
        //不需要目录,因为本身解压出来的文件目录就不是检测的目录。
        g_storage_s3mng_ptr  = new StorageS3Mng("", csstorage_retry_data_dir);
        std::thread offline_log_thread(SaveOfflineLog);

        //上传oss/s3重试线程
        GetUploadRetryHandlerInstance()->Init();

        // 初始化metric单例
        InitMetricInstance();
        AK_LOG_INFO << "init offline log StorageMng succ";
        offline_log_thread.join();
        conf_watch_thread.join();
        mq_produce_thread.join();
    }
    else
    {    
        evpp::EventLoop loop;
        g_nsq_consumer_client_ptr = new evnsq::Consumer(&loop, gstAKCSConf.szNSQTopic, gstAKCSConf.szNSQChannel, evnsq::Option());
        g_nsq_consumer_client_ptr->SetMessageCallback(&OnNsqSubMessage);        
        //注册服务
        std::thread etcdCliThread = std::thread(EtcdSrvInit);

        std::thread etcdHealthCheckThread = std::thread(EtcdHealthCheckInit);
        
        //redis初始化
        if(0 != RedisInit())
        {
            AK_LOG_FATAL << "init Redis failed";
            return -1;
        }

        //删除过期图片
        g_storage_mng_ptr = new CStorageMng(csstorage_fdfs_conf_file);
        if (g_storage_mng_ptr->Init() != 0)
        {
            AK_LOG_FATAL << "init StorageMng failed";
            return -1;
        }
        g_storage_s3mng_ptr  = new StorageS3Mng(csstorage_data_dir, csstorage_retry_data_dir);
        
        CHandlePdu::GetInstance().Init();
        
        GetFileCacheManagerInstace()->InitPicCache(gstAKCSConf.pic_cache_size);
        GetFileCacheManagerInstace()->InitWavCache(gstAKCSConf.wav_cache_size);
        GetFileCacheManagerInstace()->InitVideoCache(gstAKCSConf.video_cache_size);

        //起http维护通道线程
        std::thread httpThread(startHttpServer);//port = 9991
        FtpDirScanner dir_scanner;
        std::thread scan_ftp_dir_thread(std::bind(&FtpDirScanner::ScanStorageFtpDataDir, &dir_scanner));

        InitFileProcessThread();

        //上传oss/s3重试线程
        GetUploadRetryHandlerInstance()->Init();

        std::thread timer_thread = std::thread(TimeTaskInit);

        // 初始化metric单例
        InitMetricInstance();
        AK_LOG_INFO << "init StorageMng succ";

        loop.Run();    
        conf_watch_thread.join();
        mq_produce_thread.join();
        etcdCliThread.join();
        timer_thread.join();
        scan_ftp_dir_thread.join();
    }
    
    return 0;
}

