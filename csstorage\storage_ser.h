//author :chenyc
//storage_ser.h

#ifndef __CSSTORAGE_STORAGE_SER_H__
#define __CSSTORAGE_STORAGE_SER_H__
#include "storage_mng.h"
#include "AkcsCommonDef.h"
#include "storage_s3.h"

static const char csstorage_data_dir[] = "/usr/local/akcs/csstorage/ftp/data"; //临时图片存放路径


#define CSSTORAGE_CONF_COMMON_LEN 64

typedef struct AKCS_CONF_T
{
    /* csstorage本机配置信息 */
    //char szStorageOuterIP[CSSTORAGE_CONF_COMMON_LEN];

    /* DB配置项 */
    char akcs_db_ip[CSSTORAGE_CONF_COMMON_LEN];
    char log_db_ip[CSSTORAGE_CONF_COMMON_LEN];
    char db_username[CSSTORAGE_CONF_COMMON_LEN];
    char db_password[CSSTORAGE_CONF_COMMON_LEN];
    char akcs_db_database[CSSTORAGE_CONF_COMMON_LEN];
    char log_db_database[CSSTORAGE_CONF_COMMON_LEN];
    int akcs_db_port;
    int log_db_port;

    /*NSQ*/
    char szNSQTopic[16];
    char szNSQChannel[32];
    int bak_offline_file; //0:不保存; 1:保存

    char group_name[16];

    /*route topic*/
    char szRouteTopic[16];

    int  ncapture_path_num;
    char szCapturePath[CSSTORAGE_CONF_COMMON_LEN][CSSTORAGE_CONF_COMMON_LEN];
    char szEtcdServerAddr[CSSTORAGE_CONF_COMMON_LEN];

    //oss
    char endpoint[256];
    char region_id[64];
    char bucket_name[64];
    char user[128];
    char password[128];
    char s3_tag[32];//上传的前缀 保证同个bucket 不同服务器能区分
    int is_aws;
    int store_fdfs;//存储到fdfs还是oss 默认0
    int store_s3;//存储到oss/s3的开关，默认0
    int file_consumer_thread_num; //文件消费线程数

    size_t pic_cache_size;
    size_t wav_cache_size;
    size_t video_cache_size;
    size_t wav_cache_shard_count;

    int log_db_pool_size;
    int akcs_db_pool_size;
} AKCS_CONF;

bool Md5ValueCheck(std::string& content, std::string& mac, int& is_voice);
int UploadImageFile(CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, const std::string &file, std::string &big_url, std::string &small_url);
int UploadImageFileHandler(CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, const std::string& filename, std::string& file_url);
int UploadVideoHandler(CStorageMng* fdfs_mng_ptr, StorageS3Mng* storage_s3mng_ptr, const std::string& filepath, std::string& file_url);
bool Md5ValueCheckForNewVersion(std::string& content, std::string& uuid, int& is_voice, const std::string& file);


#endif  //__CSSTORAGE_STORAGE_SER_H__
