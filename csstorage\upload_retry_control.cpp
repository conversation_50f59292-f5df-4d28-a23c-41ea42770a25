#include <dirent.h>
#include <string>
#include "storage_dao.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "storage_util.h"
#include "storage_s3.h"
#include "storage_ser.h"
#include "thumbnail.h"
#include "upload_retry_control.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "control/FileCacheManager.h"

extern AKCS_CONF gstAKCSConf;
extern StorageS3Mng* g_storage_s3mng_ptr;


UploadRetryHandler::UploadRetryHandler()
{

}

UploadRetryHandler::~UploadRetryHandler()
{
    reupload_deque_.clear();
}

UploadRetryHandler* UploadRetryHandler::instance = NULL;

UploadRetryHandler* UploadRetryHandler::GetInstance()
{
    if (instance == NULL)
    {
        instance = new UploadRetryHandler();
    }

    return instance;
}

UploadRetryHandler* GetUploadRetryHandlerInstance()
{
    return UploadRetryHandler::GetInstance();
}

int UploadRetryHandler::Init()
{
    reupload_thread_ = std::thread(&UploadRetryHandler::ProcessReUploadFile, this);
    AK_LOG_INFO << "UploadRetryHandler Thread Start Success,thread_id=" << reupload_thread_.get_id();
    return 0;
}

int UploadRetryHandler::ProcessReUploadFile()
{
    while (1)
    {
        // 将队列中的元素转移到handler_deque处理
        std::deque<UPLOAD_RETRY_FILE_INFO> handler_deque;
        {
            std::lock_guard<std::mutex> lock(reupload_mutex_);
            reupload_deque_.swap(handler_deque);
        }
        
        // 队列中有元素进行重传
        while (handler_deque.size() > 0)
        {
            // 获取队列的第一个元素后删除
            UPLOAD_RETRY_FILE_INFO retry_file = handler_deque.front();
            handler_deque.pop_front();

            bool upload_success = false;
            int error_code = retry_file.error_code;
            
            if (error_code == UPLOAD_BIG_IMAGE_TO_S3_ERROR && 0 == RetryPicFile(retry_file))
            {
                upload_success = true;
            }
            else if (error_code == UPLOAD_VOICE_FILE_TO_S3_ERROR && 0 == RetryVoiceFile(retry_file))
            {
                upload_success = true;
            }
            else if (error_code == UPLOAD_VIDEO_FILE_TO_S3_ERROR && 0 == RetryVideoFile(retry_file))
            {
                upload_success = true;
            }

            if (!upload_success && ++retry_file.retry_times <= 5)
            {
                AddReUploadFile(retry_file);
                AK_LOG_INFO << "ReUploadFile failed, already retry " << retry_file.retry_times << " times, filename = " << retry_file.filename;
            }
        }

        sleep(3);
    }

    return 0;
}

int UploadRetryHandler::RetryPicFile(UPLOAD_RETRY_FILE_INFO& retry_file)
{
    std::string remote_file_path;
    std::string filename = retry_file.filename;

    // 上传图片文件失败重试
    if (0 == g_storage_s3mng_ptr->UploadImageFileRetry(filename, remote_file_path))
    {
        if (retry_file.is_voice_pic)
        {
            // 上传语音留言截图成功,更新数据库
            dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgPicUrl(remote_file_path, filename);
        }
        else
        {
            // 上传大图重试成功后,生成小图继续上传
            std::string big_url;
            std::string small_url;
            std::string image_thumbnail = filename.substr(0, filename.size() - 4) + "_cut.jpg";
            std::string small_image_src = std::string(csstorage_retry_data_dir) + "/" + filename;
            std::string small_image_dst = std::string(csstorage_retry_data_dir) + "/" + image_thumbnail;

            if (true == generate_image_thumbnail(small_image_src.c_str(), small_image_dst.c_str()))
            {
                g_storage_s3mng_ptr->UploadImageFile(small_image_dst, small_url);
                ::remove(small_image_dst.c_str());
            }
            big_url = remote_file_path;
        
            DaoUpatePicUrl(retry_file.mac, filename, big_url, small_url, retry_file.project_uuid);
            
            AK_LOG_INFO << "reupload success pic, mac = " << retry_file.mac 
                        << ", filename = " << filename << ", big_url = "<< big_url << ", small_url = " << small_url;
        }
    
        RemoveFile(filename);
        return 0;
    }
    
    return -1;
}

int UploadRetryHandler::RetryVoiceFile(UPLOAD_RETRY_FILE_INFO& retry_file)
{
    std::string remote_file_path;
    std::string filename = retry_file.filename;
    if (0 == g_storage_s3mng_ptr->UploadVoiceFileRetry(filename, remote_file_path))
    {
        // 更新数据库
        dbinterface::PersonalVoiceMsg::UpdatePersonalVoiceMsgFileUrl(remote_file_path, filename);

        // 删除本地文件
        RemoveFile(filename);

        AK_LOG_INFO << "reupload wav file success,mac:" << retry_file.mac 
                    << ",filename:" << retry_file.filename << ",remote_file_path" << remote_file_path;
        return 0;
    }
    
    return -1;
}

int UploadRetryHandler::RetryVideoFile(UPLOAD_RETRY_FILE_INFO& retry_file)
{
    std::string remote_file_path;
    std::string filename = retry_file.filename;
    if (0 == g_storage_s3mng_ptr->UploadVideoFileRetry(filename, remote_file_path))
    {
        // 更新数据库
        DaoUpdateVideoUrl(retry_file.mac, filename, remote_file_path, retry_file.project_uuid);

        // 删除本地文件
        RemoveFile(filename);
        
        AK_LOG_INFO << "reupload video file success, mac = " << retry_file.mac 
                    << ", filename = " << filename 
                    << ", remote_file_path = " << remote_file_path;
        return 0;
    }
    return -1;
}

void UploadRetryHandler::RemoveFile(const std::string& filename)
{
    std::string filepath = std::string(csstorage_retry_data_dir) + "/" + filename;
    ::remove(filepath.c_str());
    
    if (csstorage::common::IsWavFile(filename))
    {
        GetFileCacheManagerInstace()->RemoveWavCache(filename);
    }
    else if (csstorage::common::IsVideoFile(filename))
    {
        GetFileCacheManagerInstace()->RemoveVideoCache(filename);
    }
    else 
    {
        GetFileCacheManagerInstace()->RemovePicCache(filename);
    }
    
    return;
}

int UploadRetryHandler::AddReUploadFile(const UPLOAD_RETRY_FILE_INFO& fileinfo)
{
    std::unique_lock<std::mutex> lock(reupload_mutex_);
    reupload_deque_.push_back(fileinfo);
     
    return 0;
}
