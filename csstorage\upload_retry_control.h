#ifndef __UPLOAD_RETRY_CONTROL_H__
#define __UPLOAD_RETRY_CONTROL_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <deque>


static const char csstorage_retry_data_dir[] = "/usr/local/akcs/csstorage/ftp/data/retry"; //重传图片存放路径

typedef struct UPLOAD_RETRY_FILE_INFO_T
{
    char mac[32];
    char project_uuid[64];
    char filename[256];
    int is_voice_pic;
    int error_code;
    int retry_times;
}UPLOAD_RETRY_FILE_INFO;


class UploadRetryHandler 
{
public:
    UploadRetryHandler();
    ~UploadRetryHandler();
    static UploadRetryHandler *GetInstance();

    int Init();
    int AddReUploadFile(const UPLOAD_RETRY_FILE_INFO& fileinfo);
    int ProcessReUploadFile();

private:
    void RemoveFile(const std::string& filename);
    int RetryPicFile(UPLOAD_RETRY_FILE_INFO& retry_file);
    int RetryVoiceFile(UPLOAD_RETRY_FILE_INFO& retry_file);
    int RetryVideoFile(UPLOAD_RETRY_FILE_INFO& retry_file);

private:
    std::deque<UPLOAD_RETRY_FILE_INFO> reupload_deque_; 
    std::thread reupload_thread_;
    std::mutex reupload_mutex_; 

    static UploadRetryHandler *instance;
};

UploadRetryHandler* GetUploadRetryHandlerInstance();

#endif
