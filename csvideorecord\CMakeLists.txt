CMAKE_MINIMUM_REQUIRED(VERSION 2.8)
set (CMAKE_CXX_STANDARD 11)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src ${CMAKE_CURRENT_SOURCE_DIR}/../csbase/doorlog")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

PROJECT (csvideorecord C CXX)
SET(THIRDLIB_DIR ${PROJECT_SOURCE_DIR}/thirdlib)
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)
SET(DEPENDENT_LIBRARIES libaws-cpp-sdk-core.so libaws-cpp-sdk-s3.so libcsbase.a pthread libevent.so libhiredis.a libglog.so libmysqlclient.so libgpr.so libgrpc.so 
	libgrpc++.so libprotobuf.so libevpp.so -lcurl -lssl -lcrypto -lcpprest -letcd-cpp-api -levpp -levent libboost_system.so libboost_filesystem.so
 	libalibabacloud-oss-cpp-sdk.a libufilecppsdk.a libjson-c.a fdfsclient fastcommon)

SET(BASE_LIST_INC   ${CSBASE_SOURCE_DIR}/ 
					${CSBASE_SOURCE_DIR}/mysql/include 
					${CSBASE_SOURCE_DIR}/Rldb 
					${CSBASE_SOURCE_DIR}/redis 
					${CSBASE_SOURCE_DIR}/grpc 
					${CSBASE_SOURCE_DIR}/grpc/gens 
					${CSBASE_SOURCE_DIR}/grpc/include  
					${CSBASE_SOURCE_DIR}/etcd 
					${CSBASE_SOURCE_DIR}/evpp 
					${CSBASE_SOURCE_DIR}/jsoncpp0.5/include 
					${CSBASE_SOURCE_DIR}/encrypt 
					${CSBASE_SOURCE_DIR}/oss 
					${CSBASE_SOURCE_DIR}/oss/include
                    ${CSBASE_SOURCE_DIR}/model
                    ${CSBASE_SOURCE_DIR}/uploader
                    ${CSBASE_SOURCE_DIR}/metrics
                    ${CSBASE_SOURCE_DIR}/oss/include
                    ${CSBASE_SOURCE_DIR}/oss/include/alibabacloud 
					${CSBASE_SOURCE_DIR}/dbinterface 
					${CSBASE_SOURCE_DIR}/dbinterface/Log
					${CSBASE_SOURCE_DIR}/dbinterface/resident 
                    ${CSBASE_SOURCE_DIR}/grpc/csvideorecord
)

AUX_SOURCE_DIRECTORY(./src SRC_LIST_VIDEORECORD)
AUX_SOURCE_DIRECTORY(./src/auth SRC_LIST_VIDEORECORD_AUTH)
AUX_SOURCE_DIRECTORY(./src/download SRC_LIST_VIDEORECORD_DOWNLOAD)
AUX_SOURCE_DIRECTORY(./src/msgqueue SRC_LIST_VIDEORECORD_MSGQUEUE)
AUX_SOURCE_DIRECTORY(../csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(../csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(../csbase/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(../csbase/jsoncpp0.5/src SRC_LIST_JSON)
AUX_SOURCE_DIRECTORY(../csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(../csbase/uploader SRC_LIST_BASE_UPLOADER)
AUX_SOURCE_DIRECTORY(../csbase/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(../csbase/grpc/csvideorecord SRC_LIST_BASE_GRPC_VIDEORECORD)

INCLUDE_DIRECTORIES(${BASE_LIST_INC} ./include /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include
				    ${CSBASE_SOURCE_DIR}/fdfs_client/fdfsclient ${CSBASE_SOURCE_DIR}/fdfs_client/libfdfscomm)

LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib/ ${CSBASE_SOURCE_DIR}/redis/hiredis/ ${CSBASE_SOURCE_DIR}/evpp/lib/ /usr/local/lib/ ${CSBASE_SOURCE_DIR}/thirdlib/oss ${CSBASE_SOURCE_DIR}/json-c)

ADD_DEFINITIONS( -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO -DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
ADD_COMPILE_OPTIONS( -std=c++11 -shared -fPIC -g -W -Wall -Werror -Wno-unused-parameter -Wno-deprecated)

ADD_EXECUTABLE(csvideorecord ${SRC_LIST_VIDEORECORD} ${SRC_LIST_VIDEORECORD_AUTH} ${SRC_LIST_VIDEORECORD_DOWNLOAD} ${SRC_LIST_VIDEORECORD_MSGQUEUE}
	${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_JSON} ${SRC_LIST_BASE_METRICS} ${SRC_LIST_BASE_GRPC_VIDEORECORD}
	${CSBASE_SOURCE_DIR}/model/AkcsHttpRequest.cpp ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_BASE_UPLOADER} ${prefixed_file_list})

SET_TARGET_PROPERTIES(csvideorecord PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csvideorecord/lib")

TARGET_LINK_LIBRARIES(csvideorecord  ${DEPENDENT_LIBRARIES})
