#ifndef __CSVIDEORECORD_CLOUD_STOGARE_H__
#define __CSVIDEORECORD_CLOUD_STOGARE_H__

#include <string>
#include "uploader/s3_uploader.h"
#include <boost/noncopyable.hpp>

class CloudStorageMng : public boost::noncopyable
{
public:
    CloudStorageMng();
    ~CloudStorageMng() {};
    bool DownloadFile(const std::string& remote_filepath, const std::string& local_filepath);
private:
    std::unique_ptr<S3Uploader> uploader_;
};

#endif //__CSSTORAGE_S3_H__

