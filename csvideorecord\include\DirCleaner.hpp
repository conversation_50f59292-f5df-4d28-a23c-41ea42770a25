#ifndef __CSVIDEORECORD_DIR_CLEANER_H__
#define __CSVIDEORECORD_DIR_CLEANER_H__

#include <iostream>
#include <thread>
#include <chrono>
#include <string>
#include <ctime>
#include <dirent.h>
#include <sys/stat.h>
#include <unistd.h>
#include <atomic>
#include "util_time.h"
#include "VideoRecordDefine.h"

class DirectoryCleaner 
{
private:
    std::string base_path_;
    std::atomic<bool> running_;

public:
    DirectoryCleaner(const std::string& path) 
        : base_path_(path), running_(true) {
    }

    void CleanDirectories() {
        while (running_) 
        {
            try 
            {
                std::string current_date = GetNowDate();
                AK_LOG_INFO << "Starting directory cleanup. Current date: " << current_date;

                DIR* dir = opendir(base_path_.c_str());
                if (dir != nullptr && strstr(base_path_.c_str(), PROCESS_VIDEO_REOCRD_PLAY_DIR)) 
                {
                    struct dirent* entry;
                    while ((entry = readdir(dir)) != nullptr) 
                    {
                        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) 
                        {
                            continue;
                        }

                        std::string full_path = base_path_ + "/" + entry->d_name;
                        AK_LOG_INFO << "full_path = " << full_path;

                        struct stat path_stat;
                        if (stat(full_path.c_str(), &path_stat) == 0 &&  S_ISDIR(path_stat.st_mode)) 
                        {
                            std::string dir_name = entry->d_name;
                            if (dir_name != current_date) 
                            {
                                AK_LOG_INFO << "Removing directory: " << full_path;
                                std::string cmd = "rm -rf " + full_path;
                                system(cmd.c_str());
                            }
                        }
                    }
                    closedir(dir);
                }
                else 
                {
                    AK_LOG_INFO << "Failed to open directory: " << base_path_;
                }
            } 
            catch (const std::exception& e) 
            {
                AK_LOG_INFO << "Error during directory cleaning: " << e.what();
            }

            std::this_thread::sleep_for(std::chrono::hours(24));
        }
    }

    void Stop() {
        running_ = false;
    }

    ~DirectoryCleaner() {
        Stop();
    }
};


#endif