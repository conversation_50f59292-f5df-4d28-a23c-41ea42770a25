#ifndef __CSVIDEORECORD_FDFS_STORAGE_MNG_H__
#define __CSVIDEORECORD_FDFS_STORAGE_MNG_H__

#include <mutex>
#include <memory>
#include <string>
#include <boost/noncopyable.hpp>
#include "fdfs_client.h"
#include "uploader/fdfs_uploader.h"

class FdfsStorageMng : public boost::noncopyable
{
public:
    FdfsStorageMng(const char* file_name);
    ~FdfsStorageMng() {};
    bool DownloadFile(const std::string& remote_filepath, const std::string& local_filepath);
private:
    std::unique_ptr<FdfsUploader> uploader_;
};

#endif

