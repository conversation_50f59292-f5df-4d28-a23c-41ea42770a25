#ifndef __CSVIDEORECORD_HTTP_ACCESS_AUTH_H__
#define __CSVIDEORECORD_HTTP_ACCESS_AUTH_H__

#include "Md5.h"
#include <string>
#include <ctime>
#include <random>
#include <stdexcept>
#include "util_time.h"

namespace csvideorecord {

class VideoAuthenticator 
{
public:
    struct AuthConfig {
        std::string zlmediakit_servername;    // 服务名称
        std::string access_private_key;       // 访问私钥
        std::string zlmediakit_server_domain; // 服务器域名
    };
    
    struct VerifyResult {
        bool is_valid;
        std::int64_t timestamp;
        std::string uid;
        std::string error_msg;

        VerifyResult(bool valid = false, std::int64_t ts = 0, 
                    const std::string& user_id = "", 
                    const std::string& err = "")
            : is_valid(valid), timestamp(ts), uid(user_id), error_msg(err) {}
    };

    explicit VideoAuthenticator(const AuthConfig& config) : config_(config) {}

    // 生成鉴权url
    inline std::string GenerateAuthUrl(const std::string& filename, 
                                     const std::string& uid = "0",
                                     int expire_seconds = 1800) {
        std::string video_path = "/record/" + GetNowDate() + "/" + filename;
        std::time_t timestamp = std::time(nullptr) + expire_seconds;
        std::string random_str = GetNbitRandomString(10);
        std::string auth_key = GenerateAuthKey(video_path, timestamp, random_str, uid);
        
        return BuildFullUrl(video_path, timestamp, random_str, uid, auth_key);
    }

    // 鉴权验证部分
    inline VerifyResult VerifyAuth(const std::string& path, const std::string& params) {
        std::string auth_key = ExtractAuthKey(params);
        if (auth_key.empty()) {
            return VerifyResult(false, 0, "", "Auth key not found");
        }

        std::vector<std::string> auth_parts;
        SplitString(auth_key, "-", auth_parts);
        
        if (auth_parts.size() != 4) {
            return VerifyResult(false, 0, "", "Invalid auth key format");
        }

        std::int64_t timestamp = std::stoll(auth_parts[0]);
        std::string randstring = auth_parts[1];
        std::string uid = auth_parts[2];
        std::string request_hash = auth_parts[3];

        // 验证时间戳
        if (!VerifyTimestamp(timestamp)) {
            return VerifyResult(false, timestamp, uid, "Auth key expired");
        }

        // 验证哈希值
        std::string calc_hash_string = path + "-" + 
                                     std::to_string(timestamp) + "-" + 
                                     randstring + "-" + 
                                     uid + "-" + 
                                     config_.access_private_key;
                                     
        std::string calc_hash = akuvox_encrypt::MD5(calc_hash_string).toStr();

        if (request_hash != calc_hash) {
            return VerifyResult(false, timestamp, uid, "Invalid hash");
        }

        return VerifyResult(true, timestamp, uid);
    }

private:
    AuthConfig config_;

    // 生成认证密钥
    inline std::string GenerateAuthKey(const std::string& video_path,
                                     std::time_t timestamp,
                                     const std::string& random_str,
                                     const std::string& uid) {
        std::string auth_key_string = 
            video_path + "-" + 
            std::to_string(timestamp) + "-" + 
            random_str + "-" + 
            uid + "-" + 
            config_.access_private_key;

        return akuvox_encrypt::MD5(auth_key_string).toStr();
    }

    // 构建完整URL
    inline std::string BuildFullUrl(const std::string& video_path,
                                  std::time_t timestamp,
                                  const std::string& random_str,
                                  const std::string& uid,
                                  const std::string& auth_key) {
        return "https://" + config_.zlmediakit_server_domain + "/" + std::string(config_.zlmediakit_servername) + video_path +
               "?auth_key=" + std::to_string(timestamp) + "-" + random_str + "-" + uid + "-" + auth_key;
    }

    // 提取鉴权AuthKey
    inline std::string ExtractAuthKey(const std::string& params) {
        const std::string auth_key_prefix = "auth_key=";
        size_t pos = params.find(auth_key_prefix);
        if (pos != std::string::npos) {
            return params.substr(pos + auth_key_prefix.length());
        }
        return "";
    }

    inline bool VerifyTimestamp(std::int64_t timestamp) {
        return std::time(nullptr) <= timestamp;
    }
};

}

#endif
