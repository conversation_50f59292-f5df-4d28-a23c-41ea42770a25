#ifndef __CSVIDEORECORD_MEDIA_SERVER_API_H__
#define __CSVIDEORECORD_MEDIA_SERVER_API_H__

#include "json/json.h"
#include "AkLogging.h"
#include "SafeCacheConn.h"
#include "AkcsHttpRequest.h"
#include "VideoRecordConfig.h"
#include "VideoRecordUtil.hpp"

extern VIDEO_RECORD_CONFIG g_video_record_config;

namespace csvideorecord {

class MediaServerApi
{
public:
    static bool AddStreamProxy(const std::string& app, const std::string& mac, const std::string& rtsp_url)
    {
        std::string response;

        std::stringstream add_stream_proxy;
        add_stream_proxy << std::string(g_video_record_config.zlmediakit_inner_addr)
            << "/index/api/addStreamProxy?vhost=__defaultVhost__"
            << "&secret=" << std::string(g_video_record_config.zlmediakit_secret)
            << "&app=" << app << "&stream=" << mac << "&url=" << rtsp_url;
    
        model::HttpRequest::GetInstance().Post(add_stream_proxy.str(), "", response, 0);
    
        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);
        
        return root["code"].asInt() == 0;
    }

    static bool DelStreamProxy(const std::string& app, const std::string& mac, const std::string& stream_key)
    {
        std::string response;

        std::stringstream del_stream_proxy;
        del_stream_proxy << std::string(g_video_record_config.zlmediakit_inner_addr)
                << "/index/api/delStreamProxy?secret=" << std::string(g_video_record_config.zlmediakit_secret) << "&key=" << stream_key;

        model::HttpRequest::GetInstance().Post(del_stream_proxy.str(), "", response, 0);
         
        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);
       
        return root["code"].asInt() == 0 && root["data"]["flag"].asBool();
    }

    static bool StartRecord(const std::string& app, const std::string& mac)
    {
        std::string response;
        std::stringstream start_record;
        start_record << std::string(g_video_record_config.zlmediakit_inner_addr)
           << "/index/api/startRecord?vhost=__defaultVhost__"
           << "&secret=" << std::string(g_video_record_config.zlmediakit_secret)
           << "&app=" << app << "&stream=" << mac << "&type=1"; // type = 1 指定录制mp4
        
         model::HttpRequest::GetInstance().Post(start_record.str(), "", response, 0);
         
         Json::Value root;
         Json::Reader reader;
         reader.parse(response, root);

         return root["code"].asInt() == 0 && root["result"].asBool();
    }

    static bool StopRecord(const std::string& app, const std::string& mac)
    {
        std::string response;
        std::stringstream start_record;
        start_record << std::string(g_video_record_config.zlmediakit_inner_addr)
           << "/index/api/stopRecord?vhost=__defaultVhost__"
           << "&secret=" << std::string(g_video_record_config.zlmediakit_secret)
           << "&app=" << app << "&stream=" << mac << "&type=1";  // type = 1 指定录制mp4
        
         model::HttpRequest::GetInstance().Post(start_record.str(), "", response, 0);
         
         Json::Value root;
         Json::Reader reader;
         reader.parse(response, root);

         return root["code"].asInt() == 0 && root["result"].asBool();
    }
    
    static bool IsMediaOnline(const std::string& app, const std::string& mac)
    {
        int try_times = 3;
        std::string response;
        bool media_online = false;
        std::stringstream media_online_url;
        media_online_url << g_video_record_config.zlmediakit_inner_addr
                         << "/index/api/isMediaOnline?vhost=__defaultVhost__&schema=rtsp"
                         << "&secret=" << g_video_record_config.zlmediakit_secret
                         << "&app=" << app << "&stream=" << mac;
        
        while (--try_times >= 0) 
        {
            model::HttpRequest::GetInstance().Post(media_online_url.str(), "", response, 0);

            Json::Value root;
            Json::Reader reader;
            if (!reader.parse(response, root)) {
                AK_LOG_WARN << "isMediaOnline Failed to parse JSON message";
                return false;
            }

            if (root["code"].asInt() == 0 && root["online"].asBool()) {
                media_online = true;
                break;
            }
            
            // 等流注册上
            sleep(1);
        }
        return media_online;
    }

    static bool IsRecording(const std::string& app, const std::string& mac)
    {
        std::string response;
        std::stringstream is_recording_url;
        is_recording_url << std::string(g_video_record_config.zlmediakit_inner_addr)
             << "/index/api/isRecording?vhost=__defaultVhost__"
             << "&secret=" << std::string(g_video_record_config.zlmediakit_secret)
             << "&app=" << app << "&stream=" << mac << "&type=1";
        
        model::HttpRequest::GetInstance().Post(is_recording_url.str(), "", response, 0);

        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);
        return root["code"].asInt() == 0 && root["status"].asBool();
    }    

    // 使用ffmpeg转推添加水印,未使用
    static bool AddFFmpegSource(const std::string& app, const std::string& mac, const std::string& rtsp_url)
    {
        std::string response;

        std::stringstream add_ffmpeg_source;
        add_ffmpeg_source << std::string(g_video_record_config.zlmediakit_inner_addr)
            << "/index/api/addFFmpegSource?vhost=__defaultVhost__"
            << "&timeout_ms=10000&enable_mp4=1&ffmpeg_cmd_key=ffmpeg.cmd"
            << "&secret=" << std::string(g_video_record_config.zlmediakit_secret)
            << "&src_url=" << rtsp_url << "&dst_url=" << VideoRecordUtil::GetFFmpegSourceDstUrl(app, mac);

        model::HttpRequest::GetInstance().Post(add_ffmpeg_source.str(), "", response, 0);

        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);

        if (root["code"].asInt() != 0)
        {
            return false;
        }

        std::string source_key = root["data"]["key"].asString();
        VideoRecordUtil::CacheFFmpegSourceKey(app, mac, source_key);
        return true;
    }
    
    static bool DelFFmpegSource(const std::string& app, const std::string& mac)
    {
        std::string response;

        std::stringstream del_stream_proxy;
        del_stream_proxy << std::string(g_video_record_config.zlmediakit_inner_addr)
                << "/index/api/delFFmpegSource?secret=" << std::string(g_video_record_config.zlmediakit_secret) 
                << "&key=" << VideoRecordUtil::GetFFmpegSourceKeyCache(app, mac);
        
        model::HttpRequest::GetInstance().Post(del_stream_proxy.str(), "", response, 0);

        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);
       
        return root["code"].asInt() == 0;
    }

    static int GetMediaListNum()
    {
        int media_count = 0;
        std::string response;
        
        std::stringstream media_list;
        media_list << std::string(g_video_record_config.zlmediakit_inner_addr)
            << "/index/api/getMediaList?vhost=__defaultVhost__"
            << "&secret=" << std::string(g_video_record_config.zlmediakit_secret);

        model::HttpRequest::GetInstance().Post(media_list.str(), "", response, 0);

        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);
        if(reader.parse(response, root) && root["code"].asInt() == 0) 
        {
            media_count = root["data"].size();
        }

        return media_count;
    }
};

}

#endif
