#ifndef __CSVIDEORECORD_NOTIFY_MSG_CONTROL_H__
#define __CSVIDEORECORD_NOTIFY_MSG_CONTROL_H__

#include <thread>
#include <mutex>
#include <memory>
#include <list>
#include "AkLogging.h"
#include "NotifyMsg.h"
#include <condition_variable>
#include "NotifyStopRecord.h"
#include "NotifyStartRecord.h"

class CNotifyMsg;
class StopRecordHandle;
class StartRecordHandle;

class CNotifyMsgControl
{
public:
    using NotifyMsgPrt = std::shared_ptr<CNotifyMsg>;

public:
    CNotifyMsgControl() {};
    ~CNotifyMsgControl();

    static CNotifyMsgControl* GetInstance();

    int Init();
    int GetNotifyMsgListSize();
    int ProcessNotifyMsg();

    int AddStartRecordMsg(const StartRecordHandle&& msg);
    int AddStopRecordMsg(const StopRecordHandle&& msg);
private:
    std::mutex mutex_;
    std::thread thread_;
    std::list<NotifyMsgPrt> notify_msg_list_;
    std::condition_variable condition_variable_;
    static CNotifyMsgControl* record_instance;
};

CNotifyMsgControl* GetNotifyMsgControlInstance();

#endif
