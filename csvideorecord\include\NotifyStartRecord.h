#ifndef __CSVIDEORECORD_START_RECORD_MSG_H__
#define __CSVIDEORECORD_START_RECORD_MSG_H__

#include <string>
#include "NotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyMsgControl.h"

class StartRecordHandle : public CNotifyMsg
{
public:
    StartRecordHandle() {};
    ~StartRecordHandle() {}
    
    StartRecordHandle(const std::string& site, const std::string& mac) 
    {
        site_ = site;
        mac_ = mac;
    }

    StartRecordHandle(const StartRecordHandle &that)
    {
        site_ = that.site_;
        mac_ = that.mac_;
    }
    
    StartRecordHandle(StartRecordHandle &&that)
    {
        site_ = std::move(that.site_);
        mac_ = std::move(that.mac_);
    }

    void NotifyMsg();
private:
    std::string site_;
    std::string mac_;
};



#endif

