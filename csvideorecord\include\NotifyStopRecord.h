#ifndef __CSVIDEORECORD_STOP_RECORD_MSG_H__
#define __CSVIDEORECORD_STOP_RECORD_MSG_H__

#include <string>
#include "NotifyMsg.h"
#include "AkcsCommonDef.h"
#include "NotifyMsgControl.h"

class StopRecordHandle : public CNotifyMsg
{
public:
    StopRecordHandle() {};
    ~StopRecordHandle() {}
    
    StopRecordHandle(const std::string& site, const std::string& mac) 
    {
        site_ = site;
        mac_ = mac;
    }
    
    StopRecordHandle(const StopRecordHandle &that)
    {
        site_ = that.site_;
        mac_ = that.mac_;
    }

    StopRecordHandle(StopRecordHandle &&that)
    {
        site_ = std::move(that.site_);
        mac_ = std::move(that.mac_);
    }
    
    void NotifyMsg();

private:
    std::string site_;
    std::string mac_;
};

#endif
