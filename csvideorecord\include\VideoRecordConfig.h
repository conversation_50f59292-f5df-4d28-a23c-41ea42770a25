#ifndef __CSVIDEORECORD_CONFIG_H__
#define __CSVIDEORECORD_CONFIG_H__

#include <string>

// csvideorecord config struct
typedef struct VIDEO_RECORD_CONFIG_T
{
    // 使用fdfs
    int store_fdfs;

    // 使用对象存储服务
    int store_s3;

    // fdfs group
    char group_name[16];

    // bucket
    char bucket[64];
    
    // http server port.
    int http_port;

    // HTTP handler thread number
    int http_thread_num = 10;

    // rpc server port.
    int rpc_port;

    // etcd server
    char etcd_server_addr[64];
    
    // 服务器内网ip
    char server_inner_ip[64];
    
    // zlmediakit服务域名
    char zlmediakit_server_domain[64];

    // zlmediakit服务名   
    char zlmediakit_servername[32];
    
    // zlmediakit服务列表
    char zlmediakit_servername_list[256];

    // zlmediakit服务地址
    char zlmediakit_inner_addr[64];

    // zlmediakit密钥
    char zlmediakit_secret[64];

    // 点播鉴权私钥
    char access_privatekey[64];

    // rtsp域名
    char csvrtsp_outer_domain[64];

    // 点播有效时长
    int video_expiration_seconds;

    // DB配置项 
    int  db_port;
    char db_ip[64];
    char db_username[64];
    char db_password[64];
    char db_database[64];

    int  log_db_port;
    char log_db_ip[64];
    char log_db_database[64];
} VIDEO_RECORD_CONFIG;


#endif
