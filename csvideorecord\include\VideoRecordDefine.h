#ifndef __CSVIDEORECORD_DEFINE_H__
#define __CSVIDEORECORD_DEFINE_H__

#define PROCESS_ETC_IP                  "/etc/ip"
#define PROCESS_VIDEO_NFS_DIR           "/nfsroot/record"
#define PROCESS_UPLOAD_DIR              "/usr/local/akcs/csstorage/ftp/data"
#define PROCESS_APP_VIDEO_REOCRD_DIR    "/usr/local/akcs/csvideorecord/data/videos"
#define PROCESS_VIDEO_REOCRD_PLAY_DIR   "/usr/local/akcs/csvideorecord/data/record"
#define PROCESS_CONF_FILE               "/usr/local/akcs/csvideorecord/conf/csvideorecord.conf"
#define PROCESS_FDFS_CONF_FILE          "/usr/local/akcs/csvideorecord/conf/csvideorecord_fdfs.conf"
#define PROCESS_REDIS_CONF_FILE         "/usr/local/akcs/csvideorecord/conf/csvideorecord_redis.conf"
#define PROCESS_FDFS_LOG_PATH           "/var/log/csvideorecord/fdfs_client.log"

#define MAX_RLDB_CONN 10
#define VIDEO_AES256_KEY                "Akuvox55069013Akuvox"

#endif
