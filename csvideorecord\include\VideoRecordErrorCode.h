#ifndef __CSVIDEORECORD_ERROR_CODE_H__
#define __CSVIDEORECORD_ERROR_CODE_H__

#include <map>
#include <string>

// error codes.
#define ERR_CODE_SUCCESS                "0"
#define ERR_CODE_SERVER_ERR             "1000400001"
#define ERR_CODE_INVALID_REQUEST_ROUTE  "1000400002"
#define ERR_CODE_INVALID_REQUEST_DATA   "1000400003"
#define ERR_CODE_DOWNLOAD_FILE_FAILED   "1000400004"

// error codes description mapping.
static const std::map<std::string, std::string> g_error_code_message =
{
    {ERR_CODE_SUCCESS,                  "Success"},
    {ERR_CODE_SERVER_ERR,               "Internal Server Error"},
    {ERR_CODE_INVALID_REQUEST_ROUTE,    "Invalid request interface"},
    {ERR_CODE_INVALID_REQUEST_DATA,     "Invalid request data/parameters"},
    {ERR_CODE_DOWNLOAD_FILE_FAILED,     "Failed to download file"},
};

#endif
