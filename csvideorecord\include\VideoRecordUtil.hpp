#ifndef __CSVIDEORECORD_FILE_CRYPTO_H__
#define __CSVIDEORECORD_FILE_CRYPTO_H__

#include "util.h"
#include "AES256.h"
#include "util_time.h"
#include "util_string.h"
#include "AkcsCommonDef.h"
#include "SafeCacheConn.h"
#include "FdfsStorageMng.h"
#include "CloudStorageMng.h"
#include "HttpAccessAuth.hpp"
#include "VideoRecordDefine.h"
#include "VideoRecordConfig.h"
#include "AkcsPasswdConfuse.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

extern FdfsStorageMng* g_fdfs_storage_mng;
extern CloudStorageMng* g_cloud_storage_mng;
extern VIDEO_RECORD_CONFIG g_video_record_config;

namespace csvideorecord {

class VideoRecordUtil
{
public:
    static int DecryptFile(const std::string& filename)
    {
        char aes_encrypt_key_v1[64] = {0};
        std::string aes_encrypt_key = FileEncryptKey(filename);
        Snprintf(aes_encrypt_key_v1, sizeof(aes_encrypt_key_v1), aes_encrypt_key.c_str());
        std::string filepath = csvideorecord::VideoRecordUtil::VideoPlayLocalFilePath(filename);
        
        return FileAESDecrypt(filepath.c_str(), aes_encrypt_key_v1, filepath.c_str());
    }
    
    static std::string VideoPlayLocalFilePath(const std::string& filename)
    {
        return std::string(PROCESS_VIDEO_REOCRD_PLAY_DIR) + "/" + GetNowDate() + "/" + filename;
    }

    static std::string AppRecordFilename(const std::string& mac, const std::string& filepath)
    {
        std::string app_record_filename;

        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetMacDev(mac, dev)
            && 0 != dbinterface::ResidentPerDevices::GetMacDev(mac, dev)) {
            AK_LOG_WARN << "AppRecordFilename Failed to GetMacDev, mac = " << mac;
            return app_record_filename;
        }
        
        std::time_t timestamp = std::time(nullptr);
        std::string token_hash = csvideorecord::VideoRecordUtil::FtpFileToken(dev.uuid, timestamp, filepath);
        app_record_filename = std::string(dev.uuid) + "-" + std::to_string(timestamp) + "-APP-" + token_hash + ".mp4";
        
        AK_LOG_INFO << "AppRecordFilename: token_hash = " << token_hash << ", app_record_filename = " << app_record_filename; 
        return app_record_filename;
    }
    
    static std::string GetVideoUrlCache(const std::string& storage_url)
    {
        std::string play_url;
        SafeCacheConn redis(g_redis_db_video_record);
        if (redis.isConnect()) {
            play_url = redis.get(storage_url);
        }
        return play_url;
    }

    static void CacheVideoPlayUrl(const std::string& storage_url, const std::string& play_url)
    {
        SafeCacheConn redis(g_redis_db_video_record);
        if (redis.isConnect()) {
            redis.setex(storage_url, g_video_record_config.video_expiration_seconds, play_url);
        }
        return;
    }
    
    static std::string StreamProxyKey(const std::string& app, const std::string& mac)
    {
        return "__defaultVhost__/" + app + "/" + mac;
    }

    static std::string GetRtspUrl(const std::string& mac)
    {
        char rtsp_url[256];
        ResidentDev dev;
        if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev)
            || 0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
        {
            std::string rtsp_password = dev.rtsppwd;
            PasswdDecode(rtsp_password.c_str(), rtsp_password.size(), dev.rtsppwd, sizeof(dev.rtsppwd));
            snprintf(rtsp_url, sizeof(rtsp_url), "rtsp://user:%s@%s:554/%s", rtsp_password.c_str(), g_video_record_config.csvrtsp_outer_domain, mac.c_str());
        }
        return std::string(rtsp_url);
    }

    static bool DownloadFile(const std::string& storage_url, const std::string& local_filepath)
    {
        if (storage_url.find("group") != std::string::npos
            && g_fdfs_storage_mng->DownloadFile(storage_url, local_filepath)) {
            return true;
        } else if (storage_url.find("VIDEO") != std::string::npos &&  
            g_cloud_storage_mng->DownloadFile(storage_url, local_filepath)) {
            return true;
        }
        return false;
    }
    
    static std::string GetStorageFilename(const std::string& storage_url)
    {
        std::string filename;
        std::string filepath_prefix;
        SplitStringFromLastFilter(storage_url, "/", filepath_prefix, filename);
        return filename;
    }

    static std::string GetRecordRelatePicName(const std::string& stream_key)
    {
        std::string pic_name;
        SafeCacheConn redis(g_redis_db_video_record);
        if (redis.isConnect()) {
            pic_name = redis.get(stream_key);
            redis.del(stream_key);
        }
        return pic_name;
    }

    static std::string GenerateVideoPlayUrl(const std::string& filename)
    {
        // 生成点播url
        std::string play_url;
        csvideorecord::VideoAuthenticator::AuthConfig auth_config {
            .zlmediakit_servername = g_video_record_config.zlmediakit_servername,
            .access_private_key = g_video_record_config.access_privatekey,
            .zlmediakit_server_domain = g_video_record_config.zlmediakit_server_domain
        };
        
        csvideorecord::VideoAuthenticator authenticator(auth_config);
        play_url = authenticator.GenerateAuthUrl(filename);
        return play_url;
    }

    static void CacheFFmpegSourceKey(const std::string& app, const std::string& mac, const std::string& source_key_value)
    {
        SafeCacheConn redis(g_redis_db_video_record);
        if (redis.isConnect()) {
            redis.setex(FFmpegSourceKey(app, mac), g_video_record_config.video_expiration_seconds, source_key_value);
        }
        return;
    }
    
    static std::string GetFFmpegSourceKeyCache(const std::string& app, const std::string& mac)
    {
        std::string source_key;
        SafeCacheConn redis(g_redis_db_video_record);
        if (redis.isConnect()) {
            source_key = redis.get(FFmpegSourceKey(app, mac));
        }
        return source_key;
    }
    
    static std::string FFmpegSourceKey(const std::string& app, const std::string& mac)
    {
        return "ffmpegsource:" + app + ":" + mac;
    }
    
    static std::string GetFFmpegSourceDstUrl(const std::string& app, const std::string& mac)
    {
        return "rtsp://127.0.0.1/" + app + "/" + mac;
    }

    static std::string LogicServerCacheKey(const std::string& app, const std::string& mac)
    {
        return app + ":" + mac;
    }
private:
    VideoRecordUtil() = delete;
    ~VideoRecordUtil() = delete;

    static std::string FileEncryptKey(const std::string& filename)
    {
        std::vector<std::string> filename_contents;
        SplitString(filename, "-", filename_contents);
        std::string uuid = filename_contents[0] + "-" + filename_contents[1];
        
        std::string aes_encrypt_key = uuid + std::string(VIDEO_AES256_KEY);
        return aes_encrypt_key;
    }

    // Token = MD5(ak_ftp:uuid:timestamp:file_md5)
    static std::string FtpFileToken(const std::string& uuid, std::time_t timestamp, const std::string& filepath)
    {
        std::string file_md5 = akuvox_encrypt::MD5::GetFileMD5(filepath);
        std::string token_string = "ak_ftp:" + uuid + ":" + std::to_string(timestamp) + ":" + file_md5;
        std::string token_hash = akuvox_encrypt::MD5(token_string).toStr();
        
        AK_LOG_INFO << "FtpFileToken: token_string = " << token_string << ", token_hash = " << token_hash; 
        return token_hash;
    }
};

}

#endif
