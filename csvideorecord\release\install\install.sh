#!/bin/bash

# ****************************************************************************
# Author        :   jiaqiao.zhang
# Last modified :   2024-07-03
# Filename      :   install.sh
# Version       :   V1.0.0.0
# Description   :   csvideorecord 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IMAGE_ADDR=$3

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
APP_NAME=csvideorecord                                  # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csvideorecord
RUN_SCRIPT=csvideorecordrun.sh
CTRL_SCRIPT=csvideorecordctl.sh
LOG_PATH=/var/log/csvideorecordlog
RECORD_PLAY_PATH=/usr/local/akcs/csvideorecord/data/record
APP_VIDEO_REOCRD_DIR=/usr/local/akcs/csvideorecord/data/videos

SIGNAL=${SIGNAL:-TERM}
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
GROUP_NAME=$(grep_conf 'GROUP_NAME' $INSTALL_CONF)
STORAGE_SAVE_FDFS=$(grep_conf 'STORAGE_PIC_SAVE_FDFS' $INSTALL_CONF)
STORAGE_SAVE_S3=$(grep_conf 'STORAGE_PIC_SAVE_S3' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
VRTSP_SERVER_DOMAIN=$(grep_conf 'VRTSP_SERVER_DOMAIN' $INSTALL_CONF)
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
LOG_MYSQL_INNER_IP=$(grep_conf 'LOG_MYSQL_INNER_IP' $INSTALL_CONF)
ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
ENABLE_LOG_DBPROXY=$(grep_conf 'ENABLE_LOG_DBPROXY' $INSTALL_CONF)
DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
MEDIA_SERVER_DOMAIN=$(grep_conf 'MEDIA_SERVER_DOMAIN' $INSTALL_CONF)
MEDIA_SERVERNAME_LIST=$(grep_conf 'MEDIA_SERVERNAME_LIST' $INSTALL_CONF)

# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
if [ -f $APP_HOME/scripts/$CTRL_SCRIPT ]; then
    bash "$APP_HOME"/scripts/$CTRL_SCRIPT stop
    sleep 2
fi

# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*server_inner_ip=.*/server_inner_ip=${SERVER_INNER_IP}/g
    s/^.*zlmediakit_inner_addr=.*/zlmediakit_inner_addr=${SERVER_INNER_IP}:8808/g
    s/^.*group_name=.*/group_name=${GROUP_NAME}/g
    s/^.*store_fdfs=.*/store_fdfs=${STORAGE_SAVE_FDFS}/g
    s/^.*store_s3=.*/store_s3=${STORAGE_SAVE_S3}/g
    s/^.*akcs_db_ip=.*/akcs_db_ip=${MYSQL_INNER_IP}/g
    s/^.*log_db_ip=.*/log_db_ip=${LOG_MYSQL_INNER_IP}/g
    s/^.*csvrtsp_outer_domain=.*/csvrtsp_outer_domain=${VRTSP_SERVER_DOMAIN}/g
    s/^.*zlmediakit_server_domain=.*/zlmediakit_server_domain=${MEDIA_SERVER_DOMAIN}/g
    s/^.*zlmediakit_servername_list=.*/zlmediakit_servername_list=${MEDIA_SERVERNAME_LIST}/g
    " "$PKG_ROOT"/conf/csvideorecord.conf

sed -i "
    s/^.*on_http_access=.*/on_http_access=http:\/\/${SERVER_INNER_IP}:8809\/hook\/on_http_access/g
    s/^.*on_record_mp4=.*/on_record_mp4=http:\/\/${SERVER_INNER_IP}:8809\/hook\/on_record_mp4/g
    " "$PKG_ROOT"/conf/config.ini

# dbproxy 配置
if [ "$ENABLE_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*akcs_db_port=.*/akcs_db_port=3308/g
        s/^.*akcs_db_ip=.*/akcs_db_ip=${DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csvideorecord.conf
else
    sed -i "
        s/^.*akcs_db_port=.*/akcs_db_port=3306/g
        s/^.*akcs_db_ip=.*/akcs_db_ip=${MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csvideorecord.conf
fi

if [ "$ENABLE_LOG_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*log_db_port=.*/log_db_port=3308/g
        s/^.*log_db_ip=.*/log_db_ip=${LOG_DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csvideorecord.conf
else
    sed -i "
        s/^.*log_db_port=.*/log_db_port=3306/g
        s/^.*log_db_ip=.*/log_db_ip=${LOG_MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csvideorecord.conf
fi

sed -i "s/^.*video_record_host=.*/video_record_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csvideorecord_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csvideorecord_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csvideorecord_redis.conf
fi

# FastDFS 配置
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" "$PKG_ROOT"/conf/csvideorecord_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" "$PKG_ROOT"/conf/csvideorecord_fdfs.conf
fi

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME

cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi
cd "$PKG_ROOT"

# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts

mkdir -p $APP_VIDEO_REOCRD_DIR
mkdir -p $RECORD_PLAY_PATH

echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi
# ----------------------------------------------------------------------------
# 启动业务服务
# ----------------------------------------------------------------------------
echo '启动服务'
$APP_HOME/scripts/$CTRL_SCRIPT start
sleep 2

echo '检查服务的运行状态'
$APP_HOME/scripts/$CTRL_SCRIPT status

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi

echo "$APP_NAME install complete."

# ----------------------------------------------------------------------------
# 启动点播服务
# ----------------------------------------------------------------------------
image_name=${IMAGE_ADDR}/ak_system/zlmediakit:master
record_path="-v /usr/local/akcs/csvideorecord/data/record:/opt/media/bin/www/record"
conf_path="-v /usr/local/akcs/csvideorecord/conf/config.ini:/opt/media/conf/config.ini"
app_record_path="-v /usr/local/akcs/csvideorecord/data/videos:/opt/media/bin/www/app"

docker pull $image_name
if [ `docker ps -a | grep zlmediakit | wc -l` -gt 0 ]; then 
    docker stop zlmediakit
    docker rm zlmediakit
fi

echo "docker run -d --net host -e TZ=Asia/Shanghai --restart=always --name zlmediakit $conf_path $record_path $app_record_path $image_name"
docker run -d --net host -e TZ=Asia/Shanghai --restart=always --name zlmediakit $conf_path $record_path $app_record_path $image_name

#注册etcd
export ETCDCTL_API=3
ZLMEDIAKIT_ADDR="$SERVER_INNER_IP:8808"
ret=$(/usr/local/bin/php /bin/etcd_cli.php /bin/etcdctl "$ETCD_INNER_IP" put akcs/zlmediakit/inner/${SERVER_INNER_IP} "$ZLMEDIAKIT_ADDR")
if [[ $ret != *ok!* ]]; then
    echo "plz check etcd server is ok."
    exit 1
fi

echo "zlmediakit install complete."