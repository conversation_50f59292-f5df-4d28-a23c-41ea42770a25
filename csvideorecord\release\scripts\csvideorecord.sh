#!/bin/bash
ACMD="$1"
csvideorecord_BIN='/usr/local/akcs/csvideorecord/bin/csvideorecord'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csvideorecord()
{
	nohup $csvideorecord_BIN >/dev/null 2>&1 &
    echo "Start csvideorecord successful"
    if [ -z "`ps -fe|grep "csvideorecordrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csvideorecord/scripts/csvideorecordrun.sh >/dev/null 2>&1 &
    fi
}
stop_csvideorecord()
{
    echo "Begin to stop csvideorecordrun.sh"
    csvideorecordrunid=`ps aux | grep -w csvideorecordrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csvideorecordrunid}" ];then
	    echo "csvideorecordrun.sh is running at ${csvideorecordrunid}, will kill it first."
	    kill -9 ${csvideorecordrunid}
    fi
    echo "Begin to stop csvideorecord"
    kill -9 `pidof csvideorecord`
    sleep 2
    echo "Stop csvideorecord successful"
}

case $ACMD in
  start)
     start_csvideorecord
    ;;
  stop)
     stop_csvideorecord
    ;;
  restart)
    stop_csvideorecord
    sleep 1
    start_csvideorecord
    ;;
  status)
    if [ -f /var/run/csvideorecord.pid ];then
        pid=`cat /var/run/csvideorecord.pid`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csvideorecord is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csvideorecord is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

