#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
BACKEND_CONF=/etc/app_backend_install.conf
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
BEANSTALKD_IP=`cat $BACKEND_CONF | grep -w BEANSTALKD_IP | awk -F'=' '{ print $2 }'`

# Define functions
cmd_usage() {
  echo "usage: $0 upload <image_file> <account>"
  echo "       $0 delete <path>"
  echo "       $0 metrics"
  exit 0
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi

# Check command
if [[ "$1" == "upload" ]]; then
  image_file=$2
  account=$3
  curl http://$SERVER_INNER_IP:8798/face/upload/ -F face=@$image_file -F account=$account  -H "Trace-ID: **********" -v
elif [[ "$1" == "delete" ]]; then
  path=$2
  curl http://$SERVER_INNER_IP:8798/face/delete/ -d path=$path -H "Trace-ID: **********" -v
elif [[ "$1" == "metrics" ]]; then
  echo "curl $SERVER_INNER_IP:8809/metrics"
  curl $SERVER_INNER_IP:8809/metrics
else
  cmd_usage
fi

