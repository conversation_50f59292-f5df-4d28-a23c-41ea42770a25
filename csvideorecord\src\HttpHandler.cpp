#include "Md5.h"
#include "json/json.h"
#include "AkLogging.h"
#include "util_string.h"
#include "HttpMessage.h"
#include "HttpHandler.h"
#include "SafeCacheConn.h"
#include "AkcsCommonDef.h"
#include "AkcsHttpRequest.h"
#include "AkcsPasswdConfuse.h"
#include "VideoRecordConfig.h"
#include "VideoRecordErrorCode.h"
#include "HttpAccessAuth.hpp"
#include "MediaServerApi.hpp"
#include "VideoRecordUtil.hpp"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "MetricService.h"


extern LOG_DELIVERY g_log_delivery_config;
extern VIDEO_RECORD_CONFIG g_video_record_config;

namespace csvideorecord
{
    csvideorecord::HTTPRespCallback ReqVideoUrlHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        try
        {
            HttpRespKV resp_data;
            
            // 文件服务器上的存储路径
            std::string filename = ctx->GetQuery("filename");
            std::string storage_url = URLDecode(ctx->GetQuery("url"));

            // 判断该资源是否在缓存中
            std::string play_url = csvideorecord::VideoRecordUtil::GetVideoUrlCache(storage_url);

            // 资源在缓存中直接返回url
            if (!play_url.empty()) {
                resp_data["url"] = play_url;
                AK_LOG_INFO << "storage_url in cache, storage_url = " << storage_url << ", play_url = " << play_url;
                cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS, resp_data));
                return;
            }
            
            // 获取文件名称,下载路径
            std::string local_filepath = csvideorecord::VideoRecordUtil::VideoPlayLocalFilePath(filename);

            // 下载文件
            if (!csvideorecord::VideoRecordUtil::DownloadFile(storage_url, local_filepath)) {
                AK_LOG_WARN << "DownloadFile failed, storage_url = " << storage_url;
                cb(BuildHttpResponseMessage(ERR_CODE_DOWNLOAD_FILE_FAILED, resp_data));
                return;
            }
            
            // 对文件进行解密
            csvideorecord::VideoRecordUtil::DecryptFile(filename);
            
            // 生成点播url
            play_url = csvideorecord::VideoRecordUtil::GenerateVideoPlayUrl(filename);
            resp_data["url"] = play_url;
            AK_LOG_INFO << "GenerateVideoPlayUrl success, storage_url = " << storage_url << ", play_url = " << play_url;
            
            // 点播资源缓存到redis
            csvideorecord::VideoRecordUtil::CacheVideoPlayUrl(storage_url, play_url);

            cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS, resp_data));
            return;
        }
        catch(const std::exception& e)
        {
            AK_LOG_WARN << "ReqVideoUrlHandler catch exception: " << e.what();
            cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS));
        }
    };

    csvideorecord::HTTPRespCallback OnHttpAccessHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        try 
        {
            std::string hook_msg = ctx->body().ToString();
            AK_LOG_INFO << "OnHttpAccessHandler hook_msg = " << hook_msg;

            Json::Value root;
            Json::Reader reader;
            reader.parse(hook_msg, root);
            std::string path = root["path"].asString();
            std::string params = root["params"].asString();

            csvideorecord::VideoAuthenticator::AuthConfig config{
                .zlmediakit_servername = g_video_record_config.zlmediakit_servername,
                .access_private_key = g_video_record_config.access_privatekey,
                .zlmediakit_server_domain = g_video_record_config.zlmediakit_server_domain
            };
                
            csvideorecord::VideoAuthenticator authenticator(config);
            auto verify_result = authenticator.VerifyAuth(path, params);
                
            AK_LOG_INFO << "Auth verification for path = " << path 
                       << ", uid = " << verify_result.uid 
                       << ", result = " << (verify_result.is_valid ? "success" : "failed")
                       << ", error = " << verify_result.error_msg;

            if (verify_result.is_valid)
            {
                cb(BuildHttpResponseHookMessage(0));
            }
            else
            {
                cb(BuildHttpResponseHookMessage(-1, verify_result.error_msg));
            }
            return;
        }
        catch(const std::exception& e)
        {
            AK_LOG_WARN << "OnHttpAccessHandler catch exception: " << e.what();
            cb(BuildHttpResponseHookMessage(-1, e.what()));
        }
    };
    
    csvideorecord::HTTPRespCallback OnRecordMP4Handler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        try
        {
            std::string hook_msg = ctx->body().ToString();
            AK_LOG_INFO << "OnRecordMP4Handler hook_msg = \n" << hook_msg;
            
            Json::Value root;
            Json::Reader reader;
            if (!reader.parse(hook_msg, root)) {
                AK_LOG_WARN << "OnRecordMP4Handler Failed to parse JSON message";
                cb(BuildHttpResponseHookMessage(-1));
                return;
            }
            
            std::string app = root["app"].asString();
            std::string url = root["url"].asString();
            std::string mac = root["stream"].asString();
            std::string stream_key = csvideorecord::VideoRecordUtil::StreamProxyKey(app, mac);
            std::string local_filepath = std::string(PROCESS_APP_VIDEO_REOCRD_DIR) + "/" + url;

            SafeCacheConn redis(g_redis_db_video_record);
            std::string record_cache_key = csvideorecord::VideoRecordUtil::LogicServerCacheKey(app, mac);

            // 防止回调 处理多次
            if (!redis.isExists(record_cache_key)) {
                AK_LOG_INFO << "Already handle record callback, record_cache_key = " << record_cache_key;
                cb(BuildHttpResponseMessage(ERR_CODE_SUCCESS));
                return;
            }

            // 防止app未发Stop未停止拉流
            if (csvideorecord::MediaServerApi::IsRecording(app, mac)) {
                // 删除录制缓存的服务器地址, 停止录制 拉流
                redis.del(record_cache_key);
                csvideorecord::MediaServerApi::StopRecord(app, mac);
                csvideorecord::MediaServerApi::DelStreamProxy(app, mac, stream_key);
                AK_LOG_INFO << "Not receive app stop record request, media is still online, stream_key = " << stream_key;
            }

            // 获取本次录制关联的截图名称
            std::string pic_name = csvideorecord::VideoRecordUtil::GetRecordRelatePicName(stream_key);
            if (pic_name.empty()) {
                AK_LOG_WARN << "OnRecordMP4Handler Failed to get pic_name, stream_key = " << stream_key;
                cb(BuildHttpResponseHookMessage(-1));
                return;
            }
            
            // 文件名: cn-3cba6e767e3e11efa64706f736f69293-**********-APP-186f6cca1a801ccd27262d2bee174946.mp4
            std::string app_record_filename = csvideorecord::VideoRecordUtil::AppRecordFilename(mac, local_filepath);
            
            // 更新数据库VideoRecordName
            std::string db_delivery_uuid = dbinterface::ProjectUserManage::GetLogDeliveryUUIDByAccount(app);
            dbinterface::PersonalCapture::UpdateVideoRecordName(mac, pic_name, app_record_filename, g_log_delivery_config.personal_capture_delivery, db_delivery_uuid);
            
            // 移动给csstorage上传
            std::string upload_filepath = std::string(PROCESS_UPLOAD_DIR) + "/" + app_record_filename;
            rename(local_filepath.c_str(), upload_filepath.c_str());
            AK_LOG_INFO << "move  " << local_filepath << " to \n" << upload_filepath;
            
            cb(BuildHttpResponseHookMessage(0));
            return;
        }
        catch(const std::exception& e)
        {
           AK_LOG_WARN << "OnRecordMP4Handler catch exception: " << e.what();
           cb(BuildHttpResponseHookMessage(-1, e.what()));
        }
    };

    csvideorecord::HTTPRespCallback OnMetricsHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
    {
        try
        {
            AK_LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
            MetricService* metric_service = MetricService::GetInstance();
            if (metric_service == nullptr)
            {
                AK_LOG_WARN << "metric service init failed.";
                cb("# metric service init failed.");
                return;
            }
        
            metric_service->UpdateMetrics();
            std::string response = metric_service->ToFormateString();
            cb(response);
        }
        catch(const std::exception& e)
        {
           AK_LOG_WARN << "OnMetricsHandler catch exception: " << e.what();
           cb(BuildHttpResponseMessage(ERR_CODE_SERVER_ERR));
        }
    };
    
    csvideorecord::HTTPAllRespCallbackMap HTTPAllRespMapInit()
    {
        csvideorecord::HTTPAllRespCallbackMap OMap;
        OMap[csvideorecord::HTTP_ROUTE::VIDOE_URL] = ReqVideoUrlHandler;
        OMap[csvideorecord::HTTP_ROUTE::ON_HTTP_ACCESS] = OnHttpAccessHandler;
        OMap[csvideorecord::HTTP_ROUTE::ON_RECORD_MP4] = OnRecordMP4Handler;
        OMap[csvideorecord::HTTP_ROUTE::ON_METRICS] = OnMetricsHandler;
        return OMap;
    }
}

