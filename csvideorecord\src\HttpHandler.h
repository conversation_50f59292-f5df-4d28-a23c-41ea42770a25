#ifndef __CSVIDEORECORD_HTTP_RESPONSE_H__
#define __CSVIDEORECORD_HTTP_RESPONSE_H__

#include <cstdio>  
#include <fstream> 
#include <iostream>
#include <functional>
#include <evpp/http/context.h>

namespace csvideorecord
{
    // http request router.
    enum HTTP_ROUTE
    {
        VIDOE_URL = 0,
        ON_HTTP_ACCESS = 1,
        ON_RECORD_MP4 = 2,
        ON_METRICS = 3,
    };

    typedef std::function<void(const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)> HTTPRespCallback;
    typedef std::map<std::string, HTTPRespCallback> HTTPRespVerCallbackMap;
    typedef std::map<int, HTTPRespCallback> HTTPAllRespCallbackMap;

    HTTPAllRespCallbackMap HTTPAllRespMapInit();
}

#endif
