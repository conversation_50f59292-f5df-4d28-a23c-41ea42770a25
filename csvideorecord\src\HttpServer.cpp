#include "HttpServer.h"
#include "HttpMessage.h"
#include "HttpHandler.h"
#include "VideoRecordConfig.h"

// 全局变量
extern VIDEO_RECORD_CONFIG g_video_record_config;
static csvideorecord::HTTPAllRespCallbackMap g_http_response_callbacks;

// 当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define, http req is [ " << ctx->original_uri() << " ], remote_ip is " << ctx->remote_ip();
    LOG_INFO << "http req type is [ " << ctx->original_type() << " ]";
    ctx->AddResponseHeader("Content-Type", "application/json;charset=utf-8");
    cb(BuildHttpErrorMessage(ERR_CODE_INVALID_REQUEST_ROUTE));
}

void HttpReqVideoUrlCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    csvideorecord::HTTPRespCallback handler = g_http_response_callbacks[csvideorecord::HTTP_ROUTE::VIDOE_URL];
    if (handler)
    {
        handler(ctx, cb);
    }
    return;
}

void HttpHookVideoPlayAuthCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    csvideorecord::HTTPRespCallback handler = g_http_response_callbacks[csvideorecord::HTTP_ROUTE::ON_HTTP_ACCESS];
    if (handler)
    {
        handler(ctx, cb);
    }
    return;
}

void HttpHookVideoRecordMP4Callback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    csvideorecord::HTTPRespCallback handler = g_http_response_callbacks[csvideorecord::HTTP_ROUTE::ON_RECORD_MP4];
    if (handler)
    {
        handler(ctx, cb);
    }
    return;
}

void HttpMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    csvideorecord::HTTPRespCallback handler = g_http_response_callbacks[csvideorecord::HTTP_ROUTE::ON_METRICS];
    if (handler)
    {
        handler(ctx, cb);
    }
    return;
}

void StartHttpServer()
{
    bool ipv6 = false;
    const int port = g_video_record_config.http_port;
    const int thread_num = g_video_record_config.http_thread_num;
    g_http_response_callbacks = csvideorecord::HTTPAllRespMapInit();

    evpp::http::Server server(thread_num, ipv6);
    server.RegisterDefaultHandler(&DefaultHandler);

    // 获取点播和下载的url
    server.RegisterHandler("/video/url", HttpReqVideoUrlCallback);

    // 点播鉴权hook
    server.RegisterHandler("/hook/on_http_access", HttpHookVideoPlayAuthCallback);
    
    // 视频录制结束推送hook
    server.RegisterHandler("/hook/on_record_mp4", HttpHookVideoRecordMP4Callback);

    // metrics监控指标
    server.RegisterHandler("/metrics", HttpMetricsCallback);

    server.Init(port);
    server.Start();
    return;
}
