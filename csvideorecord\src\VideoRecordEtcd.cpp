#include "util.h"
#include <etcd/Client.hpp>
#include "evpp/event_watcher.h"
#include "EtcdDns.h"
#include "EtcdCliMng.h"
#include "VideoRecordEtcd.h"
#include "VideoRecordConfig.h"

extern VIDEO_RECORD_CONFIG g_video_record_config;
extern CEtcdDnsManager* g_etcd_dns_mng;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    AK_LOG_INFO << "RegSrv2Etcd key = " << key << ", value = " << value;
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}

int EtcdConnInit()
{
    g_etcd_dns_mng = new CEtcdDnsManager(g_video_record_config.etcd_server_addr);
    std::thread dns_thread = std::thread(&CEtcdDnsManager::StartDnsResolver, g_etcd_dns_mng);
    while(!g_etcd_dns_mng->DnsIsOk())
    {
        usleep(10);
    }
    dns_thread.detach();
    
    //域名解析完才能初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(g_etcd_dns_mng->GetAddrs());
    g_etcd_dns_mng->SetEtcdCli(g_etcd_cli_mng); 
    return 0;
}

void EtcdSrvInit()
{
    std::string rpc_addr = GetInnerIPAddr() + ":" + std::to_string(g_video_record_config.rpc_port);
    std::string http_addr = GetInnerIPAddr() + ":" + std::to_string(g_video_record_config.http_port);

    std::string rpc_reg_key = std::string("akcs/csvideorecord/rpc/") + rpc_addr;
    std::string http_reg_key = std::string("akcs/csvideorecord/innerip/") + http_addr;

    RegSrv2Etcd(rpc_reg_key, rpc_addr, 10, csbase::REG_INNER, g_etcd_loop.get());
    RegSrv2Etcd(http_reg_key, http_addr, 10, csbase::REG_INNER, g_etcd_loop.get());
    g_etcd_loop->Run();

    return;
}

