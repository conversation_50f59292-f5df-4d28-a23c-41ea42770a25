#include "util.h"
#include "EtcdCliMng.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "LogConnectionPool.h"
#include "MappingConnectionPool.h"
#include "VideoRecordInit.h"
#include "VideoRecordDefine.h"
#include "VideoRecordConfig.h"
#include "VideoRecordRpcServer.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/SystemSettingTable.h"
#include <KdcDecrypt.h>

LOG_DELIVERY g_log_delivery_config;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern VIDEO_RECORD_CONFIG g_video_record_config;

int LogDeliveryInit()
{
    g_log_delivery_config.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    g_log_delivery_config.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    g_log_delivery_config.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    if (g_log_delivery_config.personal_capture_delivery == 0 || g_log_delivery_config.personal_motion_delivery == 0 || g_log_delivery_config.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}

void ConfigInit()
{
    CConfigFileReader config_file(PROCESS_CONF_FILE);

    g_video_record_config.db_port = ATOI(config_file.GetConfigName("akcs_db_port"));
    g_video_record_config.log_db_port = ATOI(config_file.GetConfigName("log_db_port"));
    g_video_record_config.store_s3 = ATOI(config_file.GetConfigName("store_s3"));
    g_video_record_config.store_fdfs = ATOI(config_file.GetConfigName("store_fdfs"));
    g_video_record_config.rpc_port = ATOI(config_file.GetConfigName("rpc_port"));
    g_video_record_config.http_port = ATOI(config_file.GetConfigName("http_port"));
    g_video_record_config.http_thread_num = ATOI(config_file.GetConfigName("http_thread_num"));
    g_video_record_config.video_expiration_seconds = ATOI(config_file.GetConfigName("video_expiration_seconds"));    

    Snprintf(g_video_record_config.db_ip, sizeof(g_video_record_config.db_ip),  config_file.GetConfigName("akcs_db_ip"));
    Snprintf(g_video_record_config.log_db_ip, sizeof(g_video_record_config.log_db_ip),  config_file.GetConfigName("log_db_ip"));
    Snprintf(g_video_record_config.group_name, sizeof(g_video_record_config.group_name),  config_file.GetConfigName("group_name"));

    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(g_video_record_config.db_password, sizeof(g_video_record_config.db_password), decrypt_db_passwd.c_str());

    Snprintf(g_video_record_config.db_username, sizeof(g_video_record_config.db_username),  config_file.GetConfigName("db_username"));
    Snprintf(g_video_record_config.db_database, sizeof(g_video_record_config.db_database),  config_file.GetConfigName("akcs_db_database"));
    Snprintf(g_video_record_config.log_db_database, sizeof(g_video_record_config.log_db_database),  config_file.GetConfigName("log_db_database"));
    Snprintf(g_video_record_config.etcd_server_addr, sizeof(g_video_record_config.etcd_server_addr), config_file.GetConfigName("etcd_srv_net"));
    Snprintf(g_video_record_config.server_inner_ip, sizeof(g_video_record_config.server_inner_ip), config_file.GetConfigName("server_inner_ip"));
    Snprintf(g_video_record_config.zlmediakit_secret, sizeof(g_video_record_config.zlmediakit_secret), config_file.GetConfigName("zlmediakit_secret"));
    Snprintf(g_video_record_config.access_privatekey, sizeof(g_video_record_config.access_privatekey), config_file.GetConfigName("access_privatekey"));
    Snprintf(g_video_record_config.csvrtsp_outer_domain, sizeof(g_video_record_config.csvrtsp_outer_domain), config_file.GetConfigName("csvrtsp_outer_domain"));
    Snprintf(g_video_record_config.zlmediakit_inner_addr, sizeof(g_video_record_config.zlmediakit_inner_addr), config_file.GetConfigName("zlmediakit_inner_addr"));
    Snprintf(g_video_record_config.zlmediakit_server_domain, sizeof(g_video_record_config.zlmediakit_server_domain), config_file.GetConfigName("zlmediakit_server_domain"));
    Snprintf(g_video_record_config.zlmediakit_servername_list, sizeof(g_video_record_config.zlmediakit_servername_list), config_file.GetConfigName("zlmediakit_servername_list"));

    ParseMediaServeConf();
    return;
}

void ParseMediaServeConf()
{
    std::vector<std::string> servername_list;
    SplitString(g_video_record_config.zlmediakit_servername_list, ",", servername_list);

    for (const auto& sververname : servername_list)
    {
        size_t pos = sververname.find(':');
        if (pos != std::string::npos) 
        {
            std::string server_name = sververname.substr(0, pos);
            std::string server_ip = sververname.substr(pos + 1);
            
            if (strncmp(g_video_record_config.server_inner_ip, server_ip.c_str(), sizeof(g_video_record_config.server_inner_ip)) == 0)
            {
                Snprintf(g_video_record_config.zlmediakit_servername, sizeof(g_video_record_config.zlmediakit_servername), server_name.c_str());
                AK_LOG_INFO << "server_inner_ip = " << g_video_record_config.server_inner_ip << ", zlmediakit_servername = " << g_video_record_config.zlmediakit_servername;
            }
        }
    }
    
    return;
}

int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(g_video_record_config.db_ip, sizeof(g_video_record_config.db_ip),  conf_tmp.db_ip);
    g_video_record_config.db_port = conf_tmp.db_port;
    return 0;
}

int DaoInit()
{
    LogConnPool* log_conn_pool = GetLogDBConnPollInstance();
    if (NULL == log_conn_pool)
    {
        AK_LOG_WARN << "log DaoInit failed.";
        return -1;
    }
    log_conn_pool->Init(g_video_record_config.log_db_ip, g_video_record_config.db_username, g_video_record_config.db_password, g_video_record_config.log_db_database, g_video_record_config.log_db_port, MAX_RLDB_CONN, "csvideorecord");

    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    LoadConfFromConfSrv();
    gConnPool->Init(g_video_record_config.db_ip, g_video_record_config.db_username, g_video_record_config.db_password, g_video_record_config.db_database, g_video_record_config.db_port, MAX_RLDB_CONN, "csvideorecord");
    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(g_video_record_config.db_ip, g_video_record_config.db_port);
    return 0;
}

int RpcServerInit()
{
    VideoRecordRpcServer rpc_server("8810");
    rpc_server.Run();
    return 0;
}
