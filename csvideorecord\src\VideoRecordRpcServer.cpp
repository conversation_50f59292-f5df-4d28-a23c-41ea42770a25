#include "util.h"
#include "MediaServerApi.hpp"
#include "VideoRecordUtil.hpp"
#include "VideoRecordRpcServer.h"
#include "NotifyMsgControl.h"
#include "NotifyStartRecord.h"
#include "NotifyStopRecord.h"

void VideoRecordRpcServer::Run()
{
    std::string listen_net = GetEth0IPAddr() + std::string(":") + rpc_port_;
    std::string server_address(listen_net);
    ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service_);
    cq_ = builder.AddCompletionQueue();//可以多个的.
    server_ = builder.BuildAndStart();
    AK_LOG_INFO << "VideoRecordRpcServer run grpc server listening on " << server_address;

    new CallData(&service_, cq_.get(), CSVIDEORECORD_RPC_SERVER_TYPE::START_VIDEO_RECORD);
    new CallData(&service_, cq_.get(), CSVIDEORECORD_RPC_SERVER_TYPE::STOP_VIDEO_RECORD);

    std::thread HandleRpcsThread(std::bind(&VideoRecordRpcServer::HandleRpcs, this));
    HandleRpcs();
    return;
}

void VideoRecordRpcServer::CallData::Proceed()
{
    if (status_ == CREATE)
    {
        // Make this instance progress to the PROCESS state.
        status_ = PROCESS;
        switch (s_type_)
        {
            case CSVIDEORECORD_RPC_SERVER_TYPE::START_VIDEO_RECORD:
            {
                service_->RequestStartVideoRecordHandle(&ctx_, &start_video_record_request_, &start_video_record_responder_, cq_, cq_, this);
                break;
            }
            case CSVIDEORECORD_RPC_SERVER_TYPE::STOP_VIDEO_RECORD:
            {
                service_->RequestStopVideoRecordHandle(&ctx_, &stop_video_record_request_, &stop_video_record_responder_, cq_, cq_, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }
    }
    else if (status_ == PROCESS)
    {
        status_ = FINISH;
        new CallData(service_, cq_, this->s_type_);
        switch (s_type_)
        {
            case CSVIDEORECORD_RPC_SERVER_TYPE::START_VIDEO_RECORD:
            {
                StartRecordHandle start_record(start_video_record_request_.site(), start_video_record_request_.mac());
                GetNotifyMsgControlInstance()->AddStartRecordMsg(std::move(start_record));

                start_video_record_reply_.set_ret(0);
                start_video_record_responder_.Finish(start_video_record_reply_, Status::OK, this);
                break;
            }
            case CSVIDEORECORD_RPC_SERVER_TYPE::STOP_VIDEO_RECORD:
            {
                StopRecordHandle stop_record(stop_video_record_request_.site(), stop_video_record_request_.mac());
                GetNotifyMsgControlInstance()->AddStopRecordMsg(std::move(stop_record));

                stop_video_record_reply_.set_ret(0);
                stop_video_record_responder_.Finish(stop_video_record_reply_, Status::OK, this);
                break;
            }
            default:
            {
                AK_LOG_WARN << "invalid service type " << (int)s_type_;
                break;
            }
        }
    }
    else
    {
        GPR_ASSERT(status_ == FINISH);
        // Once in the FINISH state, deallocate ourselves (CallData).
        delete this;
    }
}

// This can be run in multiple threads if needed.
void VideoRecordRpcServer::HandleRpcs()
{
    //TODO 当开启多线程的时候,这个必须挪到业务线程之前?
    //new CallData(&service_, cq_.get());
    void* tag;
    bool ok;
    while (true)
    {
        {
            std::lock_guard<std::mutex> lock(mtx_cq_);
            //modified by chenyc,2021-10-19,原先的代码写得不严谨,在一些场景下ok可能为false,具体可参考:gRPC源码中CompletionQueue的描述
            //TODO: 每个HandleRpcs线程单独一个cq,避免加锁与消息干扰.
            if(cq_->Next(&tag, &ok) != true || !ok)
            {
                AK_LOG_WARN << "gRPC HandleRpcs cq next operation error ";
                continue;
            }
            //GPR_ASSERT(cq_->Next(&tag, &ok));
            //GPR_ASSERT(ok);//这里断言会概率失败
        }
        static_cast<CallData*>(tag)->Proceed();
    }
}

