#include "AkLogging.h"
#include "CloudStorageMng.h"

static const char kUploaderConfigFilePath[] = "/etc/oss_install.conf";

CloudStorageMng::CloudStorageMng()
{
    uploader_ = std::unique_ptr<S3Uploader>(new S3Uploader());
    uploader_->Init(kUploaderConfigFilePath);
}

bool CloudStorageMng::DownloadFile(const std::string& remote_filepath, const std::string& local_filepath)
{
    return uploader_->DownloadVideoFile(remote_filepath, local_filepath);
}