#include "AkLogging.h"
#include "FdfsStorageMng.h"
#include "VideoRecordDefine.h"
#include "VideoRecordConfig.h"

extern VIDEO_RECORD_CONFIG g_video_record_config;

FdfsStorageMng::FdfsStorageMng(const char* file_name)
{
    uploader_ = std::unique_ptr<FdfsUploader>(new FdfsUploader());
    uploader_->Init(file_name);
    uploader_->SetUploadGroupName(g_video_record_config.group_name);
    log_set_filename(PROCESS_FDFS_LOG_PATH);
}

bool FdfsStorageMng::DownloadFile(const std::string& remote_filepath, const std::string& local_filepath)
{
    AK_LOG_INFO << "remote_filepath = " << remote_filepath << ", local_filepath = " << local_filepath;
    return uploader_->DownloadFile(remote_filepath, local_filepath);
}
