#include <memory>
#include "AES256.h"
#include "CachePool.h"
#include "HttpServer.h"
#include "AkcsAppInit.h"
#include "EtcdCliMng.h"
#include "DirCleaner.hpp"
#include "NotifyMsgControl.h"
#include "VideoRecordInit.h"
#include "ConfigFileReader.h"
#include "ConnectionPool.h"
#include "VideoRecordEtcd.h"
#include "VideoRecordDefine.h"
#include "VideoRecordConfig.h"
#include "AkcsDnsResolver.h"
#include "FdfsStorageMng.h"
#include "CloudStorageMng.h"
#include "VideoRecordRpcServer.h"
#include "Metric.h"

VIDEO_RECORD_CONFIG g_video_record_config;
DirectoryCleaner* g_dir_cleaner = nullptr;
FdfsStorageMng* g_fdfs_storage_mng = nullptr;
CloudStorageMng* g_cloud_storage_mng = nullptr;

void DirectoryCleanerThread() 
{
    g_dir_cleaner = new DirectoryCleaner(PROCESS_VIDEO_REOCRD_PLAY_DIR);
    if (g_dir_cleaner != nullptr) {
        g_dir_cleaner->CleanDirectories();
    }
    return;
}

void InstanceInit()
{
    CacheManager::getInstance()->Init(PROCESS_REDIS_CONF_FILE, "csvideorecordCacheInstances");
    
    GetNotifyMsgControlInstance()->Init();
}

int main(int argc, char* argv[])
{
    // Make sure only one instance run.
    if (!IsSingleton2("/var/run/csvideorecord.pid"))
    {
        printf("another csvideorecord has been running in this system.\n") ;
        return -1;
    }
    
    GlogInit2(argv[0], "csvideorecordlog");

    ConfigInit();
    
    EtcdConnInit();
    
    InstanceInit();

    if(DaoInit() != 0)
    {
        AK_LOG_FATAL << "DaoInit fialed.";
        return -1;
    }

    if (LogDeliveryInit() != 0)
    {
        AK_LOG_FATAL << "LogDeliveryInit fialed.";
        return -1;
    }

    // init etcd register loop.
    std::thread etcd_cli_thread = std::thread(EtcdSrvInit);

    // fdfs
    g_fdfs_storage_mng = new FdfsStorageMng(PROCESS_FDFS_CONF_FILE);
 
    // 对象存储
    g_cloud_storage_mng = new CloudStorageMng();

    std::thread rpc_server_thread = std::thread(RpcServerInit);
    
    std::thread http_thread(StartHttpServer);
    
    std::thread directory_cleaner_thread(DirectoryCleanerThread);
    
    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csvideorecord is running.";

    http_thread.join();
    etcd_cli_thread.join();
    directory_cleaner_thread.join();

    GlogClean2();
    return 0;
}
