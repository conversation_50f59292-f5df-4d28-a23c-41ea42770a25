#include "NotifyMsgControl.h"
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"

CNotifyMsgControl* CNotifyMsgControl::record_instance = NULL;

CNotifyMsgControl::~CNotifyMsgControl()
{
    notify_msg_list_.clear();
}

CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}

CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (record_instance == NULL)
    {
        record_instance = new CNotifyMsgControl();
    }
    return record_instance;
}

int CNotifyMsgControl::Init()
{
    thread_ = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    AK_LOG_INFO << "NotifyMsg Thread Start Success, thread_id = " << thread_.get_id();
    return 0;
}

int CNotifyMsgControl::GetNotifyMsgListSize()
{
    std::unique_lock<std::mutex> lock(mutex_);
    return notify_msg_list_.size();
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        NotifyMsgPrt msg_ptr;
        {
            std::unique_lock<std::mutex> lock(mutex_);
            while (notify_msg_list_.size() == 0)
            {
                condition_variable_.wait(lock);
            }
           
            msg_ptr = notify_msg_list_.back();
            notify_msg_list_.pop_back();
        }
        
        msg_ptr->NotifyMsg();
    }
    return 0;
}

int CNotifyMsgControl::AddStartRecordMsg(const StartRecordHandle&& msg)
{
    {
        std::unique_lock<std::mutex> lock(mutex_);
        notify_msg_list_.emplace_back(std::make_shared<StartRecordHandle>(std::move(msg)));
        condition_variable_.notify_all();
    }
    return 0;
}

int CNotifyMsgControl::AddStopRecordMsg(const StopRecordHandle&& msg)
{
    {
        std::unique_lock<std::mutex> lock(mutex_);
        notify_msg_list_.emplace_back(std::make_shared<StopRecordHandle>(std::move(msg)));
        condition_variable_.notify_all();
    }
    return 0;
}

