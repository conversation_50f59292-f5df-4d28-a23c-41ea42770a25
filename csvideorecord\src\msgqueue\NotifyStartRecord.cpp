#include "NotifyStartRecord.h"
#include "MediaServerApi.hpp"
#include "VideoRecordUtil.hpp"

void StartRecordHandle::NotifyMsg()
{
    std::string rtsp_url = csvideorecord::VideoRecordUtil::GetRtspUrl(mac_);
    std::string stream_key = csvideorecord::VideoRecordUtil::StreamProxyKey(site_, mac_);
    
    // 开始拉流
    if (!csvideorecord::MediaServerApi::AddStreamProxy(site_, mac_, rtsp_url)) {
        AK_LOG_WARN << "add_stream_proxy failed, site = " << site_ << ", mac = " << mac_;
        return;
    }

    // 判断流是否注册上
    if (!csvideorecord::MediaServerApi::IsMediaOnline(site_, mac_)) {
        AK_LOG_WARN << "Media is offline, site = " << site_ << ", mac = " << mac_;
        return;
    }
    
    // 开始录制
    if (!csvideorecord::MediaServerApi::StartRecord(site_, mac_)) {
        AK_LOG_WARN << "StartRecord failed, site = " << site_ << ", mac = " << mac_;
        return;
    }
    return;
}