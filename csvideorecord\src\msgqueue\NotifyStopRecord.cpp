#include "NotifyStopRecord.h"
#include "MediaServerApi.hpp"
#include "VideoRecordUtil.hpp"

void StopRecordHandle::NotifyMsg()
{
    AK_LOG_INFO << "StopVideoRecord site = " << site_ << ", mac = " << mac_;
    std::string stream_key = csvideorecord::VideoRecordUtil::StreamProxyKey(site_, mac_);
    
    // 判断流是否在线
    if (!csvideorecord::MediaServerApi::IsMediaOnline(site_, mac_)) {
        AK_LOG_WARN << "Media is offline, site = " << site_ << ", mac = " << mac_;
    }

    // 停止拉流录制
    if (!csvideorecord::MediaServerApi::StopRecord(site_, mac_)) {
        AK_LOG_WARN << "StopRecord failed, site = " << site_ << ", mac = " << mac_;
    }

    // 删除该路流
    if (!csvideorecord::MediaServerApi::DelStreamProxy(site_, mac_, stream_key)) {
        AK_LOG_WARN << "DelStreamProxy Failed, site = " << site_ << ", mac = " << mac_;
    }
    return;
}

