import redis   # 导入redis 模块
from redis import ConnectionPool

connection_pool = ConnectionPool(
    host="RESID_SERVER_ADDR",
    port=8504,
    password="Akcs#xm2610*",
    max_connections=20,
    db=34
)

def auth_device_token(user, token):
    r = redis.Redis(connection_pool=connection_pool)
    key = "voice_" + user
    stored_token = r.get(key)
    if stored_token is None:
        return False  # 如果用户不存在，返回 False
    if stored_token.decode('utf-8') == token:  # 解码 Redis 返回的字节数据并比较
        return True
    return False