import pymysql
from dbutils.pooled_db import PooledDB

# API调用次数限制
MAX_API_CALLS = 300

# MySQL数据库配置
mysql_config = {
    'host': 'MYSQL_SERVER_ADDR',
    'port': MYSQL_SERVER_PORT,
    'user': 'dbuser01',
    'password': 'Ak@56@<EMAIL>',
    'database': 'AKCS',
    'charset': 'utf8'
}

# PyMySQL连接池配置
mysql_pool = PooledDB(
    creator=pymysql,
    maxconnections=2,
    mincached=1,
    maxcached=2,
    blocking=True,
    **mysql_config
)

def get_api_call_stats(mac: str) -> tuple:
    """
    获取API调用统计信息
    :param mac: 设备MAC地址
    :return: (exceeded, requester) 
    """
    db = None
    cursor = None
    try:
        db = mysql_pool.connection()
        cursor = db.cursor()
        
        # 查询设备信息，确定Requester值
        requester = mac
        cursor.execute("""
            SELECT Node FROM Devices WHERE MAC = %s
            UNION
            SELECT Node FROM PersonalDevices WHERE MAC = %s
        """, (mac, mac))
        result = cursor.fetchone()
        if result and result[0]:  # Node不为空或NULL
            requester = result[0]
        
        # 检查VoiceApiCallStat表中是否已存在该Requester
        cursor.execute("SELECT Count FROM VoiceApiCallStat WHERE Requester = %s", (requester,))
        stat = cursor.fetchone()
        
        count = stat[0] if stat else 0        
        if count > MAX_API_CALLS:
            return (True, requester)
        return (False, requester)

    except Exception as e:
        print(f"Error getting API call stats: {e}")
        return (True, mac)
    finally:
        if cursor:
            cursor.close()
        if db:
            db.close()


def update_api_call_stats(requester: str) -> bool:
    """
    更新API调用统计信息
    :param requester: 请求者标识
    :return: 操作是否成功
    """
    db = None
    cursor = None
    try:
        db = mysql_pool.connection()
        cursor = db.cursor()
        
        # 使用INSERT ... ON DUPLICATE KEY UPDATE语法
        cursor.execute("""
            INSERT INTO VoiceApiCallStat (UUID, Requester, Count) 
            VALUES (UUID(), %s, 1)
            ON DUPLICATE KEY UPDATE 
                Count = Count + 1,
                UpdateTime = NOW()
        """, (requester,))
        
        db.commit()
        return True
    except Exception as e:
        print(f"Error updating API call stats: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        if db:
            db.close()
