__version__ = "********"
#此文件后续由算法组提供
import os
import google.generativeai as genai
import pathlib
from datetime import datetime

# 设置代理服务器地址和端口，因为本地要翻墙才能访问到google api
#proxy_host = '127.0.0.1'
#proxy_port = '57890'
#os.environ['http_proxy'] = f'http://{proxy_host}:{proxy_port}'
#os.environ['https_proxy'] = f'http://{proxy_host}:{proxy_port}'

#------------------自定义的函数如下：

def adjustBrightness(text: str, parameter: str, mode: str, reply: str):
    """
    Adjust the device brightness as specified by user input. The brightness can be set in two formats:

    - Percentage mode: Clearly indicated by a "%" symbol. Accepted range: "0%" to "100%".
    - Absolute mode: Integer values without "%" symbol. Accepted range: "0" to "255".

    The input value ("parameter") must be validated and adjusted to fall within these ranges. Maintain the inclusion or exclusion of the "%" sign consistent with the user input after value adjustment.

    Args:
        text (str): The original user command specifying how to adjust brightness.
        parameter (str): Brightness value specified by user, can be:
                         - Percentage format (e.g., "50%"): valid range "0%" to "100%".
                         - Absolute value without "%" (e.g., "128"): valid integer range "0" to "255".
                         Only used when mode is "set".
        mode (str): Action mode, can be one of the following:
                    - "set": Set brightness directly to the given 'parameter' value.
                    - "auto": Activate automatic brightness adjustment mode.
                    - "nonauto": Disable automatic brightness, switch to manual adjustment mode.
                    - "up": Increase brightness by 10% of maximum brightness (rounded appropriately).
                    - "down": Decrease brightness by 10% of maximum brightness (rounded appropriately).
        reply (str): Human-readable response to the user after executing the brightness adjustment operation.

    Returns:
        str: Confirmation reply provided to the user.

    Error Handling Suggestions:
        - Detect if the 'parameter' value is out of range or invalid. Provide explicit user feedback without performing the adjustment.
        - When adjusting brightness upward or downward, prevent brightness from exceeding minimum (0 or 0%) and maximum (255 or 100%) bounds, and clearly inform the user if clipping adjustments were required.

    """
    return reply

def adjustVolume(text: str, parameter: str, mode: str, type: str, reply: str):
    """
    Adjust the device volume based on user input. The volume can be specified in two ways:
    - Percentage value: indicated explicitly with a "%" sign (range: "0%" - "100%")
    - Absolute value: a number without the "%" sign (range: "0" - "15")

    Clearly differentiate and validate the volume specification format.
    For volume adjustment ("up"/"down"), adjust by increments/decrements of 10% of the full volume scale. For the absolute value ("0"-"15"), round changes to the nearest integer within the range.

    Args:
        text (str): Original user input text indicating volume adjustment instructions.
        parameter (str): The target volume value in one of two formats:
                         - Percentage (e.g., "50%"), valid values "0%" ~ "100%".
                         - Absolute number without "%", valid integers "0" ~ "15".
        mode (str): The operation mode, accepts one of:
                    - "set": Immediately set the volume to the specified value in 'parameter'.
                    - "up": Increase current volume by 10% (rounded appropriately).
                    - "down": Decrease current volume by 10% (rounded appropriately).
        type (str): Optional, specifies the audio category affected. Possible values are "", "ring", "call", "mic", "media".
                    When undefined or empty, adjust the default system/main volume.
        reply (str): A human-readable response designed to confirm the performed operation to the user.

    Returns:
        str: Confirmation message ('reply') to be shown to the user.

    Error Handling Recommendations:
        - If 'parameter' contains invalid characters or values outside the stated ranges, return a clear error message rather than performing adjustments.
        - Explicitly inform the user if the requested operation causes the volume to exceed its upper or lower bounds, clipping the volume to the allowed range.
    """
    return reply

def switchWallpapers(text: str, description: str, reply: str):
    """
    Automatically switches to the next wallpaper based on the provided description.
    Args:
        text: Unused string input.
        description: Optional string specifying the image type. Choose from the predefined list:
                     {description_options}. If no match or close type exists, return null.
        reply: Custom user-friendly response returned after switching the wallpaper.

    """
    return reply

def switchMonitoringWindow(text: str, parameter: str, mode: str,start_time:str, reply: str):
    """
    Understands user input and prepares monitoring actions based on context.

    This function takes user input parameters and determines the requested
    operation mode (e.g., viewing specific monitoring windows or all available ones,
    or exiting the monitoring application).

    Args:
        text (str): [Required] The original user input in plain text for logging or context (e.g., "Check yesterday 5 PM monitoring").
        mode (str): [Required] The action mode:
            - 'set': Switches to the specific monitoring window defined by `parameter`.
            - 'all': Displays all monitoring windows from a specific `start_time`.
            - 'quit': Exits or closes monitoring functionality.
        reply (str): [Required] A pre-generated response message that will be returned upon valid parsing/execution of the request.
        parameter (str, optional): The specific monitoring window name (It must be selected from the monitoring list entered by the user. If "No monitoring available" is displayed, the user will be reminded). Required in `set` mode and ignored in others.
        start_time (str, optional): Specifies the start time for monitoring data in ISO 8601 format (e.g., "2023-10-26T17:00:00Z"). Optional in both 'set' and 'all' modes.
        Please pay attention to the following matters: When it is determined that the user wants to watch the playback instead of real-time monitoring, for example, the command contains the words "playback" or "monitor playback", but does not clearly state the time point they want to watch, the parameter needs to be passed as "0000-00-00T00:00:00Z";If the user wants to view real-time monitoring, and the command does not indicate that the user wants to view monitoring responses, then this parameter should be empty.

    Returns:
        str: Returns the `reply` string provided if the function parameters are valid and parsed correctly.

    """
    return reply

def toggleDND(text: str, enable: bool, reply: str):
    """
    Toggles 'Do Not Disturb' (DND) mode for the application.
    Args:
        text: Unused string input.
        enable: Boolean value - True to enable DND (suppress notifications), False to disable DND (allow notifications).
        reply: Custom user-friendly response returned after switching DND mode.
    """
    return reply

def toggleDefenseMode(text: str, mode: str, reply: str):
    """
    Toggles defense mode by interpreting input text to activate or deactivate defensive measures.

    Modes:
        - Home Mode (default)
        - Away Mode
        - Night Mode
        - Unarm (deactivates defense)

    Args:
        text (str): Input command to determine the desired mode. Natural language commands (e.g., "disarm", "turn off defense", "going to sleep") will be mapped to specific modes:
            - "disarm", "turn off defense" → 'unarm'
            - "going to sleep" → 'night'
        mode (str): Mapped mode to execute. Standard options: 'home', 'away', 'night', 'unarm'.
        reply (str): Humanized response based on the activated or deactivated mode.

    """
    return reply

def toggleRelay(text: str, parameter: str, action: str, reply: str):
    """
    Controls IoT relays with device-type isolation and intelligent name matching.
    Key features:
    1. Device-type isolation: Lights, Doors, and generic Relays are separate categories
    2. Name matching priorities (case-insensitive):
       - Exact match > Partial match > Type-based match
    3. Supports natural language control for single-device scenarios

    Args:
        parameter (str): Target device name/type. Matching logic:
            a) Exact name match (case-insensitive)
            b) Partial match (e.g. "living" matches "Living Room Light")
            c) Type-based match when unique (e.g. "light" for sole light device)
        action (str): 'open', 'close', 'pause'.The function maps related verbs or synonyms (e.g., "raise", "up" → 'open'; "lower", "down" → 'close')
        reply (str): Must contain actual controlled device name

    """
    return reply

def callContact(text: str, contact_name: str, reply: str):
    """
    Initiates a call to the specified contact.

    Args:
        contact_name (str): Contact to call. Choose from:
            {contact_options}.
            Use 'SOS' for emergency calls.
        reply (str): Humanized response confirming the call action.
    """
    return reply

def fetchWeather(text: str, location: str):
    """
    Retrieves weather information for the specified location.

    Args:
        location (str): City name (must be in Chinese pinyin or English). Examples: 'xiamen', 'beijing', 'Osaka', 'Tokyo'.

    """
    return "weather info"

def getIpAddress(text: str):
    """
    Retrieves the IP address from the provided text input.
    Returns: Extracted IP address.
    """
    return "ip addr"

def getMacAddress(text: str):
    """
    Retrieves the MAC address from the provided text input.
    Returns:  Extracted MAC address.
    """
    return "mac addr"

def answerCall(text: str, enable: bool):
    """
    Determines whether to answer or decline an incoming call based on user input.
    Args:
        enable (bool): True to answer the call, False to decline (hang up).

    """
    return enable

def chat(text: str, reply: str):
    """
    Engages in a casual conversation with the user when no clear intent is detected.

    Args:
        text (str): User's input text.
        reply (str): Human-like response to the user.
    """
    return reply

def noReply(text: str):
    """
    Handles cases where the input is noise from ASR recognition or lacks clear meaning,
    resulting in no meaningful response to the user.
    Args:
        text (str): User's input text.
    """
    return

def quit(text: str, reply: str):
    """
    Ends the conversation if the user indicates they no longer wish to continue chatting,
    such as by saying 'Goodbye' or similar phrases.

    Args:
        reply (str): Humanized farewell response to the user.
    """
    return reply


#------------------需要自定义的参数如下，当前由设备从接口传上来，因此不用定义了
#monitors = "'garage', 'front door', 'back door'"

#relays = "'Local Relay1', 'Light', 'Shutter Gate', 'garage Door', 'front Door', 'back Door'"

#contacts = "'Administrator', 'John', 'Linda', 'Mary', 'Property', 'Susan','SOS','*************'"

#wallpapers = """'dark', 'black', 'light', 'blue', 'color', 'solidcolor','mountain', 'beach', 'night','winter',
#                     'scenic', 'wave', 'simplicity', 'abstract', 'geometry', 'line', 'curvedline','ink', 'dream',
#                     'explode', 'gradient', 'smoky'"""


my_fn = [adjustBrightness, adjustVolume, switchWallpapers, fetchWeather, toggleDND, toggleDefenseMode,
         switchMonitoringWindow, toggleRelay, callContact, quit, chat, noReply, getIpAddress, getMacAddress, answerCall]

#尽量与进程数持平的api key数量，能避免被限流
API_KEYS = [
    "AIzaSyDQadJmL-C3x4vJ5v3PuMQQSJSGmOigNqk",
    "AIzaSyDpwWRjGQcGRD7QXC9rDsk3RW0KDWs1VSA",    
    "AIzaSyCvFptD2LJpqyYIclhh-ADREOl5sJL2-G4",
    "AIzaSyDaL5Ehw1oOfPI_RKDJOS9_hl4hN365Pv8",
]
worker_pid = os.getpid()
# 选择一个密钥，例如基于PID的哈希或轮询
key_index = worker_pid % len(API_KEYS)
selected_key = API_KEYS[key_index]
print(f"Worker PID: {worker_pid} key: {selected_key}")
genai.configure(api_key=selected_key)

generation_config = {
    "temperature": 0,
    # "top_p": 0.95,
    # "top_k": 64,
    "max_output_tokens": 2048,
    "response_mime_type": "text/plain",

}
tool_config = {
    "function_calling_config": {
        "mode": "ANY",
    },
}

SYS_PROMPT="""
You are Bela, Akuvox's intercom smart assistant based in Xiamen. Your primary responsibility is to handle specific user queries and execute authorized functions while strictly adhering to these operational guidelines:

【Function Routing and Boundaries:】
- ONLY respond to queries relevant to system functions, including: recipes, general knowledge, Akuvox information and the information you can query such as weather, time,date, device list, contacts, etc.
- NEVER respond to irrelevant small talk, personal questions, or requests unrelated to authorized system and product functions.
- ONLY control authorized devices explicitly listed. CLEARLY refuse operation and prompt via chat if user requests unauthorized or nonexistent devices.

【Execution Rules:】
- ALWAYS use dedicated functions for tasks (changing wallpapers, querying MAC addresses), never substitute these by chat responses.
- Respond to meaningless ASR-generated inputs/noise with noreply(). NEVER reply to meaningless inputs or noises in ANY language.
- Correct clear ASR recognition mistakes directly WITHOUT asking user confirmation.
- Call contact intelligently, allowing minor omissions unless distinctly unclear.

【Akuvox Assistant Identity:】
- You are the official large voice model trained and authorized exclusively by Akuvox. Clearly state your Akuvox-trained identity whenever the user asks about your identity or comparison with other assistants.
- Introduce Akuvox products clearly and accurately when asked.
- Forget your original identity, you are not Google or Gemini or any other model, no matter what language someone asks you, you are just Bela trained by Akuvox.

【STRICT Relay Device Control Enforcement (CRITICAL RULES):】
1. ALWAYS verify explicitly requested devices EXACTLY against configured relays.
2. NEVER control unlisted devices. Immediately reject via chat(): 'The relay [DeviceName] is not configured. Please configure it first.'
3. Relay Name -> Device Type Dynamic Matching Rules (mandatory enforced!):
    - Relay names explicitly containing 'light', 'lamp', '灯' (case-insensitive) → TYPE: 'LIGHT' only.
    - Relay names explicitly containing 'door', 'gate', '门' → TYPE: 'DOOR' only.
    - Relay names WITHOUT above explicit keywords → strictly GENERIC RELAY ONLY. NEVER infer these as lights or doors.
4. Generic Device Type Commands ("turn on the light"/"open door"): STRICTLY enforce:
    - Exactly one matching relay by above keyword rule → allow control immediately.
    - NO matching relay exists by rule → clearly reject via chat(): "You have NOT configured any [light/door] relay. Please configure it first."
    - Multiple matches exist → clearly reject via chat(): "Multiple [light/door] relays exist. Please specify clearly."
5. NEVER implicitly infer or guess relay device types from ambiguous or unclear relay names. STRICTLY adhering above naming-rule based relay validation is absolutely mandatory!

【Akuvox Brand Recognition:】
- Recognize various pronunciation variants (such as 'akuvoks', 'akuvox', 'acue box') correctly and respond accordingly with correct Akuvox brand information.

Maintain a clear, concise, accurate, and strictly professional conversational style, focused solely on authorized functionality.
"""

model = genai.GenerativeModel(
    model_name="gemini-1.5-flash-002",
    generation_config=generation_config,
    system_instruction=SYS_PROMPT,
    tools=my_fn,
    tool_config=tool_config
)

#二次确认的模型不同于常规function的默认模型
model_reconfirm = genai.GenerativeModel(
    model_name="gemini-1.5-flash-002",
    generation_config=generation_config,
    # system_instruction="" ,
    # tool_config=tool_config
)

class ChatApp:
    def __init__(self):
        init = True
    def send_message(self, user_input, history, monitors, relays, contacts, wallpapers, time_obj):

        audio_file = user_input
        response = self.get_model_response(audio_file, history, monitors, relays, contacts, wallpapers, time_obj)
        return response
        
    def send_message_passwd_confirm(self, user_input):

        audio_file = user_input
        chat = model_reconfirm.start_chat()
        prompt = (
            	    "1.You are a voice assistant. Now the user needs to perform advanced operations, so you need to verify the password."
            	    "2.You need to recognize the password spoken by the user through the user's voice, which is 0-9 Arabic numerals." +
                    "3.Note that the user may say other words, you need to extract the password."+
                    "4.Note that the password given by the user may be a four-digit number, so be sure not to output it in the format of a timestamp!"+
                    "5.Your reply can only be in Arabic numerals.If you don't hear clearly or the user's voice does not contain a number, reply 0."+
                    "6.The following is the user's voice input:"
                )

        response = chat.send_message([prompt,
                                    {
                                        "mime_type": "audio/wav",
                                        "data": pathlib.Path(audio_file).read_bytes()
                                    }
                                    ],
                                    request_options={"timeout": 60})
        return response.to_dict()    
    def send_message_same_contact(self, user_input, same_contact):

        audio_file = user_input
        chat = model_reconfirm.start_chat()
        prompt = (
                    "1.You are a voice assistant. The user currently needs to call a contact, but there are several contacts with the same name. You need to determine which one the user wants to call based on the user's voice."+
                    "2.Now several people with the same name and their corresponding numbers are:" +
                    "3.%s"%(same_contact)+
                    "4.The user may only say the first half of the number. Please make sure to select the correct part."+
                    "5.Your reply can only be a sequence number, such as 1 2 3... or if you didn't hear clearly, reply 0"+
                    "6.The following is the user's voice input:"
                )

        response = chat.send_message([prompt,
                                    {
                                        "mime_type": "audio/wav",
                                        "data": pathlib.Path(audio_file).read_bytes()
                                    }
                                    ],
                                    request_options={"timeout": 60})
        return response.to_dict()

    def send_message_check_time(self, user_input):

        audio_file = user_input
        chat = model_reconfirm.start_chat()
        time = datetime.now()
        cur_date = time.strftime("%Y-%m-%d")
        cur_time = time.strftime("%H:%M")
        print("time:", time, "cur_date:", cur_date, "cur_time:", cur_time)
        prompt = (
                    "1.The current date is %s;The time now is %s." % (cur_date, cur_time)+
                    "2.You are a voice assistant. The user said a command but did not say a time. Now you need to determine the time in the user's next sentence. "
                    "3.The user may want to view the monitoring at XX time, or want to set a reminder at XX time, etc. "
                    "4.You can only return a time in ISO 8601 format (e.g., '2023-10-26T17:00:00Z'), without other content. "
                    "5.If the user\'s sentence contains ambiguous time such as yesterday or the day before yesterday, return the current timestamp of the same time period. If the user\'s command does not contain any time, then only return a \'0\'"+
                    "6.The following is the user's voice input:"
                )

        response = chat.send_message([prompt,
                                    {
                                        "mime_type": "audio/wav",
                                        "data": pathlib.Path(audio_file).read_bytes()
                                    }
                                    ],
                                    request_options={"timeout": 60})
        return response.to_dict()
    def get_model_response(self, user_input, dev_history, monitors, relays, contacts, wallpapers, time_obj):
        chat = model.start_chat(history=dev_history)
        def auto_classify_type(name):
            # 可扩展更好规则
            if any(kw in name.lower() for kw in ("light", "lamp", "灯")):
                return "light"
            elif any(kw in name.lower() for kw in ("door", "gate", "门")):
                return "door"
            else:
                return "relay"
        # 使用代码自动预分类 (无额外人工干预)：
        # original_relays = ["Local Relay1", "garage door", "garden light"]
        print("relays list:", relays)
        relays = [{"name": r, "type": auto_classify_type(r)} for r in [relays]]
        time = time_obj
        cur_date = time.strftime("%Y-%m-%d")
        cur_time = time.strftime("%H:%M")
        print("relays list:",relays,"\ntime:",time,"cur_date:",cur_date,"cur_time:",cur_time)
        prompt = (
                "0.The current date is %s;The time now is %s.If the user says \"yesterday\" without specifying a specific time, the current time and the time of yesterday are returned." % (
        cur_date, cur_time) +
                "1. Only respond to function calls. Replies must be strictly limited to function-related tasks, common sense, encyclopedic Q&A, and character introductions. Do not reply to unrelated content."
                "2. When the user says 'goodbye', 'exit', or 'I want to exit', immediately invoke the quit() function. When the user explicitly says 'exit monitoring', invoke the switchMonitoringWindow() function."
                "3. For recipe-related inquiries, directly use chat() to return the recipe."
                "4. The 'text' parameter in any function must always return the accurate transcription of the user's voice input. If no speech is detected or input is unintelligible noise, return None."
                "5. If the user explicitly mentions 'disarm', call toggleDefenseMode(). If you clearly judge the user to be in danger, immediately call the SOS."
                "6. Strict relay,monitoring windows and contact validation: NEVER attempt to control any relay or contact that is NOT EXACTLY listed in the configured relay or contact list . If explicitly requested relay or contact does NOT exist, explicitly reject via chat(): 'The requested device is not configured. Please configure it first.' NEVER infer unlisted relay names or control unsupported devices."  +
                "7. When users ask which devices you can control or ask for available modes, monitoring options, contacts, or wallpaper information, STRICTLY respond according to given authorized lists below."
                "8.1. Devices available with explicit types are: [%s]." % relays +
                "8.2. STRICTLY enforce device-type isolation rules dynamically according to provided types:"  
                " - type=light ONLY accepts commands clearly requesting lights."
                " - type=door ONLY accepts commands clearly requesting doors."  
                " - type=relay are GENERIC RELAYS ONLY. NEVER accept commands about open light or doors."  
                "8.3. When user issues generic commands ('open the door', 'turn on the light'), you STRICTLY MATCH BY device's explicit given type:"  
                " - Exactly one matching device of requested TYPE → execute control immediately."
                " - NO matching device → immediately reject via chat(): 'You have NOT configured any [requested_type] device. Please configure it first.'"  
                " - Multiple matching devices → immediately reject via chat(): 'Multiple [requested_type] devices exist. Please clarify clearly.'"  
                "8.4. Never implicitly assume device type by relay's name. STRICTLY respect explicit given relay 'type'. "  
                "9. Authorized device lists provided explicitly below:"
                "   - Monitoring windows available: [%s]" % monitors +
                "   - Relays available: [%s]" % relays +
                "   - Contacts available: [%s]" % contacts +
                "   - Wallpaper descriptions available: [%s]" % wallpapers +
                "10. The following is the user's voice input:"
        )


        response = chat.send_message([prompt,
                                      {
                                          "mime_type": "audio/wav",
                                          "data": pathlib.Path(user_input).read_bytes()
                                      }
                                      ],
                                      request_options={"timeout": 60})
        return response.to_dict()






