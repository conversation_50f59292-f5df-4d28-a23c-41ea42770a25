from fastapi.responses import JSONResponse

# API调用次数限制
LIMIT_EXCEEDED_ERROR_CODE = 1000500001

def log_and_return_json(err_code, message, datas=None):
    if datas is None:
        json_res = {
            "err_code": err_code,
            "message": message
        }
    else:
        json_res = {
            "err_code": err_code,
            "message": message,
            "datas": datas
        }
    print(f"json response: {json_res}")
    return JSONResponse(content=json_res)

def response_token_error():
    return log_and_return_json(-1, "token error")
def response_api_call_limit_exceeded():
    return log_and_return_json(LIMIT_EXCEEDED_ERROR_CODE, "api call limit exceeded")
def response_success(datas):
    return log_and_return_json(0, "success", datas)
def response_error(message):
    return log_and_return_json(-1, message)