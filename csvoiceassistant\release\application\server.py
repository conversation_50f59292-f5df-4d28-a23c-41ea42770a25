import ak_gemini
import ak_auth
import json
from fastapi import FastAPI, File, UploadFile, Form, Header
from fastapi.responses import JSONResponse
import os,shutil,re
from pydantic import BaseModel
from typing import Optional
from typing import Annotated
import numpy as np
import time
from datetime import datetime
import ak_database
import ak_response


app = FastAPI()
gemini = ak_gemini.ChatApp()
worker_pid = os.getpid()

def should_count_call(response_dict):
    """
    只在 function_call.name 不是 'noReply' 且 args.reply 有内容时返回 True
    """
    try:
        candidates = response_dict.get('candidates', [])
        if candidates:
            parts = candidates[0].get('content', {}).get('parts', [])
            if parts:
                function_call = parts[0].get('function_call', {})
                name = function_call.get('name')
                args = function_call.get('args', {})
                reply = args.get('reply')
                if name != 'noReply' and reply:
                    return True
    except Exception:
        pass
    return False
# 接收上传的音频文件并返回识别结果
@app.post("/gemini/chat/function")
async def recognize_audio_file(voice: UploadFile = File(...),
history: str = Form("[]"),
monitors: str = Form(""),
relays: str = Form(""),
contacts: str = Form(""),
wallpapers: str = Form(""),
device_time: str = Form(""),
x_auth_token: str = Header(""),
x_auth_user: str = Header("")):

    now_time = datetime.now()
    print(f"\n[{now_time}] Worker PID: {worker_pid}")
    print(f"voice.filename: {voice.filename}")
    print(f"history: {history}")
    print(f"monitors: {monitors}")
    print(f"relays: {relays}")
    print(f"contacts: {contacts}")
    print(f"wallpapers: {wallpapers}")
    print(f"device_time: {device_time}")
    print(f"x_auth_token: {x_auth_token}")
    print(f"x_auth_user: {x_auth_user}")

    #如果voice.filename为stop.wav,直接返回
    if voice.filename == "stop.wav":
        return ak_response.response_success("")

    # 判断字符串是否为空
    if not device_time:  # 如果字符串为空
        time_obj = now_time  # 使用当前时间
    else:
    # 将字符串解析为 datetime 对象
        time_obj = datetime.strptime(device_time, "%Y-%m-%d %H:%M:%S")

    #鉴权
    auth_result = ak_auth.auth_device_token(x_auth_user, x_auth_token)
    if auth_result == False:
        return ak_response.response_token_error()
    # 检查API调用次数
    exceeded, requester = ak_database.get_api_call_stats(x_auth_user)
    if exceeded:
        return ak_response.response_api_call_limit_exceeded()

    # 存储临时文件并运行推理
    temp_filename = x_auth_user + "_" + str(time.time()) + ".wav"
    with open(temp_filename, "wb") as buffer:
        shutil.copyfileobj(voice.file, buffer)
    t1 = time.time()
    dev_history = json.loads(history)
    res = gemini.send_message(temp_filename, dev_history, monitors, relays, contacts, wallpapers, time_obj)  
    t2 = time.time()
    print(t2-t1)
    os.remove(temp_filename)  # 删除临时文件

    # 记录调用次数（仅当满足条件时）
    if should_count_call(res):
        ak_database.update_api_call_stats(requester)
    return ak_response.response_success(res)


@app.post("/gemini/chat/passwd_confirm")
async def recognize_audio_file(voice: UploadFile = File(...),
x_auth_token: str = Header(None),
x_auth_user: str = Header(None)):

    now_time = datetime.now()
    print(f"\n[{now_time}] Worker PID: {worker_pid}")
    print(f"voice.filename: {voice.filename}")
    print(f"x_auth_token: {x_auth_token}")
    print(f"x_auth_user: {x_auth_user}")

    #鉴权
    auth_result = ak_auth.auth_device_token(x_auth_user, x_auth_token)
    if auth_result == False:
        return ak_response.response_token_error()
    # 检查API调用次数
    exceeded, requester = ak_database.get_api_call_stats(x_auth_user)
    if exceeded:
        return ak_response.response_api_call_limit_exceeded()

    # 存储临时文件并运行推理
    temp_filename = x_auth_user + "_" + str(time.time()) + ".wav"
    with open(temp_filename, "wb") as buffer:
        shutil.copyfileobj(voice.file, buffer)
    t1 = time.time()
    res = gemini.send_message_passwd_confirm(temp_filename)  
    t2 = time.time()
    print(t2-t1)
    os.remove(temp_filename)  # 删除临时文件

    # 记录调用次数
    ak_database.update_api_call_stats(requester)
    return ak_response.response_success(res)       



@app.post("/gemini/chat/same_contact")
async def recognize_audio_file(voice: UploadFile = File(...),
x_auth_token: str = Header(None),
x_auth_user: str = Header(None),
same_contact: str = Form("1.Ella 12305821 2.Ella 12546289 3.Ella 12589325")):

    now_time = datetime.now()
    print(f"\n[{now_time}] Worker PID: {worker_pid}")
    print(f"voice.filename: {voice.filename}")
    print(f"same_contact: {same_contact}")
    print(f"x_auth_token: {x_auth_token}")
    print(f"x_auth_user: {x_auth_user}")

    #鉴权
    auth_result = ak_auth.auth_device_token(x_auth_user, x_auth_token)
    if auth_result == False:
        return ak_response.response_token_error()
    # 检查API调用次数
    exceeded, requester = ak_database.get_api_call_stats(x_auth_user)
    if exceeded:
        return ak_response.response_api_call_limit_exceeded()

    # 存储临时文件并运行推理
    temp_filename = x_auth_user + "_" + str(time.time()) + ".wav"
    with open(temp_filename, "wb") as buffer:
        shutil.copyfileobj(voice.file, buffer)
    t1 = time.time()
    res = gemini.send_message_same_contact(temp_filename)
    t2 = time.time()
    print(t2-t1)
    os.remove(temp_filename)  # 删除临时文件

    # 记录调用次数
    ak_database.update_api_call_stats(requester)
    return ak_response.response_success(res)

@app.get("/check_health")
async def check_health():

    now_time = datetime.now()
    print(f"\n[{now_time}] Worker PID: {worker_pid}")
    json_res = {
        "err_code": 0,
        "message": "success"
    }
    return json_res
