#!/bin/sh

APP_LOG_DIR=/var/log/csvoiceassistant
APP_LOG_PATH=${APP_LOG_DIR}/csvoiceassistant.log
sed -i "s/RESID_SERVER_ADDR/${RESID_SERVER_ADDR}/g" /usr/local/akcs/csvoiceassistant/application/ak_auth.py
if [ "$ENABLE_AKCS_DBPROXY" = "1" ]; then
    sed -i "
        s/MYSQL_SERVER_PORT/3308/g
        s/MYSQL_SERVER_ADDR/${AKCS_DBPROXY_INNER_IP}/g" /usr/local/akcs/csvoiceassistant/application/ak_database.py
else
    sed -i "
        s/MYSQL_SERVER_PORT/3306/g
        s/MYSQL_SERVER_ADDR/${AKCS_MYSQL_INNER_IP}/g" /usr/local/akcs/csvoiceassistant/application/ak_database.py
fi

mkdir -p $APP_LOG_DIR
uvicorn server:app --workers 4 --host 0.0.0.0 --port 8590 > ${APP_LOG_PATH} 2>&1


