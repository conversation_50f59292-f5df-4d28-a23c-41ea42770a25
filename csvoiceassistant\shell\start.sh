set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3

ENV=$4
HOST=$5
INNER_IP=$6
NICKNAME=$7
VENDOR=$8                  #云产商
HOSTNAME=$9
IPV6=${10}
LINE=${11}
DOCKER_IMG=${12}
CONTAINER_NAME=csvoiceassistant
PKG_ROOT=$RSYNC_PATH
IP_FILE=/etc/ip
KDC_CONF=/etc/kdc.conf
APP_BACKEND_CONF=$PKG_ROOT/app_backend_install.conf

grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

ENV_CONF_PARAM="
-e RESID_SERVER_ADDR=$(grep_conf 'REDIS_INNER_IP' $APP_BACKEND_CONF)
-e ENABLE_AKCS_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $APP_BACKEND_CONF)
-e AKCS_DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $APP_BACKEND_CONF)
-e AKCS_MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $APP_BACKEND_CONF)
"

ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $APP_BACKEND_CONF)
SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)

# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------

if [ `docker ps -a | grep $CONTAINER_NAME | wc -l` -gt 0 ];then
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;

    # 提取镜像名称
    image_name=$(echo "$DOCKER_IMG" | awk '{print $1}' | cut -d ':' -f 1)

    # 检查名称中是否包含关键字 csvoiceassistant
    if [[ "$image_name" == *csvoiceassistant* ]]; then
        docker images | grep "$image_name" | awk '{print $3}' | xargs docker rmi || true
    else
        #避免CD传错参数 导致误删镜像
        echo "csvoiceassistant not found in DOCKER_IMG."
    fi
fi

docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} --restart=always --net=host -v /var/log:/var/log -v /etc/ip:/etc/ip --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/sh /usr/local/akcs/csvoiceassistant/scripts/csvoiceassistant_run.sh

#注册etcd
export ETCDCTL_API=3
SERVICE_ADDR="$SERVER_INNER_IP:8590"
ret=$(/usr/local/bin/php /bin/etcd_cli.php /bin/etcdctl "$ETCD_INNER_IP" put akcs/voice_assistant_server/inner/${SERVER_INNER_IP} "$SERVICE_ADDR")
if [[ $ret != *ok!* ]]; then
    echo "plz check etcd server is ok."
    exit 1
fi


