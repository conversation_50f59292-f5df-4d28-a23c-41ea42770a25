CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (csvrecord C CXX)

SET(CSBASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
SET(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
LINK_DIRECTORIES(${CSBASE_DIR} ${CSBASE_DIR}/thirdlib ${CSBASE_DIR}/evpp/lib ${CSBASE_DIR}/thirdlib/ffmpeg/lib ${CSBASE_DIR}/thirdlib/turbojpeg/lib)

AUX_SOURCE_DIRECTORY(${SRC_DIR} SRC_LIST_VRECORD)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Lock SRC_LIST_VRECORD_LOCK)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Utility SRC_LIST_VRECORD_UTILITY)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Event SRC_LIST_VRECORD_EVENT)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Logic SRC_LIST_VRECORD_LOGIC)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Main SRC_LIST_VRECORD_MAIN)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/../../csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model SRC_LIST_VRECORD_MODEL)


ADD_DEFINITIONS(-g -Wall -gstabs+ -Werror -Wno-unused-parameter -Wno-deprecated -Wno-error=attributes)

# C++特定的编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")

# C特定的编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")

include_directories(${INC_DIR} ${SRC_DIR}/include 
${SRC_DIR}/Common/Utility ${SRC_DIR}/Common/Lock
${SRC_DIR}/Common/Event ${SRC_DIR}/Logic
${SRC_DIR}/Model ${CSBASE_DIR}
${CSBASE_DIR}/curl ${CSBASE_DIR}/thirdlib/ffmpeg/include 
${CSBASE_DIR}/thirdlib/turbojpeg/include
/usr/local/boost/include /usr/local/grpc/include /
usr/local/protobuf/include ${SRC_DIR}/../../csbase/encrypt)

add_executable(csvrecord ${SRC_LIST_VRECORD} 
${SRC_LIST_VRECORD_LOCK} 
${SRC_LIST_VRECORD_UTILITY} 
${SRC_LIST_VRECORD_EVENT} 
${SRC_LIST_VRECORD_LOGIC} 
${SRC_LIST_VRECORD_MAIN} 
${SRC_LIST_VRECORD_MODEL}
${SRC_LIST_BASE_ENCRYPT}
)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

set_target_properties(csvrecord PROPERTIES LINK_FLAGS  "-Wl,--rpath=/usr/local/akcs/csvrecord/lib")

target_link_libraries(csvrecord libavformat.a libavdevice.a libavcodec.a libavfilter.a libavutil.a libswresample.a libswscale.a -ljpeg -lturbojpeg -lpthread -lmysqlclient -levpp -lglog libcsbase.a libssl.so libcrypto.so)
