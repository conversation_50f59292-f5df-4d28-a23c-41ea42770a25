/** @file ipc.h
 * @brief ipc module interface
 * @date 2012-12-14
 * @note
 *
 *
 * Copyright(c) 2012-2020 Xiamen Ringslink telenology Co,.Ltd
 */

#ifndef __IPC_H__
#define __IPC_H__


#ifndef TRUE
#define TRUE    1
#endif

#ifndef FALSE
#define FALSE   0
#endif

#ifndef RL_TRUE
#define RL_TRUE 1
#endif

#ifndef RL_FALSE
#define RL_FALSE    0
#endif

#ifndef BOOL
typedef int                 BOOL;
#endif


#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    int len;
    int id;
    int from;  /*信息发端进程的ID*/
    int param1;
    int param2;

#define IPC_MSG_HEADER_SIZE 20
#define IPC_MSG_DATA_SIZE   2048
#define IPC_MSG_MAX_SIZE    (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)

    unsigned char data[IPC_MSG_DATA_SIZE];
} UNIX_IPC_MSG;

void ipc_show_version();

/**
 * initialize IPC module
 */
void ipc_init();


/**
 * register a process to IPC manager
 *
 * @param[in] my_id -- process ID
 *
 * @return 0 on success, others on failed.
 */
int ipc_register(int my_id);

/**
 * thread function
 *
 * @param[in] listen_fd -- socket listen ID
 *
 * @return 0 on success, others on failed.
 */
void* read_thread(int listen_fd);


typedef void (*IPC_MSG_HANDLE)(UNIX_IPC_MSG* msg, void* data);


/**
 * run a process to IPC listen
 *
 * @param[in] msg_handle -- message process handle
 * @param[in] data -- private data
 *
 * @return 0 on success, others on failed.
 */
int ipc_run(IPC_MSG_HANDLE msg_handle, void* data);

/**
 * unregister a process to IPC manager
 *
 * @param[in] id -- process ID name
 *
 * @return 0 on success, others on failed.
 */
int ipc_unregister();


/**
 * send a ipc message
 *
 * @param[in] dest_id -- destination process ID
 * @param[in] id -- ipc message id
 * @param[in]param1 – first param  for ipc message
 * @param[in]param2 – second param for ipc message
 * @param[in]dat – extern data ptr
 * @param[in]dat_len – extern data size
 *
 * @return 0 on success, others on failed.
 */
int ipc_send(int dest_id, int id, int param1, int param2, void* dat, int dat_len);

/**
 * get the quit flag to see if we need to quit the process
 *
 * @param[in] none
 *
 * @return the quitflag. RL_TRUE if needs to quit. RL_FALSE if not.
 */
BOOL get_quitflag();

/**
 * set the quit flag. RL_TRUE means quit; RL_FALSE means not
 *
 * @param[in] quit flag
 *
 * @return none.
 */
void set_quitflag(BOOL quitflag);

#ifdef __cplusplus
}
#endif

#endif

