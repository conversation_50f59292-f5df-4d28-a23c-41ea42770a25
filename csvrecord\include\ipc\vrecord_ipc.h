#ifndef __VRECORD_IPC_H__
#define __VRECORD_IPC_H__

/*RECORD to RTSP*/
//#define MSG_VRECORD2VRTSP     0xF00000
#define MSG_VRECORD2VRTSP       0xA300


/*RTSP to RECORD*/
//#define MSG_VRTSP2VRECORD     0xF10000
#define MSG_VRTSP2VRECORD       0xA200



/*common macros*/

#ifndef IP_SIZE
#define IP_SIZE                         16
#endif

#ifndef MAC_SIZE
#define MAC_SIZE                        16
#endif

#ifndef VALUE_SIZE
#define VALUE_SIZE                      256
#endif

#ifndef URL_SIZE
#define URL_SIZE                        512
#endif


#ifndef INT_SIZE
#define INT_SIZE                        12
#endif

#ifndef TRUE
#define TRUE    1
#endif

#ifndef FALSE
#define FALSE   0
#endif

#ifndef RL_TRUE
#define RL_TRUE 1
#endif

#ifndef RL_FALSE
#define RL_FALSE    0
#endif

#ifndef BOOL
typedef int                 BOOL;
#endif

#ifndef USER_SIZE
#define USER_SIZE                       32
#endif

#define DEVICE_FWVER_SIZE               16

typedef struct MESSAGE_T
{
    unsigned int id;
    unsigned long wParam;
    unsigned long lParam;
    void* lpData;
    struct MESSAGE_T* next;
} MESSAGE;

enum
{
    IPC_ID_MIN = 0,
    IPC_ID_CLOUD,
    IPC_ID_VRTPSD,
    IPC_ID_VRECORD,
    IPC_ID_MAX,
};

typedef struct SOCKET_MSG_CAPTURE_RTSP_T
{
#define RTSP_DATA_MAC_SIZE  16
#define PIC_NAME_SIZE       256
#define USER_SIZE           32
    char szMac[RTSP_DATA_MAC_SIZE];
    char szPicName[PIC_NAME_SIZE]; //截图的图片名称
    char szNode[USER_SIZE];
    char szUserName[USER_SIZE];//谁截的图
    char flow_uuid[64];
} SOCKET_MSG_CAPTURE_RTSP;

typedef struct SOCKET_MSG_VIDEO_DATA_T
{
#define VIDEO_DATA_MAC_SIZE 16
#define PIC_NAME_SIZE       256
#define VIDEO_DATA_SIZE     1500
    char szMac[VIDEO_DATA_MAC_SIZE];
    char szPicName[PIC_NAME_SIZE];
    unsigned int nDataLen;
    unsigned char szData[VIDEO_DATA_SIZE];
    char flow_uuid[64];
} SOCKET_MSG_VIDEO_DATA;

enum
{

    /** post when vrecord received capture
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param lpData -- full path of local
      *
     */
    MSG_VRECORD2VRTSP_STOP_CAPTURE = MSG_VRECORD2VRTSP + 1,

};

enum
{
    /** post when vrtsp send capture
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param lpData -- capture data
      *
     */
    MSG_VRTSP2VRECORD_SEND_CAPTURE_DATA = MSG_VRTSP2VRECORD + 1,

    /** post when vrtsp start capture
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param lpData -- capture param
      *
     */

    MSG_VRTSP2VRECORD_START_CAPTURE,

    /** post when vrtsp start record
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param lpData -- capture param
      *
     */
    MSG_VRTSP2VRECORD_START_RECORD,



};



#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    int len;
    int id;
    int from;  /*信息发端进程的ID*/
    int param1;
    int param2;

#define IPC_MSG_HEADER_SIZE 20
#define IPC_MSG_DATA_SIZE   2048
#define IPC_MSG_MAX_SIZE    (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)
    unsigned char data[IPC_MSG_DATA_SIZE];
} UNIX_IPC_MSG;

typedef struct
{
    int len;
    int id;
    int from;  /*信息发端进程的ID*/
    int param1;
    int param2;

#define IPC_MSG_HEADER_SIZE     20
#define IPC_MSG_DATA_SIZE       2048
#define IPC_MSG_MAX_SIZE        (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)

    unsigned char data[IPC_MSG_DATA_SIZE];
} IPC_MSG;


void ipc_show_version();

/**
 * initialize IPC module
 */
void ipc_init();

/**
 * register a process to IPC manager
 *
 * @param[in] my_id -- process ID
 *
 * @return 0 on success, others on failed.
 */
int ipc_register(int my_id);

/**
 * thread function
 *
 * @param[in] listen_fd -- socket listen ID
 *
 * @return 0 on success, others on failed.
 */
void* read_thread(int *listen_fd);

typedef void (*IPC_MSG_HANDLE)(UNIX_IPC_MSG* msg, void* data);

/**
 * run a process to IPC listen
 *
 * @param[in] msg_handle -- message process handle
 * @param[in] data -- private data
 *
 * @return 0 on success, others on failed.
 */
int ipc_run(IPC_MSG_HANDLE msg_handle, void* data);

/**
 * unregister a process to IPC manager
 *
 * @param[in] id -- process ID name
 *
 * @return 0 on success, others on failed.
 */
int ipc_unregister();

/**
 * send a ipc message
 *
 * @param[in] dest_id -- destination process ID
 * @param[in] id -- ipc message id
 * @param[in]param1 – first param  for ipc message
 * @param[in]param2 – second param for ipc message
 * @param[in]dat – extern data ptr
 * @param[in]dat_len – extern data size
 *
 * @return 0 on success, others on failed.
 */
int ipc_send(int dest_id, int id, int param1, int param2, void* dat, int dat_len);

/**
 * get the quit flag to see if we need to quit the process
 *
 * @param[in] none
 *
 * @return the quitflag. RL_TRUE if needs to quit. RL_FALSE if not.
 */
BOOL get_quitflag();

/**
 * set the quit flag. RL_TRUE means quit; RL_FALSE means not
 *
 * @param[in] quit flag
 *
 * @return none.
 */
void set_quitflag(BOOL quitflag);

#ifdef __cplusplus
}
#endif

#endif //#ifndef __VRTSP_IPC_H__

