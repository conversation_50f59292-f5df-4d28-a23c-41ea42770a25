#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME=csvrecord

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csvrecord   # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csvrecord
LOG_PATH=/var/log/csvrecordlog
CTRL_SCRIPT=csvrecordctl.sh
RUN_SCRIPT=csvrecordrun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------

echo "Begin to install $APP_NAME."

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "创建存放ftp的文件夹 /usr/local/akcs/csstorage/ftp/data"
mkdir -p /usr/local/akcs/csstorage/ftp/data

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi


ENV_LOAD_PARAM="
-v /usr/share/zoneinfo:/usr/share/zoneinfo
-v /var/log/csvrecordlog:/var/log/csvrecordlog
-v /var/core:/var/core
-v /etc/ip:/etc/ip
-v /etc/kdc.conf:/etc/kdc.conf 
-v /bin/crypto:/bin/crypto
-v /usr/local/akcs/csstorage/ftp:/usr/local/akcs/csstorage/ftp
-v /etc/oss_install.conf:/etc/oss_install.conf
-v /var/csvrecord_sock:/var/csvrecord_sock
"

echo ${ENV_LOAD_PARAM};

if [ $(docker ps -a --filter "name=^/${CONTAINER_NAME}$" -q | wc -l) -gt 0 ]; then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 csvrecord"
    app_pids=$(pidof csvrecord || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi

    sed -i '/csvrecord_run.sh/d' /etc/init.d/rc.local
fi

docker run -d -e TZ=Asia/Shanghai ${ENV_LOAD_PARAM} --restart=always --net=host --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csvrecord/scripts/csvrecordrun.sh ${CONTAINER_NAME}