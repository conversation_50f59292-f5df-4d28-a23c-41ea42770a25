#!/bin/bash
ACMD="$1"
CSVRECORD_BIN='/usr/local/akcs/csvrecord/bin/csvrecord'
PROCESS_PID_FILE=/var/run/csvrecord.pid

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csvrecord()
{
    nohup $CSVRECORD_BIN >/dev/null 2>&1 &
    echo "Start csvrecord successful"
}
stop_csvrecord()
{
    echo "Begin to stop csvrecord"
    kill -9 `pidof csvrecord`
    sleep 2
    echo "Stop csvrecord successful"
}

case $ACMD in
  start)
    if [ -f $PROCESS_PID_FILE ];then
        pid=`cat $PROCESS_PID_FILE`
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
	count=`ls /proc/$pid | wc -l`
	if [ $count -eq 0 ]
    then
        start_csvrecord
    else
        echo "csvrecord is already running"
    fi
    ;;
  stop)
    if [ -f $PROCESS_PID_FILE ];then
        pid=`cat $PROCESS_PID_FILE`
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
	count=`ls /proc/$pid | wc -l`
	if [ $count -eq 0 ]
    then
        echo "csvrecord is already stopping"
    else
        stop_csvrecord
    fi
    ;;
  restart)
    stop_csvrecord
    sleep 1
    start_csvrecord
    ;;
  status)
    cnt=`ss -alnp | grep csvrecord | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csvrecord is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csvrecord is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

