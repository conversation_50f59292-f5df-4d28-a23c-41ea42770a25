#include "WaitEvent.h"

#if defined(_WIN32)

CWaitEvent::CWaitEvent()
{
    m_event = CreateEvent(NULL, true, true, NULL);
}

CWaitEvent::~CWaitEvent()
{
    CloseHandle(m_event);
}

void CWaitEvent::Set()
{
    SetEvent(m_event);
}

void CWaitEvent::Reset()
{
    ResetEvent(m_event);
}

void CWaitEvent::Wait()
{
    WaitForSingleObject(m_event, INFINITE);
}

#else
CWaitEvent::CWaitEvent()
{
    pthread_mutex_init(&m_mutex, NULL);
    pthread_cond_init(&m_event, NULL);
}

CWaitEvent::~CWaitEvent()
{
    pthread_mutex_destroy(&m_mutex);
    pthread_cond_destroy(&m_event);
}

void CWaitEvent::Set()
{
    pthread_mutex_lock(&m_mutex);
    pthread_cond_signal(&m_event);
    pthread_mutex_unlock(&m_mutex);
}

void CWaitEvent::Reset()
{

}

void CWaitEvent::Wait()
{
    pthread_mutex_lock(&m_mutex);
    pthread_cond_wait(&m_event, &m_mutex);
    pthread_mutex_unlock(&m_mutex);
}

#endif



