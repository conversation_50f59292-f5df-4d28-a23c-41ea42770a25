#include "AKLog.h"
#include <stdio.h>
#include <stdarg.h>
#include "glog/logging.h"

#define MAX_PATH 256
#define BUFF_SIZE 4096

#define DEBUG   0

CAKLog::CAKLog()
{
    return;
}
void CAKLog::Init()
{
    //google::InitGoogleLogging("vrtspd");
    //FLAGS_log_dir = "./log";
    //LOG(INFO) << "glog init";
}

void CAKLog::LogT(const char* tag, const char* msg, ...)
{
    char szTag[MAX_PATH] = { 0 };
    snprintf(szTag, sizeof(szTag), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", szTag, buf);
    //
#endif // DEBUG
   LOG(INFO) << szTag << buf;
}

void CAKLog::LogD(const char* tag, const char* msg, ...)
{
    char szTag[MAX_PATH] = { 0 };
    snprintf(szTag, sizeof(szTag), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", szTag, buf);
#endif // DEBUG
    LOG(INFO) << szTag << buf;
}

void CAKLog::LogI(const char* tag, const char* msg, ...)
{
    char szTag[MAX_PATH] = { 0 };
    snprintf(szTag, sizeof(szTag), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", szTag, buf);
#endif // DEBUG
    LOG(INFO) << szTag << buf;
}

void CAKLog::LogW(const char* tag, const char* msg, ...)
{
    char szTag[MAX_PATH] = { 0 };
    snprintf(szTag, sizeof(szTag), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", szTag, buf);
#endif // DEBUG
    LOG(WARNING) << szTag << buf;
}

void CAKLog::LogE(const char* tag, const char* msg, ...)
{
    char szTag[MAX_PATH] = { 0 };
    snprintf(szTag, sizeof(szTag), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", szTag, buf);
#endif // DEBUG
    LOG(ERROR) << szTag << buf;
}

void CAKLog::LogF(const char* tag, const char* msg, ...)
{
    char szTag[MAX_PATH] = { 0 };
    snprintf(szTag, sizeof(szTag), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", szTag, buf);
#endif // DEBUG
    LOG(FATAL) << szTag << buf;
}

void CAKLog::LogHex(const char* tag, const unsigned char* data, int len)
{
    char szBuf[1024] = { 0 };
    char szTmp[32] = { 0 };
    for (int i = 0; i < len; i++)
    {
        snprintf(szTmp, 32, "%02x ", data[i]);
        if (i % 16 == 0)
        {
            strcat(szBuf, "\n");
        }

        strcat(szBuf, szTmp);
    }
    LogD(tag, "%s", szBuf);
}
