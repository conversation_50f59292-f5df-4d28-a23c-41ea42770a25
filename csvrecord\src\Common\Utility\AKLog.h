#pragma once

class CAKLog
{
public:
    CAKLog(void);

    static void Init();
    static void LogT(const char* tag, const char* msg, ...);
    static void LogD(const char* tag, const char* msg, ...);
    static void LogI(const char* tag, const char* msg, ...);
    static void LogW(const char* tag, const char* msg, ...);
    static void LogE(const char* tag, const char* msg, ...);
    static void LogF(const char* tag, const char* msg, ...);
    static void LogHex(const char* tag, const unsigned char* data, int len);
};

