#include "CaptureControl.h"
#include "Lock.h"
#include "WaitEvent.h"
#include "VrecordDefine.h"
#include "Utility.h"
#include "ipc/vrecord_ipc.h"
#include <stdlib.h>
#include <stdio.h>
#include "string.h"
#include "unistd.h"

#include "GetCaptureHandle.h"

//#include "msg.h"

void*  CaptureProcessThread(void* pData)
{
    CCaptureControl* pCaptureControl = (CCaptureControl*)pData;

    while (true)
    {
        pCaptureControl->ProcessMsg();
    }

    pthread_detach(pthread_self());
    return 0;
}

CCaptureControl* GetCaptureControlInstance()
{
    return CCaptureControl::GetInstance();
}

CCaptureControl::CCaptureControl()
{
    m_msgHeader = NULL;
    m_lock = new CLock();
    m_wait = new CWaitEvent();
}

CCaptureControl::~CCaptureControl()
{
    DelAllMsg();

    if (NULL != m_lock)
    {
        delete (CLock*)m_lock;
        m_lock = NULL;
    }
    if (NULL != m_wait)
    {
        delete (CWaitEvent*)m_wait;
        m_wait = NULL;
    }
}

CCaptureControl* CCaptureControl::instance = NULL;

CCaptureControl* CCaptureControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CCaptureControl();
    }

    return instance;
}

int CCaptureControl::Init()
{
    pthread_create(&m_tidProcess, NULL, CaptureProcessThread, this);
    return 0;
}

//处理消息
int CCaptureControl::ProcessMsg()
{
    WaitForEvent();
    Lock();
    MESSAGE* tmpNode = NULL;

    while (m_msgHeader != NULL)
    {
        tmpNode = (MESSAGE*)m_msgHeader;
        m_msgHeader = ((MESSAGE*)m_msgHeader)->next;
        Unlock();
        OnMessage(tmpNode->id, tmpNode->wParam, tmpNode->lParam, tmpNode->lpData);
        Lock();
        if (tmpNode->lpData != NULL)
        {
            delete [](char*)tmpNode->lpData;
        }
        delete (tmpNode);
    }

    m_msgHeader = NULL;

    ResetWaitEvent();

    Unlock();

    return 0;
}


//上锁消息缓冲区
void CCaptureControl::Lock()
{
    ((CLock*)m_lock)->Lock();
}

//解锁消息缓冲区
void CCaptureControl::Unlock()
{
    ((CLock*)m_lock)->Unlock();
}

//设置事件
void CCaptureControl::SetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Set();
}

//清除事件
void CCaptureControl::ResetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Reset();
}

//等待事件触发
void CCaptureControl::WaitForEvent()
{
    ((CWaitEvent*)m_wait)->Wait();
}


//增加一个新的消息
int CCaptureControl::AddMsg(uint32_t id, uint32_t wParam, uint32_t lParam, void* lpData, int nDataLen)
{
    Lock();
    MESSAGE* curNode = NULL;
    MESSAGE* newNode = new MESSAGE();
    if (NULL == newNode)
    {
        Unlock();
        return -1;
    }

    memset(newNode, 0, sizeof(MESSAGE));

    newNode->id = id;
    newNode->wParam = wParam;
    newNode->lParam = lParam;
    if ((lpData != NULL) && (nDataLen > 0))
    {
        newNode->lpData = new char[nDataLen];
        memcpy(newNode->lpData, lpData, nDataLen);
    }

    if (m_msgHeader == NULL)
    {
        m_msgHeader = newNode;
    }
    else
    {
        curNode = (MESSAGE*)m_msgHeader;
        while ((curNode != NULL) && (curNode->next != NULL))
        {
            curNode = curNode->next;
        }
        curNode->next = newNode;
    }
    SetWaitEvent();

    Unlock();

    return 0;
}


//删除所有消息
int CCaptureControl::DelAllMsg()
{
    Lock();

    MESSAGE* curNode = NULL;
    MESSAGE* tmpNode = NULL;

    curNode = (MESSAGE*)m_msgHeader;

    while (curNode != NULL)
    {
        tmpNode = curNode;
        curNode = curNode->next;
        if (tmpNode->lpData != NULL)
        {
            delete [](char*)tmpNode->lpData;
        }

        delete tmpNode;
    }

    m_msgHeader = NULL;

    Unlock();

    return 0;
}

//消息处理句柄
int CCaptureControl::OnMessage(uint32_t msg, uint32_t wParam, uint32_t lParam, void* lpData)
{
    int nMsgType = msg & MSG_TYPE_MASK;
    switch (nMsgType)
    {
        case MSG_TIMER:
        {
        }
        break;
        case MSG_MULTICAST:
        {
            SOCKET_MSG* pRecvMsg = (SOCKET_MSG*)lpData;
            OnSocketMsg(pRecvMsg);
            break;
        }
        case MSG_TCP:
        {
            SOCKET_MSG* pRecvMsg = (SOCKET_MSG*)lpData;
            OnSocketMsg(pRecvMsg);
        }
        break;
        case MSG_CTRL:
        {
            OnCtrl(msg, wParam, lParam, lpData);
        }
        break;
        default:
            break;
    }

    return 0;
}

//控制消息处理句柄
int CCaptureControl::OnCtrl(uint32_t msg, uint32_t wParam, uint32_t lParam, void* lpData)
{
    switch (msg)
    {
        case MSG_CTRL_CAPTURE_START:
        {
            CAPTURE_FILE_DATA* pCaptureData = (CAPTURE_FILE_DATA*)lpData;
            GetCaptureHandleInstance()->H264ToJpeg(pCaptureData);
        }
        break;

        default:
            break;
    }

    return 0;
}

int CCaptureControl::OnSocketMsg(SOCKET_MSG* pRecvMsg)
{
    if (pRecvMsg == NULL)
    {
        return -1;
    }
    //判断MAGIC

    //判断CRC

    //判断类型  socket的消息， record当前用的是ipc通信，暂时不用
    SOCKET_MSG_NORMAL* pNormalMsg = (SOCKET_MSG_NORMAL*)pRecvMsg->byData;
    int nMsgID = pNormalMsg->nMsgID & SOCKET_MSG_ID_MASK;

    switch (nMsgID)
    {
        case MSG_TO_SEND_VIDEO_DATA:
        {

        }
        break;
        default:
            break;
    }

    return 0;
}
