#ifndef __CAPTURE_CONTROL_H__
#define __CAPTURE_CONTROL_H__

#pragma once

#include "VrecordIncludes.h"

class CCaptureControl
{
public:
    CCaptureControl();
    ~CCaptureControl();

    static CCaptureControl* GetInstance();

    //初始化
    int Init();

    //运行
    int Run();

    //处理消息
    int ProcessMsg();
    //增加一个新的消息
    int AddMsg(unsigned int id, unsigned int wParam, unsigned int lParam, void* lpData, int nDataLen);
    //删除所有消息
    int DelAllMsg();

    //消息处理句柄
    int OnMessage(unsigned int msg, unsigned int wParam, unsigned int lParam, void* lpData);

    int OnSocketMsg(SOCKET_MSG* pRecvMsg);

    //控制消息处理句柄
    int OnCtrl(uint32_t msg, uint32_t wParam, uint32_t lParam, void* lpData);

private:
    void Lock();
    void Unlock();
    void SetWaitEvent();
    void ResetWaitEvent();
    void WaitForEvent();

    static CCaptureControl* instance;
    pthread_t m_tidTimer;
    pthread_t m_tidProcess;

    void* m_msgHeader;
    void* m_lock;
    void* m_wait;
};

CCaptureControl* GetCaptureControlInstance();

#endif
