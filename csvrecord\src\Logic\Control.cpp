#include "Control.h"
#include "Lock.h"
#include "WaitEvent.h"
#include "VrecordDefine.h"
#include "Utility.h"
#include "ipc/vrecord_ipc.h"
#include <stdlib.h>
#include <stdio.h>
#include "string.h"
#include "unistd.h"
#include "AKLog.h"

#include "CaptureControl.h"
#include "GetCaptureHandle.h"

//#include "msg.h"

int ProcessThread(void* pData)
{
    CControl* pControl = (CControl*)pData;

    while (true)
    {
        pControl->ProcessMsg();
    }

    pthread_detach(pthread_self());
    return 0;
}

void* TimerThread(void* pData)
{
    CControl* pControl = (CControl*)pData;

    while (true)
    {
        pControl->AddMsg(MSG_TIMER, TIMER_ID_BASE, 0, NULL, 0);
        usleep(TIMER_VAL_BASE * 1000);
    }

    pthread_detach(pthread_self());
    return 0;
}

CControl* GetControlInstance()
{
    return CControl::GetInstance();
}

CControl::CControl() : TAG("Control")
{
    m_msgHeader = NULL;
    m_lock = new CLock();
    m_wait = new CWaitEvent();
}

CControl::~CControl()
{
    DelAllMsg();

    if (NULL != m_lock)
    {
        delete (CLock*)m_lock;
        m_lock = NULL;
    }
    if (NULL != m_wait)
    {
        delete (CWaitEvent*)m_wait;
        m_wait = NULL;
    }
}

CControl* CControl::instance = NULL;

CControl* CControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CControl();
    }

    return instance;
}

int CControl::Init()
{
    GetCaptureControlInstance()->Init();
    pthread_create(&m_tidTimer, NULL, TimerThread, this);
    return 0;
}

int CControl::Run()
{
    return (int)ProcessThread(this);
}

//处理消息
int CControl::ProcessMsg()
{
    WaitForEvent();
    Lock();
    MESSAGE* tmpNode = NULL;

    while (m_msgHeader != NULL)
    {
        tmpNode = (MESSAGE*)m_msgHeader;
        m_msgHeader = ((MESSAGE*)m_msgHeader)->next;
        Unlock();
        OnMessage(tmpNode->id, tmpNode->wParam, tmpNode->lParam, tmpNode->lpData);
        Lock();
        if (tmpNode->lpData != NULL)
        {
            delete [](char*)tmpNode->lpData;
        }
        delete (tmpNode);
    }

    m_msgHeader = NULL;

    ResetWaitEvent();

    Unlock();

    return 0;
}


//上锁消息缓冲区
void CControl::Lock()
{
    ((CLock*)m_lock)->Lock();
}

//解锁消息缓冲区
void CControl::Unlock()
{
    ((CLock*)m_lock)->Unlock();
}

//设置事件
void CControl::SetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Set();
}

//清除事件
void CControl::ResetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Reset();
}

//等待事件触发
void CControl::WaitForEvent()
{
    ((CWaitEvent*)m_wait)->Wait();
}


//增加一个新的消息
int CControl::AddMsg(uint32_t id, uint32_t wParam, uint32_t lParam, void* lpData, int nDataLen)
{
    Lock();
    MESSAGE* curNode = NULL;
    MESSAGE* newNode = new MESSAGE();
    if (NULL == newNode)
    {
        Unlock();
        return -1;
    }

    memset(newNode, 0, sizeof(MESSAGE));

    newNode->id = id;
    newNode->wParam = wParam;
    newNode->lParam = lParam;
    if ((lpData != NULL) && (nDataLen > 0))
    {
        newNode->lpData = new char[nDataLen];
        memcpy(newNode->lpData, lpData, nDataLen);
    }

    if (m_msgHeader == NULL)
    {
        m_msgHeader = newNode;
    }
    else
    {
        curNode = (MESSAGE*)m_msgHeader;
        while ((curNode != NULL) && (curNode->next != NULL))
        {
            curNode = curNode->next;
        }
        curNode->next = newNode;
    }
    SetWaitEvent();

    Unlock();

    return 0;
}


//删除所有消息
int CControl::DelAllMsg()
{
    Lock();

    MESSAGE* curNode = NULL;
    MESSAGE* tmpNode = NULL;

    curNode = (MESSAGE*)m_msgHeader;

    while (curNode != NULL)
    {
        tmpNode = curNode;
        curNode = curNode->next;
        if (tmpNode->lpData != NULL)
        {
            delete [](char*)tmpNode->lpData;
        }

        delete tmpNode;
    }

    m_msgHeader = NULL;

    Unlock();

    return 0;
}

//消息处理句柄
int CControl::OnMessage(uint32_t msg, uint32_t wParam, uint32_t lParam, void* lpData)
{
    int nMsgType = msg & MSG_TYPE_MASK;
    switch (nMsgType)
    {
        case MSG_TIMER:
        {
            OnTimer(wParam);
        }
        break;
        case MSG_MULTICAST:
        {
            SOCKET_MSG* pRecvMsg = (SOCKET_MSG*)lpData;
            OnSocketMsg(pRecvMsg);
            break;
        }
        case MSG_TCP:
        {
            SOCKET_MSG* pRecvMsg = (SOCKET_MSG*)lpData;
            OnSocketMsg(pRecvMsg);
        }
        break;
        case MSG_CTRL:
        {
            OnCtrl(msg, wParam, lParam, lpData);
        }
        break;
        default:
            break;
    }

    return 0;
}

//控制消息处理句柄
int CControl::OnCtrl(uint32_t msg, uint32_t wParam, uint32_t lParam, void* lpData)
{
    switch (msg)
    {

        case MSG_CTRL_RECEIVED_CAPTURE_DATA:
        {
            SOCKET_MSG_VIDEO_DATA* pCaptureData = (SOCKET_MSG_VIDEO_DATA*)lpData;
            GetCaptureHandleInstance()->GetCapture(pCaptureData->szData, pCaptureData->nDataLen, pCaptureData->flow_uuid, pCaptureData->szPicName);//实际数据的长度比msg的长度少20, len的长度包括 5个int
        }
        break;

        case MSG_CTRL_RECEIVED_CAPTURE_START:
        {
            SOCKET_MSG_CAPTURE_RTSP* pCaptureData = (SOCKET_MSG_CAPTURE_RTSP*)lpData;
            CAKLog::LogI(TAG, "capture mac===%s,  capture picture ===%s, flow_uuid===%s", pCaptureData->szMac, pCaptureData->szPicName, pCaptureData->flow_uuid);
            GetCaptureHandleInstance()->SetCaptureParam(pCaptureData->flow_uuid, pCaptureData->szPicName);//实际数据的长度比msg的长度少20, len的长度包括 5个int
        }
        break;

        default:
            break;
    }

    return 0;
}

int CControl::OnSocketMsg(SOCKET_MSG* pRecvMsg)
{
    if (pRecvMsg == NULL)
    {
        return -1;
    }
    //判断MAGIC

    //判断CRC

    //判断类型  socket的消息， record当前用的是ipc通信，暂时不用
    SOCKET_MSG_NORMAL* pNormalMsg = (SOCKET_MSG_NORMAL*)pRecvMsg->byData;
    int nMsgID = pNormalMsg->nMsgID & SOCKET_MSG_ID_MASK;

    switch (nMsgID)
    {
        case MSG_TO_SEND_VIDEO_DATA:
        {

        }
        break;
        default:
            break;
    }

    return 0;
}

//定时器消息处理
int CControl::OnTimer(uint32_t nIDEvent)
{
    if (nIDEvent == TIMER_ID_BASE)
    {
        GetCaptureHandleInstance()->CheckBaseTimer();
    }

    return 0;
}
