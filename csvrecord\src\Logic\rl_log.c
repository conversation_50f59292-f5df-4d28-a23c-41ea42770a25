/** @file log.c
 * @ log implemention
 * @date 2012-12-13
 * @note
 *
 *
 * Copyright(c) 2012-2020 Xiamen Ringslink telenology Co,.Ltd
 */

#include <syslog.h>
#include <stdarg.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include "ipc/rl_log.h"
#include <sys/syscall.h>
#include <pthread.h>
#include "revision.h"

#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
#include <android/log.h>
#endif

/* string buffer size  */
#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
#define MODULE_NAME_LENTH 16
#define PREFIX_LENTH 64
#else
#define MODULE_NAME_LENTH 15
#define PREFIX_LENTH 50
#endif
#define BUFF_SIZE 4096


//typedef pthread_mutex_t LockHandler;
//
//#define LOCK_INIT(lock) pthread_mutex_init(lock, NULL)
//#define LOCK_EXIT(lock) pthread_mutex_destroy(lock)
//#define LOCK_ON(lock) pthread_mutex_lock(lock)
//#define LOCK_OFF(lock) pthread_mutex_unlock(lock)


static int g_pid;
static char g_module_name[MODULE_NAME_LENTH];

static int g_log_level = LOG_LEVEL_DEBUG;
static int g_nFlag = 0;

#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
#include <android/log.h>
#endif


// 对于每个进程只初始化一次
int rl_log_init(const char* module_name, int in_log_level)
{
    if (1 == g_nFlag)
    {
        return 0;
    }
    g_nFlag = 1;
    /* release syslog resource  */
    /* it maybe invoked more than one time */
    closelog();

    /* initilize the global variable
     open syslog connection  */
    strncpy(g_module_name, module_name, MODULE_NAME_LENTH);
    g_module_name[MODULE_NAME_LENTH - 1] = '\0';
    g_pid = getpid();
    g_log_level = in_log_level;
    openlog("", LOG_NDELAY, LOG_USER);

    return 0;
}

void rl_log_deinit()
{
    if (0 == g_nFlag)
    {
        return;
    }
    g_nFlag = 0;
    /* release syslog resource  */
    closelog();
}

void rl_log_reset_level(int log_level)
{
    g_log_level = log_level;
}
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
void rl_log_common(int dest_log_level, const char* buf)
{
    if (buf == NULL)
    {
        return;
    }
    char prefix[PREFIX_LENTH];
    char des_buf[BUFF_SIZE];
#if ADD_THREAD_ID
    int iThisThrId = syscall(__NR_gettid);
    snprintf(prefix, PREFIX_LENTH, "%s pid%d,tid%d,l%d:", g_module_name, g_pid, iThisThrId, dest_log_level);
#else
    snprintf(prefix, PREFIX_LENTH, "%s pid%d,l%d:", g_module_name, g_pid, dest_log_level);
#endif
    snprintf(des_buf, BUFF_SIZE, "%s %s\n", prefix, buf);
    syslog(dest_log_level, "%s %s\n", prefix, buf);
    printf("%s", des_buf);

}
#else
void rl_log_common(int dest_log_level, const char* fmt, va_list argptr)
{
    char log_strbuf[BUFF_SIZE];
    char fmt_with_prefix[BUFF_SIZE] = {0};
    int iThisThrId = syscall(__NR_gettid);
    snprintf(fmt_with_prefix, sizeof(fmt_with_prefix), "%s pid%d,tid%d,l%d: %s", g_module_name, g_pid, iThisThrId, dest_log_level, fmt);
    int cnt = vsnprintf(log_strbuf, BUFF_SIZE, fmt_with_prefix, argptr);
    printf("%s \n", log_strbuf);
#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
    __android_log_print(ANDROID_LOG_DEBUG, "R47", "%s", log_strbuf);
#else
    vsyslog(dest_log_level, fmt_with_prefix, argptr);
#endif
}
#endif

//void check(va_list argptr)
//{
//  argptr+
//}

void rl_log_debug(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    //#ifdef _TESTMODE
    //return;
    //#endif
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif

    if (g_log_level >= LOG_LEVEL_DEBUG)
    {
        va_start(argptr, fmt);
        /* wite arguments in the whole format string   */
        /* to log_strbuf according to the numbers of "%"  */
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_DEBUG, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_DEBUG, fmt, argptr);
#endif
        va_end(argptr);
    }

}

void rl_log_info(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    //#ifdef _TESTMODE
    //return;
    //#endif
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_INFO)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_INFO, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_INFO, fmt, argptr);
#endif
        va_end(argptr);
    }
}

void rl_log_warn(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_WARNING)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_WARNING, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_WARNING, fmt, argptr);
#endif
        va_end(argptr);
    }

}

void rl_log_err(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_ERR)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_ERR, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_ERR, fmt, argptr);
#endif
        va_end(argptr);
    }
}

void rl_log_notice(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_NOTICE)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_NOTICE, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_NOTICE, fmt, argptr);
#endif
        va_end(argptr);

    }
}

void rl_log_crit(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_CRIT)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_CRIT, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_CRIT, fmt, argptr);
#endif
        va_end(argptr);
    }

}

void rl_log_alert(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_ALERT)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_ALERT, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_ALERT, fmt, argptr);
#endif
        va_end(argptr);
    }

}

void rl_log_emerg(const char* fmt, ...)
{
    if (fmt == NULL || strlen(fmt) == 0)
    {
        return;
    }
    va_list argptr;
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
    char log_strbuf[BUFF_SIZE];
#endif
    if (strlen(fmt) == 0)
    {
        return;
    }

    if (g_log_level >= LOG_LEVEL_EMERG)
    {
        va_start(argptr, fmt);
#if !RL_FIX_RL_LOGSEG_FAULT_WITH_PERCENT_SIGN
        vsnprintf(log_strbuf, BUFF_SIZE, fmt, argptr);
        rl_log_common(LOG_LEVEL_EMERG, log_strbuf);
#else
        rl_log_common(LOG_LEVEL_EMERG, fmt, argptr);
#endif
        va_end(argptr);
    }
}

