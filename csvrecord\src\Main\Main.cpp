#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <signal.h>
#include "Control.h"
#include "ipc/vrecord_ipc.h"
#include "ConfigFileReader.h"
#include "glog/logging.h"
#include "util.h"
#include <unistd.h>
#include <netinet/in.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <fcntl.h>

//add by larry 20180305 判断单例
#define PIDFILE "/var/run/csvrecord.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

CSVRECORD_CONF gstCSVRECORDConf; //全局配置信息
int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

//end by larry

static void SignalInit(void)
{
    signal(SIGPIPE, SIG_IGN);

}
void ipc_handle(UNIX_IPC_MSG* msg, void* data)
{
    if (IPC_ID_VRECORD == msg->from)
    {
        //rl_log_debug("recv ipc msg from self.");
        return;
    }

    switch (msg->id)
    {
        case MSG_VRTSP2VRECORD_SEND_CAPTURE_DATA:
            GetControlInstance()->AddMsg(MSG_CTRL_RECEIVED_CAPTURE_DATA, msg->param1, msg->param2, msg->data, sizeof(SOCKET_MSG_VIDEO_DATA));
            break;

        case MSG_VRTSP2VRECORD_START_CAPTURE:
            GetControlInstance()->AddMsg(MSG_CTRL_RECEIVED_CAPTURE_START, msg->param1, msg->param2, msg->data, sizeof(SOCKET_MSG_CAPTURE_RTSP));
            break;

        default:
            break;
    }
}

void ConfInit()
{
    memset(&gstCSVRECORDConf, 0, sizeof(gstCSVRECORDConf));
    CConfigFileReader config_file("/usr/local/akcs/csvrecord/conf/csvrecord.conf");
    Snprintf(gstCSVRECORDConf.szCapturePath, sizeof(gstCSVRECORDConf.szCapturePath),  config_file.GetConfigName("capture_path"));
}

int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        return -1;
    }

    google::InitGoogleLogging("csvrecord");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csvrecordlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csvrecordlog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csvrecordlog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csvrecordlog/FATAL");
    FLAGS_logbufsecs = 0;
    //FLAGS_log_dir = "./log";
    LOG(INFO) << "vrecord going to start now---";
    FLAGS_max_log_size = 10;    //单日志文件最大10M

    SignalInit();
    ConfInit();

    GetControlInstance()->Init();
    ipc_register(IPC_ID_VRECORD);
    ipc_run(ipc_handle, NULL);

    //CUpLoadFile::getInstance().Init(); added by chenyc,分布式,csvreord的图片不需要走ftp的路径了,直接跟配套的csstorage上传到fdfs
    GetControlInstance()->Run();
    LOG(INFO) << "vrecord going to quit---";
    return 0;
}
