#include "stdlib.h"
#include "stdio.h"
#include "string.h"
#include "GetCaptureHandle.h"
#include "Utility.h"
#include "ipc/vrecord_ipc.h"
#include "CaptureControl.h"
#include "AKLog.h"
#include "AkLogging.h"
#include "sps.h"
#include "util.h"

#ifdef __cplusplus
extern "C" {
#endif

#include "jpeglib.h"
#include "libavcodec/avcodec.h"
#include "libavformat/avformat.h"
#include "libswscale/swscale.h"

#ifdef __cplusplus
} // endof extern "C"
#endif

extern CSVRECORD_CONF gstCSVRECORDConf;


CCaptureHandle* GetCaptureHandleInstance()
{
    return CCaptureHandle::GetInstance();
}

CCaptureHandle::CCaptureHandle() : TAG("CaptureHandle")
{
    for (int i = 0; i < MAX_SEND_DATA_COUNT; i++)
    {
        m_tCaptureInfo[i].nTimeStamp = 0;
        memset(m_tCaptureInfo[i].szMac, 0, sizeof(m_tCaptureInfo[i].szMac));
        memset(m_tCaptureInfo[i].szPicName, 0, sizeof(m_tCaptureInfo[i].szPicName));
        m_tCaptureInfo[i].nCaptureDataPos = 0;
        m_tCaptureInfo[i].nCaptureState = CAPTURE_STATE_NONE;
        m_tCaptureInfo[i].pszGetCapture = NULL;
        m_tCaptureInfo[i].last_seq_num = 0;
        memset(m_tCaptureInfo[i].last_pps_buffer, 0, sizeof(m_tCaptureInfo[i].last_pps_buffer));
        m_tCaptureInfo[i].last_pps_buffer_len = 0;
        memset(m_tCaptureInfo[i].capture_flow_uuid, 0, sizeof(m_tCaptureInfo[i].capture_flow_uuid));
    }

}

CCaptureHandle::~CCaptureHandle()
{
    for (int i = 0; i < MAX_SEND_DATA_COUNT; i++)
    {

        if (m_tCaptureInfo[i].pszGetCapture != NULL)
        {
            delete [] m_tCaptureInfo[i].pszGetCapture;
        }
        m_tCaptureInfo[i].pszGetCapture = NULL;
    }
}

CCaptureHandle* CCaptureHandle::instance = NULL;

CCaptureHandle* CCaptureHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CCaptureHandle();
    }

    return instance;
}

int CCaptureHandle::SetCaptureParam(const char* flow_uuid, const char* pszPicName)
{
    int i;
    for (i = 0; i < MAX_SEND_DATA_COUNT; i++)
    {
        if (strcmp(m_tCaptureInfo[i].capture_flow_uuid, flow_uuid) == 0)
        {
            return 0;
        }
    }

    for (i = 0; i < MAX_SEND_DATA_COUNT; i++)
    {
        if (m_tCaptureInfo[i].pszGetCapture == NULL &&
                m_tCaptureInfo[i].nCaptureState == CAPTURE_STATE_NONE &&
                m_tCaptureInfo[i].nCaptureDataPos == 0 &&
                strcmp(m_tCaptureInfo[i].capture_flow_uuid, "") == 0)
        {
            m_tCaptureInfo[i].nTimeStamp = 0;
            Snprintf(m_tCaptureInfo[i].capture_flow_uuid, sizeof(m_tCaptureInfo[i].capture_flow_uuid),  flow_uuid);
            m_tCaptureInfo[i].nCaptureState = CAPTURE_STATE_START;
            break;
        }
    }

    if (i == MAX_SEND_DATA_COUNT)
    {
        CAKLog::LogI(TAG, "exceed max allowed capture count, flow_uuid=%s", flow_uuid);
    }

    return 0;

}

int CCaptureHandle::H264ToJpeg(CAPTURE_FILE_DATA* pCaptureFileData)
{
    CAKLog::LogI(TAG, "resolution:%d*%d", pCaptureFileData->width, pCaptureFileData->height);
    av_register_all();
    AVPacket pkt;
    AVCodecContext* pCodecCtx;
    AVCodec* pCodec;
    AVFrame* pFrame;
    int frameFinished;
    av_init_packet(&pkt);
    pkt.size = pCaptureFileData->nDataLen;
    pkt.data = (unsigned char*)pCaptureFileData->szData;
    pkt.stream_index = 0;
    pkt.flags = 1;
    pkt.dts = pkt.pts = 0;
    pkt.pos = -1;
    pkt.duration = 3000;

    pCodec = avcodec_find_decoder(AV_CODEC_ID_H264);

    if (pCodec == NULL)
    {
        return -1;
    }

    pCodecCtx = avcodec_alloc_context3(pCodec);

    pCodecCtx->frame_number = 1;
    pCodecCtx->codec_type = AVMEDIA_TYPE_VIDEO;
    pCodecCtx->bit_rate = 0;
    pCodecCtx->time_base.num = 1;
    pCodecCtx->time_base.den = 30;
    pCodecCtx->width = pCaptureFileData->width;
    pCodecCtx->height = pCaptureFileData->height;
    pCodecCtx->pix_fmt = AV_PIX_FMT_YUV420P;

    if (avcodec_open2(pCodecCtx, pCodec, NULL) < 0)
    {
        return -1;
    }

    pFrame = av_frame_alloc();
    avcodec_decode_video2(pCodecCtx, pFrame, &frameFinished, &pkt);

    if (frameFinished)
    {
        int width = pCaptureFileData->width;
        int height = pCaptureFileData->height;
        int newSize = width * height * 3 / 2;  // YUV420 size

        unsigned char* yuvBuffer = new unsigned char[newSize];
        
        // Copy Y plane
        for (int i = 0; i < height; i++) {
            memcpy(yuvBuffer + i * width, pFrame->data[0] + i * pFrame->linesize[0], width);
        }
        
        // Copy U and V planes
        int uvHeight = height / 2;
        int uvWidth = width / 2;
        for (int i = 0; i < uvHeight; i++) {
            memcpy(yuvBuffer + width * height + i * uvWidth, pFrame->data[1] + i * pFrame->linesize[1], uvWidth);
            memcpy(yuvBuffer + width * height + uvWidth * uvHeight + i * uvWidth, pFrame->data[2] + i * pFrame->linesize[2], uvWidth);
        }

        // JPEG compression setup
        struct jpeg_compress_struct cinfo;
        struct jpeg_error_mgr jerr;
        FILE* outfile;
        JSAMPROW row_pointer[1];
        int row_stride;

        cinfo.err = jpeg_std_error(&jerr);
        jpeg_create_compress(&cinfo);

        char szFullPicName[URL_SIZE] = {0};
        snprintf(szFullPicName, sizeof(szFullPicName), "%s%s", gstCSVRECORDConf.szCapturePath, pCaptureFileData->szPicName);

        if ((outfile = fopen(szFullPicName, "wb")) == NULL)
        {
            delete [] yuvBuffer;
            return -1;
        }

        jpeg_stdio_dest(&cinfo, outfile);

        cinfo.image_width = width;
        cinfo.image_height = height;
        cinfo.input_components = 3;
        cinfo.in_color_space = JCS_YCbCr;

        jpeg_set_defaults(&cinfo);
        jpeg_set_quality(&cinfo, 90, TRUE);

        jpeg_start_compress(&cinfo, TRUE);

        row_stride = width * 3;
        while (cinfo.next_scanline < cinfo.image_height) {
            unsigned char* row = new unsigned char[row_stride];
            for (int i = 0; i < width; i++) {
                int y = yuvBuffer[cinfo.next_scanline * width + i];
                int u = yuvBuffer[width * height + (cinfo.next_scanline / 2) * (width / 2) + (i / 2)];
                int v = yuvBuffer[width * height + width * height / 4 + (cinfo.next_scanline / 2) * (width / 2) + (i / 2)];
                
                row[i * 3 + 0] = y;
                row[i * 3 + 1] = u;
                row[i * 3 + 2] = v;
            }
            row_pointer[0] = row;
            jpeg_write_scanlines(&cinfo, row_pointer, 1);
            delete [] row;
        }

        jpeg_finish_compress(&cinfo);
        fclose(outfile);
        jpeg_destroy_compress(&cinfo);

        delete [] yuvBuffer;
    }
    else
    {
        CAKLog::LogI(TAG, " the frame not finished,capture picture ===%s=", pCaptureFileData->szPicName);
    }

    av_frame_free(&pFrame);
    avcodec_close(pCodecCtx);
    av_free(pCodecCtx);

    return 0;
}

int CCaptureHandle::GetCapture(unsigned char* pData, unsigned int nDataLen, const char* capture_flow_uuid, const char* pszPicName)
{

    if (pData == NULL || nDataLen < 0 || capture_flow_uuid == NULL)
    {
        return -1;
    }

    if ((nDataLen - 14) <= 0)
    {
        return -1;
    }

    //属于哪个mac的包
    int index = -1;
    int i;
    //如果该mac已经申请了存储空间， 就用该空间存储这数据
    for (i = 0; i < MAX_SEND_DATA_COUNT; i++)
    {
        if (strcmp(m_tCaptureInfo[i].capture_flow_uuid, capture_flow_uuid) == 0)
        {
            index = i;
            break;
        }
    }

    if (index == -1)
    {
        return -1;
    }

    if (m_tCaptureInfo[index].nCaptureState == CAPTURE_STATE_NONE)
    {
        return -1;
    }
    //从这里开始缓存一个包
    const uint16_t seq_num = (pData[2] << 8) + pData[3];
    //CAKLog::LogI(TAG, " -------csvercord-----received a rtp, seq num is %d", seq_num);
    unsigned char szDataTmp[1500] = {0};
    unsigned int nDataLenTmp = 0;
    if (m_tCaptureInfo[i].last_seq_num == 0) //第一个包
    {
        memcpy(m_tCaptureInfo[i].last_pps_buffer, pData, nDataLen);
        m_tCaptureInfo[i].last_pps_buffer_len = nDataLen;
        m_tCaptureInfo[i].last_seq_num = seq_num;
        return 0;
    }
    else
    {
        if (seq_num > m_tCaptureInfo[i].last_seq_num)//缓存最新的一个包
        {
            memcpy(szDataTmp, m_tCaptureInfo[i].last_pps_buffer, m_tCaptureInfo[i].last_pps_buffer_len);
            nDataLenTmp = m_tCaptureInfo[i].last_pps_buffer_len;
            memset(m_tCaptureInfo[i].last_pps_buffer, 0, sizeof(m_tCaptureInfo[i].last_pps_buffer));
            memcpy(m_tCaptureInfo[i].last_pps_buffer, pData, nDataLen);
            m_tCaptureInfo[i].last_pps_buffer_len = nDataLen;
            pData = &szDataTmp[0];
            nDataLen = nDataLenTmp;
            m_tCaptureInfo[i].last_seq_num = seq_num;
        }
    }
    unsigned char szSaveBuffer[1500];
    int nSaveLen = 0;
    unsigned int FU_FLAG = 0;
    unsigned int MARK_BIT_START = 0;
    unsigned int MARK_BIT_END = 0;
    unsigned char NAL_HEAD = 0;

    FU_FLAG = (pData[12]) & (0x1C); //第13个字节和0x1C相与

    if (0x1C == FU_FLAG && (((pData[13]) & (0x1F)) == 0x05) && (m_tCaptureInfo[index].nCaptureState == CAPTURE_STATE_DOING)) //如果是FU型分割
    {
        //MARK_BIT_END = (pData[1]) >> 7;
        MARK_BIT_START = (pData[13]) >> 7; //取第二个字节的最高位，以便判断是否是此NALU的最后一包 // rtp的最后一包 marker标志
        MARK_BIT_END = (pData[13]) >> 6;

        if (MARK_BIT_START == 1)//这是当前NALU的第一包
        {
            if ((nDataLen - 14) <= 0)
            {
            }
            else
            {
                NAL_HEAD = ((pData[12]) & (0xE0)) | ((pData[13]) & (0x1F)); //取第13个字节的高3位和第14字节的低5位，拼成此NALU的头
                memset(szSaveBuffer, 0, sizeof(szSaveBuffer));
                szSaveBuffer[3] = 1;
                szSaveBuffer[4] = NAL_HEAD; //将NALU的头保存起来

                memcpy(&(szSaveBuffer[5]), &(pData[14]), nDataLen - 14); //从第15字节开始就是NALU的数据部分，保存起来
                nSaveLen = nDataLen - 9; //减12字节的RTP头，减2字节FU头，加4字节的起始码，加1字节的NALU头

                memcpy(m_tCaptureInfo[index].pszGetCapture + m_tCaptureInfo[index].nCaptureDataPos, szSaveBuffer, nSaveLen);
                m_tCaptureInfo[index].nCaptureDataPos += nSaveLen;
            }
        }
        else if (m_tCaptureInfo[index].nCaptureState == CAPTURE_STATE_DOING)
        {
            memset(szSaveBuffer, 0, sizeof(szSaveBuffer));
            if ((nDataLen - 14) > 1500)
            {
            }
            else if ((nDataLen - 14) <= 0)
            {
            }
            else
            {
                memcpy(szSaveBuffer, pData + 14, nDataLen - 14); //不是NALU的第一包，直接从第15字节保存起来
                nSaveLen = nDataLen - 14; //减12字节的RTP头，减2字节FU头

                memcpy(m_tCaptureInfo[index].pszGetCapture + m_tCaptureInfo[index].nCaptureDataPos, szSaveBuffer, nSaveLen);
                m_tCaptureInfo[index].nCaptureDataPos += nSaveLen;
            }
        }
        if (MARK_BIT_END == 1 && m_tCaptureInfo[index].nCaptureState == CAPTURE_STATE_DOING)//这是此NALU的最后一包
        {
            //m_ntimestamp = 0;//时间戳的判断，暂时不加

            //这一NALU已经收齐，下面再来的包就是下一个NALU的了, NONE是不再接收， end是结束这帧开始下一帧的接收
            m_tCaptureInfo[index].nCaptureState = CAPTURE_STATE_NONE;
            // 通知对应的csvrtspd，一帧已经发生完成，可以关闭流了
            SOCKET_MSG_CAPTURE_RTSP tCaptureResp = {0};
            Snprintf(tCaptureResp.flow_uuid, sizeof(tCaptureResp.flow_uuid),  m_tCaptureInfo[index].capture_flow_uuid);
            //Snprintf(tCaptureData.szPicName, sizeof(tCaptureData.szPicName),  m_szPicName);
            ipc_send(IPC_ID_VRTPSD, MSG_VRECORD2VRTSP_STOP_CAPTURE, 0, 0, (void*)&tCaptureResp, sizeof(SOCKET_MSG_CAPTURE_RTSP));

            // 将数据和mac，picname，添加到截图线程中 capture thread
            CAPTURE_FILE_DATA tCaptureFileData = {0};
            tCaptureFileData.nDataLen = m_tCaptureInfo[index].nCaptureDataPos;
            tCaptureFileData.width = m_tCaptureInfo[index].width;
            tCaptureFileData.height = m_tCaptureInfo[index].height;
            Snprintf(tCaptureFileData.szMac, sizeof(tCaptureFileData.szMac),  m_tCaptureInfo[index].szMac);
            Snprintf(tCaptureFileData.flow_uuid, sizeof(tCaptureFileData.flow_uuid), m_tCaptureInfo[index].capture_flow_uuid);
            Snprintf(tCaptureFileData.szPicName, sizeof(tCaptureFileData.szPicName),  m_tCaptureInfo[index].szPicName);
            memcpy(tCaptureFileData.szData, m_tCaptureInfo[index].pszGetCapture, m_tCaptureInfo[index].nCaptureDataPos);
            GetCaptureControlInstance()->AddMsg(MSG_CTRL_CAPTURE_START, 0, 0, &tCaptureFileData, sizeof(CAPTURE_FILE_DATA));
            //end

            //将该结构体进行置0操作,等待下一次使用
            //m_tCaptureInfo[index].nCaptureState = CAPTURE_STATE_START;
            if (m_tCaptureInfo[index].pszGetCapture != NULL)
            {
                delete [] m_tCaptureInfo[index].pszGetCapture;
                m_tCaptureInfo[index].pszGetCapture = NULL;
            }
            m_tCaptureInfo[index].nCaptureDataPos = 0;

            memset(m_tCaptureInfo[index].szPicName, 0, sizeof(m_tCaptureInfo[index].szPicName));
            memset(m_tCaptureInfo[index].szMac, 0, sizeof(m_tCaptureInfo[index].szMac));
            memset(m_tCaptureInfo[index].capture_flow_uuid, 0, sizeof(m_tCaptureInfo[index].capture_flow_uuid));
            m_tCaptureInfo[index].last_seq_num = 0;
            m_tCaptureInfo[index].last_pps_buffer_len = 0;
            memset(m_tCaptureInfo[index].last_pps_buffer, 0, sizeof(m_tCaptureInfo[index].last_pps_buffer));
        }

    }
    else if ((pData[12]) == 0x68 && m_tCaptureInfo[index].nCaptureState == CAPTURE_STATE_DOING) //pps
        //else if((pData[12]) == 0x68)//pps
    {
        memset(szSaveBuffer, 0, sizeof(szSaveBuffer));
        szSaveBuffer[3] = 1;
        memcpy(&(szSaveBuffer[4]), &(pData[12]), nDataLen - 12); //第13字节是此NALU的头，14字节及以后是NALU的内容，一起保存
        nSaveLen = nDataLen - 12 + 4;

        memcpy(m_tCaptureInfo[index].pszGetCapture + m_tCaptureInfo[index].nCaptureDataPos, szSaveBuffer, nSaveLen);
        m_tCaptureInfo[index].nCaptureDataPos += nSaveLen;
    }
    else if ((pData[12]) == 0x67 && m_tCaptureInfo[index].nCaptureState == CAPTURE_STATE_START) //sps
    {
        //m_ntimestamp = nTimestamp; //时间戳的判断，暂时不加

        m_tCaptureInfo[index].pszGetCapture = new unsigned char[CAPTURE_SIZE];

        Snprintf(m_tCaptureInfo[index].szPicName, sizeof(m_tCaptureInfo[index].szPicName),  pszPicName);
        m_tCaptureInfo[index].nCaptureState = CAPTURE_STATE_DOING;

        memset(szSaveBuffer, 0, sizeof(szSaveBuffer));
        szSaveBuffer[3] = 1;
        memcpy(&(szSaveBuffer[4]), &(pData[12]), nDataLen - 12); //第13字节是此NALU的头，14字节及以后是NALU的内容，一起保存
        nSaveLen = nDataLen - 12 + 4;

        h264_sps_data_t sps = {0};
        h264_parse_sps((char*)&(pData[13]), nDataLen-13, &sps);
        if(sps.width == 352 && sps.height == 288)    //适配CIF
        {
            sps.width = 320;
            sps.height = 240;
        }
        else if(sps.width == 176 && sps.height == 144)  //适配QCIF
        {
            sps.width = 160;
            sps.height = 120;
        }
        m_tCaptureInfo[index].width = sps.width;
        m_tCaptureInfo[index].height = sps.height;

        memcpy(m_tCaptureInfo[index].pszGetCapture + m_tCaptureInfo[index].nCaptureDataPos, szSaveBuffer, nSaveLen);
        m_tCaptureInfo[index].nCaptureDataPos += nSaveLen;

    }
    return 0;
}

int CCaptureHandle::CheckBaseTimer()
{
    for (int i = 0; i < MAX_SEND_DATA_COUNT; i++)
    {
        if (strcmp(m_tCaptureInfo[i].szMac, "") != 0)
        {
            if (m_tCaptureInfo[i].nTimeStamp > 15) // 如果15秒还没有结束就强制回收资源
            {
                if (m_tCaptureInfo[i].pszGetCapture != NULL)
                {
                    delete [] m_tCaptureInfo[i].pszGetCapture;
                    m_tCaptureInfo[i].pszGetCapture = NULL;
                }
                m_tCaptureInfo[i].nTimeStamp = 0;
                m_tCaptureInfo[i].nCaptureDataPos = 0;
                m_tCaptureInfo[i].nCaptureState = CAPTURE_STATE_NONE;
                memset(m_tCaptureInfo[i].szPicName, 0, sizeof(m_tCaptureInfo[i].szPicName));
                memset(m_tCaptureInfo[i].szMac, 0, sizeof(m_tCaptureInfo[i].szMac));
                m_tCaptureInfo[i].last_seq_num = 0;
                m_tCaptureInfo[i].last_pps_buffer_len = 0;
                memset(m_tCaptureInfo[i].last_pps_buffer, 0, sizeof(m_tCaptureInfo[i].last_pps_buffer));
                memset(m_tCaptureInfo[i].capture_flow_uuid, 0, sizeof(m_tCaptureInfo[i].capture_flow_uuid));
            }
            else
            {
                m_tCaptureInfo[i].nTimeStamp++;
            }
        }
    }
    return 0;
}


