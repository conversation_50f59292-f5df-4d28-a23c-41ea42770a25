#ifndef __CAPTURE_HANDLE_H__
#define __CAPTURE_HANDLE_H__

#include "VrecordIncludes.h"

#pragma once

class CCaptureHandle
{
public:
    CCaptureHandle();
    ~CCaptureHandle();

    int GetCapture(unsigned char* pData, unsigned int nDataLen, const char* capture_flow_uuid, const char* pszPicName);
    int H264ToJpeg(CAPTURE_FILE_DATA* pCaptureFileData);
    int SetCaptureParam(const char* flow_uuid, const char* pszPicName);
    int CheckBaseTimer();
    static CCaptureHandle* GetInstance();
private:
    /*
    char m_szMac[MAX_SEND_DATA_COUNT][MAC_SIZE];
    char m_szPicName[MAX_SEND_DATA_COUNT][VALUE_SIZE];
    int m_nCaptureDataPos[MAX_SEND_DATA_COUNT];
    int m_nCaptureState[MAX_SEND_DATA_COUNT];
    unsigned char *m_szGetCapture[MAX_SEND_DATA_COUNT];
    unsigned int m_ntimestamp;
    int m_nSpsNalu;
    */
    const char* TAG;
    CAPTURE_INFO m_tCaptureInfo[MAX_SEND_DATA_COUNT]; //最大同时截的路数

    static CCaptureHandle* instance;

};

CCaptureHandle* GetCaptureHandleInstance();

#endif

