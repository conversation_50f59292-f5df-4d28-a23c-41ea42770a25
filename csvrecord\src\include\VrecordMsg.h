#ifndef __VRECORD_MSG_H__
#define __VRECORD_MSG_H__
#pragma once
#include <stdint.h>
#include "VrecordMsgCommon.h"
#include "VrecordDefine.h"

#define MSG_TYPE_MASK       0xFFFF0000
#define MSG_TIMER           0x00010000
#define MSG_MULTICAST       0x00020000
#define MSG_TCP             0x00030000
#define MSG_CTRL            0x00040000

enum
{
    MSG_CTRL_RECEIVED_CAPTURE_DATA = MSG_CTRL + 1,
    MSG_CTRL_RECEIVED_CAPTURE_START,
    MSG_CTRL_CAPTURE_START,
};


typedef enum
{
    CAPTURE_STATE_NONE = 0,
    CAPTURE_STATE_START,
    CAPTURE_STATE_DOING,
    CAPTURE_STATE_END,
} CAPTURE_STATE;



typedef enum
{
    CONNECT_MODE_NONE = 0,
    CONNECT_MODE_SDMC,
    CONNECT_MODE_CLOUD,
} CONNECT_MODE;

typedef enum
{
    TRANSPORT_TYPE_TCP = 0,
    TRANSPORT_TYPE_UDP,
} TRANSPORT_TYPE;

#define MAX_UPGRADE_FILE_SIZE           (512*1024*1024)

typedef struct CLOUD_SERVER_INFO_T
{
#define CLOUDSERVER_ADDR_SIZE       24
#define CLOUDSERVER_TOKEN_SIZE      36
    char szGatewayAddr[CLOUDSERVER_ADDR_SIZE];      /*IP & Port*/
    char szAccessAddr[CLOUDSERVER_ADDR_SIZE];
    char szRestAddr[CLOUDSERVER_ADDR_SIZE];
    char szToken[CLOUDSERVER_TOKEN_SIZE];
} CLOUD_SERVER_INFO;

typedef struct CAPTURE_INFO_T
{
    unsigned int nTimeStamp;
    char szMac[MAC_SIZE];
    char ch[MAC_SIZE];
    char stm[MAC_SIZE];
    char szPicName[VALUE_SIZE];
    char capture_flow_uuid[64];
    int nSpsNalu;
    int nCaptureDataPos;
    int nCaptureState;
    unsigned char* pszGetCapture;
    //added by chenyc,2019-05-16,缓存一个包，根据实际的抓包情况，最多乱序一个包
    uint16_t last_seq_num;
    unsigned char last_pps_buffer[1500];
    int last_pps_buffer_len;
    int width;
    int height;
} CAPTURE_INFO;

typedef struct CAPTURE_FILE_DATA_T
{
    char szMac[MAC_SIZE];
    char szPicName[VALUE_SIZE];
    char flow_uuid[64];
    unsigned int nDataLen;
    unsigned char szData[CAPTURE_SIZE];
    int width;
    int height;
} CAPTURE_FILE_DATA;

typedef struct CSVRECORD_CONF_T
{
    char szCapturePath[VALUE_SIZE];
} CSVRECORD_CONF;


#endif



