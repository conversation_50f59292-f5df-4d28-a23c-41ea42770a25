#ifndef __VRECORD_MSG_COMMON_H__
#define __VRECORD_MSG_COMMON_H__
#pragma once

/*********************************************************************
*
* 这个文件定义的是WINDOWS端和LINUX端共用的宏定义和结构体,开发时一方定义即可
*
*********************************************************************/

#if defined(_WIN32)

#else

#define _T(str) str

#endif  // _WIN32

#ifndef IP_SIZE
#define IP_SIZE                         16
#endif

#ifndef MAC_SIZE
#define MAC_SIZE                        20
#endif

#ifndef VALUE_SIZE
#define VALUE_SIZE                      256
#endif

#ifndef INT_SIZE
#define INT_SIZE                        12
#endif

#define PROTOCAL_SIZE                   16
#define DEVICE_ID_SIZE                  24
#define DEVICE_TYPE_SIZE                24
#define DEVICE_STATUS_SIZE              16
#define DEVICE_SWVER_SIZE               16
#define DEVICE_HWVER_SIZE               32
#define USER_NAME_SIZE                  24

#define KEY_SIZE                        16
#define MSG_ID_SIZE                     8
#define MSG_CRC_SIZE                    8
#define ACK_RESULT_SIZE                 8
#define ACK_INFO_SIZE                   64

#define STRING_VALUE_SIZE               1024

#ifndef URL_SIZE
#define URL_SIZE                        512
#endif

#ifndef DATETIME_SIZE
#define DATETIME_SIZE                   24
#endif

#define PROTOCAL_NAME_DEFAULT           _T("1.0")

/*收到MULTICAST或TCP消息后通过该结构体转到主线程处理*/
typedef struct SOCKET_MSG_T
{
#define MAX_SOCKET_FRAME_SIZE       4096
    char szRemoteAddr[IP_SIZE];
    uint32_t nPort;
    uint32_t nSocketFd;
    uint32_t nSize;
    uint32_t nTransport;
    unsigned char byData[MAX_SOCKET_FRAME_SIZE];
} SOCKET_MSG;

#define SOCKET_MSG_VERSION_OFFSET   11

#define SOCKET_MSG_MAGIC_MSB        0xBC
#define SOCKET_MSG_MAGIC_LSB        0xDE
#define SOCKET_MSG_ID_MASK          0x07FF
#define SOCKET_MSG_VERSION_MASK     0x3800

#define MSG_TO_SEND_VIDEO_DATA          0x0001

typedef struct SOCKET_MSG_NORMAL_T
{
#define SOCKET_MSG_MAGIC_SIZE           2
#define SOCKET_MSG_DATA_SIZE            4096
#define SOCKET_MSG_NORMAL_HEADER_SIZE   10

    unsigned char byMagic[SOCKET_MSG_MAGIC_SIZE];
    uint16_t nCrc;
    uint16_t nMsgID;
    uint16_t nHeadSize;
    uint16_t nDataSize;
    unsigned char byData[SOCKET_MSG_DATA_SIZE];
} SOCKET_MSG_NORMAL;

typedef struct SOCKET_MSG_ACK_T
{
    uint32_t nSequenceNum;
    char szProtocal[PROTOCAL_SIZE];
    char szMsgID[MSG_ID_SIZE];
    char szMsgCRC[MSG_CRC_SIZE];
    char szResult[ACK_RESULT_SIZE];
    char szInfo[ACK_INFO_SIZE];
} SOCKET_MSG_ACK;

typedef struct SOCKET_MSG_HEARTBEAT_PERIOD_T
{
#define HEARTBEAT_PERIOD_SIZE       24
    char szProtocal[PROTOCAL_SIZE];
    char szExpire[HEARTBEAT_PERIOD_SIZE];
} SOCKET_MSG_HEARTBEAT_PERIOD;


#endif
