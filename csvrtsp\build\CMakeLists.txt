CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (rtsp C CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../src")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libcsbase.a pthread  libhiredis.a libevent.so libglog.so libmysqlclient.so libevpp.so libprotobuf.so libetcd-cpp-api.so libcpprest.so libboost_system.so libssl.so libcrypto.so libgpr.so libgrpc.so libgrpc++.so cppkafka
 librdkafka.so librdkafka++.so libz.so libdl.so aws-cpp-sdk-core aws-cpp-sdk-s3 libPcap++.a libPacket++.a libCommon++.a libpcap.a libZLToolKit.so libfdfsclient.so libfastcommon.so libwebrtc.a libalibabacloud-oss-cpp-sdk.a curl libglog.so)
 
SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../../)

LINK_DIRECTORIES(${BASE_SOURCE_DIR}/csbase 
${BASE_SOURCE_DIR}/csbase/thirdlib 
${BASE_SOURCE_DIR}/csbase/thirdlib/oss
${BASE_SOURCE_DIR}/csbase/redis/hiredis 
${BASE_SOURCE_DIR}/csbase/evpp/lib 
/usr/local/lib
${BASE_SOURCE_DIR}/csbase/thirdlib/PcapPlusPlus/lib
${BASE_SOURCE_DIR}/csbase/thirdlib/ZLMediaKit/lib
${BASE_SOURCE_DIR}/csbase/thirdlib/lib
)


AUX_SOURCE_DIRECTORY(../src/ SRC_LIST_RTSP)
AUX_SOURCE_DIRECTORY(../src/auth SRC_LIST_RTSP_AUTH)
AUX_SOURCE_DIRECTORY(../src/common SRC_LIST_RTSP_COMMON)
AUX_SOURCE_DIRECTORY(../src/model SRC_LIST_RTSP_MODEL)
AUX_SOURCE_DIRECTORY(../src/rtp SRC_LIST_RTSP_RTP)
AUX_SOURCE_DIRECTORY(../src/ZLMediaKit SRC_LIST_RTSP_ZLM)

AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/protobuf SRC_LIST_BASE_PROTOBUF)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/grpc SRC_LIST_BASE_GRPC)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/src/json SRC_LIST_BASE_JSONCPP)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/uploader SRC_LIST_BASE_UPLOADER)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/Tinyxml SRC_LIST_BASE_TINYXML)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/Character SRC_LIST_BASE_CHARACTER)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${BASE_SOURCE_DIR}/csbase/loop SRC_LIST_BASE_LOOP)

SET(BASE_LIST_INC ../include/ZLMediaKit 
				  ../include/glog 
				  ../include/ipc 
				  ../include/libvrtspd 
				  ../src/auth
				  ../src/common
				  ../src/model
				  ../src/rtp
				  ../src/
                   ${BASE_SOURCE_DIR}/csbase 
                   ${BASE_SOURCE_DIR}/csbase/mysql/include 
				   ${BASE_SOURCE_DIR}/csbase/Rldb 
                   ${BASE_SOURCE_DIR}/csbase/evpp 
				   ${BASE_SOURCE_DIR}/csbase/protobuf 
				   ${BASE_SOURCE_DIR}/csbase/encrypt 
				   ${BASE_SOURCE_DIR}/csbase/etcd 
				   ${BASE_SOURCE_DIR}/csbase/redis 
				   ${BASE_SOURCE_DIR}/csbase/grpc
                   ${BASE_SOURCE_DIR}/csbase/jsoncpp0.5/include 
				   ${BASE_SOURCE_DIR}/csbase/grpc/gens
                   ${BASE_SOURCE_DIR}/csbase/gid
				   ${BASE_SOURCE_DIR}/csbase/webrtc
				   ${BASE_SOURCE_DIR}/csbase/webrtc/third_party/abseil-cpp
				   ${BASE_SOURCE_DIR}/csbase/fdfs_client/fdfsclient
				   ${BASE_SOURCE_DIR}/csbase/fdfs_client/libfdfscomm
				   ${BASE_SOURCE_DIR}/csbase/thirdlib/ZLMediaKit/include
				   ${BASE_SOURCE_DIR}/csbase/thirdlib/PcapPlusPlus/include/Dist
				   ${BASE_SOURCE_DIR}/csbase/uploader
				   ${BASE_SOURCE_DIR}/csbase/dbinterface
				   ${BASE_SOURCE_DIR}/csbase/dbinterface/Log
				   ${BASE_SOURCE_DIR}/csbase/oss/include
				   ${BASE_SOURCE_DIR}/csbase/metrics
				   )

ADD_DEFINITIONS(-g -Werror -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON -DWEBRTC_POSIX
-DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -Wno-unused-parameter -Wno-deprecated
-Wno-implicit-fallthrough)
                       
# C++特定的编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -Wno-class-memaccess")

# C特定的编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")

include_directories( ../include  ${BASE_LIST_INC} /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include)

add_executable(csvrtspd
${SRC_LIST_RTSP} 
${SRC_LIST_RTSP_AUTH} 
${SRC_LIST_RTSP_COMMON} 
${SRC_LIST_RTSP_MODEL} 
${SRC_LIST_RTSP_RTP} 
${SRC_LIST_RTSP_ZLM}
${SRC_LIST_BASE_RLDB} 
${SRC_LIST_BASE_REDIS} 
${SRC_LIST_BASE_PROTOBUF} 
${SRC_LIST_BASE_ETCD} 
${SRC_LIST_BASE_REDIS}  
${SRC_LIST_BASE_GRPC}  
${SRC_LIST_BASE_JSONCPP} 
${SRC_LIST_BASE_ENCRYPT}
${SRC_LIST_BASE_TINYXML} 
${SRC_LIST_BASE_CHARACTER}
${SRC_LIST_BASE_METRICS}
${SRC_LIST_BASE_LOOP}  
${prefixed_file_list}
${BASE_SOURCE_DIR}/csbase/uploader/fdfs_uploader.cpp)

SET(EXECUTABLE_OUTPUT_PATH ${BASE_SOURCE_DIR}/csvrtsp/release/bin)
set_target_properties(csvrtspd PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csvrtsp/lib")

target_link_libraries(csvrtspd  ${DEPENDENT_LIBRARIES})
