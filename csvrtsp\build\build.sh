#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../

AKCS_SRC_BIN_DIR=${AKCS_SRC_ROOT}/csvrtsp
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_BIN_RECORD_DIR=${AKCS_SRC_ROOT}/csvrecord

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=akcs_csvrtsp_packeg
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/${AKCS_PACKAGE_NAME}
AKCS_PACKAGE_ROOT_DEBUG=${AKCS_SRC_ROOT}/${AKCS_PACKAGE_NAME}_debug

build() {

    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi

	mkdir -p $AKCS_PACKAGE_ROOT/csvrecord
	mkdir -p $AKCS_PACKAGE_ROOT/csvrtsp

	if [ -d $AKCS_PACKAGE_ROOT_DEBUG ]
	then
		rm -rf $AKCS_PACKAGE_ROOT_DEBUG
	fi

	mkdir -p $AKCS_PACKAGE_ROOT_DEBUG/csvrecord
	mkdir -p $AKCS_PACKAGE_ROOT_DEBUG/csvrtsp

    #create protobuf
    cd $AKCS_SRC_ROOT/csbp/proto || exit 1
    bash create_proto_ubuntu20.sh 
	
	cd $AKCS_SRC_BIN_DIR/build
	bash build_only_rtsp.sh build
    if [ $? -eq 0 ]; then
        echo "make rtsp successed";
    else
        echo "make rtsp failed";
        exit 1;
    fi    

	cd $AKCS_SRC_BIN_RECORD_DIR/build
	bash build.sh build
    if [ $? -eq 0 ]; then
        echo "make record successed";
    else
        echo "make record failed";
        exit 1;
    fi    

	cp -rf $AKCS_SRC_ROOT/akcs_csvrecord_packeg/*    $AKCS_PACKAGE_ROOT/csvrecord
	cp -rf $AKCS_SRC_ROOT/akcs_csvrtsp_only_packeg/* $AKCS_PACKAGE_ROOT/csvrtsp

    cp -rf $AKCS_SRC_BIN_DIR/shell/Dockerfile $AKCS_PACKAGE_ROOT
    cp -rf $AKCS_SRC_BIN_DIR/shell/Dockerfile_debug $AKCS_PACKAGE_ROOT_DEBUG/Dockerfile

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_SRC_ROOT} || exit 1
    rm -rf ${AKCS_PACKAGE_NAME}.tar.gz
	chmod -R 755 ${AKCS_PACKAGE_ROOT}
    tar zcvf ${AKCS_PACKAGE_NAME}.tar.gz ${AKCS_PACKAGE_NAME}
    echo "${AKCS_PACKAGE_ROOT}.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSBASE || exit 1
	cmake ./
    make clean

	cd $AKCS_SRC_BIN_DIR/build || exit 1
	cmake ./
    make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csadapt application, eg : $0 clean "
    echo "  $0 build ---  build csadapt application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac

