#ifndef __PCAP_CONTROL_H__
#define __PCAP_CONTROL_H__

#include <set>
#include <thread>
#include <mutex>
#include <map>
#include <memory>
#include <condition_variable>
#include "thirdlib/PcapPlusPlus/include/Dist/Device.h"
#include "thirdlib/PcapPlusPlus/include/Dist/Layer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/UdpLayer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/TcpLayer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/IPv4Layer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/IPv6Layer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PcapFileDevice.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PcapLiveDeviceList.h"
#include "thirdlib/PcapPlusPlus/include/Dist/SystemUtils.h"
#include "thirdlib/PcapPlusPlus/include/Dist/RawPacket.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PointerVector.h"
#include "PcapUdp.h"
#include "PcapWriter.h"

struct PacketStats
{
    int ethPacketCount;
    int ipv4PacketCount;
    int ipv6PacketCount;
    int tcpPacketCount;
    int udpPacketCount;
    int dnsPacketCount;
    int httpPacketCount;
    int sslPacketCount;
};

struct AddressInfo 
{
    bool is_ipv6;
    char src_ip[64];
    char dst_ip[64];
    unsigned short src_port;
    unsigned short dst_port;
};

class PcapCaptureControl
{
public:
    PcapCaptureControl();
    ~PcapCaptureControl(); 

    static PcapCaptureControl *GetInstance();
    static std::string CreateCaptureUUID(const std::string &ip, int port)
    {
        std::string uuid = ip + "_" + std::to_string(port);
        return uuid;
    }

    // 线程初始化
    int Init();

    // 循环监听554端口
    void PcapCaptureController();

    // 监听554端口
    int OpenMainWorkerDevLive();

    // web通知开启抓包
    void WebPcapCaptureStart(const std::string& capture_mac, const std::string& file_uuid);

    // web通知停止抓包
    void WebPcapCaptureStop(const std::string& capture_mac, const std::string& file_uuid);

    // 判断rtsp_payload中是否有正在抓包的mac
    bool IsMacBeingCaptured(const std::string& rtsp_payload, std::string& mac);
    
    // 判断uuid是否存在
    bool IsUUIDExist(const std::string& src_uuid, const std::string& dst_uuid);

    // rtsp_payload中有正在抓包的mac,创建PcapWriter对象
    void StartTcpCapture(const std::string &uuid, const std::string &mac);

    // udp端口分配后,创建PcapCaptureUdp对象
    void OnUdpPortDispatch(const std::string &uuid, unsigned short app_port, unsigned short dev_port);

    // 收到finack,监控停止
    void OnCaptureStop(const std::string &uuid);
    
    // 将uuid对应的包写入pcap
    void WritePacket(const std::string& uuid, pcpp::RawPacket* packet);

    void CheckCaptureTimeout();

private:
    // 抓包设备的mac_list
    std::set<std::string> capture_mac_set_;
    std::mutex capture_mac_mutex_;

    // key为mac,value为actionuuid
    std::map<std::string, std::string> mac_actionuuid_map_;
    std::mutex mac_actionuuid_map_mutex_;
    
    // mac:uuid
    std::map<std::string, std::string> mac_uuid_map_; 
    std::mutex mac_uuid_map_mutex_;

    // uuid对应writer类
    // tcp和udp包统一写到这里
    std::map<std::string, std::shared_ptr<PcapWriter>> writer_;
    std::mutex writer_mutex_;

    // uuid对应的udp抓包类
    std::map<std::string, std::shared_ptr<PcapCaptureUdp>> udp_capture_; 
    std::mutex udp_capture_mutex_;

    // listen 554 port
    pcpp::PcapLiveDevice* pcap_live_rtsp_;
    PacketStats pcap_live_rtsp_stats_;

    std::thread pcap_capture_control_thread_;
    
    static PcapCaptureControl* instance_;
};

PcapCaptureControl* GetPcapCaptureControlInstance();
#endif //__PCAP_CAPTURE_HANDLE_H__

