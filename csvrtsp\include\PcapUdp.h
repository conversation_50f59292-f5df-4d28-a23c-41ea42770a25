#ifndef __PCAP_UDP_H_
#define __PCAP_UDP_H_

#include <map>
#include <set>
#include <mutex>
#include <thread>
#include <vector>
#include <memory>
#include <condition_variable>
#include "thirdlib/PcapPlusPlus/include/Dist/Device.h"
#include "thirdlib/PcapPlusPlus/include/Dist/Layer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/UdpLayer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/TcpLayer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/IPv4Layer.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PcapFileDevice.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PcapLiveDeviceList.h"
#include "thirdlib/PcapPlusPlus/include/Dist/SystemUtils.h"
#include "thirdlib/PcapPlusPlus/include/Dist/RawPacket.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PointerVector.h"

class PcapCaptureUdp
{
public:
    PcapCaptureUdp();
    PcapCaptureUdp(const std::string& uuid, unsigned short app_udp_port,unsigned dev_udp_port)
    {
        uuid_ = uuid;
        app_udp_port_ = app_udp_port;        
        dev_udp_port_ = dev_udp_port;
    }

    ~PcapCaptureUdp();
    
    void Start();
    void Stop();
    
private:
    std::string uuid_;
    unsigned short app_udp_port_;
    unsigned short dev_udp_port_;
    std::shared_ptr<pcpp::PcapLiveDevice> pcap_live_udp_;
};








#endif
