#ifndef __PCAP_WRITER_H__
#define __PCAP_WRITER_H__

#include <mutex>
#include <memory>
#include "thirdlib/PcapPlusPlus/include/Dist/RawPacket.h"
#include "thirdlib/PcapPlusPlus/include/Dist/PcapFileDevice.h"


class PcapWriter
{
public:
    PcapWriter();
    PcapWriter(const std::shared_ptr<pcpp::PcapFileWriterDevice> pcap_writer, const std::string& local_filepath, 
        const std::string& mac, const std::time_t& timestamp);
    ~PcapWriter(); 

    void WritePacket(pcpp::RawPacket* packet);
    std::string GetMac();
    std::time_t GetTimestamp();
    std::string GetLocalFilePath();
    void Stop();
    
private:
    std::string action_uuid_;
    std::string local_filepath_;
    std::string mac_;
    std::time_t timestamp_;
    std::shared_ptr<pcpp::PcapFileWriterDevice> pcap_writer_;
    std::mutex mutex_;
};

#endif
