#ifndef __RTSP_SESSION_H__
#define __RTSP_SESSION_H__

#include <thread>
#include <mutex>
#include <memory>
#include <ctime>
#include <condition_variable>
#include "Parser.h"
#include "RtspClient.h"
#include "RtspSplitter.h"
#include "DigestAuthentication.h"
#include "thirdlib/ZLMediaKit/include/Util/onceToken.h"
#include "thirdlib/ZLMediaKit/include/Network/Session.h"
#include "thirdlib/ZLMediaKit/include/Network/Socket.h"
#include "thirdlib/ZLMediaKit/include/Network/Buffer.h"
#include "thirdlib/ZLMediaKit/include/Network/TcpServer.h"
#include "dbinterface/resident/ResidentDevices.h"

using namespace toolkit;
using namespace mediakit;

namespace akuvox 
{
    class RtspClient;
}

namespace mediakit {
    
class RtspSession : public Session, public RtspSplitter
{
public:
    RtspSession(const toolkit::Socket::Ptr &sock);
    ~RtspSession();
     
    ////Session override////
    void onRecv(const toolkit::Buffer::Ptr &buf) override;
    void onError(const toolkit::SockException &err) override;
    void onManager() override;
    
protected:
    /////RtspSplitter override/////
    //收到完整的rtsp包回调，包括sdp等content数据
    void onWholeRtspPacket(Parser &parser) override;
    
    //收到rtp包回调
    void onRtpPacket(const char *data, size_t len) override;
    
    //从rtsp头中获取Content长度
    ssize_t getContentLength(Parser &parser) override;

    /////Session override////
    ssize_t send(toolkit::Buffer::Ptr pkt) override;
    
    //收到RTCP包回调
    virtual void onRtcpPacket();

    //回复客户端
    virtual bool sendRtspResponse(const std::string &res_code, const StrCaseMap &header = StrCaseMap(), const std::string &sdp = "", const char *protocol = "RTSP/1.0");
private:
    //处理options方法,获取服务器能力
    void HandleReqOptions(const Parser &parser);
    
    //处理describe方法，请求服务器rtsp sdp信息
    void HandleReqDescribe(const Parser &parser);
    
    //处理setup方法，播放和推流协商rtp传输方式用
    void HandleReqSetup(const Parser &parser);
    
    //处理play方法，开始或恢复播放
    void HandleReqPlay(const Parser &parser);
    
    //处理SET_PARAMETER、GET_PARAMETER方法，一般用于心跳
    void HandleReqGetParameter(const Parser &parser);
    
    //处理teardown方法，结束播放
    void HandleReqTeardown(const Parser &parser);
    
    //回复客户端
    bool sendRtspResponse(const std::string &res_code, const std::initializer_list<std::string> &header, const std::string &sdp = "", const char *protocol = "RTSP/1.0");
    
    //摘要认证
    bool OnAuthDigest(std::shared_ptr<akuvox::RtspClient> &app_rtsp_client, const std::string &authorization);
    
    //认证成功
    void OnAuthSuccess(std::shared_ptr<akuvox::RtspClient> &app_rtsp_client);
    
    //认证失败
    void OnAuthFailed(std::shared_ptr<akuvox::RtspClient> &app_rtsp_client);
private:    
    //获取监控设备/三方摄像头信息
    bool GetDeviceInfo(std::shared_ptr<akuvox::RtspClient> &app_rtsp_client, const std::string &username);
    //判断转流开关
    bool IsDevMonitorNeedRepost(const ResidentDev &dev);

    // 协商srtp key,多个app同时监控需要使用下相同的srtpKey和ssrc
    void NegotiateSrtpEncryptInfo(std::shared_ptr<akuvox::RtspClient> &rtsp_client);

    //获取rtspclient
    std::shared_ptr<akuvox::RtspClient> GetRtspClient();

    //资源异常断开连接
    void HandleDisconnect(const std::string& error_msg);
    
private:
    // 客户端fd
    int fd_;

    // 客户端是否为ipv6
    bool peer_is_ipv6_;

    // 客户端ip
    std::string peer_ip_;

    // 客户端port
    unsigned short peer_port_;

    // rtsp服务端port, 554/8601/8602
    unsigned short local_port_;

    // 收到的seq,回复时一致
    int cseq_;
    
    // session号
    std::string session_id_;

    // 关联一次监控的所有资源
    uint64_t trace_id_;
    
    // 摘要认证对象
    Authenticator authenticator_;
    
    // rtsp摘要认证失败的次数,超过3次就认为是恶意攻击
    std::atomic<uint32_t> auth_failed_times_;

    // accept客户端的时间
    std::time_t accept_time_;

    // 客户端keepalive的时间
    std::time_t keepalive_time_;

    // 是否已经调用过close
    bool call_shutdown_;
};


}


#endif //__RTSP_SESSION_H__
