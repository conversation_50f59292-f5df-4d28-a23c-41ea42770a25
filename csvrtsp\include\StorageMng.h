#ifndef __PCAP_SHADOW_MNG_H__
#define __PCAP_SHADOW_MNG_H__

#include <string>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "connection_pool.h"
#include "fdfs_client/libfdfscomm/sockopt.h"
#include "fdfs_client/libfdfscomm/base64.h"
#include "fdfs_client/fdfsclient/fdfs_client.h"
#include "fdfs_client/fdfsclient/fdfs_global.h"
#include "fdfs_client/fdfsclient/fdfs_http_shared.h"
#include "dbinterface/PcapCaptureControl.h"
#include "fdfs_uploader.h"

#define LOCAL_FILE_KEY "local_filepath" //本地文件(未上传fdfs前的)路径
#define FDFS_LOG_LEVEL 7

class CShadowMng : public boost::noncopyable
{
public:
    CShadowMng();
    ~CShadowMng();    
    static CShadowMng* GetInstance();
    int StorePcapCaptureFile(const char* local_filepath, const std::string& action_uuid);

private:
    const char* tag_;
    static CShadowMng* instance_;
    std::unique_ptr<FdfsUploader> uploader_;
};

CShadowMng* GetShadowMngInstance();

#endif //__PCAP_SHADOW_MNG_H__

