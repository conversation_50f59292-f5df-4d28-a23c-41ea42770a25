
#ifndef __VRTSP_IPC_H__
#define __VRTSP_IPC_H__

#define MSG_VRTSP2C                 0xA000
#define MSG_C2VRTSP                 0xA100
#define MSG_VRTSP2VRECORD           0xA200
#define MSG_VRECORD2VRTSP           0xA300
#define MSG_VRECORD2VIESTORAGE      0xA400
#define MSG_VIESTORAGE2VRECORD      0xA500

#ifndef TRUE
#define TRUE    1
#endif

#ifndef FALSE
#define FALSE   0
#endif

#ifndef RL_TRUE
#define RL_TRUE 1
#endif

#ifndef RL_FALSE
#define RL_FALSE    0
#endif

#ifndef BOOL
typedef int                 BOOL;
#endif

#ifndef USER_SIZE
#define USER_SIZE                       32
#endif

typedef struct MESSAGE_T
{
    unsigned int id;
    unsigned long w_param;
    unsigned long l_param;
    void* l_data;
    struct MESSAGE_T* next;
} MESSAGE;

enum
{
    IPC_ID_MIN = 0,
    IPC_ID_CLOUD,
    IPC_ID_VRTPSD,
    IPC_ID_VRECORD,
    IPC_ID_VIESTORAGE, //跟视频存储服务器的unix_socket交互
    IPC_ID_MAX,
};

enum
{

    /** post when vrtsp start rtsp
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- NULL
      *
     */
    MSG_VRTSP2C_START_RTSP = MSG_VRTSP2C + 1,

    /** post when vrtsp stop rtsp
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- NULL
      *
     */
    MSG_VRTSP2C_STOP_RTSP,

    /** post vrtsp keep rtsp alive
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- NULL
      *
     */
    MSG_VRTSP2C_KEEP_RTSP,

};

enum
{
    /** post when cloud notify snapshot
    *
    *@param param1 -- NULL
    *@param param2 -- NULL
    *@param l_data -- file, mac
    *
    */
    MSG_C2VRTSP_CAPTURE_RTSP = MSG_C2VRTSP + 1,

    /** post when cloud stop snapshot
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- file, mac
      *
     */
    MSG_C2VRTSP_STOP_CAPTURE,
};

enum
{

    /** post when vrtsp send data
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- NULL
      *
     */
    MSG_VRTSP2VRECORD_SEND_CAPTURE_DATA = MSG_VRTSP2VRECORD + 1,

    /** post when vrtsp start capture
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- capture param
      *
     */
    MSG_VRTSP2VRECORD_START_CAPTURE,

    /** send start record
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- record data
      *
     */
    MSG_VRTSP2VRECORD_START_RECORD,

};

enum
{

    /** post when vrecord stop capture
      *
      *@param param1 -- NULL
      *@param param2 -- NULL
      *@param l_data -- NULL
      *
     */
    MSG_VRECORD2VRTSP_STOP_CAPTURE = MSG_VRECORD2VRTSP + 1,

};

typedef struct SOCKET_MSG_KEEP_RTSP_T
{
#define RTSP_DATA_MAC_SIZE 16
    char mac[RTSP_DATA_MAC_SIZE];
} SOCKET_MSG_KEEP_RTSP;

typedef struct SOCKET_MSG_CAPTURE_RTSP_T
{
#define RTSP_DATA_MAC_SIZE  16
#define PIC_NAME_SIZE       256
    char mac[RTSP_DATA_MAC_SIZE];
    char szPicName[PIC_NAME_SIZE]; //截图的图片名称
    char szNode[USER_SIZE];
    char szUserName[USER_SIZE];//谁截的图
    char flow_uuid[64];
} SOCKET_MSG_CAPTURE_RTSP;

typedef struct SOCKET_MSG_VIDEO_DATA_T
{
#define VIDEO_DATA_MAC_SIZE 16
#define PIC_NAME_SIZE       256
#define VIDEO_DATA_SIZE     1500
    char mac[VIDEO_DATA_MAC_SIZE];
    char szPicName[PIC_NAME_SIZE];
    unsigned int data_len;
    unsigned char szData[VIDEO_DATA_SIZE];
    char flow_uuid[64];
} SOCKET_MSG_VIDEO_DATA;

#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    int len;
    int id;
    int from;  /*信息发端进程的ID*/
    int param1;
    int param2;

#define IPC_MSG_HEADER_SIZE 20
#define IPC_MSG_DATA_SIZE   2048
#define IPC_MSG_MAX_SIZE    (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)
    unsigned char data[IPC_MSG_DATA_SIZE];
} UNIX_IPC_MSG;

typedef struct
{
    int len;
    int id;
    int from;  /*信息发端进程的ID*/
    int param1;
    int param2;

#define IPC_MSG_HEADER_SIZE     20
#define IPC_MSG_DATA_SIZE       2048
#define IPC_MSG_MAX_SIZE        (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)

    unsigned char data[IPC_MSG_DATA_SIZE];
} IPC_MSG;


void ipc_show_version();

/**
 * initialize IPC module
 */
void ipc_init();

/**
 * register a process to IPC manager
 *
 * @param[in] my_id -- process ID
 *
 * @return 0 on success, others on failed.
 */
int ipc_register(int my_id);

/**
 * thread function
 *
 * @param[in] listen_fd -- socket listen ID
 *
 * @return 0 on success, others on failed.
 */
void* read_thread(int *listen_fd);

typedef void (*IPC_MSG_HANDLE)(UNIX_IPC_MSG* msg, void* data);

/**
 * run a process to IPC listen
 *
 * @param[in] msg_handle -- message process handle
 * @param[in] data -- private data
 *
 * @return 0 on success, others on failed.
 */
int ipc_run(IPC_MSG_HANDLE msg_handle, void* data);

/**
 * unregister a process to IPC manager
 *
 * @param[in] id -- process ID name
 *
 * @return 0 on success, others on failed.
 */
int ipc_unregister();

/**
 * send a ipc message
 *
 * @param[in] dest_id -- destination process ID
 * @param[in] id -- ipc message id
 * @param[in]param1 – first param  for ipc message
 * @param[in]param2 – second param for ipc message
 * @param[in]dat – extern data ptr
 * @param[in]dat_len – extern data size
 *
 * @return 0 on success, others on failed.
 */
int ipc_send(int dest_id, int id, int param1, int param2, void* dat, int dat_len);

/**
 * get the quit flag to see if we need to quit the process
 *
 * @param[in] none
 *
 * @return the quitflag. RL_TRUE if needs to quit. RL_FALSE if not.
 */
BOOL get_quitflag();

/**
 * set the quit flag. RL_TRUE means quit; RL_FALSE means not
 *
 * @param[in] quit flag
 *
 * @return none.
 */
void set_quitflag(BOOL quitflag);

#ifdef __cplusplus
}
#endif

#endif //#ifndef __VRTSP_IPC_H__


