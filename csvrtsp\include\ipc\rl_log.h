/** @file log.h
 * @brief log interface
 * @date 2012-09-18
 * @note
 *
 *
 * Copyright(c) 2012-2020 Xiamen Ringslink telenology Co,.Ltd
 */

#ifndef __RL_LOG_H__
#define __RL_LOG_H__

#ifdef __cplusplus
extern "C" {
#endif

enum LOG_LEVEL
{
    IPC_LOG_LEVEL_EMERG = 0x00,
    IPC_LOG_LEVEL_ALERT,
    IPC_LOG_LEVEL_CRIT,
    IPC_LOG_LEVEL_ERR,
    IPC_LOG_LEVEL_WARNING,
    IPC_LOG_LEVEL_NOTICE,
    IPC_LOG_LEVEL_INFO,
    IPC_LOG_LEVEL_DEBUG
};

// 对于每个进程只初始化一次
/**
* function descriptions: log initialize before use log
*
* @param[in] module_name < process module name identify.>
* @param[in] log_level < process log level.>
*
* @return 0 on success, others on failed.
*/
int rl_log_init(const char* module_name, int in_log_level);


/**
* function descriptions: log deinitialize to release resource
*
* @return 0 on success, others on failed.
*/
void rl_log_deinit();

/**
* function descriptions: reset log level of process.
* @param[in] log_level < process log level.>
*/
void rl_log_reset_level(int log_level);

/**
* function descriptions: logging for debug
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_debug(const char* fmt, ...);


/**
* function descriptions: logging for info
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_info(const char* fmt, ...);


/**
* function descriptions: logging for notice
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_notice(const char* fmt, ...);



/**
* function descriptions: logging for warn
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_warn(const char* fmt, ...);


/**
* function descriptions: logging for error
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_err(const char* fmt, ...);


/**
* function descriptions: logging for crit
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_crit(const char* fmt, ...);


/**
* function descriptions: logging for alert
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_alert(const char* fmt, ...);


/**
* function descriptions: logging for emergency
*
* @param[in] fmt < format log string.>
* @param[in] ... < params of variable numbers.>
*
* @return 0 on success, others on failed.
*/
void rl_log_emerg(const char* fmt, ...);

#ifdef __cplusplus
}
#endif

#endif



