#ifndef __CONTROL_H__
#define __CONTROL_H__

#pragma once

#include <pthread.h>

class CControl
{
public:
    CControl();
    ~CControl();

    static CControl* GetInstance();

    //初始化
    int Init();

    //运行
    int Run();

    //处理消息
    int ProcessMsg();
    //增加一个新的消息
    int AddMsg(unsigned int id, unsigned int w_param, unsigned int l_param, void* l_data, int data_len);
    //删除所有消息
    int DelAllMsg();

    //消息处理句柄
    int OnMessage(unsigned int msg, unsigned int w_param, unsigned int l_param, void* l_data);


    //控制消息处理句柄
    int OnCtrl(unsigned int msg, unsigned int w_param, unsigned int l_param, void* l_data);

    //定时器消息处理
    int OnTimer(unsigned int id_event);

private:
    void Lock();
    void Unlock();
    void SetWaitEvent();
    void ResetWaitEvent();
    void WaitForEvent();

    static CControl* instance;
    pthread_t m_tidTimer;
    const char* tag_;

    void* msg_header_;
    void* m_lock;
    void* m_wait;
};

CControl* GetControlInstance();

#endif
