#ifndef __CSVRTSP_CONF_H__
#define __CSVRTSP_CONF_H__

#define CSVRTSP_CONF_COMMON_LEN 64

typedef struct CSVRTSP_CONF_T
{
    /* csvrtsp本机配置信息 */
    char csvrtsp_outer_ip[CSVRTSP_CONF_COMMON_LEN];
    char csvrtsp_outer_domain[CSVRTSP_CONF_COMMON_LEN];
    char csvrtsp_outer_ipv6[CSVRTSP_CONF_COMMON_LEN];
    char csvrtsp_inner_ip[CSVRTSP_CONF_COMMON_LEN];
    int log_level; //日志打印级别 LOG_LEVEL_E

    /* csgate信息 */
    char csgate_ip[CSVRTSP_CONF_COMMON_LEN];
    char csgate_port[CSVRTSP_CONF_COMMON_LEN];

    /* DB配置项 */
    char db_ip[CSVRTSP_CONF_COMMON_LEN];
    char db_username[CSVRTSP_CONF_COMMON_LEN];
    char db_password[CSVRTSP_CONF_COMMON_LEN];
    char db_database[CSVRTSP_CONF_COMMON_LEN];
    int  db_port;
    char nsq_topic[CSVRTSP_CONF_COMMON_LEN];

    char etcd_server_addr[CSVRTSP_CONF_COMMON_LEN];
    /*svn版本号*/
    char svn_version[CSVRTSP_CONF_COMMON_LEN];
    int keep_alive_times;

    char monitor_pic_cap_rootpath[CSVRTSP_CONF_COMMON_LEN];
    char monitor_mac_list[CSVRTSP_CONF_COMMON_LEN];

    char capture_mac[CSVRTSP_CONF_COMMON_LEN];

    char group_name[16];
    int reg_etcd;

    int timer_heartbeat_time;
    int timer_monitor_timeout;
    int timer_inner_keepalive_time;
    int timer_pcap_timeout;
    int timer_rtp_packet_timeout;

    // 请求统计开关
    int request_statics_switch;

    char monitor_ip_list[256]; //监控的ip 进行一些日志打印

    int rtp_process_thread_num;
} CSVRTSP_CONF;

#endif //__CSVRTSP_CONF_H__

