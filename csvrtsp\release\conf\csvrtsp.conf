#csvrtsp conf
csvrtsp_outerip=*************
csvrtsp_outer_domain=dev.akuvox.com
csvrtsp_outeripv6=2001:470:1f28:d5::2
csvrtsp_port=554

#db conf
db_ip=127.0.0.1
db_username=dbuser01
db_database=AKCS
db_port=3306

#nsq conf
nsq_route_topic=ak_route

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#保活次数控制，0时候为不限制
keep_alive_times=15

#svn版本号
svn_version=

#是否注册到etcd
reg_etcd=1

#pcap fdfs group
group_name=group1

#发送keepalive给设备
timer_heartbeat_time=30

#允许客户端的最大监控时长
timer_monitor_timeout=300

#转流服务器超时校验时长
timer_inner_keepalive_time=5

#抓包最大时长
timer_pcap_timeout=60

#设备流断开时长
timer_rtp_packet_timeout=5

log_encrypt=0
log_trace=1

#请求统计开关
request_statics_switch=1

#临时添加监控的日志，看收到包是否有转发。配置欧洲和美国的运维监控地址
monitor_ip_list=************,************

#rtp转发线程个数
rtp_process_thread_num=3
