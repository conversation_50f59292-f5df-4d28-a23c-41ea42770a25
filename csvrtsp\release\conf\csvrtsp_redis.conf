#configure for rtsp auth

csvrtspCacheInstances=rtspnonce,mac_vrtspsid,backend_limiting
# rtsp-app是否mac的nonce,过期时间为一天
rtspnonce_host=127.0.0.1
rtspnonce_port=8504
rtspnonce_db=4
rtspnonce_maxconncnt=2

# mac_vrtspsid: mac跟vrtspd_logic_id的关系
mac_vrtspsid_host=127.0.0.1
mac_vrtspsid_port=8504
mac_vrtspsid_db=9
mac_vrtspsid_maxconncnt=4

# backend_limiting: 限流
backend_limiting_host=127.0.0.1
backend_limiting_port=8504
backend_limiting_db=24
backend_limiting_maxconncnt=2

#如果sentinels有值,代表启动主从，那么_host的配置就不生效，如果没有就是单机
sentinels=