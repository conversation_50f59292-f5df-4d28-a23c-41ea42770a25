#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-15
# Filename      :   install.sh
# Version       :
# Description   :   csvrtsp 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IS_REG_ETCD=$3             #是否注册到etcd

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csvrtspd    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csvrtsp
LOG_PATH=/var/log/csvrtsplog
PCAP_PATH=/usr/local/akcs/csvrtsp/pcap
TLS_PATH=/usr/local/akcs/csvrtsp/tls
CTRL_SCRIPT=csvrtspctl.sh
RUN_SCRIPT=csvrtsprun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}


# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

SERVERIP=$(grep_conf 'SERVERIP' $IP_FILE)
SERVERIPV6=$(grep_conf 'SERVERIPV6' $IP_FILE)

VRTSP_SERVER_DOMAIN=$(grep_conf 'VRTSP_SERVER_DOMAIN' $INSTALL_CONF)
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
GROUP_NAME=$(grep_conf 'GROUP_NAME' $INSTALL_CONF)

# 防止出现 domain 为空的情况
if [ -z "$VRTSP_SERVER_DOMAIN" ]; then
    echo "Please input your csvrtsp Domain."
    exit 1;
fi


# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
app_pids=$(pidof "$APP_NAME" || true)
if [ -n "$app_pids" ]; then
    kill -s $SIGNAL $app_pids
    sleep 2
fi


# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*csvrtsp_outeripv6=.*/csvrtsp_outeripv6=${SERVERIPV6}/g
    s/^.*csvrtsp_outerip=.*/csvrtsp_outerip=${SERVERIP}/g
    s/^.*csvrtsp_outer_domain=.*/csvrtsp_outer_domain=${VRTSP_SERVER_DOMAIN}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*reg_etcd=.*/reg_etcd=${IS_REG_ETCD}/g" "$PKG_ROOT"/conf/csvrtsp.conf

sed -i "s/^.*rtspnonce_host=.*/rtspnonce_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csvrtsp_redis.conf
sed -i "s/^.*mac_vrtspsid_host=.*/mac_vrtspsid_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csvrtsp_redis.conf
sed -i "s/^.*backend_limiting_host=.*/backend_limiting_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csvrtsp_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csvrtsp_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csvrtsp_redis.conf
fi

# FastDFS 配置
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" "$PKG_ROOT"/conf/csvrtsp_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" "$PKG_ROOT"/conf/csvrtsp_fdfs.conf
fi

if [ -n "${GROUP_NAME}" ];then
	sed -i "s/^.*group_name=.*/group_name=${GROUP_NAME}/g" "$PKG_ROOT"/conf/csvrtsp.conf
fi

# dbproxy 配置
if [ "$ENABLE_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csvrtsp.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csvrtsp.conf
fi


echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "创建存放Pcap的文件夹 $PCAP_PATH"
if [ ! -d $PCAP_PATH ]; then
    mkdir -p $PCAP_PATH
fi

echo "创建存放CA的文件夹 $TLS_PATH"
if [ ! -d $TLS_PATH ]; then
    mkdir -p $TLS_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

if [ -d /usr/local/akcs/csvrtsp_scripts ]; then
    rm -rf /usr/local/akcs/csvrtsp_scripts
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME
cp -rf "$PKG_ROOT"/tls $TLS_PATH
cp -f "$PKG_ROOT"/version $APP_HOME

if [ ! -d /usr/local/akcs/csvrtsp/pcap ]; then
    mkdir -p /usr/local/akcs/csvrtsp/pcap
fi

cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi
cd "$PKG_ROOT"

# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts


echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi

if [ ! -d /var/log/csvrtsplog ];then
    mkdir -p /var/log/csvrtsplog
fi

# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi

ulimit -c unlimited


echo '启动服务'
$APP_HOME/scripts/$CTRL_SCRIPT start
sleep 2

echo '检查服务的运行状态'
$APP_HOME/scripts/$CTRL_SCRIPT status

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi

echo "$APP_NAME install complete."

