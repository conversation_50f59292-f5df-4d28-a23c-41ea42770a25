#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IS_REG_ETCD=$3             #是否注册到etcd
DOCKER_IMG=$4
CONTAINER_NAME=csvrtsp

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
APP_NAME=csvrtsp   # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csvrtsp
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
LOG_PATH=/var/log/csvrtsplog
PCAP_PATH=/usr/local/akcs/csvrtsp/pcap
TLS_PATH=/usr/local/akcs/csvrtsp/tls
RUN_SCRIPT=csvrtsprun.sh
SIGNAL=${SIGNAL:-TERM}

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------

echo "Begin to install $APP_NAME."

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

echo "拷贝运维脚本"
mkdir -p $APP_HOME/scripts
cp -rf "$PKG_ROOT"/scripts/* $APP_HOME/scripts


ENV_CONF_PARAM="
-e SERVER_IP=$(grep_conf 'SERVERIP' $IP_FILE)
-e SERVER_IPV6=$(grep_conf 'SERVERIPV6' $IP_FILE)
-e VRTSP_SERVER_DOMAIN=$(grep_conf 'VRTSP_SERVER_DOMAIN' $INSTALL_CONF)
-e MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
-e ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
-e SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
-e ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
-e DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
-e FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
-e FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
-e GROUP_NAME=$(grep_conf 'GROUP_NAME' $INSTALL_CONF)
"

echo ${ENV_CONF_PARAM};

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "创建存放Pcap的文件夹 $PCAP_PATH"
if [ ! -d $PCAP_PATH ]; then
    mkdir -p $PCAP_PATH
fi

echo "创建存放CA的文件夹 $TLS_PATH"
if [ ! -d $TLS_PATH ]; then
    mkdir -p $TLS_PATH
fi

ENV_LOAD_PARAM="
-v /usr/share/zoneinfo:/usr/share/zoneinfo
-v /var/log/csvrtsplog:/var/log/csvrtsplog
-v /var/core:/var/core
-v /etc/ip:/etc/ip
-v /etc/kdc.conf:/etc/kdc.conf
-v /bin/crypto:/bin/crypto
-v /var/csvrecord_sock:/var/csvrecord_sock
"

echo ${ENV_LOAD_PARAM};

if [ $(docker ps -a --filter "name=^/${CONTAINER_NAME}$" -q | wc -l) -gt 0 ]; then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME 2>/dev/null || echo "")
    docker stop $CONTAINER_NAME || true;
    docker rm -f $CONTAINER_NAME || true;
    if [ -n "$old_image_id" ]; then
        docker rmi -f $old_image_id || true
    fi

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 csvrtspd"
    app_pids=$(pidof csvrtspd || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi

    sed -i '/csvrtsprun.sh/d' /etc/init.d/rc.local
fi

docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} ${ENV_LOAD_PARAM} --restart=always --net=host --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csvrtsp/scripts/csvrtsprun.sh ${CONTAINER_NAME}
