#!/bin/bash

#csvrtsp安装脚本,含csvrtspd、csvrecord等组件
AKCS_INSTALL_PATH=/usr/local/akcs/csvrtsp
AKCS_RUN_SCRIPT_NAME=csvrtsprun.sh
AKCS_RUN_SCRIPT=${AKCS_INSTALL_PATH}/scripts/${AKCS_RUN_SCRIPT_NAME}

CSMEDIAGATE_INSTALL_PATH=/usr/local/akcs/csmediagate
CSMEDIAGATE_RUN_SCRIPT_NAME=csmediagaterun.sh
CSMEDIAGATE_RUN_SCRIPT=${CSMEDIAGATE_INSTALL_PATH}/scripts/${CSMEDIAGATE_RUN_SCRIPT_NAME}

INSTALL_CONF=/etc/vrtsp_install.conf
HOST_IP=/etc/ip
WORK_DIR=`pwd`
PAKCAGES_ROOT=${WORK_DIR}/..
chmod 777 -R ${PAKCAGES_ROOT}/*

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null;
    #IP地址必须为全数字
    if [ $? -ne 0 ]
    then
        return 1
    fi
    ipaddr=$1
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值
    b=`echo $ipaddr|awk -F . '{print $2}'`
    c=`echo $ipaddr|awk -F . '{print $3}'`
    d=`echo $ipaddr|awk -F . '{print $4}'`
    for num in $a $b $c $d
    do
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间
        then
            return 1
        fi
    done
    return 0
}
EchoHostIPAddr()
{
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip

    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4
        
    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
}
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6;

    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
	echo "SERVER_DOMAIN=$SERVER_DOMAIN" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}
EnterBasicSrvIPAddr()
{
    #输入外网域名
    yellow "Enter your csvrtsp server outer Domain: \c"
    read SERVER_DOMAIN;
	
    #输入etcd内网IP
    yellow "Enter your etcd cluster servers inner IPV4,(eg:************:5204;************:15204;...): \c"
    read ETCD_INNER_IP;

    #输入redis内网IP
    yellow "Enter your redis server inner IPV4: \c"
    read REDIS_INNER_IP;

    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;

    #输入FDFS内网IP
    yellow "Enter your FDFS server inner IPV4: \c"
    read FDFS_INNER_IP;

	yellow "Do you want to install media gate for weixin SmartPlus:(1 means yes, 0 means no): \c"
	read WITH_MEIDA_GATE;

	yellow "Register to etcd or not(0-no or 1-yes): \c"
	read REG_ETCD;

    for ip in $REDIS_INNER_IP $MYSQL_INNER_IP; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "ETCD_INNER_IP=$ETCD_INNER_IP" >>$INSTALL_CONF
    echo "REDIS_INNER_IP=$REDIS_INNER_IP" >>$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
    echo "FDFS_INNER_IP=$FDFS_INNER_IP" >>$INSTALL_CONF
	echo "WITH_MEIDA_GATE=$WITH_MEIDA_GATE" >>$INSTALL_CONF
	echo "REG_ETCD=$REG_ETCD" >>$INSTALL_CONF
}


function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0

	fi
}
#added by chenyc,2019-03-29,分布式脚本，先确定本机的内外网地址,所有的ip信息放在:/etc/ip里面
if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
	SERVER_DOMAIN=`cat $HOST_IP | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`	
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`

    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi

EchoBasicSrvIPAddr()
{
    etcd_inner_ip_str="ETCD_INNER_IP="
    etcd_inner_ip_cat=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    etcd_inner_ip=$etcd_inner_ip_str$etcd_inner_ip_cat
    echo $etcd_inner_ip

    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip

    redis_inner_ip_str="REDIS_INNER_IP="
    redis_inner_ip_cat=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    redis_inner_ip=$redis_inner_ip_str$redis_inner_ip_cat
    echo $redis_inner_ip

    fdfs_inner_ip_str="FDFS_INNER_IP="
    fdfs_inner_ip_cat=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    fdfs_inner_ip=$fdfs_inner_ip_str$fdfs_inner_ip_cat
    echo $fdfs_inner_ip

    tmp_str="WITH_MEIDA_GATE="
    tmp_cat=`cat $INSTALL_CONF | grep -w WITH_MEIDA_GATE | awk -F'=' '{ print $2 }'`
    str=$tmp_str$tmp_cat
    echo $str
	
    tmp_str="SERVER_DOMAIN="
    tmp_cat=`cat $INSTALL_CONF | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    str=$tmp_str$tmp_cat
    echo $str
	
    tmp_str="REG_ETCD="
    tmp_cat=`cat $INSTALL_CONF | grep -w REG_ETCD | awk -F'=' '{ print $2 }'`
    str=$tmp_str$tmp_cat
    echo $str		
}
#再确定redis、mysql、etcd、nsqlookupd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    ETCD_INNER_IP=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    REDIS_INNER_IP=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    FDFS_INNER_IP=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
	WITH_MEIDA_GATE=`cat $INSTALL_CONF | grep -w WITH_MEIDA_GATE | awk -F'=' '{ print $2 }'`
	SERVER_DOMAIN=`cat $INSTALL_CONF | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
	REG_ETCD=`cat $INSTALL_CONF | grep -w REG_ETCD | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/csvrtsp_install.conf>, please enter all information below:"
    EnterBasicSrvIPAddr
fi

if [ $SERVER_DOMAIN == "" ];then
    echo "Please input your rtsp outer Domain."
	exit 1;
fi
if [ ! -n "$REG_ETCD" ];then
	yellow "Please Enter Register etcd or not."
	exit
fi


#replace serverip,注意ip后面的等号不能有空格
sed -i "s/^.*csvrtsp_outeripv6=.*/csvrtsp_outeripv6=${SERVERIPV6}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf
sed -i "s/^.*csvrtsp_outerip=.*/csvrtsp_outerip=${SERVERIP}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf
sed -i "s/^.*csvrtsp_outer_domain=.*/csvrtsp_outer_domain=${SERVER_DOMAIN}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf
sed -i "s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf
sed -i "s/^.*rtspnonce_host=.*/rtspnonce_host=${REDIS_INNER_IP}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp_redis.conf
sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf

sed -i "s/^.*reg_etcd=.*/reg_etcd=${REG_ETCD}/g" ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf

#后于之前替换的代码,保证db redis配置不被覆盖
bash $WORK_DIR/redis-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp_redis.conf
bash $WORK_DIR/dbproxy-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csvrtsp/conf/csvrtsp.conf 

scriptpid=`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${AKCS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi

echo "stopping csvrtsp services..."
${PAKCAGES_ROOT}/csvrtsp_scripts/csvrtspctl.sh stop
sleep 1
echo "making logs directories..."
if [ ! -d /var/log/csvrtsplog ]; then
    mkdir -p /var/log/csvrtsplog
fi
if [ ! -d /var/log/csvrecordlog ]; then
    mkdir -p /var/log/csvrecordlog
fi


echo "copying akcs csvrtsp files..."
if [ -d /usr/local/akcs/csvrtsp ]; then
    rm -rf  /usr/local/akcs/csvrtsp
fi
if [ -d /usr/local/akcs/csvrecord ]; then
    rm -rf /usr/local/akcs/csvrecord
fi
if [ -d /usr/local/akcs/csvrtsp_scripts ]; then
    rm -rf /usr/local/akcs/csvrtsp_scripts
fi

chmod 777 -R /usr/local/akcs/

cp -rf ${PAKCAGES_ROOT}/csvrtsp/ /usr/local/akcs
#4.6新增md5sum校验，避免拷贝不完全
Md5sumCheck ${PAKCAGES_ROOT}/csvrtsp/bin/csvrtspd /usr/local/akcs/csvrtsp/bin/csvrtspd

mkdir -p /usr/local/akcs/csvrtsp/scripts
chmod -R 777 /usr/local/akcs/csvrtsp/scripts/
cp -rf ${PAKCAGES_ROOT}/csvrecord/ /usr/local/akcs
cp -rfp ${PAKCAGES_ROOT}/csmediagate/ /usr/local/akcs
cp -rf ${PAKCAGES_ROOT}/csvrtsp_scripts/* /usr/local/akcs/csvrtsp/scripts/


if [ -z `grep "/usr/local/akcs/csvrtsp/lib" /etc/ld.so.conf` ];then
    echo "/usr/local/akcs/csvrtsp/lib" >> /etc/ld.so.conf
fi
ldconfig

#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "bash ${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi


echo "starting services..."
chmod 777 /usr/local/akcs/csvrtsp/scripts/csvrtspctl.sh
/usr/local/akcs/csvrtsp/scripts/csvrtspctl.sh start

sleep 1
chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup bash ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
fi

#csmediagate
if [ $WITH_MEIDA_GATE -eq 1 ]; then
	chmod -R 777 /usr/local/akcs/csmediagate
	chmod 777 ${CSMEDIAGATE_RUN_SCRIPT}
	if [ -z "`ps -fe|grep "${CSMEDIAGATE_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
		nohup bash ${CSMEDIAGATE_RUN_SCRIPT} >/dev/null 2>&1 &
	fi
fi


echo "csvrtsp install completed ..."

#core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi
if [ -z "`grep "kernel.core_pattern" /etc/sysctl.conf`" ];then
	echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
fi

if [ -z "`grep "ulimit -c unlimited" /etc/profile`" ];then
	echo 'ulimit -c unlimited' >> /etc/profile
fi

sysctl -p >/dev/null
. /etc/profile

#echo status
/usr/local/akcs/csvrtsp/scripts/csvrtspctl.sh status
