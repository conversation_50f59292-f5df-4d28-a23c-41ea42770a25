#!/bin/bash
ACMD="$1"
CSVRTSP_BIN='/usr/local/akcs/csvrtsp/bin/csvrtspd'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csvrtsp()
{
    nohup $CSVRTSP_BIN >/dev/null 2>&1 &
    echo "Start csvrtsp successful"

    if [ -z "`ps -fe|grep "csvrtsprun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csvrtsp/scripts/csvrtsprun.sh >/dev/null 2>&1 &
    fi
}
stop_csvrtsp()
{
    echo "Begin to stop csvrtsprun.sh"
    csvrtsprunid=`ps aux | grep -w csvrtsprun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csvrtsprunid}" ];then
	    echo "csvrtsprun.sh is running at ${csvrtsprunid}, will kill it first."
	    kill -9 ${csvrtsprunid}
    fi
    echo "Begin to stop csvrtsp"
    kill -9 `pidof csvrtspd`
    sleep 2
    echo "Stop csvrtsp successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 554 | grep csvrtspd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csvrtsp
    else
        echo "csvrtspd is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 554 | grep csvrtspd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csvrtspd is already stopping"
    else
        stop_csvrtsp
    fi
    ;;
  restart)
    stop_csvrtsp
    sleep 1
    start_csvrtsp
    ;;
  status)
    cnt=`ss -alnp | grep 554 | grep csvrtspd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csvrtspd is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csvrtspd is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

