#!/bin/bash
ACMD="$1"
WORK_DIR="/usr/local/akcs/csvrtsp/scripts"
CSVRECORD_PWD="/usr/local/akcs/csvrecord/scripts"
CSMEDIAGATE_PWD="/usr/local/akcs/csmediagate/scripts"

INSTALL_CONF=/etc/vrtsp_install.conf
WITH_MEIDA_GATE=`cat $INSTALL_CONF | grep -w WITH_MEIDA_GATE | awk -F'=' '{ print $2 }'`

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_akcs()
{
    sh -x ${WORK_DIR}/csvrtsp.sh start
    sh -x ${CSVRECORD_PWD}/csvrecordctl.sh start

	if [ $WITH_MEIDA_GATE -eq 1 ]; then
		sh -x ${CSMEDIAGATE_PWD}/csmediagate.sh start
	fi
}

stop_akcs()
{
    sh -x ${WORK_DIR}/csvrtsp.sh stop
    sh -x ${CSVRECORD_PWD}/csvrecordctl.sh stop
	  sh -x ${CSMEDIAGATE_PWD}/csmediagate.sh stop
}

status_akcs()
{
    sh -x ${WORK_DIR}/csvrtsp.sh status
    sh -x ${CSVRECORD_PWD}/csvrecordctl.sh status
	
	if [ $WITH_MEIDA_GATE -eq 1 ]; then
		sh -x ${CSMEDIAGATE_PWD}/csmediagate.sh status
	fi
}

uninstall_akcs()
{
    sh -x ${WORK_DIR}/csvrtsp.sh stop
    kill -9 `ps aux | grep csvrtsprun.sh |grep -v grep | awk '{print $2}'`
    rm -rf /usr/local/akcs/csvrtsp/
}

case $ACMD in
  start)
        start_akcs
    ;;
  stop)
        stop_akcs
    ;;
  uninstall)
        uninstall_akcs
    ;;
  restart)
    stop_akcs
    sleep 1
    start_akcs
    ;;
  status)
    status_akcs
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status|uninstall"
    ;;
esac
exit

