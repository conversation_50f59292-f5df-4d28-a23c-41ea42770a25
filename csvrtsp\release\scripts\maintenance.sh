#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`

# Define functions
cmd_usage() {
  echo "usage: $0 echo"
  echo "       $0 rtsp_cli"
  echo "       $0 monitor_list <mac>"
  echo "       $0 svn_version"
  echo "       $0 set_pcap_mac <mac>"
  echo "       $0 clear_pcap_mac <mac>"
  echo "       $0 metrics"
  echo "       $0 rtsp_url <mac>"
  echo "       $0 request_statics <top_number>"
  echo "       $0 set_request_statics_switch <switch 0关闭 1开启>"
  exit 0
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi

# Check command
if [[ "$1" == "echo" ]]; then
  echo "curl $SERVER_INNER_IP:9997/echo"
  curl $SERVER_INNER_IP:9997/echo
elif [[ "$1" == "rtsp_cli" ]]; then
  echo "curl $SERVER_INNER_IP:9997/rtsp_cli"
  curl $SERVER_INNER_IP:9997/rtsp_cli
elif [[ "$1" == "monitor_list" ]]; then
  mac=$2
  echo "curl $SERVER_INNER_IP:9997/monitor_list?mac=$mac"
  curl $SERVER_INNER_IP:9997/monitor_list?mac=$mac
elif [[ "$1" == "svn_version" ]]; then
  echo "curl $SERVER_INNER_IP:9997/svn_version"
  curl $SERVER_INNER_IP:9997/svn_version
elif [[ "$1" == "set_pcap_mac" ]]; then
  mac=$2
  echo "curl -X POST $SERVER_INNER_IP:9997/set_pcap_mac?mac=$mac"
  curl -X POST $SERVER_INNER_IP:9997/set_pcap_mac?mac=$mac
elif [[ "$1" == "clear_pcap_mac" ]]; then
  mac=$2
  echo "curl -X POST $SERVER_INNER_IP:9997/clear_pcap_mac?mac=$mac"
  curl -X POST $SERVER_INNER_IP:9997/clear_pcap_mac?mac=$mac
elif [[ "$1" == "metrics" ]]; then
  echo "curl $SERVER_INNER_IP:9997/metrics"
  curl $SERVER_INNER_IP:9997/metrics
elif [[ "$1" == "rtsp_url" ]]; then
  mac=$2
  echo "curl $SERVER_INNER_IP:9997/rtsp_url?mac=$mac"
  curl $SERVER_INNER_IP:9997/rtsp_url?mac=$mac
  echo ""
elif [[ "$1" == "request_statics" ]]; then
  top_number=$2
  echo "curl $SERVER_INNER_IP:9997/request_statics?top_number=$top_number"
  curl $SERVER_INNER_IP:9997/request_statics?top_number=$top_number
  echo ""
elif [[ "$1" == "set_request_statics_switch" ]]; then
  switch=$2
  echo "curl $SERVER_INNER_IP:9997/set_request_statics_switch?switch=$switch"
  curl $SERVER_INNER_IP:9997/set_request_statics_switch?switch=$switch
  echo ""
else
  cmd_usage
fi
