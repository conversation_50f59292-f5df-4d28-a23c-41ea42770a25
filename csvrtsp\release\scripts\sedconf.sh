#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

# csvrtsp.conf
sed -i "
    s/^.*csvrtsp_outeripv6=.*/csvrtsp_outeripv6=${SERVER_IPV6}/g
    s/^.*csvrtsp_outerip=.*/csvrtsp_outerip=${SERVER_IP}/g
    s/^.*csvrtsp_outer_domain=.*/csvrtsp_outer_domain=${VRTSP_SERVER_DOMAIN}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*reg_etcd=.*/reg_etcd=${IS_REG_ETCD}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp.conf

if [ -n "${GROUP_NAME}" ];then
	sed -i "s/^.*group_name=.*/group_name=${GROUP_NAME}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp.conf
fi

if [ "$ENABLE_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp.conf
fi

# csvrtsp_redis.conf
sed -i "s/^.*rtspnonce_host=.*/rtspnonce_host=${REDIS_INNER_IP}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_redis.conf
sed -i "s/^.*mac_vrtspsid_host=.*/mac_vrtspsid_host=${REDIS_INNER_IP}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_redis.conf
sed -i "s/^.*backend_limiting_host=.*/backend_limiting_host=${REDIS_INNER_IP}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_redis.conf

if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_redis.conf
fi

# csvrtsp_fdfs.conf
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" /usr/local/akcs/csvrtsp/conf/csvrtsp_fdfs.conf
fi