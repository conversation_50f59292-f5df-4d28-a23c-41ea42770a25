#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   web 的远程启动脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IS_REG_ETCD=${11}             #是否注册到etcd

MIDDLEWARE=$3

ENV=$4
HOST=$5
INNER_IP=$6
NICKNAME=$7
VENDOR=$8                  #云产商
HOSTNAME=$9
IPV6=${10}
LINE=${11}

DOCKER_IMG=${12}

bash -x $RSYNC_PATH/csvrtsp/install/install_docker.sh $RSYNC_PATH $PROJECT_RUN_PATH $IS_REG_ETCD $DOCKER_IMG
bash -x $RSYNC_PATH/csvrecord/install/install_docker.sh $RSYNC_PATH $PROJECT_RUN_PATH $DOCKER_IMG

#监控插件安装
echo "===执行监控安装==="
bash -x $RSYNC_PATH/shell/monitor.sh $RSYNC_PATH $PROJECT_RUN_PATH $MIDDLEWARE $ENV $HOST $INNER_IP $NICKNAME $VENDOR $HOSTNAME $IPV6 $LINE