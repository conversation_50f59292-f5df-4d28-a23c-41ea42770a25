#include "Control.h"
#include "Lock.h"
#include "WaitEvent.h"
#include <stdlib.h>
#include <stdio.h>
#include "string.h"
#include "ipc/ipc.h"
#include "unistd.h"
#include "AKLog.h"
#include "VrtspDefine.h"
#include "rtp/RtpDeviceManager.h"
#include "rtp/RtpDeviceClient.h"
#include "RtspClientManager.h"
#include "PcapControl.h"
#include "utils.h"
#include "util.h"
#include "CsvrtspConf.h"

using namespace akuvox;
extern CSVRTSP_CONF gstCSVRTSPConf;

//#include "msg.h"

int ProcessThread(void* data)
{
    CControl* control = (CControl*)data;

    while (true)
    {
        control->ProcessMsg();
    }

    pthread_detach(pthread_self());
    return 0;
}

void* TimerThread(void* data)
{
    CControl* control = (CControl*)data;

    while (true)
    {

        control->AddMsg(MSG_TIMER, TIMER_ID_BASE, 0, NULL, 0);
        usleep(TIMER_VAL_BASE * 1000);
    }

    pthread_detach(pthread_self());
    return 0;
}

CControl* GetControlInstance()
{
    return CControl::GetInstance();
}

CControl::CControl(): tag_("Control")
{
    msg_header_ = NULL;
    m_lock = new CLock();
    m_wait = new CWaitEvent();
}

CControl::~CControl()
{
    DelAllMsg();

    if (NULL != m_lock)
    {
        delete (CLock*)m_lock;
        m_lock = NULL;
    }
    if (NULL != m_wait)
    {
        delete (CWaitEvent*)m_wait;
        m_wait = NULL;
    }
}

CControl* CControl::instance = NULL;

CControl* CControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CControl();
    }

    return instance;
}

int CControl::Init()
{
    pthread_create(&m_tidTimer, NULL, TimerThread, this);
    return 0;
}

int CControl::Run()
{
    return (int)ProcessThread(this);
}

//处理消息
int CControl::ProcessMsg()
{
    WaitForEvent();
    Lock();
    MESSAGE* tmp_node = NULL;

    while (msg_header_ != NULL)
    {
        tmp_node = (MESSAGE*)msg_header_;
        msg_header_ = ((MESSAGE*)msg_header_)->next;
        Unlock();
        OnMessage(tmp_node->id, tmp_node->w_param, tmp_node->l_param, tmp_node->l_data);
        Lock();
        if (tmp_node->l_data != NULL)
        {
            delete [](char*)tmp_node->l_data;
        }
        delete (tmp_node);
    }

    msg_header_ = NULL;

    ResetWaitEvent();

    Unlock();

    return 0;
}


//上锁消息缓冲区
void CControl::Lock()
{
    ((CLock*)m_lock)->Lock();
}

//解锁消息缓冲区
void CControl::Unlock()
{
    ((CLock*)m_lock)->Unlock();
}

//设置事件
void CControl::SetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Set();
}

//清除事件
void CControl::ResetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Reset();
}

//等待事件触发
void CControl::WaitForEvent()
{
    ((CWaitEvent*)m_wait)->Wait();
}


//增加一个新的消息
int CControl::AddMsg(unsigned int id, unsigned int w_param, unsigned int l_param, void* l_data, int data_len)
{
    Lock();
    MESSAGE* cur_node = NULL;
    MESSAGE* new_node = new MESSAGE();
    if (NULL == new_node)
    {
        Unlock();
        return -1;
    }

    memset(new_node, 0, sizeof(MESSAGE));

    new_node->id = id;
    new_node->w_param = w_param;
    new_node->l_param = l_param;
    if ((l_data != NULL) && (data_len > 0))
    {
        new_node->l_data = new char[data_len];
        memcpy(new_node->l_data, l_data, data_len);
    }

    if (msg_header_ == NULL)
    {
        msg_header_ = new_node;
    }
    else
    {
        cur_node = (MESSAGE*)msg_header_;
        while ((cur_node != NULL) && (cur_node->next != NULL))
        {
            cur_node = cur_node->next;
        }
        cur_node->next = new_node;
    }
    SetWaitEvent();

    Unlock();

    return 0;
}


//删除所有消息
int CControl::DelAllMsg()
{
    Lock();

    MESSAGE* cur_node = NULL;
    MESSAGE* tmp_node = NULL;

    cur_node = (MESSAGE*)msg_header_;

    while (cur_node != NULL)
    {
        tmp_node = cur_node;
        cur_node = cur_node->next;
        if (tmp_node->l_data != NULL)
        {
            delete [](char*)tmp_node->l_data;
        }

        delete tmp_node;
    }

    msg_header_ = NULL;

    Unlock();

    return 0;
}

//消息处理句柄
int CControl::OnMessage(unsigned int msg, unsigned int w_param, unsigned int l_param, void* l_data)
{
    int msg_type = msg & MSG_TYPE_MASK;
    switch (msg_type)
    {
        case MSG_TIMER://second timer
        {
            OnTimer(w_param);
        }
        break;
        case MSG_MULTICAST:
        case MSG_TCP:
            break;
        case MSG_CTRL:
        {
            OnCtrl(msg, w_param, l_param, l_data);
        }
        break;
        default:
            break;
    }

    return 0;
}

//控制消息处理句柄
int CControl::OnCtrl(unsigned int msg, unsigned int w_param, unsigned int l_param, void* l_data)
{
    switch (msg)
    {

        case MSG_CTRL_START_CAPTURE:
        {
            SOCKET_MSG_CAPTURE_RTSP* capture_rtsp = (SOCKET_MSG_CAPTURE_RTSP*)l_data;
            ipc_send(IPC_ID_VRECORD, MSG_VRTSP2VRECORD_START_CAPTURE, 0, 0, (void*)l_data, sizeof(SOCKET_MSG_CAPTURE_RTSP)); // 先通知其文件名
            CAKLog::LogI(tag_, "[StartCapture] Picname=%s  PicFlowUUID=%s", capture_rtsp->szPicName, capture_rtsp->flow_uuid);
            std::string flow_uuid = capture_rtsp->flow_uuid;
            std::shared_ptr<RtpDeviceClient> rtp_device = RtpDeviceManager::getInstance()->GetClientByFlowUUID(flow_uuid);// 根据流唯一标识
            if (rtp_device == NULL)
            {
                CAKLog::LogE(tag_, "[StartCapture] Cannot find device flow uuid=%s", capture_rtsp->flow_uuid);
                return -1;
            }
            Snprintf(rtp_device->pic_name_, sizeof(rtp_device->pic_name_),  capture_rtsp->szPicName);
            rtp_device->capture_ = true;

        }
        break;

        case MSG_CTRL_STOP_CAPTURE:
        {
            SOCKET_MSG_CAPTURE_RTSP* capture_rtsp = (SOCKET_MSG_CAPTURE_RTSP*)l_data;
            std::string flow_uuid = capture_rtsp->flow_uuid;
            std::shared_ptr<RtpDeviceClient> rtp_device = RtpDeviceManager::getInstance()->GetClientByFlowUUID(flow_uuid);// 根据flow_uuid获取device客户端
            if (rtp_device == NULL)
            {
                CAKLog::LogE(tag_, "[StopCapture]Cannot find monitor flow. uuid=%s", capture_rtsp->flow_uuid);
                return -1;
            }
            memset(rtp_device->pic_name_, 0, sizeof(rtp_device->pic_name_));
            rtp_device->capture_ = false;
        }
        break;

        default:
            break;
    }

    return 0;
}

//定时器消息处理
int CControl::OnTimer(unsigned int id_event)
{
    if (id_event == TIMER_ID_BASE)
    {
        static int heartbeat_time = 0;
        if (heartbeat_time >= gstCSVRTSPConf.timer_heartbeat_time)
        {
            //获取有连接app的设备mac列表， 发送心跳
            RtpDeviceManager::getInstance()->SendHeartBeatForList();
            heartbeat_time = 0;
        }
        heartbeat_time++;

        //边缘转流服务器超时校验
        static int inner_vrtspd_keepalive = 0;
        if (inner_vrtspd_keepalive >= gstCSVRTSPConf.timer_inner_keepalive_time)
        {
            //超时删除
            RtpDeviceManager::getInstance()->CheckInnerVrtspKeepAlive(5);//5秒检测一次,边缘是否超时
            inner_vrtspd_keepalive = 0;
        }
        inner_vrtspd_keepalive++;

        // check pcap capture state
        static int pcap_capture_timeout = 0;
        if (pcap_capture_timeout >= gstCSVRTSPConf.timer_pcap_timeout)
        {
            //超时删除
            GetPcapCaptureControlInstance()->CheckCaptureTimeout();
            pcap_capture_timeout = 0;
        }
        pcap_capture_timeout++;

        // 5s检测一次设备rtp packet的最后发送时间, 超过10s未发包清理相关资源
        static int rtp_packet_timeout = 0;
        if (rtp_packet_timeout >= gstCSVRTSPConf.timer_rtp_packet_timeout)
        {
            RtpDeviceManager::getInstance()->CheckDevRtpFlowStop();
            rtp_packet_timeout = 0;
        }
        rtp_packet_timeout++;
    }

    return 0;
}
