#include <stdio.h>
#include <stdlib.h>
#include <sstream>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "HttpResp.h"
#include "CsvrtspConf.h"
#include "util.h"
#include "RtspClientManager.h"
#include "AkcsPasswdConfuse.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "RouteClientMng.h"
#include "EtcdCliMng.h"
#include "AkcsCommonDef.h"
#include "SafeCacheConn.h"
#include "timeticker/akcs_time_util.h"
#include "RtpDeviceManager.h"
#include "AkcsRequestRecorder.h"
 #include "MetricService.h"
 
//全局变量
static operation_http::HTTPAllRespCallbackMap httpRespCbs;
extern CSVRTSP_CONF gstCSVRTSPConf;
extern CAkEtcdCliManager* g_etcd_cli_mng;

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}

void HttpRtspCliCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->body().ToString() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //LOG_WARN << "http req head 'api-version' is null";
        // cb("http req head 'api-version' is null");
        // return;
        head = "3.1";
    }
    auto httpRespMap = httpRespCbs[operation_http::RTSP_CLI];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

//设备监控定位运维接口，add by czw
void HttpReqMonitorListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.6";
    }
    auto httpRespMap = httpRespCbs[operation_http::MONITOR];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

void HttpReqInnerRtspClientListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string rtsp_client_list;
    rtsp_client_list = akuvox::RtpDeviceManager::getInstance()->GetInnerRtspClientList();
    cb(rtsp_client_list);
    return;
}


void HttpReqSvnVersionCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string svn_version;
    svn_version = gstCSVRTSPConf.svn_version;
    svn_version += "\n";
    cb(svn_version);
    return;
}

void HttpReqSetPcapMacCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "5.4";
    }
    auto httpRespMap = httpRespCbs[operation_http::SET_PCAP_MAC];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

void HttpReqClearPcapMacCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "5.4";
    }
    auto httpRespMap = httpRespCbs[operation_http::CLEAR_PCAP_MAC];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void HttpReqRtspUrlCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req url =  [ " << ctx->original_uri() << " ], ip = " << ctx->remote_ip();

    if (strcmp(gstCSVRTSPConf.csvrtsp_inner_ip, ctx->remote_ip().c_str()) != 0)
    {
        cb("invalid address");
        return;
    }
   
    std::string mac = ctx->GetQuery("mac");
    
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev) || 0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
    {
        std::string rtsp_password = dev.rtsppwd;
        PasswdDecode(rtsp_password.c_str(), rtsp_password.size(), dev.rtsppwd, sizeof(dev.rtsppwd));

        char rtsp_url[256];
        snprintf(rtsp_url, sizeof(rtsp_url), "rtsp://user:%s@%s:554/%s", rtsp_password.c_str(), gstCSVRTSPConf.csvrtsp_outer_domain, mac.c_str());
        cb(rtsp_url);
    }
    else
    {
        cb("mac not exsits");
    }

    return;
}

void HttpGetRequestStaticsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{	
    LOG_INFO << "http req url =  [ " << ctx->original_uri() << " ], ip = " << ctx->remote_ip();
    int top_number = ATOI(ctx->GetQuery("top_number").c_str());

    auto sorted_requests = CAkcsRequestRecorder::getInstance().GetSortedRequestCounts();

    std::stringstream statics_ret;
	statics_ret << "Top " << top_number << " most frequent requests IP: \n";
    for (size_t i = 0; i < min(sorted_requests.size(), size_t(top_number)); ++i) 
	{
        const auto& request = sorted_requests[i];
        statics_ret << "Client =  " << get<0>(request) << ", Message ID = " << get<1>(request) << ", Requests Counts = " << get<2>(request) << "\n";
    }
    
    statics_ret << "xxx_connect and xxx_mac is two types of statistics, xxx_connect is only connect, xxx_mac success monitor\n";
    AK_LOG_INFO << statics_ret.str();
    cb(statics_ret.str());
    return;
}

void HttpSetRequestStaticsSwitchCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{	
    LOG_INFO << "http req url =  [ " << ctx->original_uri() << " ], ip = " << ctx->remote_ip();
    
    gstCSVRTSPConf.request_statics_switch = ATOI(ctx->GetQuery("switch").c_str());

    std::stringstream switch_ret;
    switch_ret << "set_request_statics_switch success, switch = " << gstCSVRTSPConf.request_statics_switch;

    AK_LOG_INFO << switch_ret.str();
    cb(switch_ret.str());
    return;
}

void startHttpServer()
{
    httpRespCbs = operation_http::HTTPAllRespMapInit();
    const int port = 9997;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网
    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/rtsp_cli", HttpRtspCliCallback);
    server.RegisterHandler("/monitor_list", HttpReqMonitorListCallback);
    server.RegisterHandler("/inner_client_list", HttpReqInnerRtspClientListCallback);
    server.RegisterHandler("/svn_version", HttpReqSvnVersionCallback);
    server.RegisterHandler("/set_pcap_mac", HttpReqSetPcapMacCallback);
    server.RegisterHandler("/clear_pcap_mac", HttpReqClearPcapMacCallback);
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    server.RegisterHandler("/rtsp_url", HttpReqRtspUrlCallback);
    server.RegisterHandler("/request_statics", HttpGetRequestStaticsCallback);
    server.RegisterHandler("/set_request_statics_switch", HttpSetRequestStaticsSwitchCallback);
    server.Init(port);
    server.Start();
    //while (!server.IsStopped()) {
    //    usleep(1);
    //}
    return ;
}
