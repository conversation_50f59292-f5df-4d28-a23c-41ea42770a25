#include "Metric.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "RouteClientMng.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "RtspClientManager.h"

extern CAkEtcdCliManager* g_etcd_cli_mng;
#define VERSION_CONF_FILE "/usr/local/akcs/csvrtsp/conf/version.conf"


void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "csvrtsp_socket_client_total",
        "The number of csvrtsp socket client",
        "csvrtsp_socket_client_total",
        []() -> long { return (long) akuvox::RtspClientManager::getInstance()->GetClientCount(); }
    );
    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csvrtsp_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "logdb_get_conn_failed_count",
        "LOGDB GetConnection failed count",
        "csvrtsp_logdb_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "csvrtsp_monitor_queue_length",
        "The length of csvrtsp monitor queue",
        "csvrtsp_monitor_queue_length",
        []() -> long { return (long)(akuvox::RtspClientManager::getInstance()->ConcurrencyClientNum()); }
    );
    metric_service->AddMetric(
        "redis_check",
        "redis server status",
        "csvrtsp_redis_check_error",
        []() -> long { return (long)(CacheManager::getInstance()->CheckRedisNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "db_conn_check",
        "db conn status",
        "csvrtsp_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csroute_check",
        "route server status",
        "csvrtsp_csroute_check_error",
        []() -> long { return (long)(CRouteClientMng::Instance()->CheckRouteNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "etcd_check",
        "etcd server status",
        "csvrtsp_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );

    // rtp包处理延时监控
    std::vector<uint32_t> rtp_package_handle_latencies = {50,75,100,125,150,175,200,225,250,275,300,350,400,450,500,600,700,800,900};
    MetricLatencyPtr queue_handle_latency = std::make_shared<MetricLatency>(rtp_package_handle_latencies, "queue_handle_latency");
    metric_service->AddLatencyMetric("queue_handle_latency", queue_handle_latency);
}


