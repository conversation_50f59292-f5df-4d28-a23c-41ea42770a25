#include <iostream>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <sstream>
#include "stdlib.h"
#include "util.h"
#include "AkLogging.h"
#include "CsvrtspConf.h"
#include "PcapControl.h"
#include "StorageMng.h"
#include "AkcsCommonSt.h"
#include "dbinterface/PcapCaptureControl.h"

extern CSVRTSP_CONF gstCSVRTSPConf;

static const char pacp_capture_file[] = "/usr/local/akcs/csvrtsp/pcap/"; //临时压缩文件存放路径
static const int rtsp_listen_port1 = 554;
static const int rtsp_listen_port2 = 8601;

PcapCaptureControl::PcapCaptureControl()
{
}

PcapCaptureControl::~PcapCaptureControl()
{
}

PcapCaptureControl* PcapCaptureControl::instance_ = nullptr;

PcapCaptureControl* PcapCaptureControl::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new PcapCaptureControl();
    }

    return instance_;
}

PcapCaptureControl* GetPcapCaptureControlInstance()
{
    return PcapCaptureControl::GetInstance();
}


void GetAddressInfo(const pcpp::Packet& parsed_packet, AddressInfo& addr_info) 
{
    if (parsed_packet.isPacketOfType(pcpp::IPv4))
    {
        addr_info.is_ipv6 = false;
        if (pcpp::IPv4Layer* ipv4_layer = parsed_packet.getLayerOfType<pcpp::IPv4Layer>())
        {
            Snprintf(addr_info.src_ip, sizeof(addr_info.src_ip), ipv4_layer->getSrcIPAddress().toString().c_str());
            Snprintf(addr_info.dst_ip, sizeof(addr_info.dst_ip), ipv4_layer->getDstIPAddress().toString().c_str());
        }
    }
    else if (parsed_packet.isPacketOfType(pcpp::IPv6))
    {
        addr_info.is_ipv6 = true;
        if (pcpp::IPv6Layer* ipv6_layer = parsed_packet.getLayerOfType<pcpp::IPv6Layer>())
        {
            Snprintf(addr_info.src_ip, sizeof(addr_info.src_ip), ipv6_layer->getSrcIPAddress().toString().c_str());
            Snprintf(addr_info.dst_ip, sizeof(addr_info.dst_ip), ipv6_layer->getDstIPAddress().toString().c_str());
        }
    }
    
    if (pcpp::TcpLayer* tcp_layer = parsed_packet.getLayerOfType<pcpp::TcpLayer>()) 
    {
        addr_info.src_port = tcp_layer->getSrcPort();
        addr_info.dst_port = tcp_layer->getDstPort();
    }
}

std::string GetPacketPayload(const pcpp::Packet& parsed_packet, bool is_ipv6)
{
    std::string payload_str;
    if (is_ipv6)
    {
        if (pcpp::IPv6Layer* ipv6_layer = parsed_packet.getLayerOfType<pcpp::IPv6Layer>())
        {
            char* payload = reinterpret_cast<char*>(ipv6_layer->getData());
            payload_str.assign(payload, ipv6_layer->getLayerPayloadSize());
        }
    }
    else
    {
        if (pcpp::IPv4Layer* ipv4_layer = parsed_packet.getLayerOfType<pcpp::IPv4Layer>())
        {
            char* payload = reinterpret_cast<char*>(ipv4_layer->getData());
            payload_str.assign(payload, ipv4_layer->getLayerPayloadSize());
        }
    }
    return payload_str;
}

static void OnTcpPacketArrives(pcpp::RawPacket* packet, pcpp::PcapLiveDevice* dev, void* cookie)
{   
    pcpp::Packet parsed_packet(packet);

    AddressInfo addr_info;
    memset(&addr_info, 0, sizeof(addr_info));
    GetAddressInfo(parsed_packet, addr_info);
    
    std::string src_uuid = PcapCaptureControl::CreateCaptureUUID(addr_info.src_ip, addr_info.src_port);
    std::string dst_uuid = PcapCaptureControl::CreateCaptureUUID(addr_info.dst_ip, addr_info.dst_port);
    
    // 判断uuid是否正在抓包
    if (GetPcapCaptureControlInstance()->IsUUIDExist(src_uuid, dst_uuid))
    {
        pcpp::TcpLayer* tcp_layer = parsed_packet.getLayerOfType<pcpp::TcpLayer>();
        if (tcp_layer != nullptr)
        {
           // 判断是否结束
           struct pcpp::tcphdr* tcp_header = tcp_layer->getTcpHeader();
           if (tcp_header->finFlag) 
           {
               if (addr_info.src_port == rtsp_listen_port1 || addr_info.src_port == rtsp_listen_port2)
               {
                   AK_LOG_INFO << "set tcp finFlag start, src_uuid = " << src_uuid << ", dst_uuid = " << dst_uuid;
                   
                   // 将结束包写入文件,app的ip:port为dst_uuid
                   GetPcapCaptureControlInstance()->WritePacket(dst_uuid, packet);
    
                   // 通知抓udp包结束
                   GetPcapCaptureControlInstance()->OnCaptureStop(dst_uuid);
    
                   AK_LOG_INFO << "set tcp finFlag end, src_uuid = " << src_uuid << ", dst_uuid = " << dst_uuid;
               }
               return;
           }
        }
    
        // 将其他rtsp包写入文件
        std::string uuid = (addr_info.src_port == rtsp_listen_port1 || addr_info.src_port == rtsp_listen_port2) ? dst_uuid : src_uuid;
        GetPcapCaptureControlInstance()->WritePacket(uuid, packet);
    }
    else 
    {
        std::string mac;
        std::string payload = GetPacketPayload(parsed_packet, addr_info.is_ipv6);
        if (GetPcapCaptureControlInstance()->IsMacBeingCaptured(payload, mac))
        {
            // payload中存在抓包的mac,开始抓包
            GetPcapCaptureControlInstance()->StartTcpCapture(src_uuid, mac);
            GetPcapCaptureControlInstance()->WritePacket(src_uuid, packet);
        }
    }
}

//rtsp route 触发
void PcapCaptureControl::OnUdpPortDispatch(const std::string &uuid, unsigned short app_port, unsigned short dev_port)
{
    {
        std::lock_guard<std::mutex> lock(writer_mutex_);
        if (writer_.find(uuid) == writer_.end())
        {   
            //AK_LOG_INFO << "OnUdpPortDispatch, not find app uuid = " << uuid;
            return;
        }
    }
    
    // 开启udp抓包
    std::shared_ptr<PcapCaptureUdp> udp_capture = std::make_shared<PcapCaptureUdp>(uuid, app_port, dev_port);
    udp_capture->Start();
    
    {
        std::lock_guard<std::mutex> lock(udp_capture_mutex_);
        udp_capture_[uuid] = udp_capture;
    }

    AK_LOG_INFO << "OnUdpPortDispatch, start capture udp pcaket success, uuid = " << uuid;
}

void PcapCaptureControl::WritePacket(const std::string& uuid, pcpp::RawPacket* packet)
{
    std::lock_guard<std::mutex> lock(writer_mutex_);
    if (writer_.find(uuid) != writer_.end())
    {
        std::shared_ptr<PcapWriter> pcap_writer = writer_[uuid];
        pcap_writer->WritePacket(packet);
    }
}

// 判断uuid是否存在
bool PcapCaptureControl::IsUUIDExist(const std::string& src_uuid, const std::string& dst_uuid)
{
    std::lock_guard<std::mutex> lock(writer_mutex_);
    if ((writer_.find(src_uuid) != writer_.end()) || (writer_.find(dst_uuid) != writer_.end()))
    {
        return true;
    }     
    return false;
}

// 判断rtsp_payload中是否存在正在抓包的mac
bool PcapCaptureControl::IsMacBeingCaptured(const std::string& rtsp_payload, std::string& mac)
{
    std::lock_guard<std::mutex> lock(capture_mac_mutex_);
    for (const auto &capture_mac : capture_mac_set_)
    {
        if (rtsp_payload.find(capture_mac) != std::string::npos)
        {
            mac = capture_mac;
            return true;
        }
    }
    return false;
}

//uuid: app rtsp ip:port
void PcapCaptureControl::StartTcpCapture(const std::string &uuid, const std::string &mac)
{
    std::time_t now = time(nullptr);
    // 创建pcapWriter对象
    std::string filepath = pacp_capture_file + mac + "_" + uuid + "_" + std::to_string(now) + ".pcap";
    
    std::shared_ptr<pcpp::PcapFileWriterDevice> pcap_writer = std::make_shared<pcpp::PcapFileWriterDevice>(filepath.c_str());
    if (!pcap_writer->open())
    {
        AK_LOG_WARN << "StartTcpCapture open pcap error,mac = " << mac;
        return;
    }

    // 添加uuid和pcapWriter的映射,tcp/udp包都通过uuid往pcapWriter里面写
    {
        std::lock_guard<std::mutex> lock(writer_mutex_);
        std::shared_ptr<PcapWriter> pcap_writer_obj = std::make_shared<PcapWriter>(pcap_writer, filepath, mac, now);
        writer_[uuid] = pcap_writer_obj;
    }

    // 添加mac和uuid和的映射,用于web停止监控
    {
        std::lock_guard<std::mutex> lock(mac_uuid_map_mutex_);
        mac_uuid_map_[mac] = uuid;
    }
}

void PcapCaptureControl::CheckCaptureTimeout()
{
    std::time_t now = time(nullptr);
    std::map<std::string, std::shared_ptr<PcapWriter>> tmp_writer_;
    {
        std::lock_guard<std::mutex> lock(writer_mutex_);
        tmp_writer_ = writer_;
    }

    for (auto iter = tmp_writer_.begin(); iter != tmp_writer_.end();  ++iter)
    {
        std::shared_ptr<PcapWriter> pcap_writer = iter->second;
        std::time_t timestamp = pcap_writer->GetTimestamp();
        if (now - timestamp >= 60)
        {
            // 停止监控,释放资源
            OnCaptureStop(iter->first);
        }
    }
}

// 监控停止
void PcapCaptureControl::OnCaptureStop(const std::string &uuid)
{
    // 停止udp抓包
    {
        std::lock_guard<std::mutex> lock(udp_capture_mutex_);
        if (udp_capture_.find(uuid) != udp_capture_.end())
        {
            // 停止udp抓包
            udp_capture_[uuid]->Stop();
            // 从map中擦除<uuid,PcapCaptureUdp>
            udp_capture_.erase(uuid);
            AK_LOG_INFO << "delete PcapCaptureUdp success, uuid = = " << uuid;
        }
    }

    // 关闭pcap文件
    std::string mac;
    std::string local_filepath;
    {
        std::lock_guard<std::mutex> lock(writer_mutex_);
        if (writer_.find(uuid) != writer_.end())
        {
            // 从map中擦除<uuid,PcapWriter>
            std::shared_ptr<PcapWriter> pcap_writer = writer_[uuid];
            mac = pcap_writer->GetMac();
            local_filepath = pcap_writer->GetLocalFilePath();
            writer_.erase(uuid);
            AK_LOG_INFO << "delete PcapWriter success, uuid = " << uuid;
        }
    }

    std::string action_uuid;
    {
        std::lock_guard<std::mutex> lock(mac_actionuuid_map_mutex_);
        if (mac_actionuuid_map_.find(mac) != mac_actionuuid_map_.end())
        {
            action_uuid = mac_actionuuid_map_[mac];
        }
    }
    
    // 上传文件到fdfs
    GetShadowMngInstance()->StorePcapCaptureFile(local_filepath.c_str(), action_uuid);
    ::remove(local_filepath.c_str());
}

// web通知开始抓某台设备的包
void PcapCaptureControl::WebPcapCaptureStart(const std::string& capture_mac, const std::string& action_uuid)
{
    // 将mac插入抓包列表
    {
        std::lock_guard<std::mutex> lock(capture_mac_mutex_);
        if (capture_mac_set_.find(capture_mac) == capture_mac_set_.end())
        {
            capture_mac_set_.insert(capture_mac);
        }
        else
        {
            return;
        }
    }
    
    // mac和fileuuid的映射关系,用于更新数据库
    {
        std::lock_guard<std::mutex> lock(mac_actionuuid_map_mutex_);
        if (mac_actionuuid_map_.find(capture_mac) == mac_actionuuid_map_.end())
        {
            mac_actionuuid_map_[capture_mac] = action_uuid;
        }
        else
        {
            return;
        }
    }
    AK_LOG_INFO << "WebPcapCaptureStart, add capture_mac = " << capture_mac <<  " success";
}

// web通知停止监控指定mac的抓包
void PcapCaptureControl::WebPcapCaptureStop(const std::string& mac, const std::string& action_uuid)
{
    std::string uuid;
    {
        std::lock_guard<std::mutex> lock(mac_uuid_map_mutex_);
        if (mac_uuid_map_.find(mac) != mac_uuid_map_.end())
        {
            uuid = mac_uuid_map_[mac];
        }
    }

    OnCaptureStop(uuid);

    {
        std::lock_guard<std::mutex> lock(capture_mac_mutex_);
        if (capture_mac_set_.find(mac) != capture_mac_set_.end())
        {
            capture_mac_set_.erase(mac);
        }
    }

    {
        std::lock_guard<std::mutex> lock(mac_actionuuid_map_mutex_);
        if (mac_actionuuid_map_.find(mac) != mac_actionuuid_map_.end())
        {
            mac_actionuuid_map_.erase(mac);
            AK_LOG_INFO << "WebPcapCaptureStop success, mac = " <<  mac << ", action_uuid = " << action_uuid;
        }
    }    
}

// 监听554端口,获取app的tcp ip:port
int PcapCaptureControl::OpenMainWorkerDevLive()
{
    AK_LOG_INFO << "OpenMainWorkerDevLive Set Filter Begin";

	pcap_live_rtsp_ = pcpp::PcapLiveDeviceList::getInstance().getPcapLiveDeviceByIp(gstCSVRTSPConf.csvrtsp_inner_ip);
    if (pcap_live_rtsp_ == NULL)
    {
        AK_LOG_WARN << "Cannot find interface with IPv4 address of " << gstCSVRTSPConf.csvrtsp_inner_ip;
        return -1;
    }
    
    if (!pcap_live_rtsp_->open())
    {        
        AK_LOG_WARN << "Cannot open device live";
        return -1;
    }
    
    pcpp::OrFilter ports_filter;
    pcpp::PortFilter port1(rtsp_listen_port1, pcpp::SRC_OR_DST);
    pcpp::PortFilter port2(rtsp_listen_port2, pcpp::SRC_OR_DST);
    ports_filter.addFilter(&port1);
    ports_filter.addFilter(&port2);

    pcpp::ProtoFilter protocol_filter(pcpp::TCP);

    pcpp::AndFilter and_filter;
    and_filter.addFilter(&ports_filter);
    and_filter.addFilter(&protocol_filter);
    pcap_live_rtsp_->setFilter(and_filter);

    pcap_live_rtsp_->startCapture(OnTcpPacketArrives, &pcap_live_rtsp_stats_);
    AK_LOG_INFO << "OpenMainWorkerDevLive Set Filter End";
	return 0;
}

// listen 554 port, wait for capture mac
void PcapCaptureControl::PcapCaptureController()
{
    // 开启tcp监控
    if (0 != OpenMainWorkerDevLive())
    {
        return;
    }

    // 初始化抓包列表
    dbinterface::PcapCaptureInfoList capture_info_list;
    if (0 == dbinterface::PcapCaptureControl::GetCaptureMacList(capture_info_list))
    {
        for (const auto& capture_info : capture_info_list)
        {
            if (capture_info.status == dbinterface::PCAP_STATUS_CAPTURING)
            {
                capture_mac_set_.insert(capture_info.mac);
                mac_actionuuid_map_[capture_info.mac] = capture_info.uuid;
                AK_LOG_INFO << "init capture mac list, add mac " << capture_info.mac << ", uuid " << capture_info.uuid << " success";
            }
        }
    }
    
    while(1)
    {
        sleep(100);
    }
}

int PcapCaptureControl::Init()
{
    pcap_capture_control_thread_ = std::thread(&PcapCaptureControl::PcapCaptureController, this);
    AK_LOG_INFO <<  "PcapCaptureControl Thread Start Success, thread id = " << pcap_capture_control_thread_.get_id();
    return 0;
}
