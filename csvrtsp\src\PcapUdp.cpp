#include <iostream>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <sstream>
#include "stdlib.h"
#include "AkLogging.h"
#include "CsvrtspConf.h"
#include "PcapUdp.h"
#include "PcapControl.h"
#include "PcapLiveDevice.h"

extern CSVRTSP_CONF gstCSVRTSPConf;

static void OnUdpPacketArrives(pcpp::RawPacket* packet, pcpp::PcapLiveDevice* dev, void* cookie)
{    
    pcpp::Packet parsedPacket(packet);
    pcpp::UdpLayer* udpLayer = parsedPacket.getLayerOfType<pcpp::UdpLayer>();
    if (udpLayer != nullptr) 
    {
        std::string *uuid = static_cast<std::string *>(cookie);
        GetPcapCaptureControlInstance()->WritePacket(*uuid, packet);
    }
}

PcapCaptureUdp::PcapCaptureUdp()
{
}

PcapCaptureUdp::~PcapCaptureUdp()
{
    AK_LOG_INFO << "~PcapCaptureUdp success uuid = " << uuid_;
}

void PcapCaptureUdp::Start()
{
    pcap_live_udp_ = std::make_shared<pcpp::PcapLiveDevice>("eth0", true, true, true);
    
    if (pcap_live_udp_ == nullptr)
    {
        AK_LOG_WARN << "PcapCaptureUdp Start error, pcap_live_udp_ is nullptr";
        return;
    }
    
    if (!pcap_live_udp_->open())
    {
        AK_LOG_WARN << "PcapCaptureUdp Start error, pcap_live_udp_ open failed";
        return;
    }
    
    pcpp::ProtoFilter protocol_filter(pcpp::UDP);
    
    pcpp::OrFilter ports_filter;
    pcpp::PortFilter app_rtp_port(app_udp_port_, pcpp::SRC_OR_DST);
    pcpp::PortFilter app_rtcp_port(app_udp_port_ + 1, pcpp::SRC_OR_DST);
    pcpp::PortFilter dev_rtp_port(dev_udp_port_, pcpp::SRC_OR_DST);
    pcpp::PortFilter dev_rtcp_port(dev_udp_port_ + 1, pcpp::SRC_OR_DST);
    ports_filter.addFilter(&app_rtp_port);
    ports_filter.addFilter(&app_rtcp_port);
    ports_filter.addFilter(&dev_rtp_port);
    ports_filter.addFilter(&dev_rtcp_port);

    pcpp::AndFilter and_filter;
    and_filter.addFilter(&ports_filter);
    and_filter.addFilter(&protocol_filter);
    pcap_live_udp_->setFilter(and_filter);

    pcap_live_udp_->startCapture(OnUdpPacketArrives, (void*)&uuid_);
    AK_LOG_INFO << "PcapUdp startCapture, app_udp_port:" << app_udp_port_ << ",dev_udp_port:" << dev_udp_port_ << ",dev_uuid " << uuid_;
	return;
}

void PcapCaptureUdp::Stop()
{
    if (pcap_live_udp_ != nullptr)
    {
        pcap_live_udp_->stopCapture();
        pcap_live_udp_->close();
    }
    AK_LOG_INFO << "PcapCaptureUdp Stop success uuid = " << uuid_;
}
