#include <iostream>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <sstream>
#include "stdlib.h"
#include "AkLogging.h"
#include "CsvrtspConf.h"
#include <PcapWriter.h>


PcapWriter::PcapWriter()
{
    
}

PcapWriter::PcapWriter(std::shared_ptr<pcpp::PcapFileWriterDevice> pcap_writer, const std::string& local_filepath, const std::string& mac, const std::time_t& timestamp)
{
    pcap_writer_ = pcap_writer;
    local_filepath_ = local_filepath;
    mac_ = mac;
    timestamp_ = timestamp;
}


PcapWriter::~PcapWriter()
{
    pcap_writer_->close();
    AK_LOG_INFO << "~PcapWriter()";
}


void PcapWriter::WritePacket(pcpp::RawPacket* packet)
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (pcap_writer_ != nullptr && pcap_writer_->isOpened() && packet != nullptr)
    {
        pcap_writer_->writePacket(*packet);
    }
}

void PcapWriter::Stop()
{
    std::lock_guard<std::mutex> lock(mutex_);
    pcap_writer_->close();
}

std::string PcapWriter::GetLocalFilePath()
{
    return local_filepath_;
}

std::time_t PcapWriter::GetTimestamp()
{
    return timestamp_;
}

std::string PcapWriter::GetMac()
{
    return mac_;
}

