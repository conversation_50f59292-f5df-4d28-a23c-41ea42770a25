#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <string>
#include "AK.Adapt.pb.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include <evpp/tcp_conn.h>
#include "rtp/RtpDeviceManager.h"
#include "rtp/RtpDeviceClient.h"
#include "AK.Route.pb.h"
#include "CachePool.h"
#include "CsvrtspConf.h"
#include "RtspMQProduce.h"
#include "Control.h"
#include "ipc.h"
#include "VrtspDefine.h"
#include "SnowFlakeGid.h"
#include "RouteClient.h"
#include "AkcsMonitor.h"
#include "RouteClientMng.h"
#include "PcapControl.h"

extern std::string g_logic_srv_id;
extern CSVRTSP_CONF gstCSVRTSPConf;
extern RouteMQProduce* g_nsq_producer;

using namespace akuvox;
CRouteClient::CRouteClient(evpp::EventLoop* loop,
                           const std::string& serverAddr/*ip:port*/,
                           const std::string& name,
                           const std::string& logic_srv_id)
    : client_(loop, serverAddr, name)
    , addr_(serverAddr)
    , route_codec_(std::bind(&CRouteClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2))
    , logic_srv_id_(logic_srv_id)
    , connect_status_(false)
    , ping_status_(true)
{
    conn_ = nullptr;
    client_.SetConnectionCallback(
        std::bind(&CRouteClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&AkcsIpcMsgCodec::OnMessage, &route_codec_, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(2.0));
    client_.set_auto_reconnect(true);
    loop->RunEvery(evpp::Duration(121.0), std::bind(&CRouteClient::onRoutePingCheckTimer, this));//121.0:保证csroute有两个周期的ping
}

CRouteClient::~CRouteClient()
{
    AK_LOG_INFO << "Release  ~CRouteClient: " << addr_ ;
}

void CRouteClient::Stop()
{
    if (connect_status_ == true) //当对端先断开的时候,已经通过CRouteClient::OnConnection 把connect_status_置为false了
    {
        client_.Disconnect();
        connect_status_ = false;
    }
    client_.set_auto_reconnect(false);
}

void CRouteClient::OnConnection(const evpp::TCPConnPtr& conn)
{
    if (conn->IsConnected())
    {
        connect_status_ = true;
        AK_LOG_INFO << "connect to route server " << addr_ << " successful.";
        //注册srv-id
        AK::Server::LogicSrvReg msg_logic_srv_reg;
        msg_logic_srv_reg.set_logic_srv_uid(logic_srv_id_);
        msg_logic_srv_reg.set_srv_type(AK::Base::LOGIC_CLIENT_TYPE_VRTSP);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg_logic_srv_reg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50); //ver=50
        pdu.SetCommandId(AKCS_MSG_L2R_REG_UID_REQ);
        pdu.SetSeqNum(0);
        conn->Send(pdu.GetBuffer(), pdu.GetLength());
        conn_ = conn;
    }
    else //参考: CRouteClientMng::UpdateRouteSrv 的逻辑处理
    {
        AK_LOG_WARN << "disconnect to route server " << addr_;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csvrtsp", "csvrtsp connect csroute error", AKCS_MONITOR_ALARM_MODULE_CONNECT_ERROR);
        if (connect_status_ == true)//对端先断开
        {
            connect_status_ = false;
            //client_.Disconnect();//当对端主动关闭的时候,本段立马执行关闭.
        }
        else
        {
            //本端先断开的情况,暂时不需要业务处理. 此时connect_status_ = false由CRouteClient::Stop操作.
        }
        conn_ = nullptr;
    }
}

//csvrtspd与csroute的tcp长连接消息,都是csroute的广播消息发往csvrtspd
void CRouteClient::OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu)
{
    uint64_t traceid = pdu->GetTraceId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    uint32_t msg_id = pdu->GetCommandId();
    AK_LOG_INFO << "csroute ser msg, pdumsg id is 0x" << std::hex << msg_id;
    switch (msg_id)
    {
        case AKCS_R2V_START_RTSP_REQ://收到route对于内部csvrtspd服务的分流请求
        {
            OnStartInnerRtsp(pdu);
            break;
        }
        case AKCS_R2V_STOP_RTSP_REQ:
        {
            OnStopInnerRtsp(pdu);
            break;
        }
        case AKCS_R2V_RTSP_CAPTURE_REQ:
        {
            OnRtspCapture(pdu);
            break;
        }
        case AKCS_R2V_KEEPALIVE_RTSP_REQ:
        {
            OnRtspInnerKeepAlive(pdu);
            break;
        }
        case AKCS_MSG_R2L_PING_REQ:
        {
            OnRoutePing();
            break;
        }
        case AKCS_R2V_PCAP_CAPTURE_REQ:
        {
            OnPcapCapture(pdu);
            break;
        }
        default:
        {
            AK_LOG_WARN << "csroute ser msg,invalid pdumsg id " << msg_id;
        }
    }

}

void CRouteClient::OnStartInnerRtsp(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::StartRtspReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    const std::string mac = msg.mac();
    const std::string ip = msg.ip();
    const uint32_t port = msg.port();
    const std::string vrtspd_logic_id = msg.vrtspd_logic_id();
    const std::string srtp_key = msg.srtp_key();
    const std::string ssrc = msg.ssrc();
    const std::string flow_uuid = msg.flow_uuid();
    int32_t ssrc_int = 0;
    sscanf(ssrc.c_str(), "%x", &ssrc_int);
    AK_LOG_INFO << "OnStartInnerRtsp, flow_uuid is " << flow_uuid << ", ip is " << ip << ", port is " << port << ", ssrc is " << std::hex << ssrc_int << ", srtp_key is " << srtp_key;

    std::shared_ptr<RtpDeviceClient> dev_rtp_client = RtpDeviceManager::getInstance()->GetClientByFlowUUID(flow_uuid);// 根据流的唯一标识
    if (dev_rtp_client == nullptr)
    {
        AK_LOG_WARN << "get device rtp client obj failed, flow uuid is " << flow_uuid;
        return ;
    }
    dev_rtp_client->SetAddingAppStatus();
    dev_rtp_client->AddInnerClient(mac, ip, port, vrtspd_logic_id, ssrc_int);
    dev_rtp_client->ResetAddingAppStatus();
}

void CRouteClient::OnStopInnerRtsp(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::StopRtspReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    const std::string mac = msg.mac();
    int is_third = msg.is_third();
    std::string dev_mac = msg.dev_mac();
    const std::string vrtspd_logic_id = msg.vrtspd_logic_id();
    uint64_t msg_traceid = pdu->GetTraceId();
    std::string flow_uuid = msg.flow_uuid();
    AK_LOG_INFO << "recv stop inner rtsp client, vrtspd logicid is " << vrtspd_logic_id << ", traceid is " << msg_traceid;

    //从dev列表中移除关联的inner client,并返回所有关联的dev列表
    unsigned short dev_port_to_del = 0;
    RtpDeviceManager::getInstance()->DeviceRemoveInnerClient(vrtspd_logic_id, dev_port_to_del, mac);
    if (dev_port_to_del != 0) //已经没有本端的app或者vrtsp集群内部转流服务器在监控该设备了
    {
        std::shared_ptr<RtpDeviceClient> pDevClient = RtpDeviceManager::getInstance()->GetClientAndRemove(dev_port_to_del);
        if (pDevClient != nullptr)
        {
            uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();

            AK::Route::StopRtspReq msg_stop_rtsp_reg;
            msg_stop_rtsp_reg.set_mac(mac);
            msg_stop_rtsp_reg.set_ip(gstCSVRTSPConf.csvrtsp_outer_ip);
            msg_stop_rtsp_reg.set_port(pDevClient->local_rtp_port_);
            msg_stop_rtsp_reg.set_vrtspd_logic_id(g_logic_srv_id);
            msg_stop_rtsp_reg.set_is_third(is_third);
            msg_stop_rtsp_reg.set_dev_mac(dev_mac);
            msg_stop_rtsp_reg.set_transfer_door_uuid(msg.transfer_door_uuid());
            msg_stop_rtsp_reg.set_transfer_indoor_mac(msg.transfer_indoor_mac());
            msg_stop_rtsp_reg.set_flow_uuid(flow_uuid);
            
            CAkcsPdu pdu;
            pdu.SetMsgBody(&msg_stop_rtsp_reg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50); //ver=50
            pdu.SetSeqNum(0);
            pdu.SetTraceId(traceid);
            AK_LOG_INFO << "send ipc stop rtsp, [locallogic_rtsp_srv_id is " << g_logic_srv_id << ",mac is " << mac << ", traceid is " << traceid << " ]";

            pdu.SetCommandId(AKCS_MSG_L2R_STOP_RTSP_REQ);
            CRouteClientMng::Instance()->SendRtspMsg(pDevClient->mac_, pdu);
            
            RtpDeviceManager::getInstance()->RemoveClient(dev_port_to_del);
        }
    }
}

void CRouteClient::OnRtspInnerKeepAlive(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::RtspKeepAliveReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    const std::string mac = msg.mac();
    const std::string vrtspd_logic_id = msg.vrtspd_logic_id();
    const std::string flow_uuid = msg.flow_uuid();
    AK_LOG_INFO << "receive rtsp inner keep alive req, mac is " << mac << ",  vrtspd_logic_id is " << vrtspd_logic_id;
    RtpDeviceManager::getInstance()->DeviceKeepAliveInnerClient(vrtspd_logic_id, flow_uuid);
}

void CRouteClient::OnRtspCapture(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Route::RtspCaptureReq msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    const std::string mac = msg.mac();
    const std::string pic_name = msg.pic_name();
    const std::string node = msg.node();
    const std::string user_name = msg.user_name();
    const std::string flow_uuid = msg.flow_uuid();
    AK_LOG_INFO << "receive rtsp capture req, mac is " << mac;
    SOCKET_MSG_CAPTURE_RTSP capture_rtsp;
    ::memset(&capture_rtsp, 0, sizeof(capture_rtsp));
    Snprintf(capture_rtsp.mac, sizeof(capture_rtsp.mac),  mac.c_str());
    Snprintf(capture_rtsp.szPicName, sizeof(capture_rtsp.szPicName),  pic_name.c_str());
    Snprintf(capture_rtsp.szNode, sizeof(capture_rtsp.szNode),  node.c_str());
    Snprintf(capture_rtsp.szUserName, sizeof(capture_rtsp.szUserName),  user_name.c_str());
    Snprintf(capture_rtsp.flow_uuid, sizeof(capture_rtsp.flow_uuid), flow_uuid.c_str());
    // 根据flow_uuid将其deviceClient的 capture_ 置为true; 并发送一条ipc给vrecord， 通知其打开截图线程
    GetControlInstance()->AddMsg(MSG_CTRL_START_CAPTURE, 0, 0, &capture_rtsp, sizeof(SOCKET_MSG_CAPTURE_RTSP));
}

void CRouteClient::OnPcapCapture(const std::unique_ptr<CAkcsPdu>& pdu)
{
    AK::Adapt::WebPcapCaptureNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));
    
    int type = msg.type();
    std::string mac = msg.mac();
    std::string action_uuid = msg.uuid();
    AK_LOG_INFO << "receive rtsp capture req, msg = " << msg.DebugString();

    // 开启抓包
    if (type == PCAP_CAPTURE_START)
    {
        GetPcapCaptureControlInstance()->WebPcapCaptureStart(mac, action_uuid);   
    }
    else if(type == PCAP_CAPTURE_STOP)
    {
        GetPcapCaptureControlInstance()->WebPcapCaptureStop(mac, action_uuid);    
    }   
}


void CRouteClient::OnRoutePing()
{
    ping_status_ = true;
}
void CRouteClient::onRoutePingCheckTimer()
{
    if ((ping_status_ == false) && (connect_status_ == true))
    {
        AK_LOG_WARN << "in one ping check loop, i donnot have received any ping msg from csroute, reconnect to csroute ";
        client_.Disconnect();
        client_.Connect();
        client_.set_auto_reconnect(true);
    }
    ping_status_ = false;
}

bool CRouteClient::IsConnStatus()
{
    return connect_status_ == true;
}

int CRouteClient::SendMsg(CAkcsPdu &pdu)
{
    int ret = -1;
    if (conn_ && conn_->IsConnected())
    {
        conn_->Send(pdu.GetBuffer(), pdu.GetLength());
        ret = 0;
        return ret;
    }
    return ret;
}

std::string CRouteClient::GetAddr()
{
    return addr_;
}


