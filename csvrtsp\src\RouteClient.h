#ifndef __CSVRTSP_ROUTE_CLIENT_H__
#define __CSVRTSP_ROUTE_CLIENT_H__

#include <evpp/tcp_client.h>
#include <memory>
#include <string>
#include <event_loop.h>
#include <atomic>
#include "AkcsIpcMsgCodec.h"
#include "AkLogging.h"
class CRouteClient;
typedef std::shared_ptr<CRouteClient> RouteClientPtr;

class CRouteClient
{
public:
    CRouteClient(evpp::EventLoop* loop,
                 const std::string& serverAddr/*ip:port*/,
                 const std::string& name,
                 const std::string& logic_srv_id);

    ~CRouteClient();
    void Start()
    {
        client_.Connect();
    }
    void Stop();
    bool IsConnStatus();
    int SendMsg(CAkcsPdu &pdu);
    std::string GetAddr();
private:
    void OnConnection(const evpp::TCPConnPtr& conn);
    void OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu);
    void OnStartInnerRtsp(const std::unique_ptr<CAkcsPdu>& pdu);
    void OnStopInnerRtsp(const std::unique_ptr<CAkcsPdu>& pdu);
    void OnRtspCapture(const std::unique_ptr<CAkcsPdu>& pdu);
    void OnRtspInnerKeepAlive(const std::unique_ptr<CAkcsPdu>& pdu);
    void OnPcapCapture(const std::unique_ptr<CAkcsPdu>& pdu);
    void OnRoutePing();
    void onRoutePingCheckTimer();
private:
    evpp::TCPClient client_;
    std::string addr_;//csroute srv的ip:port字符串
    AkcsIpcMsgCodec route_codec_;
    std::string logic_srv_id_;
    std::atomic<bool> connect_status_;//与tcp服务器是否连接的状态标示符
    std::atomic<bool> ping_status_;//ping的状态标记
    evpp::TCPConnPtr conn_;
};

#endif // __CSVRTSP_ROUTE_CLIENT_H__
