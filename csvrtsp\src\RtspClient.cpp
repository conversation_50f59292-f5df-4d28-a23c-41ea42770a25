#include "RtspClient.h"
#include "RtspParse.h"
#include "RtspServerImpl.h"
#include "AKLog.h"
#include <unistd.h>
#include <netinet/in.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <string.h>
#include "strDup.hh"
#include "Config.h"
#include "util.h"
#include "AkLogging.h"
#include "CsvrtspConf.h"
#include "ConfigOperator.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "encrypt/Md5.h"

using namespace toolkit;

extern CSVRTSP_CONF gstCSVRTSPConf;

namespace akuvox
{

RtspClient::RtspClient(int fd, const std::string& ip, unsigned short port, bool is_ipv6, uint64_t trace_id)
{
    rtsp_fd_ = fd;
    client_ip_ = ip;
    rtsp_port_ = port;
    client_rtp_port_ = 0;
    client_rtcp_port_ = 0;
    local_rtp_port_ = 0;
    is_ipv6_ = is_ipv6;
    rtsp_keep_alive_times_ = 0;
    guise_mac_ = false;
    status_ = kInit;
    have_third_camera_ = 0;
    video_pt_ = 96;
    video_type_ = RTSP_VIDEO_TYPE_H264_LARGE;
    need_transfer_ = false;
    dev_enable_srtp_ = false;
    dev_ssrc_ = 0;
    trace_id_ = trace_id;
    rtp_confuse_switch_ = false;
    dev_enable_rtp_confuse_ = false;
    channel_id_ = 0;
    stream_id_ = 1;
    app_client_ssrc_ = 0;
}

RtspClient::~RtspClient()
{
    AK_LOG_INFO << "[" << trace_id_ << "] ~RtspClient, fd = " << rtsp_fd_;
}

std::string RtspClient::toString()
{
    char infos[256] = { 0 };
    snprintf(infos, sizeof(infos),
             "RtspClient[fd=%d,rtsp port=%hu,app ip=%s, app_rtp_port=%hu,local_ip=%s,local_rtp_port=%hu,request_mac=%s]",
             rtsp_fd_, rtsp_port_, client_ip_.c_str(), client_rtp_port_, local_ip_.c_str(), local_rtp_port_, mac_.c_str());
             
    std::string info = infos;
    return info;
}

std::string RtspClient::GetLocalIp()
{
    local_ip_ = gstCSVRTSPConf.csvrtsp_outer_ip;
    return local_ip_;
}

std::string RtspClient::GetLocalIpv6()
{
    local_ipv6_ = gstCSVRTSPConf.csvrtsp_outer_ipv6;
    return local_ipv6_;
}

bool RtspClient::AlreadyGenerateSSRC()
{
    return dev_ssrc_ > 0;
}

bool RtspClient::AlreadySetup()
{
    return status_ >= RtspClient::Status::kSetup;
}

bool RtspClient::AlreadyDescribe()
{
    return status_ >= RtspClient::Status::kDesc;
}

void RtspClient::AddRtspDBLog()
{
    app_manual_log_.addRtspLog2DB();
}

void RtspClient::SetStatus(Status status)
{
    status_ = status;
}

uint64_t RtspClient::GetTraceID()
{
    return trace_id_;
}

void RtspClient::ConvertMonitoringDevice()
{
    // transfer_door_uuid_为空则不是hager转流;走西班牙转流流程
    if (need_transfer_ && transfer_indoor_mac_.empty())
    {
        std::string indoor_mac;
        if (0 == dbinterface::ResidentPerDevices::GetRepostDev(app_uid_, indoor_mac))
        {
            transfer_indoor_mac_ = indoor_mac;
        }
        else if (0 == dbinterface::ResidentDevices::GetRepostDev(app_uid_, indoor_mac))
        {
            transfer_indoor_mac_ = indoor_mac;
        }
        AK_LOG_INFO << "on convertMonitoringDevice, need_transfer: " << need_transfer_ << ",transfer_door_mac: " << mac_ << ",transfer_door_sip: " << transfer_door_uuid_ << ",indoor_mac: " << transfer_indoor_mac_;
    }
}

//flow uuid生成规则：mac+"ch"+通道+"stm"+码流
void RtspClient::GenerateFlowUUID()
{
    flow_uuid_ = mac_ + "ch" + std::to_string(channel_id_) + "stm" + std::to_string(stream_id_);
}

std::string RtspClient::GetFlowUUID()
{
    return flow_uuid_;
}

void RtspClient::SetChannelID(uint8_t channel_id)
{
    channel_id_ = channel_id;
}

uint8_t RtspClient::GetChannelID()
{
    return channel_id_;
}

void RtspClient::SetStreamID(uint8_t stream_id)
{
    stream_id_ = stream_id;
}

uint8_t RtspClient::GetStreamID()
{
    return stream_id_;
}

}
