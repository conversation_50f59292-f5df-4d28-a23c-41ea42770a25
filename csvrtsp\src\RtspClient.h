#ifndef __RTSP_CLIENT_H__
#define __RTSP_CLIENT_H__

#include <map>
#include <atomic>
#include <string>
#include <memory>
#include "RtspSession.h"
#include "AkcsCommonSt.h"
#include "AppManualRtspLog.h"

#define RTSP_BUFFER_SIZE    2048
#define RTSP_VIDEO_TYPE_H264_LARGE     "H264"
#define RTSP_VIDEO_TYPE_H265_LARGE     "H265"
#define RTSP_VIDEO_TYPE_H264_SMALL     "h264"
#define RTSP_VIDEO_TYPE_H265_SMALL     "h265"

namespace akuvox
{

//app的rtsp监控类
class RtspClient
{
public:
    enum Status {
        kInit = 0,
        kOption = 1,
        kDesc = 2,
        kSetup = 3,
        kPlay = 4,
    };

public:
    RtspClient(int fd, const std::string& ip, unsigned short port, bool is_ipv6, uint64_t trace_id);
    ~RtspClient();

    std::string GetLocalIp();
    std::string GetLocalIpv6();

    void SetStatus(Status status);
    void AddRtspDBLog();

    bool AlreadySetup();
    bool AlreadyDescribe();
    bool AlreadyGenerateSSRC();

    std::string toString();
    uint64_t GetTraceID();
    void ConvertMonitoringDevice();

    
    bool GetRtpConfuseSwitch()
    {
        return rtp_confuse_switch_;
    }
    void SetRtpConfuseSwitch(bool rtp_confuse_switch)
    {
        rtp_confuse_switch_ = rtp_confuse_switch;
    }
    void GenerateFlowUUID();
    std::string GetFlowUUID();
    void SetChannelID(uint8_t channel_id);
    uint8_t GetChannelID();
    void SetStreamID(uint8_t stream_id);
    uint8_t GetStreamID();



public:
    int rtsp_fd_;
    int rtsp_keep_alive_times_;

    unsigned short rtsp_port_;
    
    std::string client_ip_;
    std::string client_port_;        //用于缓存协商出来的客户端端口，字符串如client port %d-%d

    //SETUP rtsp://************:554/live/ch00_0/trackID=0 RTSP/1.0
    //CSeq: 6
    //Authorization: Digest username="user", realm="AK VRTSPD", nonce="f9f8bec00cd3432a695d38fbeca58101", uri="rtsp://************:554/live/ch00_0/", response="336fc239d563ea42e5b53912de3c1da0"
    //User-Agent: LibVLC/3.0.20 (LIVE555 Streaming Media v2016.11.28)
    //Transport: RTP/AVP;unicast;client_port=41886-41887
    unsigned short client_rtp_port_;    //rtsp信令中协商的客户端要使用的rtp传输端口(上述的client_port),实际云端看到的不一定是这个
    unsigned short client_rtcp_port_;   //协商出来的客户端rtcp端口

    unsigned short local_rtp_port_; //服务端监听rtsp客户端(app)的上报的rtp包端口,用于rtp over udp，以实现客户端的udp端口 nat
    std::string local_ip_;  //rtsp的外网ip (ipv4/ipv6的格式,具体哪种看配置文件是否需要将ipv6协议栈打开)
    std::string local_ipv6_;
    std::string mac_;
    std::string binded_mac_;
    int have_third_camera_;
    int dclient_ver_;

    std::string session_id_;
    bool is_ipv6_;

    std::string app_uid_;
    
    //手动监控记录
    akuvox::CAppManualRtspLog app_manual_log_;
    uint32_t dev_ssrc_;//设备流的ssrc
    uint32_t app_client_ssrc_;//app 发送端的ssrc,用于app rtcp接收报告

    bool guise_mac_;//小程序authcode和mac的对应关系
    std::string mac_authcode_;//小程序校验码  和mac的对应关系
    std::atomic<Status> status_;

    int video_pt_;
    std::string video_type_;
    std::string video_fmtp_;

    bool need_transfer_;               //是否需要转流
    std::string transfer_door_uuid_;  //西班牙转流为门口机的sip, hager转流为为门口机的mac
    std::string transfer_indoor_mac_; //转流室内机的mac

    bool dev_enable_srtp_;            // 设备是否支持srtp
    std::string srtp_key_;            // rtp加密的算法类型和key
    
    uint64_t trace_id_;
    bool dev_enable_rtp_confuse_;

private:
    bool rtp_confuse_switch_;
    std::string flow_uuid_;
    uint8_t channel_id_; //通道
    uint8_t stream_id_; // 码流
};

}

#endif
