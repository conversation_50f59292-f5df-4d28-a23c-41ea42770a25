#include <sys/types.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <pthread.h>
#include <netinet/tcp.h>
#include "ipc/ipc.h"
#include "rtp/RtpAppManager.h"
#include "rtp/RtpDeviceManager.h"
#include "RtspClientManager.h"
#include "RtspServerImpl.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "AK.Route.pb.h"
#include "RtspMQProduce.h"
#include "CsvrtspConf.h"
#include "SnowFlakeGid.h"
#include "AkcsMonitor.h"
#include "RouteClientMng.h"
#include "Metric.h"


extern RouteMQProduce* g_nsq_producer;
extern CSVRTSP_CONF gstCSVRTSPConf;
extern std::string g_logic_srv_id;

namespace akuvox
{
RtspClientManager::RtspClientManager()
{
}

RtspClientManager::~RtspClientManager()
{
    ClearClient();
}

RtspClientManager* RtspClientManager::instance = nullptr;

RtspClientManager* RtspClientManager::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtspClientManager;
    }
    return instance;
}

void RtspClientManager::AddClient(int socketid, const std::string& ip, unsigned short port, bool is_ipv6, uint64_t trace_id)
{
    std::shared_ptr<RtspClient> client = std::make_shared<RtspClient>(socketid, ip, port, is_ipv6, trace_id);
    client->GetLocalIp(); //added by chenyc,2018-05-23,在这里赋值rtsp服务地址
    client->GetLocalIpv6();
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    rtsp_client_map[socketid] = client; //不管是否发生 app rtsp client socket fd 复用，直接替换
}

void RtspClientManager::AddConcurrency(int socketid)
{
    //监控告警埋点,超过80路监控并发
    if (rtsp_client_concurrency_.size() >= 80)
    {
        std::string worker_node = "csvrtspd_";
        worker_node += gstCSVRTSPConf.csvrtsp_outer_ip;
        std::string des = "the num of rtsp client more than 80";
        char desc[128] = {0};
        ::snprintf(desc, 127, "the num of rtsp client is %lu, more than alarm num 80", rtsp_client_map.size());
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, desc, AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_ONE_DEVICE_RTSP);
    }

    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    rtsp_client_concurrency_.insert(socketid);
}

// 鉴权通过的客户端数量
int RtspClientManager::ConcurrencyClientNum() const
{
    return rtsp_client_concurrency_.size();
}

void RtspClientManager::RemoveClient(int socketid)
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    rtsp_client_map.erase(socketid);
    rtsp_client_concurrency_.erase(socketid);
    AK_LOG_INFO << "Remove RtspClient, fd =  " << socketid;
}


void RtspClientManager::ClearClient()
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    rtsp_client_map.clear();
    rtsp_client_concurrency_.clear();
}

int RtspClientManager::GetClientCount() const
{
    return rtsp_client_map.size();
}

std::shared_ptr<RtspClient> RtspClientManager::GetClient(int socketid)
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    auto iter = rtsp_client_map.find(socketid);
    if (iter != rtsp_client_map.end())
    {
        return iter->second;
    }
    return nullptr;
}

std::shared_ptr<RtspClient> RtspClientManager::GetClientByRtpPort(const unsigned short rtp_port)
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    for (const auto& client : rtsp_client_map)
    {
        std::shared_ptr<RtspClient> rtsp_client = client.second;
        if ( rtsp_client && rtsp_client->local_rtp_port_ == rtp_port )
        {
            return rtsp_client;
        }
    }
    return nullptr;
}

std::vector<int> RtspClientManager::GetAllClientSocket()
{
    std::vector<int> result;
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    for (auto client : rtsp_client_map)
    {
        result.push_back(client.first);
    }
    return result;
}

void RtspClientManager::ReportAll()
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);//TODO 提到栈上的临时变量
    std::map<int, std::shared_ptr<RtspClient>>::iterator iter;
    AK_LOG_INFO << "RTSP client count = " << rtsp_client_map.size();
    
    for (iter = rtsp_client_map.begin(); iter != rtsp_client_map.end(); ++iter)
    {
        AK_LOG_INFO << iter->second->toString();
    }
}

}

