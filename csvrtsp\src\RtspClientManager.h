#ifndef __RTSP_CLIENT_MANAGER_H__
#define __RTSP_CLIENT_MANAGER_H__

#include <map>
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include "RtspClient.h"


namespace akuvox
{
class RtspClientManager
{
public:
    ~RtspClientManager();
    static RtspClientManager* getInstance();

    void AddClient(int socketid, const std::string& ip, unsigned short port, bool is_ipv6, uint64_t trace_id);
    void AddConcurrency(int socketid);
    void RemoveClient(int socketid);
    int GetClientCount() const;
    int ConcurrencyClientNum() const;
    void ClearClient();
    std::shared_ptr<RtspClient> GetClient(int socketid);
    std::vector<int> GetAllClientSocket();
    void ReportAll();
    void CheckAppConnect();
    std::shared_ptr<RtspClient> GetClientByRtpPort(const unsigned short rtp_port);

private:
    RtspClientManager();

private:
    static RtspClientManager* instance;

    std::map<int/*rtsp sockfd*/, std::shared_ptr<RtspClient>> rtsp_client_map;  //rtsp客户端数量,tcp连接成功就加进来
    std::set<int> rtsp_client_concurrency_;    //rtsp并发数量统计与管理,只有rtsp鉴权通过的才统计进来
    std::recursive_mutex rtsp_client_mutex_;
};

}

#endif 
