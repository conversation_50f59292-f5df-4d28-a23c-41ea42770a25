#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <vector>
#include "CsvrtspConf.h"
#include "util.h"
#include "RouteClient.h"
#include "RouteClientMng.h"
#include "evpp/event_watcher.h"
#include "RtspEtcd.h"
#include "loop/RouteLoopManager.h"

extern CSVRTSP_CONF gstCSVRTSPConf;
extern std::string g_logic_srv_id;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern const char *g_ak_srv_route;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

//与所有的csroute建立tcp连接
void RouteSrvConnInit(const std::set<std::string>& csroute_addrs, evpp::EventLoop* loop,
                      const std::string& logic_srv_id)
{
    for (const auto& csroute : csroute_addrs) //ip:p ort的形式
    {
        RouteClientPtr route_cli_ptr(new CRouteClient(loop, csroute, "csvrtsp client", logic_srv_id));
        route_cli_ptr->Start();
        CRouteClientMng::Instance()->AddRouteSrv(csroute, route_cli_ptr);
    }
}

//监控的回调函数,不能阻塞
void UpdateRouteSrvList()
{
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) == 0)
    {
        //更新route的连接列表
        CRouteClientMng::Instance()->UpdateRouteSrv(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), g_logic_srv_id);
    }
}


int RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl,  int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}


void EtcdSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSVRTSPConf.etcd_server_addr);//"ip:port;ip:port"
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }

    RouteSrvConnInit(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), g_logic_srv_id);
    GetRouteLoopManagerInstance()->StartLoop();
    std::string inner_addr = GetEth0IPAddr();
    char outer_addr[256] = {0};
    const char *port = "554";
    ::snprintf(outer_addr, sizeof(outer_addr), "%s:%s&[%s]:%s&%s:%s", gstCSVRTSPConf.csvrtsp_outer_ip, port,  gstCSVRTSPConf.csvrtsp_outer_ipv6, port, gstCSVRTSPConf.csvrtsp_outer_domain, port);
    char inner_reg_info[128] = {0};
    char outer_reg_info[128] = {0};
    ::snprintf(inner_reg_info, 128, "%s%s", "/akcs/csvrtspd/innerip/", gstCSVRTSPConf.csvrtsp_outer_ip);//不管是内外网,均以外网来区分后缀,方便维护
    ::snprintf(outer_reg_info, 128, "%s%s", "/akcs/csvrtspd/outerip/", gstCSVRTSPConf.csvrtsp_outer_ip);
    //RegSrv2Etcd(inner_reg_info, inner_addr, 10, csbase::REG_INNER, etcd_loop.get());
    if (gstCSVRTSPConf.reg_etcd)
    {
        RegSrv2Etcd(outer_reg_info, outer_addr, 10, csbase::REG_OUTER, g_etcd_loop.get());
    }

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_route, UpdateRouteSrvList);
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get());

    g_etcd_loop->Run();//etcd_loop 目前只有route的连接在用  

}

