#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include<evpp/evnsq/message.h>
#include "RtspMQProduce.h"
#include "AkLogging.h"


const static uint32_t kAkMsgHoldLen = sizeof(int32_t);
bool g_nsq_ready = false;

int OnRouteMQMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void OnNSQReady()
{
    g_nsq_ready = true;
}

void OnConnectError(const std::string& addr)
{   
    AK_LOG_WARN << "Connect nsqd-" << addr << "error";
    ConnectNsqErrorMutt(addr, "csvrtsp");
}

bool RouteMQProduce::OnPublish(CAkcsPdu& pdu, const std::string& topic)
{
    const std::string msg(pdu.GetBuffer(), pdu.GetLength());
    if (!client_->Publish(topic, msg))
    {
        return false;
    }
    return true;
}

