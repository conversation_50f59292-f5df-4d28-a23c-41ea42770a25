#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netdb.h>
#include <vector>
#include <sys/socket.h>
#include "RtspParse.h"
#include <netinet/in.h>
#include <arpa/inet.h>
#include "util_string.h"
#include "utils.h"
#include "AKLog.h"
#include "AkLogging.h"
#include "strDup.hh"
#include "libvrtspd/CsvrtspConf.h"
#include "RtspServerImpl.h"
#include "RtspClientManager.h"
#include "RtspMonitor.h" 
#include "DigestAuthentication.h"
#include "encrypt/Md5.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "encrypt/Md5.h"
#include "util.h"


extern CSVRTSP_CONF gstCSVRTSPConf;
static std::string getProfileLevelId(char* mpSpsPtr)
{
    char buf[50] = { 0 };
    if (mpSpsPtr == NULL)
    {
        sprintf(buf, "profile-level-id=42E01F");
    }
    return std::string(buf);
}

static std::string getSpropParamterSets(char* mpSpsPtr, char* mpPpsPtr)
{
    char buf[255] = { 0 };
    if (mpSpsPtr == NULL)
    {
        sprintf(buf, "sprop-parameter-sets=Z0LAM6tAWgk0IAAAAwAgAAAGUeMGVA==,aM48gA==");
    }

    return std::string(buf);
}

void BuildRtspSDP(std::shared_ptr<akuvox::RtspClient> pAppRtspClient, char *buf, int len)
{
    if (!pAppRtspClient->have_third_camera_)
    {
        if (pAppRtspClient->is_ipv6_)
        {
            sprintf(buf, "v=0\r\no=- 1 1 IN IP6 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP6 0::0\r\nt=0 0\r\nm=video 0 RTP/AVP 96\r\nb=AS:5000\r\na=rtpmap:96 H264/90000\r\na=fmtp:96 %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                    gstCSVRTSPConf.csvrtsp_outer_ipv6, getProfileLevelId(NULL).c_str(), getSpropParamterSets(NULL, NULL).c_str());
        }
        else
        {
            sprintf(buf, "v=0\r\no=- 1 1 IN IP4 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP 96\r\nb=AS:5000\r\na=rtpmap:96 H264/90000\r\na=fmtp:96 %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                    gstCSVRTSPConf.csvrtsp_outer_ip, getProfileLevelId(NULL).c_str(), getSpropParamterSets(NULL, NULL).c_str());
        }
    }
    else if (pAppRtspClient->video_type_ == RTSP_VIDEO_TYPE_H264_LARGE || pAppRtspClient->video_type_ == RTSP_VIDEO_TYPE_H264_SMALL)
    {
        if (pAppRtspClient->video_fmtp_.empty())
        {
            // 使用默认的video_fmtp_
            if (pAppRtspClient->is_ipv6_)
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP6 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP6 0::0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H264/90000\r\na=fmtp:%d %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ipv6, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, 
                        pAppRtspClient->video_pt_, getProfileLevelId(NULL).c_str(), getSpropParamterSets(NULL, NULL).c_str());
            }
            else
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP4 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H264/90000\r\na=fmtp:%d %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ip, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, 
                        pAppRtspClient->video_pt_, getProfileLevelId(NULL).c_str(), getSpropParamterSets(NULL, NULL).c_str());
            }                
        }
        else
        {
            // 使用协商的video_fmtp_
            if (pAppRtspClient->is_ipv6_)
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP6 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP6 0::0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H264/90000\r\na=fmtp:%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ipv6, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, pAppRtspClient->video_fmtp_.c_str());
            }
            else
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP4 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H264/90000\r\na=fmtp:%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ip, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, pAppRtspClient->video_fmtp_.c_str());
            }    
        }
    }
    else //h265
    {
        if (pAppRtspClient->video_fmtp_.empty())
        {
            // 使用默认的video_fmtp_
            if (pAppRtspClient->is_ipv6_)
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP6 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP6 0::0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H265/90000\r\na=fmtp:%d %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ipv6, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, 
                        pAppRtspClient->video_pt_, "", getSpropParamterSets(NULL, NULL).c_str());
            }
            else
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP4 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H265/90000\r\na=fmtp:%d %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ip, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, 
                        pAppRtspClient->video_pt_, "", getSpropParamterSets(NULL, NULL).c_str());
            }  
        }
        else
        {
            // 使用协商的video_fmtp_
            if (pAppRtspClient->is_ipv6_)
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP6 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP6 0::0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H265/90000\r\na=fmtp:%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ipv6, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, pAppRtspClient->video_fmtp_.c_str());
            }
            else
            {
                snprintf(buf, len,  "v=0\r\no=- 1 1 IN IP4 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP %d\r\nb=AS:5000\r\na=rtpmap:%d H265/90000\r\na=fmtp:%s\r\na=control:trackID=0\r\n\r\n",
                        gstCSVRTSPConf.csvrtsp_outer_ip, pAppRtspClient->video_pt_, pAppRtspClient->video_pt_, pAppRtspClient->video_fmtp_.c_str());
            }  
        }
    }
    
    //std::string sdp = std::string(sdp_buf);
    //snprintf(buf, len,  "RTSP/1.0 200 OK\r\nCSeq: %d\r\nServer: Streaming Server v0.1\r\nContent-Base: rtsp://%s:554/live/ch00_0/\r\nContent-Type: application/sdp\r\nContent-Length: %d\r\n\r\n%s",
           //   pAppRtspClient->seq_num_, pAppRtspClient->local_ip_.c_str(), (int)sdp.size(), sdp.c_str());
}
/*
void ResponseRequest(int fd, std::shared_ptr<akuvox::RtspClient> &pAppRtspClient)
{
    char buf[2048] = { 0 };

    if (pAppRtspClient == nullptr)
    {
        CAKLog::LogE("RtspParse", "ResponseRequest client is null");
        return;
    }

    switch (pAppRtspClient->method_)
    {
        case RTSP_CMD_OPTIONS:
        {
            sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nServer: Easy Rtsp 1.0\r\nPublic: DESCRIBE, SETUP, TEARDOWN, PLAY, PAUSE, SET_PARAMETER, GET_PARAMETER\r\n\r\n",
                    pAppRtspClient->seq_num_);
            break;
        }

        case RTSP_CMD_DESCRIBE: //区分ipv6
        {
            BuildRtspSDP(pAppRtspClient, buf, sizeof(buf));
            break;
        }

        case RTSP_CMD_SETUP:
        {
            if (pAppRtspClient->client_port_.size() == 0) //证明是rtp over tcp,当前不支持
            {
                sprintf(buf, "RTSP/1.0 461 Unsupported transport\r\nCSeq: %d\r\nCache-Control: no-cache\r\nTransport: RTP/AVP/TCP;unicast;interleaved=0-1\r\nSession: %s\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->getRandSessionId().c_str());
            }
            else  //证明是rtp over udp ,那么rtsp服务端就需要监听一个udp端口来获取到客户端的rtp数据(一般是两个包)以实现客户端的udp NAT，获取到客户端的外网udp端口
            {
                sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nCache-Control: no-cache\r\nTransport: RTP/AVP;unicast;mode=play;%s;server_port=%d-%d\r\nSession: %s\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->client_port_.c_str(), pAppRtspClient->local_rtp_port_,
                        pAppRtspClient->local_rtp_port_ + 1, pAppRtspClient->getRandSessionId().c_str());
            }
            break;
        }

        case RTSP_CMD_PLAY:
        {
            //ipv6
            if (pAppRtspClient->is_ipv6_)
            {
                sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nSession: %s;timeout=60\r\nRange: npt=now-\r\nRTP-Info: url=rtsp://[%s]:554/live/ch00_0//trackID=0;seq=0;rtptime=0\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->getSessionId().c_str(), pAppRtspClient->GetLocalIpv6().c_str());

            }
            else
            {
                sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nSession: %s;timeout=60\r\nRange: npt=now-\r\nRTP-Info: url=rtsp://%s:554/live/ch00_0//trackID=0;seq=0;rtptime=0\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->getSessionId().c_str(), pAppRtspClient->local_ip_.c_str());
            }

            break;
        }

        case RTSP_CMD_SET_GET_PARAMETER:
        {
            char time_buf[100] = { 0 };
            struct tm* timeinfo;
            time_t rawtime;
            time(&rawtime);
            timeinfo = localtime(&rawtime);
            strftime(time_buf, sizeof(time_buf), "%a %b %d %H:%M:%S %Y", timeinfo);
            sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nDate: %s\r\n\r\n",
                    pAppRtspClient->seq_num_, time_buf);
        }
        break;

        case RTSP_CMD_TEARDOWN:
        {
            sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\n\r\n",
                    pAppRtspClient->seq_num_);
            break;
        }

        default:
            break;
    }
    if (strlen(buf) > 0)
    {
        write(fd, buf, strlen(buf));//TODO chenyc,2019-01-09,响应完teardown之后,服务端主动关闭tcp连接
    }
}


//int: 通过不同的返回值区分不同的错误.-1:内部错误;-2:客户端保活次数超过限制
int ParseRequest(int fd, std::shared_ptr<akuvox::RtspClient> client, const std::string &strRequest)
{
    if (client == nullptr)
    {
        CAKLog::LogE("RtspParse", "ParseRequest client is null");
        return -1;
    }

    std::vector<std::string> lines;
    std::string strSplit("\r\n");

    client->map_.clear();
    string_split(strRequest, strSplit, &lines);
    if (lines.size() < 2)
    {
        CAKLog::LogE("RtspParse", "field < 2....");
        return -1;
    }

    // 1.Method
    const char* cmd_names[] = { RTSP_CMD_OPTIONS_STR, RTSP_CMD_DESCRIBE_STR, RTSP_CMD_SETUP_STR, RTSP_CMD_TEARDOWN_STR,
                                RTSP_CMD_PLAY_STR, RTSP_CMD_PAUSE_STR, RTSP_CMD_SET_PARAMETER_STR, RTSP_CMD_SET_GET_PARAMETER_STR,
                                RTSP_CMD_SET_RECORD_STR,RTSP_CMD_SET_ANNOUNCE_STR
                              };
    for (int i = 0; i < (int)ARRAY_SIZE(cmd_names); i++)
    {
        if (lines[0].find(cmd_names[i]) != std::string::npos)
        {
            client->method_ = i;
            break;
        }
    }
    //如果不是以上的方法之一，那么证明是非法的请求,直接断开客户端的tcp 连接:
    if(client->method_ == -1)
    {
        CAKLog::LogE("RtspParse", "The msg is invalid rtsp request");
        return -1;
    }
    
    if (client->method_ == RTSP_CMD_SET_GET_PARAMETER)
    {
        client->rtsp_keep_alive_times_++;
        //add chenzhx 防止室内机一直监控 忘记关闭的问题。
        if (gstCSVRTSPConf.keep_alive_times > 0 && client->rtsp_keep_alive_times_ >= gstCSVRTSPConf.keep_alive_times)
        {
            CAKLog::LogD("RtspParse", "recv app rtsp keepalive,mac=%s, which is more than allowed %d times.", client->mac_.c_str(), gstCSVRTSPConf.keep_alive_times);
            return -2;
            //modified by chenyc,2019-12-02,不能直接使用close全关闭,否则后续rtsp fd复用的时候,RtpAppClient会发生交叉的问题
            //std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClientByRtspFd(client->rtsp_fd_);
            //close(client->rtsp_fd_);
        }
        else
        {
            CAKLog::LogD("RtspParse", "recv app rtsp keepalive,mac=%s   %d  %d ", client->mac_.c_str(), client->rtsp_keep_alive_times_, gstCSVRTSPConf.keep_alive_times);
            client->is_connect_ = true;
        }
    }
    else if ((client->method_ == RTSP_CMD_OPTIONS) || (client->method_ == RTSP_CMD_DESCRIBE))
    {
        std::vector<std::string> params;
        std::string strSplit(" ");
        string_split(lines[0], strSplit, &params);
        if (params.size() < 2)
        {
            CAKLog::LogE("RtspParse", "option field < 2");
            return -1;
        }

        std::size_t found = params[1].rfind("/");
        if (found == std::string::npos)
        {
            CAKLog::LogE("RtspParse", "cannot find mac");
            return -1;
        }

        int have_third_camera = 0;
        std::size_t found_third = params[1].rfind("TD-");
        client->mac_ = params[1].substr(found + 1);
        if (found_third != std::string::npos)
        {
            //三方摄像头的UUID,TD-MAC-UUID
            std::vector<std::string> auth_params;
            std::string strSplit("-");
            string_split(client->mac_, strSplit, &auth_params);
            client->binded_mac_ = auth_params[1];
            //因为uuid可能带-，所以通过剔除TD-MAC-来获取
            std::string td_mac = "TD-" + auth_params[1] + "-";
            std::vector<std::string> auth_params_uuid;
            std::string split_uuid(td_mac);
            string_split(client->mac_, split_uuid, &auth_params_uuid);
            client->mac_ = auth_params_uuid[1];

            have_third_camera = 1;
        }
        client->have_third_camera_ = have_third_camera;

        //add by chenzhx,在这里启动抓包程序，在setup抓包有点晚
        if (client->method_ == RTSP_CMD_DESCRIBE)
        {
            CRtspMonitor::Instance()->StartAPPMonitor(client->mac_, client->client_ip_);
        }

        //微信小程序
        if (client->method_ == RTSP_CMD_DESCRIBE && client->mac_.length() > 32 && have_third_camera == 0)
        {
            std::vector<std::string> auth_params;
            std::string strSplit("_");//account_mac_authcode authcode=md5($randomcode$mac$rtsppwd)
            string_split(client->mac_, strSplit, &auth_params);
            if (auth_params.size() == 3)
            {
                CAKLog::LogD("RtspParse", "check weixin app,mac=%s", client->mac_.c_str());            
                ResidentDev per_dev;
                memset(&per_dev, 0, sizeof(per_dev));
                ResidentDev dev;
                memset(&dev, 0, sizeof(dev));
                std::string rtsppwd;
                if (0 == dbinterface::ResidentDevices::GetMacDev(auth_params[1], dev))
                {
                    rtsppwd = dev.rtsppwd;
                }
                else if (0 == dbinterface::ResidentPerDevices::GetMacDev(auth_params[1], per_dev))
                {
                    rtsppwd = per_dev.rtsppwd;
                }
                std::string account_nonce = AuthNonceCache::getInstance()->nonceByAccount(auth_params[0]);
                if (account_nonce.size() > 0 && rtsppwd.size() > 0)
                {
                    std::string tmp = account_nonce + auth_params[1] + rtsppwd;                
                    std::string md5 = akuvox_encrypt::MD5(tmp).toStr();
                    if (strcmp(md5.c_str(), auth_params[2].c_str()) == 0)
                    {
                        client->guise_mac_ = true;
                        client->mac_authcode_ = client->mac_;
                        client->mac_ = auth_params[1]; 
                        CAKLog::LogD("RtspParse", "check weixin app,mac=%s success!", client->mac_.c_str() );   
                    }                      
                }
            }
            
        }
    }


    // 2.CSeq
    // added by chenyc, 2018-08-14, cseq不一定在第二行
    //if (lines[1].find("CSeq") == std::string::npos)
    //{
    //  CAKLog::LogE("RtspParse", "cannot find cseq");
    //  return;
    //}

    // 3.others info, store into client data map
    for (int i = 1; i < (int)lines.size(); i++)
    {
        int pos = lines[i].find(":");
        if (pos == (int)std::string::npos)
        {
            continue;
        }

        std::string strKey = lines[i].substr(0, pos);
        std::string strValue = lines[i].substr(pos + 2, lines[i].size());
        client->map_[strKey] = strValue;

        if (strKey.find("Transport") != std::string::npos)
        {
            //CAKLog::LogD("RtspParse", "strKey:%s %s", strKey.c_str(), strValue.c_str());
            std::vector<std::string> tmpVector;
            std::string strSplit = ";";
            string_split(strValue, strSplit, &tmpVector);
            for (int j = 0; j < (int)tmpVector.size(); j++)
            {
                if (tmpVector[j].find("client_port") != std::string::npos)
                {
                    client->client_port_ = tmpVector[j];
                    sscanf(client->client_port_.c_str(), "client_port=%hu-%hu", &client->client_rtp_port_, &client->client_rtcp_port_);
                    break;
                }
            }
        }
        else if (strKey.find("CSeq") != std::string::npos)
        {
            client->seq_num_ = ATOI(lines[i].c_str() + strlen("CSeq:"));
        }
    }
    return 0;
}
*/

bool parseAuthorizationHeader(char const* buf, char const*& username, char const*& realm, char const*& nonce, char const*& uri, char const*& response)
{
    CAKLog::LogD("RtspParse", "begin parseAuthorizationHeader");
    // Initialize the result parameters to default values:
    username = realm = nonce = uri = response = NULL;

    // First, find "Authorization:"
    while (1)
    {
        if (*buf == '\0')
        {
            return false;
        }
        if (strncasecmp(buf, "Authorization: Digest ", 22) == 0)
        {
            break;
        }
        ++buf;
    }

    // Then, run through each of the fields, looking for ones we handle:
    char const* fields = buf + 22;
    while (*fields == ' ')
    {
        ++fields;
    }
    char* parameter = strDupSize(fields);
    char* value = strDupSize(fields);
    while (1)
    {
        value[0] = '\0';
        if (sscanf(fields, "%[^=]=\"%[^\"]\"", parameter, value) != 2 &&
                sscanf(fields, "%[^=]=\"\"", parameter) != 1)
        {
            break;
        }
        if (strcmp(parameter, "username") == 0)
        {
            username = strDup(value);
        }
        else if (strcmp(parameter, "realm") == 0)
        {
            realm = strDup(value);
        }
        else if (strcmp(parameter, "nonce") == 0)
        {
            nonce = strDup(value);
        }
        else if (strcmp(parameter, "uri") == 0)
        {
            uri = strDup(value);
        }
        else if (strcmp(parameter, "response") == 0)
        {
            response = strDup(value);
        }

        fields += strlen(parameter) + 2 /*="*/ + strlen(value) + 1 /*"*/;
        while (*fields == ',' || *fields == ' ')
        {
            ++fields;
        }
        // skip over any separating ',' and ' ' chars
        if (*fields == '\0' || *fields == '\r' || *fields == '\n')
        {
            break;
        }
    }
    delete[] parameter;
    delete[] value;
    CAKLog::LogT("RtspParse", "end parseAuthorizationHeader");
    return true;
}

bool parseAuthorizationHeaderForAccount(char const* buf,char* pszUserAccount, int size)
{
    const char* pTmp = nullptr;
    const char* pTmpEnd = nullptr;
    if ((pTmp = strstr(buf, "Account: ")))
    {
        pTmpEnd = pTmp;
        int i = 0;
        while (*pTmpEnd && *pTmpEnd != '\r')
        {
            i++;
            pTmpEnd++;
        }
        if (size > i + 1)
        {
            ::snprintf(pszUserAccount, i - 8, "%s", pTmp + 9);
        }
        else
        {
            ::snprintf(pszUserAccount, size, "%s", pTmp + 9);
        }
        return true;
    }
    return false;
}

bool parseAuthorizationHeaderForManual(char const* buf, int& manual)
{
    const char* pTmp = nullptr;
    const char* pTmpEnd = nullptr;
    if ((pTmp = strstr(buf, "Manual: ")))
    {
        pTmpEnd = pTmp;
        int i = 0;
        while (*pTmpEnd && *pTmpEnd != '\r')
        {
            i++;
            pTmpEnd++;
        }
        char szManual[8];
        ::snprintf(szManual, i, "%s", pTmp + 8);
        manual = ATOI(szManual);
        return true;
    }
    return false;
}

// 监控设备eg: 新-rtsp://120.79.51.88:554/0A0203200117/001 旧-rtsp://120.79.51.88:554/0A0203200117
// 监控三方摄像头eg: 新-rtsp://39.108.105.163/TD-0A0203200117-dv-22e9a0d577c311ee853200163e047e78/001 旧-rtsp://39.108.105.163/TD-0A0203200117-dv-22e9a0d577c311ee853200163e047e78
bool parseFullUrlInfo(const std::string& fullurl, std::shared_ptr<akuvox::RtspClient>& app_rtsp_client)
{
    size_t last_slash_pos = fullurl.rfind('/');
    if (last_slash_pos == std::string::npos)
    {
        AK_LOG_INFO << "fullurl:" << fullurl << ", format incorrect";
        return false;
    }
    std::string monitor_device_info_url;
    std::string last_slash_pos_url = fullurl.substr(last_slash_pos + 1);
    monitor_device_info_url = last_slash_pos_url;
    //新url格式，获取通道+码流信息
    if (last_slash_pos_url.size() <= RTSP_RESOURCE_IDENTIFY_MAX_LENGTH && last_slash_pos_url.size() >= RTSP_RESOURCE_IDENTIFY_MIN_LENGTH )
    {
        ParseFlowUrlInfo(last_slash_pos_url, app_rtsp_client);
        size_t second_last_slash_pos = fullurl.rfind('/', last_slash_pos - 1);
        if (second_last_slash_pos == std::string::npos)
        {
            AK_LOG_INFO << "fullurl:" << fullurl << ", format incorrect";
            return false;
        }
        monitor_device_info_url = fullurl.substr(second_last_slash_pos + 1, last_slash_pos - second_last_slash_pos - 1);
    }

    ParseMonitorDeviceUrlInfo(monitor_device_info_url, app_rtsp_client);
    app_rtsp_client->GenerateFlowUUID(); //生成流的唯一标识
    AK_LOG_INFO << "rtsp full url:" << fullurl << ".monitor channel=" << (int)app_rtsp_client->GetChannelID() << ", stream=" << (int)app_rtsp_client->GetStreamID() << ",flow uuid=" << app_rtsp_client->GetFlowUUID();

    //微信小程序
    if (app_rtsp_client->mac_.length() > 32 && app_rtsp_client->have_third_camera_ == 0)
    {
        ParseWeChatUrlInfo(app_rtsp_client);
    }

    return true;
}
//监控AK设备：rtsp://120.79.51.88:554/0A0203200117
//监控三方摄像头：rtsp://39.108.105.163/TD-0A0203200117-dv-22e9a0d577c311ee853200163e047e78
void ParseMonitorDeviceUrlInfo(const std::string& sub_url, std::shared_ptr<akuvox::RtspClient>& app_rtsp_client)
{
    // 判断是否监控三方摄像头
    std::size_t found_third_camera = sub_url.rfind("TD-");
    if (found_third_camera != std::string::npos)
    {  
        // 按"-"分割sub_fullurl
        std::vector<std::string> split_contents;
        SplitString(sub_url, "-", split_contents);
        
        // 三方摄像头绑定的mac
        app_rtsp_client->binded_mac_ = split_contents[1];
        
        // 三方摄像头的uuid
        app_rtsp_client->mac_ = split_contents[2] + "-" + split_contents[3];

        // 标识本次监控三方摄像头
        app_rtsp_client->have_third_camera_ = 1;
    }
    else
    {
        // 监控设备的mac
        app_rtsp_client->mac_ = sub_url;
    }
}

//{通道+码流}处理  001:零通道主码流  102:一通道子码流 1304:十三通道四号码流
void ParseFlowUrlInfo(const std::string& sub_url, std::shared_ptr<akuvox::RtspClient>& app_rtsp_client)
{
    if (sub_url.length() < RTSP_RESOURCE_IDENTIFY_MIN_LENGTH)
    {
        AK_LOG_WARN << "sub url format wrong. sub url:" << sub_url;
        return;
    }

    std::string url_stream = sub_url.substr(sub_url.length() - 2, 2);
    std::string url_channel = sub_url.substr(0, sub_url.length() - 2);

    uint8_t channel_id = static_cast<uint8_t>(std::stoul(url_channel));
    uint8_t stream_id = static_cast<uint8_t>(std::stoul(url_stream));

    app_rtsp_client->SetChannelID(channel_id);
    app_rtsp_client->SetStreamID(stream_id);
}

//微信小程序监控信息解析
void ParseWeChatUrlInfo(std::shared_ptr<akuvox::RtspClient>& app_rtsp_client)
{
    std::vector<std::string> auth_params;
    std::string strSplit("_");//account_mac_authcode authcode=md5($randomcode$mac$rtsppwd)
    string_split(app_rtsp_client->mac_, strSplit, &auth_params);
    if (auth_params.size() == 3)
    {
        CAKLog::LogD("RtspParse", "check weixin app,mac=%s", app_rtsp_client->mac_.c_str());   
        ResidentDev dev;
        ResidentDev per_dev;
        std::string rtsppwd;
        if (0 == dbinterface::ResidentDevices::GetMacDev(auth_params[1], dev))
        {
            rtsppwd = dev.rtsppwd;
        }
        else if (0 == dbinterface::ResidentPerDevices::GetMacDev(auth_params[1], per_dev))
        {
            rtsppwd = per_dev.rtsppwd;
        }
        std::string account_nonce = AuthNonceCache::getInstance()->nonceByAccount(auth_params[0]);
        if (account_nonce.size() > 0 && rtsppwd.size() > 0)
        {
            std::string tmp = account_nonce + auth_params[1] + rtsppwd;                
            std::string md5 = akuvox_encrypt::MD5(tmp).toStr();
            if (strcmp(md5.c_str(), auth_params[2].c_str()) == 0)
            {
                app_rtsp_client->guise_mac_ = true;
                app_rtsp_client->mac_ = auth_params[1]; 
                app_rtsp_client->mac_authcode_ = app_rtsp_client->mac_;
                CAKLog::LogD("RtspParse", "check weixin app,mac=%s success!", app_rtsp_client->mac_.c_str() );   
            }                      
        }
    }
}
