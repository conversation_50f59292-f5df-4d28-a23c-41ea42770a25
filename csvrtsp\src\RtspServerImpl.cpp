#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <sstream>
#include <functional>
#include "RtspServerImpl.h"
#include "RtspClientManager.h"
#include "AK.Route.pb.h"
#include "RtspMQProduce.h"
#include "CsvrtspConf.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "Singleton.h"
#include "AkcsMonitor.h"
#include "RouteClientMng.h"
#include "AkcsBussiness.h"
#include "PcapControl.h"
#include "AkLogging.h"
#include "util.h"
#include "RtpEpollThread.h"

extern RouteMQProduce* g_nsq_producer;
extern std::string g_logic_srv_id;

#define D_CLIENT_VERSION_4400  4400

extern CSVRTSP_CONF gstCSVRTSPConf;


namespace akuvox
{

CRtspServerImpl* CRtspServerImpl::gRtspServer;
CRtspServerImpl::CRtspServerImpl()
{
}

CRtspServerImpl::~CRtspServerImpl()
{
}

CRtspServerImpl* CRtspServerImpl::GetInstance()
{
    if (gRtspServer == NULL)
    {
        gRtspServer = new CRtspServerImpl();
    }

    return gRtspServer;
}

int CRtspServerImpl::start()
{
    AK_LOG_INFO << "CRtspServerImpl start";

    //added by chenyc,2022-08-11,设置判断客户端攻击时的相关参数,当满足条件时,BussinessLimit会回调cb
    using namespace std::placeholders;
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(VRTSP_AUTH_BUSSINESS, BUSSINESS_PERIOD,BUSSINESS_NUM, BUSSINESS_KEY_EXPIRE, std::bind(&CRtspServerImpl::AttackedCallback, this, _1, _2));
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(VRTSP_INVALID_MSG_BUSSINESS, BUSSINESS_PERIOD, BUSSINESS_NUM, BUSSINESS_KEY_EXPIRE, std::bind(&CRtspServerImpl::AttackedCallback, this, _1, _2));
                                
    // rtp epoll收包处理线程
    akuvox::RtpEpollThread::getInstance()->Start();

    // rtp 包分发处理线程
    akuvox::RtpProcessThread::getInstance()->Start(gstCSVRTSPConf.rtp_process_thread_num);
    
    return 0;
}

int CRtspServerImpl::stop()
{
    akuvox::RtpEpollThread::getInstance()->Stop();
    akuvox::RtpProcessThread::getInstance()->Stop();
    return 0;
}

void CRtspServerImpl::report()
{
    AK_LOG_INFO << "start report all info--------------------------";
    RtspClientManager::getInstance()->ReportAll();
    RtpAppManager::getInstance()->ReportAll();
    RtpDeviceManager::getInstance()->ReportAll();
    AK_LOG_INFO << "stop report all info--------------------------";
}

void CRtspServerImpl::InitDevRtpClient(std::shared_ptr<RtspClient>& app_rtsp_client, std::shared_ptr<RtpDeviceClient>& dev_rtp_client)
{
    dev_rtp_client->mac_ = app_rtsp_client->mac_;
    dev_rtp_client->channel_id_ = app_rtsp_client->GetChannelID();
    dev_rtp_client->stream_id_ = app_rtsp_client->GetStreamID();
    dev_rtp_client->SetTraceID(app_rtsp_client->trace_id_);
    dev_rtp_client->SetSrtpKey(app_rtsp_client->srtp_key_);
    dev_rtp_client->SetDclientVer(app_rtsp_client->dclient_ver_);

    // ssrc在srtp协商时生成,srtp加解密需要用到ssrc,多个app同时监控时ssrc要一致
    if (app_rtsp_client->AlreadyGenerateSSRC())
    {
        dev_rtp_client->SetSsrc(app_rtsp_client->dev_ssrc_);
    }
    
    // ssrc未经过srtp协商生成 且 (设备dclient版本 >= 4400 或者 西班牙转流场景下生成ssrc)
    if (!dev_rtp_client->AlreadyGenerateSSRC() && (dev_rtp_client->dclient_ver_ >= D_CLIENT_VERSION_4400 || app_rtsp_client->need_transfer_))
    {
        dev_rtp_client->SetSsrc(rand());
        app_rtsp_client->dev_ssrc_ = dev_rtp_client->ssrc_;
        AK_LOG_INFO << "[" << app_rtsp_client->trace_id_ << "], generate dev ssrc = " << dev_rtp_client->ssrc_;
    }

    if (app_rtsp_client->need_transfer_)
    {
        dev_rtp_client->need_transfer_ = true;
        dev_rtp_client->SetTransferDoorUUID(app_rtsp_client->transfer_door_uuid_);
        dev_rtp_client->SetTransferIndoorMac(app_rtsp_client->transfer_indoor_mac_);
    }

    //有三方摄像头时为绑定设备的mac,没有三方摄像头时为被监控的设备mac
    if (app_rtsp_client->have_third_camera_)
    {
        dev_rtp_client->have_third_camera_ = 1;
        dev_rtp_client->dev_mac_ = app_rtsp_client->binded_mac_;
    }
    else
    {
        dev_rtp_client->dev_mac_ = dev_rtp_client->mac_;
    }

    dev_rtp_client->SetRtpConfuseSwitch(app_rtsp_client->dev_enable_rtp_confuse_);
}

void CRtspServerImpl::InitAppRtpClient(std::shared_ptr<RtspClient>& app_rtsp_client, std::shared_ptr<RtpAppClient>& app_rtp_client, std::shared_ptr<RtpDeviceClient>& dev_rtp_client)
{
    // 设置dev正在添加app状态
    dev_rtp_client->SetAddingAppStatus();

    // 设置app监控的dev对象
    app_rtp_client->setDeviceClient(dev_rtp_client);

    // 设置app的rtp port
    app_rtsp_client->local_rtp_port_ = app_rtp_client->local_rtp_port_;

    //设置app侧的rtp混淆开关
    app_rtp_client->SetRtpConfuseSwitch(app_rtsp_client->GetRtpConfuseSwitch());

    //设置app的流标识
    app_rtp_client->app_client_ssrc_ = app_rtsp_client->app_client_ssrc_;

    // 将本地为接受app穿越nat的udp包的fd加入udp的eventloop
    // 将app的rtp-fd添加到eventloop监听事件丿此时,设备的rtp-fd还不需要添加进县待app执行rtsp-play,再添加也不迟
    RtpEpollThread::getInstance()->AddRtpSocket(app_rtp_client->rtp_fd_, app_rtp_client->rtcp_fd_, app_rtsp_client->trace_id_);
    
    // 建立一台设备需要给几台app发送rtp监控数据的映射关系
    RtpDeviceManager::getInstance()->DeviceAddApp(dev_rtp_client->local_rtp_port_, app_rtp_client);
}

bool CRtspServerImpl::DevMonitorNumLimit(std::shared_ptr<RtspClient> app_rtsp_client,std::shared_ptr<RtpDeviceClient> dev_rtp_client)
{
    if (dev_rtp_client->MonotorAppNum() > 100)
    {
        //TODO:add chenzhx:RemoveClient里面有判断是不是有app在监控，所以这边可以直接操作。
        //主要是因为AddClient返回的可能是已经存在的，而不是新申请的。所以移除要判断。
        //不然DeviceAddApp这个关系如果未建立，那么超时检测也会删除不了pDevClient。那么通过fd查找pDevClient就会异常
        RtpDeviceManager::getInstance()->RemoveClient(dev_rtp_client->local_rtp_port_);
        return true;
    }
    return false;
}

bool CRtspServerImpl::HandleSetup(std::shared_ptr<RtspClient>& app_rtsp_client)
{
    uint64_t trace_id = app_rtsp_client->trace_id_;
    AK_LOG_INFO << "[" << trace_id << "] begin setup, app_rtsp_client socket fd = " << app_rtsp_client->rtsp_fd_;

    if (!app_rtsp_client->AlreadyDescribe())
    {
        AK_LOG_WARN << "[" << trace_id << "] must describe before setup, mac = " << app_rtsp_client->mac_;
        return false;
    }

    AK_LOG_INFO << "[" << trace_id << "] monitor flow uuid = " << app_rtsp_client->GetFlowUUID(); 
    
    std::shared_ptr<RtpDeviceClient> dev_rtp_client = RtpDeviceManager::getInstance()->AddClient(trace_id, app_rtsp_client->GetFlowUUID());
    if (dev_rtp_client == nullptr)
    {
        AK_LOG_WARN << "[" << trace_id << "] dev_rtp_client is nullptr, mac = " << app_rtsp_client->mac_;
        return false;
    }

    std::string monitor_ip_list = gstCSVRTSPConf.monitor_ip_list;
    if (monitor_ip_list.find(app_rtsp_client->client_ip_) != std::string::npos)
    {
        dev_rtp_client->SetClientIsMonitorIP();
    }
    
    // 单台设备超过一定数量的app时,超过部分就不再处理
    if (DevMonitorNumLimit(app_rtsp_client, dev_rtp_client))
    {
        AK_LOG_WARN << "[" << trace_id << "] there has been more than 100 app rtsp client for mac = " << app_rtsp_client->mac_;
        return false;
    }
    
    // 初始化dev_rtp_client
    InitDevRtpClient(app_rtsp_client, dev_rtp_client);
    
    std::shared_ptr<RtpAppClient> app_rtp_client = RtpAppManager::getInstance()->AddClient(trace_id, app_rtsp_client->rtsp_fd_);
    if (app_rtp_client == nullptr)
    {
        AK_LOG_WARN << "[" << trace_id << "] app_rtp_client is nullptr, mac = " << app_rtsp_client->mac_;
        RtpDeviceManager::getInstance()->RemoveClient(dev_rtp_client->local_rtp_port_);
        return false;
    }

    // 初始化app_rtp_client
    InitAppRtpClient(app_rtsp_client, app_rtp_client, dev_rtp_client);

    // 设置app rtsp信令状态机
    app_rtsp_client->SetStatus(RtspClient::Status::kSetup);

    AK_LOG_INFO << "[" << trace_id << "] end setup, app_rtsp_client socket fd = " << app_rtsp_client->rtsp_fd_;
    return true;
}

bool CRtspServerImpl::DevAlreadyPlay(std::shared_ptr<RtspClient> app_rtsp_client, std::shared_ptr<RtpDeviceClient> dev_rtp_client)
{
    if (dev_rtp_client->state_ == DEV_STATE_PLAY)
    {
        AK_LOG_INFO << "[" << app_rtsp_client->trace_id_ << "] dev already paly, there is other app monitoring dev = " << app_rtsp_client->mac_;

        // 已经有app在监控设备了
        std::string rtsp_uuid = PcapCaptureControl::CreateCaptureUUID(app_rtsp_client->client_ip_, app_rtsp_client->rtsp_port_);
        GetPcapCaptureControlInstance()->OnUdpPortDispatch(rtsp_uuid, app_rtsp_client->local_rtp_port_, dev_rtp_client->local_rtp_port_);
        return true;
    }
    return false;
}

bool CRtspServerImpl::HandlePlay(std::shared_ptr<RtspClient>& app_rtsp_client)
{
    uint64_t trace_id = app_rtsp_client->trace_id_;
    uint8_t channel_id = app_rtsp_client->GetChannelID();
    uint8_t stream_id = app_rtsp_client->GetStreamID();
    AK_LOG_INFO << "[" << trace_id << "] begin play, app_rtsp_client socket fd = " << app_rtsp_client->rtsp_fd_;

    // 判断app是否执行过setup
    if (!app_rtsp_client->AlreadySetup())
    {
        AK_LOG_WARN << "[" << trace_id << "] handle play, must setup before play, MAC is " << app_rtsp_client->mac_;
        return false;
    }
    //根据流的唯一标识获取dev rtp client
    std::shared_ptr<RtpDeviceClient> dev_rtp_client = RtpDeviceManager::getInstance()->GetClientByFlowUUID(app_rtsp_client->GetFlowUUID());
    if (dev_rtp_client == nullptr)
    {
        AK_LOG_WARN << "[" << trace_id << "] handle play, dev rtp client is nullptr, mac = " << app_rtsp_client->mac_;
        return false;
    }

    // 先判断是否已经有app执行过play指令了, 避免多次执行csvrtspd->csmain,下发设备上报视频流的指令
    if (DevAlreadyPlay(app_rtsp_client, dev_rtp_client))
    {
        return true;
    }

    if (dev_rtp_client->state_ == DEV_STATE_INIT)
    {
        dev_rtp_client->state_ = DEV_STATE_PLAY;
        
        // 将设备的发流rtp-fd添加到eventloop监听事件中
        RtpEpollThread::getInstance()->AddRtpSocket(dev_rtp_client->rtp_fd_, dev_rtp_client->rtcp_fd_, trace_id);

        std::string camera_name = GetCameraNameByChannelID(channel_id);

        // 发送play指令到csroute
        AK::Route::StartRtspReq msg_start_rtsp_reg;
        msg_start_rtsp_reg.set_mac(dev_rtp_client->mac_);
        msg_start_rtsp_reg.set_dev_mac(dev_rtp_client->dev_mac_);
        msg_start_rtsp_reg.set_ip(app_rtsp_client->local_ip_);
        msg_start_rtsp_reg.set_ipv6(app_rtsp_client->local_ipv6_);
        msg_start_rtsp_reg.set_port(dev_rtp_client->local_rtp_port_);
        msg_start_rtsp_reg.set_vrtspd_logic_id(g_logic_srv_id); //csvrtsp_39.108.105.163
        msg_start_rtsp_reg.set_is_third(app_rtsp_client->have_third_camera_);
        msg_start_rtsp_reg.set_video_pt(app_rtsp_client->video_pt_);
        msg_start_rtsp_reg.set_video_type(app_rtsp_client->video_type_);
        msg_start_rtsp_reg.set_video_fmtp(app_rtsp_client->video_fmtp_);
        msg_start_rtsp_reg.set_srtp_key(app_rtsp_client->srtp_key_);
        msg_start_rtsp_reg.set_transfer_door_uuid(app_rtsp_client->transfer_door_uuid_);
        msg_start_rtsp_reg.set_transfer_indoor_mac(app_rtsp_client->transfer_indoor_mac_);        
        msg_start_rtsp_reg.set_rtp_confuse(dev_rtp_client->GetRtpConfuseSwitch());
        msg_start_rtsp_reg.set_flow_uuid(app_rtsp_client->GetFlowUUID());
        msg_start_rtsp_reg.set_camera(camera_name);
        msg_start_rtsp_reg.set_stream_id(stream_id);

        char ssrc[32] = "";
        snprintf(ssrc, sizeof(ssrc), "%X", dev_rtp_client->ssrc_);
        msg_start_rtsp_reg.set_ssrc(ssrc);

        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg_start_rtsp_reg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50); //ver=50        
        pdu.SetSeqNum(0);
        pdu.SetTraceId(trace_id);
        pdu.SetCommandId(AKCS_MSG_L2R_START_RTSP_REQ);
        CRouteClientMng::Instance()->SendRtspMsg(dev_rtp_client->mac_, pdu);
      
        AK_LOG_INFO << "==========================Start RTSP======Check Local Rtp Port Here=========================================";
        AK_LOG_INFO << "[" << trace_id << "] send start rtsp msq, local dev rtp port = " << dev_rtp_client->local_rtp_port_ << ", local app rtp port = " << app_rtsp_client->local_rtp_port_ ;
        AK_LOG_INFO << "[" << trace_id << "] send start rtsp msq, monitor client account = " << app_rtsp_client->app_uid_ << " url monitor mac = " << dev_rtp_client->mac_.c_str() << ", monitor camera = " << camera_name << ", monitor stream = " << (int)stream_id << ", flow uuid=" << app_rtsp_client->GetFlowUUID() << ", ssrc = 0X" << dev_rtp_client->ssrc_;
        AK_LOG_INFO << "[" << trace_id << "] send start rtsp msq, video_type = " << app_rtsp_client->video_type_ << ", video_pt = "<< app_rtsp_client->video_pt_ << ", video_fmtp = " << app_rtsp_client->video_fmtp_;
        AK_LOG_INFO << "[" << trace_id << "] send start rtsp msq, transfer_door_uuid=" << app_rtsp_client->transfer_door_uuid_ << ", transfer_indoor_mac=" << app_rtsp_client->transfer_indoor_mac_ << ", srtp_key = " << app_rtsp_client->srtp_key_;
        AK_LOG_INFO << "==========================Start RTSP======Check Local Rtp Port Here=========================================";
        
        // 开启抓包
        std::string rtsp_uuid = PcapCaptureControl::CreateCaptureUUID(app_rtsp_client->client_ip_, app_rtsp_client->rtsp_port_);
        GetPcapCaptureControlInstance()->OnUdpPortDispatch(rtsp_uuid, app_rtsp_client->local_rtp_port_, dev_rtp_client->local_rtp_port_);
    }
    return true;
}

//added by chenyc,2024.01.30,HandleClose是最终所有关闭rtsp客户端必经的路径,在这个函数里面处理所有资源释放的动作.
bool CRtspServerImpl::HandleClose(std::shared_ptr<RtspClient> &rtsp_client)
{
    rtsp_client->AddRtspDBLog();

    int rtsp_client_fd = rtsp_client->rtsp_fd_;
    uint64_t trace_id = rtsp_client->GetTraceID();
    std::string local_ip = rtsp_client->local_ip_;
    int have_third_camera = rtsp_client->have_third_camera_;
    std::string srtp_key = rtsp_client->srtp_key_;
    std::string transfer_door_uuid = rtsp_client->transfer_door_uuid_;
    std::string transfer_indoor_mac = rtsp_client->transfer_indoor_mac_;
    std::string camera_name = GetCameraNameByChannelID(rtsp_client->GetChannelID());
    int stream_id = rtsp_client->GetStreamID();
    
    AK_LOG_INFO << "[" << trace_id << "] begin close, rtsp client socket fd = " <<  rtsp_client_fd;

    // 一定要先销毁RtspClient, method not allowed或其他异常情况下还未生成rtp_app_client资源,会直接return
    RtspClientManager::getInstance()->RemoveClient(rtsp_client_fd);
    
    std::shared_ptr<RtpAppClient> rtp_app_client = RtpAppManager::getInstance()->GetClientByRtspFd(rtsp_client_fd);
    if (rtp_app_client == nullptr)
    {
        AK_LOG_INFO << "[" << trace_id << "] can not find rtp app client, rtsp app client fd is = " <<  rtsp_client_fd;
        return true;
    }
    
    // 销毁 app rtp 资源
    RtpAppManager::getInstance()->RemoveClient(rtp_app_client->local_rtp_port_);

    // 删除dev rtp 持有的 app rtp对象, 若无正在监控设备的app(最后一个app结束监控), 获取 dev rtp 对象进行stop rtsp操作
    RtpDevClientPtr rtp_dev_client_to_del;
    RtpDeviceManager::getInstance()->DeviceRemoveApp(rtp_app_client, rtp_dev_client_to_del);

    // 删除已经没有app客户端在监控的设备
    if (rtp_dev_client_to_del)
    {
		AK::Route::StopRtspReq msg_stop_rtsp_req;
		msg_stop_rtsp_req.set_ip(local_ip);
		msg_stop_rtsp_req.set_mac(rtp_dev_client_to_del->mac_);
		msg_stop_rtsp_req.set_dev_mac(rtp_dev_client_to_del->dev_mac_);
		msg_stop_rtsp_req.set_port(rtp_dev_client_to_del->local_rtp_port_);
		msg_stop_rtsp_req.set_vrtspd_logic_id(g_logic_srv_id);
		msg_stop_rtsp_req.set_is_third(have_third_camera);
		msg_stop_rtsp_req.set_transfer_door_uuid(transfer_door_uuid);
		msg_stop_rtsp_req.set_transfer_indoor_mac(transfer_indoor_mac); // 转流室内机的mac
		msg_stop_rtsp_req.set_srtp_key(srtp_key);
        msg_stop_rtsp_req.set_camera(camera_name);
        msg_stop_rtsp_req.set_stream_id(stream_id);
        msg_stop_rtsp_req.set_flow_uuid(rtp_dev_client_to_del->flow_uuid_);

		CAkcsPdu pdu;
		pdu.SetMsgBody(&msg_stop_rtsp_req);
		pdu.SetHeadLen(sizeof(PduHeader_t));
		pdu.SetVersion(50);
		pdu.SetSeqNum(0);
		pdu.SetTraceId(trace_id);
		pdu.SetCommandId(AKCS_MSG_L2R_STOP_RTSP_REQ);

		AK_LOG_INFO << "[" << trace_id << "] send stop rtsp msg, local ip = " << local_ip << ", local dev rtp port = " << rtp_dev_client_to_del->local_rtp_port_;
		AK_LOG_INFO << "[" << trace_id << "] send stop rtsp msg, mac = " << rtp_dev_client_to_del->mac_ << ", dev_mac = " << rtp_dev_client_to_del->dev_mac_;	
		AK_LOG_INFO << "[" << trace_id << "] send stop rtsp msg, transfer_door_uuid = " << transfer_door_uuid << ", transfer_indoor_mac = " << transfer_indoor_mac << ", srtp_key = " << srtp_key;

		CRouteClientMng::Instance()->SendRtspMsg(rtp_dev_client_to_del->mac_, pdu);

        // 销毁 dev rtp 资源
        RtpDeviceManager::getInstance()->RemoveClient(rtp_dev_client_to_del->local_rtp_port_);
    }

    return true;
}

// 设备rtp流断开,清理资源
void CRtspServerImpl::HandleDevFlowStop(const std::set<uint16_t>& app_rtp_port_list)
{
    // 通过app_rtp_port获取app_rtsp_client
    for(const auto& app_rtp_port : app_rtp_port_list)
    {
        std::shared_ptr<RtspClient> app_rtsp_client = RtspClientManager::getInstance()->GetClientByRtpPort(app_rtp_port);
        if (app_rtsp_client != nullptr)
        {
            // 断开app连接,清理相关资源
            HandleDisconnect(app_rtsp_client->rtsp_fd_);
        }
    }
}

//所有中间处理流程出现问题的,全部使用这个接口进行rtsp客户端资源的释放
bool CRtspServerImpl::HandleDisconnect(int connfd)
{
    std::shared_ptr<RtspClient> rtsp_client = RtspClientManager::getInstance()->GetClient(connfd);
    if (rtsp_client)
    {
        HandleClose(rtsp_client);
    }
    return true;
}

void CRtspServerImpl::HandleAttacked(int fd, const std::string &bussiness, const std::string &client_ip)
{
    AK_LOG_WARN << "rtsp client req is invalid, bussiness is " << bussiness << ", key is " << client_ip;

    HandleDisconnect(fd);
    
    AKCS::Singleton<BussinessLimit>::instance().AddBussiness(bussiness, client_ip);
}

void CRtspServerImpl::AttackedCallback(const std::string& bussiness, const std::string& key)
{
    AK_LOG_WARN << "there is one attack happens, iptables input drop,bussiness is " << bussiness <<", ip is " << key;
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("csvrtspd", key);
}

}

