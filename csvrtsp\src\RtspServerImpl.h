#pragma once
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <memory>
#include <string>
#include <map>
#include <vector>
#include <sys/epoll.h>
#include "rtsp_server_interface.h"
#include "RtspClient.h"
#include "WaitEvent.h"
#include "AkcsCommonDef.h"
#include "rtp/RtpAppManager.h"
#include "rtp/RtpDeviceManager.h"
#include "rtp/RtpEpollThread.h"
#include "rtp/RtpProcessThread.h"

#define RTSP_SERVER_PORT    554
#define RTSP_CLIENT_MAX     1000
#define RTSP_EVENT_MAX      (RTSP_CLIENT_MAX + 1)
#define RTSP_MSG_MAX        100
#define RTSP_MSG_WARN_COUNT 20

const std::string VRTSP_AUTH_BUSSINESS = "vrtspd_auth_failed";
const std::string VRTSP_INVALID_MSG_BUSSINESS = "vrtspd_invalid_msg";
const uint32_t BUSSINESS_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t BUSSINESS_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
const uint32_t BUSSINESS_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s

namespace akuvox
{
class CRtspServerImpl : public IRtspServer
{
public:
    CRtspServerImpl();
    virtual ~CRtspServerImpl();
    static CRtspServerImpl* GetInstance();
    virtual int start();
    virtual int stop();
    virtual void report();
    
    bool HandleSetup(std::shared_ptr<RtspClient>& app_rtsp_client);
    bool HandlePlay(std::shared_ptr<RtspClient>& app_rtsp_client);

    bool HandleDisconnect(int connfd);
    void HandleDevFlowStop(const std::set<uint16_t>& app_rtp_port_list);
    void HandleAttacked(int fd, const std::string &bussiness, const std::string &client_ip);
    
private:
    bool HandleClose(std::shared_ptr<RtspClient> &client);
    void AttackedCallback(const std::string& bussiness, const std::string& key);
    
    bool DevAlreadyPlay(std::shared_ptr<RtspClient> app_rtsp_client, std::shared_ptr<RtpDeviceClient> dev_rtp_client);
    bool DevMonitorNumLimit(std::shared_ptr<RtspClient> app_rtsp_client,std::shared_ptr<RtpDeviceClient> dev_rtp_client);
    
    void InitDevRtpClient(std::shared_ptr<RtspClient>& app_rtsp_client, std::shared_ptr<RtpDeviceClient>& dev_rtp_client);
    void InitAppRtpClient(std::shared_ptr<RtspClient>& app_rtsp_client, std::shared_ptr<RtpAppClient>& app_rtp_client, std::shared_ptr<RtpDeviceClient>& dev_rtp_client);
    
private:
    static CRtspServerImpl* gRtspServer;
};

}

