#include <atomic>
#include <iomanip>
#include "util.h"
#include "util_time.h"
#include "Util/MD5.h"
#include "Util/base64.h"
#include "Parser.h"
#include "RtspParse.h"
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "SnowFlakeGid.h"
#include "SafeCacheConn.h"
#include "CsvrtspConf.h"
#include "RtspSession.h"
#include "RtspServerImpl.h"
#include "RtspClientManager.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/Account.h"
#include "timeticker/akcs_time_util.h"
#include "AkcsRequestRecorder.h"
#include "dbinterface/CommunityInfo.h"
#include "util_judge.h"

#define VRTSP_REALM_TEXT  "AK VRTSPD"
extern CSVRTSP_CONF gstCSVRTSPConf;
const int32_t RTSP_CLIENT_MAX_NUM = 200;                  // RTSP客户端最大数量
const uint32_t RTSP_CLIENT_AUTH_FAILED_TRY_NUM = 3;       // RTSP客户端鉴权失败最大的可重试次数
const uint32_t RTSP_CLIENT_MAX_NOT_ACTIVE_TIME = 5;       // RTSP客户端鉴权通过最大时间
const uint32_t RTSP_CLIENT_MAX_NOT_KEEPALIVE_TIME = 60;   // RTSP客户端最长未发保活包时间
 
using namespace std;
using namespace toolkit;

namespace mediakit 
{

RtspSession::RtspSession(const Socket::Ptr &sock) : Session(sock)
{
    cseq_ = 0;
    call_shutdown_ = false;
    auth_failed_times_ = 0;
    fd_ = getSock()->rawFD();
    peer_ip_ = get_peer_ip();
    peer_port_ = get_peer_port();
    session_id_ = makeRandStr(20);
    local_port_ = get_local_port();
    peer_is_ipv6_ = IsIpv6(peer_ip_);
    keepalive_time_ = time(nullptr);
    accept_time_ = std::time(nullptr);
    trace_id_ = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
	

    std::string ip_connect = peer_ip_ + "_connect";
    if (gstCSVRTSPConf.request_statics_switch)
    {
        CAkcsRequestRecorder::getInstance().RecordRequest(ip_connect, peer_ip_);
    }

    // 硬编码限制rtsp-tcp客户端数量为200, 超过该数值,考虑被定点攻击了,不处理新的客户端连接,onManager关闭空闲连接,保证系统资源不会被攻击耗尽
    if (akuvox::RtspClientManager::getInstance()->GetClientCount() > RTSP_CLIENT_MAX_NUM)
    {
        // 这边不创建RtspClient,处理rtsp信令时,检查fd对应的RtspClient是否存在,不存在则进行shutdown操作
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csvrtspd", "There are more than 200 rtsp-tcp clients,check rtsp service immediately", AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_RTSP);
    }
    else
    {
        // 添加到RtspClientManager, 用于管理Rtsp客户端资源
        akuvox::RtspClientManager::getInstance()->AddClient(fd_, peer_ip_, peer_port_, peer_is_ipv6_, trace_id_);
        
        AK_LOG_INFO << "[" << trace_id_ << "] RtspSession receive connect, fd = " << fd_ << ", local ip = " << get_local_ip() << ", local port = " << get_local_port(); 
        AK_LOG_INFO << "[" << trace_id_ << "] RtspSession receive connect, fd = " << fd_ << ", peer_ip = " << peer_ip_ <<  ", peer_port = " << peer_port_ << ", peer_is_ipv6 = " << peer_is_ipv6_ << ", session_id = " << session_id_;
    }
}

RtspSession::~RtspSession()
{
    AK_LOG_INFO << "[" << trace_id_ << "] ~RtspSession, fd = " << fd_;
}

void RtspSession::onRecv(const Buffer::Ptr &buf)
{
    // tls 打印log
    if (local_port_ == 8602) 
    {
        AK_LOG_INFO << "[" << trace_id_ << "] onRecv data: " << buf->data();
    }

    // 设置glog线程traceid
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);
    
    // RtspSplitter解析客户端发送过来的数据,若为rtsp信令则回调onWholeRtspPacket处理
    input(buf->data(), buf->size());
}

// 每两秒会调用一次
void RtspSession::onManager()
{
    // 客户端连接上后超过5s未鉴权通过的客户端直接清理资源
    std::time_t time_now = time(nullptr);
    if (time_now - accept_time_ >= RTSP_CLIENT_MAX_NOT_ACTIVE_TIME && !authenticator_.AuthSuccess())
    {
        HandleDisconnect("onManager, client auth success spend over 5s, shutdown RtspSession");
    }

    // 设备断流时会主动关闭监控, 检测rtsp_client被销毁  
    std::shared_ptr<akuvox::RtspClient> rtsp_client = GetRtspClient();
    if (rtsp_client == nullptr)
    {
        HandleDisconnect("onManager, rtsp client already destruction on active close, shutdown RtspSession");
    }

    // 客户端最长监控时间300s
    if (time_now - accept_time_ >= gstCSVRTSPConf.timer_monitor_timeout)
    {
        HandleDisconnect("onManager, rtsp client monitor time over 300s, shutdown RtspSession");
    }

/*
    // 超过一分钟未keepalive的客户端直接清理资源(通话时转化到三方摄像头监控,这时不会发保活包)
    if (time_now - keepalive_time_ >= RTSP_CLIENT_MAX_NOT_KEEPALIVE_TIME)
    {
        HandleDisconnect("onManager, client keepalive over 60s, shutdown RtspSession");
    }
*/
}

// 连接状态error
void RtspSession::onError(const SockException &err)
{
    // 出现onError要把资源清理掉, 不然会一直keepalive
    HandleDisconnect("onError, delete RtspClient and shutdown RtspSession");
}

void RtspSession::onRtpPacket(const char *data, size_t len)
{
}

void RtspSession::onRtcpPacket()
{
}

ssize_t RtspSession::getContentLength(Parser &parser) 
{
    return RtspSplitter::getContentLength(parser);
}

ssize_t RtspSession::send(Buffer::Ptr pkt)
{
    return Session::send(std::move(pkt));
}

bool RtspSession::sendRtspResponse(const string &res_code, const std::initializer_list<string> &header, const string &sdp, const char *protocol) 
{
    string key;
    StrCaseMap header_map;
    int i = 0;
    for(auto &val : header)
    {
        if(++i % 2 == 0)
        {
            header_map.emplace(key,val);
        }
        else
        {
            key = val;
        }
    }
    return sendRtspResponse(res_code ,header_map, sdp, protocol);
}

bool RtspSession::sendRtspResponse(const string &res_code, const StrCaseMap &header_const, const string &sdp, const char *protocol)
{
    auto header = header_const;
    header.emplace("CSeq", StrPrinter << cseq_);
    if(!session_id_.empty())
    {
        header.emplace("Session", session_id_);
    }

    header.emplace("Server","Streaming Server v0.1");
    header.emplace("Date",GetNowDateStr());

    if(!sdp.empty())
    {
        header.emplace("Content-Length",StrPrinter << sdp.size());
        header.emplace("Content-Type","application/sdp");
    }

    _StrPrinter printer;
    printer << protocol << " " << res_code << "\r\n";
    for (auto &pr : header)
    {
        printer << pr.first << ": " << pr.second << "\r\n";
    }
    printer << "\r\n";

    if(!sdp.empty())
    {
        printer << sdp;
    }

    // tls 打印log
    if (local_port_ == 8602) 
    {
        AK_LOG_INFO << "[" << trace_id_ << "] sendRtspResponse printer: " << printer;
    }

    return send(std::make_shared<BufferString>(std::move(printer))) > 0 ;
}

void RtspSession::onWholeRtspPacket(Parser &parser)
{
    string method = parser.method();
    cseq_ = atoi(parser["CSeq"].data());
    AK_LOG_INFO << "[" << trace_id_ << "] method: " << method << ",seq: " << cseq_ << ",session_id: " << session_id_;

    using rtsp_request_handler = void (RtspSession::*)(const Parser &parser);
    static unordered_map<string, rtsp_request_handler> s_cmd_functions;
    static onceToken token([]()
    {
        s_cmd_functions.emplace("OPTIONS", &RtspSession::HandleReqOptions);
        s_cmd_functions.emplace("DESCRIBE", &RtspSession::HandleReqDescribe);
        s_cmd_functions.emplace("SETUP", &RtspSession::HandleReqSetup);
        s_cmd_functions.emplace("PLAY", &RtspSession::HandleReqPlay);
        s_cmd_functions.emplace("GET_PARAMETER", &RtspSession::HandleReqGetParameter);
        s_cmd_functions.emplace("TEARDOWN", &RtspSession::HandleReqTeardown);
    });

    auto it = s_cmd_functions.find(method);
    if (it == s_cmd_functions.end()) 
    {
       sendRtspResponse("403 Forbidden");

       // 非法的rtsp信令,直接清理资源
       HandleDisconnect(StrPrinter << "cmd not found 403 Forbiddens, method = " << method);
       return;
    }
    
    (this->*(it->second))(parser);
    parser.clear();
}

void RtspSession::HandleReqOptions(const Parser &parser)
{
    std::shared_ptr<akuvox::RtspClient> rtsp_client = GetRtspClient();
    if (rtsp_client)
    {
	    rtsp_client->SetStatus(akuvox::RtspClient::Status::kOption);
        sendRtspResponse("200 OK",{"Public" , "OPTIONS, DESCRIBE, SETUP, TEARDOWN, PLAY, GET_PARAMETER"});
        
        AK_LOG_INFO << "[" << trace_id_ << "] handle options success, peer_ip : " << peer_ip_ << ", peer_port : " << peer_port_ << ", fd : " << fd_;
    }
    else
    {
        HandleDisconnect("handle options failed, rtsp client is nullptr");
    }
}

void RtspSession::HandleReqGetParameter(const Parser &parser)
{
    std::shared_ptr<akuvox::RtspClient> rtsp_client = GetRtspClient();
    if (rtsp_client)
    {
        // 防止室内机一直监控 忘记关闭的问题
        if (gstCSVRTSPConf.keep_alive_times > 0 && ++rtsp_client->rtsp_keep_alive_times_ >= gstCSVRTSPConf.keep_alive_times)
        {
            HandleDisconnect(StrPrinter << "recv app rtsp keepalive, mac = " << rtsp_client->mac_ << ", which is more than allowed " << gstCSVRTSPConf.keep_alive_times << " times.");
        }
        else
        {
            sendRtspResponse("200 OK");
            
            keepalive_time_ = time(nullptr);
            AK_LOG_INFO << "recv app rtsp keepalive, mac = " << rtsp_client->mac_ << ", rtsp_keep_alive_times = " << rtsp_client->rtsp_keep_alive_times_ << ", allowed rtsp_keep_alive_times = " << gstCSVRTSPConf.keep_alive_times;
        }
    }
    else
    {
        HandleDisconnect("handle get parameter failed, rtsp client is nullptr");
    }
}

void RtspSession::HandleReqDescribe(const Parser &parser)
{   
    std::shared_ptr<akuvox::RtspClient> rtsp_client = GetRtspClient();
    if (rtsp_client) 
    {
        // 异步执行后续操作
        weak_ptr<RtspSession> weak_self = static_pointer_cast<RtspSession>(shared_from_this());
        
        // 此对象已经销毁
        auto strong_self = weak_self.lock();
        if (!strong_self) 
        {
            return; 
        }
        
        //切换到自己的线程然后执行
        strong_self->async([weak_self, &rtsp_client, parser]() 
        {
            auto strong_self = weak_self.lock();
            // 此对象已经销毁
            if (!strong_self) 
            { 
                return; 
            } 

            auto manual = parser["Manual"];
            auto account = parser["Account"];
            auto srtp_key = parser["SrtpKey"];         // hager项目,携带rtp加密type和key
            auto transfer_door_mac = parser["x-Dmac"]; // hager项目,携带转流门口机的mac
            auto authorization = parser["Authorization"];
            
            AK_LOG_INFO << "[" << rtsp_client->trace_id_ << "] On Describe, account = " << account << ", manual = " << manual;
            AK_LOG_INFO << "[" << rtsp_client->trace_id_ << "] On Describe, authorization : " << authorization;
            AK_LOG_INFO << "[" << rtsp_client->trace_id_ << "] On Describe, srtp_key = " << srtp_key << ", transfer_door_mac = " << transfer_door_mac;

            if (parseFullUrlInfo(parser.fullUrl(), rtsp_client) ==  true)
            {
                // 记录监控操作到数据库
                rtsp_client->app_uid_ = account;
                rtsp_client->app_manual_log_.setRtspLogInfo(rtsp_client->mac_, rtsp_client->app_uid_, ATOI(manual.data()));

                // srtp加密的密钥
                rtsp_client->srtp_key_ = srtp_key;
                
                // 判断是否为hager转流项目,携带X-Dmac即门口机mac
                if (!transfer_door_mac.empty())
                {
                    rtsp_client->need_transfer_ = true;                       // X-Dmac有值,转流flag置为1
                    rtsp_client->transfer_door_uuid_ = transfer_door_mac;  // hager转流门口机mac
                    rtsp_client->transfer_indoor_mac_ = rtsp_client->mac_; // hager转流室内机mac
                }

                if (rtsp_client->guise_mac_ || strong_self->OnAuthDigest(rtsp_client, authorization)) 
                {
                    // 鉴权通过加入并发数
                    akuvox::RtspClientManager::getInstance()->AddConcurrency(rtsp_client->rtsp_fd_);

                    // 微信小程序
                    if (rtsp_client->guise_mac_) 
                    {
                        AK_LOG_INFO << "[" << rtsp_client->trace_id_ << "] this is Applets, authcode=" << rtsp_client->mac_authcode_ << ",mac=" << rtsp_client->mac_.c_str();
                    }

                    // 设置鉴权成功标识
                    strong_self->authenticator_.SetAuthSuccess();

                    // 鉴权成功 回复客户端
                    rtsp_client->SetStatus(akuvox::RtspClient::Status::kDesc);
                    strong_self->OnAuthSuccess(rtsp_client);
                } 
                else 
                {
                    // 鉴权失败 回复客户端
                    strong_self->OnAuthFailed(rtsp_client);
                }
            }
        });
    }
    else
    {
        HandleDisconnect("handle describe failed, rtsp_client is nullptr");
    }
}

void RtspSession::HandleReqSetup(const Parser &parser)
{
    bool rtp_confuse_switch = false;
    std::shared_ptr<akuvox::RtspClient> rtsp_client = GetRtspClient();

    if(rtsp_client)    
    {  
        //设置app侧（监控方）的rtp混淆开关
        std::string rtp_confuse = parser["X-Rtp-Confuse"].data();
        rtp_confuse_switch = ATOI(rtp_confuse.c_str()) ? true : false;
        rtsp_client->SetRtpConfuseSwitch(rtp_confuse_switch);
        //设置app侧（监控方）的ssrc
        std::string app_ssrc = parser["X-SSRC"].data();
        if (app_ssrc.size() > 0)
        {
            rtsp_client->app_client_ssrc_ = std::stoul(app_ssrc);
        }
    }
				
    if (rtsp_client && akuvox::CRtspServerImpl::GetInstance()->HandleSetup(rtsp_client))
    {
        string client_port = mediakit::findSubString(parser["Transport"].data(), "client_port=", NULL);
        rtsp_client->client_port_ = client_port;
        sscanf(rtsp_client->client_port_.c_str(), "client_port=%hu-%hu", &rtsp_client->client_rtp_port_, &rtsp_client->client_rtcp_port_);
        std::string response_rtp_confuse = rtp_confuse_switch ? "1" : "0";        
        sendRtspResponse("200 OK",
                     {   
                         "Cache-Control", "no-cache",
                         "Session"      , session_id_,
                         "X-Rtp-Confuse", response_rtp_confuse,
                         "X-SSRC",             std::to_string(rtsp_client->app_client_ssrc_),
                         "Transport"    , StrPrinter << "RTP/AVP;unicast;mode=play;" << "client_port=" << client_port << ";"<< "server_port=" << rtsp_client->local_rtp_port_ << "-" << rtsp_client->local_rtp_port_ + 1
                     });
    }
    else
    {
        HandleDisconnect("handle setup failed, rtsp_client is nullptr");
    }
}

void RtspSession::HandleReqPlay(const Parser &parser)
{
    std::shared_ptr<akuvox::RtspClient> rtsp_client = GetRtspClient();
    if (rtsp_client && akuvox::CRtspServerImpl::GetInstance()->HandlePlay(rtsp_client))
    {
        sendRtspResponse("200 OK",
                    {
                         "Range"   ,  "npt=now-",
                         "Session" ,  StrPrinter << session_id_ << ";timeout=60",
                         "RTP-Info",  StrPrinter << "url=rtsp://" << rtsp_client->local_ip_ << ":" << local_port_ << "/live/ch00_0/trackID=0;seq=0;rtptime=0"
                    });
    }
    else
    {
        HandleDisconnect("handle play failed, rtsp_client is nullptr");
    }
}

void RtspSession::HandleReqTeardown(const Parser &parser)
{
    sendRtspResponse("200 OK");
    
    HandleDisconnect("handle teardown succes");
}

bool RtspSession::GetDeviceInfo(std::shared_ptr<akuvox::RtspClient> &rtsp_client, const std::string &username)
{
    // 三方摄像头根据uuid查找绑定的mac，根据设备mac获取rtsp用户名和密码
    std::string dev_mac;
    if (rtsp_client->have_third_camera_)
    {
        ThirdPartyCamreaInfo camera_info;
        if(0 == dbinterface::ThirdPartyCamrea::GetThirdPartyCameraByUUID(rtsp_client->mac_, camera_info)
        || 0 == dbinterface::PersonalThirdPartyCamrea::GetPersonalThirdPartyCameraByUUID(rtsp_client->mac_, camera_info))
        {
            dev_mac = camera_info.mac;
        }
        else
        {
            AK_LOG_INFO << "[" << trace_id_ << "] Query database uuid, mac:" << rtsp_client->mac_;
            return false;
        }

        if (dev_mac != rtsp_client->binded_mac_)
        {
            AK_LOG_INFO << "[" << trace_id_ << "] Request mac does not match dev mac, request_mac:" << rtsp_client->binded_mac_ << ",and database binded_mac:" << dev_mac;
            return false;
        }

        rtsp_client->video_pt_ = camera_info.video_pt;
        rtsp_client->video_type_ = camera_info.video_type;
        rtsp_client->video_fmtp_ = camera_info.video_fmtp;
    }
    else
    {
        dev_mac = rtsp_client->mac_;
    }

    ResidentDev dev_info;
    if (0 == dbinterface::ResidentPerDevices::GetMacDev(dev_mac, dev_info) || 0 == dbinterface::ResidentDevices::GetMacDev(dev_mac, dev_info))
    {
        rtsp_client->dclient_ver_ = dev_info.dclient_ver;
        rtsp_client->dev_enable_srtp_ = SwitchHandle(dev_info.fun_bit, FUNC_DEV_SUPPORT_SRTP);
        rtsp_client->dev_enable_rtp_confuse_ = dbinterface::Account::CheckRtpConfuseByDev(dev_info);
        authenticator_.setUsernameAndPassword(username.c_str(), dev_info.rtsppwd, false);
        if (!rtsp_client->dev_enable_srtp_)
        {
            rtsp_client->srtp_key_ = ""; // 设备不支持srtp,将key置空不下发
            AK_LOG_INFO << "[" << trace_id_ << "] device not support srtp, mac=" << dev_info.mac << ",device func_bit = " << dev_info.fun_bit;
        }
        
        // 非hager项目的转流
        if (!rtsp_client->need_transfer_) 
        {
            // 走西班牙转流流程
            rtsp_client->need_transfer_ = IsDevMonitorNeedRepost(dev_info);
            rtsp_client->transfer_door_uuid_ = dev_info.sip;
            rtsp_client->ConvertMonitoringDevice();
        }
        return true;
    }
    else
    {
        AK_LOG_INFO << "[" << trace_id_ << "] query database fail mac=" << rtsp_client->mac_;

        // 数据库中未查询到监控的mac 判断为攻击
        akuvox::CRtspServerImpl::GetInstance()->HandleAttacked(rtsp_client->rtsp_fd_, VRTSP_INVALID_MSG_BUSSINESS, rtsp_client->client_ip_);
        return false;
    }
}

bool RtspSession::OnAuthDigest(std::shared_ptr<akuvox::RtspClient> &rtsp_client, const std::string &authorization)
{
    std::string nonce_cache = AuthNonceCache::getInstance()->nonceByMac(rtsp_client->mac_);
    if (nonce_cache.size() == 0)
    {
        //该mac对应的nonce已经过期或者还未设定过
        return false;
    } 
    
    // 解析Authorization
    auto auth_str = findSubString(authorization.data(), " ", NULL);

    // 解析到map中,使用kv获取字段值
    auto map_tmp = Parser::parseArgs(auth_str, ",", "=");
    decltype(map_tmp) auth_map;
    for(auto &pr : map_tmp)
    {
        auth_map[trim(string(pr.first)," \"")] = trim(pr.second," \"");
    }
    
    //check realm
    auto realm = auth_map["realm"];
    if(authenticator_.realm() != realm)
    {
        AK_LOG_INFO << "[" << trace_id_ << "] realm not mached:" << realm << " != " << realm;
        return false;
    }
    
    // check nonce
    auto nonce = auth_map["nonce"];
    if (nonce_cache != nonce)
    {
        AK_LOG_INFO << "[" << trace_id_ << "] nonce not mached:" << nonce << " != " << nonce_cache;
        return false;
    }
    
    //check username and uri
    auto uri = auth_map["uri"];
    auto username = auth_map["username"];
    auto response = auth_map["response"];
    if(username.empty() || uri.empty() || response.empty())
    {
       AK_LOG_WARN << "[" << trace_id_ << "] username/uri/response empty:" << username << "," << uri << "," << response;
       return false;
    }
    
    // 获取设备信息
    if (GetDeviceInfo(rtsp_client, username) == true)
    {
        // 计算摘要认证
        std::string our_response = authenticator_.computeDigestResponseByNonce("DESCRIBE", uri.c_str(), nonce_cache.c_str());        
        if (our_response == response)
        {
            AK_LOG_INFO << "[" << trace_id_ << "] Authorized digest success for rtsp client fd = " << rtsp_client->rtsp_fd_ << ", mac = " << rtsp_client->mac_;
            return true;
        }
        else
        {
            AK_LOG_INFO << "[" << trace_id_ << "] Unauthorized digest for rtsp client fd = " << rtsp_client->rtsp_fd_ << ", request response=" << response << ", our_response=" << our_response;
            return false;
        }
    }
    else
    {
        akuvox::CRtspServerImpl::GetInstance()->HandleAttacked(rtsp_client->rtsp_fd_, VRTSP_INVALID_MSG_BUSSINESS, rtsp_client->client_ip_);
        AK_LOG_WARN << "[" << trace_id_ << "] rtsp client req is invalid, mac:" << rtsp_client->mac_ << " is not exist, bussiness is " << VRTSP_INVALID_MSG_BUSSINESS << ", ip is " << rtsp_client->client_ip_;
    }
    
    return false;
}     

void RtspSession::OnAuthSuccess(std::shared_ptr<akuvox::RtspClient> &rtsp_client)
{
    std::string ip_monitor_mac = peer_ip_ + "_" + std::string(rtsp_client->mac_);
    if (gstCSVRTSPConf.request_statics_switch)
    {
        CAkcsRequestRecorder::getInstance().RecordRequest(ip_monitor_mac, rtsp_client->mac_);
    }

    // 构造sdp内容
    char sdp_buf[2048] = { 0 };
    BuildRtspSDP(rtsp_client, sdp_buf, sizeof(sdp_buf));

    // 构造reply headers
    mediakit::StrCaseMap headers;
    headers.emplace("Content-Base", StrPrinter << "rtsp://" << gstCSVRTSPConf.csvrtsp_outer_ip << ":" << local_port_ << "/live/ch00_0/");

    // hager srtp key协商: 室内机群呼到app, 多个app同时发起监控, 要使用同一个srtp_key和ssrc才能解密
    // 设备支持srtp且app携带了srtp_key才回复SrtpKey字段
    if (rtsp_client->dev_enable_srtp_ && !rtsp_client->srtp_key_.empty()) 
    {
        // 多个app同时监控时srtp_key和dev_ssrc要一致才能解密
        NegotiateSrtpEncryptInfo(rtsp_client);
        headers.emplace("SrtpKey", rtsp_client->srtp_key_);
    }
    
    sendRtspResponse("200 OK", headers, sdp_buf);
    AK_LOG_INFO << "[" << trace_id_ << "] Authorized Success for rtsp client fd = " << fd_ << ", mac = " << rtsp_client->mac_ 
                << ", dev enable srtp = " << rtsp_client->dev_enable_srtp_ << ", srtp key =" << rtsp_client->srtp_key_;
}

void RtspSession::OnAuthFailed(std::shared_ptr<akuvox::RtspClient> &rtsp_client)
{
    // rtsp鉴权认证失败的次数,超过3次就认为是恶意攻击
    if (++auth_failed_times_ >= RTSP_CLIENT_AUTH_FAILED_TRY_NUM)
    {
        AK_LOG_INFO << "[" << trace_id_ << "] AuthFailed 3 times, add ip block = " << rtsp_client->client_ip_;
        akuvox::CRtspServerImpl::GetInstance()->HandleAttacked(rtsp_client->rtsp_fd_, VRTSP_INVALID_MSG_BUSSINESS, rtsp_client->client_ip_);
        return;
    }
    
    // 在这里生成一个任意的nonce,并响应给401的对端
    authenticator_.setRealmAndRandomNonce(VRTSP_REALM_TEXT); 
    
    std::string nonce_cache = AuthNonceCache::getInstance()->nonceByMac(rtsp_client->mac_);
    if (nonce_cache.size() == 0) //防止该mac对应的nonce还没有写入redis成功
    {
        nonce_cache = "888888888888192e3ea10d54635a1ef8"; //写死一个
    }

    // 回复给客户端. 注意：双引号 和 等号 附近的空格
    sendRtspResponse("401 Unauthorized", 
        {
            "WWW-Authenticate", StrPrinter << "Digest realm=\"" << authenticator_.realm() << "\", nonce=\"" << nonce_cache << "\""
        });

    AK_LOG_INFO << "[" << trace_id_ << "] Unauthorized for rtsp client = " << rtsp_client->rtsp_fd_ << ", mac = " << rtsp_client->mac_;
}

/*
// 多个app同时监控时srtp需要使用同一套srtpkey和ssrc才能解密
// 使用redis的hset缓存srtpkey和ssrc, 把对一台设备过来监控的app都协商成同一套

    local mac=KEYS[1]              // monitor mac
    local srtp_key=ARGV[1]         // srtp key
    local srtp_key_value=ARGV[2]   // srtp value
    local dev_ssrc=ARGV[3]         // ssrc key
    local dev_ssrc_value=ARGV[4]   // ssrc value
    local expire_time=ARGV[5]      // expire time
    local hset_key=mac .. '_srtp'  // redis hash set的key = mac_srtp

    // 返回结果
    local result = {};
    
    // 判断redis中key是否存在
    local srtp_key_exists=redis.call('HEXISTS', hset_key, srtp_key)

    if srtp_key_exists == 1 then 
        // key存在, 从redis中获取值
        result = redis.call('HGETALL',hset_key);
    else 
        // key不存在, 将值set到redis中
        redis.call('HMSET', hset_key, srtp_key, srtp_key_value, dev_ssrc, dev_ssrc_value)
        result = {'srtp_key', srtp_key_value, 'dev_ssrc', dev_ssrc_value}
    end 

    // 设置key的过期时间, 和vrtspd_logic_id的缓存时长一致
    redis.call('EXPIRE', mac..'_srtp', expire_time)

    return result;
*/
static const char KSrtpNegotiationScript[] = "local flow_uuid=KEYS[1];local srtp_key=ARGV[1];local srtp_key_value=ARGV[2];local dev_ssrc=ARGV[3]; local dev_ssrc_value=ARGV[4]; local expire_time=ARGV[5];local hset_key=flow_uuid .. '_srtp';local srtp_key_exists=redis.call('HEXISTS', hset_key, srtp_key);local result = {};if srtp_key_exists==1 then result = redis.call('HGETALL',hset_key); else redis.call('HMSET',hset_key,srtp_key,srtp_key_value,dev_ssrc,dev_ssrc_value);result = {'srtp_key', srtp_key_value, 'dev_ssrc', dev_ssrc_value};end redis.call('EXPIRE',flow_uuid..'_srtp',expire_time);return result;";
void RtspSession::NegotiateSrtpEncryptInfo(std::shared_ptr<akuvox::RtspClient> &rtsp_client)
{
    int32_t dev_ssrc = rand();
    std::map<string, string> cache_map;
    std::string flow_uuid = rtsp_client->GetFlowUUID();
    std::vector<std::string> keys = {flow_uuid};
    std::vector<std::string> args = {"srtp_key", rtsp_client->srtp_key_, "dev_ssrc", std::to_string(dev_ssrc), "33"};

    SafeCacheConn redis(g_redis_db_mac_vrtspsid);
    if (!redis.eval(KSrtpNegotiationScript, keys, args, cache_map))
    {
        HandleDisconnect("negotiateSrtpEncryptInfo failed, redis eval script failed");
    }

    rtsp_client->srtp_key_ = cache_map["srtp_key"];
    rtsp_client->dev_ssrc_ = std::stoul(cache_map["dev_ssrc"]);

    AK_LOG_INFO << "[" << trace_id_ << "] negotiateSrtpEncryptInfo success, app uid = " << rtsp_client->app_uid_ << ", dev_ssrc =" << rtsp_client->dev_ssrc_ << ", srtp_key =" << rtsp_client->srtp_key_;
}

std::shared_ptr<akuvox::RtspClient> RtspSession::GetRtspClient()
{
    return akuvox::RtspClientManager::getInstance()->GetClient(fd_);    
}

void RtspSession::HandleDisconnect(const std::string& error_msg)
{
    if (!call_shutdown_)
    {
        call_shutdown_ = true;
        
        AK_LOG_INFO << "[" << trace_id_ << "] handle disconnect, msg = " << error_msg << ", fd = " << fd_;
        
        // 清理rtsp客户端资源
        akuvox::CRtspServerImpl::GetInstance()->HandleDisconnect(fd_);
        
        // 关闭RtspSession连接
        shutdown(SockException(Err_shutdown, error_msg));
    }
    
    AK_LOG_INFO << "[" << trace_id_ << "] already handle disconnect, msg = " << error_msg << ", fd = " << fd_;
    
    return;
}

bool RtspSession::IsDevMonitorNeedRepost(const ResidentDev &dev)
{
    //未开启转流开关的，直接返回
    if (dev.repost == 0)
    {
        return false;
    }

    //非社区类型直接返回
    if (dev.project_type != project::RESIDENCE)
    {
        return true;
    }

    //社区设备，若是即插即用的项目，根据设备实际在线状态判断是否需要转流
    CommunityInfo comm_info(dev.project_mng_id);
    if (akjudge::IsInstallerKitProject(comm_info.GetProjectPlanType()) && akjudge::IsDevDclientOnline(dev.status))
    {
        return false; //设备实际在线，不走转流
    }

    return true;
}

}
