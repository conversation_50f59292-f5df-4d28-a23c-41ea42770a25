#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <assert.h>
#include "AKLog.h"
#include "CsvrtspConf.h"
#include "AkcsMonitor.h"
#include "StorageMng.h"
#include "util.h"
#include "AkLogging.h"

#define FDFS_CONF_PATH       "/usr/local/akcs/csvrtsp/conf/csvrtsp_fdfs.conf"

extern CSVRTSP_CONF gstCSVRTSPConf; //全局配置信息

CShadowMng* GetShadowMngInstance()
{
    return CShadowMng::GetInstance();
}

CShadowMng* CShadowMng::instance_ = NULL;

CShadowMng* CShadowMng::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new CShadowMng();
    }

    return instance_;
}

CShadowMng::CShadowMng() : tag_("StorageMng")
{
    uploader_ = std::unique_ptr<FdfsUploader>(new FdfsUploader());
    uploader_->Init(FDFS_CONF_PATH);
    uploader_->SetUploadGroupName(gstCSVRTSPConf.group_name);
}

CShadowMng::~CShadowMng()
{

}

int CShadowMng::StorePcapCaptureFile(const char* local_filepath, const std::string& action_uuid)
{
    if(local_filepath == nullptr || action_uuid.size() == 0)
    {
        AK_LOG_INFO << "StorePcapCaptureFile param error, return";
        return -1;
    }

    std::string path_after;
    std::string filename(local_filepath);
    size_t index = filename.find_last_of("/");
    filename = filename.substr(index + 1);

    int retry_time_per_upload = 1; //每次上传重试次数为1

    if(uploader_->UploadFile(local_filepath, path_after, retry_time_per_upload) != 0)
    {
        //重试一次，仍然上传失败
        if(uploader_->UploadFile(local_filepath, path_after, retry_time_per_upload) != 0)
        {
            AK_LOG_INFO << "StorePcapCaptureFile error, return";
            return -1;
        }
    }
    
    dbinterface::PcapCaptureControl::InsertPcapCaptureControlList(action_uuid, filename, path_after);
    AK_LOG_INFO << "StorePcapCaptureFile success, filename:" << filename << ",path_after:" << path_after;
    return 0;    
}
