#include <stdio.h>
#include <stdlib.h>

#include "TcpClient.h"
#include "HttpResp.h"

namespace tcp_client
{

//tcp-client与网关连接回调函数
void OnConnection(const evpp::TCPConnPtr& conn)
{
    if (conn->IsConnected())
    {
        LOG_INFO << "Accept a new connection from " << conn->remote_addr();
        //连接成功后,将相关数据通过发送给网关
        conn->Send("hello from access server");
    }
    else
    {
        LOG_INFO << "Disconnected from " << conn->remote_addr();
    }
}

void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
{
    LOG_TRACE << "Receive a message [" << buf->ToString() << "]";
}

}