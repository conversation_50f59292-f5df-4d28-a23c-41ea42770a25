#include "AKLog.h"
#include <stdio.h>
#include <stdarg.h>
#include "glog/logging.h"

#define MAX_PATH 256
#define BUFF_SIZE 4096

#define DEBUG   0


// 这是已导出类的构造函数。
// 有关类定义的信息，请参阅 HyqLog.h
CAKLog::CAKLog()
{
    return;
}
void CAKLog::Init()
{
    //google::InitGoogleLogging("vrtspd");
    //FLAGS_log_dir = "./log";
    //LOG(INFO) << "glog init";
}

void CAKLog::LogT(const char* tag, const char* msg, ...)
{
    char tags[MAX_PATH] = { 0 };
    snprintf(tags, sizeof(tags), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", tags, buf);
    //LOG(INFO) << tags << buf;
#endif // DEBUG
    LOG(INFO) << tags << buf;
}

void CAKLog::LogD(const char* tag, const char* msg, ...)
{
    char tags[MAX_PATH] = { 0 };
    snprintf(tags, sizeof(tags), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", tags, buf);
#endif // DEBUG
    LOG(INFO) << tags << buf;
}

void CAKLog::LogI(const char* tag, const char* msg, ...)
{
    char tags[MAX_PATH] = { 0 };
    snprintf(tags, sizeof(tags), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", tags, buf);
#endif // DEBUG
    LOG(INFO) << tags << buf;
}

void CAKLog::LogW(const char* tag, const char* msg, ...)
{
    char tags[MAX_PATH] = { 0 };
    snprintf(tags, sizeof(tags), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", tags, buf);
#endif // DEBUG
    LOG(WARNING) << tags << buf;
}

void CAKLog::LogE(const char* tag, const char* msg, ...)
{
    char tags[MAX_PATH] = { 0 };
    snprintf(tags, sizeof(tags), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", tags, buf);
#endif // DEBUG
    LOG(ERROR) << tags << buf;
}

void CAKLog::LogF(const char* tag, const char* msg, ...)
{
    char tags[MAX_PATH] = { 0 };
    snprintf(tags, sizeof(tags), "[%s] ", tag);

    char buf[BUFF_SIZE] = { 0 };
    va_list args;
    va_start(args, msg);
    vsnprintf(buf, sizeof(buf), msg, args);
    va_end(args);

#if DEBUG
    printf("%s%s\n", tags, buf);
#endif // DEBUG
    LOG(FATAL) << tags << buf;
}

void CAKLog::LogHex(const char* tag, const unsigned char* data, int len)
{
    char buff[1024] = { 0 };
    char tmp[32] = { 0 };
    for (int i = 0; i < len; i++)
    {
        snprintf(tmp, 32, "%02x ", data[i]);
        if (i % 16 == 0)
        {
            strcat(buff, "\n");
        }

        strcat(buff, tmp);
    }
    LogD(tag, "%s", buff);
}
