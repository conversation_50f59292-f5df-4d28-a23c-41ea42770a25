#pragma once
#include <string>
#include <iostream>
#include <fstream>

using namespace std;

namespace akuvox
{
class CConfigOperator
{
public:
    CConfigOperator(void);
    ~CConfigOperator(void);
    void SetFilePath(const string& filepath);
    bool GetConfigValue(const string& name, const string& key, string& value);

private:
    bool OpenFile();
    void FindName(const string& line, const string& name);
    bool FindKey(const string& line, const string& key, string& value);

    const char* tag_;
    ifstream fin_;
    string filepath_;
    bool findname_;
};
}
