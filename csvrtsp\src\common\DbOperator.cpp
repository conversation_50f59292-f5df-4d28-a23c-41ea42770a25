#include "DbOperator.h"
#include "AKLog.h"



namespace akuvox
{
CDbOperator::CDbOperator() : tag_("DbOperator")
{
    mysql_conn_ = mysql_init(nullptr);
    if (nullptr == mysql_conn_)
    {
        CAKLog::LogE(tag_, "mysql init error: %s", mysql_error(mysql_conn_));
    }

    //!此处要设置读超时时间为3秒，不然查询时有可能卡住处理线程，不设置默认超时时间长达16分钟
    int timeout = 3;
    mysql_options(mysql_conn_, MYSQL_OPT_READ_TIMEOUT, (const char*)&timeout);

    //数据库为长连接，有可能会出现Lost connection to MySQL server during query，MySQL server has gone away
    //设置自动连接可以自动恢复
    char reconnect = 1;
    mysql_options(mysql_conn_, MYSQL_OPT_RECONNECT, &reconnect);
    CAKLog::LogT(tag_, "mysql init success");
}

CDbOperator::~CDbOperator()
{
    if (nullptr != mysql_conn_)  // 关闭数据库连接
    {
        mysql_close(mysql_conn_);
        mysql_conn_ = nullptr;
        CAKLog::LogT(tag_, "mysql close success");
    }
}

//DbUtil* DbUtil::instance = nullptr;

//DbUtil* DbUtil::getInstance()
//{
//  if (instance == nullptr) {
//      instance = new DbUtil;
//  }
//  return instance;
//}

bool CDbOperator::Connect(std::string& server, std::string& username, std::string& password, std::string& database)
{
    if (!mysql_real_connect(mysql_conn_, server.c_str(), username.c_str(), password.c_str(), database.c_str(), 3306, nullptr, 0))
    {
        CAKLog::LogE(tag_, "connect database %s error [%s]", database.c_str(), mysql_error(mysql_conn_));
        return false;
    }
    if (mysql_set_character_set(mysql_conn_, "utf8"))
    {
        CAKLog::LogE(tag_, "set utf8 error %s", database.c_str(), mysql_error(mysql_conn_));
    }

    CAKLog::LogI(tag_, "connect database %s success", database.c_str());
    return true;
}

bool CDbOperator::Query(const char* sql, MYSQL_RES** res)
{
    CAKLog::LogD(tag_, "begin Query sql=[%s]", sql);
    if (mysql_query(mysql_conn_, sql))
    {
        CAKLog::LogE(tag_, "query [%s] error [%s]", sql, mysql_error(mysql_conn_));
        return false;
    }
    CAKLog::LogD(tag_, "end Query sql=[%s]", sql);
    *res = mysql_store_result(mysql_conn_);
    return true;
}
}
