#pragma once
#include <string>
#include <map>
#include <set>
#include "mysql.h"


namespace akuvox
{
class CDbOperator
{
public:
    CDbOperator();
    ~CDbOperator();
    //      static DbUtil* getInstance();

    bool Connect(std::string& server, std::string& username, std::string& password, std::string& database);
    bool Query(const char* sql, MYSQL_RES** res);

private:

private:
    //      static DbUtil* instance;
    MYSQL* mysql_conn_;
    const char* tag_;
};
}
