#include "WaitEvent.h"

#if defined(_WIN32)

CWaitEvent::CWaitEvent()
{
    m_event = CreateEvent(nullptr, true, true, nullptr);
}

CWaitEvent::~CWaitEvent()
{
    CloseHandle(m_event);
}

void CWaitEvent::Set()
{
    SetEvent(m_event);
}

void CWaitEvent::Reset()
{
    ResetEvent(m_event);
}

void CWaitEvent::Wait()
{
    WaitForSingleObject(m_event, INFINITE);
}

#else
CWaitEvent::CWaitEvent()
{
    pthread_mutex_init(&m_mutex, nullptr);
    pthread_cond_init(&m_event, nullptr);
}

CWaitEvent::~CWaitEvent()
{
    pthread_mutex_destroy(&m_mutex);
    pthread_cond_destroy(&m_event);
}

void CWaitEvent::Set()
{
    pthread_mutex_lock(&m_mutex);
    pthread_cond_signal(&m_event);
    pthread_mutex_unlock(&m_mutex);
}

void CWaitEvent::Reset()
{
    //pthread_mutex_lock(&m_mutex);
    //pthread_cond_init(&m_event, nullptr);
    //pthread_mutex_unlock(&m_mutex);
}

void CWaitEvent::Wait()
{
    pthread_mutex_lock(&m_mutex);
    pthread_cond_wait(&m_event, &m_mutex);
    pthread_mutex_unlock(&m_mutex);
}

#endif



