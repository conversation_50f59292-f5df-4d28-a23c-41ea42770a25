/** @file ipc.c
 * @brief ipc module
 * @date 2012-12-27
 * @note
 *  History:
 *
 * Copyright(c) 2012-2020 Xiamen Ringslink telenology Co,.Ltd
 */
#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <pthread.h>
#include <unistd.h>
#include <asm/ioctls.h>

#include <sys/syscall.h>
#define gettid()   syscall(__NR_gettid)
#include "rl_log.h"
#include "ipc/ipc.h"
#include "revision.h"

#define IPC_DOMAIN_DIRECTORY    "/var/csvrecord_sock/"


typedef struct
{
    int id;  /*本进程的ID*/
    int fd;
    int tid;  /*线程的ID*/
    IPC_MSG_HANDLE msg_hanle;
    void* data;
} IPC_DATA;

char* ipc_id_str[] =
{
    "NULL",
    "IPC_ID_VA",
    "IPC_ID_PHONE",
    "IPC_ID_AUTOP",
    "IPC_ID_LIGHTTPD",
    "IPC_ID_NETCONFIG",
    "IPC_ID_UPGRADER",
    "IPC_ID_SIMULATOR",
    "IPC_ID_TEST",
};


IPC_DATA g_ipc_data = {0, 0, 0, NULL, NULL};

BOOL g_quitflag = RL_FALSE;

void ipc_show_version()
{
    rl_log_info("Init %s, Version %s, Build at %s %s", MOD_NAME, MOD_VERSION, __DATE__,  __TIME__);
}

/**
 * initialize IPC module
 */
void ipc_init()
{
    rl_log_info("Init %s, Version %s, Build at %s %s", MOD_NAME, MOD_VERSION, __DATE__,  __TIME__);
    return;
}

/**
 * register a process to IPC manager
 */
int ipc_register(int my_id)
{
    g_ipc_data.id = my_id;

    return 0;
}

/*线程函数*/
void* read_thread(int *p_listen_fd)
{
	int listen_fd = *p_listen_fd;
    rl_log_debug("This is thread, thread id is %u, gettid()=%d\n", (unsigned int)pthread_self(), gettid());
    UNIX_IPC_MSG* msg = NULL;
    int read_fd = 0;
    int msg_len = 0;
    socklen_t len;
    struct sockaddr_un cli_addr;

    // get_quitflag() is defined in rllib.c and is used
    // to let process quit when it has been set to quit
    while (get_quitflag() == RL_FALSE)
    {
        len = sizeof(cli_addr);
        read_fd = accept(listen_fd, (struct sockaddr*)&cli_addr, &len);

        if (read_fd < 0)
        {
            rl_log_err("accept failed: %s", strerror(errno));
            continue;
        }

        msg_len = 0;

        /*read 4 bytes for messgae lenght first*/
        len = read(read_fd, &msg_len, sizeof(int));
        if (len < sizeof(int))
        {
            /*FIX ME: must clear the recv buffer.*/
            rl_log_err("read len failed: read_len = %d", len);
            close(read_fd);
            continue;
        }

        if ((msg_len < 8) || (msg_len > IPC_MSG_MAX_SIZE))
        {
            /*FIX ME: must clear the recv buffer.*/
            rl_log_err("msg len failed: msg_len = %d", msg_len);
            close(read_fd);
            continue;
        }

        msg = (UNIX_IPC_MSG*)malloc(msg_len);
        if (msg == NULL)
        {
            rl_log_err("malloc msg buffer failed.");
            close(read_fd);
            continue;
        }
        msg->len = msg_len;

        /*read the other bytes*/
        len = read(read_fd, &msg->id, msg_len - sizeof(int));

        close(read_fd);

        if (len != (msg_len - sizeof(int)))
        {
            if (msg != NULL)
            {
                free(msg);
                msg = NULL;
            }

            rl_log_err("msg_len != read_len: msg_len = %d, read_len = %d", len, (msg_len - sizeof(int)));

            continue;
        }

#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
        if (IPC_ID_VA == g_ipc_data.id)
        {
            ipc_socket_send(msg, sizeof(IPC_MSG));
        }
#endif
        (*g_ipc_data.msg_hanle)(msg, g_ipc_data.data);

        if (msg != NULL)
        {
            free(msg);
            msg = NULL;
        }
    }

    return 0;
}

/**
 * run a process to IPC listen
 */
int ipc_run(IPC_MSG_HANDLE msg_handle, void* data)
{
    pthread_t thread_id = 0;
    int listen_fd = 0;

    struct sockaddr_un srv_addr;

    if (g_ipc_data.id < 1)
    {
        rl_log_err("registe ipc failed");
        return -1;
    }

    /*save message process handle and private data pointer*/
    g_ipc_data.msg_hanle = msg_handle;
    g_ipc_data.data = data;

    /*create an UNIX DOMAIN socket for listening*/

    listen_fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (listen_fd < 0)
    {
        rl_log_err("create socket failed: %s", strerror(errno));
        return -1;
    }

    memset(&srv_addr, 0, sizeof(struct sockaddr_un));
    srv_addr.sun_family = AF_UNIX;
    snprintf(srv_addr.sun_path, sizeof(srv_addr.sun_path), "%s%d", IPC_DOMAIN_DIRECTORY, g_ipc_data.id);
    unlink(srv_addr.sun_path);

    if (bind(listen_fd, (struct sockaddr*)&srv_addr, sizeof(srv_addr)) < 0)
    {
        system("mkdir -p "IPC_DOMAIN_DIRECTORY);
        if (bind(listen_fd, (struct sockaddr*)&srv_addr, sizeof(srv_addr)) < 0)
        {
            close(listen_fd);
            unlink(srv_addr.sun_path);
            rl_log_err("bind failed: %s", strerror(errno));
            return -1;
        }
    }

    if (listen(listen_fd, 64) < 0)
    {
        close(listen_fd);
        unlink(srv_addr.sun_path);

        rl_log_err("listen failed: %s", strerror(errno));
        return -1;
    }

    /*change the authority of file*/
    chmod(srv_addr.sun_path, 00777);

    g_ipc_data.fd = listen_fd;
    /*start listening*/

    if (pthread_create(&thread_id, NULL, (void*)read_thread, &g_ipc_data.fd) != 0)  /*创建线程*/
    {
        rl_log_err("pthread_create failed: %s", strerror(errno));
    }
    else
    {
        rl_log_debug("create thread sucess\n");
        g_ipc_data.tid = thread_id; /*线程ID*/
    }

#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
    if (IPC_ID_VA == g_ipc_data.id)
    {
        ipc_socket_init();
    }
#endif

    return 0;
}

/**
 * unregister a process to IPC manager
 */
int ipc_unregister()
{
#if (RL_PLATFORMID != RL_PLATFORMID_ANDROID)
    pthread_cancel(g_ipc_data.tid); /*销毁线程*/
#endif
    //pthread_join(g_ipc_data.tid, NULL);
    rl_log_debug("leave thread\n");

    g_ipc_data.msg_hanle = NULL;
    g_ipc_data.data = NULL;
    g_ipc_data.id = 0;

    if (g_ipc_data.fd > 0)
    {
        close(g_ipc_data.fd);
    }

    g_ipc_data.fd = 0;

#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
    int status = 0;
    if (0 != (status = pthread_kill(g_ipc_data.tid, SIGUSR1)))
    {
        rl_log_err("%s: Error cancelling thread %d, error = %d",
                   __FUNCTION__, g_ipc_data.tid, status);
    }
    rl_log_debug("%s: We will cause a segmentation fault, it's normal", __FUNCTION__);
#endif

    return 0;
}

/**
 * send a ipc message
 */
int ipc_send(int dest_id, int id, int param1, int param2, void* dat, int dat_len)
{
    int send_fd = 0;
    struct sockaddr_un srv_addr;
    IPC_MSG* msg = (IPC_MSG*)malloc(sizeof(IPC_MSG));
    if (msg == NULL)
    {
        rl_log_err("memery alloc failed: %s\n", strerror(errno));
        return -1;
    }
    memset(msg, 0, sizeof(IPC_MSG));
    msg->param1 = param1;
    msg->param2 = param2;
    msg->id = id;
    msg->from = g_ipc_data.id;

    if (dat_len > IPC_MSG_DATA_SIZE)
    {
        rl_log_err("data len %d is larger than %d\n", dat_len, IPC_MSG_DATA_SIZE);
        free(msg);
        return 1;
    }

    if ((dat != NULL) && (dat_len > 0))
    {
        memcpy(msg->data, dat, dat_len);
        msg->len = IPC_MSG_HEADER_SIZE + dat_len;
    }
    else
    {
        msg->len = IPC_MSG_HEADER_SIZE;
    }

    if (g_ipc_data.id < 1)
    {
        free(msg);
        return -1;
    }

    /*create an UNIX DOMAIN socket for connceting*/

    send_fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (send_fd < 0)
    {
        rl_log_err("socket failed: %s", strerror(errno));
        free(msg);
        return -1;
    }

    unsigned long ul = 1;
    if (ioctl(send_fd, FIONBIO, &ul) == -1)
    {
        rl_log_err("ioctl failed: %s", strerror(errno));
        close(send_fd);
        free(msg);
        return -1;
    }

    memset(&srv_addr, 0, sizeof(struct sockaddr_un));
    srv_addr.sun_family = AF_UNIX;
    snprintf(srv_addr.sun_path, sizeof(srv_addr.sun_path), "%s%d", IPC_DOMAIN_DIRECTORY, dest_id);
    if (connect(send_fd, (struct sockaddr*)&srv_addr, sizeof(srv_addr)) < 0)
    {
        close(send_fd);
        //rl_log_err("connect %d failed: %s", dest_id, strerror(errno));
        free(msg);
        return -1;
    }

    if (write(send_fd, msg, msg->len) < 0)
    {
        rl_log_err("write failed: %s\n", strerror(errno));
        free(msg);
        close(send_fd);
        return -1;
    }

    free(msg);
    close(send_fd);

    return 0;
}

/**

 * broadcast a ipc message
 */
int ipc_broadcast(int id, int param1, int param2, void* dat, int dat_len)
{
    int i = 0;
    for (i = (int)IPC_ID_MIN + 1; i < (int)IPC_ID_MAX; i++)
    {
        ipc_send(i, id, param1, param2, dat, dat_len);
    }

    return 0;
}

BOOL get_quitflag()
{
    return g_quitflag;
}

void set_quitflag(BOOL quitflag)
{
    g_quitflag = quitflag;
}
