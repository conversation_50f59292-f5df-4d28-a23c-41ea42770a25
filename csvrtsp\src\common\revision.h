#ifndef __REVISION_H__
#define __REVISION_H__

//#include "rl_revision.h"

/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : liyang.liu
*  Date         : 2013/03/20
*  Reason       :
*  Modification : (1)makefile优化，ipc需要重新发布
*  Compile Def  :
*************************************************************/

/*********************************************
*  新版本号：*******
*  旧版本号：*******
*  修改者：Jianan
*  提交时间：2012-12-27
*  修改原因：需要版本维护，解决内存出错
*  修改内容：增加ipc_init, 在ipc_send里 return前增加free
**********************************************/

/*********************************************
*  新版本号：*******
*  旧版本号：*******
*  修改者：shaowei.chen
*  提交时间：2012-12-24
*  修改原因：需要使用新的IPC接口
*  修改内容：修改了IPC接口, 不需要组成IPC_MSG，只需要传入需要传输的内容
**********************************************/

/*********************************************
*  新版本号：*******
*  旧版本号：无
*  修改者：shaowei.chen
*  提交时间：2012-12-15
*  修改原因：第一个版本
*  修改内容：
**********************************************/

/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2012/12/24
*  Reason       : 提交ipc进程的第一版本
*  Modification :
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2012/12/24
*  Reason       : 需要使用新的IPC接口
*  Modification : 修改了IPC接口, 不需要组成IPC_MSG，只需要传入需要传输的内容
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : jianan.lin
*  Date         : 2012/12/25
*  Reason       : (1)内存出错
                  (2)ipc send 一些条件未判断
*  Modification : (1)增加free(msg);
                  (2)当 data长度大于IPC_MSG_DATA_SIZE时，ipc send返回1，成功返回0，其他返回-1
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2013/01/11
*  Reason       : (1)增加版本打印
*  Modification :
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2013/01/11
*  Reason       : (1)增加IPC广播
*  Modification :
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : sichao.yang
*  Date         : 2013/02/16
*  Reason       :
*  Modification : (1)增加IPC广播，用于AUTOP发送消息
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : sichao.yang
*  Date         : 2013/02/20
*  Reason       :
*  Modification : (1)修改ipc_send接口，读取完数据立即关闭socket
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2013/03/01
*  Reason       :
*  Modification : (1)修改ipc_send为非阻塞发送，防止接收方缓冲区满时造成发送方卡住
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2013/04/02
*  Reason       :
*  Modification : (1)打开-Wall去掉一些警告信息
                  (2)去掉rllib库的依赖
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2013/04/17
*  Reason       :
*  Modification : (1)修改ipc打印的版本号是数字的错误
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******0
*  Old version  : *******
*  Modifier     : shaowei.chen
*  Date         : 2013/06/8
*  Reason       :
*  Modification : (1)增加广播到RCPE进程
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******1
*  Old version  : *******0
*  Modifier     : shaowei.chen
*  Date         : 2013/06/11
*  Reason       :
*  Modification : (1)非_TEST_MODE模式IPC_ID_TEST不加入到广播队列
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******2
*  Old version  : *******1
*  Modifier     : sichao.yang
*  Date         : 2013/08/01
*  Reason       :
*  Modification : (1)增加BROAD_ID_DHCP_OPTION，用于Netconfig发送Dhcp Option准备好的消息
*  Compile Def  :
*************************************************************/
/************************************************************
*  New version  : *******3
*  Old version  : *******2
*  Modifier     : sichao.yang
*  Date         : 2013/08/05
*  Reason       :
*  Modification : (1)增加Netconfig发给Phone的消息类型N2P
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******4
*  Old version  : *******3
*  Modifier     : shaowei.chen
*  Date         : 2013/08/08
*  Reason       :
*  Modification : (1)增加BROAD_ID_SET_LOGLEVEL
*  Compile Def  :
*************************************************************/

/*************************************************************
*  New version  : *******5
*  Old version  : *******4
*  Modifier     : LinKy
*  Date         : 2014/01/17
*  Reason       :
*  Modification : (1) 在Android平台增加ipc_socket机制, 利用socket控制远端VA, 当
                PLATFORMID == PLATFORMID_ANDROID时该机制启用
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******6
*  Old version  : *******5
*  Modifier     : Will
*  Date         : 2014/02/12
*  Reason       :
*  Modification : (1)增加IPC_ID_PMONITOR
*  Compile Def  :
*************************************************************/
/*************************************************************
*  New version  : *******7
*  Old version  : *******6
*  Modifier     : Pantheon
*  Date         : 2013/08/21
*  Reason       :
*  Modification : (1)去掉宏
                                    *#if RL_GLOBAL_ADD_ADVANCE_PMONITOR
*                                       IPC_ID_PMONITOR,
*                                   #endif
*                               改为IPC_ID_PMONITOR,因为新增了R52 R53 也需要用到这个
*
*  Compile Def  :
*************************************************************/
/*********************************************
*  New version : *******7
*  Old version : *******8
*  Author      : Mike
*  CommitType  ：
*  Date        ：2015/11/12
*  Reason：    ：调整IPC_ID_TEST位置，并且非_TEST_MODE模式下取消该定义
*
*  Modified:   ：
*
*  Issue       :
*  Affected    :
**********************************************/

/*********************************************
 *  New version  : *******3
 *  Old version  : *******2
 *  Author  : Andy
 *  CommitType :  进程优化
 *  Date：2016/6/30
 *  Reason：
 *  Modified: 修改va/netcast id为 netconfig id
 *  Issue:
 *
 *  Affected:
 **********************************************/
/*********************************************
 *  New version  : *******4
 *  Old version  : *******3
 *  Author  : Oliver
 *  CommitType :  需求
 *  Date：2016/09/18
 *  Reason：
 *  Modified: 增加定义IPC_ID_DCLIENT；
 *  Issue:
 *
 *  Affected:
 **********************************************/
/*********************************************
*  New version : ********
*  Old version : *******4
*  Author      : Zalman/Andy
*  CommitType  ：bug修复
*  Date        ：2016/12/15
*  Reason：    ：代码检查时发现二位数组定义时少了逗号
*  Modified:   ：
*  Issue       :
*  Affected    : ipc
**********************************************/
#define MOD_VERSION  "********"
#define MOD_NAME    "libipc"

#define RL_PLATFORMID   0xFFFF


#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif

