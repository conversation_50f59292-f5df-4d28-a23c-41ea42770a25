#include "utils.h"
#include <stdio.h>
#include <string.h>


void string_split(const std::string& s, std::string& delim, std::vector< std::string >* ret)
{
    size_t last = 0;
    size_t index = s.find(delim, last);
    while (index != std::string::npos)
    {
        ret->push_back(s.substr(last, index - last));
        last = index + delim.size();
        index = s.find(delim, last);
    }
    if (index - last > 0)
    {
        ret->push_back(s.substr(last, index - last));
    }
}

