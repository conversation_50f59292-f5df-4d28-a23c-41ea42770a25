#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <string>
#include <map>
#include <thread>
#include "libvrtspd/HttpServer.h"
#include <evpp/tcp_client.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "libvrtspd/TcpClient.h"
#include "ipc/ipc.h"
#include "libvrtspd/rtsp_server_interface.h"
#include "glog/logging.h"
#include "libvrtspd/CsvrtspConf.h"
#include "ConfigFileReader.h"
#include <fcntl.h>
#include "libvrtspd/Control.h"
#include "libvrtspd/VrtspDefine.h"
#include <evnsq/producer.h>
#include <event_loop.h>
#include <evnsq/client.h>
#include "RtspMQProduce.h"
#include "RtspEtcd.h"
#include "CachePool.h"
#include "ConnectionPool.h"
#include "AkcsMonitor.h"
#include "SnowFlakeGid.h"
#include "RtspMonitor.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "PcapControl.h"
#include "RtspSession.h"
#include "thirdlib/ZLMediaKit/include/Util/File.h"
#include "thirdlib/ZLMediaKit/include/Util/SSLBox.h"
#include "thirdlib/ZLMediaKit/include/Network/TcpServer.h"
#include <KdcDecrypt.h>
#include "Metric.h"

#define MAX_RTSP_RLDB_CONN 2
RouteMQProduce* g_nsq_producer = nullptr;
std::string g_logic_srv_id;
//add by larry 20180305 判断单例
#define PIDFILE "/var/run/csvrtspd.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_conf_db_addr;
static const char kCaFilepath[] = "/usr/local/akcs/csvrtsp/tls/ca";
static const char kCertFilepath[] = "/usr/local/akcs/csvrtsp/tls/certificate";
static const char conf_file[] = "/usr/local/akcs/csvrtsp/conf/csvrtsp.conf";
static const char ip_conf_file[] = "/etc/ip";

int g_etcd_dns_res = 0;

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}


//end by larry

void ipc_handle(UNIX_IPC_MSG* msg, void* data)
{

    if (IPC_ID_VRTPSD == msg->from)
    {
        return;
    }

    switch (msg->id)
    {
        //added by chenyc,2019-03-04,分布式,csmain->csvrtspd 由unix socket改成route转发, 见AKCS_R2V_P2P_RTSP_CAPTURE_REQ
        //case MSG_C2VRTSP_CAPTURE_RTSP:
        // 根据mac将其deviceClient的 capture_ 置为true; 并发送一条ipc给vrecord， 通知其打开截图线程
        //  GetControlInstance()->AddMsg(MSG_CTRL_START_CAPTURE, msg->param1, msg->param2, msg->data, sizeof(SOCKET_MSG_CAPTURE_RTSP));
        //  break;

        case MSG_VRECORD2VRTSP_STOP_CAPTURE:
            // 根据flow_uuid将其deviceClient的 capture_ 置为false;
            GetControlInstance()->AddMsg(MSG_CTRL_STOP_CAPTURE, msg->param1, msg->param2, msg->data, sizeof(SOCKET_MSG_CAPTURE_RTSP));
            break;

        default:
            break;
    }
}

static bool g_vrtsp_flag = true;
CSVRTSP_CONF gstCSVRTSPConf; //全局配置信息

void signal_handler(int sig)
{
    printf("I got a signal %d\nI'm quitting.\n", sig);
    g_vrtsp_flag = false;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstCSVRTSPConf.db_ip, gstCSVRTSPConf.db_port);
    return 0;
}
void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, gstCSVRTSPConf.db_ip) != 0) || (conf_tmp.db_port != gstCSVRTSPConf.db_port))
    {
        Snprintf(gstCSVRTSPConf.db_ip, sizeof(gstCSVRTSPConf.db_ip),  conf_tmp.db_ip);
        gstCSVRTSPConf.db_port = conf_tmp.db_port;
        DaoReInit();
    }
}
int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    memset(&conf_tmp, 0, sizeof(conf_tmp));
    
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(gstCSVRTSPConf.db_ip, sizeof(gstCSVRTSPConf.db_ip),  conf_tmp.db_ip);
    gstCSVRTSPConf.db_port = conf_tmp.db_port;
    return 0;
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSVRTSPConf.etcd_server_addr);

    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void ConfInit()
{
    CConfigFileReader config_file(conf_file);

    Snprintf(gstCSVRTSPConf.csvrtsp_outer_ip, sizeof(gstCSVRTSPConf.csvrtsp_outer_ip),  config_file.GetConfigName("csvrtsp_outerip"));
    Snprintf(gstCSVRTSPConf.csvrtsp_outer_domain, sizeof(gstCSVRTSPConf.csvrtsp_outer_domain),  config_file.GetConfigName("csvrtsp_outer_domain"));
    Snprintf(gstCSVRTSPConf.csvrtsp_outer_ipv6, sizeof(gstCSVRTSPConf.csvrtsp_outer_ipv6),  config_file.GetConfigName("csvrtsp_outeripv6"));
    Snprintf(gstCSVRTSPConf.db_username, sizeof(gstCSVRTSPConf.db_username),  config_file.GetConfigName("db_username"));

    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));
    Snprintf(gstCSVRTSPConf.db_password, sizeof(gstCSVRTSPConf.db_password), decrypt_db_passwd.c_str());
    Snprintf(gstCSVRTSPConf.db_database, sizeof(gstCSVRTSPConf.db_database),  config_file.GetConfigName("db_database"));
 
    if(LoadConfFromConfSrv() != 0)
    {
        Snprintf(gstCSVRTSPConf.db_ip, sizeof(gstCSVRTSPConf.db_ip),  config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        gstCSVRTSPConf.db_port = ATOI(db_port);
    }
    
    Snprintf(gstCSVRTSPConf.nsq_topic, sizeof(gstCSVRTSPConf.nsq_topic),  config_file.GetConfigName("nsq_route_topic"));
    Snprintf(gstCSVRTSPConf.svn_version, sizeof(gstCSVRTSPConf.svn_version),  config_file.GetConfigName("svn_version"));
    Snprintf(gstCSVRTSPConf.group_name, sizeof(gstCSVRTSPConf.group_name), config_file.GetConfigName("group_name"));

    Snprintf(gstCSVRTSPConf.monitor_ip_list, sizeof(gstCSVRTSPConf.monitor_ip_list), config_file.GetConfigName("monitor_ip_list"));

    gstCSVRTSPConf.rtp_process_thread_num = ATOI(config_file.GetConfigName("rtp_process_thread_num"));

    const char* keep_alive_times = config_file.GetConfigName("keep_alive_times");
    gstCSVRTSPConf.keep_alive_times = ATOI(keep_alive_times);

    const char* timer_heartbeat_time = config_file.GetConfigName("timer_heartbeat_time");
    gstCSVRTSPConf.timer_heartbeat_time = ATOI(timer_heartbeat_time);
    
    const char* timer_monitor_timeout = config_file.GetConfigName("timer_monitor_timeout");
    gstCSVRTSPConf.timer_monitor_timeout = ATOI(timer_monitor_timeout);
    
    const char* timer_inner_keepalive_time = config_file.GetConfigName("timer_inner_keepalive_time");
    gstCSVRTSPConf.timer_inner_keepalive_time = ATOI(timer_inner_keepalive_time);
    
    const char* timer_pcap_timeout = config_file.GetConfigName("timer_pcap_timeout");
    gstCSVRTSPConf.timer_pcap_timeout = ATOI(timer_pcap_timeout);
    
    const char* timer_rtp_packet_timeout = config_file.GetConfigName("timer_rtp_packet_timeout");
    gstCSVRTSPConf.timer_rtp_packet_timeout = ATOI(timer_rtp_packet_timeout);   

    const char* reg_etcd = config_file.GetConfigName("reg_etcd");
    gstCSVRTSPConf.reg_etcd = ATOI(reg_etcd);

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    gstCSVRTSPConf.request_statics_switch = ATOI(config_file.GetConfigName("request_statics_switch"));

    CConfigFileReader config_file1(ip_conf_file);
    Snprintf(gstCSVRTSPConf.csvrtsp_inner_ip, sizeof(gstCSVRTSPConf.csvrtsp_inner_ip),  config_file1.GetConfigName("SERVER_INNER_IP"));
}

void* CtrlRun()
{
    GetControlInstance()->Init();
    GetControlInstance()->Run();
    return NULL;
}

int InstanceInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/csvrtsp/conf/csvrtsp_redis.conf", "csvrtspCacheInstances");
}

/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    conn_pool->Init(gstCSVRTSPConf.db_ip, gstCSVRTSPConf.db_username, gstCSVRTSPConf.db_password, gstCSVRTSPConf.db_database, gstCSVRTSPConf.db_port, MAX_RTSP_RLDB_CONN, "csvrtsp");
    return 0;
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstCSVRTSPConf.etcd_server_addr, sizeof(gstCSVRTSPConf.etcd_server_addr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstCSVRTSPConf.etcd_server_addr;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    CConfigFileReader config_file(conf_file);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstCSVRTSPConf.etcd_server_addr, sizeof(gstCSVRTSPConf.etcd_server_addr),  config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstCSVRTSPConf.etcd_server_addr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstCSVRTSPConf.etcd_server_addr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}

void LoadCertificate()
{
    //加载文件夹下的所有certificate
    toolkit::File::scanDir(kCertFilepath, [](const string &path, bool isDir) 
    {
        if (!isDir) 
        {
            //最后的一个证书会当做默认证书(客户端ssl握手时未指定主机)
            if (toolkit::SSL_Initor::Instance().loadCertificate(path.data(), true))
            {
                AK_LOG_INFO << "loadCertificate " << path << " success";
            }
            else
            {
                AK_LOG_INFO << "loadCertificate " << path << " fail";
            }
        }
        return true;
    });

    //加载文件夹下的所有ca
    toolkit::File::scanDir(kCaFilepath, [](const string &path, bool isDir) 
    {
        if (!isDir) 
        {
            if (toolkit::SSL_Initor::Instance().trustCertificate(path.data(), true))
            {
                AK_LOG_INFO << "trustCertificate " << path << " success";
            }
            else
            {
                AK_LOG_INFO << "trustCertificate " << path << " fail";
            }
        }
        return true;
    });
    
    toolkit::SSL_Initor::Instance().ignoreInvalidCertificate(false);
}

void InitLog()
{
    //glog初始化
    google::InitGoogleLogging("vrtspd");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csvrtsplog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csvrtsplog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csvrtsplog/ERROR");
    FLAGS_logbufsecs = 0;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        printf("another csmain has been running in this system.");
        return -1;
    }

    // 初始化log
    InitLog();

    //配置中心初始化
    //一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
    memset(&gstCSVRTSPConf, 0, sizeof(CSVRTSP_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }
    ConfSrvInit();
    
    // 初始化配置信息
    ConfInit();

    // 初始化redis
    if (InstanceInit() != 0)
    {
        AK_LOG_FATAL << "init instance failed";
        return -1;
    }

    // 初始化数据库
    if (DaoInit() != 0)
    {
        AK_LOG_FATAL << "DaoInit fialed.";
        return -1;
    }
    
    //added by chenyc,2019-08-06,traceid实现.前期先用配置文件来区分这两个值，后面需要将traecid（uuid）获取做成一个服务
    AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setWorkerId(1);
    AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setDatacenterId(1);

    AK_LOG_INFO << "csvrtspd start";

    akuvox::IRtspServer* rtsp_server = akuvox::CRtspServerFactory::GetInstance();
    rtsp_server->start();
    
    g_logic_srv_id = "csvrtsp_";
    g_logic_srv_id += GetEth0IPAddr();

    //ipc
    ipc_register(IPC_ID_VRTPSD);
    ipc_run(ipc_handle, (void*)rtsp_server);//注册

    // 起消息处理线程
    std::thread control(CtrlRun);
    
    //注册服务
    std::thread etcdCliThread = std::thread(EtcdSrvInit);

    // 初始化etcd对象
    ConfSrvInit();

    //起http服务线程
    std::thread httpThread(startHttpServer);

    //起Pcap抓包线程
    GetPcapCaptureControlInstance()->Init();

    // 加载证书和CA
    LoadCertificate();

    // 启动rtsp服务器
    toolkit::TcpServer::Ptr rtsp_service(new toolkit::TcpServer());    
    toolkit::TcpServer::Ptr rtsps_service(new toolkit::TcpServer());    
    toolkit::TcpServer::Ptr rtsp_wechat_service(new toolkit::TcpServer());    
    rtsp_service->start<mediakit::RtspSession>(554);
    rtsp_wechat_service->start<mediakit::RtspSession>(8601);
    rtsps_service->start<toolkit::SessionWithSSL<mediakit::RtspSession>>(8602);

    //nsq消息发布
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQMessage);//基本不需要关心
    client.SetReadyCallback(&OnNSQReady);//ready(与其中一个nsqd服务tcp连接上之后)之后才能开始发布消息.
    client.SetConnectErrorCallback(&OnConnectError);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514"); //所有的生产者都连接本机的nsqd即可,而消费者必须连接到nsqlookupd指定的主题才行
    client.ConnectToNSQDs(nsqd_tcp_addr);
    g_nsq_producer = new RouteMQProduce(&client);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    std::thread conf_watch_thread = std::thread(ConfWatch);
    
    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "vrtspd is starting";

    nsq_loop.Run();
    httpThread.join();
    dnsThread.join();
    conf_watch_thread.join();
    
    ipc_unregister();
    LOG(INFO) << "vrtspd end";
    google::ShutdownGoogleLogging();
}

