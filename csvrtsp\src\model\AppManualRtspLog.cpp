#include <string.h>
#include "AKLog.h"
#include "DbOperator.h"
#include "ConfigOperator.h"
#include "Config.h"
#include <sstream>
#include "CsvrtspConf.h"
#include "RldbQuery.h"
#include "AppManualRtspLog.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include "dbinterface/AppManualRtsp.h" 
#include "util.h"


extern CSVRTSP_CONF gstCSVRTSPConf; //全局配置信息

namespace akuvox
{

CAppManualRtspLog::CAppManualRtspLog() : tag_("AppManualRtspLog")
{
    manual_ = 0;
}

CAppManualRtspLog::~CAppManualRtspLog()
{
    //addRtspLog2DB();
}

void CAppManualRtspLog::setRtspLogInfo(const std::string& mac, const std::string& user, int manual)
{
    user_account_ = user;
    mac_ = mac;
    manual_ = manual;

    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    char cur_time[36] = {0};
    ::snprintf(cur_time, 36, "%04d-%02d-%02d %02d:%02d:%02d", (1900 + p->tm_year), (p->tm_mon + 1), p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    create_time_ = cur_time;
    start_timep_ = timep;
    return;
}

int CAppManualRtspLog::addRtspLog2DB()
{
    if (!manual_) //不是手动
    {
        return 0;
    }

    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(user_account_, account))
    {
        return 0;
    }
    
    int role;
    char accounts[64] = {0};
    Snprintf(accounts, sizeof(accounts), account.account);
    role = account.role;


    if (role != ACCOUNT_ROLE_PERSONNAL_ATTENDANT && role != ACCOUNT_ROLE_COMMUNITY_ATTENDANT) //主账号
    {
        node_ = user_account_;
    }
    else
    {
        //如果是从账号,则查询出对应的主账号
        ResidentPerAccount node;
        memset(&node, 0, sizeof(node));
        if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(user_account_, node))
        {
            return 0;
        }
        node_ = node.account;
    }

    time_t timep;
    time(&timep);
    int time = timep - start_timep_;
    //插入数据
    dbinterface::AppManualRtsp::InsertAppManualRtsp(mac_, user_account_, node_, create_time_, time);

    return 1;

}

}
