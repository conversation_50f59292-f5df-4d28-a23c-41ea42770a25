#ifndef _APP_MANUAL_RTSP_LOG_H_
#define _APP_MANUAL_RTSP_LOG_H_


#include <string>
#include <vector>
#include <set>
#include "DbOperator.h"
#include "ConfigOperator.h"
#include "ConnectionPool.h"
//#include "util_cstring.h"
#include <time.h>
#include "AkcsCommonDef.h"

namespace akuvox
{

class CAppManualRtspLog
{
public:
    ~CAppManualRtspLog();
    CAppManualRtspLog();
    void setRtspLogInfo(const std::string& mac, const std::string& user, int manual);
    int addRtspLog2DB();
private:
    const char* tag_;
    std::string mac_;
    int manual_;
    std::string create_time_;
    std::string user_account_;
    std::string node_;
    time_t start_timep_;
};
}

#endif
