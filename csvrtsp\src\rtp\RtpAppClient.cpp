#include <unistd.h>
#include <netinet/in.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <string.h>
#include "AKLog.h"
#include "AkLogging.h"
#include <string>
#include "ByteIO.h"
#include "RtpDeviceClient.h"
#include "RtpAppClient.h"
#include "RtpEpollThread.h"

namespace akuvox
{
RtpAppClient::RtpAppClient(uint64_t trace_id, unsigned short local_rtp_port, int rtsp_fd) : tag_("RtpAppClient")
{
    rtp_fd_ = -1;
    rtcp_fd_ = -1;
    local_rtp_port_ = local_rtp_port;
    local_rtcp_port_ = local_rtp_port + 1;
    //m_nRemoteRtpPort = nRemoteRtpPort;
    rtsp_fd_ = rtsp_fd;
    init_ = false;
    hasnat_ = false;
    has_rctp_nat_ = false;
    has_dev_rtp_ssrc_set_ = false;
    memset(&app_addr_, 0, sizeof(app_addr_));
    memset(&app_rtcp_addr_, 0, sizeof(app_rtcp_addr_));

    receiver_rtcp_num = 0;
    //TODO:app_addr_ m_nRtpFd生存周期要长于rtcp_module 不然里面会引用空指针
    rtcp_module.reset(new AKModuleRtpRtcp(&app_addr_, &rtp_fd_));
    rtcp_receiver.reset(new AKRtcpReceiver(rtcp_module));

    //TODO:app_rtcp_addr_ m_nRtcpFd生存周期要长于rtcp_transport 不然里面会引用空指针
    rtcp_transport.reset(new AKRtcpTransport(&app_rtcp_addr_, &rtcp_fd_)); //发送nack
    rtcp_sender.reset(new AkRtcpSender(rtcp_transport.get()));

    rtcp_module->setRtcpSender(rtcp_sender);

    trace_id_ = trace_id;
    rtp_confuse_switch_ = false;
    app_client_ssrc_ = 0;
}

RtpAppClient::~RtpAppClient()
{
    AK_LOG_INFO << "[" << trace_id_ << "] ~RtpAppClient(), rtp_fd = " << rtp_fd_ << ", rtcp_fd = " << rtcp_fd_;
    // 如果init_为true，则表示已经初始化成功，需要移除socket
    // 如果init_为false，说明CreateRtpSocket失败了, 后面重试生成的fd可能和上次失败的一样，不能remove掉
    if (init_)
    {
        RtpEpollThread::getInstance()->RemoveRtpSocket(rtp_fd_, rtcp_fd_, trace_id_);    
        rtp_fd_ = -1;
        rtcp_fd_ = -1;
    }
}

std::string RtpAppClient::toString()
{
    char infos[256] = { 0 };
    snprintf(infos, sizeof(infos), "RtpAppClient[fd=%d,local port=%hu,rtspfd=%d,addr[ip:port=%s]]",
             rtp_fd_, local_rtp_port_, rtsp_fd_, Sock_ntop((SA*)&app_addr_, sizeof(app_addr_)));
    std::string info = infos;
    return info;
}
//这个要根据app所处的而网络环境是ipv6还是ipv4来决定怎样创建app的rtp包的描述符
//ipv6
bool RtpAppClient::CreateRtpSocket()
{
    if (init_)
    {
        return true;
    }

    //struct sockaddr_in address;
    //bzero(&address, sizeof(address));
    //address.sin_family = AF_INET;
    //address.sin_port = htons(local_rtp_port_);  //服务器接受app即将穿透nat的udp包,为该包准备一个服务器本端的端口
    //address.sin_addr.s_addr = htonl(INADDR_ANY);

    //rtp_fd_ = socket(PF_INET, SOCK_DGRAM, 0);
    struct sockaddr_in6 address;
    bzero(&address, sizeof(address));
    address.sin6_family = AF_INET6;
    address.sin6_port = htons(local_rtp_port_);
    address.sin6_addr = in6addr_any;
    rtp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (-1 == rtp_fd_)
    {
        CAKLog::LogE(tag_, "create socket error=%s errno=%d", strerror(errno), errno);
        return false;
    }
    
    AK_LOG_INFO << "[" << trace_id_ << "] create app rtp socket = " << rtp_fd_ << ", recv app nat packet, local app udp port = " << local_rtp_port_ << ", rtsp client fd = " << rtsp_fd_;
   
    //int on = 1;
    int ret = bind(rtp_fd_, (struct sockaddr*)&address, sizeof(address));
    if (-1 == ret)
    {
        CAKLog::LogE(tag_, "bind socket error=%s errno=%d", strerror(errno), errno);
        close(rtp_fd_);
        return false;
    }

    struct sockaddr_in6 address_rtcp;
    bzero(&address_rtcp, sizeof(address_rtcp));
    address_rtcp.sin6_family = AF_INET6;
    address_rtcp.sin6_port = htons(local_rtcp_port_);
    address_rtcp.sin6_addr = in6addr_any;
    rtcp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (-1 == rtcp_fd_)
    {
        CAKLog::LogE(tag_, "create rtcp socket error=%s errno=%d", strerror(errno), errno);
        close(rtp_fd_);
        return false;
    }

    AK_LOG_INFO << "[" << trace_id_ << "] create app rtcp socket = " << rtcp_fd_ << ", app recv rtcp port = " << local_rtcp_port_;

    ret = bind(rtcp_fd_, (struct sockaddr*)&address_rtcp, sizeof(address_rtcp));
    if (-1 == ret)
    {
        CAKLog::LogE(tag_, "bind rtcp socket error=%s errno=%d", strerror(errno), errno);
        close(rtcp_fd_);
        close(rtp_fd_);
        return false;
    }
    init_ = true;
    return true;
}

void RtpAppClient::setDeviceClient(std::shared_ptr<RtpDeviceClient> rtpdev)
{
    rtp_device_client = rtpdev;
    rtcp_module->setDeviceClient(rtpdev);
}

void RtpAppClient::SetRtpConfuseSwitch(bool rtp_confuse_switch)
{
    AK_LOG_INFO << "[" << trace_id_ << "] app rtp confuse switch: " << rtp_confuse_switch;
    rtp_confuse_switch_ = rtp_confuse_switch; 
    rtcp_module->SetRtpConfuseSwitch(rtp_confuse_switch);
    rtcp_transport->rtcp_confuse_switch_ = rtp_confuse_switch;
}

void RtpAppClient::setAppClientSsrc(uint32_t ssrc)
{
    //std::set<uint32_t> ssrcs = {ssrc};
    //rtcp_receiver->rtcp_receiver_.SetSsrcs(ssrc, ssrcs);
    //rtcp_receiver->rtcp_receiver_.SetRemoteSSRC(kSenderSsrc);
}
void RtpAppClient::setDevRtpSsrc(uint32_t ssrc)
{
    dev_ssrc_ = ssrc;
    rtcp_sender->SetSSRC(dev_ssrc_);
}

void RtpAppClient::onRtcpMessage(unsigned char* data, unsigned int data_len)
{
    unsigned char data_convert[RTP_BUFFER_SIZE];
    memset(data_convert, 0, RTP_BUFFER_SIZE);
    int data_convert_len = RTP_BUFFER_SIZE;

    if(rtp_confuse_switch_)
    {
        //如果开启了rtp混淆，则需要解混淆
        RtpConfuse::DecRtpConfuse(data, data_len, data_convert, &data_convert_len);
    }
    else
    {                    
        memcpy(data_convert, data, data_len);
        data_convert_len = data_len;
    }

    const uint8_t* ptr = &data_convert[4];
    uint32_t recv_ssrc = akcs::ByteReader<uint32_t>::ReadBigEndian(ptr);
    //TODO:设置app的ssrc 目前没有协商 setAppClientSsrc
    if (!has_rctp_nat_) //是否已经完成app的udp-nat工作
    {
        has_rctp_nat_ = true;
        rtcp_receiver->SetRemoteSsrc(recv_ssrc);
    }

    if (receiver_rtcp_num % 20 == 0)
    {
        //TODO 注入一个rtp，让其产生发送者报告里面的last timestamp 后面要优化(目前不需要去统计rtp数据).统计数据最长超时是8秒
        //所以通过app主动发出的rtcp包，平台这里就根据20个rtcp包插入一个rtp包，给rtcp_sender用
        rtcp_sender->InsertIncomingPacket(recv_ssrc, receiver_rtcp_num);
    }
    receiver_rtcp_num++;
    if (!has_dev_rtp_ssrc_set_ && rtp_device_client->first_dev_rtp_arrive)
    {
        has_dev_rtp_ssrc_set_ = true;
        setDevRtpSsrc(rtp_device_client->dev_rtp_ssrc_);
        rtcp_receiver->SetSsrc(dev_ssrc_, recv_ssrc);
    }
    //rtcp_receiver->SetSsrc(rtp_device_client->ssrc_, rtp_device_client->ssrc_);
    rtcp_receiver->rtcp_receiver_->IncomingPacket(data_convert, data_convert_len);

#if 0 //计算app rtt
    const uint8_t* ptr = &data[4];
    uint32_t recv_ssrc = akcs::ByteReader<uint32_t>::ReadBigEndian(ptr);

    int64_t last_rtt_ms = 0;
    int64_t avg_rtt_ms = 0;
    rtcp_receiver->rtcp_receiver_->RTT(recv_ssrc, &last_rtt_ms, &avg_rtt_ms, nullptr, nullptr);
    CAKLog::LogE(tag_, "==============%ld app rtt=%d", recv_ssrc, avg_rtt_ms);
#endif

}
uint16_t RtpAppClient::getRtpPort()
{
    return local_rtp_port_;
}

}
