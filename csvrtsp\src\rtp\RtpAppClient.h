#pragma once
#include <string>
#include <map>
#include <netinet/in.h>
#include "Unp.h"
#include <vector>
#include <memory>
#include "RtcpReceiver.h"
#include "RtcpSender.h"
#include "NackModule.h"
#include "VrtspDefine.h"
#include "RtpConfuse.h"


namespace akuvox
{
class RtpDeviceClient;
class AKModuleRtpRtcp;
class AKRtcpReceiver;

//app的用于接受监控数据rtp包的类
class RtpAppClient
{

public:
    /*
     * local_rtp_port:服务器用于接收app端rtp数据的端口
     * rtsp_fd_:一一对应的RtspClient套接字
     * nRemoteRtpPort:协商出来的app端rtp端口，实际服务器不是往这个端口给app发送数据
     */
    RtpAppClient(uint64_t trace_id, unsigned short local_rtp_port, int rtsp_fd);
    ~RtpAppClient();

    bool CreateRtpSocket();
    std::string toString();
    void onRtcpMessage(unsigned char* data, unsigned int data_len);
    void setDeviceClient(std::shared_ptr<RtpDeviceClient> rtpdev);
    void setAppClientSsrc(uint32_t ssrc);
    void setDevRtpSsrc(uint32_t ssrc);
    bool operator<(const RtpAppClient& client)
    {
        return local_rtp_port_ < client.local_rtp_port_;
    }
    uint16_t getRtpPort();
    bool GetRtpConfuseSwitch()
    {
        return rtp_confuse_switch_;
    }
    
    void SetRtpConfuseSwitch(bool rtp_confuse_switch);
    
    bool IsSupportCheckSSRC()
    {
        return app_client_ssrc_ > 0;
    }

public:
    int rtp_fd_;   //app接收rtp包的udp网络描述符
    int rtcp_fd_;   //app接收rtcp包的udp网络描述符
    //ipv6
    //struct sockaddr_in app_addr_;     //当rtsp服务器收到app发过来的几个RTP包后，保存nat后的ip+端口信息，后面服务器转发到这个实际端口，而不是协商端口
    struct sockaddr_storage app_addr_;
    unsigned short local_rtp_port_;     //用于接收App的服务器rtp端口 rtsp服务器也会接受rtsp客户端的rtp包,目的是实现NAT
    struct sockaddr_storage app_rtcp_addr_;
    unsigned short local_rtcp_port_;
    //unsigned short m_nRemoteRtpPort;  //协商出来的APP端口,是app-rtsp客户端的内网端口
    int rtsp_fd_;
    bool init_;
    bool hasnat_;
    bool has_rctp_nat_;
    bool has_dev_rtp_ssrc_set_;
    const char* tag_;
    std::shared_ptr<AKModuleRtpRtcp> rtcp_module;
    std::shared_ptr<AKRtcpReceiver> rtcp_receiver;
    std::shared_ptr<AKRtcpTransport> rtcp_transport;
    std::shared_ptr<AkRtcpSender> rtcp_sender;
    std::shared_ptr<RtpDeviceClient> rtp_device_client;//app监控的对应的设备的rtp客户端对象

    uint32_t dev_ssrc_;//设备流的ssrc
    uint32_t app_client_ssrc_;//app 发送端的ssrc,用于app rtcp接收报告 由rtsp下发，目前未实现
    int receiver_rtcp_num;
    uint64_t trace_id_;
    
private:
    bool rtp_confuse_switch_;
};

typedef std::shared_ptr<RtpAppClient> RtpAppClientPtr;

}
