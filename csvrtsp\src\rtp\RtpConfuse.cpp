#include <sys/socket.h>
#include "RtpConfuse.h"
#include "AkLogging.h"
#include "Unp.h"

/*
生成混淆头,并且返回异或混淆key
@head 头
@len 值中参，入参是头的可接受长度，出参是头的实际长度
@confuse_key 混淆的key
*/
int RtpConfuse::CreateConfuseHead(char *head, int *len, char *confuse_key)
{
    if (!head && *len < 10) //head+random_num=2+max(7)
    {
        return -1;
    }
    time_t t;
    srand((unsigned) time(&t));

    char *tmp = head;
    *tmp =  (char)(rand() % 256);

    //type //这个不能让wireshark识别到profile
    *tmp |=(1<<5); //将X的第Y位置1
    *tmp &=~(1<<6);//将X的第Y位清0
    *tmp &=~(1<<7);//将X的第Y位清0

    *(tmp + 1) =  (char)(rand() % 256);
    
    char *byte = (char *)tmp;
    *confuse_key = *(tmp + 1);
    char random_num = (*byte & 0x1C) >> 2;
    
    *len = AK_RTP_CONFUSE_HEAD_LEN + random_num;

    char tmp_str[10];
    memcpy(head + AK_RTP_CONFUSE_HEAD_LEN, tmp_str, random_num);
    
    return 0;
}

/*
获取混淆信息
*/
void RtpConfuse::GetConfuseInfo(const unsigned char *src, char *type, char *random_num, char *confuse_key)
{
    if (!type || !random_num || !confuse_key)
    {
        return;
    }
    char *byte = (char *)src;
    *type = *byte & 0xE0 >> 5;
    *random_num = (*byte & 0x1C) >> 2;
    *confuse_key = *(byte + 1);  
}

/*执行异或混淆*/
void RtpConfuse::RtpOrConfuse(unsigned char *src, char confuse_key)
{
    if (!src)
    {
        return;
    }
    char real_key = confuse_key & 0xff;
    char tmp_src = *src & 0xff;
    *src = real_key ^ tmp_src; 
}

int RtpConfuse::DecRtpConfuse(unsigned char *src, int src_len, unsigned char *out, int *out_len)
{
    if (!src || !out || !out_len || src_len <= 0 )
    {
        return -1;
    }
    
    if (src_len < AK_RTP_CONFUSE_JUDGE_LESS_LEN)
    {
        memcpy(out, src, src_len);
        *out_len = src_len;
        return 0;
    }

    char type = 0;
    char random_num = 0;
    char confuse_key = 0;
    int skip_num = 0;
    
    GetConfuseInfo(src, &type, &random_num, &confuse_key);
    skip_num = AK_RTP_CONFUSE_HEAD_LEN + (int)random_num;
    
    RtpOrConfuse(src + skip_num, confuse_key);
    if (*out_len < src_len - skip_num)
    {
        return -2;
    }
    memcpy(out, src + skip_num, src_len - skip_num);
    *out_len = src_len - skip_num;
    
    return 0;
}

int RtpConfuse::EncRtpConfuse(const unsigned char *src, int src_len, unsigned char *out, int *out_len)
{
    if (!src || !out || !out_len || src_len <= 0)
    {
        return -1;
    }
    char head[20] = "";
    int head_len = 0;
    char confuse_key1 = 0;
    CreateConfuseHead(head, &head_len, &confuse_key1);
        
    if (*out_len < src_len + head_len)
    {
        return -2;
    }
    memcpy(out, head, head_len);
    memcpy(out + head_len, src, src_len);
    RtpOrConfuse(out  + head_len, confuse_key1);
    *out_len = src_len + head_len;

    return 0;
}


void RtpConfuse::Sendto(bool confuse_switch, int sockfd, const unsigned char *buf, size_t len, int flags,
           const struct sockaddr_storage *dest_addr, size_t addrlen)
{
    
    unsigned char data_convert[AK_RTP_MAX_LEN];
    memset(data_convert, 0, AK_RTP_MAX_LEN);
    int data_convert_len = AK_RTP_MAX_LEN;

    if(confuse_switch)
    {   
        RtpConfuse::EncRtpConfuse(buf, len, data_convert, &data_convert_len);                
    }
    else
    {
        memcpy(data_convert, buf, len);
        data_convert_len = len;
    }

    ::Sendto(sockfd, data_convert, data_convert_len, 0, (SA*)dest_addr, addrlen);
}

//部分rtcp发送用的小写sendto,和原本使用保持一致           
void RtpConfuse::sendto(bool confuse_switch, int sockfd, const unsigned char *buf, size_t len, int flags,
           const struct sockaddr_storage *dest_addr, size_t addrlen)
{

    unsigned char data_convert[AK_RTP_MAX_LEN];
    memset(data_convert, 0, AK_RTP_MAX_LEN);
    int data_convert_len = AK_RTP_MAX_LEN;

    if(confuse_switch)
    {   
       RtpConfuse::EncRtpConfuse(buf, len, data_convert, &data_convert_len);       
    }
    else
    {
       memcpy(data_convert, buf, len);
       data_convert_len = len;
    }

    
    ::sendto(sockfd, data_convert, data_convert_len, 0, (SA*)dest_addr, addrlen);
}


