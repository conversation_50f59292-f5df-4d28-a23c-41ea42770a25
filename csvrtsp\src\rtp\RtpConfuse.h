#ifndef __RTP_CONFUSE_H__
#define __RTP_CONFUSE_H__
#include<time.h>

#define AK_RTP_MAX_LEN 4096
#define AK_RTP_CONFUSE_HEAD_LEN 2
#define AK_RTP_CONFUSE_JUDGE_LESS_LEN 10 //最少10字节进入混淆

typedef struct {
  unsigned type:3; /* type */
  unsigned random_num:3;	/* random insert     */
  unsigned x:2;	/* Reserved bit  */
  unsigned confuse_key:8;	/* confuse  */
} ak_rtp_confuse_hdr_t;

class RtpConfuse
{
public:
    RtpConfuse() {};
    ~RtpConfuse() {};
    static void Sendto(bool confuse_switch, int sockfd, const unsigned char *buf, size_t len, int flags,
               const struct sockaddr_storage *dest_addr, size_t addrlen);
    static void sendto(bool confuse_switch, int sockfd, const unsigned char *buf, size_t len, int flags,
               const struct sockaddr_storage *dest_addr, size_t addrlen);
    static int DecRtpConfuse(unsigned char *src, int src_len, unsigned char *out, int *out_len);    

    static int CreateConfuseHead(char *head, int *len, char *confuse_key);
    static void GetConfuseInfo(const unsigned char *src, char *type, char *random_num, char *confuse_key);
    static void RtpOrConfuse(unsigned char *src, char confuse_key);
    static int EncRtpConfuse(const unsigned char *src, int src_len, unsigned char *out, int *out_len);
};

#endif
