#include <unistd.h>
#include <netinet/in.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <string.h>
#include <iostream>
#include <fstream>
#include <string>
#include <ctime>
#include "AKLog.h"
#include "string.h"
#include "ipc/ipc.h"
#include "AkLogging.h"
#include "ByteIO.h"
#include "NackModule.h"
#include "Unp.h"
#include "util.h"
#include "MetricService.h"
#include "RtpAppManager.h"
#include "RtpDeviceClient.h"
#include "RtpDeviceManager.h"
#include "RtpEpollThread.h"
#include "modules/rtp_rtcp/include/rtp_header_parser.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

#define D_CLIENT_VERSION_4400  4400

namespace akuvox
{
RtpDeviceClient::RtpDeviceClient(uint64_t trace_id, unsigned short local_rtp_port, const std::string& flow_uuid)
{
    local_rtp_port_ = local_rtp_port;
    local_rtcp_port_ = local_rtp_port + 1;
    // mac_ = mac;
    rtp_fd_ = -1;
    state_ = DEV_STATE_NONE;
    capture_ = false;
    adding_app_ = false;
    ssrc_ = 0;
    first_true_rtp_ssrc_ = 0;
    is_has_checked_ssrc_ = false;
    srand((unsigned int)GetCurrentMilliTimeStamp());
    local_ssrc_ = rand();
    last_rtp_packet_time_ = time(nullptr);

    rtcp_fd_ = -1;
    has_rctp_nat_ = false;
    has_rtp_nat = false;
    first_dev_rtp_arrive = false;
    dev_rtp_ssrc_ = 0;
    memset(&dev_rtp_addr, 0, sizeof(dev_rtp_addr));
    memset(&dev_rtcp_addr, 0, sizeof(dev_rtcp_addr));
    v_rtp_packets_.resize(RTP_PACKET_MAX_BUFFER_SIZE);
    v_third_rtp_packets_.resize(RTP_PACKET_THIRD_CACHE_BUFFER_SIZE);
    rtp_packets_index_ = 0;
    rtp_delay_packets_index_ = 0;
    third_rtp_packets_index_ = 0;
    nat_need_buffer_pkg_ = true;
    have_third_camera_ = 0;
    need_transfer_ = false;
    rtcp_module.reset(new AKModuleRtpRtcp(&dev_rtp_addr, &rtp_fd_));
    rtcp_receiver.reset(new AKRtcpReceiver(rtcp_module));
    rtcp_transport.reset(new AKRtcpTransport(&dev_rtcp_addr, &rtcp_fd_)); //发送nack
    rtcp_sender.reset(new AkRtcpSender(rtcp_transport.get()));
    rtcp_module->setRtcpSender(rtcp_sender);
    trace_id_ = trace_id;
    belong_to_one_thread_ = false;
    rtp_confuse_switch_ = false;
    flow_uuid_ = flow_uuid;
    is_monitor_ip_ = false;
}

RtpDeviceClient::~RtpDeviceClient()
{
    AK_LOG_INFO << "[" << trace_id_ << "] ~RtpDeviceClient(), rtp_fd = " << rtp_fd_ << ", rtcp_fd = " << rtcp_fd_;
    if (inner_vrtsp_rtp_fd_ > 0)
    {
        close(inner_vrtsp_rtp_fd_);
        inner_vrtsp_rtp_fd_ = 0;
    }

    // CreateRtpSocket成功才能remove fd
    if (state_ > DEV_STATE_NONE) 
    {
        RtpEpollThread::getInstance()->RemoveRtpSocket(rtp_fd_, rtcp_fd_, trace_id_);
        rtp_fd_ = -1;
        rtcp_fd_ = -1;
    }
}

std::string RtpDeviceClient::toString()
{
    char infos[256] = { 0 };
    snprintf(infos, sizeof(infos), "RtpDevClient[rtp fd=%d,local port=%hu,mac=%s,app count=%lu capture = %d]",
             rtp_fd_, local_rtp_port_, mac_.c_str(), rtp_app_clients_.size(), capture_);
    std::string info = infos;
    return info;
}

bool RtpDeviceClient::CreateRtpSocket()
{
    //modified by chenyc,2019-01-08,当并发时,会对同一台设备多次创建socket,句柄泄露.改m_nState为原子变量
    if (state_ > DEV_STATE_NONE) //平台接受设备rtp packet的网络socket已经准备好了
    {
        return true;
    }

    struct sockaddr_in6 address;
    bzero(&address, sizeof(address));
    address.sin6_family = AF_INET6;
    address.sin6_port = htons(local_rtp_port_);
    address.sin6_addr = in6addr_any;
    rtp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (-1 == rtp_fd_)
    {
        AK_LOG_WARN << "[" << trace_id_ << "] create dev rtp packet socket error = " << strerror(errno) << ", errno = " << errno;
        return false;
    }
  
    AK_LOG_INFO  << "[" << trace_id_ << "] create dev rtp packet socket = " << rtp_fd_ << ", local dev port = " << local_rtp_port_;

    int on = 1;
    int ret = setsockopt(rtp_fd_, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));
    if (-1 == ret)
    {        
        AK_LOG_WARN << "[" << trace_id_ << "] set reuseaddr socket error = " << strerror(errno) << ", errno = " << errno;
        close(rtp_fd_);
        return false;
    }
    ret = bind(rtp_fd_, (struct sockaddr*)&address, sizeof(address));
    if (-1 == ret)
    {        
        AK_LOG_WARN << "[" << trace_id_ << "] bind socket error = " << strerror(errno) << ", errno = " << errno;
        close(rtp_fd_);
        return false;
    }

    //create rtcp
    struct sockaddr_in6 address_rtcp;
    bzero(&address_rtcp, sizeof(address_rtcp));
    address_rtcp.sin6_family = AF_INET6;
    address_rtcp.sin6_port = htons(local_rtcp_port_);
    address_rtcp.sin6_addr = in6addr_any;
    rtcp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (-1 == rtcp_fd_)
    {
        AK_LOG_WARN << "[" << trace_id_ << "] create rtcp socket error = " << strerror(errno) << ", errno = " << errno;
        close(rtp_fd_);
        return false;
    }

    AK_LOG_INFO  << "[" << trace_id_ << "] create dev rtcp packet socket = " << rtcp_fd_ << ", local dev port = " << local_rtcp_port_;

    on = 1;
    ret = setsockopt(rtcp_fd_, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));
    if (-1 == ret)
    {
        AK_LOG_WARN << "[" << trace_id_ << "] set reuseaddr rtcp socket error = " << strerror(errno) << ", errno = " << errno;
        close(rtcp_fd_);
        close(rtp_fd_);
        return false;
    }

    ret = bind(rtcp_fd_, (struct sockaddr*)&address_rtcp, sizeof(address_rtcp));
    if (-1 == ret)
    {
        AK_LOG_WARN << "[" << trace_id_ << "] bind rtcp socket error = " << strerror(errno) << ", errno = " << errno;
        close(rtcp_fd_);
        close(rtp_fd_);
        return false;
    }

    inner_vrtsp_rtp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (inner_vrtsp_rtp_fd_ == -1)
    {
        AK_LOG_WARN << "[" << trace_id_ << "] socket error, failed to create inner vrtspd rtp fd, errno is " << errno;
        return false;
    }

    state_ = DEV_STATE_INIT;
    return true;
}

void RtpDeviceClient::AddMsg(unsigned char* data, unsigned int data_len)
{
    RtpPackage rtp_package;
    rtp_package.fd = rtp_fd_;
    rtp_package.data_len = data_len;
    rtp_package.create_time = std::chrono::steady_clock::now();

    memcpy(rtp_package.data, data, data_len);
    {
        std::lock_guard<std::mutex> lock(dev_rtp_package_list_lock_);
        dev_rtp_package_list_.push_back(rtp_package);
        if(dev_rtp_package_list_.size() > RTP_MSG_MAX)
        {
            dev_rtp_package_list_.pop_front();
            AK_LOG_WARN << "[" << trace_id_ << "] dev rtp socket = " << rtp_fd_ << " recv msg full, delete old rtp pkg";
        }
    }
}

bool RtpDeviceClient::HasMessage()
{
    std::thread::id tmp_tid = std::this_thread::get_id();
    if (tid_ == tmp_tid)//分配到该线程
    {
        return dev_rtp_package_list_.size() > 0;
    }
    else if (belong_to_one_thread_ == false) //tid_ == 0的情况下,证明还没有分配线程，需要各个线程争抢
    {
        std::lock_guard<std::mutex> lock(dev_rtp_package_list_lock_);
        // 防止多个线程第一次同时获取到RtpDeviceClient对象,进行ProcessMsg
        if (belong_to_one_thread_ == false)
        {
            tid_ = tmp_tid;
            belong_to_one_thread_ = true;
            return dev_rtp_package_list_.size() > 0;
        }
    }
    else
    {
       // AK_LOG_WARN << "thread dispatch error, tmp_tid = " << tmp_tid << ", tid_ = " << tid_;
    }

    //证明该设备的RTP处理已经被分配到其他线程处理了
    return false;
}

void RtpDeviceClient::RecordRtpPackageHandleLatency(RtpPackage& rtp_package)
{
    std::chrono::steady_clock::time_point package_handle_time = std::chrono::steady_clock::now();
    auto duration_all_handle = std::chrono::duration_cast<std::chrono::milliseconds>(package_handle_time - rtp_package.create_time);
        
    MetricService* metric_service = MetricService::GetInstance();
    if(metric_service) 
    {
        metric_service->AddLatencyLatencyValue("queue_handle_latency", duration_all_handle.count(), "RtpForward");
    }
}

void RtpDeviceClient::ProcessMsg()
{
    std::list<RtpPackage> tmp_rtp_package_list;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_package_list_lock_);
        tmp_rtp_package_list.swap(dev_rtp_package_list_);
    }

    //对应的app列表提到这里来处理,当每一次循环处理设备的rtp包时,才获取一次app列表,新增的只能等待下一个循环,以此提升每次循环的处理效率
    std::set<RtpAppClientPtr> tmp_rtp_app_clients;
    {
        std::lock_guard<std::mutex> lock(rtp_app_client_mutex_);
        tmp_rtp_app_clients = rtp_app_clients_;
    }

    //分发流给内部的vrtspd,需要修改ssrc
    VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_tmp;
    {
        std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
        vrtsp_logic_id_addrs_tmp = vrtsp_logic_id_addrs_;
    }

    for(auto &rtp_package : tmp_rtp_package_list)
    {
        // 记录rtp包处理延迟
        RecordRtpPackageHandleLatency(rtp_package);
        
        unsigned char *data = rtp_package.data;
        uint32_t data_len = rtp_package.data_len;
        //added by chenyc,20201118,1500适配rtp的长度,当前RtpPacketInfo结构体成员packet_data_的长度也是1500
        if (data_len >= 1500)
        {
            LOG_EVERY_N(WARNING, 20) << "[" << trace_id_ << "] get " << google::COUNTER << " times log, rtp data length > 1500. length is " << rtp_package.data_len;
            return;
        }
        
        if ((tmp_rtp_app_clients.size() == 0) && (vrtsp_logic_id_addrs_.size() == 0))
        {
            //added by chenyc,2019-12-06,每100次日志,记录一次即可,实际次数是COUNTER次
            LOG_EVERY_N(WARNING, 100) << "[" << trace_id_ << "] get " << google::COUNTER << " times log, there are not app rtsp client and inner rtspd client for dev rtp packet. mac is " << mac_;
            continue;
        }

        
        // 更新rtp packet的时间
        RecordPacketTime();

        // 如果该客户端需要截图,就发送相应的数据过去,设备的视频存储也是从这里获取数据
     	SendCaptureFlow(data, data_len);   
        
        //TODO:如果是集群内部转流。因为是阿里云局域网发送rtp包，先不处理丢包的情况。
        const uint8_t* ptr = &data[2];
        uint16_t seq_num = akcs::ByteReader<uint16_t>::ReadBigEndian(ptr);
        
        //add nack judge list
        OnReceivedPacket(seq_num);
        
        if (++rtp_packets_index_ >= RTP_PACKET_MAX_BUFFER_SIZE)
        {
            rtp_packets_index_ = 0;
        }

        v_rtp_packets_[rtp_packets_index_].seq_ = seq_num;
        v_rtp_packets_[rtp_packets_index_].data_len_ = data_len;
        memcpy(&v_rtp_packets_[rtp_packets_index_].packet_data_, data, data_len);

        //只在监控三方时才需要缓存，用于解决app nat未完成的情况下,第三方IPC的i帧已经发送完毕,进而导致I帧丢失问题，IPC的画面永远出不来
        ThirdCameraCaching(seq_num, data_len, data);

        // 发送给app
        for (auto &rtp_app_client : tmp_rtp_app_clients)
        {
            //通过服务器本端的m_nRtpFd,发给app的nat后的地址m_appAddr
            if (rtp_app_client->hasnat_)
            {
                //在有三方摄像头并且nat第一次完成的情况下发缓存的包
                if (have_third_camera_ && nat_need_buffer_pkg_)
                {
                    for (int i=0; i<third_rtp_packets_index_; i++)
                    {
                    
                        RtpConfuse::Sendto(rtp_app_client->GetRtpConfuseSwitch(), rtp_app_client->rtp_fd_, v_third_rtp_packets_[i].packet_data_, v_third_rtp_packets_[i].data_len_, 0, &rtp_app_client->app_addr_, sizeof(rtp_app_client->app_addr_));
                    }
                    nat_need_buffer_pkg_ = false;
                }
                                
                //发完第三方IPC缓存的rtp包,实时的rtp也发送过去
                RtpConfuse::Sendto(rtp_app_client->GetRtpConfuseSwitch(), rtp_app_client->rtp_fd_, data, data_len, 0, &rtp_app_client->app_addr_, sizeof(rtp_app_client->app_addr_));
            }
            else
            {
                // app未发nat包
                LOG_EVERY_N(WARNING, 50) << "[" << trace_id_ << "] app not send nat packet " << google::COUNTER << " times, send rtp packet to app failed, app local rtp port = " << rtp_app_client->local_rtp_port_;
            }
        }

        // 维护rtp包缓存队列，最大窗口为RTP_PACKET_DELAY_BUFFER_SIZE
        rtp_delay_packets_.push(v_rtp_packets_[rtp_packets_index_]);
        if (rtp_delay_packets_index_ < RTP_PACKET_DELAY_BUFFER_SIZE)
        {
            // 当前窗口大小增加
            rtp_delay_packets_index_++;
        }
        else
        {
            // 窗口向后移动
            rtp_delay_packets_.pop();
        }

        // 发送给内部转流集群
        ProcessInnerClusterForwarding(data, data_len, vrtsp_logic_id_addrs_tmp);
    }
}

std::string RtpDeviceClient::GetInnerRtspClientList()
{
    std::string inner_rtsp_client_list = "inner: \n";
    VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_tmp;
    {
        std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
        vrtsp_logic_id_addrs_tmp = vrtsp_logic_id_addrs_;
    }

    for (auto vrtsp_logic_id_addr : vrtsp_logic_id_addrs_tmp)
    {
        struct sockaddr_in *s = (struct sockaddr_in *)(&vrtsp_logic_id_addr.second.addr);
        char ip_str[1000];
        int port = 0;
        inet_ntop(AF_INET, &s->sin_addr, ip_str, sizeof(ip_str));
        port = ntohs(s->sin_port);
        inner_rtsp_client_list += "ip:";
        inner_rtsp_client_list += ip_str;
        inner_rtsp_client_list += "\n";
        inner_rtsp_client_list += "port:";
        inner_rtsp_client_list += std::to_string(port);
        inner_rtsp_client_list += "\n";
    }

    return inner_rtsp_client_list;
}

//获取监控此设备的APP列表，add by czw
std::string RtpDeviceClient::GetAppClientList()
{
    std::string app_client_list = "APP: ";
    std::set<RtpAppClientPtr> tmp_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(rtp_app_client_mutex_);
        tmp_rtp_clients = rtp_app_clients_;
    }

    for (auto rtp_client : tmp_rtp_clients)
    {
        //通过服务器本端的m_nRtpFd,发给app的nat后的地址m_appAddr
        std::string app_client_addr = Sock_ntop((SA*)&rtp_client->app_addr_, sizeof(rtp_client->app_addr_));
        app_client_list += app_client_addr.c_str();
        app_client_list += "\n";
    }
    return app_client_list;
}

int RtpDeviceClient::MonotorAppNum()
{
    return rtp_app_clients_.size();
}
bool RtpDeviceClient::IsAddingAppStatus()
{
    return adding_app_ == true;
}
void RtpDeviceClient::SetAddingAppStatus()
{
    adding_app_ = true;
}
void RtpDeviceClient::ResetAddingAppStatus()
{
    adding_app_ = false;
}
void RtpDeviceClient::AddMonitorApp(const RtpAppClientPtr& rtp_client)
{
    std::lock_guard<std::mutex> lock(rtp_app_client_mutex_);
    rtp_app_clients_.insert(rtp_client);
}

bool RtpDeviceClient::FindAndRemoveApp(const RtpAppClientPtr& rtp_app_client)
{
    std::lock_guard<std::mutex> lock(rtp_app_client_mutex_);
    auto iter =  rtp_app_clients_.find(rtp_app_client);
    if (iter != rtp_app_clients_.end())
    {
        rtp_app_clients_.erase(iter);
        AK_LOG_INFO << "[" << trace_id_ << "] " << toString() <<  " remove rtp app client port = " << rtp_app_client->getRtpPort();
        return true;
    }
    
    return false;
}

void RtpDeviceClient::GetAllApp(std::set<RtpAppClientPtr>& clients)
{
    std::lock_guard<std::mutex> lock(rtp_app_client_mutex_);
    clients = rtp_app_clients_;
}

void RtpDeviceClient::GetAllApp(std::set<uint16_t>& clients_rtp_port)
{
    std::set<RtpAppClientPtr> tmp_rtp_app_clients_;
    {
        std::lock_guard<std::mutex> lock(rtp_app_client_mutex_);
        tmp_rtp_app_clients_ = rtp_app_clients_;
    }
    for(const auto &rtp_app_client : tmp_rtp_app_clients_)
    {
        clients_rtp_port.insert(rtp_app_client->getRtpPort());
    }
}

void RtpDeviceClient::AddInnerClient(const std::string& mac, const std::string& ip,
                                     const int32_t port, const std::string& vrtspd_logic_id, const int32_t ssrc_req)
{
    struct sockaddr_storage addr;
    ::memset(&addr, 0, sizeof(struct sockaddr_storage));
    struct sockaddr_in* addr_v4 = (struct sockaddr_in*)&addr;
    addr_v4->sin_family = AF_INET;
    addr_v4->sin_port = htons(static_cast<uint16_t>(port));
    if (::inet_aton(ip.c_str(), &(addr_v4->sin_addr)) == 0)
    {
        AK_LOG_WARN << "[" << trace_id_ << "] Invalid address: " << ip;
        return;
    }

    struct sockaddr_storage rtcp_addr;
    ::memset(&rtcp_addr, 0, sizeof(struct sockaddr_storage));
    struct sockaddr_in* rtcp_addr_v4 = (struct sockaddr_in*)&rtcp_addr;
    rtcp_addr_v4->sin_family = AF_INET;
    rtcp_addr_v4->sin_port = htons(static_cast<uint16_t>(port + 1));
    if (::inet_aton(ip.c_str(), &(rtcp_addr_v4->sin_addr)) == 0)
    {
        AK_LOG_WARN << "[" << trace_id_ << "]  Invalid address: " << ip;
        return;
    }

    struct RtspInnerCli rtsp_inner_cli;
    ::memset(&rtsp_inner_cli, 0, sizeof(struct RtspInnerCli));
    rtsp_inner_cli.addr = addr;
    rtsp_inner_cli.rtcp_addr = rtcp_addr;
    rtsp_inner_cli.ssrc_req = ssrc_req;
    rtsp_inner_cli.keepalive_time = 0;
    rtsp_inner_cli.have_unsent_rtp_delay_packets_ = true;
    {
        std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
        vrtsp_logic_id_addrs_[vrtspd_logic_id] = rtsp_inner_cli;
    }
    AK_LOG_INFO << "Add inner client success. logic id=" << vrtspd_logic_id << " ip=" << ip << " port=" << port;
}
bool RtpDeviceClient::FindAndRemoveInnerClient(const std::string& vrtspd_logic_id, const std::string& mac)
{
    if (mac != mac_)
    {
        return false;
    }
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    auto iter = vrtsp_logic_id_addrs_.find(vrtspd_logic_id);
    if (iter != vrtsp_logic_id_addrs_.end())
    {
        vrtsp_logic_id_addrs_.erase(iter);        
        AK_LOG_INFO << "[" << trace_id_ << "] " << toString() <<  " remove inner rtsp client, logic id = " << vrtspd_logic_id;
        return true;
    }

    return false;
}
void RtpDeviceClient::KeepAliveRtspInnerClient(const std::string& vrtsp_logic_id,   const std::string& flow_uuid)
{
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    auto iter = vrtsp_logic_id_addrs_.find(vrtsp_logic_id);
    if (iter != vrtsp_logic_id_addrs_.end())
    {
        iter->second.keepalive_time = 0;
        AK_LOG_INFO << "[" << trace_id_ << "] rtsp inner client keepalive, flow_uuid = " << flow_uuid << ", vrtsp client id  = " << iter->first;
    }
}
bool RtpDeviceClient::CheckRtspInnerClient(int timer_step)
{
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    for (auto iter = vrtsp_logic_id_addrs_.begin(); iter != vrtsp_logic_id_addrs_.end();)
    {
        if (iter->second.keepalive_time >= 60)
        {
            AK_LOG_INFO << "[" << trace_id_ << "] rtsp inner client overtime, mac = " << mac_ << ", vrtsp client id  = " << iter->first;
            vrtsp_logic_id_addrs_.erase(iter++);
            continue;
        }
        iter->second.keepalive_time += timer_step;
        iter++;
    }
    
    //检查下是否已经没有app和内部的边缘转流服务器了
    if ((MonotorAppNum() == 0) && !IsAddingAppStatus() && (RtspInnerClientNum() == 0))
    {
        return true;
    }
    else
    {
        return false;
    }

}
int RtpDeviceClient::RtspInnerClientNum()
{
    return vrtsp_logic_id_addrs_.size();
}

void RtpDeviceClient::SetDclientVer(int dclient_ver)
{
    dclient_ver_ = dclient_ver;
}

void RtpDeviceClient::SetSrtpKey(const std::string& srtp_key)
{
    srtp_key_ = srtp_key;
}

void RtpDeviceClient::SetSsrc(int32_t ssrc)
{
    ssrc_ = ssrc;
}

void RtpDeviceClient::SetTraceID(uint64_t trace_id)
{
    trace_id_ = trace_id;
}

void RtpDeviceClient::SetTransferDoorUUID(const std::string& transfer_door_uuid)
{
    transfer_door_uuid_ = transfer_door_uuid;

}

void RtpDeviceClient::SetTransferIndoorMac(const std::string& transfer_indoor_mac)
{
    transfer_indoor_mac_ = transfer_indoor_mac;
}

bool RtpDeviceClient::ParseRtpHeader(struct sockaddr_storage& dev_addr, uint8_t* rtp_data, uint32_t rtp_data_len)
{
    if (rtp_data_len < kRtpMinParseLength)
    {
        return false;
    }
    // Version
    const uint8_t V = rtp_data[0] >> 6;
    if (V != kRtpExpectedVersion)
    {
        return false;
    }
    //rtcp处理
    if (webrtc::RtpHeaderParser::IsRtcp(rtp_data, rtp_data_len))
    {
        const uint8_t* ptr = &rtp_data[4];
        uint32_t recv_ssrc = akcs::ByteReader<uint32_t>::ReadBigEndian(ptr);
        if (!has_rctp_nat_) //是否已经完成app的udp-nat工作
        {
            has_rctp_nat_ = true;
            rtcp_receiver->SetRemoteSsrc(recv_ssrc);
            rtcp_sender->SetRemoteSSRC(recv_ssrc);
            rtcp_sender->SetSSRC(local_ssrc_);
        }
        else
        {
            rtcp_receiver->rtcp_receiver_->IncomingPacket(rtp_data, rtp_data_len);
            rtcp_sender->InsertIncomingPacket(recv_ssrc, 123); //设备发送一个平台就会回复一个

            int64_t last_rtt_ms = 0;
            int64_t avg_rtt_ms = 0;
            rtcp_receiver->rtcp_receiver_->RTT(recv_ssrc, &last_rtt_ms, &avg_rtt_ms, nullptr, nullptr);
            UpdateRtt(avg_rtt_ms);
        }
        //分发流给内部的vrtspd,需要修改ssrc
        VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_tmp;
        {
            std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
            vrtsp_logic_id_addrs_tmp = vrtsp_logic_id_addrs_;
        }
        for (auto& vrtsp_logic_id_addr : vrtsp_logic_id_addrs_tmp)
        {
            unsigned char* ptr = &rtp_data[4];
            akcs::ByteWriter<uint32_t>::WriteBigEndian(ptr, vrtsp_logic_id_addr.second.ssrc_req);
            RtpConfuse::Sendto(rtp_confuse_switch_, inner_vrtsp_rtp_fd_, rtp_data, rtp_data_len, 0, &(vrtsp_logic_id_addr.second.rtcp_addr), sizeof(vrtsp_logic_id_addr.second.rtcp_addr));
        }

        //rtcp在上面已经处理了,调用处不用再往下了,故直接return false
        return false;
    }

    const uint8_t* ptr = &rtp_data[8];
    uint32_t recv_ssrc = akcs::ByteReader<uint32_t>::ReadBigEndian(ptr);
    //判断SSRC是否正确
    if (dclient_ver_ >= D_CLIENT_VERSION_4400)
    {
        if (!first_dev_rtp_arrive)
        {
            std::set<uint32_t> reg_ssrcs = {local_ssrc_, (uint32_t)ssrc_};
            rtcp_receiver->rtcp_receiver_->SetSsrcs(local_ssrc_, reg_ssrcs);
            first_dev_rtp_arrive = true;
            dev_rtp_ssrc_ = recv_ssrc;
        }
        if ((uint32_t)ssrc_ != recv_ssrc)
        {
            LOG_EVERY_N(WARNING, 100) << "[" << trace_id_ <<  "] mac:" << mac_ <<  " recv rtp sscr:" << recv_ssrc << ",but server required ssrc:" << ssrc_;
            return false;
        }
        return true;
    }
    else//旧版本的设备
    {
        if (first_true_rtp_ssrc_ == 0)
        {
            first_true_rtp_ssrc_ = recv_ssrc;
            dev_rtp_ssrc_ = recv_ssrc;
            std::set<uint32_t> reg_ssrcs = {local_ssrc_, first_true_rtp_ssrc_};
            rtcp_receiver->rtcp_receiver_->SetSsrcs(local_ssrc_, reg_ssrcs);
            first_dev_rtp_arrive = true;
            return true;
        }
        else if (first_true_rtp_ssrc_ == recv_ssrc)
        {
            return true;
        }
        else if ((first_true_rtp_ssrc_ != recv_ssrc) && (is_has_checked_ssrc_)) //证明两次的rtp包发生切换了,那么只能根据dev_addr_地址与设备的外网地址是否一致来保证合法性了.
        {
            AK_LOG_WARN << "[" << trace_id_ << "] error, required rtp packet should from "  << outer_ip_;
            return false;
        }
        else // rtp_ssrc_ != ssrc 且 b_checked_ssrc==false,那么就进入设备外网ip校验的流程
        {
            ResidentDev per_dev;
            memset(&per_dev, 0, sizeof(per_dev));
            ResidentDev dev;
            memset(&dev, 0, sizeof(dev));
            if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac_, per_dev))
            {
                outer_ip_ = per_dev.outer_ip;
            }
            else if (0 == dbinterface::ResidentDevices::GetMacDev(mac_, dev))
            {
                outer_ip_ = dev.outer_ip;
            }

            //获取到设备的外网ip
            //检查rtp包的来源ip与外网ip是否一致
            char* p = nullptr;
            std::string rtp_outer_ip;
            p = Sock_ntop_ip((SA*)&dev_addr, sizeof(dev_addr));
            if (p)
            {
                rtp_outer_ip = p;
            }
            if (rtp_outer_ip != outer_ip_)
            {
                AK_LOG_WARN << "[" << trace_id_ << "]error, rtp packet from "  << rtp_outer_ip << ", but required rtp packet from " << outer_ip_;
                return false;
            }
            else
            {
                first_true_rtp_ssrc_ = recv_ssrc;//赋值正确的ssrc
                is_has_checked_ssrc_ = true;
                return true;
            }
        }
        return true;
    }
}
//重写CNackModuleManager::SendNack
void RtpDeviceClient::SendNack(const std::vector<uint16_t>& sequence_numbers)
{
    //rtcp nat 未穿透
    if (!has_rctp_nat_)
    {
        return;
    }
    uint16_t* kList = new uint16_t[sequence_numbers.size()];
    if (!sequence_numbers.empty())
    {
        memcpy(kList, &sequence_numbers[0], sequence_numbers.size()*sizeof(uint16_t));
    }
    rtcp_sender->rtcp_sender_->SetRTCPStatus(webrtc::RtcpMode::kReducedSize);
    rtcp_sender->rtcp_sender_->SendRTCP(rtcp_sender->feedback_state(), webrtc::kRtcpNack, sequence_numbers.size(), kList);
    delete []kList;
}

void RtpDeviceClient::PassthruRemb(uint32_t bitrate)
{
    //rtcp nat 未穿透
    if (!has_rctp_nat_)
    {
        return;
    }  
    std::vector<uint32_t> remb_ssrc;
    remb_ssrc.push_back(local_ssrc_);
    rtcp_sender->rtcp_sender_->SetRTCPStatus(webrtc::RtcpMode::kReducedSize);
    rtcp_sender->rtcp_sender_->SetRemb(bitrate, remb_ssrc);
    rtcp_sender->rtcp_sender_->SendRTCP(rtcp_sender->feedback_state(), webrtc::kRtcpRemb);
}

void RtpDeviceClient::GetAppNackRtpPacket(const std::vector<uint16_t>& sequence_numbers, std::vector<RtpPacketInfo>& packets)
{
    for (auto it : sequence_numbers)
    {
        for (size_t i = 0; i < RTP_PACKET_MAX_BUFFER_SIZE; i++)
        {
            if (v_rtp_packets_[i].seq_ == it)
            {
                packets.push_back(v_rtp_packets_[i]);
            }
        }
    }
}

bool RtpDeviceClient::AlreadyGenerateSSRC()
{
    return ssrc_ > 0;
}

std::time_t RtpDeviceClient::GetLastRtpPacketTime()
{
    return last_rtp_packet_time_;
}

void RtpDeviceClient::SendCaptureFlow(unsigned char* data, unsigned int data_len)
{
    if (capture_)
    {
        SOCKET_MSG_VIDEO_DATA packet_data;
        memset(&packet_data, 0, sizeof(SOCKET_MSG_VIDEO_DATA));
        packet_data.data_len = data_len;
        Snprintf(packet_data.szPicName, sizeof(packet_data.szPicName),  pic_name_);
        Snprintf(packet_data.mac, sizeof(packet_data.mac),  mac_.c_str());
        Snprintf(packet_data.flow_uuid, sizeof(packet_data.flow_uuid), flow_uuid_.c_str());
        memcpy(packet_data.szData, data, data_len);
        ipc_send(IPC_ID_VRECORD, MSG_VRTSP2VRECORD_SEND_CAPTURE_DATA, 0, 0, &packet_data, sizeof(SOCKET_MSG_VIDEO_DATA));
    }
}

void RtpDeviceClient::RecordPacketTime()
{
    last_rtp_packet_time_ = time(nullptr);
}

void RtpDeviceClient::ThirdCameraCaching(uint16_t seq_num, unsigned int data_len, unsigned char *data)
{
    if (have_third_camera_)
    {
        if (third_rtp_packets_index_ < RTP_PACKET_THIRD_CACHE_BUFFER_SIZE && nat_need_buffer_pkg_)
        {
            v_third_rtp_packets_[third_rtp_packets_index_].seq_ = seq_num;
            v_third_rtp_packets_[third_rtp_packets_index_].data_len_ = data_len;
            memcpy(&v_third_rtp_packets_[third_rtp_packets_index_].packet_data_, data, data_len);
            third_rtp_packets_index_++;
        }
    } 
}

void RtpDeviceClient::SetRtpConfuseSwitch(bool rtp_confuse_switch)
{
    AK_LOG_INFO << "[" << trace_id_ << "] dev rtp confuse switch: " << rtp_confuse_switch;
    rtp_confuse_switch_ = rtp_confuse_switch; 
    rtcp_transport->rtcp_confuse_switch_ = rtp_confuse_switch;
}

void RtpDeviceClient::ProcessInnerClusterForwarding(unsigned char* data, unsigned int data_len, 
                                                    const VrtspdLogicIDNetAddr& vrtsp_logic_id_addrs_tmp)
{
    // 存储需要更新的转流集群logic id
    std::vector<std::string> processed_clients;

    for (const auto& vrtsp_logic_id_addr : vrtsp_logic_id_addrs_tmp)
    {
        bool need_send_delay_packets = IsNeedSendDelayPackets(vrtsp_logic_id_addr.first);

        if (need_send_delay_packets)
        {
            // 发送缓存中的包
            SendDelayPackets(vrtsp_logic_id_addr.second);
            processed_clients.push_back(vrtsp_logic_id_addr.first);
        }

        // 发送当前的包
        unsigned char* ptr = &data[8];
        akcs::ByteWriter<uint32_t>::WriteBigEndian(ptr, vrtsp_logic_id_addr.second.ssrc_req);
        RtpConfuse::Sendto(rtp_confuse_switch_, inner_vrtsp_rtp_fd_, data, data_len, 0, &(vrtsp_logic_id_addr.second.addr), sizeof(vrtsp_logic_id_addr.second.addr));
    }

    UpdateInnerClusterClientFlag(processed_clients);
}

// 监控三方摄像头，对于还未发送缓存包的边缘节点，并且缓存包数量足够的，需要发送
bool RtpDeviceClient::IsNeedSendDelayPackets(const std::string& client_id)
{
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    auto iter = vrtsp_logic_id_addrs_.find(client_id);
    if (iter == vrtsp_logic_id_addrs_.end())
    {
        return false;
    }
    return (iter->second.have_unsent_rtp_delay_packets_ && 
            rtp_delay_packets_index_ >= RTP_PACKET_DELAY_BUFFER_SIZE &&
            have_third_camera_);
}

void RtpDeviceClient::SendDelayPackets(const RtspInnerCli& vrtsp_logic_id_addr)
{
    auto tmp_delay_queue = rtp_delay_packets_;

    while (!tmp_delay_queue.empty())
    {
        auto info = tmp_delay_queue.top();
        unsigned char* ptr = &info.packet_data_[8];
        akcs::ByteWriter<uint32_t>::WriteBigEndian(ptr, vrtsp_logic_id_addr.ssrc_req);
        tmp_delay_queue.pop();
        RtpConfuse::Sendto(rtp_confuse_switch_, inner_vrtsp_rtp_fd_, info.packet_data_, 
                        info.data_len_, 0, &(vrtsp_logic_id_addr.addr), 
                        sizeof(vrtsp_logic_id_addr.addr));
    }

}

void RtpDeviceClient::UpdateInnerClusterClientFlag(const std::vector<std::string>& client_ids)
{
    if (client_ids.empty())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    for (const auto& id : client_ids)
    {
        auto iter = vrtsp_logic_id_addrs_.find(id);
        if (iter != vrtsp_logic_id_addrs_.end())
        {
            iter->second.have_unsent_rtp_delay_packets_ = false;
        }
    }
}

}
