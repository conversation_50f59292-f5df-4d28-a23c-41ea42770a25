#pragma once
#include <string>
#include <map>
#include <set>
#include <mutex>
#include <atomic>
#include <chrono>
#include <netinet/in.h>
#include "Unp.h"
#include "NackModule.h"
#include "RtcpSender.h"
#include "RtcpReceiver.h"
#include <queue>
#include "VrtspDefine.h"
#include "RtpAppClient.h"
#include "RtpConfuse.h"
#include "RtpEpollThread.h"

enum
{
    DEV_STATE_NONE = 0,
    DEV_STATE_INIT,
    DEV_STATE_PLAY,
    DEV_STATE_MAX
};

#define RTP_MSG_MAX         100     //队列最大数100
#define RTP_MSG_WARN_COUNT  70      //高水位告警
#define PIC_NAME_SIZE       256

namespace akuvox
{
class AKRtcpReceiver;
class AKModuleRtpRtcp;

struct RtpPackage
{
    int fd;
    unsigned int data_len;
    unsigned char data[RTP_BUFFER_SIZE];
    std::chrono::steady_clock::time_point create_time;

    RtpPackage() {
        memset(this, 0, sizeof(*this));
    }
};

struct RtspInnerCli
{
    struct sockaddr_storage addr;//内部分流服务器的网络地址
    struct sockaddr_storage rtcp_addr;//内部分流服务器的网络地址
    int32_t ssrc_req;//内部分流服务器需求的ssrc
    int32_t keepalive_time;//内部分流服务器(边缘)keepalive
    bool have_unsent_rtp_delay_packets_;//是否存在未发送的延迟的rtp包
};

enum
{
    //rtcp
    kRtcpExpectedVersion = 2,
    kRtcpMinHeaderLength = 4,
    kRtcpMinParseLength = 8,
    //rtp
    kRtpExpectedVersion = 2,
    kRtpMinParseLength = 12
};

class RtpDeviceClient : public CNackModuleManager
{
public:
    typedef std::map<std::string/* 内部分流服务器逻辑id*/, struct RtspInnerCli/*内部分流服务器的信息*/> VrtspdLogicIDNetAddr;
public:
    /*
    * local_rtp_port:服务器用于接收dev端rtp数据的端口
    * mac:设备端的mac
    */
    RtpDeviceClient(uint64_t trace_id, unsigned short local_rtp_port, const std::string& flow_uuid);
    ~RtpDeviceClient();

    bool CreateRtpSocket();
    void AddMsg(unsigned char* data, unsigned int data_len);
    void ProcessMsg();
    std::string GetAppClientList();
    std::string GetInnerRtspClientList();
    bool HasMessage();
    int MonotorAppNum();
    std::string toString();
    bool IsAddingAppStatus();
    void SetAddingAppStatus();
    void ResetAddingAppStatus();
    void AddMonitorApp(const RtpAppClientPtr& rtp_client);
    bool FindAndRemoveApp(const RtpAppClientPtr& rtp_app_client);
    void GetAllApp(std::set<RtpAppClientPtr>& clients);
    void GetAllApp(std::set<uint16_t>& clients_rtp_port);
    void AddInnerClient(const std::string& mac, const std::string& ip,
                        const int32_t port, const std::string& vrtspd_logic_id, const int32_t ssrc_req);
    bool FindAndRemoveInnerClient(const std::string& vrtspd_logic_id, const std::string& mac);
    int RtspInnerClientNum();
    void KeepAliveRtspInnerClient(const std::string& vrtsp_logic_id,    const std::string& flow_uuid);
    bool CheckRtspInnerClient(int timer_step);
    void SetDclientVer(int dclient_ver);
    void SetSsrc(int32_t ssrc);
    void SetSrtpKey(const std::string& srtp_key);
    void SetTraceID(uint64_t trace_id);
    void SetTransferDoorUUID(const std::string& transfer_door_uuid);
    void SetTransferIndoorMac(const std::string& transfer_indoor_mac);
    bool ParseRtpHeader(struct sockaddr_storage& dev_addr, uint8_t* rtp_data, uint32_t rtp_data_len);

    void SendNack(const std::vector<uint16_t>& sequence_numbers) override;
    void PassthruRemb(uint32_t bitrate);
    void GetAppNackRtpPacket(const std::vector<uint16_t>& sequence_numbers, std::vector<RtpPacketInfo>& packets);
    std::time_t GetLastRtpPacketTime();
    bool AlreadyGenerateSSRC();
    void RecordRtpPackageHandleLatency(RtpPackage& rtp_package);

    void UpdateRtt(int64_t rtt_ms)
    {
        nack_module_.UpdateRtt(rtt_ms);
    }

    int OnReceivedPacket(uint16_t seq_num)
    {
        nack_module_.OnReceivedPacket(seq_num, false, false);
        if (nack_module_.TimeUntilNextProcess() <= 0)
        {
            nack_module_.Process();
        }
		return 0;
    }
    
    bool GetRtpConfuseSwitch()
    {
        return rtp_confuse_switch_;
    }
    
    void SetRtpConfuseSwitch(bool rtp_confuse_switch);

    void SetClientIsMonitorIP()
    {
        is_monitor_ip_ =true;
    }
    
    bool IsClientIsMonitorIP()
    {
        return is_monitor_ip_;
    }

    
private:
    // 处理内部转流集群包转发
    void ProcessInnerClusterForwarding(unsigned char* data, unsigned int data_len, 
                                        const VrtspdLogicIDNetAddr& vrtsp_logic_id_addrs_tmp);

    bool IsNeedSendDelayPackets(const std::string& client_id);

    void SendDelayPackets(const RtspInnerCli& vrtsp_logic_id_addr);

    void UpdateInnerClusterClientFlag(const std::vector<std::string>& client_ids);

    // 将流打往cscapture进行截图
    void SendCaptureFlow(unsigned char* data, unsigned int data_len);

    // 记录设备发流时间
    void RecordPacketTime();

    // 缓存三方摄像头包
    void ThirdCameraCaching(uint16_t seq_num, unsigned int data_len, unsigned char *data);
public:
    int rtp_fd_;
    int rtcp_fd_;
    unsigned short local_rtcp_port_;        //用于接收R2X rtcp端口
    unsigned short local_rtp_port_;     //用于接收R2X rtp媒体包的服务器rtp端口
    std::string mac_;                  //有三方摄像头时为uuid,没有三方摄像头时为设备mac
    std::string dev_mac_;              //绑定三方摄像头的设备mac或者被监控设备的mac地址
    int have_third_camera_;            //是否有三方摄像头的标识
    std::atomic<int> state_;
    bool capture_;
    char pic_name_[PIC_NAME_SIZE];
    int dclient_ver_;
    int32_t ssrc_;//平台生成的ssrc值
    struct sockaddr_storage dev_addr_;
    bool need_transfer_;               //是否需要转流
    std::string transfer_door_uuid_;  //西班牙转流为门口机的sip, hager转流为为门口机的mac
    std::string transfer_indoor_mac_; //转流室内机的mac
    std::string srtp_key_; // srtp key
    uint64_t trace_id_;
private:
    std::mutex rtp_app_client_mutex_;
    std::set<RtpAppClientPtr> rtp_app_clients_; //设备关联的client列表(client可能是APP也可能是室内机)
    std::mutex dev_rtp_package_list_lock_;
    std::list<RtpPackage> dev_rtp_package_list_;//云上recv的设备媒体rtp包列表
    
    std::atomic<bool> adding_app_;
    //vrtspd内部分流集群
    std::mutex vrtsp_logic_id_mutex_;
    VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_;
    int inner_vrtsp_rtp_fd_;
    uint32_t first_true_rtp_ssrc_;
    bool is_has_checked_ssrc_;//已经校验好ssrc了
    std::string outer_ip_;//设备的外网ip

public:
    bool has_rctp_nat_;
    bool has_rtp_nat;
    struct sockaddr_storage dev_rtcp_addr;
    struct sockaddr_storage dev_rtp_addr;

    std::shared_ptr<AKRtcpTransport> rtcp_transport;
    std::shared_ptr<AkRtcpSender> rtcp_sender;
    std::shared_ptr<AKRtcpReceiver> rtcp_receiver;
    std::shared_ptr<AKModuleRtpRtcp> rtcp_module;

    uint32_t dev_rtp_ssrc_;//设备流的ssrc
    uint32_t local_ssrc_;//发给设备流的ssrc，TODO:app 过来流的ssrc
    bool first_dev_rtp_arrive;

    int rtp_packets_index_;
    std::vector<RtpPacketInfo> v_rtp_packets_;

    // 三方摄像头缓存
    int third_rtp_packets_index_;
    std::vector<RtpPacketInfo> v_third_rtp_packets_;

    // 第三方摄像头的场景,nat穿透未完成缓存rtp包的标识，默认1，穿透完成后置为0，不再缓存
    bool nat_need_buffer_pkg_;

    // 内部转流缓存
    int rtp_delay_packets_index_;
    std::priority_queue<RtpPacketInfo> rtp_delay_packets_;
    
    std::time_t last_rtp_packet_time_;// 记录设备发流的时间
    uint8_t channel_id_;
    uint8_t stream_id_;
    std::string flow_uuid_;
    
private:
    std::thread::id tid_;//处理线程对应的id
    std::atomic<bool> belong_to_one_thread_;
    bool rtp_confuse_switch_;

    bool is_monitor_ip_;
};
}
