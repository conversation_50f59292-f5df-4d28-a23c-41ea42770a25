#pragma once
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include "RtpDeviceClient.h"

#define LOCAL_DEVICE_RTP_PORT_BASE  60000
#define LOCAL_DEVICE_RTP_PORT_MAX   65000

//如果时间太短直接关闭rtsp连接,这样客户会蒙掉  还没有反应过来就断开退出了;关闭连接是为了下一次监控正常可用
#define DEVICE_STOP_RTP_FLOW_MAX_TIME  10

namespace akuvox
{
typedef std::shared_ptr<RtpDeviceClient> RtpDevClientPtr;
typedef std::list<RtpDevClientPtr> RtpDevClientPtrList;



class RtpDeviceManager
{
public:
    typedef std::map<unsigned short/*dev rtp port*/, std::shared_ptr<RtpDeviceClient>> DevRtpMap;
public:
    ~RtpDeviceManager();
    static RtpDeviceManager* getInstance();

    std::shared_ptr<RtpDeviceClient> AddClient(uint64_t trace_id, const std::string& mac);
    void RemoveClient(unsigned short local_port);
    void ClearClient();
    std::shared_ptr<RtpDeviceClient> GetClientAndRemove(unsigned short dev_local_port);
    std::shared_ptr<RtpDeviceClient> GetClientByRtpPort(unsigned short local_port);
    std::shared_ptr<RtpDeviceClient> GetClientByMac(const std::string& mac);
    std::shared_ptr<RtpDeviceClient> GetClientByFlowUUID(const std::string& flow_uuid);
    std::shared_ptr<RtpDeviceClient> GetClientBySocket(int socket);
    std::shared_ptr<RtpDeviceClient> GetClientByRtcpSocket(int socket);
    void GetHasMsgDevClient(RtpDevClientPtrList &rtp_dev_client_list);

    //4.6运维接口新增获取监控点列表，此处遍历查询，add by czw
    std::string GetMonitorList();
    std::string GetMonitorListByMac(const std::string& mac);
    //获取内部转流的对象信息
    std::string GetInnerRtspClientList();

    void DeviceAddApp(unsigned short nDevRtpPort, const std::shared_ptr<RtpAppClient>& rtp_client);
    void DeviceRemoveApp(const RtpAppClientPtr& rtp_app_client, RtpDevClientPtr& rtp_dev_client_to_del);
    void AddMsg(int fd, struct sockaddr_storage& dev_addr, unsigned char* data, unsigned int data_len);
    void ReportAll();
    void SendHeartBeatForList();
    //当csroute转发csvrtsp集群内,其他服务停止监控的时候调用.
    void DeviceRemoveInnerClient(const std::string& vrtsp_logic_id, unsigned short& vec_dev_port, const std::string& flow_uuid);
    void DeviceKeepAliveInnerClient(const std::string& vrtsp_logic_id,  const std::string& flow_uuid);
    void CheckInnerVrtspKeepAlive(int timer_step);

    // 检测设备rtp packet的最后发送时间, 超过5s未发包删除
    void CheckDevRtpFlowStop();
private:
    RtpDeviceManager();
    unsigned short GenLocalPort();

private:
    static RtpDeviceManager* instance;
    const char* tag_;
    std::mutex dev_rtp_clients_mutex;
    std::map<unsigned short/*dev rtp port*/, std::shared_ptr<RtpDeviceClient>> dev_rtp_clients_;  //key==本端接收设备rtp流的端口
    unsigned short cur_local_port_;
};
}
