#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "RtpEpollThread.h"
#include "RtpAppManager.h"
#include "RtpDeviceManager.h"
#include "RtpAppClient.h"
#include "modules/video_coding/nack_module.h"
#include "ByteIO.h"
#include "AkLogging.h"
#include "CsvrtspConf.h"
#include "Network/sockutil.h"

extern CSVRTSP_CONF gstCSVRTSPConf;

namespace akuvox
{

RtpEpollThread* RtpEpollThread::instance = nullptr;

RtpEpollThread* RtpEpollThread::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtpEpollThread;
    }
    return instance;
}

void RtpEpollThread::ReleaseInstance()
{
    if (nullptr != instance)
    {
        delete instance;
        instance = nullptr;
    }
}

RtpEpollThread::RtpEpollThread()
{
    epoll_fd_ = -1;
    working_ = false;
    recv_buf_ = new unsigned char[RTP_BUFFER_SIZE];

    // 创建唤醒管道
    if (pipe(wake_pipe_) == -1) {
        AK_LOG_ERROR << "Failed to create wake-up pipe: " << strerror(errno);
        AK_LOG_INFO << "pipe[0] = " << wake_pipe_[0] << ", pipe[1] = " << wake_pipe_[1];
    } else {
        // 设置管道两端为非阻塞
        SetNonblocking(wake_pipe_[0]);
        SetNonblocking(wake_pipe_[1]);
    }
}

RtpEpollThread::~RtpEpollThread()
{
    AK_LOG_INFO << "~RtpEpollThread()";
    if (recv_buf_ != nullptr)
    {
        delete[] recv_buf_;
        recv_buf_ = nullptr;
    }
        
    // 关闭唤醒管道
    if (wake_pipe_[0] >= 0) {
        close(wake_pipe_[0]);
        wake_pipe_[0] = -1;
    }
    if (wake_pipe_[1] >= 0) {
        close(wake_pipe_[1]);
        wake_pipe_[1] = -1;
    }
}

bool RtpEpollThread::Start()
{
    working_ = true;
    epoll_thread_ = std::thread([this]() { this->EpollThread(nullptr); });
    return true;
}

void RtpEpollThread::Stop()
{
    working_ = false;
    epoll_thread_.join();
}


void RtpEpollThread::WakeUpEpollThread()
{
    if (wake_pipe_[1] >= 0) {
        char byte = 1;
        int ret = write(wake_pipe_[1], &byte, 1);
        if (ret != 1) {
            AK_LOG_WARN << "Failed to write to wake-up pipe: " << strerror(errno);
        } else {
            AK_LOG_INFO << "Wake up epoll thread";
        }
    }
}

int RtpEpollThread::EpollThread(void* arg)
{
    AK_LOG_INFO << __FUNCTION__ << " start";

    epoll_fd_ = epoll_create(RTP_EVENT_MAX);
    if (-1 == epoll_fd_)
    {
        AK_LOG_WARN << "RTP create epoll error = " << strerror(errno) << " errno = " << errno;
        return -1;
    }

    // 将唤醒管道的读端添加到epoll中
    if (wake_pipe_[0] >= 0) {
        epoll_event event;
        memset(&event, 0, sizeof(event));
        event.data.fd = wake_pipe_[0];
        event.events = EPOLLIN;
        if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, wake_pipe_[0], &event) != 0) {
            AK_LOG_ERROR << "Failed to add wake pipe to epoll: " << strerror(errno);
        } else {
            AK_LOG_INFO << "Added wake-up pipe to epoll, fd = " << wake_pipe_[0];
        }
    }

    epoll_event events[RTP_EVENT_MAX];
    memset(events, 0, sizeof(events));

    while (working_)
    {
        // 处理添加和删除socket操作
        ProcessSocketOperations();

        int ret = epoll_wait(epoll_fd_, events, RTP_EVENT_MAX, -1);
        if (ret < 0)
        {
            AK_LOG_WARN << "RTP epoll failure, errno = " << errno;
            if (errno == 22)
            {
                AK_LOG_FATAL << "RTP epoll failure, errno = " << errno;
            }
            continue;
        }

        if (ret > 0) {
            lt(events, ret);
        }
    }

    if (epoll_fd_ > 0)
    {
        close(epoll_fd_);
        epoll_fd_ = -1;
    }
    
    AK_LOG_INFO << __FUNCTION__ << " end";
    return 0;
}

int RtpEpollThread::SetNonblocking(int fd)
{
    int old_option = fcntl(fd, F_GETFL);
    int new_option = old_option | O_NONBLOCK;
    fcntl(fd, F_SETFL, new_option);
    return old_option;
}

int RtpEpollThread::SetTimeOut(int sockfd)
{
    // 设置接收超时时间
    struct timeval timeout;
    timeout.tv_sec = 0;  // 设置超时为 0 秒
    timeout.tv_usec = 10000; // 微秒部分 10ms
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        AK_LOG_WARN << "Set socket timeout options failed fd:" << sockfd;
    }
    return 0;
}

void RtpEpollThread::AddRtpSocket(int rtp_fd, int rtcp_fd, uint64_t trace_id)
{
    SocketOperation op;
    op.op_type = SocketOpType::ADD;
    op.rtp_fd = rtp_fd;
    op.rtcp_fd = rtcp_fd;
    op.trace_id = trace_id;
 
    {
        std::lock_guard<std::mutex> lock(socket_op_mutex_);
        socket_op_queue_.push(op);
    }

    // 唤醒epoll线程
    WakeUpEpollThread();

    AK_LOG_INFO << "AddRtpSocket rtp_fd=" << rtp_fd << ", rtcp_fd=" << rtcp_fd << ", size = " << socket_op_queue_.size() << ", trace_id = " << trace_id;
    return;
}

void RtpEpollThread::RemoveRtpSocket(int rtp_fd, int rtcp_fd, uint64_t trace_id)
{
    SocketOperation op;
    op.op_type = SocketOpType::REMOVE;
    op.rtp_fd = rtp_fd;
    op.rtcp_fd = rtcp_fd;
    op.trace_id = trace_id;
    
    {
        std::lock_guard<std::mutex> lock(socket_op_mutex_);
        socket_op_queue_.push(op);
    }

    // 唤醒epoll线程
    WakeUpEpollThread();
    
    AK_LOG_INFO << "RemoveRtpSocket rtp_fd=" << rtp_fd << ", rtcp_fd=" << rtcp_fd << ", size = " << socket_op_queue_.size() << ", trace_id = " << trace_id;
    return;
}

void RtpEpollThread::ProcessSocketOperations()
{
    std::queue<SocketOperation> operations;
    {
        std::lock_guard<std::mutex> lock(socket_op_mutex_);
        operations.swap(socket_op_queue_);
    }
    
    while (!operations.empty())
    {
        SocketOperation op = operations.front();
        operations.pop();

        if (op.op_type == SocketOpType::ADD)
        {
            AddFd(op.rtp_fd, false, op.trace_id);
            AddFd(op.rtcp_fd, false, op.trace_id);
            AK_LOG_INFO << "[" << op.trace_id << "] Queued operation: Added RTP socket fd = " << op.rtp_fd << " and RTCP socket fd = " << op.rtcp_fd;
        }
        else if (op.op_type == SocketOpType::REMOVE)
        {
            RemoveFd(op.rtp_fd, op.trace_id);
            RemoveFd(op.rtcp_fd, op.trace_id);
            AK_LOG_INFO << "[" << op.trace_id << "] Queued operation: Removed RTP socket fd = " << op.rtp_fd << " and RTCP socket fd = " << op.rtcp_fd;
        }
    }   
}

void RtpEpollThread::AddFd(int fd, bool enable_et, uint64_t trace_id)
{
    SetTimeOut(fd);    
    SetNonblocking(fd);

    epoll_event event;
    memset(&event, 0, sizeof(event));

    event.data.fd = fd;
    event.events = EPOLLIN;
    if (enable_et)
    {
        event.events |= EPOLLET;
    }
    int ret = epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, fd, &event);
    if (ret != 0)
    {
        AK_LOG_WARN << "[" << trace_id << "] RtpEpollThread epoll add error fd = " << fd;
    }    
    AK_LOG_INFO << "[" << trace_id << "] RtpEpollThread epoll add fd = " << fd;
}

void RtpEpollThread::RemoveFd(int fd, uint64_t trace_id)
{
    epoll_event event;
    memset(&event, 0, sizeof(event));

    event.data.fd = fd;
    int ret = epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, fd, &event);
    if (ret != 0)
    {
        AK_LOG_WARN << "[" << trace_id << "] RtpEpollThread epoll remove error fd = " << fd;
    }      

    // 关闭socket
    close(fd);
    AK_LOG_INFO << "[" << trace_id << "] RtpEpollThread epoll del fd = " << fd;
}

void RtpEpollThread::lt(epoll_event* events, int number)
{
    for (int i = 0; i < number; i++)
    {
        int sockfd = events[i].data.fd;

        // 处理唤醒管道 
        if (sockfd == wake_pipe_[0]) 
        {
            char buffer[16];
            // 清空管道
            while (read(wake_pipe_[0], buffer, sizeof(buffer)) > 0) {
                // 只需清空
            }
            continue;
        }

        if (events[i].events & EPOLLIN)
        {
            struct sockaddr_storage raddr;
            socklen_t val = sizeof(raddr);
            memset(recv_buf_, 0, RTP_BUFFER_SIZE);
            //ipv6
            int ret = recvfrom(sockfd, recv_buf_, RTP_BUFFER_SIZE, 0, (SA*)&raddr, &val);
            if (ret <= 0)
            {
                AK_LOG_WARN << "RTP recv err " << ret;
                continue;
            }
             
            //先根据udp-fd确定是设备发送过来的还是app发送过来的
            //APP发过来的几个UDP包
            std::shared_ptr<RtpAppClient> rtp_app = RtpAppManager::getInstance()->GetClientBySocket(sockfd);
            if (rtp_app != NULL)
            {
                if (!rtp_app->hasnat_) //是否已经完成app的udp-nat工作
                {
                    ProcessApp(rtp_app, raddr, recv_buf_, ret);
                }
                continue;
            }
            
            //App rtcp nat
            std::shared_ptr<RtpAppClient>  rtcp_app = RtpAppManager::getInstance()->GetClientByRtcpSocket(sockfd);
            if (rtcp_app != NULL)
            {
                if (!rtcp_app->has_rctp_nat_) //是否已经完成app的udp-nat工作
                {
                    memcpy(&rtcp_app->app_rtcp_addr_, &raddr, sizeof(struct sockaddr_storage));
                    //rtcp_app->has_rctp_nat_ = true; onRtcpMessage里面会再次判断
                    rtcp_app->onRtcpMessage(recv_buf_, ret);
                }
                else
                {
                    rtcp_app->onRtcpMessage(recv_buf_, ret);
                }
                continue;
            }
            LOG_EVERY_N(INFO, 300) << "get " << google::COUNTER <<  " chenzhx lt rtcp_dev fd=" << sockfd;
            //dev rtcp nat
            std::shared_ptr<RtpDeviceClient> rtcp_device = RtpDeviceManager::getInstance()->GetClientByRtcpSocket(sockfd);
            if (rtcp_device != NULL)
            {
                if (!rtcp_device->has_rctp_nat_)
                {
                    //目前设备发过来的rtcp没有用，只是nat用
                    memcpy(&rtcp_device->dev_rtcp_addr, &raddr, sizeof(struct sockaddr_storage));
                }
                //dev rtcp的处理在下面RtpDeviceManager::getInstance()->AddMsg中的ParseRtpHeader
            }

            LOG_EVERY_N(INFO, 300) << "get " << google::COUNTER <<  " chenzhx lt rtp_dev fd=" << sockfd;
            //设备发过来的rtp监控包
            RtpDeviceManager::getInstance()->AddMsg(sockfd, raddr, recv_buf_, ret);//ret==udp包长度

            // 唤醒处理线程
            RtpProcessThread::getInstance()->SetEvent();
        }
        else
        {
            AK_LOG_WARN << "something else happened";
        }
    }
}

int RtpEpollThread::ProcessApp(std::shared_ptr<RtpAppClient>& app_rtp_client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len)
{
    if (app_rtp_client->IsSupportCheckSSRC())
    {
        unsigned char data_convert[RTP_BUFFER_SIZE];
        memset(data_convert, 0, RTP_BUFFER_SIZE);
        int data_convert_len = RTP_BUFFER_SIZE;
        if (app_rtp_client->GetRtpConfuseSwitch())
        {
            //如果开启了rtp混淆，则需要解混淆
            RtpConfuse::DecRtpConfuse(data, data_len, data_convert, &data_convert_len);
        }
        else
        {
            memcpy(data_convert, data, data_len);
        }
        //获取ssrc        
        const uint8_t* ptr = &data_convert[8];
        uint32_t recv_ssrc = akcs::ByteReader<uint32_t>::ReadBigEndian(ptr);

        if (app_rtp_client->app_client_ssrc_ != recv_ssrc)
        {
            AK_LOG_WARN << "ssrc not match. nat failed. app ssrc=" << app_rtp_client->app_client_ssrc_ << " recv ssrc=" << recv_ssrc;
            return 0;
        }
    }

    app_rtp_client->hasnat_ = true;
    memcpy(&app_rtp_client->app_addr_, &addr, sizeof(struct sockaddr_storage));

    AK_LOG_INFO << "==========================PROCESS APP RTP NAT START=========================================";
    AK_LOG_INFO << "[" << app_rtp_client->trace_id_ << "] recv app rtp nat packet, client ip:port = " << Sock_ntop((SA*)&app_rtp_client->app_addr_, sizeof(app_rtp_client->app_addr_)) << ", local rtp port = " << app_rtp_client->local_rtp_port_;
    AK_LOG_INFO << "==========================PROCESS APP RTP NAT END===========================================";

    return 0;
}

int RtpEpollThread::ProcessRtcpApp(std::shared_ptr<RtpAppClient>& app_rtp_client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len)
{
    app_rtp_client->has_rctp_nat_ = true;
    memcpy(&app_rtp_client->app_rtcp_addr_, &addr, sizeof(struct sockaddr_storage));
    
    AK_LOG_INFO << "==========================PROCESS APP RTCP NAT START=========================================";
    AK_LOG_INFO << "[" << app_rtp_client->trace_id_ << "] recv app rtcp nat packet, client ip:port = " << Sock_ntop((SA*)&app_rtp_client->app_rtcp_addr_, sizeof(app_rtp_client->app_rtcp_addr_)) << ", local rtcp port = " << app_rtp_client->local_rtcp_port_;;
    AK_LOG_INFO << "==========================PROCESS APP RTCP NAT END===========================================";
    
    return 0;
}

} // namespace akuvox