#ifndef RTP_EPOLL_THREAD_H
#define RTP_EPOLL_THREAD_H

#include <sys/epoll.h>
#include <string>
#include <map>
#include <vector>
#include <thread>
#include <memory>
#include <queue>
#include <mutex>
#include "WaitEvent.h"
#include "Unp.h"
#include "RtpProcessThread.h"

namespace akuvox
{

#define RTP_EVENT_MAX   2000
#define RTP_BUFFER_SIZE 2048

class RtpAppClient;
class RtpDeviceClient;

// Socket operation type enum
enum class SocketOpType 
{
    ADD,
    REMOVE
};

// Socket operation structure
struct SocketOperation 
{
    SocketOpType op_type;
    int rtp_fd;
    int rtcp_fd;
    uint64_t trace_id;
};

class RtpEpollThread
{
public:
    static RtpEpollThread* getInstance();
    static void ReleaseInstance();

    bool Start();
    void Stop();

    void AddRtpSocket(int rtp_fd, int rtcp_fd, uint64_t trace_id);
    void RemoveRtpSocket(int rtp_fd, int rtcp_fd, uint64_t trace_id);

private:
    RtpEpollThread();
    ~RtpEpollThread();

    static RtpEpollThread* instance;

    // Methods from original RtpControl
    int EpollThread(void* arg);
    void AddFd(int fd, bool enable_et, uint64_t trace_id);
    void RemoveFd(int fd, uint64_t trace_id);
    void lt(epoll_event* events, int number);
    int SetNonblocking(int fd);
    int SetTimeOut(int sockfd);
    int ProcessApp(std::shared_ptr<RtpAppClient>& app_rtp_client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len);
    int ProcessRtcpApp(std::shared_ptr<RtpAppClient>& app_rtp_client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len);

    // Process socket operations from the queue
    void ProcessSocketOperations();

    // Wake up the epoll thread
    void WakeUpEpollThread();

    // Member variables
    int epoll_fd_;
    bool working_;
    std::thread epoll_thread_;
    unsigned char* recv_buf_;
    
    // Wake-up pipe for epoll
    int wake_pipe_[2] = {-1, -1};

    // Socket operation queue
    std::queue<SocketOperation> socket_op_queue_;
    std::mutex socket_op_mutex_;

    std::shared_ptr<RtpDeviceClient> CreateRtpSocket(uint64_t trace_id, uint16_t local_rtp_port, const std::string& flow_uuid);
};

} // namespace akuvox

#endif // RTP_EPOLL_THREAD_H