#include "RtpProcessThread.h"
#include "RtpDeviceManager.h"
#include "AkLogging.h"

namespace akuvox
{

RtpProcessThread* RtpProcessThread::instance = nullptr;

RtpProcessThread* RtpProcessThread::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtpProcessThread;
    }
    return instance;
}

void RtpProcessThread::ReleaseInstance()
{
    if (nullptr != instance)
    {
        delete instance;
        instance = nullptr;
    }
}

RtpProcessThread::RtpProcessThread()
{
    working_ = false;
    event_ = new CWaitEvent();
}

RtpProcessThread::~RtpProcessThread()
{
    AK_LOG_INFO << "~RtpProcessThread()";
    if (event_ != nullptr)
    {
        delete (CWaitEvent*)event_;
        event_ = nullptr;
    }
}

bool RtpProcessThread::Start(int thread_count)
{
    working_ = true;
    for (int i = 0; i < thread_count; i++)
    {
        threads_.push_back(std::thread(&RtpProcessThread::ProcessThread, this));
    }
    return true;
}

void RtpProcessThread::Stop()
{
    working_ = false;
    for (size_t i = 0; i < threads_.size(); ++i)
    {
        threads_[i].join();
    }
}

void RtpProcessThread::SetEvent()
{
    event_->Set();
}

int RtpProcessThread::ProcessThread()
{
    AK_LOG_INFO << "ProcessThread start tid = " << std::this_thread::get_id();

    while (working_)
    {
        RtpDevClientPtrList rtp_dev_client_list;
        RtpDeviceManager::getInstance()->GetHasMsgDevClient(rtp_dev_client_list);
        while (rtp_dev_client_list.size() == 0)
        {
            event_->Wait();
            RtpDeviceManager::getInstance()->GetHasMsgDevClient(rtp_dev_client_list);
        }

        for(auto &rtp_dev : rtp_dev_client_list)
        {
            rtp_dev->ProcessMsg();
        }
    }
    
    AK_LOG_INFO << "ProcessThread end tid = " << std::this_thread::get_id();
    return 0;
}

} // namespace akuvox
