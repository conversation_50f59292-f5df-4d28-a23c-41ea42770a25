#ifndef RTP_PROCESS_THREAD_H
#define RTP_PROCESS_THREAD_H

#include <sys/epoll.h>
#include <string>
#include <map>
#include <vector>
#include <thread>
#include <memory>
#include "WaitEvent.h"
#include "Unp.h"

namespace akuvox
{

class RtpProcessThread
{
public:
    static RtpProcessThread* getInstance();
    static void ReleaseInstance();

    bool Start(int thread_count);
    void Stop();
    void SetEvent();

private:
    RtpProcessThread();
    ~RtpProcessThread();

    static RtpProcessThread* instance;
    
    int ProcessThread();
    
    bool working_;
    std::vector<std::thread> threads_;
    CWaitEvent* event_;
};

} // namespace akuvox

#endif // RTP_PROCESS_THREAD_H
