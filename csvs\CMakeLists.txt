CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (csvs C CXX)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libglog.so libmysqlclient.so libgpr.so libgrpc.so libgrpc++.so libprotobuf.so)
LINK_DIRECTORIES( /usr/local/lib ../csbase ../csbase/thirdlib )

AUX_SOURCE_DIRECTORY(./ SRC_LIST_STORAGE)
AUX_SOURCE_DIRECTORY(./dao SRC_LIST_STORAGE_DAO)
AUX_SOURCE_DIRECTORY(../csbase/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(../csbase/grpc SRC_LIST_BASE_GRPC)
SET(BASE_LIST_INC ../csbase ../csbase/mysql/include ../csbase/Rldb ../csbase/grpc)
SET(STORAGE_MODE_LIST_INC ./dao)

ADD_DEFINITIONS( -std=c++11 -g -W -Wall -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL -DAC_HAS_DEBUG -DLINUX_DAEMON)
                           
include_directories(${BASE_LIST_INC} ${STORAGE_MODE_LIST_INC})

add_executable(csvs ${SRC_LIST_STORAGE} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_STORAGE_DAO} ${SRC_LIST_BASE_GRPC})
target_link_libraries(csvs  ${DEPENDENT_LIBRARIES})
