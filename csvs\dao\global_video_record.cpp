#include <sstream>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "global_video_record.h"

GlobalVieRecord* GlobalVieRecord::instance = NULL;

GlobalVieRecord* GlobalVieRecord::GetInstance()
{
    if (instance == NULL)
    {
        instance = new GlobalVieRecord();
    }

    return instance;
}

int GlobalVieRecord::DaoAddVideo(const std::string& uri, uint32_t& id)
{
    //std::stringstream streamSQL;
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "insert into %s(VideoUri) values('%s')", "GlobalVideoList", uri.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed";
        return -1;
    }
    std::string sql2 = sql;
    int nRet = conn->Execute(sql2) > 0 ? 0 : -1;
    if (nRet != 0)
    {
        AK_LOG_WARN << "insert into GlobalVideoList video record failed";
        return -1;
    }
    std::string sql_id = "SELECT last_insert_id()";
    CRldbQuery query(pTmpConn);
    query.Query(sql_id);
    //终端用户名是全局唯一的,故用if not while
    char video_id[16] = {0};
    if (query.MoveToNextRow())
    {
        ::strncpy(video_id, query.GetRowData(0), sizeof(video_id) - 1);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    id = ::atoi(video_id);
    //释放数据库连接
    ReleaseDBConn(conn);
    return 0;
}

int GlobalVieRecord::DaoGetVideoUri(const uint32_t vid, std::string& uri)
{
    std::stringstream streamSQL;
    streamSQL << "select VideoUri from GlobalVideoList where ID = '"
              << vid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed";
        return -1;
    }

    CRldbQuery query(pTmpConn);
    query.Query(streamSQL.str());
    //终端用户名是全局唯一的,故用if not while
    char video_uri[128] = {0};
    if (query.MoveToNextRow())
    {
        ::strncpy(video_uri, query.GetRowData(0), sizeof(video_uri) - 1);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    uri = video_uri;
    return 0;
}

