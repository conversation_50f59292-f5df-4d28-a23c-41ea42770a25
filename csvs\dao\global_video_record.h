#ifndef __CSVS_PERSONNAL_DEVICES_H__
#define __CSVS_PERSONNAL_DEVICES_H__

#include <string>
#include <boost/noncopyable.hpp>

class GlobalVieRecord : public boost::noncopyable
{
public:
    GlobalVieRecord()
    {
    }
    ~GlobalVieRecord()
    {
    }
    int DaoAddVideo(const std::string& uri, uint32_t& id);
    int DaoGetVideoUri(const uint32_t vid, std::string& uri);
    static GlobalVieRecord* GetInstance();
private:

    static GlobalVieRecord* instance;

};

#endif //__CSVS_PERSONNAL_DEVICES_H__
