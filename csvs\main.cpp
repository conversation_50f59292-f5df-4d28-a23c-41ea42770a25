// cs_vie_storage server
// Created on: 2018-08-08
// Author: chenyc

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <vector>
#include <string>
#include <map>
#include <thread>
#include <errno.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <signal.h>
#include "glog/logging.h"
#include "vie_storage_mng.h"
#include "AkLogging.h"
#include "util.h"
#include "Rldb/RldbQuery.h"
#include "ConfigFileReader.h"
#include "vie_storage_ser.h"
#include "msg_queue.h"
#include "vie_rpc_server.h"

static const char csvs_conf_file[] = "/usr/local/akcs/csvs/conf/csvs.conf";
char MP4_PATH[] = "/home/<USER>/";

CStorageMng* g_storage_mng_ptr = nullptr;

AKCS_CONF gstAKCSConf;

#define PIDFILE "/var/run/csvs.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = ::open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if (write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;  //close on exec, not on-fork
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

int ConfInit()
{
    memset(&gstAKCSConf, 0, sizeof(AKCS_CONF));
    CConfigFileReader config_file(csvs_conf_file);

    ::strncpy(gstAKCSConf.szStorageOuterIP, config_file.GetConfigName("csvs_outerip"), sizeof(gstAKCSConf.szStorageOuterIP));
    ::strncpy(gstAKCSConf.szDbIP, config_file.GetConfigName("db_ip"), sizeof(gstAKCSConf.szDbIP));
    ::strncpy(gstAKCSConf.szDbUserName, config_file.GetConfigName("db_username"), sizeof(gstAKCSConf.szDbUserName));
    ::strncpy(gstAKCSConf.szDbPassword, config_file.GetConfigName("db_passwd"), sizeof(gstAKCSConf.szDbPassword));
    ::strncpy(gstAKCSConf.szDbDatabase, config_file.GetConfigName("db_database"), sizeof(gstAKCSConf.szDbDatabase));

    char* db_port = config_file.GetConfigName("db_port");
    gstAKCSConf.nDbPort = ::atoi(db_port);

    char* video_length = config_file.GetConfigName("video_length");
    gstAKCSConf.nVideoLength = ::atoi(video_length);
    return 0;

}

/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstAKCSConf.szDbIP, gstAKCSConf.szDbUserName, gstAKCSConf.szDbPassword, gstAKCSConf.szDbDatabase, gstAKCSConf.nDbPort, 4);
    return 0;
}

//路径初始化
int Mp4PathInit()
{
    int i = 0;
    for (; i < 1000; i++)
    {
        char mp4_hash_path[36] = {0};
        ::snprintf(mp4_hash_path, 36, "%s%d/", MP4_PATH, i);
        if (!fileExist(mp4_hash_path))
        {
            if (::mkdir(mp4_hash_path, 0777) != 0)
            {
                AK_LOG_FATAL << "mkdir path error, path is" << mp4_hash_path << "errno is " << errno;
                return -1;
            }
        }
    }
    return 0;
}
void TimerSrv()
{
    while (1)
    {
        ::sleep(1);
        g_storage_mng_ptr->InspectAllFfmpegProc();
    }
}

void SigChld(int signo)
{
    pid_t pid;
    int   stat;
    ::signal(SIGCHLD, &SigChld);
    while ((pid = ::waitpid(-1, &stat, WNOHANG)) > 0)
    {
        AK_LOG_INFO << "child terminated " << pid;
        //当该pid意外死亡(非超时由csvs进程kill掉的情况),应该兼容 CVideoStorage::Stop的操作,否则
        //有误杀进程的风险
        g_storage_mng_ptr->RemoveFfmpegProc(pid);
        //TODO,当不到30s(或者其他规定时间)就意外挂掉,要再次拉起.但是时间就无法保证...
    }
    return;
}

void QuitJob()
{
    //清理ffmoeg子进程
    AK_LOG_WARN << "I'm ready quit...";
    g_storage_mng_ptr->StopAllFfmpegProc();
}

void StopProcess(int signo)
{
    AK_LOG_WARN << "receive signal:" << signo;
    switch (signo)
    {
        //典型的终止进程的几个信号
        case SIGINT:
        case SIGTERM:
        case SIGQUIT:
        case SIGHUP:
        case SIGKILL:
            QuitJob();
            ::_exit(0);
            break;
    }
}

int main()
{
    //glog初始化
    google::InitGoogleLogging("csvs");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csvslog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csvslog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csvslog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csvslog/FATAL");
    FLAGS_logbufsecs = 0;
    FLAGS_max_log_size = 10;    //单日志文件最大10M

    /* 读取配置文件 */
    if (ConfInit() != 0)
    {
        AK_LOG_FATAL << "init conf failed";
        return -1;
    }

    /* 读取配置文件 */
    if (Mp4PathInit() != 0)
    {
        AK_LOG_FATAL << "init conf failed";
        return -1;
    }

    if (DaoInit() != 0)
    {
        AK_LOG_FATAL << "init dao failed";
        return -1;
    }

    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        AK_LOG_FATAL << "another csvs has been running in this system.";
        return -1;
    }

    //种植SIGCHILD处理函数
    ::signal(SIGCHLD, &SigChld);
    ::signal(SIGPIPE, SIG_IGN);
    ::signal(SIGINT, StopProcess);
    ::signal(SIGTERM, StopProcess);
    ::signal(SIGQUIT, StopProcess);
    ::signal(SIGHUP, StopProcess);
    ::signal(SIGKILL, StopProcess);

    GetNotifyMsgControlInstance()->Init();
    g_storage_mng_ptr = new CStorageMng();
    std::thread TimerThread(TimerSrv);
    //rpc服务
    ServerImpl rpc_server;
    rpc_server.Run();
    TimerThread.join();
    return 0;
}

