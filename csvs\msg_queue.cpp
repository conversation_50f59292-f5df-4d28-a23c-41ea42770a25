#include "msg_queue.h"
#include "vie_storage_mng.h"

extern CStorageMng* g_storage_mng_ptr;
CNotifyMsgControl::CNotifyMsgControl(): m_MsgCount(0)
{

}

CNotifyMsgControl::~CNotifyMsgControl()
{
    m_NotifyMsgList.clear();
}

CNotifyMsgControl* CNotifyMsgControl::instance = NULL;

CNotifyMsgControl* CNotifyMsgControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CNotifyMsgControl();
    }

    return instance;
}

CNotifyMsgControl* GetNotifyMsgControlInstance()
{
    return CNotifyMsgControl::GetInstance();
}
//在主线程初始化,注意m_pCurl不是线程安全的.
int CNotifyMsgControl::Init()
{
    m_t = std::thread(&CNotifyMsgControl::ProcessNotifyMsg, this);
    return 0;
}

int CNotifyMsgControl::ProcessNotifyMsg()
{
    while (1)
    {
        NotifyMsgPrt TmpPtr;
        {
            std::unique_lock<std::mutex> lock(m_mtx);
            while (m_NotifyMsgList.size() == 0)
            {
                m_cv.wait(lock);
            }
            TmpPtr = m_NotifyMsgList.back();
            m_NotifyMsgList.pop_back();
        }
        TmpPtr->NotifyMsg();
    }
    return 0;
}

int CNotifyMsgControl::AddVideoStorageMsg(const CVideoStorageMsg& CMsg)
{
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        NotifyMsgPrt TmpPtr(new CVideoStorageMsg(CMsg));
        m_NotifyMsgList.push_front(TmpPtr);
        m_cv.notify_all();
    }
    return 0;
}

int CVideoStorageMsg::NotifyMsg()
{
    if (type_ == START_VIDEO_STORAGE)
    {
        //启动视频裸数据存储
        //g_storage_mng_ptr->StartWriteRtpToM3u8(uid_);//StartWriteRtpToM3u8

    }
    else if (type_ == STOP_VIDEO_STORAGE)
    {
        //g_storage_mng_ptr->StopWriteRtpToM3u8(uid_);//StartWriteRtpToM3u8
    }
    else
    {
        return -1;
    }
    return 0;
}


