#ifndef __NOTIFY_MSG_CONTROL_H__
#define __NOTIFY_MSG_CONTROL_H__

#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <list>
//#include "ipc_unix_socket.h"

enum VideoType
{
    NULL_VIDEO_STORAGE = 0,
    START_VIDEO_STORAGE,
    STOP_VIDEO_STORAGE,
};

class CNotifyMsg; //前置声明
class CVideoStorageMsg;


class CNotifyMsgControl
{
public:
    typedef std::shared_ptr<CNotifyMsg> NotifyMsgPrt;

public:
    CNotifyMsgControl();
    ~CNotifyMsgControl();

    static CNotifyMsgControl* GetInstance();

    //初始化
    int Init();
    //处理消息
    int ProcessNotifyMsg();
    //开始启动视频存储
    int AddVideoStorageMsg(const CVideoStorageMsg& CMsg);

private:

    std::list<NotifyMsgPrt> m_NotifyMsgList;
    std::mutex m_mtx;
    std::condition_variable m_cv;
    std::thread m_t;
    int32_t m_MsgCount;  //通知消息队列中未消费的消息个数
    static CNotifyMsgControl* instance;
};

CNotifyMsgControl* GetNotifyMsgControlInstance();


class CNotifyMsg
{
public:
    CNotifyMsg()
    {

    }
    virtual ~CNotifyMsg()
    {

    }
    int virtual NotifyMsg() = 0;
};


//开始视频存储通知消息
class CVideoStorageMsg : public CNotifyMsg
{
public:
    CVideoStorageMsg()
    {
        type_ = NULL_VIDEO_STORAGE;
    }
    CVideoStorageMsg(const std::string& uid, const std::string& node, VideoType type)
    {
        uid_ = uid;
        node_ = node;
        type_ = type;
    }
    CVideoStorageMsg(const CVideoStorageMsg& other) //深拷贝
    {
        uid_ = other.uid_;
        node_ = other.node_;
        type_ = other.type_;
    }

    ~CVideoStorageMsg()
    {
    }
    int NotifyMsg();
private:
    std::string uid_;
    std::string node_;
    VideoType type_;

};

#endif //__NOTIFY_MSG_CONTROL_H__

