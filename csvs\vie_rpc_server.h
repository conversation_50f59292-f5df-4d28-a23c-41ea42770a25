#include <memory>
#include <iostream>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>

#include "csvs.grpc.pb.h"
#include "AkLogging.h"
#include "vie_storage_mng.h"

using grpc::Server;
using grpc::ServerAsyncResponseWriter;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::ServerCompletionQueue;
using grpc::Status;

using VideoStorage::VsReply;
using VideoStorage::VsRequest;
using VideoStorage::VsDelReply;
using VideoStorage::VsDelRequest;

using VideoStorage::VideoStorageMsg; //rpc服务名

extern CStorageMng* g_storage_mng_ptr;

class ServerImpl
{
public:
    ~ServerImpl()
    {
        server_->Shutdown();
        // Always shutdown the completion queue after the server.
        cq_->Shutdown();
    }

    // There is no shutdown handling in this code.
    void Run()
    {
        std::string server_address("0.0.0.0:9001");

        ServerBuilder builder;
        // Listen on the given address without any authentication mechanism.
        //通过这样来加密: auto channel_creds = grpc::SslCredentials(grpc::SslCredentialsOptions());
        // auto channel = grpc::CreateChannel("myservice.example.com", creds);
        builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
        // Register "service_" as the instance through which we'll communicate with
        // clients. In this case it corresponds to an *asynchronous* service.
        builder.RegisterService(&service_);
        // Get hold of the completion queue used for the asynchronous communication
        // with the gRPC runtime.
        cq_ = builder.AddCompletionQueue();
        // Finally assemble the server.
        server_ = builder.BuildAndStart();
        AK_LOG_INFO << "Server listening on " << server_address;

        // Proceed to the server's main loop.
        new CallData(&service_, cq_.get(), ServerImpl::CallData::VIDEO_STORAGE_ACT);
        new CallData(&service_, cq_.get(), ServerImpl::CallData::VIDEO_STORAGE_DEL);
        //std::thread HandleRpcsThread(ServerImpl::HandleRpcs);
        std::thread HandleRpcsThread(std::bind(&ServerImpl::HandleRpcs, this));
        HandleRpcs();
    }

private:
    // Class encompasing the state and logic needed to serve a request.
    class CallData
    {
    public:
        enum ServiceType
        {
            VIDEO_STORAGE_ACT = 0,
            VIDEO_STORAGE_DEL = 1,
        };
    public:
        CallData(VideoStorageMsg::AsyncService* service, ServerCompletionQueue* cq, ServiceType s_type)
            : service_(service), cq_(cq), s_type_(s_type), responder_(&ctx_), del_responder_(&ctx_), status_(CREATE)
        {
            // Invoke the serving logic right away.
            Proceed();
        }

        void Proceed()
        {
            if (status_ == CREATE)
            {
                // Make this instance progress to the PROCESS state.
                status_ = PROCESS;
                switch (s_type_)
                {
                    //根据不同的服务 注册不同的 服务类型到 ServerCompletionQueue 队列， 看名字和使用有点像完成端口  (没有去验证研究)
                    case VIDEO_STORAGE_ACT:
                    {
                        service_->RequestVideoStorageHandle(&ctx_, &request_, &responder_, cq_, cq_, this);
                        break;
                    }
                    case VIDEO_STORAGE_DEL:
                    {
                        service_->RequestDelVideoStorageHandle(&ctx_, &del_request_, &del_responder_, cq_, cq_, this);
                        break;
                    }
                    default:
                        break;
                }

                //为每一个rpc客户端的请求提供一个 唯一标签，这里用calldata对象的地址坐标签
                //TODO,这样被限制死了,一个接口就要一个线程来刷?
                //service_->RequestVideoStorageHandle(&ctx_, &request_, &responder_, cq_, cq_, this);
            }
            else if (status_ == PROCESS)
            {
                //注意与 new CallData(&service_, cq_.get());  中cq_的区别，分别属于不同类的成员变量,一个是裸指针，一个时智能指针.
                //生成一个新的CallData  并压入队列之内，同时在构造函数内部调用Proceed。完成标签的设定。。等下下次有新的客户请求过来，在HandleRpcs
                //会使用该新生成的CallData来承载新请求的对象.也就是要保证消耗一个CallData，就要准备一个供后面的请求使用
                //new CallData(service_, cq_);
                status_ = FINISH;
                new CallData(service_, cq_, this->s_type_);
                switch (s_type_)
                {
                    case ServerImpl::CallData::VIDEO_STORAGE_ACT:
                    {
                        std::string rtsp_srv_ip = request_.rtsp_srv_ip();
                        std::string storage_uid = request_.storage_uid();
                        std::string dev_rtsp_pwd = request_.dev_rtsp_pwd();
                        std::string dev_rtsp_node = request_.storage_node();
                        VideoStorage::VideoStorageAction act_type = request_.act();
                        std::string mp4_url;
                        std::string mac;
                        uint32_t video_id = 0;
                        if (act_type == VideoStorage::START_VIDEO_STORAGE)
                        {
                            //启动视频裸数据存储,可能返回空,表示同一台设备正在录制
                            mp4_url = g_storage_mng_ptr->StartWriteRtpToMP4(storage_uid, dev_rtsp_pwd, rtsp_srv_ip, mac, video_id); //StartWriteRtpToM3u8
                        }
                        else if (act_type == VideoStorage::STOP_VIDEO_STORAGE) //z这个暂时不用,一律是30s.
                        {
                            g_storage_mng_ptr->StopWriteRtpToMP4(storage_uid);//StartWriteRtpToM3u8
                            mp4_url = "stop ok";
                        }
                        else
                        {
                            mp4_url = "invalid request";
                        }
                        reply_.set_hls_uri(mp4_url);
                        reply_.set_global_video_id(video_id);
                        reply_.set_resp_storage_mac(mac);
                        reply_.set_resp_storage_node(dev_rtsp_node);

                        responder_.Finish(reply_, Status::OK, this); //再一次加入队列中
                    }
                    break;
                    case ServerImpl::CallData::VIDEO_STORAGE_DEL:
                    {
                        uint32_t global_video_id = del_request_.global_video_id();
                        g_storage_mng_ptr->DelVideoClip(global_video_id);
                        //status_ = FINISH;
                        del_responder_.Finish(del_reply_, Status::OK, this); //再一次加入队列中
                    }
                    break;
                    default:
                    {
                        //status_ = FINISH;
                    }
                    break;
                }

            }
            else
            {
                GPR_ASSERT(status_ == FINISH);
                // Once in the FINISH state, deallocate ourselves (CallData).
                delete this;
            }
        }

    private:
        // The means of communication with the gRPC runtime for an asynchronous
        // server.
        VideoStorageMsg::AsyncService* service_;
        // The producer-consumer queue where for asynchronous server notifications.  客户端用的是:CompletionQueue,都是生产者消费者的模型
        ServerCompletionQueue* cq_;
        // Context for the rpc, allowing to tweak aspects of it such as the use
        // of compression, authentication, as well as to send metadata back to the
        // client.
        ServerContext ctx_;
        //多个接口服务用这个来标示
        ServiceType s_type_;

        // What we get from the client.
        VsRequest request_;
        // What we send back to the client.
        VsReply reply_;

        // What we get from the client.
        VsDelRequest del_request_;
        // What we send back to the client.
        VsDelReply del_reply_;

        // The means to get back to the client.
        //对于客户端则是: ClientAsyncResponseReader 读，都是针对reply而言的..
        ServerAsyncResponseWriter<VsReply> responder_;
        ServerAsyncResponseWriter<VsDelReply> del_responder_;

        // Let's implement a tiny state machine with the following states.
        enum CallStatus { CREATE, PROCESS, FINISH };
        CallStatus status_;  // The current serving state.
    };

private:
    // This can be run in multiple threads if needed. //多线程的话，不需要自己加锁吗?  多线程避免一个处理占用太长cpu时间...
    void HandleRpcs()
    {
        //TODO 当开启多线程的时候,这个必须挪到业务线程之前?
        //new CallData(&service_, cq_.get());
        void* tag;  // uniquely identifies a request.
        bool ok;
        while (true)
        {
            {
                std::lock_guard<std::mutex> lock(mtx_cq_);
                GPR_ASSERT(cq_->Next(&tag, &ok));
                GPR_ASSERT(ok);
            }
            static_cast<CallData*>(tag)->Proceed();
        }
    }
private:
    std::unique_ptr<ServerCompletionQueue> cq_;
    VideoStorageMsg::AsyncService service_;
    std::unique_ptr<Server> server_;
    std::mutex mtx_cq_;
};


