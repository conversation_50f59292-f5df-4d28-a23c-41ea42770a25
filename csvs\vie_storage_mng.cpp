#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <dirent.h>
#include <fcntl.h>
#include <assert.h>
#include <unistd.h>
#include <signal.h>
#include <fstream>
#include <unistd.h>
#include <list>
#include "AkLogging.h"
#include "vie_storage_mng.h"
#include "vie_storage_ser.h"
#include "global_video_record.h"

extern AKCS_CONF gstAKCSConf;
extern char MP4_PATH[];
static const char CSVS_BIN_PATH[] = "/usr/local/akcs/csvs/bin";
extern CStorageMng* g_storage_mng_ptr;

CVideoStorage::~CVideoStorage()
{

}

int CVideoStorage::Stop()
{
    return g_storage_mng_ptr->KillFfmpegProc(ffmpeg_pid_);
}

bool CVideoStorage::IsOverTime(time_t time)
{
    bool is_over = false;
    //配置文件的时间+5,具体的时间限定采用ffmpeg自带的-t参数..
    is_over = ((time > time_start_) && (time - time_start_ > gstAKCSConf.nVideoLength + 6)) ? true : false;
    return is_over;
}

pid_t CVideoStorage::GetPid()
{
    return ffmpeg_pid_;
}

std::string CVideoStorage::GetMac()
{
    return mac_;
}

std::string CVideoStorage::GetUid()
{
    return uid_;
}
int CVideoStorage::Start(std::string& mp4_uri, uint32_t& video_id)
{
    mp4_uri = "/video/";//暂时如此.相对地址是:/home/<USER>

    //根据uid做哈希,暂时先不实现gate_id，待后续分布式开发时实现
    std::size_t hash_int = std::hash<std::string> {}(uid_) % 1000;
    char hash_str_tmp[12] = {0};
    ::snprintf(hash_str_tmp, 12, "%ld", hash_int);
    hash_str_ = hash_str_tmp;

    std::string rtsp_uri = "rtsp://user:" + rtsp_pwd_;
    rtsp_uri += "@";
    rtsp_uri += rtsp_srv_ip_;
    rtsp_uri += "/";
    rtsp_uri += mac_;
    //提取文件名
    std::string mp4_file = uid_ + ".mp4";
    mp4_uri += hash_str_ + '/' + mp4_file;
    std::string mp4_path = MP4_PATH;
    mp4_path += hash_str_ + '/' + mp4_file;
    ///usr/local/akcs/csvs/ffmpeg/bin/ffmpeg -y -rtsp_transport udp -i rtsp://user:11j453k6j0407v48@**************/0C11050563B0 -c:v copy /home/<USER>/999/03.mp4
    std::vector<std::string> params;
    params.push_back(g_ffmpeg_bin);
    params.push_back("-y"); //输出文件强制覆盖
    params.push_back("-rtsp_transport");
    params.push_back("udp");
    params.push_back("-i");
    params.push_back(rtsp_uri);//rtsp uri
    params.push_back("-c:v");
    params.push_back("copy"); //-t 0:0:20 or 30s
    params.push_back("-t");
    params.push_back("30");//gstAKCSConf.nVideoLength
    params.push_back(mp4_path);

    std::string cli;
    if (true)
    {
        for (int i = 0; i < (int)params.size(); i++)
        {
            std::string ffp = params[i];
            cli += ffp;
            if (i < (int)params.size() - 1)
            {
                cli += " ";
            }
        }
        AK_LOG_INFO << "start ffmpeg, params:" << cli;
    }

    //启动ffmpeg线程来进行视频文件格式的转换。在该线程里面，调用进程
    pid_t pid;
    if ((pid = ::fork()) < 0)
    {
        AK_LOG_WARN << "vfork process failed.";
        return -1;
    }

    //记录开始时间
    time_start_ = ::time(nullptr);

    if (pid == 0)
    {
        // ignore the SIGINT and SIGTERM
        signal(SIGINT, SIG_IGN);
        signal(SIGTERM, SIG_IGN);
        // redirect logs to file.
        int log_fd = -1;
        int flags = O_CREAT | O_WRONLY | O_APPEND;
        mode_t mode = S_IRUSR | S_IWUSR | S_IRGRP | S_IWGRP | S_IROTH;
        if ((log_fd = ::open("/var/log/csvslog/csvs_ffmpeg_bin00.log", flags, mode)) < 0)
        {
            exit(-1);
        }

        // log basic info
        if (true)
        {
            char buf[4096];
            int pos = 0;
            pos += snprintf(buf + pos, sizeof(buf) - pos, "\n");
            pos += snprintf(buf + pos, sizeof(buf) - pos, "ffmpeg uid=%s\n", uid_.c_str());
            ::write(log_fd, buf, pos);
        }

        // dup to stdout and stderr.
        if (dup2(log_fd, STDOUT_FILENO) < 0)
        {
            exit(-1);
        }
        if (dup2(log_fd, STDERR_FILENO) < 0)
        {
            exit(-1);
        }
        int ret = 0;
        setsid();
        // close other fds,把从父进程继承过来的fds全部关闭掉  FD_CLOEXEC是否有必要?
        for (int i = 3; i < 1024; i++)
        {
            ::close(i);
        }

        // memory leak in child process, it's ok.
        char** charpv_params = new char* [params.size() + 1];
        for (int i = 0; i < (int)params.size(); i++)
        {
            std::string& p = params[i];
            charpv_params[i] = (char*)p.data();
        }
        // EOF: NULL
        charpv_params[params.size()] = NULL;
        ret = execv(g_ffmpeg_bin.c_str(), charpv_params); //进程被ffmpeg抢占
        if (ret < 0)
        {
            AK_LOG_WARN << "execv ffmpeg process failed.";
        }
        ::exit(ret);  //进程退出.
    }

    // parent.
    if (pid > 0) //此pid就是子进程的进程ID
    {
        //开始登记该子进程何时需要结束掉，当父进程关闭掉子进程后，还需要处理拷贝所有mp4文件到视频存储的任务.
        ffmpeg_pid_ = pid;
        AK_LOG_INFO << "start ffmpeg process successful, uid is [" << uid_ << "], "
                    << "pid is [" << ffmpeg_pid_ << "]";
        g_storage_mng_ptr->AddFfmpegProc(pid);
    }
    //查数据库
    return GlobalVieRecord::GetInstance()->DaoAddVideo(mp4_uri, video_id);
}


///////////////////////////////////////////////////////////////
CStorageMng::CStorageMng()
{

}

CStorageMng::~CStorageMng()
{
    //ffmpeg_pids_.clear();
}

//清理主机中已有的ffmpeg视频录制子进程
int CStorageMng::Init()
{
    return 0;
}

std::string CStorageMng::StartWriteRtpToMP4(const std::string& uid,
        const std::string& pwd,
        const std::string& rtsp_ip,
        std::string& mac,
        uint32_t& vid)
{
    std::string mp4_uri;
    std::size_t mac_pos = uid.find('_');
    mac = uid.substr(0, mac_pos);
    //判断该设备知否已经在录制
    {
        std::lock_guard<std::mutex> lock(mac_storages_mutex_);
        MacListIter iter = mac_storages.find(mac);
        if (iter != mac_storages.end())
        {
            return mp4_uri;
        }
    }
    //限制同时视频封装的个数,防止cpu压力过大导致,ffmpeg子进程接受rtp、封装视频文件出错
    VideoStoragePtr storage_ptr;
    {
        std::lock_guard<std::mutex> lock(uid_storages_mutex_);
        std::size_t uid_nums = uid_storages_.size();
        if (uid_nums >= kUidNum_)
        {
            AK_LOG_WARN << "there are too many video storaging now";
            return mp4_uri;
        }
        VideoStorageListIter iter = uid_storages_.find(uid);
        if (iter == uid_storages_.end())
        {
            VideoStoragePtr storage_ptr(new CVideoStorage(uid, pwd, rtsp_ip, mac));
            //storage_ptr->Init();
            uid_storages_.insert(std::make_pair(uid, storage_ptr));
            storage_ptr->Start(mp4_uri, vid);
            mac_storages.insert(mac);
        }
    }

    AK_LOG_INFO << "request video storage successful, map uri is:" << mp4_uri;
    return mp4_uri;
}

int CStorageMng::StopWriteRtpToMP4(const std::string& uid)
{
    std::string mac_stop;
    {
        std::lock_guard<std::mutex> lock(uid_storages_mutex_);
        VideoStorageListIter iter = uid_storages_.find(uid);
        if (iter != uid_storages_.end())
        {
            AK_LOG_INFO << "kill ffmpeg process, uid is: " << iter->first;
            iter->second->Stop();
            mac_stop = iter->second->GetMac();
            uid_storages_.erase(iter);
        }
    }

    {
        std::lock_guard<std::mutex> lock(mac_storages_mutex_);
        mac_storages.erase(mac_stop);
    }

    return 0;
}

int CStorageMng::StopAllFfmpegProc()
{
    AK_LOG_INFO << "stop all ffmpeg processs";
    {
        std::lock_guard<std::mutex> lock(uid_storages_mutex_);
        VideoStorageListIter iter = uid_storages_.begin();
        for (; iter != uid_storages_.end();)
        {
            iter->second->Stop();
            uid_storages_.erase(iter++);
        }
    }
    ffmpeg_pids_.clear();
    return 0;
}

//巡检所有进程
//TODO 用最小堆实现对定时器的管理,不需要,数量不多
int CStorageMng::InspectAllFfmpegProc()
{
    //AK_LOG_WARN <<"begin to InspectAllFfmpegProc";
    time_t time_now = ::time(nullptr);
    std::string mac_stop;
    std::list<std::string> mac_stops;
    {
        std::lock_guard<std::mutex> lock(uid_storages_mutex_);
        VideoStorageListIter iter = uid_storages_.begin();
        for (; iter != uid_storages_.end();)
        {
            if (iter->second->IsOverTime(time_now))
            {
                //AK_LOG_WARN << "there is one ffmpeg process run over time, pid is [" << iter->second->GetPid() << "] uid is " << iter->second->GetUid();
                iter->second->Stop();
                mac_stop = iter->second->GetMac();
                mac_stops.push_back(mac_stop);
                uid_storages_.erase(iter++);
                continue;
            }
            iter++;
        }
    }

    {
        std::lock_guard<std::mutex> lock(mac_storages_mutex_);
        for (const auto& mac : mac_stops)
        {
            mac_storages.erase(mac);
        }
        //mac_stops.clear();
    }
    return 0;
}

//删除视频片段
int CStorageMng::DelVideoClip(const uint32_t vid)
{
    std::string video_uri;
    int ret = 0;
    ret = GlobalVieRecord::GetInstance()->DaoGetVideoUri(vid, video_uri);
    if (ret != 0)
    {
        //TODO,对于这种失败,应该引入MQ,将失败信息写入告警系统,运维人员及时介入
        AK_LOG_WARN << "get video uri failed, vid is: "  << vid;
        return -1;
    }

    //根据相对路径进行删除
    const std::string video_pre = "/video/";
    std::size_t video_pos = video_uri.find(video_pre);
    if (video_pos == std::string::npos)
    {
        AK_LOG_WARN << "del video failed, vid format is wrong: "  << vid;
        return -1;
    }
    std::string video_relative_uri = video_uri.substr(video_pos + video_pre.length());//偏移过"video/"
    std::string video_absolute_path = MP4_PATH;
    video_absolute_path += video_relative_uri;
    ::remove(video_absolute_path.c_str());
    return 0;
}

//添加ffmpeg进程
void CStorageMng::AddFfmpegProc(const pid_t pid)
{
    {
        std::lock_guard<std::mutex> lock(ffmpeg_pids__mutex_);
        ffmpeg_pids_.insert(pid);
    }
}

//删除ffmpeg进程
void CStorageMng::RemoveFfmpegProc(const pid_t pid)
{
    {
        std::lock_guard<std::mutex> lock(ffmpeg_pids__mutex_);
        ffmpeg_pids_.erase(pid); //不管是通过kill关闭,还是异常关闭，都会在子进程挂掉的时候,进入这个流程中.
    }
}

//杀掉个别超时的ffmpeg进程
int CStorageMng::KillFfmpegProc(const pid_t pid)
{
    {
        std::lock_guard<std::mutex> lock(ffmpeg_pids__mutex_);
        std::set<pid_t>::iterator iter = ffmpeg_pids_.find(pid);
        if (iter == ffmpeg_pids_.end())
        {
            //对于这种子进程意外死亡的,有需要通过MQ写入告警系统.
            //AK_LOG_WARN << "find pid of ffmpeg to kill failed, maybe it was terminated before, ffmpeg pid is: "  << pid;
            return 0;
        }
        ffmpeg_pids_.erase(iter);
    }
    AK_LOG_WARN << "kill ffmpeg, it was not terminated by ffmpeg (-t) timer successful before, ffmpeg pid is: "  << pid;
    //由SIGKILL改成SIGINT,因为用SIGKILL会直接结束ffmpeg进程,导致不优雅地关闭,mp4文件信息无法完美收尾
    if (::kill(pid, SIGINT) != 0)
    {
        AK_LOG_WARN << "kill ffmpeg process failed, pid is :[" << pid << "] errno is :" << errno;
        return -1;
    }
    return 0;

}

