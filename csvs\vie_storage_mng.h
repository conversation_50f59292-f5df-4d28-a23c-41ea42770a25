//author :chenyc
//file:  vie_storage_mng.h

#ifndef __CSSTORAGE_STORAGE_MNG_H__
#define __CSSTORAGE_STORAGE_MNG_H__

#include <string>
#include <boost/noncopyable.hpp>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <map>
#include <set>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <dirent.h>

class CVideoStorage;
typedef std::shared_ptr<CVideoStorage> VideoStoragePtr;
typedef std::map<std::string, VideoStoragePtr> VideoStorageList;
typedef VideoStorageList::iterator VideoStorageListIter;
const std::string g_ffmpeg_bin = "/usr/local/akcs/csvs/ffmpeg/bin/ffmpeg"; //
//const char video_data_dir[] = "/home/<USER>";

//h264裸数据存储对象,每个视频存储都有一个该临时对象为之服务.
class CVideoStorage
{
public:
    CVideoStorage(const std::string uid, const std::string& pwd, const std::string& rtsp_ip, const std::string& mac)
        : fp(nullptr),
          uid_(uid),
          rtsp_pwd_(pwd),
          rtsp_srv_ip_(rtsp_ip),
          ffmpeg_pid_(0),
          time_start_(0),
          mac_(mac)
    {

    }
    ~CVideoStorage();
    int Start(std::string& hls_uri, uint32_t& video_id);
    int Stop();

    //暂定最长30s
    bool IsOverTime(time_t time);
    pid_t GetPid();
    std::string GetMac();
    std::string GetUid();
private:
    FILE* fp;
    std::string uid_;
    std::string rtsp_pwd_;
    std::string rtsp_srv_ip_;
    pid_t ffmpeg_pid_;
    time_t time_start_;
    std::string mac_;
    std::string hash_str_;
};

//所有视频存储时间的管理器
class CStorageMng : public boost::noncopyable
{
public:
    typedef std::set<std::string> MacList;
    typedef MacList::iterator MacListIter;

public:
    CStorageMng();
    ~CStorageMng();
    int Init();
    std::string StartWriteRtpToMP4(const std::string& uid,
                                   const std::string& pwd,
                                   const std::string& rtsp_ip,
                                   std::string& mac,
                                   uint32_t& vid);
    int StopWriteRtpToMP4(const std::string& uid);
    int StopAllFfmpegProc();
    int InspectAllFfmpegProc();
    int DelVideoClip(const uint32_t vid);
    void RemoveFfmpegProc(const pid_t pid);
    int KillFfmpegProc(const pid_t pid);
    void AddFfmpegProc(const pid_t pid);

private:
    //H264ToTs(const std::string& h264_file)

private:
    std::mutex uid_storages_mutex_;
    VideoStorageList uid_storages_;
    std::mutex mac_storages_mutex_;
    MacList mac_storages; //已经在录制的设备列表
    const static uint32_t kUidNum_ = 150;
    std::mutex ffmpeg_pids__mutex_;
    std::set<pid_t> ffmpeg_pids_;
};

#endif //__CSSTORAGE_STORAGE_MNG_H__

