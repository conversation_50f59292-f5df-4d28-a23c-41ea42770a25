//author :chenyc
//storage_ser.h

#ifndef __CSVS_STORAGE_SER_H__
#define __CSVS_STORAGE_SER_H__

#define CSSTORAGE_CONF_COMMON_LEN 64

typedef struct AKCS_CONF_T
{
    /* csstorage本机配置信息 */
    char szStorageOuterIP[CSSTORAGE_CONF_COMMON_LEN];

    /* DB配置项 */
    char szDbIP[CSSTORAGE_CONF_COMMON_LEN];
    char szDbUserName[CSSTORAGE_CONF_COMMON_LEN];
    char szDbPassword[CSSTORAGE_CONF_COMMON_LEN];
    char szDbDatabase[CSSTORAGE_CONF_COMMON_LEN];
    int  nDbPort;
    int  nVideoLength;

} AKCS_CONF;


#endif  //__CSVS_STORAGE_SER_H__