##########################################################################################
## (C)Copyright 2020-2030 Akuvox .Ltd 
##
##########################################################################################

# CMake 最低版本号要求
cmake_minimum_required (VERSION 3.16)
set (CMAKE_CXX_STANDARD 11)

# 项目信息
project (FaceDetectV2)

# 包含所需的头文件
SET(NEED_THIRDLIB_INC ${CMAKE_CURRENT_SOURCE_DIR}/
				${CMAKE_CURRENT_SOURCE_DIR}/thirdlib/facesdk_open/include/
				${CMAKE_CURRENT_SOURCE_DIR}/thirdlib/opencv/include/
				${PROJECT_SOURCE_DIR}/thirdlib/ncnn/include/ncnn/)
include_directories(${NEED_THIRDLIB_INC})

# 包含第三方库
link_directories(${PROJECT_SOURCE_DIR}/thirdlib/facesdk_open/lib/
				${PROJECT_SOURCE_DIR}/thirdlib/opencv/lib/
				${PROJECT_SOURCE_DIR}/thirdlib/ncnn/lib/)
SET(NEED_THIRDLIB_LIB 	facesdk
				opencv_highgui opencv_imgproc opencv_core
				zlib libjasper libjpeg libpng
				pthread
				ncnn)

# 查找目录下的所有源文件
# 并将名称保存到 DIR_LIB_SRCS 变量
aux_source_directory(. DIR_SRCS)

# 指定生成目标
add_executable(FaceDetectV2 ${DIR_SRCS})

# 添加链接库
target_link_libraries(FaceDetectV2
						z
						${NEED_THIRDLIB_LIB})
