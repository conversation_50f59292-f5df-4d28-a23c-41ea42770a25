##########################################################################################
## (C)Copyright 2020-2030 Akuvox .Ltd 
##
##########################################################################################

#!/bin/bash

PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
echo "-------- start compile"
if [ -f $PWD/../src/thirdlib/facesdk_open/x64/lib/libfacesdk.a ]; then
	echo "-------- find libfacesdk.a!"
fi

cp $PWD/CMakeLists.txt $PWD/../src/CMakeLists.txt
if [ -f $PWD/../src/CMakeLists.txt ]; then
	cmake ../src
	make -j 4

	for x in CMakeCache.txt cmake_install.cmake Makefile CTestTestfile.cmake; do
		if [ -f $PWD/$x ]; then
			rm -rf $x
		fi
	done

	for x in thirdlib modulelib CMakeFiles; do
		if [ -d $PWD/$x ];then
			rm -rf $x
		fi
	done

	if [ -f $PWD/FaceDetectV2 ]; 
		then
        cp $PWD/../test.png $PWD/test.png
        rm -f $PWD/../src/CMakeLists.txt
		echo "-------- compile success!"

	else
		echo "-------- compile failed!"
	fi
else
	echo "-------- Not find any cmake file!"
fi
