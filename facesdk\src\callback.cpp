#include "callback.h"

ICallBack :: ICallBack(){};

ICallBack :: ~ICallBack(){};

int ICallBack :: Notify	(const int msgID, const int paramA, const int paramB, const char*paramC) {
	printf("[%s]msgID:%d, paramA:%d, paramC:%s \n", __FUNCTION__, msgID, paramA, paramC);
    msg_id_ = msgID;
    if (msgID == MSG_ID_FACESDK_CHECK_FACESIZE)
    {
        param_a_ = paramA;
    }
    param_b_ = paramB;
    param_c_ = paramC;
	return msgID;
}

int ICallBack :: GetMsgID(){
    return msg_id_;
}

int ICallBack :: GetParamA(){
    return param_a_;
}

int ICallBack :: GetParamB(){
    return param_b_;
}

std::string ICallBack :: GetParamC(){
    return param_c_;
}