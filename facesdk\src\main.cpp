#include <iostream>
#include <unistd.h>
#include "revision.h"
#include "facesdk_config.h"
#include "facesdk_callback.h"
#include "facesdkAPI.h"
#include "callback.h"
#include "opencv2/opencv.hpp"

int main(int argc, char *argv[]) {
    //-------------------------------------------单张注册------------------------------
    std::string loadImgName = argv[1];
    const char* file_save_path = argv[2];

    printf("#### hmhm src: %s\n", loadImgName.c_str());
    size_t found =  loadImgName.find_last_of('.');
    if (found == std::string::npos)
    {
        return -1;
    }
    
    std::string prefix = loadImgName.substr(0 ,found);
    std::string suffix = loadImgName.substr(found+1);

    ICallBack *cb = new ICallBack();

    FacesdkAPI_InitEngine("/NoNeed/", cb);


    FacesdkConfig cfg;
    strcpy(cfg.cfgFaceInfoSavePath, file_save_path);
    FacesdkAPI_UpdateConfig(cfg);

    cv::Mat img = cv::imread(loadImgName);
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    int ret = FacesdkAPI_DoRegFaceSingle(img.data,
        img.cols,
        img.rows);

    int result = cb->GetParamA();
    //人脸sdk裁剪后的默认时png格式的，需要自行转换
    std::string default_file = cb->GetParamC() + ".png";
    
    unlink(loadImgName.c_str());
    ::rename(default_file.c_str(), loadImgName.c_str());
    
    printf("Ret: %d\n", ret);

    switch (ret)
    {
        case 1:
            ret = UPLOAD_FACEPIC_SUCCESS;
            break;
        case 0:
            ret = UPLOAD_FACEPIC_ERROR_NO_FACE;
            break;
        case MSG_ID_FACESDK_CHECK_FACENUM:
            ret = UPLOAD_FACEPIC_ERROR_MULTI_FACES;
            break;
        case MSG_ID_FACESDK_CHECK_FACESIZE:
            if (result == CHECK_FACESIZE_LARGE)
            {
                ret = UPLOAD_FACEPIC_ERROR_FACE_LARGE;
            }
            else if (result == CHECK_FACESIZE_SMALL)
            {
                ret = UPLOAD_FACEPIC_ERROR_FACE_SMALL;
            }
            break;
        case MSG_ID_FACESDK_CHECK_FACEPOSE:
            ret = UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW;
            break;
        case MSG_ID_FACESDK_CHECK_FACEQUALITY:
            ret = UPLOAD_FACEPIC_ERROR_NOT_CLEAR;
            break;
        case MSG_ID_FACESDK_CHECK_FACEMASK:
            ret = UPLOAD_FACEPIC_ERROR_WEAR_MASK;
            break;
        default:
            break;
    }

    FacesdkAPI_DeinitEngine();
    return ret;
}
