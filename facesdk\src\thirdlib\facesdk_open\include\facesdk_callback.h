/**
 * 
 * Akuvox自研的人脸识别SDK
 * Akuvox Lisence
 * 
 * By LinKy
 * 2018-06-22
 */

/* Header for Facesdk CALLBACK */

#ifndef __FACESDK_CALLBACK_H__
#define __FACESDK_CALLBACK_H__

enum {
    FACESDK_LOG_LEVEL_ERROR = 3,
    FACESDK_LOG_LEVEL_WARN  = 4,
    FACESDK_LOG_LEVEL_INFO  = 6,
    FACESDK_LOG_LEVEL_DEBUG = 7,
};

enum {
    CHECK_FACESIZE_SMALL    =  -1,
    CHECK_FACESIZE_RIGHT    =   0,
    CHECK_FACESIZE_LARGE    =   1,
};

#define MSG_ID_FACESDK_LOG_RESULT            0x4001
#define MSG_ID_FACESDK_SAVE_RESULT           0x4002

#define MSG_ID_FACESDK_CHECK_FACENUM         0x5001
#define MSG_ID_FACESDK_CHECK_FACESIZE        0x5002
#define MSG_ID_FACESDK_CHECK_FACEPOSE        0x5003
#define MSG_ID_FACESDK_CHECK_FACEQUALITY     0x5004
#define MSG_ID_FACESDK_CHECK_FACEMASK        0x5005

enum {
    UPLOAD_FACEPIC_ERROR_SYSTEM = -1,      //System Error：系统错误，包括解码失败，重命名图片失败等
    UPLOAD_FACEPIC_SUCCESS = 0,      
    UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW = 100,      //Not front view：人脸的旋转角度 或俯视、仰视、侧脸的角度过大
    UPLOAD_FACEPIC_ERROR_WEAR_MASK = 101,      //Mask detected：检测到口罩     
    UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION = 102,      //Resolution is too low.：人脸分辨率太小
    UPLOAD_FACEPIC_ERROR_WRONG_FORMAT = 103,      //File format error.：人脸格式错误
    UPLOAD_FACEPIC_ERROR_NO_FACE= 104,      //No face dectected.：图片中未检测到人脸
    UPLOAD_FACEPIC_ERROR_FILE_LARGE = 105,      //The file is too larger：图片大于10MB
    UPLOAD_FACEPIC_ERROR_FACE_LARGE = 106,      //The face is too larger.：图片中人脸过大
    UPLOAD_FACEPIC_ERROR_FACE_SMALL= 107,     //The face is too small：图片中人脸过小
    UPLOAD_FACEPIC_ERROR_MULTI_FACES= 108,      //More than one face：图片中人脸不止1个
    UPLOAD_FACEPIC_ERROR_NOT_CLEAR= 113      //Face not clear enough.: 图片中人脸不清晰
};




/* API Adatper 接口命名遵循Java规范 */
class FacesdkCallBack {
public:
    virtual ~FacesdkCallBack() {}

    /*
     * msgID
     * paramA       - 人脸识别结果数量
     * paramB       - 无, 预留给校验码
     * paramC        - 消息ID及参数, 具体含义详见上方宏定义
     * return        - 0表示清空成功, 其他表示出错
     */
    virtual int Notify(const int msgID, const int paramA, const int paramB, const char*paramC) = 0;
};


#endif

