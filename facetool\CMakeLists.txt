CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (Face C CXX)
SET(DEPENDENT_LIBRARIES libfacesdk_cloud.a libncnn.a libade.a libopencv_core.a libopencv_features2d.a libopencv_gapi.a libopencv_imgcodecs.a libopencv_ml.a libopencv_photo.a libopencv_ts.a libopencv_videoio.a libopencv_calib3d.a libopencv_dnn.a libopencv_flann.a libopencv_highgui.a libopencv_imgproc.a libopencv_objdetect.a libopencv_stitching.a libopencv_video.a libncnn.a libdl.so libz.so -fopenmp libittnotify.a liblibpng.a libIlmImf.a liblibwebp.a liblibjasper.a liblibtiff.a libjpeg.a)
LINK_DIRECTORIES(include/facesdk_cloud/lib include/ncnn/lib include/opencv/lib include/opencv/lib/3rdparty)

AUX_SOURCE_DIRECTORY(./ SRC_LIST_ROUTE)
SET(BASE_LIST_INC include/facesdk_cloud/include include/facesdk_cloud/include/modulelib/detect_face include/facesdk_cloud/include/modulelib/detect_mask include/facesdk_cloud/include/modulelib/recognize_face include/ncnn/include  include/opencv/include)

ADD_DEFINITIONS( -fopenmp -std=c++11 -g -W -Wall)
                           
include_directories( ${BASE_LIST_INC})
add_executable(FaceDetect ${SRC_LIST_ROUTE} )
target_link_libraries(FaceDetect  ${DEPENDENT_LIBRARIES})
