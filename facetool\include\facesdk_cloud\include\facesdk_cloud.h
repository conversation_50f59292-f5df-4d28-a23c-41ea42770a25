
#ifndef __FACESDK_CLOUD_H__
#define __FACESDK_CLOUD_H__

#include "opencv2/opencv.hpp"

#include "facesdk_cloud_defs.h"
#include "faceAlign.h"
#include "detect_face.h"
#include "detect_mask.h"
#include "recognize_face.h"

class FacesdkCloud {
public:
	FacesdkCloud();
	~FacesdkCloud();

	/*
	 * [主线程]初始化引擎, 有二次调用保护
	 *
	 * pAllModelsRootPath			    - 所有模型文件的根目录, SDK会自行选择加载
	 * return                           - 0表示成功,
	 *                                  - -1表示初始化异常
	 */
	int InitEngine(const char* pAllModelsRootPath);

	/*
	 * [主线程]注销引擎, 有二次调用保护
	 *
	 * return			- 0表示成功,
	 *					- -1表示注销异常
	 */
	int DeinitEngine();

	/*
	 * [主线程]人脸检测，保存裁剪数据
	 *
	 * strSrcPath			- 人脸检测图片源地址,
	 * strDstPath			- 人脸裁剪图保存地址,
	 * saveType				- 保存类型 
	 *							- 0表示裁剪人脸
	 *							- 1表示对齐人脸
	 *							- 0表示不做任何操作
	 * outputW				- 人脸裁剪图的宽,
	 * outputH				- 人脸裁剪图的高,
	 * return				- 1表示裁剪成功,
	 *							-  0表示未检测到人脸
	 *							- -1表示模型未初始化或者源地址图片异常
	 *							- -2人脸检测结果-比例不符合规范
	 *							- -3人脸检测结果-姿态不符合规范
	 *							- -4人脸检测结果-清晰度不符合规范
	 *							- -5人脸检测结果-口罩遮挡不符合规范
	 */
	int DoFaceDeal(std::string strSrcPath, 
		std::string strDstPath, 
		std::string featurePath, 
		const int saveType,
		const int outputW, 
		const int outputH);


	float GetFaceSimilarity(const float *f1, const float *f2);

private:
	// 模型预热
	int __PreheatAllModel();

	//人脸口罩校验
	int __FaceMaskCheck(cv::Mat img);

	//人脸姿态校验
	int __FacePoseCheck(cv::Mat img,
		FaceInfo faceInfo);
	
	//人脸清晰度校验
	int __FaceQualityCheck(cv::Mat img,
		FaceInfo faceInfo);

	//人脸尺寸校验
	int __FaceSizeCheck(cv::Mat img,
		FaceInfo faceInfo);
	
	//图片大小调整
	int __doImgResize(cv::Mat &img, 
		const int resizeW, 
		const int resizeH, 
		const bool keepScale);

	//人脸图片裁剪
	int __doImgCropWithSize(cv::Mat &img,
		FaceInfo faceInfo,
		const int resizeW,
		const int resizeH);
	
	//获取人脸特征
	int __GetFaceFeature(cv::Mat img,
		float *features);
	
	//保存特征到本地
	int __SaveFaceFeatures(std::string filename,
		float *features);
	
	

private:
	int                                 m_init;

	DetectFaceModule*					m_pDetectFaceModule;
	DetectMaskModule*					m_pDetectMaskModule;
	RecognizeFaceModule*				m_pRecognizeFaceModule;

	FaceAlign*							m_pFaceAlign;
	std::vector<FaceInfo>				m_faces;
};

#endif