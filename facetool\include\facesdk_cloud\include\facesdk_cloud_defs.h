#ifndef __FACESDK_CLOUD_DEFS_H__
#define __FACESDK_CLOUD_DEFS_H__

#define TRUE	1
#define FALSE	0

#ifndef MIN
#  define MIN(a,b)  ((a) > (b) ? (b) : (a))
#endif

#ifndef MAX
#  define MAX(a,b)  ((a) < (b) ? (b) : (a))
#endif

enum {
	FACE_STATUS_ERR_BAD_MASK		= -5,
	FACE_STATUS_ERR_BAD_QUALITY		= -4,
	FACE_STATUS_ERR_BAD_POSE		= -3,
	FACE_STATUS_ERR_BAD_SIZE		= -2,
	FACE_STATUS_ERR_INIT			= -1,
	FACE_STATUS_RET_FAIL			=  0,
	FACE_STATUS_RET_FINISH			=  1,
};

enum {
	FACE_SAVE_TYPE_CROP				= 0,
	FACE_SAVE_TYPE_ALIGN			= 1,
	FACE_SAVE_TYPE_ORIG				,
};

#define FACE_KEYPOINTS_NUMS							5		//对输入图像检测的人脸关键点数目
#define KEEP_RESIZE_SCALE							1		//对输入图像进行缩放的参数

//对输入图像的尺寸要求
#define IMG_INPUT_CHANNELS							3
#define IMG_INPUT_RESIZE_W							480
#define IMG_INPUT_RESIZE_H							480

#define LIMIT_MAX_FACE_NUMS							1		//对同一图中检测人脸个数的限制

#define LIMIT_FACE_ROLL_ANGLE_MAX					45  //人脸最大倾斜角度限制
#define LIMIT_FACE_YAW_ANGLE_MAX					30  //人脸最大水平角度限制
#define LIMIT_FACE_PITCH_ANGLE_MAX					20  //人脸最大俯仰角度限制
#define LIMIT_FACE_EYE_SPAN_MIN						60  //人脸最小眼间距限制

//人脸检测阈值
#define FACEID_FACE_DETECT_DEF                      0.4f // start from v4.1.0.114

#define LIMIT_MIN_FACE_AREA							6400	//对同一图中检测人脸大小的限制
#define LIMIT_MAX_FACE_SCALE						0.67	//对同一图中检测人脸比例的限制

//对检测出的人脸进行质量校验
#define DO_FACE_QUALITY_CHECK						1
#define IMG_QUALITY_THRESHOLD						100

//对输出人脸裁剪图像进行缩放的参数
#define FACE_CROP_OUTPUT_W							480
#define FACE_CROP_OUTPUT_H							480
#define IMG_PROCESS_SAVE_PADDING_SCALE				1.67

//对人脸特征序列个数的限制
#define LIMIT_MIN_FEATURE_NUMS                  3
#define LIMIT_MAX_FEATURE_NUMS                  5

//对人脸特征序列中各特征相互最低相似度的限制
#define LIMIT_MIN_SIM_TH                        0.85

//人脸特征长度
#define RECOGNIZE_FACE_FEATURE_SIZE				128

// test defined ------------------------------------------------------------
#define TEST_LOCAL                          0
#define TEST_SAVE_IMG                       0
#define TEST_SHOW_TIME                      0

#endif