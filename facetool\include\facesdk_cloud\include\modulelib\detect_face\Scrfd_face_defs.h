#ifndef _SCRFD_FACE_DEFS_H_
#define _SCRFD_FACE_DEFS_H_

#include "net.h"
#include "opencv2/opencv.hpp"

#define SCRFD_INPUT_W								480        // %32==0
#define SCRFD_INPUT_H							    480        // %32==0
#define FACE_DETECT_SCRFD_FMC						3

#define DETECT_FACE_NCNN_RUNTIME_THREADS			2
#define DETECT_FACE_NCNN_LIGHT_MODE 				true

#define SCRFD_NCNN_RUNTIME_THREADS					1
#define SCRFD_NCNN_LIGHT_MODE						true


struct scale_window {
	int h;
	int w;
	float scale;
};

struct face_landmark {
	float x[5];
	float y[5];
};

struct face_box {
    float score;
    
    float x0;
    float y0;
    float x1;
    float y1;
    
    
    face_landmark landmark;
};

#endif