#ifndef __SCRFD_FACE_IMPL_H__
#define __SCRFD_FACE_IMPL_H__

#include "detect_face.h"
#include "Scrfd_face_defs.h"
#include "net.h"

#include <vector>
#include <mutex> 

class ScrfdFaceMoudle : public DetectFaceModule{
public:
	ScrfdFaceMoudle();
	virtual ~ScrfdFaceMoudle();

	/*
     * 加载模型, 有二次调用保护
     *
     * pModelPath    		 			- 模型路径, 已弃用
     * return                           - 0表示成功, 
     *                                  - -1表示加载失败
     */
	virtual int LoadModel(const char *pModelPath = NULL);

	/*
    * 获取当前图像的人脸区域, 并返回相应坐标
    *
    * img            				- 人脸图像
    * faces           				- 人脸检测信息
    * cfgLimitFace         			- 限制同屏人脸数量
    * cfgDetectThreshold  			- 人脸检测阈值
    * 
    * return                        - 检测到的人脸数量, -1表示检测过程出错
    */
    virtual int DetectFaces(cv::Mat img, 
            std::vector<FaceInfo> &faces,
            const float cfgDetectThreshold,
            const int cfgLimitFace);

private:
	int __ImgDeal(cv::Mat &img);

    void __RunScrfdNet(ncnn::Mat& img, std::vector<face_box>& faceobjects);

	void __NMSBoxes(std::vector<face_box> &input, float threshold, std::vector<face_box> &output);

    void __GenerateAnchor(int base_size, 
            const ncnn::Mat& ratios, 
            const ncnn::Mat& scales, 
            std::vector<ncnn::Mat>& anchors);

private:
	int											m_init;
	float										m_resizeX;
	float										m_resizeY;

	// SCRFD objs
    ncnn::Net                                   m_SCRFDNet;
    float                                       m_SCRFDProbThreshold;
    const float                                 m_SCRFDNmsThreshold;
    float                                       *m_mean_vals;
    float                                       *m_norm_vals;
	int										    *class_layer_idx;
	int										    *bbox_layer_idx;
	int										    *landmark_layer_idx;	
    std::vector<ncnn::Mat>                      *m_anchorsGenerated;
    int                                         *m_RPN;


};

#endif


