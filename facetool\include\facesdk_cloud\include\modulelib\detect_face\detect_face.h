/**
 * 
 * Akuvox自研的人脸检测模块
 * Akuvox Lisence
 * 
 * By LinKy
 * 2018-07-26
 */

/* header for detect face interface */

#ifndef __DETECT_FACE_H__
#define __DETECT_FACE_H__

#include "opencv2/opencv.hpp"
typedef cv::Rect_<float> Rect2f;

typedef struct DetectFaceInfo{
    Rect2f bbox;
    std::vector<cv::Point2f> landmarks;
    float score;
    cv::Mat data_v3;
    cv::Mat data_v4;
    cv::Mat dataIR_v3;
    cv::Mat dataIR_v4;
    int aliveStatus = 1;
    int maskStatus = 0;
}FaceInfo;

class DetectFaceModule {
public:
    virtual ~DetectFaceModule() {}

    /*
    * 加载模型, 有二次调用保护
    *
    * pModelPath    		 			- 模型路径, 该传参根据实际情况看是否允许为空
    * return                           - 0表示成功, 
    *                                  - -1表示加载失败
    */
    virtual int LoadModel(const char *pModelPath) = 0;

    /*
    * 获取当前图像的人脸区域, 并返回相应坐标
    *
    * img            				- 人脸图像
    * faces           				- 人脸检测信息
    * cfgLimitFace         			- 限制同屏人脸数量
    * cfgDetectThreshold  			- 人脸检测阈值
    * 
    * return                        - 检测到的人脸数量, -1表示检测过程出错
    */
    virtual int DetectFaces(cv::Mat img, 
            std::vector<FaceInfo> &faces,
            const float cfgDetectThreshold,
            const int cfgLimitFace) = 0;

};


#endif


