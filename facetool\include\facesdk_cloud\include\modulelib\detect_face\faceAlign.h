#ifndef _FACE_ALIGN_H_
#define _FACE_ALIGN_H_

#include <opencv2/opencv.hpp>

#define PI						3.1415926f
#define SEMI_CIRCLE				180.0f

class FaceAlign {
public:
    FaceAlign();
    ~FaceAlign();
    int Align(cv::Mat img_src, std::vector<cv::Point2f> &keypoints, cv::Mat& img_transformer);
    //获取倾斜角度
	int GetRollAngle(const std::vector<cv::Point2f> keypoints);
	//获取侧脸角度
	int GetYawAngle(const std::vector<cv::Point2f> keypoints);
	//获取俯仰角度
	int GetPitchAngle(const std::vector<cv::Point2f> keypoints);
    //旋转图片
	int Rotation3D(cv::Mat img_src, 
		const std::vector<cv::Point2f> keypoints, 
		cv::<PERSON>& img_transformer,
		const bool isAlign = true);
private:
    cv::<PERSON>0(const cv::Mat &src);
    cv::<PERSON>(const cv::Mat &A, const cv::Mat &B);
    cv::<PERSON>0(const cv::Mat &src);
    int MatrixRank(cv::Mat M);
    cv::Mat SimilarTransform(const cv::Mat& src, const cv::Mat& dst);

    float points_dst[5][2] = {
        { 30.2946f + 8.0f, 51.6963f },
        { 65.5318f + 8.0f, 51.5014f },
        { 48.0252f + 8.0f, 71.7366f },
        { 33.5493f + 8.0f, 92.3655f },
        { 62.7299f + 8.0f, 92.2041f }

        //{ 30.2946f, 51.6963f },
        //{ 65.5318f, 51.5014f },
        //{ 48.0252f, 71.7366f },
        //{ 33.5493f, 92.3655f },
        //{ 62.7299f, 92.2041f }
    };
};


#endif