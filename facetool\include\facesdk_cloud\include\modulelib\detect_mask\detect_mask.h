/*
 * 
 * Akuvox自研的活体检测模块 基于IR镜头
 * Akuvox Lisence
 * 
 * By minzhe.huang
 * 2020-07-01
 */

/* header for detect alive interface */

#ifndef __DETECT_MASK_H__
#define __DETECT_MASK_H__

#include "opencv2/opencv.hpp"
#include "detect_face.h"

class DetectMaskModule {
public:
    virtual ~DetectMaskModule() {}

    /*
     * 加载模型, 有二次调用保护
     *
     * pModelPath    		 			- 模型路径, 该传参根据实际情况看是否允许为空
     * return                           - 0表示成功, 
     *                                  - -1表示加载失败
     */
    virtual int LoadModel(const char *pModelPath) = 0;


     /*
     * 获取当前图像的口罩区域, 并返回相应坐标
     *
     * img           					- 图像数据
     * faces           					- 检测到的人脸信息
     * cfgLimitFace         			- 人脸检测数量最大值
     * return                           - 检测到的人脸数量, -1表示检测过程出错
     */
    virtual int GetMasks(cv::Mat img, 
        std::vector<FaceInfo> &faces,
        const int cfgLimitFace) = 0;
    
    /*
     * 获取当前自带的口罩图片
     *
     * imgMask           			    - 口罩图像
     */
    virtual int GetMask(cv::Mat &imgMask) = 0;

    /*
     * 获取当前自带的口罩图片
     *
     * imgMask           			    - 口罩图像
     */
    virtual int PutMask(cv::Mat &img, cv::Mat &imgMask) = 0;

};


#endif


