#ifndef _FACE_MASK_SSD_IMPL_H_
#define _FACE_MASK_SSD_IMPL_H_

#include "detect_mask.h"
#include "faceMaskSSD_defs.h"
#include "faceMaskSSD_anchor.h"

#include "net.h"
#include <vector>
#include <ctime>

typedef struct DetectMaskPerNCNNOutInfo{
    ncnn::Mat out;
    int outLayerIdx;
    float * outData;
    int outSize;
    cv::Mat outMat;
}NCNNOutInfo;

class FaceMaskSSD : public DetectMaskModule{
public:

    FaceMaskSSD();
    virtual ~FaceMaskSSD();
    virtual int LoadModel(const char *pModelPath = NULL);
    virtual int GetMasks(cv::Mat img, 
        std::vector<FaceInfo> &faces,
        const int cfgLimitFace);

    virtual int GetMask(cv::Mat &imgMask);

    virtual int PutMask(cv::Mat &img, cv::Mat &imgMask);
    
private:
    int __ImgDeal(cv::Mat &img);

    cv::Mat __GetNCNNOutInfo(ncnn::Extractor &extractor, 
        std::vector<NCNNOutInfo> &outNets, 
        int &capacity,
        int sw);

    int __FaceMaskSSDNMS(std::vector<FaceInfo> facesTmp,
        std::vector<FaceInfo> &faces,
        float m_ThresholdIOU);

private:
    int											m_init;
    ncnn::Net                                   m_NCNNNet;
    float                                      *m_mean_vals;
    float                                      *m_norm_vals;
    int 										m_offsetX;
    int 										m_offsetY;
    float 										m_resizeX;
    float 										m_resizeY;
    int											m_fillX;
    int											m_fillY;
    float									    m_ThresholdIOU;
    float									    m_ThresholdScore;
    float										m_E;
    float									   *m_variances;
    float									   *m_anchorCentersX;
    float									   *m_anchorCentersY;
    float									   *m_anchorW;
    float									   *m_anchorH;
    float									  **m_outRescale;
    float									   *m_perdictCenterX;
    float									   *m_perdictCenterY;
    float									   *m_perdictW;
    float									   *m_perdictH;

    //mask相关
    cv::Mat                                     m_imgMask;

};



#endif