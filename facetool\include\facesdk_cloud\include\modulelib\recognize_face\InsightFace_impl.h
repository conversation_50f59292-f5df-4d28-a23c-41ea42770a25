/**
 * 
 * Akuvox自研的人脸检测模块 - InsightFace方案实现
 * Akuvox Lisence
 * 
 * By LinKy
 * 2019-05-09
 *
 * Copyright (c) 2018 Akuvox
 * 
 *
 
 @inproceedings{guo2018stacked,
   title={Stacked Dense U-Nets with Dual Transformers for Robust Face Alignment},
   author={<PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>},
   booktitle={BMVC},
   year={2018}
 }
 @inproceedings{deng2018arcface,
 title={ArcFace: Additive Angular Margin Loss for Deep Face Recognition},
 author={<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>},
 booktitle={CVPR},
 year={2019}
 }

 *
 */

/* header for InsightFace interface, depends on ncnn */

#ifndef __INSIGHTFACE_IMPL_H__
#define __INSIGHTFACE_IMPL_H__

#include "recognize_face.h"
#include "InsightFace_defs.h"

#include <pthread.h>
#include "net.h"


class InsightFaceImpl : public RecognizeFaceModule {
public:
    InsightFaceImpl();
    virtual ~InsightFaceImpl();

    /*
     * 加载模型, 有二次调用保护
     *
     * pModelPath    		 			- 模型路径, 已弃用
     * return                           - 0表示成功, 
     *                                  - -1表示加载失败
     */
    virtual int LoadModel(const char *pModelPath = NULL);

      //预热模型
    virtual int WarmUp();
    /*
     * 获取当前人脸的特征
     *
     * pModelDesc    		 			- pRGBData数据
     * features                         - 浮点型的特征向量, 维度为1x128
     *
     * return                           - 0表示成功, 
     *                                  - -1表示加载失败
     */
    virtual int GetFaceFeature(cv::Mat pRGBData, float *features);

    /*
     * 获取两个人脸特征之间的余弦夹角
     *
     * pModelDesc    		 			- pRGBData数据
     * f1                               - 浮点型的特征向量, 维度为1x128
     * f2                               - 浮点型的特征向量, 维度为1x128
     *
     * return                           - 两个向量夹角, 若f1/f2维度不相同, 则会导致挂掉
     */
    virtual float GetFaceSimilarity(const float *f1, const float *f2);

private:
    void __ReleaseNCNNObjs();

    int __GetFaceFeature(ncnn::Net &net, 
            cv::Mat pRGBData, 
            float features[INSIGHTFACE_FEATURE_SIZE]);
    
private:
    int										      	m_init;
    pthread_mutex_t								m_mutex;

    // NCNN objs
    ncnn::Net                                   m_NCNNNet;
};

#endif


