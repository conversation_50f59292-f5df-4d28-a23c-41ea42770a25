/**
 * 
 * Akuvox自研的人脸识别模块
 * Akuvox Lisence
 * 
 * By LinKy
 * 2019-05-09
 */

/* header for recognize face interface */

#ifndef __RECOGNIZE_FACE_H__
#define __RECOGNIZE_FACE_H__

#include "opencv2/opencv.hpp"

class RecognizeFaceModule {
public:
	virtual ~RecognizeFaceModule() {}

	/*
     * 加载模型, 有二次调用保护
     *
     * pModelPath    		 			- 模型路径, 该传参根据实际情况看是否允许为空
     * return                           - 0表示成功, 
     *                                  - -1表示加载失败
     */
	virtual int LoadModel(const char *pModelPath) = 0;


     //模型预热
     virtual int WarmUp() = 0;

	/*
     * 获取当前人脸的特征
     *
     * pModelDesc    		 			- pRGBData数据
     * features                         - 浮点型的特征向量, 每个实现最好注明出维度
     *
     * return                           - 0表示成功, 
     *                                  - -1表示加载失败
     */
	virtual int GetFaceFeature(cv::Mat pRGBData, float *features) = 0;

    /*
     * 获取两个人脸特征之间的相似度
     *
     * pModelDesc    		 			- pRGBData数据
     * f1                               - 浮点型的特征向量, 每个实现最好注明出维度
     * f2                               - 浮点型的特征向量, 每个实现最好注明出维度
     *
     * return                           - 两个特征向量的相似度或距离, 具体由实现者决定
     */
	virtual float GetFaceSimilarity(const float *f1, const float *f2) = 0;
};


#endif


