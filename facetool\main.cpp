#include <iostream>
#include "facesdk_cloud_defs.h"
#include "facesdk_cloud.h"
#include "revision.h"
#include <regex>
#include <dirent.h>
#include <sys/types.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <stdio.h>
#include "opencv2/opencv.hpp"

#if TEST_LOCAL
#include <sstream>
#include <fstream>
#include <stdio.h>
#include <stdlib.h>
const std::string preHead = "./";
const std::string readImgName = "test.png";
const std::string writeImgName = "save.png";
const std::string writeFeatureName = "features.txt";
#endif

static FacesdkCloud facesdkCloud;

void getFiles(const char *path, std::vector<std::string>& files){
    std::string path0 = path;
    path0 +="/";
    DIR* pDir;
    struct dirent* ptr;

    struct stat s;
    lstat(path, &s);

    if(!S_ISDIR(s.st_mode)){
        std::cout << "not a valid directory: " << path << std::endl;
        return;
    }

    if(!(pDir = opendir(path))){
        std::cout << "opendir error: " << path << std::endl;
        return;
    }

    std::string subFile;
    while((ptr = readdir(pDir)) != 0){
        subFile = ptr -> d_name;
        if(subFile == "." || subFile == "..")
            continue;
        subFile = path0 + subFile;
        std::cout  << subFile << std::endl;
        files.push_back(subFile);
    }
    closedir(pDir);
}

//初始化所有模型
void initAllModel(){
   
    facesdkCloud.InitEngine("/tmp/");
   
}

//获取人脸特征序列的最佳索引
//featuresList      人脸特征序列
int getBestFaceIdx(std::vector<float*> &featuresList){
    if((int)featuresList.size() == 0){
        return -1;
    }

    if((int)featuresList.size() < LIMIT_MIN_FEATURE_NUMS){
        return 0;
    }

    int maxFeaturesNum = MIN((int)featuresList.size(), LIMIT_MAX_FEATURE_NUMS);
    int bestIdx = 0;
    float maxSimilarity = 0.0f;
    float avgSimilarity = 0.0f;
    float tmpSimilarity = 0.0f;
    std::vector<std::vector<float>> featureSimValueMap(maxFeaturesNum);
    for(int i = 0; i < maxFeaturesNum; i++){
        featureSimValueMap[i].resize(maxFeaturesNum);
        featureSimValueMap[i][i] = 0;
    }
    for(int i = 0; i < maxFeaturesNum; i++){
        for(int j = i + 1; j < maxFeaturesNum; j++){
            tmpSimilarity = facesdkCloud.GetFaceSimilarity(featuresList[i], featuresList[j]);
            if(tmpSimilarity < LIMIT_MIN_SIM_TH){
                return -1;
            }
            featureSimValueMap[i][j] = featureSimValueMap[j][i] = tmpSimilarity;
                
        }
        avgSimilarity = 0.0f;
        for(int k = 0; k < maxFeaturesNum; k++){
            avgSimilarity += featureSimValueMap[i][k];
        }
        avgSimilarity /= (maxFeaturesNum - 1);
        if(avgSimilarity > maxSimilarity){
            maxSimilarity = avgSimilarity;
            bestIdx = i;
        }

    }

    return bestIdx;
}

void readFeatures(const char *filename, float *features)
{

    FILE *fp;
    if((fp=fopen(filename,"rb"))== NULL)
    {
        std::cout << "open file error!" << std::endl;
    }

    fread(features, sizeof(float), RECOGNIZE_FACE_FEATURE_SIZE, fp);
    fclose(fp);    
}

int main(int argc, char *argv[]) {
    if (argc < 3)
    {
        std::cout << "usage " << argv[0] << " DetectDynamic <File>  <SaveFile> <FeatureFile> " << std::endl;
        std::cout << "usage " << argv[0] << " DetectOptimal <FeatureFile1> <FeatureFile2> ...." << std::endl;
        std::cout << "usage " << argv[0] << " DetectDir <DIR>" << std::endl;
        std::cout << "usage " << argv[0] << " DetectFile <FILE>" << std::endl;
        return -1;
    }
   
    initAllModel();
   
    std::vector<std::string> files;
    std::string loadImgName;
    std::string saveImgName;
    std::string featureFile;
    std::cout << "Op Version: " << MOD_VERSION << std::endl;
    std::cout << "Debug Mod: " << TEST_LOCAL << std::endl;
   
    if (strcmp("DetectDynamic", argv[1]) == 0 && argc == 5)
    {

#if !TEST_LOCAL
        loadImgName = argv[2];
        saveImgName = argv[3];
        featureFile = argv[4];
#endif
#if TEST_LOCAL
        loadImgName = preHead + readImgName;
        saveImgName = preHead + writeImgName;
        featureFile = preHead + writeFeatureName;
#endif  
       
        return facesdkCloud.DoFaceDeal(loadImgName, 
                                        saveImgName, 
                                        featureFile,
                                        FACE_SAVE_TYPE_CROP,
                                        FACE_CROP_OUTPUT_W,
                                        FACE_CROP_OUTPUT_H);

    }else if (strcmp("DetectOptimal", argv[1]) == 0 && argc >= 3)
    {
        
        std::vector<float*> featuresList;

        int i = 0;
        int c = argc - 2;
        while (c > i)
        {
            float *features = new float[RECOGNIZE_FACE_FEATURE_SIZE];
            readFeatures(argv[i + 2], features);
            featuresList.push_back(new float[RECOGNIZE_FACE_FEATURE_SIZE]);
            memcpy(featuresList[i], features, RECOGNIZE_FACE_FEATURE_SIZE * sizeof(float));
            i++;   
            
        }
        int id = getBestFaceIdx(featuresList);
        std::cout << id;
    }

    
    if (strcmp("DetectDir", argv[1]) == 0)
    {
        getFiles(argv[2], files);
    }else if (strcmp("DetectFile", argv[1]) == 0)
    {
        std::string detectfile=argv[2];
        files.push_back(detectfile);
    }    

    if (strcmp("DetectDir", argv[1]) == 0 || strcmp("DetectFile", argv[1]) == 0)
    {   
        int ret = 0;
        for (auto &file : files)
        {   
            try
            {       
                loadImgName = file;
                saveImgName = file + "-detect.png";
                ret = facesdkCloud.DoFaceDeal(loadImgName, 
                                            saveImgName, 
                                            featureFile,
                                            FACE_SAVE_TYPE_CROP,
                                            FACE_CROP_OUTPUT_W,
                                            FACE_CROP_OUTPUT_H);

                printf("Img: %s, Ret: %d\n", file.c_str(), ret);
                if(ret > 0){
                    rename(saveImgName.c_str(), (file + "-detect").c_str());
                }

            }catch ( cv::Exception & e )
            {
                std::cout << "file:" << file << " has an exception:" << e.msg << std::endl;
            }
                
        }
    }

    return 0;
}

