#!/bin/sh


if [ ! $# -eq 2 ];then
   echo $0 local_filepath remote_filepath
   echo $0 xx.rom  5000/xx.rom
   exit
fi

local_filepath=$1
remote_filepath=$2

echo "scloud:"
/usr/local/oss_control_client/oss_upload_custom_tool oss-ap-southeast-1.aliyuncs.com s-fw-device-upgrade $local_filepath $remote_filepath
echo "ecloud:"
/usr/local/oss_control_client/oss_upload_custom_tool oss-eu-west-1.aliyuncs.com e-fw-device-upgrade $local_filepath $remote_filepath
echo "ucloud:"
/usr/local/oss_control_client/oss_upload_custom_tool oss-us-west-1.aliyuncs.com u-fw-device-upgrade $local_filepath $remote_filepath

