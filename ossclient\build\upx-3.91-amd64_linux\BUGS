                 ooooo     ooo ooooooooo.   ooooooo  ooooo
                 `888'     `8' `888   `Y88.  `8888    d8'
                  888       8   888   .d88'    Y888..8P
                  888       8   888ooo88P'      `8888'
                  888       8   888            .8PY888.
                  `88.    .8'   888           d8'  `888b
                    `YbodP'    o888o        o888o  o88888o


                    The Ultimate Packer for eXecutables
   Copyright (c) 1996-2013 <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>
                        http://upx.sourceforge.net


Limitations and other things which are not (yet) supported:
===========================================================

djgpp2/coff
-----------
 * all overlays (except Allegro pakfiles) are silently stripped

dos/exe
-------
 * normal dos/exes with new exe headers
 * max ~24000 relocation records (...should be enough for everyone ;-)
 * exe + sys combined images

watcom/le
---------
 * 16-bit selector alias fixups
 * 16-bit offset relocation for objects larger than 4 KiB
 * 16:16 fixups

If you need any of the above (they're very rare), send us an URL of a
test file.

 * 16-bit objects are not loaded into DOS memory
 * There is still a problem with the wdosx extender: if you compress a
   watcom/le file which does NOT contain the wdosx extender, and after this
   you bind the wdosx stub to the compressed file, then it will work.
   Otherwise it won't.
 * unpacked pmwlite compressed programs might not work when compressed
   with UPX (this is a bug in pmwunlite)

win32/pe
--------
 * writable shared sections (`--force' *may* work)
 * certificates in the image
 * compressing files which contain a big BSS requires lots of memory
   during compression

