-----<PERSON><PERSON><PERSON> PGP SIGNED MESSAGE-----


                 ooooo     ooo ooooooooo.   ooooooo  ooooo
                 `888'     `8' `888   `Y88.  `8888    d8'
                  888       8   888   .d88'    Y888..8P
                  888       8   888ooo88P'      `8888'
                  888       8   888            .8PY888.
                  `88.    .8'   888           d8'  `888b
                    `YbodP'    o888o        o888o  o88888o


                    The Ultimate Packer for eXecutables
          Copyright (c) 1996-2000 <PERSON> & <PERSON><PERSON>
               http://wildsau.idv.uni-linz.ac.at/mfx/upx.html
                          http://www.nexus.hu/upx
                            http://upx.tsx.org


PLEASE CAREFULLY READ THIS LICENSE AGREEMENT, ESPECIALLY IF YOU PLAN
TO MODIFY THE UPX SOURCE CODE OR USE A MODIFIED UPX VERSION.


ABSTRACT
========

   UPX and UCL are copyrighted software distributed under the terms
   of the GNU General Public License (hereinafter the "GPL").

   The stub which is imbedded in each UPX compressed program is part
   of UPX and UCL, and contains code that is under our copyright. The
   terms of the GNU General Public License still apply as compressing
   a program is a special form of linking with our stub.

   As a special exception we grant the free usage of UPX for all
   executables, including commercial programs.
   See below for details and restrictions.


COPYRIGHT
=========

   UPX and UCL are copyrighted software. All rights remain with the authors.

   UPX is Copyright (C) 1996-2000 Markus Franz Xaver Johannes Oberhumer
   UPX is Copyright (C) 1996-2000 Laszlo Molnar

   UCL is Copyright (C) 1996-2000 Markus Franz Xaver Johannes Oberhumer


GNU GENERAL PUBLIC LICENSE
==========================

   UPX and the UCL library are free software; you can redistribute them
   and/or modify them under the terms of the GNU General Public License as
   published by the Free Software Foundation; either version 2 of
   the License, or (at your option) any later version.

   UPX and UCL are distributed in the hope that they will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program; see the file COPYING.


SPECIAL EXCEPTION FOR COMPRESSED EXECUTABLES
============================================

   The stub which is imbedded in each UPX compressed program is part
   of UPX and UCL, and contains code that is under our copyright. The
   terms of the GNU General Public License still apply as compressing
   a program is a special form of linking with our stub.

   Hereby Markus F.X.J. Oberhumer and Laszlo Molnar grant you special
   permission to freely use and distribute all UPX compressed programs
   (including commercial ones), subject to the following restrictions:

   1. You must compress your program with a completely unmodified UPX
      version; either with our precompiled version, or (at your option)
      with a self compiled version of the unmodified UPX sources as
      distributed by us.
   2. This also implies that the UPX stub must be completely unmodfied, i.e.
      the stub imbedded in your compressed program must be byte-identical
      to the stub that is produced by the official unmodified UPX version.
   3. The decompressor and any other code from the stub must exclusively get
      used by the unmodified UPX stub for decompressing your program at
      program startup. No portion of the stub may get read, copied,
      called or otherwise get used or accessed by your program.


ANNOTATIONS
===========

  - You can use a modified UPX version or modified UPX stub only for
    programs that are compatible with the GNU General Public License.

  - We grant you special permission to freely use and distribute all UPX
    compressed programs. But any modification of the UPX stub (such as,
    but not limited to, removing our copyright string or making your
    program non-decompressible) will immediately revoke your right to
    use and distribute a UPX compressed program.

  - UPX is not a software protection tool; by requiring that you use
    the unmodified UPX version for your proprietary programs we
    make sure that any user can decompress your program. This protects
    both you and your users as nobody can hide malicious code -
    any program that cannot be decompressed is highly suspicious
    by definition.

  - You can integrate all or part of UPX and UCL into projects that
    are compatible with the GNU GPL, but obviously you cannot grant
    any special exceptions beyond the GPL for our code in your project.

  - We want to actively support manufacturers of virus scanners and
    similar security software. Please contact us if you would like to
    incorporate parts of UPX or UCL into such a product.



Markus F.X.J. Oberhumer                   Laszlo Molnar
<EMAIL>        <EMAIL>

Linz, Austria, 25 Feb 2000



-----BEGIN PGP SIGNATURE-----
Version: 2.6.3ia
Charset: noconv

iQCVAwUBOLaLS210fyLu8beJAQFYVAP/ShzENWKLTvedLCjZbDcwaBEHfUVcrGMI
wE7frMkbWT2zmkdv9hW90WmjMhOBu7yhUplvN8BKOtLiolEnZmLCYu8AGCwr5wBf
dfLoClxnzfTtgQv5axF1awp4RwCUH3hf4cDrOVqmAsWXKPHtm4hx96jF6L4oHhjx
OO03+ojZdO8=
=CS52
-----END PGP SIGNATURE-----
