UPX TODO list. Last updated 2006-12-08.


IMPORTANT PROBLEMS THAT SHOULD BE FIXED SOON:

- [None]


OTHER:

- docs: convert docs from upx.pod to use AsciiDoc

- check all <const_cast> to make sure they are not invalid

- throwNotCompressible() is not a real error, so make the output nicer
  (info: bla bla). Also ui.cpp (total_*).


-----------------------------------------------------------------------


IMPROVED COMPRESSION RATIO
==========================

- experiment with new filters

- implement filters for dos/exe

- filters: could we exploit a f->firstcall info field ?

- for small programs (e.g. < 64k), try an additional algorithm
  to see if it gives better compression


ALL FORMATS
===========

- more thoroughly test the exe-header in canPack()
  and throw exceptions when encountering bad values.

- implement `--cpu=486' option to use bswap on the 32-bit formats
    (if cpu >= 486)


FORMAT DJGPP2/COFF
==================

- handle overlays

- fix default file extension handling when the --coff option is set


FORMAT DOS/EXE
==============

- implement filters

- add a check so that we don't pack djgpp1 binaries


FORMAT LINUX/386
================

- don't mmap() the temporary output file - this seems to improve
    file io speed


FORMAT TMT/ADAM
===============

- the decompressors are already aligned, no need for an
    extra alignment


FORMAT WATCOM/LE
================

- handle files without relocations

- the decompressors are already aligned, no need for an
    extra alignment

- fix default file extension handling when the --le option is set

- handle holes in the file


FORMAT WIN16/NE
===============

- implement readFileHeader() to correctly identify a win16/ne
  executable, so that the call for contribution will get thrown


FORMAT WIN32/PE
===============

- fix the section alignment with the Intel compiler

- decrease runtime memory overhead

3 - difficult) don't compress the BSS section and other holes.

4 - medium - ml) fix when objectalign < 0x1000

4 - easy - ml) put the original offset of moved resources somewhere into
    the res.dir. (if it's safe to do)

4 - ??? - ml) fix FIXMEs

5 - medium - ml) try to put the preprocessed imports & relocs back to their
    original section if possible. this could save some virtual memory
    address space.

