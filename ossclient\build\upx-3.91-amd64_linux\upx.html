<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>upx - compress or expand executable files</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body style="background-color: white">


<!-- INDEX BEGIN -->
<div name="index">
<p><a name="__index__"></a></p>
<!--

<ul>

  <li><a href="#name">NAME</a></li>
  <li><a href="#synopsis">SYNOPSIS</a></li>
  <li><a href="#abstract">ABSTRACT</a></li>
  <li><a href="#disclaimer">DISCLAIMER</a></li>
  <li><a href="#description">DESCRIPTION</a></li>
  <li><a href="#commands">COMMANDS</a></li>
  <ul>

    <li><a href="#compress">Compress</a></li>
    <li><a href="#decompress">Decompress</a></li>
    <li><a href="#test">Test</a></li>
    <li><a href="#list">List</a></li>
  </ul>

  <li><a href="#options">OPTIONS</a></li>
  <li><a href="#compression_levels___tuning">COMPRESSION LEVELS &amp; TUNING</a></li>
  <li><a href="#overlay_handling_options">OVERLAY HANDLING OPTIONS</a></li>
  <li><a href="#environment">ENVIRONMENT</a></li>
  <li><a href="#notes_for_the_supported_executable_formats">NOTES FOR THE SUPPORTED EXECUTABLE FORMATS</a></li>
  <ul>

    <li><a href="#notes_for_atari_tos">NOTES FOR ATARI/TOS</a></li>
    <li><a href="#notes_for_bvmlinuz_i386">NOTES FOR BVMLINUZ/I386</a></li>
    <li><a href="#notes_for_dos_com">NOTES FOR DOS/COM</a></li>
    <li><a href="#notes_for_dos_exe">NOTES FOR DOS/EXE</a></li>
    <li><a href="#notes_for_dos_sys">NOTES FOR DOS/SYS</a></li>
    <li><a href="#notes_for_djgpp2_coff">NOTES FOR DJGPP2/COFF</a></li>
    <li><a href="#notes_for_linux__general_">NOTES FOR LINUX [general]</a></li>
    <li><a href="#notes_for_linux_elf386">NOTES FOR LINUX/ELF386</a></li>
    <li><a href="#notes_for_linux_sh386">NOTES FOR LINUX/SH386</a></li>
    <li><a href="#notes_for_linux_386">NOTES FOR LINUX/386</a></li>
    <li><a href="#notes_for_ps1_exe">NOTES FOR PS1/EXE</a></li>
    <li><a href="#notes_for_rtm32_pe_and_arm_pe">NOTES FOR RTM32/PE and ARM/PE</a></li>
    <li><a href="#notes_for_tmt_adam">NOTES FOR TMT/ADAM</a></li>
    <li><a href="#notes_for_vmlinuz_386">NOTES FOR VMLINUZ/386</a></li>
    <li><a href="#notes_for_watcom_le">NOTES FOR WATCOM/LE</a></li>
    <li><a href="#notes_for_win32_pe">NOTES FOR WIN32/PE</a></li>
  </ul>

  <li><a href="#diagnostics">DIAGNOSTICS</a></li>
  <li><a href="#bugs">BUGS</a></li>
  <li><a href="#authors">AUTHORS</a></li>
  <li><a href="#copyright">COPYRIGHT</a></li>
</ul>

-->


</div>
<!-- INDEX END -->

<p>
</p>
<h1><a name="name">NAME</a></h1>
<p>upx - compress or expand executable files</p>
<p>
</p>
<hr />
<h1><a name="synopsis">SYNOPSIS</a></h1>
<p><strong>upx</strong> [&nbsp;<em>command</em>&nbsp;] [&nbsp;<em>options</em>&nbsp;] <em>filename</em>...</p>
<p>
</p>
<hr />
<h1><a name="abstract">ABSTRACT</a></h1>
<pre>
                    The Ultimate Packer for eXecutables
   Copyright (c) 1996-2013 Markus Oberhumer, Laszlo Molnar &amp; John Reiser
                        <a href="http://upx.sourceforge.net">http://upx.sourceforge.net</a></pre>
<p><strong>UPX</strong> is a portable, extendable, high-performance executable packer for
several different executable formats. It achieves an excellent compression
ratio and offers <em>*very*</em> fast decompression. Your executables suffer
no memory overhead or other drawbacks for most of the formats supported,
because of in-place decompression.</p>
<p>While you may use <strong>UPX</strong> freely for both non-commercial and commercial
executables (for details see the file LICENSE), we would highly
appreciate if you credit <strong>UPX</strong> and ourselves in the documentation,
possibly including a reference to the <strong>UPX</strong> home page. Thanks.</p>
<p>[ Using <strong>UPX</strong> in non-OpenSource applications without proper credits
is considered not politically correct ;-) ]</p>
<p>
</p>
<hr />
<h1><a name="disclaimer">DISCLAIMER</a></h1>
<p><strong>UPX</strong> comes with ABSOLUTELY NO WARRANTY; for details see the file LICENSE.</p>
<p>This is the first production quality release, and we plan that future 1.xx
releases will be backward compatible with this version.</p>
<p>Please report all problems or suggestions to the authors. Thanks.</p>
<p>
</p>
<hr />
<h1><a name="description">DESCRIPTION</a></h1>
<p><strong>UPX</strong> is a versatile executable packer with the following features:</p>
<pre>
  - excellent compression ratio: compresses better than zip/gzip,
      use UPX to decrease the size of your distribution !</pre>
<pre>
  - very fast decompression: about 10 MiB/sec on an ancient Pentium 133,
      about 200 MiB/sec on an Athlon XP 2000+.</pre>
<pre>
  - no memory overhead for your compressed executables for most of the
      supported formats</pre>
<pre>
  - safe: you can list, test and unpack your executables
      Also, a checksum of both the compressed and uncompressed file is
      maintained internally.</pre>
<pre>
  - universal: UPX can pack a number of executable formats:
      * atari/tos
      * bvmlinuz/386    [bootable Linux kernel]
      * djgpp2/coff
      * dos/com
      * dos/exe
      * dos/sys
      * linux/386
      * linux/elf386
      * linux/sh386
      * ps1/exe
      * rtm32/pe
      * tmt/adam
      * vmlinuz/386     [bootable Linux kernel]
      * vmlinux/386
      * watcom/le (supporting DOS4G, PMODE/W, DOS32a and CauseWay)
      * win32/pe (exe and dll)
      * arm/pe (exe and dll)
      * linux/elfamd64
      * linux/elfppc32
      * mach/elfppc32</pre>
<pre>
  - portable: UPX is written in portable endian-neutral C++</pre>
<pre>
  - extendable: because of the class layout it's very easy to support
      new executable formats or add new compression algorithms</pre>
<pre>
  - free: UPX can be distributed and used freely. And from version 0.99
      the full source code of UPX is released under the GNU General Public
      License (GPL) !</pre>
<p>You probably understand now why we call <strong>UPX</strong> the &quot;<em>ultimate</em>&quot;
executable packer.</p>
<p>
</p>
<hr />
<h1><a name="commands">COMMANDS</a></h1>
<p>
</p>
<h2><a name="compress">Compress</a></h2>
<p>This is the default operation, eg. <strong>upx yourfile.exe</strong> will compress the file
specified on the command line.</p>
<p>
</p>
<h2><a name="decompress">Decompress</a></h2>
<p>All <strong>UPX</strong> supported file formats can be unpacked using the <strong>-d</strong> switch, eg.
<strong>upx -d yourfile.exe</strong> will uncompress the file you've just compressed.</p>
<p>
</p>
<h2><a name="test">Test</a></h2>
<p>The <strong>-t</strong> command tests the integrity of the compressed and uncompressed
data, eg. <strong>upx -t yourfile.exe</strong> check whether your file can be safely
decompressed. Note, that this command doesn't check the whole file, only
the part that will be uncompressed during program execution. This means
that you should not use this command instead of a virus checker.</p>
<p>
</p>
<h2><a name="list">List</a></h2>
<p>The <strong>-l</strong> command prints out some information about the compressed files
specified on the command line as parameters, eg <strong>upx -l yourfile.exe</strong>
shows the compressed / uncompressed size and the compression ratio of
<em>yourfile.exe</em>.</p>
<p>
</p>
<hr />
<h1><a name="options">OPTIONS</a></h1>
<p><strong>-q</strong>: be quiet, suppress warnings</p>
<p><strong>-q -q</strong> (or <strong>-qq</strong>): be very quiet, suppress errors</p>
<p><strong>-q -q -q</strong> (or <strong>-qqq</strong>): produce no output at all</p>
<p><strong>--help</strong>: prints the help</p>
<p><strong>--version</strong>: print the version of <strong>UPX</strong></p>
<p><strong>--exact</strong>: when compressing, require to be able to get a byte-identical file
after decompression with option <strong>-d</strong>. [NOTE: this is work in progress and is
not supported for all formats yet. If you do care, as a workaround you can
compress and then decompress your program a first time - any further
compress-decompress steps should then yield byte-identical results
as compared to the first decompressed version.]</p>
<p>[ ...to be written... - type `<strong>upx --help</strong>' for now ]</p>
<p>
</p>
<hr />
<h1><a name="compression_levels___tuning">COMPRESSION LEVELS &amp; TUNING</a></h1>
<p><strong>UPX</strong> offers ten different compression levels from <strong>-1</strong> to <strong>-9</strong>,
and <strong>--best</strong>.  The default compression level is <strong>-8</strong> for files
smaller than 512 KiB, and <strong>-7</strong> otherwise.</p>
<ul>
<li>
<p>Compression levels 1, 2 and 3 are pretty fast.</p>
</li>
<li>
<p>Compression levels 4, 5 and 6 achieve a good time/ratio performance.</p>
</li>
<li>
<p>Compression levels 7, 8 and 9 favor compression ratio over speed.</p>
</li>
<li>
<p>Compression level <strong>--best</strong> may take a long time.</p>
</li>
</ul>
<p>Note that compression level <strong>--best</strong> can be somewhat slow for large
files, but you definitely should use it when releasing a final version
of your program.</p>
<p>Quick info for achieving the best compression ratio:</p>
<ul>
<li>
<p>Try <strong>upx --brute myfile.exe</strong> or even <strong>upx --ultra-brute myfile.exe</strong>.</p>
</li>
<li>
<p>Try if <strong>--overlay=strip</strong> works.</p>
</li>
<li>
<p>For win32/pe programs there's <strong>--strip-relocs=0</strong>. See notes below.</p>
</li>
</ul>
<p>
</p>
<hr />
<h1><a name="overlay_handling_options">OVERLAY HANDLING OPTIONS</a></h1>
<p>Info: An &quot;overlay&quot; means auxiliary data attached after the logical end of
an executable, and it often contains application specific data
(this is a common practice to avoid an extra data file, though
it would be better to use resource sections).</p>
<p><strong>UPX</strong> handles overlays like many other executable packers do: it simply
copies the overlay after the compressed image. This works with some
files, but doesn't work with others, depending on how an application
actually accesses this overlayed data.</p>
<pre>
  --overlay=copy    Copy any extra data attached to the file. [DEFAULT]</pre>
<pre>
  --overlay=strip   Strip any overlay from the program instead of
                    copying it. Be warned, this may make the compressed
                    program crash or otherwise unusable.</pre>
<pre>
  --overlay=skip    Refuse to compress any program which has an overlay.</pre>
<p>
</p>
<hr />
<h1><a name="environment">ENVIRONMENT</a></h1>
<p>The environment variable <strong>UPX</strong> can hold a set of default
options for <strong>UPX</strong>. These options are interpreted first and
can be overwritten by explicit command line parameters.
For example:</p>
<pre>
    for DOS/Windows:   set UPX=-9 --compress-icons#0
    for sh/ksh/zsh:    UPX=&quot;-9 --compress-icons=0&quot;; export UPX
    for csh/tcsh:      setenv UPX &quot;-9 --compress-icons=0&quot;</pre>
<p>Under DOS/Windows you must use '#' instead of '=' when setting the
environment variable because of a COMMAND.COM limitation.</p>
<p>Not all of the options are valid in the environment variable -
<strong>UPX</strong> will tell you.</p>
<p>You can explicitly use the <strong>--no-env</strong> option to ignore the
environment variable.</p>
<p>
</p>
<hr />
<h1><a name="notes_for_the_supported_executable_formats">NOTES FOR THE SUPPORTED EXECUTABLE FORMATS</a></h1>
<p>
</p>
<h2><a name="notes_for_atari_tos">NOTES FOR ATARI/TOS</a></h2>
<p>This is the executable format used by the Atari ST/TT, a Motorola 68000
based personal computer which was popular in the late '80s. Support
of this format is only because of nostalgic feelings of one of
the authors and serves no practical purpose :-).
See <a href="http://www.freemint.de">http://www.freemint.de</a> for more info.</p>
<p>Packed programs will be byte-identical to the original after uncompression.
All debug information will be stripped, though.</p>
<p>Extra options available for this executable format:</p>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_bvmlinuz_i386">NOTES FOR BVMLINUZ/I386</a></h2>
<p>Same as vmlinuz/i386.</p>
<p>
</p>
<h2><a name="notes_for_dos_com">NOTES FOR DOS/COM</a></h2>
<p>Obviously <strong>UPX</strong> won't work with executables that want to read data from
themselves (like some commandline utilities that ship with Win95/98/ME).</p>
<p>Compressed programs only work on a 286+.</p>
<p>Packed programs will be byte-identical to the original after uncompression.</p>
<p>Maximum uncompressed size: ~65100 bytes.</p>
<p>Extra options available for this executable format:</p>
<pre>
  --8086              Create an executable that works on any 8086 CPU.</pre>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --all-filters       Compress the program several times, using all
                      available preprocessing filters. This may improve
                      the compression ratio in some cases, but usually
                      the default filter gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_dos_exe">NOTES FOR DOS/EXE</a></h2>
<p>dos/exe stands for all &quot;normal&quot; 16-bit DOS executables.</p>
<p>Obviously <strong>UPX</strong> won't work with executables that want to read data from
themselves (like some command line utilities that ship with Win95/98/ME).</p>
<p>Compressed programs only work on a 286+.</p>
<p>Extra options available for this executable format:</p>
<pre>
  --8086              Create an executable that works on any 8086 CPU.</pre>
<pre>
  --no-reloc          Use no relocation records in the exe header.</pre>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_dos_sys">NOTES FOR DOS/SYS</a></h2>
<p>Compressed programs only work on a 286+.</p>
<p>Packed programs will be byte-identical to the original after uncompression.</p>
<p>Maximum uncompressed size: ~65350 bytes.</p>
<p>Extra options available for this executable format:</p>
<pre>
  --8086              Create an executable that works on any 8086 CPU.</pre>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --all-filters       Compress the program several times, using all
                      available preprocessing filters. This may improve
                      the compression ratio in some cases, but usually
                      the default filter gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_djgpp2_coff">NOTES FOR DJGPP2/COFF</a></h2>
<p>First of all, it is recommended to use <strong>UPX</strong> *instead* of <strong>strip</strong>. strip has
the very bad habit of replacing your stub with its own (outdated) version.
Additionally <strong>UPX</strong> corrects a bug/feature in strip v2.8.x: it
will fix the 4 KiB alignment of the stub.</p>
<p><strong>UPX</strong> includes the full functionality of stubify. This means it will
automatically stubify your COFF files. Use the option <strong>--coff</strong> to
disable this functionality (see below).</p>
<p><strong>UPX</strong> automatically handles Allegro packfiles.</p>
<p>The DLM format (a rather exotic shared library extension) is not supported.</p>
<p>Packed programs will be byte-identical to the original after uncompression.
All debug information and trailing garbage will be stripped, though.</p>
<p>Extra options available for this executable format:</p>
<pre>
  --coff              Produce COFF output instead of EXE. By default
                      UPX keeps your current stub.</pre>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --all-filters       Compress the program several times, using all
                      available preprocessing filters. This may improve
                      the compression ratio in some cases, but usually
                      the default filter gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_linux__general_">NOTES FOR LINUX [general]</a></h2>
<p>Introduction</p>
<pre>
  Linux/386 support in UPX consists of 3 different executable formats,
  one optimized for ELF executables (&quot;linux/elf386&quot;), one optimized
  for shell scripts (&quot;linux/sh386&quot;), and one generic format
  (&quot;linux/386&quot;).</pre>
<pre>
  We will start with a general discussion first, but please
  also read the relevant docs for each of the individual formats.</pre>
<pre>
  Also, there is special support for bootable kernels - see the
  description of the vmlinuz/386 format.</pre>
<p>General user's overview</p>
<pre>
  Running a compressed executable program trades less space on a
  ``permanent'' storage medium (such as a hard disk, floppy disk,
  CD-ROM, flash memory, EPROM, etc.) for more space in one or more
  ``temporary'' storage media (such as RAM, swap space, /tmp, etc.).
  Running a compressed executable also requires some additional CPU
  cycles to generate the compressed executable in the first place,
  and to decompress it at each invocation.</pre>
<pre>
  How much space is traded?  It depends on the executable, but many
  programs save 30% to 50% of permanent disk space.  How much CPU
  overhead is there?  Again, it depends on the executable, but
  decompression speed generally is at least many megabytes per second,
  and frequently is limited by the speed of the underlying disk
  or network I/O.</pre>
<pre>
  Depending on the statistics of usage and access, and the relative
  speeds of CPU, RAM, swap space, /tmp, and file system storage, then
  invoking and running a compressed executable can be faster than
  directly running the corresponding uncompressed program.
  The operating system might perform fewer expensive I/O operations
  to invoke the compressed program.  Paging to or from swap space
  or /tmp might be faster than paging from the general file system.
  ``Medium-sized'' programs which access about 1/3 to 1/2 of their
  stored program bytes can do particularly well with compression.
  Small programs tend not to benefit as much because the absolute
  savings is less.  Big programs tend not to benefit proportionally
  because each invocation may use only a small fraction of the program,
  yet UPX decompresses the entire program before invoking it.
  But in environments where disk or flash memory storage is limited,
  then compression may win anyway.</pre>
<pre>
  Currently, executables compressed by UPX do not share RAM at runtime
  in the way that executables mapped from a file system do.  As a
  result, if the same program is run simultaneously by more than one
  process, then using the compressed version will require more RAM and/or
  swap space.  So, shell programs (bash, csh, etc.)  and ``make''
  might not be good candidates for compression.</pre>
<pre>
  UPX recognizes three executable formats for Linux: Linux/elf386,
  Linux/sh386, and Linux/386.  Linux/386 is the most generic format;
  it accommodates any file that can be executed.  At runtime, the UPX
  decompression stub re-creates in /tmp a copy of the original file,
  and then the copy is (re-)executed with the same arguments.
  ELF binary executables prefer the Linux/elf386 format by default,
  because UPX decompresses them directly into RAM, uses only one
  exec, does not use space in /tmp, and does not use /proc.
  Shell scripts where the underlying shell accepts a ``-c'' argument
  can use the Linux/sh386 format.  UPX decompresses the shell script
  into low memory, then maps the shell and passes the entire text of the
  script as an argument with a leading ``-c''.</pre>
<p>General benefits:</p>
<pre>
  - UPX can compress all executables, be it AOUT, ELF, libc4, libc5,
    libc6, Shell/Perl/Python/... scripts, standalone Java .class
    binaries, or whatever...
    All scripts and programs will work just as before.</pre>
<pre>
  - Compressed programs are completely self-contained. No need for
    any external program.</pre>
<pre>
  - UPX keeps your original program untouched. This means that
    after decompression you will have a byte-identical version,
    and you can use UPX as a file compressor just like gzip.
    [ Note that UPX maintains a checksum of the file internally,
      so it is indeed a reliable alternative. ]</pre>
<pre>
  - As the stub only uses syscalls and isn't linked against libc it
    should run under any Linux configuration that can run ELF
    binaries.</pre>
<pre>
  - For the same reason compressed executables should run under
    FreeBSD and other systems which can run Linux binaries.
    [ Please send feedback on this topic ]</pre>
<p>General drawbacks:</p>
<pre>
  - It is not advisable to compress programs which usually have many
    instances running (like `sh' or `make') because the common segments of
    compressed programs won't be shared any longer between different
    processes.</pre>
<pre>
  - `ldd' and `size' won't show anything useful because all they
    see is the statically linked stub.  Since version 0.82 the section
    headers are stripped from the UPX stub and `size' doesn't even
    recognize the file format.  The file patches/patch-elfcode.h has a
    patch to fix this bug in `size' and other programs which use GNU BFD.</pre>
<p>General notes:</p>
<pre>
  - As UPX leaves your original program untouched it is advantageous
    to strip it before compression.</pre>
<pre>
  - If you compress a script you will lose platform independence -
    this could be a problem if you are using NFS mounted disks.</pre>
<pre>
  - Compression of suid, guid and sticky-bit programs is rejected
    because of possible security implications.</pre>
<pre>
  - For the same reason there is no sense in making any compressed
    program suid.</pre>
<pre>
  - Obviously UPX won't work with executables that want to read data
    from themselves. E.g., this might be a problem for Perl scripts
    which access their __DATA__ lines.</pre>
<pre>
  - In case of internal errors the stub will abort with exitcode 127.
    Typical reasons for this to happen are that the program has somehow
    been modified after compression.
    Running `strace -o strace.log compressed_file' will tell you more.</pre>
<p>
</p>
<h2><a name="notes_for_linux_elf386">NOTES FOR LINUX/ELF386</a></h2>
<p>Please read the general Linux description first.</p>
<p>The linux/elf386 format decompresses directly into RAM,
uses only one exec, does not use space in /tmp,
and does not use /proc.</p>
<p>Linux/elf386 is automatically selected for Linux ELF executables.</p>
<p>Packed programs will be byte-identical to the original after uncompression.</p>
<p>How it works:</p>
<pre>
  For ELF executables, UPX decompresses directly to memory, simulating
  the mapping that the operating system kernel uses during exec(),
  including the PT_INTERP program interpreter (if any).
  The brk() is set by a special PT_LOAD segment in the compressed
  executable itself.  UPX then wipes the stack clean except for
  arguments, environment variables, and Elf_auxv entries (this is
  required by bugs in the startup code of /lib/ld-linux.so as of
  May 2000), and transfers control to the program interpreter or
  the e_entry address of the original executable.</pre>
<pre>
  The UPX stub is about 1700 bytes long, partly written in assembler
  and only uses kernel syscalls. It is not linked against any libc.</pre>
<p>Specific drawbacks:</p>
<pre>
  - For linux/elf386 and linux/sh386 formats, you will be relying on
    RAM and swap space to hold all of the decompressed program during
    the lifetime of the process.  If you already use most of your swap
    space, then you may run out.  A system that is &quot;out of memory&quot;
    can become fragile.  Many programs do not react gracefully when
    malloc() returns 0.  With newer Linux kernels, the kernel
    may decide to kill some processes to regain memory, and you
    may not like the kernel's choice of which to kill.  Running
    /usr/bin/top is one way to check on the usage of swap space.</pre>
<p>Extra options available for this executable format:</p>
<pre>
  (none)</pre>
<p>
</p>
<h2><a name="notes_for_linux_sh386">NOTES FOR LINUX/SH386</a></h2>
<p>Please read the general Linux description first.</p>
<p>Shell scripts where the underling shell accepts a ``-c'' argument
can use the Linux/sh386 format.  <strong>UPX</strong> decompresses the shell script
into low memory, then maps the shell and passes the entire text of the
script as an argument with a leading ``-c''.
It does not use space in /tmp, and does not use /proc.</p>
<p>Linux/sh386 is automatically selected for shell scripts that
use a known shell.</p>
<p>Packed programs will be byte-identical to the original after uncompression.</p>
<p>How it works:</p>
<pre>
  For shell script executables (files beginning with &quot;#!/&quot; or &quot;#! /&quot;)
  where the shell is known to accept &quot;-c &lt;command&gt;&quot;, UPX decompresses
  the file into low memory, then maps the shell (and its PT_INTERP),
  and passes control to the shell with the entire decompressed file
  as the argument after &quot;-c&quot;.  Known shells are sh, ash, bash, bsh, csh,
  ksh, tcsh, pdksh.  Restriction: UPX cannot use this method
  for shell scripts which use the one optional string argument after
  the shell name in the script (example: &quot;#! /bin/sh option3\n&quot;.)</pre>
<pre>
  The UPX stub is about 1700 bytes long, partly written in assembler
  and only uses kernel syscalls. It is not linked against any libc.</pre>
<p>Specific drawbacks:</p>
<pre>
  - For linux/elf386 and linux/sh386 formats, you will be relying on
    RAM and swap space to hold all of the decompressed program during
    the lifetime of the process.  If you already use most of your swap
    space, then you may run out.  A system that is &quot;out of memory&quot;
    can become fragile.  Many programs do not react gracefully when
    malloc() returns 0.  With newer Linux kernels, the kernel
    may decide to kill some processes to regain memory, and you
    may not like the kernel's choice of which to kill.  Running
    /usr/bin/top is one way to check on the usage of swap space.</pre>
<p>Extra options available for this executable format:</p>
<pre>
  (none)</pre>
<p>
</p>
<h2><a name="notes_for_linux_386">NOTES FOR LINUX/386</a></h2>
<p>Please read the general Linux description first.</p>
<p>The generic linux/386 format decompresses to /tmp and needs
/proc file system support. It starts the decompressed program
via the <code>execve()</code> syscall.</p>
<p>Linux/386 is only selected if the specialized linux/elf386
and linux/sh386 won't recognize a file.</p>
<p>Packed programs will be byte-identical to the original after uncompression.</p>
<p>How it works:</p>
<pre>
  For files which are not ELF and not a script for a known &quot;-c&quot; shell,
  UPX uses kernel execve(), which first requires decompressing to a
  temporary file in the file system.  Interestingly -
  because of the good memory management of the Linux kernel - this
  often does not introduce a noticeable delay, and in fact there
  will be no disk access at all if you have enough free memory as
  the entire process takes places within the file system buffers.</pre>
<pre>
  A compressed executable consists of the UPX stub and an overlay
  which contains the original program in a compressed form.</pre>
<pre>
  The UPX stub is a statically linked ELF executable and does
  the following at program startup:</pre>
<pre>
    1) decompress the overlay to a temporary location in /tmp
    2) open the temporary file for reading
    3) try to delete the temporary file and start (execve)
       the uncompressed program in /tmp using /proc/&lt;pid&gt;/fd/X as
       attained by step 2)
    4) if that fails, fork off a subprocess to clean up and
       start the program in /tmp in the meantime</pre>
<pre>
  The UPX stub is about 1700 bytes long, partly written in assembler
  and only uses kernel syscalls. It is not linked against any libc.</pre>
<p>Specific drawbacks:</p>
<pre>
  - You need additional free disk space for the uncompressed program
    in your /tmp directory. This program is deleted immediately after
    decompression, but you still need it for the full execution time
    of the program.</pre>
<pre>
  - You must have /proc file system support as the stub wants to open
    /proc/&lt;pid&gt;/exe and needs /proc/&lt;pid&gt;/fd/X. This also means that you
    cannot compress programs that are used during the boot sequence
    before /proc is mounted.</pre>
<pre>
  - Utilities like `top' will display numerical values in the process
    name field. This is because Linux computes the process name from
    the first argument of the last execve syscall (which is typically
    something like /proc/&lt;pid&gt;/fd/3).</pre>
<pre>
  - Because of temporary decompression to disk the decompression speed
    is not as fast as with the other executable formats. Still, I can see
    no noticeable delay when starting programs like my ~3 MiB emacs (which
    is less than 1 MiB when compressed :-).</pre>
<p>Extra options available for this executable format:</p>
<pre>
  --force-execve      Force the use of the generic linux/386 &quot;execve&quot;
                      format, i.e. do not try the linux/elf386 and
                      linux/sh386 formats.</pre>
<p>
</p>
<h2><a name="notes_for_ps1_exe">NOTES FOR PS1/EXE</a></h2>
<p>This is the executable format used by the Sony PlayStation (PSone),
a Mips R3000 based gaming console which is popular since the late '90s.
Support of this format is very similar to the Atari one, because of
nostalgic feelings of one of the authors.</p>
<p>Packed programs will be byte-identical to the original after uncompression,
until further notice.</p>
<p>Maximum uncompressed size: ~1.89 / ~7.60 MiB.</p>
<p>Notes:</p>
<pre>
  - UPX creates as default a suitable executable for CD-Mastering
    and console transfer. For a CD-Master main executable you could also try
    the special option &quot;--boot-only&quot; as described below.
    It has been reported that upx packed executables are fully compatible with
    the Sony PlayStation 2 (PS2, PStwo) and Sony PlayStation Portable (PSP) in
    Sony PlayStation (PSone) emulation mode.</pre>
<pre>
  - Normally the packed files use the same memory areas like the uncompressed
    versions, so they will not override other memory areas while unpacking.
    If this isn't possible UPX will abort showing a 'packed data overlap'
    error. With the &quot;--force&quot; option UPX will relocate the loading address
    for the packed file, but this isn't a real problem if it is a single or
    the main executable.</pre>
<p>Extra options available for this executable format:</p>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --8-bit             Uses 8 bit size compression [default: 32 bit]</pre>
<pre>
  --8mib-ram          PSone has 8 MiB ram available [default: 2 MiB]</pre>
<pre>
  --boot-only         This format is for main exes and CD-Mastering only !
                      It may slightly improve the compression ratio,
                      decompression routines are faster than default ones.
                      But it cannot be used for console transfer !</pre>
<pre>
  --no-align          This option disables CD mode 2 data sector format
                      alignment. May slightly improves the compression ratio,
                      but the compressed executable will not boot from a CD.
                      Use it for console transfer only !</pre>
<p>
</p>
<h2><a name="notes_for_rtm32_pe_and_arm_pe">NOTES FOR RTM32/PE and ARM/PE</a></h2>
<p>Same as win32/pe.</p>
<p>
</p>
<h2><a name="notes_for_tmt_adam">NOTES FOR TMT/ADAM</a></h2>
<p>This format is used by the TMT Pascal compiler - see <a href="http://www.tmt.com/">http://www.tmt.com/</a> .</p>
<p>Extra options available for this executable format:</p>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --all-filters       Compress the program several times, using all
                      available preprocessing filters. This may improve
                      the compression ratio in some cases, but usually
                      the default filter gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_vmlinuz_386">NOTES FOR VMLINUZ/386</a></h2>
<p>The vmlinuz/386 and bvmlinuz/386 formats take a gzip-compressed
bootable Linux kernel image (&quot;vmlinuz&quot;, &quot;zImage&quot;, &quot;bzImage&quot;),
gzip-decompress it and re-compress it with the <strong>UPX</strong> compression method.</p>
<p>vmlinuz/386 is completely unrelated to the other Linux executable
formats, and it does not share any of their drawbacks.</p>
<p>Notes:</p>
<pre>
  - Be sure that &quot;vmlinuz/386&quot; or &quot;bvmlinuz/386&quot; is displayed
  during compression - otherwise a wrong executable format
  may have been used, and the kernel won't boot.</pre>
<p>Benefits:</p>
<pre>
  - Better compression (but note that the kernel was already compressed,
  so the improvement is not as large as with other formats).
  Still, the bytes saved may be essential for special needs like
  boot disks.</pre>
<pre>
     For example, this is what I get for my 2.2.16 kernel:
        1589708  vmlinux
         641073  bzImage        [original]
         560755  bzImage.upx    [compressed by &quot;upx -9&quot;]</pre>
<pre>
  - Much faster decompression at kernel boot time (but kernel
    decompression speed is not really an issue these days).</pre>
<p>Drawbacks:</p>
<pre>
  (none)</pre>
<p>Extra options available for this executable format:</p>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --all-filters       Compress the program several times, using all
                      available preprocessing filters. This may improve
                      the compression ratio in some cases, but usually
                      the default filter gives the best results anyway.</pre>
<p>
</p>
<h2><a name="notes_for_watcom_le">NOTES FOR WATCOM/LE</a></h2>
<p><strong>UPX</strong> has been successfully tested with the following extenders:
  DOS4G, DOS4GW, PMODE/W, DOS32a, CauseWay.
  The WDOS/X extender is partly supported (for details
  see the file bugs BUGS).</p>
<p>DLLs and the LX format are not supported.</p>
<p>Extra options available for this executable format:</p>
<pre>
  --le                Produce an unbound LE output instead of
                      keeping the current stub.</pre>
<p>
</p>
<h2><a name="notes_for_win32_pe">NOTES FOR WIN32/PE</a></h2>
<p>The PE support in <strong>UPX</strong> is quite stable now, but probably there are
still some incompatibilities with some files.</p>
<p>Because of the way <strong>UPX</strong> (and other packers for this format) works, you
can see increased memory usage of your compressed files because the whole
program is loaded into memory at startup.
If you start several instances of huge compressed programs you're
wasting memory because the common segments of the program won't
get shared across the instances.
On the other hand if you're compressing only smaller programs, or
running only one instance of larger programs, then this penalty is
smaller, but it's still there.</p>
<p>If you're running executables from network, then compressed programs
will load faster, and require less bandwidth during execution.</p>
<p>DLLs are supported. But UPX compressed DLLs can not share common data and
code when they got used by multiple applications. So compressing msvcrt.dll
is a waste of memory, but compressing the dll plugins of a particular
application may be a better idea.</p>
<p>Screensavers are supported, with the restriction that the filename
must end with &quot;.scr&quot; (as screensavers are handled slightly different
than normal exe files).</p>
<p>UPX compressed PE files have some minor memory overhead (usually in the
10 - 30 KiB range) which can be seen by specifying the &quot;-i&quot; command
line switch during compression.</p>
<p>Extra options available for this executable format:</p>
<pre>
 --compress-exports=0 Don't compress the export section.
                      Use this if you plan to run the compressed
                      program under Wine.
 --compress-exports=1 Compress the export section. [DEFAULT]
                      Compression of the export section can improve the
                      compression ratio quite a bit but may not work
                      with all programs (like winword.exe).
                      UPX never compresses the export section of a DLL
                      regardless of this option.</pre>
<pre>
  --compress-icons=0  Don't compress any icons.
  --compress-icons=1  Compress all but the first icon.
  --compress-icons=2  Compress all icons which are not in the
                      first icon directory. [DEFAULT]
  --compress-icons=3  Compress all icons.</pre>
<pre>
  --compress-resources=0  Don't compress any resources at all.</pre>
<pre>
  --keep-resource=list Don't compress resources specified by the list.
                      The members of the list are separated by commas.
                      A list member has the following format: I&lt;type[/name]&gt;.
                      I&lt;Type&gt; is the type of the resource. Standard types
                      must be specified as decimal numbers, user types can be
                      specified by decimal IDs or strings. I&lt;Name&gt; is the
                      identifier of the resource. It can be a decimal number
                      or a string. For example:</pre>
<pre>
                      --keep-resource=2/MYBITMAP,5,6/12345</pre>
<pre>
                      UPX won't compress the named bitmap resource &quot;MYBITMAP&quot;,
                      it leaves every dialog (5) resource uncompressed, and
                      it won't touch the string table resource with identifier
                      12345.</pre>
<pre>
  --force             Force compression even when there is an
                      unexpected value in a header field.
                      Use with care.</pre>
<pre>
  --strip-relocs=0    Don't strip relocation records.
  --strip-relocs=1    Strip relocation records. [DEFAULT]
                      This option only works on executables with base
                      address greater or equal to 0x400000. Usually the
                      compressed files becomes smaller, but some files
                      may become larger. Note that the resulting file will
                      not work under Windows 3.x (Win32s).
                      UPX never strips relocations from a DLL
                      regardless of this option.</pre>
<pre>
  --all-methods       Compress the program several times, using all
                      available compression methods. This may improve
                      the compression ratio in some cases, but usually
                      the default method gives the best results anyway.</pre>
<pre>
  --all-filters       Compress the program several times, using all
                      available preprocessing filters. This may improve
                      the compression ratio in some cases, but usually
                      the default filter gives the best results anyway.</pre>
<p>
</p>
<hr />
<h1><a name="diagnostics">DIAGNOSTICS</a></h1>
<p>Exit status is normally 0; if an error occurs, exit status
is 1. If a warning occurs, exit status is 2.</p>
<p><strong>UPX</strong>'s diagnostics are intended to be self-explanatory.</p>
<p>
</p>
<hr />
<h1><a name="bugs">BUGS</a></h1>
<p>Please report all bugs immediately to the authors.</p>
<p>
</p>
<hr />
<h1><a name="authors">AUTHORS</a></h1>
<pre>
 Markus F.X.J. Oberhumer &lt;<EMAIL>&gt;
 <a href="http://www.oberhumer.com">http://www.oberhumer.com</a></pre>
<pre>
 Laszlo Molnar &lt;<EMAIL>&gt;</pre>
<pre>
 John F. Reiser &lt;<EMAIL>&gt;</pre>
<pre>
 Jens Medoch &lt;<EMAIL>&gt;</pre>
<p>
</p>
<hr />
<h1><a name="copyright">COPYRIGHT</a></h1>
<p>Copyright (C) 1996-2013 Markus Franz Xaver Johannes Oberhumer</p>
<p>Copyright (C) 1996-2013 Laszlo Molnar</p>
<p>Copyright (C) 2000-2013 John F. Reiser</p>
<p>Copyright (C) 2002-2013 Jens Medoch</p>
<p>This program may be used freely, and you are welcome to
redistribute it under certain conditions.</p>
<p>This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<strong>UPX License Agreement</strong> for more details.</p>
<p>You should have received a copy of the UPX License Agreement along
with this program; see the file LICENSE. If not, visit the UPX home page.</p>

</body>

</html>
