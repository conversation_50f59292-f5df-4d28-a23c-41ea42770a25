<?php

require_once '/usr/local/oss_control_client/aliyun-oss-php-sdk-2.3.0.phar';

use OSS\OssClient;
use OSS\Core\OssException;


if ($argc < 2 )
{
    exit( "Usage: program local_file_path remote_dir\n" );
}

// 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。

$base_dir="BASE_DIR";
$accessKeyId = "LTAILDY6u3uotCJm";
$accessKeySecret = "46EL7LJInh6xvJ6fh5KTvEyXEvqYCb";
// Endpoint以新加坡为例，其它Region请按实际情况填写。
//外网
$endpoint = "oss-ap-southeast-1.aliyuncs.com";
//ECS经典网络
$endpoint = "oss-ap-southeast-1-internal.aliyuncs.com";
// 存储空间名称
$bucket= "server-log-back";
// 文件名称
$object = $base_dir."/".$argv[2];
// <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
$filePath = $argv[1];

try{
    $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);

    $ossClient->uploadFile($bucket, $object, $filePath);
} catch(OssException $e) {
    printf("FAILED\n");
    printf($e->getMessage() . "\n");
    return;
}
print("OK" . "\n");
