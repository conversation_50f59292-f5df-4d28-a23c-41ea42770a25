<?php

require_once './aliyun-oss-php-sdk-2.3.0.phar';

use OSS\OssClient;
use OSS\Core\OssException;


if ($argc < 2 )
{
    exit( "Usage: program local_file_path remote_dir\n" );
}

// 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。

$base_dir="FaceDebug";
$accessKeyId = "STS.NTGCY346Fy1zkY6FDda6DmKFb";
$accessKeySecret = "9CG8FeaFjcwwhL8PFVoMqrLWQgybDiSuY529GuozNYA2";
$securityToken = 'CAIS0QN1q6Ft5B2yfSjIr5fyCOOH2elnzvORaX+HokQxbblogo7tgDz2IH1NdHFhBusevvozm25Y6f8flqN9QpRIckDJZ9Z/tmnTUc1VJdivgde8yJBZolLPewHKedeRvqL7Z+H+U6mSGJOEYEzFkSle2KbzcS7YMXWuLZyOj+wADLEQRRLqVSdaI91UKwB+0klzVx3rOO2qLwThj0fJEUNsoXAcs25k7rmlycDuU3O8w1Tx0b0SvJ+jYMrmPdJhJolySZKx2+MzfKPK02l+4hNH2Kp8kaMDu1WHt9qGUV5K+EfEYaiG+dZrKxJ1I6w9FOtOpfP/z7pWs+feu4n91hkKf4MVWi/EFoe725mGSqH7Ld09cqv3fnDK3ZWFP5L26UQKaHkWGA5AZtVmaB0wABc3GDbBMf3lqhKYbwynTLWJlaUx3t9/xl7rp52kLlOCb7iTzClfeP18ZkgzZRkNxj6jIO1UbgdIdE5rQrKSQsB5ZRtd8+TGtAbfeiBq034Q/d+GPqqG5/FGOd+gAcgZj9pDOK4r6TV6Ew7FLJu1kVoReWBfRrJb7bLgI5fX6sXenbzDOb+ZUahX5AkFL2CO9neXK3RLNib24ME5bl6IUwUgSSV2FvkagAGHOuq00ZmyTs+2zv+/iPq2O5S0I9dD8t0SS0yeP9nUB1mzwx2WMKVRl/M1g7YvF/FfsyM2wLCjJxQPPR6VYG+kqNSaMKHYHMt4ECCtpP5vLEuFgBrrBBwhc8stLofuPO7giqx+82XChrbAY3y5Yo6qwH888B1Vvs2gOvK5dHXq6A==';
// Endpoint以新加坡为例，其它Region请按实际情况填写。
//外网
$endpoint = "https://oss-eu-central-1.aliyuncs.com";
// 存储空间名称
$bucket= "ecloud-log-back2";
// 文件名称
$object = $base_dir."/".$argv[2];
// <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
$filePath = $argv[1];

try{
    $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint, false, $securityToken);;

    $ossClient->uploadFile($bucket, $object, $filePath);
} catch(OssException $e) {
    printf("FAILED\n");
    printf($e->getMessage() . "\n");
    return;
}
print("OK" . "\n");
