#!/bin/bash

# ****************************************************************************
# Author        :   jianjun.li
# Last modified :   2022-05-18
# Filename      :   install_oss.sh
# Version       :
# Description   :   base_common 的安装脚本，用来安装 oss
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

# ============================================================================
# ==== main
# ============================================================================
echo "install oss"

cd "$(dirname "$0")"
# PKG_ROOT=$(cd .. && pwd)
PKG_ROOT=$RSYNC_PATH

INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
KDC_CONF=$RSYNC_PATH/shell/kdc.conf
OSS_CLIENT_PATH="$PKG_ROOT"
APP_HOME=$PROJECT_RUN_PATH

cd "$OSS_CLIENT_PATH" || exit 1

if [ ! -d /var/log/oss_upload_client_log ]; then
   mkdir /var/log/oss_upload_client_log
fi

if [ ! -d $APP_HOME ]; then
   mkdir $APP_HOME
fi

# 不同 oss
OSS_TAG=$(grep_conf 'OSS_TAG' $INSTALL_CONF)
OSS_PROVIDER=$(grep_conf 'OSS_PROVIDER' $INSTALL_CONF)
storage_type=0
is_aws=0
if [ $OSS_PROVIDER == "s3" ];then
    storage_type=1;
    is_aws=1;
elif [ $OSS_PROVIDER == "us3" ];then
    storage_type=2;
fi
RegionID=$(grep_conf 'OSS_RegionID' $INSTALL_CONF || true)

BucketForLog=$(grep_conf 'OSS_BucketForLog' $INSTALL_CONF)
BucketForPic=$(grep_conf 'OSS_BucketForPic' $INSTALL_CONF)
BucketForFace=$(grep_conf 'OSS_BucketForFace' $INSTALL_CONF)
Endpoint=$(grep_conf 'OSS_Endpoint' $INSTALL_CONF)
OuterEndpoint=$(grep_conf 'OSS_OuterEndpoint' $INSTALL_CONF)
StsEndpoint=$(grep_conf 'OSS_StsEndpoint' $INSTALL_CONF)
RoleArn=$(grep_conf 'OSS_RoleArn' $INSTALL_CONF)


if [ $storage_type -eq 1 ];then
    passwd=$(grep_conf 'S3_PASSWORD' $KDC_CONF)
    user=$(grep_conf 'S3_USER' $KDC_CONF)
elif [ $storage_type -eq 2 ];then
    passwd=$(grep_conf 'US3_PASSWORD' $KDC_CONF)
    user=$(grep_conf 'US3_USER' $KDC_CONF)
else
    passwd=$(grep_conf 'OSS_PASSWORD' $KDC_CONF)
    user=$(grep_conf 'OSS_USER' $KDC_CONF)    
fi


if [ "$OSS_TAG" = 'discreet' ]; then
    cp -f "$OSS_CLIENT_PATH/oss_upload_tool_discreet" "$OSS_CLIENT_PATH/oss_upload_tool"
else
    cp -f "$OSS_CLIENT_PATH/oss_upload_tool_akcs" "$OSS_CLIENT_PATH/oss_upload_tool"
fi


cat > /etc/oss_install.conf <<-EOF
IS_AWS=$is_aws
STORAGE_TYPE=$storage_type
OSS_TAG=$OSS_TAG
BucketForLog=$BucketForLog
BucketForPic=$BucketForPic
BucketForFace=$BucketForFace

RegionID=$RegionID

Endpoint=$Endpoint
OuterEndpoint=$OuterEndpoint
StsEndpoint=$StsEndpoint
RoleArn=$RoleArn

User=$user
Password=$passwd

EOF

chmod 755 /etc/oss_install.conf
cp -r "$OSS_CLIENT_PATH"/* $APP_HOME
chmod -R 755 $APP_HOME/*



