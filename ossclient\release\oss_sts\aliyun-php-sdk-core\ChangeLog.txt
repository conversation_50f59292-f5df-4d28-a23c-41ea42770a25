2019-03-04 Version: 1.3.9
1, Support CCC.

2019-02-25 Version: 1.3.8
1, Fixed SignatureDoesNotMatch.

2019-01-11 Version: 1.3.7
1, Support sign for Windows.

2019-01-04 Version: 1.3.6
1, add endpoints for Iot.

2019-01-03 Version: 1.3.5
1, Regenerate cdn.

2019-01-01 Version: 1.3.4
1, use DIRECTORY_SEPARATOR.

2018-12-31 Version: 1.3.3
1, Optimized autoload.

2018-12-29 Version: 1.3.2
1, Support autoload directories.

2018-12-29 Version: 1.3.1
1, Fixed invalid released.

2018-12-29 Version: 1.3.0
1, fixed method for ROA request.

2018-12-29 Version: 1.2.9
1, Support magic method call for get request parameters.

2018-12-28 Version: 1.2.8
1, Format core code.

2018-12-27 Version: 1.2.7
1, Fixed protocolType conflict.

2018-12-27 Version: 1.2.6
1, Support BssOpenApi product.

2018-12-27 Version: 1.2.5
1, Support Afs product.

2018-12-27 Version: 1.2.4
1, Fixed isset domain in LocationService

2018-12-27 Version: 1.2.3
1, Add the aliyun dns class load path.
2, Fixed PHP Notice in LocationService.php.

2018-12-24 Version: 1.2.2
1, Fixed ROA sign
2, ROA support JSON Format
3, Response Field identification


2018-12-11 Version: 1.2.1
1, Add endpoint data for DYSMS

2018-11-06 Version: 1.2.1
1, fix Notice for LocationService::checkCacheIsExpire

2018-11-06 Version: 1.2.1
1, fix Notice for LocationService::checkCacheIsExpire

2018-11-06 Version: 1.2.1
1, fix Notice for LocationService::checkCacheIsExpire

2018-03-13 Version: 1.1.4
1, Three scene completion StsToken

2017-12-22 Version: 1.2.0
1, STS token that supports ordinary scenes.

2017-09-30 Version: 1.1.3
1, fix bug:修改客户端默认时区

2017-09-06 Version: 1.1.4
1, 修改IoT套件美西和新加坡节点的endpoint

2017-07-25 Version: 1.1.3
1, 修改IoT套件在华东2个Region上的endpoint。

