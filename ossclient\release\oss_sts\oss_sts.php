<?php
include_once 'aliyun-php-sdk-core/Config.php';
use Sts\Request\V20150401 as Sts;
define("REGION_ID", $argv[1]);
define("ENDPOINT", $argv[2]);
// 只允许RAM用户使用角色
DefaultProfile::addEndpoint(REGION_ID, REGION_ID, "Sts", ENDPOINT);
$iClientProfile = DefaultProfile::getProfile(REGION_ID, $argv[3], $argv[4]);
$client = new DefaultAcsClient($iClientProfile);
// 指定角色ARN
$roleArn = $argv[5];
// 在扮演角色时，添加一个权限策略，进一步限制角色的权限
// 以下权限策略表示拥有可以读取所有OSS的只读权限
$policy=<<<POLICY
{
  "Statement": [
    {
      "Action": [
	"oss:PutObject"
      ],
      "Effect": "Allow",
      "Resource": [
      	"acs:oss:*:*:server-log-back/FaceDebug/*",
	"acs:oss:*:*:ccloud-log-back2/FaceDebug/*",
	"acs:oss:*:*:scloud-log-back2/FaceDebug/*",
	"acs:oss:*:*:ecloud-log-back2/FaceDebug/*",
	"acs:oss:*:*:ucloud-log-back2/FaceDebug/*"	
      ]
    }
  ],
  "Version": "1"
}
POLICY;
$request = new Sts\AssumeRoleRequest();
// RoleSessionName即临时身份的会话名称，用于区分不同的临时身份
$request->setRoleSessionName("face_debug");
$request->setRoleArn($roleArn);
$request->setPolicy($policy);
$request->setDurationSeconds(3600);
try {
    $response = $client->getAcsResponse($request);
} catch(ServerException $e) {
    print "Error: " . $e->getErrorCode() . " Message: " . $e->getMessage() . "\n";
} catch(ClientException $e) {
    print "Error: " . $e->getErrorCode() . " Message: " . $e->getMessage() . "\n";
}
?>
