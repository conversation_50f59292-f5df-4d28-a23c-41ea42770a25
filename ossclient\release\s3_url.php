<?php
require '/usr/local/oss_control_client/aws.phar';


use Aws\S3\S3Client;  
use Aws\Exception\AwsException;

$user = $argv[1];
$passwd = $argv[2];
$region = $argv[3];
$bucket = $argv[4];
$path = $argv[5];
$time = $argv[6];
$credentials = new Aws\Credentials\Credentials($user, $passwd);
$s3Client = new Aws\S3\S3Client([
    'version'     => 'latest',
    'region'      => $region,
    'credentials' => $credentials
]);


$cmd = $s3Client->getCommand('GetObject', [
'Bucket' => $bucket,
'Key' => $path
]);

$request = $s3Client->createPresignedRequest($cmd, "$time second");

// Get the actual presigned-url
$presignedUrl = (string)$request->getUri();
echo $presignedUrl;
