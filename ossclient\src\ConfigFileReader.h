/*
 * ConfigFileReader.h
 *
 *  Created on: 2013-7-2
 *      Author: <EMAIL>
 */

#ifndef __AKCS_BASE_CONFIGFILE_READER_H__
#define __AKCS_BASE_CONFIGFILE_READER_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
using namespace std;
class CConfigFileReader
{
public:
	CConfigFileReader(const char* filename);
	//~CConfigFileReader();

    const char* GetConfigName(const char* name);
    int SetConfigValue(const char* name, const char*  value);
private:
    void _LoadFile(const char* filename);
    int _WriteFIle(const char*filename = NULL);
    void _ParseLine(char* line);
    char* _TrimSpace(char* name);

    bool m_load_ok;
    map<string, string> m_config_map;
    string m_config_file;
};



#endif /* __AKCS_BASE_CONFIGFILE_READER_H__ */
