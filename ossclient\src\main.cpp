#include <alibabacloud/oss/OssClient.h>
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/time.h>
#include <sys/types.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include<fstream>
#include <ctime>


#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>
#include <aws/s3/model/PutObjectRequest.h>
#include <iostream>
#include <fstream>
#include <sys/stat.h>
#include <aws/s3/model/GetObjectRequest.h>
#include <aws/core/http/HttpTypes.h>


#include "AkLogging.h"
#include "ConfigFileReader.h"

using namespace AlibabaCloud::OSS;

#define AWS_CONFIG "/usr/local/oss_control_client/aws"

std::string GetEth1IPAddr()
{
    int inet_sock;
    struct sockaddr_in sin;
    struct ifreq ifr;  
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);  
    strncpy(ifr.ifr_name, "eth0", IFNAMSIZ);
    ifr.ifr_name[IFNAMSIZ - 1] = 0;
    ioctl(inet_sock, SIOCGIFADDR, &ifr);  
    close(inet_sock);
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    return inet_ntoa(sin.sin_addr);
}

void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string)
{
	size_t pos = 0;
	size_t src_size = src_string.size();
	size_t dst_size = dst_string.size();
	while ((pos = replace_string.find(src_string, pos)) != std::string::npos)
	{
		replace_string.replace(pos, src_size, dst_string);
		pos += dst_size;
	}
}

int main(int argc, char *argv[])
{
    if (argc < 3)
    {
        printf("usage: /usr/local/oss_control_client/oss_upload_tool <filepath> <remote_path>\n");
        //<local_file_dir>:用于aws_cli docker 访问挂载的目录
        printf("aws usage: /usr/local/oss_control_client/oss_upload_tool <filepath> <remote_path> <local_file_dir>\n");
       
        std::cout << "error";
        return -1;
    }
    
    
    CConfigFileReader config_file("/etc/oss_install.conf");
    /* 初始化OSS账号信息 */
    //scloud-log-back
    std::string BucketName = config_file.GetConfigName("BucketForLog");    
    //oss-eu-central-1-internal.aliyuncs.com
    std::string Endpoint = config_file.GetConfigName("Endpoint");
    std::string is_aws = config_file.GetConfigName("IS_AWS"); 
    std::string RegionID = config_file.GetConfigName("RegionID");  

    std::string user = config_file.GetConfigName("User");  
    std::string passwd = config_file.GetConfigName("Password");
    
    CConfigFileReader ip_file("/etc/ip");
    std::string server_ip = ip_file.GetConfigName("SERVERIP");      

    if(is_aws.size() > 0 && 1 == atoi(is_aws.c_str()))   //走亚马逊s3
    {
        std::string file_path = argv[1];
        std::string remote_path = argv[2];
        std::string local_file_dir = argv[3];
        StringReplace(file_path, "\\", "");        
        StringReplace(remote_path, "\\", "");
        StringReplace(local_file_dir, "\\", "");

        Aws::SDKOptions options_;
        options_.loggingOptions.logLevel = Aws::Utils::Logging::LogLevel::Error;
        Aws::InitAPI(options_);
        //s3连接 默认连接超时1s 
        Aws::Client::ClientConfiguration cfg;
        cfg.endpointOverride = Endpoint;  // S3服务器地址和端口
        cfg.scheme = Aws::Http::Scheme::HTTP;
        cfg.verifySSL = false;
        cfg.region = RegionID;
        cfg.connectTimeoutMs = 3000;
        cfg.requestTimeoutMs = 3000;//Socket read timeouts 3s
        
        Aws::Auth::AWSCredentials cred(user, passwd);
        std::shared_ptr<Aws::S3::S3Client> s3_client_ = std::make_shared<Aws::S3::S3Client>(cred, cfg, Aws::Client::AWSAuthV4Signer::PayloadSigningPolicy::Never,false);

        Aws::S3::Model::PutObjectRequest object_request;
        object_request.SetBucket(BucketName);
        object_request.SetKey(remote_path);
        const std::shared_ptr<Aws::IOStream> input_data =
            Aws::MakeShared<Aws::FStream>("SampleAllocationTag",
                                          file_path,
                                          std::ios_base::in | std::ios_base::binary);
        object_request.SetBody(input_data);

        // 上传文件
        auto put_object_outcome = s3_client_->PutObject(object_request);
        if (!put_object_outcome.IsSuccess()) {
            auto error = put_object_outcome.GetError();
            std::cout << "upload file error. filePath:" << file_path << "   " <<  error.GetExceptionName() << ": "
                << error.GetMessage();
            return -1;
        }  
        return 0;

    }

    std::ofstream infile("/var/log/oss_upload_client_log/upload.log", std::ios::app);
    time_t rawtime;
    struct tm *info;
    time( &rawtime );
    info = localtime( &rawtime );
    std::string log_time = asctime(info);
    log_time.pop_back();//去掉换行
    log_time += "  ";
    
    /* 初始化OSS账号信息 */
    std::string AccessKeyId = user;
    std::string AccessKeySecret = passwd;
  

    /* 初始化网络等资源 */
    InitializeSdk();

    ClientConfiguration conf;
    OssClient client(Endpoint, AccessKeyId, AccessKeySecret, conf);


    std::string ObjectName = GetEth1IPAddr();
    ObjectName += "/";
    ObjectName += argv[2];
    StringReplace(ObjectName, "\\", "");

    // <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
    std::string filePath = argv[1];
    StringReplace(filePath, "\\", "");

    /* 上传文件 */
    auto outcome = client.PutObject(BucketName, ObjectName, filePath);
    infile << log_time << ObjectName << "  " << filePath << "\n";
    if (!outcome.isSuccess()) {
        /* 异常处理 */
        infile << log_time << "BucketName:" << BucketName << " Endpoint:" << Endpoint << " filePath:" << filePath;
        infile << log_time << " PutObject fail" << 
        ",code:" <<  outcome.error().Code() << 
        ",message:" <<  outcome.error().Message() << 
        ",requestId:" <<  outcome.error().RequestId() << "\n";
        ShutdownSdk();
        std::cout << "error";
        return -1;
    }
    /* 释放网络等资源 */
    ShutdownSdk();
   
    infile.close();
    return 0;
}








