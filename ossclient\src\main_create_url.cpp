#include <alibabacloud/oss/OssClient.h>
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/time.h>
#include <sys/types.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include<fstream>
#include <ctime>

#include "AkLogging.h"
#include "ConfigFileReader.h"

using namespace AlibabaCloud::OSS;

#define AWS_CONFIG "/usr/local/oss_control_client/aws"

std::string GetEth1IPAddr()
{
    int inet_sock;
    struct sockaddr_in sin;
    struct ifreq ifr;  
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);  
    strncpy(ifr.ifr_name, "eth0", IFNAMSIZ);
    ifr.ifr_name[IFNAMSIZ - 1] = 0;
    ioctl(inet_sock, SIOCGIFADDR, &ifr);  
    close(inet_sock);
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    return inet_ntoa(sin.sin_addr);
}

void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string)
{
	size_t pos = 0;
	size_t src_size = src_string.size();
	size_t dst_size = dst_string.size();
	while ((pos = replace_string.find(src_string, pos)) != std::string::npos)
	{
		replace_string.replace(pos, src_size, dst_string);
		pos += dst_size;
	}
}

int main(int argc, char *argv[])
{
    if (argc < 3)
    {
        printf("usage: /usr/local/oss_control_client/oss_get_url_tool <remote_path> <expire_time>\n");
       
        std::cout << "error";
        return -1;
    }
    
    
    CConfigFileReader config_file("/etc/oss_install.conf");
    /* 初始化OSS账号信息 */
    //scloud-log-back
    std::string BucketName = config_file.GetConfigName("BucketForLog");
    std::string RegionID = config_file.GetConfigName("RegionID"); 
    //oss-eu-central-1-internal.aliyuncs.com
    std::string Endpoint = config_file.GetConfigName("Endpoint");  
    std::string is_aws = config_file.GetConfigName("IS_AWS"); 
    std::string user = config_file.GetConfigName("User");  
    std::string passwd = config_file.GetConfigName("Password");
    
    CConfigFileReader ip_file("/etc/ip");
    std::string server_ip = ip_file.GetConfigName("SERVERIP");   
      

    if(is_aws.size() > 0 && 1 == atoi(is_aws.c_str()))   //走亚马逊s3
    {
        std::string remote_path = argv[1];
        std::string expire_time = argv[2];
        StringReplace(remote_path, "\\", "");
        int expire = atoi(expire_time.c_str());
        if(expire > 604800) //s3过期时间不得超过一周
        {
            expire = 604800;    
        }
        char data[2048];
        snprintf(data, sizeof(data), "php /usr/local/oss_control_client/s3_url.php '%s' '%s' '%s' '%s' '%s' '%d'", 
			user.c_str(),passwd.c_str(),RegionID.c_str(),BucketName.c_str(),remote_path.c_str(),expire);
        if (system(data) < 0)
        {
            printf("Run aws-cli error\n");
            return -1;
        }
        return 0;
    }

    std::ofstream infile("/var/log/oss_upload_client_log/upload.log", std::ios::app);
    time_t rawtime;
    struct tm *info;
    time( &rawtime );
    info = localtime( &rawtime );
    std::string log_time = asctime(info);
    log_time.pop_back();//去掉换行
    log_time += "  ";
    
    /* 初始化OSS账号信息 */
    std::string AccessKeyId = user;
    std::string AccessKeySecret = passwd;
  

    /* 初始化网络等资源 */
    InitializeSdk();

    ClientConfiguration conf;
    OssClient client(Endpoint, AccessKeyId, AccessKeySecret, conf);


    std::string ObjectName = GetEth1IPAddr();
    ObjectName += "/";
    ObjectName += argv[1];
    StringReplace(ObjectName, "\\", "");
    std::string expire_time = argv[2];
    time_t now_time = time(NULL);

    auto outcome = client.GeneratePresignedUrl(BucketName, ObjectName, now_time+atoi(expire_time.c_str()));
    std::string url = outcome.result();
    if (!outcome.isSuccess()) {
        
        ShutdownSdk();
        infile.close();
        return -1;   
    }
    
    printf("%s\n",url.c_str());
    ShutdownSdk();  
    infile.close();
    return 0;

}








