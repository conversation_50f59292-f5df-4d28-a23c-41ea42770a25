#include <alibabacloud/oss/OssClient.h>
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/time.h>
#include <sys/types.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include<fstream>
#include <ctime>

#include "AkLogging.h"
#include "ConfigFileReader.h"

using namespace AlibabaCloud::OSS;


std::string GetEth1IPAddr()
{
    int inet_sock;
    struct sockaddr_in sin;
    struct ifreq ifr;  
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);  
    strncpy(ifr.ifr_name, "eth0", IFNAMSIZ);
    ifr.ifr_name[IFNAMSIZ - 1] = 0;
    ioctl(inet_sock, SIOCGIFADDR, &ifr);  
    close(inet_sock);
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    return inet_ntoa(sin.sin_addr);
}

int main(int argc, char *argv[])
{
    if (argc < 5)
    {
        printf("usage: /usr/local/oss_control_client/oss_upload_tool <ip> <bucketName> <filepath> <remote_path>\n");
        return -1;
    }
    
    std::ofstream infile("/var/log/oss_upload_client_log/upload.log", std::ios::app);

    time_t rawtime;
    struct tm *info;
    time( &rawtime );
    info = localtime( &rawtime );
    std::string log_time = asctime(info);
    log_time.pop_back();//去掉换行
    log_time += "  ";


    CConfigFileReader config_file("/etc/oss_install.conf");
    /* 初始化OSS账号信息 */
    std::string user = config_file.GetConfigName("User");  
    std::string passwd = config_file.GetConfigName("Password"); 
    
    /* 初始化OSS账号信息 */
    /*Discreet
    AccessKey ID
    LTAI5tCtQ9gpHhb3tHcC2qwq
    AccessKey Secret
    ******************************
    */
    std::string AccessKeyId = user;
    std::string AccessKeySecret = passwd;

    //CConfigFileReader config_file("/etc/oss_install.conf");
    /* 初始化OSS账号信息 */
    //scloud-log-back
    std::string BucketName = argv[2];
    //oss-eu-central-1-internal.aliyuncs.com
    std::string Endpoint = argv[1];    
    

        /* 初始化网络等资源 */
    InitializeSdk();

    ClientConfiguration conf;
    OssClient client(Endpoint, AccessKeyId, AccessKeySecret, conf);


    std::string ObjectName = argv[4];
    // <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
    std::string filePath = argv[3];


    /* 上传文件 */
    auto outcome = client.PutObject(BucketName, ObjectName, filePath);
    infile << log_time << ObjectName << "  " << filePath << "\n";
    if (!outcome.isSuccess()) {
        /* 异常处理 */
        infile << log_time << "BucketName:" << BucketName << " Endpoint:" << Endpoint << " filePath:" << filePath;
        infile << log_time << "PutObject fail" << 
        ",code:" <<  outcome.error().Code() << 
        ",message:" <<  outcome.error().Message() << 
        ",requestId:" <<  outcome.error().RequestId() << "\n";
        ShutdownSdk();
        printf("fail!\n");
        return -1;
    }
    else
    {
        auto outcome = client.DoesObjectExist(BucketName, ObjectName);
        
        if (outcome) {
            std::cout << "http://" << BucketName << "." << Endpoint << "/" << ObjectName << std::endl;
            //http://u-fw-device-upgrade.oss-us-west-1.aliyuncs.com/5000/113.30.4.98.rom
        }                              
        else {                         
            std::cout << "The Object does not exist! obj:" << ObjectName << std::endl;
        }
    }

    infile.close();
    /* 释放网络等资源 */
    ShutdownSdk();
    return 0;
}








