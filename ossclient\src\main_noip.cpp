#include <alibabacloud/oss/OssClient.h>
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/time.h>
#include <sys/types.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include<fstream>
#include <ctime>

#include "AkLogging.h"
#include "ConfigFileReader.h"

using namespace AlibabaCloud::OSS;

#define AWS_CONFIG "/usr/local/oss_control_client/aws"

std::string GetEth1IPAddr()
{
    int inet_sock;
    struct sockaddr_in sin;
    struct ifreq ifr;  
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);  
    strncpy(ifr.ifr_name, "eth0", IFNAMSIZ);
    ifr.ifr_name[IFNAMSIZ - 1] = 0;
    ioctl(inet_sock, SIOCGIFADDR, &ifr);  
    close(inet_sock);
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    return inet_ntoa(sin.sin_addr);
}

void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string)
{
	size_t pos = 0;
	size_t src_size = src_string.size();
	size_t dst_size = dst_string.size();
	while ((pos = replace_string.find(src_string, pos)) != std::string::npos)
	{
		replace_string.replace(pos, src_size, dst_string);
		pos += dst_size;
	}
}

int main(int argc, char *argv[])
{
    if (argc < 3)
    {
        printf("usage: /usr/local/oss_control_client/oss_upload_tool <filepath> <remote_path>\n");
        //<local_file_dir>:用于aws_cli docker 访问挂载的目录
        printf("aws usage: /usr/local/oss_control_client/oss_upload_tool <filepath> <remote_path> <local_file_dir>\n");
       
        std::cout << "error";
        return -1;
    }
    
    
    CConfigFileReader config_file("/etc/oss_install.conf");
    /* 初始化OSS账号信息 */
    //scloud-log-back
    std::string BucketName = config_file.GetConfigName("BucketForLog");    
    //oss-eu-central-1-internal.aliyuncs.com
    std::string Endpoint = config_file.GetConfigName("Endpoint");  
    std::string is_aws = config_file.GetConfigName("IS_AWS"); 
    std::string user = config_file.GetConfigName("User");  
    std::string passwd = config_file.GetConfigName("Password");    
       

    if(is_aws.size() > 0 && 1 == atoi(is_aws.c_str()))   //走亚马逊s3
    {
        std::string file_path = argv[1];
        std::string remote_path = argv[2];
        std::string local_file_dir = argv[3];
        char data[2048];
        snprintf(data, sizeof(data), "docker run --rm -i -e LANG=en_US.UTF-8 -v %s:/root/.aws -v %s:%s amazon/aws-cli \
            s3 cp %s s3://%s/%s", 
			AWS_CONFIG, local_file_dir.c_str(), local_file_dir.c_str(), file_path.c_str(), BucketName.c_str(), remote_path.c_str());
        if (system(data) < 0)
        {
            printf("Run aws-cli error\n");
            return -1;
        }
        return 0;
    }

    std::ofstream infile("/var/log/oss_upload_client_log/upload.log", std::ios::app);
    time_t rawtime;
    struct tm *info;
    time( &rawtime );
    info = localtime( &rawtime );
    std::string log_time = asctime(info);
    log_time.pop_back();//去掉换行
    log_time += "  ";
    
    /* 初始化OSS账号信息 */
    std::string AccessKeyId = user;
    std::string AccessKeySecret = passwd;
  

    /* 初始化网络等资源 */
    InitializeSdk();

    ClientConfiguration conf;
    OssClient client(Endpoint, AccessKeyId, AccessKeySecret, conf);


    std::string ObjectName;
    ObjectName += argv[2];
    StringReplace(ObjectName, "\\", "");
    // <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
    std::string filePath = argv[1];
    StringReplace(filePath, "\\", "");

    /* 上传文件 */
    auto outcome = client.PutObject(BucketName, ObjectName, filePath);
    infile << log_time << ObjectName << "  " << filePath << "\n";
    if (!outcome.isSuccess()) {
        /* 异常处理 */
        infile << log_time << "BucketName:" << BucketName << " Endpoint:" << Endpoint << " filePath:" << filePath;
        infile << log_time << " PutObject fail" << 
        ",code:" <<  outcome.error().Code() << 
        ",message:" <<  outcome.error().Message() << 
        ",requestId:" <<  outcome.error().RequestId() << "\n";
        ShutdownSdk();
        std::cout << "error";
        return -1;
    }
    /* 释放网络等资源 */
    ShutdownSdk();
   
    infile.close();
    return 0;
}








