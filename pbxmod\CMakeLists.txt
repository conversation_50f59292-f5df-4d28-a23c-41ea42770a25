cmake_minimum_required(VERSION 2.6)
PROJECT(Base)

AUX_SOURCE_DIRECTORY(./src SRC_LIST1)
AUX_SOURCE_DIRECTORY(./hash SRC_LIST_HASH)
AUX_SOURCE_DIRECTORY(../csbase/grpc SRC_GRPC)
AUX_SOURCE_DIRECTORY(../csbase/etcd SRC_ETCD)
AUX_SOURCE_DIRECTORY(../csbase/cspbxrpc SRC_CSPBXRPC)
AUX_SOURCE_DIRECTORY(../csbase/grpc/cspbxrpc SRC_GRPC_CSPBXRPC)
AUX_SOURCE_DIRECTORY(../csbase/protobuf SRC_PROTOBUF)
AUX_SOURCE_DIRECTORY(../csbase/nsq SRC_NSQ)
AUX_SOURCE_DIRECTORY(../csbase/beanstalk-client SRC_BEANSTALKD)
AUX_SOURCE_DIRECTORY(../csbase SRC_CSBASE)
AUX_SOURCE_DIRECTORY(../csbase/jsoncpp0.5/src/json SRC_JSON)
AUX_SOURCE_DIRECTORY(../csbase/encrypt SRC_ENCRYPT)
AUX_SOURCE_DIRECTORY(../csbase/smarthome SRC_SMARTHOME)
AUX_SOURCE_DIRECTORY(../csbase/Tinyxml SRC_BASE_TINYXML)
AUX_SOURCE_DIRECTORY(../csbase/Character SRC_BASE_CHARACTER)

SET(SRC_MODEL ${SRC_MODEL} ../csbase/model/AkcsHttpRequest.cpp)

SET(LIBRARY_OUTPUT_PATH ./lib)

ADD_DEFINITIONS( -std=c++11 -g -W -Wall -Wno-unused-parameter -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO -shared -fPIC
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL
-DAC_HAS_DEBUG -DLINUX_DAEMON
-DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -DAKCS_MAKE_PBX_MOD)

SET(BASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../)
link_directories(${BASE_SOURCE_DIR}/csbase/thirdlib ${BASE_SOURCE_DIR}/csbase)

INCLUDE_DIRECTORIES(./include ./hash ../csbase ../csbase/pbxmod  ../csbase/cspbxrpc ../csbase/grpc/cspbxrpc ../csbase/grpc ../csbase/grpc/include  ../csbase/etcd ../csbase/evpp ../csbase/nsq ../csbase/grpc/gens
 ../csbase/protobuf ../csbase/beanstalk-client  ../csbase/jsoncpp0.5/include ../csbase/evpp ../csbase/curl ../csbase/gid ../csbase/smarthome ../csbase/model  ../csbase/encrypt)
set(LIBRARIES evpp event glog pthread protobuf etcd-cpp-api cpprest boost_system ssl crypto gpr grpc grpc++ curl)

ADD_LIBRARY(akpbxmod SHARED ${SRC_LIST1} ${SRC_ETCD} ${SRC_PROTOBUF} ${SRC_CSPBXRPC} ${SRC_GRPC_CSPBXRPC} ${SRC_NSQ}  ${SRC_BEANSTALKD} 
${SRC_CSBASE} ${SRC_JSON} ${SRC_ENCRYPT} ${SRC_MODEL} ${SRC_SMARTHOME} ${SRC_BASE_TINYXML} ${SRC_BASE_CHARACTER} ${SRC_LIST_HASH})

set_target_properties(akpbxmod PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/freeswitch/akcslibs")
target_link_libraries(akpbxmod ${LIBRARIES})

