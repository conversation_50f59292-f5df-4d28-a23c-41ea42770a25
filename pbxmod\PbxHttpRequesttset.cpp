#include <iostream>
#include <string>
#include <map>
#include <curl/curl.h>
#include <curl/types.h>
#include <curl/easy.h>
#include <sstream>
#include <stdlib.h>
#include <string.h>

typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespuestKV;
#define HTTP_HEAD_API_VERSION "api-version:6000"

struct MemoryStruct 
{
    char *memory;
    size_t size;
    MemoryStruct()
    {
        memory = (char *)malloc(1);
        size = 0;
    }
    ~MemoryStruct()
    {
        free(memory);
        memory = NULL;
        size = 0;
    }
};

size_t WriteMemoryCallback(void *ptr, size_t size, size_t nmemb, void *data)
{
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *)data;

    mem->memory = (char *)realloc(mem->memory, mem->size + realsize + 1);
    if (mem->memory) 
    {
        memcpy(&(mem->memory[mem->size]), ptr, realsize);
        mem->size += realsize;
        mem->memory[mem->size] = 0;
    }
    return realsize;
}

int HttpGetRequest(const std::string &urlhead, const HttpRespuestKV &kv,  std::string &respone)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        std::cout  << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();

    if( NULL == pcurl)
    {
        std::cout  << "Init CURL failed...";
        return -1;
    }

    std::stringstream url;
    url << urlhead << "?";
    for (const auto& tmpkv : kv)
    {
        url << "&" << tmpkv.first  << "=" << tmpkv.second;
    }

    std::cout  << "http get:" << url.str();
    
    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 3L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长 
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数

    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.str().c_str() ); //需要获取的URL地址

    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false); // 
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证

    curl_slist *pList = NULL;
    pList = curl_slist_append(pList,"Accept-Encoding:gzip, deflate, sdch");  
    pList = curl_slist_append(pList,"Connection:keep-alive");
    pList = curl_slist_append(pList, HTTP_HEAD_API_VERSION);
    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, pList); 

    res = curl_easy_perform(pcurl);  //执行请求

    long res_code=0;
    res=curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);

    //正确响应后，请请求转写成本地文件的文件
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201))
    {
        respone = data_trunk.memory;
        ret = 0;
        std::cout << "ret:" << respone << res_code;
    }
    else
    {
        std::cout << "error ret http code=" << res_code;
    }
    
    curl_slist_free_all(pList); 
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}

#define PBX_HTTP_URL_UID_STATUS       "/app-status"
#define PBX_HTTP_URL_APP_WAKEUP       "/app-wakeup"
#define PBX_HTTP_URL_DEVICE_WAKEUP    "/wakeup-device"
#define PBX_HTTP_URL_WRITE_HISTORY    "/write-history"
#define PBX_HTTP_URL_LANDLINE_STATUS  "/landline-status"
//http parma
#define PBX_HTTP_PARMA_STATUS     "Status"
#define PBX_HTTP_PARMA_UID        "Sip"
#define PBX_HTTP_PARMA_CALLER     "Caller"
#define PBX_HTTP_PARMA_CALLEE     "Callee"
#define PBX_HTTP_PARMA_CALLER_NAME     "CallerName"
#define PBX_HTTP_PARMA_CALLED          "Called"
#define PBX_HTTP_PARMA_START_TIME     "StartTime"
#define PBX_HTTP_PARMA_ANSWER_TIME     "AnswerTime"
#define PBX_HTTP_PARMA_BILL_SECOND     "BillSecond"
#define PBX_HTTP_PARMA_APPTYPE     "AppType"
#define PBX_HTTP_PARMA_PHONE     "Phone"
#define PBX_HTTP_PARMA_TRACEID     "TraceId"


int main()
{
    int status = 0;
    std::string respone;
    std::string url = "https://czcloud.uat.akubela.com/api/akuvox-integration/v1.0/invoke/akuvox-integration/method/extern";
    url += PBX_HTTP_URL_UID_STATUS;
    
    HttpRespuestKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_UID, "5938101029"));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_TRACEID, std::to_string(1123)));
    HttpGetRequest(url, kv, respone);
    std::cout << "retxxx:" << respone;
}
