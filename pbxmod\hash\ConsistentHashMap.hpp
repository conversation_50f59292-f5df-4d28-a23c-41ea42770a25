#include <map>
#include <string>
#include <list>
#include <functional> 
#include <algorithm>

#ifndef __AKCS_BASE_CONSISTENT_HASH_H__
#define __AKCS_BASE_CONSISTENT_HASH_H__

template <typename T,
          typename Hash,
          typename Alloc = std::allocator<std::pair<const typename Hash::result_type,T > > >
class ConsistentHash
{
public:
    typedef typename Hash::result_type size_type;
    typedef std::map<size_type,T,std::less<size_type>,Alloc> map_type;
    typedef typename map_type::value_type value_type;
    typedef value_type& reference;
    typedef const value_type& const_reference;
    typedef typename map_type::iterator iterator;
    typedef typename map_type::reverse_iterator reverse_iterator;
    typedef Alloc allocator_type;

public:
    ConsistentHash() {}
    ~ConsistentHash() {}
public:
    std::size_t size() const {
        return nodes_.size();
    }
    bool empty() const {
        return nodes_.empty();
    }
    std::pair<iterator,bool> insert(const T& node) {
        size_type hash = hasher_(node);
        return nodes_.insert(value_type(hash,node)); //map的value_type就是跟pair一样
    }
    void erase(iterator it) {
        nodes_.erase(it);
    }
    std::size_t erase(const T& node) {
        size_type hash = hasher_(node);
        return nodes_.erase(hash);
    }
    iterator find(size_type hash) {
        if(nodes_.empty()) {
            return nodes_.end();
        }
        iterator it = nodes_.lower_bound(hash); //第一个大于或者等于hash的虚拟节点
        if (it == nodes_.end()) {
            it = nodes_.begin();
        }
        return it;
    }
    void clear() {
		nodes_.clear();
    }
    
    iterator begin() { return nodes_.begin(); }
    iterator end() { return nodes_.end(); }
    reverse_iterator rbegin() { return nodes_.rbegin(); }
    reverse_iterator rend() { return nodes_.rend(); }
private:
    Hash hasher_;
    map_type nodes_;
};

#endif //__AKCS_BASE_CONSISTENT_HASH_H__
