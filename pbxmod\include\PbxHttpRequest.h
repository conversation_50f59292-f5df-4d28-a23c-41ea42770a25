



#ifndef PBX_HTTP_REQUEST_H_
#define PBX_HTTP_REQUEST_H_


#include "PbxMsgDef.h"

#ifdef __cplusplus
extern "C" {
#endif


int HttpQueryUidStatus(char *url, char* uid, uint64_t traceid);
int HttpWakeupApp(char *url, AKCS_WAKEUP_APP* wakeup, uint64_t traceid);
int HttpHangupApp(char *url, AKCS_HANGUP_APP* hangup, uint64_t traceid);
int HttpQueryLandlineStatus(char *url, AKCS_LANDLINE_STATUS* landline, uint64_t traceid);
void HttpWriteCallHistory(char *url, AKCS_CALL_HISTORY* history, uint64_t traceid);



#ifdef __cplusplus
}
#endif



#endif











