#ifndef PBX_MSG_CONTROL_H_
#define PBX_MSG_CONTROL_H_


#include "PbxMsgDef.h"

#ifdef AKCS_MAKE_PBX_MOD
#include <string>
const std::string PBX_SIP_BUSSINESS = "pbx_http";
const uint32_t PBX_SIP_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t PBX_SIP_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
const uint32_t PBX_SIP_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s
const std::string AKCS_ATTACK_IP = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用
const uint32_t AKCS_ATTACK_IP_RELEASE = 172800; //2天后将攻击者的ip从iptables列表中删除掉,60 * 60 * 24 *2s
#endif

#ifdef PBX_MAKE_OPENSIPS
typedef unsigned int        uint32_t;
typedef unsigned long int        uint64_t;
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*模块初始化*/
void pbx_mod_init(PBX_INIT_MOD_TYPE type);
/*模块反初始化*/
void pbx_mod_deinit(PBX_INIT_MOD_TYPE type);

int QueryUidStatus(AKCS_UID_STATUS& uid_status, uint64_t traceid);
int WakeupApp(AKCS_WAKEUP_APP* wakeup, uint64_t traceid);
void HangupApp(AKCS_HANGUP_APP* hangup, uint64_t traceid);
int QueryLandlineStatus(AKCS_LANDLINE_STATUS* landline, uint64_t traceid);
int QueryLandlineNumber(AKCS_LANDLINE_NUMBER* landline, uint64_t traceid);
int QueryMainSiteSip(char* sip, char* main_sip, uint64_t traceid);
void WriteCallHistory(AKCS_CALL_HISTORY* history, uint64_t traceid, int apptype);
int QuerySmartHomeUidStatus(uint64_t traceid, char* uid, char *caller, char *real_callee, const std::string &command);
int WakeupSmartHomeApp(AKCS_WAKEUP_APP* wakeup, uint64_t traceid);
int HangupSmartHomeApp(AKCS_HANGUP_APP* hangup, uint64_t traceid);
uint64_t GetTraceid();
//added by chenyc,2019-11-27,pbx关于防止攻击的接口.
void InitBussiness();
int AddBussiness(char* ip);
//PBX接入业务告警系统,因为没有字符串的长度参数，调用者确保node_srv跟alarm_desc字符串指针确保以'/0'结尾
void AddMonitorAlarm(char* node_srv, char* alarm_desc);

/**判断url_host是否在opensips的列表里面*/
bool HostInOpensipsList(const char *url_host);
//consistent hash
void InitHash(int num);
void ReloadHash(int num);
int GetConsistentHash(const std::string& key);


#ifdef __cplusplus
}
#endif
#endif
