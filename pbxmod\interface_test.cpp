#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "PbxMsgControl.h"

int main(int argc, char* argv[])
{
    int count = 1;
    if (argc > 1)
    {
        count = atoi(argv[1]);
    }
    printf("start!!!!! %d\n", count);
    pbx_mod_init(PBX_MOD_FREESWITCH);

    for (int i = 0; i < count; i++)
    {
        AKCS_WAKEUP_APP x = {"6201100004", "6201100002", "R29", 1};
        WakeupApp(&x, 11111);

        AKCS_LANDLINE_STATUS x2 = {"6201100002", "13025259878"};
        QueryLandlineStatus(&x2, 22222);

        AKCS_CALL_HISTORY x3 = {10, "6201100002", "6201100004", "6201100004", "2020-04-16 10:10:10", "2020-04-16 10:10:15"};
        WriteCallHistory(&x3, 33333);

        sleep(1);
    }

    printf("end!!!!!\n");
}






