g++  interface_test.cpp -I./include -I../csbase \
-I../csbase/etcd -I../csbase/evpp \
-I../csbase/nsq -I../csbase/csmain \
-I../csbase/session -I../csbase/grpc/gens \
-I../csbase/grpc/csmain -I../csbase/grpc/cssession \
-I../csbase/grpc \
-I../csbase/beanstalk-client \
-L../csbase/thirdlib -L../csbase  -L../csbase/evpp/lib  -L./lib \
-lakpbxmod  -lglog -lpthread -lmysqlclient \
  -levpp -lglog -lssl -lcrypto -levent  -lgpr -lgrpc -lgrpc++ \
-lprotobuf -lboost_system -lcpprest -letcd-cpp-api -levnsq \
-DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -std=c++11

g++ interface_test.cpp -o pbxmod_demo -I ./include -lakpbxmod -L ./lib


