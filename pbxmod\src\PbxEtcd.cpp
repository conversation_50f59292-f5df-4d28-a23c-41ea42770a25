#include <string.h>
#include <signal.h>
#include <etcd/Client.hpp>
#include <evnsq/producer.h>
#include "PbxMsgControl.h"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include "PbxEtcd.h"
#include "PbxMsgDef.h"
#include <evnsq/producer.h>
#include "RouteMqProduce.h"
#include "cspbx_rpc_client.h"
#include "cspbx_rpc_client_mng.h"
#include "util.h"
#include "PbxMsgControl.h"
#include "AkLogging.h"
#include "smarthome/SmarthomeHandle.h"



#ifdef __cplusplus
extern "C" {
#endif

extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_ak_srv_opensips;
extern const char *g_ak_srv_cspbxrpc;
extern const char *g_ak_srv_smg;

extern CSPBX_CONF gstPBXConf;
extern AKCS_OPENSIPS_INFO g_opensips_info;

void PbxRpcClientSrvInit(const std::set<std::string>& cspbx_rpc_inner_addrs)
{
    for (const auto& cspbx_rpc_addr : cspbx_rpc_inner_addrs)
    {
        PbxRpcClientPtr cspbx_rpc_cli_ptr(new PbxRpcClient(cspbx_rpc_addr));
        PbxRpcClientMng::Instance()->AddPbxRpcSrv(cspbx_rpc_addr, cspbx_rpc_cli_ptr);
    }
}

//************:5070&[]:5070&remoteconfig-dev.akuvox.com:5070
void OpensipsSrvInit(const std::vector<std::string>& opensips_addrs)
{
    g_opensips_info.clear();
    for (const auto &opensips_node : opensips_addrs)
    {
        std::set<std::string> items;
        SplitString(opensips_node, "&", items);

        for (const auto &item : items)
        {
            std::size_t found = item.find(":");
            if (found != std::string::npos)
            {
                std::size_t ipv6 = item.find("]");
                if (ipv6 != std::string::npos)
                {
                    std::string node_ip = item.substr(1, ipv6 - 1);
                    g_opensips_info.insert(node_ip);
                    AK_LOG_INFO << "Add opensips ip/domain:" << node_ip;
                }
                else
                {
                    std::string node_ip = item.substr(0, found);
                    g_opensips_info.insert(node_ip);
                    AK_LOG_INFO << "Add opensips ip/domain:" << node_ip;
                }
            }            
        }
    }
}

void UpdateOpensipsSrvList()
{
    std::vector<std::string> opensips_addrs;
    if (g_etcd_cli_mng->GetAllOpsSrvs(opensips_addrs) == 0)
    {
        OpensipsSrvInit(opensips_addrs);
    }
}

void UpdatePbxRpcSrvList()
{
    std::set<std::string> cspbx_rpc_addrs;
    if (g_etcd_cli_mng->GetAllPbxRpcInnerSrvs(cspbx_rpc_addrs) == 0)
    {
        PbxRpcClientMng::Instance()->UpdatePbxRpcSrv(cspbx_rpc_addrs);
    }
}

void UpdateSmgConfFromConfSrv()
{
    std::string smg_addr;
    g_etcd_cli_mng->LoadSrvSmgConf(smg_addr);
    //reload
    SmarthomeHandle::GetInstance().SetSmgConf(smg_addr);  
    AK_LOG_INFO << "Add smarthome gateway ip/domain:" << smg_addr;
}

//含csroute cspush的连接
void EtcdSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstPBXConf.etcd_server);//"http://ip:port"

    // cspbxrpc
    std::set<std::string> cspbx_rpc_inner_addrs;
    if (g_etcd_cli_mng->GetAllPbxRpcInnerSrvs(cspbx_rpc_inner_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    PbxRpcClientSrvInit(cspbx_rpc_inner_addrs);
    
    //cspbx rpc async
    std::thread rpc_async_thread = std::thread(AsyncCompleteCsPbxRpc);

    std::vector<std::string> opensips_addrs;
    if (g_etcd_cli_mng->GetAllOpsSrvs(opensips_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    OpensipsSrvInit(opensips_addrs);

    //家居网关
    UpdateSmgConfFromConfSrv();

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_opensips, UpdateOpensipsSrvList);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_smg, UpdateSmgConfFromConfSrv);
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_cspbxrpc, UpdatePbxRpcSrvList);

    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void EtcdSrvInitForOpensips()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstPBXConf.etcd_server);//"http://ip:port"

    //reg to etcd
    /*放到安装脚本统一注册
    std::string inner_addr = GetEth0IPAddr();
    char outer_addr[64] = {0};
    ::snprintf(outer_addr, 64, "%s:%s&[%s]:%s", gstPBXConf.opensips_out_ip, "5070",  gstPBXConf.opensips_out_ip, "5070");
    //char inner_reg_info[64] = {0};
    char outer_reg_info[64] = {0};
    //::snprintf(inner_reg_info, 64, "%s%s", "/akcs/opensips/innerip/", gstPBXConf.opensips_out_ip);
    ::snprintf(outer_reg_info, 64, "%s%s", "/akcs/opensips/outerip/", gstPBXConf.opensips_out_ip);
    RegSrv2Etcd(outer_reg_info, outer_addr, 10, csbase::REG_OUTER);
    */
}


#ifdef __cplusplus
}
#endif

