#include <iostream>
#include <string>
#include <map>
#include <curl/curl.h>
#include <curl/types.h>
#include <curl/easy.h>
#include <sstream>
#include <stdlib.h>
#include <string.h>
#include "json/json.h"
#include "AkcsPbxHttpMsg.h"
#include "PbxMsgDef.h"
#include "PbxHttpRequest.h"
#include "AkLogging.h"
#include "util.h"


#ifdef __cplusplus
extern "C" {
#endif


extern CSPBX_CONF gstPBXConf;
typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespuestKV;
#define HTTP_HEAD_API_VERSION "api-version:6000"

struct MemoryStruct 
{
    char *memory;
    size_t size;
    MemoryStruct()
    {
        memory = (char *)malloc(1);
        size = 0;
    }
    ~MemoryStruct()
    {
        free(memory);
        memory = NULL;
        size = 0;
    }
};

size_t WriteMemoryCallback(void *ptr, size_t size, size_t nmemb, void *data)
{
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *)data;

    mem->memory = (char *)realloc(mem->memory, mem->size + realsize + 1);
    if (mem->memory) 
    {
        memcpy(&(mem->memory[mem->size]), ptr, realsize);
        mem->size += realsize;
        mem->memory[mem->size] = 0;
    }
    return realsize;
}

int HttpGetRequest(const std::string &urlhead, const HttpRespuestKV &kv,  std::string &respone)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return ret;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();

    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return ret;
    }

    std::stringstream url;
    url << urlhead << "?";
    for (const auto& tmpkv : kv)
    {
        url << "&" << tmpkv.first  << "=" << tmpkv.second;
    }

    AK_LOG_INFO << "http get:" << url.str();
    
    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 3L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长 
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数

    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.str().c_str() ); //需要获取的URL地址
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false); // 
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证

    curl_slist *pList = NULL;
    pList = curl_slist_append(pList,"Accept-Encoding:gzip, deflate, sdch");  
    pList = curl_slist_append(pList,"Connection:keep-alive");
    pList = curl_slist_append(pList, HTTP_HEAD_API_VERSION);
    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, pList); 

    res = curl_easy_perform(pcurl);  //执行请求

    long res_code=0;
    res=curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);

    //正确响应后，请请求转写成本地文件的文件
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201)) //TODO:res_code一直返回0    
    {
        respone = data_trunk.memory;
        ret = 0;
        AK_LOG_INFO << "ret:" << respone;
    }
    else
    {
        AK_LOG_INFO << "error ret http code: " << res_code;
    }
    
    curl_slist_free_all(pList); 
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}

int HttpPostRequest(const std::string &url, const std::string &data,  std::string &respone)
{
    int ret = -1;
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if(CURLE_OK != res)
    {
        AK_LOG_WARN << "curl init failed";
        return -1;
    }

    CURL *pcurl = NULL;
    pcurl = curl_easy_init();

    if( NULL == pcurl)
    {
        AK_LOG_WARN << "Init CURL failed...";
        return -1;
    }

    AK_LOG_INFO << "http post:" << url << " datas:" << data;
    
    curl_easy_setopt(pcurl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_easy_setopt(pcurl, CURLOPT_TIMEOUT, 3L);//请求超时时长
    curl_easy_setopt(pcurl, CURLOPT_CONNECTTIMEOUT, 3L); //连接超时时长 
    curl_easy_setopt(pcurl, CURLOPT_HEADER, 0L);  //若启用，会将头文件的信息作为数据流输出
    curl_easy_setopt(pcurl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);  //得到请求结果后的回调函数
    curl_easy_setopt(pcurl, CURLOPT_POSTFIELDS, data.c_str());


    MemoryStruct data_trunk;  //请求结果的保存格式
    curl_easy_setopt(pcurl, CURLOPT_WRITEDATA, &data_trunk);

    curl_easy_setopt(pcurl, CURLOPT_NOSIGNAL, 1L); //关闭中断信号响应
    curl_easy_setopt(pcurl, CURLOPT_VERBOSE, 1L); //启用时会汇报所有的信息
    curl_easy_setopt(pcurl, CURLOPT_URL, url.c_str() ); //需要获取的URL地址
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYHOST, false); // 
    curl_easy_setopt(pcurl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证


    curl_slist *pList = NULL;
    pList = curl_slist_append(pList,"Accept-Encoding:gzip, deflate, sdch"); 
    pList = curl_slist_append(pList,"Connection:keep-alive");
    pList = curl_slist_append(pList,"Content-Type:application/json");	
    pList = curl_slist_append(pList, HTTP_HEAD_API_VERSION);
    curl_easy_setopt(pcurl, CURLOPT_HTTPHEADER, pList); 

    res = curl_easy_perform(pcurl);  //执行请求

    long res_code=0;
    res=curl_easy_getinfo(pcurl, CURLINFO_RESPONSE_CODE, &res_code);

    //正确响应后，请请求转写成本地文件的文件
    if(( res == CURLE_OK ) && (res_code == 200 || res_code == 201)) //TODO:res_code一直返回0   
    {
        respone = data_trunk.memory;
        ret = 0;
        AK_LOG_INFO << "ret:" << respone;
    }
    else
    {
        AK_LOG_INFO << "error ret http code: " << res_code;
    }
    curl_slist_free_all(pList); 
    curl_easy_cleanup(pcurl);
    curl_global_cleanup();
    return ret;
}


int HttpQueryUidStatus(char* http_head_url, char* uid, uint64_t traceid)
{
    if (!uid)
    {
        return 0;
    }

    int status = -1;
    std::string respone;
    std::string url = http_head_url;
    url += PBX_HTTP_URL_UID_STATUS;
    
    HttpRespuestKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_UID, uid));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_TRACEID, std::to_string(traceid)));
    int ret = HttpGetRequest(url, kv, respone);
    if (ret == -1)
    {
        return status;
    }

    Json::Reader reader;
    Json::Value root;  
    if (!reader.parse(respone, root))
    {
        //失败的时候直接当作不在线，然后进行唤醒
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
        return status;
    }
    if (root[HTTP_RET_PARMA_RESULT_CODE] == HTTP_RET_RESULT_SUCC)
    {
        status = atoi(root[HTTP_RET_PARMA_DATA][PBX_HTTP_PARMA_STATUS].asCString());
    }
    else
    {
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
    }

    return status;
}

int HttpWakeupApp(char* http_head_url, AKCS_WAKEUP_APP* wakeup, uint64_t traceid)
{
    if (!wakeup)
    {
        return 0;
    }
    int status = -1;
    std::string respone;
    std::string data;
    std::string url = http_head_url;
    std::string url_function = PBX_HTTP_URL_APP_WAKEUP;
    url += url_function;
/*
    Json::Value item;
    Json::FastWriter w;
    item[PBX_HTTP_PARMA_CALLEE] = wakeup->callee_sip;
    item[PBX_HTTP_PARMA_CALLER] = wakeup->caller_sip;
    item[PBX_HTTP_PARMA_CALLER_NAME] = wakeup->nick_name_location;
    item[PBX_HTTP_PARMA_APPTYPE] = wakeup->app_type;
    item[PBX_HTTP_PARMA_TRACEID] = std::to_string(traceid);
    data = w.write(item);
    HttpPostRequest(url, data, respone);
*/
    HttpRespuestKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_CALLEE, wakeup->callee_sip));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_CALLER, wakeup->caller_sip));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_CALLER_NAME, URLEncode(wakeup->nick_name_location)));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_APPTYPE, std::to_string(wakeup->app_type)));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_TRACEID, std::to_string(traceid)));
    int ret = HttpGetRequest(url, kv, respone);
    if (ret == -1)
    {
        return status;
    }

    Json::Reader reader;
    Json::Value root; 
    if (!reader.parse(respone, root))
    {
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
        return status;
    }
    
    if (root[HTTP_RET_PARMA_RESULT_CODE] == HTTP_RET_RESULT_SUCC)
    {
        status = atoi(root[HTTP_RET_PARMA_DATA][PBX_HTTP_PARMA_STATUS].asCString());
    }
    else
    {
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
    }  
    return status;
}

int HttpQueryLandlineStatus(char* http_head_url, AKCS_LANDLINE_STATUS* landline, uint64_t traceid)
{
    if (!landline)
    {
        return 0;
    }

    int status = 0;
    std::string respone;
    std::string url = http_head_url;
    url += PBX_HTTP_URL_LANDLINE_STATUS;
    
    HttpRespuestKV kv;
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_PHONE, landline->phone_number));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_CALLER, landline->caller_sip));
    kv.insert(std::map<std::string, std::string>::value_type(PBX_HTTP_PARMA_TRACEID, std::to_string(traceid)));
    HttpGetRequest(url, kv, respone);

    Json::Reader reader;
    Json::Value root;  
    if (!reader.parse(respone, root))
    {
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
        return status;
    }
    
    if (root[HTTP_RET_PARMA_RESULT_CODE] == HTTP_RET_RESULT_SUCC)
    {
        status = atoi(root[HTTP_RET_PARMA_DATA][PBX_HTTP_PARMA_STATUS].asCString());
    }
    else
    {
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
    } 

    return status;
}

void HttpWriteCallHistory(char *http_head_url, AKCS_CALL_HISTORY* history, uint64_t traceid)
{
    if (!history)
    {
        return;
    }

    std::string respone;
    std::string data;
    std::string url = http_head_url;
    url += PBX_HTTP_URL_WRITE_HISTORY;

    Json::Value item;
    Json::FastWriter w;
    item[PBX_HTTP_PARMA_CALLEE] = history->callee_sip;
    item[PBX_HTTP_PARMA_CALLER] = history->caller_sip;
    item[PBX_HTTP_PARMA_CALLED] = history->called_sip;
    item[PBX_HTTP_PARMA_ANSWER_TIME] = history->answer_time;
    item[PBX_HTTP_PARMA_START_TIME] = history->start_time;
    item[PBX_HTTP_PARMA_BILL_SECOND] = history->bill_second;
    item[PBX_HTTP_PARMA_TRACEID] = std::to_string(traceid);

    data = w.write(item);
    HttpPostRequest(url, data, respone);

    Json::Reader reader;
    Json::Value root;  
    if (!reader.parse(respone, root))
    {
        AK_LOG_WARN << "respone error,traceid:" << traceid << " data:" << respone;
        return;
    }
    return;

}



#ifdef __cplusplus
}
#endif

