#include "PbxMsgControl.h"
#include "PbxMsgDef.h"
#include "AK.Route.pb.h"
#include "ConfigFileReader.h"
#include <sys/stat.h>
#include <errno.h>
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "gid/SnowFlakeGid.h"
#include <evnsq/message.h>
#include <evnsq/producer.h>
#include "AkcsMonitor.h"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include "PbxEtcd.h"
#include "PbxMsgDef.h"
#include <evnsq/producer.h>
#include "RouteMqProduce.h"
#include "cspbx_rpc_client.h"
#include "cspbx_rpc_client_mng.h"
#include "util.h"
#include "PbxMsgControl.h"
#include "AkLogging.h"
#include "PbxHttpRequest.h"
#include "AkcsDnsResolver.h"
#include "AkcsCommonDef.h"
#include "smarthome/SmarthomeHandle.h"
#include "ConsistentHash.h"

#ifdef __cplusplus
extern "C" {
#endif

AKCS_OPENSIPS_INFO g_opensips_info;
CSPBX_CONF gstPBXConf;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
Beanstalk::Client* g_beanstalkd_client_ptr = nullptr;
int g_etcd_dns_res = 0;
akcs_consistent_hash::QueueConsistentHash* g_pbx_consistent_hash = nullptr;


typedef enum
{
	AKCS_DEVICE_TYPE_NORMAL = 0,
	AKCS_DEVICE_TYPE_APP,
	AKCS_DEVICE_TYPE_APP_SDK,
	AKCS_DEVICE_TYPE_APP_ANDROID_CHINA,
	AKCS_DEVICE_TYPE_BELAHOME,
	AKCS_DEVICE_TYPE_SMARTHOME_LOCK_WAKEUP,
    AKCS_DEVICE_TYPE_BELAHOME_APP_SDK

}SWITCH_AKCS_DEVICE_TYPE;


int OnRouteMQAlarmMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void ProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQAlarmMessage);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}

int QuerySmartHomeUidStatus(uint64_t traceid, char* callee, char *caller, char *real_callee, const std::string &command)
{
    int status = 0;
    if (gstPBXConf.enable_smarthome_gateway)
    {
        status = SmarthomeHandle::GetInstance().GetMobileStatus(traceid, callee, caller, real_callee, command);
        if (status == -1)
        {
            status = SmarthomeHandle::GetInstance().GetMobileStatus(traceid, callee, caller, real_callee, command);
        }
    }
    else
    {
        status = HttpQueryUidStatus(gstPBXConf.smarthome_http_head_url, callee, traceid);
        if (status == -1)
        {
            status = HttpQueryUidStatus(gstPBXConf.smarthome_http_head_url, callee, traceid);
        }
    }
    return status;
}

int QueryUidStatus(AKCS_UID_STATUS& uid_status, uint64_t traceid)
{
    if (strlen(uid_status.uid) == 0)
    {
        return 0;
    }

    int status = 0;
    if (gstPBXConf.is_nearby_instance)
    {
        status = HttpQueryUidStatus(gstPBXConf.http_head_url, uid_status.uid, traceid);
    }
    else if(AKCS_DEVICE_TYPE_BELAHOME_APP_SDK == uid_status.callee_app_type)
    {
        //如果是家居sdk直接调用家居的查找，这个流程要先于下面的else,因为下面的else是主被叫都判断了会先判断成功
        status = QuerySmartHomeUidStatus(traceid, uid_status.uid, uid_status.caller, uid_status.original_callee, SMARTHOME_HANDLE_PBX_GET_SDK_STATUS);
        return status;
    }    
    //多套房情况下从站点未注册，这里app_type获取不到，因此也要判断主站点的app type
    else if (AKCS_DEVICE_TYPE_BELAHOME == uid_status.callee_app_type || AKCS_DEVICE_TYPE_BELAHOME == uid_status.original_caller_app_type
    || AKCS_DEVICE_TYPE_BELAHOME == uid_status.main_site_caller_app_type   || strlen(uid_status.uid) == 15) // 兼容家居室内机呼叫SL50锁(长sip)
    {
        //主叫或被叫是纯家居账号,直接返回查询家居接口结果
        if (strlen(uid_status.caller) == 15 || strlen(uid_status.uid) == 15)
        {
            //TODO：目前不知道家居的账号是app还是设备 callee_type/caller_type区别不了家居的设备类型
            if(AKCS_DEVICE_TYPE_BELAHOME != uid_status.callee_app_type)
            {
                return 1;//不是app直接返回在线
            }        
            //在拨号规则已经做了拦截了,呼叫不到对讲的设备,可以呼叫到对讲app
            status = QuerySmartHomeUidStatus(traceid, uid_status.uid, uid_status.caller, uid_status.original_callee, SMARTHOME_HANDLE_PBX_GET_MOBILE_STATUS);
        }
        else
        {
            //都是对讲账号,查询对讲接口
            int akcs_status = 0;
            PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
            if (rpc_client)
            {
                akcs_status = rpc_client->QueryUidStatus(uid_status.uid, traceid, uid_status.caller, uid_status.original_callee, uid_status.callee_app_type);
            }
            
            //主被叫存在过期,且对方type为0,1,50的直接返回;其他情况返回家居接口结果
            if ((akcs_status == APP_STATE_CALLER_EXPIRE && (strcmp(uid_status.callee_type, "0") == 0 || strcmp(uid_status.callee_type, "1") == 0 || strcmp(uid_status.callee_type, "50") == 0))
              ||(akcs_status == APP_STATE_CALLEE_EXPIRE && (strcmp(uid_status.caller_type, "0") == 0 || strcmp(uid_status.caller_type, "1") == 0 || strcmp(uid_status.caller_type, "50") == 0)) )
            {
                return akcs_status;
            }
            else
            {
                if(strcmp(uid_status.callee_type, "6") != 0)
                {
                    return 1;
                }             
                status = QuerySmartHomeUidStatus(traceid, uid_status.uid, uid_status.caller, uid_status.original_callee, SMARTHOME_HANDLE_PBX_GET_MOBILE_STATUS);
            }
        }
    }    
    else
    {
        PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
        if (rpc_client)
        {
            status = rpc_client->QueryUidStatus(uid_status.uid, traceid, uid_status.caller, uid_status.original_callee, uid_status.callee_app_type);
        }
    }

    return status;
}

int WakeupSmartHomeApp(AKCS_WAKEUP_APP* wakeup, uint64_t traceid)
{
    int status = 0;
    if (gstPBXConf.enable_smarthome_gateway)
    {
        //走家居网关
        std::string command = SMARTHOME_HANDLE_PBX_WAKEUP_MOBILE;
        if(wakeup->app_type == AKCS_DEVICE_TYPE_SMARTHOME_LOCK_WAKEUP)
        {
            command = SMARTHOME_HANDLE_PBX_WAKEUP_SMARTLOCK;
        }
        else if (wakeup->app_type == AKCS_DEVICE_TYPE_BELAHOME_APP_SDK)
        {
            command = SMARTHOME_HANDLE_PBX_WAKEUP_SDK;
        }
        status = SmarthomeHandle::GetInstance().WakeupMobile(traceid, wakeup->caller_sip, wakeup->callee_sip, wakeup->nick_name_location, command);
        if (status == -1)
        {
            // 请求家居接口失败,返回离线
            return 0;
        }
    }
    else
    {
        status = HttpWakeupApp(gstPBXConf.smarthome_http_head_url, wakeup, traceid);
        if (status == -1)//重试
        {
            status = HttpWakeupApp(gstPBXConf.smarthome_http_head_url, wakeup, traceid);
            if (status == -1)
            {
                // 请求家居接口失败,返回离线
                return 0;
            }
        }
    }   
    return status;
}

int HangupSmartHomeApp(AKCS_HANGUP_APP* hangup, uint64_t traceid)
{
    int status = 0;
    if (gstPBXConf.enable_smarthome_gateway)
    {
        std::string command = SMARTHOME_HANDLE_PBX_HANGUP_MOBILE;
        if (hangup->app_type == AKCS_DEVICE_TYPE_BELAHOME_APP_SDK)
        {
            command = SMARTHOME_HANDLE_PBX_HANGUP_SDK;
        }
        status = SmarthomeHandle::GetInstance().HangupMobile(traceid, hangup->caller_sip, hangup->callee_sip, hangup->nick_name_location, command);
        if (status == -1)
        {
            return 0;
        }
    }

    return status;
}


int WakeupApp(AKCS_WAKEUP_APP* wakeup, uint64_t traceid)
{
    if (!wakeup)
    {
        return 0;
    }
    
    int status = 0;
    if (gstPBXConf.is_nearby_instance)
    {
        status = HttpWakeupApp(gstPBXConf.http_head_url, wakeup, traceid);
    }
    else if (AKCS_DEVICE_TYPE_BELAHOME == wakeup->app_type 
        || AKCS_DEVICE_TYPE_SMARTHOME_LOCK_WAKEUP == wakeup->app_type
        || AKCS_DEVICE_TYPE_BELAHOME_APP_SDK == wakeup->app_type)
    {
        // x_name为转流门口机名称,有值直接用; xcaller为转流门口机sip,需要查下真实的呼叫名称
        if (strlen(wakeup->x_name) > 0)
        {
            snprintf(wakeup->nick_name_location, sizeof(wakeup->nick_name_location), "%s", wakeup->x_name);
        }
        else if (strlen(wakeup->x_caller) > 0)
        {
            PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
            if (rpc_client)
            {
                std::string nick_name_location = rpc_client->QuerySipInfo(wakeup->x_caller, traceid);
                if (nick_name_location.size() > 0)
                {
                    ::snprintf(wakeup->nick_name_location, sizeof(wakeup->nick_name_location), "%s", nick_name_location.c_str());
                }
            }
        }
        status = WakeupSmartHomeApp(wakeup, traceid);
    }
    else
    {
        PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
        if (rpc_client)
        {
            status = rpc_client->WakeupApp(traceid, wakeup->caller_sip, wakeup->original_callee, wakeup->nick_name_location, wakeup->app_type, wakeup->x_caller, wakeup->timestamp, wakeup->x_name);

        }
    }

    return status;
}

void HangupApp(AKCS_HANGUP_APP* hangup_app, uint64_t traceid)
{
    if (!hangup_app)
    {
        return;
    }
    // 家居通知
    if (AKCS_DEVICE_TYPE_BELAHOME == hangup_app->app_type || AKCS_DEVICE_TYPE_BELAHOME_APP_SDK == hangup_app->app_type)
    {
        HangupSmartHomeApp(hangup_app, traceid);
        return;
    }

    PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
    if (rpc_client)
    {
        rpc_client->HangupApp(traceid, hangup_app->caller_sip, hangup_app->original_callee, hangup_app->nick_name_location, hangup_app->app_type, hangup_app->x_caller, hangup_app->x_name);
    }
    return;
}

int QueryLandlineStatus(AKCS_LANDLINE_STATUS* landline, uint64_t traceid)
{
    if (!landline)
    {
        return 0;
    }

    int status = 0;
    if (gstPBXConf.is_nearby_instance)
    {
        status = HttpQueryLandlineStatus(gstPBXConf.http_head_url, landline, traceid);
    }
    else
    {
        PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
        if (rpc_client)
        {
            status = rpc_client->QueryLandlineStatus(landline->caller_sip, landline->phone_number, traceid);
        }        
    }

    return status;
}

int QueryLandlineNumber(AKCS_LANDLINE_NUMBER* landline, uint64_t traceid)
{
    if (!landline)
    {
        return 0;
    }
    
    std::string ret;
    std::string phone_code;
    
    PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
    if(rpc_client)
    {
        ret = rpc_client->QueryLandlineNumber(landline->sip, landline->type, traceid, phone_code);
        snprintf(landline->phone_number, sizeof(landline->phone_number), "%s", ret.c_str());
        snprintf(landline->phone_code, sizeof(landline->phone_code), "%s", phone_code.c_str());
    }
    return 0;
}

int QueryMainSiteSip(char* sip, char* main_sip, uint64_t traceid)
{
    if (sip == nullptr || main_sip == nullptr)
    {
        return 0;
    }
    
    std::string ret;
    PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
    if(rpc_client)
    {
        ret = rpc_client->QueryMainSiteSip(sip, traceid);
        snprintf(main_sip, AKCS_SIP_SIZE, "%s", ret.c_str());
    }
    return 0;
}

void WriteCallHistory(AKCS_CALL_HISTORY* history, uint64_t traceid,int calltype)
{
    if (!history)
    {
        return;
    }
    if (gstPBXConf.is_nearby_instance)
    {
        HttpWriteCallHistory(gstPBXConf.http_head_url, history, traceid);
    }
    else if (AKCS_DEVICE_TYPE_BELAHOME == calltype) //服用设备类型
    {
        HttpWriteCallHistory(gstPBXConf.smarthome_http_head_url, history, traceid);
    }    
    else
    {
        PbxRpcClientPtr rpc_client = PbxRpcClientMng::Instance()->GetRpcRandomClientInstance();
        if (rpc_client)
        {
            rpc_client->WriteCallHistory(history, traceid);
        }
    }

    return;
}



uint64_t GetTraceid()
{
    return AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
}


void PBXAttackCallback(const std::string& bussiness, const std::string& key)
{
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("PBX", key);
    return;
}

void InitBussiness()
{
    //延迟消息客户端,第一次初始化,使用时一定要避免初始化之前就引用.
    g_beanstalkd_client_ptr = new Beanstalk::Client(gstPBXConf.szBeansTalkdIP, gstPBXConf.nBeansTalkdPort);
    g_beanstalkd_client_ptr->use(AKCS_ATTACK_IP);
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(PBX_SIP_BUSSINESS, PBX_SIP_PERIOD,
            PBX_SIP_NUM, PBX_SIP_KEY_EXPIRE, PBXAttackCallback);
}
int AddBussiness(char* ip)
{
    //chenyc 2019-11-25,记录出错的次数,达到一定频率的时候,通过iptables加入黑名单. eg：remote_ip is ************ remote_ip是ipv4的形式
    int ret = AKCS::Singleton<BussinessLimit>::instance().AddBussiness(PBX_SIP_BUSSINESS, ip);
    if (ret == BussinessLimit::OUT_OF_LIMIT)
    {
        //有命中限制业务的时候，去巡检是否有过期的业务主键
        AKCS::Singleton<BussinessLimit>::instance().RemoveBussiness();
    }
    return ret;
}
//业务告警接口
void AddMonitorAlarm(char* node_srv, char* alarm_desc)
{
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(node_srv, alarm_desc, AKCS_MONITOR_ALARM_FREESWITCH);
    return;
}

void ConfInit(PBX_INIT_MOD_TYPE type)
{
    memset(&gstPBXConf, 0, sizeof(CSPBX_CONF));
    if (type == PBX_MOD_FREESWITCH)
    {
        CConfigFileReader config_file("/usr/local/freeswitch/server.conf");
        ::strncpy(gstPBXConf.etcd_server, config_file.GetConfigName("ETCD_SERVER_ADDR"), sizeof(gstPBXConf.etcd_server));
        ::strncpy(gstPBXConf.pbx_out_ip, config_file.GetConfigName("LOCAL_EXTERNAL_IP"), sizeof(gstPBXConf.pbx_out_ip));

        ::strncpy(gstPBXConf.szBeansTalkdIP, config_file.GetConfigName("BEANSTALKD_INNER_IP"), sizeof(gstPBXConf.szBeansTalkdIP));
        gstPBXConf.nBeansTalkdPort = atoi(config_file.GetConfigName("BEANSTALKD_PORT"));

        ::strncpy(gstPBXConf.http_head_url, config_file.GetConfigName("CSOUTERAPI_HTTP_URL"), sizeof(gstPBXConf.http_head_url));
        gstPBXConf.is_nearby_instance = atoi(config_file.GetConfigName("IS_NEARBY_INSTANCE"));

        ::strncpy(gstPBXConf.smarthome_http_head_url, config_file.GetConfigName("SMARTHOME_HTTP_URL"), sizeof(gstPBXConf.smarthome_http_head_url));
         gstPBXConf.enable_smarthome_gateway = atoi(config_file.GetConfigName("ENABLE_SMARTHOME_GATEWAY"));

         gstPBXConf.freeswitch_db_num = atoi(config_file.GetConfigName("FREESWITCH_DB_NUM"));
    }
    else
    {
        CConfigFileReader config_file("/usr/local/opensips/server.conf");
        ::strncpy(gstPBXConf.etcd_server, config_file.GetConfigName("ETCD_SERVER_ADDR"), sizeof(gstPBXConf.etcd_server));
        ::strncpy(gstPBXConf.opensips_out_ip, config_file.GetConfigName("LOCAL_EXTERNAL_IP"), sizeof(gstPBXConf.opensips_out_ip));

        ::strncpy(gstPBXConf.szBeansTalkdIP, config_file.GetConfigName("BEANSTALKD_INNER_IP"), sizeof(gstPBXConf.szBeansTalkdIP));
        gstPBXConf.nBeansTalkdPort = atoi(config_file.GetConfigName("BEANSTALKD_PORT"));

        ::strncpy(gstPBXConf.http_head_url, config_file.GetConfigName("CSOUTERAPI_HTTP_URL"), sizeof(gstPBXConf.http_head_url));
        gstPBXConf.is_nearby_instance = atoi(config_file.GetConfigName("IS_NEARBY_INSTANCE"));

        ::strncpy(gstPBXConf.smarthome_http_head_url, config_file.GetConfigName("SMARTHOME_HTTP_URL"), sizeof(gstPBXConf.smarthome_http_head_url));
    }

    CConfigFileReader config_file1("/etc/ip");
    ::strncpy(gstPBXConf.freeswitch_node, config_file1.GetConfigName("AKCS_HOSTNAME"), sizeof(gstPBXConf.freeswitch_node));
}

void glogInit(const char* argv)
{
    if (0 != access("/var/log/pbx_mod", 0))
    {
        mode_t mode = 0755;
        ::mkdir("/var/log/pbx_mod", mode);
    }

    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/pbx_mod/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/pbx_mod/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/pbx_mod/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/pbx_mod/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstPBXConf.etcd_server, sizeof(gstPBXConf.etcd_server), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstPBXConf.etcd_server;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    int need_res = 0;
    std::string etcd_net = gstPBXConf.etcd_server;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstPBXConf.etcd_server, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}


void init(PBX_INIT_MOD_TYPE type)
{
    glogInit("pbx_mod_init");
    ConfInit(type);
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }

    if (type == PBX_MOD_FREESWITCH)
    {
        std::thread rpcSmClientThread;
        std::thread etcdCliThread;
        if (!gstPBXConf.is_nearby_instance)
        {
            etcdCliThread = std::thread(EtcdSrvInit);
        }
        std::thread mqProduceThread = std::thread(ProduceInit);
        InitBussiness();
        InitHash(gstPBXConf.freeswitch_db_num);
        AK_LOG_INFO << "pbx_mod now is init.";
        if (!gstPBXConf.is_nearby_instance)
        {
            etcdCliThread.join();
        }
        mqProduceThread.join();
        glogClean();
    }
    else
    {
        std::thread etcdCliThread;
        if (!gstPBXConf.is_nearby_instance)
        {
            etcdCliThread = std::thread(EtcdSrvInitForOpensips);
        }
        
        std::thread mqProduceThread = std::thread(ProduceInit);
        InitBussiness();
        AK_LOG_INFO << "pbx_mod now is init.";
        if (!gstPBXConf.is_nearby_instance)
        {
            etcdCliThread.join();
        }        
        mqProduceThread.join();
        glogClean();
    }
    return;
}

void pbx_mod_init(PBX_INIT_MOD_TYPE type)
{
    std::thread init_thread = std::thread(init, type);
    init_thread.detach();
    sleep(3);//等待init启动
}

void pbx_mod_deinit(PBX_INIT_MOD_TYPE type)
{
    //资源回收不好处理，先不处理
    //g_etcd_loop->stop();
}

bool HostInOpensipsList(const char *url_host)
{
    std::set<std::string>::iterator it = g_opensips_info.find(url_host);
    if (it != g_opensips_info.end())
    {
        return true;
    }
    return false;
}

void InitHash(int num)
{
    if(g_pbx_consistent_hash == nullptr)
    {
        g_pbx_consistent_hash = new akcs_consistent_hash::QueueConsistentHash();
    }
    g_pbx_consistent_hash->InitQueueNumList(num);
}
void ReloadHash(int num)
{
    g_pbx_consistent_hash->ReloadQueueNumList(num);
}
int GetConsistentHash(const std::string& key)
{
    return g_pbx_consistent_hash->GetQueueNumByKey(key);
}



#ifdef __cplusplus
}
#endif

