##########################################################################################
## (C)Copyright 2012-2020 Ringslink .Ltd 
##
##########################################################################################

OBJS:=$(patsubst %.cpp,$(MOD_OBJ_DIR)%.o,$(wildcard *.cpp))
export CPPFLAGS += -Wno-deprecated

.PHONY: all clean

all:$(OBJS)

$(MOD_OBJ_DIR)%.o : %.cpp
	$(CXX) $(CPPFLAGS) -o $@ -c $<

clean:
	-rm $(OBJS)



