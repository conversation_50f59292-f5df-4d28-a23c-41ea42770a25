##########################################################################################
## (C)Copyright 2012-2020 Ringslink .Ltd
##
##########################################################################################
export PJ_BIN_DIR := $(PJ_DIR)bin/
export PJ_BUILD_DIR := $(PJ_DIR)build/
export PJ_INC_DIR := $(PJ_DIR)include/
export PJ_LIB_DIR := $(PJ_DIR)lib/
export PJ_SRC_DIR := $(PJ_DIR)src/
export THIRD_LIB_DIR :=$(PJ_DIR)thirdlib
export PJ_APP_DIR := $(PJ_SRC_DIR)app/
export PJ_NET_DIR := $(PJ_SRC_DIR)net/
export PJ_COMMON_DIR := $(PJ_SRC_DIR)common/

export CPPFLAGS := -std=c++11  -I$(PJ_INC_DIR)
export CFLAGS := -fPIC -I$(PJ_INC_DIR)
export LDFLAGS :=  -L$(PJ_LIB_DIR) -L$(THIRD_LIB_DIR)
export LIBFLAGS = -Wl,-rpath=$(PJ_LIB_DIR)  -Wl,-rpath=$(THIRD_LIB_DIR) -L$(PJ_LIB_DIR) -L$(THIRD_LIB_DIR)


export _DEBUG := 1
ifeq ($(_DEBUG), 1)
	export CPPFLAGS += -g -Wall
	export CFLAGS += -g -Wall
endif

export INCFLAGS_PJ := -I$(PJ_INC_DIR)
#export INCFLAGS_APP := -I$(PJ_INC_DIR)app/
#export INCFLAGS_COMMON := -I$(PJ_INC_DIR)common/

#export INCFLAGS_TEST := -I$(PJ_INC_DIR)test/
#export INCFLAGS_HYQLOG := -I$(PJ_INC_DIR)hyqlog/
#export INCFLAGS_HYQUTIL := -I$(PJ_INC_DIR)hyqutil/


