#ifndef __CSVRTSP_CONF_H__
#define __CSVRTSP_CONF_H__

#define CSVRTSP_CONF_COMMON_LEN 64

typedef struct CSVRTSP_CONF_T
{
    /* rtsp本机配置信息 */
    char csvrtsp_outer_ip[CSVRTSP_CONF_COMMON_LEN];
    char csvrtsp_outer_domain[CSVRTSP_CONF_COMMON_LEN];
    char csvrtsp_outer_ipv6[CSVRTSP_CONF_COMMON_LEN];

    char etcd_server_addr[CSVRTSP_CONF_COMMON_LEN];

    int keep_alive_times;

    char monitor_pic_cap_rootpath[CSVRTSP_CONF_COMMON_LEN];
    char monitor_mac_list[CSVRTSP_CONF_COMMON_LEN];
    
    int reg_etcd;

} CSVRTSP_CONF;

#endif //__CSVRTSP_CONF_H__

