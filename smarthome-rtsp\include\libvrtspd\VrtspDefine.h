#ifndef __VRTSP_DEFINE_H__
#define __VRTSP_DEFINE_H__
#pragma once

/*Timer*/
#define TIMER_ID_BASE       0
#define TIMER_VAL_BASE      1000

#define MSG_TYPE_MASK       0xFFFF0000
#define MSG_TIMER           0x00010000
#define MSG_MULTICAST       0x00020000
#define MSG_TCP             0x00030000
#define MSG_CTRL            0x00040000

enum
{
    MSG_CTRL_START_CAPTURE = MSG_CTRL + 1,
    MSG_CTRL_STOP_CAPTURE,
};

#define RTP_PACKET_MAX_BUFFER_SIZE  250
#define RTP_PACKET_DELAY_BUFFER_SIZE  50


struct RtpPacketInfo
{
    int seq_;
    int data_len_;
    unsigned char packet_data_[1500];
    RtpPacketInfo(int seq, int datalen, const char* data)
    {
        seq_ = seq;
        data_len_ = datalen;
        if (data)
        {
            memcpy(packet_data_, data, datalen);
        }

    }
    RtpPacketInfo()
    {
        seq_ = 0;
        data_len_ = 0;
        packet_data_[0] = 0;
    }
    friend bool operator<(const RtpPacketInfo& a, const RtpPacketInfo& b)
    {
        return a.seq_ > b.seq_;
    }

};




#endif
