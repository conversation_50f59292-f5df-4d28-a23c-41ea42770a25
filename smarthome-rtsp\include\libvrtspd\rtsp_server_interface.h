#pragma once


typedef struct RTSP_DATA_T
{
#define RTSP_DATA_IP_SIZE   16
#define RTSP_DATA_MAC_SIZE  16
    int remote_port;
    char remote_ip[RTSP_DATA_IP_SIZE];
    char mac[RTSP_DATA_MAC_SIZE];
    char SSR<PERSON>[32];//十六进制 AB0103D0
} RTSP_DATA;

namespace akuvox
{
class IRtspServer
{
public:
    virtual ~IRtspServer() {}

public:
    virtual int start() = 0;
    virtual int stop() = 0;
    virtual void report() = 0;  //该方法目前只是用于调试
};

class CRtspServerFactory
{
public:
    static IRtspServer* GetInstance();
};

}
