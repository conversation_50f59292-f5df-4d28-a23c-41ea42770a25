#!/bin/bash
ACMD="$1"
RTSP_BIN='/usr/local/akcs/smarthome-rtsp/bin/smarthome-rtsp'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_rtsp()
{
    nohup $RTSP_BIN >/dev/null 2>&1 &
    echo "Start smarthome-rtsp successful"

    if [ -z "`ps -fe|grep "smarthome-rtsprun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/smarthome-rtsp/scripts/smarthome-rtsprun.sh >/dev/null 2>&1 &
    fi
}
stop_rtsp()
{
    echo "Begin to stop smarthome-rtsprun.sh"
    rtsprunid=`ps aux | grep -w smarthome-rtsprun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${rtsprunid}" ];then
	    echo "smarthome-rtsprun.sh is running at ${rtsprunid}, will kill it first."
	    kill -9 ${rtsprunid}
    fi
    echo "Begin to stop smarthome-rtsp"
    kill -9 `pidof smarthome-rtsp`
    sleep 2
    echo "Stop smarthome-rtsp successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 554 | grep smarthome-rtsp | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_rtsp
    else
        echo "smarthome-rtsp is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 554 | grep smarthome-rtsp | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "smarthome-rtsp is already stopping"
    else
        stop_rtsp
    fi
    ;;
  restart)
    stop_rtsp
    sleep 1
    start_rtsp
    ;;
  status)
    cnt=`ss -alnp | grep 554 | grep smarthome-rtsp | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m smarthome-rtsp is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m smarthome-rtsp is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

