#!/bin/bash
PROCESS_NAME=smarthome-rtsp
PROCESS_START_CMD="/usr/local/akcs/smarthome-rtsp/scripts/smarthome-rtspctl.sh start"
PROCESS_PID_FILE=/var/run/smarthome-rtsp.pid
LOG_FILE=/var/log/smarthome-rtsp_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/smarthome-rtsp/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/smarthome-rtsp/scripts/log_back.sh"
rtsplog_path="/var/log/smarthome-rtsplog"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

times=1
while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
	let times+=1
    if [ $times -gt 120 ];then
       times=1
       common_log_back ${rtsplog_path}
    fi
done
