##########################################################################################
## (C)Copyright 2003-2013 Yinqing.Huang.Ltd
##
##########################################################################################
MOD_OBJ_NAME = smarthome-rtsp
MOD_NAME = vrtspd

PWD := $(shell pwd)
export MOD_DIR := $(PWD)/../
export CSBASE_DIR := $(PWD)/../../../../../csbase
export CSVRTSP_DIR := $(PWD)/../../../../../smarthome-rtsp

PJ_DIR ?=

include $(PJ_DIR)/build/PROJECT.mak
include $(PJ_DIR)/build/TOOL.mak
include $(PJ_DIR)/build/MOD.mak

objects_dir := ../objects
$(shell if [ ! -e $(objects_dir) ];then mkdir -p $(objects_dir); fi)

MOD_BIN_DIR=$(CSVRTSP_DIR)/release/bin/

CPPFLAGS +=  -DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -DNDEBUG -Wl,--rpath=/usr/local/akcs/csvrtsp/lib -DWEBRTC_POSIX -I$(MOD_INC_DIR) -I$(MOD_SRC_DIR) -I$(MOD_SRC_DIR)common/cstring -I$(MOD_SRC_DIR)common -I$(MOD_SRC_DIR)auth/ -I/$(CSBASE_DIR)  
CPPFLAGS += -I$(CSVRTSP_DIR)/include/libvrtspd -I$(CSBASE_DIR)/protobuf -I$(CSBASE_DIR)/evpp -I$(CSBASE_DIR)/etcd -I/$(CSBASE_DIR)/webrtc -I/$(CSBASE_DIR)/webrtc/third_party/abseil-cpp -I$(CSBASE_DIR)/gid -I${CSBASE_DIR}/grpc -I${CSBASE_DIR}/grpc/gens -I$(CSBASE_DIR)/jsoncpp0.5/include \
-I${CSBASE_DIR}/encrypt -I$(CSBASE_DIR)/model -I$(CSBASE_DIR)/Rldb -I$(CSBASE_DIR)/mysql/include -I$(CSBASE_DIR)/smarthome

LDFLAGS += -L$(CSBASE_DIR)/ -L$(CSBASE_DIR)/thirdlib/ -L$(CSBASE_DIR)/evpp/lib -lpthread -lglog -levpp -lmysqlclient -levent -lprotobuf -letcd-cpp-api -lcpprest -lboost_system -lssl -lcrypto -Bstatic -lcsbase  -lwebrtc -lgpr -lgrpc -lgrpc++ -lcurl

MOD_SRC_DIRS := $(MOD_SRC_DIR)common $(MOD_SRC_DIR)rtp $(MOD_SRC_DIR)auth $(MOD_SRC_DIR) ${CSBASE_DIR}/protobuf ${CSBASE_DIR}/etcd $(MOD_SRC_DIR)common/cstring ${CSBASE_DIR}/encrypt  $(CSBASE_DIR)/jsoncpp0.5/src/json $(CSBASE_DIR)/smarthome $(CSBASE_DIR)/model $(CSBASE_DIR)/Rldb

.PHONY: all $(MOD_SRC_DIRS) clean

all: check $(MOD_SRC_DIRS) main install

check:
ifeq ($(PJ_DIR),)
	@echo "Build failed: PJ_DIR is nullptr"
	@exit 1
endif

$(MOD_SRC_DIRS):
	@echo ""
	$(MAKE) --directory=$@

main:
	@echo ""
	g++ -o $(MOD_BIN_DIR)$(MOD_OBJ_NAME) $(MOD_OBJ_DIR)*.o $(LDFLAGS)  $(CPPFLAGS)
ifneq ($(_DEBUG), 1)
	$(STRIP) $(MOD_BIN_DIR)$(MOD_OBJ_NAME)
endif

install:
	@echo ""
	#cp $(MOD_BIN_DIR)$(MOD_OBJ_NAME) $(PJ_BIN_DIR)$(MOD_OBJ_NAME)
	#mkdir -p $(PJ_INC_DIR)lib$(MOD_NAME)
	#-cp $(MOD_INC_DIR)*  $(PJ_INC_DIR)lib$(MOD_NAME)/ -rf


clean: check
	for d in $(MOD_SRC_DIRS); \
	do \
		cd $${d}; \
		$(MAKE) clean;  \
		cd ..;	\
	done

	-rm $(MOD_BIN_DIR)$(MOD_OBJ_NAME)
