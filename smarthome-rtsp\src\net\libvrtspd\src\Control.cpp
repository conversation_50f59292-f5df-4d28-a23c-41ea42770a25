#include "Control.h"
#include "Lock.h"
#include "WaitEvent.h"
#include <stdlib.h>
#include <stdio.h>
#include "string.h"
#include "unistd.h"
#include "AKLog.h"
#include "VrtspDefine.h"
#include "rtp/RtpDeviceManager.h"
#include "rtp/RtpDeviceClient.h"
#include "RtspClientManager.h"
#include "utils.h"


using namespace akuvox;

//#include "msg.h"

int ProcessThread(void* data)
{
    CControl* control = (CControl*)data;

    while (true)
    {
        control->ProcessMsg();
    }

    pthread_detach(pthread_self());
    return 0;
}

void* TimerThread(void* data)
{
    CControl* control = (CControl*)data;

    while (true)
    {

        control->AddMsg(MSG_TIMER, TIMER_ID_BASE, 0, NULL, 0);
        usleep(TIMER_VAL_BASE * 1000);
    }

    pthread_detach(pthread_self());
    return 0;
}

CControl* GetControlInstance()
{
    return CControl::GetInstance();
}

CControl::CControl(): tag_("Control")
{
    msg_header_ = NULL;
    m_lock = new CLock();
    m_wait = new CWaitEvent();
}

CControl::~CControl()
{
    DelAllMsg();

    if (NULL != m_lock)
    {
        delete (CLock*)m_lock;
        m_lock = NULL;
    }
    if (NULL != m_wait)
    {
        delete (CWaitEvent*)m_wait;
        m_wait = NULL;
    }
}

CControl* CControl::instance = NULL;

CControl* CControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CControl();
    }

    return instance;
}

int CControl::Init()
{
    pthread_create(&m_tidTimer, NULL, TimerThread, this);
    return 0;
}

int CControl::Run()
{
    return (int)ProcessThread(this);
}

//处理消息
int CControl::ProcessMsg()
{
    WaitForEvent();
    Lock();
    MESSAGE* tmp_node = NULL;

    while (msg_header_ != NULL)
    {
        tmp_node = (MESSAGE*)msg_header_;
        msg_header_ = ((MESSAGE*)msg_header_)->next;
        Unlock();
        OnMessage(tmp_node->id, tmp_node->w_param, tmp_node->l_param, tmp_node->l_data);
        Lock();
        if (tmp_node->l_data != NULL)
        {
            delete [](char*)tmp_node->l_data;
        }
        delete (tmp_node);
    }

    msg_header_ = NULL;

    ResetWaitEvent();

    Unlock();

    return 0;
}


//上锁消息缓冲区
void CControl::Lock()
{
    ((CLock*)m_lock)->Lock();
}

//解锁消息缓冲区
void CControl::Unlock()
{
    ((CLock*)m_lock)->Unlock();
}

//设置事件
void CControl::SetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Set();
}

//清除事件
void CControl::ResetWaitEvent()
{
    ((CWaitEvent*)m_wait)->Reset();
}

//等待事件触发
void CControl::WaitForEvent()
{
    ((CWaitEvent*)m_wait)->Wait();
}


//增加一个新的消息
int CControl::AddMsg(unsigned int id, unsigned int w_param, unsigned int l_param, void* l_data, int data_len)
{
    Lock();
    MESSAGE* cur_node = NULL;
    MESSAGE* new_node = new MESSAGE();
    if (NULL == new_node)
    {
        Unlock();
        return -1;
    }

    memset(new_node, 0, sizeof(MESSAGE));

    new_node->id = id;
    new_node->w_param = w_param;
    new_node->l_param = l_param;
    if ((l_data != NULL) && (data_len > 0))
    {
        new_node->l_data = new char[data_len];
        memcpy(new_node->l_data, l_data, data_len);
    }

    if (msg_header_ == NULL)
    {
        msg_header_ = new_node;
    }
    else
    {
        cur_node = (MESSAGE*)msg_header_;
        while ((cur_node != NULL) && (cur_node->next != NULL))
        {
            cur_node = cur_node->next;
        }
        cur_node->next = new_node;
    }
    SetWaitEvent();

    Unlock();

    return 0;
}


//删除所有消息
int CControl::DelAllMsg()
{
    Lock();

    MESSAGE* cur_node = NULL;
    MESSAGE* tmp_node = NULL;

    cur_node = (MESSAGE*)msg_header_;

    while (cur_node != NULL)
    {
        tmp_node = cur_node;
        cur_node = cur_node->next;
        if (tmp_node->l_data != NULL)
        {
            delete [](char*)tmp_node->l_data;
        }

        delete tmp_node;
    }

    msg_header_ = NULL;

    Unlock();

    return 0;
}

//消息处理句柄
int CControl::OnMessage(unsigned int msg, unsigned int w_param, unsigned int l_param, void* l_data)
{
    int msg_type = msg & MSG_TYPE_MASK;
    switch (msg_type)
    {
        case MSG_TIMER://second timer
        {
            OnTimer(w_param);
        }
        break;
        
        case MSG_MULTICAST:
        case MSG_TCP:
        case MSG_CTRL:
        break;
        
        default:
            break;
    }

    return 0;
}


//定时器消息处理
int CControl::OnTimer(unsigned int id_event)
{
    if (id_event == TIMER_ID_BASE)
    {
        static int heartbeat_time = 0;
        if (heartbeat_time >= 30)
        {
            //获取有连接app的设备mac列表， 发送心跳
            RtpDeviceManager::getInstance()->SendHeartBeatForList();
            heartbeat_time = 0;
        }
        heartbeat_time++;

        //check rtsp state
        static int rtsp_timeout = 0;
        if (rtsp_timeout >= 60)
        {
            //超时删除
            RtspClientManager::getInstance()->CheckAppConnect();
            rtsp_timeout = 0;
        }
        rtsp_timeout++;

    }

    return 0;
}
