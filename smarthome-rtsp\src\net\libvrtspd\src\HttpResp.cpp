#include <sstream>
#include "RtspClientManager.h"
#include "rtp/RtpDeviceManager.h"
#include "RtspMonitor.h"
#include "HttpResp.h"

namespace operation_http
{
static const std::string V31 = "3.1";
static const std::string V46 = "4.6";
static const std::string V54 = "5.4";

HTTPRespCallback ReqEchoHandlerV31 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    cb(ctx->body().ToString());
};

//rtsp client num
operation_http::HTTPRespCallback ReqRtspCliHandlerV31 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int rtsp_cli_num = akuvox::RtspClientManager::getInstance()->GetClientCount();
    std::stringstream oss;
    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << MESSAGE << ": " << RTSP_CLIETN_NUM_SUCCESS << ",\n"
        << DATAS << ": " << "\n"
        << "{" <<  "\n"
        << RTSP_CLIENT_NUM << ": " << rtsp_cli_num << "\n"
        << "}" << "\n"
        << "}" << "\n";
    cb(oss.str());
    return;
};

operation_http::HTTPRespCallback ReqMonitorListHandlerV46 = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::stringstream oss;
    std::string monitor_list;
    std::string mac = ctx->GetQuery("mac");

    if (0 == mac.length())
    {
        monitor_list = akuvox::RtpDeviceManager::getInstance()->GetMonitorList();
    }
    else
    {
        monitor_list = akuvox::RtpDeviceManager::getInstance()->GetMonitorListByMac(mac);
    }
    oss << monitor_list;
    cb(oss.str());
    return;
};

operation_http::HTTPRespCallback ReqSetPacpMacHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::stringstream oss;
    std::string monitor_list;
    std::string mac = ctx->GetQuery("mac");

    if (0 != mac.length())
    {
        CRtspMonitor::Instance()->AddMac(mac);
    }

    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << "\"now_list\"" << ": \"" << CRtspMonitor::Instance()->MonitorList() << "\"\n"
        << "}" << "\n";
    
    cb(oss.str());
    return;
};

operation_http::HTTPRespCallback ReqClearPacpMacHandler = [](const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::stringstream oss;
    std::string monitor_list;
    std::string mac = ctx->GetQuery("mac");

    if (0 != mac.length())
    {
        CRtspMonitor::Instance()->ClearMac(mac);
    }
    
    oss << "{" <<  "\n"
        << RESULT << ": 0," << "\n"
        << "\"now_list\"" << ": \"" << CRtspMonitor::Instance()->MonitorList() << "\"\n"
        << "}" << "\n";

    cb(oss.str());
    return;
};

operation_http::HTTPRespVerCallbackMap HTTPEchoMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V31] = ReqEchoHandlerV31;
    return OMap;
}

operation_http::HTTPRespVerCallbackMap HTTPRtspCliMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V31] = ReqRtspCliHandlerV31;
    return OMap;
}

operation_http::HTTPRespVerCallbackMap HTTPMonitorMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V46] = ReqMonitorListHandlerV46;
    return OMap;
}

operation_http::HTTPRespVerCallbackMap HTTPSetPcapMacMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V54] = ReqSetPacpMacHandler;
    return OMap;
}

operation_http::HTTPRespVerCallbackMap HTTPClearPcapMacMap()
{
    operation_http::HTTPRespVerCallbackMap OMap;
    OMap[V54] = ReqClearPacpMacHandler;
    return OMap;
}

operation_http::HTTPAllRespCallbackMap HTTPAllRespMapInit()
{
    operation_http::HTTPAllRespCallbackMap OMap;
    OMap[operation_http::ECHO] = HTTPEchoMap();
    OMap[operation_http::RTSP_CLI] = HTTPRtspCliMap();//获取rtsp cli连接数量的接口
    OMap[operation_http::MONITOR] = HTTPMonitorMap();
    OMap[operation_http::SET_PCAP_MAC] = HTTPSetPcapMacMap();
    OMap[operation_http::CLEAR_PCAP_MAC] = HTTPClearPcapMacMap();
    return OMap;
}
}
