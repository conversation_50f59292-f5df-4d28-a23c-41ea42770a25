#include <stdio.h>
#include <stdlib.h>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"

#include "HttpServer.h"
#include "HttpResp.h"
#include "CsvrtspConf.h"
#include "util.h"

//全局变量
static operation_http::HTTPAllRespCallbackMap httpRespCbs;
extern CSVRTSP_CONF gstCSVRTSPConf;

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}
void HttpReqEchoCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->body().ToString() << " ]";
    std::string name = ctx->GetQuery("user");
    if (name.length() == 0)
    {
        LOG_WARN << "http req name is null";
        cb("http req name is null");
        return;
    }
    const std::string api_version = ctx->FindRequestHeader(API_VERSION);
    if (api_version.length() == 0)
    {
        LOG_WARN << "http req head 'api-version' is null";
        cb("http req head 'api-version' is null");
        return;
    }
    auto httpRespEchoMap = httpRespCbs[operation_http::ECHO];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespEchoMap.find(api_version);
    if (it ==  httpRespEchoMap.end())
    {
        LOG_WARN << "http req version is [ " << api_version << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

void HttpRtspCliCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->body().ToString() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        //LOG_WARN << "http req head 'api-version' is null";
        // cb("http req head 'api-version' is null");
        // return;
        head = "3.1";
    }
    auto httpRespMap = httpRespCbs[operation_http::RTSP_CLI];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }
    //调用回调函数
    it->second(ctx, cb);
    return ;
}

//设备监控定位运维接口，add by czw
void HttpReqMonitorListCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "4.6";
    }
    auto httpRespMap = httpRespCbs[operation_http::MONITOR];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

void HttpReqSetPcapMacCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "5.4";
    }
    auto httpRespMap = httpRespCbs[operation_http::SET_PCAP_MAC];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

void HttpReqClearPcapMacCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    const char* head = ctx->FindRequestHeader("api-version");
    if (nullptr == head)
    {
        head = "5.4";
    }
    auto httpRespMap = httpRespCbs[operation_http::CLEAR_PCAP_MAC];
    operation_http::HTTPRespVerCallbackMap::iterator it = httpRespMap.find(head);
    if (it == httpRespMap.end())
    {
        LOG_WARN << "http req version is [ " << head << " ] ,which out of request";
        cb("http req version is out of request");
        return;
    }

    it->second(ctx, cb);
    return ;

}

void startHttpServer()
{
    httpRespCbs = operation_http::HTTPAllRespMapInit();
    const int port = 19997;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网
    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/echo", HttpReqEchoCallback);
    server.RegisterHandler("/rtsp_cli", HttpRtspCliCallback);
    server.RegisterHandler("/monitor_list", HttpReqMonitorListCallback);
    server.RegisterHandler("/set_pcap_mac", HttpReqSetPcapMacCallback);
    server.RegisterHandler("/clear_pcap_mac", HttpReqClearPcapMacCallback);
    server.Init(port);
    server.Start();
    //while (!server.IsStopped()) {
    //    usleep(1);
    //}
    return ;
}
