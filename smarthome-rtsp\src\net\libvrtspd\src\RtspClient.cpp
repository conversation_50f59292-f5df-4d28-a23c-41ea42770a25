#include "RtspClient.h"
#include "RtspParse.h"
#include "AKLog.h"
#include <unistd.h>
#include <netinet/in.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <string.h>
#include "strDup.hh"
#include "AkLogging.h"
#include "CsvrtspConf.h"
#include "ConfigOperator.h"
#include "SmarthomeHandle.h"


extern CSVRTSP_CONF gstCSVRTSPConf;

namespace akuvox
{
RtspClient::RtspClient(int socketid, const std::string& ip, unsigned short port, bool is_ipv6) : tag_("RtspClient")
{
    rtsp_fd_ = socketid;
    client_ip_ = ip;
    rtsp_port_ = port;
    seq_num_ = 0;
    method_ = 0;
    client_rtp_port_ = 0;
    client_rtcp_port_ = 0;
    local_rtp_port_ = 0;
    is_connect_ = true;
    is_ipv6_ = is_ipv6;
    memset(rtsp_recv_buf_, 0, sizeof(rtsp_recv_buf_));
    rtsp_recv_size_ = 0;
    rtsp_keep_alive_times_ = 0;
    auth_failed_num_ = 0;
    method_ = -1;
    status_ = kInit;
    video_pt_ = 96;
    video_type_ = RTSP_VIDEO_TYPE_H264_LARGE;
}

RtspClient::~RtspClient()
{
    CAKLog::LogT(tag_, "~RtspClient()");
}

std::string RtspClient::toString()
{
    char infos[256] = { 0 };
    snprintf(infos, sizeof(infos),
             "RtspClient[fd=%d,rtsp port=%hu,app ip=%s, app_rtp_port=%hu,local_ip=%s,local_rtp_port=%hu,request_mac=%s]",
             rtsp_fd_, rtsp_port_, client_ip_.c_str(), client_rtp_port_,
             local_ip_.c_str(), local_rtp_port_, mac_.c_str());
    std::string info = infos;
    return info;
}

std::string RtspClient::getRandSessionId()
{
    std::string strRandSessionId;
    int i;
    char buf[2];
    char c;
    for (i = 0; i < 20; i++)
    {
        c = random() % 16;
        sprintf(buf, "%X", c);
        strRandSessionId.push_back(buf[0]);
    }
    return strRandSessionId;
}

std::string RtspClient::getSessionId()
{
    return map_["Session"];
}

std::string RtspClient::GetLocalIp()
{
    local_ip_ = gstCSVRTSPConf.csvrtsp_outer_ip;
    return local_ip_;
}

std::string RtspClient::GetLocalIpv6()
{
    local_ipv6_ = gstCSVRTSPConf.csvrtsp_outer_ipv6;
    return local_ipv6_;
}


#define VRTSP_REALM_TEXT  "AK VRTSPD"

//外部调用如下: if (!client->authenticationOK("DESCRIBE", full_request))
bool RtspClient::authenticationOK(char const* cmd_name, char const* full_request)
{
    char const* username = NULL;
    char const* realm = NULL;
    char const* nonce = NULL;
    char const* uri = NULL;
    char const* response = NULL;
    Boolean success = False;

    //过滤user
    char access_id[256] = "";
    int nManul = 0;
    parseAuthorizationHeaderForAccount(full_request, access_id, sizeof(access_id));
    parseAuthorizationHeaderForManual(full_request, nManul);
    app_uid_ = access_id;

    do
    {
        // To authenticate, we first need to have a nonce set up
        // from a previous attempt:
        //该mac对应的nonce已经过期或者还未设定过
        std::string nonce_cache = AuthNonceCache::getInstance()->nonceByMac(mac_);
        if (nonce_cache.size() == 0)
        {
            break;
        }

        // Next, the request needs to contain an "Authorization:" header,
        // containing a username, (our) realm, (our) nonce, uri,
        // and response string:

        if (!parseAuthorizationHeader(full_request, username, realm, nonce, uri, response)
                || username == NULL
                || realm == NULL || strcmp(realm, current_authenticator_.realm()) != 0
                || nonce == NULL || strcmp(nonce, nonce_cache.c_str()) != 0
                || uri == NULL || response == NULL)
        {
            break;
        }

        //username must not be empty.
        if (0 == strlen(username))
        {
            CAKLog::LogW(tag_, "username is empty", username);
            break;
        }

        //TODO: lookup password by username, now user name must be user
        // Next, the username has to be known to us:

        std::string dev_mac;
        dev_mac = mac_;

        std::string rtsppwd;
        int smarthome_ret;
        smarthome_ret =  SmarthomeHandle::GetInstance().GetRtspPasswd(access_id, rtsppwd);        
        if(smarthome_ret != 0 || rtsppwd.size() == 0)
        {
           success  = 0; 
           AK_LOG_INFO << "GetRtspPasswd error";
           break;
        }
        
        current_authenticator_.setUsernameAndPassword(username, rtsppwd.c_str(), False);

        
        // Finally, compute a digest response from the information that we have,
        // and compare it to the one that we were given:
        //char const* ourResponse = current_authenticator_.computeDigestResponse(cmd_name, uri);
        std::string ourResponse = current_authenticator_.computeDigestResponseByNonce(cmd_name, uri, nonce_cache);
        AK_LOG_INFO << "resp=" << std::string(response) << ",ourRes=" << ourResponse;
        success = (strcmp(ourResponse.c_str(), response) == 0); //鉴权成功
    }
    while (0);

    delete[](char*)username;
    delete[](char*)realm;
    delete[](char*)nonce;
    delete[](char*)uri;
    delete[](char*)response;

    if (success)
    {
        CAKLog::LogD(tag_, "Authorized Success for rtsp client=%d,mac=%s", rtsp_fd_, mac_.c_str());
        return true;
    }

    // If we get here, there was some kind of authentication failure.
    // Send back a "401 Unauthorized" response, with a new random nonce:
    // 放在这里的原因是:一旦该rtspclient鉴权成功过了，那么下回该tcp上面的连接都不需要再改变这个nonce的值
    current_authenticator_.setRealmAndRandomNonce(VRTSP_REALM_TEXT); //在这里生成一个任意的nonce,并响应给401的对端
    std::string nonce_tmp = AuthNonceCache::getInstance()->nonceByMac(mac_);
    if (nonce_tmp.size() == 0) //防止该mac对应的nonce还没有写入redis成功
    {
        nonce_tmp = "888888888888192e3ea10d54635a1ef8"; //写死一个
    }
    unsigned char fResponseBuffer[RTSP_BUFFER_SIZE] = {0};
    snprintf((char*)fResponseBuffer, sizeof fResponseBuffer,
             "RTSP/1.0 401 Unauthorized\r\n"
             "CSeq: %d\r\n"
             "%s"
             "WWW-Authenticate: Digest realm=\"%s\", nonce=\"%s\"\r\n\r\n",
             seq_num_,
             dateHeader(),
             current_authenticator_.realm(), nonce_tmp.c_str());
    write(rtsp_fd_, (const char*)fResponseBuffer, strlen((const char*)fResponseBuffer));
    CAKLog::LogD(tag_, "Unauthorized for rtsp client=%d,mac=%s", rtsp_fd_, mac_.c_str());
    return false;
}
}

