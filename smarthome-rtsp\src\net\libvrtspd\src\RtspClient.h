#pragma once
#include <string>
#include <map>
#include "DigestAuthentication.h"
#include <map>
#include <atomic>

#define RTSP_BUFFER_SIZE    2048
#define RTSP_VIDEO_TYPE_H264_LARGE     "H264"
#define RTSP_VIDEO_TYPE_H265_LARGE     "H265"
#define RTSP_VIDEO_TYPE_H264_SMALL     "h264"
#define RTSP_VIDEO_TYPE_H265_SMALL     "h265"



namespace akuvox
{

//app的rtsp监控类
class RtspClient
{
public:
    enum Status {
        kInit = 0,
        kOption = 1,
        kDesc = 2,
        kSetup = 3,
        kPlay = 4,
    };

public:
    RtspClient(int socketid, const std::string& ip, unsigned short port, bool is_ipv6);
    ~RtspClient();

    std::string toString();
    std::string getRandSessionId();
    std::string getSessionId();
    std::string GetLocalIp();
    std::string GetLocalIpv6();
    bool authenticationOK(char const* cmdName, char const* fullRequestStr);
    std::string getAppUid()
    {
        return app_uid_;
    }
    void setAppUid(std::string& app_uid)
    {
        app_uid_ = app_uid;
    }
    void AddAuthFailedNum()
    {
        auth_failed_num_++;
    }
    
    uint32_t AuthFailedNum()
    {
        return auth_failed_num_;
    }
    std::string GetClientIp() const
    {
        return client_ip_;
    }
    //added by chenyc,2022.08.19，增加rtsp客户端信令交互的状态机
    void SetStatus(Status status)
    {
        status_ = status;
    }
    Status GetStatus() const
    {
        return status_;
    }

public:
    int rtsp_fd_;
    int rtsp_keep_alive_times_;
    unsigned short rtsp_port_;
    std::string client_ip_;

    std::string client_port_;        //用于缓存协商出来的客户端端口，字符串如client port %d-%d
    unsigned short client_rtp_port_;    //协商出来的客户端rtp端口
    unsigned short client_rtcp_port_;   //协商出来的客户端rtcp端口

    unsigned short local_rtp_port_; //服务端监听rtsp客户端(app)的上报的rtp包端口,用于rtp over udp，以实现客户端的udp端口 nat
    std::string local_ip_;  //rtsp的外网ip (ipv4/ipv6的格式,具体哪种看配置文件是否需要将ipv6协议栈打开)
    std::string local_ipv6_;
    std::string mac_;
    std::string binded_mac_;
    int dclient_ver_;
    int seq_num_;
    int method_;

    std::string session_id_;
    bool is_connect_;
    bool is_ipv6_;
    std::map<std::string, std::string> map_;

    char rtsp_recv_buf_[RTSP_BUFFER_SIZE];// 用来保存临时数据
    int rtsp_recv_size_;

    //std::string m_strUrlSuffix
    Authenticator current_authenticator_;
    const char* tag_;

    std::string app_uid_;

    uint32_t dev_ssrc_;//设备流的ssrc
    uint32_t app_client_ssrc_;//app 发送端的ssrc,用于app rtcp接收报告

    std::atomic<uint32_t> auth_failed_num_;//该rtsp通道鉴权认证失败的次数,超过3次就认为是恶意攻击
    std::atomic<Status> status_;

    int video_pt_;
    std::string video_type_;
};
}
