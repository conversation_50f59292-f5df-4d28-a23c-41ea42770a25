#include <sys/types.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <pthread.h>
#include <netinet/tcp.h>
#include "AKLog.h"
#include "rtp/RtpAppManager.h"
#include "rtp/RtpDeviceManager.h"
#include "RtspClientManager.h"
#include "rtp/RtpControl.h"
#include "RtspServerImpl.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "CsvrtspConf.h"
#include "SnowFlakeGid.h"
#include "AkcsMonitor.h"
#include "SmarthomeHandle.h"

extern CSVRTSP_CONF gstCSVRTSPConf;
extern std::string g_logic_srv_id;

namespace akuvox
{
RtspClientManager::RtspClientManager() : tag_("RtspClientManager")
{
}

RtspClientManager::~RtspClientManager()
{
    ClearClient();
}

RtspClientManager* RtspClientManager::instance = nullptr;

RtspClientManager* RtspClientManager::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtspClientManager;
    }
    return instance;
}

void RtspClientManager::AddClient(int socketid, const std::string& ip, unsigned short port, bool is_ipv6)
{
    std::shared_ptr<RtspClient> client = std::make_shared<RtspClient>(socketid, ip, port, is_ipv6);
    client->GetLocalIp(); //added by chenyc,2018-05-23,在这里赋值rtsp服务地址
    client->GetLocalIpv6();
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    m_mapRtspClients[socketid] = client;//不管是否发生 app rtsp client socket fd 复用，直接替换
}

void RtspClientManager::AddConcurrency(int socketid)
{
    //监控告警埋点,超过50路监控并发
    if (m_mapRtspConcurrency.size() >= 50)
    {
        std::string worker_node = "smarthome-rtsp_";
        worker_node += gstCSVRTSPConf.csvrtsp_outer_ip;
        std::string des = "the num of rtsp client more than 50";
        char desc[128] = {0};
        ::snprintf(desc, 127, "the num of rtsp client is %lu, more than alarm num 50", m_mapRtspClients.size());
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, desc, AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_ONE_DEVICE_RTSP);
    }

    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    m_mapRtspConcurrency[socketid] = socketid;
}

void RtspClientManager::RemoveClient(int socketid)
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    m_mapRtspClients.erase(socketid);
    m_mapRtspConcurrency.erase(socketid);
}

//删除之前存在的app rtsp 一个app在平台只能存在一个rtsp
std::shared_ptr<RtspClient> RtspClientManager::getBeforeAppClient(std::string app_uid, int nCurrentSocketid)
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    for (auto clients : m_mapRtspClients)
    {
        std::shared_ptr<RtspClient> client = clients.second;

        if (client && client->getAppUid() == app_uid && nCurrentSocketid != client->rtsp_fd_)
        {
            return client;
        }
    }
    return nullptr;
}

void RtspClientManager::ClearClient()
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    m_mapRtspClients.clear();
    m_mapRtspConcurrency.clear();
}

int RtspClientManager::GetClientCount() const
{
    return m_mapRtspClients.size();
}

std::shared_ptr<RtspClient> RtspClientManager::GetClient(int socketid)
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    auto iter = m_mapRtspClients.find(socketid);
    if (iter != m_mapRtspClients.end())
    {
        return iter->second;
    }
    return nullptr;
}

std::vector<int> RtspClientManager::GetAllClientSocket()
{
    std::vector<int> result;
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
    for (auto client : m_mapRtspClients)
    {
        result.push_back(client.first);
    }
    return result;
}

void RtspClientManager::ReportAll()
{
    std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);//TODO 提到栈上的临时变量
    std::map<int, std::shared_ptr<RtspClient>>::iterator iter;
    CAKLog::LogD(tag_, "RTSP client count=%lu", m_mapRtspClients.size());
    for (iter = m_mapRtspClients.begin(); iter != m_mapRtspClients.end(); ++iter)
    {
        CAKLog::LogD(tag_, "%s", iter->second->toString().c_str());
    }
}
void RtspClientManager::CheckAppConnect()
{
    std::vector<int> fds;
    std::map<int, std::shared_ptr<RtspClient>> rtsp_clients;  //int==rtsp sockfd
    {
        std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
        rtsp_clients = m_mapRtspClients;
    }

    for (auto& clients : rtsp_clients)
    {
        std::shared_ptr<RtspClient> client = clients.second;

        if (client->is_connect_)
        {
            client->is_connect_ = false;
            continue;
        }

        std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClientByRtspFd(client->rtsp_fd_);
        if (app_client == nullptr)
        {
            CAKLog::LogT(tag_, "can not find app client rtp port=%hu", client->client_rtp_port_);
            //此时app 的rtp可能在tear dowm处已经销毁,但是app rtsp fd还没有销毁,要关闭app rtsp fd
            {
                std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
                close(client->rtsp_fd_);
                m_mapRtspClients.erase(client->rtsp_fd_);
                m_mapRtspConcurrency.erase(client->rtsp_fd_);
            }
            continue;
        }
        CAKLog::LogD(tag_, "there is one rtsp client timeout,remove it, app client rtp port=%hu", client->client_rtp_port_);
        RtpControl::getInstance()->RemoveFd(app_client->rtp_fd_);
        //TODO added by chenyc,2019-10-11,所有这种clode等释放资源的操作,全部放到析构函数里面去做(所谓的RAII). 否则当fd复用的时候,
        //会发生串流,插入失败等问题
        close(app_client->rtp_fd_);
        RtpControl::getInstance()->RemoveFd(app_client->rtcp_fd_);
        close(app_client->rtcp_fd_);
        unsigned short dev_port_to_del = 0;
        //移除已经没有app在监控其视频流的设备列表
        RtpDeviceManager::getInstance()->DeviceRemoveApp(app_client->local_rtp_port_, dev_port_to_del);
        RtpAppManager::getInstance()->RemoveClient(app_client->local_rtp_port_);
        if (dev_port_to_del != 0) //检查该设备是否还有app在监控,若没有,则删除之
        {
            std::shared_ptr<RtpDeviceClient> pDevClient = RtpDeviceManager::getInstance()->GetClientAndRemove(dev_port_to_del);
            if (pDevClient != nullptr)
            {
                RtpControl::getInstance()->RemoveFd(pDevClient->rtp_fd_);
                close(pDevClient->rtp_fd_);
                pDevClient->rtp_fd_ = -1;
                RtpControl::getInstance()->RemoveFd(pDevClient->rtcp_fd_);
                close(pDevClient->rtcp_fd_);
                pDevClient->rtcp_fd_ = -1;

                //zhiwei.chen 向家居请求关闭设备rtp传输 
                CAKLog::LogD(tag_, "send stop rtsp[local ip=%s,local port=%d,mac=%s]",
                             client->local_ip_.c_str(), pDevClient->local_rtp_port_, pDevClient->mac_.c_str());
                SmarthomeHandle::GetInstance().NotifyDevStopRtp(pDevClient->mac_);
            }
        }
        fds.push_back(client->rtsp_fd_);
    }
    {
        std::lock_guard<std::recursive_mutex> lock(rtsp_client_mutex_);
        for (const auto fd : fds)
        {
            CAKLog::LogI(tag_, "app rtsp client time out, socket=%d", fd);
            close(fd);
            m_mapRtspClients.erase(fd);
            m_mapRtspConcurrency.erase(fd);
        }
    }
    return;
}
}

