#pragma once
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include "RtspClient.h"

namespace akuvox
{
class RtspClientManager
{
public:
    ~RtspClientManager();
    static RtspClientManager* getInstance();

    void AddClient(int socketid, const std::string& ip, unsigned short port, bool is_ipv6);
    void AddConcurrency(int socketid);
    void RemoveClient(int socketid);
    int GetClientCount() const;
    void ClearClient();
    std::shared_ptr<RtspClient> GetClient(int socketid);
    std::vector<int> GetAllClientSocket();
    void ReportAll();
    void CheckAppConnect();
    std::shared_ptr<RtspClient> getBeforeAppClient(std::string stAppUid, int nCurrentSocketid);

private:
    RtspClientManager();

private:
    static RtspClientManager* instance;
    const char* tag_;

    std::map<int, std::shared_ptr<RtspClient>> m_mapRtspClients;  //int==rtsp sockfd
    std::map<int, int> m_mapRtspConcurrency;    //rtsp并发,鉴权通过的.使用map便于删除
    //pthread_mutex_t m_lock;
    std::recursive_mutex rtsp_client_mutex_;
};
}
