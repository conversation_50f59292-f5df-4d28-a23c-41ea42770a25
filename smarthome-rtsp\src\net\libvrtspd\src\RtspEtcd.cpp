#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <vector>
#include "CsvrtspConf.h"
#include "util.h"
#include "evpp/event_watcher.h"
#include "RtspEtcd.h"
#include "RtspServerImpl.h"

extern CSVRTSP_CONF gstCSVRTSPConf;
extern std::string g_logic_srv_id;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);


int RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl,  int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}


void EtcdSrvInit()
{
    char outer_addr[128] = {0};
    ::snprintf(outer_addr, sizeof(outer_addr), "%s:%d&[%s]:%d&%s:%d", gstCSVRTSPConf.csvrtsp_outer_ip, RTSP_SERVER_PORT,  gstCSVRTSPConf.csvrtsp_outer_ipv6, RTSP_SERVER_PORT, gstCSVRTSPConf.csvrtsp_outer_domain, RTSP_SERVER_PORT);
    char outer_reg_info[64] = {0};
    ::snprintf(outer_reg_info, 64, "%s%s", "/smarthome/rtsp/outerip/", gstCSVRTSPConf.csvrtsp_outer_ip);
    if (gstCSVRTSPConf.reg_etcd)
    {
        RegSrv2Etcd(outer_reg_info, outer_addr, 10, csbase::REG_OUTER, g_etcd_loop.get());
    }

    g_etcd_loop->Run(); 
}

