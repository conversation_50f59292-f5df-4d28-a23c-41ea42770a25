#include <stdio.h>
#include <stdlib.h>
#include <map>
#include <set>
#include <mutex>
#include "AkLogging.h"
#include "RtspMonitor.h"
#include <unistd.h>
#include "CsvrtspConf.h"


extern CSVRTSP_CONF gstCSVRTSPConf;
CRtspMonitor* CRtspMonitor::instance_ = NULL;

//s:待分割的源字符串,C:分隔符,V:分割后的结果集,vector<>
static void SplitString(const std::string& s, const std::string& c, std::vector<std::string>& oVec)
{
  std::string::size_type pos1 = 0;
  auto pos2 = s.find(c);
  while(std::string::npos != pos2)
  {
	//容错，防止加入一个空串,形如:12,,23,42 或者:12,23, 或者:12,23,,
    if (pos2 > pos1)
    {
    	oVec.push_back(s.substr(pos1, pos2-pos1));
    }
    pos1 = pos2 + c.size();
    pos2 = s.find(c, pos1);
  }
  //将最后一个字符串压入容器中
  if(pos1 != s.length())
  {
      oVec.push_back(s.substr(pos1));
  }
}

static int AkSystem(const char * cmd, std::string& ret)
{
    FILE * fp;   
    if ((fp = popen(cmd, "r")) == NULL)
    {
        return -1;
    }
    else
    {
        char temp_str[4096];
        memset(temp_str, 0, 4096);
        while (fgets(temp_str, 4096, fp) != NULL)
        {
            ret = temp_str;
        }
        pclose(fp);
        return 0;
    }
}

void CRtspMonitor::InitMonitorList(    const std::string& str)
{
    SplitString(str, ",", monitor_mac_list_);
}

CRtspMonitor* CRtspMonitor::Instance()
{
    if (!instance_)
    {
        instance_ = new CRtspMonitor();
    }
    return instance_;
}

/*return now monitor list*/
void CRtspMonitor::AddMac(const std::string& mac)
{
    std::lock_guard<std::mutex> lock(mutex_);
    monitor_mac_list_.push_back(mac);    
}



void CRtspMonitor::ClearMac(    const std::string& mac)
{
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string>::iterator it = monitor_mac_list_.begin();
    for (; it != monitor_mac_list_.end();)
    {
        if (*it == mac)
        {
            it = monitor_mac_list_.erase(it);
            break;
        }
        else
        {
            ++it;
        }
    }
}

/*return now monitor list*/
std::string CRtspMonitor::MonitorList(    )
{
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::stringstream s;
    for (auto& mac : monitor_mac_list_)
    {
        s << mac << ",";
    }
    return s.str();
}


//TODO:添加uuid，在删除时候才不会误删除
void CRtspMonitor::StartAPPMonitor(    const std::string& mac, const std::string& app_ip)
{
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& mac_tmp : monitor_mac_list_)
    {
        if (mac_tmp == mac)
        {
            char cmd[512] = "";
            std::string out;
            snprintf(cmd,sizeof(cmd), "ps -ef |grep %s | grep APP_%s_ | grep -v grep", app_ip.c_str(), mac.c_str());
            AkSystem(cmd, out);

            if (strstr(out.c_str(), "APP_"))
            {
                return;
            }
            
            snprintf(cmd, sizeof(cmd), "nohup tcpdump -i any -s0  -G 60 host %s  -W 1 -C 1 -w %s/RTSP_APP_%s_%%m%%d_%%H%%M%%S.pcap >/dev/null &", 
                                       app_ip.c_str(), gstCSVRTSPConf.monitor_pic_cap_rootpath, mac.c_str());                                
            system(cmd);
            break;
        }
    }
}

void CRtspMonitor::StartDevMonitor(    const std::string& mac,  int dev_start_port)
{
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& mac_tmp : monitor_mac_list_)
    {
        if (mac_tmp == mac)
        {
            char cmd[512] = "";
            snprintf(cmd, sizeof(cmd), " nohup tcpdump -i any -s0  -G 60  port %d or port %d -W 1 -C 1 -w %s/RTSP_DEV_%s_%%m%%d_%%H%%M%%S_%d.pcap >/dev/null &", 
                dev_start_port, dev_start_port + 1, gstCSVRTSPConf.monitor_pic_cap_rootpath, mac.c_str(), dev_start_port);
            system(cmd);
            break;
        }
    }
}


void CRtspMonitor::ClearMonitor(const std::string& mac)
{
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& mac_tmp : monitor_mac_list_)
    {
        if (mac_tmp == mac)
        {
            char cmd[512] = "";
            snprintf(cmd, sizeof(cmd), "sleep 1 && kill -9 `ps -ef |grep tcpdump  |grep '_%s_' | grep -v grep | awk '{print $2}'`", mac.c_str());
            system(cmd);
            break;
        }
    }
}


