#ifndef __CSVRTSP_MONITOR_MNG_H__
#define __CSVRTSP_MONITOR_MNG_H__
#include <list>
#include <string>
#include <vector>
#include <mutex>
#include <boost/noncopyable.hpp>

class CRtspMonitor : public boost::noncopyable
{
public:
    CRtspMonitor()
    {}
    ~CRtspMonitor()
    {}
    static CRtspMonitor* Instance();
    void AddMac(const std::string& mac);
    void ClearMac(const std::string& mac);
    void StartAPPMonitor(    const std::string& mac, const std::string& app_ip);
    void StartDevMonitor(    const std::string& mac, int dev_start_port);
    void ClearMonitor(const std::string& mac);
    std::string MonitorList(   );
    void InitMonitorList(    const std::string& str);

private:
    std::mutex mutex_;
    std::vector<std::string/*mac*/> monitor_mac_list_;
    static CRtspMonitor* instance_;
};

#endif //__CSVRTSP_MONITOR_MNG_H__




