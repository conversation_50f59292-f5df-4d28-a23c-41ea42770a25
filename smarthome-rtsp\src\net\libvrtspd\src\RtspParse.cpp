#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netdb.h>
#include <vector>
#include <sys/socket.h>
#include "RtspParse.h"
#include <netinet/in.h>
#include <arpa/inet.h>
#include "utils.h"
#include "AKLog.h"
#include "strDup.hh"
#include "libvrtspd/CsvrtspConf.h"
#include "RtspServerImpl.h"
#include "RtspClientManager.h"
#include "RtspMonitor.h" 
#include "DigestAuthentication.h"
#include "encrypt/Md5.h"
#include "encrypt/Md5.h"


extern CSVRTSP_CONF gstCSVRTSPConf;
static std::string getProfileLevelId(char* mpSpsPtr)
{
    char buf[50] = { 0 };
    if (mpSpsPtr == NULL)
    {
        sprintf(buf, "profile-level-id=42E01F");
    }
    return std::string(buf);
}

static std::string getSpropParamterSets(char* mpSpsPtr, char* mpPpsPtr)
{
    char buf[255] = { 0 };
    if (mpSpsPtr == NULL)
    {
        sprintf(buf, "sprop-parameter-sets=Z0LAM6tAWgk0IAAAAwAgAAAGUeMGVA==,aM48gA==");
    }

    return std::string(buf);
}

void BuildRtspSDP(std::shared_ptr<akuvox::RtspClient> pAppRtspClient, char *buf, int len)
{
    char sdp_buf[2048] = { 0 };
    
    if (pAppRtspClient->is_ipv6_)
    {
        sprintf(sdp_buf, "v=0\r\no=- 1 1 IN IP6 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP6 0::0\r\nt=0 0\r\nm=video 0 RTP/AVP 96\r\nb=AS:5000\r\na=rtpmap:96 H264/90000\r\na=fmtp:96 %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                gstCSVRTSPConf.csvrtsp_outer_ipv6, getProfileLevelId(NULL).c_str(), getSpropParamterSets(NULL, NULL).c_str());
    }
    else
    {
        sprintf(sdp_buf, "v=0\r\no=- 1 1 IN IP4 %s\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP 96\r\nb=AS:5000\r\na=rtpmap:96 H264/90000\r\na=fmtp:96 %s;packetization-mode=1;%s\r\na=control:trackID=0\r\n\r\n",
                gstCSVRTSPConf.csvrtsp_outer_ip, getProfileLevelId(NULL).c_str(), getSpropParamterSets(NULL, NULL).c_str());
    }
    
    std::string sdp = std::string(sdp_buf);
    // strSDP = "v=0\r\no=- 1 1 IN IP4 127.0.0.1\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP 96\r\nb=AS:5000\r\na=rtpmap:96 H264/90000\r\na=fmtp:96 \r\na=control:trackID=0\r\n\r\n";
    //      strSDP = "v=0\r\no=- 1 1 IN IP4 127.0.0.1\r\ns=Easy Rtsp 1.0\r\ni=Easy\r\nc=IN IP4 0.0.0.0\r\nt=0 0\r\nm=video 0 RTP/AVP 96\r\nb=AS:5000\r\na=rtpmap:96 H264/90000\r\na=fmtp:96 profile-level-id=42C033;sprop-parameter-sets=Z0LAM6tAWgk0IAAAAwAgAAAGUeMGVA==,aM48gA==\r\na=control:trackID=0\r\n\r\n";
    snprintf(buf, len,  "RTSP/1.0 200 OK\r\nCSeq: %d\r\nServer: Streaming Server v0.1\r\nContent-Base: rtsp://%s:%d/live/ch00_0/\r\nContent-Type: application/sdp\r\nContent-Length: %d\r\n\r\n%s",
            pAppRtspClient->seq_num_, pAppRtspClient->local_ip_.c_str(), RTSP_SERVER_PORT, (int)sdp.size(), sdp.c_str());
}

void ResponseRequest(int fd, std::shared_ptr<akuvox::RtspClient> pAppRtspClient)
{
    char buf[2048] = { 0 };

    if (pAppRtspClient == nullptr)
    {
        CAKLog::LogE("RtspParse", "ResponseRequest client is null");
        return;
    }

    switch (pAppRtspClient->method_)
    {
        case RTSP_CMD_OPTIONS:
        {
            sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nServer: Easy Rtsp 1.0\r\nPublic: DESCRIBE, SETUP, TEARDOWN, PLAY, PAUSE, SET_PARAMETER, GET_PARAMETER\r\n\r\n",
                    pAppRtspClient->seq_num_);
            break;
        }

        case RTSP_CMD_DESCRIBE: //区分ipv6
        {
            BuildRtspSDP(pAppRtspClient, buf, sizeof(buf));
            break;
        }

        case RTSP_CMD_SETUP:
        {
            if (pAppRtspClient->client_port_.size() == 0) //证明是rtp over tcp,当前不支持
            {
                sprintf(buf, "RTSP/1.0 461 Unsupported transport\r\nCSeq: %d\r\nCache-Control: no-cache\r\nTransport: RTP/AVP/TCP;unicast;interleaved=0-1\r\nSession: %s\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->getRandSessionId().c_str());
            }
            else  //证明是rtp over udp ,那么rtsp服务端就需要监听一个udp端口来获取到客户端的rtp数据(一般是两个包)以实现客户端的udp NAT，获取到客户端的外网udp端口
            {
                sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nCache-Control: no-cache\r\nTransport: RTP/AVP;unicast;mode=play;%s;server_port=%d-%d\r\nSession: %s\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->client_port_.c_str(), pAppRtspClient->local_rtp_port_,
                        pAppRtspClient->local_rtp_port_ + 1, pAppRtspClient->getRandSessionId().c_str());

            }
            break;
        }

        case RTSP_CMD_PLAY:
        {
            //ipv6
            if (pAppRtspClient->is_ipv6_)
            {
                sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nSession: %s;timeout=60\r\nRange: npt=now-\r\nRTP-Info: url=rtsp://[%s]:%d/live/ch00_0//trackID=0;seq=0;rtptime=0\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->getSessionId().c_str(), pAppRtspClient->GetLocalIpv6().c_str(), RTSP_SERVER_PORT);

            }
            else
            {
                sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nSession: %s;timeout=60\r\nRange: npt=now-\r\nRTP-Info: url=rtsp://%s:%d/live/ch00_0//trackID=0;seq=0;rtptime=0\r\n\r\n",
                        pAppRtspClient->seq_num_, pAppRtspClient->getSessionId().c_str(), pAppRtspClient->local_ip_.c_str(), RTSP_SERVER_PORT);

            }

            break;
        }

        case RTSP_CMD_SET_GET_PARAMETER:
        {
            char time_buf[100] = { 0 };
            struct tm* timeinfo;
            time_t rawtime;
            time(&rawtime);
            timeinfo = localtime(&rawtime);
            strftime(time_buf, sizeof(time_buf), "%a %b %d %H:%M:%S %Y", timeinfo);
            sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\nDate: %s\r\n\r\n",
                    pAppRtspClient->seq_num_, time_buf);
        }
        break;

        case RTSP_CMD_TEARDOWN:
        {
            sprintf(buf, "RTSP/1.0 200 OK\r\nCSeq: %d\r\n\r\n",
                    pAppRtspClient->seq_num_);
            break;
        }

        default:
            break;
    }
    if (strlen(buf) > 0)
    {
        write(fd, buf, strlen(buf));//TODO chenyc,2019-01-09,响应完teardown之后,服务端主动关闭tcp连接
    }
}

//int: 通过不同的返回值区分不同的错误.-1:内部错误;-2:客户端保活次数超过限制
int ParseRequest(int fd, std::shared_ptr<akuvox::RtspClient> client, std::string strRequest)
{
    if (client == nullptr)
    {
        CAKLog::LogE("RtspParse", "ParseRequest client is null");
        return -1;
    }

    std::vector<std::string> lines;
    std::string strSplit("\r\n");

    client->map_.clear();
    string_split(strRequest, strSplit, &lines);
    if (lines.size() < 2)
    {
        CAKLog::LogE("RtspParse", "field < 2....");
        return -1;
    }

    // 1.Method
    const char* cmd_names[] = { RTSP_CMD_OPTIONS_STR, RTSP_CMD_DESCRIBE_STR, RTSP_CMD_SETUP_STR, RTSP_CMD_TEARDOWN_STR,
                                RTSP_CMD_PLAY_STR, RTSP_CMD_PAUSE_STR, RTSP_CMD_SET_PARAMETER_STR, RTSP_CMD_SET_GET_PARAMETER_STR,
                                RTSP_CMD_SET_RECORD_STR,RTSP_CMD_SET_ANNOUNCE_STR
                              };
    for (int i = 0; i < (int)ARRAY_SIZE(cmd_names); i++)
    {
        if (lines[0].find(cmd_names[i]) != std::string::npos)
        {
            client->method_ = i;
            break;
        }
    }
    //如果不是以上的方法之一，那么证明是非法的请求,直接断开客户端的tcp 连接:
    if(client->method_ == -1)
    {
        CAKLog::LogE("RtspParse", "The msg is invalid rtsp request");
        return -1;
    }
    
    if (client->method_ == RTSP_CMD_SET_GET_PARAMETER)
    {
        client->rtsp_keep_alive_times_++;
        //add chenzhx 防止室内机一直监控 忘记关闭的问题。
        if (gstCSVRTSPConf.keep_alive_times > 0 && client->rtsp_keep_alive_times_ >= gstCSVRTSPConf.keep_alive_times)
        {
            CAKLog::LogD("RtspParse", "recv app rtsp keepalive,mac=%s, which is more than allowed %d times.", client->mac_.c_str(), gstCSVRTSPConf.keep_alive_times);
            return -2;
            //modified by chenyc,2019-12-02,不能直接使用close全关闭,否则后续rtsp fd复用的时候,RtpAppClient会发生交叉的问题
            //std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClientByRtspFd(client->rtsp_fd_);
            //close(client->rtsp_fd_);
        }
        else
        {
            CAKLog::LogD("RtspParse", "recv app rtsp keepalive,mac=%s   %d  %d ", client->mac_.c_str(), client->rtsp_keep_alive_times_, gstCSVRTSPConf.keep_alive_times);
            client->is_connect_ = true;
        }
    }
    else if ((client->method_ == RTSP_CMD_OPTIONS) || (client->method_ == RTSP_CMD_DESCRIBE))
    {
        std::vector<std::string> params;
        std::string strSplit(" ");
        string_split(lines[0], strSplit, &params);
        if (params.size() < 2)
        {
            CAKLog::LogE("RtspParse", "option field < 2");
            return -1;
        }

        std::size_t found = params[1].rfind("/");
        if (found == std::string::npos)
        {
            CAKLog::LogE("RtspParse", "cannot find mac");
            return -1;
        }
        client->mac_ = params[1].substr(found + 1);

        //add by chenzhx,在这里启动抓包程序，在setup抓包有点晚
        if (client->method_ == RTSP_CMD_DESCRIBE)
        {
            CRtspMonitor::Instance()->StartAPPMonitor(client->mac_, client->client_ip_);
        }

    }


    // 2.CSeq
    // added by chenyc, 2018-08-14, cseq不一定在第二行
    //if (lines[1].find("CSeq") == std::string::npos)
    //{
    //  CAKLog::LogE("RtspParse", "cannot find cseq");
    //  return;
    //}

    // 3.others info, store into client data map
    for (int i = 1; i < (int)lines.size(); i++)
    {
        int pos = lines[i].find(":");
        if (pos == (int)std::string::npos)
        {
            continue;
        }

        std::string strKey = lines[i].substr(0, pos);
        std::string strValue = lines[i].substr(pos + 2, lines[i].size());
        client->map_[strKey] = strValue;

        if (strKey.find("Transport") != std::string::npos)
        {
            //CAKLog::LogD("RtspParse", "strKey:%s %s", strKey.c_str(), strValue.c_str());
            std::vector<std::string> tmpVector;
            std::string strSplit = ";";
            string_split(strValue, strSplit, &tmpVector);
            for (int j = 0; j < (int)tmpVector.size(); j++)
            {
                if (tmpVector[j].find("client_port") != std::string::npos)
                {
                    client->client_port_ = tmpVector[j];
                    sscanf(client->client_port_.c_str(), "client_port=%hu-%hu", &client->client_rtp_port_, &client->client_rtcp_port_);
                    break;
                }
            }
        }
        else if (strKey.find("CSeq") != std::string::npos)
        {
            client->seq_num_ = atoi(lines[i].c_str() + strlen("CSeq:"));
        }
    }
    return 0;
}

bool parseAuthorizationHeader(char const* buf,
                                 char const*& username,
                                 char const*& realm,
                                 char const*& nonce, char const*& uri,
                                 char const*& response)
{
    CAKLog::LogD("RtspParse", "begin parseAuthorizationHeader");
    // Initialize the result parameters to default values:
    username = realm = nonce = uri = response = NULL;

    // First, find "Authorization:"
    while (1)
    {
        if (*buf == '\0')
        {
            return false;
        }
        if (strncasecmp(buf, "Authorization: Digest ", 22) == 0)
        {
            break;
        }
        ++buf;
    }

    // Then, run through each of the fields, looking for ones we handle:
    char const* fields = buf + 22;
    while (*fields == ' ')
    {
        ++fields;
    }
    char* parameter = strDupSize(fields);
    char* value = strDupSize(fields);
    while (1)
    {
        value[0] = '\0';
        if (sscanf(fields, "%[^=]=\"%[^\"]\"", parameter, value) != 2 &&
                sscanf(fields, "%[^=]=\"\"", parameter) != 1)
        {
            break;
        }
        if (strcmp(parameter, "username") == 0)
        {
            username = strDup(value);
        }
        else if (strcmp(parameter, "realm") == 0)
        {
            realm = strDup(value);
        }
        else if (strcmp(parameter, "nonce") == 0)
        {
            nonce = strDup(value);
        }
        else if (strcmp(parameter, "uri") == 0)
        {
            uri = strDup(value);
        }
        else if (strcmp(parameter, "response") == 0)
        {
            response = strDup(value);
        }

        fields += strlen(parameter) + 2 /*="*/ + strlen(value) + 1 /*"*/;
        while (*fields == ',' || *fields == ' ')
        {
            ++fields;
        }
        // skip over any separating ',' and ' ' chars
        if (*fields == '\0' || *fields == '\r' || *fields == '\n')
        {
            break;
        }
    }
    delete[] parameter;
    delete[] value;
    CAKLog::LogT("RtspParse", "end parseAuthorizationHeader");
    return true;
}

bool parseAuthorizationHeaderForAccount(char const* buf,char* pszUserAccount, int size)
{
    const char* pTmp = nullptr;
    const char* pTmpEnd = nullptr;
    if ((pTmp = strstr(buf, "Account: ")))
    {
        pTmpEnd = pTmp;
        int i = 0;
        while (*pTmpEnd && *pTmpEnd != '\r')
        {
            i++;
            pTmpEnd++;
        }
        if (size > i + 1)
        {
            ::snprintf(pszUserAccount, i - 8, "%s", pTmp + 9);
        }
        else
        {
            ::snprintf(pszUserAccount, size, "%s", pTmp + 9);
        }
        return true;
    }
    return false;
}

bool parseAuthorizationHeaderForManual(char const* buf, int& manual)
{
    const char* pTmp = nullptr;
    const char* pTmpEnd = nullptr;
    if ((pTmp = strstr(buf, "Manual: ")))
    {
        pTmpEnd = pTmp;
        int i = 0;
        while (*pTmpEnd && *pTmpEnd != '\r')
        {
            i++;
            pTmpEnd++;
        }
        char szManual[8];
        ::snprintf(szManual, i, "%s", pTmp + 8);
        manual = atoi(szManual);
        return true;
    }
    return false;
}


