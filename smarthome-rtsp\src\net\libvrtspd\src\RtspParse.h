#pragma once
#include "RtspClient.h"
#include <string>
#include <memory>
#include "Boolean.hh"



#define RTSP_CMD_OPTIONS_STR "OPTIONS"
#define RTSP_CMD_DESCRIBE_STR "DESCRIBE"
#define RTSP_CMD_SETUP_STR "SETUP"
#define RTSP_CMD_TEARDOWN_STR "TEARDOWN"
#define RTSP_CMD_PLAY_STR "PLAY"
#define RTSP_CMD_PAUSE_STR "PAUSE"
#define RTSP_CMD_SET_PARAMETER_STR "SET_PARAMETER"
#define RTSP_CMD_SET_GET_PARAMETER_STR "GET_PARAMETER"
#define RTSP_CMD_SET_ANNOUNCE_STR "ANNOUNCE"
#define RTSP_CMD_SET_RECORD_STR "RECORD"

enum
{
    RTSP_CMD_NONE = -1,
    RTSP_CMD_OPTIONS = 0,
    RTSP_CMD_DESCRIBE,
    RTSP_CMD_SETUP,
    RTSP_CMD_TEARDOWN,
    RTSP_CMD_PLAY,
    RTSP_CMD_PAUSE,
    RTSP_CMD_SET_PARAMETER,
    RTSP_CMD_SET_GET_PARAMETER,
    RTSP_CMD_SET_ANNOUNCE,
    RTSP_CMD_SET_RECORD,
    RTSP_CMD_MAX
};

int ParseRequest(int fd, std::shared_ptr<akuvox::RtspClient> client, std::string strRequest);
void ResponseRequest(int fd, std::shared_ptr<akuvox::RtspClient> client);
bool parseAuthorizationHeader(char const* buf,
                                 char const*& username,
                                 char const*& realm,
                                 char const*& nonce, char const*& uri,
                                 char const*& response);
bool parseAuthorizationHeaderForAccount(char const* buf,char* pszUserAccount, int size);
bool parseAuthorizationHeaderForManual(char const* buf, int& manual);
