#include <sys/types.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <pthread.h>
#include <sstream>
#include <functional>
#include "utils.h"
#include "AKLog.h"
#include "rtp/RtpControl.h"
#include "rtp/RtpAppManager.h"
#include "rtp/RtpDeviceManager.h"
#include "RtspParse.h"
#include "RtspServerImpl.h"
#include "RtspClientManager.h"
#include "Unp.h"
#include "CsvrtspConf.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "Singleton.h"
#include "SnowFlakeGid.h"
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "RtspMonitor.h"
#include "AkcsBussiness.h"
#include "AkcsMonitor.h"
#include "SmarthomeHandle.h"

//using namespace std;
extern CSVRTSP_CONF gstCSVRTSPConf;
extern std::string g_logic_srv_id;

namespace akuvox
{
void* rtsp_recv_thread(void* data)
{
    CRtspServerImpl* rtsp_server = (CRtspServerImpl*)data;
    rtsp_server->OnRun();
    return NULL;
}

void* rtsp_process_thread(void* data)
{
    CRtspServerImpl* rtsp_server = (CRtspServerImpl*)data;
    rtsp_server->ThreadProcess();
    return NULL;
}

CRtspServerImpl* CRtspServerImpl::gRtspServer;
CRtspServerImpl::CRtspServerImpl() : tag_("RtspServerImpl")
{
    m_nTidRecv = 0;
    m_nTidProcess = 0;
    m_bRunState = false;
    m_nListenFd = 0;
    epoll_fd_ = 0;
    pthread_mutex_init(&m_mutex, NULL);
    recv_buf_ = new unsigned char[RTSP_BUFFER_SIZE];
    msg_header_ = nullptr;
    msg_count_ = 0;
    event_ = new CWaitEvent();
}

CRtspServerImpl::~CRtspServerImpl()
{
    if (recv_buf_ != nullptr)
    {
        delete[] recv_buf_;
        recv_buf_ = nullptr;
    }

    if (event_ != nullptr)
    {
        delete (CWaitEvent*)event_;
        event_ = nullptr;
    }

    pthread_mutex_destroy(&m_mutex);
}

CRtspServerImpl* CRtspServerImpl::GetInstance()
{
    if (gRtspServer == NULL)
    {
        gRtspServer = new CRtspServerImpl();
    }

    return gRtspServer;
}

int CRtspServerImpl::start()
{
    CAKLog::LogI(tag_, "start");
    if (m_nTidRecv != 0 || m_nTidProcess != 0)
    {
        CAKLog::LogW(tag_, "Rtsp has running.Recv thread id=%d, process thread id=%d", m_nTidRecv, m_nTidProcess);
        return 0;
    }
    using namespace std::placeholders;
    //added by chenyc,2022-08-11,设置判断客户端攻击时的相关参数,当满足条件时,BussinessLimit会回调cb
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(VRTSP_AUTH_BUSSINESS, BUSSINESS_PERIOD,
                                     BUSSINESS_NUM, BUSSINESS_KEY_EXPIRE, std::bind(&CRtspServerImpl::AttackedCallback, this, _1, _2));

     AKCS::Singleton<BussinessLimit>::instance().InitBussiness(VRTSP_INVALID_MSG_BUSSINESS, BUSSINESS_PERIOD,
                                      BUSSINESS_NUM, BUSSINESS_KEY_EXPIRE, std::bind(&CRtspServerImpl::AttackedCallback, this, _1, _2));
                                
    CreateRtspSocket();
    m_bRunState = true;
    pthread_create(&m_nTidRecv, NULL, rtsp_recv_thread, this);
    pthread_create(&m_nTidProcess, NULL, rtsp_process_thread, this);
    RtpControl::getInstance()->Start();

    return 0;
}

int CRtspServerImpl::stop()
{
    if (m_nTidRecv == 0)
    {
        CAKLog::LogW(tag_, "Rtsp is not running");
    }

    pthread_mutex_lock(&m_mutex);
    m_bRunState = false;
    pthread_mutex_unlock(&m_mutex);
    pthread_join(m_nTidRecv, NULL);
    pthread_join(m_nTidProcess, NULL);
    m_nTidRecv = 0;
    m_nTidProcess = 0;
    RtpControl::getInstance()->Stop();
    ReleaseRtspSocket();

    return 0;
}

void CRtspServerImpl::report()
{
    CAKLog::LogD(tag_, "start report all info--------------------------");
    RtspClientManager::getInstance()->ReportAll();
    RtpAppManager::getInstance()->ReportAll();
    RtpDeviceManager::getInstance()->ReportAll();
    CAKLog::LogD(tag_, "stop report all info------------------------------");
}

bool CRtspServerImpl::CreateRtspSocket(int port, int &listen_fd)
{
    listen_fd = socket(AF_INET6, SOCK_STREAM, 0);
    if (listen_fd < 0)
    {
        CAKLog::LogE(tag_, "create rtsp tcp socket error=%s errno=%d", strerror(errno), errno);
        return false;
    }
    CAKLog::LogI(tag_, "create rtsp tcp socket=%d", listen_fd);

    //reuse addr
    int yes = 1;
    int ret = setsockopt(listen_fd, SOL_SOCKET, SO_REUSEADDR, (char*)&yes, sizeof(yes));
    if (ret < 0)
    {
        CAKLog::LogE(tag_, "set reuseaddr socket error=%s errno=%d", strerror(errno), errno);
        close(listen_fd);
        return false;
    }
    struct sockaddr_in6 server_addr;
    bzero(&server_addr, sizeof(server_addr));
    server_addr.sin6_family = AF_INET6;
    server_addr.sin6_addr = in6addr_any;
    server_addr.sin6_port = htons(port);

    //memset(server_addr.sin_zero, '\0', sizeof(server_addr.sin_zero));

    ret = bind(listen_fd, (struct sockaddr*)&server_addr, sizeof(server_addr));
    if (ret < 0)
    {
        CAKLog::LogE(tag_, "bind socket error=%s errno=%d", strerror(errno), errno);
        close(listen_fd);
        return false;
    }

    // nodelay
    yes = 1;
    ret = setsockopt(listen_fd, IPPROTO_TCP, TCP_NODELAY, (char*)&yes, sizeof(yes));
    if (ret < 0)
    {
        CAKLog::LogE(tag_, "set nodelay socket error=%s errno=%d", strerror(errno), errno);
        close(listen_fd);
        return false;
    }

    //listen
    ret = listen(listen_fd, RTSP_CLIENT_MAX);
    if (ret < 0)
    {
        CAKLog::LogE(tag_, "listen socket error=%s errno=%d", strerror(errno), errno);
        close(listen_fd);
        return false;
    }

    //epoll listen fd
    AddFd(listen_fd, false);
    return true;
}


//ipv6
bool CRtspServerImpl::CreateRtspSocket()
{
    //create epoll
    epoll_fd_ = epoll_create(RTSP_EVENT_MAX);
    if (-1 == epoll_fd_)
    {
        CAKLog::LogE(tag_, "create epoll error=%s errno=%d", strerror(errno), errno);
        exit(1);
    }

    CreateRtspSocket(RTSP_SERVER_PORT, m_nListenFd);
    
    return true;
}

void CRtspServerImpl::ReleaseRtspSocket()
{
    //release resource
    std::vector<int> clients = RtspClientManager::getInstance()->GetAllClientSocket();
    for (auto client : clients)
    {
        HandleDisconnect(client);
    }

    RemoveFd(m_nListenFd);
    close(m_nListenFd);
   
}

int CRtspServerImpl::SetNonblocking(int fd)
{
    int old_option = fcntl(fd, F_GETFL);
    int new_option = old_option | O_NONBLOCK;
    fcntl(fd, F_SETFL, new_option);
    return old_option;
}

void CRtspServerImpl::AddFd(int fd, bool enable_et)
{
    pthread_mutex_lock(&m_mutex);
    epoll_event event = { 0 };
    event.data.fd = fd;
    event.events = EPOLLIN;
    if (enable_et)
    {
        event.events |= EPOLLET;
    }
    int ret = epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, fd, &event);
    if (ret != 0)
    {
        CAKLog::LogW(tag_, "RTSP add monitor error fd=%d", fd);
    }     
    SetNonblocking(fd);
    CAKLog::LogD(tag_, "RTSP add monitor fd=%d", fd);
    pthread_mutex_unlock(&m_mutex);
}

void CRtspServerImpl::RemoveFd(int fd)
{
    pthread_mutex_lock(&m_mutex);
    epoll_event event = { 0 };
    event.data.fd = fd;
    int ret = epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, fd, &event);
    if (ret != 0)
    {
        CAKLog::LogW(tag_, "RTSP del monitor error fd=%d", fd);
    }     
    CAKLog::LogD(tag_, "RTSP del monitor fd=%d", fd);
    pthread_mutex_unlock(&m_mutex);
}

void CRtspServerImpl::OnRun()
{
    CAKLog::LogI(tag_, "RTSP thread running");
    epoll_event events[RTSP_EVENT_MAX] = {0};
    while (m_bRunState)
    {
        int ret = epoll_wait(epoll_fd_, events, RTSP_EVENT_MAX, -1);
        if (ret == -1)
        {
            CAKLog::LogE(tag_,  "rtsp epoll_wait failure, errno is %d", errno);
            //usleep(100 * 1000);
            continue;
        }

        LevelTrigger(events, ret);
    }

    close(epoll_fd_);
    CAKLog::LogI(tag_, "RTSP thread end");
}

void CRtspServerImpl::LevelTrigger(epoll_event* events, int number)
{
    //struct sockaddr_in client_addr;
    struct sockaddr_storage client_addr;
    socklen_t client_addr_size = sizeof(client_addr);
    for (int i = 0; i < number; i++)
    {
        int sockfd = events[i].data.fd;
        //ipv6
        if (sockfd == m_nListenFd) //ipv4/ipv6兼容
        {
            int client_sock = Accept(sockfd, (struct sockaddr*)&client_addr, &client_addr_size);
            if (client_sock >= 0)
            {
                CAKLog::LogD(tag_, "rtsp client connection from %s, rtsp port:%d \n", 
                    Sock_ntop((SA*)&client_addr, client_addr_size), RTSP_SERVER_PORT);
                //added by chenyc,2020-03-09,硬编码限制rtsp-tcp客户端数量为200,超过该数值,考虑被定点攻击了,直接关闭新的客户端连接,
                //保证系统资源不会被攻击耗尽;
                if (RtspClientManager::getInstance()->GetClientCount() > 200)
                {
                    ::close(client_sock);
                    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("smarthome-rtsp", "There are more than 200 rtsp-tcp clients", AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_RTSP);
                    break;
                }
                AddMsgToList(client_sock, RTSP_MSG_TYPE_CONNECT, (unsigned char*)&client_addr, sizeof(client_addr));
                AddFd(client_sock, false);
            }
        }
        else if (events[i].events & EPOLLIN)
        {
            //CAKLog::LogT(tag_, "event trigger once");
            memset(recv_buf_, 0, RTSP_BUFFER_SIZE);
            int ret = recv(sockfd, recv_buf_, RTSP_BUFFER_SIZE, 0);//收到 rst，没有处理.rtsp 客户端描述符
            if (ret < 0)
            {
                CAKLog::LogE(tag_, "socket=%d recv err %d", sockfd, ret);
                //added by chenyc ,需要处理错误的异常情况.
                if (errno == EAGAIN || errno == EINTR)//不是错误
                {
                    continue;
                }
                else //socket发生错误了,典型的就是,对端重置socket了
                {
                    CAKLog::LogW(tag_, "socket fd =%d recv -1,net error", sockfd);
                    RemoveFd(sockfd);
                    //::close(sockfd);modified by chenyc,2019-12-02,清理app-rtsp  跟app-rtp资源之间会发生交叉的风险,应尽量避免,转移到资源清理流程去close,后续用RAII保证
                    AddMsgToList(sockfd, RTSP_MSG_TYPE_NET_ERROR, NULL, 0);
                }
            }
            else if (ret == 0)
            {
                CAKLog::LogI(tag_, "socket=%d recv 0,disconnect", sockfd);
                RemoveFd(sockfd);
                //close(sockfd);
                AddMsgToList(sockfd, RTSP_MSG_TYPE_DISCONNECT, NULL, 0);
            }
            else
            {
                if (recv_buf_[0] == 0x24 || recv_buf_[0] == 0xff || recv_buf_[0] == 0x80)
                {
                    CAKLog::LogI(tag_, "buf %#x %#x", recv_buf_[0], recv_buf_[1]);
                }
                else
                {
                    if (isascii(recv_buf_[0]))
                    {
                        AddMsgToList(sockfd, RTSP_MSG_TYPE_DATA, recv_buf_, ret);
                    }
                }
            }
        }
        else
        {
            CAKLog::LogE(tag_,  "something else happened");
        }
    }
}

int CRtspServerImpl::AddMsgToList(int fd, int msg_type, unsigned char* data, unsigned int data_len)
{
    RTSP_MSG_LIST* cur_node = nullptr;
    RTSP_MSG_LIST* new_node = new RTSP_MSG_LIST();
    if (nullptr == new_node)
    {
        return -1;
    }

    memset(new_node, 0, sizeof(RTSP_MSG_LIST));
    new_node->fd = fd;
    new_node->msg_type = msg_type;
    if (data != nullptr && data_len > 0)
    {
        new_node->data_len = data_len;
        new_node->data = new unsigned char[data_len + 1];
        memset(new_node->data, 0, data_len + 1);
        memcpy(new_node->data, data, data_len);
    }
    pthread_mutex_lock(&m_mutex);
    if (msg_header_ == nullptr)
    {
        msg_header_ = new_node;
        msg_count_ = 1;
    }
    else
    {
        if (msg_count_ >= RTSP_MSG_MAX)
        {
            CAKLog::LogE(tag_, "RTSP socket=%d recv msg full.delete old pkg", fd);
            RTSP_MSG_LIST* tmp_node2 = (RTSP_MSG_LIST*)msg_header_;
            msg_header_ = ((RTSP_MSG_LIST*)msg_header_)->next;
            if (tmp_node2->data != nullptr)
            {
                delete[] tmp_node2->data;
            }
            delete tmp_node2;
            msg_count_--;
        }

        cur_node = (RTSP_MSG_LIST*)msg_header_;
        while ((cur_node != nullptr) && (cur_node->next != nullptr))
        {
            cur_node = cur_node->next;
        }
        cur_node->next = new_node;
        msg_count_++;
        if (msg_count_ > RTSP_MSG_WARN_COUNT)
        {
            CAKLog::LogW(tag_, "RTSP msg count already reach %d", msg_count_);
        }
    }
    event_->Set();
    pthread_mutex_unlock(&m_mutex);

    return 0;
}

void CRtspServerImpl::ThreadProcess()
{
    CAKLog::LogI(tag_, "RTSP ProcessThread start");
    while (m_bRunState)
    {
        pthread_mutex_lock(&m_mutex);
        if (NULL == msg_header_)
        {
            pthread_mutex_unlock(&m_mutex);
            event_->Wait();
            pthread_mutex_lock(&m_mutex);
        }

        RTSP_MSG_LIST* tmp_node = nullptr;
        while (msg_header_ != nullptr)
        {
            tmp_node = (RTSP_MSG_LIST*)msg_header_;
            msg_header_ = ((RTSP_MSG_LIST*)msg_header_)->next;
            msg_count_--;
            pthread_mutex_unlock(&m_mutex);
            if (!ProcessMsg(tmp_node->fd, tmp_node->msg_type, tmp_node->data, tmp_node->data_len))
            {
                CAKLog::LogE(tag_, "%s ProcessMsg failed", __FUNCTION__);
            }

            pthread_mutex_lock(&m_mutex);
            if (tmp_node->data != nullptr)
            {
                delete[] tmp_node->data;
            }
            delete tmp_node;
        }
        pthread_mutex_unlock(&m_mutex);
    }

    CAKLog::LogI(tag_, "RTSP ProcessThread end");
}

bool CRtspServerImpl::ProcessMsg(int fd, int msg_type, unsigned char* data, unsigned int data_len)
{
    bool ret = false;
    switch (msg_type)
    {
        case RTSP_MSG_TYPE_CONNECT:
        {
            ret = HandleConnect(fd, data, data_len);
            break;
        }
        case RTSP_MSG_TYPE_DISCONNECT:
        {
            ret = HandleDisconnect(fd);
            break;
        }
        case RTSP_MSG_TYPE_NET_ERROR:
        {
            ret = HandleNetError(fd);
            break;
        }
        case RTSP_MSG_TYPE_DATA:
        {
            ret = handleParseRtsp(fd, (char*)data, data_len);
            break;
        }
        default:
            break;
    }

    return ret;
}

bool CRtspServerImpl::HandleConnect(int connfd, unsigned char* pPkgBuf, unsigned int nPkgSize)
{
    //判断是否是 ipv6
    //bool ipv6 = ((SA *)pPkgBuf)->sa_family == AF_INET6 ? true : false;//[::ffff:**************]:37414 会全部检测为ipv6
    std::string ip = sock_inet_ntop((SA*)pPkgBuf);
    bool ipv6 = false;
    if (ip.find("::ffff") == std::string::npos)
    {
        ipv6 = true;
    }
    else
    {
        ip = ip.substr(7);//跳过::ffff:,直接获取ipv4的地址
    }
    RtspClientManager::getInstance()->AddClient(connfd, ip, ntohs(sock_get_port((SA*)pPkgBuf)), ipv6);
    return true;
}

bool CRtspServerImpl::HandleDisconnect(int connfd)
{
    CAKLog::LogT(tag_, "handle disconnect fd=%d", connfd);
    //先销毁rtp client
    std::shared_ptr<RtspClient> client = RtspClientManager::getInstance()->GetClient(connfd);
    if (client)
    {
        handleTeardown(client);
    }
    RtspClientManager::getInstance()->RemoveClient(connfd);
    ::close(connfd);
    return true;
}
bool CRtspServerImpl::HandleNetError(int connfd)//connfd:rtsp客户端的rtsp fd
{
    std::shared_ptr<RtspClient> client = RtspClientManager::getInstance()->GetClient(connfd);
    if (client != NULL)
    {
        handleTeardown(client);
    }
    RtspClientManager::getInstance()->RemoveClient(connfd);
    ::close(connfd);
    return true;
}

bool CRtspServerImpl::handleParseRtsp(int nClientFd, char* pPkgBuf, unsigned int nPkgSize)
{
    std::shared_ptr<RtspClient> pAppRtspClient = RtspClientManager::getInstance()->GetClient(nClientFd);
    if (pAppRtspClient == NULL)
    {
        CAKLog::LogE(tag_, "can not find client=%d", nClientFd);
        return false;
    }
    char byRecvBuf[RTSP_BUFFER_SIZE];
    memset(byRecvBuf, 0, sizeof(byRecvBuf));

    //与上一帧数据组合起来
    int size = pAppRtspClient->rtsp_recv_size_ + nPkgSize;
    if (size > RTSP_BUFFER_SIZE)
    {
        memset(pAppRtspClient->rtsp_recv_buf_, 0, sizeof(pAppRtspClient->rtsp_recv_buf_));
        pAppRtspClient->rtsp_recv_size_ = 0;
        return false;
    }

    memcpy(byRecvBuf, pAppRtspClient->rtsp_recv_buf_, pAppRtspClient->rtsp_recv_size_);
    memcpy(byRecvBuf + pAppRtspClient->rtsp_recv_size_, (char*)pPkgBuf, nPkgSize);

	//added by chenyc,2021.08.30,判断一条rtsp信令结束的条件是连续两个\r\n 同时如果还有SDP，还得在第一个连续的两个\r\n基础上，通过Content-Length来解析SDP
	//具体可见rtsp标准信令格式
    if (::strstr(byRecvBuf, "\r\n\r\n") == NULL)
    {
        memcpy(pAppRtspClient->rtsp_recv_buf_, byRecvBuf, size);
        pAppRtspClient->rtsp_recv_size_ = size;
        return false;
    }
    else//已经是一条完整的rtsp请求信令了
    {
        memset(pAppRtspClient->rtsp_recv_buf_, 0, sizeof(pAppRtspClient->rtsp_recv_buf_));
        pAppRtspClient->rtsp_recv_size_ = 0;
    }
    std::string request = byRecvBuf;
    //modified by chenyc,2019-12-03,通过返回值判断错误,增加客户端rtsp保活超时
    int parse_ret = ParseRequest(nClientFd, pAppRtspClient, request);
    if (parse_ret != 0)//modified by chenyc,2022-08-10, 只要rtsp信令解析错误，证明是非法的请求，直接关闭tcp连接
    {
        RemoveFd(nClientFd);
        HandleDisconnect(nClientFd);
        if(parse_ret == -1)
        {
            AddBussiness(VRTSP_INVALID_MSG_BUSSINESS, pAppRtspClient->GetClientIp());
        }
        return false;
    }
    bool ret = true;
    switch (pAppRtspClient->method_)
    {
        case RTSP_CMD_OPTIONS:
        {
            ret = handleOption(pAppRtspClient);
            break;
        }
        case RTSP_CMD_DESCRIBE:
        {
            ret = handleDescribe(pAppRtspClient, pPkgBuf);
            if (!ret)
            {
                //如果授权未成功，则不返回200OK,在handleDescribe里面已经回复401了
                return true;
            }
            break;
        }
        case RTSP_CMD_SETUP:
        {
            ret = handleSetup(pAppRtspClient);
            break;
        }
        case RTSP_CMD_PLAY:
        {
            ret = handlePlay(pAppRtspClient);
            break;
        }
        case RTSP_CMD_TEARDOWN:
        {
            CRtspMonitor::Instance()->ClearMonitor(pAppRtspClient->mac_);
            ret = handleTeardown(pAppRtspClient);
            break;
        }
        default:
            break;
    }

    ResponseRequest(nClientFd, pAppRtspClient);
    return ret;
}

bool CRtspServerImpl::handleOption(std::shared_ptr<RtspClient> pAppRtspClient)
{
    CAKLog::LogD(tag_, "begin option, socket=%d", pAppRtspClient->rtsp_fd_);
    //client->GetLocalIp(); modified by chenyc,2018-05-23
    CAKLog::LogT(tag_, "end option, socket=%d", pAppRtspClient->rtsp_fd_);
	pAppRtspClient->SetStatus(RtspClient::Status::kOption);
    return true;
}

//setup的response也要检验
bool CRtspServerImpl::handleDescribe(std::shared_ptr<RtspClient> pAppRtspClient, const char* full_request)
{
    CAKLog::LogD(tag_, "begin describe, socket=%d", pAppRtspClient->rtsp_fd_);
    bool ret = true;
    do
    {
        if (!pAppRtspClient->authenticationOK("DESCRIBE", full_request))
        {
            //added by chenyc,2022-08-11,记录该rtsp客户端鉴权失败的次数，若超过限定次数,则直接关闭tcp连接.
            pAppRtspClient->AddAuthFailedNum();
            if(pAppRtspClient->AuthFailedNum() >= RTSP_CLIENT_AUTH_FAILED_TRY_NUM)
            {
                RemoveFd(pAppRtspClient->rtsp_fd_);
                HandleDisconnect(pAppRtspClient->rtsp_fd_);
                //加入恶意攻击的判断业务流程
                AddBussiness(VRTSP_AUTH_BUSSINESS, pAppRtspClient->GetClientIp());
            }
            ret = false;
        }
        else
        {
            //鉴权通过加入并发数
            pAppRtspClient->SetStatus(RtspClient::Status::kDesc);
            RtspClientManager::getInstance()->AddConcurrency(pAppRtspClient->rtsp_fd_);
        }

    }
    while (0);
    CAKLog::LogT(tag_, "end describe, socket=%d", pAppRtspClient->rtsp_fd_);
    return ret;
}

bool CRtspServerImpl::handleSetup(std::shared_ptr<RtspClient> pAppRtspClient)
{
    if (pAppRtspClient->GetStatus() < RtspClient::Status::kDesc)
    {
        AK_LOG_WARN << "Must describe before setup,MAC is " << pAppRtspClient->mac_;
        RemoveFd(pAppRtspClient->rtsp_fd_);
        HandleDisconnect(pAppRtspClient->rtsp_fd_);
        return false;
    }
    CAKLog::LogD(tag_, "begin setup, socket=%d", pAppRtspClient->rtsp_fd_);
    //modified by chenyc,2019-01-08,app的数量,根据 RtpDeviceClient::OnMessage 的逻辑,单台设备超过一定数量的app时,超过部分就不再处理.
    std::shared_ptr<RtpDeviceClient> pDevClient = RtpDeviceManager::getInstance()->AddClient(pAppRtspClient->mac_);
    if (pDevClient == nullptr)
    {
        return false;
    }
    pDevClient->SetDclientVer(pAppRtspClient->dclient_ver_);
    if (pDevClient->MonotorAppNum() > 100)
    {
        CAKLog::LogE(tag_, "there has been more than 100 app rtsp client for dev [%s]", pAppRtspClient->mac_.c_str());
        //TODO:add chenzhx:RemoveClient里面有判断是不是有app在监控，所以这边可以直接操作。
        //主要是因为AddClient返回的可能是已经存在的，而不是新申请的。所以移除要判断。
        //不然DeviceAddApp这个关系如果未建立，那么超时检测也会删除不了pDevClient。那么通过fd查找pDevClient就会异常
        RtpDeviceManager::getInstance()->RemoveClient(pDevClient->local_rtp_port_);
        return false;
    }

    std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->AddClient(pAppRtspClient->rtsp_fd_);
    if (app_client == nullptr)
    {
        RtpDeviceManager::getInstance()->RemoveClient(pDevClient->local_rtp_port_);
        return false;
    }

    pDevClient->SetAddingAppStatus();
    //add app-dev
    app_client->setDeviceClient(pDevClient);
    RtpControl::getInstance()->AddFd(app_client->rtcp_fd_, false);
    pAppRtspClient->local_rtp_port_ = app_client->local_rtp_port_;
    //赋值app需要的ssrc
    pAppRtspClient->dev_ssrc_ = app_client->dev_ssrc_;
    pAppRtspClient->app_client_ssrc_ = app_client->app_client_ssrc_;
    //将app的rtp-fd添加到eventloop监听事件丿此时,设备的rtp-fd还不需要添加进县待app执行rtsp-play,再添加也不迟
    RtpControl::getInstance()->AddFd(app_client->rtp_fd_, false); //将本地为接受app 穿越nat的udp包的fd加入udp的eventloop丿
    //建立一台设备需要给几台app发送rtp监控数据的映射关糿
    RtpDeviceManager::getInstance()->DeviceAddApp(pDevClient->local_rtp_port_, app_client->local_rtp_port_);
    CAKLog::LogT(tag_, "end setup, socket=%d", pAppRtspClient->rtsp_fd_);

    CRtspMonitor::Instance()->StartDevMonitor(pAppRtspClient->mac_, pDevClient->local_rtp_port_);
    pAppRtspClient->SetStatus(RtspClient::Status::kSetup);
    return true;
}

bool CRtspServerImpl::handlePlay(std::shared_ptr<RtspClient> pAppRtspClient)
{
    if (pAppRtspClient->GetStatus() < RtspClient::Status::kSetup)
    {
        AK_LOG_WARN << "Must setup before play,MAC is " << pAppRtspClient->mac_;
        RemoveFd(pAppRtspClient->rtsp_fd_);
        HandleDisconnect(pAppRtspClient->rtsp_fd_);
        return false;
    }

    CAKLog::LogD(tag_, "begin play, socket=%d", pAppRtspClient->rtsp_fd_);
    std::shared_ptr<RtpDeviceClient> pDevClient = RtpDeviceManager::getInstance()->GetClientByMac(pAppRtspClient->mac_);
    if (pDevClient == nullptr)
    {
        return false;
    }
    //设备在本服务器,更新缓存时间:
    //modified by chenyc,2019-01-08,先判断是否已经有app执行过play指令了,
    //避免多次执行csvrtspd->csmain,下发设备上报视频流的指令..
    if (pDevClient->state_ == DEV_STATE_PLAY)//这里错处了...
    {
        CAKLog::LogD(tag_, "there has been one app monitor dev [%s]", pAppRtspClient->mac_.c_str());
        return true;
    }
    if (pDevClient->state_ == DEV_STATE_INIT)
    {
        pDevClient->state_ = DEV_STATE_PLAY;
        //将设备的发流rtp-fd添加到eventloop监听事件中
        RtpControl::getInstance()->AddFd(pDevClient->rtp_fd_, false);
        RtpControl::getInstance()->AddFd(pDevClient->rtcp_fd_, false);

        pDevClient->dev_mac_ = pDevClient->mac_;

        CAKLog::LogD(tag_, "set_video_type:%s", pAppRtspClient->video_type_.c_str());
        CAKLog::LogD(tag_, "set_video_pt:%d", pAppRtspClient->video_pt_);

        //zhiwei.chen 向家居请求开启设备rtp传输 
        //ssrc方案暂时移除了
        CAKLog::LogD(tag_, "send start rtsp mq msg to smg,[local ip=%s,local dev port=%d,local app port =%d, mac=%s,ssrc=0X%X]", 
                     pAppRtspClient->local_ip_.c_str(), pDevClient->local_rtp_port_, pAppRtspClient->local_rtp_port_, pDevClient->mac_.c_str(), pDevClient->ssrc_);
        SmarthomeHandle::GetInstance().NotifyDevBeginRtp(pDevClient->mac_, pAppRtspClient->local_ip_, pDevClient->local_rtp_port_);
    }
    return true;
}

bool CRtspServerImpl::handleTeardown(std::shared_ptr<RtspClient>client)
{
    CAKLog::LogD(tag_, "begin tear down, socket=%d", client->rtsp_fd_);
    std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClientByRtspFd(client->rtsp_fd_);
    if (app_client == nullptr)
    {
        CAKLog::LogT(tag_, "can not find app client rtp port=%hu", client->client_rtp_port_);
        return true;
    }

    //停止监控app rtp socket
    RtpControl::getInstance()->RemoveFd(app_client->rtp_fd_);
    close(app_client->rtp_fd_);
    RtpControl::getInstance()->RemoveFd(app_client->rtcp_fd_);
    close(app_client->rtcp_fd_);

    //从dev列表中移除关联的app,并返回所有关联app的dev列表
    unsigned short dev_port_to_del = 0;
    RtpDeviceManager::getInstance()->DeviceRemoveApp(app_client->local_rtp_port_, dev_port_to_del);
    //销毁app
    RtpAppManager::getInstance()->RemoveClient(app_client->local_rtp_port_);

    if (dev_port_to_del != 0) //检查该设备是否还有app在监控,若没则删除之
    {
        std::shared_ptr<RtpDeviceClient> pDevClient = RtpDeviceManager::getInstance()->GetClientAndRemove(dev_port_to_del);
        if (pDevClient != nullptr)
        {
            RtpControl::getInstance()->RemoveFd(pDevClient->rtp_fd_);
            close(pDevClient->rtp_fd_);
            RtpControl::getInstance()->RemoveFd(pDevClient->rtcp_fd_);
            close(pDevClient->rtcp_fd_);

            pDevClient->dev_mac_ = pDevClient->mac_;

            //zhiwei.chen 向家居请求关闭设备rtp传输 
            CAKLog::LogD(tag_, "send stop rtsp mq msg to smg,[local ip=%s,local port=%d,mac=%s,dev_mac=%s]",
                         client->local_ip_.c_str(), pDevClient->local_rtp_port_, pDevClient->mac_.c_str(), pDevClient->dev_mac_.c_str());
            SmarthomeHandle::GetInstance().NotifyDevStopRtp(pDevClient->mac_);

        }
    }
    CAKLog::LogT(tag_, "app rtsp client tear down, app rtsp client socket=%d", client->rtsp_fd_);
    return true;
}
void CRtspServerImpl::AddBussiness(const std::string &bussiness, const std::string &key)
{
    AK_LOG_WARN << "rtsp client req is invalid, bussiness is " << bussiness << ", key is " << key;
    AKCS::Singleton<BussinessLimit>::instance().AddBussiness(bussiness, key);
}

void CRtspServerImpl::AttackedCallback(const std::string& bussiness, const std::string& key)
{
    AK_LOG_WARN << "there is one attack happens, iptables input drop,bussiness is " << bussiness <<", ip is " << key;
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("csvrtspd", key);
}

}

