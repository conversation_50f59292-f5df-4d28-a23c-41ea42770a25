#pragma once
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/select.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <memory>
#include <string>
#include <map>
#include <vector>
#include <sys/epoll.h>
#include "rtsp_server_interface.h"
#include "RtspClient.h"
#include "WaitEvent.h"
#include "AkcsCommonDef.h"

#define RTSP_SERVER_PORT    10554
#define RTSP_CLIENT_MAX     1000
#define RTSP_EVENT_MAX      (RTSP_CLIENT_MAX + 1)
#define RTSP_MSG_MAX        100
#define RTSP_MSG_WARN_COUNT 20

const uint32_t RTSP_CLIENT_AUTH_FAILED_TRY_NUM = 3; //RTSP客户端鉴权失败最大的可重试次数
const std::string VRTSP_AUTH_BUSSINESS = "vrtspd_auth_failed";
const std::string VRTSP_INVALID_MSG_BUSSINESS = "vrtspd_invalid_msg";
const uint32_t BUSSINESS_PERIOD = 3600;//一个小时,60 * 60s
const uint32_t BUSSINESS_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
const uint32_t BUSSINESS_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s

enum RTSP_MSG_TYPE
{
    RTSP_MSG_TYPE_CONNECT = 0,
    RTSP_MSG_TYPE_DISCONNECT,
    RTSP_MSG_TYPE_NET_ERROR,
    RTSP_MSG_TYPE_DATA,
    RTSP_MSG_TYPE_MAX
};

typedef struct _RTSP_MSG_LIST
{
    int fd;
    int msg_type;
    unsigned int data_len;
    unsigned char* data;
    struct _RTSP_MSG_LIST* next;
} RTSP_MSG_LIST;


namespace akuvox
{
class CRtspServerImpl : public IRtspServer
{
    friend void* rtsp_recv_thread(void* data);
    friend void* rtsp_process_thread(void* data);
public:
    CRtspServerImpl();
    virtual ~CRtspServerImpl();
    static CRtspServerImpl* GetInstance();

public:
    virtual int start();
    virtual int stop();
    virtual void report();

private:
    bool CreateRtspSocket();
    void ReleaseRtspSocket();

    int SetNonblocking(int fd);
    void AddFd(int fd, bool enable_et);
    void RemoveFd(int fd);
    void OnRun();
    void LevelTrigger(epoll_event* events, int number);
    int AddMsgToList(int fd, int msg_type, unsigned char* data, unsigned int data_len);

    void ThreadProcess();
    bool ProcessMsg(int fd, int msg_type, unsigned char* data, unsigned int data_len);
    bool HandleConnect(int connfd, unsigned char* pPkgBuf, unsigned int nPkgSize);
    bool HandleDisconnect(int connfd);
    bool HandleNetError(int connfd);//added by chenyc,2019-01-09
    bool handleParseRtsp(int nClientFd, char* pPkgBuf, unsigned int nPkgSize);
    bool handleOption(std::shared_ptr<RtspClient> client);
    bool handleDescribe(std::shared_ptr<RtspClient>client, const char* pFullRequestStr);
    bool handleSetup(std::shared_ptr<RtspClient> client);
    bool handlePlay(std::shared_ptr<RtspClient> pAppRtspClient);
    bool handleTeardown(std::shared_ptr<RtspClient> client);
    bool CreateRtspSocket(int port, int &listen_fd);
    void AddBussiness(const std::string &bussiness, const std::string &key);
    void AttackedCallback(const std::string& bussiness, const std::string& key);

private:
    static CRtspServerImpl* gRtspServer;
    const char* tag_;

    pthread_t m_nTidRecv;
    pthread_t m_nTidProcess;
    pthread_mutex_t m_mutex;
    bool m_bRunState;
    int m_nListenFd;
    int epoll_fd_;
    unsigned char* recv_buf_;
    void* msg_header_;
    unsigned int msg_count_;
    CWaitEvent* event_;
};

}

