#include "AKLog.h"
#include <string.h>
#include "WebrtcLog.h"


rtc::LogSink* g_webrtc_log = nullptr;

void AkWebrtcLogSink::OnLogMessage(const std::string& message, rtc::LoggingSeverity severity)
{
    switch (severity)
    {
        case rtc::INFO:
            AK_LOG_INFO << message;
            break;
        case rtc::WARNING:
            AK_LOG_WARN << message;
            break;
        case rtc::LS_ERROR:
            AK_LOG_WARN << message;
            break;
        default:
            AK_LOG_WARN << message;
            break;

    }

}


void AkWebrtcLogInit()
{
    g_webrtc_log = new AkWebrtcLogSink();
    rtc::LogMessage::AddLogToStream(g_webrtc_log, rtc::INFO);
    rtc::LogMessage::AddLogToStream(g_webrtc_log, rtc::WARNING);
    rtc::LogMessage::AddLogToStream(g_webrtc_log, rtc::LS_ERROR);
}

