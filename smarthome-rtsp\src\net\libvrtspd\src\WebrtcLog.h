#pragma once
#include <string>
#include <map>
#include <vector>
#include <memory>
#include "rtc_base/logging.h"

#include <string.h>
#include <algorithm>

#include "rtc_base/arraysize.h"
#include "rtc_base/checks.h"
#include "rtc_base/event.h"
#include "rtc_base/platform_thread.h"
#include "rtc_base/stream.h"
#include "rtc_base/time_utils.h"
#include "AkLogging.h"



class AkWebrtcLogSink: virtual public rtc::LogSink
{
public:
    virtual void OnLogMessage(const std::string& message, rtc::LoggingSeverity severity);
    virtual void OnLogMessage(const std::string& message)
    {
        AK_LOG_INFO << message;
    }
};

void AkWebrtcLogInit();


