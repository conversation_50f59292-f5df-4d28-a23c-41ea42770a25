	/**********
This library is free software; you can redistribute it and/or modify it under
the terms of the GNU Lesser General Public License as published by the
Free Software Foundation; either version 2.1 of the License, or (at your
option) any later version. (See <http://www.gnu.org/copyleft/lesser.html>.)

This library is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
FOR A PARTICULAR PURPOSE.  See the GNU Lesser General Public License for
more details.

You should have received a copy of the GNU Lesser General Public License
along with this library; if not, write to the Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
**********/
// "liveMedia"
// Copyright (c) 1996-2010 Live Networks, Inc.  All rights reserved.
// A class used for digest authentication.
// Implementation

#include "DigestAuthentication.h"
#include <strDup.hh>
#include <sys/time.h> // for gettimeofday()
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <map>
#include "AKLog.h"
#include "encrypt/Md5.h"

Authenticator::Authenticator()
{
    assign(VRTSP_REALM_TEXT, NULL, NULL, NULL, False);
}

Authenticator::Authenticator(char const* username, char const* password)
{
    setUsernameAndPassword(username, password);
}

Authenticator::Authenticator(const Authenticator& orig)
{
    assign(orig.realm(), orig.nonce(), orig.username(), orig.password(),
           orig.password_is_md5_);
}

Authenticator& Authenticator::operator=(const Authenticator& right_side)
{
    if (&right_side != this)
    {
        reset();
        assign(right_side.realm(), right_side.nonce(),
               right_side.username(), right_side.password(), right_side.password_is_md5_);
    }

    return *this;
}

Authenticator::~Authenticator()
{
    reset();
}

void Authenticator::reset()
{
    resetRealmAndNonce();
    resetUsernameAndPassword();
}

void Authenticator::setRealmAndNonce(char const* realm, char const* nonce)
{
    resetRealmAndNonce();
    assignRealmAndNonce(realm, nonce);
}

void Authenticator::setRealmAndRandomNonce(char const* realm)
{
    resetRealmAndNonce();

    // Construct data to seed the random nonce:
    struct
    {
        struct timeval timestamp;
        unsigned counter;
    } seedData;
    gettimeofday(&seedData.timestamp, NULL);
    static unsigned counter = 0;
    seedData.counter = ++counter;

    // Use MD5 to compute a 'random' nonce from this seed data:
    std::string nonce_buf = akuvox_encrypt::MD5((unsigned char*)(&seedData), sizeof(seedData)).toStr();
   
    assignRealmAndNonce(realm, nonce_buf.c_str());
}

std::string Authenticator::computeDigestResponseByNonce(char const* cmd, char const* url,
        std::string nonce_cache) const
{
    // The "response" field is computed as:
    //    md5(md5(<username>:<realm>:<password>):<nonce>:md5(<cmd>:<url>))
    // or, if "password_is_md5_" is True:
    //    md5(<password>:<nonce>:md5(<cmd>:<url>))
    std::string ha1buf;
    if (password_is_md5_)
    {
        ha1buf = std::string(password());
    }
    else
    {
        std::string ha1data = std::string(username()) + ":" + std::string(realm()) +  ":" + std::string(password());
        ha1buf = akuvox_encrypt::MD5(ha1data).toStr();
    }

    std::string ha2data = std::string(cmd) + ":" + std::string(url);
    std::string ha2buf = akuvox_encrypt::MD5(ha2data).toStr();

    std::string digest_data = ha1buf + ":" + nonce_cache + ":" + ha2buf;
    std::string result = akuvox_encrypt::MD5(digest_data).toStr();
    return result;
}

void Authenticator::setUsernameAndPassword(char const* username,
        char const* password,
        Boolean password_is_md5)
{
    resetUsernameAndPassword();
    assignUsernameAndPassword(username, password, password_is_md5);
}

std::string Authenticator::computeDigestResponse(char const* cmd,
        char const* url) const
{
    // The "response" field is computed as:
    //    md5(md5(<username>:<realm>:<password>):<nonce>:md5(<cmd>:<url>))
    // or, if "password_is_md5_" is True:
    //    md5(<password>:<nonce>:md5(<cmd>:<url>))
    std::string ha1buf;
    if (password_is_md5_)
    {
        ha1buf = std::string(password());
    }
    else
    {
        std::string ha1data = std::string(username()) + ":" + std::string(realm()) +  ":" + std::string(password());
        ha1buf = akuvox_encrypt::MD5(ha1data).toStr();
    }

    std::string ha2data = std::string(cmd) + ":" + std::string(url);
    std::string ha2buf = akuvox_encrypt::MD5(ha2data).toStr();

    std::string digest_data = ha1buf + ":" + nonce() + ":" + ha2buf;
    std::string result = akuvox_encrypt::MD5(digest_data).toStr();
    
    return result;
}

void Authenticator::resetRealmAndNonce()
{
    delete[] realm_;
    realm_ = NULL;
    delete[] nonce_;
    nonce_ = NULL;
}

void Authenticator::resetUsernameAndPassword()
{
    delete[] username_;
    username_ = NULL;
    delete[] password_;
    password_ = NULL;
    password_is_md5_ = False;
}

void Authenticator::assignRealmAndNonce(char const* realm, char const* nonce)
{
    realm_ = strDup(realm);
    nonce_ = strDup(nonce);
}

void Authenticator
::assignUsernameAndPassword(char const* username, char const* password,
                            Boolean password_is_md5)
{
    username_ = strDup(username);
    password_ = strDup(password);
    password_is_md5_ = password_is_md5;
}

void Authenticator::assign(char const* realm, char const* nonce,
                           char const* username, char const* password,
                           Boolean password_is_md5)
{
    assignRealmAndNonce(realm, nonce);
    assignUsernameAndPassword(username, password, password_is_md5);
}

/////////////////////
AuthNonceCache* AuthNonceCache::s_auth_nonce_cache_ = nullptr;
AuthNonceCache* AuthNonceCache::getInstance()
{
    if (!s_auth_nonce_cache_)
    {
        s_auth_nonce_cache_ = new AuthNonceCache();
    }

    return s_auth_nonce_cache_;
}

std::string AuthNonceCache::nonceByMac(std::string& mac)
{    

    // Use MD5 to compute a 'random' nonce from this seed data:
    std::string nonce_buf = akuvox_encrypt::MD5((unsigned char*)mac.c_str(), mac.size()).toStr();

    return nonce_buf;
}



