/**********
This library is free software; you can redistribute it and/or modify it under
the terms of the GNU Lesser General Public License as published by the
Free Software Foundation; either version 2.1 of the License, or (at your
option) any later version. (See <http://www.gnu.org/copyleft/lesser.html>.)

This library is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS
FOR A PARTICULAR PURPOSE.  See the GNU Lesser General Public License for
more details.

You should have received a copy of the GNU Lesser General Public License
along with this library; if not, write to the Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
**********/
// "liveMedia"
// Copyright (c) 1996-2010 Live Networks, Inc.  All rights reserved.
// A class used for digest authentication.
// C++ header

#ifndef _DIGEST_AUTHENTICATION_HH
#define _DIGEST_AUTHENTICATION_HH

#ifndef _BOOLEAN_HH
#include <Boolean.hh>
#endif

#include <time.h>
#include <string>
#include <map>

typedef struct NonceTimeT
{
    std::string nonce;
    time_t nonce_time;
} NonceTime;

// A class used for digest authentication.
// The "realm", and "nonce" fields are supplied by the server
// (in a "401 Unauthorized" response).
// The "username" and "password" fields are supplied by the client.

typedef std::map<std::string, NonceTime> MacNonceList; //mac-nonce容器
typedef MacNonceList::iterator MacNonceListIter;
const char VRTSP_REALM_TEXT[] = "AK VRTSPD";

class Authenticator
{
public:
    Authenticator();
    Authenticator(char const* username, char const* password);
    Authenticator(const Authenticator& orig);
    Authenticator& operator=(const Authenticator& right_side);
    virtual ~Authenticator();

    void reset();
    void setRealmAndNonce(char const* realm, char const* nonce);
    void setRealmAndRandomNonce(char const* realm);
    // as above, except that the nonce is created randomly.
    // (This is used by servers.)
    void setUsernameAndPassword(char const* username, char const* password,
                                Boolean password_is_md5 = False);
    // If "password_is_md5" is True, then "password" is actually the value computed
    // by md5(<username>:<realm>:<actual-password>)

    char const* realm() const
    {
        return realm_;
    }
    char const* nonce() const
    {
        return nonce_;
    }
    char const* username() const
    {
        return username_;
    }
    char const* password() const
    {
        return password_;
    }

    std::string computeDigestResponse(char const* cmd, char const* url) const;

    std::string computeDigestResponseByNonce(char const* cmd, char const* url, std::string nonce_cache) const;

private:
    void resetRealmAndNonce();
    void resetUsernameAndPassword();
    void assignRealmAndNonce(char const* realm, char const* nonce);
    void assignUsernameAndPassword(char const* username, char const* password,
                                   Boolean password_is_md5);
    void assign(char const* realm, char const* nonce,
                char const* username, char const* password, Boolean password_is_md5);

private:
    char* realm_;
    char* nonce_;
    char* username_;
    char* password_;
    Boolean password_is_md5_;
    MacNonceList mac_nonce_times_;
};

//redis,for rtsp nonce
class AuthNonceCache
{
public:

    std::string nonceByMac(std::string& mac);
    virtual ~AuthNonceCache()
    {
        if (s_auth_nonce_cache_)
        {
            delete s_auth_nonce_cache_;
            s_auth_nonce_cache_ = nullptr;
        }
    }
    static AuthNonceCache* getInstance();
public:
    friend class Authenticator;
private:
    AuthNonceCache(): tag_("AuthNonceCache")
    {}
private:
    static AuthNonceCache* s_auth_nonce_cache_;
    const char* tag_;

};

#endif
