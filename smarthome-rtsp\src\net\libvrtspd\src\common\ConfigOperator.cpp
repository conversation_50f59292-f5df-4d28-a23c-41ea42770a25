#include "ConfigOperator.h"
#include "AKLog.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <string.h>
#include <stdlib.h>


namespace akuvox
{
CConfigOperator::CConfigOperator(void) : tag_("ConfigOperator")
{
    findname_ = false;
}
CConfigOperator::~CConfigOperator(void)
{
}

/************************************
设置配置文件路径
************************************/
void CConfigOperator::SetFilePath(const string& filepath)
{
    filepath_ = filepath;
}

/************************************
打开配置文件
************************************/
bool CConfigOperator::OpenFile()
{
    if (fin_.is_open())
    {
        fin_.close();
    }

    fin_.open(filepath_.c_str());
    if (!fin_.is_open())
    {
        CAKLog::LogE(tag_, "can not open config file=%s", filepath_.c_str());
        return false;
    }
    return true;
}
/************************************
查找配置文件的名字
************************************/
void CConfigOperator::FindName(const string& line, const string& name)
{
    if (string::npos != line.find('['))
    {
        string sTemp = line.substr(line.find('[') + 1, line.find(']') - line.find('[') - 1);
        if (0 == strcmp(sTemp.c_str(), name.c_str()))
        {
            findname_ = true;
        }
        else
        {
            findname_ = false;
        }
    }
}
/************************************
查找配置文件的Key
************************************/
bool CConfigOperator::FindKey(const string& line, const string& key, string& value)
{
    int dele_place = line.find("//");
    int find_equal = line.find('=');
    //被注释的行，或者是包含key但是已经被注视掉了，过滤
    if ((-1 != dele_place && dele_place <  find_equal) || (-1 != dele_place && -1 == find_equal) || -1 == find_equal)
    {
        return false;
    }
    string tmp = line.substr(0, line.find('='));

    if (0 == strcmp(tmp.c_str(), key.c_str()))
    {
        value = line.substr(line.find('=') + 1, line.length() - line.find('=') - 1);
        return true;
    }

    return false;
}

/************************************
读取配置文件NEMA KEY 对应的Value信息
************************************/
bool CConfigOperator::GetConfigValue(const string& name, const string& key, string& value)
{
    if (!OpenFile())
    {
        return false;
    }
    char str[256];
    findname_ = false;
    while (NULL != fin_.getline(str, sizeof(str)))
    {
        FindName(str, name);
        if (findname_)
        {
            string tmp;
            if (FindKey(str, key, tmp))
            {
                fin_.close();
                value = tmp;
                return true;
            }
        }

    }
    fin_.close();
    return false;
}
}
