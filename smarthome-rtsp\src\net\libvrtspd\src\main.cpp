#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <string>
#include <map>
#include <thread>
#include "libvrtspd/HttpServer.h"
#include <evpp/tcp_client.h>
#include <evpp/buffer.h>
#include <evpp/tcp_conn.h>
#include "libvrtspd/rtsp_server_interface.h"
#include "glog/logging.h"
#include "libvrtspd/CsvrtspConf.h"
#include "ConfigFileReader.h"
#include <fcntl.h>
#include "libvrtspd/Control.h"
#include "libvrtspd/VrtspDefine.h"
#include <evnsq/producer.h>
#include <event_loop.h>
#include <evnsq/client.h>
#include "RtspEtcd.h"
#include "AkcsMonitor.h"
#include "SnowFlakeGid.h"
#include "WebrtcLog.h"
#include "RtspMonitor.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "SmarthomeHandle.h"


std::string g_logic_srv_id;
#define PIDFILE "/var/run/smarthome-rtsp.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char *g_ak_srv_smg;
static const char conf_file[] = "/usr/local/akcs/smarthome-rtsp/conf/smarthome-rtsp.conf";
int g_etcd_dns_res = 0;
CSVRTSP_CONF gstCSVRTSPConf; //全局配置信息

int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

bool isSingleton()
{
    int fd, val;
    char buf[10];
    if ((fd = open(PIDFILE, O_WRONLY | O_CREAT, FILE_MODE)) < 0)
    {
        return false;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return false;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return false;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return false;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return false;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return false;
    }
    return true;
}

void UpdateSmgConfFromConfSrv()
{
    std::string smg_addr;
    g_etcd_cli_mng->LoadSrvSmgConf(smg_addr);
    //reload
    SmarthomeHandle::GetInstance().SetSmgConf(smg_addr);  
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSVRTSPConf.etcd_server_addr);
    UpdateSmgConfFromConfSrv();
    //后续/akconf/smg/inner_addr有变更会回调UpdateConfFromConfSrv
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_smg, UpdateSmgConfFromConfSrv);
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

void ConfInit()
{
    CConfigFileReader config_file(conf_file);

    ::strncpy(gstCSVRTSPConf.csvrtsp_outer_ip, config_file.GetConfigName("rtsp_outerip"), sizeof(gstCSVRTSPConf.csvrtsp_outer_ip));
    ::strncpy(gstCSVRTSPConf.csvrtsp_outer_domain, config_file.GetConfigName("rtsp_outer_domain"), sizeof(gstCSVRTSPConf.csvrtsp_outer_domain));
    ::strncpy(gstCSVRTSPConf.csvrtsp_outer_ipv6, config_file.GetConfigName("rtsp_outeripv6"), sizeof(gstCSVRTSPConf.csvrtsp_outer_ipv6));

    const char* tmp = config_file.GetConfigName("keep_alive_times");
    gstCSVRTSPConf.keep_alive_times = ::atoi(tmp);

    const char* reg_etcd = config_file.GetConfigName("reg_etcd");
    gstCSVRTSPConf.reg_etcd = ::atoi(reg_etcd);

    ::strncpy(gstCSVRTSPConf.monitor_mac_list, config_file.GetConfigName("monitor_mac_list"), sizeof(gstCSVRTSPConf.monitor_mac_list));
    ::strncpy(gstCSVRTSPConf.monitor_pic_cap_rootpath, config_file.GetConfigName("monitor_pacp_path"), sizeof(gstCSVRTSPConf.monitor_pic_cap_rootpath));
    if (strlen(gstCSVRTSPConf.monitor_pic_cap_rootpath) == 0)
    {
        ::strncpy(gstCSVRTSPConf.monitor_pic_cap_rootpath, "/root", sizeof(gstCSVRTSPConf.monitor_pic_cap_rootpath));
    }

}

void* CtrlRun()
{
    GetControlInstance()->Init();
    GetControlInstance()->Run();
    return NULL;
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;    
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(gstCSVRTSPConf.etcd_server_addr, sizeof(gstCSVRTSPConf.etcd_server_addr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstCSVRTSPConf.etcd_server_addr;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    CConfigFileReader config_file(conf_file);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    ::strncpy(gstCSVRTSPConf.etcd_server_addr, config_file.GetConfigName("etcd_srv_net"), sizeof(gstCSVRTSPConf.etcd_server_addr));

    int need_res = 0;
    std::string etcd_net = gstCSVRTSPConf.etcd_server_addr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstCSVRTSPConf.etcd_server_addr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}

int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!isSingleton())
    {
        printf("another smarthome-rtsp has been running in this system.");
        return -1;
    }

    //glog初始化
    google::InitGoogleLogging("smarthome-rtsp");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/smarthome-rtsplog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/smarthome-rtsplog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/smarthome-rtsplog/ERROR");
    FLAGS_logbufsecs = 0;
    FLAGS_max_log_size = 50;    //单日志文件最大50M

	memset(&gstCSVRTSPConf, 0, sizeof(CSVRTSP_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }   
    
    ConfInit();
    
    //added by chenyc,2019-08-06,traceid实现.前期先用配置文件来区分这两个值，后面需要将traecid（uuid）获取做成一个服务
    AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setWorkerId(1);
    AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setDatacenterId(1);

    g_logic_srv_id = "smarthome-rtsp_";
    g_logic_srv_id += GetEth0IPAddr();

    akuvox::IRtspServer* rtsp_server = akuvox::CRtspServerFactory::GetInstance();
    rtsp_server->start();

    LOG(INFO) << "smarthome-rtsp start ";
    
    std::thread control(CtrlRun);

    ConfSrvInit();

    //注册服务
    std::thread etcdCliThread = std::thread(EtcdSrvInit);

    //初始化监控抓包列表
    CRtspMonitor::Instance()->InitMonitorList(gstCSVRTSPConf.monitor_mac_list);

    //起http服务线程
    std::thread httpThread(startHttpServer);
    
    //nsq消息发布
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514"); //所有的生产者都连接本机的nsqd即可,而消费者必须连接到nsqlookupd指定的主题才行
    client.ConnectToNSQDs(nsqd_tcp_addr);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);

    std::thread conf_watch_thread = std::thread(ConfWatch);

    nsq_loop.Run();
    httpThread.join();
    dnsThread.join();
    conf_watch_thread.join();
    LOG(INFO) << "smarthome-rtsp end";
    google::ShutdownGoogleLogging();
}

