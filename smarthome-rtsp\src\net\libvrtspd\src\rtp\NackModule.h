#pragma once
#include <string>
#include <map>
#include <vector>
#include <memory>
#include "RtpDeviceClient.h"

#include "modules/video_coding/nack_module.h"
#include "system_wrappers/include/clock.h"


namespace akuvox
{

class CNackModuleManager :
    public webrtc::NackSender,
    public webrtc::KeyFrameRequestSender
{
public:
    CNackModuleManager()
        : clock_(webrtc::Clock::GetRealTimeClock()),
          nack_module_(clock_, this, this),
          keyframes_requested_(0) {}

    void SendNack(const std::vector<uint16_t>& sequence_numbers) override
    {
        sent_nacks_.insert(sent_nacks_.end(), sequence_numbers.begin(),
                           sequence_numbers.end());
    }

    int OnReceivedPacket(uint16_t seq_num)
    {
        nack_module_.OnReceivedPacket(seq_num, false, false);
        return 1;
    }

    void RequestKeyFrame() override
    {
        ++keyframes_requested_;
    }
    void UpdateRtt(int64_t rtt_ms)
    {
        nack_module_.UpdateRtt(rtt_ms);
    }

    webrtc::Clock* clock_;
    webrtc::NackModule nack_module_;
    std::vector<uint16_t> sent_nacks_;
    int keyframes_requested_;
};


}
