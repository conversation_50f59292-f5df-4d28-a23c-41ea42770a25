
#include "RtcpReceiver.h"
#include <string.h>
#include "AKLog.h"
#include "VrtspDefine.h"
namespace akuvox
{
void AKModuleRtpRtcp::OnReceivedNack(const std::vector<uint16_t>& nacks)
{
    if (!rtp_device_client)
    {
        CAKLog::LogE("", "rtp_device_client uninitialized\n");
        return;
    }
    std::vector<RtpPacketInfo> oPackets;
    rtp_device_client->GetAppNackRtpPacket(nacks, oPackets);
    for (auto it : oPackets)
    {
        if (rtpfd_ && *rtpfd_ != -1)
        {
            //Sendto不用这个接口，因为这里的输写数据会有异步问题，出现刷错误日志
            sendto(*rtpfd_, it.packet_data_, it.data_len_, 0, (SA*)rtp_addr_, sizeof(*rtp_addr_));
        }
    }
}

void AKModuleRtpRtcp::OnReceivedRemb(uint32_t bitrate)
{
    if (!rtp_device_client)
    {
        CAKLog::LogE("", "rtp_device_client uninitialized\n");
        return;
    }
   
    rtp_device_client->PassthruRemb(bitrate);
}

void AKModuleRtpRtcp::OnRequestSendReport()
{

}
//接收到对端的SR/RR报告
void AKModuleRtpRtcp::OnReceivedRtcpReportBlocks(const webrtc::ReportBlockList&)
{
    if (!rtcpreceiver_ || !rtcpsender_)
    {
        CAKLog::LogE("", "rtcp_receiver_ or rtcpsender_ uninitialized\n");
        return;
    }
    // NTP from incoming SenderReport.
    uint32_t ntp_secs = 0;
    uint32_t ntp_frac = 0;
    // Local NTP time when we received a RTCP packet with a send block.
    uint32_t rtcp_arrival_time_secs = 0;
    uint32_t rtcp_arrival_time_frac = 0;
    uint32_t remote_sr = 0;

    if (!rtcpreceiver_->NTP(&ntp_secs, &ntp_frac, &rtcp_arrival_time_secs,
                            &rtcp_arrival_time_frac, NULL))
    {
        //TODO:室内机监控时候本身可能不支持
        //CAKLog::LogE("", "rtcp_receiver_->NTP error\n");
        //return;
    }
    remote_sr = ((ntp_secs & 0x0000ffff) << 16) + ((ntp_frac & 0xffff0000) >> 16);


    //https://www.cnblogs.com/lingdhox/p/5746210.html 参考计算RTT
    const uint32_t kPacketCount = 0x12345;
    const uint32_t kOctetCount = 0x23456;
    rtcpsender_->rtcp_sender_->SetRTCPStatus(webrtc::RtcpMode::kReducedSize);//更新下RTCP的发送状态,进入下一个
    webrtc::RTCPSender::FeedbackState feedback_state = rtcpsender_->rtp_rtcp_impl_->GetFeedbackState();
    rtcpsender_->rtcp_sender_->SetSendingStatus(feedback_state, true);
    feedback_state.packets_sent = kPacketCount;
    feedback_state.media_bytes_sent = kOctetCount;
    feedback_state.last_rr_ntp_frac = rtcp_arrival_time_frac;
    feedback_state.last_rr_ntp_secs = rtcp_arrival_time_secs;
    feedback_state.remote_sr = remote_sr;

    rtcpsender_->rtcp_sender_->SendRTCP(feedback_state, webrtc::RTCPPacketType::kRtcpSr);
}
}
