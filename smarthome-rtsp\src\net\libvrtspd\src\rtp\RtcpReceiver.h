#ifndef RTCP_RECEIVER_H_
#define RTCP_RECEIVER_H_
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include <memory>
#include "api/array_view.h"
#include "api/units/timestamp.h"
#include "api/video/video_bitrate_allocation.h"
#include "api/video/video_bitrate_allocator.h"
#include "modules/rtp_rtcp/include/report_block_data.h"
//#include "modules/rtp_rtcp/mocks/mock_rtcp_bandwidth_observer.h"
#include "modules/rtp_rtcp/source/byte_io.h"
#include "modules/rtp_rtcp/source/rtcp_packet.h"
#include "modules/rtp_rtcp/source/rtcp_packet/app.h"
#include "modules/rtp_rtcp/source/rtcp_packet/bye.h"
#include "modules/rtp_rtcp/source/rtcp_packet/compound_packet.h"
#include "modules/rtp_rtcp/source/rtcp_packet/extended_jitter_report.h"
#include "modules/rtp_rtcp/source/rtcp_packet/extended_reports.h"
#include "modules/rtp_rtcp/source/rtcp_packet/fir.h"
#include "modules/rtp_rtcp/source/rtcp_packet/nack.h"
#include "modules/rtp_rtcp/source/rtcp_packet/pli.h"
#include "modules/rtp_rtcp/source/rtcp_packet/rapid_resync_request.h"
#include "modules/rtp_rtcp/source/rtcp_packet/receiver_report.h"
#include "modules/rtp_rtcp/source/rtcp_packet/remb.h"
#include "modules/rtp_rtcp/source/rtcp_packet/sdes.h"
#include "modules/rtp_rtcp/source/rtcp_packet/sender_report.h"
#include "modules/rtp_rtcp/source/rtcp_packet/tmmbr.h"
#include "modules/rtp_rtcp/source/rtcp_packet/transport_feedback.h"
#include "modules/rtp_rtcp/source/rtcp_receiver.h"
#include "modules/rtp_rtcp/source/time_util.h"
#include "rtc_base/arraysize.h"
#include "rtc_base/fake_clock.h"
#include "rtc_base/random.h"
#include "system_wrappers/include/ntp_time.h"
#include "RtpDeviceClient.h"
#include "VrtspDefine.h"
#include "AKLog.h"


namespace akuvox
{

class RtpDeviceClient;
class AKModuleRtpRtcp : public webrtc::RTCPReceiver::ModuleRtpRtcp
{
public:
    AKModuleRtpRtcp(struct sockaddr_storage* rtp_addr, int* rtp_fd)
    {
        rtp_addr_ = rtp_addr;
        rtpfd_ = rtp_fd;
    }
    void SetTmmbn(std::vector<webrtc::rtcp::TmmbItem>) override {}
    void OnRequestSendReport();
    void OnReceivedNack(const std::vector<uint16_t>& nacks);    
    void OnReceivedRemb(uint32_t bitrate) override;
    void setDeviceClient(std::shared_ptr<RtpDeviceClient> rtpdev)
    {
        rtp_device_client = rtpdev;
    }
    void setRtcpSender(std::shared_ptr<AkRtcpSender> rtcpsender)
    {
        rtcpsender_ = rtcpsender;
    }
    void setRtcpReceiver(std::shared_ptr<webrtc::RTCPReceiver> rtcp_receiver)
    {
        rtcpreceiver_ = rtcp_receiver;
    }
    void OnReceivedRtcpReportBlocks(const webrtc::ReportBlockList&);
    struct sockaddr_storage* rtp_addr_;
    int* rtpfd_;
    std::shared_ptr<RtpDeviceClient> rtp_device_client;//设备的rtp客户端对象
    std::shared_ptr<AkRtcpSender> rtcpsender_;
    std::shared_ptr<webrtc::RTCPReceiver> rtcpreceiver_;
};



using webrtc::rtcp::ReceiveTimeInfo;
// SSRC of remote peer, that sends rtcp packet to the rtcp receiver under test.
constexpr uint32_t kSenderSsrc = 0x11111111;
// SSRCs of local peer, that rtcp packet addressed to.
constexpr uint32_t kReceiverMainSsrc = 0x123456;
// RtcpReceiver can accept several ssrc, e.g. regular and rtx streams.
constexpr uint32_t kReceiverExtraSsrc = 0x1234567;
// SSRCs to ignore (i.e. not configured in RtcpReceiver).
constexpr uint32_t kNotToUsSsrc = 0x654321;
constexpr uint32_t kUnknownSenderSsrc = 0x54321;

constexpr int64_t kRtcpIntervalMs = 1000;

class AKRtcpReceiver
{
public:
    AKRtcpReceiver(std::shared_ptr<AKModuleRtpRtcp> rtp_rtcp_impl)
        : system_clock_(webrtc::Clock::GetRealTimeClock())
    {
        rtcp_receiver_.reset(new webrtc::RTCPReceiver(system_clock_,
                             false, nullptr, nullptr, nullptr, nullptr, nullptr, nullptr,
                             kRtcpIntervalMs,
                             rtp_rtcp_impl.get()));
        rtp_rtcp_impl_ = rtp_rtcp_impl;
        rtp_rtcp_impl_->setRtcpReceiver(rtcp_receiver_);
    }

    void SetRemoteSsrc(uint32_t remote_ssrc)
    {
        rtcp_receiver_->SetRemoteSSRC(remote_ssrc);

    }

    void SetSsrc(uint32_t main_ssrc, uint32_t reg_ssrc)
    {
        std::set<uint32_t> ssrcs = {main_ssrc, reg_ssrc};
        rtcp_receiver_->SetSsrcs(main_ssrc, ssrcs);
    }

    /*
      void InjectRtcpPacket(rtc::ArrayView<const uint8_t> raw) {
        rtcp_receiver_.IncomingPacket(raw.data(), raw.size());
      }

      void InjectRtcpPacket(const webrtc::rtcp::RtcpPacket& packet) {
        rtc::Buffer raw = packet.Build();
        rtcp_receiver_.IncomingPacket(raw.data(), raw.size());
      }
    */
    //webrtc::SimulatedClock system_clock_;
    webrtc::Clock* system_clock_;
    // Callbacks to packet_type_counter_observer are frequent but most of the time
    // are not interesting.
    //NiceMock<MockRtcpPacketTypeCounterObserver> packet_type_counter_observer_;
    //StrictMock<MockRtcpBandwidthObserver> bandwidth_observer_;
    //StrictMock<MockRtcpIntraFrameObserver> intra_frame_observer_;
    //StrictMock<MockRtcpLossNotificationObserver> rtcp_loss_notification_observer_;
    //StrictMock<MockTransportFeedbackObserver> transport_feedback_observer_;
    //StrictMock<MockVideoBitrateAllocationObserver> bitrate_allocation_observer_;
    std::shared_ptr<AKModuleRtpRtcp> rtp_rtcp_impl_;

    std::shared_ptr<webrtc::RTCPReceiver> rtcp_receiver_;

};

}

#endif
