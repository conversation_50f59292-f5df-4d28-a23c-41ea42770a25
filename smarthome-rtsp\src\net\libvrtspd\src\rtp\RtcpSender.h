#pragma once
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include "AKLog.h"


#include "modules/rtp_rtcp/include/rtp_rtcp_defines.h"
#include "modules/rtp_rtcp/source/rtcp_packet/bye.h"
#include "modules/rtp_rtcp/source/rtcp_packet/common_header.h"
#include "modules/rtp_rtcp/source/rtcp_sender.h"
#include "modules/rtp_rtcp/source/rtp_packet_received.h"
#include "modules/rtp_rtcp/source/rtp_rtcp_impl.h"
#include "modules/rtp_rtcp/source/time_util.h"
#include "rtc_base/rate_limiter.h"


namespace akuvox
{
class AKRtcpTransport : public webrtc::Transport
{
public:
    AKRtcpTransport(struct sockaddr_storage* paddr, int* rtcpfd)
    {
        addr_ = paddr;
        rtcpfd_ = rtcpfd;
    }

    bool SendRtp(const uint8_t* /*data*/,
                 size_t /*len*/,
                 const webrtc::PacketOptions& options) override
    {
        return false;
    }
    bool SendRtcp(const uint8_t* data, size_t len);
    struct sockaddr_storage* addr_;
    int* rtcpfd_;
};

namespace RtcpSender
{
static const uint32_t kSenderSsrc = 0x11111111;
static const uint32_t kRemoteSsrc = 0x22222222;
static const uint32_t kStartRtpTimestamp = 0x34567;
static const uint32_t kRtpTimestamp = 0x45678;
}  // namespace

class AkRtcpSender
{
public:
    AkRtcpSender(AKRtcpTransport* ts)
        : clock_(webrtc::Clock::GetRealTimeClock()),
          receive_statistics_(webrtc::ReceiveStatistics::Create(clock_)),
          retransmission_rate_limiter_(clock_, 1000)
    {
        rtcp_transport_ = ts;
        webrtc::RtpRtcp::Configuration configuration;
        configuration.audio = false;
        configuration.clock = clock_;
        configuration.outgoing_transport = rtcp_transport_;
        configuration.retransmission_rate_limiter = &retransmission_rate_limiter_;
        configuration.rtcp_report_interval_ms = 1000;

        rtp_rtcp_impl_.reset(new webrtc::ModuleRtpRtcpImpl(configuration));
        rtcp_sender_.reset(new webrtc::RTCPSender(false, clock_, receive_statistics_.get(),
                           nullptr, nullptr, rtcp_transport_,
                           configuration.rtcp_report_interval_ms));
        rtcp_sender_->SetSSRC(RtcpSender::kSenderSsrc);
        rtcp_sender_->SetRemoteSSRC(RtcpSender::kRemoteSsrc);
        rtcp_sender_->SetTimestampOffset(RtcpSender::kStartRtpTimestamp);
        rtcp_sender_->SetLastRtpTime(RtcpSender::kRtpTimestamp, clock_->TimeInMilliseconds(),
                                     /*payload_type=*/0);
    }

    void InsertIncomingPacket(uint32_t remote_ssrc, uint16_t seq_num)
    {
        webrtc::RtpPacketReceived packet;
        packet.SetSsrc(remote_ssrc);
        packet.SetSequenceNumber(seq_num);
        packet.SetTimestamp(123456);
        packet.SetPayloadSize(100 - 12);
        receive_statistics_->OnRtpPacket(packet);
    }

    void SetSSRC(uint32_t Ssrc)
    {
        rtcp_sender_->SetSSRC(Ssrc);
    }
    void SetRemoteSSRC(uint32_t Ssrc)
    {
        rtcp_sender_->SetRemoteSSRC(Ssrc);
    }

    //test::RtcpPacketParser* parser() { return &rtcp_transport_.parser_; }

    webrtc::RTCPSender::FeedbackState feedback_state()
    {
        return rtp_rtcp_impl_->GetFeedbackState();
    }

    //webrtc::SimulatedClock clock_;
    webrtc::Clock* clock_;
    AKRtcpTransport* rtcp_transport_;
    std::unique_ptr<webrtc::ReceiveStatistics> receive_statistics_;
    std::unique_ptr<webrtc::ModuleRtpRtcpImpl> rtp_rtcp_impl_;
    std::unique_ptr<webrtc::RTCPSender> rtcp_sender_;
    webrtc::RateLimiter retransmission_rate_limiter_;
};

}
