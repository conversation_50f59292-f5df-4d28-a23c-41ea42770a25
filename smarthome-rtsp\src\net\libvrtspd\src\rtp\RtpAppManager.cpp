#include "RtpAppManager.h"
#include "AKLog.h"

namespace akuvox
{
RtpAppManager::RtpAppManager() : tag_("RtpAppManager")
{
    cur_local_port_ = LOCAL_APP_RTP_PORT_BASE;
}

RtpAppManager::~RtpAppManager()
{
    ClearClient();
}

RtpAppManager* RtpAppManager::instance = nullptr;

RtpAppManager* RtpAppManager::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtpAppManager;
    }
    return instance;
}

std::shared_ptr<RtpAppClient> RtpAppManager::AddClient(int rtsp_fd)
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    for (auto iter : app_rtp_clients)
    {
        //根据协商端口查找
        if (iter.second->rtsp_fd_ == rtsp_fd)
        {
            CAKLog::LogE(tag_, "app rtspfd=%d has exist.", rtsp_fd);
            return iter.second;
        }
    }

    std::shared_ptr<RtpAppClient> client = nullptr;
    unsigned short local_port = 0;
    //生成本地端口
    int try_times = 3;
    while (try_times--)
    {
        local_port = GenLocalPort();
        if (local_port > 0)
        {
            client = std::make_shared<RtpAppClient>(local_port, rtsp_fd);
            bool ret = client->CreateRtpSocket();//setup完之后,开始创建接受设备的 udp包的地址
            if (!ret)
            {
                CAKLog::LogW(tag_, "create device socket error, try again!");
                local_port = 0;
                client = nullptr;
                continue;
            }
            else
            {
                break;
            }
        }
    }

    if (local_port)
    {
        app_rtp_clients.insert(std::pair<unsigned short, std::shared_ptr<RtpAppClient>>(local_port, client));
    }
    else
    {
        CAKLog::LogE(tag_, "gen local rtp port for app rtp packet fail");
    }
    return client;
}

void RtpAppManager::RemoveClient(unsigned short local_port)
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    std::map<unsigned short, std::shared_ptr<RtpAppClient>>::iterator iter;
    iter = app_rtp_clients.find(local_port);
    if (iter != app_rtp_clients.end())
    {
        app_rtp_clients.erase(local_port);
    }
}

void RtpAppManager::ClearClient()
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    app_rtp_clients.clear();
}
std::shared_ptr<RtpAppClient> RtpAppManager::GetClientByRtspFd(int rtsp_fd)
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    for (auto client : app_rtp_clients)
    {
        if (client.second->rtsp_fd_ == rtsp_fd)
        {
            return client.second;
        }
    }
    return nullptr;
}

//TODO：查找时候和rtp重复了一遍，可以和在一起,合在一起要注意资源问题
std::shared_ptr<RtpAppClient> RtpAppManager::GetClientByRtcpSocket(int socket)
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    for (auto client : app_rtp_clients)
    {
        if (client.second->rtcp_fd_ == socket)
        {
            return client.second;
        }
    }
    return nullptr;
}

std::shared_ptr<RtpAppClient> RtpAppManager::GetClientBySocket(int socket)
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    for (auto client : app_rtp_clients)
    {
        if (client.second->rtp_fd_ == socket)
        {
            return client.second;
        }
    }
    return nullptr;
}

std::shared_ptr<RtpAppClient> RtpAppManager::GetClient(unsigned short local_port)
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    auto iter = app_rtp_clients.find(local_port);
    if (iter != app_rtp_clients.end())
    {
        return iter->second;
    }
    return nullptr;
}

unsigned short RtpAppManager::GenLocalPort()
{
    unsigned short i = (cur_local_port_ + 2) % (LOCAL_APP_RTP_PORT_MAX - LOCAL_APP_RTP_PORT_BASE) + LOCAL_APP_RTP_PORT_BASE;
    for (; i < LOCAL_APP_RTP_PORT_MAX;)
    {
        auto iter = app_rtp_clients.find(i);
        if (iter == app_rtp_clients.end())
        {
            cur_local_port_ = i;
            return i;
        }
        i += 2;
    }

    return 0;
}

void RtpAppManager::ReportAll()
{
    std::lock_guard<std::mutex> lock(rtp_app_mutex_);
    CAKLog::LogD(tag_, "RTP app count=%lu", app_rtp_clients.size());
    std::map<unsigned short, std::shared_ptr<RtpAppClient>>::iterator iter;
    for (iter = app_rtp_clients.begin(); iter != app_rtp_clients.end(); ++iter)
    {
        CAKLog::LogD(tag_, "%s", iter->second->toString().c_str());
    }
}
}

