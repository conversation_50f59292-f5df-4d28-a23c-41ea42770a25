#pragma once
#include <string>
#include <map>
#include <memory>
#include "RtpAppClient.h"

#define LOCAL_APP_RTP_PORT_BASE 59000
#define LOCAL_APP_RTP_PORT_MAX  60000

namespace akuvox
{
class RtpAppManager
{
public:
    ~RtpAppManager();
    static RtpAppManager* getInstance();

    std::shared_ptr<RtpAppClient> AddClient(int rtsp_fd);
    void RemoveClient(unsigned short local_port);
    void ClearClient();
    //根据协商端口找到app client
    std::shared_ptr<RtpAppClient> GetClientByRtspFd(int rtsp_fd);
    std::shared_ptr<RtpAppClient> GetClientBySocket(int socket);
    std::shared_ptr<RtpAppClient> GetClientByRtcpSocket(int socket);
    std::shared_ptr<RtpAppClient> GetClient(unsigned short local_port);  //根据app的rtp端口来查找app的rtp客户端
    void ReportAll();

private:
    RtpAppManager();
    unsigned short GenLocalPort(); //获取服务器当前未分配出去的端口

private:
    static RtpAppManager* instance;
    const char* tag_;

    std::map<unsigned short/*app rtp port*/, std::shared_ptr<RtpAppClient>> app_rtp_clients;//key是服务器分配的app的端口，即local rtp port
    std::mutex rtp_app_mutex_;
    unsigned short cur_local_port_;
};
}
