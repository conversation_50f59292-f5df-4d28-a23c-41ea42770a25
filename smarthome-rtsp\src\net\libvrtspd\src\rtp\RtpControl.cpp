#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "AKLog.h"
//#include "ClientManager.h"
#include "RtpAppManager.h"
#include "RtpDeviceManager.h"
#include "RtpAppClient.h"
#include "RtpControl.h"
#include "modules/video_coding/nack_module.h"
#include "ByteIO.h"
namespace akuvox
{
#define RTP_EVENT_MAX   2000
#define RTP_BUFFER_SIZE 4096


RtpControl::RtpControl() : tag_("RtpControl")
{
    epoll_fd_ = -1;
    working_ = false;
    recv_buf_ = new unsigned char[RTP_BUFFER_SIZE];
    event_ = new CWaitEvent();
    thread_count_ = 3;
}

RtpControl::~RtpControl()
{
    CAKLog::LogI(tag_, "~RtpControl()");
    if (recv_buf_ != nullptr)
    {
        delete[] recv_buf_;
        recv_buf_ = nullptr;
    }

    if (event_ != nullptr)
    {
        delete (CWaitEvent*)event_;
        event_ = nullptr;
    }
}

RtpControl* RtpControl::instance = nullptr;

RtpControl* RtpControl::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtpControl;
    }
    return instance;
}

void RtpControl::ReleaseInstance()
{
    if (nullptr != instance)
    {
        delete instance;
        instance = nullptr;
    }
}

bool RtpControl::Start()
{
    /*business线程*/
    working_ = true;
	//added by chenyc,2020-11-06,当前代码下,只能单线程运行.
    epoll_ = std::thread(&RtpControl::EpollThread, this, this);
    for (int i = 0; i < thread_count_; i++)
    {
        //std::thread tProcess();
        threads_.push_back(std::thread(&RtpControl::ProcessThread, this, this));
        //tProcess.detach();
    }

    return true;
}

void RtpControl::Stop()
{
    working_ = false;
    for (size_t i = 0; i < threads_.size(); ++i)
    {
        threads_[i].join();
    }

    epoll_.join();
}


int RtpControl::EpollThread(void* arg)
{
    CAKLog::LogI(tag_, "%s, start", __FUNCTION__);

    epoll_fd_ = epoll_create(RTP_EVENT_MAX);
    if (-1 == epoll_fd_)
    {
        CAKLog::LogE(tag_, "RTP create epoll error=%s errno=%d", strerror(errno), errno);
        return -1;
    }

    //
    epoll_event events[RTP_EVENT_MAX] = {0};
    while (working_)
    {
        int ret = epoll_wait(epoll_fd_, events, RTP_EVENT_MAX, -1);
        if (ret < 0)
        {
            CAKLog::LogE(tag_, "RTP epoll failure, errno: %d", errno);
            continue;
        }

        lt(events, ret);
    }

    if (epoll_fd_ > 0)
    {
        close(epoll_fd_);
        epoll_fd_ = -1;
    }
    CAKLog::LogI(tag_, "%s end", __FUNCTION__);
    return 0;
}

int RtpControl::SetNonblocking(int fd)
{
    int old_option = fcntl(fd, F_GETFL);
    int new_option = old_option | O_NONBLOCK;
    fcntl(fd, F_SETFL, new_option);
    return old_option;
}

void RtpControl::AddFd(int fd, bool enable_et)
{
    epoll_event event = { 0 };
    event.data.fd = fd;
    event.events = EPOLLIN;
    if (enable_et)
    {
        event.events |= EPOLLET;
    }
    int ret = epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, fd, &event);
    if (ret != 0)
    {
        CAKLog::LogW(tag_, "RTP add monitor fd=%d", fd);
    }     
    SetNonblocking(fd);
    CAKLog::LogD(tag_, "RTP add monitor fd=%d", fd);
}
void RtpControl::RemoveFd(int fd)
{
    epoll_event event = {0};
    event.data.fd = fd;
    int ret = epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, fd, &event);
    if (ret != 0)
    {
        CAKLog::LogW(tag_, "RTP del monitor fd=%d", fd);
    }      
    CAKLog::LogD(tag_, "RTP del monitor fd=%d", fd);
}

void RtpControl::lt(epoll_event* events, int number)
{
    for (int i = 0; i < number; i++)
    {
        int sockfd = events[i].data.fd;
        if (events[i].events & EPOLLIN)
        {
            //struct sockaddr_in raddr;
            //int val = sizeof(struct sockaddr);
            struct sockaddr_storage raddr;
            socklen_t val = sizeof(raddr);
            memset(recv_buf_, 0, RTP_BUFFER_SIZE);
            //ipv6
            int ret = recvfrom(sockfd, recv_buf_, RTP_BUFFER_SIZE, 0, (SA*)&raddr, &val);
            if (ret <= 0)
            {
                CAKLog::LogW(tag_, "RTP recv err %d", ret);
                //close(sockfd);
                continue;
            }
            //CAKLog::LogD(tag_,"recv udp msg from %s\n", Sock_ntop((struct sockaddr *)&raddr, val));
            //先根据udp-fd确定是设备发送过来的还是app发送过来的
            //APP发过来的几个UDP包
            std::shared_ptr<RtpAppClient>  rtp_app = RtpAppManager::getInstance()->GetClientBySocket(sockfd);
            if (rtp_app != NULL)
            {
                if (!rtp_app->hasnat_) //是否已经完成app的udp-nat工作
                {
                    ProcessApp(rtp_app, raddr, recv_buf_, ret);
                }
                continue;
            }

            //App rtcp nat
            std::shared_ptr<RtpAppClient>  rtcp_app = RtpAppManager::getInstance()->GetClientByRtcpSocket(sockfd);
            if (rtcp_app != NULL)
            {
                if (!rtcp_app->has_rctp_nat_) //是否已经完成app的udp-nat工作
                {
                    memcpy(&rtcp_app->app_rtcp_addr_, &raddr, sizeof(struct sockaddr_storage));
                    //rtcp_app->has_rctp_nat_ = true; onRtcpMessage里面会再次判断
                    rtcp_app->onRtcpMessage(recv_buf_, ret);
                }
                else
                {
                    rtcp_app->onRtcpMessage(recv_buf_, ret);
                }
                continue;
            }

            //dev rtcp nat
            std::shared_ptr<RtpDeviceClient> rtcp_device = RtpDeviceManager::getInstance()->GetClientByRtcpSocket(sockfd);
            if (rtcp_device != NULL)
            {
                if (!rtcp_device->has_rctp_nat_)
                {
                    //目前设备发过来的rtcp没有用，只是nat用
                    memcpy(&rtcp_device->dev_rtcp_addr, &raddr, sizeof(struct sockaddr_storage));
                    //rtcp_device->has_rctp_nat_ = true; //AddMsg里面会再次判断
                    //CAKLog::LogE(tag_, "====DEV NAT\n");
                }
                //continue;
            }

            //CAKLog::LogT(tag_, "get %d bytes from socketid=%d,ip=%s,port=%d",
            //  ret,
            //  sockfd,
            //  inet_ntoa(raddr.sin_addr),
            //  ntohs(raddr.sin_port));

            //设备发过来的rtp监控包
            RtpDeviceManager::getInstance()->AddMsg(sockfd, raddr, recv_buf_, ret);;//ret==udp包长度
            event_->Set();
        }
        else
        {
            CAKLog::LogE(tag_, "something else happened");
        }
    }
}

int RtpControl::ProcessApp(std::shared_ptr<RtpAppClient> client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len)
{
    memcpy(&client->app_addr_, &addr, sizeof(struct sockaddr_storage));
    client->hasnat_ = true;
    CAKLog::LogI(tag_, "recv app nat client ip:port=%s, local udp port is %d", Sock_ntop((SA*)&client->app_addr_, sizeof(client->app_addr_)), client->local_rtp_port_);
    return 0;
}

int RtpControl::ProcessRtcpApp(std::shared_ptr<RtpAppClient> client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len)
{
    memcpy(&client->app_rtcp_addr_, &addr, sizeof(struct sockaddr_storage));
    client->has_rctp_nat_ = true;
    CAKLog::LogI(tag_, "recv app rtcp nat client ip:port=%s", Sock_ntop((SA*)&client->app_rtcp_addr_, sizeof(client->app_rtcp_addr_)));

    return 0;
}

int RtpControl::ProcessThread(void* arg)
{
    CAKLog::LogI(tag_, "ProcessThread start tid=%u", std::this_thread::get_id());
    while (working_)
    {
        std::vector<std::shared_ptr<RtpDeviceClient>> rtp_dev_clients;
        RtpDeviceManager::getInstance()->GetHasMsgClient(rtp_dev_clients);
        while (rtp_dev_clients.size() == 0)
        {
            event_->Wait();
            RtpDeviceManager::getInstance()->GetHasMsgClient(rtp_dev_clients);
        }
        for (const auto& dev_client : rtp_dev_clients)
        {
            dev_client->ProcessMsg();
        }
    }
    CAKLog::LogI(tag_, "ProcessThread end tid=%u", std::this_thread::get_id());
    return 0;
}




}
