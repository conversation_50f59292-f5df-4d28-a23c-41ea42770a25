#pragma once
#include <sys/epoll.h>
#include <string>
#include <map>
#include <vector>
#include <thread>
#include <memory>
//#include <mutex>
#include "RtpDeviceClient.h"
#include "RtpAppClient.h"
#include "WaitEvent.h"
#include "Unp.h"

//class RtpAppClient;
namespace akuvox
{
class RtpControl
{
public:
    ~RtpControl();
    static RtpControl* getInstance();
    static void ReleaseInstance();

    bool Start();
    void Stop();
    void AddFd(int fd, bool enable_et);
    void RemoveFd(int fd);
private:
    RtpControl();
    void OnRun();
    int EpollThread(void* arg);
    int SetNonblocking(int fd);
    void lt(epoll_event* events, int number);

    //int AddMsgToList(int fd, unsigned char* data, unsigned int data_len);
    int ProcessThread(void* arg);
    //      int ProcessMsg(int fd, struct sockaddr_in addr, unsigned char* data, unsigned int data_len);
    //      int ProcessDevice(RtpDeviceClient* client, unsigned char* data, unsigned int data_len);
    int ProcessApp(std::shared_ptr<RtpAppClient> client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len);
    int ProcessRtcpApp(std::shared_ptr<RtpAppClient> client, struct sockaddr_storage addr, unsigned char* data, unsigned int data_len);


private:
    static RtpControl* instance;
    const char* tag_;

    bool working_;
    int epoll_fd_;
    std::thread epoll_;
    unsigned char* recv_buf_;
    //      std::mutex m_lock;
    CWaitEvent* event_;
    //      void *msg_header_;
    //      unsigned int msg_count_;
    int thread_count_;
    std::vector<std::thread> threads_;
};
}
