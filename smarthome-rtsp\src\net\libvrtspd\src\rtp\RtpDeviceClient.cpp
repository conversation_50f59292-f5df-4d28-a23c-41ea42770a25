#include <unistd.h>
#include <netinet/in.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <string.h>
#include "RtpDeviceClient.h"
#include "RtpDeviceManager.h"
#include "RtpAppManager.h"
#include "AKLog.h"
#include "string.h"
#include "AkLogging.h"
#include "ByteIO.h"
#include "Unp.h"
#include "NackModule.h"
#include "ByteIO.h"
#include "modules/rtp_rtcp/include/rtp_header_parser.h"


namespace akuvox
{
RtpDeviceClient::RtpDeviceClient(unsigned short local_rtp_port, std::string& mac) : tag_("RtpDevClient")
{
    local_rtp_port_ = local_rtp_port;
    local_rtcp_port_ = local_rtp_port + 1;
    mac_ = mac;
    rtp_fd_ = -1;
    state_ = DEV_STATE_NONE;
    msg_header_ = nullptr;
    msg_count_ = 0;
    processing_ = false;
    capture_ = false;
    adding_app_ = false;
    ssrc_ = 0;
    first_true_rtp_ssrc_ = 0;
    is_has_checked_ssrc_ = false;
    srand((unsigned int)time(0));
    local_ssrc_ = rand();

    rtcp_fd_ = -1;
    has_rctp_nat_ = false;
    has_rtp_nat = false;
    first_dev_rtp_arrive = false;
    dev_rtp_ssrc_ = 0;
    memset(&dev_rtp_addr, 0, sizeof(dev_rtp_addr));
    memset(&dev_rtcp_addr, 0, sizeof(dev_rtcp_addr));
    v_rtp_packets_.resize(RTP_PACKET_MAX_BUFFER_SIZE);
    rtp_packets_index_ = 0;
    rtp_delay_packets_index_ = 0;
    rtcp_module.reset(new AKModuleRtpRtcp(&dev_rtp_addr, &rtp_fd_));
    rtcp_receiver.reset(new AKRtcpReceiver(rtcp_module));
    rtcp_transport.reset(new AKRtcpTransport(&dev_rtcp_addr, &rtcp_fd_)); //发送nack
    rtcp_sender.reset(new AkRtcpSender(rtcp_transport.get()));
    rtcp_module->setRtcpSender(rtcp_sender);
}

RtpDeviceClient::~RtpDeviceClient()
{
    CAKLog::LogT(tag_, "~RtpDeviceClient()");
    if (inner_vrtsp_rtp_fd_ > 0)
    {
        ::close(inner_vrtsp_rtp_fd_);
        inner_vrtsp_rtp_fd_ = 0;
    }
    m_lock.lock();
    processing_ = true;
    RTP_MSG_LIST* tmp_node = nullptr;
    while (msg_header_ != nullptr)
    {
        tmp_node = (RTP_MSG_LIST*)msg_header_;
        msg_header_ = ((RTP_MSG_LIST*)msg_header_)->next;
        msg_count_--;
        if (tmp_node->data != nullptr)
        {
            delete[] tmp_node->data;
        }
        delete tmp_node;
    }
    processing_ = false;
    m_lock.unlock();
}

std::string RtpDeviceClient::toString()
{
    char infos[256] = { 0 };
    snprintf(infos, sizeof(infos), "RtpDevClient[rtp fd=%d,local port=%hu,mac=%s,app count=%lu capture = %d]",
             rtp_fd_, local_rtp_port_, mac_.c_str(), set_apps_.size(), capture_);
    std::string info = infos;
    return info;
}

bool RtpDeviceClient::CreateRtpSocket()
{
    //modified by chenyc,2019-01-08,当并发时,会对同一台设备多次创建socket,句柄泄露.改m_nState为原子变量
    if (state_ > DEV_STATE_NONE) //平台接受设备rtp packet的网络socket已经准备好了
    {
        return true;
    }

    struct sockaddr_in6 address;
    bzero(&address, sizeof(address));
    address.sin6_family = AF_INET6;
    address.sin6_port = htons(local_rtp_port_);
    address.sin6_addr = in6addr_any;
    rtp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (-1 == rtp_fd_)
    {
        CAKLog::LogE(tag_, "create dev rtp packet socket error=%s errno=%d", strerror(errno), errno);
        return false;
    }

    CAKLog::LogI(tag_, "create dev rtp packet socket=%d port is %d", rtp_fd_, local_rtp_port_);

    int on = 1;
    int ret = setsockopt(rtp_fd_, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));
    if (-1 == ret)
    {
        CAKLog::LogE(tag_, "set reuseaddr socket error=%s errno=%d", strerror(errno), errno);
        close(rtp_fd_);
        return false;
    }
    ret = bind(rtp_fd_, (struct sockaddr*)&address, sizeof(address));
    if (-1 == ret)
    {
        CAKLog::LogE(tag_, "bind socket error=%s errno=%d", strerror(errno), errno);
        close(rtp_fd_);
        return false;
    }

    //create rtcp
    struct sockaddr_in6 address_rtcp;
    bzero(&address_rtcp, sizeof(address_rtcp));
    address_rtcp.sin6_family = AF_INET6;
    address_rtcp.sin6_port = htons(local_rtcp_port_);
    address_rtcp.sin6_addr = in6addr_any;
    rtcp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (-1 == rtcp_fd_)
    {
        CAKLog::LogE(tag_, "create rtcp socket error=%s errno=%d", strerror(errno), errno);
        close(rtp_fd_);
        return false;
    }

    CAKLog::LogI(tag_, "create dev rtcp socket=%d port is %d", rtcp_fd_, local_rtcp_port_);
    on = 1;
    ret = setsockopt(rtcp_fd_, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));
    if (-1 == ret)
    {
        CAKLog::LogE(tag_, "set reuseaddr rtcp socket error=%s errno=%d", strerror(errno), errno);
        close(rtcp_fd_);
        close(rtp_fd_);
        return false;
    }

    ret = bind(rtcp_fd_, (struct sockaddr*)&address_rtcp, sizeof(address_rtcp));
    if (-1 == ret)
    {
        CAKLog::LogE(tag_, "bind rtcp socket error=%s errno=%d", strerror(errno), errno);
        close(rtcp_fd_);
        close(rtp_fd_);
        return false;
    }

    inner_vrtsp_rtp_fd_ = socket(AF_INET6, SOCK_DGRAM, 0);
    if (inner_vrtsp_rtp_fd_ == -1)
    {
        AK_LOG_WARN << "socket error, failed to create inner vrtspd rtp fd, errno is " << errno;
        return false;
    }

    state_ = DEV_STATE_INIT;
    return true;
}

void RtpDeviceClient::AddMsg(unsigned char* data, unsigned int data_len)
{
    RTP_MSG_LIST* cur_node = nullptr;
    RTP_MSG_LIST* new_node = new RTP_MSG_LIST();
    if (nullptr == new_node)
    {
        return;
    }

    memset(new_node, 0, sizeof(RTP_MSG_LIST));
    new_node->fd = rtp_fd_;
    new_node->data_len = data_len;
    new_node->data = new unsigned char[data_len + 1];
    new_node->next = nullptr;
    memset(new_node->data, 0, data_len + 1);
    memcpy(new_node->data, data, data_len);

    m_lock.lock();
    if (msg_header_ == nullptr)
    {
        msg_header_ = new_node;
        msg_count_ = 1;
    }
    else
    {
        if (msg_count_ >= RTP_MSG_MAX)
        {
            CAKLog::LogI(tag_, "rtp socket=%d recv msg full.delete old pkg", rtp_fd_);
            RTP_MSG_LIST* tmp_node2 = (RTP_MSG_LIST*)msg_header_;
            msg_header_ = ((RTP_MSG_LIST*)msg_header_)->next;
            if (tmp_node2->data != NULL)
            {
                delete[] tmp_node2->data;
            }
            delete tmp_node2;
            msg_count_--;
        }

        cur_node = (RTP_MSG_LIST*)msg_header_;
        while ((cur_node != nullptr) && (cur_node->next != nullptr))
        {
            cur_node = cur_node->next;
        }
        cur_node->next = new_node;
        msg_count_++;
        if (msg_count_ > RTP_MSG_WARN_COUNT)
        {
            CAKLog::LogW(tag_, "rtp socket=%d msg count already reach %d", rtp_fd_, msg_count_);
        }
    }

    m_lock.unlock();
}

void RtpDeviceClient::ProcessMsg()
{
    m_lock.lock();
    processing_ = true;
    RTP_MSG_LIST* tmp_node = nullptr;
    while (msg_header_ != nullptr)
    {
        tmp_node = (RTP_MSG_LIST*)msg_header_;
        msg_header_ = ((RTP_MSG_LIST*)msg_header_)->next;
        msg_count_--;
        m_lock.unlock();
        if (!OnMessage(tmp_node->data, tmp_node->data_len))
        {
            CAKLog::LogE(tag_, "%s ProcessMsg failed", __FUNCTION__);
        }
        m_lock.lock();
        if (tmp_node->data != nullptr)
        {
            delete[] tmp_node->data;
        }
        delete tmp_node;
    }
    processing_ = false;
    m_lock.unlock();
}

bool RtpDeviceClient::HasMessage()
{
    bool has_msg = false;
    m_lock.lock();
    has_msg = ((msg_header_ != nullptr ? true : false) && !processing_);
    m_lock.unlock();
    return has_msg;
}
//added by chenyc,2020-11-06,当前的代码结构只能单线程:
bool RtpDeviceClient::OnMessage(unsigned char* data, unsigned int data_len)
{
    if (data == nullptr)
    {
        return false;
    }
    //added by chenyc,20201118,1500适配rtp的长度,当前RtpPacketInfo结构体成员packet_data_的长度也是1500
	if (data_len >= 1500)
    {
		LOG_EVERY_N(WARNING, 20) << "get " << google::COUNTER << "times log, rtp data length > 1500. length is " << data_len;
		return false;
    }

    if ((set_apps_.size() == 0) && (vrtsp_logic_id_addrs_.size() == 0))
    {
        //added by chenyc,2019-12-06,每100次日志,记录一次即可,实际次数是COUNTER次
        LOG_EVERY_N(WARNING, 100) << "get " << google::COUNTER << "times log, there are not app rtsp client and inner rtspd client for dev rtp packet. mac is " << mac_;
        return true;
    }


    //TODO:如果是集群内部转流。因为是阿里云局域网发送rtp包，先不处理丢包的情况。
    const uint8_t* ptr = &data[2];
    uint16_t seq_num = akcs::ByteReader<uint16_t>::ReadBigEndian(ptr);
    //add nack judge list
    OnReceivedPacket(seq_num);

    m_buffer_lock_.lock();
    rtp_packets_index_++;
    if (rtp_packets_index_ >= RTP_PACKET_MAX_BUFFER_SIZE)
    {
        rtp_packets_index_ = 0;
    }

    v_rtp_packets_[rtp_packets_index_].seq_ = seq_num;
    v_rtp_packets_[rtp_packets_index_].data_len_ = data_len;
    memcpy(&v_rtp_packets_[rtp_packets_index_].packet_data_, data, data_len);

    RtpPacketInfo info;
    rtp_delay_packets_.push(v_rtp_packets_[rtp_packets_index_]);
    if (rtp_delay_packets_index_ > RTP_PACKET_DELAY_BUFFER_SIZE)
    {
        info = rtp_delay_packets_.top();
        rtp_delay_packets_.pop();
    }
    else
    {
        rtp_delay_packets_index_++;

    }
    m_buffer_lock_.unlock();

    //modified by chenyc, 2019-01-08
    std::set<unsigned short> dev_rtp_to_apps;
    {
        std::lock_guard<std::mutex> lock(apps_mutex_);
        dev_rtp_to_apps = set_apps_;
    }
    for (auto app_rtp_port : dev_rtp_to_apps)
    {
        std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClient(app_rtp_port);
        if (app_client == nullptr)
        {
            LOG_EVERY_N(ERROR, 100) << "get " << google::COUNTER << " times log, app client local rtp sendto local app port not found. port is "
                                    << app_rtp_port << ", mac is :" << mac_;
            continue;
        }
        //通过服务器本端的m_nRtpFd,发给app的nat后的地址m_appAddr
        if (app_client->hasnat_)
        {
            Sendto(app_client->rtp_fd_, data, data_len, 0, (SA*)&app_client->app_addr_, sizeof(app_client->app_addr_));
        }
    }
    //分发流给内部的vrtspd,需要修改ssrc
    VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_tmp;
    {
        std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
        vrtsp_logic_id_addrs_tmp = vrtsp_logic_id_addrs_;
    }
    for (auto& vrtsp_logic_id_addr : vrtsp_logic_id_addrs_tmp)
    {
        unsigned char* ptr = &data[8];
        akcs::ByteWriter<uint32_t>::WriteBigEndian(ptr, vrtsp_logic_id_addr.second.ssrc_req);
        Sendto(inner_vrtsp_rtp_fd_, info.packet_data_, info.data_len_, 0, (SA*) & (vrtsp_logic_id_addr.second.addr), sizeof(vrtsp_logic_id_addr.second.addr));
    }

    return true;
}

//获取监控此设备的APP列表，add by czw
std::string RtpDeviceClient::GetAppClientList()
{
    std::string app_client_list = "APP: ";
    std::set<unsigned short> dev_rtp_to_apps;
    {
        std::lock_guard<std::mutex> lock(apps_mutex_);
        dev_rtp_to_apps = set_apps_;
    }
    for (auto app_rtp_port : dev_rtp_to_apps)
    {
        std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClient(app_rtp_port);
        if (app_client == nullptr)
        {
            CAKLog::LogE(tag_, "app client local rtp sendto port=%hu not found", app_rtp_port);
            continue;
        }
        //通过服务器本端的m_nRtpFd,发给app的nat后的地址m_appAddr
        std::string app_client_addr = Sock_ntop((SA*)&app_client->app_addr_, sizeof(app_client->app_addr_));
        app_client_list += app_client_addr.c_str();
        app_client_list += "\n";
    }
    return app_client_list;
}

int RtpDeviceClient::MonotorAppNum()
{
    return set_apps_.size();
}
bool RtpDeviceClient::IsAddingAppStatus()
{
    return adding_app_ == true;
}
void RtpDeviceClient::SetAddingAppStatus()
{
    adding_app_ = true;
}
void RtpDeviceClient::ResetAddingAppStatus()
{
    adding_app_ = false;
}
void RtpDeviceClient::AddMonitorApp(const unsigned short app_port)
{
    std::lock_guard<std::mutex> lock(apps_mutex_);
    set_apps_.insert(app_port);
}

bool RtpDeviceClient::FindAndRemoveApp(const unsigned short app_port)
{
    bool ret = false;
    std::lock_guard<std::mutex> lock(apps_mutex_);
    auto iter = set_apps_.find(app_port);
    if (iter != set_apps_.end())
    {
        set_apps_.erase(iter);
        CAKLog::LogD(tag_, "%s remove app client port=%hu", toString().c_str(), app_port);
        ret = true;
    }
    return ret;
}

void RtpDeviceClient::GetAllApp(std::set<unsigned short>& apps)
{
    std::lock_guard<std::mutex> lock(apps_mutex_);
    apps = set_apps_;
}
void RtpDeviceClient::AddInnerClient(const std::string& mac, const std::string& ip,
                                     const int32_t port, const std::string& vrtspd_logic_id, const int32_t ssrc_req)
{
    struct sockaddr_storage addr;
    ::memset(&addr, 0, sizeof(struct sockaddr_storage));
    struct sockaddr_in* addr_v4 = (struct sockaddr_in*)&addr;
    addr_v4->sin_family = AF_INET;
    addr_v4->sin_port = htons(static_cast<uint16_t>(port));
    if (::inet_aton(ip.c_str(), &(addr_v4->sin_addr)) == 0)
    {
        AK_LOG_WARN << "Invalid address: " << ip;
        return;
    }

    struct sockaddr_storage rtcp_addr;
    ::memset(&rtcp_addr, 0, sizeof(struct sockaddr_storage));
    struct sockaddr_in* rtcp_addr_v4 = (struct sockaddr_in*)&rtcp_addr;
    rtcp_addr_v4->sin_family = AF_INET;
    rtcp_addr_v4->sin_port = htons(static_cast<uint16_t>(port + 1));
    if (::inet_aton(ip.c_str(), &(rtcp_addr_v4->sin_addr)) == 0)
    {
        AK_LOG_WARN << "Invalid address: " << ip;
        return;
    }

    struct RtspInnerCli rtsp_inner_cli;
    ::memset(&rtsp_inner_cli, 0, sizeof(struct RtspInnerCli));
    rtsp_inner_cli.addr = addr;
    rtsp_inner_cli.rtcp_addr = rtcp_addr;
    rtsp_inner_cli.ssrc_req = ssrc_req;
    rtsp_inner_cli.keepalive_time = 0;
    {
        std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
        vrtsp_logic_id_addrs_[vrtspd_logic_id] = rtsp_inner_cli;
    }
}
bool RtpDeviceClient::FindAndRemoveInnerClient(const std::string& vrtspd_logic_id, const std::string& mac)
{
    bool ret = false;
    if (mac != mac_)
    {
        //CAKLog::LogD(tag_, "%s remove inner rtsp client error, logic id=%d, remove mac is %s, required mac is %s", toString().c_str(),
        //vrtspd_logic_id.c_str(), mac.c_str(), mac_.c_str());
        return ret;
    }
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    auto iter = vrtsp_logic_id_addrs_.find(vrtspd_logic_id);
    if (iter != vrtsp_logic_id_addrs_.end())
    {
        vrtsp_logic_id_addrs_.erase(iter);
        CAKLog::LogD(tag_, "%s remove inner rtsp client, logic id=%s", toString().c_str(), vrtspd_logic_id.c_str());
        ret = true;
    }
    return ret;
}
void RtpDeviceClient::KeepAliveRtspInnerClient(const std::string& vrtsp_logic_id,   const std::string& mac)
{
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    auto iter = vrtsp_logic_id_addrs_.find(vrtsp_logic_id);
    if (iter != vrtsp_logic_id_addrs_.end())
    {
        CAKLog::LogD(tag_, "rtsp inner client keepalive, mac is %s, vrtsp client id is %s", mac_.c_str(), iter->first.c_str());
        iter->second.keepalive_time = 0;
    }
}
bool RtpDeviceClient::CheckRtspInnerClient(int timer_step)
{
    std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
    for (auto iter = vrtsp_logic_id_addrs_.begin(); iter != vrtsp_logic_id_addrs_.end();)
    {
        if (iter->second.keepalive_time >= 60)
        {
            CAKLog::LogD(tag_, "rtsp inner client overtime, mac is %s, vrtsp client id is %s", mac_.c_str(), iter->first.c_str());
            vrtsp_logic_id_addrs_.erase(iter++);
            continue;
        }
        iter->second.keepalive_time += timer_step;
        iter++;
    }
    //检查下是否已经没有app和内部的边缘转流服务器了
    if ((MonotorAppNum() == 0) && (IsAddingAppStatus()) && (RtspInnerClientNum() == 0))
    {
        return true;
    }
    else
    {
        return false;
    }

}
int RtpDeviceClient::RtspInnerClientNum()
{
    return vrtsp_logic_id_addrs_.size();
}

void RtpDeviceClient::SetDclientVer(int dclient_ver)
{
    dclient_ver_ = dclient_ver;
}

bool RtpDeviceClient::ParseRtpHeader(struct sockaddr_storage& dev_addr, uint8_t* rtp_data, uint32_t rtp_data_len)
{
    if (rtp_data_len < kRtpMinParseLength)
    {
        return false;
    }
    // Version
    const uint8_t V = rtp_data[0] >> 6;
    if (V != kRtpExpectedVersion)
    {
        return false;
    }
    //rtcp
    if (webrtc::RtpHeaderParser::IsRtcp(rtp_data, rtp_data_len))
    {
        const uint8_t* ptr = &rtp_data[4];
        uint32_t recv_ssrc = akcs::ByteReader<uint32_t>::ReadBigEndian(ptr);
        if (!has_rctp_nat_) //是否已经完成app的udp-nat工作
        {
            has_rctp_nat_ = true;
            rtcp_receiver->SetRemoteSsrc(recv_ssrc);
            rtcp_sender->SetRemoteSSRC(recv_ssrc);
            rtcp_sender->SetSSRC(local_ssrc_);
        }
        else
        {
            rtcp_receiver->rtcp_receiver_->IncomingPacket(rtp_data, rtp_data_len);
            rtcp_sender->InsertIncomingPacket(recv_ssrc, 123); //设备发送一个平台就会回复一个

            int64_t last_rtt_ms = 0;
            int64_t avg_rtt_ms = 0;
            rtcp_receiver->rtcp_receiver_->RTT(recv_ssrc, &last_rtt_ms, &avg_rtt_ms, nullptr, nullptr);
            UpdateRtt(avg_rtt_ms);
        }
        //分发流给内部的vrtspd,需要修改ssrc
        VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_tmp;
        {
            std::lock_guard<std::mutex> lock(vrtsp_logic_id_mutex_);
            vrtsp_logic_id_addrs_tmp = vrtsp_logic_id_addrs_;
        }
        for (auto& vrtsp_logic_id_addr : vrtsp_logic_id_addrs_tmp)
        {
            unsigned char* ptr = &rtp_data[4];
            akcs::ByteWriter<uint32_t>::WriteBigEndian(ptr, vrtsp_logic_id_addr.second.ssrc_req);
            Sendto(inner_vrtsp_rtp_fd_, rtp_data, rtp_data_len, 0, (SA*) & (vrtsp_logic_id_addr.second.rtcp_addr), sizeof(vrtsp_logic_id_addr.second.rtcp_addr));
        }


        return true;
    }

    return true;
}
//重写CNackModuleManager::SendNack
void RtpDeviceClient::SendNack(const std::vector<uint16_t>& sequence_numbers)
{
    //rtcp nat 未穿透
    if (!has_rctp_nat_)
    {
        return;
    }
    uint16_t* kList = new uint16_t[sequence_numbers.size()];
    if (!sequence_numbers.empty())
    {
        memcpy(kList, &sequence_numbers[0], sequence_numbers.size()*sizeof(uint16_t));
    }
    rtcp_sender->rtcp_sender_->SetRTCPStatus(webrtc::RtcpMode::kReducedSize);
    rtcp_sender->rtcp_sender_->SendRTCP(rtcp_sender->feedback_state(), webrtc::kRtcpNack, sequence_numbers.size(), kList);
    delete []kList;
}

void RtpDeviceClient::PassthruRemb(uint32_t bitrate)
{
    //rtcp nat 未穿透
    if (!has_rctp_nat_)
    {
        return;
    }  
    std::vector<uint32_t> remb_ssrc;
    remb_ssrc.push_back(local_ssrc_);
    rtcp_sender->rtcp_sender_->SetRTCPStatus(webrtc::RtcpMode::kReducedSize);
    rtcp_sender->rtcp_sender_->SetRemb(bitrate, remb_ssrc);
    rtcp_sender->rtcp_sender_->SendRTCP(rtcp_sender->feedback_state(), webrtc::kRtcpRemb);
}

void RtpDeviceClient::GetAppNackRtpPacket(const std::vector<uint16_t>& sequence_numbers, std::vector<RtpPacketInfo>& packets)
{
    for (auto it : sequence_numbers)
    {
        for (size_t i = 0; i < RTP_PACKET_MAX_BUFFER_SIZE; i++)
        {
            if (v_rtp_packets_[i].seq_ == it)
            {
                packets.push_back(v_rtp_packets_[i]);
            }
        }
    }
}

}
