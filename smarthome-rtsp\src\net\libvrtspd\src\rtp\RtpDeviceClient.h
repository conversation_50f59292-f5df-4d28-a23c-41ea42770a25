#pragma once
#include <string>
#include <map>
#include <set>
#include <mutex>
#include <atomic>
#include <netinet/in.h>
#include "Unp.h"
#include "NackModule.h"
#include "RtcpSender.h"
#include "RtcpReceiver.h"
#include<queue>

#include "VrtspDefine.h"
enum
{
    DEV_STATE_NONE = 0,
    DEV_STATE_INIT,
    DEV_STATE_PLAY,
    DEV_STATE_MAX
};

#define RTP_MSG_MAX         100     //队列最大数100
#define RTP_MSG_WARN_COUNT  50      //队列超过50答应提示信息
#define PIC_NAME_SIZE       256

namespace akuvox
{
class AKRtcpReceiver;
class AKModuleRtpRtcp;
typedef struct _RTP_MSG_LIST
{
    int fd;
    unsigned int data_len;
    unsigned char* data;
    struct _RTP_MSG_LIST* next;
} RTP_MSG_LIST;
struct RtspInnerCli
{
    struct sockaddr_storage addr;//内部分流服务器的网络地址
    struct sockaddr_storage rtcp_addr;//内部分流服务器的网络地址
    int32_t ssrc_req;//内部分流服务器需求的ssrc
    int32_t keepalive_time;//内部分流服务器(边缘)keepalive
};

enum
{
    //rtcp
    kRtcpExpectedVersion = 2,
    kRtcpMinHeaderLength = 4,
    kRtcpMinParseLength = 8,
    //rtp
    kRtpExpectedVersion = 2,
    kRtpMinParseLength = 12
};

class RtpDeviceClient : public CNackModuleManager
{
public:
    typedef std::map<std::string/* 内部分流服务器逻辑id*/, struct RtspInnerCli/*内部分流服务器的信息*/> VrtspdLogicIDNetAddr;
public:
    /*
    * local_rtp_port:服务器用于接收dev端rtp数据的端口
    * mac:设备端的mac
    */
    RtpDeviceClient(unsigned short local_rtp_port, std::string& mac);
    ~RtpDeviceClient();

    bool CreateRtpSocket();
    void AddMsg(unsigned char* data, unsigned int data_len);
    void ProcessMsg();
    bool OnMessage(unsigned char* data, unsigned int data_len);
    std::string GetAppClientList();
    bool HasMessage();
    int MonotorAppNum();
    std::string toString();
    bool IsAddingAppStatus();
    void SetAddingAppStatus();
    void ResetAddingAppStatus();
    void AddMonitorApp(const unsigned short app_port);
    bool FindAndRemoveApp(const unsigned short app_port);
    void GetAllApp(std::set<unsigned short>& apps);
    void AddInnerClient(const std::string& mac, const std::string& ip,
                        const int32_t port, const std::string& vrtspd_logic_id, const int32_t ssrc_req);
    bool FindAndRemoveInnerClient(const std::string& vrtspd_logic_id, const std::string& mac);
    int RtspInnerClientNum();
    void KeepAliveRtspInnerClient(const std::string& vrtsp_logic_id,    const std::string& mac);
    bool CheckRtspInnerClient(int timer_step);
    void SetDclientVer(int dclient_ver);
    bool ParseRtpHeader(struct sockaddr_storage& dev_addr, uint8_t* rtp_data, uint32_t rtp_data_len);

    void SendNack(const std::vector<uint16_t>& sequence_numbers) override;
    void PassthruRemb(uint32_t bitrate);
    void GetAppNackRtpPacket(const std::vector<uint16_t>& sequence_numbers, std::vector<RtpPacketInfo>& packets);
    void UpdateRtt(int64_t rtt_ms)
    {
        nack_module_.UpdateRtt(rtt_ms);
    }

    int OnReceivedPacket(uint16_t seq_num)
    {
        nack_module_.OnReceivedPacket(seq_num, false, false);
        if (nack_module_.TimeUntilNextProcess() <= 0)
        {
            nack_module_.Process();
        }
		return 0;
    }

public:
    const char* tag_;
    int rtp_fd_;
    int rtcp_fd_;
    unsigned short local_rtcp_port_;        //用于接收R2X rtcp端口
    unsigned short local_rtp_port_;     //用于接收R2X rtp媒体包的服务器rtp端口
    std::string mac_;                  //有三方摄像头时为uuid,没有三方摄像头时为设备mac
    std::string dev_mac_;              //绑定三方摄像头的设备mac或者被监控设备的mac地址
    std::atomic<int> state_;
    bool capture_;
    char pic_name_[PIC_NAME_SIZE];
    int dclient_ver_;
    int32_t ssrc_;//平台生成的ssrc值
    struct sockaddr_storage dev_addr_;
private:
    std::mutex apps_mutex_;
    std::set<unsigned short> set_apps_; //设备rtp数据关联的app列表，列表项是app的服务器上的rtp接受port
    std::mutex m_lock; //用于保护该 dev发送过来的消息队列
    void* msg_header_;
    unsigned int msg_count_;
    bool processing_;
    std::atomic<bool> adding_app_;
    //vrtspd内部分流集群
    std::mutex vrtsp_logic_id_mutex_;
    VrtspdLogicIDNetAddr vrtsp_logic_id_addrs_;
    int inner_vrtsp_rtp_fd_;
    uint32_t first_true_rtp_ssrc_;
    bool is_has_checked_ssrc_;//已经校验好ssrc了
    std::string outer_ip_;//设备的外网ip

public:
    bool has_rctp_nat_;
    bool has_rtp_nat;
    struct sockaddr_storage dev_rtcp_addr;
    struct sockaddr_storage dev_rtp_addr;

    std::shared_ptr<AKRtcpTransport> rtcp_transport;
    std::shared_ptr<AkRtcpSender> rtcp_sender;
    std::shared_ptr<AKRtcpReceiver> rtcp_receiver;
    std::shared_ptr<AKModuleRtpRtcp> rtcp_module;

    uint32_t dev_rtp_ssrc_;//设备流的ssrc
    uint32_t local_ssrc_;//发给设备流的ssrc，TODO:app 过来流的ssrc
    bool first_dev_rtp_arrive;

    std::mutex m_buffer_lock_; //用于保护缓存队列
    std::vector<RtpPacketInfo> v_rtp_packets_;
    int rtp_packets_index_;
    std::priority_queue<RtpPacketInfo> rtp_delay_packets_;
    int rtp_delay_packets_index_;
};
}
