#include "RtpDeviceManager.h"
#include "RtpAppManager.h"
#include "AKLog.h"
#include <string.h>
#include "CsvrtspConf.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include "SnowFlakeGid.h"
#include "RtpDeviceManager.h"
#include "SmarthomeHandle.h"


extern CSVRTSP_CONF gstCSVRTSPConf;
extern std::string g_logic_srv_id;

namespace akuvox
{
RtpDeviceManager::RtpDeviceManager() : tag_("RtpDeviceManager")
{
    cur_local_port_ = LOCAL_DEVICE_RTP_PORT_BASE;
}

RtpDeviceManager::~RtpDeviceManager()
{
    ClearClient();
}

RtpDeviceManager* RtpDeviceManager::instance = nullptr;

RtpDeviceManager* RtpDeviceManager::getInstance()
{
    if (instance == nullptr)
    {
        instance = new RtpDeviceManager;
    }
    return instance;
}

std::shared_ptr<RtpDeviceClient> RtpDeviceManager::AddClient(std::string& mac)
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    for (auto iter : dev_rtp_clients_)
    {
        if (0 == strcasecmp(iter.second->mac_.c_str(), mac.c_str()))
        {
            CAKLog::LogI(tag_, "device rtp mac=%s has exist.", mac.c_str());
            return iter.second;
        }
    }

    std::shared_ptr<RtpDeviceClient> client = nullptr;
    unsigned short local_port = 0;
    //生成本地端口
    //added by chenyc,注意:当前为单线程, 多线程会有多个设备共用一个rtp收流端口的问题.
    int try_times = 3;
    while (try_times--)
    {
        local_port = GenLocalPort();
        if (local_port > 0)
        {
            client = std::make_shared<RtpDeviceClient>(local_port, mac);
            bool ret = client->CreateRtpSocket();//setup完之后,开始创建接受设备的 udp包的地址
            if (!ret)
            {
                CAKLog::LogW(tag_, "create device socket error, try again!");
                local_port = 0;
                client = nullptr;
                continue;
            }
            else
            {
                break;
            }
        }
    }

    if (local_port)
    {
        dev_rtp_clients_.insert(std::pair<unsigned short, std::shared_ptr<RtpDeviceClient>>(local_port, client));
    }
    else
    {
        CAKLog::LogE(tag_, "gen local rtp port for dev rtp packet fail");
    }
    return client;
}

void RtpDeviceManager::RemoveClient(unsigned short dev_local_port)
{
    std::map<unsigned short, std::shared_ptr<RtpDeviceClient>>::iterator iter;
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    iter = dev_rtp_clients_.find(dev_local_port);
    if (iter != dev_rtp_clients_.end())
    {
        std::shared_ptr<RtpDeviceClient> rtp_dev_client = iter->second;
        if (!rtp_dev_client->IsAddingAppStatus()) //added by chenyc, 2019-01-09,判断是否正在加app监控客户端中
        {
            dev_rtp_clients_.erase(dev_local_port);
        }
    }
}

void RtpDeviceManager::ClearClient()
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    dev_rtp_clients_.clear();
}
std::shared_ptr<RtpDeviceClient> RtpDeviceManager::GetClientAndRemove(unsigned short dev_local_port)
{
    std::shared_ptr<RtpDeviceClient> dev_rtp_client;
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    auto iter = dev_rtp_clients_.find(dev_local_port);
    if (iter != dev_rtp_clients_.end())
    {
        dev_rtp_client = iter->second;
        if (!dev_rtp_client->IsAddingAppStatus()) //added by chenyc, 2019-01-09,判断是否正在加app监控客户端中
        {
            dev_rtp_clients_.erase(dev_local_port);
        }
    }
    return dev_rtp_client;
}
std::shared_ptr<RtpDeviceClient> RtpDeviceManager::GetClient(unsigned short dev_local_port)
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    auto iter = dev_rtp_clients_.find(dev_local_port);
    if (iter != dev_rtp_clients_.end())
    {
        return iter->second;
    }
    return nullptr;
}

std::shared_ptr<RtpDeviceClient> RtpDeviceManager::GetClientByMac(const std::string& mac)
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    for (auto client : dev_rtp_clients_)
    {
        if (0 == strcasecmp(client.second->mac_.c_str(), mac.c_str()))
        {
            return client.second;
        }
    }
    return nullptr;
}

std::shared_ptr<RtpDeviceClient> RtpDeviceManager::GetClientBySocket(int socket)
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    for (auto client : dev_rtp_clients_)
    {
        if (client.second->rtp_fd_ == socket)
        {
            return client.second;
        }
    }
    return nullptr;
}
std::shared_ptr<RtpDeviceClient> RtpDeviceManager::GetClientByRtcpSocket(int socket)
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    for (auto client : dev_rtp_clients_)
    {
        if (client.second->rtcp_fd_ == socket)
        {
            return client.second;
        }
    }
    return nullptr;
}

std::string RtpDeviceManager::GetMonitorList()
{

    DevRtpMap tmp_dev_rtp_clients;
    {
        //由于是智能指针，赋值给局部变量后计数值加一，并不用担心造成野指针。局部锁达到缩小临界区
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    std::stringstream oss;
    for (auto client : tmp_dev_rtp_clients)
    {
        std::shared_ptr<RtpDeviceClient> pDevClient = client.second;
        oss << "\n" << "MAC: " << pDevClient->mac_ << "\n"
            << "DEV: " << Sock_ntop((SA*)&pDevClient->dev_addr_, sizeof(pDevClient->dev_addr_)) << "\n"
            << pDevClient->GetAppClientList();
    }

    if (0 == oss.str().size())
    {
        oss << "No Found\n";
    }

    return oss.str();
}

std::string RtpDeviceManager::GetMonitorListByMac(const std::string& mac)
{
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    std::stringstream oss;
    for (auto client : tmp_dev_rtp_clients)
    {
        if (0 == strcasecmp(client.second->mac_.c_str(), mac.c_str()))
        {
            std::shared_ptr<RtpDeviceClient> pDevClient = client.second;
            oss << "\n" << "MAC: " << pDevClient->mac_ << "\n"
                << "DEV: " << Sock_ntop((SA*)&pDevClient->dev_addr_, sizeof(pDevClient->dev_addr_)) << "\n"
                << pDevClient->GetAppClientList();
        }
    }

    if (0 == oss.str().size())
    {
        oss << "No Found\n";
    }

    return oss.str();
}

void RtpDeviceManager::DeviceAddApp(unsigned short nDevRtpPort, unsigned short app_rtp_port)
{
    std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
    //先找到dev
    std::map<unsigned short, std::shared_ptr<RtpDeviceClient>>::iterator iter;
    //如果不用AddingAppStatus()这个来保护的话,可能运行到这里,其他线程删除掉m_mapDevRtpClients后,nDevRtpPort复用,对应的dev已经修改了,导致串流
    iter = dev_rtp_clients_.find(nDevRtpPort);
    if (iter != dev_rtp_clients_.end())
    {
        //modified by chenyc,2019-01-08
        iter->second->AddMonitorApp(app_rtp_port);
        iter->second->ResetAddingAppStatus();
    }
}

void RtpDeviceManager::DeviceRemoveApp(unsigned short app_rtp_port, unsigned short& vec_dev_port)
{
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        //遍历所有dev,查找要删除的app-rtp-port归属于那个dev-rtp-port客户端,删除之.
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    std::set<unsigned short>::iterator iter;
    for (auto client : tmp_dev_rtp_clients)//TODO,通过遍历的方式,效率低下
    {
        if (client.second->FindAndRemoveApp(app_rtp_port))
        {
            if ((client.second->MonotorAppNum() == 0) && (!client.second->IsAddingAppStatus())
                    && (client.second->RtspInnerClientNum() == 0))//added by chenyc 2019-01-23,增加判断是否还有内部转流rtsp服务器
            {
                vec_dev_port = client.first;
                CAKLog::LogD(tag_, "%s has 0 app client.", client.second->toString().c_str());
            }
            break;
        }
    }
}

void RtpDeviceManager::DeviceRemoveInnerClient(const std::string& vrtsp_logic_id, unsigned short& vec_dev_port, const std::string& mac)
{
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        //遍历所有dev,查找要删除的app-rtp-port归属于那个dev-rtp-port客户端,删除之.
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    std::set<unsigned short>::iterator iter;
    for (auto client : tmp_dev_rtp_clients)
    {
        if (client.second->FindAndRemoveInnerClient(vrtsp_logic_id, mac))
        {
            if ((client.second->MonotorAppNum() == 0) && (!client.second->IsAddingAppStatus())
                    && (client.second->RtspInnerClientNum() == 0))//added by chenyc 2019-01-23,增加判断是否还有内部转流rtsp服务器
            {
                vec_dev_port = client.first;
                CAKLog::LogD(tag_, "%s has 0 rtsp inner client.", client.second->toString().c_str());
            }
            break;
        }
    }
}
unsigned short RtpDeviceManager::GenLocalPort()
{
    unsigned short i = (cur_local_port_ + 2) % (LOCAL_DEVICE_RTP_PORT_MAX - LOCAL_DEVICE_RTP_PORT_BASE);
    for (; i < (LOCAL_DEVICE_RTP_PORT_MAX - LOCAL_DEVICE_RTP_PORT_BASE); i += 2)
    {
        //int nPort = LOCAL_DEVICE_RTP_PORT_BASE + i%(LOCAL_DEVICE_RTP_PORT_MAX - LOCAL_DEVICE_RTP_PORT_BASE);
        int nPort = LOCAL_DEVICE_RTP_PORT_BASE + i;
        std::map<unsigned short, std::shared_ptr<RtpDeviceClient>>::iterator iter;
        //added by chenyc,2019-01-09, 当两个线程同时进入的时候,会导致两台设备共用一个 rtp 收流端口
        iter = dev_rtp_clients_.find(nPort);
        if (iter == dev_rtp_clients_.end())
        {
            cur_local_port_ = nPort;
            return nPort;
        }
    }

    return 0;
}
//TODO chenyc,直接传本地端口进来。
void RtpDeviceManager::AddMsg(int fd, struct sockaddr_storage& dev_addr, unsigned char* data, unsigned int data_len)
{
    std::shared_ptr<RtpDeviceClient> rtp_device = RtpDeviceManager::getInstance()->GetClientBySocket(fd);
    if (rtp_device == NULL)
    {
        rtp_device = RtpDeviceManager::getInstance()->GetClientByRtcpSocket(fd);
        if (rtp_device == NULL)
        {
            CAKLog::LogE(tag_, "%s cannot find device fd=%d", __FUNCTION__, fd);
            return;
        }
    }

    //rtp_device->AddMsg(data, data_len);
    if (rtp_device->ParseRtpHeader(dev_addr, data, data_len))
    {
        rtp_device->AddMsg(data, data_len);

        if (!rtp_device->has_rtp_nat)
        {
            memcpy(&rtp_device->dev_addr_, &dev_addr, sizeof(struct sockaddr_storage));
            rtp_device->has_rtp_nat = true;
        }
    }
}

void RtpDeviceManager::GetHasMsgClient(std::vector<std::shared_ptr<RtpDeviceClient>>& rtp_dev_clients)
{
    //modified by chenyc,2019-01-08,拷贝到本地即可,增加可定无所谓下个循环再读出来即可,
    //删除也无所谓 发送rtp时还会检测该设备下是否有app,对业务没有影响.
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    //modified by chency,2019-01-08,当前面的dev的rtp包很大时，后面的dev的rtp包的消费者容易造成饿死的现象或者较大的延迟.
    for (auto client : tmp_dev_rtp_clients)
    {
        if (client.second->HasMessage())
        {
            rtp_dev_clients.emplace_back(client.second);
        }
    }  
}

void RtpDeviceManager::ReportAll()
{
    //modified by chenyc,2019-01-08,只是打印,拷贝到本地再打印信息就可以
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        //遍历所有dev,查找要删除的app-rtp-port归属于那个dev-rtp-port客户端,删除之.
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }

    CAKLog::LogD(tag_, "RTP device count=%lu", tmp_dev_rtp_clients.size());
    std::map<unsigned short, std::shared_ptr<RtpDeviceClient>>::iterator iter;
    for (iter = tmp_dev_rtp_clients.begin(); iter != tmp_dev_rtp_clients.end(); ++iter)
    {
        CAKLog::LogD(tag_, "%s", iter->second->toString().c_str());
        std::set<unsigned short> apps;
        iter->second->GetAllApp(apps);
        for (auto appid : apps)
        {
            std::shared_ptr<RtpAppClient> app_client = RtpAppManager::getInstance()->GetClient(appid);
            if (app_client == nullptr)
            {
                CAKLog::LogE(tag_, "app client local port=%hu not found", appid);
                continue;
            }
            CAKLog::LogD(tag_, "%s", app_client->toString().c_str());
        }
    }
}


//add by larry 20171221
void RtpDeviceManager::SendHeartBeatForList()
{
    //added by chenyc,2019-01-08
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        //遍历所有dev,查找要删除的app-rtp-port归属于那个dev-rtp-port客户端,删除之.
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    std::map<unsigned short, std::shared_ptr<RtpDeviceClient>>::iterator iter;
    for (iter = tmp_dev_rtp_clients.begin(); iter != tmp_dev_rtp_clients.end(); ++iter)
    {
        if (iter->second != nullptr)
        {
            if ((iter->second->MonotorAppNum() > 0) || (iter->second->RtspInnerClientNum() > 0))
            {
                //zhiwei.chen 向家居发起设备保活请求
                CAKLog::LogD(tag_, "send keep alive rtsp mq msg to smg ,mac=%s,dev_mac=%s", iter->second->mac_.c_str(), iter->second->dev_mac_.c_str());
                SmarthomeHandle::GetInstance().NotifyDevKeepalive(iter->second->mac_);
            }
        }
    }
}
//end by larry
void RtpDeviceManager::DeviceKeepAliveInnerClient(const std::string& vrtsp_logic_id, const std::string& mac)
{
    DevRtpMap tmp_dev_rtp_clients;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    for (const auto& dev_rtp_client : tmp_dev_rtp_clients)
    {
        if (mac == dev_rtp_client.second->mac_)
        {
            dev_rtp_client.second->KeepAliveRtspInnerClient(vrtsp_logic_id, mac);//5秒检测一次,边缘是否超时
            break;
        }
    }

}

void RtpDeviceManager::CheckInnerVrtspKeepAlive(int timer_step)
{
    DevRtpMap tmp_dev_rtp_clients;
    DevRtpMap dev_rtp_client_to_dels;
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        tmp_dev_rtp_clients = dev_rtp_clients_;
    }
    for (const auto& dev_rtp_client : tmp_dev_rtp_clients)
    {
        if (dev_rtp_client.second->CheckRtspInnerClient(timer_step))
        {
            dev_rtp_client_to_dels.insert(dev_rtp_client);
        }
    }
    //需要删除的rtpdev对象
    if (dev_rtp_client_to_dels.size() > 0)
    {
        std::lock_guard<std::mutex> lock(dev_rtp_clients_mutex);
        {
            for (const auto dev_rtp_client_to_del : dev_rtp_client_to_dels)
            {
                if ((dev_rtp_client_to_del.second->MonotorAppNum() == 0) && (dev_rtp_client_to_del.second->IsAddingAppStatus())
                        && (dev_rtp_client_to_del.second->RtspInnerClientNum() == 0))
                {
                    dev_rtp_clients_.erase(dev_rtp_client_to_del.first);
                }
            }

        }
    }
}

}
