#pragma once
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include "RtpDeviceClient.h"

#define LOCAL_DEVICE_RTP_PORT_BASE  64000
#define LOCAL_DEVICE_RTP_PORT_MAX   65000

namespace akuvox
{
class RtpDeviceManager
{
public:
    typedef std::map<unsigned short/*dev rtp port*/, std::shared_ptr<RtpDeviceClient>> DevRtpMap;
public:
    ~RtpDeviceManager();
    static RtpDeviceManager* getInstance();

    std::shared_ptr<RtpDeviceClient> AddClient(std::string& mac);
    void RemoveClient(unsigned short local_port);
    void ClearClient();
    std::shared_ptr<RtpDeviceClient> GetClientAndRemove(unsigned short dev_local_port);
    std::shared_ptr<RtpDeviceClient> GetClient(unsigned short local_port);
    std::shared_ptr<RtpDeviceClient> GetClientByMac(const std::string& mac);
    std::shared_ptr<RtpDeviceClient> GetClientBySocket(int socket);
    std::shared_ptr<RtpDeviceClient> GetClientByRtcpSocket(int socket);
    void GetHasMsgClient(std::vector<std::shared_ptr<RtpDeviceClient>>& rtp_dev_clients);

    //4.6运维接口新增获取监控点列表，此处遍历查询，add by czw
    std::string GetMonitorList();
    std::string GetMonitorListByMac(const std::string& mac);

    /*
    * local_port:设备的local rtp port
    * app_rtp_port:关联app的local rtp port
    */
    void DeviceAddApp(unsigned short local_port, unsigned short app_rtp_port);
    /*
    * app_rtp_port:关联app的local rtp port
    * vecDevFds:关联app为0的dev的列表
    */
    void DeviceRemoveApp(unsigned short app_rtp_port, unsigned short& vec_dev_port);

    void AddMsg(int fd, struct sockaddr_storage& dev_addr, unsigned char* data, unsigned int data_len);
    void ReportAll();
    void SendHeartBeatForList();
    //当csroute转发csvrtsp集群内,其他服务停止监控的时候调用.
    void DeviceRemoveInnerClient(const std::string& vrtsp_logic_id, unsigned short& vec_dev_port, const std::string& mac);
    void DeviceKeepAliveInnerClient(const std::string& vrtsp_logic_id,  const std::string& mac);
    void CheckInnerVrtspKeepAlive(int timer_step);

private:
    RtpDeviceManager();
    unsigned short GenLocalPort();

private:
    static RtpDeviceManager* instance;
    const char* tag_;
    std::mutex dev_rtp_clients_mutex;
    std::map<unsigned short/*dev rtp port*/, std::shared_ptr<RtpDeviceClient>> dev_rtp_clients_;  //key==本端接收设备rtp流的端口
    unsigned short cur_local_port_;
};
}
