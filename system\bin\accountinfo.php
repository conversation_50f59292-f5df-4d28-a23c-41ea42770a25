<?php
/*处理大版本升级时候，需要根据低版本在插入数据的操作*/
/*V4.0到4.1需要插入PersonalAccountCnf配置*/
date_default_timezone_set("PRC");

function getDB()
{
    $dbhost = "localhost";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}


$db = getDB();
$type = $argv[1];
$value = $argv[2];

$devstr = "Dev:\n";
$uid="";
if ($type == "uid")
{
  $uid=$value;  
}
else if ($type == "mac")
{
    $sth2 = $db->prepare("select Node From PersonalDevices where Mac=:MAC;");
    $sth2->bindValue(':MAC', $value);
    $sth2->execute();
    $ret = $sth2->fetch(PDO::FETCH_ASSOC);
    $uid = $ret['Node'];
    if (empty($uid))
    {
        $sth2 = $db->prepare("select Node From Devices where Mac=:MAC;");
        $sth2->bindValue(':MAC', $value);
        $sth2->execute();
        $ret = $sth2->fetch(PDO::FETCH_ASSOC);
        $uid = $ret['Node'];        
    }
}
else
{
    exit(0);
}

$sth2 = $db->prepare("select Account,Role,Name From PersonalAccount where Account=:Account;");
$sth2->bindValue(':Account', $uid);
$sth2->execute();
$AccountInfo = $sth2->fetch(PDO::FETCH_ASSOC);
$Node = "";
if ($AccountInfo['Role'] == '10') //个人主账号
{
    //根据Account查找主账号下面的设备列表
    $sth2 = $db->prepare("select A.Account as Node, A.SipAccount as AppSip, A.Name, D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd as RtspPwd,A.SipPwd as AppSipPwd from PersonalAccount A left join PersonalDevices D on D.Node = A.Account   where A.Account = :account");
}
else if ($AccountInfo['Role'] == '20')//社区主账号
{
    $sth2 = $db->prepare("select A.Account as Node, A.SipAccount as AppSip, A.Name, D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd as RtspPwd,A.SipPwd as AppSipPwd from PersonalAccount A left join Devices D on D.Node = A.Account   where A.Account = :account");
}
else if ($AccountInfo['Role'] == '11')//个人从账号
{
    $sth2 = $db->prepare("select B.Account as Node,A.SipAccount as AppSip, A.Name, D.MAC,D.SipAccount,D.Location,D.Type,D.RtspPwd as RtspPwd,A.SipPwd as AppSipPwd from PersonalAccount A left join PersonalAccount B on B.ID = A.ParentID left join PersonalDevices D on D.Node = B.Account where A.Account = :account");			
}
else //社区从账号
{    
    //根据Account查找主账号下面的设备列表
    $sth2 = $db->prepare("select B.Account as Node, A.SipAccount as AppSip, A.Name, D.MAC,D.SipAccount,D.Location,D.Type,D.RtspPwd as RtspPwd,A.SipPwd as AppSipPwd from PersonalAccount A left join PersonalAccount B on B.ID = A.ParentID left join Devices D on D.Node = B.Account where A.Account = :account");
}
$sth2->bindParam(':account', $uid, PDO::PARAM_STR);

$ret = $sth2->execute();        
$dev_list = $sth2->fetchALL(PDO::FETCH_ASSOC);

$appSip = null;
$appName = null;
foreach ($dev_list as $row => $dev)
{	
    $Node = $dev['Node'];;
    $appSip = $dev['AppSip'];
    $appName = $dev['Name'];
    $devstr = $devstr.$dev['MAC']." ".$dev['Location']." ".$dev['SipAccount']." type:".$dev['Type']."\n";
}    


$accStr = "\nAccount:\n";
$sth2 = $db->prepare("select ID,Account,Role,Name,ParentID,EnableIpDirect From PersonalAccount where Account=:Account 
union 
select A.ID, A.Account,A.Role,A.Name,A.ParentID,B.EnableIpDirect From PersonalAccount A left join PersonalAccount B on A.ParentID=B.ID and (A.role=11 or A.role=21) where B.Account=:Account;");
$sth2->bindValue(':Account', $Node);
$ret = $sth2->execute();        
$Account_list = $sth2->fetchALL(PDO::FETCH_ASSOC);
$i = 0;
foreach ($Account_list as $row => $acc)
{	
    if ($i == 0) {
       $accStr = $accStr."Master: ".$acc['Account']." ".$acc['Name']." IPdirect:".$acc['EnableIpDirect']."\n";
       $i = 1;
    }
    else {
       $accStr = $accStr."Slave: ".$acc['Account']." ".$acc['Name']."\n";
    }
}    

$pbxStr = "\nPBX:\n";
$pbxStr = $pbxStr."username   groupname  groupring type\n";
$sth2 = $db->prepare("select F.username,F.groupname,F.groupring,F.type from freeswitch.userinfo F left join AKCS.SipGroup2 S on S.SipGroup=F.Groupname  where S.Account=:Account");
$sth2->bindValue(':Account', $Node);
$ret = $sth2->execute();        
$PBX_list = $sth2->fetchALL(PDO::FETCH_ASSOC);
foreach ($PBX_list as $row => $acc)
{	
    $pbxStr = $pbxStr.$acc['username']." ".$acc['groupname']."       ".$acc['groupring']."   ".$acc['type']."\n";
}

echo $devstr;
echo $accStr;
echo $pbxStr;

?>
