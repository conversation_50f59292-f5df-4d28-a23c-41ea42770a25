#!/usr/bin/python3.4

import os
#运行在csadapt服务器下
import sys
from urllib import request
import json
import pymysql
import hashlib
import time
import base64
from urllib import parse as url_parse
from urllib import request as url_request

argc=len(sys.argv)
if argc ==0 or argc !=3:
       print("usage:")
       print("%s {MAC} Config" % sys.argv[0])
       print("%s {MAC} Contact" % sys.argv[0])
       print("旧小区:")
       print("%s {MAC} Rfid" % sys.argv[0])
       print("%s {MAC} Privatekey" % sys.argv[0])
       print("%s {MAC} Face" % sys.argv[0])
       print("新小区:")
       print("%s {MAC} UserAll" % sys.argv[0])
       print("%s {MAC} Schedule" % sys.argv[0])
       print("%s {MAC} UserMeta" % sys.argv[0])
       print("重启和更新mac配置文件:")
       print("%s reboot {MAC}" % sys.argv[0])
       print("%s updatemac {MAC}" % sys.argv[0])
       print("%s sendkeysend {MAC}" % sys.argv[0])
       print("获取rtspUrl:")
       print("%s RtspUrl {MAC}" % sys.argv[0])
       print("")#空行
       print("*******************以下是各个业务组件对外提供的HTTP维护通道*******************")
       print("")
       print("************************************csmain************************************")
       print("开启csmain发送给设备消息的日志: curl  http://inner_ip:9998/setsendlog?enable=1")
       print("csmain组件版本查询:curl http://inner_ip:9998/svn_version")
       print("curl http://inner_ip:9998/dev_status?mac=0C1105802268")
       print("curl http://inner_ip:9998/app_status?uid=01235694")
       print("curl http://inner_ip:9997/monitor_list")
       print("curl http://inner_ip:9997/monitor_list?mac=0C1105802268")
       print("开启抓包：curl http://inner_ip:9997/set_pcap_mac?mac=0C1105802268")
       print("停止抓包：curl http://inner_ip:9997/clear_pcap_mac?mac=0C1105802268")
       print("curl http://inner_ip:9998/setTzMd?tz_md5=")
       print("curl http://inner_ip:9998/setTzMd?tz_data_md5=")
       print("curl http://inner_ip:9998/requestDevReportStatus?mac=")
       print("让设备从新下载配置文件:curl http://inner_ip:9998/triggerMacKeysend -X POST -d '{\"mac\":\"0A0203200117;0C1105062DC3\"}'")
       print("通知csmain下面的所有设备全部重启:curl http://inner_ip:9998/notifyAllDevsReboot")
       print("csmain模拟设备刷新权限组的接口:curl http://inner_ip:9998/triggerReqUserInfo?mac=xxxx&users_list=yyyy;mmm;nnn")
       print("重新加载部分配置参数:curl http://inner_ip:9998/reloadConf")
       print("")
       print("************************************csgate************************************")
       print("打印网关返回给app的消息：curl http://127.0.0.1:9999/decrypt_log_output?switch=1")
       print("csgate组件版本查询:curl http://inner_ip:9999/svn_version")
       print("")
       print("************************************csvrtsp************************************")
       print("vrtsp组件版本查询:curl http://inner_ip:9997/svn_version")
       print("")
       print("************************************PBX************************************")
       print('查询sip是否在线：fs_cli -x "sofia status profile internal reg 5100100001"')
       print('下游fs节点load_balance查看：./opensipsctl fifo lb_list')
       print('强制OpenSIPS从DB中重新载入所有的配置信息：./opensipsctl fifo lb_reload')
       print('修改目的fs的状态（可用/不可用），例如将id为12的目的地置为可用：./opensipsctl fifo lb_status 12 1')
       print("")
       print("************************************csconfig************************************")
       print('人脸上传fdfs失败处理：php /var/www/html/apache-v3.0/notify/tools/face_reupload.php')
       print('配置上传fdfs失败处理：/usr/local/akcs/csconfig/bin/csconfig -s')
       print('手动触发刷新配置方法 cd /var/www/html/apache-v3.0/notify/tools && php GenerateConfigFile.php')
       print('获取设备监控地址RTSP URL: curl http://inner_ip:9996/getDeviceRtspUrl?mac=A80700230905')
       print("************************************RTSP抓包************************************")
       print('开启抓包 到web节点执行php /var/www/html/apache-v3.0/notify/tools/pcap_capture.php $mac 1')
       print('关闭抓包 到web节点执行php /var/www/html/apache-v3.0/notify/tools/pcap_capture.php $mac 0')
       print('导出抓包 到web节点执行php /var/www/html/apache-v3.0/notify/tools/download_pcap.php $mac')
       print("************************************slim开启log调试************************************")
       print("到web节点执行sed -i 's/const LOG_DEBUG = 0;/const LOG_DEBUG = 1;/g' /var/www/html/slim/framework/util/log.php")
       print("************************************密码混淆解密************************************")
       print('到web节点执行php /var/www/html/apache-v3.0/notify/tools/decode_password.php')
       print("")
       exit(1)

def main():
    p = os.popen("cat /usr/local/akcs/csadapt/conf/csadapt.conf | grep -w db_ip | awk -F'=' '{ print $2 }' | tr -d  \"\\n\" ")
    dbip = p.read()
    p.close()
    
    p = os.popen("cat /etc/web_backend_install.conf | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }' | tr -d  \"\\n\" ")
    fdfs = p.read()
    p.close()

    p = os.popen("cat /etc/ip | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }' | tr -d  \"\\n\" ")
    ip_address = p.read()
    p.close()
    
    operation = sys.argv[1]
    if operation == "reboot":
        MAC = sys.argv[2]
        csmainIP = getMacServer(dbip, MAC)
        req_csmain_maintance(("http://%s:9998/rebootDev" % csmainIP), {"mac": MAC})
        exit(1)
    elif operation == "updatemac":
        MAC = sys.argv[2]
        csmainIP = getMacServer(dbip, MAC)
        req_csmain_maintance(("http://%s:9998/UpdateConfig" % csmainIP), {"mac": MAC})
        exit(1)
    elif operation == "sendkeysend":
        MAC = sys.argv[2]
        csmainIP = getMacServer(dbip, MAC)
        req_csmain_maintance(("http://%s:9998/triggerMacKeysend" % csmainIP), {"mac": MAC})
        exit(1)


    mac = sys.argv[1]
    configType = sys.argv[2]

    if configType == "RtspUrl":
        url = 'http://' + ip_address + ':9996/getDeviceRtspUrl'
        params = {'mac': mac}
        print(httpGet(url, params))
        exit(1)
        
    typeMap = {"Config": "0","Privatekey": "1","Rfid": "2","Contact": "3","Face": "4","Schedule": "5","UserMeta": "6","UserAll": "7"}
    typeNum = typeMap[configType]
    token = getToken(mac, typeNum)
    params = {'mac': mac,'type': typeNum,'token': token}
    url = 'http://' + ip_address + ':9996/getDevicesShadow'
    print(httpGet(url, params))
    exit(1)
        
def getMacServer(dbip, mac):
    # 打开数据库连接
    db = pymysql.connect(dbip, "dbuser01", "Ak@56@<EMAIL>", "AKCS")
    # 使用cursor()方法获取操作游标
    cursor = db.cursor()

    # SQL 查询语句
    sql = "select AccSrvID From Devices where Mac='%s' union all select AccSrvID From PersonalDevices where Mac='%s'" % (
    mac, mac)
    try:
        # 执行SQL语句
        cursor.execute(sql)
        # 获取所有记录列表
        csmain_server = cursor.fetchone()[0]
    except:
        print("Error: unable to fetch data")
        exit(1)
    # 关闭数据库连接
    db.close()
    return csmain_server
    
def getMacPath(dbip, mac, type):
    # 打开数据库连接
    db = pymysql.connect(dbip, "dbuser01", "Ak@56@<EMAIL>", "AKCS")
    # 使用cursor()方法获取操作游标
    cursor = db.cursor()

    # SQL 查询语句
    sql = "select %sPath From DevicesShadow where Mac='%s'" % (
    type, mac)
    try:
        # 执行SQL语句
        cursor.execute(sql)
        # 获取所有记录列表
        path = cursor.fetchone()[0]
    except:
        path = ''
    # 关闭数据库连接
    db.close()   
    return path
    
def getEncodeUrl(raw_url):
    key = "ak_download"
    path = 'https://127.0.0.1:8091' + raw_url
    timestamp = str(int(time.time()) + 3600)
    raw_str = key + ':' + raw_url + ':' + timestamp
    h = hashlib.md5(raw_str.encode('UTF8'))
    md5 = h.digest()
    token = base64.b64encode(md5)
    trantab = str.maketrans('+/', '-_', '=')
    token = str(token, encoding='UTF8').translate(trantab)
    encode_url = path + '?token=' + token + '&e=' + timestamp
    return encode_url

# 请求体数据
def req_csmain_maintance(url, dictinfo):
    #request_data = {"mac": MAC}
    headers = {"content-type": "application/json"}
    req = request.Request(url=url,
                          headers=headers,
                          data=json.dumps(dictinfo).encode("utf-8"))
    res = request.urlopen(req).read().decode("utf-8")
    print(res)

# 计算token
def getToken(mac, configtype):
    key = 'Akuvox2023'
    raw_str = mac + ':' + configtype + ':' + key
    md5hash = hashlib.md5(raw_str.encode('UTF8'))
    md5 = md5hash.hexdigest()
    return md5

def httpGet(url, params):
    querystring = url_parse.urlencode(params)
    u = url_request.urlopen(url + '?' + querystring, timeout=5)
    content = u.read().decode('utf8')
    return content

main()
