#!/bin/sh
mysqlexec="mysql -h localhost -S /tmp/mysql.sock  -u root -pAk@56@<EMAIL> mysql -e"
name=$2
pw=$3
ip=$4
num=$#

check_create()
{
    if [ $num -ne 4 ];then
       echo "usage: $0 $1 <name> <passwd> <ip>"
       exit 1
    fi
    if [ $name = "root" ] || [ $name = "akuvox" ];then
        echo "Please change <name>"
        exit 0
    fi
    if [ $ip"x" = '%x' ];then
        echo "The IP cannot be %"
        exit 0
    fi     
}

case "$1" in
        create_r)
            check_create create_r
            `$mysqlexec "grant select on AKCS.*  to $name@\"$ip\" Identified by \"$pw\"" 2>/dev/null`
            `$mysqlexec "grant select on freeswitch.*  to $name" 2>/dev/null`
            `$mysqlexec "flush privileges" 2>/dev/null`
        ;;
        create_rw)
            check_create create_rw
            `$mysqlexec "grant select,insert,delete,update,create,drop,INDEX,CREATE VIEW,SHOW VIEW on AKCS.*  to $name@\"$ip\" Identified by \"$pw\"" 2>/dev/null`
            `$mysqlexec "grant select,insert,delete,update,create,drop,INDEX,CREATE VIEW,SHOW VIEW on freeswitch.*  to $name" 2>/dev/null`
            `$mysqlexec "flush privileges" 2>/dev/null`
        ;;
        list)
            CMD="select distinct(User) from user;"
            MYSQL_CMD="mysql -N -h localhost -S /tmp/mysql.sock  -u root -pAk@56@<EMAIL> mysql"
            (echo $CMD | ${MYSQL_CMD} 2>/dev/null)|while read user #如果要查找多个字段，只需要在这里添加个参数
            do
                echo "$user"
            done
        ;;
        delete)
            if [ $num -ne 2 ];then
               echo "usage: $0 $1 <name>"
               exit 1
            fi
            if [ $name = "root" ] || [ $name = "akuvox" ];then
                echo "Cannot delete $name"
                exit 0
            fi
            `$mysqlexec "delete from user where user=\"$name\"" 2>/dev/null`
            `$mysqlexec "flush privileges" 2>/dev/null`
        ;;
        *)
            echo "usage: $0 create_r <name> <passwd> <ip>"
            echo "       $0 create_rw <name> <passwd> <ip>"
            echo "       $0 list"
            echo "       $0 delete <name>"
            exit 3
        ;;
esac