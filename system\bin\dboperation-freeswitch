#!/bin/sh
mysqlexec="mysql -h 127.0.0.1 -P 3305  -u root -pAk@56@<EMAIL> mysql -e"
name=$2
pw=$3
ip=$4
num=$#

check_create()
{
    if [ $num -ne 4 ];then
       echo "usage: $0 $1 <name> <passwd> <ip>"
       exit 1
    fi
    if [ $name = "root" ] || [ $name = "akuvox" ];then
        echo "Please change <name>"
        exit 0
    fi
    if [ $ip"x" = '%x' ];then
        echo "The IP cannot be %"
        exit 0
    fi     
}

case "$1" in
        create_r)
            check_create create_r
            `$mysqlexec "grant select on freeswitch.*  to $name@\"$ip\" Identified by \"$pw\""`
            `$mysqlexec "flush privileges"`
        ;;
        create_rw)
            check_create create_rw
            `$mysqlexec "grant select,insert,delete,update,create,drop,INDEX,CREATE VIEW,SHOW VIEW,ALTER on freeswitch.*  to $name@\"$ip\" Identified by \"$pw\""`
            `$mysqlexec "flush privileges"`
        ;;
        grant_rep)
            check_create grant_rep
            `$mysqlexec "GRANT SELECT, UPDATE,DELETE,INSERT,REPLICATION SLAVE, REPLICATION CLIENT ON *.*  to $name@\"$ip\" Identified by \"$pw\""`
            `$mysqlexec "flush privileges"`
        ;;		
        root_for_inner_ip)
			host=$2
            `$mysqlexec "insert into user(Host,User,Password,Select_priv,Insert_priv,Update_priv,Delete_priv,Create_priv,Drop_priv,Reload_priv,Shutdown_priv,\
			Process_priv,File_priv,Grant_priv,References_priv,Index_priv, Alter_priv,Show_db_priv,Super_priv,Create_tmp_table_priv,Lock_tables_priv,\
			Execute_priv,Repl_slave_priv,Repl_client_priv,Create_view_priv,Show_view_priv,Create_routine_priv,Alter_routine_priv,Create_user_priv,\
			Event_priv,Trigger_priv,Create_tablespace_priv,ssl_type,ssl_cipher,x509_issuer,x509_subject,max_questions,max_updates,max_connections,\
			max_user_connections,plugin,authentication_string,password_expired) select \"$host\",User,Password,Select_priv,Insert_priv,Update_priv,Delete_priv,Create_priv,Drop_priv,Reload_priv,Shutdown_priv,\
			Process_priv,File_priv,Grant_priv,References_priv,Index_priv, Alter_priv,Show_db_priv,Super_priv,Create_tmp_table_priv,Lock_tables_priv,\
			Execute_priv,Repl_slave_priv,Repl_client_priv,Create_view_priv,Show_view_priv,Create_routine_priv,Alter_routine_priv,Create_user_priv,\
			Event_priv,Trigger_priv,Create_tablespace_priv,ssl_type,ssl_cipher,x509_issuer,x509_subject,max_questions,max_updates,max_connections,\
			max_user_connections,plugin,authentication_string,password_expired from user where user='root' and Host='127.0.0.1'"`
            `$mysqlexec "flush privileges"`			
        ;;
        list)
            CMD="select distinct(User) from user;"
            MYSQL_CMD="mysql -N -h localhost -S /tmp/mysql.sock  -u root -pAk@56@<EMAIL> mysql"
            (echo $CMD | ${MYSQL_CMD} 2>/dev/null)|while read user #如果要查找多个字段，只需要在这里添加个参数
            do
                echo "$user"
            done
        ;;
        delete)
            if [ $num -ne 2 ];then
               echo "usage: $0 $1 <name>"
               exit 1
            fi
            if [ $name = "root" ] || [ $name = "akuvox" ];then
                echo "Cannot delete $name"
                exit 0
            fi
            `$mysqlexec "delete from user where user=\"$name\""`
            `$mysqlexec "flush privileges"`
        ;;
        *)
            echo "usage: $0 create_r <name> <passwd> <ip>"
            echo "       $0 create_rw <name> <passwd> <ip>"
			echo "       $0 root_for_inner_ip  <ip>"
			echo "       $0 grant_rep <name> <passwd> <ip>" 
            echo "       $0 list"
            echo "       $0 delete <name>"
            exit 3
        ;;
esac