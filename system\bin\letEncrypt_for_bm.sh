#!/bin/sh

#https://certbot.eff.org/lets-encrypt/ubuntubionic-nginx
#apt install snapd -y
#sudo snap install core; sudo snap refresh core
#sudo snap install --classic certbot
#sudo ln -s /snap/bin/certbot /usr/bin/certbot

#其他
#sudo snap list
#sudo snap remove <package-name>
#apt-get autoremove snapd


OPT_TYPE=$1
DOMAIN=$2
NGINXCONF_PATH=/usr/local/nginx/conf/sites-enabled/bm_main.conf
RENEW_CRON_PATH=/var/spool/cron/crontabs/root
if [ $OPT_TYPE"x" != "installx" ] && [ $OPT_TYPE"x" != "renewx" ] || [ $# != 2 ];then
 echo usage:
 echo $0 install domain
 echo $0 renew domain
 exit
fi
copy_cert()
{
     cp /etc/letsencrypt/archive/$DOMAIN/fullchain1.pem /usr/local/nginx/conf/cert/akcs_web_cert.pem
    cp /etc/letsencrypt/archive/$DOMAIN/privkey1.pem /usr/local/nginx/conf/cert/akcs_web_privkey.pem
}
copy_new_cert()
{
     cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem /usr/local/nginx/conf/cert/akcs_web_cert.pem
    cp /etc/letsencrypt/live/$DOMAIN/privkey.pem /usr/local/nginx/conf/cert/akcs_web_privkey.pem
}


if [ $OPT_TYPE"x" = "installx" ];then
   if [ -z "`grep acme-challenge $NGINXCONF_PATH`" ];then
     sed -i 's/#download/&\nlocation ^~ \/.well-known\/acme-challenge\/ {\ndefault_type \"text\/plain\";\nroot \/var\/www_for_bm\/html;\n}\n/' $NGINXCONF_PATH
   fi
   /usr/bin/certbot certonly --email <EMAIL> --agree-tos   --webroot -w /var/www_for_bm/html -d $DOMAIN
   copy_cert
   /etc/init.d/nginx restart
   if [ -z "`grep letEncrypt.sh $RENEW_CRON_PATH`" ];then
     echo "0 1 */5 * * /bin/bash /bin/letEncrypt.sh renew $DOMAIN" >> $RENEW_CRON_PATH
     /etc/init.d/cron restart
   fi
   exit
fi

if [ $OPT_TYPE"x" = "renewx" ];then
   //usr/bin/certbot renew --quiet
   copy_new_cert
   /usr/local/nginx/sbin/nginx -s reload
   exit
fi
