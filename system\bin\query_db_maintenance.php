<?php

$cfg = @parse_ini_file("/etc/app_backend_install.conf");
$dbuser = "dbuser01";
$dbpass = "Ak@56@<EMAIL>";
$dbip = $cfg['MYSQL_INNER_IP'];
$dbport = 3306;
$dbname = "AKCS";
$pbx_dbip = $cfg['FREESWITCH_MYSQL_INNER_IP'];
$pbx_dbport = $cfg['FREESWITCH_MYSQL_PORT'];
$pbx_dbname = "freeswitch";

function getDB()
{
    global $dbuser,$dbpass,$dbip,$dbport,$dbname;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function execute_mysql_cmd($dbip, $dbname, $dbport, $sql)
{
    global $dbuser,$dbpass;
    // 构建 shell 命令
    $cmd = sprintf(
        "mysql -h %s -u %s -p%s -P %s -D %s -s -e \"%s\" | awk '{ printf \"%%20s%%s\\n\", \$1, \$2 }'",
        $dbip,
        $dbuser,
        $dbpass,
        $dbport,
        $dbname,
        $sql
    );

    // 执行命令并获取结果
    $result = array();
    exec($cmd, $result);

    // 返回执行结果
    foreach ($result as $line) {
        echo $line . "\n";
    }
}

function getUserBySip($sip)
{
    $db = getDB();
    $account_sec = "ID, Name,ParentID,Account,UnitID,EnableIpDirect,UUID,
    Phone,PhoneCode,SipAccount,Active,ExpireTime < now() as isExpire,Role,Initialization,TempKeyPermission,Language,EnableSmartHome,
    ParentUUID,PhoneExpireTime < now() as isPhoneExpire,unix_timestamp(ExpireTime),unix_timestamp(PhoneExpireTime),TimeZone,ExpireTime,PhoneExpireTime,RoomNumber,unix_timestamp(NOW()),
    ReadMsgID,UserInfoUUID,RoomID,PhoneStatus";
    $sth = $db->prepare("select $account_sec from PersonalAccount where Account = :account");
    $sth->bindParam(':account', $sip, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function getCommunityInfoByMngID($mng_id)
{
    $db = getDB();
    $sth = $db->prepare("select * from CommunityInfo where AccountID = :mng_id");
    $sth->bindParam(':mng_id', $mng_id, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function printUserInfoBySip($sip)
{
    global $dbname,$dbip,$dbport;
    $sql = "select * from PersonalAccount where Account = '$sip'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
}

function getUserByEmail($email)
{
    $db = getDB();
    $sth = $db->prepare("select A.Account from PersonalAccount A join PersonalAccountUserInfo B on A.UserInfoUUID = B.UUID where B.Email = :email");
    $sth->bindParam(':email', $email, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $data;
}

function getDeviceInfoByMac($mac)
{
    $db = getDB();
    $sth = $db->prepare("select A.*,B.AuthCode from Devices A join MacPool B on A.MAC = B.Mac where A.MAC = :mac");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function printDeviceInfoByMac($mac)
{
    echo "Devices\n";
    global $dbname,$dbip,$dbport;
    $sql = "select A.*,B.AuthCode as MacPoolAuthcode from Devices A left join MacPool B on A.MAC = B.Mac where A.MAC = '$mac'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
    
    echo "PersonalDevices\n";
    $sql = "select A.*,B.AuthCode as MacPoolAuthcode from PersonalDevices A left join MacPool B on A.MAC = B.Mac where A.MAC = '$mac'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);    
}

function getPersonalDeviceInfoByMac($mac)
{
    $db = getDB();
    $sth = $db->prepare("select A.*,B.AuthCode as MacPoolAuthcode from PersonalDevices A left join MacPool B on A.MAC = B.Mac where A.MAC = :mac");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function printPersonalDeviceInfoByMac($mac)
{
    global $dbname,$dbip,$dbport;
    $sql = "select A.*,B.AuthCode as MacPoolAuthcode  from PersonalDevices A left join MacPool B on A.MAC = B.Mac where A.MAC = '$mac'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
}

function IsDeviceIndoor($firmware)
{
    $db = getDB();
    $sth = $db->prepare("select ID from VersionModel where VersionNumber = :firmware and Type = 2");
    $sth->bindParam(':firmware', $firmware, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        return true;
    }

    return false;
}

function checkDeviceOnlineFlag($dev_info)
{
    $mac = $dev_info['MAC'];
    //校验设备是否为室内机
    $version = explode('.', $dev_info['Firmware'])[0];
    //校验室内机上线标识
    if (IsDeviceIndoor($version)) {
        if (($dev_info['Flags']>>8)&1) {
            echo "Device:$mac is online.\n";
        } else {
            echo "Device:$mac is not online.\n";
        }
    } else {
        echo "Device:$mac is not indoor.\n";
    }
}

function checkUserIndoorPlan($sip)
{
    $db = getDB();
    $sth = $db->prepare("select MAC from DevicesSpecial where Account = :account");
    $sth->bindParam(':account', $sip, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        echo "User:$sip has indoor plan.\n";
        $mac = $data['MAC'];
        //获取设备版本号及室内机上线标识
        $dev_info = getDeviceInfoByMac($mac);
        if ($dev_info) {
            checkDeviceOnlineFlag($dev_info);
        } else {
            $per_dev_info = getPersonalDeviceInfoByMac($mac);
            checkDeviceOnlineFlag($per_dev_info);
        }
    } else {
        echo "User:$sip does not have indoor plan.\n";
    }
}

function printNodeInfo($node)
{
    //打印主账号信息
    global $dbname,$dbip,$dbport;
    echo "Node Accounts Info are:\n";
    $sql = "select * from PersonalAccount where Account = '$node'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
    //打印从账号信息
    $sql2 = "select distinct B.* from PersonalAccount A join PersonalAccount B on A.UUID = B.ParentUUID where A.Account = '$node'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql2);
    echo "\n";
    //打印房间设备列表
    echo "Node Devices Info are:\n";
    $sql3 = "select * from Devices where Node = '$node'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql3);
    $sql4 = "select * from PersonalDevices where Node = '$node'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql4);
}

function printPbxSipInfo($sip)
{
    global $pbx_dbip,$pbx_dbname,$pbx_dbport;
    $sql = "select username,groupname,type,groupring,communityid,deleted,password from userinfo where username = '$sip'\G";
    execute_mysql_cmd($pbx_dbip, $pbx_dbname, $pbx_dbport, $sql);
}

function printPbxGroupSipInfo($sip)
{
    global $pbx_dbip,$pbx_dbname,$pbx_dbport;
    $sql = "select username,groupname,type,groupring,communityid,deleted,password from userinfo where groupname = '$sip'\G";
    execute_mysql_cmd($pbx_dbip, $pbx_dbname, $pbx_dbport, $sql);
}

function printUserInfoByPhone($phone_like)
{
    global $dbname,$dbip,$dbport;
    $sql = "select A.* from PersonalAccount A join PersonalAccountUserInfo B on A.UserInfoUUID = B.UUID where B.MobileNumber like '%$phone_like%'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
}

function checkUserLandline($uid)
{
    $user_info = getUserBySip($uid);
    if ($user_info) {
        //单住户高级功能开关
        if (($user_info['Switch']>>2)&1) {
            echo "User:$uid Feature Plan Switch is on.\n";
        } else {
            echo "User:$uid Feature Plan Switch is off.\n";
        }

        //单住户落地开关
        if ($user_info['PhoneStatus']) {
            echo "User:$uid Phone Status is on.\n";
        } else {
            echo "User:$uid Phone Status is off.\n";
        }

        //单住户落地过期
        if ($user_info['isPhoneExpire']) {
            echo "User:$uid Landline is expire.\n";
        } else {
            echo "User:$uid Landline is not expire.\n";
        }
    } else {
        echo "User:$uid does not exist.\n";
    }
}

function printDeviceByOuterIP($ip)
{
    global $dbname,$dbip,$dbport;
    echo "Devices\n";
    $sql = "select * from Devices where outerIP like '%$ip%'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
    
    echo "Personal Devices\n";
    $sql = "select * from PersonalDevices where outerIP like '%$ip%'\G";
    execute_mysql_cmd($dbip, $dbname, $dbport, $sql);
}


function checkCommunityLandline($mng_id)
{
    $comm_info = getCommunityInfoByMngID($mng_id);
    if ($comm_info) {
        //社区落地开关
        if (($comm_info['Switch']>>1)&1) {
            echo "Comm:$mng_id Landline Switch is on.\n";
        } else {
            echo "Comm:$mng_id Landline Switch is off.\n";
        }
    } else {
        echo "Comm:$mng_id does not exist.\n";
    }
}

function cmd_usage($cmd)
{
    echo("usage: ".$cmd. " user_indoor_plan <emial/sip>\n");
    echo("       ".$cmd. " node_info <node>\n");
    echo("       ".$cmd. " device_info <mac>\n");
    echo("       ".$cmd. " pbx_sip_info <sip>\n");
    echo("       ".$cmd. " pbx_gruop_info <sip>\n");
    echo("       ".$cmd. " user_phone_like <phone>\n");
    echo("       ".$cmd. " user_landline_status <uid>\n");
    echo("       ".$cmd. " comm_landline_status <mng_id>\n");
	echo("       ".$cmd. " device_ip_like <ip>\n");
    exit(0);
}

if ($argc < 2) {
    cmd_usage($argv[0]);
}

if ($argv[1] == "user_indoor_plan") {
    $sip_or_email = $argv[2];
    if (strpos($sip_or_email, "@") !== false) {
        $uid_arr = getUserByEmail($sip_or_email);
        foreach ($uid_arr as $uid) {
            checkUserIndoorPlan($uid['Account']);
        }
    } else {
        checkUserIndoorPlan($sip_or_email);
    }
} elseif ($argv[1] == "node_info") {
    $node = $argv[2];
    printNodeInfo($node);
} elseif ($argv[1] == "device_info") {
    $mac = $argv[2];
    printDeviceInfoByMac($mac);
} elseif ($argv[1] == "pbx_sip_info") {
    $sip = $argv[2];
    printPbxSipInfo($sip);
} elseif ($argv[1] == "pbx_gruop_info") {
    $sip = $argv[2];
    printPbxGroupSipInfo($sip);
} elseif ($argv[1] == "user_phone_like") {
    $phone_like = $argv[2];
    printUserInfoByPhone($phone_like);
} elseif ($argv[1] == "user_landline_status") {
    $uid = $argv[2];
    checkUserLandline($uid);
} elseif ($argv[1] == "comm_landline_status") {
    $mng_id = $argv[2];
    checkCommunityLandline($mng_id);
} elseif ($argv[1] == "device_ip_like") {
    $ip = $argv[2];
    printDeviceByOuterIP($ip);
} else {
    cmd_usage($argv[0]);
}
