#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-25
# Filename      :   install.sh
# Version       :
# Description   :   akcs_system 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }


cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)

echo 'setp1: stop systemrun'
bash "$PKG_ROOT"/system/build/stop_systemrun.sh $RSYNC_PATH $PROJECT_RUN_PATH

#echo 'setp2: set scripts'
#bash "$PKG_ROOT"/system/build/set_scripts.sh $RSYNC_PATH $PROJECT_RUN_PATH

#echo 'setp3: set nginx'
#bash "$PKG_ROOT"/system/build/set_nginx.sh $RSYNC_PATH $PROJECT_RUN_PATH

echo 'setp4: set php'
bash "$PKG_ROOT"/system/build/set_php.sh $RSYNC_PATH $PROJECT_RUN_PATH

echo 'setp5: set mysql'
bash "$PKG_ROOT"/system/build/set_mysql.sh $RSYNC_PATH $PROJECT_RUN_PATH

echo 'setp7: start systemrun'
bash "$PKG_ROOT"/system/build/start_systemrun.sh $RSYNC_PATH $PROJECT_RUN_PATH

echo 'akcs_system install complete.'

