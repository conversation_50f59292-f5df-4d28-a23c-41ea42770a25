#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-25
# Filename      :   set_fdfs.sh
# Version       :
# Description   :   设置 akcs_fdfs 的脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)

IP_FILE=/etc/ip
APP_HOME=/usr/local/fastdfs
RUN_SCRIPT=fdfs_run.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
FDFS_DIR=$PKG_ROOT/system/fdfs


echo '读取配置'
if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)


# 替换配置文件
echo '替换配置文件的配置'

# 处理 fdfs 的配置文件
sed -i "s/^bind_addr=.*/bind_addr=${SERVER_INNER_IP}/g" "$FDFS_DIR"/tracker.conf
sed -i "s/^bind_addr=.*/bind_addr=${SERVER_INNER_IP}/g" "$FDFS_DIR"/storage.conf
sed -i "s/^tracker_server=.*/tracker_server=${SERVER_INNER_IP}:22122/g" "$FDFS_DIR"/storage.conf


echo '复制安装包的文件'
cp -rf "${FDFS_DIR}"/tracker.conf /etc/fdfs/
cp -rf "${FDFS_DIR}"/storage.conf /etc/fdfs/
cp -rf "${FDFS_DIR}"/http.conf /etc/fdfs/
cp -rf "${FDFS_DIR}"/anti-steal.jpg /etc/fdfs/
cp -rf "${FDFS_DIR}"/fdfs_run.sh $APP_HOME/scripts/
cp -rf "${FDFS_DIR}"/fdfs_storaged_nginx.sh $APP_HOME/scripts/
cp -rf "${FDFS_DIR}"/fdfs_tracker_nginx.sh $APP_HOME/scripts/
cp -rf "${FDFS_DIR}"/fdfs_storaged.sh $APP_HOME/scripts/
chmod -R 755 $APP_HOME/scripts


echo "停止 FDFS 的守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi


echo "启动 FDFS 的守护脚本 $RUN_SCRIPT"
nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
sleep 2

rm -rf /home/<USER>/data/*

if docker ps | awk '{print $NF}' | grep -w tracker; then
    docker restart tracker
    docker exec tracker fdfs_trackerd /etc/fdfs/tracker.conf
fi

if docker ps | awk '{print $NF}' | grep -w storage; then
    docker restart storage
    docker exec storage fdfs_storaged /etc/fdfs/storage.conf
fi

