#!/bin/bash

# ****************************************************************************
# Author        :   jianjun.li
# Last modified :   2022-04-25
# Filename      :   set_mysql.sh
# Version       :
# Description   :   设置 akcs_mysql 的脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)


# mysql升级，替换完配置直接重启，升级时候会升级数据库，不能停止。
# 确保 query.log slow-query.log 两个日志文件存在

if [ ! -d /var/log/mysqllog ]; then
    mkdir /var/log/mysqllog
    chmod 755 /var/log/mysqllog
fi

if [ ! -f /var/log/mysqllog/query.log ]; then
    touch /var/log/mysqllog/query.log
    chmod 644 /var/log/mysqllog/query.log
fi

if [ ! -f /var/log/mysqllog/slow-query.log ]; then
    touch /var/log/mysqllog/slow-query.log
    chmod 644 /var/log/mysqllog/slow-query.log
fi

chown -R mysql:mysql /var/log/mysqllog

# 复制运维文件
rm -f "$PKG_ROOT"/system/bin/dboperation-freeswitch    # 因为 akcs_system 的这个文件比 akcs_mysql 中的旧，所以不复制
chmod 755 "$PKG_ROOT"/system/bin/*
cp -rf "$PKG_ROOT"/system/bin/* /bin

