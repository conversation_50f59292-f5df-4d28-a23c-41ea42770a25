#!/bin/bash

# ****************************************************************************
# Author        :   jianjun.li
# Last modified :   2022-04-25
# Filename      :   set_nginx.sh
# Version       :
# Description   :   设置 akcs_nginx 的脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)

NGX_INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_HOME=/usr/local/nginx
RUN_SCRIPT=update_cert_from_remote_server_run.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT


echo '读取配置'
if [ ! -f $NGX_INSTALL_CONF ]; then
    echo "文件不存在：$NGX_INSTALL_CONF"
    exit 1
fi

# 读取安装配置
WEB_DOMAIN=$(grep_conf 'WEB_DOMAIN' $NGX_INSTALL_CONF)
ALI_TRACKER_IP=$(grep_conf 'ALI_TRACKER_IP' $NGX_INSTALL_CONF)
AWS_TRACKER_IP=$(grep_conf 'AWS_TRACKER_IP' $NGX_INSTALL_CONF)
TRACKER_INNER_IP=$(grep_conf 'TRACKER_INNER_IP' $NGX_INSTALL_CONF)
GATE_INNER_IP=$(grep_conf 'GATE_INNER_IP' $NGX_INSTALL_CONF)
MASTER_WEB_INNER_IP=$(grep_conf 'MASTER_WEB_INNER_IP' $NGX_INSTALL_CONF)
TRACKER_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $NGX_INSTALL_CONF)

# 替换配置文件
echo '替换配置文件的配置'

# 替换 nginx 的文件
cp -rf "$PKG_ROOT"/system/nginx/etc $APP_HOME
cp -rf "$PKG_ROOT"/system/nginx/html $APP_HOME
cp -rf "$PKG_ROOT"/system/nginx/logs $APP_HOME
rm -f $APP_HOME/sbin/nginx
cp -rf "$PKG_ROOT"/system/nginx/sbin $APP_HOME
cp -rf "$PKG_ROOT"/system/nginx/scripts $APP_HOME
cp -rf "$PKG_ROOT"/system/nginx/temp $APP_HOME

# 复制文件
# 如果证书已经存在，则不复制证书
if [ -f $APP_HOME/conf/cert/wildcard/fullchain.crt ]; then
    rm -rf "$PKG_ROOT"/system/nginx/conf/cert
fi

if [ -d $APP_HOME/conf/cert ]; then
    rm -rf "$PKG_ROOT"/system/nginx/conf/cert
fi

cp -rf "$PKG_ROOT"/system/nginx/* $APP_HOME

if [ ! -d $APP_HOME/conf/bm_conf ]; then
    mkdir -p $APP_HOME/conf/bm_conf
fi

chmod 755 -R $APP_HOME/sbin
chmod 755 -R $APP_HOME/scripts

# 开机启动脚本
cp -f "$PKG_ROOT"/system/nginx/etc/init.d/nginx /etc/init.d
chmod 755 /etc/init.d/nginx

if [ -z "$ALI_TRACKER_IP" ]; then
    ALI_TRACKER_IP="127.0.0.1"
fi

if [ -z "$AWS_TRACKER_IP" ]; then
    AWS_TRACKER_IP="127.0.0.1"
fi

if [ -z "$TRACKER_INNER_IP" ]; then
    TRACKER_INNER_IP="127.0.0.1"
fi

sed -i "s/^.*YOUR_DOMAIN=.*/YOUR_DOMAIN=${WEB_DOMAIN}/g" $APP_HOME/scripts/update_cert_from_remote_server.sh

sed -i "s/TRACKER_INNER_IP/${TRACKER_INNER_IP}/g" $APP_HOME/conf/sites-enabled/upstream.conf
if [ -z "$TRACKER_BACKUP_INNER_IP" ]; then
    #若不存在tracker双机，backup这行清空
    sed -i "s/^.*TRACKER_BACKUP_INNER_IP.*//g" $APP_HOME/conf/sites-enabled/upstream.conf
else
    sed -i "s/TRACKER_BACKUP_INNER_IP/${TRACKER_BACKUP_INNER_IP}/g" $APP_HOME/conf/sites-enabled/upstream.conf
fi

sed -i "
    s/ALI_TRACKER_IP/${ALI_TRACKER_IP}/g
    s/AWS_TRACKER_IP/${AWS_TRACKER_IP}/g
    " $APP_HOME/conf/sites-enabled/fdfs.conf
sed -i "s/GATE_INNER_IP/${GATE_INNER_IP}/g" $APP_HOME/conf/sites-enabled/gate.conf

#update second web nginx  config
sed -i "s/MASTER_WEB_INNER_IP/${MASTER_WEB_INNER_IP}/g" $APP_HOME/conf/common/web-location-download-second-web.conf
sed -i "s/MASTER_WEB_INNER_IP/${MASTER_WEB_INNER_IP}/g" $APP_HOME/conf/common/web-location.conf
sed -i "s/MASTER_WEB_INNER_IP/${MASTER_WEB_INNER_IP}/g" $APP_HOME/conf/sites-enabled/fdfs.conf
sed -i "s/MASTER_WEB_INNER_IP/${MASTER_WEB_INNER_IP}/g" $APP_HOME/conf/sites-enabled/smarthomeapi.conf


echo "停止证书的守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo '启动证书的守护脚本'
nohup bash $RUN_SCRIPT_PATH >/dev/null &
sleep 2

echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi

echo 'Reload Nginx 的配置'
/etc/init.d/nginx reload


