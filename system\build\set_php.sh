#!/bin/bash

# ****************************************************************************
# Author        :   jianjun.li
# Last modified :   2022-04-25
# Filename      :   set_php.sh
# Version       :
# Description   :   设置 akcs_php 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
PHP_INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)

IP_FILE=/etc/ip
APP_HOME=/usr/local/php

     
echo '读取配置'
if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

TRACKER_INNER_IP=$(grep_conf 'TRACKER_INNER_IP' $PHP_INSTALL_CONF)
FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $PHP_INSTALL_CONF)

# 替换配置文件
echo '替换配置文件的配置'

sed -i "s/^tracker_server=.*/tracker_server=${TRACKER_INNER_IP}:22122/g" "$PKG_ROOT"/system/php/etc/client.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" "$PKG_ROOT"/system/php/etc/client.conf
fi


echo '复制安装包的文件'

if [ ! -d /var/log/php ]; then
    mkdir /var/log/php
    chmod 755 -R /var/log/php
fi

# 确定 php-fpm 的日志记录路径
if [ ! -d /var/log/php-fpmlog ]; then
    mkdir /var/log/php-fpmlog
    chmod 755 -R /var/log/php-fpmlog
fi

cp -rf "$PKG_ROOT"/system/php/* $APP_HOME


echo '重启 php-fpm 进程'
/etc/init.d/php-fpm restart




