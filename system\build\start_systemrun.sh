#!/bin/bash

# ****************************************************************************
# Author        :   jianjun.li
# Last modified :   2022-04-25
# Filename      :   start_systemrun.sh
# Version       :
# Description   :   用来启动 akcs_system 的守护脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)


INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_HOME=/usr/local/akcs/scripts
RUN_SCRIPT=systemrun.sh
RUN_SCRIPT_PATH=$APP_HOME/$RUN_SCRIPT


echo '读取配置'
if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi


if [ ! -d /var/log/redis ]; then
    mkdir -p /var/log/redis
    chmod 755 /var/log/redis
fi

if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

# 复制守护脚本
cp -rf "$PKG_ROOT"/system/build/systemrun.sh $APP_HOME
cp -rf "$PKG_ROOT"/system_scripts/common.sh $APP_HOME
chmod 755 -R $APP_HOME

# 添加到开机启动
if ! grep -q -w "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi


ulimit -c unlimited

echo '启动守护脚本'
chmod 755 $RUN_SCRIPT_PATH
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo 'akcs_system 安装成功'

