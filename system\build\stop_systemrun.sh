#!/bin/bash

# ****************************************************************************
# Author        :   ji<PERSON><PERSON>.li
# Last modified :   2022-04-25
# Filename      :   stop_systemrun.sh
# Version       :
# Description   :   杀死守护脚本 systemrun.sh
#                   systemrun.sh 是 nginx 和  php-fpm 的守护脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }


RUN_SCRIPT=systemrun.sh

echo 'stop systemrun.sh'

# 杀死守护脚本
echo "Stopping $RUN_SCRIPT ..."
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

