#!/bin/sh

#csroute安装脚本,含csroute、nsq等两个组件
AKCS_ETCD_INSTALL_PATH=/usr/local/etcd
AKCS_RUN_SCRIPT_NAME=etcdrun.sh
AKCS_RUN_SCRIPT=${AKCS_ETCD_INSTALL_PATH}/scripts/${AKCS_RUN_SCRIPT_NAME}
PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PAKCAGES_ROOT=${PWD}/../..
chmod 777 -R ${PAKCAGES_ROOT}/akcs_etcd/*
HOST_IP=/etc/ip
#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null; 
    #IP地址必须为全数字 
    if [ $? -ne 0 ] 
    then 
        return 1 
    fi 
    ipaddr=$1 
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值 
    b=`echo $ipaddr|awk -F . '{print $2}'` 
    c=`echo $ipaddr|awk -F . '{print $3}'` 
    d=`echo $ipaddr|awk -F . '{print $4}'` 
    for num in $a $b $c $d 
    do 
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间 
        then 
            return 1 
        fi 
    done 
    return 0 
}
EchoHostIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
    
    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4
        
    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
}
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6;    

    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}
if [ -f $HOST_IP ];then
    EchoHostIPAddr    
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    
    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi

#replace serverip,注意ip后面的等号不能有空格
ETCD_LISTEN_LINE="listen-client-urls: http:\/\/127.0.0.1:8507, http:\/\/${SERVER_INNER_IP}:8507"
sed -i "s/^.*listen-client-urls.*/${ETCD_LISTEN_LINE}/g" ${PAKCAGES_ROOT}/akcs_etcd/conf/akcs-etcd.conf

scriptpid=`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${AKCS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi

echo "stopping etcd services..."
${PAKCAGES_ROOT}/akcs_etcd/scripts/etcdctl.sh stop
sleep 1
echo "making logs directories..."
if [ ! -d /var/log/etcdlog ]; then
    mkdir -p /var/log/etcdlog
fi

echo "copying akcs csgate files..."
if [ -d /usr/local/etcd ]; then
    rm -rf  /usr/local/etcd
fi
mkdir -p /usr/local/etcd/bin
mkdir -p /usr/local/etcd/conf
mkdir -p /usr/local/etcd/scripts
chmod 777 -R /usr/local/etcd
cp -rf ${PAKCAGES_ROOT}/akcs_etcd/bin /usr/local/etcd/
cp -rf ${PAKCAGES_ROOT}/akcs_etcd/conf /usr/local/etcd/
cp -rf ${PAKCAGES_ROOT}/akcs_etcd/scripts /usr/local/etcd/

if [ -z "`grep "export ETCDCTL_API=3" /etc/profile`" ];then
	echo "export ETCDCTL_API=3" >> /etc/profile
    source /etc/profile
fi
   
if [ ! -d  /usr/local/etcd/data ];then
    mkdir /usr/local/etcd/data
    mkdir /usr/local/etcd/wal
fi
#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi

echo "starting services..."
chmod 777 /usr/local/etcd/scripts/etcdctl.sh
/usr/local/etcd/scripts/etcdctl.sh start

sleep 1
chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
fi
echo "etcd install completed ..."
source /etc/profile


#echo status
/usr/local/etcd/scripts/etcdctl.sh status
