#!/bin/bash

PWD="/usr/local/etcd/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|status|uninstall"
     exit
fi

uninstall_etcd()
{
    bash ${PWD}/etcdctl.sh stop
    kill -9 `ps aux | grep etcdrun.sh |grep -v grep | awk '{print $2}'`
    rm -rf /usr/local/etcd/
}

case "$1" in
	start)
        nohup /usr/local/etcd/bin/etcd --config-file /usr/local/etcd/conf/akcs-etcd.conf > /var/log/etcdlog/etcd.log 2>&1 &
		;;
	stop)
        kill `pidof etcd`
		;;
    uninstall)
        uninstall_etcd
		;;
    status)
        cnt=`netstat -alnp | grep 8507 | grep etcd | grep -v grep | wc -l`
        if [ "$cnt" -eq "0" ]
        then
            echo "\033[1;31;05m etcd is stop!!!\033[0m"
        else
            echo "\033[0;32m etcd is running \033[0m"
        fi
        ;;  
	*)
		echo "Usage: etcd {start|stop|status|uninstall}" >&2
		exit 3
		;;
esac
exit
