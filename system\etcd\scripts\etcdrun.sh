#!/bin/bash

PROCESS_ETCD_NAME=etcd
PROCESS_ETCD_PATH=/usr/local/etcd/scripts/etcdctl.sh

PROCESS_START_CMD="/usr/local/etcd/scripts/etcdctl.sh start"

LOG_FILE=/var/log/etcd_run_daemon.log

FIRST_RUN=1 #第一次启动这个程序 不需要通知运维。

SERVERIP=`cat /etc/ip`

CONF_FILE=/etc/app_backend_install.conf
MAIL_LIST=`cat $CONF_FILE | grep APP_STOP_EMAIL_LIST | awk -F'=' '{ print $2 }'`

EMAIL_TIMES=120
DETECTFILE_ROOT=/tmp

app_stop_email() 
{
    email=0
    if [ -f $DETECTFILE_ROOT/.$1* ];then
        time=`ls $DETECTFILE_ROOT/.$1* | awk -F '_' '{print $2}'`
        unix=`date +%s`
        let time=$time+$EMAIL_TIMES
        if [ $time -lt $unix ];then
            #报警  重新计算时间
            rm  $DETECTFILE_ROOT/.$1*
            touch $DETECTFILE_ROOT/.$1_`date +%s`
            email=1
        fi
    else
        touch $DETECTFILE_ROOT/.$1_`date +%s`
        email=1
    fi
    if [ $email -eq 1 ];then
        processname=$1
        echo "${processname} is stopped，请及时排查原因。/nIP:${SERVERIP}" | mutt -s "应用程序停止警告"  ${MAIL_LIST}
    fi
}


run_etcd() {
        count=`netstat -ntlp | grep etcd | grep -v grep | wc -l`
        if [ $count -eq 0 ]
        then
                date >> $LOG_FILE
        echo "warning !, etcd is stopped..." >> $LOG_FILE
        `$PROCESS_START_CMD`
                if [ $FIRST_RUN -ne 1 ];then
                        echo "etcd stop报警"  >> $LOG_FILE
                        app_stop_email etcd
                fi
                sleep 2
        fi
}


while [ 1 ]
do
    run_etcd
	FIRST_RUN=0
	sleep 5
done
