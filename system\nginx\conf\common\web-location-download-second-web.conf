#download
location /group2/ {
    proxy_pass http://fdfs;
}
location /download/face/group2/ {
    proxy_pass http://fdfs/group2/;
}

location /download/face/ {
    proxy_pass http://fdfs/group2/M00/face/;
}

location /download/community/ {
    proxy_pass https://MASTER_WEB_INNER_IP/download/community/;	
}

location /download/personal/ {
    proxy_pass https://MASTER_WEB_INNER_IP/download/personal/;	
}

location /download/per_qrcode/ {
    proxy_pass https://MASTER_WEB_INNER_IP/download/per_qrcode/;	
}

location /download/UserDetail/ {
    proxy_pass https://MASTER_WEB_INNER_IP/download/UserDetail/;
}			

location /download/ {
    proxy_pass https://MASTER_WEB_INNER_IP/download/;			
}