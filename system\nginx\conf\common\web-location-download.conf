#download
location /group2/ {
    proxy_pass http://fdfs;
}

location /download/face/group2/ {
    proxy_pass http://fdfs/group2/;
}

location /download/face/ {
    proxy_pass http://fdfs/group2/M00/face/;
}

location /download/community/ {
    alias   /var/www/download/community/;
    access_log /var/log/nginx/logs/dev_download.log;
    error_log /var/log/nginx/logs/dev_download_err.log warn;	
    secure_link $arg_token,$arg_e;
    secure_link_md5 ak_download:$uri:$arg_e;
    if ($secure_link = "") {
        return 403;
    }
    if ($secure_link = "0") {
        return 403;
    }
}

location /download/personal/ {
    alias   /var/www/download/personal/;
    access_log /var/log/nginx/logs/dev_download.log;
    error_log /var/log/nginx/logs/dev_download_err.log warn;
    
    secure_link $arg_token,$arg_e;
    secure_link_md5 ak_download:$uri:$arg_e;
    if ($secure_link = "") {
        return 403;
    }
    if ($secure_link = "0") {
        return 403;
    }
}

location /download/per_qrcode/ {
    alias   /var/www/download/per_qrcode/;

    secure_link $arg_token,$arg_e;
    secure_link_md5 ak_download:$uri:$arg_e;
    if ($secure_link = "") {
        return 403;
    }
    if ($secure_link = "0") {
        return 403;
    }
}

location /download/UserDetail/ {
    alias   /var/www/download/UserDetail/;
    access_log /var/log/nginx/logs/dev_download.log;
    error_log /var/log/nginx/logs/dev_download_err.log warn;
    
    secure_link $arg_token,$arg_e;
    secure_link_md5 ak_download:$uri:$arg_e;
    if ($secure_link = "") {
        return 403;
    }
    if ($secure_link = "0") {
        return 403;
    }
}			

location /download/UserMeta/ {
    alias   /var/www/download/UserMeta/;
    access_log /var/log/nginx/logs/dev_download.log;
    error_log /var/log/nginx/logs/dev_download_err.log warn;
    
    secure_link $arg_token,$arg_e;
    secure_link_md5 ak_download:$uri:$arg_e;
    if ($secure_link = "") {
        return 403;
    }
    if ($secure_link = "0") {
        return 403;
    }
}

location /download/Schedule/ {
    alias   /var/www/download/Schedule/;
    access_log /var/log/nginx/logs/dev_download.log;
    error_log /var/log/nginx/logs/dev_download_err.log warn;
    
    secure_link $arg_token,$arg_e;
    secure_link_md5 ak_download:$uri:$arg_e;
    if ($secure_link = "") {
        return 403;
    }
    if ($secure_link = "0") {
        return 403;
    }
}

location /download/ {
    limit_req zone=webqpslimit burst=200 nodelay;
    alias   /var/www/download/;
    access_log /var/log/nginx/logs/dev_download.log;
    error_log /var/log/nginx/logs/dev_download_err.log warn;			
}