location /nginx_status {
    allow 127.0.0.1;
    deny all;
    stub_status on;
    access_log off;
}


location /status {
    allow 127.0.0.1;
    deny all;
    fastcgi_param SCRIPT_FILENAME $fastcgi_script_name;
    include fastcgi_params;
    fastcgi_pass 127.0.0.1:9000;
}
#wss
location /wss/
{
    proxy_pass http://MASTER_WEB_INNER_IP:8282;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    proxy_set_header X-AKCS-Real-IP $remote_addr;
    proxy_set_header X-Real-IP $remote_addr;
    #6.7.0引入SLB，内部转发时不再带上X-Forwarded-For，否则无法判断哪一级属于客户IP还是SLB IP，内部转发通过X-AKCS-Real-IP来标识客户端真实IP
    #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}

location = / {
    # 前端资源不需要限制，当前nginx版本1.18.0不支持limit_conn off和limit_conn webiplimit 0的关闭限制的写法，设置为比较大不会达到的数值代替关闭限制
    limit_conn webiplimit 9999;
    # 使用302而不是301
    rewrite  ^(.*)$ https://${host}/manage-new/ redirect;
}

location / {
    limit_conn webiplimit 9999;
    limit_req zone=webqpslimit burst=200 nodelay;
    root   /var/www/html;
    index  index.php index.html;
    include /usr/local/nginx/conf/common/web-head.conf;
}

location /VBell/ {
    limit_conn webiplimit 9999;
    root   /var/www/html;
    index  index.php index.html;
}

location /dist/ {
    limit_conn webiplimit 9999;
    root   /var/www/html;
    index  index.php index.html;
    include /usr/local/nginx/conf/common/web-head.conf;
}
location ~ /manage$ {
    limit_conn webiplimit 9999;
    rewrite ^(.*)$ https://${host}/manage-new/ permanent;
}
location /manage/ {
    limit_conn webiplimit 9999;
    rewrite ^(.*)$ https://${host}/manage-new/ permanent;
}
location /manager/ {
    limit_conn webiplimit 9999;
    root   /var/www/html;
    index  index.php index.html;
    include /usr/local/nginx/conf/common/web-head.conf;
}
location /manage-new/ {
    limit_conn webiplimit 9999;
    root   /var/www/html;
    index  index.php index.html;
    include /usr/local/nginx/conf/common/web-head.conf;
}		

# pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
#
location /apache-v3.0/ {
    client_max_body_size    1000m;
    index  index.php index.html index.htm;
    if (!-e $request_filename)
    {      
        rewrite /(.*)$ /apache-v3.0/route.php?/$1 last;
        break;
    }
}
location /web-server/ {
    client_max_body_size    1000m;
    index  index.php index.html index.htm;
    if (!-e $request_filename)
    {
        rewrite /(.*)$ /web-server/route.php?/$1 last;
        break;
    }
}		
location /MaintenanceServer/ {
    index  index.php index.html index.htm;
    if (!-e $request_filename)
    {      
        rewrite /(.*)$ /MaintenanceServer/route.php?/$1 last;
        break;
    }
}

location ~ \.php$ {
    include /usr/local/nginx/conf/common/web-head.conf;
    limit_req zone=webqpslimit burst=200 nodelay;
    #root   html;
    alias  /var/www/html;
    fastcgi_pass   127.0.0.1:9000;
    fastcgi_index  index.php;
    fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
    fastcgi_read_timeout 600;
    include        fastcgi_params;
    fastcgi_connect_timeout 1200;
}