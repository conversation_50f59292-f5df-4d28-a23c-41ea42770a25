#group1用于截图存储 group2用于配置存储 group3用于亚马逊迁移
server {
        listen  [::]:8091 ssl;
        listen  8091 ssl;
		
		access_log /var/log/nginx/logs/dev_download.log;
		error_log /var/log/nginx/logs/dev_download_err.log warn;
		
        include /usr/local/nginx/conf/common/ssl-root.conf;
		
		location /download/ {
            alias   /var/www/download/;	

            secure_link $arg_token,$arg_e;
            secure_link_md5 ak_download:$uri:$arg_e;
            if ($secure_link = "") {
				return 403;
            }
            if ($secure_link = "0") {
				return 403;
            }			
        }

        location /group2/ {
		    secure_link $arg_token,$arg_e;
            secure_link_md5 ak_download:$uri:$arg_e;
            if ($secure_link = "") {
				return 403;
            }
            if ($secure_link = "0") {
				return 403;
            }
			proxy_pass http://fdfs;		
        }

        location /group1 {
			proxy_pass http://ALI_TRACKER_IP:8090/group1;		
        }
		
		location /group3 {
			proxy_pass http://AWS_TRACKER_IP:8090/group3;		
        }
}
