server {
        listen  [::]:443 ssl;
        listen  443 ssl;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers x-auth-token,x-community-id,x-cloud-lang,x-cloud-version;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS,PUT;
        access_log /var/log/nginx/logs/web_access.log;
        error_log /var/log/nginx/logs/web_error.log warn;

        include /usr/local/nginx/conf/common/ssl-root.conf;
        include /usr/local/nginx/conf/common/web-location.conf;
        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;        


}
server {
    	listen  80;
    	listen  [::]:80;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers x-auth-token,x-community-id,x-cloud-lang,x-cloud-version;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS,PUT;

        access_log /var/log/nginx/logs/web_access.log;
        error_log /var/log/nginx/logs/web_error.log warn;

        include /usr/local/nginx/conf/common/web-location.conf;
        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;        
}


server {
        listen  [::]:9443 ssl;
        listen  9443 ssl;
        access_log /var/log/nginx/logs/web_access_ios.log;
        error_log /var/log/nginx/logs/web_error_ios.log warn;

        include /usr/local/nginx/conf/common/ssl-root.conf;
        include /usr/local/nginx/conf/common/web-location.conf;
        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;        
}

#6.7.0 web增加SLB， slb 外面443只能代理到http协议的端口，但是80端口又需要重定向到443，强制走了证书校验，所以只能再开一个端口
server {
        listen  [::]:81;
        listen  81;
        #关闭slb导致的端口代理nginx自动补全/重定向带上了反向代理端口的bug
		port_in_redirect off;
        access_log /var/log/nginx/logs/web_access.log main;
        error_log /var/log/nginx/logs/web_error.log warn;
        include /usr/local/nginx/conf/common/web-location.conf;
        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;
}

#6.7.0 web增加SLB 给H5使用
server {
        listen  [::]:82;
        listen  82;
        #关闭slb导致的端口代理nginx自动补全/重定向带上了反向代理端口的bug
        port_in_redirect off;
        access_log /var/log/nginx/logs/web_access_ios.log;
        error_log /var/log/nginx/logs/web_error_ios.log warn;

        include /usr/local/nginx/conf/common/web-location.conf;
        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;
}
