server {
    listen  [::]:80;
    listen  80;
    rewrite ^(.*)$ https://${host}$1 permanent;
    access_log /var/log/nginx/logs/web_access.log;
    error_log /var/log/nginx/logs/web_error.log warn;    
}

server {
        listen  [::]:443 ssl;
        listen  443 ssl;
        access_log /var/log/nginx/logs/web_access.log;
        error_log /var/log/nginx/logs/web_error.log warn;

		include /usr/local/nginx/conf/common/ssl-root.conf;

        error_page  404 403 500 502 503 504  /upgrade.html;
        location = /upgrade.html {
                    root   /var/www/html/upgrade;
                    index  upgrade.html;

        }

        #6.7.0 健康探测接口，只要url包含/common/monitor/healthCheck都匹配成健康探测
        location ~ ^/.*common/monitor/healthCheck {
            return 200 "ok";
        }

		location /nginx_status {
			allow 127.0.0.1;
			deny all;
			stub_status on;
			access_log off;
		}

		location /status {
            return 200 "ok!";
		}

        location / {
		    limit_req zone=webqpslimit burst=200 nodelay;
            root   /var/www/html/upgrade;
            index  upgrade.html;
        }

        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;
}

#6.7.0 web增加SLB
server {
        listen  [::]:81;
        listen  81;
        access_log /var/log/nginx/logs/web_access.log;
        error_log /var/log/nginx/logs/web_error.log warn;

        error_page  404 403 500 502 503 504  /upgrade.html;
        location = /upgrade.html {
                    root   /var/www/html/upgrade;
                    index  upgrade.html;

        }

        #6.7.0 健康探测接口，只要url包含/common/monitor/healthCheck都匹配成健康探测
        location ~ ^/.*common/monitor/healthCheck {
            return 200 "ok";
        }

		location /nginx_status {
			allow 127.0.0.1;
			deny all;
			stub_status on;
			access_log off;
		}

		location /status {
            return 200 "ok!";
		}

        location / {
		    limit_req zone=webqpslimit burst=200 nodelay;
            root   /var/www/html/upgrade;
            index  upgrade.html;
        }

        #download
        include /usr/local/nginx/conf/common/web-location-download.conf;
}


server {
        listen  [::]:9443 ssl;
        listen  9443 ssl;
        access_log /var/log/nginx/logs/web_access.log;
        error_log /var/log/nginx/logs/web_error.log warn;

		include /usr/local/nginx/conf/common/ssl-root.conf;

        error_page  404 403 500 502 503 504  /upgrade.html;
        location = /upgrade.html {
                    root   /var/www/html/upgrade;
                    index  upgrade.html;

        }

        #6.7.0 健康探测接口，只要url包含/common/monitor/healthCheck都匹配成健康探测
        location ~ ^/.*common/monitor/healthCheck {
            return 200 "ok";
        }

        location / {
            root   /var/www/html/upgrade;
            index  upgrade.html;
        }
}

#6.7.0 web增加SLB 给H5使用
server {
        listen  [::]:82;
        listen  82;
        access_log /var/log/nginx/logs/web_access.log;
        error_log /var/log/nginx/logs/web_error.log warn;

        error_page  404 403 500 502 503 504  /upgrade.html;
        location = /upgrade.html {
                    root   /var/www/html/upgrade;
                    index  upgrade.html;

        }

        #6.7.0 健康探测接口，只要url包含/common/monitor/healthCheck都匹配成健康探测
        location ~ ^/.*common/monitor/healthCheck {
            return 200 "ok";
        }

        location / {
            root   /var/www/html/upgrade;
            index  upgrade.html;
        }
}
