
server {
        listen  [::]:443 ssl;
        listen  443 ssl;
		
        server_name api.*;

        location / {
           proxy_pass http://127.0.0.1:6080;
        }
}
limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
server {
        listen  [::]:6080;
        listen  6080;

        #logs
        access_log /var/log/nginx/logs/openapi_access.log;
        error_log /var/log/nginx/logs/openapi_error.log warn;

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
		
        location / {
        	root   /var/www/html-api/openapi;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {      
                rewrite /(.*)$ /openapi/index.php?/$1 last;
                break;
            }
        }
		
	    location ~ \.php$ {
	    limit_req zone=one burst=5 nodelay;
	    #root   html;
            alias  /var/www/html-api;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }
}
