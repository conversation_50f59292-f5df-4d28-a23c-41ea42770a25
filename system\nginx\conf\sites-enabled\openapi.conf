geo $limit{
    default 1;
    141.226.185.157 0;
    141.226.185.158 0;
    141.226.185.159 0;
    ************* 0;
	********** 0;
	*************** 0;
	************* 0;	
}
map $limit $limit_key{
    1 $binary_remote_addr;
    0 "";
}
limit_req_zone $limit_key zone=api:10m rate=50r/s;

server {
        listen  [::]:443 ssl;
        listen  443 ssl;
		
        server_name api.*;

        include /usr/local/nginx/conf/common/ssl-root.conf;

        #logs
        access_log /var/log/nginx/logs/openapi_access.log main;
        error_log /var/log/nginx/logs/openapi_error.log warn;

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
		
        location /openApi/ {
            root   /var/www/html-api/openapi/;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {
                rewrite /(.*)$ /openapi/v4/route.php?/$1 last;
                break;
            }
        }


        location / {
        	root   /var/www/html-api/openapi;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {      
                rewrite /(.*)$ /openapi/index.php?/$1 last;
                break;
            }
        }
		
		location ~ \.php$ {
		limit_req zone=api burst=50 nodelay;
            #root   html;
            alias  /var/www/html-api;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }
}

#6.7.0 web增加SLB，SLB只能转发http
server {
        listen  [::]:81;
        listen  81;

        server_name api.*;

        #关闭slb导致的端口代理nginx自动补全/重定向带上了反向代理端口的bug
        port_in_redirect off;

        #logs
        access_log /var/log/nginx/logs/openapi_access.log main;
        error_log /var/log/nginx/logs/openapi_error.log warn;

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000

        location /openApi/ {
            root   /var/www/html-api/openapi/;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {
                rewrite /(.*)$ /openapi/v4/route.php?/$1 last;
                break;
            }
        }


        location / {
        	root   /var/www/html-api/openapi;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {
                rewrite /(.*)$ /openapi/index.php?/$1 last;
                break;
            }
        }

		location ~ \.php$ {
		limit_req zone=api burst=50 nodelay;
            #root   html;
            alias  /var/www/html-api;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }
}

server {
        listen  [::]:6080 ssl;
        listen  6080 ssl;
		
        server_name api.*;

        include /usr/local/nginx/conf/common/ssl-root.conf;

        #logs
        access_log /var/log/nginx/logs/openapi_access.log main;
        error_log /var/log/nginx/logs/openapi_error.log warn;

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
		
        location / {
        	root   /var/www/html-api/openapi;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {      
                rewrite /(.*)$ /openapi/index.php?/$1 last;
                break;
            }
        }
		
		location ~ \.php$ {
		limit_req zone=api burst=100 nodelay;
            #root   html;
            alias  /var/www/html-api;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }
}

