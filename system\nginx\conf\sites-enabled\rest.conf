server {
        listen        [::]:8080;
        listen        8080;
        listen  [::]:8443 ssl;
        listen  8443 ssl;

        #logs
        access_log /var/log/nginx/logs/rest_access.log main;
        error_log /var/log/nginx/logs/rest_error.log warn;

        include /usr/local/nginx/conf/common/ssl-root.conf;


        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        location / {
        	root   /var/www/html/slim;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {      
                rewrite /(.*)$ /slim/index.php?/$1 last;
                break;
            }
        }
        location ~ \.php$ {
            #root   html;
            alias  /var/www/html;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }

}
