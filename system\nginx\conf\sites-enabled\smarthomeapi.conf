server {
       listen  [::]:8531 ssl;
       listen  8531 ssl;
        #logs
        access_log /var/log/nginx/logs/smarthome_api_access.log main;
        error_log /var/log/nginx/logs/smarthome_api_error.log warn;

        include /usr/local/nginx/conf/common/ssl-root.conf;
        
        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        location /pbx {
        	root   /var/www_for_smarthome/html/SmartHomePbxApi;
            index  index.php index.html index.htm;
            if (!-e $request_filename)
            {      
                rewrite /(.*)$ /SmartHomePbxApi/index.php?/$1 last;
                break;
            }
        }

        #smg对外代理
        location /command/ {
            proxy_pass http://MASTER_WEB_INNER_IP:8533;
        }

        location /notice/ {
            proxy_pass http://MASTER_WEB_INNER_IP:8533;
        }

        location /cloud-adapt/v1/ {
            proxy_pass http://127.0.0.1:8789;
            proxy_set_header X-AKCS-Real-IP $remote_addr;
            proxy_set_header X-Real-IP $remote_addr;
            #6.7.0引入SLB，内部转发时不再带上X-Forwarded-For，否则无法判断哪一级属于客户IP还是SLB IP，内部转发通过X-AKCS-Real-IP来标识客户端真实IP
            #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
	    #location /cloud-adapt/v1/ {
        #    root   /var/www_for_smarthome/html/smartHome2;
        #    index  index.php index.html index.htm;
        #    if (!-e $request_filename)
        #    {
        #        rewrite /(.*)$ /smartHome2/route.php?/$1 last;
        #        break;
        #    }
        #}
        
        location ~ \.php$ {
            alias  /var/www_for_smarthome/html;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }

}

#v2版本接口
server {
       listen  [::]:8534 ssl;
       listen  8534 ssl;
        #logs
        access_log /var/log/nginx/logs/smarthome_api_access.log main;
        error_log /var/log/nginx/logs/smarthome_api_error.log warn;

        include /usr/local/nginx/conf/common/ssl-all.conf;

	    location /cloud-adapt/v2/ {
            proxy_pass http://127.0.0.1:8789;
            proxy_set_header X-AKCS-Real-IP $remote_addr;
            proxy_set_header X-Real-IP $remote_addr;
            #6.7.0引入SLB，内部转发时不再带上X-Forwarded-For，否则无法判断哪一级属于客户IP还是SLB IP，内部转发通过X-AKCS-Real-IP来标识客户端真实IP
            #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
	    #location /cloud-adapt/v2/ {
        #    root   /var/www_for_smarthome/html/smartHome2;
        #    index  index.php index.html index.htm;
        #    if (!-e $request_filename)
        #    {
        #        rewrite /(.*)$ /smartHome2/route.php?/$1 last;
        #        break;
        #    }
        #}
        
        location ~ \.php$ {
            alias  /var/www_for_smarthome/html;
            fastcgi_pass   127.0.0.1:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            include        fastcgi_params;
        }

}
