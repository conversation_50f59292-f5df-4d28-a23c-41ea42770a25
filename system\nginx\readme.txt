
./configure --with-http_secure_link_module --prefix=/usr/local/nginx --with-http_stub_status_module --with-http_ssl_module --with-http_ssl_module --with-http_realip_module --with-http_gzip_static_module --http-client-body-temp-path=/usr/local/nginx/temp/client_body_temp --http-proxy-temp-path=/usr/local/nginx/temp/proxy_temp --http-fastcgi-temp-path=/usr/local/nginx/temp/fastcgi_temp --http-uwsgi-temp-path=/usr/local/nginx/temp/uwsgi_temp --http-scgi-temp-path=/usr/local/nginx/temp/scgi_temp --with-http_v2_module --with-openssl=/home/<USER>/openssl-1.0.2j

make




添加配置nginx配置说明。
1、conf\sites-enabled 下添加配置
2、conf\nginx.conf 添加文件路径
3、scripts\conf 下也要对应添加各种模式下配置。
   nginx.conf---正常环境配置
   nginx-develop.conf---开发环境的配置(web要切换为http的环境配置)
   nginx-upgrade.conf---升级时候的配置(升级时候提示服务器正在升级的配置)
   备注：升级时候的配置，要保证一些服务不受影响，比如下载配置。
         所以新增配置时候要考虑upgrade.conf要怎么配置。
   
   