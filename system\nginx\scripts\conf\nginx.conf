
#user  nobody;
worker_processes  auto;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

pid        /usr/local/nginx/logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 200m;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent $request_time "$http_referer" '
                      '"$http_user_agent" $http_x_forwarded_for';

    #access_log  logs/access.log  main;

	limit_req_zone $binary_remote_addr zone=webqpslimit:10m rate=200r/s;
	limit_conn_zone $binary_remote_addr zone=webiplimit:10m;
	limit_conn webiplimit 100;
	
    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip_static  on;

    include /usr/local/nginx/conf/sites-enabled/upstream.conf;
    include /usr/local/nginx/conf/sites-enabled/main.conf;
    include /usr/local/nginx/conf/sites-enabled/rest.conf;
	include /usr/local/nginx/conf/sites-enabled/openapi.conf;
	#aws迁移时候用到，已经不需要
	#include /usr/local/nginx/conf/sites-enabled/openapi-aws.conf;
	include /usr/local/nginx/conf/sites-enabled/fdfs.conf;
    include /usr/local/nginx/conf/sites-enabled/gate.conf;
    include /usr/local/nginx/conf/sites-enabled/smarthomeapi.conf;

	#for akcs intall bm
	include /usr/local/nginx/conf/bm_conf/*;	
}
