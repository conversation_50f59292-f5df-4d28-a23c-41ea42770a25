#!/bin/sh
#这个文件是为了3.x->4.0从apache替换到nginx做的一个专门的升级文件
PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

#######NGINX安装升级开始############
NGINX_INSTALL_DIR=/usr/local/nginx
NGINX_DIR=${PWD}/../../nginx
NGINX_INSTALL_CONFIG=/etc/app_backend_install.conf

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}

yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

EnterBasicSrvIPAddr()
{
    #输入redis内网IP
    yellow "Enter your web server domain,(eg:test.akuvox.com): \c"
    read NGINX_DOMAIN;
	
	yellow "Enter your your fdfs tracker inner ip: \c"
	read TRACKER_INNER_IP;
	
	yellow "Enter your aliyun tracker ip: \c"
	read ALI_TRACKER_IP;
	
	yellow "Enter your awsyun tracker outer ip: \c"
	read AWS_TRACKER_IP;

    #写入基础服务的IP文件
    echo "" >$NGINX_INSTALL_CONFIG
    echo "NGINX_DOMAIN=$NGINX_DOMAIN" >>$NGINX_INSTALL_CONFIG
}

EchoBasicSrvIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    inner_ip_str="NGINX_DOMAIN="
    inner_ip_cat=`cat $NGINX_INSTALL_CONFIG | grep -w NGINX_DOMAIN | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
	inner_ip_str="ALI_TRACKER_IP="
    inner_ip_cat=`cat $NGINX_INSTALL_CONFIG | grep -w ALI_TRACKER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
	inner_ip_str="AWS_TRACKER_IP="
    inner_ip_cat=`cat $NGINX_INSTALL_CONFIG | grep -w AWS_TRACKER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
	inner_ip_str="TRACKER_INNER_IP="
    inner_ip_cat=`cat $NGINX_INSTALL_CONFIG | grep -w TRACKER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
} 


if [ -f $NGINX_INSTALL_CONFIG ];then
    EchoBasicSrvIPAddr
    NGINX_DOMAIN=`cat $NGINX_INSTALL_CONFIG | grep -w NGINX_DOMAIN | awk -F'=' '{ print $2 }'`
    ALI_TRACKER_IP=`cat $NGINX_INSTALL_CONFIG | grep -w ALI_TRACKER_IP | awk -F'=' '{ print $2 }'`
    AWS_TRACKER_IP=`cat $NGINX_INSTALL_CONFIG | grep -w AWS_TRACKER_IP | awk -F'=' '{ print $2 }'`
	TRACKER_INNER_IP=`cat $NGINX_INSTALL_CONFIG | grep -w TRACKER_INNER_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the basic server ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/app_backend_install.conf>, please enter all information below:" 
    EnterBasicSrvIPAddr
fi

#所有的配置全部替换

cp ${NGINX_DIR}/etc ${NGINX_INSTALL_DIR} -R
cp ${NGINX_DIR}/html ${NGINX_INSTALL_DIR} -R
cp ${NGINX_DIR}/logs ${NGINX_INSTALL_DIR} -R
rm ${NGINX_INSTALL_DIR}/sbin/nginx
cp ${NGINX_DIR}/sbin ${NGINX_INSTALL_DIR} -R
cp ${NGINX_DIR}/scripts ${NGINX_INSTALL_DIR} -R
cp ${NGINX_DIR}/temp ${NGINX_INSTALL_DIR} -R

#要略过证书的拷贝
if [ -f /usr/local/nginx/conf/cert/wildcard/fullchain.crt ];then
	rm -rf ${NGINX_DIR}/conf/cert
fi

if [ -d /usr/local/nginx/conf/cert ];then
	rm ${NGINX_DIR}/conf/cert -rf
fi
cp ${NGINX_DIR}/* ${NGINX_INSTALL_DIR} -R

if [ ! -d /usr/local/nginx/conf/bm_conf ];then
	mkdir -p /usr/local/nginx/conf/bm_conf
fi

#安装启动脚本
cp $NGINX_DIR/etc/init.d/nginx /etc/init.d

if [ ! -n "$ALI_TRACKER_IP" ];then
	ALI_TRACKER_IP="127.0.0.1"
fi
if [ ! -n "$AWS_TRACKER_IP" ];then
	AWS_TRACKER_IP="127.0.0.1"
fi
if [ ! -n "$TRACKER_INNER_IP" ];then
	TRACKER_INNER_IP="127.0.0.1"
fi
sed -i "s/^.*YOUR_DOMAIN=.*/YOUR_DOMAIN=${NGINX_DOMAIN}/g" /usr/local/nginx/scripts/update_cert_from_remote_server.sh
sed -i "s/ALI_TRACKER_IP/${ALI_TRACKER_IP}/g" /usr/local/nginx/conf/sites-enabled/fdfs.conf
sed -i "s/AWS_TRACKER_IP/${AWS_TRACKER_IP}/g" /usr/local/nginx/conf/sites-enabled/fdfs.conf
sed -i "s/TRACKER_INNER_IP/${TRACKER_INNER_IP}/g" /usr/local/nginx/conf/sites-enabled/fdfs.conf
sed -i "s/TRACKER_INNER_IP/${TRACKER_INNER_IP}/g" /usr/local/nginx/conf/sites-enabled/main.conf
sed -i "s/TRACKER_INNER_IP/${TRACKER_INNER_IP}/g" /usr/local/nginx/conf/sites-enabled/main-product.conf

#杀死守护脚本
kill -9 `ps -ef | grep update_cert_from_remote_server_run.sh | grep -v grep | awk '{print $2}'`
#启动证书过期检测
nohup bash /usr/local/nginx/scripts/update_cert_from_remote_server_run.sh >/dev/null &

echo '添加到开机启动'
if ! grep -q "/usr/local/nginx/scripts/update_cert_from_remote_server_run.sh" /etc/init.d/rc.local; then
    echo "bash /usr/local/nginx/scripts/update_cert_from_remote_server_run.sh &" >> /etc/init.d/rc.local
fi

