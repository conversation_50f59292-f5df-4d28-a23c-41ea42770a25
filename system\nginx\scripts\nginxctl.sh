#!/bin/sh
ACMD="$1"
PWD="/usr/local/akcs/csmain/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` normal|upgrade|develop"
     exit
fi

normal_nginx()
{   
   cp /usr/local/nginx/scripts/conf/nginx.conf /usr/local/nginx/conf/nginx.conf
   bash /etc/init.d/nginx restart
}
upgrade_nginx()
{
   cp /usr/local/nginx/scripts/conf/nginx-upgrade.conf /usr/local/nginx/conf/nginx.conf
   bash /etc/init.d/nginx restart    
}

develop_nginx()
{
   cp /usr/local/nginx/scripts/conf/nginx-develop.conf /usr/local/nginx/conf/nginx.conf
   bash /etc/init.d/nginx restart   
}



case $ACMD in
  normal)
        normal_nginx
    ;;
  upgrade)
        upgrade_nginx
    ;;
  develop)
        develop_nginx
    ;; 
  *)  
    echo "Usage: sh `basename $0` normal|upgrade|develop"
    ;;
esac
exit

