#!/bin/sh

CERT_MANAGE_SERVER="certmanage.akuvox.com:1026"
#CERT_MANAGE_SERVER="certmanage.akuvox.com:1026"
YOUR_DOMAIN="test180.akuvox.com"
ROOT_CERT_DIR=/usr/local/nginx/conf/cert/root
WILDCARD_CERT_DIR=/usr/local/nginx/conf/cert/wildcard

UPDATE_LOG=/var/log/update_cert_from_remote_server.log

while [ 1 ]
do
	#比较md5值不同时候替换掉
	is_update=0
	date_time=`date`
	#根证书不加入证书管理
	#if [ -f /usr/local/nginx/conf/cert/root/fullchain.crt ];then
	#	md5=`curl -s -k  https://${CERT_MANAGE_SERVER}/check_md5?dir=/cert/${YOUR_DOMAIN}/root/fullchain.crt`
	#	local_md5=`md5sum /usr/local/nginx/conf/cert/root/fullchain.crt | awk '{print $1}' | tr -d '\n'`
	#	#md5可能是空 请求失败
	#	if [  ${#md5} -eq 32 ] && [ "$md5" != "$local_md5" ];then
	#		curl -s -k https://${CERT_MANAGE_SERVER}/cert/${YOUR_DOMAIN}/root/fullchain.crt -o $ROOT_CERT_DIR/fullchain.crt
	#		curl -s -k https://${CERT_MANAGE_SERVER}/cert/${YOUR_DOMAIN}/root/private.pem -o $ROOT_CERT_DIR/private.pem
	#		is_update=1
	#		echo "$date_time remote cert is change update root cert " >> $UPDATE_LOG		
	#	fi
	#fi
	
	if [ -f /usr/local/nginx/conf/cert/wildcard/fullchain.crt ];then
		md5=`curl -s -k  https://${CERT_MANAGE_SERVER}/check_md5?dir=/cert/${YOUR_DOMAIN}/wildcard/fullchain.crt`
		local_md5=`md5sum /usr/local/nginx/conf/cert/wildcard/fullchain.crt | awk '{print $1}' | tr -d '\n'`
		if [  ${#md5} -eq 32 ] && [ "$md5" != "$local_md5" ];then
			curl -s -k https://${CERT_MANAGE_SERVER}/cert/${YOUR_DOMAIN}/wildcard/fullchain.crt -o $WILDCARD_CERT_DIR/fullchain.crt
			curl -s -k https://${CERT_MANAGE_SERVER}/cert/${YOUR_DOMAIN}/wildcard/private.pem -o $WILDCARD_CERT_DIR/private.pem
			is_update=1
			echo "$date_time remote cert is change update wildcard cert" >> $UPDATE_LOG
		fi
	fi
	if [ $is_update -eq 1 ];then
		/usr/local/nginx/sbin/nginx -s reload
		echo "$date_time reload" >> $UPDATE_LOG
	fi	
	
	sleep 30
done


