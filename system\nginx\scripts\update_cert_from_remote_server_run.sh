#!/bin/sh


PROCESS_NAME=update_cert_from_remote_server.sh
LOG_FILE=/var/log/update_cert_from_remote_server_run_daemon.log

APP_STOP_EMAIL_LIST=`cat /etc/notify_email.conf  | grep 'APP_STOP_EMAIL_LIST=' | awk -F '=' '{print $2}'`

#程序判断是否挂掉的共同函数
COMMON_SERVERIP=`cat /etc/ip | grep SERVERIP=  | awk -F '=' '{print $2}'`
COMMON_FIRST_RUN=1

app_stop_email() 
{
    DEATH_DETECT_TIME_FILE=/tmp/.$COMMON_PROCESS_NAME
    
    email=0
    if [ -f ${DEATH_DETECT_TIME_FILE}* ];then
        time=`ls ${DEATH_DETECT_TIME_FILE}* | awk -F '_' '{print $NF}'`
        unix=`date +%s`
        let time=$time+60
        if [ $time -lt $unix ];then
            #报警  重新计算时间
            rm  /tmp/.$1*
            touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
            email=1
        fi
    else
        touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
        email=1
    fi
    if [ $email -eq 1 ];then
        echo "${PROCESS_NAME} is stopped, IP:${COMMON_SERVERIP}" | mutt -s "${COMMON_SERVERIP} ${PROCESS_NAME} is stopped"  ${APP_STOP_EMAIL_LIST} &
        echo "sending email...." >> $LOG_FILE
    fi
}

common_run_detect() {
    count=`ps -ef | grep $PROCESS_NAME | grep -v grep | wc -l`
    if [ $count -eq 0 ]
    then
        date >> $LOG_FILE
        echo "warning !, $PROCESS_NAME is stopped..." >> $LOG_FILE
        nohup bash /usr/local/nginx/scripts/update_cert_from_remote_server.sh >/dev/null &
        if [ $COMMON_FIRST_RUN -ne 1 ];then
            echo "$PROCESS_NAME stop"  >> $LOG_FILE
            app_stop_email
        fi
        sleep 2
    fi
}


while [ 1 ]
do
	#比较md5值不同时候替换掉
	common_run_detect
	sleep 30
done


