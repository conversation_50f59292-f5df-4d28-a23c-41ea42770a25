#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   build.sh
# Version       :
# Description   :   web 的构建脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

DOCKER_TAG=$3          #docker镜像tag号
CONTAINER_NAME=$4      #启动容器名称
PROJECT_NAME=$5
JOB_NAME=$6
CONTAINER_NAME=app_backend_for_cicd      #启动容器名称

WORKSPACE_PATH=/opt/jenkins/workspace

DOCKER_ROOT=/home/<USER>
DOCKER_PROJECT_ROOT=$DOCKER_ROOT/$JOB_NAME/$PROJECT_NAME

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=objects
AKCS_PACKAGE_ROOT=$WORKSPACE_PATH/$JOB_NAME/$PROJECT_NAME/$AKCS_PACKAGE_NAME

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

build_project_config(){
    echo "【开始生成项目配置】"
}
docker_start_container(){

    if [[ -n $(docker ps -q -f "name=${CONTAINER_NAME}") ]];then
        echo "已启动容器：$CONTAINER_NAME"
    elif [[ -n $(docker ps -aq -f "name=${CONTAINER_NAME}") ]];then
        echo "容器已停止，正在重新启动容器中"
        docker restart $CONTAINER_NAME
    else
        echo "未启动容器，正在启动中"
        docker run -d --name $CONTAINER_NAME -v $WORKSPACE_PATH:$DOCKER_ROOT akcloud-1.0:latest /bin/bash -c "while true;do sleep 500000;done"
    fi
}
docker_build_project(){
    echo "【在容器中，开始编译项目】"
    mkdir -p $AKCS_PACKAGE_ROOT
    docker exec $CONTAINER_NAME bash -c "cd $DOCKER_PROJECT_ROOT/build && bash build.sh build"
}

build_project_config
docker_start_container
docker_build_project