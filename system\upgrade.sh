#!/bin/bash
PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

#######NGINX安装升级开始############未重启nginx，因为升级时有service nginx upgrade
NGINX_INSTALL_DIR=/usr/local/nginx
NGINX_DIR=${PWD}/nginx
cd $NGINX_DIR/scripts/
bash nginx_install.sh
bash /etc/init.d/nginx reload
cd -
#######php安装升级开始############
PHP_INSTALL_DIR=/usr/local/php
PHP_DIR=${PWD}/php
stty erase ^H
font_off(){
	echo -en "\033[0m"
}

yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

FDFS_TRACKER=`cat /etc/ip | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
sed -i "s/^.*tracker_server=.*/tracker_server=${FDFS_TRACKER}:22122/g" ${PHP_DIR}/etc/client.conf
#确定php-fpm的日志记录路径
if [ ! -d /var/log/php-fpmlog ];then
    mkdir /var/log/php-fpmlog 2>/dev/null
    chmod 777 -R /var/log/php-fpmlog
fi

cp -R ${PHP_DIR}/* ${PHP_INSTALL_DIR}
killall php-fpm
sleep 1
/usr/local/php/sbin/php-fpm >/dev/null 2>&1

#######mysql升级，替换完配置直接重启，升级时候会升级数据库，不能停止。
#added by chenyc,2019-08-26,确保query.log slow-query.log两个日志文件存在
if [ ! -f /var/log/mysqllog/query.log ];then
    touch /var/log/mysqllog/query.log
fi
if [ ! -f /var/log/mysqllog/slow-query.log ];then
    touch /var/log/mysqllog/slow-query.log
fi
chmod 777 -R /var/log/mysqllog/

#添加运维看文件/bin/configcat
BIN_DIR=${PWD}/bin
chmod 777 $BIN_DIR/*
cp -R ${BIN_DIR}/* /bin

#added by chenyc,处理fdfs的配置文件更新
FDFS_DIR=${PWD}/fdfs
chmod -R 777 ${FDFS_DIR}/*
cp -rf ${FDFS_DIR}/tracker.conf /etc/fdfs/
cp -rf ${FDFS_DIR}/storage.conf /etc/fdfs/
cp -rf ${FDFS_DIR}/http.conf /etc/fdfs/
cp -rf ${FDFS_DIR}/anti-steal.jpg /etc/fdfs/
cp -rf ${FDFS_DIR}/fdfs_run.sh /usr/local/fastdfs/scripts/
cp -rf ${FDFS_DIR}/fdfs_storaged_nginx.sh /usr/local/fastdfs/scripts/
cp -rf ${FDFS_DIR}/fdfs_tracker_nginx.sh /usr/local/fastdfs/scripts/
scriptpid=`ps -fe|grep fdfs_run.sh |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "fdfs_run.sh is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi
nohup bash /usr/local/fastdfs/scripts/fdfs_run.sh >/dev/null 2>&1 &

rm -rf /home/<USER>/data/*
docker restart tracker
docker exec tracker fdfs_trackerd /etc/fdfs/tracker.conf
docker restart storage
docker exec storage fdfs_storaged /etc/fdfs/storage.conf


#core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi
if [ -z "`grep "kernel.core_pattern" /etc/sysctl.conf`" ];then
	echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
fi

if [ -z "`grep "ulimit -c unlimited" /etc/profile`" ];then
	echo 'ulimit -c unlimited' >> /etc/profile
fi

sysctl -p >/dev/null
. /etc/profile
